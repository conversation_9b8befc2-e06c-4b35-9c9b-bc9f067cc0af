TESTING GUIDELINES
==================

This guide describes standards and best practices for writing tests in the PMS project.

## GENERAL PRINCIPLES

1. **Full Coverage**: Every public function/method must have tests
2. **Isolation**: Tests should be independent of each other
3. **Clarity**: Test names should clearly describe the tested scenario
4. **Reproducibility**: Tests should give the same result on every run

## TEST STRUCTURE

### Test Naming
Use the following format:
```
func TestServiceName_MethodName_Scenario(t *testing.T)
```

Examples:
- `TestUserAppService_GetAllAsAdmin_Success`
- `TestUserAppService_GetAllAsAdmin_WithFilters`
- `TestUserAppService_UpdateAsAdmin_UserUpdateError`

### Test Function Structure
```go
func TestServiceName_MethodName_Scenario(t *testing.T) {
    // 1. Arrange - prepare mocks and data
    mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
    
    // Prepare input data
    inputData := SomeStruct{...}
    
    // Prepare expected results
    expectedResult := SomeResult{...}
    
    // Setup mocks
    mockUser.SomeMethodMock.Expect(inputData).Return(expectedResult, nil)
    
    // 2. Act - execute the tested method
    svc := NewServiceName(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
    result, err := svc.TestedMethod(inputData)
    
    // 3. Assert - verify results
    require.NoError(t, err)
    require.Equal(t, expectedResult, result)
    
    // Additional checks if necessary
    require.Equal(t, uint64(1), mockUser.SomeMethodAfterCounter())
}
```

## USING MOCKS

### Helper Functions for Creating Mocks
Create helper functions to reduce code duplication:

```go
func createMocks(t *testing.T) (*GroupMock, *ParticipantMock, *ProductMock, *RoleMock, *UserMock) {
    return groupmocks.NewGroupDomainServiceMock(t),
        participantmocks.NewParticipantDomainServiceMock(t),
        productmocks.NewProductDomainServiceMock(t),
        rolemocks.NewRoleDomainServiceMock(t),
        usermocks.NewUserDomainServiceMock(t)
}
```

### Mock Setup
1. **Correct Input Parameters**: Mocks should expect exact parameters
2. **Realistic Data**: Returned data should correspond to real data
3. **Call Verification**: Use counters to verify the number of calls

```go
// Correct
mockUser.UpdateMock.Expect(userentity.UserUpdateData{
    ID:         userID,
    CategoryID: &categoryID,
    IsAdmin:    &isAdmin,
}).Return(updatedUser, nil)

// Call verification
require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
```

## TESTED SCENARIOS

### For each method, test:

1. **Happy Path (Success scenarios)**:
   - Basic successful case
   - Boundary values
   - Various combinations of input parameters

2. **Edge Cases (Boundary cases)**:
   - Empty input data
   - Nil values
   - Empty slices vs nil slices
   - Maximum/minimum values

3. **Error Cases (Error scenarios)**:
   - Errors from each dependency
   - Different types of errors
   - Validation errors

### Examples of scenarios for CRUD operations:

**GET methods**:
- Successful data retrieval
- Empty result
- Filtering and sorting
- Pagination
- Domain service errors

**UPDATE methods**:
- Full update of all fields
- Partial update
- Update with nil/empty values
- Errors at each update stage
- Errors when retrieving updated data

**DELETE methods**:
- Successful deletion
- Object not found
- Cascade deletion
- Different contexts

## ASSERTIONS IN TESTS

### Basic Assertions
```go
// Check for no error
require.NoError(t, err)

// Check for error presence
require.Error(t, err)
require.Equal(t, expectedError, err)

// Check equality
require.Equal(t, expected, actual)

// Check slice/map length
require.Len(t, slice, expectedLength)

// Check nil/not nil
require.Nil(t, value)
require.NotNil(t, value)
```

### Specific Assertions
```go
// Check struct fields
require.Equal(t, expected.ID, actual.ID)
require.Equal(t, expected.Name, actual.Name)

// Check boolean values
require.True(t, condition)
require.False(t, condition)

// Check slice contents
require.Contains(t, slice, element)
require.ElementsMatch(t, expected, actual)
```

## WORKING WITH FILTERING AND SORTING

### Important to understand the architecture:
1. **Application Service**: Gets all data from domain service, then applies filtering/sorting
2. **Domain Service**: Returns raw data

### Correct approach in tests:
```go
// Mock should return ALL data (before filtering)
allUsers := []userentity.AdminUser{
    {ID: 1, Email: "<EMAIL>", IsAdmin: true},
    {ID: 2, Email: "<EMAIL>", IsAdmin: false},
}

// Filter
search := "admin"
q := query.AdminUser{Search: &search}

// Mock returns all data
mockUser.GetAllAsAdminMock.Expect().Return(allUsers, nil)

// Check that filtering works correctly
result, err := svc.GetAllAsAdmin(q)
require.NoError(t, err)
require.Len(t, result.Items, 1) // only admin remains after filtering
require.Equal(t, "<EMAIL>", result.Items[0].Email)
```

## ANTI-PATTERNS (DON'T DO THIS)

### ❌ Wrong:
```go
// Applying filtering in test instead of checking service work
filteredUsers := []userentity.AdminUser{
    {ID: 1, Email: "<EMAIL>", IsAdmin: true},
}
mockUser.GetAllAsAdminMock.Expect().Return(filteredUsers, nil)
```

### ❌ Wrong:
```go
// Duplicating mock creation in each test
func TestSomething(t *testing.T) {
    mockGroup := groupmocks.NewGroupDomainServiceMock(t)
    mockUser := usermocks.NewUserDomainServiceMock(t)
    // ...
}
```

### ❌ Wrong:
```go
// Unclear test names
func TestGetUsers(t *testing.T) { ... }
func TestUpdate(t *testing.T) { ... }
```

### ❌ Wrong:
```go
// Missing mock call verification
mockUser.SomeMethodMock.Expect().Return(result, nil)
// ... method call ...
// No check that mock was actually called
```

## FILE ORGANIZATION

### Test File Structure:
```
service/
├── user_service.go
├── user_service_test.go
├── admin_user.go
├── admin_user_test.go
└── common_test.go  // helper functions
```

### Helper Functions:
- Place common helper functions in a separate `common_test.go` file
- Create factories for test data
- Use functions for creating mocks

## COMMENTS IN TESTS

### When to add comments:
```go
// Explaining complex logic
// Mock should return all data, filtering will happen in application service
mockUser.GetAllAsAdminMock.Expect().Return(allUsers, nil)

// Describing expected behavior
// Check that filtering works correctly:
// should remain only 1 user (admin with isAdmin=true and search="admin")
require.Len(t, result.Items, 1)
```

## TEST PERFORMANCE

1. **Use table-driven tests** for similar scenarios
2. **Avoid time.Sleep()** in tests
3. **Use parallel tests** where possible: `t.Parallel()`
4. **Don't create real connections** to DB in unit tests

## VALIDATION BEFORE COMMIT

Before committing, always:
1. Run tests: `go test ./...`
2. Check coverage: `go test -cover ./...`
3. Ensure all new methods are covered by tests
4. Verify tests pass in CI/CD

## COMPLETE TEST FILE EXAMPLE

See `internal/application/user/service/admin_user_test.go` as a reference for properly written tests.

===================================

Follow these recommendations to ensure high quality tests and project stability. 