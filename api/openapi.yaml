openapi: 3.0.4
info:
  title: PMS API
  version: v1
servers:
  - url: https://lk.ds.ecpk.sibintek.ru
  - url: https://lk.ds.ecpk-dev.sibintek.ru
  - url: http://localhost:8080
security:
  - Authorization: []
tags:
  - name: AdminGroup
    description: Operations related to groups by admin
  - name: AdminPermission
    description: Operations related to permissions by admin
  - name: AdminProduct
    description: Operations related to products by admin
  - name: AdminProposal
    description: Operations related to proposal(s) by admin
  - name: AdminRole
    description: Operations related to roles by admin
  - name: AdminUser
    description: Operations related to users by admin
  - name: Category
    description: Operations related to categories
  - name: Event
    description: Operations related to events
  - name: Keycloak
    description: Operations related to keycloak
  - name: Participant
    description: Operations related to participants
  - name: Permission
    description: Operations related to permissions
  - name: Product
    description: Operations related to products
  - name: ProductGroup
    description: Operations related to groups of products
  - name: ProductRole
    description: Operations related to roles of products
  - name: Proposal
    description: Operations related to proposals
  - name: Status
    description: Operations related to status
  - name: SystemGroup
    description: Operations related to system product group
  - name: SystemRole
    description: Operations related to system product role
  - name: User
    description: Operations related to users
x-types:
  - &proposalStatus
    name: status
    in: query
    description: Status of the proposal (draft,archive,on_approval,rejected,approved).
    schema:
      type: string
      enum:
        - draft
        - archive
        - on_approval
        - rejected
        - approved
      default: ""
  - &proposalType
    name: type
    in: query
    description: Filter by proposal type (OR,ER).
    schema:
      type: string
      enum:
        - OR
        - ER
      default: ""
  - &productID
    name: productID
    description: Product ID
    in: path
    required: true
    schema:
      type: integer
      format: int64
  - &groupID
    name: groupID
    description: Group ID
    in: path
    required: true
    schema:
      type: integer
      x-go-type: int64
  - &roleID
    name: roleID
    description: Role ID
    in: path
    required: true
    schema:
      type: integer
      x-go-type: int64
  - &categoryID
    name: categoryID
    description: Category ID
    in: path
    required: true
    schema:
      type: integer
      x-go-type: int64
  - &userID
    name: userID
    description: User ID
    in: path
    required: true
    schema:
      type: integer
      x-go-type: int64
  - &participantID
    name: participantID
    description: Participant ID
    in: path
    required: true
    schema:
      type: integer
      x-go-type: int64
  - &proposalID
    name: proposalID
    description: Proposal ID
    in: path
    required: true
    schema:
      type: integer
      x-go-type: int64
  - &productIDs
    name: productIDs
    description: Product IDs
    in: query
    schema:
      type: array
      items:
        type: integer
        format: int64
  - &groupIDs
    name: groupIDs
    description: Group IDs
    in: query
    schema:
      type: array
      items:
        type: integer
        format: int64
  - &roleIDs
    name: roleIDs
    description: Role IDs
    in: query
    schema:
      type: array
      items:
        type: integer
        format: int64
  - &categoryIDs
    name: categoryIDs
    description: Category IDs
    in: query
    schema:
      type: array
      items:
        type: integer
        format: int64
  - &status
    name: status
    in: query
    description: Status of the product.
    schema:
      type: string
      enum:
        - active
        - archive
      default: active
  - &isAdmin
    name: isAdmin
    in: query
    description: Filter by admin status.
    schema:
      type: boolean
  - &type
    name: type
    in: query
    description: Filter by type (custom or system).
    schema:
      type: string
      enum:
        - custom
        - system
      default: ""
  - &search
    name: search
    in: query
    description: data for search
    schema:
      type: string
  - &limit
    name: limit
    description: Limit the number of entities returned.
    in: query
    schema:
      type: integer
      x-go-type: int64
      default: 10
  - &offset
    name: offset
    description: Offset the number of entities returned.
    in: query
    schema:
      type: integer
      x-go-type: int64
      default: 0
  - &sortProductsAsAdmin
    name: sort
    in: query
    description: Sort the products by a field.
    schema:
      type: string
      enum:
        - techName
        - participantCount
        - owners
        - status
        - createdAt
        - lastUpdatedAt
      default: techName
  - &sortGroupsAsAdmin
    name: sort
    in: query
    description: Sort the groups by a field.
    schema:
      type: string
      enum:
        - name
        - type
        - product
        - userCount
        - roleCount
      default: name
  - &sortRolesAsAdmin
    name: sort
    in: query
    description: Sort the roles by a field.
    schema:
      type: string
      enum:
        - name
        - type
        - product
        - userCount
        - groupCount
      default: name
  - &sortProposalsAsAdmin
    name: sort
    in: query
    description: Sort proposals by a field
    schema:
      type: string
      enum:
        - proposalID
        - product
        - status
        - owners
        - createdAt
      default: createdAt
  - &sortUsersAsAdmin
    name: sort
    in: query
    description: Sort the users by a field
    schema:
      type: string
      enum:
        - fullName
        - category
        - createdAt
        - product
      default: fullName
  - &sortProducts
    name: sort
    in: query
    description: Sort the products by a field.
    schema:
      type: string
      enum:
        - techName
      default: techName
  - &sortRoles
    name: sort
    in: query
    description: Sort the roles by a field.
    schema:
      type: string
      enum:
        - roleName
        - roleType
        - product
      default: product
  - &typeEvent
    name: type
    in: query
    description: Sort the type by a schema.
    schema:
      type: string
      enum:
        - message
        - status
      default: proposalMessage
  - &order
    name: order
    in: query
    description: Order
    schema:
      type: string
      enum:
        - ascend
        - descend
      default: ascend
  - &response400
    description: Invalid request format
    content:
      application/json:
        schema:
          $ref: "#/components/schemas/ErrorResponseV1DTO"
  - &response401
    description: Unauthorized
  - &response403
    description: Forbidden
  - &response429
    description: Too many requests
  - &response500
    description: Internal server error
    content:
      application/json:
        schema:
          $ref: "#/components/schemas/ErrorResponseV1DTO"
paths:
  /v1/admin/groups:
    post:
      summary: Create group
      description: Create group
      operationId: createGroupAsAdmin
      tags:
        - AdminGroup
      requestBody:
        description: Group creation request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminGroupCreateV1DTO"
      responses:
        "200":
          description: Group successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get groups and meta
      description: Get groups and meta.
      operationId: getGroupsAsAdmin
      tags:
        - AdminGroup
      parameters:
        - *search
        - *productIDs
        - *type
        - *status
        - *limit
        - *offset
        - *sortGroupsAsAdmin
        - *order
      responses:
        "200":
          description: Groups and meta successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminGroupCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/groups/categories:
    get:
      summary: Get groups with categories visibility
      description: Get groups with categories visibility.
      operationId: getGroupsWithCategoriesVisibilityAsAdmin
      tags:
        - AdminGroup
      responses:
        "200":
          description: Groups and their categories successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GroupViewAdminEntityV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update categories visibility for groups
      description: Update categories visibility for groups.
      operationId: updateCategoriesVisibilityForGroupsAsAdmin
      tags:
        - AdminGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/GroupViewAdminEntityV1DTO"
      responses:
        "200":
          description: Permissions successfully updated
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GroupViewAdminEntityV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/groups/{groupID}:
    get:
      summary: Get group
      description: Get group
      operationId: getGroupAsAdmin
      tags:
        - AdminGroup
      parameters:
        - *groupID
      responses:
        "200":
          description: Group successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update group
      description: Update group
      operationId: updateGroupAsAdmin
      tags:
        - AdminGroup
      parameters:
        - *groupID
      requestBody:
        description: Group update request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminGroupUpdateV1DTO"
      responses:
        "200":
          description: Groups successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete group
      description: Delete group
      operationId: deleteGroupAsAdmin
      tags:
        - AdminGroup
      parameters:
        - *groupID
      responses:
        "200":
          description: The group has been successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/permissions:
    get:
      summary: Get all permissions as admin
      description: Returns all permissions as admin.
      operationId: getPermissionsAsAdmin
      tags:
        - AdminPermission
      responses:
        "200":
          description: Permissions successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PermissionWithCategoriesV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update categories permissions as admin
      description: Update categories permissions as admin.
      operationId: updateCategoriesPermissionsAsAdmin
      tags:
        - AdminPermission
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/PermissionWithCategoriesV1DTO"
      responses:
        "200":
          description: Permissions successfully updated
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PermissionWithCategoriesV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/products:
    post:
      summary: Create a new product as admin
      description: Creates a new product as admin.
      operationId: createProductAsAdmin
      tags:
        - AdminProduct
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductCreateV1DTO"
      responses:
        "200":
          description: Product successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get all products as admin
      description: Returns all products as admin.
      operationId: getProductsAsAdmin
      tags:
        - AdminProduct
      parameters:
        - *search
        - *status
        - *limit
        - *offset
        - *sortProductsAsAdmin
        - *order
      responses:
        "200":
          description: Products successfully retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: "#/components/schemas/AdminProductCollectionV1DTO"
                required:
                  - items
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/products/{productID}:
    get:
      summary: Get product by ID as admin
      description: Returns product by ID as admin.
      operationId: getProductAsAdmin
      tags:
        - AdminProduct
      parameters:
        - *productID
      responses:
        "200":
          description: Product successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update product as admin
      description: Updates a product as admin.
      operationId: updateProductAsAdmin
      tags:
        - AdminProduct
      parameters:
        - *productID
      requestBody:
        description: Product Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminProductUpdateV1DTO"
      responses:
        "200":
          description: Product successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/proposals:
    get:
      summary: Get all proposals by filters as admin
      description: Returns all proposals by filters as admin
      operationId: getProposalsAsAdmin
      tags:
        - AdminProposal
      parameters:
        - *search
        - *proposalType
        - *proposalStatus
        - *productIDs
        - *sortProposalsAsAdmin
        - *order
        - *limit
        - *offset
      responses:
        "200":
          description: Proposals successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminProposalCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/proposals/{proposalID}:
    get:
      summary: Get proposal by proposalID as admin
      description: Return proposal by proposalID as admin
      operationId: getProposalAsAdmin
      tags:
        - AdminProposal
      parameters:
        - *proposalID
      responses:
        "200":
          description: Proposal successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/roles:
    post:
      summary: Create role as admin
      description: Create role as admin.
      operationId: createRoleAsAdmin
      tags:
        - AdminRole
      requestBody:
        description: Role Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminRoleCreateV1DTO"
      responses:
        "200":
          description: Role created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get all roles as admin
      description: Returns all roles as admin.
      operationId: getRolesAsAdmin
      tags:
        - AdminRole
      parameters:
        - *search
        - *productIDs
        - *type
        - *status
        - *limit
        - *offset
        - *sortRolesAsAdmin
        - *order
      responses:
        "200":
          description: Roles successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminRoleCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/roles/categories:
    get:
      summary: Get roles with categories visibility as admin
      description: Get roles with categories visibility as admin.
      operationId: getRolesWithCategoriesVisibilityAsAdmin
      tags:
        - AdminRole
      responses:
        "200":
          description: Roles and their categories successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RoleViewAdminEntityV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update categories visibility for roles as admin
      description: Update categories visibility for roles as admin.
      operationId: updateCategoriesVisibilityForRolesAsAdmin
      tags:
        - AdminRole
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/RoleViewAdminEntityV1DTO"
      responses:
        "200":
          description: Permissions successfully updated
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RoleViewAdminEntityV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/roles/{roleID}:
    get:
      summary: Get role by id as admin
      description: Get role by id as admin.
      operationId: getRoleAsAdmin
      tags:
        - AdminRole
      parameters:
        - *roleID
      responses:
        "200":
          description: Successfully got role
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update role as admin
      description: Updates role as admin.
      operationId: updateRoleAsAdmin
      tags:
        - AdminRole
      parameters:
        - *roleID
      requestBody:
        description: Role Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminRoleUpdateV1DTO"
      responses:
        "200":
          description: Successfully updated role
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete role as admin
      description: Deletes role as admin.
      operationId: deleteRoleAsAdmin
      tags:
        - AdminRole
      parameters:
        - *roleID
      responses:
        "200":
          description: Successfully deleted role
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/users:
    get:
      summary: Get all users by filters as admin
      description: Returns all users by filters as admin
      operationId: getUsersAsAdmin
      tags:
        - AdminUser
      parameters:
        - *search
        - *productIDs
        - *categoryIDs
        - *isAdmin
        - *limit
        - *offset
        - *sortUsersAsAdmin
        - *order
      responses:
        "200":
          description: Users successfully retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: "#/components/schemas/AdminUserCollectionV1DTO"
                required:
                  - items
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/admin/users/{userID}:
    get:
      summary: Get user
      description: Returns the user with the specified ID
      operationId: getUserAsAdmin
      tags:
        - AdminUser
      parameters:
        - *userID
      responses:
        "200":
          description: User successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update user as admin
      description: Updates user as admin.
      operationId: updateUserAsAdmin
      tags:
        - AdminUser
      parameters:
        - *userID
      requestBody:
        description: User Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminUserUpdateV1DTO"
      responses:
        "200":
          description: User successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserWithDetailsV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete user as admin
      description: Deletes user as admin.
      operationId: deleteUserAsAdmin
      tags:
        - AdminUser
      parameters:
        - *userID
      responses:
        "200":
          description: User successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/categories:
    post:
      summary: Create new category
      description: Creates a new category.
      operationId: createCategory
      tags:
        - Category
      requestBody:
        description: Category Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CategoryCreateV1DTO"
      responses:
        "200":
          description: Category created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get all categories
      description: Returns all categories.
      operationId: getCategories
      tags:
        - Category
      responses:
        "200":
          description: Categories successfully found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CategoryV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/categories/{categoryID}:
    get:
      summary: Get category
      description: Returns the category with the specified ID.
      operationId: getCategory
      tags:
        - Category
      parameters:
        - *categoryID
      responses:
        "200":
          description: Category successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update category
      description: Updates a category with the specified ID.
      operationId: updateCategory
      tags:
        - Category
      parameters:
        - *categoryID
      requestBody:
        description: Category Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CategoryUpdateV1DTO"
      responses:
        "200":
          description: Category successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CategoryV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete category
      description: Deletes category.
      operationId: deleteCategory
      tags:
        - Category
      parameters:
        - *categoryID
      responses:
        "200":
          description: Category successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/categories/{categoryID}/permissions:
    get:
      summary: Get category permissions
      description: Returns the permissions for the specified category.
      operationId: getCategoryPermissions
      tags:
        - Category
      parameters:
        - *categoryID
      responses:
        "200":
          description: Permissions successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PermissionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/events:
    post:
      summary: Create new event
      description: Creates a new event.
      operationId: createEvent
      tags:
        - Event
      requestBody:
        description: Event Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EventV1DTO"
      responses:
        "200":
          description: Event successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/events/source:
    get:
      summary: Get events source
      description: Get events source.
      operationId: getEventSource
      tags:
        - Event
      responses:
        "200":
          description: Event source successfully gets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventSourceResponseItemV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/events/unread:
    get:
      summary: Get unread events
      description: Get unread events.
      operationId: getUnreadEvents
      tags:
        - Event
      responses:
        "200":
          description: Unread events successfully gets
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventUnreadResponseItemV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/groups:
    post:
      summary: Create System Group
      description: Creates system group.
      operationId: createSystemGroup
      tags:
        - SystemGroup
      requestBody:
        description: System Group Create Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GroupCreateV1DTO"
      responses:
        "200":
          description: System group successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupShortV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get System Groups
      description: Gets System groups.
      operationId: getSystemGroups
      tags:
        - SystemGroup
      responses:
        "200":
          description: Successfully got system groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GroupShortV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/groups/{groupID}:
    get:
      summary: Get system Group Roles
      description: Gets system group roles.
      operationId: getSystemGroupFull
      tags:
        - SystemGroup
      parameters:
        - *groupID
      responses:
        "200":
          description: Successfully got system group roles
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update system group
      description: Updates system group.
      operationId: updateSystemGroup
      tags:
        - SystemGroup
      parameters:
        - *groupID
      requestBody:
        description: System Group Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GroupUpdateV1DTO"
      responses:
        "200":
          description: Successfully updated system group
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete system group
      description: Deletes system group.
      operationId: deleteSystemGroup
      tags:
        - SystemGroup
      parameters:
        - *groupID
      responses:
        "200":
          description: Successfully deleted system group
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/keycloak/users:
    get:
      summary: Search users
      description: Searches for users with the specified search data.
      operationId: getKeycloakUsers
      tags:
        - Keycloak
      parameters:
        - *search
      responses:
        "200":
          description: Users successfully found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/KeycloakUserV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/permissions:
    get:
      summary: Get permissions
      description: Gets all available permissions.
      operationId: getPermissions
      tags:
        - Permission
      responses:
        "200":
          description: Successfully got permissions list
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PermissionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products:
    post:
      summary: Create new product
      description: Creates a new product.
      operationId: createProduct
      tags:
        - Product
      requestBody:
        description: Product Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductCreateV1DTO"
      responses:
        "200":
          description: Product successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get all products
      description: Returns all products.
      operationId: getProducts
      tags:
        - Product
      responses:
        "200":
          description: Products successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}:
    get:
      summary: Get product
      description: Returns the product with the specified ID.
      operationId: getProduct
      tags:
        - Product
      parameters:
        - *productID
      responses:
        "200":
          description: Product successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update product
      description: Updates a product.
      operationId: updateProduct
      tags:
        - Product
      parameters:
        - *productID
      requestBody:
        description: Product Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProductUpdateV1DTO"
      responses:
        "200":
          description: Product successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProductV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete product
      description: Deletes a product with the specified product ID.
      operationId: deleteProduct
      tags:
        - Product
      parameters:
        - *productID
      responses:
        "200":
          description: Product successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/groups:
    post:
      summary: Create Product Group
      description: Creates product group.
      operationId: createProductGroup
      tags:
        - ProductGroup
      parameters:
        - *productID
      requestBody:
        description: Product Group Create Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GroupCreateV1DTO"
      responses:
        "200":
          description: Product group successfully created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupShortV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get Product Groups
      description: Gets product groups.
      operationId: getProductGroups
      tags:
        - ProductGroup
      parameters:
        - *productID
      responses:
        "200":
          description: Successfully got product groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GroupShortV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/groups/{groupID}:
    get:
      summary: Get product group with roles and participant IDs
      description: Gets product group with roles and participant IDs.
      operationId: getProductGroupFull
      tags:
        - ProductGroup
      parameters:
        - *productID
        - *groupID
      responses:
        "200":
          description: Successfully got product product group roles
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update product group
      description: Updates product group.
      operationId: updateProductGroup
      tags:
        - ProductGroup
      parameters:
        - *productID
        - *groupID
      requestBody:
        description: Product Group Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GroupUpdateV1DTO"
      responses:
        "200":
          description: Successfully updated product group
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GroupFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete product group
      description: Deletes product group.
      operationId: deleteProductGroup
      tags:
        - ProductGroup
      parameters:
        - *productID
        - *groupID
      responses:
        "200":
          description: Successfully deleted product group
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/mypermissions:
    get:
      summary: Get user permissions
      description: Returns the permissions of the user with the specified ID.
      operationId: getParticipantPermissions
      tags:
        - Permission
      parameters:
        - *productID
      responses:
        "200":
          description: Permissions successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PermissionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/participants:
    post:
      summary: Add new participant to product
      description: Adds a new product participant.
      operationId: createParticipant
      tags:
        - Participant
      parameters:
        - *productID
      requestBody:
        description: Product Participant Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ParticipantCreateV1DTO"
      responses:
        "200":
          description: Product participant successfully added
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ParticipantV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get all product participants
      description: Returns all product participants.
      operationId: getParticipants
      tags:
        - Participant
      parameters:
        - *productID
      responses:
        "200":
          description: Product participants successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ParticipantCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/participants/{participantID}:
    get:
      summary: Get product participant
      description: Returns the product participant with the specified Product ID and User ID.
      operationId: getParticipant
      tags:
        - Participant
      parameters:
        - *productID
        - *participantID
      responses:
        "200":
          description: Product participant successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ParticipantV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete product participant
      description: Deletes a product participant with the specified Product ID and participant ID.
      operationId: deleteParticipant
      tags:
        - Participant
      parameters:
        - *productID
        - *participantID
      responses:
        "200":
          description: Product participant successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/participants/{participantID}/groups:
    patch:
      summary: Update product participant groups
      description: Updates the product participant groups with the specified Product ID and User ID.
      operationId: updateParticipantGroups
      tags:
        - Participant
      parameters:
        - *productID
        - *participantID
      requestBody:
        description: Product Participant Groups Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ParticipantGroupsUpdateV1DTO"
      responses:
        "200":
          description: Product participant groups successfully updated
          content:
            application/json:
              schema:
                type: array
                items:
                  type: integer
                  x-go-type: int64
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/participants/{participantID}/roles:
    patch:
      summary: Update product participant roles
      description: Updates the product participant roles with the specified Product ID and User ID.
      operationId: updateParticipantRoles
      tags:
        - Participant
      parameters:
        - *productID
        - *participantID
      requestBody:
        description: Product Participant Role Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ParticipantRolesUpdateV1DTO"
      responses:
        "200":
          description: Product participant roles successfully updated
          content:
            application/json:
              schema:
                type: array
                items:
                  type: integer
                  x-go-type: int64
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/proposals:
    post:
      summary: Create new proposal
      description: Creates a new proposal request form.
      operationId: createProposal
      tags:
        - Proposal
      parameters:
        - *productID
      requestBody:
        description: Proposal Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProposalCreateV1DTO"
      responses:
        "200":
          description: Proposal created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get all products proposals
      description: Returns all products proposals.
      operationId: getProposals
      tags:
        - Proposal
      parameters:
        - *productID
      responses:
        "200":
          description: Proposals successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/proposals/{proposalID}:
    get:
      summary: Get product proposal
      description: Returns the product proposal with the specified ID.
      operationId: getProposal
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
      responses:
        "200":
          description: Proposal successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update product proposal
      description: Updates a product proposal with the specified ID.
      operationId: updateProposal
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
      requestBody:
        description: Proposal Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProposalUpdateV1DTO"
      responses:
        "200":
          description: Proposal successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete proposal
      description: Delete proposal
      operationId: deleteProposal
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
      responses:
        "200":
          description: Category successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/proposals/{proposalID}/history:
    get:
      summary: Get proposal history
      description: Get proposal history
      operationId: getProposalHistory
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
        - *typeEvent
      responses:
        "200":
          description: Proposal history successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalHistoryContainerV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/proposals/{proposalID}/message:
    post:
      summary: Send proposal message
      description: Send proposal message
      operationId: sendProposalMessage
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
      requestBody:
        description: Proposal Message Send Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProposalChatMessageV1DTO"
      responses:
        "200":
          description: Proposal message successfully sent
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
  /v1/products/{productID}/proposals/{proposalID}/status:
    post:
      summary: Update proposal status
      description: Update proposal status (send to approval, reject, approve, etc.)
      operationId: updateProposalStatus
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
      requestBody:
        description: Proposal Status Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProposalStatusUpdateV1DTO"
      responses:
        "200":
          description: Proposal status successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ProposalV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/proposals/{proposalID}/view:
    put:
      summary: Mark proposal as viewed
      description: Records when a user viewed a proposal by setting the current timestamp
      operationId: markProposalAsViewed
      tags:
        - Proposal
      parameters:
        - *productID
        - *proposalID
      requestBody:
        description: Proposal Last Viewed Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ProposalLastViewedAtV1DTO"
      responses:
        "200":
          description: Proposal marked as viewed
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
  /v1/products/{productID}/roles:
    post:
      summary: Create product role
      description: Creates product role.
      operationId: createProductRole
      tags:
        - ProductRole
      parameters:
        - *productID
      requestBody:
        description: Product Role Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RoleCreateV1DTO"
      responses:
        "200":
          description: Product role created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get product roles
      description: Gets product roles.
      operationId: getProductRoles
      tags:
        - ProductRole
      parameters:
        - *productID
      responses:
        "200":
          description: Successfully got product roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RoleShortV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/products/{productID}/roles/{roleID}:
    get:
      summary: Get product role with permissions and participant IDs
      description: Gets product role with permissions and participant IDs.
      operationId: getProductRoleFull
      tags:
        - ProductRole
      parameters:
        - *productID
        - *roleID
      responses:
        "200":
          description: Successfully got product role permissions
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update product role
      description: Updates product role.
      operationId: updateProductRole
      tags:
        - ProductRole
      parameters:
        - *productID
        - *roleID
      requestBody:
        description: Product Role Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RoleUpdateV1DTO"
      responses:
        "200":
          description: Successfully updated product role
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete product role
      description: Deletes product role.
      operationId: deleteProductRole
      tags:
        - ProductRole
      parameters:
        - *productID
        - *roleID
      responses:
        "200":
          description: Product role successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/roles:
    post:
      summary: Create system role
      description: Creates system role.
      operationId: createSystemRole
      tags:
        - SystemRole
      requestBody:
        description: System Role Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RoleCreateV1DTO"
      responses:
        "200":
          description: System role created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get system roles
      description: Gets system roles.
      operationId: getSystemRoles
      tags:
        - SystemRole
      responses:
        "200":
          description: Successfully got product roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RoleShortV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/roles/{roleID}:
    get:
      summary: Get system role permissions
      description: Gets system role permissions.
      operationId: getSystemRoleFull
      tags:
        - SystemRole
      parameters:
        - *roleID
      responses:
        "200":
          description: Successfully got product role permissions
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update system role
      description: Updates system role.
      operationId: updateSystemRole
      tags:
        - SystemRole
      parameters:
        - *roleID
      requestBody:
        description: System Role Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RoleUpdateV1DTO"
      responses:
        "200":
          description: Product role successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RoleFullV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete system role
      description: Deletes system role.
      operationId: deleteSystemRole
      tags:
        - SystemRole
      parameters:
        - *roleID
      responses:
        "200":
          description: System role successfully deleted
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/status:
    get:
      summary: Get status
      description: Returns the status of the services.
      operationId: getStatus
      tags:
        - Status
      responses:
        "200":
          description: Status successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users:
    post:
      summary: Create new user
      description: Creates a new user with the specified external and internal IDs.
      operationId: createUser
      tags:
        - User
      requestBody:
        description: User Creation Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserCreateV1DTO"
      responses:
        "200":
          description: User created successfully
          content:
            application/json:
              schema:
                type: string
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    get:
      summary: Get users
      description: Returns the users.
      operationId: getUsers
      tags:
        - User
      parameters:
        - *search
        - *productIDs
        - *categoryIDs
        - *isAdmin
        - *limit
        - *offset
        - *sortUsersAsAdmin
        - *order
      responses:
        "200":
          description: Users successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users/current:
    get:
      summary: Get current user
      description: Returns current user.
      operationId: getCurrentUser
      tags:
        - User
      responses:
        "200":
          description: User successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users/mypermissions:
    get:
      summary: Get user permissions
      description: Returns the permissions of the user with the specified ID.
      operationId: getUserPermissions
      tags:
        - Permission
      responses:
        "200":
          description: Permissions successfully retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PermissionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users/{userID}:
    get:
      summary: Get user
      description: Returns the user with the specified ID.
      operationId: getUser
      tags:
        - User
      parameters:
        - *userID
      responses:
        "200":
          description: User successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    patch:
      summary: Update user
      description: Returns the user with the specified ID.
      operationId: updateUser
      tags:
        - User
      parameters:
        - *userID
      requestBody:
        description: User Update Request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserUpdateV1DTO"
      responses:
        "200":
          description: User successfully update
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users/{userID}/groups:
    get:
      summary: Get user groups with products
      description: Returns a list of groups associated with the user, including information about related products.
      operationId: getUserGroupsWithProducts
      tags:
        - User
      parameters:
        - *userID
        - *productIDs
      responses:
        "200":
          description: Successfully got user groups with products
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GroupWithProductCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete user groups
      description: Deletes the groups of the user with the specified ID.
      operationId: deleteUserGroups
      tags:
        - User
      parameters:
        - *userID
        - *groupIDs
      responses:
        "200":
          description: Specified user groups deleted successfully
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users/{userID}/products:
    get:
      summary: Get user products
      description: Returns products.
      operationId: getUserProducts
      tags:
        - User
      parameters:
        - *userID
        - *sortProducts
        - *order
      responses:
        "200":
          description: User products successfully retrieved
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserProductCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete user products
      description: Deletes the products of the user with the specified ID.
      operationId: deleteUserProducts
      tags:
        - User
      parameters:
        - *userID
        - *productIDs
      responses:
        "200":
          description: Specified user products deleted successfully
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
  /v1/users/{userID}/roles:
    get:
      summary: Get user roles with products
      description: Returns a list of roles associated with the user, including information about related products.
      operationId: getUserRolesWithProducts
      tags:
        - User
      parameters:
        - *userID
        - *productIDs
        - *type
        - *sortRoles
        - *order
      responses:
        "200":
          description: Successfully got user roles with products
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RolesWithProductsCollectionV1DTO"
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
    delete:
      summary: Delete user roles
      description: Deletes the roles of the user with the specified ID.
      operationId: deleteUserRoles
      tags:
        - User
      parameters:
        - *userID
        - *roleIDs
      responses:
        "200":
          description: Specified user roles deleted successfully
        "400": *response400
        "401": *response401
        "403": *response403
        "429": *response429
        "500": *response500
components:
  schemas:
    AdminGroupCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/AdminGroupV1DTO"
          x-order: 1
        meta:
          allOf:
            - $ref: "#/components/schemas/PaginationV1DTO"
          x-order: 2
      required:
        - items
        - meta
    AdminGroupCreateV1DTO:
      type: object
      properties:
        type:
          type: string
          enum: ["system", "custom"]
          x-order: 1
        name:
          type: string
          x-order: 2
        productID:
          type: integer
          x-go-type: int64
          x-order: 3
      required:
        - type
        - name
    AdminGroupUpdateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
        isActive:
          type: boolean
          x-order: 2
        users:
          type: array
          x-order: 3
          items:
            $ref: "#/components/schemas/UserProductLinkV1DTO"
        roles:
          type: array
          x-order: 4
          items:
            $ref: "#/components/schemas/RoleIDV1DTO"
    AdminGroupV1DTO:
      allOf:
        - $ref: "#/components/schemas/GroupBasicV1DTO"
        - type: object
          properties:
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 5
            userCount:
              type: integer
              x-go-type: int64
              x-order: 6
            roleCount:
              type: integer
              x-go-type: int64
              x-order: 7
          required:
            - roleCount
            - userCount
    AdminProductCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/AdminProductV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/PaginationV1DTO"
          x-order: 2
      required:
        - items
        - meta
    AdminProductUpdateV1DTO:
      type: object
      properties:
        iid:
          type: string
          x-go-name: IID
          x-order: 1
        techName:
          type: string
          x-order: 2
        name:
          type: string
          x-order: 3
        desc:
          type: string
          x-order: 4
        status:
          type: string
          x-order: 5
          enum:
            - active
            - archive
          default: active
        ownerEmails:
          type: array
          x-order: 6
          items:
            type: string
        roleIDs:
          type: array
          x-order: 7
          items:
            type: integer
            x-go-type: int64
        groupIDs:
          type: array
          x-order: 8
          items:
            type: integer
            x-go-type: int64
        participantIDs:
          type: array
          x-order: 9
          items:
            type: string
    AdminProductV1DTO:
      allOf:
        - $ref: "#/components/schemas/ProductV1DTO"
        - type: object
          properties:
            status:
              type: string
              enum:
                - active
                - archive
              x-order: 10
            participantCount:
              type: integer
              x-go-type: int64
              x-order: 11
            owners:
              type: array
              x-order: 12
              items:
                $ref: "#/components/schemas/OwnerV1DTO"
          required:
            - status
            - participantCount
            - owners
    AdminProposalCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/AdminProposalV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/PaginationV1DTO"
          x-order: 2
      required:
        - items
        - meta
    AdminProposalV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        propSeq:
          type: integer
          x-go-type: int64
          x-go-name: PropSeq
          x-order: 2
        productID:
          type: integer
          x-go-type: int64
          x-go-name: ProductID
          x-order: 3
        price:
          type: integer
          x-go-type: float64
          x-order: 4
        type:
          type: string
          x-order: 5
        status:
          type: string
          x-order: 6
        creatorID:
          type: integer
          x-go-type: int64
          x-order: 7
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 8
        updatedAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 9
        proposalNumber:
          type: string
          x-order: 10
        data:
          type: object
          x-order: 11
        productOwners:
          type: array
          items:
            type: string
          x-order: 12
        product:
          allOf:
            - $ref: "#/components/schemas/ProductBasicV1DTO"
          x-order: 13
      required:
        - id
        - propSeq
        - productID
        - price
        - type
        - status
        - creatorID
        - createdAt
        - updatedAt
        - proposalNumber
        - data
    AdminRoleCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/AdminRoleV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/PaginationV1DTO"
          x-order: 2
      required:
        - items
        - meta
    AdminRoleCreateV1DTO:
      type: object
      properties:
        type:
          type: string
          enum: ["system", "custom"]
          x-order: 1
        name:
          type: string
          x-order: 2
        productID:
          type: integer
          x-go-type: int64
          x-order: 3
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/PermissionV1DTO"
          x-order: 4
      required:
        - type
        - name
        - permissions
    AdminRoleUpdateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
        isActive:
          type: boolean
          x-order: 2
        users:
          type: array
          x-order: 3
          items:
            $ref: "#/components/schemas/UserProductLinkV1DTO"
        groups:
          type: array
          x-order: 4
          items:
            $ref: "#/components/schemas/GroupIDV1DTO"
        permissions:
          type: array
          x-order: 5
          items:
            $ref: "#/components/schemas/PermissionV1DTO"
    AdminRoleV1DTO:
      allOf:
        - $ref: "#/components/schemas/RoleBasicV1DTO"
        - type: object
          properties:
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 5
            userCount:
              type: integer
              x-go-type: int64
              x-order: 6
            groupCount:
              type: integer
              x-go-type: int64
              x-order: 7
          required:
            - userCount
            - groupCount
    AdminUserCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/AdminUserV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/PaginationV1DTO"
          x-order: 2
      required:
        - items
        - meta
    AdminUserUpdateV1DTO:
      type: object
      properties:
        isAdmin:
          type: boolean
          x-order: 1
        categoryID:
          type: integer
          x-go-type: int64
          x-order: 2
        products:
          type: array
          x-order: 3
          items:
            $ref: "#/components/schemas/ProductIDV1DTO"
        roles:
          type: array
          x-order: 4
          items:
            $ref: "#/components/schemas/RoleProductLinkV1DTO"
        groups:
          type: array
          x-order: 5
          items:
            $ref: "#/components/schemas/GroupProductLinkV1DTO"
    AdminUserV1DTO:
      allOf:
        - $ref: "#/components/schemas/UserBasicV1DTO"
        - type: object
          properties:
            category:
              type: integer
              x-go-type: int64
              x-order: 10
            position:
              type: string
              x-order: 11
            isAdmin:
              type: boolean
              x-order: 12
            lastLoginAt:
              type: string
              format: date-time
              x-go-type: time.Time
              x-order: 13
            createdAt:
              type: string
              format: date-time
              x-go-type: time.Time
              x-order: 14
            products:
              type: array
              items:
                $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 15
          required:
            - category
            - position
            - isAdmin
            - createdAt
    CategoryCreateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
      required:
        - name
    CategoryUpdateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
      required:
        - name
    CategoryV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
      required:
        - id
        - name
    CategoryWithCheckedV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        isActive:
          type: boolean
          x-order: 3
      required:
        - id
        - name
        - isActive
    ErrorResponseV1DTO:
      type: object
      properties:
        status:
          type: integer
          x-order: 1
        code:
          type: string
          x-order: 2
        message:
          type: string
          x-order: 3
        objectType:
          type: string
          x-order: 4
        objectID:
          type: string
          x-order: 5
        state:
          type: string
          x-order: 6
      required:
        - status
        - code
        - message
        - objectType
        - objectID
        - state
    EventSourceResponseItemV1DTO:
      type: object
      properties:
        type:
          type: string
          x-order: 1
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 2
        message:
          type: string
          x-order: 3
        status:
          allOf:
            - $ref: "#/components/schemas/EventStatusV1DTO"
          x-order: 4
        meta:
          type: object
      required:
        - type
        - createdAt
        - meta
    EventStatusV1DTO:
      type: string
      enum: ["default", "success", "error", "warning"]
    EventUnreadResponseItemV1DTO:
      type: array
      items:
        $ref: "#/components/schemas/EventSourceResponseItemV1DTO"
      x-order: 1
      required:
        - items
    EventV1DTO:
      type: object
      properties:
        event:
          type: object
          x-order: 1
      required:
        - event
    GroupBasicV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        isActive:
          type: boolean
          x-order: 4
      required:
        - id
        - name
        - type
        - isActive
    GroupCreateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
      required:
        - name
    GroupFullV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        participantIDs:
          type: array
          x-order: 4
          items:
            type: integer
            x-go-type: int64
        userIDs:
          type: array
          x-order: 5
          items:
            type: integer
            x-go-type: int64
        roleIDs:
          type: array
          x-order: 6
          items:
            type: integer
            x-go-type: int64
      required:
        - id
        - name
        - type
    GroupIDV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
      required:
        - id
    GroupProductLinkV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        productID:
          type: integer
          x-go-type: int64
          x-go-name: ProductID
          x-order: 2
      required:
        - id
    GroupShortV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        roleCount:
          type: integer
          x-go-type: int64
          x-order: 4
        participantCount:
          type: integer
          x-go-type: int64
          x-order: 5
        userCount:
          type: integer
          x-go-type: int64
          x-order: 6
      required:
        - id
        - name
        - type
        - roleCount
        - participantCount
        - userCount
    GroupUpdateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
        type:
          type: string
          x-order: 2
        participantIDs:
          type: array
          x-order: 3
          items:
            type: integer
            x-go-type: int64
        roleIDs:
          type: array
          x-order: 4
          items:
            type: integer
            x-go-type: int64
    GroupViewAdminEntityV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        roleCount:
          type: integer
          x-go-type: int64
          x-order: 4
        participantCount:
          type: integer
          x-go-type: int64
          x-order: 5
        userCount:
          type: integer
          x-go-type: int64
          x-order: 6
        categories:
          type: array
          x-order: 7
          items:
            $ref: "#/components/schemas/CategoryWithCheckedV1DTO"
      required:
        - id
        - name
        - type
        - roleCount
        - participantCount
        - userCount
        - categories
    GroupWithCountsV1DTO:
      allOf:
        - $ref: "#/components/schemas/GroupBasicV1DTO"
        - type: object
          properties:
            participantCount:
              type: integer
              x-go-type: int64
              x-order: 15
            roleCount:
              type: integer
              x-go-type: int64
              x-order: 16
          required:
            - participantCount
            - roleCount
    GroupWithDetailsV1DTO:
      allOf:
        - $ref: "#/components/schemas/GroupBasicV1DTO"
        - type: object
          properties:
            createdAt:
              type: string
              format: date-time
              x-go-type: time.Time
              x-order: 5
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 6
            users:
              type: array
              items:
                $ref: "#/components/schemas/UserWithProductV1DTO"
              x-order: 7
            roles:
              type: array
              items:
                $ref: "#/components/schemas/RoleWithProductV1DTO"
              x-order: 8
          required:
            - createdAt
            - users
            - roles
    GroupWithProductCollectionV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        product:
          allOf:
            - $ref: "#/components/schemas/ProductBasicV1DTO"
          x-order: 4
      required:
        - id
        - name
        - type
    GroupWithProductIDV1DTO:
      allOf:
        - $ref: "#/components/schemas/GroupBasicV1DTO"
        - type: object
          properties:
            productID:
              type: integer
              x-go-type: int64
              x-order: 5
    GroupWithProductV1DTO:
      allOf:
        - $ref: "#/components/schemas/GroupBasicV1DTO"
        - type: object
          properties:
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 8
    KeycloakUserV1DTO:
      type: object
      properties:
        id:
          type: string
          x-go-name: ID
          x-order: 1
        email:
          type: string
          x-order: 2
        username:
          type: string
          x-order: 3
        fullName:
          type: string
          x-go-name: FullName
          x-order: 4
        lastName:
          type: string
          x-go-name: LastName
          x-order: 5
        firstName:
          type: string
          x-go-name: FirstName
          x-order: 6
        position:
          type: string
          x-order: 7
        enabled:
          type: boolean
          x-order: 8
        photo:
          type: string
          x-order: 9
      required:
        - id
        - email
        - username
        - fullName
        - lastName
        - firstName
        - position
        - enabled
        - photo
    MethodV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
      required:
        - name
    OwnerV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        email:
          type: string
          x-order: 2
        fullName:
          type: string
          x-go-name: FullName
          x-order: 3
      required:
        - id
        - email
        - fullName
    PaginationV1DTO:
      type: object
      properties:
        limit:
          type: integer
          x-go-type: int64
          x-order: 1
        offset:
          type: integer
          x-go-type: int64
          x-order: 2
        total:
          type: integer
          x-go-type: int64
          x-order: 3
      required:
        - limit
        - offset
        - total
    ParticipantCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/ParticipantV1DTO"
      required:
        - items
    ParticipantCreateV1DTO:
      type: object
      properties:
        email:
          type: string
          x-order: 1
      required:
        - email
    ParticipantGroupsUpdateV1DTO:
      type: object
      properties:
        groupIDs:
          type: array
          items:
            type: integer
            x-go-type: int64
    ParticipantRolesUpdateV1DTO:
      type: object
      properties:
        roleIDs:
          type: array
          items:
            type: integer
            x-go-type: int64
    ParticipantShortV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        userID:
          type: integer
          x-go-type: int64
          x-order: 2
        email:
          type: string
          x-order: 3
        fullName:
          type: string
          x-order: 4
        isOwner:
          type: boolean
          x-order: 5
        productID:
          type: integer
          x-go-type: int64
          x-order: 6
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 7
        updatedAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 8
      required:
        - id
        - userID
        - email
        - fullName
        - isOwner
        - productID
        - createdAt
        - updatedAt
    ParticipantV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        productID:
          type: integer
          x-go-type: int64
          x-order: 2
        userID:
          type: integer
          x-go-type: int64
          x-order: 3
        email:
          type: string
          x-order: 4
        fullName:
          type: string
          x-order: 5
        position:
          type: string
          x-order: 6
        isOwner:
          type: boolean
          x-order: 7
        groupIDs:
          type: array
          items:
            type: integer
            x-go-type: int64
          x-order: 8
        roleIDs:
          type: array
          items:
            type: integer
            x-go-type: int64
          x-order: 9
        lastLoginAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 10
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 11
        updatedAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 12
      required:
        - id
        - productID
        - userID
        - email
        - fullName
        - position
        - isOwner
        - groupIDs
        - roleIDs
        - createdAt
        - updatedAt
    PermissionV1DTO:
      type: object
      properties:
        label:
          type: string
          x-order: 1
        name:
          type: string
          x-order: 2
        methods:
          type: array
          x-order: 3
          items:
            $ref: "#/components/schemas/MethodV1DTO"
      required:
        - label
        - name
        - methods
    PermissionWithCategoriesV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
        categories:
          type: array
          x-order: 2
          items:
            $ref: "#/components/schemas/CategoryWithCheckedV1DTO"
      required:
        - name
        - categories
    ProductBasicV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        iid:
          type: string
          x-go-name: IID
          x-order: 2
        techName:
          type: string
          x-order: 3
        name:
          type: string
          x-order: 4
      required:
        - id
        - techName
        - name
    ProductCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/ProductV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/ProductMetaV1DTO"
          x-order: 2
      required:
        - items
        - meta
    ProductCreateV1DTO:
      type: object
      properties:
        iid:
          type: string
          x-go-name: IID
          x-order: 1
        techName:
          type: string
          x-order: 2
        name:
          type: string
          x-order: 3
        ownerEmails:
          type: array
          x-order: 4
          items:
            type: string
      required:
        - iid
        - techName
        - name
        - ownerEmails
    ProductIDV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
      required:
        - id
    ProductMetaV1DTO:
      type: object
      properties:
        activeProductID:
          type: integer
          x-go-type: int64
      required:
        - activeProductID
    ProductUpdateV1DTO:
      type: object
      properties:
        iid:
          type: string
          x-go-name: IID
          x-order: 1
        techName:
          type: string
          x-order: 2
        name:
          type: string
          x-order: 3
        desc:
          type: string
          x-order: 4
    ProductV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-name: ID
          x-go-type: int64
          x-order: 1
        iid:
          type: string
          x-go-name: IID
          x-order: 2
        techName:
          type: string
          x-order: 3
        name:
          type: string
          x-order: 4
        desc:
          type: string
          x-order: 5
        creatorID:
          type: integer
          x-go-type: int64
          x-go-name: CreatorID
          x-order: 6
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 7
        updatedAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 8
      required:
        - id
        - iid
        - techName
        - name
        - desc
        - creatorID
        - createdAt
        - updatedAt
    ProductWithDetailsV1DTO:
      allOf:
        - $ref: "#/components/schemas/AdminProductV1DTO"
        - type: object
          properties:
            participants:
              type: array
              x-order: 20
              items:
                $ref: "#/components/schemas/ParticipantShortV1DTO"
            roles:
              type: array
              x-order: 21
              items:
                $ref: "#/components/schemas/RoleWithCountsV1DTO"
            groups:
              type: array
              x-order: 22
              items:
                $ref: "#/components/schemas/GroupWithCountsV1DTO"
          required:
            - participants
            - roles
            - groups
    ProposalChatMessageV1DTO:
      type: object
      properties:
        message:
          type: string
          x-order: 1
      required:
        - message
    ProposalCreateV1DTO:
      type: object
      properties:
        price:
          type: integer
          x-go-type: float64
          x-order: 1
        type:
          type: string
          x-order: 2
        status:
          type: string
          x-order: 3
        data:
          type: object
          x-order: 4
      required:
        - price
        - data
    ProposalHistoryContainerV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            oneOf:
              - $ref: "#/components/schemas/ProposalHistoryMessageEventV1DTO"
              - $ref: "#/components/schemas/ProposalHistoryStatusEventV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/ProposalLastViewedAtV1DTO"
          x-order: 2
      required:
        - items
        - meta
    ProposalHistoryEventStatusV1DTO:
      allOf:
        - $ref: "#/components/schemas/ProposalStatusV1DTO"
        - type: string
    ProposalHistoryMessageEventV1DTO:
      type: object
      properties:
        type:
          type: string
          enum: ["message"]
          x-order: 1
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 2
        message:
          type: string
          x-order: 3
        status:
          allOf:
            - $ref: "#/components/schemas/ProposalHistoryEventStatusV1DTO"
          x-order: 4
        meta:
          allOf:
            - $ref: "#/components/schemas/ProposalHistoryMessageMetaV1DTO"
          x-order: 4
      required:
        - type
        - createdAt
        - meta
    ProposalHistoryMessageMetaV1DTO:
      type: object
      properties:
        userID:
          type: integer
          x-order: 1
          x-go-type: int64
      required:
        - userID
    ProposalHistoryStatusEventV1DTO:
      type: object
      properties:
        type:
          type: string
          enum: ["status"]
          x-order: 1
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 2
        message:
          type: string
          x-order: 3
        status:
          allOf:
            - $ref: "#/components/schemas/ProposalHistoryEventStatusV1DTO"
          x-order: 4
        meta:
          allOf:
            - $ref: "#/components/schemas/ProposalHistoryStatusMetaV1DTO"
          x-order: 5
      required:
        - type
        - createdAt
        - meta
    ProposalHistoryStatusMetaV1DTO:
      type: object
      properties:
        status:
          allOf:
            - $ref: "#/components/schemas/ProposalStatusV1DTO"
          x-order: 1
      required:
        - status
    ProposalHistoryV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        status:
          type: string
          x-order: 2
        updatedAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 3
        message:
          type: string
          x-order: 4
      required:
        - id
        - status
        - updatedAt
    ProposalLastViewedAtV1DTO:
      type: object
      properties:
        lastViewedAt:
          type: string
          format: date-time
          x-go-type: time.Time
      required:
        - lastViewedAt
    ProposalMessageEventV1DTO:
      type: object
      properties:
        type:
          type: string
          enum: ["proposalMessage"]
          x-order: 1
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 2
        message:
          type: string
          x-order: 3
        status:
          allOf:
            - $ref: "#/components/schemas/EventStatusV1DTO"
          x-order: 4
        meta:
          allOf:
            - $ref: "#/components/schemas/ProposalMessageMetaV1DTO"
          x-order: 5
      required:
        - type
        - createdAt
        - meta
    ProposalMessageMetaV1DTO:
      type: object
      properties:
        productID:
          type: integer
          x-order: 1
          x-go-type: int64
        proposalID:
          type: integer
          x-order: 2
          x-go-type: int64
        userID:
          type: integer
          x-order: 3
          x-go-type: int64
      required:
        - productID
        - proposalID
        - userID
    ProposalStatusEventV1DTO:
      type: object
      properties:
        type:
          type: string
          enum: ["proposalStatus"]
          x-order: 1
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 2
        message:
          type: string
          x-order: 3
        status:
          allOf:
            - $ref: "#/components/schemas/EventStatusV1DTO"
          x-order: 4
        meta:
          allOf:
            - $ref: "#/components/schemas/ProposalStatusMetaV1DTO"
          x-order: 5
      required:
        - type
        - createdAt
        - meta
    ProposalStatusMetaV1DTO:
      type: object
      properties:
        productID:
          type: integer
          x-order: 1
          x-go-type: int64
        proposalID:
          type: integer
          x-order: 2
          x-go-type: int64
        status:
          allOf:
            - $ref: "#/components/schemas/ProposalStatusV1DTO"
          x-order: 3
      required:
        - productID
        - proposalID
        - status
    ProposalStatusUpdateV1DTO:
      type: object
      properties:
        status:
          type: string
          x-order: 1
        message:
          type: string
          x-order: 2
      required:
        - status
    ProposalStatusV1DTO:
      type: string
      enum: ["draft", "archive", "on_approval", "rejected", "approved"]
    ProposalUpdateV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        productID:
          type: integer
          x-go-type: int64
          x-go-name: ProductID
          x-order: 2
        propSeq:
          type: integer
          x-go-type: int64
          x-go-name: PropSeq
          x-order: 3
        price:
          type: integer
          x-go-type: float64
          x-order: 4
        type:
          type: string
          x-order: 5
        status:
          type: string
          x-order: 6
        data:
          type: object
          x-order: 7
      required:
        - id
        - productID
        - propSeq
        - price
        - status
        - data
    ProposalV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        propSeq:
          type: integer
          x-go-type: int64
          x-go-name: PropSeq
          x-order: 2
        productID:
          type: integer
          x-go-type: int64
          x-go-name: ProductID
          x-order: 3
        price:
          type: integer
          x-go-type: float64
          x-order: 4
        type:
          type: string
          x-order: 5
        status:
          type: string
          x-order: 6
        creatorID:
          type: integer
          x-go-type: int64
          x-order: 7
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 8
        updatedAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 9
        proposalNumber:
          type: string
          x-order: 10
        data:
          type: object
          x-order: 11
      required:
        - id
        - propSeq
        - productID
        - price
        - type
        - status
        - creatorID
        - createdAt
        - updatedAt
        - data
    RoleBasicV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        isActive:
          type: boolean
          x-order: 4
      required:
        - id
        - name
        - type
        - isActive
    RoleCreateV1DTO:
      type: object
      properties:
        type:
          type: string
          x-order: 1
        name:
          type: string
          x-order: 2
        permissions:
          type: array
          x-order: 3
          items:
            $ref: "#/components/schemas/PermissionV1DTO"
      required:
        - type
        - name
        - permissions
    RoleFullV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        participantIDs:
          type: array
          x-order: 4
          items:
            type: integer
            x-go-type: int64
        groupIDs:
          type: array
          x-order: 5
          items:
            type: integer
            x-go-type: int64
        userIDs:
          type: array
          x-order: 6
          items:
            type: integer
            x-go-type: int64
        permissions:
          type: array
          x-order: 7
          items:
            $ref: "#/components/schemas/PermissionV1DTO"
      required:
        - id
        - name
        - type
        - permissions
    RoleIDV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
      required:
        - id
    RoleProductLinkV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        productID:
          type: integer
          x-go-type: int64
          x-order: 2
      required:
        - id
    RoleShortV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        groupCount:
          type: integer
          x-go-type: int64
          x-order: 4
        participantCount:
          type: integer
          x-go-type: int64
          x-order: 5
        userCount:
          type: integer
          x-go-type: int64
          x-order: 6
      required:
        - id
        - name
        - type
        - groupCount
        - participantCount
        - userCount
    RoleUpdateV1DTO:
      type: object
      properties:
        name:
          type: string
          x-order: 1
        type:
          type: string
          x-order: 2
        participantIDs:
          type: array
          x-order: 3
          items:
            type: integer
            x-go-type: int64
        groupIDs:
          type: array
          x-order: 4
          items:
            type: integer
            x-go-type: int64
        userIDs:
          type: array
          x-order: 5
          items:
            type: integer
            x-go-type: int64
        permissions:
          type: array
          x-order: 6
          items:
            $ref: "#/components/schemas/PermissionV1DTO"
    RoleViewAdminEntityV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        name:
          type: string
          x-order: 2
        type:
          type: string
          x-order: 3
        groupCount:
          type: integer
          x-go-type: int64
          x-order: 4
        participantCount:
          type: integer
          x-go-type: int64
          x-order: 5
        userCount:
          type: integer
          x-go-type: int64
          x-order: 6
        categories:
          type: array
          x-order: 7
          items:
            $ref: "#/components/schemas/CategoryWithCheckedV1DTO"
      required:
        - id
        - name
        - type
        - groupCount
        - participantCount
        - userCount
        - categories
    RoleWithCountsV1DTO:
      allOf:
        - $ref: "#/components/schemas/RoleBasicV1DTO"
        - type: object
          properties:
            participantCount:
              type: integer
              x-go-type: int64
              x-order: 15
            groupCount:
              type: integer
              x-go-type: int64
              x-order: 16
          required:
            - participantCount
            - groupCount
    RoleWithDetailsV1DTO:
      allOf:
        - $ref: "#/components/schemas/RoleBasicV1DTO"
        - type: object
          properties:
            createdAt:
              type: string
              format: date-time
              x-go-type: time.Time
              x-order: 15
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 16
            users:
              type: array
              items:
                $ref: "#/components/schemas/UserWithProductV1DTO"
              x-order: 17
            groups:
              type: array
              items:
                $ref: "#/components/schemas/GroupWithProductV1DTO"
              x-order: 18
            permissions:
              type: array
              items:
                $ref: "#/components/schemas/PermissionV1DTO"
              x-order: 19
          required:
            - createdAt
            - users
            - groups
            - permissions
    RoleWithProductIDV1DTO:
      allOf:
        - $ref: "#/components/schemas/RoleBasicV1DTO"
        - type: object
          properties:
            productID:
              type: integer
              x-go-type: int64
              x-go-name: ProductID
              x-order: 4
    RoleWithProductV1DTO:
      allOf:
        - $ref: "#/components/schemas/RoleBasicV1DTO"
        - type: object
          properties:
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 4
    RolesWithProductsCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/RoleWithProductV1DTO"
      required:
        - items
    StatusV1DTO:
      type: object
      properties:
        pms:
          type: object
          x-order: 1
          properties:
            status:
              type: string
              x-order: 1
            version:
              type: string
              x-order: 2
          required:
            - status
            - version
        services:
          type: array
          x-order: 2
          items:
            type: object
            properties:
              type:
                type: string
                x-order: 1
              name:
                type: string
                x-order: 2
              status:
                type: string
                x-order: 4
              version:
                type: string
                x-order: 3
            required:
              - type
              - name
              - status
              - version
      required:
        - pms
        - services
    UserBasicV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        email:
          type: string
          x-order: 2
        fullName:
          type: string
          x-order: 3
      required:
        - id
        - email
        - fullName
    UserCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/UserV1DTO"
        meta:
          allOf:
            - $ref: "#/components/schemas/PaginationV1DTO"
          x-order: 2
      required:
        - items
        - meta
    UserCreateV1DTO:
      type: object
      properties:
        email:
          type: string
          x-order: 1
        fullName:
          type: string
          x-order: 2
        photo:
          type: string
          x-order: 3
      required:
        - email
        - fullName
        - photo
    UserProductCollectionV1DTO:
      type: object
      properties:
        items:
          type: array
          x-order: 1
          items:
            $ref: "#/components/schemas/ProductBasicV1DTO"
      required:
        - items
    UserProductLinkV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        productID:
          type: integer
          x-go-type: int64
          x-go-name: ProductID
          x-order: 2
      required:
        - id
    UserUpdateV1DTO:
      type: object
      properties:
        categoryID:
          type: integer
          x-go-type: int64
          x-order: 1
          minItems: 1
          minimum: 1
        isAdmin:
          type: boolean
          x-order: 2
        roleIDs:
          type: array
          x-order: 3
          minItems: 1
          items:
            type: integer
            x-go-type: int64
            minimum: 1
        groupIDs:
          type: array
          x-order: 4
          minItems: 1
          items:
            type: integer
            x-go-type: int64
            minimum: 1
        productIDs:
          type: array
          x-order: 5
          minItems: 1
          items:
            type: integer
            x-go-type: int64
            minimum: 1
    UserV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-name: ID
          x-go-type: int64
          x-order: 1
        category:
          type: integer
          x-go-type: int64
          x-order: 2
        email:
          type: string
          x-order: 3
        fullName:
          type: string
          x-order: 4
        position:
          type: string
          x-order: 5
        isAdmin:
          type: boolean
          x-order: 6
        lastLoginAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 7
        photo:
          type: string
          x-order: 8
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 9
        products:
          type: array
          items:
            $ref: "#/components/schemas/ProductBasicV1DTO"
          x-order: 10
      required:
        - id
        - category
        - email
        - fullName
        - position
        - isAdmin
        - photo
        - createdAt
    UserWithDetailsV1DTO:
      type: object
      properties:
        id:
          type: integer
          x-go-type: int64
          x-go-name: ID
          x-order: 1
        email:
          type: string
          x-order: 2
        fullName:
          type: string
          x-order: 3
        category:
          type: integer
          x-go-type: int64
          x-order: 4
        position:
          type: string
          x-order: 5
        isAdmin:
          type: boolean
          x-order: 6
        lastLoginAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 7
        createdAt:
          type: string
          format: date-time
          x-go-type: time.Time
          x-order: 8
        photo:
          type: string
          x-order: 9
        products:
          type: array
          items:
            $ref: "#/components/schemas/ProductBasicV1DTO"
          x-order: 10
        roles:
          type: array
          items:
            $ref: "#/components/schemas/RoleWithProductIDV1DTO"
          x-order: 11
        groups:
          type: array
          items:
            $ref: "#/components/schemas/GroupWithProductIDV1DTO"
          x-order: 12
      required:
        - id
        - email
        - fullName
        - category
        - position
        - isAdmin
        - photo
        - createdAt
        - products
        - roles
        - groups
    UserWithProductV1DTO:
      allOf:
        - $ref: "#/components/schemas/UserBasicV1DTO"
        - type: object
          properties:
            product:
              allOf:
                - $ref: "#/components/schemas/ProductBasicV1DTO"
              x-order: 11
  securitySchemes:
    Authorization:
      type: http
      scheme: bearer
      bearerFormat: JWT
