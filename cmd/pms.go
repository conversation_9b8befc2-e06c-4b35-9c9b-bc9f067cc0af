package main

import (
	"context"
	"os"
	"syscall"
	"time"

	appctx "github.com/nixys/nxs-go-appctx/v3"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
	apiserver "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/routines/api-server"
)

//go:generate go tool oapi-codegen --config=../api/admingroup.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/adminpermission.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/adminproduct.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/adminproposal.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/adminrole.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/adminuser.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/category.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/event.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/keycloak.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/participant.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/permission.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/product.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/productgroup.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/productrole.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/proposal.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/status.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/systemgroup.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/systemrole.cfg.yaml ../api/openapi.yaml
//go:generate go tool oapi-codegen --config=../api/user.cfg.yaml ../api/openapi.yaml

//go:generate go tool sqlc generate -f ../sqlc.yaml
//go:generate go tool sqlc vet -f ../sqlc.yaml

func main() {
	serverStartTime := time.Now()
	globalCtx := context.Background()
	err := appctx.Init(globalCtx).
		RoutinesSet(
			map[string]appctx.RoutineParam{
				"api-server": {
					Handler: apiserver.Runtime,
				},
			},
		).
		ValueInitHandlerSet(func() (any, error) {
			return ctx.InitAppCtx(globalCtx, serverStartTime)
		}).
		SignalsSet([]appctx.SignalsParam{
			{
				Signals: []os.Signal{
					syscall.SIGTERM,
				},
				Handler: func(sig appctx.Signal) {
					sig.Shutdown(nil)
				},
			},
		}).
		Run()
	if err != nil {
		switch {
		case errkit.IsArgSuccessExitError(err):
			os.Exit(0)
		default:
			os.Exit(1)
		}
	}
}
