package ctx

import (
	"fmt"
	"time"

	conf "github.com/nixys/nxs-go-conf"
)

type confOpts struct {
	LogFile  string `conf:"logfile" conf_extraopts:"default=stdout" json:"log_file,omitempty"`
	LogLevel string `conf:"loglevel" conf_extraopts:"default=info" json:"log_level,omitempty"`
	PidFile  string `conf:"pidfile" json:"pid_file,omitempty"`

	API      apiConf      `conf:"api" conf_extraopts:"required"`
	Redis    redisConf    `conf:"redis" conf_extraopts:"required" json:"redis"`
	PgSQL    pgSQLConf    `conf:"pgSQL" conf_extraopts:"required" json:"pg_sql"`
	Keycloak keycloakConf `conf:"keycloak" conf_extraopts:"required" json:"keycloak"`
}

type apiConf struct {
	Bind              string        `conf:"bind" conf_extraopts:"default=0.0.0.0:8080"`
	TLS               *tlsConf      `conf:"tls"`
	ClientMaxBodySize string        `conf:"clientMaxBodySize" conf_extraopts:"default=1m"`
	CORS              corsConf      `conf:"cors" conf_extraopts:"required"`
	RateLimit         rateLimitConf `conf:"rateLimit"`
}

type rateLimitConf struct {
	PerIPLimit   int  `conf:"perIPLimit" conf_extraopts:"default=100"`
	PerIPWindow  int  `conf:"perIPWindow" conf_extraopts:"default=1"`
	GlobalLimit  int  `conf:"globalLimit" conf_extraopts:"default=5000"`
	GlobalWindow int  `conf:"globalWindow" conf_extraopts:"default=1"`
	BurstLimit   int  `conf:"burstLimit" conf_extraopts:"default=30"`
	BurstWindow  int  `conf:"burstWindow" conf_extraopts:"default=10"`
	Enabled      bool `conf:"enabled" conf_extraopts:"default=true"`
}

type tlsConf struct {
	CertFile string `conf:"certfile"`
	KeyFile  string `conf:"keyfile"`
}

type corsConf struct {
	AllowOrigins []string `conf:"allowOrigins" conf_extraopts:"required"`
}

type redisConf struct {
	Host         string        `conf:"host" conf_extraopts:"required"`
	Port         int           `conf:"port" conf_extraopts:"required"`
	Password     string        `conf:"password"`
	DB           int           `conf:"db" conf_extraopts:"default=0"`
	PoolSize     int           `conf:"poolSize" conf_extraopts:"default=10"`
	DialTimeout  time.Duration `conf:"dialTimeout" conf_extraopts:"default=5"`
	PoolTimeout  time.Duration `conf:"poolTimeout" conf_extraopts:"default=5"`
	ReadTimeout  time.Duration `conf:"readTimeout" conf_extraopts:"default=2"`
	WriteTimeout time.Duration `conf:"writeTimeout" conf_extraopts:"default=2"`
}

type pgSQLConf struct {
	Host     string `conf:"host" conf_extraopts:"required"`
	Port     int    `conf:"port" conf_extraopts:"required"`
	DB       string `conf:"db" conf_extraopts:"required"`
	User     string `conf:"user" conf_extraopts:"required"`
	Password string `conf:"password" conf_extraopts:"required"`
	SSLMode  string `conf:"sslmode" conf_extraopts:"default=disable"`
}

type keycloakConf struct {
	URL                string `conf:"url" conf_extraopts:"required"`
	Realm              string `conf:"realm" conf_extraopts:"required"`
	ClientID           string `conf:"clientID" conf_extraopts:"required"`
	Username           string `conf:"username" conf_extraopts:"required"`
	Password           string `conf:"password" conf_extraopts:"required"`
	GrantType          string `conf:"grantType" conf_extraopts:"default=password"`
	SearchLimit        int    `conf:"searchLimit" conf_extraopts:"default=120"`
	InsecureSkipVerify bool   `conf:"insecureSkipVerify" conf_extraopts:"default=false"`
}

func readConf(confPath string) (confOpts, error) {

	const op = "ctx.readConf"

	var c confOpts

	err := conf.Load(&c, conf.Settings{
		ConfPath:    confPath,
		ConfType:    conf.ConfigTypeYAML,
		UnknownDeny: true,
	})
	if err != nil {
		return c, fmt.Errorf("%s: %w", op, err)
	}

	return c, nil
}
