package ctx

import (
	"fmt"
	"os"

	"github.com/pborman/getopt/v2"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

const (
	confPathDefault = "./configs/pms.conf"
)

// Args contains arguments value read from command line
type Args struct {
	ConfigPath string
}

var version string

// ReadArgs reads arguments from command line
func ReadArgs() (Args, error) {

	args := getopt.New()

	helpFlag := args.BoolLong(
		"help",
		'h',
		"Show help")

	versionFlag := args.BoolLong(
		"version",
		'v',
		"Show program version")

	confPath := args.StringLong(
		"conf",
		'c',
		confPathDefault,
		"Config file path")

	args.Parse(os.Args)

	/* Show help */
	if *helpFlag {
		argsHelp(args)
		return Args{}, errkit.NewArgSuccessExit("help displayed")
	}

	/* Show version */
	if *versionFlag {
		argsVersion()
		return Args{}, errkit.NewArgSuccessExit("version displayed")
	}

	return Args{
		ConfigPath: *confPath,
	}, nil
}

func argsHelp(args *getopt.Set) {

	additionalDescription := `
	
Additional description

  Application for processing of the billing system.
`

	args.PrintUsage(os.Stdout)
	fmt.Printf("%s\n", additionalDescription)
}

func argsVersion() {
	fmt.Println(version)
}
