package ctx

import (
	"io"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestReadArgs_Normal tests normal ReadArgs execution with --conf parameter.
func TestReadArgs_Normal(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	// Set arguments: app name and config parameter.
	os.Args = []string{"app", "--conf", "custom.conf"}

	args, err := ReadArgs()
	require.NoError(t, err)
	require.Equal(t, "custom.conf", args.ConfigPath)
}

// captureOutput redirects os.Stdout and returns captured output.
func captureOutput(f func()) string {
	origStdout := os.Stdout
	r, w, err := os.Pipe()
	if err != nil {
		panic(err)
	}
	os.Stdout = w

	f()

	err = w.Close()
	require.NoError(nil, err)

	out, err := io.ReadAll(r)
	if err != nil {
		panic(err)
	}
	os.Stdout = origStdout
	return string(out)
}

// TestReadArgs_Help tests --help flag scenario.
// Function should display help and return special error with "help displayed" text.
func TestReadArgs_Help(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	os.Args = []string{"app", "--help"}

	var retErr error
	out := captureOutput(func() {
		_, retErr = ReadArgs()
	})

	require.Error(t, retErr)
	require.Contains(t, retErr.Error(), "help displayed")
	// Check that output contains usage and additional description.
	require.Contains(t, out, "Usage")
	require.Contains(t, out, "Additional description")
}

// TestReadArgs_Version tests --version flag scenario.
// Function should display version and return special error with "version displayed" text.
func TestReadArgs_Version(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	// Set test version.
	version = "test-version"

	os.Args = []string{"app", "--version"}

	var retErr error
	out := captureOutput(func() {
		_, retErr = ReadArgs()
	})

	require.Error(t, retErr)
	require.Contains(t, retErr.Error(), "version displayed")
	require.Contains(t, out, "test-version")
}

// TestReadArgs_DefaultConfig tests ReadArgs with default configuration path.
func TestReadArgs_DefaultConfig(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	// Test with no arguments (should use default config path)
	os.Args = []string{"app"}

	args, err := ReadArgs()
	require.NoError(t, err)
	require.Equal(t, confPathDefault, args.ConfigPath)
}

// TestReadArgs_ShortFlags tests ReadArgs with short flag versions.
func TestReadArgs_ShortFlags(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	// Test short version flag
	os.Args = []string{"app", "-v"}
	version = "short-flag-version"

	var retErr error
	out := captureOutput(func() {
		_, retErr = ReadArgs()
	})

	require.Error(t, retErr)
	require.Contains(t, retErr.Error(), "version displayed")
	require.Contains(t, out, "short-flag-version")
}

// TestReadArgs_ShortHelpFlag tests ReadArgs with short help flag.
func TestReadArgs_ShortHelpFlag(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	os.Args = []string{"app", "-h"}

	var retErr error
	out := captureOutput(func() {
		_, retErr = ReadArgs()
	})

	require.Error(t, retErr)
	require.Contains(t, retErr.Error(), "help displayed")
	require.Contains(t, out, "Usage")
}

// TestReadArgs_ShortConfigFlag tests ReadArgs with short config flag.
func TestReadArgs_ShortConfigFlag(t *testing.T) {
	origArgs := os.Args
	defer func() { os.Args = origArgs }()

	os.Args = []string{"app", "-c", "short-config.conf"}

	args, err := ReadArgs()
	require.NoError(t, err)
	require.Equal(t, "short-config.conf", args.ConfigPath)
}
