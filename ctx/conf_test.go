package ctx

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/require"
)

// validConfig is a minimal valid YAML configuration used for tests.
var validConfig = `
logfile: stdout
loglevel: info
pidfile: ""
api:
  bind: "0.0.0.0:8080"
  clientMaxBodySize: "1m"
  cors:
    allowOrigins:
      - "*"
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  poolSize: 10
  dialTimeout: 5
  poolTimeout: 5
  readTimeout: 2
  writeTimeout: 2
pgSQL:
  host: "localhost"
  port: 5432
  db: "testdb"
  user: "user"
  password: "pass"
  sslmode: "disable"
keycloak:
  url: "http://localhost:8080"
  realm: "test"
  clientID: "client"
  username: "admin"
  password: "pass"
  grantType: "password"
  searchLimit: 120
  insecureSkipVerify: false
`

// validConfigWithUnknown contains an extra unknown field to trigger an error.
var validConfigWithUnknown = `
logfile: stdout
loglevel: info
pidfile: ""
unknownField: "unexpected"
api:
  bind: "0.0.0.0:8080"
  clientMaxBodySize: "1m"
  cors:
    allowOrigins:
      - "*"
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  poolSize: 10
  dialTimeout: 5
  poolTimeout: 5
  readTimeout: 2
  writeTimeout: 2
pgSQL:
  host: "localhost"
  port: 5432
  db: "testdb"
  user: "user"
  password: "pass"
  sslmode: "disable"
keycloak:
  url: "http://localhost:8080"
  realm: "test"
  clientID: "client"
  username: "admin"
  password: "pass"
  grantType: "password"
  searchLimit: 120
  insecureSkipVerify: false
`

// TestReadConf_Success tests that readConf successfully loads a valid configuration.
func TestReadConf_Success(t *testing.T) {
	// Create a temporary configuration file with valid settings.
	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config.yaml")
	err := os.WriteFile(configPath, []byte(validConfig), 0644)
	require.NoError(t, err)

	confOpts, err := readConf(configPath)
	require.NoError(t, err)

	// Verify some critical fields.
	require.Equal(t, "stdout", confOpts.LogFile)
	require.Equal(t, "info", confOpts.LogLevel)
	require.Equal(t, "0.0.0.0:8080", confOpts.API.Bind)
	require.NotEmpty(t, confOpts.API.CORS.AllowOrigins)
	require.Equal(t, "*", confOpts.API.CORS.AllowOrigins[0])
}

// TestReadConf_MissingFile tests the behavior of readConf when the configuration file is missing.
func TestReadConf_MissingFile(t *testing.T) {
	// Attempt to load a configuration from a non-existent file.
	_, err := readConf("nonexistent.yaml")
	require.Error(t, err)
}

// TestReadConf_UnknownField tests that readConf returns an error when the configuration file contains an unknown field.
func TestReadConf_UnknownField(t *testing.T) {
	// Create a temporary configuration file with an extra unknown field.
	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "config_unknown.yaml")
	err := os.WriteFile(configPath, []byte(validConfigWithUnknown), 0644)
	require.NoError(t, err)

	_, err = readConf(configPath)
	require.Error(t, err)
}

// TestReadConf_InvalidYAML tests that readConf returns an error when the configuration file contains invalid YAML.
func TestReadConf_InvalidYAML(t *testing.T) {
	tmpDir := t.TempDir()
	configPath := filepath.Join(tmpDir, "invalid.yaml")
	invalidYAML := `
invalid: yaml: content:
  - missing
    proper: indentation
`
	err := os.WriteFile(configPath, []byte(invalidYAML), 0644)
	require.NoError(t, err)

	_, err = readConf(configPath)
	require.Error(t, err)
}
