package ctx

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

// cleanupVersionFile removes VERSION file if it exists in current directory
func cleanupVersionFile(t *testing.T) {
	t.Helper()
	if _, err := os.Stat("VERSION"); err == nil {
		err := os.Remove("VERSION")
		if err != nil {
			t.Logf("Warning: failed to cleanup VERSION file: %v", err)
		}
	}
}

// TestNewTLS_WithTLSConfig tests newTLS function when TLS configuration is provided.
func TestNewTLS_WithTLSConfig(t *testing.T) {
	apiConf := apiConf{
		TLS: &tlsConf{
			CertFile: "/path/to/cert.pem",
			KeyFile:  "/path/to/key.pem",
		},
	}

	result := newTLS(apiConf)
	require.NotNil(t, result)
	require.Equal(t, "/path/to/cert.pem", result.CertFile)
	require.Equal(t, "/path/to/key.pem", result.KeyFile)
}

// TestNewTLS_WithoutTLSConfig tests newTLS function when TLS configuration is nil.
func TestNewTLS_WithoutTLSConfig(t *testing.T) {
	apiConf := apiConf{
		TLS: nil,
	}

	result := newTLS(apiConf)
	require.Nil(t, result)
}

// TestNewBts_ValidSize tests newBts function with valid size strings.
func TestNewBts_ValidSize(t *testing.T) {
	tests := []struct {
		name     string
		size     string
		expected int64
	}{
		{"1 megabyte", "1m", 1048576},
		{"1 kilobyte", "1k", 1024},
		{"1 gigabyte", "1g", 1073741824},
		{"100 bytes", "100", 100},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			apiConf := apiConf{
				ClientMaxBodySize: tt.size,
			}

			result, err := newBts(apiConf)
			require.NoError(t, err)
			require.Equal(t, tt.expected, result)
		})
	}
}

// TestNewBts_InvalidSize tests newBts function with invalid size strings.
func TestNewBts_InvalidSize(t *testing.T) {
	apiConf := apiConf{
		ClientMaxBodySize: "invalid-size",
	}

	_, err := newBts(apiConf)
	require.Error(t, err)
}

// TestNewCORS tests newCORS function.
func TestNewCORS_Success(t *testing.T) {
	apiConf := apiConf{
		CORS: corsConf{
			AllowOrigins: []string{"http://localhost:3000", "https://example.com"},
		},
	}

	result := newCORS(apiConf)
	require.Len(t, result.AllowOrigins, 2)
	require.Contains(t, result.AllowOrigins, "http://localhost:3000")
	require.Contains(t, result.AllowOrigins, "https://example.com")
}

// TestNewCORS_EmptyOrigins tests newCORS function with empty origins.
func TestNewCORS_EmptyOrigins(t *testing.T) {
	apiConf := apiConf{
		CORS: corsConf{
			AllowOrigins: []string{},
		},
	}

	result := newCORS(apiConf)
	require.Empty(t, result.AllowOrigins)
}

// TestNewRateLimit tests newRateLimit function.
func TestNewRateLimit_Success(t *testing.T) {
	apiConf := apiConf{
		RateLimit: rateLimitConf{
			PerIPLimit:   100,
			PerIPWindow:  60,
			GlobalLimit:  5000,
			GlobalWindow: 60,
			BurstLimit:   30,
			BurstWindow:  10,
			Enabled:      true,
		},
	}

	result := newRateLimit(apiConf)
	require.Equal(t, 100, result.PerIPLimit)
	require.Equal(t, 60, result.PerIPWindow)
	require.Equal(t, 5000, result.GlobalLimit)
	require.Equal(t, 60, result.GlobalWindow)
	require.Equal(t, 30, result.BurstLimit)
	require.Equal(t, 10, result.BurstWindow)
	require.True(t, result.Enabled)
}

// TestNewAPICtx_Success tests newAPICtx function with valid configuration.
func TestNewAPICtx_Success(t *testing.T) {
	apiConf := apiConf{
		Bind:              "0.0.0.0:8080",
		ClientMaxBodySize: "1m",
		TLS: &tlsConf{
			CertFile: "/path/to/cert.pem",
			KeyFile:  "/path/to/key.pem",
		},
		CORS: corsConf{
			AllowOrigins: []string{"*"},
		},
		RateLimit: rateLimitConf{
			Enabled: true,
		},
	}

	result, err := newAPICtx(apiConf)
	require.NoError(t, err)
	require.Equal(t, "0.0.0.0:8080", result.Bind)
	require.NotNil(t, result.TLS)
	require.Equal(t, int64(1048576), result.ClientMaxBodySizeBytes)
	require.Len(t, result.CORS.AllowOrigins, 1)
	require.True(t, result.RateLimit.Enabled)
}

// TestNewAPICtx_InvalidBodySize tests newAPICtx function with invalid body size.
func TestNewAPICtx_InvalidBodySize(t *testing.T) {
	apiConf := apiConf{
		Bind:              "0.0.0.0:8080",
		ClientMaxBodySize: "invalid-size",
		CORS: corsConf{
			AllowOrigins: []string{"*"},
		},
	}

	_, err := newAPICtx(apiConf)
	require.Error(t, err)
}

// TestTmpLogError tests tmpLogError function.
func TestTmpLogError_Success(t *testing.T) {
	// This function uses global tmpLogger, so we just test it doesn't panic
	require.NotPanics(t, func() {
		tmpLogError("test.operation", os.ErrNotExist)
	})
}

// TestLogError tests logError function.
func TestLogError_Success(t *testing.T) {
	ctx := &Ctx{
		Log: logger.NewTemporary(),
	}

	// This function should not panic
	require.NotPanics(t, func() {
		logError(ctx, "test.operation", os.ErrNotExist)
	})
}

// TestReadVersion_Success tests readVersion function when VERSION file exists.
func TestReadVersion_Success(t *testing.T) {
	// Create temporary VERSION file
	tmpDir := t.TempDir()
	versionFile := filepath.Join(tmpDir, "VERSION")
	expectedVersion := "1.2.3"
	err := os.WriteFile(versionFile, []byte("  "+expectedVersion+"  \n"), 0644)
	require.NoError(t, err)

	// Change to temp directory
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	version, err := readVersion()
	require.NoError(t, err)
	require.Equal(t, expectedVersion, version)
}

// TestReadVersion_FileNotExists tests readVersion function when VERSION file doesn't exist.
func TestReadVersion_FileNotExists(t *testing.T) {
	// Change to temp directory without VERSION file
	tmpDir := t.TempDir()
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	_, err = readVersion()
	require.Error(t, err)
}

// TestWriteVersion_Success tests writeVersion function.
func TestWriteVersion_Success(t *testing.T) {
	tmpDir := t.TempDir()
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
		// Cleanup any VERSION file that might have been created in original directory
		cleanupVersionFile(t)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	version := "test-version"
	err = writeVersion(version)
	require.NoError(t, err)

	// Verify file was created with correct content
	content, err := os.ReadFile("VERSION")
	require.NoError(t, err)
	require.Equal(t, version, string(content))
}

// TestSetVersion_FileExists tests setVersion function when VERSION file exists.
func TestSetVersion_FileExists(t *testing.T) {
	tmpDir := t.TempDir()
	versionFile := filepath.Join(tmpDir, "VERSION")
	expectedVersion := "existing-version"
	err := os.WriteFile(versionFile, []byte(expectedVersion), 0644)
	require.NoError(t, err)

	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
		// Cleanup any VERSION file that might have been created in original directory
		cleanupVersionFile(t)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	ctx := &Ctx{}
	ctx.setVersion()
	require.Equal(t, expectedVersion, ctx.Version)
}

// TestSetVersion_FileNotExists tests setVersion function when VERSION file doesn't exist.
func TestSetVersion_FileNotExists(t *testing.T) {
	tmpDir := t.TempDir()
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
		// Cleanup any VERSION file that might have been created in original directory
		cleanupVersionFile(t)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	ctx := &Ctx{}
	ctx.setVersion()
	require.Equal(t, "unknown", ctx.Version)

	// Verify VERSION file was created
	content, err := os.ReadFile("VERSION")
	require.NoError(t, err)
	require.Equal(t, "unknown", string(content))
}

// TestValidateConfigFilePath_ValidPaths tests validateConfigFilePath function with valid paths.
func TestValidateConfigFilePath_ValidPaths(t *testing.T) {
	validPaths := []string{
		"configs/authorization.yaml",
		"configs/test.yml",
		"configs/subdir/config.yaml",
	}

	for _, path := range validPaths {
		t.Run(path, func(t *testing.T) {
			err := validateConfigFilePath(path)
			require.NoError(t, err)
		})
	}
}

// TestValidateConfigFilePath_InvalidPaths tests validateConfigFilePath function with invalid paths.
func TestValidateConfigFilePath_InvalidPaths(t *testing.T) {
	tests := []struct {
		name string
		path string
	}{
		{"directory traversal", "configs/../etc/passwd"},
		{"directory traversal with dots", "configs/../../etc/passwd"},
		{"not in configs directory", "other/config.yaml"},
		{"root path", "/etc/config.yaml"},
		{"non-yaml extension", "configs/config.txt"},
		{"no extension", "configs/config"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateConfigFilePath(tt.path)
			require.Error(t, err)
		})
	}
}

// TestSetVersion_EmptyFile tests setVersion function when VERSION file is empty.
func TestSetVersion_EmptyFile(t *testing.T) {
	tmpDir := t.TempDir()
	versionFile := filepath.Join(tmpDir, "VERSION")
	err := os.WriteFile(versionFile, []byte(""), 0644)
	require.NoError(t, err)

	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
		// Cleanup any VERSION file that might have been created in original directory
		cleanupVersionFile(t)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	ctx := &Ctx{}
	ctx.setVersion()
	require.Equal(t, "unknown", ctx.Version)

	// Verify VERSION file was updated
	content, err := os.ReadFile("VERSION")
	require.NoError(t, err)
	require.Equal(t, "unknown", string(content))
}

// TestSetVersion_WriteError tests setVersion function when writeVersion fails.
func TestSetVersion_WriteError(t *testing.T) {
	tmpDir := t.TempDir()
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
		// Cleanup any VERSION file that might have been created in original directory
		cleanupVersionFile(t)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	// Create a directory named VERSION to cause write error
	err = os.Mkdir("VERSION", 0755)
	require.NoError(t, err)

	ctx := &Ctx{}
	// This should not panic even if writeVersion fails
	require.NotPanics(t, func() {
		ctx.setVersion()
	})
	require.Equal(t, "unknown", ctx.Version)
}

// TestNewAPICtx_WithoutTLS tests newAPICtx function without TLS configuration.
func TestNewAPICtx_WithoutTLS(t *testing.T) {
	apiConf := apiConf{
		Bind:              "0.0.0.0:8080",
		ClientMaxBodySize: "1m",
		TLS:               nil,
		CORS: corsConf{
			AllowOrigins: []string{"*"},
		},
		RateLimit: rateLimitConf{
			Enabled: false,
		},
	}

	result, err := newAPICtx(apiConf)
	require.NoError(t, err)
	require.Equal(t, "0.0.0.0:8080", result.Bind)
	require.Nil(t, result.TLS)
	require.Equal(t, int64(1048576), result.ClientMaxBodySizeBytes)
	require.Len(t, result.CORS.AllowOrigins, 1)
	require.False(t, result.RateLimit.Enabled)
}

// TestValidateConfigFilePath_WindowsPaths tests validateConfigFilePath with Windows-style paths.
func TestValidateConfigFilePath_WindowsPaths(t *testing.T) {
	// Test Windows-style path separator
	err := validateConfigFilePath("configs\\authorization.yaml")
	require.NoError(t, err)

	// Test invalid Windows-style path
	err = validateConfigFilePath("other\\config.yaml")
	require.Error(t, err)
}

// TestReadVersion_WithWhitespace tests readVersion function with whitespace in file.
func TestReadVersion_WithWhitespace(t *testing.T) {
	tmpDir := t.TempDir()
	versionFile := filepath.Join(tmpDir, "VERSION")
	// Version with various whitespace characters
	versionContent := "\t\n  1.2.3-beta  \r\n\t"
	err := os.WriteFile(versionFile, []byte(versionContent), 0644)
	require.NoError(t, err)

	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
		// Cleanup any VERSION file that might have been created in original directory
		cleanupVersionFile(t)
	}()
	err = os.Chdir(tmpDir)
	require.NoError(t, err)

	version, err := readVersion()
	require.NoError(t, err)
	require.Equal(t, "1.2.3-beta", version)
}
