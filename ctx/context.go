package ctx

import (
	"context"
	"crypto/tls"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/coreos/go-oidc/v3/oidc"
	"github.com/docker/go-units"
	"golang.org/x/oauth2"
	"gopkg.in/yaml.v3"

	appservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/shared/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/authorization"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/identityprovider"
	newcache "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/cache"
	newprimedb "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/primedb"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

// Ctx defines application custom context
type Ctx struct {
	API             apiCtx
	Log             logger.Interface
	AppService      *appservice.Service
	AuthOIDC        authOIDC
	Authorization   *authorization.Config
	Version         string
	ServerStartTime time.Time
}

type apiCtx struct {
	Bind                   string
	TLS                    *apiTLSCtx
	ClientMaxBodySizeBytes int64
	AuthToken              string
	CORS                   corsCtx
	RateLimit              rateLimitCtx
}

type rateLimitCtx struct {
	PerIPLimit   int
	PerIPWindow  int
	GlobalLimit  int
	GlobalWindow int
	BurstLimit   int
	BurstWindow  int
	Enabled      bool
}

type apiTLSCtx struct {
	CertFile string
	KeyFile  string
}

type corsCtx struct {
	AllowOrigins []string
}

type authOIDC struct {
	CtxOIDC  context.Context
	Verifier *oidc.IDTokenVerifier
}

func InitAppCtx(ctx context.Context, serverStartTime time.Time) (any, error) {

	const op = "ctx.InitAppCtx"

	c, conf, err := newCtxConf(serverStartTime)
	if err != nil {
		return nil, err
	}

	c.AppService, err = c.newServiceBuilder(ctx, conf)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}

	c.AuthOIDC, err = authInit(ctx, conf.Keycloak.URL, conf.Keycloak.Realm, conf.Keycloak.InsecureSkipVerify)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}

	c.API, err = newAPICtx(conf.API)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}

	c.Authorization, err = initAuthorizationConfig("configs/authorization.yaml", c.AppService)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}

	return c, nil
}

func newNewPrimeDB(conf confOpts) (newprimedb.Registry, error) {

	db, err := newprimedb.Connect(newprimedb.Settings{
		Host:     conf.PgSQL.Host,
		Port:     conf.PgSQL.Port,
		Database: conf.PgSQL.DB,
		User:     conf.PgSQL.User,
		Password: os.Getenv(conf.PgSQL.Password),
		SSLMode:  conf.PgSQL.SSLMode,
	})
	if err != nil {
		return newprimedb.Registry{}, err
	}

	return *newprimedb.NewRegistry(&db), nil
}

func newNewCache(conf confOpts) (newcache.Registry, error) {
	redisAddr := fmt.Sprintf("%s:%d", conf.Redis.Host, conf.Redis.Port)
	redisPassword := os.Getenv(conf.Redis.Password)
	return *newcache.NewRegistry(redisAddr, redisPassword), nil
}

func newIdentityProvider(ctx context.Context, conf confOpts) (identityprovider.Registry, error) {
	return *identityprovider.NewRegistry(ctx, identityprovider.Settings{
		ProviderURL: conf.Keycloak.URL,
		Realm:       conf.Keycloak.Realm,
		ClientID:    conf.Keycloak.ClientID,
		Username:    conf.Keycloak.Username,
		Password:    os.Getenv(conf.Keycloak.Password),
		GrantType:   conf.Keycloak.GrantType,
		SearchLimit: conf.Keycloak.SearchLimit,
	}), nil
}

func (c *Ctx) newServiceBuilder(ctx context.Context, conf confOpts) (*appservice.Service, error) {
	const op = "ctx.newServiceBuilder"

	db, err := newNewPrimeDB(conf)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}
	c.Log.With(
		slog.Any("execution time", time.Since(c.ServerStartTime).String()),
	).Debug("postgres: connected")

	caches, err := newNewCache(conf)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}
	c.Log.With(
		slog.Any("execution time", time.Since(c.ServerStartTime).String()),
	).Debug("redis: connected")

	identityProvider, err := newIdentityProvider(ctx, conf)
	if err != nil {
		logError(c, op, err)
		return nil, err
	}

	return appservice.NewFromDependencies(db, caches, identityProvider, c.Version), nil
}

func newCtxConf(serverStartTime time.Time) (*Ctx, confOpts, error) {

	const op = "ctx.newCtxConf"

	c := &Ctx{ServerStartTime: serverStartTime}

	args, err := ReadArgs()
	if err != nil {
		return nil, confOpts{}, err
	}

	c.setVersion()

	conf, err := readConf(args.ConfigPath)
	if err != nil {
		tmpLogError(op, err)
		return nil, confOpts{}, err
	}

	c.Log, err = logger.Init(conf.LogFile, conf.LogLevel)
	if err != nil {
		tmpLogError(op, err)
		return nil, confOpts{}, err
	}

	return c, conf, nil
}

func newAPICtx(apiConf apiConf) (apiCtx, error) {

	newBts, err := newBts(apiConf)
	if err != nil {
		return apiCtx{}, err
	}

	newTLS := newTLS(apiConf)
	cors := newCORS(apiConf)
	rateLimit := newRateLimit(apiConf)

	return apiCtx{
		Bind:                   apiConf.Bind,
		TLS:                    newTLS,
		ClientMaxBodySizeBytes: newBts,
		CORS:                   cors,
		RateLimit:              rateLimit,
	}, nil
}

func newTLS(apiConf apiConf) *apiTLSCtx {
	if apiConf.TLS == nil {
		return nil
	}
	return &apiTLSCtx{
		CertFile: apiConf.TLS.CertFile,
		KeyFile:  apiConf.TLS.KeyFile,
	}
}

func newBts(apiConf apiConf) (int64, error) {
	bts, err := units.RAMInBytes(apiConf.ClientMaxBodySize)
	if err != nil {
		return 0, err
	}
	return bts, nil
}

func newCORS(apiConf apiConf) corsCtx {
	return corsCtx{
		AllowOrigins: func() []string {
			var orgns []string
			return append(orgns, apiConf.CORS.AllowOrigins...)
		}(),
	}
}

func newRateLimit(apiConf apiConf) rateLimitCtx {
	return rateLimitCtx{
		PerIPLimit:   apiConf.RateLimit.PerIPLimit,
		PerIPWindow:  apiConf.RateLimit.PerIPWindow,
		GlobalLimit:  apiConf.RateLimit.GlobalLimit,
		GlobalWindow: apiConf.RateLimit.GlobalWindow,
		BurstLimit:   apiConf.RateLimit.BurstLimit,
		BurstWindow:  apiConf.RateLimit.BurstWindow,
		Enabled:      apiConf.RateLimit.Enabled,
	}
}

var tmpLogger = logger.NewTemporary()

func tmpLogError(op string, err error) {
	tmpLogger.Error(op, slog.Any("details", err))
}

func logError(c *Ctx, op string, err error) {
	c.Log.Error(op, slog.Any("details", err))
}

func (c *Ctx) setVersion() {
	var err error
	c.Version, err = readVersion()
	if err != nil || c.Version == "" {
		c.Version = "unknown"
		if err := writeVersion(c.Version); err != nil {
			tmpLogError("setVersion", err)
		}
	}
}

func readVersion() (string, error) {
	b, err := os.ReadFile("VERSION")
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(b)), nil
}

func writeVersion(version string) error {
	return os.WriteFile("VERSION", []byte(version), 0600)
}

func authInit(ctx context.Context, kcURL, realm string, insecureSkipVerify bool) (authOIDC, error) {

	const op = "ctx.authInit"

	ctxOIDC := context.WithValue(
		ctx,
		oauth2.HTTPClient,
		&http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: insecureSkipVerify, // #nosec G402 -- Required for secure internal environment
				},
			},
		},
	)

	provider, err := oidc.NewProvider(
		ctxOIDC,
		fmt.Sprintf("%s/realms/%s", kcURL, realm),
	)
	if err != nil {
		return authOIDC{}, fmt.Errorf("%s: %w", op, err)
	}

	return authOIDC{
		CtxOIDC: ctxOIDC,
		Verifier: provider.Verifier(
			&oidc.Config{
				ClientID: "account",
			},
		),
	}, nil
}

// initAuthorizationConfig loads authorization configuration from YAML file
func initAuthorizationConfig(filePath string, appService *appservice.Service) (*authorization.Config, error) {
	// Validate file path for security (G304)
	if err := validateConfigFilePath(filePath); err != nil {
		return nil, fmt.Errorf("invalid authorization file path: %w", err)
	}

	data, err := os.ReadFile(filepath.Clean(filePath)) // #nosec G304 -- filepath is validated above
	if err != nil {
		return nil, fmt.Errorf("failed to read authorization authorization file: %w", err)
	}

	var authConf authorization.Config
	if err := yaml.Unmarshal(data, &authConf); err != nil {
		return nil, fmt.Errorf("failed to parse authorization authorization: %w", err)
	}

	if appService != nil {
		validator := authorization.NewServiceValidator(appService.Permission)
		authConf.SetValidator(validator)
	}

	if err := authConf.Validate(); err != nil {
		return nil, fmt.Errorf("invalid authorization authorization: %w", err)
	}

	return &authConf, nil
}

// validateConfigFilePath validates that the file path is safe and allowed
func validateConfigFilePath(filePath string) error {
	// Clean the path to prevent directory traversal
	cleanPath := filepath.Clean(filePath)

	// Check for directory traversal attempts
	if strings.Contains(cleanPath, "..") {
		return fmt.Errorf("directory traversal detected in path: %s", filePath)
	}

	// Only allow files in configs/ directory
	if !strings.HasPrefix(cleanPath, "configs/") && !strings.HasPrefix(cleanPath, "configs\\") {
		return fmt.Errorf("authorization file must be in configs/ directory, got: %s", filePath)
	}

	// Only allow .yaml files
	if !strings.HasSuffix(cleanPath, ".yaml") && !strings.HasSuffix(cleanPath, ".yml") {
		return fmt.Errorf("authorization file must be a YAML file, got: %s", filePath)
	}

	return nil
}
