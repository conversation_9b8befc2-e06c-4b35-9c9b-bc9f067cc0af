#!/usr/bin/env python3
"""
Universal analysis of Go domain service method usage.
Analyzes interface methods to find unused and internal-only methods.
"""

import argparse
import re
import subprocess
import sys
from pathlib import Path

def parse_interface_methods(interface_file_path):
    """Parse interface file and extract method names"""
    try:
        with open(interface_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find interface definition - handle nested braces properly
        interface_start = content.find('interface {')
        if interface_start == -1:
            print(f"❌ Interface start not found in {interface_file_path}")
            return []
        
        # Find the matching closing brace
        brace_count = 0
        interface_end = -1
        
        for i in range(interface_start + len('interface {'), len(content)):
            if content[i] == '{':
                brace_count += 1
            elif content[i] == '}':
                if brace_count == 0:
                    interface_end = i
                    break
                brace_count -= 1
        
        if interface_end == -1:
            print(f"❌ Interface end not found in {interface_file_path}")
            return []
        
        interface_body = content[interface_start + len('interface {'):interface_end]
        
        # Extract method names
        method_pattern = r'^\s*([A-Z]\w*)\s*\('
        methods = []
        
        for line in interface_body.split('\n'):
            line = line.strip()
            if line and not line.startswith('//') and not line.startswith('*'):
                match = re.match(method_pattern, line)
                if match:
                    methods.append(match.group(1))
        
        return methods
    
    except FileNotFoundError:
        print(f"❌ File not found: {interface_file_path}")
        return []
    except Exception as e:
        print(f"❌ Error parsing interface: {e}")
        return []

def extract_domain_info(interface_path):
    """Extract domain name and service path from interface file path"""
    path = Path(interface_path)
    
    # Get service directory path (e.g., internal/domain/group/service/)
    service_dir = str(path.parent) + "/"
    
    # Extract domain name from path (e.g., "group" from internal/domain/group/service/)
    path_parts = path.parts
    domain_index = -1
    
    for i, part in enumerate(path_parts):
        if part == "domain" and i + 1 < len(path_parts):
            domain_index = i + 1
            break
    
    if domain_index == -1:
        print("❌ Could not extract domain name from path")
        return None, None
    
    domain_name = path_parts[domain_index]
    
    return domain_name, service_dir

def search_external_usage(method, domain_name):
    """Search for external usage via domainName"""
    # Use word boundaries to avoid partial matches
    # Pattern: domainName + '.' + method + '('
    cmd = ["grep", "-rn", "--include=*.go", f"{domain_name}Domain\\.{method}(", "."]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
        
        # Filter out test files and mocks
        filtered = []
        for line in lines:
            if "_test.go:" not in line and "_mock.go:" not in line and "mock" not in line.lower():
                filtered.append(line)
        
        return filtered
    except:
        return []

def search_internal_usage(method, service_dir):
    """Search for internal usage within the same package"""
    # Use word boundaries to avoid partial matches
    # Pattern: 's.' + method + '('
    cmd = ["grep", "-rn", "--include=*.go", f"s\\.{method}(", service_dir]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
        
        # Filter out test files, mocks, and method definitions
        filtered = []
        for line in lines:
            if ("_test.go:" not in line and 
                "_mock.go:" not in line and 
                "mock" not in line.lower() and
                f"func (s *" not in line):
                filtered.append(line)
        
        return filtered
    except:
        return []

def main():
    parser = argparse.ArgumentParser(
        description='Analyze Go domain service interface method usage',
        epilog='''
Examples:
  python3 scripts/using.py internal/domain/group/service/service.go
  python3 scripts/using.py internal/domain/user/service/service.go
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('interface_path', 
                        help='Path to the interface file (e.g., internal/domain/group/service/service.go)')
    
    args = parser.parse_args()
    
    # Extract domain info from path
    domain_name, service_dir = extract_domain_info(args.interface_path)
    if not domain_name or not service_dir:
        sys.exit(1)
    
    # Parse interface methods
    methods = parse_interface_methods(args.interface_path)
    if not methods:
        print("❌ No methods found in interface")
        sys.exit(1)
    
    print(f"🔍 Analyzing {domain_name.capitalize()}DomainService methods...")
    print(f"📁 Interface: {args.interface_path}")
    print(f"📁 Service dir: {service_dir}")
    print(f"📋 Found {len(methods)} methods: {', '.join(methods)}\n")
    
    unused = []
    internal_only = []
    external = []
    
    for method in methods:
        external_usages = search_external_usage(method, domain_name)
        internal_usages = search_internal_usage(method, service_dir)
        
        # Filter external usages to exclude service package itself
        true_external = [u for u in external_usages if service_dir not in u]
        
        if not external_usages and not internal_usages:
            unused.append(method)
            print(f"❌ {method}: No usages")
        elif true_external:
            external.append((method, true_external, internal_usages))
            print(f"✅ {method}: {len(true_external)} external, {len(internal_usages)} internal")
        else:
            internal_only.append((method, internal_usages))
            print(f"⚠️  {method}: {len(internal_usages)} internal only")
    
    print("\n" + "="*60)
    print(f"📊 ANALYSIS RESULTS FOR {domain_name.upper()}DOMAINSERVICE")
    print("="*60)
    
    print(f"\n❌ UNUSED METHODS ({len(unused)}):")
    for method in unused:
        print(f"  - {method}")
    
    print(f"\n⚠️  INTERNAL-ONLY METHODS ({len(internal_only)}):")
    for method, usages in internal_only:
        print(f"  - {method} ({len(usages)} internal usages)")
        for usage in usages[:5]:  # Show max 5 usages
            file_line = usage.split(':')
            if len(file_line) >= 2:
                print(f"    └─ {file_line[0]}:{file_line[1]}")
        if len(usages) > 5:
            print(f"    └─ ... and {len(usages) - 5} more")
    
    print(f"\n✅ EXTERNALLY USED METHODS ({len(external)}):")
    for method, ext_usages, int_usages in external:
        print(f"  - {method} ({len(ext_usages)} external, {len(int_usages)} internal)")
        if len(ext_usages) <= 3:  # Show all if few
            for usage in ext_usages:
                file_line = usage.split(':')
                if len(file_line) >= 2:
                    print(f"    └─ {file_line[0]}:{file_line[1]}")
        else:  # Show first 3 if many
            for usage in ext_usages[:3]:
                file_line = usage.split(':')
                if len(file_line) >= 2:
                    print(f"    └─ {file_line[0]}:{file_line[1]}")
            print(f"    └─ ... and {len(ext_usages) - 3} more")

if __name__ == '__main__':
    main() 