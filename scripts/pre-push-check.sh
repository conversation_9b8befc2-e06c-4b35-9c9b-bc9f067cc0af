#!/bin/bash

set -e

# Show usage examples if help is requested
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo ""
    echo "🔧 Pre-push Check Script"
    echo "========================="
    echo ""
    echo "Usage examples:"
    echo ""
    echo "  # Normal run (all checks)"
    echo "  ./scripts/pre-push-check.sh"
    echo ""
    echo "  # Fast mode (skip slow checks: mocks, coverage, security, vulnerabilities)"
    echo "  FAST_CHECK=1 ./scripts/pre-push-check.sh"
    echo ""
    echo "  # Minimal mode (only critical: formatting, static analysis, build)"
    echo "  MINIMAL_CHECK=1 ./scripts/pre-push-check.sh"
    echo ""
    echo "  # Skip specific checks:"
    echo "  SKIP_MOCKS=1 ./scripts/pre-push-check.sh"
    echo "  SKIP_TESTS=1 ./scripts/pre-push-check.sh"
    echo "  SKIP_COVERAGE=1 ./scripts/pre-push-check.sh"
    echo "  SKIP_SECURITY=1 ./scripts/pre-push-check.sh"
    echo "  SKIP_VULNERABILITIES=1 ./scripts/pre-push-check.sh"
    echo "  SKIP_SQLC=1 ./scripts/pre-push-check.sh"
    echo ""
    echo "  # Combine multiple skips:"
    echo "  SKIP_MOCKS=1 SKIP_COVERAGE=1 ./scripts/pre-push-check.sh"
    echo ""
    echo "  # Check tool versions only:"
    echo "  CHECK_VERSIONS=1 ./scripts/pre-push-check.sh"
    echo ""
    echo "  # Run tests and coverage only:"
    echo "  TESTS_ONLY=1 ./scripts/pre-push-check.sh"
    echo ""
    echo "🔧 Recommended tool installations:"
    echo "  go install github.com/securego/gosec/v2/cmd/gosec@latest"
    echo "  go install golang.org/x/vuln/cmd/govulncheck@latest"
    echo ""
    echo "💡 Manual security check:"
    echo "  gosec -exclude-generated -exclude='*/mocks/*' ./..."
    echo ""
    exit 0
fi

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check names constants
readonly CHECK_CODE_GENERATION="Code generation"
readonly CHECK_CODE_FORMATTING="Code formatting"
readonly CHECK_STATIC_ANALYSIS="Static analysis"
readonly CHECK_DEPENDENCIES="Dependencies"
readonly CHECK_ENDPOINT_CONSISTENCY="Endpoint consistency"
readonly CHECK_TESTS="Tests"
readonly CHECK_TEST_COVERAGE="Test coverage"
readonly CHECK_APPLICATION_BUILD="Application build"
readonly CHECK_SECURITY="Security"
readonly CHECK_VULNERABILITIES="Vulnerabilities"
readonly CHECK_FILE_NORMALIZATION="File normalization"

# Check headers with emojis
readonly HEADER_CODE_GENERATION="🔄 $CHECK_CODE_GENERATION..."
readonly HEADER_CODE_FORMATTING="✨ $CHECK_CODE_FORMATTING..."
readonly HEADER_STATIC_ANALYSIS="🔍 $CHECK_STATIC_ANALYSIS..."
readonly HEADER_DEPENDENCIES="📦 $CHECK_DEPENDENCIES..."
readonly HEADER_ENDPOINT_CONSISTENCY="🔗 $CHECK_ENDPOINT_CONSISTENCY..."
readonly HEADER_TESTS="🧪 $CHECK_TESTS..."
readonly HEADER_TEST_COVERAGE="📊 $CHECK_TEST_COVERAGE..."
readonly HEADER_APPLICATION_BUILD="🔨 $CHECK_APPLICATION_BUILD..."
readonly HEADER_SECURITY="🔒 $CHECK_SECURITY..."
readonly HEADER_VULNERABILITIES="🛡️ $CHECK_VULNERABILITIES..."
readonly HEADER_FILE_NORMALIZATION="📁 $CHECK_FILE_NORMALIZATION..."

# Code generation sub-check names
readonly SUBCHECK_NAME_SORT_OPENAPI="Sort OpenAPI specification"
readonly SUBCHECK_NAME_GENERATE_API="Generate API files"
readonly SUBCHECK_NAME_GENERATE_MOCKS="Generate mocks"
readonly SUBCHECK_NAME_GENERATE_SQLC="Generate SQLC"
readonly SUBCHECK_NAME_CHECK_SQLC="Check SQLC"

# Code generation sub-check headers
readonly SUBCHECK_SORT_OPENAPI="    📋 $SUBCHECK_NAME_SORT_OPENAPI..."
readonly SUBCHECK_GENERATE_API="    🔧 $SUBCHECK_NAME_GENERATE_API..."
readonly SUBCHECK_GENERATE_MOCKS="    🧪 $SUBCHECK_NAME_GENERATE_MOCKS..."
readonly SUBCHECK_GENERATE_SQLC="    🗃️ $SUBCHECK_NAME_GENERATE_SQLC..."
readonly SUBCHECK_CHECK_SQLC="    🟢 $SUBCHECK_NAME_CHECK_SQLC..."

# Errors array
declare -a ERRORS=()
declare -a WARNINGS=()
declare -a SKIPPED=()
TOTAL_CHECKS=0
PASSED_CHECKS=0
SKIPPED_CHECKS=0

# Get version from go.mod (AWK optimized)
get_gomod_version() {
    local package="$1"
    # Use single AWK command instead of grep + awk + sed pipeline
    awk -v pkg="$package" '
    # Direct match for dependencies with version in same line
    $1 == pkg && NF >= 2 && $2 ~ /^v[0-9]/ {gsub(/^v/, "", $2); print $2; exit}
    # Match any line containing the package and extract version
    $0 ~ pkg && $0 ~ /v[0-9]/ {
        for(i=1; i<=NF; i++) {
            if($i ~ /^v[0-9]/) {
                gsub(/^v/, "", $i)
                print $i
                exit
            }
        }
    }' go.mod
}

# Get working test packages (AWK optimized - single test run)
get_working_test_packages_optimized() {
    local working_packages=""
    
    # Find directories with test files, excluding mocks, gen, and tests
    for pkg in $(find . -name "*_test.go" -type f | sed 's|/[^/]*$||' | sort -u | grep -v '/mocks/' | grep -v '/gen/' | grep -v '/tests/'); do
        # Test if package tests pass (single run for speed)
        if go test "${pkg}" >/dev/null 2>&1; then
            if [ -z "${working_packages}" ]; then
                working_packages="${pkg}"
            else
                working_packages="${working_packages} ${pkg}"
            fi
        fi
    done
    
    echo "${working_packages}"
}



# Get working test packages (AWK optimized - with double testing for reliability)
get_working_test_packages_legacy() {
    local working_packages=""
    
    # Find directories with test files, excluding mocks, gen, and tests
    for pkg in $(find . -name "*_test.go" -type f | sed 's|/[^/]*$||' | sort -u | grep -v '/mocks/' | grep -v '/gen/' | grep -v '/tests/'); do
        # Test if package tests pass consistently (run twice to catch flaky tests)
        if go test "${pkg}" >/dev/null 2>&1 && go test "${pkg}" >/dev/null 2>&1; then
            if [ -z "${working_packages}" ]; then
                working_packages="${pkg}"
            else
                working_packages="${working_packages} ${pkg}"
            fi
        fi
    done
    
    echo "${working_packages}"
}

# Check tool version
check_tool_version() {
    local tool_name="$1"
    local version_cmd="$2"
    local gomod_package="$3"
    local version_line="$4"  # Which line contains version (default: 1)
    local expected_version
    local installed_version
    
    if [ -z "$version_line" ]; then
        version_line=1
    fi
    
    expected_version=$(get_gomod_version "${gomod_package}")
    if [ -z "${expected_version}" ]; then
        echo "        🟡  Could not find ${gomod_package} version in go.mod"
        return 1
    fi
    
    if ! command -v "${tool_name}" > /dev/null 2>&1; then
        echo "        🔴 ${tool_name} not installed"
        return 1
    fi
    
    installed_version=$(${version_cmd} 2>/dev/null | sed -n "${version_line}p" | grep -o 'v[0-9][0-9.]*' | sed 's/^v//' || echo "")
    if [ -z "${installed_version}" ]; then
        echo "        🟡  Could not get ${tool_name} version"
        return 1
    fi
    
    if [ "${installed_version}" != "${expected_version}" ]; then
        echo "        🔴 ${tool_name} version mismatch: installed v${installed_version}, expected v${expected_version}"
        return 1
    fi
    
    echo "        🟢 ${tool_name} version v${installed_version} matches go.mod"
    return 0
}

# Check if a specific check should be skipped
should_skip() {
    local check_name="$1"
    
    # Fast mode skips slow checks
    if [ "$FAST_CHECK" = "1" ]; then
        case "$check_name" in
            "mocks"|"coverage"|"security"|"vulnerabilities")
                return 0
                ;;
        esac
    fi
    
    # Minimal mode only runs critical checks
    if [ "$MINIMAL_CHECK" = "1" ]; then
        case "$check_name" in
            "formatting"|"static_analysis"|"build"|"endpoint_consistency")
                return 1
                ;;
            *)
                return 0
                ;;
        esac
    fi
    
    # Individual skip variables
    case "$check_name" in
        "mocks")
            [ "$SKIP_MOCKS" = "1" ]
            ;;
        "sqlc")
            [ "$SKIP_SQLC" = "1" ]
            ;;
        "coverage")
            [ "$SKIP_COVERAGE" = "1" ]
            ;;
        "security")
            [ "$SKIP_SECURITY" = "1" ]
            ;;
        "vulnerabilities")
            [ "$SKIP_VULNERABILITIES" = "1" ]
            ;;
        "tests")
            [ "$SKIP_TESTS" = "1" ]
            ;;
        "endpoint_consistency")
            [ "$SKIP_ENDPOINT_CONSISTENCY" = "1" ]
            ;;
        *)
            return 1
            ;;
    esac
}

# Print skip status
print_skip() {
    local check_name="$1"
    local reason="$2"
    local indent="${3:-  }"  # Default 2 spaces, can be overridden
    echo -e "${indent}${YELLOW}🟡 $check_name (skipped: $reason)${NC}"
    SKIPPED+=("$check_name")
    SKIPPED_CHECKS=$((SKIPPED_CHECKS + 1))
}

# Status function
print_status() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if [ "$1" -eq 0 ]; then
        echo -e "  ${GREEN}🟢 $2${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "  ${RED}🔴 $2${NC}"
        ERRORS+=("$2")
    fi
}



# Summary function
print_summary() {
    echo ""
    echo "═══════════════════════════════════════════════════════════════"
    echo -e "${GREEN}📊 CHECKS SUMMARY${NC}"
    echo "═══════════════════════════════════════════════════════════════"
    echo "Total checks: $TOTAL_CHECKS"
    if [ "$PASSED_CHECKS" -eq "$TOTAL_CHECKS" ]; then
        echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    else
        echo -e "Passed: ${YELLOW}$PASSED_CHECKS${NC}"
    fi
    if [ "${#ERRORS[@]}" -eq 0 ]; then
        echo -e "Errors: ${GREEN}${#ERRORS[@]}${NC}"
    else
        echo -e "Errors: ${RED}${#ERRORS[@]}${NC}"
    fi
    if [ "${#WARNINGS[@]}" -eq 0 ]; then
        echo -e "Warnings: ${GREEN}${#WARNINGS[@]}${NC}"
    else
        echo -e "Warnings: ${YELLOW}${#WARNINGS[@]}${NC}"
    fi
    if [ "$SKIPPED_CHECKS" -eq 0 ]; then
        echo -e "Skipped: ${GREEN}$SKIPPED_CHECKS${NC}"
    else
        echo -e "Skipped: ${YELLOW}$SKIPPED_CHECKS${NC}"
    fi

    if [ ${#ERRORS[@]} -gt 0 ]; then
        echo ""
        echo -e "${RED}🔴 ERRORS:${NC}"
        for error in "${ERRORS[@]}"; do
            echo -e "   • ${RED}$error${NC}"
        done
    fi

    if [ ${#WARNINGS[@]} -gt 0 ]; then
        echo ""
        echo -e "${YELLOW}🟡  WARNINGS:${NC}"
        for warning in "${WARNINGS[@]}"; do
            echo -e "   • ${YELLOW}$warning${NC}"
        done
    fi

    if [ ${#SKIPPED[@]} -gt 0 ]; then
        echo ""
        echo -e "${YELLOW}🟡 SKIPPED:${NC}"
        for skipped in "${SKIPPED[@]}"; do
            echo -e "   • ${YELLOW}$skipped${NC}"
        done
    fi

    echo ""
    if [ ${#ERRORS[@]} -eq 0 ]; then
        echo -e "${GREEN}🟢 All critical checks passed! Code is ready to push.${NC}"
        if [ ${#WARNINGS[@]} -gt 0 ]; then
            echo -e "${YELLOW}💡 Consider fixing warnings before pushing.${NC}"
        fi
    else
        echo -e "${RED}🔴 Critical errors found. Fix them before pushing.${NC}"
        echo ""
        echo "🔧 Diagnostic commands:"
        for error in "${ERRORS[@]}"; do
            case "$error" in
                "$CHECK_CODE_GENERATION")
                    echo "   make generate-all             # $SUBCHECK_NAME_GENERATE_API"
                    echo "   make generate-mocks           # $SUBCHECK_NAME_GENERATE_MOCKS"
                    echo "   make generate-sqlc            # $SUBCHECK_NAME_GENERATE_SQLC"
                    echo "   make sqlc-vet                 # $SUBCHECK_NAME_CHECK_SQLC"
                    ;;
                "$CHECK_CODE_FORMATTING")
                    echo "   gofmt -l .                    # Show formatting issues and errors"
                    echo "   gofmt -w .                    # Fix formatting issues"
                    echo "   go build ./...                # Show compilation errors in detail"
                    echo "   # Note: Fix compilation errors before running gofmt"
                    ;;
                "$CHECK_STATIC_ANALYSIS")
                    echo "   go vet ./...                  # Static analysis details"
                    ;;
                "$CHECK_ENDPOINT_CONSISTENCY")
                    echo "   ./scripts/check-endpoint-methods-consistency.sh  # Detailed endpoint analysis"
                    echo "   cat configs/authorization.yaml   # Review authorization config"
                    echo "   cat api/openapi.yaml             # Review OpenAPI specification"
                    ;;
                "$CHECK_TESTS")
                    echo "   go test ./...                 # Run all tests with details"
                    ;;
                "$CHECK_APPLICATION_BUILD")
                    echo "   go build ./cmd/pms.go         # Build details"
                    ;;
                "$CHECK_TEST_COVERAGE:"*)
                    echo "   go test -cover ./...          # Coverage details (includes all packages)"
                    echo "   go test ./...                 # Run all tests"
                    echo "   # Note: Coverage calculation excludes mocks and gen folders"
                    ;;
            esac
        done
        
        # Show diagnostic commands for important warnings
        for warning in "${WARNINGS[@]}"; do
            case "$warning" in
                "File normalization error")
                    echo "   git status                    # Check file status"
                    echo "   git add --renormalize .       # Fix file normalization"
                    echo "   git diff --check              # Check for whitespace issues"
                    ;;
                "go.mod/go.sum changed")
                    echo "   git add go.mod go.sum         # Add dependency changes to commit"
                    ;;
                "$CHECK_ENDPOINT_CONSISTENCY")
                    echo "   ./scripts/check-endpoint-methods-consistency.sh  # Show detailed analysis"
                    echo "   # Note: Extra endpoints in authorization may indicate outdated rules"
                    ;;
                "Security issues")
                    echo "   gosec -exclude-generated -exclude='*/mocks/*' ./...  # Show security details"
                    ;;
                "Vulnerabilities in dependencies")
                    echo "   govulncheck ./...             # Show vulnerability details"
                    ;;
                "Test coverage:"*)
                    echo "   go test -cover ./...          # Show detailed coverage"
                    echo "   go test ./...                 # Run all tests"
                    echo "   # Note: Consider adding more tests to improve coverage"
                    ;;
                "Could not get test coverage information")
                    echo "   go test ./...                 # Check if tests run properly"
                    echo "   go test -cover ./...          # Try coverage calculation manually"
                    echo "   # Note: Make sure test packages exist and are working"
                    ;;
                "gosec not installed")
                    echo "   go install github.com/securego/gosec/v2/cmd/gosec@latest  # Install gosec"
                    ;;
                "govulncheck not installed")
                    echo "   go install golang.org/x/vuln/cmd/govulncheck@latest      # Install govulncheck"
                    ;;
            esac
        done
        echo ""
        exit 1
    fi
}

# Check versions only mode
if [ "$CHECK_VERSIONS" = "1" ]; then
    echo ""
    echo -e "${YELLOW}🔧 CHECKING TOOL VERSIONS ONLY${NC}"
    echo "═══════════════════════════════════════════════════════════════"
    
    echo ""
    echo "🔧 oapi-codegen:"
    check_tool_version "oapi-codegen" "oapi-codegen -version" "github.com/oapi-codegen/oapi-codegen/v2" "2"
    
    echo ""
    echo "🧪 minimock:"
    check_tool_version "minimock" "minimock -version" "github.com/gojuno/minimock/v3"
    
    echo ""
    echo "🗃️ sqlc:"
    check_tool_version "sqlc" "sqlc version" "github.com/sqlc-dev/sqlc"
    
    echo ""
    echo "═══════════════════════════════════════════════════════════════"
    echo -e "${GREEN}🟢 Version check completed${NC}"
    exit 0
fi

# Tests and coverage only mode
if [ "$TESTS_ONLY" = "1" ]; then
    echo ""
    echo -e "${YELLOW}🧪 RUNNING TESTS AND COVERAGE ONLY${NC}"
    echo "═══════════════════════════════════════════════════════════════"
    
    echo ""
    echo "🧪 Running all tests..."
    echo "  🔍 Detecting all test packages..."
    
    # Find all packages with test files (including potentially failing ones) - using traditional method for reliability
    all_test_packages=""
    for pkg in $(find . -name "*_test.go" -type f | sed 's|/[^/]*$||' | sort -u | grep -v '/mocks/' | grep -v '/gen/' | grep -v '/tests/'); do
        if [ -z "${all_test_packages}" ]; then
            all_test_packages="${pkg}"
        else
            all_test_packages="${all_test_packages} ${pkg}"
        fi
    done
    
    if [ -z "${all_test_packages}" ]; then
        echo -e "  ${RED}🔴 No test packages found${NC}"
        exit 1
    fi
    
    # Run tests on all packages and filter output
    # Use word splitting intentionally for package list
    # shellcheck disable=SC2086
    if go test ${all_test_packages}; then
        echo -e "  ${GREEN}🟢 All tests passed${NC}"
        tests_passed=1
    else
        echo -e "  ${RED}🔴 Some tests failed${NC}"
        tests_passed=0
    fi
    
    echo ""
    if [ "${tests_passed}" = "1" ]; then
        echo "📊 Calculating test coverage..."
            # AWK optimized: get packages excluding mocks and gen
    coverage_packages=$(go list ./... | awk '!/\/mocks(\/|$)|\/gen\// {packages=packages $0 " "} END{print packages}')
        # Use word splitting intentionally for package list
        # shellcheck disable=SC2086
        if go test -coverprofile=coverage.out -covermode=atomic ${coverage_packages} >/dev/null 2>&1; then
        # Use fast AWK-based coverage calculation instead of go tool cover
        coverage_output=$(awk 'BEGIN{t=0;c=0} NR==1&&/^mode:/{next} NF>=3&&$2~/^[0-9]+$/&&$3~/^[0-9]+$/{t+=$2;if($3>0)c+=$2} END{printf"%.1f%%",(t>0)?(c*100.0/t):0}' coverage.out 2>/dev/null || echo "0%")
        
        # Apply color gradient based on coverage percentage
        if [ -n "${coverage_output}" ] && [ "${coverage_output}" != "0%" ]; then
            coverage_num=${coverage_output%\%}
            if command -v bc > /dev/null 2>&1 && [ -n "${coverage_num}" ]; then
                if (( $(echo "${coverage_num} <= 33" | bc -l) )); then
                    echo -e "  ${RED}🔴 Test coverage: ${coverage_output}${NC}"
                    coverage_color="${RED}"
                elif (( $(echo "${coverage_num} <= 85" | bc -l) )); then
                    echo -e "  ${YELLOW}🟡  Test coverage: ${coverage_output}${NC}"
                    coverage_color="${YELLOW}"
                else
                    echo -e "  ${GREEN}🟢 Test coverage: ${coverage_output}${NC}"
                    coverage_color="${GREEN}"
                fi
            else
                echo -e "  ${GREEN}🟢 Test coverage: ${coverage_output}${NC}"
                coverage_color="${GREEN}"
            fi
        else
            echo -e "  ${GREEN}🟢 Test coverage: ${coverage_output}${NC}"
            coverage_color="${GREEN}"
        fi
        else
            echo -e "  ${RED}🔴 Coverage calculation failed${NC}"
            coverage_output="0%"
            coverage_color="${RED}"
        fi
        # Always clean up coverage file
        rm -f coverage.out
    else
        echo "⏭️  Skipping test coverage calculation (tests failed)"
        coverage_output="0%"
        coverage_color="${RED}"
    fi
    
    echo ""
    echo "═══════════════════════════════════════════════════════════════"
    if [ "${tests_passed}" = "1" ]; then
        echo -e "${GREEN}🟢 Tests and coverage check completed successfully${NC}"
        echo -e "   Tests: ${GREEN}PASSED${NC}"
        echo -e "   Coverage: ${coverage_color}${coverage_output}${NC}"
        exit 0
    else
        echo -e "${RED}🔴 Tests failed${NC}"
        exit 1
    fi
fi

# Print active modes
if [ "$FAST_CHECK" = "1" ] || [ "$MINIMAL_CHECK" = "1" ] || [ -n "$SKIP_MOCKS$SKIP_TESTS$SKIP_SQLC$SKIP_COVERAGE$SKIP_SECURITY$SKIP_VULNERABILITIES" ]; then
    echo ""
    echo -e "${YELLOW}📋 ACTIVE MODES:${NC}"
    [ "$FAST_CHECK" = "1" ] && echo -e "   • ${YELLOW}Fast mode (skipping slow checks)${NC}"
    [ "$MINIMAL_CHECK" = "1" ] && echo -e "   • ${YELLOW}Minimal mode (only critical checks)${NC}"
    [ "$SKIP_MOCKS" = "1" ] && echo -e "   • ${YELLOW}Skipping mock generation${NC}"
    [ "$SKIP_TESTS" = "1" ] && echo -e "   • ${YELLOW}Skipping tests${NC}"
    [ "$SKIP_SQLC" = "1" ] && echo -e "   • ${YELLOW}Skipping SQLC generation${NC}"
    [ "$SKIP_COVERAGE" = "1" ] && echo -e "   • ${YELLOW}Skipping test coverage${NC}"
    [ "$SKIP_SECURITY" = "1" ] && echo -e "   • ${YELLOW}Skipping security checks${NC}"
    [ "$SKIP_VULNERABILITIES" = "1" ] && echo -e "   • ${YELLOW}Skipping vulnerability checks${NC}"
fi

# Catch exit for summary
trap print_summary EXIT

# Progress counter
CURRENT_CHECK=0
TOTAL_MAIN_CHECKS=11

# Helper function to show progress
show_progress() {
    CURRENT_CHECK=$((CURRENT_CHECK + 1))
    echo ""
    echo "[$CURRENT_CHECK/$TOTAL_MAIN_CHECKS] $1"
}

show_progress "$HEADER_CODE_GENERATION"
generation_failed=0

echo "$SUBCHECK_SORT_OPENAPI"
if ! ./scripts/sort-openapi.sh > /dev/null 2>&1; then
    echo "        🔴 Error in sort-openapi.sh"
    generation_failed=1
fi

echo "$SUBCHECK_GENERATE_API"
echo "      🔧 Checking oapi-codegen version..."
if ! check_tool_version "oapi-codegen" "oapi-codegen -version" "github.com/oapi-codegen/oapi-codegen/v2" "2"; then
    generation_failed=1
fi
if ! make generate-all > /dev/null 2>&1; then
    echo "        🔴 Error in generate-all"
    generation_failed=1
fi

if should_skip "mocks"; then
    print_skip "$SUBCHECK_NAME_GENERATE_MOCKS" "SKIP_MOCKS or FAST_CHECK" "    "
else
    echo "$SUBCHECK_GENERATE_MOCKS"
    echo "      🧪 Checking minimock version..."
    if ! check_tool_version "minimock" "minimock -version" "github.com/gojuno/minimock/v3"; then
        generation_failed=1
    fi
    if ! make generate-mocks > /dev/null 2>&1; then
        echo "        🔴 Error in generate-mocks"
        generation_failed=1
    fi
fi

if should_skip "sqlc"; then
    print_skip "$SUBCHECK_NAME_GENERATE_SQLC" "SKIP_SQLC" "    "
    print_skip "$SUBCHECK_NAME_CHECK_SQLC" "SKIP_SQLC" "    "
else
    echo "$SUBCHECK_GENERATE_SQLC"
    echo "      🗃️ Checking sqlc version..."
    if ! check_tool_version "sqlc" "sqlc version" "github.com/sqlc-dev/sqlc"; then
        generation_failed=1
    fi
    if ! make generate-sqlc > /dev/null 2>&1; then
        echo "        🔴 Error in generate-sqlc"
        generation_failed=1
    fi

    echo "$SUBCHECK_CHECK_SQLC"
    if ! make sqlc-vet > /dev/null 2>&1; then
        echo "        🔴 Error in sqlc-vet"
        generation_failed=1
    fi
fi

if [ "${generation_failed}" -eq 0 ]; then
    print_status 0 "$CHECK_CODE_GENERATION"
else
    print_status 1 "$CHECK_CODE_GENERATION"
fi

show_progress "$HEADER_CODE_FORMATTING"
# AWK optimized: single gofmt call with AWK processing
gofmt_result=$(gofmt -l . 2>&1)
gofmt_output=$(echo "$gofmt_result" | awk '/\.go$/ {print}')
gofmt_errors=$(echo "$gofmt_result" | awk '!/\.go$/ && NF>0 {print}')

# Check if there are compilation errors (stderr output)
if [ -n "${gofmt_errors}" ]; then
    print_status 1 "$CHECK_CODE_FORMATTING"
# Check if there are formatting issues (stdout output)
elif [ -n "${gofmt_output}" ]; then
    echo "    ${gofmt_output}" | head -10
    # AWK optimized line count
    gofmt_line_count=$(echo "${gofmt_output}" | awk 'END{print NR}')
    if [ "${gofmt_line_count}" -gt 10 ]; then
        echo "... and $(( gofmt_line_count - 10 )) more files"
    fi
    echo "  Run: gofmt -w ."
    print_status 1 "$CHECK_CODE_FORMATTING"
else
    print_status 0 "$CHECK_CODE_FORMATTING"
fi

show_progress "$HEADER_STATIC_ANALYSIS"
if go vet ./... > /dev/null 2>&1; then
    print_status 0 "$CHECK_STATIC_ANALYSIS"
else
    print_status 1 "$CHECK_STATIC_ANALYSIS"
fi

show_progress "$HEADER_DEPENDENCIES"
if [ "$MINIMAL_CHECK" = "1" ]; then
    print_skip "$CHECK_DEPENDENCIES" "MINIMAL_CHECK"
else
    go mod tidy
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if ! git diff --exit-code go.mod go.sum > /dev/null 2>&1; then
        echo -e "  ${YELLOW}🟡  go.mod/go.sum changed - add to commit${NC}"
        WARNINGS+=("go.mod/go.sum changed")
    else
        echo -e "  ${GREEN}🟢 $CHECK_DEPENDENCIES${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
fi

show_progress "$HEADER_ENDPOINT_CONSISTENCY"  
if should_skip "endpoint_consistency"; then
    print_skip "$CHECK_ENDPOINT_CONSISTENCY" "SKIP_ENDPOINT_CONSISTENCY"
else
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    # Temporarily disable exit on error for this check
    set +e
    # Run endpoint consistency check and capture output
    endpoint_check_output=$(./scripts/check-endpoint-methods-consistency.sh 2>&1)
    endpoint_check_result=$?
    # Re-enable exit on error
    set -e
    
    # Debug: uncomment for troubleshooting  
    # echo "DEBUG: endpoint_check_result=$endpoint_check_result"
    # echo "DEBUG: output length=$(echo "$endpoint_check_output" | wc -l)"
    
    # Extract Issues found section from output - include the actual numbers
    issues_section=$(echo "$endpoint_check_output" | sed -n '/^Issues found:/,/^$/p' | head -3)
    
    if [ $endpoint_check_result -eq 0 ]; then
        echo -e "  ${GREEN}🟢 $CHECK_ENDPOINT_CONSISTENCY${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        # Check if it's warnings only or has errors
        if echo "$endpoint_check_output" | grep -q "🔴 FAILED:"; then
            echo -e "  ${RED}🔴 $CHECK_ENDPOINT_CONSISTENCY${NC}"
            ERRORS+=("$CHECK_ENDPOINT_CONSISTENCY")
        else
            echo -e "  ${YELLOW}🟡 $CHECK_ENDPOINT_CONSISTENCY${NC}"
            WARNINGS+=("$CHECK_ENDPOINT_CONSISTENCY")
        fi
        
        # Show the Issues found section with proper indentation
        if [[ -n "$issues_section" ]]; then
            echo "$issues_section" | while IFS= read -r line; do
                [[ -n "$line" ]] && echo "    $line"
            done
        fi
    fi
fi

show_progress "$HEADER_TESTS"
tests_passed_normal=0
if [ "$MINIMAL_CHECK" = "1" ]; then
    print_skip "$CHECK_TESTS" "MINIMAL_CHECK"
elif should_skip "tests"; then
    print_skip "$CHECK_TESTS" "SKIP_TESTS"
else
    if go test ./... > /dev/null 2>&1; then
        print_status 0 "$CHECK_TESTS"
        tests_passed_normal=1
    else
        print_status 1 "$CHECK_TESTS"
        tests_passed_normal=0
    fi
fi

show_progress "$HEADER_TEST_COVERAGE"
if should_skip "coverage"; then
    print_skip "$CHECK_TEST_COVERAGE" "SKIP_COVERAGE or FAST_CHECK"
elif [ "$tests_passed_normal" = "0" ]; then
    print_skip "$CHECK_TEST_COVERAGE" "tests failed"
else
    echo "  🔍 Calculating coverage for working test packages (excluding mocks and gen)..."
    # AWK optimized: get all packages excluding mocks and gen for consistent coverage calculation
    coverage_packages=$(go list ./... | awk '!/\/mocks(\/|$)|\/gen\// {packages=packages $0 " "} END{print packages}')
    if [ -n "${coverage_packages}" ]; then
        # Run tests with coverage and capture only the final percentage
        # Use word splitting intentionally for package list
        # shellcheck disable=SC2086
        if go test -coverprofile=coverage.out -covermode=atomic ${coverage_packages} >/dev/null 2>&1; then
            # Use fast AWK-based coverage calculation instead of go tool cover
            coverage_output=$(awk 'BEGIN{t=0;c=0} NR==1&&/^mode:/{next} NF>=3&&$2~/^[0-9]+$/&&$3~/^[0-9]+$/{t+=$2;if($3>0)c+=$2} END{printf"%.1f%%",(t>0)?(c*100.0/t):0}' coverage.out 2>/dev/null || echo "0%")
        else
            coverage_output="0%"
        fi
        # Clean up coverage file if it was created
        rm -f coverage.out
    else
        coverage_output="0%"
    fi

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if [ -n "${coverage_output}" ] && [ "${coverage_output}" != "0%" ]; then
        coverage_num=${coverage_output%\%}
        if command -v bc > /dev/null 2>&1 && [ -n "${coverage_num}" ]; then
            if (( $(echo "${coverage_num} <= 33" | bc -l) )); then
                echo -e "  ${RED}🔴 ${CHECK_TEST_COVERAGE}: ${coverage_output}${NC}"
                ERRORS+=("${CHECK_TEST_COVERAGE}: ${coverage_output}")
            elif (( $(echo "${coverage_num} <= 85" | bc -l) )); then
                echo -e "  ${YELLOW}🟡  ${CHECK_TEST_COVERAGE}: ${coverage_output}${NC}"
                WARNINGS+=("Test coverage: ${coverage_output}")
            else
                echo -e "  ${GREEN}🟢 ${CHECK_TEST_COVERAGE}: ${coverage_output}${NC}"
                PASSED_CHECKS=$((PASSED_CHECKS + 1))
            fi
        else
            echo -e "  ${GREEN}🟢 ${CHECK_TEST_COVERAGE}: ${coverage_output}${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        fi
    else
        echo -e "  ${YELLOW}🟡  Could not get test coverage information${NC}"
        echo "      Possible causes:"
        echo "      • No working test packages found"
        echo "      • Tests failed to run"
        echo "      • Coverage calculation failed"
        echo "      • Missing coverage tools (go test -cover)"
        echo "      Run manually: go test -cover ./..."
        WARNINGS+=("Could not get test coverage information")
    fi
fi

show_progress "$HEADER_APPLICATION_BUILD"
if go build ./cmd/pms.go > /dev/null 2>&1; then
    print_status 0 "$CHECK_APPLICATION_BUILD"
    # Remove built file
    rm -f pms
else
    print_status 1 "$CHECK_APPLICATION_BUILD"
fi

show_progress "$HEADER_SECURITY"
if should_skip "security"; then
    print_skip "$CHECK_SECURITY" "SKIP_SECURITY or FAST_CHECK"
else
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if command -v gosec > /dev/null 2>&1; then
        if gosec -quiet -exclude-generated ./... > /dev/null 2>&1; then
            echo -e "  ${GREEN}🟢 $CHECK_SECURITY${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "  ${YELLOW}🟡  Security issues (see gosec)${NC}"
            WARNINGS+=("Security issues")
        fi
    else
        echo -e "  ${YELLOW}🟡  gosec not installed (install: go install github.com/securego/gosec/v2/cmd/gosec@latest)${NC}"
        WARNINGS+=("gosec not installed")
    fi
fi

show_progress "$HEADER_VULNERABILITIES"
if should_skip "vulnerabilities"; then
    print_skip "$CHECK_VULNERABILITIES" "SKIP_VULNERABILITIES or FAST_CHECK"
else
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if command -v govulncheck > /dev/null 2>&1; then
        if govulncheck ./... > /dev/null 2>&1; then
            echo -e "  ${GREEN}🟢 $CHECK_VULNERABILITIES${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "  ${YELLOW}🟡  Vulnerabilities in dependencies (see govulncheck)${NC}"
            WARNINGS+=("Vulnerabilities in dependencies")
        fi
    else
        echo -e "  ${YELLOW}🟡  govulncheck not installed (install: go install golang.org/x/vuln/cmd/govulncheck@latest)${NC}"
        WARNINGS+=("govulncheck not installed")
    fi
fi

show_progress "$HEADER_FILE_NORMALIZATION"
if [ "$MINIMAL_CHECK" = "1" ]; then
    print_skip "$CHECK_FILE_NORMALIZATION" "MINIMAL_CHECK"
else
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if git add --renormalize . > /dev/null 2>&1; then
        echo -e "  ${GREEN}🟢 $CHECK_FILE_NORMALIZATION${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "  ${YELLOW}🟡  File normalization error${NC}"
        WARNINGS+=("File normalization error")
    fi
fi

# Disable trap to call print_summary only once
trap - EXIT
print_summary 