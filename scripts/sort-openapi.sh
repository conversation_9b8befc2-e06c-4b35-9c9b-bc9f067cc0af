#!/bin/bash

# OpenAPI Schema Sorter
# Sorts OpenAPI schema components for better readability

echo "🔧 OpenAPI Schema Sorter"
echo "========================"
echo ""

# [1/5] Check yq installation
echo "[1/5] 🔍 Checking yq installation..."
if ! command -v yq &> /dev/null; then
  echo "      🔴 yq not installed"
  echo "      💡 Install yq: https://github.com/mikefarah/yq"
  exit 1
fi
echo "      🟢 yq found"

# [2/5] Check API file
echo "[2/5] 📁 Checking API file..."
ROOT_DIR=$(git rev-parse --show-toplevel)
API_FILE="$ROOT_DIR/api/openapi.yaml"

if [ ! -f "$API_FILE" ]; then
  echo "      🔴 File $API_FILE not found"
  exit 1
fi
echo "      🟢 File found: $API_FILE"

# [3/5] Sort schemas
echo "[3/5] 🔤 Sorting component schemas..."
if yq -i '(.components.schemas) |= (to_entries | sort_by(.key) | from_entries)' "$API_FILE"; then
  echo "      🟢 Schemas sorted"
else
  echo "      🔴 Failed to sort schemas"
  exit 1
fi

# [4/5] Sort tags and paths
echo "[4/5] 🏷️  Sorting tags and paths..."
if yq -i '(.tags) |= sort_by(.name)' "$API_FILE"; then
  echo "      🟢 Tags sorted"
else
  echo "      🔴 Failed to sort tags"
  exit 1
fi

# Sort HTTP methods order
if yq -i '
  .paths[] |= (
    pick(["post", "get", "patch", "delete"]) + 
    with_entries(
      select(
        .key | (
          . != "post" and 
          . != "get" and 
          . != "patch" and 
          . != "delete"
        )
      )
    )
  )
' "$API_FILE"; then
  echo "      🟢 HTTP methods sorted"
else
  echo "      🔴 Failed to sort HTTP methods"
  exit 1
fi

# Sort paths alphabetically
if yq -i '.paths |= (to_entries | sort_by(.key) | from_entries)' "$API_FILE"; then
  echo "      🟢 Paths sorted"
else
  echo "      🔴 Failed to sort paths"
  exit 1
fi

# [5/5] Sort operation properties
echo "[5/5] ⚙️  Sorting operation properties..."
if yq -i '(.paths[][] | select(tag == "!!map")) |= 
       with_entries(select(.key == "summary")) + 
       with_entries(select(.key == "description")) + 
       with_entries(select(.key == "operationId")) + 
       with_entries(select(.key == "tags")) + 
       with_entries(select(.key == "parameters")) + 
       with_entries(select(.key == "requestBody")) + 
       with_entries(select(.key == "responses")) + 
       with_entries(select(.key != "summary" and 
                          .key != "description" and 
                          .key != "operationId" and 
                          .key != "tags" and 
                          .key != "parameters" and
                          .key != "requestBody" and
                          .key != "responses"))' "$API_FILE"; then
  echo "      🟢 Operation properties sorted"
else
  echo "      🔴 Failed to sort operation properties"
  exit 1
fi

echo ""
echo "🎉 OpenAPI schema sorted successfully!"
echo "�� File: $API_FILE"
