#!/usr/bin/env python3
"""
Script for sorting methods in Go interfaces by CRUD principle.
Designed for files containing only interfaces (e.g., repository files).
"""

import argparse
import os
import re
import sys

def categorize_methods_by_crud(method_names):
    """Categorizes methods by CRUD operations with prefix priority"""
    create_methods = []
    read_methods = []
    update_methods = []
    delete_methods = []
    
    # Define prefix priorities for each CRUD operation
    create_prefixes = ['create', 'add', 'assign']
    read_prefixes = ['get', 'find', 'fetch', 'list', 'is', 'exist', 'has']
    update_prefixes = ['update', 'edit', 'modify', 'set']
    delete_prefixes = ['delete', 'remove', 'unassign', 'deactivate']
    
    def get_prefix_priority(method_name, prefixes):
        """Returns prefix priority and method name for sorting"""
        method_lower = method_name.lower()
        for i, prefix in enumerate(prefixes):
            if method_lower.startswith(prefix):
                return i, method_name.lower()
        return len(prefixes), method_name.lower()
    
    for method_name in method_names:
        method_lower = method_name.lower()
        
        # Check each CRUD category
        if any(method_lower.startswith(prefix) for prefix in create_prefixes):
            create_methods.append(method_name)
        elif any(method_lower.startswith(prefix) for prefix in read_prefixes):
            read_methods.append(method_name)
        elif any(method_lower.startswith(prefix) for prefix in update_prefixes):
            update_methods.append(method_name)
        elif any(method_lower.startswith(prefix) for prefix in delete_prefixes):
            delete_methods.append(method_name)
        else:
            # If unclear, assign to read operations
            read_methods.append(method_name)
    
    # Sort each group by prefix priority, then alphabetically
    create_sorted = sorted(create_methods, key=lambda x: get_prefix_priority(x, create_prefixes))
    read_sorted = sorted(read_methods, key=lambda x: get_prefix_priority(x, read_prefixes))
    update_sorted = sorted(update_methods, key=lambda x: get_prefix_priority(x, update_prefixes))
    delete_sorted = sorted(delete_methods, key=lambda x: get_prefix_priority(x, delete_prefixes))
    
    return create_sorted, read_sorted, update_sorted, delete_sorted

def find_interfaces_in_content(content):
    """Find all interfaces in the content with proper brace matching"""
    lines = content.split('\n')
    interfaces = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for interface declaration
        interface_match = re.match(r'^type\s+(\w+)\s+interface\s*\{', line)
        if interface_match:
            interface_name = interface_match.group(1)
            start_line = i
            brace_count = 1
            end_line = i
            
            # Find the end of the interface by counting braces
            j = i + 1
            while j < len(lines) and brace_count > 0:
                current_line = lines[j]
                # Count braces, but ignore those in strings and comments
                in_string = False
                in_comment = False
                escape_next = False
                
                k = 0
                while k < len(current_line):
                    char = current_line[k]
                    
                    if escape_next:
                        escape_next = False
                        k += 1
                        continue
                    
                    if char == '\\' and in_string:
                        escape_next = True
                        k += 1
                        continue
                    
                    if not in_comment:
                        if char == '"' and not in_string:
                            in_string = True
                        elif char == '"' and in_string:
                            in_string = False
                        elif char == '/' and k + 1 < len(current_line) and current_line[k + 1] == '/' and not in_string:
                            in_comment = True
                            break
                        elif not in_string:
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                    
                    k += 1
                
                if brace_count == 0:
                    end_line = j
                    break
                
                j += 1
            
            if brace_count == 0:  # Found complete interface
                interface_content = '\n'.join(lines[start_line:end_line + 1])
                interfaces.append({
                    'name': interface_name,
                    'content': interface_content,
                    'start_line': start_line,
                    'end_line': end_line
                })
            
            i = end_line + 1
        else:
            i += 1
    
    return interfaces

def parse_interface_methods(interface_content):
    """Extracts methods from interface content"""
    methods = []
    lines = interface_content.split('\n')
    
    # Skip the first line (interface declaration) and last line (closing brace)
    for line in lines[1:-1]:
        line = line.strip()
        
        # Skip empty lines and comments
        if not line or line.startswith('//'):
            continue
        
        # Look for method lines (contain parentheses)
        if '(' in line and ')' in line:
            # Extract method name
            method_match = re.match(r'^\s*(\w+)\s*\(', line)
            if method_match:
                method_name = method_match.group(1)
                methods.append((method_name, line))
    
    return methods

def sort_interface_methods(interface_content):
    """Sorts methods in interface by CRUD principle"""
    lines = interface_content.split('\n')
    
    if len(lines) < 3:  # Need at least opening, content, and closing
        return interface_content
    
    # Extract methods from the interface body (excluding first and last lines)
    methods = parse_interface_methods(interface_content)
    
    if not methods:
        return interface_content
    
    # Separate names and lines
    method_names = [method[0] for method in methods]
    method_lines = {method[0]: method[1] for method in methods}
    
    # Sort by CRUD
    create_methods, read_methods, update_methods, delete_methods = categorize_methods_by_crud(method_names)
    
    # Build result
    result_lines = [lines[0]]  # Interface declaration
    
    # Add CREATE methods
    if create_methods:
        for method_name in create_methods:
            if method_name in method_lines:
                result_lines.append('\t' + method_lines[method_name])
    
    # Add READ methods
    if read_methods:
        for method_name in read_methods:
            if method_name in method_lines:
                result_lines.append('\t' + method_lines[method_name])
    
    # Add UPDATE methods
    if update_methods:
        for method_name in update_methods:
            if method_name in method_lines:
                result_lines.append('\t' + method_lines[method_name])
    
    # Add DELETE methods
    if delete_methods:
        for method_name in delete_methods:
            if method_name in method_lines:
                result_lines.append('\t' + method_lines[method_name])
    
    # Remove trailing empty line if present
    if result_lines and result_lines[-1] == '':
        result_lines.pop()
    
    result_lines.append(lines[-1])  # Closing brace
    
    return '\n'.join(result_lines)

def process_file(file_path):
    """Processes file with interfaces"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all interfaces in the file
    interfaces = find_interfaces_in_content(content)
    
    if not interfaces:
        print("No interfaces found in file")
        return content
    
    print(f"Found {len(interfaces)} interface(s): {', '.join([iface['name'] for iface in interfaces])}")
    
    result_content = content
    
    # Process interfaces in reverse order to maintain line positions
    for interface_info in reversed(interfaces):
        original_content = interface_info['content']
        sorted_content = sort_interface_methods(original_content)
        result_content = result_content.replace(original_content, sorted_content)
    
    return result_content

def main():
    parser = argparse.ArgumentParser(
        description='Sort methods in Go interfaces by CRUD principle',
        epilog='''
Usage examples:
  python3 sort_interface_methods.py internal/domain/product/repository/product.go
  python3 sort_interface_methods.py internal/domain/user/repository/user.go
  python3 sort_interface_methods.py internal/domain/group/service/service.go
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('file_path', help='Path to file with interfaces')
    
    args = parser.parse_args()
    file_path = args.file_path
    
    if not os.path.exists(file_path):
        print(f"Error: file {file_path} not found")
        return 1
    
    print(f"Sorting interface methods: {file_path}")
    
    try:
        # Create backup
        backup_path = file_path + '.backup'
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # Process file
        sorted_content = process_file(file_path)
        
        # Check if content changed
        if sorted_content == original_content:
            print("ℹ️  No changes needed - interfaces are already sorted")
            os.remove(backup_path)  # Remove unnecessary backup
            return 0
        
        # Write result
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(sorted_content)
        
        print(f"✅ Interface methods successfully sorted!")
        print(f"📁 Backup saved to {backup_path}")
        
        return 0
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main()) 