#!/usr/bin/env python3
import re
import os
import sys
import argparse
from typing import List, Dict, Set, Tuple, Optional

class GoElement:
    """Представляет элемент Go кода"""
    def __init__(self, name: str, content: str, start_line: int, end_line: int, element_type: str):
        self.name = name
        self.content = content
        self.start_line = start_line
        self.end_line = end_line
        self.element_type = element_type  # 'type', 'func', 'method', 'const', 'var', 'init'

class GoParser:
    """Парсер Go файлов с новой логикой"""
    
    def __init__(self, content: str):
        self.content = content
        self.lines = content.split('\n')
        self.elements: List[GoElement] = []
        self.processed_lines: Set[int] = set()
        
    def parse(self) -> Dict[str, any]:
        """Основной метод парсинга"""
        result = {
            'package_and_imports': '',
            'standard_constants': [],  # константы стандартных типов
            'standard_variables': [],  # переменные стандартных типов
            'type_groups': [],  # [(type_name, type_element, constructor, methods)]
            'custom_constants': [],    # константы кастомных типов  
            'custom_variables': [],    # переменные кастомных типов
            'init_functions': [],
            'public_functions': [],
            'private_functions': []
        }
        
        # 1. Извлекаем package и imports
        result['package_and_imports'] = self._extract_package_and_imports()
        
        # 2. Извлекаем все элементы
        self._extract_all_elements()
        
        # 3. Группируем по типам
        result['type_groups'] = self._group_by_types()
        
        # 4. Разделяем константы и переменные
        all_constants = self._get_elements_by_type('const')
        all_variables = self._get_elements_by_type('var')
        
        # Получаем все имена кастомных типов
        custom_type_names = [type_name for type_name, _, _, _ in result['type_groups']]
        
        result['standard_constants'], result['custom_constants'] = self._separate_by_type_usage(all_constants, custom_type_names)
        result['standard_variables'], result['custom_variables'] = self._separate_by_type_usage(all_variables, custom_type_names)
        
        # 5. Группируем остальные элементы
        result['init_functions'] = self._get_elements_by_type('init')
        result['public_functions'] = self._get_public_functions()
        result['private_functions'] = self._get_private_functions()
        
        return result
    
    def _extract_package_and_imports(self) -> str:
        """Извлекает package и imports"""
        package_end = 0
        
        for i, line in enumerate(self.lines):
            stripped = line.strip()
            if stripped.startswith('import') or 'import (' in stripped:
                if '(' in stripped:
                    # Multi-line import
                    brace_count = 1
                    for j in range(i + 1, len(self.lines)):
                        if ')' in self.lines[j]:
                            brace_count -= self.lines[j].count(')')
                            if brace_count <= 0:
                                package_end = j + 1
                                break
                else:
                    # Single import
                    package_end = i + 1
                break
            elif stripped.startswith(('type ', 'const ', 'var ', 'func ', '//go:generate')):
                package_end = i
                break
        
        if package_end > 0:
            self.processed_lines.update(range(0, package_end))
            return '\n'.join(self.lines[:package_end]).strip()
        
        return ''
    
    def _extract_all_elements(self):
        """Извлекает все элементы из файла"""
        i = 0
        while i < len(self.lines):
            if i in self.processed_lines:
                i += 1
                continue
            
            line = self.lines[i].strip()
            
            # Пропускаем пустые строки и комментарии (они войдут в элементы)
            if not line or line.startswith('//'):
                i += 1
                continue
            
            # Ищем декларации
            if line.startswith('type '):
                element = self._extract_type_declaration(i)
                if element:
                    self.elements.append(element)
                    i = element.end_line
                else:
                    i += 1
            elif line.startswith('const '):
                element = self._extract_const_declaration(i)
                if element:
                    self.elements.append(element)
                    i = element.end_line
                else:
                    i += 1
            elif line.startswith('var '):
                element = self._extract_var_declaration(i)
                if element:
                    self.elements.append(element)
                    i = element.end_line
                else:
                    i += 1
            elif line.startswith('func '):
                element = self._extract_function(i)
                if element:
                    self.elements.append(element)
                    i = element.end_line
                else:
                    i += 1
            else:
                i += 1
    
    def _extract_type_declaration(self, start_line: int) -> Optional[GoElement]:
        """Извлекает объявление типа"""
        # Ищем предшествующие комментарии
        comment_start = self._find_preceding_comments(start_line)
        
        # Извлекаем имя типа
        type_line = self.lines[start_line].strip()
        type_match = re.search(r'type\s+(\w+)\s+(?:struct|interface|\w+)', type_line)
        if not type_match:
            return None
        
        type_name = type_match.group(1)
        
        # Определяем конец декларации
        end_line = self._find_declaration_end(start_line, type_line)
        
        # Собираем контент
        content = '\n'.join(self.lines[comment_start:end_line])
        
        # Отмечаем обработанные строки
        self.processed_lines.update(range(comment_start, end_line))
        
        return GoElement(type_name, content, comment_start, end_line, 'type')
    
    def _extract_const_declaration(self, start_line: int) -> Optional[GoElement]:
        """Извлекает объявление констант"""
        comment_start = self._find_preceding_comments(start_line)
        const_line = self.lines[start_line].strip()
        
        # Определяем имя константы или блока
        if '(' in const_line:
            # Блок констант
            name = 'const_block'
            end_line = self._find_block_end(start_line, '(', ')')
        else:
            # Одиночная константа
            const_match = re.search(r'const\s+(\w+)', const_line)
            name = const_match.group(1) if const_match else 'const'
            end_line = start_line + 1
        
        content = '\n'.join(self.lines[comment_start:end_line])
        self.processed_lines.update(range(comment_start, end_line))
        
        return GoElement(name, content, comment_start, end_line, 'const')
    
    def _extract_var_declaration(self, start_line: int) -> Optional[GoElement]:
        """Извлекает объявление переменных"""
        comment_start = self._find_preceding_comments(start_line)
        var_line = self.lines[start_line].strip()
        
        # Определяем имя переменной или блока
        if '(' in var_line:
            # Блок переменных
            name = 'var_block'
            end_line = self._find_block_end(start_line, '(', ')')
        else:
            # Одиночная переменная
            var_match = re.search(r'var\s+(\w+)', var_line)
            name = var_match.group(1) if var_match else 'var'
            end_line = start_line + 1
        
        content = '\n'.join(self.lines[comment_start:end_line])
        self.processed_lines.update(range(comment_start, end_line))
        
        return GoElement(name, content, comment_start, end_line, 'var')
    
    def _extract_function(self, start_line: int) -> Optional[GoElement]:
        """Извлекает функцию или метод"""
        comment_start = self._find_preceding_comments(start_line)
        
        # Собираем заголовок функции
        func_header = ''
        header_end = start_line
        for i in range(start_line, len(self.lines)):
            func_header += self.lines[i].strip() + ' '
            header_end = i + 1
            if '{' in func_header:
                break
        
        # Извлекаем имя функции и определяем тип
        func_match = re.search(r'func\s+(?:\([^)]*\)\s+)?(\w+)', func_header)
        if not func_match:
            return None
        
        func_name = func_match.group(1)
        
        # Определяем тип функции
        if func_name == 'init':
            element_type = 'init'
        elif re.search(r'func\s+\([^)]*\)\s+\w+', func_header):
            element_type = 'method'
        else:
            element_type = 'func'
        
        # Найти конец функции
        end_line = self._find_function_end(start_line)
        
        content = '\n'.join(self.lines[comment_start:end_line])
        self.processed_lines.update(range(comment_start, end_line))
        
        return GoElement(func_name, content, comment_start, end_line, element_type)
    
    def _find_preceding_comments(self, start_line: int) -> int:
        """Находит начало предшествующих комментариев"""
        comment_start = start_line
        for i in range(start_line - 1, -1, -1):
            line = self.lines[i].strip()
            if line.startswith('//'):
                comment_start = i
            elif not line:  # пустая строка
                continue
            else:
                break
        return comment_start
    
    def _find_declaration_end(self, start_line: int, declaration_line: str) -> int:
        """Находит конец объявления типа"""
        if '{' in declaration_line:
            # Struct или interface с телом
            return self._find_block_end(start_line, '{', '}')
        else:
            # Простое объявление типа
            return start_line + 1
    
    def _find_block_end(self, start_line: int, open_char: str, close_char: str) -> int:
        """Находит конец блока с фигурными/круглыми скобками"""
        brace_count = 0
        started = False
        
        for i in range(start_line, len(self.lines)):
            line = self.lines[i]
            if open_char in line:
                brace_count += line.count(open_char)
                started = True
            if close_char in line:
                brace_count -= line.count(close_char)
            
            if started and brace_count <= 0:
                return i + 1
        
        return len(self.lines)
    
    def _find_function_end(self, start_line: int) -> int:
        """Находит конец функции"""
        brace_count = 0
        started = False
        
        for i in range(start_line, len(self.lines)):
            line = self.lines[i]
            if '{' in line:
                brace_count += line.count('{')
                started = True
            if '}' in line:
                brace_count -= line.count('}')
            
            if started and brace_count <= 0:
                return i + 1
        
        return len(self.lines)
    
    def _group_by_types(self) -> List[Tuple[str, GoElement, Optional[GoElement], List[GoElement]]]:
        """Группирует типы с их конструкторами и методами"""
        type_groups = []
        types = [e for e in self.elements if e.element_type == 'type']
        methods = [e for e in self.elements if e.element_type == 'method']
        funcs = [e for e in self.elements if e.element_type == 'func']
        
        # Множество для отслеживания использованных элементов
        used_funcs = set()
        used_methods = set()
        
        # Сортируем типы: сначала конкретные типы (struct), потом интерфейсы  
        concrete_types = []
        interfaces = []
        
        for type_elem in types:
            if 'interface' in type_elem.content:
                interfaces.append(type_elem)
            else:
                concrete_types.append(type_elem)
        
        # Объединяем: сначала конкретные типы, потом интерфейсы
        sorted_types = concrete_types + interfaces
        
        for type_elem in sorted_types:
            type_name = type_elem.name
            
            # Ищем конструктор
            constructor = None
            for func in funcs:
                if func not in used_funcs and self._is_constructor_for_type(func.name, type_name):
                    constructor = func
                    used_funcs.add(func)
                    break
            
            # Если не нашли по имени, ищем по содержимому функции
            if not constructor:
                constructor = self._find_constructor_for_type(
                    [f for f in funcs if f not in used_funcs], 
                    type_name
                )
                if constructor:
                    used_funcs.add(constructor)
            
            # Ищем методы для этого типа
            type_methods = []
            for method in methods:
                if method not in used_methods and self._is_method_for_type(method.content, type_name):
                    type_methods.append(method)
                    used_methods.add(method)
            
            type_groups.append((type_name, type_elem, constructor, type_methods))
        
        # Удаляем только использованные методы, а не все методы
        self.elements = [e for e in self.elements if e.element_type != 'type']
        self.elements = [e for e in self.elements if e.element_type != 'method' or e not in used_methods]
        self.elements = [e for e in self.elements if e.element_type != 'func' or e not in used_funcs]
        
        return type_groups
    
    def _is_constructor_for_type(self, func_name: str, type_name: str) -> bool:
        """Проверяет, является ли функция конструктором для типа"""
        # Более строгая проверка имени конструктора
        if func_name.startswith('New'):
            # Удаляем префикс New и сравниваем с именем типа
            constructor_type = func_name[3:]  # Убираем "New"
            return constructor_type == type_name or constructor_type.startswith(type_name)
        return False
    
    def _find_constructor_for_type(self, funcs: List[GoElement], type_name: str) -> Optional[GoElement]:
        """Ищет конструктор для типа по анализу содержимого функции"""
        for func in funcs:
            if func.name.startswith('New'):
                # Проверяем, что функция создает и возвращает наш тип
                if re.search(rf'return\s+&{re.escape(type_name)}\{{', func.content):
                    return func
                # Или присваивает наш тип в переменную и возвращает её
                if re.search(rf'{re.escape(type_name)}\{{.*?\}}\s*$', func.content, re.MULTILINE | re.DOTALL):
                    return func
        return None
    
    def _is_method_for_type(self, method_content: str, type_name: str) -> bool:
        """Проверяет, является ли метод методом указанного типа"""
        # Ищем ресивер метода - более точный паттерн
        receiver_patterns = [
            rf'func\s+\(\s*\w+\s+\*{re.escape(type_name)}\s*\)',  # (s *TypeName)
            rf'func\s+\(\s*\w+\s+{re.escape(type_name)}\s*\)',   # (s TypeName)
        ]
        
        for pattern in receiver_patterns:
            if re.search(pattern, method_content):
                return True
        
        return False
    
    def _get_elements_by_type(self, element_type: str) -> List[GoElement]:
        """Получает элементы указанного типа"""
        return [e for e in self.elements if e.element_type == element_type]
    
    def _get_public_functions(self) -> List[GoElement]:
        """Получает публичные функции и методы внешних типов"""
        funcs = [e for e in self.elements if e.element_type in ['func', 'method']]
        return [f for f in funcs if f.name[0].isupper()]

    def _get_private_functions(self) -> List[GoElement]:
        """Получает приватные функции и методы внешних типов"""
        funcs = [e for e in self.elements if e.element_type in ['func', 'method']]
        return [f for f in funcs if f.name[0].islower()]
    
    def _separate_by_type_usage(self, elements: List[GoElement], custom_type_names: List[str]) -> Tuple[List[GoElement], List[GoElement]]:
        """Разделяет константы/переменные на стандартные и кастомные типы"""
        standard = []
        custom = []
        
        for element in elements:
            content = element.content
            
            # Проверяем, использует ли элемент кастомные типы
            uses_custom_type = False
            for type_name in custom_type_names:
                # Ищем использование кастомного типа в объявлении
                if re.search(rf'\b{re.escape(type_name)}\b', content):
                    uses_custom_type = True
                    break
            
            if uses_custom_type:
                custom.append(element)
            else:
                standard.append(element)
        
        return standard, custom

def categorize_methods_by_crud(method_names: List[str]) -> Tuple[List[str], List[str], List[str], List[str], List[str]]:
    """Categorize methods by CRUD operations with prefix priority"""
    create_methods = []
    read_methods = []
    update_methods = []
    delete_methods = []
    utility_methods = []
    
    # Define prefix priorities for each CRUD operation
    create_prefixes = ['new', 'init', 'create', 'add', 'assign']
    read_prefixes = ['get', 'find', 'fetch', 'list', 'is', 'exist', 'has']
    update_prefixes = ['update', 'edit', 'modify', 'set']
    delete_prefixes = ['delete', 'remove', 'unassign', 'deactivate']
    utility_prefixes = ['extract', 'identify', 'unmarshal', 'error', 'string']
    
    def get_prefix_priority(method_name: str, prefixes: List[str]) -> Tuple[int, str]:
        """Return prefix priority and method name for sorting"""
        method_lower = method_name.lower()
        for i, prefix in enumerate(prefixes):
            if method_lower.startswith(prefix):
                return i, method_name.lower()  # Sort by lowercase for alphabet
        return len(prefixes), method_name.lower()  # If not found, put at end
    
    for method_name in method_names:
        method_lower = method_name.lower()
        
        # Check utility functions first (highest priority)
        if any(method_lower.startswith(prefix) for prefix in utility_prefixes):
            utility_methods.append(method_name)
        # Check each CRUD category
        elif any(method_lower.startswith(prefix) for prefix in create_prefixes):
            create_methods.append(method_name)
        elif any(method_lower.startswith(prefix) for prefix in read_prefixes):
            read_methods.append(method_name)
        elif any(method_lower.startswith(prefix) for prefix in update_prefixes):
            update_methods.append(method_name)
        elif any(method_lower.startswith(prefix) for prefix in delete_prefixes):
            delete_methods.append(method_name)
        else:
            # If unclear, categorize as utility functions
            utility_methods.append(method_name)
    
    # Sort each group by prefix priority, then alphabetically
    create_sorted = sorted(create_methods, key=lambda x: get_prefix_priority(x, create_prefixes))
    read_sorted = sorted(read_methods, key=lambda x: get_prefix_priority(x, read_prefixes))
    update_sorted = sorted(update_methods, key=lambda x: get_prefix_priority(x, update_prefixes))
    delete_sorted = sorted(delete_methods, key=lambda x: get_prefix_priority(x, delete_prefixes))
    utility_sorted = sorted(utility_methods, key=lambda x: get_prefix_priority(x, utility_prefixes))
    
    return create_sorted, read_sorted, update_sorted, delete_sorted, utility_sorted

def reorganize_file_new_way(parsed_data: Dict) -> str:
    """Собирает файл в новом порядке"""
    result = []
    
    # 1. Package и imports
    if parsed_data['package_and_imports']:
        result.append(parsed_data['package_and_imports'])
        result.append('')
    
    # 2. Стандартные константы (до типов) - сортируем по алфавиту
    sorted_std_constants = sorted(parsed_data['standard_constants'], key=lambda x: x.name)
    for const in sorted_std_constants:
        result.append(const.content)
        result.append('')
    
    # 3. Стандартные переменные (до типов) - сортируем по алфавиту
    sorted_std_variables = sorted(parsed_data['standard_variables'], key=lambda x: x.name)
    for var in sorted_std_variables:
        result.append(var.content)
        result.append('')
    
    # 4. Типы с их константами/переменными, конструкторами и методами
    used_constant_indices = set()
    used_variable_indices = set()
    
    for type_name, type_elem, constructor, methods in parsed_data['type_groups']:
        # Объявление типа
        result.append(type_elem.content)
        result.append('')
        
        # Константы этого типа (сразу после объявления)
        type_constants = []
        for i, const in enumerate(parsed_data['custom_constants']):
            if i not in used_constant_indices:
                # Проверяем, использует ли константа данный тип в объявлении
                # Ищем паттерн: ConstantName [пробелы] TypeName [пробелы] = "value"
                pattern = rf'\b\w+\s+{re.escape(type_name)}\s*='
                if re.search(pattern, const.content, re.MULTILINE):
                    type_constants.append(const)
                    used_constant_indices.add(i)
        
        if type_constants:
            # Группируем константы в один блок
            const_lines = []
            constant_declarations = []  # Список для сортировки констант
            
            for const in type_constants:
                # Извлекаем только строки с константами (без комментариев заголовка)
                lines = const.content.split('\n')
                inside_block = False
                for line in lines:
                    if 'const (' in line:
                        inside_block = True
                        continue
                    elif inside_block and line.strip() == ')':
                        break
                    elif inside_block and line.strip() and not line.strip().startswith('//'):
                        constant_declarations.append(line)
            
            # Сортируем константы по алфавиту
            constant_declarations.sort(key=lambda x: x.strip().split()[0] if x.strip() else '')
            
            if constant_declarations:
                const_lines.append(f"// Константы для типа {type_name}")
                const_lines.append("const (")
                const_lines.extend(constant_declarations)
                const_lines.append(")")
                result.append('\n'.join(const_lines))
                result.append('')
        
        # Переменные этого типа  
        type_variables = []
        for i, var in enumerate(parsed_data['custom_variables']):
            if i not in used_variable_indices:
                # Проверяем, является ли переменная действительно переменной данного типа
                # Ищем паттерн: varName [пробелы] TypeName [пробелы] = value
                pattern = rf'\b\w+\s+{re.escape(type_name)}\s*='
                if re.search(pattern, var.content, re.MULTILINE):
                    type_variables.append(var)
                    used_variable_indices.add(i)
        
        if type_variables:
            # Для переменных просто добавляем их как есть (сортируем по имени переменной)
            sorted_type_variables = sorted(type_variables, key=lambda x: x.name)
            for var in sorted_type_variables:
                result.append(var.content)
                result.append('')
        
        # Конструктор
        if constructor:
            result.append(constructor.content)
            result.append('')
        
        # Методы с CRUD сортировкой
        method_names = [method.name for method in methods]
        create_methods, read_methods, update_methods, delete_methods, utility_methods = categorize_methods_by_crud(method_names)
        
        # Собираем методы в порядке CRUD
        ordered_method_names = create_methods + read_methods + update_methods + delete_methods + utility_methods
        
        # Добавляем методы в правильном порядке
        methods_dict = {method.name: method for method in methods}
        for method_name in ordered_method_names:
            if method_name in methods_dict:
                result.append(methods_dict[method_name].content)
                result.append('')
    
    # 5. Оставшиеся кастомные константы (не привязанные к типам) - сортируем по алфавиту
    remaining_constants = [const for i, const in enumerate(parsed_data['custom_constants']) 
                          if i not in used_constant_indices]
    sorted_remaining_constants = sorted(remaining_constants, key=lambda x: x.name)
    for const in sorted_remaining_constants:
        result.append(const.content)
        result.append('')
    
    # 6. Оставшиеся кастомные переменные (не привязанные к типам) - сортируем по алфавиту
    remaining_variables = [var for i, var in enumerate(parsed_data['custom_variables']) 
                          if i not in used_variable_indices]
    sorted_remaining_variables = sorted(remaining_variables, key=lambda x: x.name)
    for var in sorted_remaining_variables:
        result.append(var.content)
        result.append('')
    
    # 7. Init функции
    for init_func in parsed_data['init_functions']:
        result.append(init_func.content)
        result.append('')
    
    # 8. Публичные функции (сортированные по CRUD, потом по алфавиту)
    public_funcs = parsed_data['public_functions']
    public_func_names = [func.name for func in public_funcs]
    create_funcs, read_funcs, update_funcs, delete_funcs, utility_funcs = categorize_methods_by_crud(public_func_names)
    
    ordered_public_names = create_funcs + read_funcs + update_funcs + delete_funcs + utility_funcs
    public_funcs_dict = {func.name: func for func in public_funcs}
    
    for func_name in ordered_public_names:
        if func_name in public_funcs_dict:
            result.append(public_funcs_dict[func_name].content)
            result.append('')
    
    # 9. Приватные функции (сортированные по CRUD, потом по алфавиту)
    private_funcs = parsed_data['private_functions']
    private_func_names = [func.name for func in private_funcs]
    create_priv, read_priv, update_priv, delete_priv, utility_priv = categorize_methods_by_crud(private_func_names)
    
    ordered_private_names = create_priv + read_priv + update_priv + delete_priv + utility_priv
    private_funcs_dict = {func.name: func for func in private_funcs}
    
    for func_name in ordered_private_names:
        if func_name in private_funcs_dict:
            result.append(private_funcs_dict[func_name].content)
            result.append('')
    
    return '\n'.join(result).rstrip() + '\n'

def main():
    parser = argparse.ArgumentParser(
        description='Reorganize Go files in idiomatic order by types and their methods',
        epilog='''
New organization order:
1. Package and imports
2. Standard type constants and variables (string, int, bool, etc.)
3. For each custom type (concrete types first, then interfaces):
   a. Type declaration
   b. Constants of this type (grouped into single const block)
   c. Variables of this type
   d. Constructor for this type (New* function)
   e. Methods of this type (sorted by CRUD: Create → Read → Update → Delete → Utilities)
4. Remaining constants/variables (not tied to specific types)
5. Init functions
6. Public functions (sorted by CRUD: Create → Read → Update → Delete → Utilities)
7. Private functions (sorted by CRUD: Create → Read → Update → Delete → Utilities)

Type sorting: concrete types (structs, type aliases) come before interfaces
CRUD prefixes:
- Create: new, init, create, add, assign
- Read: get, find, fetch, list, is, exist, has
- Update: update, edit, modify, set
- Delete: delete, remove, unassign, deactivate
- Utilities: extract, identify, unmarshal, error, string

Usage examples:
  python3 reorganize_code.py internal/errkit/errkit.go
  python3 reorganize_code.py internal/domain/group/service/group.go
  python3 reorganize_code.py internal/domain/category/service/category.go
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('file_path', help='File path to reorganize (e.g.: internal/errkit/errkit.go)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file_path):
        print(f"Error: file {args.file_path} not found")
        return 1
    
    print(f"Reorganizing file: {args.file_path}")
    
    try:
        # Читаем файл
        with open(args.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Парсим
        parser_obj = GoParser(content)
        parsed_data = parser_obj.parse()
        
        # Реорганизуем
        reorganized_content = reorganize_file_new_way(parsed_data)
        
        # Создаем backup
        backup_path = args.file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Записываем результат
        with open(args.file_path, 'w', encoding='utf-8') as f:
            f.write(reorganized_content)
        
        # Статистика
        type_count = len(parsed_data['type_groups'])
        method_count = sum(len(methods) for _, _, _, methods in parsed_data['type_groups'])
        constructor_count = sum(1 for _, _, constructor, _ in parsed_data['type_groups'] if constructor)
        
        print(f"✅ File successfully reorganized!")
        print(f"📁 Backup saved to {backup_path}")
        print(f"📊 Types found: {type_count}")
        print(f"🏗️  Constructors found: {constructor_count}")
        print(f"🔧 Methods found: {method_count}")
        print(f"📋 Constants found: {len(parsed_data['standard_constants']) + len(parsed_data['custom_constants'])}")
        print(f"📝 Variables found: {len(parsed_data['standard_variables']) + len(parsed_data['custom_variables'])}")
        print(f"🔄 Init functions found: {len(parsed_data['init_functions'])}")
        print(f"🔓 Public functions found: {len(parsed_data['public_functions'])}")
        print(f"🔒 Private functions found: {len(parsed_data['private_functions'])}")
        
        return 0
        
    except Exception as e:
        print(f"Error processing file: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main()) 