#!/bin/bash

# Test coverage analyzer
# Analyzes and explains how coverage percentage is calculated

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "📊 COVERAGE ANALYZER"
echo "════════════════════"

# Define coverage file
COVERAGE_FILE=${1:-"coverage_all.out"}
SHOULD_CLEANUP=false

# Create coverage file if it doesn't exist
if [ ! -f "$COVERAGE_FILE" ]; then
    echo -e "  📝 Creating coverage file: ${COVERAGE_FILE}"
    echo -e "  🔧 Running tests for all packages..."
    
    # Get packages excluding mocks and gen
    packages=$(go list ./... | grep -v '/mocks' | grep -v '/gen/')
    
    # Run tests with coverage (hide output)
    if go test -coverprofile="$COVERAGE_FILE" $packages > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Tests completed successfully${NC}"
        SHOULD_CLEANUP=true
    else
        echo -e "  ${RED}❌ Error running tests${NC}"
        exit 1
    fi
else
    echo -e "  📁 Using existing file: ${COVERAGE_FILE}"
fi

# Analyze coverage data
echo ""
echo "📊 Analyzing coverage file..."

# Use fast AWK for calculation
awk_result=$(awk '
BEGIN {
    total = 0
    covered = 0
}

# Skip mode line
NR == 1 && /^mode:/ { next }

# Process coverage lines
NF >= 3 && $2 ~ /^[0-9]+$/ && $3 ~ /^[0-9]+$/ {
    statements = $2
    exec_count = $3
    total += statements
    if (exec_count > 0) {
        covered += statements
    }
}

END {
    percentage = (total > 0) ? (covered * 100.0 / total) : 0.0
    printf "%d,%d,%.1f", total, covered, percentage
}
' "$COVERAGE_FILE")

# Parse AWK results
total=$(echo "$awk_result" | cut -d',' -f1)
covered=$(echo "$awk_result" | cut -d',' -f2)
percentage=$(echo "$awk_result" | cut -d',' -f3)

echo ""
echo "═══════════════════════════════════════════════════════"
echo "📈 Total statements: $total"
echo "✅ Covered statements: $covered"
echo "❌ Uncovered statements: $((total - covered))"
echo ""
echo "🧮 Calculation: ($covered × 100) ÷ $total = $percentage%"
echo ""

# Apply color based on coverage percentage
coverage_num=${percentage%\%}
if command -v bc > /dev/null 2>&1 && [ -n "${coverage_num}" ]; then
    if (( $(echo "${coverage_num} <= 33" | bc -l) )); then
        echo -e "🎯 ${RED}RESULT: ${percentage}%${NC}"
        coverage_color="${RED}"
    elif (( $(echo "${coverage_num} <= 85" | bc -l) )); then
        echo -e "🎯 ${YELLOW}RESULT: ${percentage}%${NC}"
        coverage_color="${YELLOW}"
    else
        echo -e "🎯 ${GREEN}RESULT: ${percentage}%${NC}"
        coverage_color="${GREEN}"
    fi
else
    echo -e "🎯 ${GREEN}RESULT: ${percentage}%${NC}"
    coverage_color="${GREEN}"
fi

# Verify with Go tool
go_result=$(go tool cover -func="$COVERAGE_FILE" 2>/dev/null | tail -1 | grep -o '[0-9.]*%' || echo "N/A")
echo -e "🔧 Go tool result: ${go_result}"

if [ "$go_result" = "${percentage}%" ]; then
    echo -e "${GREEN}✅ Results match!${NC}"
else
    echo -e "${YELLOW}⚠️  Minor difference (likely rounding)${NC}"
fi

# Clean up coverage file if we created it
if [ "$SHOULD_CLEANUP" = true ]; then
    echo ""
    echo "🧹 Cleaning up temporary coverage file: $COVERAGE_FILE"
    rm -f "$COVERAGE_FILE"
    echo -e "${GREEN}✅ Cleanup completed${NC}"
fi

echo ""
echo -e "${GREEN}📈 Coverage analysis completed!${NC}" 