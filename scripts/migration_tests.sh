#!/bin/bash

# Exit on error
set -e

# --- Database Configuration ---
DB_HOST="${POSTGRES_HOST}"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="${POSTGRES_PASSWORD}"
TEST_DB_NAME="pms_test"
MIGRATION_FILE="migrations/20250625120634_consolidated_migration.sql"
TEST_DIR="migrations/tests"

# --- Check for POSTGRES_PASSWORD ---
if [ -z "${DB_PASSWORD}" ]; then
  echo "Error: POSTGRES_PASSWORD environment variable is not set."
  exit 1
fi

# --- Set PGPASSWORD for psql ---
export PGPASSWORD=$DB_PASSWORD

# --- 1. Create Test Database ---
echo "Dropping existing test database (if any)..."
# Terminate active connections to the test database
psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '${TEST_DB_NAME}' AND pid <> pg_backend_pid();" >/dev/null 2>&1 || true
dropdb -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" --if-exists "${TEST_DB_NAME}"
echo "Creating test database: ${TEST_DB_NAME}..."
createdb -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" "${TEST_DB_NAME}"
echo "Test database created."

# --- 2. Apply Migrations ---
echo "Applying migration: ${MIGRATION_FILE} to ${TEST_DB_NAME}..."
sed -n '/-- +goose Up/,/-- +goose Down/{/-- +goose Down/!p;}' ${MIGRATION_FILE} | psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${TEST_DB_NAME}
echo "Migration applied."

# --- 3. Run Tests ---
echo "Running tests from ${TEST_DIR}..."

# Initialize test tracking variables
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
FAILED_TEST_LIST=()

for test_file in $(ls ${TEST_DIR}/*.sql | sort); do
  test_name=$(basename ${test_file})
  echo "=========================================="
  echo "Executing test: ${test_name}..."
  echo "=========================================="

  TOTAL_TESTS=$((TOTAL_TESTS + 1))

  # Run test and capture exit code
  if psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${TEST_DB_NAME} -f ${test_file} > /dev/null 2>&1; then
    echo "✅ PASSED: ${test_name}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
  else
    echo "❌ FAILED: ${test_name}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    FAILED_TEST_LIST+=("${test_name}")

    # Show error details for failed tests
    echo "Error details:"
    psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${TEST_DB_NAME} -f ${test_file}
  fi
  echo ""
done

echo "=========================================="
echo "TEST EXECUTION SUMMARY"
echo "=========================================="
echo "Total tests: ${TOTAL_TESTS}"
echo "Passed: ${PASSED_TESTS}"
echo "Failed: ${FAILED_TESTS}"

if [ ${FAILED_TESTS} -gt 0 ]; then
  echo ""
  echo "Failed tests:"
  for failed_test in "${FAILED_TEST_LIST[@]}"; do
    echo "  - ${failed_test}"
  done
  echo ""
  echo "❌ Some tests failed!"
else
  echo ""
  echo "✅ All tests passed successfully!"
fi
echo "=========================================="

# --- 4. Drop Test Database ---
echo "Dropping test database: ${TEST_DB_NAME}..."
# Terminate active connections to the test database
psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '${TEST_DB_NAME}' AND pid <> pg_backend_pid();" >/dev/null 2>&1 || true
dropdb -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" "${TEST_DB_NAME}"
echo "Test database dropped."

# --- Unset PGPASSWORD ---
unset PGPASSWORD

# Final script result
if [ ${FAILED_TESTS} -gt 0 ]; then
  echo "Migration test script finished with ${FAILED_TESTS} failed test(s)!"
  exit 1
else
  echo "Migration test script finished successfully! All ${PASSED_TESTS} tests passed."
fi
