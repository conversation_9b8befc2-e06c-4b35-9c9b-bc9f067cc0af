# 📋 Скрипты автоматизации проекта

Набор инструментов для автоматизации разработки, проверки качества кода и анализа архитектуры проекта.

## 📖 Содержание

1. [🔧 Pre-push Check Script](#-pre-push-check-script)
   - [Описание](#-описание)
   - [Использование](#-использование)
   - [Режимы работы](#-режимы-работы)
   - [Интеллектуальное тестирование](#-интеллектуальное-тестирование)
   - [Требуемые инструменты](#-требуемые-инструменты)
   - [Структура вывода](#-структура-вывода)
   - [Современный дизайн](#-современный-дизайн)
   - [Обработка ошибок](#-обработка-ошибок)
   - [Интеграция с Git Hooks](#-интеграция-с-git-hooks)

2. [🔍 Анализ использования интерфейсов](#-анализ-использования-интерфейсов)
   - [Описание analyze_interface_usage.py](#1-описание-analyze_interface_usagepy)
   - [Использование](#2-использование)
   - [Примеры анализа](#3-примеры-анализа)

3. [🚀 Скрипты реорганизации Go кода](#-скрипты-реорганизации-go-кода)
   - [Универсальная реорганизация кода](#1-reorganize_codepy---универсальная-реорганизация-go-кода)
   - [Сортировка методов интерфейсов](#2-sort_interface_methodspy---interface-method-sorting)
   - [Общие принципы](#общие-принципы)

4. [📋 Конвенции проекта](#-конвенции-проекта)

---

## 🔧 Pre-push Check Script

Автоматизированный скрипт для проверки качества кода перед отправкой изменений в репозиторий.

## 📋 Описание

Скрипт `pre-push-check.sh` выполняет комплексную проверку кодовой базы, включая:

- 🔄 **Генерацию кода** (API, моки, SQLC)
- ✨ **Форматирование кода** (gofmt)
- 🔍 **Статический анализ** (go vet)
- 📦 **Управление зависимостями** (go mod tidy)
- 🧪 **Тесты** (go test ./...)
- 📊 **Покрытие тестами** (только стабильные пакеты)
- 🔨 **Сборку приложения**
- 🔗 **Соответствие эндпоинтов** (check-endpoint-methods-consistency.sh)
- 🔒 **Проверку безопасности** (gosec)
- 🛡️ **Поиск уязвимостей** (govulncheck)
- 📁 **Нормализацию файлов**

## 🚀 Использование

### Базовые команды

```bash
# Полная проверка (все проверки)
./scripts/pre-push-check.sh

# Быстрая проверка (пропуск медленных проверок)
FAST_CHECK=1 ./scripts/pre-push-check.sh

# Минимальная проверка (только критичные проверки)
MINIMAL_CHECK=1 ./scripts/pre-push-check.sh

# Проверка только версий инструментов
CHECK_VERSIONS=1 ./scripts/pre-push-check.sh

# Только тесты и покрытие
TESTS_ONLY=1 ./scripts/pre-push-check.sh

# Справка по всем опциям
./scripts/pre-push-check.sh --help
```

### Пропуск отдельных проверок

```bash
# Пропуск генерации моков
SKIP_MOCKS=1 ./scripts/pre-push-check.sh

# Пропуск тестов
SKIP_TESTS=1 ./scripts/pre-push-check.sh

# Пропуск проверки покрытия
SKIP_COVERAGE=1 ./scripts/pre-push-check.sh

# Пропуск проверки безопасности
SKIP_SECURITY=1 ./scripts/pre-push-check.sh

# Пропуск поиска уязвимостей
SKIP_VULNERABILITIES=1 ./scripts/pre-push-check.sh

# Пропуск SQLC генерации
SKIP_SQLC=1 ./scripts/pre-push-check.sh

# Пропуск проверки соответствия эндпоинтов
SKIP_ENDPOINT_CONSISTENCY=1 ./scripts/pre-push-check.sh
```

### Комбинирование флагов

```bash
# Пропуск нескольких проверок
SKIP_MOCKS=1 SKIP_COVERAGE=1 ./scripts/pre-push-check.sh

# Быстрая проверка с дополнительными пропусками
FAST_CHECK=1 SKIP_SECURITY=1 SKIP_ENDPOINT_CONSISTENCY=1 ./scripts/pre-push-check.sh
```

## 🎯 Режимы работы

### 🏃 Fast Mode (`FAST_CHECK=1`)
Пропускает медленные проверки:
- Генерацию моков
- Расчет покрытия тестами
- Проверку безопасности
- Поиск уязвимостей

### ⚡ Minimal Mode (`MINIMAL_CHECK=1`)
Выполняет только критичные проверки:
- Форматирование кода
- Статический анализ
- Соответствие эндпоинтов
- Сборку приложения

### 🔧 Version Check (`CHECK_VERSIONS=1`)
Проверяет только версии инструментов:
- `oapi-codegen`
- `minimock`
- `sqlc`

### 🧪 Tests Only (`TESTS_ONLY=1`)
Запускает только тесты и рассчитывает покрытие:
- Автоматически находит стабильные тест-пакеты
- Запускает тесты с детальным выводом
- Рассчитывает покрытие кода с цветовой индикацией

## 📊 Интеллектуальное тестирование

### 🧪 Тесты
- **Полные тесты**: Запускает `go test ./...` (все тесты, включая падающие)
- **Быстрая диагностика**: В режиме `TESTS_ONLY` использует оптимизированный поиск пакетов

### 📈 Покрытие тестами
Интеллектуальная система расчета покрытия:
- Автоматически находит все пакеты с тестами
- Исключает папки `/mocks/` и `/gen/`
- Тестирует стабильность пакетов (однократно для скорости)
- Рассчитывает корректное покрытие только по работающим тестам

### 🎨 Цветовая индикация покрытия
Применяется во всех режимах (основной, `TESTS_ONLY`):
- 🔴 **≤ 33%** - критично низкое (ошибка)
- 🟡 **34-85%** - требует внимания (предупреждение)
- 🟢 **> 85%** - отличное покрытие

## 🛠️ Требуемые инструменты

### Обязательные
- `go` - компилятор Go
- `gofmt` - форматирование кода
- `git` - система контроля версий

### Для генерации кода
- `oapi-codegen` - генерация API клиентов
- `minimock` - генерация моков
- `sqlc` - генерация SQL кода

### Для проверки безопасности (опционально)
- `gosec` - анализ безопасности
- `govulncheck` - поиск уязвимостей

### Установка инструментов

```bash
# Инструменты безопасности
go install github.com/securego/gosec/v2/cmd/gosec@latest
go install golang.org/x/vuln/cmd/govulncheck@latest

# Версии других инструментов автоматически определяются из go.mod
# и проверяются при запуске скрипта
```

## 📋 Структура вывода

Скрипт использует современный дизайн с прогресс-индикатором и цветными статусами:

### Основной режим

```
📋 ACTIVE MODES:
   • Skipping mock generation
   • Skipping SQLC generation

[1/10] 🔄 Code generation...
    🔧 Generate API files...
      🔧 Checking oapi-codegen version...
        🟢 oapi-codegen version v2.4.1 matches go.mod
    🟡 Generate mocks (skipped: SKIP_MOCKS or FAST_CHECK)
    🟡 Generate SQLC (skipped: SKIP_SQLC)
  🟢 Code generation

[2/10] ✨ Code formatting...
  🟢 Code formatting

[5/10] 🧪 Tests...
  🔴 Tests

[6/10] 📊 Test coverage...
  🔍 Calculating coverage for working test packages (excluding mocks and gen)...
  🟡 Test coverage: 45.2%
```

### Режим Tests Only

```
🧪 RUNNING TESTS AND COVERAGE ONLY
═══════════════════════════════════════════════════════════════
  🔍 Detecting test packages...

🧪 Running tests...
ok      git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/category/service
ok      git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/service
  🟢 All tests passed

📊 Calculating test coverage...
  🟡  Test coverage: 45.3%

═══════════════════════════════════════════════════════════════
🟢 Tests and coverage check completed successfully
   Tests: PASSED
   Coverage: 45.3%
```

### 🎨 Цветовые индикаторы статуса:
- **🟢** - Проверка прошла успешно
- **🔴** - Обнаружена критическая ошибка  
- **🟡** - Предупреждение или проверка пропущена

### 📊 Прогресс-индикатор:
- **[5/10]** - Показывает текущий прогресс выполнения
- Всего 10 основных проверок
- Помогает оценить оставшееся время

## 🎨 Современный дизайн

### ✨ Пользовательский интерфейс:
- **Прогресс в реальном времени** - видно сколько проверок осталось
- **Цветные статусы** - интуитивно понятные 🟢🔴🟡
- **Иерархическая структура** - четкие отступы для подпроверок
- **Консистентность** - единый стиль для всех элементов

### 🧠 Принципы UX:
- **Цветовая схема светофора** - зеленый/красный/желтый
- **Единообразные эмодзи** - все статусы в виде цветных кругов
- **Быстрое сканирование** - проблемы видны с первого взгляда
- **Минимум шума** - чистый и понятный вывод

## 🚨 Обработка ошибок

### При ошибках скрипт выводит:
- 📊 **Сводку результатов**
- 🔴 **Список критических ошибок**
- 🟡 **Предупреждения и пропущенные проверки**
- 🔧 **Конкретные диагностические команды**

### Пример итоговой сводки:
```bash
═══════════════════════════════════════════════════════════════
📊 CHECKS SUMMARY
═══════════════════════════════════════════════════════════════
Total checks: 8
Passed: 6
Errors: 1
Warnings: 1
Skipped: 3

🔴 ERRORS:
   • Tests

🟡 WARNINGS:
   • Test coverage: 45.2%

🟡 SKIPPED:
   • Generate mocks
   • Security
   • Vulnerabilities

🔴 Critical errors found. Fix them before pushing.

🔧 Diagnostic commands:
   go test ./...                 # Run all tests with details
```

## 🔍 Проверка версий инструментов

Скрипт автоматически проверяет соответствие версий установленных инструментов версиям в `go.mod`:

```bash
🔧 CHECKING TOOL VERSIONS ONLY
═══════════════════════════════════════════════════════════════

🔧 oapi-codegen:
        🟢 oapi-codegen version v2.4.1 matches go.mod

🧪 minimock:
        🔴 minimock not installed

🗃️ sqlc:
        🔴 sqlc version mismatch: installed v1.28.0, expected v1.29.0

═══════════════════════════════════════════════════════════════
🟢 Version check completed
```

## 📝 Ручная проверка безопасности

Для ручного запуска проверки безопасности используйте:

```bash
# Рекомендуемые флаги для gosec
gosec -exclude-generated -exclude='*/mocks/*' ./...
```

## 🎯 Интеграция с Git Hooks

Для автоматического запуска перед push добавьте в `.git/hooks/pre-push`:

```bash
#!/bin/bash
exec ./scripts/pre-push-check.sh
```

Сделайте хук исполняемым:
```bash
chmod +x .git/hooks/pre-push
```

## 📈 Коды возврата

- `0` - Все критические проверки прошли успешно
- `1` - Обнаружены критические ошибки, требующие исправления

## 🔧 Диагностические команды

При ошибках скрипт автоматически предлагает команды для исправления:

- **Code generation**: `make generate-all`, `make generate-mocks`, `make generate-sqlc`, `make sqlc-vet`
- **Formatting**: `gofmt -l .`, `gofmt -w .`
- **Static analysis**: `go vet ./...`
- **Tests**: `go test ./...`
- **Build**: `go build ./cmd/pms.go`
- **Coverage**: `go test -cover ./...`
- **Endpoint consistency**: `./scripts/check-endpoint-methods-consistency.sh`

## 🤝 Поддержка

При возникновении проблем:

1. Запустите с флагом `--help` для справки
2. Проверьте версии инструментов: `CHECK_VERSIONS=1 ./scripts/pre-push-check.sh`
3. Используйте диагностические команды из вывода скрипта
4. Проверьте установку требуемых инструментов

---

## 🚀 Ключевые особенности

- **🤖 Автоматическая адаптация** - не требует ручного обновления списков тестов
- **🎯 Интеллектуальное тестирование** - автоматически находит стабильные пакеты
- **📊 Прогресс в реальном времени** - точный счетчик выполненных проверок
- **🎨 Современный дизайн** - интуитивно понятный интерфейс с эмодзи
- **⚡ Гибкие режимы** - от быстрой проверки до полного анализа
- **🔧 Умная диагностика** - конкретные команды для исправления проблем
- **📋 Проверка версий** - автоматическое сопоставление с go.mod
- **🧪 Специальные режимы** - отдельные режимы для тестов и проверки версий
- **🔗 Проверка эндпоинтов** - автоматическая валидация соответствия OpenAPI и авторизации

*Скрипт создан с учетом современных принципов UX и автоматически адаптируется к изменениям в кодовой базе.*

---

## 🔍 Анализ использования интерфейсов

### 1. Описание `analyze_interface_usage.py`

**Назначение**: Универсальный анализатор использования методов доменных сервисов Go. Автоматически парсит интерфейсы и выявляет неиспользуемые методы и методы только для внутреннего использования.

**Функциональность**:
- 📊 **Автоматический парсинг интерфейсов** - извлекает все методы из Go интерфейса
- 🔍 **Анализ внешнего использования** - ищет вызовы через `{domain}Domain.{method}`
- 🏠 **Анализ внутреннего использования** - находит вызовы внутри пакета через `s.{method}(`
- 📈 **Классификация методов**:
  - ❌ **Неиспользуемые** - методы без использования
  - ⚠️ **Internal-only** - используются только внутри пакета
  - ✅ **Внешне используемые** - используются в других пакетах

**Автоматическое извлечение**:
- **Путь пакета** из пути до интерфейса
- **Доменное имя** из структуры папок (`internal/domain/{domain}/service/`)
- **Список методов** из определения интерфейса

### 2. Использование

```bash
# Анализ GroupDomainService
python3 scripts/analyze_interface_usage.py internal/domain/group/service/service.go

# Анализ CategoryDomainService  
python3 scripts/analyze_interface_usage.py internal/domain/category/service/category.go

# Любой другой доменный сервис
python3 scripts/analyze_interface_usage.py путь/до/интерфейса.go

# Справка
python3 scripts/analyze_interface_usage.py --help
```

### 3. Примеры анализа

#### Анализ GroupDomainService
```bash
$ python3 scripts/analyze_interface_usage.py internal/domain/group/service/service.go

🔍 Analyzing GroupDomainService methods...
📁 Interface: internal/domain/group/service/service.go
📁 Service dir: internal/domain/group/service/
📋 Found 37 methods: Create, CreateAsAdmin, AssignToRoles, ...

============================================================
📊 ANALYSIS RESULTS FOR GROUPDOMAINSERVICE
============================================================

❌ UNUSED METHODS (2):
  - GetByRoleID
  - GetParticipantGroupByParticipantID

⚠️  INTERNAL-ONLY METHODS (9):
  - AssignToRoles (1 internal usages)
    └─ internal/domain/group/service/group.go:593
  - FetchParticipantIDs (1 internal usages)
    └─ internal/domain/group/service/group.go:123
  ...

✅ EXTERNALLY USED METHODS (26):
  - Create (2 external, 0 internal)
    └─ ./internal/application/group/service/group.go:42
    └─ ./internal/application/group/service/admin_group.go:16
  ...
```

#### Анализ CategoryDomainService
```bash
$ python3 scripts/analyze_interface_usage.py internal/domain/category/service/category.go

📊 ANALYSIS RESULTS FOR CATEGORYDOMAINSERVICE
============================================================

❌ UNUSED METHODS (1):
  - GetFullByID

✅ EXTERNALLY USED METHODS (7):
  - Create, GetAll, GetByID, GetByName, GetCategoryFull, Update, Delete
```

**Применение результатов**:
- **Неиспользуемые методы** → можно безопасно удалить
- **Internal-only методы** → кандидаты для преобразования в приватные методы
- **Внешне используемые** → ключевые методы архитектуры, нельзя удалять

---

## 🚀 Скрипты реорганизации Go кода

### Уникальная реорганизация кода

#### 1. `reorganize_code.py` - Универсальная реорганизация Go кода

**Назначение**: Универсальная реорганизация любых Go файлов с идиоматичной организацией кода.

**🚀 Возможности v2.0:**

- **Универсальность** - работает с любыми Go файлами (не только сервисами)
- **CRUD сортировка** - методы и функции сортируются по префиксам
- **Группировка по типам** - константы каждого типа размещаются сразу после его объявления
- **Улучшенная логика** - интеллектуальный поиск конструкторов по содержимому
- **Правильный порядок типов** - конкретные типы перед интерфейсами

**📐 Новая организация файла:**

```
1. Package и imports
2. Стандартные константы/переменные (string, int, bool)
3. Для каждого типа (конкретные типы → интерфейсы):
   a. Объявление типа
   b. Константы этого типа (в едином блоке)  
   c. Переменные этого типа
   d. Конструктор (New* функция)
   e. Методы типа (CRUD сортировка)
4. Оставшиеся константы/переменные
5. Init функции
6. Публичные функции (CRUD сортировка) 
7. Приватные функции (CRUD сортировка)
```

**🎯 CRUD сортировка префиксов:**

- **Create**: `new`, `init`, `create`, `add`, `assign`
- **Read**: `get`, `find`, `fetch`, `list`, `is`, `exist`, `has`
- **Update**: `update`, `edit`, `modify`, `set`
- **Delete**: `delete`, `remove`, `unassign`, `deactivate` 
- **Utilities**: `extract`, `identify`, `unmarshal`, `error`, `string`

**Использование**:
```bash
# Универсальная реорганизация любого Go файла
python3 scripts/reorganize_code.py internal/errkit/errkit.go
python3 scripts/reorganize_code.py internal/domain/group/service/service.go
python3 scripts/reorganize_code.py internal/domain/permission/service/permission.go

# Справка по всем возможностям
python3 scripts/reorganize_code.py --help
```

**✅ Решенные проблемы v2.0:**

- ❌ ~~Ломал файлы с embedded fields~~ → ✅ **Корректно обрабатывает встроенные поля**
- ❌ ~~Работал только с сервисами~~ → ✅ **Универсальный для любых Go файлов**
- ❌ ~~Нарушал порядок зависимостей~~ → ✅ **Константы сразу после типов** 
- ❌ ~~Неправильная группировка методов~~ → ✅ **CRUD сортировка с приоритетами**

#### 2. `sort_interface_methods.py` - Interface Method Sorting

**Purpose**: Sort methods in interfaces by CRUD principle (for repository, cache files).

**Method sorting**:
- **Create**: create, add, assign
- **Read**: get, find, fetch, list, is, exist, has  
- **Update**: update, edit, modify, set
- **Delete**: delete, remove, unassign, deactivate

**Usage**:
```bash
python3 scripts/sort_interface_methods.py internal/domain/product/repository/product.go
python3 scripts/sort_interface_methods.py internal/domain/user/repository/user.go
```

### Общие принципы

#### CRUD сортировка
1. **Create** операции (create, add, assign)
2. **Read** операции (get, find, fetch, list, is, exist, has)
3. **Update** операции (update, edit, modify, set)
4. **Delete** операции (delete, remove, unassign, deactivate)

#### Приоритеты префиксов
Внутри каждой CRUD группы методы сортируются:
1. По приоритету префикса (create > add > assign)
2. По алфавиту

#### Безопасность
- Автоматическое создание `.backup` файлов
- Сохранение оригинального форматирования
- Обработка ошибок

### Примеры

#### До реорганизации:
```go
type UserService interface {
    Update(data UserData) error
    Delete(id int64) error
    GetByID(id int64) (User, error)
    Create(data UserData) (User, error)
    GetAll() ([]User, error)
}
```

#### После реорганизации:
```go
type UserService interface {
    Create(data UserData) (User, error)
    GetAll() ([]User, error)
    GetByID(id int64) (User, error)
    Update(data UserData) error
    Delete(id int64) error
}
```

---

## 📋 Конвенции проекта

### Именование скриптов
- **Python скрипты (.py)**: `snake_case` - следует PEP 8 стандарту
- **Shell скрипты (.sh)**: `kebab-case` - традиционный Unix подход

### Структура папки scripts/
```
scripts/
├── README.md                    # Документация (русский)
├── analyze_interface_usage.py   # Python: snake_case
├── reorganize_code.py        # Python: snake_case
├── sort_interface_methods.py    # Python: snake_case  
├── sort-openapi.sh             # Shell: kebab-case
├── setup-bruno-api.sh          # Shell: kebab-case
├── pre-push-check.sh           # Shell: kebab-case
└── check-endpoint-methods-consistency.sh # Shell: kebab-case
```

### Языки
- **Комментарии в скриптах**: английский
- **README файлы**: русский
- **Справочная информация (--help)**: английский

---

## 🔄 Проверка соответствия эндпоинтов

### `check-endpoint-methods-consistency.sh`

**Назначение**: Автоматическая проверка соответствия эндпоинтов и HTTP методов между спецификацией OpenAPI и конфигурацией авторизации.

**Функциональность**:
- 📋 **Извлечение эндпоинтов** из `api/openapi.yaml` с методами
- 🔍 **Анализ правил авторизации** из `configs/authorization.yaml`
- ⚖️ **Интеллектуальное сравнение** - различает полное отсутствие эндпоинтов от различий в методах
- 🎨 **Цветная индикация** - красные ошибки, желтые предупреждения, зеленые успехи
- 📊 **Детальная статистика** по секциям конфигурации
- 🔢 **Точный подсчёт проблем** - каждый отсутствующий метод считается отдельно
- 📝 **Детальное отображение методов** - показывает конкретные HTTP методы вместо "all methods"

**Проверяемые секции**:
- `excluded_paths` - эндпоинты без проверки прав
- `endpoint_permissions` - эндпоинты с проверкой прав

**Использование**:
```bash
# Полная проверка соответствия
./scripts/check-endpoint-methods-consistency.sh

# Интегрирована в pre-push-check.sh
./scripts/pre-push-check.sh

# Включена в минимальные проверки
MINIMAL_CHECK=1 ./scripts/pre-push-check.sh

# Пропуск при необходимости
SKIP_ENDPOINT_CONSISTENCY=1 ./scripts/pre-push-check.sh
```

**Пример вывода**:
```
🔍 Checking endpoint and method consistency between api/openapi.yaml and configs/authorization.yaml
═══════════════════════════════════════════════════════════════

🔴 ERRORS - Endpoints completely missing in configs/authorization.yaml:
   • /v1/admin/groups (GET,POST)
   • /v1/admin/groups/{groupID} (DELETE,GET,PATCH)
   • /v1/events/source (GET)
   • /v1/products/{productID}/proposals/{proposalID}/message (POST)

🔴 ERRORS - Methods missing in configs/authorization.yaml (endpoint exists but methods differ):
   • /v1/admin/users/{userID}:DELETE
   • /v1/users:POST
   • /v1/users/{userID}/products:DELETE

🟡 WARNINGS - Endpoints completely missing in api/openapi.yaml:
   • /v1/user/{userID}/groups (GET)

🟡 WARNINGS - Methods exist in configs/authorization.yaml but missing in api/openapi.yaml:
   • /v1/products/{productID}/participants/{participantID}/groups:GET
   • /v1/products/{productID}/participants/{participantID}/roles:GET

═══════════════════════════════════════════════════════════════
📊 ANALYSIS SUMMARY
═══════════════════════════════════════════════════════════════
Total endpoints:
   OpenAPI endpoints+methods: 91
   OpenAPI unique paths: 50
   Authorization endpoints+methods: 62
   Authorization unique paths: 35

Issues found:
   Errors: 32
   Warnings: 3

🔴 FAILED: Critical inconsistencies found between files
   Need to update configs/authorization.yaml according to api/openapi.yaml
```

**Возвращаемые коды**:
- `0` - Все эндпоинты соответствуют (🟢 зеленый итог)
- `1` - Найдены критичные ошибки (🔴 красный итог)  
- `1` - Только предупреждения (🟡 желтый итог)

**Интеграция в процесс разработки**:
- ✅ **Автоматически запускается** в `pre-push-check.sh` как критичная проверка
- ✅ **Включена в минимальные проверки** (`MINIMAL_CHECK=1`)
- 🔗 **Влияет на общий результат** - ошибки блокируют push
- 🎯 **Умная диагностика** - показывает конкретные команды для исправления
- 📊 **Цветная индикация** в общей сводке `pre-push-check.sh`

**Особенности подсчёта v2.0**:
- 🔢 **Подсчёт по методам**: Каждый отсутствующий HTTP метод считается отдельной ошибкой/предупреждением
- 📝 **Детальное отображение**: Показывает конкретные методы `(GET,POST)` вместо `(2 methods)`
- 📊 **Реалистичные числа**: Общее количество проблем отражает фактический объём работы по исправлению
- 🎯 **Точная диагностика**: Разработчик сразу видит, какие именно методы нужно добавить

**Пример быстрой проверки**:
```bash
# Быстрая проверка только критичных элементов
SKIP_TESTS=1 SKIP_COVERAGE=1 SKIP_SECURITY=1 SKIP_VULNERABILITIES=1 ./scripts/pre-push-check.sh
```
