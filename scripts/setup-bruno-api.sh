#!/bin/bash

# Bruno API Setup Script
# Configures authentication and structure for Bruno API collection

set -e

API_DIR="api/.bru/PMS API"

echo "🔧 Bruno API Setup"
echo "=================="
echo ""

# [1/5] Check directory existence
echo "[1/5] 📁 Checking Bruno API directory..."
if [ ! -d "$API_DIR" ]; then
    echo "      🔴 Directory $API_DIR not found!"
    echo "      💡 Ensure Bruno API collection exists"
    exit 1
fi
echo "      🟢 Directory found: $API_DIR"

# [2/5] Update all .bru files in subfolders
echo "[2/5] 📝 Updating .bru files in subfolders..."

file_count=0
processed_count=0

# Count files first
while IFS= read -r file; do
    ((file_count++))
done < <(find "$API_DIR" -name "*.bru" -not -path "*/environments/*" -not -name "collection.bru" -not -name "folder.bru")

if [ $file_count -eq 0 ]; then
    echo "      🟡 No .bru files found"
else
    echo "      🔍 Found $file_count .bru files to process"
    
    find "$API_DIR" -name "*.bru" -not -path "*/environments/*" -not -name "collection.bru" -not -name "folder.bru" | while read -r file; do
        ((processed_count++))
        echo "      ⚙️  Processing [$processed_count/$file_count]: $(basename "$file")"
        
        # Create temporary file
        temp_file=$(mktemp)
        
        # Read and process file line by line
        inside_method_block=false
        method_block=""
        has_auth_line=false
        
        while IFS= read -r line; do
            # Check method block start (get, post, put, delete, patch, etc.)
            if echo "$line" | grep -E "^(get|post|put|delete|patch|head|options) \{" > /dev/null; then
                inside_method_block=true
                method_block="$line"
                has_auth_line=false
                echo "$line" >> "$temp_file"
                continue
            fi
            
            # If inside method block
            if [ "$inside_method_block" = true ]; then
                # Check block closing
                if [ "$line" = "}" ]; then
                    inside_method_block=false
                    # Add auth: inherit before closing block, only if no auth line exists
                    if [ "$has_auth_line" = false ]; then
                        echo "  auth: inherit" >> "$temp_file"
                    fi
                    echo "$line" >> "$temp_file"
                    method_block=""
                    has_auth_line=false
                    continue
                else
                    # Check for auth: line
                    if echo "$line" | grep -E "^\s*auth:" > /dev/null; then
                        has_auth_line=true
                        # Replace any auth: line with auth: inherit
                        echo "$line" | sed 's/auth:.*/auth: inherit/' >> "$temp_file"
                    else
                        echo "$line" >> "$temp_file"
                    fi
                    # Collect entire method block for verification
                    method_block="$method_block"$'\n'"$line"
                    continue
                fi
            fi
            
            # Remove existing auth:bearer blocks
            if echo "$line" | grep -E "^auth:bearer \{" > /dev/null; then
                # Skip auth:bearer block
                while IFS= read -r auth_line; do
                    if [ "$auth_line" = "}" ]; then
                        break
                    fi
                done
                continue
            fi
            
            echo "$line" >> "$temp_file"
        done < "$file"
        
        # Add auth:bearer block to end of file
        echo "auth:bearer {" >> "$temp_file"
        echo "  token: {{token}}" >> "$temp_file"
        echo "}" >> "$temp_file"
        echo "" >> "$temp_file"
        
        # Replace original file
        mv "$temp_file" "$file"
    done
    
    echo "      🟢 Processed $file_count .bru files"
fi

# [3/5] Update collection.bru
echo "[3/5] 📄 Updating collection.bru..."
collection_file="$API_DIR/collection.bru"

if cat > "$collection_file" << 'EOF'
auth {
  mode: oauth2
}

auth:oauth2 {
  grant_type: password
  access_token_url: {{keycloakURL}}/realms/ecpk/protocol/openid-connect/token
  refresh_token_url: 
  username: {{login}}
  password: {{password}}
  client_id: pms-c
  client_secret: 
  scope: 
  credentials_placement: body
  credentials_id: credentials
  token_placement: header
  token_header_prefix: Bearer
  auto_fetch_token: true
  auto_refresh_token: true
}
EOF
then
    echo "      🟢 collection.bru updated"
else
    echo "      🔴 Failed to update collection.bru"
    exit 1
fi

# [4/5] Update seq in folder.bru files
echo "[4/5] 🔢 Updating folder numbering in folder.bru..."

# Get list of all folders, excluding environments, and sort alphabetically
folders=$(find "$API_DIR" -mindepth 1 -maxdepth 1 -type d -not -name "environments" | sort)
folder_count=$(echo "$folders" | wc -l)

if [ -z "$folders" ] || [ "$folder_count" -eq 0 ]; then
    echo "      🟡 No folders found"
else
    echo "      🔍 Found $folder_count folders to number"
    
    seq_num=1
    for folder in $folders; do
        folder_bru="$folder/folder.bru"
        if [ -f "$folder_bru" ]; then
            folder_name=$(basename "$folder")
            echo "      ⚙️  Folder [$seq_num/$folder_count]: $folder_name -> seq: $seq_num"
            
            # Create temporary file
            temp_file=$(mktemp)
            
            # Update seq in folder.bru
            while IFS= read -r line; do
                if echo "$line" | grep -E "^\s*seq:" > /dev/null; then
                    echo "  seq: $seq_num" >> "$temp_file"
                else
                    echo "$line" >> "$temp_file"
                fi
            done < "$folder_bru"
            
            # Replace original file
            mv "$temp_file" "$folder_bru"
            
            ((seq_num++))
        else
            folder_name=$(basename "$folder")
            echo "      🟡 folder.bru not found in: $folder_name"
        fi
    done
    
    echo "      🟢 Numbered $((seq_num-1)) folders"
fi

# [5/5] Rename environment files
echo "[5/5] 🌍 Renaming environment files..."

if [ -d "$API_DIR/environments" ]; then
    env_files=$(find "$API_DIR/environments" -name "*.bru")
    env_count=$(echo "$env_files" | wc -l)
    
    if [ -z "$env_files" ] || [ "$env_count" -eq 0 ]; then
        echo "      🟡 No environment .bru files found"
    else
        echo "      🔍 Found $env_count environment files"
        
        processed_env=0
        echo "$env_files" | while read -r env_file; do
            ((processed_env++))
            # Extract baseUrl from file
            base_url=$(awk '/baseUrl:/ {print $2}' "$env_file")
            
            if [ -n "$base_url" ]; then
                echo "      ⚙️  File [$processed_env/$env_count]: baseUrl = $base_url"
                
                # Determine new filename based on baseUrl
                new_name=""
                if echo "$base_url" | grep -q "\.ecpk-dev\."; then
                    new_name="dev.bru"
                elif echo "$base_url" | grep -q "\.ecpk\."; then
                    new_name="prod.bru"
                elif echo "$base_url" | grep -q "localhost"; then
                    new_name="local.bru"
                fi
                
                if [ -n "$new_name" ]; then
                    new_path="$API_DIR/environments/$new_name"
                    old_name=$(basename "$env_file")
                    
                    # Rename file if name changed
                    if [ "$old_name" != "$new_name" ]; then
                        echo "        🔄 $old_name -> $new_name"
                        mv "$env_file" "$new_path"
                    else
                        echo "        🟢 $old_name already has correct name"
                    fi
                else
                    echo "        🟡 Could not determine type for baseUrl: $base_url"
                fi
            else
                echo "        🟡 baseUrl not found in file: $(basename "$env_file")"
            fi
        done
        
        echo "      🟢 Processed $env_count environment files"
    fi
else
    echo "      🟡 Environments folder not found"
fi

echo ""
echo "🎉 Bruno API setup completed successfully!"
echo ""
echo "📊 Operations completed:"
echo "  🔧 Updated auth blocks in all .bru files"
echo "  🔐 Set auth: inherit in methods"
echo "  📄 Updated collection.bru"
echo "  🔢 Numbered folders in folder.bru files"
echo "  🌍 Renamed environment files"
echo ""
echo "💡 You can now restart Bruno to apply changes" 