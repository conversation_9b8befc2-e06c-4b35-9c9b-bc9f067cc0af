#!/bin/bash

# End-to-End тесты для PMS API
# Скрипт для запуска E2E тестов с настройкой тестовой среды

set -e

# --- Конфигурация ---
# Используем те же переменные, что и в migration_tests.sh
DB_HOST="${POSTGRES_HOST:-localhost}"
DB_PORT="${POSTGRES_PORT:-5432}"
DB_USER="${POSTGRES_USER:-postgres}"
DB_PASSWORD="${POSTGRES_PASSWORD}"

# Дополнительные переменные для Redis и Keycloak
REDIS_HOST="${REDIS_HOST:-localhost}"
REDIS_PORT="${REDIS_PORT:-6379}"
REDIS_PASSWORD="${REDIS_PASSWORD}"
KEYCLOAK_URL="${KEYCLOAK_URL:-http://localhost:8080}"
KEYCLOAK_REALM="${KEYCLOAK_REALM:-master}"
KEYCLOAK_CLIENT_ID="${KEYCLOAK_CLIENT_ID:-admin-cli}"
KEYCLOAK_USERNAME="${KEYCLOAK_USERNAME:-admin}"
KEYCLOAK_PASSWORD="${KEYCLOAK_PASSWORD}"

# --- Проверка переменных окружения ---
if [ -z "${DB_PASSWORD}" ]; then
  echo "Error: POSTGRES_PASSWORD environment variable is not set."
  exit 1
fi

echo "=========================================="
echo "PMS E2E TESTS"
echo "=========================================="
echo "Database Host: ${DB_HOST}"
echo "Database Port: ${DB_PORT}"
echo "Database User: ${DB_USER}"
echo "Redis Host: ${REDIS_HOST}"
echo "Redis Port: ${REDIS_PORT}"
echo "Keycloak URL: ${KEYCLOAK_URL}"
echo "Keycloak Realm: ${KEYCLOAK_REALM}"
echo "=========================================="

# --- Проверка доступности PostgreSQL ---
echo "Checking PostgreSQL connection..."
export PGPASSWORD=$DB_PASSWORD
if ! psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d postgres -c "SELECT 1;" >/dev/null 2>&1; then
  echo "❌ Failed to connect to PostgreSQL at ${DB_HOST}:${DB_PORT}"
  echo "Please ensure PostgreSQL is running and credentials are correct."
  exit 1
fi
echo "✅ PostgreSQL connection successful"

# --- Запуск E2E тестов ---
echo ""
echo "Running E2E tests..."
echo "=========================================="

# Переходим в директорию с интеграционными тестами
cd tests/e2e/integration

# Экспортируем переменные окружения для тестов
export POSTGRES_HOST=$DB_HOST
export POSTGRES_PORT=$DB_PORT
export POSTGRES_USER=$DB_USER
export POSTGRES_PASSWORD=$DB_PASSWORD
export REDIS_HOST=$REDIS_HOST
export REDIS_PORT=$REDIS_PORT
export REDIS_PASSWORD=$REDIS_PASSWORD
export KEYCLOAK_URL=$KEYCLOAK_URL
export KEYCLOAK_REALM=$KEYCLOAK_REALM
export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
export KEYCLOAK_USERNAME=$KEYCLOAK_USERNAME
export KEYCLOAK_PASSWORD=$KEYCLOAK_PASSWORD

# Запускаем интеграционные тесты с подробным выводом
if go test -v; then
  echo ""
  echo "=========================================="
  echo "✅ ALL E2E TESTS PASSED SUCCESSFULLY!"
  echo "=========================================="
  exit 0
else
  echo ""
  echo "=========================================="
  echo "❌ SOME E2E TESTS FAILED!"
  echo "=========================================="
  exit 1
fi
