#!/bin/bash

# Extended script to check endpoint and method consistency between api/openapi.yaml and configs/authorization.yaml
# Validates both paths and HTTP methods in excluded_paths and endpoint_permissions

# Don't exit on error to show complete analysis
# Note: using subshell to avoid affecting parent script
set +e

OPENAPI_FILE="api/openapi.yaml"
AUTH_FILE="configs/authorization.yaml"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo "🔍 Checking endpoint and method consistency between $OPENAPI_FILE and $AUTH_FILE"
echo "═══════════════════════════════════════════════════════════════"
echo ""

# Check file existence
if [[ ! -f "$OPENAPI_FILE" ]]; then
    echo -e "${RED}🔴 File $OPENAPI_FILE not found${NC}"
    exit 1
fi

if [[ ! -f "$AUTH_FILE" ]]; then
    echo -e "${RED}🔴 File $AUTH_FILE not found${NC}"
    exit 1
fi

echo "📋 Extracting endpoints and methods from $OPENAPI_FILE..."

# Simple method to extract paths and methods from OpenAPI
openapi_endpoints=$(grep -E "^  /v1/|^    (get|post|put|patch|delete):" "$OPENAPI_FILE" | \
    awk '
    /^  \/v1\// { path = $1; gsub(/:$/, "", path) }
    /^    (get|post|put|patch|delete):/ { 
        method = toupper($1); gsub(/:$/, "", method)
        print path ":" method 
    }' | sort)

# Extract unique paths from OpenAPI
openapi_paths=$(echo "$openapi_endpoints" | cut -d: -f1 | sort | uniq)

echo "📋 Extracting endpoints from excluded_paths..."

# Extract excluded_paths
excluded_endpoints=$(awk '
BEGIN { in_excluded = 0 }
/^excluded_paths:/ { in_excluded = 1; next }
/^[a-zA-Z]/ && in_excluded { in_excluded = 0 }
in_excluded && /^  "/ {
    path = $1
    gsub(/^"|":$/, "", path)
    while ((getline line) > 0 && line ~ /^    - "/) {
        gsub(/^    - "|"$/, "", line)
        print path ":" line
    }
}
' "$AUTH_FILE" | sort)

echo "📋 Extracting endpoints from endpoint_permissions..."

# Extract endpoint_permissions - simplified approach
permission_endpoints=$(awk '
BEGIN { in_permissions = 0 }
/^endpoint_permissions:/ { in_permissions = 1; next }
/^[a-zA-Z]/ && in_permissions { in_permissions = 0 }
in_permissions && /^  "/ {
    path = $1
    gsub(/^"|":$/, "", path)
    current_path = path
}
in_permissions && /^    "/ {
    method = $1
    gsub(/^"|":$/, "", method)
    if (current_path)
        print current_path ":" method
}
' "$AUTH_FILE" | sort)

# Combine all endpoints from authorization.yaml
all_auth_endpoints=$(echo -e "$excluded_endpoints\n$permission_endpoints" | sort | uniq)

# Extract unique paths from auth
auth_paths=$(echo "$all_auth_endpoints" | cut -d: -f1 | sort | uniq)

echo ""
echo "🔍 Analyzing discrepancies..."
echo ""

has_errors=false
declare -a errors=()
declare -a warnings=()

# 1. Find completely missing endpoints (paths that don't exist at all)
missing_paths_in_auth=$(comm -23 <(echo "$openapi_paths") <(echo "$auth_paths"))
missing_paths_in_openapi=$(comm -13 <(echo "$openapi_paths") <(echo "$auth_paths"))

# 2. Find endpoints with method mismatches (paths exist in both but methods differ)
common_paths=$(comm -12 <(echo "$openapi_paths") <(echo "$auth_paths"))

method_mismatches_missing_in_auth=""
method_mismatches_missing_in_openapi=""

while IFS= read -r path; do
    [[ -z "$path" ]] && continue
    
    # Get methods for this path from both files
    openapi_methods=$(echo "$openapi_endpoints" | grep "^$path:" | cut -d: -f2 | sort)
    auth_methods=$(echo "$all_auth_endpoints" | grep "^$path:" | cut -d: -f2 | sort)
    
    # Find method differences
    missing_methods_in_auth=$(comm -23 <(echo "$openapi_methods") <(echo "$auth_methods"))
    missing_methods_in_openapi=$(comm -13 <(echo "$openapi_methods") <(echo "$auth_methods"))
    
    if [[ -n "$missing_methods_in_auth" ]]; then
        while IFS= read -r method; do
            [[ -n "$method" ]] && method_mismatches_missing_in_auth+="$path:$method"$'\n'
        done <<< "$missing_methods_in_auth"
    fi
    
    if [[ -n "$missing_methods_in_openapi" ]]; then
        while IFS= read -r method; do
            [[ -n "$method" ]] && method_mismatches_missing_in_openapi+="$path:$method"$'\n'
        done <<< "$missing_methods_in_openapi"
    fi
done <<< "$common_paths"

# Count errors and warnings
error_count=0
warning_count=0

# Display results
if [[ -n "$missing_paths_in_auth" ]]; then
    echo -e "${RED}🔴 ERRORS - Endpoints completely missing in $AUTH_FILE:${NC}"
    path_count=0
    echo "$missing_paths_in_auth" | while IFS= read -r path; do
        if [[ -n "$path" ]]; then
            # Get methods list for this path from OpenAPI
            methods_list=$(echo "$openapi_endpoints" | grep "^$path:" | cut -d: -f2 | tr '\n' ',' | sed 's/,$//')
            echo -e "   • ${RED}$path ($methods_list)${NC}"
        fi
    done
    echo ""
    # Count missing methods (not paths) - each missing path contributes all its methods
    while IFS= read -r path; do
        if [[ -n "$path" ]]; then
            methods_for_path=$(echo "$openapi_endpoints" | grep "^$path:" | cut -d: -f2 | wc -l | tr -d ' ')
            error_count=$((error_count + methods_for_path))
        fi
    done <<< "$missing_paths_in_auth"
    errors+=("Endpoints missing in authorization config")
    has_errors=true
fi

if [[ -n "$method_mismatches_missing_in_auth" ]]; then
    echo -e "${RED}🔴 ERRORS - Methods missing in $AUTH_FILE (endpoint exists but methods differ):${NC}"
    echo "$method_mismatches_missing_in_auth" | while IFS= read -r endpoint; do
        [[ -n "$endpoint" ]] && echo -e "   • ${RED}$endpoint${NC}"
    done
    echo ""
    # Count missing methods
    method_count=$(echo "$method_mismatches_missing_in_auth" | awk 'NF{count++} END{print count+0}')
    error_count=$((error_count + method_count))
    errors+=("Methods missing in authorization config")
    has_errors=true
fi

if [[ -n "$missing_paths_in_openapi" ]]; then
    echo -e "${YELLOW}🟡 WARNINGS - Endpoints completely missing in $OPENAPI_FILE:${NC}"
    echo "$missing_paths_in_openapi" | while IFS= read -r path; do
        if [[ -n "$path" ]]; then
            # Get methods list for this path from authorization config
            methods_list=$(echo "$all_auth_endpoints" | grep "^$path:" | cut -d: -f2 | tr '\n' ',' | sed 's/,$//')
            echo -e "   • ${YELLOW}$path ($methods_list)${NC}"
        fi
    done
    echo ""
    # Count missing methods in OpenAPI (not paths) - each missing path contributes all its methods
    while IFS= read -r path; do
        if [[ -n "$path" ]]; then
            methods_for_path=$(echo "$all_auth_endpoints" | grep "^$path:" | cut -d: -f2 | wc -l | tr -d ' ')
            warning_count=$((warning_count + methods_for_path))
        fi
    done <<< "$missing_paths_in_openapi"
    warnings+=("Endpoints exist in authorization but missing in OpenAPI")
fi

if [[ -n "$method_mismatches_missing_in_openapi" ]]; then
    echo -e "${YELLOW}🟡 WARNINGS - Methods exist in $AUTH_FILE but missing in $OPENAPI_FILE:${NC}"
    echo "$method_mismatches_missing_in_openapi" | while IFS= read -r endpoint; do
        [[ -n "$endpoint" ]] && echo -e "   • ${YELLOW}$endpoint${NC}"
    done
    echo ""
    # Count missing methods in OpenAPI
    method_count=$(echo "$method_mismatches_missing_in_openapi" | awk 'NF{count++} END{print count+0}')
    warning_count=$((warning_count + method_count))
    warnings+=("Methods exist in authorization but missing in OpenAPI")
fi

# Show overall statistics
echo "═══════════════════════════════════════════════════════════════"
echo "📊 ANALYSIS SUMMARY"
echo "═══════════════════════════════════════════════════════════════"
echo "Total endpoints:"
echo "   OpenAPI endpoints+methods: $(echo "$openapi_endpoints" | wc -l | tr -d ' ')"
echo "   OpenAPI unique paths: $(echo "$openapi_paths" | wc -l | tr -d ' ')"
echo "   Authorization endpoints+methods: $(echo "$all_auth_endpoints" | wc -l | tr -d ' ')"
echo "   Authorization unique paths: $(echo "$auth_paths" | wc -l | tr -d ' ')"
echo ""
echo "Authorization distribution:"
echo "   Excluded paths: $(echo "$excluded_endpoints" | wc -l | tr -d ' ')"
echo "   Endpoint permissions: $(echo "$permission_endpoints" | wc -l | tr -d ' ')"
echo "   Common paths: $(echo "$common_paths" | wc -l | tr -d ' ')"
echo ""
echo "Issues found:"
if [[ $error_count -eq 0 ]]; then
    echo -e "   Errors: ${GREEN}$error_count${NC}"
else
    echo -e "   Errors: ${RED}$error_count${NC}"
fi

if [[ $warning_count -eq 0 ]]; then
    echo -e "   Warnings: ${GREEN}$warning_count${NC}"
else
    echo -e "   Warnings: ${YELLOW}$warning_count${NC}"
fi

echo ""
echo "═══════════════════════════════════════════════════════════════"

# Final result
if [[ "$has_errors" == "true" ]]; then
    echo -e "${RED}🔴 FAILED: Critical inconsistencies found between files${NC}"
    echo -e "   Need to update $AUTH_FILE according to $OPENAPI_FILE"
    echo ""
    echo "🔧 Diagnostic commands:"
    echo "   # Review authorization configuration:"
    echo "   cat configs/authorization.yaml"
    echo "   # Review OpenAPI specification:"
    echo "   cat api/openapi.yaml"
    echo "   # Compare endpoint lists:"
    echo "   ./scripts/check-endpoint-methods-consistency.sh"
    exit_code=1
elif [[ ${#warnings[@]} -gt 0 ]]; then
    echo -e "${YELLOW}🟡 WARNING: Some inconsistencies found but not critical${NC}"
    echo -e "   Authorization config has extra endpoints not in OpenAPI"
    echo -e "   This may indicate outdated authorization rules"
    exit_code=1
else
    echo -e "${GREEN}🟢 SUCCESS: All endpoints and methods are consistent${NC}"
    exit_code=0
fi

exit $exit_code