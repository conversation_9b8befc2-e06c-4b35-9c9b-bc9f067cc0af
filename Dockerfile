FROM harbor.ds.ecpk.sibintek.ru/golden/golang:1.24.2-alpine AS build-env

WORKDIR /usr/src/ecpk-project
ADD ./ /usr/src/ecpk-project

RUN ls -la /usr/src/ecpk-project

RUN go build -o /pms ./cmd/pms.go

FROM harbor.ds.ecpk.sibintek.ru/golden/alpine:3.21.3

COPY --from=build-env /pms /
COPY --from=build-env /usr/src/ecpk-project/migrations /migrations
COPY --from=build-env /usr/src/ecpk-project/VERSION /VERSION
COPY --from=build-env /usr/src/ecpk-project/api/openapi.yaml /api/openapi.yaml
COPY --from=build-env /usr/src/ecpk-project/configs/authorization.yaml /configs/authorization.yaml

ENTRYPOINT ["./pms"]
CMD ["/pms", "-c", "/pms.conf"]
