package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestInit_WithStdout_Success(t *testing.T) {
	// Act
	logger, err := Init(LogOutputStdout, LogLevelInfo)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, logger)

	// Check that logger implements Interface
	_, ok := logger.(Interface)
	require.True(t, ok)

	// Check that Close() works without error for stdout
	require.NoError(t, logger.Close())
}

func TestInit_WithStderr_Success(t *testing.T) {
	// Act
	logger, err := Init(LogOutputStderr, LogLevelDebug)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, logger)

	// Check that Close() works without error for stderr
	require.NoError(t, logger.Close())
}

func TestInit_WithValidFile_Success(t *testing.T) {
	// Arrange
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")

	// Act
	logger, err := Init(logFile, LogLevelWarn)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, logger)

	// Check that file was created
	_, err = os.Stat(logFile)
	require.NoError(t, err)

	// Close the logger
	require.NoError(t, logger.Close())
}

func TestInit_WithInvalidLogLevel_Error(t *testing.T) {
	// Arrange
	invalidLevel := "invalid"

	// Act
	logger, err := Init(LogOutputStdout, invalidLevel)

	// Assert
	require.Error(t, err)
	require.Nil(t, logger)
	require.Contains(t, err.Error(), "unknown log level")
}

func TestInit_WithInvalidFilePath_Error(t *testing.T) {
	// Arrange - path with traversal
	invalidPath := "../../../etc/passwd"

	// Act
	logger, err := Init(invalidPath, LogLevelInfo)

	// Assert
	require.Error(t, err)
	require.Nil(t, logger)
	require.Contains(t, err.Error(), "path traversal detected")
}

func TestInit_WithEmptyFilePath_Error(t *testing.T) {
	// Arrange
	emptyPath := "   "

	// Act
	logger, err := Init(emptyPath, LogLevelInfo)

	// Assert
	require.Error(t, err)
	require.Nil(t, logger)
	require.Contains(t, err.Error(), "log file path cannot be empty")
}

func TestInit_AllLogLevels_Success(t *testing.T) {
	// Test all valid log levels
	logLevels := []string{
		LogLevelDebug,
		LogLevelInfo,
		LogLevelWarn,
		LogLevelError,
	}

	for _, level := range logLevels {
		t.Run(fmt.Sprintf("Level_%s", level), func(t *testing.T) {
			// Act
			logger, err := Init(LogOutputStdout, level)

			// Assert
			require.NoError(t, err)
			require.NotNil(t, logger)
			require.NoError(t, logger.Close())
		})
	}
}

func TestNewTemporary_Success(t *testing.T) {
	// Act
	logger := NewTemporary()

	// Assert
	require.NotNil(t, logger)

	// Check that logger implements Interface
	_, ok := logger.(Interface)
	require.True(t, ok)

	// Check that Close() works without error for temporary logger
	require.NoError(t, logger.Close())
}

func TestValidateLogFilePath_ValidPath_Success(t *testing.T) {
	// Arrange
	validPaths := []string{
		"test.log",
		"logs/app.log",
		"/tmp/test.log",
		"./logs/test.log",
	}

	for _, path := range validPaths {
		t.Run(fmt.Sprintf("Path_%s", strings.ReplaceAll(path, "/", "_")), func(t *testing.T) {
			// Act
			err := validateLogFilePath(path)

			// Assert
			require.NoError(t, err)
		})
	}
}

func TestValidateLogFilePath_InvalidPath_Error(t *testing.T) {
	testCases := []struct {
		name     string
		path     string
		errorMsg string
	}{
		{
			name:     "PathTraversal",
			path:     "../etc/passwd",
			errorMsg: "path traversal detected",
		},
		{
			name:     "EmptyPath",
			path:     "",
			errorMsg: "log file path cannot be empty",
		},
		{
			name:     "WhitespaceOnlyPath",
			path:     "   ",
			errorMsg: "log file path cannot be empty",
		},
		{
			name:     "NullBytePath",
			path:     "test\x00.log",
			errorMsg: "log file path contains null bytes",
		},
		{
			name:     "PathTraversalAfterCleaning",
			path:     "logs/../../../etc/passwd",
			errorMsg: "path traversal detected",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act
			err := validateLogFilePath(tc.path)

			// Assert
			require.Error(t, err)
			require.Contains(t, err.Error(), tc.errorMsg)
		})
	}
}

func TestValidateLogFilePath_ComplexValidPaths_Success(t *testing.T) {
	// Test more complex valid paths to ensure 100% coverage
	validPaths := []string{
		"simple.log",
		"path/to/file.log",
		"/absolute/path/file.log",
		"./relative/path.log",
		"logs/app-2023-12-25.log",
		"logs/app_with_underscores.log",
		"logs/app-with-dashes.log",
		"logs/app.with.dots.log",
		"logs/123456.log",
		"C:\\Windows\\Logs\\app.log", // Windows path (if running on Windows)
	}

	for _, path := range validPaths {
		t.Run(fmt.Sprintf("ValidPath_%s", strings.ReplaceAll(path, "/", "_")), func(t *testing.T) {
			// Act
			err := validateLogFilePath(path)

			// Assert
			require.NoError(t, err, "Path should be valid: %s", path)
		})
	}
}

func TestLogger_Close_WithFile_Success(t *testing.T) {
	// Arrange
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")
	logger, err := Init(logFile, LogLevelInfo)
	require.NoError(t, err)

	// Act
	err = logger.Close()

	// Assert
	require.NoError(t, err)
}

func TestLogger_Close_WithoutFile_Success(t *testing.T) {
	// Arrange
	logger, err := Init(LogOutputStdout, LogLevelInfo)
	require.NoError(t, err)

	// Act
	err = logger.Close()

	// Assert
	require.NoError(t, err)
}

func TestLogger_Close_TemporaryLogger_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act
	err := logger.Close()

	// Assert
	require.NoError(t, err)
}

func TestLogger_With_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act
	loggerWithFields := logger.With("key", "value", "number", 42)

	// Assert
	require.NotNil(t, loggerWithFields)

	// Check that returned logger implements Interface
	_, ok := loggerWithFields.(Interface)
	require.True(t, ok)

	// Check that original logger is still functional
	require.NotNil(t, logger)

	// Close both loggers
	require.NoError(t, logger.Close())
	require.NoError(t, loggerWithFields.Close())
}

func TestLogger_With_EmptyArgs_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act
	loggerWithFields := logger.With()

	// Assert
	require.NotNil(t, loggerWithFields)

	// Close both loggers
	require.NoError(t, logger.Close())
	require.NoError(t, loggerWithFields.Close())
}

func TestLogger_FormattedMethods_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Test all formatted logging methods
	testCases := []struct {
		name   string
		method func(string, ...any)
	}{
		{
			name:   "Debugf",
			method: logger.Debugf,
		},
		{
			name:   "Infof",
			method: logger.Infof,
		},
		{
			name:   "Warnf",
			method: logger.Warnf,
		},
		{
			name:   "Errorf",
			method: logger.Errorf,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act & Assert - should not panic
			require.NotPanics(t, func() {
				tc.method("Test message: %s, number: %d", "test", 42)
			})
		})
	}

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_FormattedMethods_WithNilArgs_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - should not panic with nil args
	require.NotPanics(t, func() {
		logger.Debugf("Simple message")
		logger.Infof("Message without args")
		logger.Warnf("Warning: %s", "test")
		logger.Errorf("Error: %v", nil)
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_FormattedMethods_Integration_Success(t *testing.T) {
	// Arrange - create logger that writes to a temporary file
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")
	logger, err := Init(logFile, LogLevelDebug)
	require.NoError(t, err)

	// Act - write some logs
	logger.Debugf("Debug message: %s", "test")
	logger.Infof("Info message: %d", 42)
	logger.Warnf("Warning message: %v", true)
	logger.Errorf("Error message: %s", "error")

	// Close logger to flush
	require.NoError(t, logger.Close())

	// Assert - check that file contains logs
	content, err := os.ReadFile(logFile)
	require.NoError(t, err)

	logContent := string(content)
	require.Contains(t, logContent, "Debug message: test")
	require.Contains(t, logContent, "Info message: 42")
	require.Contains(t, logContent, "Warning message: true")
	require.Contains(t, logContent, "Error message: error")
}

func TestLogger_InterfaceCompliance_Success(t *testing.T) {
	// Test that Logger struct properly implements Interface
	var _ Interface = (*Logger)(nil)

	// Test that functions return Interface
	logger := NewTemporary()
	var _ Interface = logger

	loggerWithFields := logger.With("key", "value")
	var _ Interface = loggerWithFields

	fileLogger, err := Init(LogOutputStdout, LogLevelInfo)
	require.NoError(t, err)
	var _ Interface = fileLogger

	// Cleanup
	require.NoError(t, logger.Close())
	require.NoError(t, loggerWithFields.Close())
	require.NoError(t, fileLogger.Close())
}

// Test helper functions for edge cases

func TestLogger_FilePermissions_Success(t *testing.T) {
	// Arrange
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")

	// Act
	logger, err := Init(logFile, LogLevelInfo)
	require.NoError(t, err)

	// Write something to verify file works
	logger.Infof("Test message")
	require.NoError(t, logger.Close())

	// Assert - check file permissions (should be 0600)
	fileInfo, err := os.Stat(logFile)
	require.NoError(t, err)
	require.Equal(t, os.FileMode(0600), fileInfo.Mode().Perm())
}

func TestLogger_MultipleClose_Success(t *testing.T) {
	// Arrange
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")
	logger, err := Init(logFile, LogLevelInfo)
	require.NoError(t, err)

	// Act - close multiple times
	require.NoError(t, logger.Close())

	// Second close should not panic but may return error (this is normal Go behavior)
	logger.Close() // Don't check error as it's expected for closed files

	// Assert - no panic expected
}

func TestInit_NonExistentDirectory_Success(t *testing.T) {
	// Arrange
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "subdir", "test.log")

	// Create parent directory
	require.NoError(t, os.MkdirAll(filepath.Dir(logFile), 0755))

	// Act
	logger, err := Init(logFile, LogLevelInfo)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, logger)
	require.NoError(t, logger.Close())
}

func TestLogger_BasicMethods_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Test all basic logging methods
	testCases := []struct {
		name   string
		method func(string, ...any)
	}{
		{
			name:   "Debug",
			method: logger.Debug,
		},
		{
			name:   "Info",
			method: logger.Info,
		},
		{
			name:   "Warn",
			method: logger.Warn,
		},
		{
			name:   "Error",
			method: logger.Error,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act & Assert - should not panic
			require.NotPanics(t, func() {
				tc.method("Test message", "key", "value", "number", 42)
			})
		})
	}

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_BasicMethods_WithSimpleMessage_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - should not panic with simple messages
	require.NotPanics(t, func() {
		logger.Debug("Debug message")
		logger.Info("Info message")
		logger.Warn("Warning message")
		logger.Error("Error message")
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestInit_FileOpenError_Error(t *testing.T) {
	// Arrange - try to create file in non-existent directory without creating parent
	invalidPath := "/non/existent/directory/test.log"

	// Act
	logger, err := Init(invalidPath, LogLevelInfo)

	// Assert
	require.Error(t, err)
	require.Nil(t, logger)
	require.Contains(t, err.Error(), "log init:")
}

func TestInit_ReadOnlyDirectoryError_Error(t *testing.T) {
	// Arrange - create a read-only directory
	tmpDir := t.TempDir()
	readOnlyDir := filepath.Join(tmpDir, "readonly")
	require.NoError(t, os.Mkdir(readOnlyDir, 0444)) // read-only permissions

	logFile := filepath.Join(readOnlyDir, "test.log")

	// Act
	logger, err := Init(logFile, LogLevelInfo)

	// Assert
	require.Error(t, err)
	require.Nil(t, logger)
	require.Contains(t, err.Error(), "log init:")
}

func TestValidateLogFilePath_EdgeCases_Error(t *testing.T) {
	testCases := []struct {
		name     string
		path     string
		errorMsg string
	}{
		{
			name:     "PathWithMultipleNullBytes",
			path:     "test\x00\x00.log",
			errorMsg: "log file path contains null bytes",
		},
		{
			name:     "PathWithTraversalInMiddle",
			path:     "logs/../../../root/test.log",
			errorMsg: "path traversal detected",
		},
		{
			name:     "OnlySpacesPath",
			path:     "     ",
			errorMsg: "log file path cannot be empty",
		},
		{
			name:     "TabsAndSpacesPath",
			path:     "\t   \n  ",
			errorMsg: "log file path cannot be empty",
		},
		{
			name:     "PathWithTraversalAtEnd",
			path:     "logs/..",
			errorMsg: "path traversal detected",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act
			err := validateLogFilePath(tc.path)

			// Assert
			require.Error(t, err)
			require.Contains(t, err.Error(), tc.errorMsg)
		})
	}
}

func TestLogger_With_NilValues_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act - test with nil values
	loggerWithNilFields := logger.With("key", nil, "empty", "", "zero", 0)

	// Assert
	require.NotNil(t, loggerWithNilFields)
	assertLoggerImplementsInterface(t, loggerWithNilFields)

	// Test that logging still works
	require.NotPanics(t, func() {
		loggerWithNilFields.Info("Test with nil values")
	})

	// Cleanup
	require.NoError(t, logger.Close())
	require.NoError(t, loggerWithNilFields.Close())
}

func TestLogger_FormattedMethods_EmptyFormat_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - test with empty format strings
	require.NotPanics(t, func() {
		logger.Debugf("")
		logger.Infof("")
		logger.Warnf("")
		logger.Errorf("")
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_FormattedMethods_InvalidFormat_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - test with invalid format specifiers
	require.NotPanics(t, func() {
		logger.Debugf("Invalid format %q %d", "string") // missing argument
		logger.Infof("Wrong type %d", "string")         // wrong type
		logger.Warnf("Extra args %s", "arg1", "arg2")   // extra arguments
		logger.Errorf("No format specifiers", "arg1")   // no format specifiers
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_BasicMethods_NilValues_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - test with nil values in args
	require.NotPanics(t, func() {
		logger.Debug("Test message", "key", nil)
		logger.Info("Test message", "empty", "", "zero", 0)
		logger.Warn("Test message", "bool", false)
		logger.Error("Test message", "slice", []string(nil))
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_BasicMethods_EmptyMessage_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - test with empty messages
	require.NotPanics(t, func() {
		logger.Debug("")
		logger.Info("")
		logger.Warn("")
		logger.Error("")
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_Close_NonOwner_Success(t *testing.T) {
	// Arrange - create logger with file, then create derived logger
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")
	mainLogger, err := Init(logFile, LogLevelInfo)
	require.NoError(t, err)

	// Create derived logger (non-owner)
	derivedLogger := mainLogger.With("derived", true)

	// Act - close derived logger first (should not close file)
	require.NoError(t, derivedLogger.Close())

	// Assert - main logger should still work
	require.NotPanics(t, func() {
		mainLogger.Info("Still works")
	})

	// Close main logger
	require.NoError(t, mainLogger.Close())
}

func TestLogger_MultipleWith_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act - chain multiple With calls
	logger1 := logger.With("key1", "value1")
	logger2 := logger1.With("key2", "value2")
	logger3 := logger2.With("key3", "value3")

	// Assert
	require.NotNil(t, logger1)
	require.NotNil(t, logger2)
	require.NotNil(t, logger3)

	// Test that all loggers work
	require.NotPanics(t, func() {
		logger.Info("Original logger")
		logger1.Info("Logger with key1")
		logger2.Info("Logger with key1 and key2")
		logger3.Info("Logger with key1, key2, and key3")
	})

	// Cleanup
	require.NoError(t, logger.Close())
	require.NoError(t, logger1.Close())
	require.NoError(t, logger2.Close())
	require.NoError(t, logger3.Close())
}

func TestLogger_LargeMessage_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()
	largeMessage := strings.Repeat("A", 10000) // 10KB message

	// Act & Assert - should handle large messages
	require.NotPanics(t, func() {
		logger.Info("Large message", "data", largeMessage)
		logger.Infof("Large formatted message: %s", largeMessage)
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_SpecialCharacters_Success(t *testing.T) {
	// Arrange
	logger := NewTemporary()

	// Act & Assert - test with special characters
	require.NotPanics(t, func() {
		logger.Info("Special chars: 🚀 ñ ü ß α ∑ ∞")
		logger.Infof("Unicode: %s", "测试 тест テスト")
		logger.Debug("Escape sequences", "newline", "line1\nline2", "tab", "col1\tcol2")
		logger.Error("JSON-like", "json", `{"key": "value", "number": 42}`)
	})

	// Cleanup
	require.NoError(t, logger.Close())
}

// Helper function for tests
func assertLoggerImplementsInterface(t *testing.T, logger Interface) {
	require.NotNil(t, logger)

	// Check that logger implements Interface
	_, ok := logger.(Interface)
	require.True(t, ok)
}

func TestLogger_Debugf_WithDebugLevel_Success(t *testing.T) {
	// Arrange - create logger with Debug level to ensure Debug messages are actually logged
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "debug.log")
	logger, err := Init(logFile, LogLevelDebug)
	require.NoError(t, err)

	// Act
	logger.Debugf("Debug message: %s", "test")
	require.NoError(t, logger.Close())

	// Assert - check that debug message was written
	content, err := os.ReadFile(logFile)
	require.NoError(t, err)
	require.Contains(t, string(content), "Debug message: test")
}

func TestLogger_LevelFiltering_Success(t *testing.T) {
	// Test that log levels are properly filtered
	testCases := []struct {
		logLevel       string
		shouldLogDebug bool
		shouldLogInfo  bool
		shouldLogWarn  bool
		shouldLogError bool
	}{
		{LogLevelDebug, true, true, true, true},
		{LogLevelInfo, false, true, true, true},
		{LogLevelWarn, false, false, true, true},
		{LogLevelError, false, false, false, true},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Level_%s", tc.logLevel), func(t *testing.T) {
			// Arrange
			tmpDir := t.TempDir()
			logFile := filepath.Join(tmpDir, "level_test.log")
			logger, err := Init(logFile, tc.logLevel)
			require.NoError(t, err)

			// Act - log at all levels
			logger.Debug("debug message")
			logger.Info("info message")
			logger.Warn("warn message")
			logger.Error("error message")
			require.NoError(t, logger.Close())

			// Assert - check which messages appear in log
			content, err := os.ReadFile(logFile)
			require.NoError(t, err)
			logContent := string(content)

			if tc.shouldLogDebug {
				require.Contains(t, logContent, "debug message")
			} else {
				require.NotContains(t, logContent, "debug message")
			}

			if tc.shouldLogInfo {
				require.Contains(t, logContent, "info message")
			} else {
				require.NotContains(t, logContent, "info message")
			}

			if tc.shouldLogWarn {
				require.Contains(t, logContent, "warn message")
			} else {
				require.NotContains(t, logContent, "warn message")
			}

			if tc.shouldLogError {
				require.Contains(t, logContent, "error message")
			} else {
				require.NotContains(t, logContent, "error message")
			}
		})
	}
}

func TestLogger_ConcurrentAccess_Success(t *testing.T) {
	// Test concurrent access to logger (basic thread safety test)
	logger := NewTemporary()

	// Run multiple goroutines that log simultaneously
	done := make(chan bool)
	numGoroutines := 10

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < 10; j++ {
				logger.Infof("Goroutine %d, message %d", id, j)
				logger.With("goroutine", id, "message", j).Info("Structured log")
			}
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Cleanup
	require.NoError(t, logger.Close())
}

func TestLogger_FileAppend_Success(t *testing.T) {
	// Test that logger appends to existing file
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "append_test.log")

	// First logger - write initial content
	logger1, err := Init(logFile, LogLevelInfo)
	require.NoError(t, err)
	logger1.Info("First message")
	require.NoError(t, logger1.Close())

	// Second logger - should append to existing file
	logger2, err := Init(logFile, LogLevelInfo)
	require.NoError(t, err)
	logger2.Info("Second message")
	require.NoError(t, logger2.Close())

	// Assert - both messages should be in file
	content, err := os.ReadFile(logFile)
	require.NoError(t, err)
	logContent := string(content)
	require.Contains(t, logContent, "First message")
	require.Contains(t, logContent, "Second message")
}

func TestValidateLogFilePath_PathCleaningEdgeCases_Success(t *testing.T) {
	// Test paths that get cleaned but remain valid
	testCases := []struct {
		name string
		path string
	}{
		{
			name: "RedundantSlashes",
			path: "logs//app.log",
		},
		{
			name: "CurrentDirectoryReferences",
			path: "logs/./app.log",
		},
		{
			name: "TrailingSlash",
			path: "logs/app.log/",
		},
		{
			name: "ComplexButValidPath",
			path: "logs/./subdir/app.log",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Act
			err := validateLogFilePath(tc.path)

			// Assert - these should all be valid after cleaning
			require.NoError(t, err, "Path should be valid after cleaning: %s", tc.path)
		})
	}
}

func TestValidateLogFilePath_AllValidationsPass_Success(t *testing.T) {
	// Test a path that passes all validation checks to ensure 100% coverage
	// This should hit the final return nil line
	validPath := "valid/path/to/logfile.log"

	// Act
	err := validateLogFilePath(validPath)

	// Assert
	require.NoError(t, err)
}
