package logger

import (
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
)

// Interface defines the logger contract
type Interface interface {
	Debug(msg string, args ...any)
	Info(msg string, args ...any)
	Warn(msg string, args ...any)
	Error(msg string, args ...any)

	Debugf(format string, args ...any)
	Infof(format string, args ...any)
	Warnf(format string, args ...any)
	Errorf(format string, args ...any)

	With(args ...any) Interface

	Close() error
}

type Logger struct {
	slog.Logger
	file    *os.File // File reference for closing (nil for stdout/stderr)
	isOwner bool     // Indicates if this logger owns the file
}

// Compile-time check that Logger implements Interface
var _ Interface = (*Logger)(nil)

const (
	LogOutputStdout = "stdout"
	LogOutputStderr = "stderr"
	LogLevelDebug   = "debug"
	LogLevelInfo    = "info"
	LogLevelWarn    = "warn"
	LogLevelError   = "error"
)

// NewTemporary creates a temporary logger for use before main logger initialization.
// Outputs to stdout with Info level.
func NewTemporary() Interface {
	return &Logger{
		Logger: *slog.New(
			slog.NewJSONHandler(
				os.Stdout,
				&slog.HandlerOptions{
					Level: slog.LevelInfo,
					ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
						if a.Key == slog.TimeKey && len(groups) == 0 {
							t := a.Value.Time().UTC()
							a.Value = slog.StringValue(t.Format("2006-01-02T15:04:05.000000000Z"))
						}
						return a
					},
				},
			),
		),
		file:    nil,   // stdout doesn't need closing
		isOwner: false, // temporary logger doesn't own resources
	}
}

// Close closes the logger file if it was opened.
// Does nothing for stdout/stderr.
// Only the file owner can close it.
// Returns an error if the file cannot be closed.
func (l *Logger) Close() error {
	if l.file != nil && l.isOwner {
		return l.file.Close()
	}
	return nil
}

func (l *Logger) Debugf(format string, args ...any) {
	l.logf(l.Debug, format, args...)
}

func (l *Logger) Errorf(format string, args ...any) {
	l.logf(l.Error, format, args...)
}

func (l *Logger) Infof(format string, args ...any) {
	l.logf(l.Info, format, args...)
}

func (l *Logger) Warnf(format string, args ...any) {
	l.logf(l.Warn, format, args...)
}

// With returns a new logger with additional attributes
func (l *Logger) With(args ...any) Interface {
	return &Logger{
		Logger:  *l.Logger.With(args...),
		file:    l.file, // Use the same file
		isOwner: false,  // Derived loggers do NOT own the file
	}
}

// logf is a common method for formatted logging
func (l *Logger) logf(logFunc func(string, ...any), format string, args ...any) {
	logFunc(fmt.Sprintf(format, args...))
}

func Init(file, level string) (Interface, error) {

	var (
		f           *os.File
		fileToClose *os.File
		err         error
	)

	switch file {
	case LogOutputStdout:
		f = os.Stdout
		fileToClose = nil // stdout doesn't need closing
	case LogOutputStderr:
		f = os.Stderr
		fileToClose = nil // stderr doesn't need closing
	default:
		if err := validateLogFilePath(file); err != nil {
			return nil, fmt.Errorf("log init: %w", err)
		}

		cleanFile := filepath.Clean(file)
		f, err = os.OpenFile(cleanFile, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0600)
		if err != nil {
			return nil, fmt.Errorf("log init: %w", err)
		}
		fileToClose = f // file needs closing
	}

	var (
		l         slog.Level
		logLevels = map[string]slog.Level{
			LogLevelDebug: slog.LevelDebug,
			LogLevelInfo:  slog.LevelInfo,
			LogLevelWarn:  slog.LevelWarn,
			LogLevelError: slog.LevelError,
		}
	)

	l, ok := logLevels[level]
	if !ok {
		return nil, fmt.Errorf("log init: %w", fmt.Errorf("unknown log level: %s", level))
	}

	return &Logger{
		Logger: *slog.New(
			slog.NewJSONHandler(
				f,
				&slog.HandlerOptions{
					Level: l,
					// Remove ReplaceAttr if high performance is needed
					ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
						if a.Key == slog.TimeKey && len(groups) == 0 {
							t := a.Value.Time().UTC()
							a.Value = slog.StringValue(t.Format("2006-01-02T15:04:05.000000000Z"))
						}
						return a
					},
				},
			),
		),
		file:    fileToClose,
		isOwner: true, // Main logger owns the file
	}, nil
}

func validateLogFilePath(file string) error {
	// Check for path traversal BEFORE path cleaning
	if strings.Contains(file, "..") {
		return fmt.Errorf("path traversal detected in log file path: %s", file)
	}

	// Check for empty path
	if strings.TrimSpace(file) == "" {
		return fmt.Errorf("log file path cannot be empty")
	}

	cleanPath := filepath.Clean(file)

	// Check for invalid characters
	if strings.ContainsAny(cleanPath, "\x00") {
		return fmt.Errorf("log file path contains null bytes: %s", file)
	}

	return nil
}
