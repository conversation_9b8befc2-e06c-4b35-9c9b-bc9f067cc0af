package ratelimiter

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRateLimiter_Allow(t *testing.T) {
	testCases := []struct {
		name        string
		maxRequests int
		window      time.Duration
		clientID    string
		requests    int
		expectAllow bool
	}{
		{
			name:        "Allow requests within limit",
			maxRequests: 5,
			window:      time.Second,
			clientID:    "client1",
			requests:    3,
			expectAllow: true,
		},
		{
			name:        "Block requests exceeding limit",
			maxRequests: 2,
			window:      time.Second,
			clientID:    "client1",
			requests:    3,
			expectAllow: false,
		},
		{
			name:        "Different clients have separate limits",
			maxRequests: 1,
			window:      time.Second,
			clientID:    "client2",
			requests:    1,
			expectAllow: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			rl := New(tc.maxRequests, tc.window)
			defer rl.Stop()

			var lastResult bool
			for i := 0; i < tc.requests; i++ {
				lastResult = rl.Allow(tc.clientID)
			}

			if tc.expectAllow {
				assert.True(t, lastResult, "Expected request to be allowed")
			} else {
				assert.False(t, lastResult, "Expected request to be blocked")
			}
		})
	}
}

func TestRateLimiter_WindowReset(t *testing.T) {
	rl := New(2, 100*time.Millisecond)
	defer rl.Stop()

	clientID := "test-client"

	// Use up the limit
	assert.True(t, rl.Allow(clientID))
	assert.True(t, rl.Allow(clientID))
	assert.False(t, rl.Allow(clientID)) // Should be blocked

	// Wait for window to reset
	time.Sleep(150 * time.Millisecond)

	// Should be allowed again
	assert.True(t, rl.Allow(clientID))
}

func TestRateLimiter_ConcurrentAccess(t *testing.T) {
	rl := New(100, time.Second)
	defer rl.Stop()

	const numGoroutines = 50
	const requestsPerGoroutine = 10

	var wg sync.WaitGroup
	allowedCount := make(chan bool, numGoroutines*requestsPerGoroutine)

	// Launch multiple goroutines making concurrent requests
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(clientID string) {
			defer wg.Done()
			for j := 0; j < requestsPerGoroutine; j++ {
				allowed := rl.Allow(clientID)
				allowedCount <- allowed
			}
		}("client")
	}

	wg.Wait()
	close(allowedCount)

	// Count allowed requests
	allowed := 0
	for result := range allowedCount {
		if result {
			allowed++
		}
	}

	// Should respect the rate limit (100 requests per second)
	assert.LessOrEqual(t, allowed, 100, "Should not exceed rate limit")
}

func TestRateLimiter_Cleanup(t *testing.T) {
	// Create a concrete instance to access internal fields for testing
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      50 * time.Millisecond,
	}
	rl.startCleanup()
	defer rl.Stop()

	// Make some requests
	rl.Allow("client1")
	rl.Allow("client2")
	rl.Allow("client3")

	// Check that clients are tracked
	rl.mu.Lock()
	initialClients := len(rl.requests)
	rl.mu.Unlock()

	assert.Equal(t, 3, initialClients, "Should track 3 clients")

	// Wait for cleanup to run (cleanup runs every minute, but we can trigger it manually)
	time.Sleep(100 * time.Millisecond) // Wait for requests to expire
	rl.cleanup()                       // Manually trigger cleanup

	// Check that old entries are cleaned up
	rl.mu.Lock()
	finalClients := len(rl.requests)
	rl.mu.Unlock()

	assert.Equal(t, 0, finalClients, "Should clean up expired entries")
}

func TestRateLimiter_Stop(t *testing.T) {
	// Create a concrete instance to access internal fields for testing
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      time.Second,
	}
	rl.startCleanup()

	// Verify timer is running
	require.NotNil(t, rl.cleanupTimer)

	// Stop the rate limiter
	rl.Stop()

	// Timer should be stopped (we can't easily test this directly,
	// but at least verify Stop() doesn't panic)
	assert.NotPanics(t, func() {
		rl.Stop() // Should be safe to call multiple times
	})
}

func TestNew(t *testing.T) {
	maxRequests := 50
	window := 2 * time.Second

	rl := New(maxRequests, window)
	defer rl.Stop()

	// Verify interface is returned
	assert.NotNil(t, rl)

	// Test basic functionality
	assert.True(t, rl.Allow("test-client"))
}

// TestNew_InputValidation tests input parameter validation
func TestNew_InputValidation(t *testing.T) {
	t.Run("negative maxRequests", func(t *testing.T) {
		rl := New(-5, time.Second)
		defer rl.Stop()

		// Should default to 1 and allow exactly 1 request
		assert.True(t, rl.Allow("client1"))
		assert.False(t, rl.Allow("client1")) // Second request should be blocked
	})

	t.Run("zero maxRequests", func(t *testing.T) {
		rl := New(0, time.Second)
		defer rl.Stop()

		// Should default to 1 and allow exactly 1 request
		assert.True(t, rl.Allow("client2"))
		assert.False(t, rl.Allow("client2")) // Second request should be blocked
	})

	t.Run("negative window", func(t *testing.T) {
		rl := New(5, -time.Second)
		defer rl.Stop()

		// Should default to 1 second window - hard to test directly,
		// but we can verify it doesn't panic and works
		assert.True(t, rl.Allow("client3"))
		assert.True(t, rl.Allow("client3"))
	})

	t.Run("zero window", func(t *testing.T) {
		rl := New(5, 0)
		defer rl.Stop()

		// Should default to 1 second window
		assert.True(t, rl.Allow("client4"))
		assert.True(t, rl.Allow("client4"))
	})

	t.Run("valid parameters", func(t *testing.T) {
		rl := New(3, 100*time.Millisecond)
		defer rl.Stop()

		// Should work normally with valid parameters
		assert.True(t, rl.Allow("client5"))
		assert.True(t, rl.Allow("client5"))
		assert.True(t, rl.Allow("client5"))
		assert.False(t, rl.Allow("client5")) // Fourth request should be blocked
	})
}

func BenchmarkRateLimiter_Allow(b *testing.B) {
	rl := New(1000, time.Second)
	defer rl.Stop()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		clientID := "bench-client"
		for pb.Next() {
			rl.Allow(clientID)
		}
	})
}

func TestRateLimiter_Interface(t *testing.T) {
	// Test that our implementation satisfies the interface
	var _ Interface = New(10, time.Second)
}

func TestRateLimiter_PeriodicCleanup(t *testing.T) {
	// Create a rate limiter with very short cleanup interval for testing
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      50 * time.Millisecond,
	}

	rl.startCleanup()
	defer rl.Stop()

	// Make some requests
	rl.Allow("client1")
	rl.Allow("client2")
	rl.Allow("client3")

	// Verify clients are tracked
	rl.mu.Lock()
	initialClients := len(rl.requests)
	rl.mu.Unlock()
	assert.Equal(t, 3, initialClients, "Should track 3 clients initially")

	// Wait for requests to expire
	time.Sleep(150 * time.Millisecond) // Wait longer than window

	// Manually trigger cleanup to test the functionality
	rl.cleanup()

	// Check that cleanup occurred
	rl.mu.Lock()
	finalClients := len(rl.requests)
	rl.mu.Unlock()
	assert.Equal(t, 0, finalClients, "Should clean up expired entries")
}

func TestRateLimiter_CleanupRescheduling(t *testing.T) {
	// Create a rate limiter
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      30 * time.Millisecond,
	}

	rl.startCleanup()
	defer rl.Stop()

	// Add some data that will expire
	rl.Allow("client1")

	// Verify the cleanup timer was created and is running
	assert.NotNil(t, rl.cleanupTimer, "Cleanup timer should be created")

	// Wait and verify the rate limiter continues to function
	time.Sleep(100 * time.Millisecond)

	// Should still be able to make requests
	assert.True(t, rl.Allow("client2"), "Should still accept new requests after cleanup cycles")
}

func TestRateLimiter_CleanupWithActiveClients(t *testing.T) {
	// Create a rate limiter with short window for testing
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      100 * time.Millisecond,
	}
	rl.startCleanup()
	defer rl.Stop()

	// Add some old requests that should be cleaned up
	rl.Allow("old_client1")
	rl.Allow("old_client2")

	// Wait for requests to become old enough to be outside the buffer (window * 2)
	time.Sleep(250 * time.Millisecond) // More than 200ms (window * 2)

	// Add some fresh requests that should NOT be cleaned up
	rl.Allow("fresh_client1")
	rl.Allow("fresh_client2")

	// Manually trigger cleanup
	rl.cleanup()

	// Check that only fresh clients remain
	rl.mu.Lock()
	remainingClients := len(rl.requests)
	hasOldClient1 := false
	hasOldClient2 := false
	hasFreshClient1 := false
	hasFreshClient2 := false

	for clientID := range rl.requests {
		switch clientID {
		case "old_client1":
			hasOldClient1 = true
		case "old_client2":
			hasOldClient2 = true
		case "fresh_client1":
			hasFreshClient1 = true
		case "fresh_client2":
			hasFreshClient2 = true
		}
	}
	rl.mu.Unlock()

	assert.Equal(t, 2, remainingClients, "Should keep only fresh clients")
	assert.False(t, hasOldClient1, "Old client1 should be cleaned up")
	assert.False(t, hasOldClient2, "Old client2 should be cleaned up")
	assert.True(t, hasFreshClient1, "Fresh client1 should remain")
	assert.True(t, hasFreshClient2, "Fresh client2 should remain")
}

func TestRateLimiter_CleanupTimerManagement(t *testing.T) {
	// Create a rate limiter
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      time.Second,
	}

	// Start cleanup
	rl.startCleanup()

	// Verify timer is created
	require.NotNil(t, rl.cleanupTimer, "Cleanup timer should be created")

	// Stop the timer
	rl.Stop()

	// Start cleanup again
	rl.startCleanup()

	// Verify new timer is created and is still not nil
	require.NotNil(t, rl.cleanupTimer, "New cleanup timer should be created")

	// Test that Stop can be called multiple times safely
	assert.NotPanics(t, func() {
		rl.Stop()
		rl.Stop() // Should be safe to call multiple times
	})
}

func TestRateLimiter_CleanupBufferLogic(t *testing.T) {
	// Test the buffer logic in cleanup (window * 2)
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      100 * time.Millisecond,
	}
	rl.startCleanup()
	defer rl.Stop()

	// Add requests at different times
	now := time.Now()

	// Manually add requests with specific timestamps
	rl.mu.Lock()
	// Request within window (should be kept)
	rl.requests["recent"] = []time.Time{now.Add(-50 * time.Millisecond)}
	// Request outside window but within buffer (should be kept)
	rl.requests["buffer"] = []time.Time{now.Add(-150 * time.Millisecond)}
	// Request outside buffer (should be removed)
	rl.requests["old"] = []time.Time{now.Add(-250 * time.Millisecond)}
	rl.mu.Unlock()

	// Trigger cleanup
	rl.cleanup()

	// Check results
	rl.mu.Lock()
	_, hasRecent := rl.requests["recent"]
	_, hasBuffer := rl.requests["buffer"]
	_, hasOld := rl.requests["old"]
	rl.mu.Unlock()

	assert.True(t, hasRecent, "Recent requests should be kept")
	assert.True(t, hasBuffer, "Requests within buffer should be kept")
	assert.False(t, hasOld, "Old requests outside buffer should be removed")
}

func TestRateLimiter_StartCleanupRecursive(t *testing.T) {
	// Test the startCleanup function more thoroughly
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      10 * time.Millisecond,
	}

	// Verify initial state
	assert.Nil(t, rl.cleanupTimer, "Timer should be nil initially")

	// Start cleanup which should set up the timer
	rl.startCleanup()

	// Verify timer was created
	assert.NotNil(t, rl.cleanupTimer, "Cleanup timer should be created")

	// Add some test data
	rl.Allow("test-client")

	// The recursive call in startCleanup is hard to test directly because it uses
	// a 1-minute timer. However, we can verify that:
	// 1. The timer is properly created
	// 2. The cleanup function works correctly
	// 3. Stop functionality works

	// Test that cleanup works manually
	rl.cleanup()

	// Verify the timer is still there after manual cleanup
	assert.NotNil(t, rl.cleanupTimer, "Timer should persist after manual cleanup")

	// Test stopping
	rl.Stop()

	// Test that we can restart after stopping
	rl.startCleanup()
	assert.NotNil(t, rl.cleanupTimer, "Should be able to restart cleanup timer")

	// Final cleanup
	rl.Stop()
}

// TestRateLimiter_InternalTimerBehavior tests timer management in detail
func TestRateLimiter_InternalTimerBehavior(t *testing.T) {
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 5,
		window:      time.Second,
	}

	// Test multiple start/stop cycles
	for i := 0; i < 3; i++ {
		// Start cleanup
		rl.startCleanup()
		assert.NotNil(t, rl.cleanupTimer, "Timer should be created on iteration %d", i)

		// Add some data to make sure the limiter works
		assert.True(t, rl.Allow("client"), "Should allow request on iteration %d", i)

		// Stop cleanup
		rl.Stop()
	}

	// Final verification that everything still works
	rl.startCleanup()
	assert.True(t, rl.Allow("final-client"), "Should work after multiple start/stop cycles")
	rl.Stop()
}

// TestRateLimiter_TimerRescheduling attempts to test the timer rescheduling logic
func TestRateLimiter_TimerRescheduling(t *testing.T) {
	// Create a custom RateLimiter that we can manipulate for testing
	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: 10,
		window:      50 * time.Millisecond,
	}

	// Test the pattern used in startCleanup with very short intervals
	timerFired := false
	testCompleted := false

	// Simulate the startCleanup timer behavior
	rl.cleanupTimer = time.AfterFunc(10*time.Millisecond, func() {
		timerFired = true
		// This simulates the cleanup call in the real startCleanup
		rl.cleanup()

		// This simulates the recursive call in startCleanup
		if !testCompleted {
			// Set up another timer to simulate the recursive scheduling
			rl.cleanupTimer = time.AfterFunc(10*time.Millisecond, func() {
				// This would be the recursive call behavior
			})
		}
	})

	// Add some test data
	rl.Allow("test-client")

	// Wait for the timer to fire
	time.Sleep(50 * time.Millisecond)

	// Mark test as completed to stop recursive scheduling
	testCompleted = true

	// Verify the timer fired and cleanup happened
	assert.True(t, timerFired, "Timer should have fired")
	assert.NotNil(t, rl.cleanupTimer, "Timer should exist after firing")

	// Clean up
	rl.Stop()
}

// TestRateLimiter_SliceOptimization verifies slice reuse optimization works correctly
func TestRateLimiter_SliceOptimization(t *testing.T) {
	rl := New(3, 100*time.Millisecond)
	defer rl.Stop()

	// Fill up requests to trigger slice operations
	clientID := "optimization-test"

	// Add multiple requests
	for i := 0; i < 3; i++ {
		assert.True(t, rl.Allow(clientID), "Should allow request %d", i)
	}

	// Wait for some to expire
	time.Sleep(150 * time.Millisecond)

	// Add new request - this should trigger slice optimization
	assert.True(t, rl.Allow(clientID), "Should allow request after expiration")

	// Verify internal state is reasonable
	if concreteRL, ok := rl.(*RateLimiter); ok {
		concreteRL.mu.Lock()
		requests := concreteRL.requests[clientID]
		concreteRL.mu.Unlock()

		// Should have cleaned up old entries and have only fresh ones
		assert.LessOrEqual(t, len(requests), 3, "Should not accumulate too many entries")
	}
}
