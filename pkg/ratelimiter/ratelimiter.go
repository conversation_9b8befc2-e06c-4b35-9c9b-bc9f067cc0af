package ratelimiter

import (
	"sync"
	"time"
)

// Interface defines the rate limiter contract
type Interface interface {
	// Allow checks if the request is allowed for the given client
	Allow(clientID string) bool
	// Stop stops internal rate limiter processes
	Stop()
}

// RateLimiter provides thread-safe rate limiting functionality
type RateLimiter struct {
	mu           sync.Mutex
	requests     map[string][]time.Time
	maxRequests  int
	window       time.Duration
	cleanupTimer *time.Timer
}

// Compile-time check that RateLimiter implements Interface
var _ Interface = (*RateLimiter)(nil)

// New creates a new rate limiter instance
func New(maxRequests int, window time.Duration) Interface {
	// Validate input parameters
	if maxRequests <= 0 {
		maxRequests = 1 // Default to allowing at least 1 request
	}
	if window <= 0 {
		window = time.Second // Default to 1 second window
	}

	rl := &RateLimiter{
		requests:    make(map[string][]time.Time),
		maxRequests: maxRequests,
		window:      window,
	}
	rl.startCleanup()
	return rl
}

// Allow checks if the request should be allowed for the given client
func (rl *RateLimiter) Allow(clientID string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	requests := rl.requests[clientID]

	// Remove old requests outside the time window
	cutoff := now.Add(-rl.window)

	// Optimize slice operations by reusing the same underlying array
	validIdx := 0
	for _, req := range requests {
		if req.After(cutoff) {
			requests[validIdx] = req
			validIdx++
		}
	}
	// Trim the slice to only include valid requests
	requests = requests[:validIdx]

	// Check if limit is exceeded
	if len(requests) >= rl.maxRequests {
		rl.requests[clientID] = requests
		return false
	}

	// Add current request
	requests = append(requests, now)
	rl.requests[clientID] = requests
	return true
}

// Stop stops the cleanup timer
func (rl *RateLimiter) Stop() {
	if rl.cleanupTimer != nil {
		rl.cleanupTimer.Stop()
	}
}

// startCleanup starts periodic cleanup of old entries
func (rl *RateLimiter) startCleanup() {
	rl.cleanupTimer = time.AfterFunc(time.Minute, func() {
		rl.cleanup()
		rl.startCleanup() // Schedule next cleanup
	})
}

// cleanup removes old entries to prevent memory leaks
func (rl *RateLimiter) cleanup() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	cutoff := now.Add(-rl.window * 2) // Keep some buffer

	for clientID, requests := range rl.requests {
		// Optimize slice operations by reusing the same underlying array
		validIdx := 0
		for _, req := range requests {
			if req.After(cutoff) {
				requests[validIdx] = req
				validIdx++
			}
		}

		if validIdx == 0 {
			delete(rl.requests, clientID)
		} else {
			// Trim the slice to only include valid requests
			rl.requests[clientID] = requests[:validIdx]
		}
	}
}
