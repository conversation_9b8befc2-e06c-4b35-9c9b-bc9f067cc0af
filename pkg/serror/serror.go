package serror

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sync"
)

var ErrUnknownCode = errors.New("unknown error code")

type Base struct {
	Status  int    `json:"status"`
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (b *Base) HttpStatus() int {
	return b.Status
}

// ErrorTyped - интерфейс для типизированных ошибок с дженериками
type ErrorTyped[T any] interface {
	error
	GetTypedData() T
}

// TypedError - обёртка для типизированных ошибок
type TypedError[T any] struct {
	Status  int    `json:"status"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
}

func (e *TypedError[T]) Error() string {
	return fmt.Sprintf("status: %d, code: %s, message: %s", e.Status, e.Code, e.Message)
}

func (e *TypedError[T]) GetTypedData() T {
	return e.Data
}

func (e *TypedError[T]) HttpStatus() int {
	return e.Status
}

// typeInfo кэширует метаданные типа для избежания повторных reflection операций
type typeInfo struct {
	elemType   reflect.Type
	isValidPtr bool
	isStruct   bool
}

// typeCache кэширует метаданные типов для оптимизации
var (
	typeCache = make(map[reflect.Type]*typeInfo)
	cacheMux  sync.RWMutex
)

// getTypeInfo возвращает кэшированную информацию о типе или создает новую
func getTypeInfo(t reflect.Type) *typeInfo {
	cacheMux.RLock()
	if info, exists := typeCache[t]; exists {
		cacheMux.RUnlock()
		return info
	}
	cacheMux.RUnlock()

	cacheMux.Lock()
	defer cacheMux.Unlock()

	// Двойная проверка после получения write lock
	if info, exists := typeCache[t]; exists {
		return info
	}

	info := &typeInfo{}
	if t.Kind() == reflect.Ptr {
		info.isValidPtr = true
		elemType := t.Elem()
		info.elemType = elemType
		info.isStruct = elemType.Kind() == reflect.Struct
	}

	typeCache[t] = info
	return info
}

// optimizedUnmarshal - оптимизированная версия с использованием json.RawMessage
type tempStruct struct {
	Code string `json:"code"`
}

// UnmarshalTyped - дженерик версия для прямого парсинга в конкретный тип
func UnmarshalTyped[T any](body []byte) (*T, error) {
	if len(body) == 0 {
		return nil, errors.New("unexpected end of JSON input")
	}

	var result T
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal to type %T: %w", result, err)
	}

	return &result, nil
}

// UnmarshalErrorTyped - дженерик версия для парсинга ошибок с type safety
func UnmarshalErrorTyped[T any](body []byte) (ErrorTyped[T], error) {
	if len(body) == 0 {
		return nil, errors.New("unexpected end of JSON input")
	}

	var result TypedError[T]
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal error to type %T: %w", result, err)
	}

	return &result, nil
}

// Registry - реестр типов ошибок с дженерик поддержкой
type Registry[T any] struct {
	types map[string]func() T
	mutex sync.RWMutex
}

func NewRegistry[T any]() *Registry[T] {
	return &Registry[T]{
		types: make(map[string]func() T),
	}
}

func (r *Registry[T]) Register(code string, factory func() T) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.types[code] = factory
}

func (r *Registry[T]) UnmarshalByCode(body []byte) (T, error) {
	var zero T

	if len(body) == 0 {
		return zero, errors.New("unexpected end of JSON input")
	}

	// Быстрое извлечение кода ошибки
	var temp tempStruct
	if err := json.Unmarshal(body, &temp); err != nil {
		return zero, fmt.Errorf("failed to extract error code: %w", err)
	}

	r.mutex.RLock()
	factory, exists := r.types[temp.Code]
	r.mutex.RUnlock()

	if !exists {
		return zero, fmt.Errorf("%w: code '%s'", ErrUnknownCode, temp.Code)
	}

	result := factory()
	if err := json.Unmarshal(body, &result); err != nil {
		return zero, fmt.Errorf("failed to unmarshal to registered type for code '%s': %w", temp.Code, err)
	}

	return result, nil
}

// Unmarshal разбирает JSON тело ответа в соответствующий тип ошибки из errInstances.
// errInstances должен содержать указатели на нулевые значения типов ошибок.
func Unmarshal(body []byte, errInstances map[string]error) error {
	if err := validateInput(body); err != nil {
		return err
	}

	// Сначала пытаемся получить базовую структуру для извлечения кода
	base, err := parseBaseError(body)
	if err != nil {
		return err
	}

	return createSpecificErrorOptimized(body, base.Code, errInstances)
}

// createSpecificErrorOptimized - оптимизированная версия создания специфичной ошибки с кэшированием
func createSpecificErrorOptimized(body []byte, code string, errInstances map[string]error) error {
	instancePrototype, ok := errInstances[code]
	if !ok {
		return fmt.Errorf("%w: code '%s'", ErrUnknownCode, code)
	}

	// Получаем кэшированную информацию о типе
	prototypeType := reflect.TypeOf(instancePrototype)
	typeInfo := getTypeInfo(prototypeType)

	if !typeInfo.isValidPtr {
		return fmt.Errorf("invalid configuration: errInstances must contain pointers to error types, but got %T for code '%s'", instancePrototype, code)
	}
	if !typeInfo.isStruct {
		return fmt.Errorf("invalid configuration: errInstances must contain pointers to struct types, but got pointer to %s for code '%s'", typeInfo.elemType.Kind(), code)
	}

	// Создаем новый экземпляр и парсим напрямую в него
	newErrInstance := reflect.New(typeInfo.elemType)
	if err := json.Unmarshal(body, newErrInstance.Interface()); err != nil {
		return fmt.Errorf("failed to unmarshal specific error type '%T' for code '%s': %w", newErrInstance.Interface(), code, err)
	}

	finalErr, ok := newErrInstance.Interface().(error)
	if !ok {
		// Это не должно произойти
		return fmt.Errorf("internal error: instance for code '%s' does not implement error interface", code)
	}
	return finalErr
}

// UnmarshalFast - еще более быстрая версия для hot path, требует предварительной валидации errInstances
func UnmarshalFast(body []byte, errInstances map[string]error) error {
	if len(body) == 0 {
		return errors.New("unexpected end of JSON input")
	}

	// Быстрое извлечение кода ошибки
	var temp tempStruct
	if err := json.Unmarshal(body, &temp); err != nil {
		return fmt.Errorf("failed to unmarshal error code: %w", err)
	}

	instancePrototype, ok := errInstances[temp.Code]
	if !ok {
		return fmt.Errorf("%w: code '%s'", ErrUnknownCode, temp.Code)
	}

	// Пропускаем проверки валидации для производительности
	prototypeType := reflect.TypeOf(instancePrototype)
	typeInfo := getTypeInfo(prototypeType)

	newErrInstance := reflect.New(typeInfo.elemType)
	if err := json.Unmarshal(body, newErrInstance.Interface()); err != nil {
		return fmt.Errorf("failed to unmarshal specific error type for code '%s': %w", temp.Code, err)
	}

	return newErrInstance.Interface().(error)
}

// validateInput проверяет корректность входных данных
func validateInput(body []byte) error {
	if len(body) == 0 {
		// json.Unmarshal(nil, ...) и json.Unmarshal([]byte{}, ...) возвращают EOF.
		// Вернем стандартную ошибку для согласованности
		return errors.New("unexpected end of JSON input")
	}
	return nil
}

// parseBaseError разбирает JSON в базовую структуру для получения кода ошибки
func parseBaseError(body []byte) (*Base, error) {
	var base Base
	if err := json.Unmarshal(body, &base); err != nil {
		// Если не удалось разобрать даже базовую структуру, возвращаем ошибку парсинга
		return nil, fmt.Errorf("failed to unmarshal base error structure: %w", err)
	}
	return &base, nil
}

// createSpecificError создает конкретный тип ошибки на основе кода
func createSpecificError(body []byte, code string, errInstances map[string]error) error {
	instancePrototype, ok := errInstances[code]
	if !ok {
		// Если код не найден, возвращаем ошибку неизвестного кода
		return fmt.Errorf("%w: code '%s'", ErrUnknownCode, code)
	}

	if err := validateErrorPrototype(instancePrototype, code); err != nil {
		return err
	}

	return unmarshalToSpecificType(body, instancePrototype, code)
}

// validateErrorPrototype проверяет корректность прототипа ошибки
func validateErrorPrototype(instancePrototype error, code string) error {
	prototypeType := reflect.TypeOf(instancePrototype)
	if prototypeType.Kind() != reflect.Ptr {
		return fmt.Errorf("invalid configuration: errInstances must contain pointers to error types, but got %T for code '%s'", instancePrototype, code)
	}
	elemType := prototypeType.Elem()
	if elemType.Kind() != reflect.Struct {
		return fmt.Errorf("invalid configuration: errInstances must contain pointers to struct types, but got pointer to %s for code '%s'", elemType.Kind(), code)
	}
	return nil
}

// unmarshalToSpecificType создает и заполняет экземпляр конкретного типа ошибки
func unmarshalToSpecificType(body []byte, instancePrototype error, code string) error {
	// Создаем новый экземпляр (указатель) нужного типа ошибки
	elemType := reflect.TypeOf(instancePrototype).Elem()
	newErrInstance := reflect.New(elemType)

	// Анмаршалим тело JSON в новый экземпляр
	if err := json.Unmarshal(body, newErrInstance.Interface()); err != nil {
		// Если произошла ошибка при разборе в конкретный тип (хотя база разобралась)
		return fmt.Errorf("failed to unmarshal specific error type '%T' for code '%s': %w", newErrInstance.Interface(), code, err)
	}

	// Возвращаем успешно разобранную конкретную ошибку.
	// Преобразуем reflect.Value (указатель) в интерфейс error
	finalErr, ok := newErrInstance.Interface().(error)
	if !ok {
		// Это не должно произойти, если errInstances настроен правильно
		return fmt.Errorf("internal error: instance for code '%s' does not implement error interface", code)
	}
	return finalErr
}

// Error возвращает строковое представление базовой ошибки
func (b *Base) Error() string {
	return fmt.Sprintf("status: %d, code: %s, message: %s", b.Status, b.Code, b.Message)
}
