# SSE (Server-Sent Events) Architecture

Эта документация описывает архитектуру системы событий в реальном времени для PMS (Project Management System).

## Обзор архитектуры

Система построена на основе Server-Sent Events (SSE) и обеспечивает передачу событий в реальном времени между клиентом и сервером.

## Обзор эндпоинтов

### Сводная таблица эндпоинтов

| Эндпоинт | Метод | Назначение | Тип |
|----------|--------|------------|-----|
| `/v1/events/source` | GET | Установка SSE-соединения | SSE |
| `/v1/events/unread` | GET | Получение непрочитанных событий | REST API |
| `/v1/products/{productID}/proposals/{proposalID}/message` | POST | Отправка сообщения по заявке | REST API + SSE |
| `/v1/products/{productID}/proposals/{proposalID}/status` | POST | Изменение статуса заявки | REST API + SSE |
| `/v1/products/{productID}/proposals/{proposalID}/history` | GET | Получение истории заявки | REST API |
| `/v1/products/{productID}/proposals/{proposalID}/view` | PUT | Отметка просмотра заявки | REST API |

### Краткое описание эндпоинтов

#### **SSE Эндпоинты**
- **`GET /v1/events/source`** - Основной эндпоинт для установки SSE-соединения. Поддерживает keep-alive, JWT аутентификацию и автоматическое управление соединениями.
- **`GET /v1/events/unread`** - Получение списка непрочитанных событий для пользователя. Учитывает timestamp последнего просмотра заявок.

#### **Proposal Эндпоинты**
- **`POST /v1/products/{productID}/proposals/{proposalID}/message`** - Отправка сообщения по заявке с автоматической генерацией SSE события `proposal_message_received`.
- **`POST /v1/products/{productID}/proposals/{proposalID}/status`** - Изменение статуса заявки с генерацией SSE события `proposal_status_changed`.
- **`GET /v1/products/{productID}/proposals/{proposalID}/history`** - Получение полной истории заявки (сообщения + изменения статуса).
- **`PUT /v1/products/{productID}/proposals/{proposalID}/view`** - Отметка времени последнего просмотра заявки пользователем.

### Связи между эндпоинтами

```
┌────────────────────────────────────────────────────────────────────┐
│                      SSE События + База данных                     │
├────────────────────────────────────────────────────────────────────┤
│  POST /message  ──────────────────────────────┐                    │
│       │                                       │                    │
│       ▼                                       ▼                    │
│  DB: proposals_history ──────────────────► GET /events/source      │
│  (event_type: "message")                      │                    │
│       │                                       │                    │
│       ▼                                       ▼                    │
│  proposalMessage ────────────────────────► SSE Manager             │
│                                               │                    │
│  POST /status  ───────────────────────────────┤                    │
│       │                                       │                    │
│       ▼                                       ▼                    │
│  DB: proposals_history ──────────────────► proposalStatus          │
│  (event_type: "status")                       │                    │
│                                               ▼                    │
│  PUT /view ──────────────────────────────► GET /events/unread      │
│                                                                    │
│  GET /history ◄─────────────────────────── proposals_history       │
│                                            (message + status)      │
└────────────────────────────────────────────────────────────────────┘
```

## Основные компоненты

### 1. SSE Эндпоинты

#### GET /v1/events/source
- **Назначение**: Установка SSE-соединения для получения событий в реальном времени
- **Тип**: Server-Sent Events (Content-Type: text/event-stream)
- **Аутентификация**: Требуется JWT токен
- **Функциональность**: 
  - Установка постоянного соединения
  - Отправка keep-alive сообщений каждые 10 секунд
  - Автоматическое управление соединениями

#### GET /v1/events/unread
- **Назначение**: Получение непрочитанных событий для пользователя
- **Тип**: REST API (application/json)
- **Возвращает**: Массив событий типа `EventSourceResponseItemV1DTO`

### 2. Proposal Эндпоинты (интеграция с SSE)

#### POST /v1/products/{productID}/proposals/{proposalID}/message
- **Назначение**: Отправка сообщения по заявке
- **Сохранение**: Сообщения сохраняются в таблицу `proposals_history` с типом события `"message"`
- **SSE События**: Генерирует `proposalMessage` для получателей
- **Логика получателей** (динамически определяется из базы данных):
  - Если отправитель админ → получатель создатель заявки (ID извлекается из сущности Proposal)
  - Если отправитель обычный пользователь → получатели все админы из БД + создатель заявки

#### POST /v1/products/{productID}/proposals/{proposalID}/status
- **Назначение**: Изменение статуса заявки
- **Сохранение**: Изменения статуса автоматически сохраняются в таблицу `proposals_history` с типом события `"status"`
- **SSE События**: Генерирует `proposalStatus` для связанных пользователей
- **Статусы**: draft, on_approval, rejected, approved

#### GET /v1/products/{productID}/proposals/{proposalID}/history
- **Назначение**: Получение истории заявки
- **Возвращает**: Массив событий истории (сообщения + изменения статуса)
- **Источник данных**: Таблица `proposals_history` в базе данных
- **Типы событий**: `"message"` для сообщений чата, `"status"` для изменений статуса

#### PUT /v1/products/{productID}/proposals/{proposalID}/view
- **Назначение**: Отметка последнего просмотра заявки пользователем
- **Функциональность**: Обновляет timestamp последнего просмотра заявки для конкретного пользователя
- **Тело запроса**: `ProposalLastViewedAtV1DTO` с полем `lastViewedAt`
- **Использование**: Для определения непрочитанных сообщений и событий по заявке
- **Связь с SSE**: Влияет на отображение непрочитанных событий в `/v1/events/unread`

## Типы событий

### EventStatus
```typescript
enum EventStatus {
  "default" | "success" | "error" | "warning"
}
```

### Базовая структура события
```typescript
interface EventSourceResponseItem {
  type: string;
  createdAt: string;
  message?: string;
  status: EventStatus;
  meta: object;
}
```

### ProposalStatusEvent
```typescript
interface ProposalStatusEvent {
  type: "proposalStatus";
  createdAt: string;
  message:? string;
  status?: EventStatus;
  meta: ProposalStatusMeta;
}

interface ProposalStatusMeta {
  productID: number;
  proposalID: number;
  status: ProposalStatus;
}
```

### ProposalMessageEvent
```typescript
interface ProposalMessageEvent {
  type: "proposalMessage";
  createdAt: string;
  message?: string;
  status?: EventStatus;
  meta: ProposalMessageMeta;
}

interface ProposalMessageMeta {
  productID: number;
  proposalID: number;
  userID: number;
}
```

## Типы заявок

### ProposalStatus
```typescript
enum ProposalStatus {
  "draft" | "archive" | "on_approval" | "rejected" | "approved"
}
```

## История заявок

### ProposalHistoryContainer
```typescript
interface ProposalHistoryContainer {
  items: (ProposalHistoryMessageEvent | ProposalHistoryStatusEvent)[];
  meta: {
    lastViewedAt: string;
  };
}
```

### ProposalHistoryMessageEvent
```typescript
interface ProposalHistoryMessageEvent {
  type: "message";
  createdAt: string;
  message?: string;
  status?: ProposalHistoryEventStatus;
  meta: ProposalHistoryMessageMeta;
}

interface ProposalHistoryMessageMeta {
  userID: number;
}
```

### ProposalHistoryStatusEvent
```typescript
interface ProposalHistoryStatusEvent {
  type: "status";
  createdAt: string;
  message?: string;
  meta: ProposalHistoryStatusMeta;
}

interface ProposalHistoryStatusMeta {
  status: ProposalStatus;
}
```

## Текущая реализация

### Что реализовано
- ✅ SSE Manager для управления соединениями
- ✅ Эндпоинт `/v1/events/source` с полной поддержкой SSE
- ✅ Интеграция SSE в `SendProposalMessage` эндпоинт
- ✅ Интеграция SSE в `SendProposalToApprovalOrReject` эндпоинт
- ✅ Эндпоинт `/v1/products/{productID}/proposals/{proposalID}/view` для отметки просмотра
- ✅ Аутентификация SSE соединений
- ✅ Keep-alive механизм
- ✅ Логирование всех SSE операций
- ✅ Сохранение сообщений в базу данных
- ✅ **Интеграция с proposal history** - сообщения сохраняются в таблицу `proposals_history`
- ✅ **Типизация событий** - обновлена OpenAPI спецификация с правильными типами событий
- ✅ Динамическое определение получателей сообщений из базы данных

### Что требует доработки
- ❌ Эндпоинт `/v1/events/unread` (пока заглушка)
- ❌ Персистентность непрочитанных событий

## Логика работы

### Отправка сообщения
1. Пользователь отправляет POST запрос на `/v1/products/{productID}/proposals/{proposalID}/message`
2. Сервер валидирует запрос и проверяет существование заявки
3. **Сервер сохраняет сообщение в базу данных** в таблицу `proposals_history` с типом события `"message"`
4. **Сервер динамически определяет получателей сообщения из базы данных**:
   - Извлекает CreatorID из сущности Proposal
   - Получает список всех админов из таблицы users (IsAdmin = true)
   - Применяет логику: админ→создатель, пользователь→админы+создатель
5. Сервер отправляет SSE событие `proposalMessage` всем получателям
6. Клиенты получают событие через активное SSE соединение

### Изменение статуса заявки
1. Пользователь отправляет POST запрос на `/v1/products/{productID}/proposals/{proposalID}/status`
2. Сервер обновляет статус в базе данных
3. **Сервер автоматически сохраняет изменение статуса в `proposals_history`** с типом события `"status"`
4. Сервер отправляет SSE событие `proposalStatus` связанным пользователям
5. Клиенты получают уведомление об изменении статуса

### Отметка просмотра заявки
1. Пользователь открывает заявку в интерфейсе
2. Клиент отправляет PUT запрос на `/v1/products/{productID}/proposals/{proposalID}/view`
3. Сервер обновляет timestamp последнего просмотра для пользователя
4. Последующие запросы к `/v1/events/unread` учитывают этот timestamp для определения непрочитанных событий

### Подключение к SSE
1. Клиент выполняет GET запрос на `/v1/events/source` с JWT токеном
2. Сервер аутентифицирует пользователя и устанавливает SSE соединение
3. Сервер отправляет keep-alive сообщения каждые 10 секунд
4. Клиент остается подключенным и получает события в реальном времени

## Безопасность

- Все SSE соединения требуют аутентификации через JWT токены
- Пользователи получают только те события, на которые у них есть права
- Соединения автоматически закрываются при истечении токена
- Защита от несанкционированного доступа к событиям других пользователей

## Производительность

- Автоматическое управление соединениями (удаление мертвых соединений)
- Heartbeat проверки для поддержания активных соединений
- Эффективная рассылка событий с помощью горутин
- Логирование для мониторинга производительности

## Работа с базой данных

### Таблица proposals_history

Все сообщения чата и изменения статуса сохраняются в таблицу `proposals_history` со следующей структурой:

```sql
CREATE TABLE proposals_history (
    id          BIGINT PRIMARY KEY,
    proposal_id BIGINT NOT NULL,
    event_type  VARCHAR NOT NULL,       -- "message" или "status"
    status      VARCHAR,                -- пустое для сообщений
    message     TEXT,                   -- текст сообщения или комментарий
    user_id     BIGINT,                 -- ID пользователя (отправителя)
    created_at  TIMESTAMP DEFAULT NOW()
);
```

### Типы событий в БД

- **`"message"`** - сообщения чата пользователей
  - `message` содержит текст сообщения
  - `status` остается пустым
  - `user_id` содержит ID отправителя

- **`"status"`** - изменения статуса заявки
  - `status` содержит новый статус заявки
  - `message` может содержать комментарий к изменению
  - `user_id` содержит ID пользователя, изменившего статус

### SQLC интеграция

Для безопасной работы с базой данных используется SQLC:

```go
// Создание записи сообщения
func (p *ProposalHistoryDB) CreateMessage(proposalID int64, message string, userID int64) error {
    _, err := p.queries.Create(context.Background(), sqlcproposalhistory.CreateParams{
        ProposalID: proposalID,
        EventType:  constants.EventTypeMessage,
        Status:     "", // Для сообщений статус пустой
        Message:    &message,
        UserID:     &userID,
    })
    return err
}
```

### Архитектурные слои

1. **Presentation Layer**: `internal/presentation/api/proposal.go`
   - Принимает HTTP запросы
   - Вызывает Application Service

2. **Application Layer**: `internal/application/proposal/service/proposal.go`
   - Координирует бизнес-логику
   - Вызывает Domain Service

3. **Domain Layer**: `internal/domain/proposal/service/proposal.go`
   - Содержит бизнес-логику
   - Вызывает Repository

4. **Infrastructure Layer**: `internal/infrastructure/repositories/primedb/proposal_history.go`
   - Работает с базой данных через SQLC
   - Реализует интерфейсы Repository

## Динамическое определение получателей сообщений

### Архитектура решения

#### 1. Доменная модель
```go
// internal/domain/proposal/entity/proposal.go
type Proposal struct {
    ID        int64
    CreatorID int64  // ID создателя заявки
    // ... другие поля
}

// internal/domain/user/entity/user.go
type User struct {
    ID      int64
    IsAdmin bool     // Флаг администратора
    // ... другие поля
}
```

#### 2. Репозиторий пользователей
```go
// internal/domain/user/repository/user.go
type UserPrimeDB interface {
    GetAdminUsers() ([]userentity.User, error)
    // ... другие методы
}

// internal/infrastructure/repositories/primedb/user.go
func (db *UserDB) GetAdminUsers() ([]userentity.User, error) {
    isAdmin := true
    adminFilter := userentity.UserFiltersData{
        IsAdmin: &isAdmin,
    }
    return db.GetByFilters(adminFilter)
}
```

#### 3. Логика определения получателей
```go
// internal/presentation/api/proposal.go
func (h *ProposalHandler) getMessageRecipients(prop any, senderID int64, isAdmin bool) []int64 {
    var recipients []int64
    
    // Извлечение CreatorID из заявки
    var creatorID int64
    if proposal, ok := prop.(*proposalentity.Proposal); ok {
        creatorID = proposal.CreatorID
    }
    
    if isAdmin {
        // Админ отправляет → получатель создатель заявки
        if creatorID != senderID {
            recipients = append(recipients, creatorID)
        }
    } else {
        // Пользователь отправляет → получатели все админы из БД
        adminUsers, err := h.appService.User.GetAdminUsers()
        if err == nil {
            for _, admin := range adminUsers {
                if admin.ID != senderID {
                    recipients = append(recipients, admin.ID)
                }
            }
        }
        
        // Также добавляем создателя заявки
        if creatorID != senderID {
            recipients = append(recipients, creatorID)
        }
    }
    
    return recipients
}
```

### Слои архитектуры

1. **Presentation Layer**: `internal/presentation/api/proposal.go`
   - Метод `getMessageRecipients` для определения получателей
   - Интеграция с Application Service для получения админов

2. **Application Layer**: `internal/application/user/service/user.go`
   - Метод `GetAdminUsers` для получения списка админов
   - Координация между доменными сервисами

3. **Domain Layer**: `internal/domain/user/service/user.go`
   - Доменный сервис для работы с пользователями
   - Бизнес-логика определения админов

4. **Infrastructure Layer**: `internal/infrastructure/repositories/primedb/user.go`
   - Реализация запросов к базе данных
   - Фильтрация пользователей по флагу IsAdmin

## Будущие улучшения

1. **Масштабирование**: Поддержка нескольких серверов через Redis pub/sub
2. **Метрики**: Детальные метрики производительности SSE системы