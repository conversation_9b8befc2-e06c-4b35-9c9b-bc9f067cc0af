version: "2"

servers:
  - engine: &postgresql "postgresql"
    uri: ${DATABASE_URL}

# user
# participant
# role
# group
# product
# category
# categoryrole
# categorygroup
# categorypermission
# grouprole
# permission
# rolepermission
# db
# proposal
# section
# participantgroup
# participantrole
# userrole
# usergroup
# proposalhistory

sql:
  - name: "user"
    engine: *postgresql
    schema: &common-schema "./migrations"
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/user.sql"
    gen:
      go:
        package: "user"
        sql_package: &pgxV5 "pgx/v5"
        out: "./gen/sqlc/user"
        emit_pointers_for_null_types: true
        overrides: &common-overrides
          - go_type: "int64"
            db_type: "serial"
            null: true
          - go_type: "int64"
            db_type: "pg_catalog.int4"
            null: true
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
          - db_type: "pg_catalog.int4"
            go_type:
              type: "int64"
              pointer: true
            nullable: true
          - db_type: "pg_catalog.numeric"
            go_type: "float64"
            null: true
          - db_type: "pg_catalog.numeric"
            go_type:
              type: "float64"
              pointer: true
            nullable: true
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
              pointer: true
            nullable: true
          - db_type: "jsonb"
            go_type:
              import: "encoding/json"
              type: "RawMessage"
              pointer: true
          - db_type: "bytea"
            go_type:
              type: "[]byte"
              pointer: true
            nullable: true
  - name: "participant"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/participant.sql"
    gen:
      go:
        package: "participant"
        sql_package: *pgxV5
        out: "./gen/sqlc/participant"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "role"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/role.sql"
    gen:
      go:
        package: "role"
        sql_package: *pgxV5
        out: "./gen/sqlc/role"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "group"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/group.sql"
    gen:
      go:
        package: "group"
        sql_package: *pgxV5
        out: "./gen/sqlc/group"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "product"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/product.sql"
    gen:
      go:
        package: "product"
        sql_package: *pgxV5
        out: "./gen/sqlc/product"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "category"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/category.sql"
    gen:
      go:
        package: "category"
        sql_package: *pgxV5
        out: "./gen/sqlc/category"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "categoryrole"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/category_role.sql"
    gen:
      go:
        package: "categoryrole"
        sql_package: *pgxV5
        out: "./gen/sqlc/categoryrole"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "categorygroup"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/category_group.sql"
    gen:
      go:
        package: "categorygroup"
        sql_package: *pgxV5
        out: "./gen/sqlc/categorygroup"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "categorypermission"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/category_permission.sql"
    gen:
      go:
        package: "categorypermission"
        sql_package: *pgxV5
        out: "./gen/sqlc/categorypermission"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "grouprole"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/group_role.sql"
    gen:
      go:
        package: "grouprole"
        sql_package: *pgxV5
        out: "./gen/sqlc/grouprole"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "permission"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/permission.sql"
    gen:
      go:
        package: "permission"
        sql_package: *pgxV5
        out: "./gen/sqlc/permission"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "rolepermission"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/role_permission.sql"
    gen:
      go:
        package: "rolepermission"
        sql_package: *pgxV5
        out: "./gen/sqlc/rolepermission"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "db"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/db.sql"
    gen:
      go:
        package: "dbversion"
        sql_package: *pgxV5
        out: "./gen/sqlc/dbversion"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "proposal"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/proposal.sql"
    gen:
      go:
        package: "proposal"
        sql_package: *pgxV5
        out: "./gen/sqlc/proposal"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "section"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/section.sql"
    gen:
      go:
        package: "section"
        sql_package: *pgxV5
        out: "./gen/sqlc/section"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "participantgroup"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/participant_group.sql"
    gen:
      go:
        package: "participantgroup"
        sql_package: *pgxV5
        out: "./gen/sqlc/participantgroup"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "participantrole"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/participant_role.sql"
    gen:
      go:
        package: "participantrole"
        sql_package: *pgxV5
        out: "./gen/sqlc/participantrole"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "userrole"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/user_role.sql"
    gen:
      go:
        package: "userrole"
        sql_package: *pgxV5
        out: "./gen/sqlc/userrole"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "usergroup"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/user_group.sql"
    gen:
      go:
        package: "usergroup"
        sql_package: *pgxV5
        out: "./gen/sqlc/usergroup"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "proposalhistory"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/proposal_history.sql"
    gen:
      go:
        package: "proposalhistory"
        sql_package: *pgxV5
        out: "./gen/sqlc/proposalhistory"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
  - name: "proposaluserviews"
    engine: *postgresql
    schema: *common-schema
    queries:
      - "./internal/infrastructure/repositories/primedb/queries/proposal_user_views.sql"
    gen:
      go:
        package: "proposaluserviews"
        sql_package: *pgxV5
        out: "./gen/sqlc/proposaluserviews"
        emit_pointers_for_null_types: true
        overrides: *common-overrides
