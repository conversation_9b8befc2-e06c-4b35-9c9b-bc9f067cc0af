# Line ending normalization
* text=auto eol=lf

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.tar.gz binary
*.tar binary
*.gz binary

# Text files
*.sh text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.json text eol=lf
*.go text eol=lf
*.mod text eol=lf
*.sum text eol=lf
*.md text eol=lf
*.txt text eol=lf
*.sql text eol=lf
*.conf text eol=lf
*.env text eol=lf

# Makefile
Makefile text eol=lf
*.mk text eol=lf

# Dockerfile
Dockerfile text eol=lf

# Generated SQLC files
gen/sqlc/**/*.go text eol=lf

# Executables
*.exe binary
*.dll binary
*.so binary
*.dylib binary 