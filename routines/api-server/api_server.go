package apiserver

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	appctx "github.com/nixys/nxs-go-appctx/v3"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	routes "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/presentation/http"
)

type httpServerContext struct {
	http.Server
	done chan any
}

func Runtime(app appctx.App) error {

	cc := app.ValueGet().(*ctx.Ctx)

	s := startServer(cc)

	<-app.SelfCtxDone()

	c, f := context.WithTimeout(app.SelfCtx(), 1*time.Second)
	defer f()

	cc.Log.Debugf("api: shutting down")

	err := shutdownServer(c, s)
	if err != nil {

		cc.Log.With(
			slog.Any("details", err),
		).Error("api: shutdown")

		err = fmt.Errorf("api: %w", err)
	}
	return err
}

func startServer(cc *ctx.Ctx) *httpServerContext {

	s := &httpServerContext{
		Server: http.Server{
			Addr:         cc.API.Bind,
			ReadTimeout:  60 * time.Second,
			WriteTimeout: 60 * time.Second,
			IdleTimeout:  120 * time.Second,
			Handler:      routes.SetRoutes(cc),
		},
		done: make(chan any),
	}

	go func() {
		cc.Log.With(
			slog.Any("execution time", time.Since(cc.ServerStartTime).String()),
		).Debug("api: server starting")

		if cc.API.TLS != nil {
			if err := s.ListenAndServeTLS(cc.API.TLS.CertFile, cc.API.TLS.KeyFile); err != nil {
				cc.Log.With(
					slog.Any("details", err),
				).Debug("api: server listen tls")
			}
		} else {
			if err := s.ListenAndServe(); err != nil {
				cc.Log.With(
					slog.Any("details", err),
				).Debug("api: server listen")
			}
		}
		s.done <- true
	}()

	return s
}

func shutdownServer(c context.Context, s *httpServerContext) error {

	// Shutdown server
	if err := s.Shutdown(c); err != nil {
		return fmt.Errorf("server shutdown: %w", err)
	}

	<-s.done
	return nil
}
