// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: proposal_user_views.sql

package proposaluserviews

import (
	"context"
	"time"
)

const getByProposalIDAndUserID = `-- name: GetByProposalIDAndUserID :one
SELECT id, proposal_id, user_id, viewed_at
FROM proposal_user_views
WHERE proposal_id = $1::bigint
  AND user_id = $2::bigint
`

type GetByProposalIDAndUserIDParams struct {
	ProposalID int64
	UserID     int64
}

func (q *Queries) GetByProposalIDAndUserID(ctx context.Context, arg GetByProposalIDAndUserIDParams) (ProposalUserView, error) {
	row := q.db.QueryRow(ctx, getByProposalIDAndUserID, arg.ProposalID, arg.UserID)
	var i ProposalUserView
	err := row.Scan(
		&i.ID,
		&i.ProposalID,
		&i.UserID,
		&i.ViewedAt,
	)
	return i, err
}

const update = `-- name: Update :one
INSERT INTO proposal_user_views (proposal_id, user_id, viewed_at)
VALUES ($1, $2, $3)
ON CONFLICT (proposal_id, user_id)
    DO UPDATE SET viewed_at = $3
RETURNING id, proposal_id, user_id, viewed_at
`

type UpdateParams struct {
	ProposalID int64
	UserID     int64
	ViewedAt   time.Time
}

func (q *Queries) Update(ctx context.Context, arg UpdateParams) (ProposalUserView, error) {
	row := q.db.QueryRow(ctx, update, arg.ProposalID, arg.UserID, arg.ViewedAt)
	var i ProposalUserView
	err := row.Scan(
		&i.ID,
		&i.ProposalID,
		&i.UserID,
		&i.ViewedAt,
	)
	return i, err
}
