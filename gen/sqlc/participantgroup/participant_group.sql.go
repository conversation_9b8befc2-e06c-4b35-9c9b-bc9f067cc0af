// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: participant_group.sql

package participantgroup

import (
	"context"
)

const createByGroupIDAndParticipantIDs = `-- name: CreateByGroupIDAndParticipantIDs :exec
INSERT INTO participants_groups (participant_id, group_id)
SELECT unnest($1::BIGINT[]), $2
    ON CONFLICT (participant_id, group_id) DO NOTHING
`

type CreateByGroupIDAndParticipantIDsParams struct {
	ParticipantIds []int64
	GroupID        int64
}

func (q *Queries) CreateByGroupIDAndParticipantIDs(ctx context.Context, arg CreateByGroupIDAndParticipantIDsParams) error {
	_, err := q.db.Exec(ctx, createByGroupIDAndParticipantIDs, arg.ParticipantIds, arg.GroupID)
	return err
}

const createParticipantGroup = `-- name: CreateParticipantGroup :one
INSERT INTO participants_groups (participant_id, group_id)
VALUES ($1, $2)
ON CONFLICT (participant_id, group_id) DO NOTHING
RETURNING id, participant_id, group_id, created_at
`

type CreateParticipantGroupParams struct {
	ParticipantID int64
	GroupID       int64
}

func (q *Queries) CreateParticipantGroup(ctx context.Context, arg CreateParticipantGroupParams) (ParticipantsGroup, error) {
	row := q.db.QueryRow(ctx, createParticipantGroup, arg.ParticipantID, arg.GroupID)
	var i ParticipantsGroup
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.GroupID,
		&i.CreatedAt,
	)
	return i, err
}

const deleteParticipantGroupByParticipantAndGroupID = `-- name: DeleteParticipantGroupByParticipantAndGroupID :exec
DELETE
FROM participants_groups
WHERE participant_id = $1
  AND group_id = $2
`

type DeleteParticipantGroupByParticipantAndGroupIDParams struct {
	ParticipantID int64
	GroupID       int64
}

func (q *Queries) DeleteParticipantGroupByParticipantAndGroupID(ctx context.Context, arg DeleteParticipantGroupByParticipantAndGroupIDParams) error {
	_, err := q.db.Exec(ctx, deleteParticipantGroupByParticipantAndGroupID, arg.ParticipantID, arg.GroupID)
	return err
}

const deleteParticipantGroupsByGroupID = `-- name: DeleteParticipantGroupsByGroupID :exec
DELETE
FROM participants_groups
WHERE group_id = $1
`

func (q *Queries) DeleteParticipantGroupsByGroupID(ctx context.Context, groupID int64) error {
	_, err := q.db.Exec(ctx, deleteParticipantGroupsByGroupID, groupID)
	return err
}

const deleteParticipantGroupsByGroupIDAndParticipantIDs = `-- name: DeleteParticipantGroupsByGroupIDAndParticipantIDs :exec
DELETE
FROM participants_groups
WHERE group_id = $1
  AND participant_id = ANY ($2::BIGINT[])
`

type DeleteParticipantGroupsByGroupIDAndParticipantIDsParams struct {
	GroupID        int64
	ParticipantIds []int64
}

func (q *Queries) DeleteParticipantGroupsByGroupIDAndParticipantIDs(ctx context.Context, arg DeleteParticipantGroupsByGroupIDAndParticipantIDsParams) error {
	_, err := q.db.Exec(ctx, deleteParticipantGroupsByGroupIDAndParticipantIDs, arg.GroupID, arg.ParticipantIds)
	return err
}

const deleteParticipantGroupsByParticipantID = `-- name: DeleteParticipantGroupsByParticipantID :exec
DELETE
FROM participants_groups
WHERE participant_id = $1
`

func (q *Queries) DeleteParticipantGroupsByParticipantID(ctx context.Context, participantID int64) error {
	_, err := q.db.Exec(ctx, deleteParticipantGroupsByParticipantID, participantID)
	return err
}

const deleteParticipantGroupsByParticipantIDs = `-- name: DeleteParticipantGroupsByParticipantIDs :exec
DELETE
FROM participants_groups
WHERE participant_id = ANY ($1::BIGINT[])
`

func (q *Queries) DeleteParticipantGroupsByParticipantIDs(ctx context.Context, dollar_1 []int64) error {
	_, err := q.db.Exec(ctx, deleteParticipantGroupsByParticipantIDs, dollar_1)
	return err
}

const getAllParticipantGroups = `-- name: GetAllParticipantGroups :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
`

func (q *Queries) GetAllParticipantGroups(ctx context.Context) ([]ParticipantsGroup, error) {
	rows, err := q.db.Query(ctx, getAllParticipantGroups)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsGroup
	for rows.Next() {
		var i ParticipantsGroup
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.GroupID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantGroupByID = `-- name: GetParticipantGroupByID :one
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetParticipantGroupByID(ctx context.Context, id int64) (ParticipantsGroup, error) {
	row := q.db.QueryRow(ctx, getParticipantGroupByID, id)
	var i ParticipantsGroup
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.GroupID,
		&i.CreatedAt,
	)
	return i, err
}

const getParticipantGroupByParticipantAndGroupID = `-- name: GetParticipantGroupByParticipantAndGroupID :one
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE participant_id = $1
  AND group_id = $2
LIMIT 1
`

type GetParticipantGroupByParticipantAndGroupIDParams struct {
	ParticipantID int64
	GroupID       int64
}

func (q *Queries) GetParticipantGroupByParticipantAndGroupID(ctx context.Context, arg GetParticipantGroupByParticipantAndGroupIDParams) (ParticipantsGroup, error) {
	row := q.db.QueryRow(ctx, getParticipantGroupByParticipantAndGroupID, arg.ParticipantID, arg.GroupID)
	var i ParticipantsGroup
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.GroupID,
		&i.CreatedAt,
	)
	return i, err
}

const getParticipantGroupsByGroupID = `-- name: GetParticipantGroupsByGroupID :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE group_id = $1
`

func (q *Queries) GetParticipantGroupsByGroupID(ctx context.Context, groupID int64) ([]ParticipantsGroup, error) {
	rows, err := q.db.Query(ctx, getParticipantGroupsByGroupID, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsGroup
	for rows.Next() {
		var i ParticipantsGroup
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.GroupID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantGroupsByParticipantID = `-- name: GetParticipantGroupsByParticipantID :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE participant_id = $1
`

func (q *Queries) GetParticipantGroupsByParticipantID(ctx context.Context, participantID int64) ([]ParticipantsGroup, error) {
	rows, err := q.db.Query(ctx, getParticipantGroupsByParticipantID, participantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsGroup
	for rows.Next() {
		var i ParticipantsGroup
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.GroupID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantGroupsByParticipantIDs = `-- name: GetParticipantGroupsByParticipantIDs :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE participant_id = ANY ($1::BIGINT[])
`

func (q *Queries) GetParticipantGroupsByParticipantIDs(ctx context.Context, dollar_1 []int64) ([]ParticipantsGroup, error) {
	rows, err := q.db.Query(ctx, getParticipantGroupsByParticipantIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsGroup
	for rows.Next() {
		var i ParticipantsGroup
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.GroupID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateParticipantGroup = `-- name: UpdateParticipantGroup :one
UPDATE participants_groups
SET participant_id = COALESCE($2::BIGINT, participant_id),
    group_id       = COALESCE($3::BIGINT, group_id)
WHERE id = $1
RETURNING id, participant_id, group_id, created_at
`

type UpdateParticipantGroupParams struct {
	ID            int64
	ParticipantID *int64
	GroupID       *int64
}

func (q *Queries) UpdateParticipantGroup(ctx context.Context, arg UpdateParticipantGroupParams) (ParticipantsGroup, error) {
	row := q.db.QueryRow(ctx, updateParticipantGroup, arg.ID, arg.ParticipantID, arg.GroupID)
	var i ParticipantsGroup
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.GroupID,
		&i.CreatedAt,
	)
	return i, err
}
