// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: category_role.sql

package categoryrole

import (
	"context"
)

const create = `-- name: Create :one
WITH ins AS (
    INSERT INTO categories_roles (category_id, role_id)
        VALUES ($1, $2)
        ON CONFLICT (category_id, role_id) DO NOTHING
        RETURNING id, category_id, role_id)
SELECT id, category_id, role_id
FROM ins
UNION ALL
SELECT id, category_id, role_id
FROM categories_roles
WHERE category_id = $1
  AND role_id = $2
LIMIT 1
`

type CreateParams struct {
	CategoryID int64
	RoleID     int64
}

type CreateRow struct {
	ID         int64
	CategoryID int64
	RoleID     int64
}

func (q *Queries) Create(ctx context.Context, arg CreateParams) (CreateRow, error) {
	row := q.db.QueryRow(ctx, create, arg.CategoryID, arg.RoleID)
	var i CreateRow
	err := row.Scan(&i.ID, &i.CategoryID, &i.RoleID)
	return i, err
}

const delete = `-- name: Delete :exec
DELETE
FROM categories_roles
WHERE category_id = $1
  AND role_id = $2
`

type DeleteParams struct {
	CategoryID int64
	RoleID     int64
}

func (q *Queries) Delete(ctx context.Context, arg DeleteParams) error {
	_, err := q.db.Exec(ctx, delete, arg.CategoryID, arg.RoleID)
	return err
}

const getAll = `-- name: GetAll :many
SELECT id, category_id, role_id
FROM categories_roles
`

func (q *Queries) GetAll(ctx context.Context) ([]CategoriesRole, error) {
	rows, err := q.db.Query(ctx, getAll)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesRole
	for rows.Next() {
		var i CategoriesRole
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.RoleID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByCategoryID = `-- name: GetByCategoryID :many
SELECT id, category_id, role_id
FROM categories_roles
WHERE category_id = $1
`

func (q *Queries) GetByCategoryID(ctx context.Context, categoryID int64) ([]CategoriesRole, error) {
	rows, err := q.db.Query(ctx, getByCategoryID, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesRole
	for rows.Next() {
		var i CategoriesRole
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.RoleID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByID = `-- name: GetByID :one
SELECT id, category_id, role_id
FROM categories_roles
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetByID(ctx context.Context, id int64) (CategoriesRole, error) {
	row := q.db.QueryRow(ctx, getByID, id)
	var i CategoriesRole
	err := row.Scan(&i.ID, &i.CategoryID, &i.RoleID)
	return i, err
}

const getByRoleID = `-- name: GetByRoleID :many
SELECT id, category_id, role_id
FROM categories_roles
WHERE role_id = $1
`

func (q *Queries) GetByRoleID(ctx context.Context, roleID int64) ([]CategoriesRole, error) {
	rows, err := q.db.Query(ctx, getByRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesRole
	for rows.Next() {
		var i CategoriesRole
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.RoleID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const hasCategoryRole = `-- name: HasCategoryRole :one
SELECT EXISTS(SELECT 1
              FROM categories_roles
              WHERE category_id = $1
                AND role_id = $2) AS exists
`

type HasCategoryRoleParams struct {
	CategoryID int64
	RoleID     int64
}

func (q *Queries) HasCategoryRole(ctx context.Context, arg HasCategoryRoleParams) (bool, error) {
	row := q.db.QueryRow(ctx, hasCategoryRole, arg.CategoryID, arg.RoleID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}
