// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: role.sql

package role

import (
	"context"
	"time"
)

const createRole = `-- name: CreateRole :one
INSERT INTO roles (name,
                   product_id,
                   is_system)
VALUES ($1,
        $2,
        $3)
RETURNING id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
`

type CreateRoleParams struct {
	Name      string
	ProductID *int64
	IsSystem  bool
}

func (q *Queries) CreateRole(ctx context.Context, arg CreateRoleParams) (Role, error) {
	row := q.db.QueryRow(ctx, createRole, arg.Name, arg.ProductID, arg.IsSystem)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.IsProtected,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deactivateByIDs = `-- name: DeactivateByIDs :exec
UPDATE roles
SET active_flg     = false,
    updated_at     = NOW()
WHERE id =  ANY ($1::bigint[])
`

func (q *Queries) DeactivateByIDs(ctx context.Context, roleIds []int64) error {
	_, err := q.db.Exec(ctx, deactivateByIDs, roleIds)
	return err
}

const existByName = `-- name: ExistByName :one
SELECT COUNT(*) > 0 AS exists
FROM roles
WHERE name = $1
`

func (q *Queries) ExistByName(ctx context.Context, name string) (bool, error) {
	row := q.db.QueryRow(ctx, existByName, name)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const getAllByUserID = `-- name: GetAllByUserID :many
WITH user_participants AS (SELECT p.id AS participant_id
                           FROM participants p
                           WHERE p.user_id = $1),
     user_roles AS (SELECT role_id
                    FROM users_roles
                    WHERE user_id = $1),
     participant_roles AS (SELECT pr.role_id
                           FROM participants_roles pr
                                    INNER JOIN user_participants up ON pr.participant_id = up.participant_id),
     user_groups AS (SELECT group_id
                     FROM users_groups
                     WHERE user_id = $1),
     participant_groups AS (SELECT pg.group_id
                            FROM participants_groups pg
                                     INNER JOIN user_participants up ON pg.participant_id = up.participant_id),
     all_group_ids AS (
         SELECT group_id
         FROM user_groups
         UNION
         SELECT group_id
         FROM participant_groups
     ),
     group_roles AS (SELECT role_id
                     FROM groups_roles gr
                              INNER JOIN all_group_ids agi ON gr.group_id = agi.group_id),
     all_role_ids AS (
         SELECT role_id
         FROM user_roles
         UNION
         SELECT role_id
         FROM participant_roles
         UNION
         SELECT role_id
         FROM group_roles
     )
SELECT r.id,
       r.name,
       r.product_id,
       r.is_system,
       r.active_flg,
       r.created_at,
       r.updated_at,
       r.deleted_at
FROM roles r
         INNER JOIN all_role_ids ari ON r.id = ari.role_id
`

type GetAllByUserIDRow struct {
	ID        int64
	Name      string
	ProductID *int64
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

func (q *Queries) GetAllByUserID(ctx context.Context, userID int64) ([]GetAllByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getAllByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllByUserIDRow
	for rows.Next() {
		var i GetAllByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllRoles = `-- name: GetAllRoles :many
SELECT id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
FROM roles
`

func (q *Queries) GetAllRoles(ctx context.Context) ([]Role, error) {
	rows, err := q.db.Query(ctx, getAllRoles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Role
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.IsProtected,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllRolesPaginated = `-- name: GetAllRolesPaginated :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       r.active_flg             AS is_active,
       p.id                     AS product_id,
       p.iid                    AS product_iid,
       p.name                   AS product_name,
       p.tech_name              AS tech_name,
       CASE
        WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                 AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
         LEFT JOIN products p ON r.product_id = p.id
GROUP BY r.id, r.name, r.active_flg, p.id, p.iid, p.name, p.tech_name, r.is_system
ORDER BY r.id, r.name
LIMIT $2::bigint OFFSET $1::bigint
`

type GetAllRolesPaginatedParams struct {
	OffsetValue int64
	LimitValue  int64
}

type GetAllRolesPaginatedRow struct {
	RoleID      int64
	RoleName    string
	IsActive    bool
	ProductID   *int32
	ProductIid  *string
	ProductName *string
	TechName    *string
	RoleType    string
	GroupCount  int64
	UserCount   int64
}

func (q *Queries) GetAllRolesPaginated(ctx context.Context, arg GetAllRolesPaginatedParams) ([]GetAllRolesPaginatedRow, error) {
	rows, err := q.db.Query(ctx, getAllRolesPaginated, arg.OffsetValue, arg.LimitValue)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllRolesPaginatedRow
	for rows.Next() {
		var i GetAllRolesPaginatedRow
		if err := rows.Scan(
			&i.RoleID,
			&i.RoleName,
			&i.IsActive,
			&i.ProductID,
			&i.ProductIid,
			&i.ProductName,
			&i.TechName,
			&i.RoleType,
			&i.GroupCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllRolesTotalCount = `-- name: GetAllRolesTotalCount :one
SELECT COUNT(*)
FROM roles
`

func (q *Queries) GetAllRolesTotalCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, getAllRolesTotalCount)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getAllWithProductIDByUserID = `-- name: GetAllWithProductIDByUserID :many
WITH user_direct_roles AS (
    SELECT ur.role_id, NULL::integer as product_id
    FROM users_roles ur
    WHERE ur.user_id = $1
),
participant_roles_with_products AS (
    SELECT pr.role_id, p.product_id
    FROM participants_roles pr
    INNER JOIN participants p ON pr.participant_id = p.id
    WHERE p.user_id = $1
),
all_user_roles AS (
    SELECT role_id, product_id FROM user_direct_roles
    UNION
    SELECT role_id, product_id FROM participant_roles_with_products
)
SELECT r.id,
       r.name,
       aur.product_id,
       r.is_system,
       r.active_flg
FROM roles r
INNER JOIN all_user_roles aur ON r.id = aur.role_id
ORDER BY r.id, aur.product_id NULLS FIRST
`

type GetAllWithProductIDByUserIDRow struct {
	ID        int64
	Name      string
	ProductID *int64
	IsSystem  bool
	ActiveFlg bool
}

func (q *Queries) GetAllWithProductIDByUserID(ctx context.Context, userID int64) ([]GetAllWithProductIDByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getAllWithProductIDByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllWithProductIDByUserIDRow
	for rows.Next() {
		var i GetAllWithProductIDByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOwnerRole = `-- name: GetOwnerRole :one
SELECT id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
FROM roles
WHERE name = 'product_owner'
LIMIT 1
`

func (q *Queries) GetOwnerRole(ctx context.Context) (Role, error) {
	row := q.db.QueryRow(ctx, getOwnerRole)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.IsProtected,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getRoleByID = `-- name: GetRoleByID :one
SELECT id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
FROM roles
WHERE id = $1
`

func (q *Queries) GetRoleByID(ctx context.Context, id int64) (Role, error) {
	row := q.db.QueryRow(ctx, getRoleByID, id)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.IsProtected,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getRoleByIDAndProductID = `-- name: GetRoleByIDAndProductID :one
SELECT id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
FROM roles
WHERE id = $1
  AND product_id = $2
`

type GetRoleByIDAndProductIDParams struct {
	ID        int64
	ProductID *int64
}

func (q *Queries) GetRoleByIDAndProductID(ctx context.Context, arg GetRoleByIDAndProductIDParams) (Role, error) {
	row := q.db.QueryRow(ctx, getRoleByIDAndProductID, arg.ID, arg.ProductID)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.IsProtected,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getRoleCategoryStates = `-- name: GetRoleCategoryStates :many
SELECT r.id                                  AS role_id,
       c.id                                  AS category_id,
       c.name                                AS category_name,
       (cr.category_id IS NOT NULL)::boolean AS is_active
FROM roles r
         CROSS JOIN categories c
         LEFT JOIN categories_roles cr ON
    cr.role_id = r.id AND
    cr.category_id = c.id
WHERE r.is_system = true
  AND r.active_flg = true
ORDER BY r.id, c.id
`

type GetRoleCategoryStatesRow struct {
	RoleID       int64
	CategoryID   int64
	CategoryName string
	IsActive     bool
}

func (q *Queries) GetRoleCategoryStates(ctx context.Context) ([]GetRoleCategoryStatesRow, error) {
	rows, err := q.db.Query(ctx, getRoleCategoryStates)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRoleCategoryStatesRow
	for rows.Next() {
		var i GetRoleCategoryStatesRow
		if err := rows.Scan(
			&i.RoleID,
			&i.CategoryID,
			&i.CategoryName,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRoleWithProductByParticipantID = `-- name: GetRoleWithProductByParticipantID :many
WITH role_data AS (
    SELECT
        r.id,
        r.name,
        COALESCE(p.product_id, r.product_id) AS product_id,
        r.is_system,
        r.active_flg,
        r.created_at,
        r.updated_at,
        r.deleted_at,
        pr.participant_id
    FROM roles r
             INNER JOIN participants_roles pr ON pr.role_id = r.id
             LEFT JOIN participants p ON p.id = pr.participant_id
    WHERE pr.participant_id  = $1
)
SELECT
    rd.id,
    rd.name,
    rd.participant_id,
    rd.product_id,
    rd.is_system,
    rd.active_flg,
    rd.created_at,
    rd.updated_at,
    rd.deleted_at
FROM role_data rd
`

type GetRoleWithProductByParticipantIDRow struct {
	ID            int64
	Name          string
	ParticipantID int64
	ProductID     int64
	IsSystem      bool
	ActiveFlg     bool
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

func (q *Queries) GetRoleWithProductByParticipantID(ctx context.Context, participantID int64) ([]GetRoleWithProductByParticipantIDRow, error) {
	rows, err := q.db.Query(ctx, getRoleWithProductByParticipantID, participantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRoleWithProductByParticipantIDRow
	for rows.Next() {
		var i GetRoleWithProductByParticipantIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ParticipantID,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolesByProductID = `-- name: GetRolesByProductID :many
SELECT id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
FROM roles
WHERE product_id = $1
`

func (q *Queries) GetRolesByProductID(ctx context.Context, productID *int64) ([]Role, error) {
	rows, err := q.db.Query(ctx, getRolesByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Role
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.IsProtected,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolesBySystemType = `-- name: GetRolesBySystemType :many
SELECT id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
FROM roles
WHERE is_system = $1
`

func (q *Queries) GetRolesBySystemType(ctx context.Context, isSystem bool) ([]Role, error) {
	rows, err := q.db.Query(ctx, getRolesBySystemType, isSystem)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Role
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.IsProtected,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolesShorts = `-- name: GetRolesShorts :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       CASE
           WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                  AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(pr.participant_id) AS participant_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN participants_roles pr ON r.id = pr.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
WHERE r.active_flg = true
  AND r.product_id = $1
GROUP BY r.id, r.name
ORDER BY r.id, r.name
`

type GetRolesShortsRow struct {
	RoleID           int64
	RoleName         string
	RoleType         string
	GroupCount       int64
	ParticipantCount int64
	UserCount        int64
}

func (q *Queries) GetRolesShorts(ctx context.Context, productID *int64) ([]GetRolesShortsRow, error) {
	rows, err := q.db.Query(ctx, getRolesShorts, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRolesShortsRow
	for rows.Next() {
		var i GetRolesShortsRow
		if err := rows.Scan(
			&i.RoleID,
			&i.RoleName,
			&i.RoleType,
			&i.GroupCount,
			&i.ParticipantCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolesShortsAsAdminByProductID = `-- name: GetRolesShortsAsAdminByProductID :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       r.active_flg             AS is_active,
       CASE
           WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                  AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(pr.participant_id) AS participant_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN participants_roles pr ON r.id = pr.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
WHERE r.product_id = $1 AND r.active_flg = $2
GROUP BY r.id, r.name
ORDER BY r.id, r.name
`

type GetRolesShortsAsAdminByProductIDParams struct {
	ProductID *int64
	ActiveFlg bool
}

type GetRolesShortsAsAdminByProductIDRow struct {
	RoleID           int64
	RoleName         string
	IsActive         bool
	RoleType         string
	GroupCount       int64
	ParticipantCount int64
	UserCount        int64
}

func (q *Queries) GetRolesShortsAsAdminByProductID(ctx context.Context, arg GetRolesShortsAsAdminByProductIDParams) ([]GetRolesShortsAsAdminByProductIDRow, error) {
	rows, err := q.db.Query(ctx, getRolesShortsAsAdminByProductID, arg.ProductID, arg.ActiveFlg)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRolesShortsAsAdminByProductIDRow
	for rows.Next() {
		var i GetRolesShortsAsAdminByProductIDRow
		if err := rows.Scan(
			&i.RoleID,
			&i.RoleName,
			&i.IsActive,
			&i.RoleType,
			&i.GroupCount,
			&i.ParticipantCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolesWithProductByParticipantIDs = `-- name: GetRolesWithProductByParticipantIDs :many
WITH role_data AS (
    SELECT
        r.id,
        r.name,
        COALESCE(p.product_id, r.product_id) AS product_id,
        r.is_system,
        r.active_flg,
        r.created_at,
        r.updated_at,
        r.deleted_at,
        pr.participant_id
    FROM roles r
             INNER JOIN participants_roles pr ON pr.role_id = r.id
             LEFT JOIN participants p ON p.id = pr.participant_id
    WHERE pr.participant_id  = ANY ($1::BIGINT[])
)
SELECT
    rd.id,
    rd.name,
    rd.participant_id,
    rd.product_id,
    rd.is_system,
    rd.active_flg,
    rd.created_at,
    rd.updated_at,
    rd.deleted_at
FROM role_data rd
`

type GetRolesWithProductByParticipantIDsRow struct {
	ID            int64
	Name          string
	ParticipantID int64
	ProductID     int64
	IsSystem      bool
	ActiveFlg     bool
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

func (q *Queries) GetRolesWithProductByParticipantIDs(ctx context.Context, dollar_1 []int64) ([]GetRolesWithProductByParticipantIDsRow, error) {
	rows, err := q.db.Query(ctx, getRolesWithProductByParticipantIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRolesWithProductByParticipantIDsRow
	for rows.Next() {
		var i GetRolesWithProductByParticipantIDsRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ParticipantID,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSystemRolesWithStats = `-- name: GetSystemRolesWithStats :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       CASE
           WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                  AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(pr.participant_id) AS participant_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN participants_roles pr ON r.id = pr.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
WHERE r.is_system = true
  AND r.active_flg = true
GROUP BY r.id, r.name
ORDER BY r.id, r.name
`

type GetSystemRolesWithStatsRow struct {
	RoleID           int64
	RoleName         string
	RoleType         string
	GroupCount       int64
	ParticipantCount int64
	UserCount        int64
}

func (q *Queries) GetSystemRolesWithStats(ctx context.Context) ([]GetSystemRolesWithStatsRow, error) {
	rows, err := q.db.Query(ctx, getSystemRolesWithStats)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSystemRolesWithStatsRow
	for rows.Next() {
		var i GetSystemRolesWithStatsRow
		if err := rows.Scan(
			&i.RoleID,
			&i.RoleName,
			&i.RoleType,
			&i.GroupCount,
			&i.ParticipantCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const isRoleProtected = `-- name: IsRoleProtected :one
SELECT is_protected
FROM roles
WHERE id = $1
`

func (q *Queries) IsRoleProtected(ctx context.Context, id int64) (bool, error) {
	row := q.db.QueryRow(ctx, isRoleProtected, id)
	var is_protected bool
	err := row.Scan(&is_protected)
	return is_protected, err
}

const updateRole = `-- name: UpdateRole :one
UPDATE roles
SET name           = COALESCE($2::VARCHAR, name),
    product_id     = COALESCE($3::BIGINT, product_id),
    is_system = COALESCE($4::BOOLEAN, is_system),
    active_flg     = COALESCE($5::BOOLEAN, active_flg),
    updated_at     = NOW()
WHERE id = $1
RETURNING id, name, product_id, is_system, active_flg, is_protected, created_at, updated_at, deleted_at
`

type UpdateRoleParams struct {
	ID        int64
	Name      *string
	ProductID *int64
	IsSystem  *bool
	ActiveFlg *bool
}

func (q *Queries) UpdateRole(ctx context.Context, arg UpdateRoleParams) (Role, error) {
	row := q.db.QueryRow(ctx, updateRole,
		arg.ID,
		arg.Name,
		arg.ProductID,
		arg.IsSystem,
		arg.ActiveFlg,
	)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.IsProtected,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
