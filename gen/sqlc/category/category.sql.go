// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: category.sql

package category

import (
	"context"
)

const createCategory = `-- name: CreateCategory :one
INSERT INTO categories (name)
VALUES ($1)
ON CONFLICT (name) DO NOTHING
RETURNING id, name
`

func (q *Queries) CreateCategory(ctx context.Context, name string) (Category, error) {
	row := q.db.QueryRow(ctx, createCategory, name)
	var i Category
	err := row.Scan(&i.ID, &i.Name)
	return i, err
}

const deleteCategory = `-- name: DeleteCategory :exec
DELETE
FROM categories
WHERE id = $1
`

func (q *Queries) DeleteCategory(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteCategory, id)
	return err
}

const getAllCategories = `-- name: GetAllCategories :many
SELECT id, name
FROM categories
`

func (q *Queries) GetAllCategories(ctx context.Context) ([]Category, error) {
	rows, err := q.db.Query(ctx, getAllCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Category
	for rows.Next() {
		var i Category
		if err := rows.Scan(&i.ID, &i.Name); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCategoryByID = `-- name: GetCategoryByID :one
SELECT id, name
FROM categories
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetCategoryByID(ctx context.Context, id int64) (Category, error) {
	row := q.db.QueryRow(ctx, getCategoryByID, id)
	var i Category
	err := row.Scan(&i.ID, &i.Name)
	return i, err
}

const getCategoryByName = `-- name: GetCategoryByName :one
SELECT id, name
FROM categories
WHERE name = $1
LIMIT 1
`

func (q *Queries) GetCategoryByName(ctx context.Context, name string) (Category, error) {
	row := q.db.QueryRow(ctx, getCategoryByName, name)
	var i Category
	err := row.Scan(&i.ID, &i.Name)
	return i, err
}

const updateCategory = `-- name: UpdateCategory :one
UPDATE categories
SET name = $2
WHERE id = $1
RETURNING id, name
`

type UpdateCategoryParams struct {
	ID   int64
	Name string
}

func (q *Queries) UpdateCategory(ctx context.Context, arg UpdateCategoryParams) (Category, error) {
	row := q.db.QueryRow(ctx, updateCategory, arg.ID, arg.Name)
	var i Category
	err := row.Scan(&i.ID, &i.Name)
	return i, err
}
