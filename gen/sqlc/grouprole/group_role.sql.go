// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: group_role.sql

package grouprole

import (
	"context"
)

const createGroupRole = `-- name: CreateGroupRole :one
INSERT INTO groups_roles (group_id,
                          role_id)
VALUES ($1, $2)
ON CONFLICT (group_id, role_id) DO NOTHING
RETURNING id, group_id, role_id, created_at
`

type CreateGroupRoleParams struct {
	GroupID int64
	RoleID  int64
}

func (q *Queries) CreateGroupRole(ctx context.Context, arg CreateGroupRoleParams) (GroupsRole, error) {
	row := q.db.QueryRow(ctx, createGroupRole, arg.GroupID, arg.RoleID)
	var i GroupsRole
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const createGroupRolesByRoleIDAndGroupIDs = `-- name: CreateGroupRolesByRoleIDAndGroupIDs :exec
INSERT INTO groups_roles (group_id, role_id)
SELECT unnest($1::BIGINT[]), $2::BIGINT
ON CONFLICT (group_id, role_id) DO NOTHING
`

type CreateGroupRolesByRoleIDAndGroupIDsParams struct {
	GroupIds []int64
	RoleID   int64
}

func (q *Queries) CreateGroupRolesByRoleIDAndGroupIDs(ctx context.Context, arg CreateGroupRolesByRoleIDAndGroupIDsParams) error {
	_, err := q.db.Exec(ctx, createGroupRolesByRoleIDAndGroupIDs, arg.GroupIds, arg.RoleID)
	return err
}

const createRoleGroupsByGroupIDAndRoleIDs = `-- name: CreateRoleGroupsByGroupIDAndRoleIDs :exec
INSERT INTO groups_roles (role_id, group_id)
SELECT unnest($1::BIGINT[]), $2::BIGINT
    ON CONFLICT (group_id, role_id) DO NOTHING
`

type CreateRoleGroupsByGroupIDAndRoleIDsParams struct {
	RoleIds []int64
	GroupID int64
}

func (q *Queries) CreateRoleGroupsByGroupIDAndRoleIDs(ctx context.Context, arg CreateRoleGroupsByGroupIDAndRoleIDsParams) error {
	_, err := q.db.Exec(ctx, createRoleGroupsByGroupIDAndRoleIDs, arg.RoleIds, arg.GroupID)
	return err
}

const deleteGroupRole = `-- name: DeleteGroupRole :exec
DELETE
FROM groups_roles
WHERE id = $1
`

func (q *Queries) DeleteGroupRole(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteGroupRole, id)
	return err
}

const deleteGroupRoleByGroupRoleID = `-- name: DeleteGroupRoleByGroupRoleID :exec
DELETE
FROM groups_roles
WHERE group_id = $1
  AND role_id = $2
`

type DeleteGroupRoleByGroupRoleIDParams struct {
	GroupID int64
	RoleID  int64
}

func (q *Queries) DeleteGroupRoleByGroupRoleID(ctx context.Context, arg DeleteGroupRoleByGroupRoleIDParams) error {
	_, err := q.db.Exec(ctx, deleteGroupRoleByGroupRoleID, arg.GroupID, arg.RoleID)
	return err
}

const deleteGroupRolesByGroupID = `-- name: DeleteGroupRolesByGroupID :exec
DELETE
FROM groups_roles
WHERE group_id = $1
`

func (q *Queries) DeleteGroupRolesByGroupID(ctx context.Context, groupID int64) error {
	_, err := q.db.Exec(ctx, deleteGroupRolesByGroupID, groupID)
	return err
}

const deleteGroupRolesByGroupIDsAndRoleID = `-- name: DeleteGroupRolesByGroupIDsAndRoleID :exec
DELETE
FROM groups_roles
WHERE role_id = $1::BIGINT
  AND group_id = ANY ($2::BIGINT[])
`

type DeleteGroupRolesByGroupIDsAndRoleIDParams struct {
	RoleID   int64
	GroupIds []int64
}

func (q *Queries) DeleteGroupRolesByGroupIDsAndRoleID(ctx context.Context, arg DeleteGroupRolesByGroupIDsAndRoleIDParams) error {
	_, err := q.db.Exec(ctx, deleteGroupRolesByGroupIDsAndRoleID, arg.RoleID, arg.GroupIds)
	return err
}

const deleteGroupRolesByRoleIDs = `-- name: DeleteGroupRolesByRoleIDs :exec
DELETE
From groups_roles
WHERE role_id = ANY ($1::BIGINT[])
`

func (q *Queries) DeleteGroupRolesByRoleIDs(ctx context.Context, dollar_1 []int64) error {
	_, err := q.db.Exec(ctx, deleteGroupRolesByRoleIDs, dollar_1)
	return err
}

const deleteRoleGroupsByRoleIDsAndGroupID = `-- name: DeleteRoleGroupsByRoleIDsAndGroupID :exec
DELETE
FROM groups_roles
WHERE group_id = $1::BIGINT
  AND role_id = ANY ($2::BIGINT[])
`

type DeleteRoleGroupsByRoleIDsAndGroupIDParams struct {
	GroupID int64
	RoleIds []int64
}

func (q *Queries) DeleteRoleGroupsByRoleIDsAndGroupID(ctx context.Context, arg DeleteRoleGroupsByRoleIDsAndGroupIDParams) error {
	_, err := q.db.Exec(ctx, deleteRoleGroupsByRoleIDsAndGroupID, arg.GroupID, arg.RoleIds)
	return err
}

const getAllGroupRoles = `-- name: GetAllGroupRoles :many
SELECT id, group_id, role_id, created_at
FROM groups_roles
`

func (q *Queries) GetAllGroupRoles(ctx context.Context) ([]GroupsRole, error) {
	rows, err := q.db.Query(ctx, getAllGroupRoles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GroupsRole
	for rows.Next() {
		var i GroupsRole
		if err := rows.Scan(
			&i.ID,
			&i.GroupID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getGroupRoleByGroupRoleID = `-- name: GetGroupRoleByGroupRoleID :one
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE group_id = $1
  AND role_id = $2
`

type GetGroupRoleByGroupRoleIDParams struct {
	GroupID int64
	RoleID  int64
}

func (q *Queries) GetGroupRoleByGroupRoleID(ctx context.Context, arg GetGroupRoleByGroupRoleIDParams) (GroupsRole, error) {
	row := q.db.QueryRow(ctx, getGroupRoleByGroupRoleID, arg.GroupID, arg.RoleID)
	var i GroupsRole
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const getGroupRoleByID = `-- name: GetGroupRoleByID :one
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE id = $1
`

func (q *Queries) GetGroupRoleByID(ctx context.Context, id int64) (GroupsRole, error) {
	row := q.db.QueryRow(ctx, getGroupRoleByID, id)
	var i GroupsRole
	err := row.Scan(
		&i.ID,
		&i.GroupID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const getGroupRolesByGroupID = `-- name: GetGroupRolesByGroupID :many
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE group_id = $1
`

func (q *Queries) GetGroupRolesByGroupID(ctx context.Context, groupID int64) ([]GroupsRole, error) {
	rows, err := q.db.Query(ctx, getGroupRolesByGroupID, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GroupsRole
	for rows.Next() {
		var i GroupsRole
		if err := rows.Scan(
			&i.ID,
			&i.GroupID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getGroupRolesByRoleID = `-- name: GetGroupRolesByRoleID :many
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE role_id = $1
`

func (q *Queries) GetGroupRolesByRoleID(ctx context.Context, roleID int64) ([]GroupsRole, error) {
	rows, err := q.db.Query(ctx, getGroupRolesByRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GroupsRole
	for rows.Next() {
		var i GroupsRole
		if err := rows.Scan(
			&i.ID,
			&i.GroupID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
