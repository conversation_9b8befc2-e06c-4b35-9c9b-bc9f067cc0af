// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: role_permission.sql

package rolepermission

import (
	"context"
)

const createRolePermission = `-- name: CreateRolePermission :one
INSERT INTO roles_permissions (role_id,
                               permission_id)
VALUES ($1, $2)
ON CONFLICT (role_id, permission_id) DO NOTHING
RETURNING id, role_id, permission_id, created_at
`

type CreateRolePermissionParams struct {
	RoleID       int64
	PermissionID int64
}

func (q *Queries) CreateRolePermission(ctx context.Context, arg CreateRolePermissionParams) (RolesPermission, error) {
	row := q.db.QueryRow(ctx, createRolePermission, arg.RoleID, arg.PermissionID)
	var i RolesPermission
	err := row.Scan(
		&i.ID,
		&i.RoleID,
		&i.PermissionID,
		&i.CreatedAt,
	)
	return i, err
}

const createRolePermissionsByRoleIDAndPermissionIDs = `-- name: CreateRolePermissionsByRoleIDAndPermissionIDs :exec
INSERT INTO roles_permissions (role_id, permission_id)
SELECT $1::BIGINT, unnest($2::BIGINT[])
ON CONFLICT (role_id, permission_id) DO NOTHING
`

type CreateRolePermissionsByRoleIDAndPermissionIDsParams struct {
	RoleID        int64
	PermissionIds []int64
}

func (q *Queries) CreateRolePermissionsByRoleIDAndPermissionIDs(ctx context.Context, arg CreateRolePermissionsByRoleIDAndPermissionIDsParams) error {
	_, err := q.db.Exec(ctx, createRolePermissionsByRoleIDAndPermissionIDs, arg.RoleID, arg.PermissionIds)
	return err
}

const deleteByID = `-- name: DeleteByID :exec
DELETE
FROM roles_permissions
WHERE id = $1
`

func (q *Queries) DeleteByID(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteByID, id)
	return err
}

const deleteByPermissionID = `-- name: DeleteByPermissionID :exec
DELETE
FROM roles_permissions
WHERE permission_id = $1
`

func (q *Queries) DeleteByPermissionID(ctx context.Context, permissionID int64) error {
	_, err := q.db.Exec(ctx, deleteByPermissionID, permissionID)
	return err
}

const deleteByRoleID = `-- name: DeleteByRoleID :exec
DELETE
FROM roles_permissions
WHERE role_id = $1
`

func (q *Queries) DeleteByRoleID(ctx context.Context, roleID int64) error {
	_, err := q.db.Exec(ctx, deleteByRoleID, roleID)
	return err
}

const deleteByRoleIDAndPermissionID = `-- name: DeleteByRoleIDAndPermissionID :exec
DELETE
FROM roles_permissions
WHERE role_id = $1
  AND permission_id = $2
`

type DeleteByRoleIDAndPermissionIDParams struct {
	RoleID       int64
	PermissionID int64
}

func (q *Queries) DeleteByRoleIDAndPermissionID(ctx context.Context, arg DeleteByRoleIDAndPermissionIDParams) error {
	_, err := q.db.Exec(ctx, deleteByRoleIDAndPermissionID, arg.RoleID, arg.PermissionID)
	return err
}

const deleteRolePermissionsByRoleIDAndPermissionIDs = `-- name: DeleteRolePermissionsByRoleIDAndPermissionIDs :exec
DELETE
FROM roles_permissions
WHERE role_id = $1::BIGINT
  AND permission_id = ANY($2::BIGINT[])
`

type DeleteRolePermissionsByRoleIDAndPermissionIDsParams struct {
	RoleID        int64
	PermissionIds []int64
}

func (q *Queries) DeleteRolePermissionsByRoleIDAndPermissionIDs(ctx context.Context, arg DeleteRolePermissionsByRoleIDAndPermissionIDsParams) error {
	_, err := q.db.Exec(ctx, deleteRolePermissionsByRoleIDAndPermissionIDs, arg.RoleID, arg.PermissionIds)
	return err
}

const getAllRolePermissions = `-- name: GetAllRolePermissions :many
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
`

func (q *Queries) GetAllRolePermissions(ctx context.Context) ([]RolesPermission, error) {
	rows, err := q.db.Query(ctx, getAllRolePermissions)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RolesPermission
	for rows.Next() {
		var i RolesPermission
		if err := rows.Scan(
			&i.ID,
			&i.RoleID,
			&i.PermissionID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolePermissionByID = `-- name: GetRolePermissionByID :one
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE id = $1
`

func (q *Queries) GetRolePermissionByID(ctx context.Context, id int64) (RolesPermission, error) {
	row := q.db.QueryRow(ctx, getRolePermissionByID, id)
	var i RolesPermission
	err := row.Scan(
		&i.ID,
		&i.RoleID,
		&i.PermissionID,
		&i.CreatedAt,
	)
	return i, err
}

const getRolePermissionByRolePermissionID = `-- name: GetRolePermissionByRolePermissionID :one
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE role_id = $1
  AND permission_id = $2
`

type GetRolePermissionByRolePermissionIDParams struct {
	RoleID       int64
	PermissionID int64
}

func (q *Queries) GetRolePermissionByRolePermissionID(ctx context.Context, arg GetRolePermissionByRolePermissionIDParams) (RolesPermission, error) {
	row := q.db.QueryRow(ctx, getRolePermissionByRolePermissionID, arg.RoleID, arg.PermissionID)
	var i RolesPermission
	err := row.Scan(
		&i.ID,
		&i.RoleID,
		&i.PermissionID,
		&i.CreatedAt,
	)
	return i, err
}

const getRolePermissionsByPermissionID = `-- name: GetRolePermissionsByPermissionID :many
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE permission_id = $1
`

func (q *Queries) GetRolePermissionsByPermissionID(ctx context.Context, permissionID int64) ([]RolesPermission, error) {
	rows, err := q.db.Query(ctx, getRolePermissionsByPermissionID, permissionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RolesPermission
	for rows.Next() {
		var i RolesPermission
		if err := rows.Scan(
			&i.ID,
			&i.RoleID,
			&i.PermissionID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRolePermissionsByRoleID = `-- name: GetRolePermissionsByRoleID :many
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE role_id = $1
`

func (q *Queries) GetRolePermissionsByRoleID(ctx context.Context, roleID int64) ([]RolesPermission, error) {
	rows, err := q.db.Query(ctx, getRolePermissionsByRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RolesPermission
	for rows.Next() {
		var i RolesPermission
		if err := rows.Scan(
			&i.ID,
			&i.RoleID,
			&i.PermissionID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
