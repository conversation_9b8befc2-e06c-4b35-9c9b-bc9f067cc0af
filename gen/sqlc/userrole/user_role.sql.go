// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user_role.sql

package userrole

import (
	"context"
)

const createUserRole = `-- name: CreateUserRole :one
INSERT INTO users_roles (user_id, role_id)
VALUES ($1, $2)
ON CONFLICT (user_id, role_id) DO NOTHING
RETURNING id, user_id, role_id, created_at
`

type CreateUserRoleParams struct {
	UserID int64
	RoleID int64
}

func (q *Queries) CreateUserRole(ctx context.Context, arg CreateUserRoleParams) (UsersRole, error) {
	row := q.db.QueryRow(ctx, createUserRole, arg.UserID, arg.RoleID)
	var i UsersRole
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const createUserRolesByRoleIDAndUserIDs = `-- name: CreateUserRolesByRoleIDAndUserIDs :exec
INSERT INTO users_roles (user_id, role_id)
SELECT unnest($1::BIGINT[]), $2
ON CONFLICT (user_id, role_id) DO NOTHING
`

type CreateUserRolesByRoleIDAndUserIDsParams struct {
	UserIds []int64
	RoleID  int64
}

func (q *Queries) CreateUserRolesByRoleIDAndUserIDs(ctx context.Context, arg CreateUserRolesByRoleIDAndUserIDsParams) error {
	_, err := q.db.Exec(ctx, createUserRolesByRoleIDAndUserIDs, arg.UserIds, arg.RoleID)
	return err
}

const createUserRolesByUserIDAndRoleIDs = `-- name: CreateUserRolesByUserIDAndRoleIDs :exec
INSERT INTO users_roles (user_id, role_id)
SELECT $1, unnest($2::BIGINT[])
ON CONFLICT (user_id, role_id) DO NOTHING
`

type CreateUserRolesByUserIDAndRoleIDsParams struct {
	UserID  int64
	RoleIds []int64
}

func (q *Queries) CreateUserRolesByUserIDAndRoleIDs(ctx context.Context, arg CreateUserRolesByUserIDAndRoleIDsParams) error {
	_, err := q.db.Exec(ctx, createUserRolesByUserIDAndRoleIDs, arg.UserID, arg.RoleIds)
	return err
}

const deleteUserRoleByUserIDAndRoleID = `-- name: DeleteUserRoleByUserIDAndRoleID :exec
DELETE
FROM users_roles
WHERE user_id = $1
  AND role_id = $2
`

type DeleteUserRoleByUserIDAndRoleIDParams struct {
	UserID int64
	RoleID int64
}

func (q *Queries) DeleteUserRoleByUserIDAndRoleID(ctx context.Context, arg DeleteUserRoleByUserIDAndRoleIDParams) error {
	_, err := q.db.Exec(ctx, deleteUserRoleByUserIDAndRoleID, arg.UserID, arg.RoleID)
	return err
}

const deleteUserRolesByRoleID = `-- name: DeleteUserRolesByRoleID :exec
DELETE
FROM users_roles
WHERE role_id = $1
`

func (q *Queries) DeleteUserRolesByRoleID(ctx context.Context, roleID int64) error {
	_, err := q.db.Exec(ctx, deleteUserRolesByRoleID, roleID)
	return err
}

const deleteUserRolesByRoleIDAndUserIDs = `-- name: DeleteUserRolesByRoleIDAndUserIDs :exec
DELETE
FROM users_roles
WHERE role_id = $1
  AND user_id = ANY ($2::BIGINT[])
`

type DeleteUserRolesByRoleIDAndUserIDsParams struct {
	RoleID  int64
	UserIds []int64
}

func (q *Queries) DeleteUserRolesByRoleIDAndUserIDs(ctx context.Context, arg DeleteUserRolesByRoleIDAndUserIDsParams) error {
	_, err := q.db.Exec(ctx, deleteUserRolesByRoleIDAndUserIDs, arg.RoleID, arg.UserIds)
	return err
}

const deleteUserRolesByUserID = `-- name: DeleteUserRolesByUserID :exec
DELETE
FROM users_roles
WHERE user_id = $1
`

func (q *Queries) DeleteUserRolesByUserID(ctx context.Context, userID int64) error {
	_, err := q.db.Exec(ctx, deleteUserRolesByUserID, userID)
	return err
}

const deleteUserRolesByUserIDAndRoleIDs = `-- name: DeleteUserRolesByUserIDAndRoleIDs :exec
DELETE
FROM users_roles
WHERE user_id = $1
  AND role_id = ANY ($2::BIGINT[])
`

type DeleteUserRolesByUserIDAndRoleIDsParams struct {
	UserID  int64
	RoleIds []int64
}

func (q *Queries) DeleteUserRolesByUserIDAndRoleIDs(ctx context.Context, arg DeleteUserRolesByUserIDAndRoleIDsParams) error {
	_, err := q.db.Exec(ctx, deleteUserRolesByUserIDAndRoleIDs, arg.UserID, arg.RoleIds)
	return err
}

const getAllUserRoles = `-- name: GetAllUserRoles :many
SELECT id, user_id, role_id, created_at
FROM users_roles
`

func (q *Queries) GetAllUserRoles(ctx context.Context) ([]UsersRole, error) {
	rows, err := q.db.Query(ctx, getAllUserRoles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []UsersRole
	for rows.Next() {
		var i UsersRole
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserRoleByID = `-- name: GetUserRoleByID :one
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetUserRoleByID(ctx context.Context, id int64) (UsersRole, error) {
	row := q.db.QueryRow(ctx, getUserRoleByID, id)
	var i UsersRole
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const getUserRoleByUserIDRoleID = `-- name: GetUserRoleByUserIDRoleID :one
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE user_id = $1
  AND role_id = $2
LIMIT 1
`

type GetUserRoleByUserIDRoleIDParams struct {
	UserID int64
	RoleID int64
}

func (q *Queries) GetUserRoleByUserIDRoleID(ctx context.Context, arg GetUserRoleByUserIDRoleIDParams) (UsersRole, error) {
	row := q.db.QueryRow(ctx, getUserRoleByUserIDRoleID, arg.UserID, arg.RoleID)
	var i UsersRole
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const getUserRoleIDs = `-- name: GetUserRoleIDs :many
SELECT role_id
FROM users_roles
WHERE user_id = $1
`

func (q *Queries) GetUserRoleIDs(ctx context.Context, userID int64) ([]int64, error) {
	rows, err := q.db.Query(ctx, getUserRoleIDs, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var role_id int64
		if err := rows.Scan(&role_id); err != nil {
			return nil, err
		}
		items = append(items, role_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserRolesByRoleID = `-- name: GetUserRolesByRoleID :many
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE role_id = $1
`

func (q *Queries) GetUserRolesByRoleID(ctx context.Context, roleID int64) ([]UsersRole, error) {
	rows, err := q.db.Query(ctx, getUserRolesByRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []UsersRole
	for rows.Next() {
		var i UsersRole
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserRolesByUserID = `-- name: GetUserRolesByUserID :many
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE user_id = $1
`

func (q *Queries) GetUserRolesByUserID(ctx context.Context, userID int64) ([]UsersRole, error) {
	rows, err := q.db.Query(ctx, getUserRolesByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []UsersRole
	for rows.Next() {
		var i UsersRole
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateUserRole = `-- name: UpdateUserRole :one
UPDATE users_roles
SET user_id = COALESCE($2, user_id),
    role_id = COALESCE($3, role_id)
WHERE id = $1
RETURNING id, user_id, role_id, created_at
`

type UpdateUserRoleParams struct {
	ID     int64
	UserID int64
	RoleID int64
}

func (q *Queries) UpdateUserRole(ctx context.Context, arg UpdateUserRoleParams) (UsersRole, error) {
	row := q.db.QueryRow(ctx, updateUserRole, arg.ID, arg.UserID, arg.RoleID)
	var i UsersRole
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}
