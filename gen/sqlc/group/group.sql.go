// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: group.sql

package group

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const countGroups = `-- name: CountGroups :one
SELECT COUNT(*)
FROM groups
WHERE 
    name ILIKE ('%' || $1::TEXT || '%') AND
    ($2::BOOLEAN IS NULL OR is_system = $2) AND
    ($3::bigint[] IS NULL OR product_id = ANY ($3)) AND
     ($4::BOOLEAN IS NULL OR active_flg = $4)
`

type CountGroupsParams struct {
	Search     string
	IsSystem   *bool
	ProductIds []int64
	Status     *bool
}

func (q *Queries) CountGroups(ctx context.Context, arg CountGroupsParams) (int64, error) {
	row := q.db.QueryRow(ctx, countGroups,
		arg.Search,
		arg.IsSystem,
		arg.ProductIds,
		arg.Status,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createGroup = `-- name: CreateGroup :one
INSERT INTO groups (name,
                    product_id,
                    is_system,
                    active_flg)
VALUES ($1,
        $2,
        $3,
        true)
RETURNING id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
`

type CreateGroupParams struct {
	Name      string
	ProductID *int64
	IsSystem  bool
}

func (q *Queries) CreateGroup(ctx context.Context, arg CreateGroupParams) (Group, error) {
	row := q.db.QueryRow(ctx, createGroup, arg.Name, arg.ProductID, arg.IsSystem)
	var i Group
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deactivateByIDs = `-- name: DeactivateByIDs :exec
UPDATE groups SET
active_flg = false
WHERE id =  ANY ($1::bigint[])
`

func (q *Queries) DeactivateByIDs(ctx context.Context, groupsIds []int64) error {
	_, err := q.db.Exec(ctx, deactivateByIDs, groupsIds)
	return err
}

const deleteByID = `-- name: DeleteByID :exec
UPDATE groups SET
active_flg = false
WHERE id = $1::BIGINT
RETURNING id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
`

func (q *Queries) DeleteByID(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteByID, id)
	return err
}

const existByName = `-- name: ExistByName :one
SELECT COUNT(*) > 0 AS exists
FROM groups
WHERE name = $1
`

func (q *Queries) ExistByName(ctx context.Context, name string) (bool, error) {
	row := q.db.QueryRow(ctx, existByName, name)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const getAllByUserID = `-- name: GetAllByUserID :many
WITH user_groups AS (SELECT ug.group_id
                     FROM users_groups ug
                     WHERE ug.user_id = $1),
     user_participants AS (SELECT id AS participant_id
                           FROM participants
                           WHERE user_id = $1),
     participant_groups AS (SELECT pg.group_id
                            FROM participants_groups pg
                            INNER JOIN user_participants up ON pg.participant_id = up.participant_id),
     all_group_ids AS (
         SELECT group_id
         FROM user_groups
         UNION
         SELECT group_id
         FROM participant_groups
     )
SELECT g.id,
       g.name,
       g.product_id,
       g.is_system,
       g.active_flg,
       g.created_at,
       g.updated_at,
       g.deleted_at
FROM groups g
         INNER JOIN all_group_ids agi ON g.id = agi.group_id
`

func (q *Queries) GetAllByUserID(ctx context.Context, userID int64) ([]Group, error) {
	rows, err := q.db.Query(ctx, getAllByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllGroups = `-- name: GetAllGroups :many
SELECT id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
FROM groups
ORDER BY updated_at DESC
`

func (q *Queries) GetAllGroups(ctx context.Context) ([]Group, error) {
	rows, err := q.db.Query(ctx, getAllGroups)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllWithProductIDByUserID = `-- name: GetAllWithProductIDByUserID :many
WITH user_direct_groups AS (
    SELECT ug.group_id, NULL::integer as product_id
    FROM users_groups ug
    WHERE ug.user_id = $1
),
participant_groups_with_products AS (
    SELECT pg.group_id, p.product_id
    FROM participants_groups pg
    INNER JOIN participants p ON pg.participant_id = p.id
    WHERE p.user_id = $1
),
all_user_groups AS (
    SELECT group_id, product_id FROM user_direct_groups
    UNION
    SELECT group_id, product_id FROM participant_groups_with_products
)
SELECT g.id,
       g.name,
       aug.product_id,
       g.is_system,
       g.active_flg,
       g.created_at,
       g.updated_at,
       g.deleted_at
FROM groups g
INNER JOIN all_user_groups aug ON g.id = aug.group_id
ORDER BY g.id, aug.product_id NULLS FIRST
`

type GetAllWithProductIDByUserIDRow struct {
	ID        int64
	Name      string
	ProductID *int64
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

func (q *Queries) GetAllWithProductIDByUserID(ctx context.Context, userID int64) ([]GetAllWithProductIDByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getAllWithProductIDByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllWithProductIDByUserIDRow
	for rows.Next() {
		var i GetAllWithProductIDByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByGroupIDAndProductID = `-- name: GetByGroupIDAndProductID :one
SELECT id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
FROM groups
WHERE id = $1
  AND product_id = $2
ORDER BY updated_at DESC
`

type GetByGroupIDAndProductIDParams struct {
	ID        int64
	ProductID *int64
}

func (q *Queries) GetByGroupIDAndProductID(ctx context.Context, arg GetByGroupIDAndProductIDParams) (Group, error) {
	row := q.db.QueryRow(ctx, getByGroupIDAndProductID, arg.ID, arg.ProductID)
	var i Group
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getByProductID = `-- name: GetByProductID :many
SELECT id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
FROM groups
WHERE product_id = $1
ORDER BY updated_at DESC
`

func (q *Queries) GetByProductID(ctx context.Context, productID *int64) ([]Group, error) {
	rows, err := q.db.Query(ctx, getByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getBySystemType = `-- name: GetBySystemType :many
SELECT id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
FROM groups
WHERE is_system = $1
  AND active_flg = true
ORDER BY updated_at DESC
`

func (q *Queries) GetBySystemType(ctx context.Context, isSystem bool) ([]Group, error) {
	rows, err := q.db.Query(ctx, getBySystemType, isSystem)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getGroup = `-- name: GetGroup :one
SELECT id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
FROM groups
WHERE id = $1
ORDER BY updated_at DESC
`

func (q *Queries) GetGroup(ctx context.Context, id int64) (Group, error) {
	row := q.db.QueryRow(ctx, getGroup, id)
	var i Group
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getGroupCategoryStates = `-- name: GetGroupCategoryStates :many
SELECT g.id                                  AS group_id,
       c.id                                  AS category_id,
       c.name                                AS category_name,
       (cg.category_id IS NOT NULL)::boolean AS is_active
FROM groups g
         CROSS JOIN categories c
         LEFT JOIN categories_groups cg ON
    cg.group_id = g.id AND
    cg.category_id = c.id
WHERE g.is_system = true
  AND g.active_flg = true
ORDER BY g.id, g.name
`

type GetGroupCategoryStatesRow struct {
	GroupID      int64
	CategoryID   int64
	CategoryName string
	IsActive     bool
}

func (q *Queries) GetGroupCategoryStates(ctx context.Context) ([]GetGroupCategoryStatesRow, error) {
	rows, err := q.db.Query(ctx, getGroupCategoryStates)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetGroupCategoryStatesRow
	for rows.Next() {
		var i GetGroupCategoryStatesRow
		if err := rows.Scan(
			&i.GroupID,
			&i.CategoryID,
			&i.CategoryName,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getGroupsShortsAsAdminByProductID = `-- name: GetGroupsShortsAsAdminByProductID :many
SELECT g.id AS group_id,
       g.name AS group_name,
       g.active_flg AS is_active,
       CASE
           WHEN g.is_system THEN 'system'
           ELSE 'custom'
       END AS group_type,
       COUNT(gr.role_id) AS role_count,
       COUNT(pg.participant_id) AS participant_count,
       COUNT(ug.user_id) AS user_count
FROM groups g
         LEFT JOIN groups_roles gr ON g.id = gr.group_id
         LEFT JOIN participants_groups pg ON g.id = pg.group_id
         LEFT JOIN users_groups ug ON g.id = ug.group_id
WHERE g.product_id = $1 AND g.active_flg = $2
GROUP BY g.id, g.name
ORDER BY g.id, g.name
`

type GetGroupsShortsAsAdminByProductIDParams struct {
	ProductID *int64
	ActiveFlg bool
}

type GetGroupsShortsAsAdminByProductIDRow struct {
	GroupID          int64
	GroupName        string
	IsActive         bool
	GroupType        string
	RoleCount        int64
	ParticipantCount int64
	UserCount        int64
}

func (q *Queries) GetGroupsShortsAsAdminByProductID(ctx context.Context, arg GetGroupsShortsAsAdminByProductIDParams) ([]GetGroupsShortsAsAdminByProductIDRow, error) {
	rows, err := q.db.Query(ctx, getGroupsShortsAsAdminByProductID, arg.ProductID, arg.ActiveFlg)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetGroupsShortsAsAdminByProductIDRow
	for rows.Next() {
		var i GetGroupsShortsAsAdminByProductIDRow
		if err := rows.Scan(
			&i.GroupID,
			&i.GroupName,
			&i.IsActive,
			&i.GroupType,
			&i.RoleCount,
			&i.ParticipantCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSystemGroups = `-- name: GetSystemGroups :many
SELECT id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
FROM groups g
WHERE g.is_system = true AND
      g.active_flg = true
GROUP BY g.id, g.name
ORDER BY g.id, g.name
`

func (q *Queries) GetSystemGroups(ctx context.Context) ([]Group, error) {
	rows, err := q.db.Query(ctx, getSystemGroups)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSystemGroupsWithStats = `-- name: GetSystemGroupsWithStats :many
SELECT g.id                     AS group_id,
       g.name                   AS group_name,
       g.is_system        AS is_system,
       COUNT(gr.role_id)        AS role_count,
       COUNT(pg.participant_id) AS participant_count,
       COUNT(ug.user_id)        AS user_count
FROM groups g
         LEFT JOIN groups_roles gr ON g.id = gr.group_id
         LEFT JOIN participants_groups pg ON g.id = pg.group_id
         LEFT JOIN users_groups ug ON g.id = ug.group_id
WHERE g.is_system = true
  AND g.active_flg = true
GROUP BY g.id, g.name
ORDER BY g.id, g.name
`

type GetSystemGroupsWithStatsRow struct {
	GroupID          int64
	GroupName        string
	IsSystem         bool
	RoleCount        int64
	ParticipantCount int64
	UserCount        int64
}

func (q *Queries) GetSystemGroupsWithStats(ctx context.Context) ([]GetSystemGroupsWithStatsRow, error) {
	rows, err := q.db.Query(ctx, getSystemGroupsWithStats)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSystemGroupsWithStatsRow
	for rows.Next() {
		var i GetSystemGroupsWithStatsRow
		if err := rows.Scan(
			&i.GroupID,
			&i.GroupName,
			&i.IsSystem,
			&i.RoleCount,
			&i.ParticipantCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWithStatsByUserID = `-- name: GetWithStatsByUserID :many
SELECT
    g.id AS group_id,
    g.name AS group_name,
    g.is_system AS is_system,
    COUNT(gr.role_id) AS role_count,
    COUNT(pg.participant_id) AS participant_count,
    COUNT(ug.user_id) AS user_count
FROM groups g
LEFT JOIN groups_roles gr ON g.id = gr.group_id
LEFT JOIN participants_groups pg ON g.id = pg.group_id
LEFT JOIN users_groups ug ON g.id = ug.group_id
WHERE g.is_system = true AND
      g.active_flg = true AND ug.user_id = $1
GROUP BY g.id, g.name
ORDER BY g.id, g.name
`

type GetWithStatsByUserIDRow struct {
	GroupID          int64
	GroupName        string
	IsSystem         bool
	RoleCount        int64
	ParticipantCount int64
	UserCount        int64
}

func (q *Queries) GetWithStatsByUserID(ctx context.Context, userID int64) ([]GetWithStatsByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getWithStatsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetWithStatsByUserIDRow
	for rows.Next() {
		var i GetWithStatsByUserIDRow
		if err := rows.Scan(
			&i.GroupID,
			&i.GroupName,
			&i.IsSystem,
			&i.RoleCount,
			&i.ParticipantCount,
			&i.UserCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listGroups = `-- name: ListGroups :many
SELECT 
    id, 
    name, 
    product_id, 
    is_system, 
    active_flg, 
    created_at, 
    updated_at, 
    deleted_at
FROM groups
WHERE 
    name ILIKE ('%' || $1::TEXT || '%') AND
    ($2::BOOLEAN IS NULL OR is_system = $2) AND
    ($3::bigint[] IS NULL OR product_id = ANY ($3)) AND
    ($4::BOOLEAN IS NULL OR active_flg = $4)
ORDER BY updated_at DESC
LIMIT $6::BIGINT OFFSET $5::BIGINT
`

type ListGroupsParams struct {
	Search     string
	IsSystem   *bool
	ProductIds []int64
	Status     *bool
	Offset     int64
	Limit      int64
}

func (q *Queries) ListGroups(ctx context.Context, arg ListGroupsParams) ([]Group, error) {
	rows, err := q.db.Query(ctx, listGroups,
		arg.Search,
		arg.IsSystem,
		arg.ProductIds,
		arg.Status,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateGroup = `-- name: UpdateGroup :one
UPDATE groups SET
    name = COALESCE($2::TEXT, name),
    product_id = COALESCE($3::BIGINT, product_id),
    is_system = COALESCE($4::BOOLEAN, is_system),
    active_flg = COALESCE($5::BOOLEAN, active_flg),
    deleted_at = COALESCE($6::TimeSTAMP, deleted_at),
    updated_at = NOW()
WHERE id = $1
RETURNING id, name, product_id, is_system, active_flg, created_at, updated_at, deleted_at
`

type UpdateGroupParams struct {
	ID        int64
	Name      *string
	ProductID *int64
	IsSystem  *bool
	ActiveFlg *bool
	DeletedAt pgtype.Timestamp
}

func (q *Queries) UpdateGroup(ctx context.Context, arg UpdateGroupParams) (Group, error) {
	row := q.db.QueryRow(ctx, updateGroup,
		arg.ID,
		arg.Name,
		arg.ProductID,
		arg.IsSystem,
		arg.ActiveFlg,
		arg.DeletedAt,
	)
	var i Group
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
