// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user_group.sql

package usergroup

import (
	"context"
	"time"
)

const assignUserToGroups = `-- name: AssignUserToGroups :exec
INSERT INTO users_groups (user_id, group_id)
SELECT $1, unnest($2::BIGINT[])
ON CONFLICT (user_id, group_id) DO NOTHING
`

type AssignUserToGroupsParams struct {
	UserID   int64
	GroupIds []int64
}

// @param group_ids BIGINT[]
func (q *Queries) AssignUserToGroups(ctx context.Context, arg AssignUserToGroupsParams) error {
	_, err := q.db.Exec(ctx, assignUserToGroups, arg.UserID, arg.GroupIds)
	return err
}

const createUserGroup = `-- name: CreateUserGroup :one
INSERT INTO users_groups (user_id, group_id)
VALUES ($1, $2)
ON CONFLICT (user_id, group_id) DO NOTHING
RETURNING id, user_id, group_id, created_at
`

type CreateUserGroupParams struct {
	UserID  int64
	GroupID int64
}

func (q *Queries) CreateUserGroup(ctx context.Context, arg CreateUserGroupParams) (UsersGroup, error) {
	row := q.db.QueryRow(ctx, createUserGroup, arg.UserID, arg.GroupID)
	var i UsersGroup
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.GroupID,
		&i.CreatedAt,
	)
	return i, err
}

const createUserGroupsByGroupIDAndUserIDs = `-- name: CreateUserGroupsByGroupIDAndUserIDs :exec
INSERT INTO users_groups (user_id, group_id)
SELECT unnest($1::BIGINT[]), $2
    ON CONFLICT (user_id, group_id) DO NOTHING
`

type CreateUserGroupsByGroupIDAndUserIDsParams struct {
	UserIds []int64
	GroupID int64
}

func (q *Queries) CreateUserGroupsByGroupIDAndUserIDs(ctx context.Context, arg CreateUserGroupsByGroupIDAndUserIDsParams) error {
	_, err := q.db.Exec(ctx, createUserGroupsByGroupIDAndUserIDs, arg.UserIds, arg.GroupID)
	return err
}

const deleteUserGroupsByGroupID = `-- name: DeleteUserGroupsByGroupID :exec
DELETE
FROM users_groups
WHERE group_id = $1
`

func (q *Queries) DeleteUserGroupsByGroupID(ctx context.Context, groupID int64) error {
	_, err := q.db.Exec(ctx, deleteUserGroupsByGroupID, groupID)
	return err
}

const deleteUserGroupsByGroupIDAndUserIDs = `-- name: DeleteUserGroupsByGroupIDAndUserIDs :exec
DELETE
FROM users_groups
WHERE group_id = $1
  AND user_id = ANY ($2::BIGINT[])
`

type DeleteUserGroupsByGroupIDAndUserIDsParams struct {
	GroupID int64
	UserIds []int64
}

func (q *Queries) DeleteUserGroupsByGroupIDAndUserIDs(ctx context.Context, arg DeleteUserGroupsByGroupIDAndUserIDsParams) error {
	_, err := q.db.Exec(ctx, deleteUserGroupsByGroupIDAndUserIDs, arg.GroupID, arg.UserIds)
	return err
}

const deleteUserGroupsByUserID = `-- name: DeleteUserGroupsByUserID :exec
DELETE
FROM users_groups
WHERE user_id = $1
`

func (q *Queries) DeleteUserGroupsByUserID(ctx context.Context, userID int64) error {
	_, err := q.db.Exec(ctx, deleteUserGroupsByUserID, userID)
	return err
}

const deleteUserGroupsByUserIDAndGroupIDs = `-- name: DeleteUserGroupsByUserIDAndGroupIDs :exec
DELETE
FROM users_groups
WHERE user_id = $1
  AND group_id = ANY ($2::BIGINT[])
`

type DeleteUserGroupsByUserIDAndGroupIDsParams struct {
	UserID   int64
	GroupIds []int64
}

func (q *Queries) DeleteUserGroupsByUserIDAndGroupIDs(ctx context.Context, arg DeleteUserGroupsByUserIDAndGroupIDsParams) error {
	_, err := q.db.Exec(ctx, deleteUserGroupsByUserIDAndGroupIDs, arg.UserID, arg.GroupIds)
	return err
}

const getGroupsByIDs = `-- name: GetGroupsByIDs :many
SELECT id,
       name,
       product_id,
       is_system,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM groups
WHERE id = ANY ($1::BIGINT[])
`

func (q *Queries) GetGroupsByIDs(ctx context.Context, dollar_1 []int64) ([]Group, error) {
	rows, err := q.db.Query(ctx, getGroupsByIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Group
	for rows.Next() {
		var i Group
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantGroups = `-- name: GetParticipantGroups :many
WITH user_groups AS (SELECT group_id
                     FROM users_groups
                     WHERE user_id = $1),
     participant_groups AS (SELECT group_id
                            FROM participants_groups
                            WHERE participant_id = $1)
SELECT group_id
FROM user_groups
UNION
SELECT group_id
FROM participant_groups
`

func (q *Queries) GetParticipantGroups(ctx context.Context, userID int64) ([]int64, error) {
	rows, err := q.db.Query(ctx, getParticipantGroups, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var group_id int64
		if err := rows.Scan(&group_id); err != nil {
			return nil, err
		}
		items = append(items, group_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantGroupsWithProductByUserID = `-- name: GetParticipantGroupsWithProductByUserID :many
WITH group_data AS (
    SELECT g.id, g.name, g.product_id, g.is_system, g.active_flg, g.created_at, g.updated_at, g.deleted_at
    FROM groups g
             INNER JOIN participants_groups pg ON pg.group_id = g.id
             INNER JOIN participants p ON p.id = pg.participant_id
    WHERE p.user_id = $1
),
     missing_product_ids AS (
         SELECT DISTINCT g.id AS group_id, p.product_id
         FROM group_data g
                  INNER JOIN participants_groups pg ON pg.group_id = g.id
                  INNER JOIN participants p ON p.id = pg.participant_id
         WHERE g.product_id IS NULL AND p.user_id = $1
     )
SELECT gd.id, gd.name,
       COALESCE(mpi.product_id, gd.product_id) AS product_id,
       gd.is_system, gd.active_flg, gd.created_at, gd.updated_at, gd.deleted_at
FROM group_data gd
         LEFT JOIN missing_product_ids mpi ON mpi.group_id = gd.id
`

type GetParticipantGroupsWithProductByUserIDRow struct {
	ID        int64
	Name      string
	ProductID int64
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

func (q *Queries) GetParticipantGroupsWithProductByUserID(ctx context.Context, userID int64) ([]GetParticipantGroupsWithProductByUserIDRow, error) {
	rows, err := q.db.Query(ctx, getParticipantGroupsWithProductByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetParticipantGroupsWithProductByUserIDRow
	for rows.Next() {
		var i GetParticipantGroupsWithProductByUserIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ProductID,
			&i.IsSystem,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProductsByIDs = `-- name: GetProductsByIDs :many
SELECT id, name, active_flg, created_at, updated_at, deleted_at
FROM products
WHERE id = ANY ($1::BIGINT[])
`

type GetProductsByIDsRow struct {
	ID        int64
	Name      string
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

func (q *Queries) GetProductsByIDs(ctx context.Context, dollar_1 []int64) ([]GetProductsByIDsRow, error) {
	rows, err := q.db.Query(ctx, getProductsByIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetProductsByIDsRow
	for rows.Next() {
		var i GetProductsByIDsRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserGroups = `-- name: GetUserGroups :many
SELECT group_id
FROM users_groups
WHERE user_id = $1
`

func (q *Queries) GetUserGroups(ctx context.Context, userID int64) ([]int64, error) {
	rows, err := q.db.Query(ctx, getUserGroups, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var group_id int64
		if err := rows.Scan(&group_id); err != nil {
			return nil, err
		}
		items = append(items, group_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserGroupsByGroupID = `-- name: GetUserGroupsByGroupID :many
SELECT id, user_id, group_id, created_at
FROM users_groups
WHERE group_id = $1
`

func (q *Queries) GetUserGroupsByGroupID(ctx context.Context, groupID int64) ([]UsersGroup, error) {
	rows, err := q.db.Query(ctx, getUserGroupsByGroupID, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []UsersGroup
	for rows.Next() {
		var i UsersGroup
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.GroupID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserGroupsByUserID = `-- name: GetUserGroupsByUserID :many
SELECT id, user_id, group_id, created_at
FROM users_groups
WHERE user_id = $1
`

func (q *Queries) GetUserGroupsByUserID(ctx context.Context, userID int64) ([]UsersGroup, error) {
	rows, err := q.db.Query(ctx, getUserGroupsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []UsersGroup
	for rows.Next() {
		var i UsersGroup
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.GroupID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
