// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: product.sql

package product

import (
	"context"
	"time"
)

const addParticipant = `-- name: AddParticipant :one
INSERT INTO participants (product_id, user_id)
VALUES ($1, $2)
ON CONFLICT (product_id, user_id) DO NOTHING
RETURNING id, product_id, user_id, created_at, updated_at
`

type AddParticipantParams struct {
	ProductID int64
	UserID    int64
}

func (q *Queries) AddParticipant(ctx context.Context, arg AddParticipantParams) (Participant, error) {
	row := q.db.QueryRow(ctx, addParticipant, arg.ProductID, arg.UserID)
	var i Participant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const addParticipantRole = `-- name: AddParticipantRole :exec
INSERT INTO participants_roles (participant_id, role_id)
VALUES ($1, $2)
ON CONFLICT (participant_id, role_id) DO NOTHING
`

type AddParticipantRoleParams struct {
	ParticipantID int64
	RoleID        int64
}

func (q *Queries) AddParticipantRole(ctx context.Context, arg AddParticipantRoleParams) error {
	_, err := q.db.Exec(ctx, addParticipantRole, arg.ParticipantID, arg.RoleID)
	return err
}

const createProduct = `-- name: CreateProduct :one
INSERT INTO products (iid, tech_name, name, creator_id, active_flg)
VALUES ($1, $2, $3, $4, TRUE)
ON CONFLICT (tech_name) DO NOTHING
RETURNING
    id,
    iid,
    tech_name,
    name,
    description,
    creator_id,
    active_flg,
    created_at,
    updated_at,
    deleted_at
`

type CreateProductParams struct {
	Iid       *string
	TechName  string
	Name      string
	CreatorID int64
}

func (q *Queries) CreateProduct(ctx context.Context, arg CreateProductParams) (Product, error) {
	row := q.db.QueryRow(ctx, createProduct,
		arg.Iid,
		arg.TechName,
		arg.Name,
		arg.CreatorID,
	)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Iid,
		&i.TechName,
		&i.Name,
		&i.Description,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getAllProducts = `-- name: GetAllProducts :many
SELECT id,
       iid,
       tech_name,
       name,
       description,
       creator_id,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM products
`

func (q *Queries) GetAllProducts(ctx context.Context) ([]Product, error) {
	rows, err := q.db.Query(ctx, getAllProducts)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Product
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Iid,
			&i.TechName,
			&i.Name,
			&i.Description,
			&i.CreatorID,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProductByID = `-- name: GetProductByID :one
SELECT id,
       iid,
       tech_name,
       name,
       description,
       creator_id,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM products
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetProductByID(ctx context.Context, id int64) (Product, error) {
	row := q.db.QueryRow(ctx, getProductByID, id)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Iid,
		&i.TechName,
		&i.Name,
		&i.Description,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getProductByIID = `-- name: GetProductByIID :one
SELECT id,
       iid,
       tech_name,
       name,
       description,
       creator_id,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM products
WHERE iid = $1
LIMIT 1
`

func (q *Queries) GetProductByIID(ctx context.Context, iid *string) (Product, error) {
	row := q.db.QueryRow(ctx, getProductByIID, iid)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Iid,
		&i.TechName,
		&i.Name,
		&i.Description,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getProductsByUserID = `-- name: GetProductsByUserID :many
SELECT p.id,
       p.iid,
       p.tech_name,
       p.name,
       p.description,
       p.creator_id,
       p.active_flg,
       p.created_at,
       p.updated_at,
       p.deleted_at
FROM products p
         INNER JOIN participants pt ON pt.product_id = p.id
WHERE pt.user_id = $1
`

func (q *Queries) GetProductsByUserID(ctx context.Context, userID int64) ([]Product, error) {
	rows, err := q.db.Query(ctx, getProductsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Product
	for rows.Next() {
		var i Product
		if err := rows.Scan(
			&i.ID,
			&i.Iid,
			&i.TechName,
			&i.Name,
			&i.Description,
			&i.CreatorID,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRoleByName = `-- name: GetRoleByName :one
SELECT id,
       name,
       product_id,
       is_system,
       active_flg,
       is_protected,
       created_at,
       updated_at,
       deleted_at
FROM roles
WHERE name = $1
LIMIT 1
`

func (q *Queries) GetRoleByName(ctx context.Context, name string) (Role, error) {
	row := q.db.QueryRow(ctx, getRoleByName, name)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ProductID,
		&i.IsSystem,
		&i.ActiveFlg,
		&i.IsProtected,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateProduct = `-- name: UpdateProduct :one
UPDATE products
SET iid         = COALESCE($2::VARCHAR, iid),
    tech_name   = COALESCE($3::VARCHAR, tech_name),
    name        = COALESCE($4::VARCHAR, name),
    description = COALESCE($5::VARCHAR, description),
    active_flg  = COALESCE($6::BOOLEAN, active_flg),
    deleted_at  = COALESCE($7::TIMESTAMPTZ, deleted_at)
WHERE id = $1
RETURNING
    id,
    iid,
    tech_name,
    name,
    description,
    creator_id,
    active_flg,
    created_at,
    updated_at,
    deleted_at
`

type UpdateProductParams struct {
	ID          int64
	Iid         *string
	TechName    *string
	Name        *string
	Description *string
	ActiveFlg   *bool
	DeletedAt   *time.Time
}

func (q *Queries) UpdateProduct(ctx context.Context, arg UpdateProductParams) (Product, error) {
	row := q.db.QueryRow(ctx, updateProduct,
		arg.ID,
		arg.Iid,
		arg.TechName,
		arg.Name,
		arg.Description,
		arg.ActiveFlg,
		arg.DeletedAt,
	)
	var i Product
	err := row.Scan(
		&i.ID,
		&i.Iid,
		&i.TechName,
		&i.Name,
		&i.Description,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
