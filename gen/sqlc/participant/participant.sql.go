// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: participant.sql

package participant

import (
	"context"
	"time"
)

const create = `-- name: Create :one
INSERT INTO participants (product_id, user_id)
VALUES ($1, $2)
ON CONFLICT (product_id, user_id) DO NOTHING
RETURNING id, product_id, user_id, created_at, updated_at
`

type CreateParams struct {
	ProductID int64
	UserID    int64
}

func (q *Queries) Create(ctx context.Context, arg CreateParams) (Participant, error) {
	row := q.db.QueryRow(ctx, create, arg.ProductID, arg.UserID)
	var i Participant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createByUserIDAndProductIDs = `-- name: CreateByUserIDAndProductIDs :exec
INSERT INTO participants (product_id, user_id)
SELECT unnest($1::BIGINT[]), $2
ON CONFLICT (product_id, user_id) DO NOTHING
`

type CreateByUserIDAndProductIDsParams struct {
	ProductIds []int64
	UserID     int64
}

func (q *Queries) CreateByUserIDAndProductIDs(ctx context.Context, arg CreateByUserIDAndProductIDsParams) error {
	_, err := q.db.Exec(ctx, createByUserIDAndProductIDs, arg.ProductIds, arg.UserID)
	return err
}

const deleteByIDAndProductID = `-- name: DeleteByIDAndProductID :exec
DELETE
FROM participants
WHERE id = $1::BIGINT
  AND product_id = $2::BIGINT
`

type DeleteByIDAndProductIDParams struct {
	ID        int64
	ProductID int64
}

func (q *Queries) DeleteByIDAndProductID(ctx context.Context, arg DeleteByIDAndProductIDParams) error {
	_, err := q.db.Exec(ctx, deleteByIDAndProductID, arg.ID, arg.ProductID)
	return err
}

const deleteByUserIDAndGroupIDs = `-- name: DeleteByUserIDAndGroupIDs :exec
DELETE
FROM participants_groups
WHERE participant_id IN (SELECT p.id
                         FROM participants p
                         WHERE p.user_id = $1::BIGINT)
  AND group_id = ANY ($2::BIGINT[])
`

type DeleteByUserIDAndGroupIDsParams struct {
	UserID  int64
	GroupID []int64
}

func (q *Queries) DeleteByUserIDAndGroupIDs(ctx context.Context, arg DeleteByUserIDAndGroupIDsParams) error {
	_, err := q.db.Exec(ctx, deleteByUserIDAndGroupIDs, arg.UserID, arg.GroupID)
	return err
}

const deleteByUserIDAndProductIDs = `-- name: DeleteByUserIDAndProductIDs :exec
DELETE
FROM participants
WHERE user_id = $1::BIGINT
  AND product_id = ANY ($2::BIGINT[])
`

type DeleteByUserIDAndProductIDsParams struct {
	UserID    int64
	ProductID []int64
}

func (q *Queries) DeleteByUserIDAndProductIDs(ctx context.Context, arg DeleteByUserIDAndProductIDsParams) error {
	_, err := q.db.Exec(ctx, deleteByUserIDAndProductIDs, arg.UserID, arg.ProductID)
	return err
}

const deleteByUserIDAndRoleIDs = `-- name: DeleteByUserIDAndRoleIDs :exec
DELETE
FROM participants_roles
WHERE participant_id IN (SELECT p.id
                         FROM participants p
                         WHERE p.user_id = $1::BIGINT)
  AND role_id = ANY ($2::BIGINT[])
`

type DeleteByUserIDAndRoleIDsParams struct {
	UserID int64
	RoleID []int64
}

func (q *Queries) DeleteByUserIDAndRoleIDs(ctx context.Context, arg DeleteByUserIDAndRoleIDsParams) error {
	_, err := q.db.Exec(ctx, deleteByUserIDAndRoleIDs, arg.UserID, arg.RoleID)
	return err
}

const deleteByUserIDsAndProductID = `-- name: DeleteByUserIDsAndProductID :exec
DELETE
FROM participants
WHERE product_id = $1::BIGINT AND user_id = ANY ($2::BIGINT[])
`

type DeleteByUserIDsAndProductIDParams struct {
	ProductID int64
	UserID    []int64
}

func (q *Queries) DeleteByUserIDsAndProductID(ctx context.Context, arg DeleteByUserIDsAndProductIDParams) error {
	_, err := q.db.Exec(ctx, deleteByUserIDsAndProductID, arg.ProductID, arg.UserID)
	return err
}

const getByGroupID = `-- name: GetByGroupID :many
SELECT p.id, p.user_id, p.product_id, p.created_at, p.updated_at
FROM participants p
JOIN participants_groups pg ON p.id = pg.participant_id
WHERE pg.group_id = $1::BIGINT
`

type GetByGroupIDRow struct {
	ID        int64
	UserID    int64
	ProductID int64
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (q *Queries) GetByGroupID(ctx context.Context, groupID int64) ([]GetByGroupIDRow, error) {
	rows, err := q.db.Query(ctx, getByGroupID, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetByGroupIDRow
	for rows.Next() {
		var i GetByGroupIDRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.ProductID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByID = `-- name: GetByID :one
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE id = $1::BIGINT
LIMIT 1
`

func (q *Queries) GetByID(ctx context.Context, id int64) (Participant, error) {
	row := q.db.QueryRow(ctx, getByID, id)
	var i Participant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getByIDAndProductID = `-- name: GetByIDAndProductID :one
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE id = $1::BIGINT
  AND product_id = $2::BIGINT
LIMIT 1
`

type GetByIDAndProductIDParams struct {
	ID        int64
	ProductID int64
}

func (q *Queries) GetByIDAndProductID(ctx context.Context, arg GetByIDAndProductIDParams) (Participant, error) {
	row := q.db.QueryRow(ctx, getByIDAndProductID, arg.ID, arg.ProductID)
	var i Participant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getByParticipantByProductIDAndUserEmail = `-- name: GetByParticipantByProductIDAndUserEmail :one
SELECT p.product_id, p.id, p.user_id, u.full_name, u.email
FROM participants p
         JOIN users u ON p.user_id = u.id
WHERE p.product_id = $1::BIGINT
  AND u.email = $2::TEXT
LIMIT 1
`

type GetByParticipantByProductIDAndUserEmailParams struct {
	ProductID int64
	Email     string
}

type GetByParticipantByProductIDAndUserEmailRow struct {
	ProductID int64
	ID        int64
	UserID    int64
	FullName  string
	Email     string
}

func (q *Queries) GetByParticipantByProductIDAndUserEmail(ctx context.Context, arg GetByParticipantByProductIDAndUserEmailParams) (GetByParticipantByProductIDAndUserEmailRow, error) {
	row := q.db.QueryRow(ctx, getByParticipantByProductIDAndUserEmail, arg.ProductID, arg.Email)
	var i GetByParticipantByProductIDAndUserEmailRow
	err := row.Scan(
		&i.ProductID,
		&i.ID,
		&i.UserID,
		&i.FullName,
		&i.Email,
	)
	return i, err
}

const getByProductID = `-- name: GetByProductID :many
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE product_id = $1::BIGINT
`

func (q *Queries) GetByProductID(ctx context.Context, productID int64) ([]Participant, error) {
	rows, err := q.db.Query(ctx, getByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Participant
	for rows.Next() {
		var i Participant
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByRoleID = `-- name: GetByRoleID :many
SELECT p.id, p.user_id, p.product_id, p.created_at, p.updated_at
FROM participants p
JOIN participants_roles pr ON p.id = pr.participant_id
WHERE pr.role_id = $1::BIGINT

UNION

SELECT p.id, p.user_id, p.product_id, p.created_at, p.updated_at
FROM participants p
JOIN participants_groups pg ON p.id = pg.participant_id
JOIN groups_roles gr ON pg.group_id = gr.group_id
WHERE gr.role_id = $1::BIGINT
`

type GetByRoleIDRow struct {
	ID        int64
	UserID    int64
	ProductID int64
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (q *Queries) GetByRoleID(ctx context.Context, roleID int64) ([]GetByRoleIDRow, error) {
	rows, err := q.db.Query(ctx, getByRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetByRoleIDRow
	for rows.Next() {
		var i GetByRoleIDRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.ProductID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByUserID = `-- name: GetByUserID :many
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE user_id = $1::BIGINT
`

func (q *Queries) GetByUserID(ctx context.Context, userID int64) ([]Participant, error) {
	rows, err := q.db.Query(ctx, getByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Participant
	for rows.Next() {
		var i Participant
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getByUserIDAndProductID = `-- name: GetByUserIDAndProductID :one
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE user_id = $1::BIGINT
  AND product_id = $2::BIGINT
LIMIT 1
`

type GetByUserIDAndProductIDParams struct {
	UserID    int64
	ProductID int64
}

func (q *Queries) GetByUserIDAndProductID(ctx context.Context, arg GetByUserIDAndProductIDParams) (Participant, error) {
	row := q.db.QueryRow(ctx, getByUserIDAndProductID, arg.UserID, arg.ProductID)
	var i Participant
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getByUserIDAndProductIDs = `-- name: GetByUserIDAndProductIDs :many
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE user_id = $1::BIGINT
  AND product_id = ANY ($2::BIGINT[])
`

type GetByUserIDAndProductIDsParams struct {
	UserID    int64
	ProductID []int64
}

func (q *Queries) GetByUserIDAndProductIDs(ctx context.Context, arg GetByUserIDAndProductIDsParams) ([]Participant, error) {
	rows, err := q.db.Query(ctx, getByUserIDAndProductIDs, arg.UserID, arg.ProductID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Participant
	for rows.Next() {
		var i Participant
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOwnersByProductID = `-- name: GetOwnersByProductID :many
SELECT p.product_id, p.id, p.user_id, u.full_name, u.email
FROM participants p
         JOIN users u ON p.user_id = u.id
         JOIN participants_roles pr ON p.id = pr.participant_id
         JOIN roles r ON pr.role_id = r.id
WHERE p.product_id = $1::BIGINT
  AND r.name = 'product_owner'
`

type GetOwnersByProductIDRow struct {
	ProductID int64
	ID        int64
	UserID    int64
	FullName  string
	Email     string
}

func (q *Queries) GetOwnersByProductID(ctx context.Context, productID int64) ([]GetOwnersByProductIDRow, error) {
	rows, err := q.db.Query(ctx, getOwnersByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetOwnersByProductIDRow
	for rows.Next() {
		var i GetOwnersByProductIDRow
		if err := rows.Scan(
			&i.ProductID,
			&i.ID,
			&i.UserID,
			&i.FullName,
			&i.Email,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
