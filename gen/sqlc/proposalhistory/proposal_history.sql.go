// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: proposal_history.sql

package proposalhistory

import (
	"context"
)

const create = `-- name: Create :one
INSERT INTO proposals_history (proposal_id, event_type, status, message, user_id)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, proposal_id, status, message, event_type, user_id, created_at
`

type CreateParams struct {
	ProposalID int64
	EventType  string
	Status     string
	Message    *string
	UserID     *int64
}

func (q *Queries) Create(ctx context.Context, arg CreateParams) (ProposalsHistory, error) {
	row := q.db.QueryRow(ctx, create,
		arg.ProposalID,
		arg.EventType,
		arg.Status,
		arg.Message,
		arg.UserID,
	)
	var i ProposalsHistory
	err := row.Scan(
		&i.ID,
		&i.ProposalID,
		&i.Status,
		&i.Message,
		&i.EventType,
		&i.UserID,
		&i.CreatedAt,
	)
	return i, err
}

const getByProposalID = `-- name: GetByProposalID :many
SELECT id, proposal_id, status, message, event_type, user_id, created_at
FROM proposals_history
WHERE proposal_id = $1
ORDER BY created_at DESC
`

func (q *Queries) GetByProposalID(ctx context.Context, proposalID int64) ([]ProposalsHistory, error) {
	rows, err := q.db.Query(ctx, getByProposalID, proposalID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ProposalsHistory
	for rows.Next() {
		var i ProposalsHistory
		if err := rows.Scan(
			&i.ID,
			&i.ProposalID,
			&i.Status,
			&i.Message,
			&i.EventType,
			&i.UserID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProposalIDAndUserID = `-- name: GetProposalIDAndUserID :many
SELECT id, proposal_id, status, message, event_type, user_id, created_at
FROM proposals_history
WHERE user_id = $1
ORDER BY created_at DESC
`

func (q *Queries) GetProposalIDAndUserID(ctx context.Context, userID *int64) ([]ProposalsHistory, error) {
	rows, err := q.db.Query(ctx, getProposalIDAndUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ProposalsHistory
	for rows.Next() {
		var i ProposalsHistory
		if err := rows.Scan(
			&i.ID,
			&i.ProposalID,
			&i.Status,
			&i.Message,
			&i.EventType,
			&i.UserID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUnreadEventsForUser = `-- name: GetUnreadEventsForUser :many
SELECT ph.id, ph.proposal_id, ph.status, ph.message, ph.event_type, ph.user_id, ph.created_at
FROM proposals_history ph
LEFT JOIN proposal_user_views puv ON ph.proposal_id = puv.proposal_id AND puv.user_id = $1
WHERE (puv.viewed_at IS NULL OR ph.created_at > puv.viewed_at)
ORDER BY ph.created_at DESC
`

func (q *Queries) GetUnreadEventsForUser(ctx context.Context, userID int64) ([]ProposalsHistory, error) {
	rows, err := q.db.Query(ctx, getUnreadEventsForUser, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ProposalsHistory
	for rows.Next() {
		var i ProposalsHistory
		if err := rows.Scan(
			&i.ID,
			&i.ProposalID,
			&i.Status,
			&i.Message,
			&i.EventType,
			&i.UserID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
