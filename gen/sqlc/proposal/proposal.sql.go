// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: proposal.sql

package proposal

import (
	"context"
	"time"
)

const createProposal = `-- name: CreateProposal :one
WITH new_proposal AS (
    INSERT INTO proposals (product_id, seq_num, price, type, status, active_flg, creator_id)
        VALUES ($1,
                COALESCE((SELECT MAX(seq_num) FROM proposals WHERE product_id = $1), 0) + 1,
                $2,
                $3,
                $4,
                true,
                $5)
        RETURNING id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number),
     data_arr AS (SELECT CASE
                             WHEN jsonb_typeof($6::jsonb) = 'array' THEN $6::jsonb
                             ELSE jsonb_build_array($6::jsonb)
                             END AS arr),
     inserted_sections AS (
         INSERT INTO sections (product_id, seq_num, num, data)
             SELECT $1,
                    np.seq_num,
                    gs + 1,
                    da.arr -> gs
             FROM new_proposal np,
                  data_arr da,
                  generate_series(0, jsonb_array_length(da.arr) - 1) gs
             RETURNING id, product_id, seq_num, num, data)
SELECT id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number
FROM new_proposal
`

type CreateProposalParams struct {
	ProductID int64
	Price     float64
	Type      string
	Status    string
	CreatorID int64
	Values    []byte
}

type CreateProposalRow struct {
	ID        int64
	ProductID int64
	SeqNum    int64
	Price     float64
	Status    string
	Type      string
	CreatorID int64
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
	Number    *string
}

func (q *Queries) CreateProposal(ctx context.Context, arg CreateProposalParams) (CreateProposalRow, error) {
	row := q.db.QueryRow(ctx, createProposal,
		arg.ProductID,
		arg.Price,
		arg.Type,
		arg.Status,
		arg.CreatorID,
		arg.Values,
	)
	var i CreateProposalRow
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.SeqNum,
		&i.Price,
		&i.Status,
		&i.Type,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Number,
	)
	return i, err
}

const deleteProposal = `-- name: DeleteProposal :one
UPDATE proposals
SET active_flg = false,
    updated_at = NOW()
WHERE id = $1
  AND product_id = $2
RETURNING id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number
`

type DeleteProposalParams struct {
	ID        int64
	ProductID int64
}

func (q *Queries) DeleteProposal(ctx context.Context, arg DeleteProposalParams) (Proposal, error) {
	row := q.db.QueryRow(ctx, deleteProposal, arg.ID, arg.ProductID)
	var i Proposal
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.SeqNum,
		&i.Price,
		&i.Status,
		&i.Type,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Number,
	)
	return i, err
}

const getAll = `-- name: GetAll :many
SELECT id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number
FROM proposals
WHERE active_flg = true
`

func (q *Queries) GetAll(ctx context.Context) ([]Proposal, error) {
	rows, err := q.db.Query(ctx, getAll)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Proposal
	for rows.Next() {
		var i Proposal
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.SeqNum,
			&i.Price,
			&i.Status,
			&i.Type,
			&i.CreatorID,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Number,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProposalByID = `-- name: GetProposalByID :one
SELECT id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number
FROM proposals
WHERE id = $1
`

func (q *Queries) GetProposalByID(ctx context.Context, id int64) (Proposal, error) {
	row := q.db.QueryRow(ctx, getProposalByID, id)
	var i Proposal
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.SeqNum,
		&i.Price,
		&i.Status,
		&i.Type,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Number,
	)
	return i, err
}

const getProposalByProductIDAndProposalID = `-- name: GetProposalByProductIDAndProposalID :one
SELECT id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number
FROM proposals
WHERE product_id = $1
  AND id = $2
  AND active_flg = true
`

type GetProposalByProductIDAndProposalIDParams struct {
	ProductID int64
	ID        int64
}

func (q *Queries) GetProposalByProductIDAndProposalID(ctx context.Context, arg GetProposalByProductIDAndProposalIDParams) (Proposal, error) {
	row := q.db.QueryRow(ctx, getProposalByProductIDAndProposalID, arg.ProductID, arg.ID)
	var i Proposal
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.SeqNum,
		&i.Price,
		&i.Status,
		&i.Type,
		&i.CreatorID,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Number,
	)
	return i, err
}

const getProposalsByProductID = `-- name: GetProposalsByProductID :many
SELECT id, product_id, seq_num, price, status, type, creator_id, active_flg, created_at, updated_at, deleted_at, number
FROM proposals
WHERE product_id = $1
  AND active_flg = true
ORDER BY seq_num
`

func (q *Queries) GetProposalsByProductID(ctx context.Context, productID int64) ([]Proposal, error) {
	rows, err := q.db.Query(ctx, getProposalsByProductID, productID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Proposal
	for rows.Next() {
		var i Proposal
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.SeqNum,
			&i.Price,
			&i.Status,
			&i.Type,
			&i.CreatorID,
			&i.ActiveFlg,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Number,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateProposal = `-- name: UpdateProposal :one
WITH updated_proposal AS (
    UPDATE proposals p SET
        price = COALESCE($3, p.price),
        type = COALESCE($4, p.type),
        status = COALESCE($5, p.status),
        updated_at = NOW()
        WHERE p.id = $1 AND p.product_id = $2 AND p.active_flg = true
        RETURNING p.id, p.product_id, p.seq_num, p.price, p.type, p.status, p.active_flg, p.created_at, p.updated_at, p.deleted_at, p.creator_id, p.number),
     deleted_sections AS (
         DELETE FROM sections
             WHERE product_id = $2
               AND seq_num = (SELECT seq_num FROM updated_proposal)
               AND $6::jsonb IS NOT NULL),
     data_arr AS (SELECT CASE
                           WHEN jsonb_typeof($6::jsonb) = 'array' THEN $6::jsonb
                           ELSE jsonb_build_array($6::jsonb)
                           END AS arr
                  WHERE $6::jsonb IS NOT NULL),
     inserted_sections AS (
         INSERT INTO sections (product_id, seq_num, num, data)
             SELECT $2,
                    (SELECT seq_num FROM updated_proposal),
                    gs + 1,
                    da.arr -> gs
             FROM data_arr da,
                  generate_series(0, jsonb_array_length(da.arr) - 1) gs
             ON CONFLICT (product_id, seq_num, num) DO UPDATE SET data = EXCLUDED.data
             RETURNING id, product_id, seq_num, num, data)
SELECT id, product_id, seq_num, price, type, status, active_flg, created_at, updated_at, deleted_at, creator_id, number
FROM updated_proposal
`

type UpdateProposalParams struct {
	ID        int64
	ProductID int64
	Price     *float64
	Type      *string
	Status    *string
	Values    []byte
}

type UpdateProposalRow struct {
	ID        int64
	ProductID int64
	SeqNum    int64
	Price     float64
	Type      string
	Status    string
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
	CreatorID int64
	Number    *string
}

func (q *Queries) UpdateProposal(ctx context.Context, arg UpdateProposalParams) (UpdateProposalRow, error) {
	row := q.db.QueryRow(ctx, updateProposal,
		arg.ID,
		arg.ProductID,
		arg.Price,
		arg.Type,
		arg.Status,
		arg.Values,
	)
	var i UpdateProposalRow
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.SeqNum,
		&i.Price,
		&i.Type,
		&i.Status,
		&i.ActiveFlg,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.CreatorID,
		&i.Number,
	)
	return i, err
}
