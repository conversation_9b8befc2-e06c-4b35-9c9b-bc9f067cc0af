// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: user.sql

package user

import (
	"context"
	"time"
)

const createUser = `-- name: CreateUser :one
INSERT INTO users (category_id, email, full_name, position, photo)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT (email) DO NOTHING
RETURNING id, category_id, email, full_name, position, is_admin, active_flg, last_active_product_id, last_login_at, created_at, updated_at, deleted_at, photo
`

type CreateUserParams struct {
	CategoryID int64
	Email      string
	FullName   string
	Position   *string
	Photo      *[]byte
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.CategoryID,
		arg.Email,
		arg.FullName,
		arg.Position,
		arg.Photo,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Email,
		&i.FullName,
		&i.Position,
		&i.IsAdmin,
		&i.ActiveFlg,
		&i.LastActiveProductID,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Photo,
	)
	return i, err
}

const getAllUsers = `-- name: GetAllUsers :many
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
`

func (q *Queries) GetAllUsers(ctx context.Context) ([]User, error) {
	rows, err := q.db.Query(ctx, getAllUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllUsersPaginated = `-- name: GetAllUsersPaginated :many
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
LIMIT $2::bigint OFFSET $1::bigint
`

type GetAllUsersPaginatedParams struct {
	OffsetValue int64
	LimitValue  int64
}

func (q *Queries) GetAllUsersPaginated(ctx context.Context, arg GetAllUsersPaginatedParams) ([]User, error) {
	rows, err := q.db.Query(ctx, getAllUsersPaginated, arg.OffsetValue, arg.LimitValue)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllUsersTotalCount = `-- name: GetAllUsersTotalCount :one
SELECT COUNT(*)
FROM users
`

func (q *Queries) GetAllUsersTotalCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, getAllUsersTotalCount)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getByGroupID = `-- name: GetByGroupID :many
SELECT DISTINCT u.id, u.category_id, u.email, u.full_name, u.position, u.is_admin, u.active_flg, u.last_active_product_id, u.last_login_at, u.created_at, u.updated_at, u.deleted_at, u.photo
FROM public.users u
WHERE u.id IN (

    SELECT p.user_id
    FROM public.participants p
             JOIN public.participants_groups pg ON p.id = pg.participant_id
    WHERE pg.group_id = $1

    UNION

    SELECT ug.user_id
    FROM public.users_groups ug
    WHERE ug.group_id = $1
)
ORDER BY u.id
`

func (q *Queries) GetByGroupID(ctx context.Context, groupID int64) ([]User, error) {
	rows, err := q.db.Query(ctx, getByGroupID, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
WHERE email = $1
LIMIT 1
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Email,
		&i.FullName,
		&i.Position,
		&i.IsAdmin,
		&i.ActiveFlg,
		&i.LastActiveProductID,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Photo,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetUserByID(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Email,
		&i.FullName,
		&i.Position,
		&i.IsAdmin,
		&i.ActiveFlg,
		&i.LastActiveProductID,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Photo,
	)
	return i, err
}

const getUsersByCategoryID = `-- name: GetUsersByCategoryID :many
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
WHERE category_id = $1
`

func (q *Queries) GetUsersByCategoryID(ctx context.Context, categoryID int64) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersByCategoryID, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersByFilters = `-- name: GetUsersByFilters :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON u.id = p.user_id
WHERE (
    ($1::text IS NULL OR u.email ILIKE '%' || $1 || '%')
        OR ($2::text IS NULL OR u.full_name ILIKE '%' || $2 || '%')
    )
  AND ($3::boolean IS NULL OR u.is_admin = $3)
  AND ($4::bigint[] IS NULL OR u.category_id = ANY ($4))
  AND ($5::bigint[] IS NULL OR p.product_id = ANY ($5))
`

type GetUsersByFiltersParams struct {
	Email       *string
	FullName    *string
	IsAdmin     *bool
	CategoryIds []int64
	ProductIds  []int64
}

func (q *Queries) GetUsersByFilters(ctx context.Context, arg GetUsersByFiltersParams) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersByFilters,
		arg.Email,
		arg.FullName,
		arg.IsAdmin,
		arg.CategoryIds,
		arg.ProductIds,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersByFiltersPaginated = `-- name: GetUsersByFiltersPaginated :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON u.id = p.user_id
WHERE (
    ($1::text IS NULL OR u.email ILIKE '%' || $1 || '%')
        OR ($2::text IS NULL OR u.full_name ILIKE '%' || $2 || '%')
    )
  AND ($3::boolean IS NULL OR u.is_admin = $3)
  AND ($4::bigint[] IS NULL OR u.category_id = ANY ($4))
  AND ($5::bigint[] IS NULL OR p.product_id = ANY ($5))
LIMIT $7::bigint OFFSET $6::bigint
`

type GetUsersByFiltersPaginatedParams struct {
	Email       *string
	FullName    *string
	IsAdmin     *bool
	CategoryIds []int64
	ProductIds  []int64
	OffsetValue int64
	LimitValue  int64
}

func (q *Queries) GetUsersByFiltersPaginated(ctx context.Context, arg GetUsersByFiltersPaginatedParams) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersByFiltersPaginated,
		arg.Email,
		arg.FullName,
		arg.IsAdmin,
		arg.CategoryIds,
		arg.ProductIds,
		arg.OffsetValue,
		arg.LimitValue,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersByFiltersTotalCount = `-- name: GetUsersByFiltersTotalCount :one
SELECT COUNT(DISTINCT u.id)
FROM users u
         LEFT JOIN participants p ON u.id = p.user_id
WHERE (
    ($1::text IS NULL OR u.email ILIKE '%' || $1 || '%')
        OR ($2::text IS NULL OR u.full_name ILIKE '%' || $2 || '%')
    )
  AND ($3::boolean IS NULL OR u.is_admin = $3)
  AND ($4::bigint[] IS NULL OR u.category_id = ANY ($4))
  AND ($5::bigint[] IS NULL OR p.product_id = ANY ($5))
`

type GetUsersByFiltersTotalCountParams struct {
	Email       *string
	FullName    *string
	IsAdmin     *bool
	CategoryIds []int64
	ProductIds  []int64
}

func (q *Queries) GetUsersByFiltersTotalCount(ctx context.Context, arg GetUsersByFiltersTotalCountParams) (int64, error) {
	row := q.db.QueryRow(ctx, getUsersByFiltersTotalCount,
		arg.Email,
		arg.FullName,
		arg.IsAdmin,
		arg.CategoryIds,
		arg.ProductIds,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getUsersByParticipantGroupID = `-- name: GetUsersByParticipantGroupID :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON p.user_id = u.id
         LEFT JOIN participants_groups pg ON pg.participant_id = p.id
WHERE pg.group_id = $1
`

func (q *Queries) GetUsersByParticipantGroupID(ctx context.Context, groupID int64) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersByParticipantGroupID, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersByParticipantRoleID = `-- name: GetUsersByParticipantRoleID :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON p.user_id = u.id
         LEFT JOIN participants_roles pr ON pr.participant_id = p.id
WHERE pr.role_id = $1
`

func (q *Queries) GetUsersByParticipantRoleID(ctx context.Context, roleID int64) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersByParticipantRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.CategoryID,
			&i.Email,
			&i.FullName,
			&i.Position,
			&i.IsAdmin,
			&i.ActiveFlg,
			&i.LastActiveProductID,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Photo,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateUser = `-- name: UpdateUser :one
UPDATE users
SET category_id            = COALESCE($2, category_id),
    email                  = COALESCE($3, email),
    full_name              = COALESCE($4, full_name),
    position               = COALESCE($5, position),
    is_admin               = COALESCE($6, is_admin),
    active_flg             = COALESCE($7, active_flg),
    last_active_product_id = COALESCE($8, last_active_product_id),
    last_login_at          = COALESCE($9, last_login_at),
    deleted_at             = COALESCE($10, deleted_at),
    photo                  = COALESCE($11, photo)
WHERE id = $1
RETURNING id, category_id, email, full_name, position, is_admin, active_flg, last_active_product_id, last_login_at, created_at, updated_at, deleted_at, photo
`

type UpdateUserParams struct {
	ID                  int64
	CategoryID          *int64
	Email               *string
	FullName            *string
	Position            *string
	IsAdmin             *bool
	ActiveFlg           *bool
	LastActiveProductID *int64
	LastLoginAt         *time.Time
	DeletedAt           *time.Time
	Photo               *[]byte
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUser,
		arg.ID,
		arg.CategoryID,
		arg.Email,
		arg.FullName,
		arg.Position,
		arg.IsAdmin,
		arg.ActiveFlg,
		arg.LastActiveProductID,
		arg.LastLoginAt,
		arg.DeletedAt,
		arg.Photo,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.CategoryID,
		&i.Email,
		&i.FullName,
		&i.Position,
		&i.IsAdmin,
		&i.ActiveFlg,
		&i.LastActiveProductID,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.Photo,
	)
	return i, err
}
