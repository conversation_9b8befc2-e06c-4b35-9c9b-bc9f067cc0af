// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package user

import (
	"time"
)

type CategoriesGroup struct {
	ID         int64
	CategoryID int64
	GroupID    int64
}

type CategoriesPermission struct {
	ID           int64
	CategoryID   int64
	PermissionID int64
}

type CategoriesRole struct {
	ID         int64
	CategoryID int64
	RoleID     int64
}

type Category struct {
	ID   int64
	Name string
}

type Group struct {
	ID        int64
	Name      string
	ProductID *int64
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

type GroupsRole struct {
	ID        int64
	GroupID   int64
	RoleID    int64
	CreatedAt time.Time
}

type Participant struct {
	ID        int64
	ProductID int64
	UserID    int64
	CreatedAt time.Time
	UpdatedAt time.Time
}

type ParticipantsGroup struct {
	ID            int64
	ParticipantID int64
	GroupID       int64
	CreatedAt     time.Time
}

type ParticipantsRole struct {
	ID            int64
	ParticipantID int64
	RoleID        int64
	CreatedAt     time.Time
}

type Permission struct {
	ID     int64
	Name   string
	Method string
}

type Product struct {
	ID          int64
	Iid         *string
	TechName    string
	Name        string
	Description *string
	CreatorID   int64
	ActiveFlg   bool
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time
}

type Proposal struct {
	ID        int64
	ProductID int64
	SeqNum    int64
	Price     float64
	Status    string
	Type      string
	CreatorID int64
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
	Number    *string
}

type ProposalUserView struct {
	ID         int64
	ProposalID int64
	UserID     int64
	ViewedAt   time.Time
}

type ProposalsHistory struct {
	ID         int64
	ProposalID int64
	Status     string
	Message    *string
	EventType  string
	UserID     *int64
	CreatedAt  time.Time
}

type Role struct {
	ID          int64
	Name        string
	ProductID   *int64
	IsSystem    bool
	ActiveFlg   bool
	IsProtected bool
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time
}

type RolesPermission struct {
	ID           int64
	RoleID       int64
	PermissionID int64
	CreatedAt    time.Time
}

type Section struct {
	ID        int64
	ProductID int64
	SeqNum    int64
	Num       int64
	Data      []byte
}

type User struct {
	ID                  int64
	CategoryID          int64
	Email               string
	FullName            string
	Position            *string
	IsAdmin             bool
	ActiveFlg           bool
	LastActiveProductID int64
	LastLoginAt         *time.Time
	CreatedAt           time.Time
	UpdatedAt           time.Time
	DeletedAt           *time.Time
	Photo               *[]byte
}

type UsersGroup struct {
	ID        int64
	UserID    int64
	GroupID   int64
	CreatedAt time.Time
}

type UsersRole struct {
	ID        int64
	UserID    int64
	RoleID    int64
	CreatedAt time.Time
}
