// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: section.sql

package section

import (
	"context"
)

const getSectionsByProposalID = `-- name: GetSectionsByProposalID :many
SELECT id, product_id, seq_num, num, data
FROM sections
WHERE product_id = $1
  AND seq_num = $2
ORDER BY num
`

type GetSectionsByProposalIDParams struct {
	ProductID int64
	SeqNum    int64
}

func (q *Queries) GetSectionsByProposalID(ctx context.Context, arg GetSectionsByProposalIDParams) ([]Section, error) {
	rows, err := q.db.Query(ctx, getSectionsByProposalID, arg.ProductID, arg.SeqNum)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Section
	for rows.Next() {
		var i Section
		if err := rows.Scan(
			&i.ID,
			&i.ProductID,
			&i.SeqNum,
			&i.Num,
			&i.Data,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateSectionsByProposalID = `-- name: UpdateSectionsByProposalID :one
WITH deleted_sections AS (
    DELETE FROM sections s
        WHERE s.product_id = $1 AND s.seq_num = $2),
     data_arr AS (SELECT CASE
                             WHEN jsonb_typeof($3::jsonb) = 'array' THEN $3::jsonb
                             ELSE jsonb_build_array($3::jsonb)
                             END AS arr),
     inserted_sections AS (
         INSERT INTO sections (product_id, seq_num, num, data)
             SELECT $1,
                    $2,
                    gs + 1,
                    da.arr -> gs
             FROM data_arr da,
                  generate_series(0, jsonb_array_length(da.arr) - 1) gs
             RETURNING id, product_id, seq_num, num, data)
SELECT id, product_id, seq_num, num, data
FROM inserted_sections
`

type UpdateSectionsByProposalIDParams struct {
	ProductID int64
	SeqNum    int64
	Values    []byte
}

type UpdateSectionsByProposalIDRow struct {
	ID        int64
	ProductID int64
	SeqNum    int64
	Num       int64
	Data      []byte
}

func (q *Queries) UpdateSectionsByProposalID(ctx context.Context, arg UpdateSectionsByProposalIDParams) (UpdateSectionsByProposalIDRow, error) {
	row := q.db.QueryRow(ctx, updateSectionsByProposalID, arg.ProductID, arg.SeqNum, arg.Values)
	var i UpdateSectionsByProposalIDRow
	err := row.Scan(
		&i.ID,
		&i.ProductID,
		&i.SeqNum,
		&i.Num,
		&i.Data,
	)
	return i, err
}
