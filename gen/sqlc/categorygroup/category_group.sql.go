// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: category_group.sql

package categorygroup

import (
	"context"
)

const createCategoryGroup = `-- name: CreateCategoryGroup :one
INSERT INTO categories_groups (category_id, group_id)
VALUES ($1, $2)
ON CONFLICT (category_id, group_id) DO NOTHING
RETURNING id, category_id, group_id
`

type CreateCategoryGroupParams struct {
	CategoryID int64
	GroupID    int64
}

func (q *Queries) CreateCategoryGroup(ctx context.Context, arg CreateCategoryGroupParams) (CategoriesGroup, error) {
	row := q.db.QueryRow(ctx, createCategoryGroup, arg.CategoryID, arg.GroupID)
	var i CategoriesGroup
	err := row.Scan(&i.ID, &i.CategoryID, &i.GroupID)
	return i, err
}

const delete = `-- name: Delete :exec
DELETE
FROM categories_groups
WHERE category_id = $1
  AND group_id = $2
`

type DeleteParams struct {
	CategoryID int64
	GroupID    int64
}

func (q *Queries) Delete(ctx context.Context, arg DeleteParams) error {
	_, err := q.db.Exec(ctx, delete, arg.CategoryID, arg.GroupID)
	return err
}

const getAllCategoryGroups = `-- name: GetAllCategoryGroups :many
SELECT id, category_id, group_id
FROM categories_groups
`

func (q *Queries) GetAllCategoryGroups(ctx context.Context) ([]CategoriesGroup, error) {
	rows, err := q.db.Query(ctx, getAllCategoryGroups)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesGroup
	for rows.Next() {
		var i CategoriesGroup
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.GroupID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCategoryGroupByID = `-- name: GetCategoryGroupByID :one
SELECT id, category_id, group_id
FROM categories_groups
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetCategoryGroupByID(ctx context.Context, id int64) (CategoriesGroup, error) {
	row := q.db.QueryRow(ctx, getCategoryGroupByID, id)
	var i CategoriesGroup
	err := row.Scan(&i.ID, &i.CategoryID, &i.GroupID)
	return i, err
}

const getCategoryGroupsByCategoryID = `-- name: GetCategoryGroupsByCategoryID :many
SELECT id, category_id, group_id
FROM categories_groups
WHERE category_id = $1
`

func (q *Queries) GetCategoryGroupsByCategoryID(ctx context.Context, categoryID int64) ([]CategoriesGroup, error) {
	rows, err := q.db.Query(ctx, getCategoryGroupsByCategoryID, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesGroup
	for rows.Next() {
		var i CategoriesGroup
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.GroupID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const hasCategoryGroup = `-- name: HasCategoryGroup :one
SELECT EXISTS(SELECT 1
              FROM categories_groups
              WHERE category_id = $1
                AND group_id = $2) AS exists
`

type HasCategoryGroupParams struct {
	CategoryID int64
	GroupID    int64
}

func (q *Queries) HasCategoryGroup(ctx context.Context, arg HasCategoryGroupParams) (bool, error) {
	row := q.db.QueryRow(ctx, hasCategoryGroup, arg.CategoryID, arg.GroupID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const updateCategoryGroup = `-- name: UpdateCategoryGroup :one
UPDATE categories_groups
SET group_id = $2
WHERE category_id = $1
RETURNING id, category_id, group_id
`

type UpdateCategoryGroupParams struct {
	CategoryID int64
	GroupID    int64
}

func (q *Queries) UpdateCategoryGroup(ctx context.Context, arg UpdateCategoryGroupParams) (CategoriesGroup, error) {
	row := q.db.QueryRow(ctx, updateCategoryGroup, arg.CategoryID, arg.GroupID)
	var i CategoriesGroup
	err := row.Scan(&i.ID, &i.CategoryID, &i.GroupID)
	return i, err
}
