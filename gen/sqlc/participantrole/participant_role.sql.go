// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: participant_role.sql

package participantrole

import (
	"context"
)

const createByRoleIDAndParticipantIDs = `-- name: CreateByRoleIDAndParticipantIDs :exec
INSERT INTO participants_roles (participant_id, role_id)
SELECT unnest($1::BIGINT[]), $2
ON CONFLICT (participant_id, role_id) DO NOTHING
`

type CreateByRoleIDAndParticipantIDsParams struct {
	ParticipantIds []int64
	RoleID         int64
}

func (q *Queries) CreateByRoleIDAndParticipantIDs(ctx context.Context, arg CreateByRoleIDAndParticipantIDsParams) error {
	_, err := q.db.Exec(ctx, createByRoleIDAndParticipantIDs, arg.ParticipantIds, arg.RoleID)
	return err
}

const createParticipantRole = `-- name: CreateParticipantRole :one
INSERT INTO participants_roles (participant_id, role_id)
VALUES ($1, $2)
ON CONFLICT (participant_id, role_id) DO NOTHING
RETURNING id, participant_id, role_id, created_at
`

type CreateParticipantRoleParams struct {
	ParticipantID int64
	RoleID        int64
}

func (q *Queries) CreateParticipantRole(ctx context.Context, arg CreateParticipantRoleParams) (ParticipantsRole, error) {
	row := q.db.QueryRow(ctx, createParticipantRole, arg.ParticipantID, arg.RoleID)
	var i ParticipantsRole
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const deleteParticipantRoleByParticipantAndRoleID = `-- name: DeleteParticipantRoleByParticipantAndRoleID :exec
DELETE
FROM participants_roles
WHERE participant_id = $1
  AND role_id = $2
`

type DeleteParticipantRoleByParticipantAndRoleIDParams struct {
	ParticipantID int64
	RoleID        int64
}

func (q *Queries) DeleteParticipantRoleByParticipantAndRoleID(ctx context.Context, arg DeleteParticipantRoleByParticipantAndRoleIDParams) error {
	_, err := q.db.Exec(ctx, deleteParticipantRoleByParticipantAndRoleID, arg.ParticipantID, arg.RoleID)
	return err
}

const deleteParticipantRolesByParticipantID = `-- name: DeleteParticipantRolesByParticipantID :exec
DELETE
FROM participants_roles
WHERE participant_id = $1
`

func (q *Queries) DeleteParticipantRolesByParticipantID(ctx context.Context, participantID int64) error {
	_, err := q.db.Exec(ctx, deleteParticipantRolesByParticipantID, participantID)
	return err
}

const deleteParticipantRolesByParticipantIDs = `-- name: DeleteParticipantRolesByParticipantIDs :exec
DELETE
FROM participants_roles
WHERE participant_id = ANY ($1::BIGINT[])
`

func (q *Queries) DeleteParticipantRolesByParticipantIDs(ctx context.Context, dollar_1 []int64) error {
	_, err := q.db.Exec(ctx, deleteParticipantRolesByParticipantIDs, dollar_1)
	return err
}

const deleteParticipantRolesByRoleID = `-- name: DeleteParticipantRolesByRoleID :exec
DELETE
FROM participants_roles
WHERE role_id = $1
`

func (q *Queries) DeleteParticipantRolesByRoleID(ctx context.Context, roleID int64) error {
	_, err := q.db.Exec(ctx, deleteParticipantRolesByRoleID, roleID)
	return err
}

const deleteParticipantRolesByRoleIDAndParticipantIDs = `-- name: DeleteParticipantRolesByRoleIDAndParticipantIDs :exec
DELETE
FROM participants_roles
WHERE role_id = $1
  AND participant_id = ANY ($2::BIGINT[])
`

type DeleteParticipantRolesByRoleIDAndParticipantIDsParams struct {
	RoleID         int64
	ParticipantIds []int64
}

func (q *Queries) DeleteParticipantRolesByRoleIDAndParticipantIDs(ctx context.Context, arg DeleteParticipantRolesByRoleIDAndParticipantIDsParams) error {
	_, err := q.db.Exec(ctx, deleteParticipantRolesByRoleIDAndParticipantIDs, arg.RoleID, arg.ParticipantIds)
	return err
}

const getAllParticipantRoles = `-- name: GetAllParticipantRoles :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
`

func (q *Queries) GetAllParticipantRoles(ctx context.Context) ([]ParticipantsRole, error) {
	rows, err := q.db.Query(ctx, getAllParticipantRoles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsRole
	for rows.Next() {
		var i ParticipantsRole
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantRoleByID = `-- name: GetParticipantRoleByID :one
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetParticipantRoleByID(ctx context.Context, id int64) (ParticipantsRole, error) {
	row := q.db.QueryRow(ctx, getParticipantRoleByID, id)
	var i ParticipantsRole
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const getParticipantRoleByParticipantAndRoleID = `-- name: GetParticipantRoleByParticipantAndRoleID :one
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE participant_id = $1
  AND role_id = $2
LIMIT 1
`

type GetParticipantRoleByParticipantAndRoleIDParams struct {
	ParticipantID int64
	RoleID        int64
}

func (q *Queries) GetParticipantRoleByParticipantAndRoleID(ctx context.Context, arg GetParticipantRoleByParticipantAndRoleIDParams) (ParticipantsRole, error) {
	row := q.db.QueryRow(ctx, getParticipantRoleByParticipantAndRoleID, arg.ParticipantID, arg.RoleID)
	var i ParticipantsRole
	err := row.Scan(
		&i.ID,
		&i.ParticipantID,
		&i.RoleID,
		&i.CreatedAt,
	)
	return i, err
}

const getParticipantRolesByParticipantID = `-- name: GetParticipantRolesByParticipantID :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE participant_id = $1
`

func (q *Queries) GetParticipantRolesByParticipantID(ctx context.Context, participantID int64) ([]ParticipantsRole, error) {
	rows, err := q.db.Query(ctx, getParticipantRolesByParticipantID, participantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsRole
	for rows.Next() {
		var i ParticipantsRole
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantRolesByParticipantIDs = `-- name: GetParticipantRolesByParticipantIDs :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE participant_id = ANY ($1::BIGINT[])
`

func (q *Queries) GetParticipantRolesByParticipantIDs(ctx context.Context, dollar_1 []int64) ([]ParticipantsRole, error) {
	rows, err := q.db.Query(ctx, getParticipantRolesByParticipantIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsRole
	for rows.Next() {
		var i ParticipantsRole
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantRolesByRoleID = `-- name: GetParticipantRolesByRoleID :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE role_id = $1
`

func (q *Queries) GetParticipantRolesByRoleID(ctx context.Context, roleID int64) ([]ParticipantsRole, error) {
	rows, err := q.db.Query(ctx, getParticipantRolesByRoleID, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsRole
	for rows.Next() {
		var i ParticipantsRole
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getParticipantRolesByRoleIDAndParticipantIDs = `-- name: GetParticipantRolesByRoleIDAndParticipantIDs :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE role_id = $1
  AND participant_id = ANY ($2::BIGINT[])
`

type GetParticipantRolesByRoleIDAndParticipantIDsParams struct {
	RoleID         int64
	ParticipantIds []int64
}

func (q *Queries) GetParticipantRolesByRoleIDAndParticipantIDs(ctx context.Context, arg GetParticipantRolesByRoleIDAndParticipantIDsParams) ([]ParticipantsRole, error) {
	rows, err := q.db.Query(ctx, getParticipantRolesByRoleIDAndParticipantIDs, arg.RoleID, arg.ParticipantIds)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsRole
	for rows.Next() {
		var i ParticipantsRole
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getProductOwnersByParticipantID = `-- name: GetProductOwnersByParticipantID :many
SELECT pr.id, pr.participant_id, pr.role_id, pr.created_at
FROM participants_roles pr
WHERE pr.role_id = (SELECT id
                    FROM roles
                    WHERE name = 'product_owner')
  AND pr.participant_id IN (SELECT p.id
                            FROM participants p
                            WHERE p.product_id IN (SELECT p2.product_id
                                                   FROM participants p2
                                                   WHERE p2.id = $1))
`

func (q *Queries) GetProductOwnersByParticipantID(ctx context.Context, id int64) ([]ParticipantsRole, error) {
	rows, err := q.db.Query(ctx, getProductOwnersByParticipantID, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ParticipantsRole
	for rows.Next() {
		var i ParticipantsRole
		if err := rows.Scan(
			&i.ID,
			&i.ParticipantID,
			&i.RoleID,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const isParticipantOwner = `-- name: IsParticipantOwner :one
SELECT COUNT(*) > 0 as is_owner
FROM participants_roles
WHERE role_id = (SELECT id
                 FROM roles
                 WHERE name = 'product_owner')
  AND participant_id = $1
`

func (q *Queries) IsParticipantOwner(ctx context.Context, participantID int64) (bool, error) {
	row := q.db.QueryRow(ctx, isParticipantOwner, participantID)
	var is_owner bool
	err := row.Scan(&is_owner)
	return is_owner, err
}
