// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: category_permission.sql

package categorypermission

import (
	"context"
)

const createCategoryPermission = `-- name: CreateCategoryPermission :one
INSERT INTO categories_permissions (category_id, permission_id)
VALUES ($1, $2)
ON CONFLICT (category_id, permission_id) DO NOTHING
RETURNING id, category_id, permission_id
`

type CreateCategoryPermissionParams struct {
	CategoryID   int64
	PermissionID int64
}

func (q *Queries) CreateCategoryPermission(ctx context.Context, arg CreateCategoryPermissionParams) (CategoriesPermission, error) {
	row := q.db.QueryRow(ctx, createCategoryPermission, arg.CategoryID, arg.PermissionID)
	var i CategoriesPermission
	err := row.Scan(&i.ID, &i.CategoryID, &i.PermissionID)
	return i, err
}

const deleteCategoryPermission = `-- name: DeleteCategoryPermission :exec
DELETE
FROM categories_permissions
WHERE category_id = $1
  AND permission_id = $2
`

type DeleteCategoryPermissionParams struct {
	CategoryID   int64
	PermissionID int64
}

func (q *Queries) DeleteCategoryPermission(ctx context.Context, arg DeleteCategoryPermissionParams) error {
	_, err := q.db.Exec(ctx, deleteCategoryPermission, arg.CategoryID, arg.PermissionID)
	return err
}

const getAllCategoryPermissions = `-- name: GetAllCategoryPermissions :many
SELECT id, category_id, permission_id
FROM categories_permissions
`

func (q *Queries) GetAllCategoryPermissions(ctx context.Context) ([]CategoriesPermission, error) {
	rows, err := q.db.Query(ctx, getAllCategoryPermissions)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesPermission
	for rows.Next() {
		var i CategoriesPermission
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.PermissionID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCategoryPermissionByID = `-- name: GetCategoryPermissionByID :one
SELECT id, category_id, permission_id
FROM categories_permissions
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetCategoryPermissionByID(ctx context.Context, id int64) (CategoriesPermission, error) {
	row := q.db.QueryRow(ctx, getCategoryPermissionByID, id)
	var i CategoriesPermission
	err := row.Scan(&i.ID, &i.CategoryID, &i.PermissionID)
	return i, err
}

const getCategoryPermissionsByCategoryID = `-- name: GetCategoryPermissionsByCategoryID :many
SELECT id, category_id, permission_id
FROM categories_permissions
WHERE category_id = $1
`

func (q *Queries) GetCategoryPermissionsByCategoryID(ctx context.Context, categoryID int64) ([]CategoriesPermission, error) {
	rows, err := q.db.Query(ctx, getCategoryPermissionsByCategoryID, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CategoriesPermission
	for rows.Next() {
		var i CategoriesPermission
		if err := rows.Scan(&i.ID, &i.CategoryID, &i.PermissionID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const hasCategoryPermission = `-- name: HasCategoryPermission :one
SELECT EXISTS(SELECT 1
              FROM categories_permissions
              WHERE category_id = $1
                AND permission_id = $2) AS exists
`

type HasCategoryPermissionParams struct {
	CategoryID   int64
	PermissionID int64
}

func (q *Queries) HasCategoryPermission(ctx context.Context, arg HasCategoryPermissionParams) (bool, error) {
	row := q.db.QueryRow(ctx, hasCategoryPermission, arg.CategoryID, arg.PermissionID)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const updateCategoryPermission = `-- name: UpdateCategoryPermission :one
UPDATE categories_permissions
SET permission_id = $2
WHERE category_id = $1
RETURNING id, category_id, permission_id
`

type UpdateCategoryPermissionParams struct {
	CategoryID   int64
	PermissionID int64
}

func (q *Queries) UpdateCategoryPermission(ctx context.Context, arg UpdateCategoryPermissionParams) (CategoriesPermission, error) {
	row := q.db.QueryRow(ctx, updateCategoryPermission, arg.CategoryID, arg.PermissionID)
	var i CategoriesPermission
	err := row.Scan(&i.ID, &i.CategoryID, &i.PermissionID)
	return i, err
}
