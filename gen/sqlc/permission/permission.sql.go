// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: permission.sql

package permission

import (
	"context"
)

const createPermission = `-- name: CreatePermission :one
INSERT INTO permissions (name, method)
VALUES ($1, $2)
ON CONFLICT (name, method) DO NOTHING
RETURNING id, name, method
`

type CreatePermissionParams struct {
	Name   string
	Method string
}

func (q *Queries) CreatePermission(ctx context.Context, arg CreatePermissionParams) (Permission, error) {
	row := q.db.QueryRow(ctx, createPermission, arg.Name, arg.Method)
	var i Permission
	err := row.Scan(&i.ID, &i.Name, &i.Method)
	return i, err
}

const getAllPermissions = `-- name: GetAllPermissions :many
SELECT id, name, method
FROM permissions
`

func (q *Queries) GetAllPermissions(ctx context.Context) ([]Permission, error) {
	rows, err := q.db.Query(ctx, getAllPermissions)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Permission
	for rows.Next() {
		var i Permission
		if err := rows.Scan(&i.ID, &i.Name, &i.Method); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllUniqueNames = `-- name: GetAllUniqueNames :many
SELECT DISTINCT name
FROM permissions
`

func (q *Queries) GetAllUniqueNames(ctx context.Context) ([]string, error) {
	rows, err := q.db.Query(ctx, getAllUniqueNames)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, err
		}
		items = append(items, name)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getIDsByName = `-- name: GetIDsByName :many
SELECT id
FROM permissions
WHERE name = $1
`

func (q *Queries) GetIDsByName(ctx context.Context, name string) ([]int64, error) {
	rows, err := q.db.Query(ctx, getIDsByName, name)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		items = append(items, id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPermissionByID = `-- name: GetPermissionByID :one
SELECT id, name, method
FROM permissions
WHERE id = $1
`

func (q *Queries) GetPermissionByID(ctx context.Context, id int64) (Permission, error) {
	row := q.db.QueryRow(ctx, getPermissionByID, id)
	var i Permission
	err := row.Scan(&i.ID, &i.Name, &i.Method)
	return i, err
}

const getPermissionByNameMethod = `-- name: GetPermissionByNameMethod :one
SELECT id, name, method
FROM permissions
WHERE name = $1
  AND method = $2
`

type GetPermissionByNameMethodParams struct {
	Name   string
	Method string
}

func (q *Queries) GetPermissionByNameMethod(ctx context.Context, arg GetPermissionByNameMethodParams) (Permission, error) {
	row := q.db.QueryRow(ctx, getPermissionByNameMethod, arg.Name, arg.Method)
	var i Permission
	err := row.Scan(&i.ID, &i.Name, &i.Method)
	return i, err
}

const getPermissionsByUserID = `-- name: GetPermissionsByUserID :many
SELECT DISTINCT p.id, p.name, p.method
FROM permissions p
WHERE p.id IN (
    -- Permissions through direct user roles
    SELECT rp.permission_id
    FROM users_roles ur
    JOIN roles r ON ur.role_id = r.id
    JOIN roles_permissions rp ON r.id = rp.role_id
    WHERE ur.user_id = $1
      AND r.active_flg = true

    UNION

    -- Permissions through user groups and group roles
    SELECT rp.permission_id
    FROM users_groups ug
    JOIN groups g ON ug.group_id = g.id
    JOIN groups_roles gr ON g.id = gr.group_id
    JOIN roles r ON gr.role_id = r.id
    JOIN roles_permissions rp ON r.id = rp.role_id
    WHERE ug.user_id = $1
      AND g.active_flg = true
      AND r.active_flg = true
)
ORDER BY p.name, p.method
`

func (q *Queries) GetPermissionsByUserID(ctx context.Context, userID int64) ([]Permission, error) {
	rows, err := q.db.Query(ctx, getPermissionsByUserID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Permission
	for rows.Next() {
		var i Permission
		if err := rows.Scan(&i.ID, &i.Name, &i.Method); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updatePermission = `-- name: UpdatePermission :one
UPDATE permissions
SET name   = COALESCE($1, name),
    method = COALESCE($2, method)
WHERE id = $3
RETURNING id, name, method
`

type UpdatePermissionParams struct {
	Name   string
	Method string
	ID     int64
}

func (q *Queries) UpdatePermission(ctx context.Context, arg UpdatePermissionParams) (Permission, error) {
	row := q.db.QueryRow(ctx, updatePermission, arg.Name, arg.Method, arg.ID)
	var i Permission
	err := row.Scan(&i.ID, &i.Name, &i.Method)
	return i, err
}
