// Package user provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package user

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for GetUsersParamsSort.
const (
	GetUsersParamsSortCategory  GetUsersParamsSort = "category"
	GetUsersParamsSortCreatedAt GetUsersParamsSort = "createdAt"
	GetUsersParamsSortFullName  GetUsersParamsSort = "fullName"
	GetUsersParamsSortProduct   GetUsersParamsSort = "product"
)

// Defines values for GetUsersParamsOrder.
const (
	GetUsersParamsOrderAscend  GetUsersParamsOrder = "ascend"
	GetUsersParamsOrderDescend GetUsersParamsOrder = "descend"
)

// Defines values for GetUserProductsParamsSort.
const (
	TechName GetUserProductsParamsSort = "techName"
)

// Defines values for GetUserProductsParamsOrder.
const (
	GetUserProductsParamsOrderAscend  GetUserProductsParamsOrder = "ascend"
	GetUserProductsParamsOrderDescend GetUserProductsParamsOrder = "descend"
)

// Defines values for GetUserRolesWithProductsParamsType.
const (
	Custom GetUserRolesWithProductsParamsType = "custom"
	System GetUserRolesWithProductsParamsType = "system"
)

// Defines values for GetUserRolesWithProductsParamsSort.
const (
	GetUserRolesWithProductsParamsSortProduct  GetUserRolesWithProductsParamsSort = "product"
	GetUserRolesWithProductsParamsSortRoleName GetUserRolesWithProductsParamsSort = "roleName"
	GetUserRolesWithProductsParamsSortRoleType GetUserRolesWithProductsParamsSort = "roleType"
)

// Defines values for GetUserRolesWithProductsParamsOrder.
const (
	Ascend  GetUserRolesWithProductsParamsOrder = "ascend"
	Descend GetUserRolesWithProductsParamsOrder = "descend"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupWithProductCollectionV1DTO defines model for GroupWithProductCollectionV1DTO.
type GroupWithProductCollectionV1DTO struct {
	ID      int64              `json:"id"`
	Name    string             `json:"name"`
	Type    string             `json:"type"`
	Product *ProductBasicV1DTO `json:"product,omitempty"`
}

// PaginationV1DTO defines model for PaginationV1DTO.
type PaginationV1DTO struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
	Total  int64 `json:"total"`
}

// ProductBasicV1DTO defines model for ProductBasicV1DTO.
type ProductBasicV1DTO struct {
	ID       int64   `json:"id"`
	IID      *string `json:"iid,omitempty"`
	TechName string  `json:"techName"`
	Name     string  `json:"name"`
}

// RoleBasicV1DTO defines model for RoleBasicV1DTO.
type RoleBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// RoleWithProductV1DTO defines model for RoleWithProductV1DTO.
type RoleWithProductV1DTO struct {
	ID       int64              `json:"id"`
	Name     string             `json:"name"`
	Type     string             `json:"type"`
	IsActive bool               `json:"isActive"`
	Product  *ProductBasicV1DTO `json:"product,omitempty"`
}

// RolesWithProductsCollectionV1DTO defines model for RolesWithProductsCollectionV1DTO.
type RolesWithProductsCollectionV1DTO struct {
	Items []RoleWithProductV1DTO `json:"items"`
}

// UserCollectionV1DTO defines model for UserCollectionV1DTO.
type UserCollectionV1DTO struct {
	Items []UserV1DTO     `json:"items"`
	Meta  PaginationV1DTO `json:"meta"`
}

// UserCreateV1DTO defines model for UserCreateV1DTO.
type UserCreateV1DTO struct {
	Email    string `json:"email"`
	FullName string `json:"fullName"`
	Photo    string `json:"photo"`
}

// UserProductCollectionV1DTO defines model for UserProductCollectionV1DTO.
type UserProductCollectionV1DTO struct {
	Items []ProductBasicV1DTO `json:"items"`
}

// UserUpdateV1DTO defines model for UserUpdateV1DTO.
type UserUpdateV1DTO struct {
	CategoryID *int64   `json:"categoryID,omitempty"`
	IsAdmin    *bool    `json:"isAdmin,omitempty"`
	RoleIDs    *[]int64 `json:"roleIDs,omitempty"`
	GroupIDs   *[]int64 `json:"groupIDs,omitempty"`
	ProductIDs *[]int64 `json:"productIDs,omitempty"`
}

// UserV1DTO defines model for UserV1DTO.
type UserV1DTO struct {
	ID          int64                `json:"id"`
	Category    int64                `json:"category"`
	Email       string               `json:"email"`
	FullName    string               `json:"fullName"`
	Position    string               `json:"position"`
	IsAdmin     bool                 `json:"isAdmin"`
	LastLoginAt *time.Time           `json:"lastLoginAt,omitempty"`
	Photo       string               `json:"photo"`
	CreatedAt   time.Time            `json:"createdAt"`
	Products    *[]ProductBasicV1DTO `json:"products,omitempty"`
}

// GetUsersParams defines parameters for GetUsers.
type GetUsersParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`

	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`

	// CategoryIDs Category IDs
	CategoryIDs *[]int64 `form:"categoryIDs,omitempty" json:"categoryIDs,omitempty"`

	// IsAdmin Filter by admin status.
	IsAdmin *bool `form:"isAdmin,omitempty" json:"isAdmin,omitempty"`

	// Limit Limit the number of entities returned.
	Limit *int64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Offset the number of entities returned.
	Offset *int64 `form:"offset,omitempty" json:"offset,omitempty"`

	// Sort Sort the users by a field
	Sort *GetUsersParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetUsersParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetUsersParamsSort defines parameters for GetUsers.
type GetUsersParamsSort string

// GetUsersParamsOrder defines parameters for GetUsers.
type GetUsersParamsOrder string

// DeleteUserGroupsParams defines parameters for DeleteUserGroups.
type DeleteUserGroupsParams struct {
	// GroupIDs Group IDs
	GroupIDs *[]int64 `form:"groupIDs,omitempty" json:"groupIDs,omitempty"`
}

// GetUserGroupsWithProductsParams defines parameters for GetUserGroupsWithProducts.
type GetUserGroupsWithProductsParams struct {
	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`
}

// DeleteUserProductsParams defines parameters for DeleteUserProducts.
type DeleteUserProductsParams struct {
	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`
}

// GetUserProductsParams defines parameters for GetUserProducts.
type GetUserProductsParams struct {
	// Sort Sort the products by a field.
	Sort *GetUserProductsParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetUserProductsParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetUserProductsParamsSort defines parameters for GetUserProducts.
type GetUserProductsParamsSort string

// GetUserProductsParamsOrder defines parameters for GetUserProducts.
type GetUserProductsParamsOrder string

// DeleteUserRolesParams defines parameters for DeleteUserRoles.
type DeleteUserRolesParams struct {
	// RoleIDs Role IDs
	RoleIDs *[]int64 `form:"roleIDs,omitempty" json:"roleIDs,omitempty"`
}

// GetUserRolesWithProductsParams defines parameters for GetUserRolesWithProducts.
type GetUserRolesWithProductsParams struct {
	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`

	// Type Filter by type (custom or system).
	Type *GetUserRolesWithProductsParamsType `form:"type,omitempty" json:"type,omitempty"`

	// Sort Sort the roles by a field.
	Sort *GetUserRolesWithProductsParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetUserRolesWithProductsParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetUserRolesWithProductsParamsType defines parameters for GetUserRolesWithProducts.
type GetUserRolesWithProductsParamsType string

// GetUserRolesWithProductsParamsSort defines parameters for GetUserRolesWithProducts.
type GetUserRolesWithProductsParamsSort string

// GetUserRolesWithProductsParamsOrder defines parameters for GetUserRolesWithProducts.
type GetUserRolesWithProductsParamsOrder string

// CreateUserJSONRequestBody defines body for CreateUser for application/json ContentType.
type CreateUserJSONRequestBody = UserCreateV1DTO

// UpdateUserJSONRequestBody defines body for UpdateUser for application/json ContentType.
type UpdateUserJSONRequestBody = UserUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get users
	// (GET /v1/users)
	GetUsers(c *gin.Context, params GetUsersParams)
	// Create new user
	// (POST /v1/users)
	CreateUser(c *gin.Context)
	// Get current user
	// (GET /v1/users/current)
	GetCurrentUser(c *gin.Context)
	// Get user
	// (GET /v1/users/{userID})
	GetUser(c *gin.Context, userID int64)
	// Update user
	// (PATCH /v1/users/{userID})
	UpdateUser(c *gin.Context, userID int64)
	// Delete user groups
	// (DELETE /v1/users/{userID}/groups)
	DeleteUserGroups(c *gin.Context, userID int64, params DeleteUserGroupsParams)
	// Get user groups with products
	// (GET /v1/users/{userID}/groups)
	GetUserGroupsWithProducts(c *gin.Context, userID int64, params GetUserGroupsWithProductsParams)
	// Delete user products
	// (DELETE /v1/users/{userID}/products)
	DeleteUserProducts(c *gin.Context, userID int64, params DeleteUserProductsParams)
	// Get user products
	// (GET /v1/users/{userID}/products)
	GetUserProducts(c *gin.Context, userID int64, params GetUserProductsParams)
	// Delete user roles
	// (DELETE /v1/users/{userID}/roles)
	DeleteUserRoles(c *gin.Context, userID int64, params DeleteUserRolesParams)
	// Get user roles with products
	// (GET /v1/users/{userID}/roles)
	GetUserRolesWithProducts(c *gin.Context, userID int64, params GetUserRolesWithProductsParams)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetUsers operation middleware
func (siw *ServerInterfaceWrapper) GetUsers(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUsersParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "categoryIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "categoryIDs", c.Request.URL.Query(), &params.CategoryIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter categoryIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "isAdmin" -------------

	err = runtime.BindQueryParameter("form", true, false, "isAdmin", c.Request.URL.Query(), &params.IsAdmin)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter isAdmin: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsers(c, params)
}

// CreateUser operation middleware
func (siw *ServerInterfaceWrapper) CreateUser(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateUser(c)
}

// GetCurrentUser operation middleware
func (siw *ServerInterfaceWrapper) GetCurrentUser(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCurrentUser(c)
}

// GetUser operation middleware
func (siw *ServerInterfaceWrapper) GetUser(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUser(c, userID)
}

// UpdateUser operation middleware
func (siw *ServerInterfaceWrapper) UpdateUser(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateUser(c, userID)
}

// DeleteUserGroups operation middleware
func (siw *ServerInterfaceWrapper) DeleteUserGroups(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteUserGroupsParams

	// ------------- Optional query parameter "groupIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "groupIDs", c.Request.URL.Query(), &params.GroupIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupIDs: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteUserGroups(c, userID, params)
}

// GetUserGroupsWithProducts operation middleware
func (siw *ServerInterfaceWrapper) GetUserGroupsWithProducts(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUserGroupsWithProductsParams

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUserGroupsWithProducts(c, userID, params)
}

// DeleteUserProducts operation middleware
func (siw *ServerInterfaceWrapper) DeleteUserProducts(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteUserProductsParams

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteUserProducts(c, userID, params)
}

// GetUserProducts operation middleware
func (siw *ServerInterfaceWrapper) GetUserProducts(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUserProductsParams

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUserProducts(c, userID, params)
}

// DeleteUserRoles operation middleware
func (siw *ServerInterfaceWrapper) DeleteUserRoles(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params DeleteUserRolesParams

	// ------------- Optional query parameter "roleIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "roleIDs", c.Request.URL.Query(), &params.RoleIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleIDs: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteUserRoles(c, userID, params)
}

// GetUserRolesWithProducts operation middleware
func (siw *ServerInterfaceWrapper) GetUserRolesWithProducts(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUserRolesWithProductsParams

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", c.Request.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter type: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUserRolesWithProducts(c, userID, params)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/users", wrapper.GetUsers)
	router.POST(options.BaseURL+"/v1/users", wrapper.CreateUser)
	router.GET(options.BaseURL+"/v1/users/current", wrapper.GetCurrentUser)
	router.GET(options.BaseURL+"/v1/users/:userID", wrapper.GetUser)
	router.PATCH(options.BaseURL+"/v1/users/:userID", wrapper.UpdateUser)
	router.DELETE(options.BaseURL+"/v1/users/:userID/groups", wrapper.DeleteUserGroups)
	router.GET(options.BaseURL+"/v1/users/:userID/groups", wrapper.GetUserGroupsWithProducts)
	router.DELETE(options.BaseURL+"/v1/users/:userID/products", wrapper.DeleteUserProducts)
	router.GET(options.BaseURL+"/v1/users/:userID/products", wrapper.GetUserProducts)
	router.DELETE(options.BaseURL+"/v1/users/:userID/roles", wrapper.DeleteUserRoles)
	router.GET(options.BaseURL+"/v1/users/:userID/roles", wrapper.GetUserRolesWithProducts)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+wcXXPbNvKvcHD30M4wlh2nvVRvaZxkfNfWHie5PmQ8NzC5kpCQBAuASnUe/fcbfBCE",
	"SPBLlmL5qifL4GKx2O9dArxHEU1zmkEmOJreIx4tIMXq5xvGKLsBntOMw7/PLj5cydGc0RyYIKBgIhqD",
	"/CtWOaAp4oKRbI5C9OczymJgaPp8HaIUOMfzbrjzdYjo3WeIxOVFJ+APFvCDAukAfbEOERdYdEP9aKAK",
	"7oCRTMAcmAt3tl6HiMEfBWEQo+mnclKomVBtc4M+Z1clMbdhuYp+hNYhesdokf9OxOKa0biIxGuaJBAJ",
	"QrMWxpO4hdo5fZbhVI6qJdVABfbji80thUgD90gw11RJOJwkVzM0/XSP/s5ghqbob5NKgyZGfSZmGz9j",
	"TiK9g/VtTTKiT3zndYaTGBl6zWwfJ6/xnGS4g3MJSYloZ14Xr+hsxmGLuZKFggqcjJ/aYIIm39JSIvay",
	"oiGE/akR2cDkCNNBZDA5TOlVPqUoEC1+6wP0K4udapbycemGJvBtWMRfRYIs3X3cUZoAzuo7HmSTO7Af",
	"h6Y2zjg+yfJnmAuo8XUd1hm7e6eybmzi1myDO/vg/c5VQLr5o2+jDTZVpGDG8KozkOhVfBL4yIHtmFqJ",
	"cgCJMpwJPEI2Nbe7IZnn/h2bNVo3zgCLtsQDUkySTvWXe5gVSfLboAC3oIKOsyZNgbNGiaVtP4ND+yh5",
	"eoxiZ6r3MY/bJRBhAXPKVjpfS0l2qck9C+U/JC1S9Xt8lJ3LXOjyYpMFI1Guw02K2hjyQvvlOCVZt1t2",
	"sqBvRJpMdBlN4Bstd+7xnloLeuS/XToUKeOOXyn3P6MsxQJNkVS3Z4JUEaqeSphBCXLyQcNZtD+twwFu",
	"4XyoW1CqscPY369jshJJMBe/0DnJdsmZfwzycC8lFOVEuqbeEszYwr781Kk3d7EqF3q9b0l7xe5y266+",
	"NZ2dLAAhKhgRq/eSWq3frwqxoIz8F5f8uAPMgL0tJfLP3z/Ikk5NkPJUTyvpLITI0VriJtlMM56IRD65",
	"/vV98Or6EoVoCYwr5Gh5JtlBc8hwTtAUnZ+cnkglyrFYKGomy7MJlnuaKPcox9ZhY3RiOKRN1Adwb7zr",
	"uv48B5YSLslpTHVk7R2f3FvPuPbA5JTjhLc+ULPVT8906QC5d7Bjq/r5vXaeDZQFB8a9g5N7+Wdjim+R",
	"amxyXwXBdS+An8WwNK2Xjf8nnBYsgsZwkTHAcTVcV4Z2KX+BVZRQ/KW+fy9RTYl3y9r3tEFbB5CPYC94",
	"uuqkdwM2x0yQiOQ4E2NAJ/fOf/0UdU0dxoJODDUD8CNo2lg3XIvJjZg0WRAudPDfYnLVENxisu3VbTF3",
	"SeBrz8whDG9zMLW5bWD1HZQWiea6uRQDjxjJddxBNyAKlvFALCBQkCdIRQqmAtNljKboHYiPCocMGAyn",
	"IBTCT3VUMRY4mFEWcMAsWshAKYf/KEDFVJPR2Ic6dDdTgbWs5Tcxm+geyGTVj9VJnl3MNnewmU6ZP9Wy",
	"rnq24CHitfG1HVRU7nh/ZLwliQAW3K0CFVsCLe6TFoqqVKXB7jJF9KzxC0mJUCqRFekdsIDOAsgEkcl5",
	"wJTGQNy2ZNlCrBaMYYaLRMjEa1hR0aToSvUjtyXJdjM9NG1N0nvKRGU2SiDBjEAStyk+ZS0kuGkmZLL4",
	"+uQOOXlplWdWXfPbsN+ArlTi28Ic88xHGOYRZLFDlh2Q+OUvz+q3MrfW73WU0j8/PdUvczIBmW7I5XlC",
	"IuVhJp+5zn+r5fv6S/UWh0qDN/er/FXAiygCziUrV1JHGIElxNLIXuyQJM+LLA9Fl9kSJyQOZNUBXATG",
	"Eyhazppu+WOGTYFQEnzeBHpL2R2JY8gUxPOfmhAfKA1SnK3KZbmE/OERNi+AZTgJOLAlsADkLF0ZFWmK",
	"ZaCXUUZbkvSKeC4DjBIjutWFoyd26RYeD3CQwVc1OfhKxEIZJc8hIjMCcQB/msVxFgekpOTywhPpNEK1",
	"qq4PgYufabzarfo6jccW1Q0UDKFZcKOJQG65KlgB6wfaWN1m/WQYd7NhSEfzOUjz0VplDaFpRG42OIkK",
	"xgzxnVmhgVMovYnhaw1gTWaPbr/bYo6+/on5ele1erS1pSxvdFWGVTi+GHF50Vr29FU9SvtUm1YlVzkW",
	"iyq30rQ1vLcnG+9OPm+PtnW0rXF5lDeNwiJa7M5C9Au8wzKS/eRs7qvKNivRMHvL2HZowoWi9Gi/B2m/",
	"Ro0GhMUy8lV9YBRDAvpA4ubCF2pcW7iGDuhstL1rLJKUd3rFQ7D6Rq9D0dbRpLMHAHbUoWuJzbVmkWWs",
	"YriRgRbXsb56Amapdd+Vni/AdiagOEgIF9LwjPgx5zQiqsK2FigXCAOSRUkRk2wekEyLgtAswHe0EAGD",
	"RE0p3x20Zq7aSN2jaYdpsI/R3X9oQj3oUELfsesmWQ3VfO/G7TkVG+5DKY19mXr0G4ecjvtlNjDAu8dh",
	"hoT4Ev5hQf7oNXYR6q0sjsH+SQb7dlvtCfe98fmw7cu+3rQKXL3hPBn/itO5K1G+S7RDf7k3mG35QEvx",
	"biVwbMQ9xcg/NtqX53wGhXoF/LA4r26wHKYTkqR1RPjyAPsjhnfN/2Nsf5KxnRnN37aO18LfcxnfuGB2",
	"zMcHHI6TkMF3UcEFTQPKAr7iAtLv2zIXc2HRl0E4uYNGJ+EUtkGJi82jtLI8KIkqj3xVFEmkJq2SP82l",
	"8L/k0bDei5iD2ytaUsfuyhPKsXwia6ZbzlUc5Tdrl3A+3UoF1ctpx1qwxFy24dPJJPlyEvMTiPIvJ5zc",
	"Sbf05YQV6u5xG+CzGJYdwBKWRjhZUC6mL09fnqqbrYbshqWW0YHb4CFo2VcqzwNXxqsO/qoWJPJYvReX",
	"c8yiDeG1BRmO1S0gfSiNtxqBTx25/45/34FSgQzGWTlnDzbpWQZjqk4DezApRRyKybkdZLGUh9AHIzEX",
	"gCyCN/L/wbPLaz3V/H+VI4Ol5d6TsWiuq9FttNNBtL0+OkhGamD1CrUV1zjDs1VcK75ROlhd2XHRjLMI",
	"+/0Zg+C9/n/wdJUhlfvRHHOQqafjeFTDKFlWRzjeUCsM2jTVdwVk1uJzwJoFZbFdMjn4LmZ4JkLMogVZ",
	"Qkiz/+A8Z3SJk5DBZ4gExKEegbg1AbXs7klB1VooRGY1WTRU66m0Xq8oQcyag9LAKnO2+9Ip9NVN+Obm",
	"gXnz1Q0K0ZubQYRUdYS/gLFVRGcN01tLtJ8b8C9rTg3sr8fhX1Z3OPawqnObyb+y8wGG3a/+SCVqFXTa",
	"1cu9ILmfMllbV4d+mwuF/69F+rc+oPPIXcVDvUjYiGZS7luFJ6w/8uS0AsqBMkqNi0D7u9h4gP2h/d2a",
	"Pd7iPKzXnKEbXF7TIlMf2vua6TS0+t6jc8czwVzoA6Gb3xYZ0G50+gJb7ybb3Mnmx92qZmShLmXq/Ujn",
	"qX9/6+boFsQqHo2k1tZWD7rx6wq5pHgj8lcEW8WwquL72swAHj+Be8oHdujg0Hv3lj4VzRR5euktwlip",
	"fb/a78yWdFZfnjWqeHCvFcLD6Mr39OO7OvFDevCH0H2/Xf8vAAD//++V7ZPTWQAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
