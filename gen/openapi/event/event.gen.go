// Package event provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package event

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for EventStatusV1DTO.
const (
	Default EventStatusV1DTO = "default"
	Error   EventStatusV1DTO = "error"
	Success EventStatusV1DTO = "success"
	Warning EventStatusV1DTO = "warning"
)

// EventUnreadResponseItemV1DTO defines model for EventUnreadResponseItemV1DTO.
type EventUnreadResponseItemV1DTO = []EventSourceResponseItemV1DTO

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// EventSourceResponseItemV1DTO defines model for EventSourceResponseItemV1DTO.
type EventSourceResponseItemV1DTO struct {
	Type      string                 `json:"type"`
	CreatedAt time.Time              `json:"createdAt"`
	Message   *string                `json:"message,omitempty"`
	Status    *EventStatusV1DTO      `json:"status,omitempty"`
	Meta      map[string]interface{} `json:"meta"`
}

// EventStatusV1DTO defines model for EventStatusV1DTO.
type EventStatusV1DTO string

// EventV1DTO defines model for EventV1DTO.
type EventV1DTO struct {
	Event map[string]interface{} `json:"event"`
}

// CreateEventJSONRequestBody defines body for CreateEvent for application/json ContentType.
type CreateEventJSONRequestBody = EventV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Create new event
	// (POST /v1/events)
	CreateEvent(c *gin.Context)
	// Get events source
	// (GET /v1/events/source)
	GetEventSource(c *gin.Context)
	// Get unread events
	// (GET /v1/events/unread)
	GetUnreadEvents(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// CreateEvent operation middleware
func (siw *ServerInterfaceWrapper) CreateEvent(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateEvent(c)
}

// GetEventSource operation middleware
func (siw *ServerInterfaceWrapper) GetEventSource(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetEventSource(c)
}

// GetUnreadEvents operation middleware
func (siw *ServerInterfaceWrapper) GetUnreadEvents(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUnreadEvents(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.POST(options.BaseURL+"/v1/events", wrapper.CreateEvent)
	router.GET(options.BaseURL+"/v1/events/source", wrapper.GetEventSource)
	router.GET(options.BaseURL+"/v1/events/unread", wrapper.GetUnreadEvents)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xa3XLbthJ+FQ7OuUhmaNFOnEyO7nwSJ+O2qT3+aS8yng5MriTEJMAAoFxVo3fv4Ick",
	"RBIUqVZpOpMrScBi98PiW+wC0BrFLMsZBSoFmq6RiBeQYf31nHPGr0HkjAr45eTd7aVqzTnLgUsCWiZm",
	"CahPucoBTZGQnNA5CtHvR4wnwNH0xSZEGQiB5/1yLzchYg+fIZYX73oFX1WCt1qkR/R0EyIhseyXem2l",
	"CuGIESphDtyVO9lsQsThS0E4JGj6qRwUGifU09zC58yqBHMfllZMF9qE6HwJVN6wgsdQevxCQubzOgcs",
	"ITmT6seM8QxLNEUJlnAkSaaMtmc7Z0e2UYlMbo3cXuuUgcSOUD2L2o04TS9naPppjf7LYYam6D9RzbPI",
	"kiwys9aDzEw3943lk7vWuLUq0ni9dpHF6/e6Y3+6RkCLTOlJYIaLVA0XRRyDUAsNKiJQiJ4wpwrHfdPR",
	"pc47ygEnnStJJGTaRbsd46FDY8JGYQ0Fc45X2y6yqDxsAtXXXs8+J5shbZcqDkBccCJXN2oqxsBZIReM",
	"kz+wJIyqhgfAHPj7krg//Hqr3KwHoKntrUm8kDJHG6Wb0BnTQIlMVc/Vx5vg7OoChWgJXGjlaHmiHMRy",
	"oDgnaIpeTo4npyhEOZYLjSZankQ4yQiN5pwVuWrbhK3WKMYS5owbH3UJrPXnxbtNsz8HnhGh4LSG5pwl",
	"RSy97dHafutSy1nOBE6Ft0OP1l87hnOWtqeiG3umavrX6qNDZSGAi87GaK0+toZ0GanborX9vvIM2hLo",
	"drEmpV7inAlN6AREzEluaIfe6i1BBDig8BRo6QnSVOGamRdJJaTDBRnOg5D/Z8nKZDsqbazgPE9JrMdF",
	"n4WhtYneQbFtI1mRehuk7g00CsJocG0AIDf8JC9Ax6PZGPSMXxwff1WAdk+cFWm6Cuxeq+Lu9O/E0a4/",
	"OvBc0CVOSRLYpQpsOtRYTtokuKPY7kYl4JdtofeMP5AkAaolXvyvLXHLWJBhuirNCiX56h+YvAROcRoI",
	"4EvggUlQehsusgzzVcXomvNqY8VzobZxQ/P7zVYARULnHQVwDh1h9AGkUSQCI9kOog8gnQyGDs1Vb6L0",
	"slcP2CbxHMwifmfwN8fgFuN2UrjQ9VcvhY2I1dtJYVPEnZuscmgO+yrGDpfduci/k/hfROItzvlJ3KxL",
	"/QXnI6zilOHHZinWWR+1i8/+srOrt4WtR6gLcKd4turFuyWbYy5JTHJM5RjRaO382o2ob+gwF/RqaNTi",
	"3Qra5X6/nKf6HzEoWhAhmWLrPoPr24M9Blc3B3uMXRJ42jFyiMN9Z53GWJ9YcwaNiDTHorjg3OxJW82e",
	"APAdpbbbW3xsdLfDviFQTdA5veurm8a5/dP95l5JqC1OaIGCp/Z8LqZRlD5OEjGBOH+cCPJAqITHCS/Q",
	"JvQLHiWw7BFWsizG6YIJOX1z/OZYXxDZXbOZCy7L/C0CDqk6kASSBcY5wcMq0MdTFCKK9S3Dmfr5QfVq",
	"o0N0OYvkU3hViQzXatfHq9L0j9GnQ+OZeN6jUosM1qkp4tN2zVIYrElzz6fpTgAfrMm5UKi0vLV3BIOV",
	"VKnYKjCZeOjoMv3W438sWwavlpvPKjVXdes+7HQU7c9HR8lIBtqYYzO/rnGBZ+jXp28UB+vU6qoZFxHV",
	"5b9VYG6Rhw9fCQlZOR/jMUeZ7h3no4ZG5bKmwvGBWmswoamv5+Uqh64N2LhALZNcQOXk4FnC8UyGmMcL",
	"soSQ0d9wnnO2xGnI4TPEEpLQtEDyXB3EiFL2pQC+qs1X7q7L9vKCfopQWN/aK1soRNaaOtbV9vQhzlhU",
	"ItZmxy1+20fvSSqBq32rmpcaEzy7vA7Pr72w7WPEDtCX1yhE59eDgFi+B/o9SZvMsVzUFquCpnVl6MKo",
	"3owIla9P66v28tmrw7Bmo9esrfR7jXY8rdWPUgZIh11FWq9ZU4UdwGqZSLyW69voA1hXwea1bMq2A1h1",
	"ko6fXu5B5hAYyujq4bct/A9j3QaX8AR0FV7be1H1qLczsBrPdD2B5oNgQ+1wAGzE+eybmDuceSf0fBDq",
	"4DscjFY2U+u+V3rCsTSpqNzvq4YyS43LQLpytgWIDxARZ7a+bsXEA2MpYNpvwyS3uBCSZQHjtrr4i3nO",
	"qFNyWtugaSdY4mCmEIDyls//ZWdrtn7NP5GMSL24tMgegKulBiqJJKAKIVlwColvwqka3D3jk+Nwz+3n",
	"cjYTsDckpkd3Y9ob0g3j0g0Ac3QLZgRSLxDBuAcGkhAvfsaZGwxOk5Nc3rJCv1SxJ2rK0PrPNs4fO1Is",
	"5F2e2N9D6FTNx7kX2Hs2dHsm9qeNBeswFOqEXc5HbZ7m+yi09fn7a4LVPhqJtjpbOWjHg3UXuUS8lflr",
	"wBUxKqrUg0f5uL6Z2Bf1rEjTBrudpjJpNThczmQU1sNH49emZ72gJRSl1KJTX2+3mToKn85mGp4xvUca",
	"K9n3sfqTX4mz/tufpeIQaJf6j1Wejdz2ddYSIgaauLVE2aD0q2/d1r+Fl8Adb4B9r39D3v2+hRe/+82f",
	"AQAA//8GGbmBUCsAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
