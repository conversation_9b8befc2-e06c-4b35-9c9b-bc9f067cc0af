// Package permission provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package permission

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// MethodV1DTO defines model for MethodV1DTO.
type MethodV1DTO struct {
	Name string `json:"name"`
}

// PermissionV1DTO defines model for PermissionV1DTO.
type PermissionV1DTO struct {
	Label   string        `json:"label"`
	Name    string        `json:"name"`
	Methods []MethodV1DTO `json:"methods"`
}

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get permissions
	// (GET /v1/permissions)
	GetPermissions(c *gin.Context)
	// Get user permissions
	// (GET /v1/products/{productID}/mypermissions)
	GetParticipantPermissions(c *gin.Context, productID int64)
	// Get user permissions
	// (GET /v1/users/mypermissions)
	GetUserPermissions(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetPermissions operation middleware
func (siw *ServerInterfaceWrapper) GetPermissions(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetPermissions(c)
}

// GetParticipantPermissions operation middleware
func (siw *ServerInterfaceWrapper) GetParticipantPermissions(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetParticipantPermissions(c, productID)
}

// GetUserPermissions operation middleware
func (siw *ServerInterfaceWrapper) GetUserPermissions(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUserPermissions(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/permissions", wrapper.GetPermissions)
	router.GET(options.BaseURL+"/v1/products/:productID/mypermissions", wrapper.GetParticipantPermissions)
	router.GET(options.BaseURL+"/v1/users/mypermissions", wrapper.GetUserPermissions)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xaW2/juBX+KwLbh1lAYyWZ7GDrt2CTWaTtNEGSaR8GQUFLxzYnEqklKc+6hv57wYsk",
	"WhJlyYVnU+w8WSLP5TvkuZHWDsUsyxkFKgWa75CI15Bh/XjDOeMPIHJGBfzz/PrpTo3mnOXAJQFNE7ME",
	"1K/c5oDmSEhO6AqF6Le3jCfA0fyiDFEGQuDVMN27MkRs8QVieXs9SPhjTfikSQZIL8sQCYnlMNV7S1UI",
	"h4xQCSvgLt15WYaIw68F4ZCg+eeKKTSL0Ji5h8+xqgLzHFZazBQqQ/QR5JolnkWmOBs2oQNNc/TpuQee",
	"ESEIox5dKV5AekCZMlXB1QxEQqYf/sxhieboT1HjUJH1psg1r6xhYc7xtu0EB429aBtrIFvOBlvXfLXR",
	"EBecyO2jAmYsvirkmnHyHywJo2pgAZgD/8B4hiWao7/+60ltnWZAczuLauFrKXNUKtmELplGTmSqZu4/",
	"PgZX97coRBvgQgtHm3NlP8uB4pygOXo3O5tdohDlWK41mmhzHuEkIzRacVbkaqwMO6NRjCWsGDeb1kew",
	"07+312V7Pq89oMOac5YUsfSORzv71CeWs5wJnArvhObWjz3snKVdU/TggKlmfqd+ekQWArjoHYx26meP",
	"pU9JMxbt7PPWw7RH0L/EsLEpdu89EqzgMXSGC8oBJ81w2xn8u/wC2zhl+KVt/z4otAKpfhIQMSe5cX30",
	"C0gR4DQN8AaTFC9SCBy2GdKey3Wg3CaG/t4Rq8LSVAut4uLszJQIKoFqbTjPUxJr/uiLMNFmUsToTNJO",
	"YO1soiNx36rHIo5BiGWRpttgxaRrU5ASoRPj5USwQxh7CmcPrFu6wSlJApXKQMhgaRKOxnLe3ZtPFNtE",
	"BYkhetcl+sD4giQJUE1x8ZcuxRNjQYbptlIrFOWPv4PxEjjFaSCAb4AHoLhMhi6yDPOt8S53q1TKxSuh",
	"Mn7jBOi5rPy7k7yG01bfbCfMBoj6Yq+XPNuOCb0HkAWnIpDrvZgL2FIPqWAOvhK51m8ih5gsCSTB7XV/",
	"VGIuSUxyTFsBmmOOM5A6M3xuY7g3oAPdqxA1ospSVVnnqDYKuQVY8gJCxzmWVeEkVL6/bApl1VKV5fMr",
	"TRTOUgXCTRocJCewqQLve6Z4hZlCR8j0dLEXqnkTNoeSgEsa7Zy3wwlhiHVcBhqU0Gql+gV0u7VhOk/z",
	"NoEpWhMhmdquY5ibI+QRzPXp7gjeDYGvBzjHLLivVW3x+sjaFrR6O9PVxgXnJij3hn+P+vNJAP8/6Ay/",
	"J/w/TsL3nfz2xzv5tzXd7TJbBHVAO5cNutVqXTN8flZtkDHR9GIFT+11gphHUfoyS8QM4vxlJshC9U4v",
	"M16gMvQTvk1gM0CsaFmM0zUTcv7T2U9nSCGwS9b2hbsqokXAIcUSkkCywCxOsNgG+jTddIZX6vUXNauV",
	"jpHlJhmPQGcbR0u1++MVaeanyNOl4I34YUCkJhktU7uIT9oDS2G0JO17PkkqB4+W5Nx/1FJ+tlcao4XY",
	"K45awI16H81dXVw0/H+rRkbvltu/1WKcw9Ax3ukIOt4fHSETPdDGHFv6ZU0LPON+Q/Im+WDTSrpipkVE",
	"fZNuBTya99HsWyEhq+wxK+YI07PT1qglUS1ZW+D0QG0kmNB8DtFvb1WL0peAzRJU7Ve1yMGbhOOlDDGP",
	"12QDIaP/xnnO2QanIYcvEEtIQjMCyQ+z6hj/awF826ivl7sp2wkscZGqUzsKEdAiU2VU60IhstpUo9fo",
	"0z2d0ahIrE7n1t3e3JfdNfpAUglc5a3aLsUTvLl7CG8evLCl+T/lAOi7BxSim4dRQL7VhUdHsfZGr1p7",
	"sTSotOd/qhV724y+v+zTq5zWq9acOk6gtSokXs3N5fkJtKtg82o2bdsJtDpFx+9e7sH9FBiq6Brwb3vQ",
	"PY12G1zCE9B1eO3novq0dzCwOic8b6D5INhQOx0AG3E+/SbmTqfeCT0fhCb4TgejU83Uvh9VnnAsTSmq",
	"8n09UFWpaRVId862AfEBIuLK9tedmFgwlgKmwzpMcYsLIVkWMG67i/+xzhlxik5LG2V2giUOlgoBqNXy",
	"rX812bHWL/nvJCNSby4tsgVwtdVAJZEEVCMkC04h8RmcKuZ+i8/PwiPTz91yKeBoSExz92M6GtIj49IN",
	"AHN0C5YEUi8QwbgHBpIQr/9hvnmonMIZcorLz6ygSgj7Sk0b2ny5wkG1qFdqNsVCfsoT+z7GnWp7nHuB",
	"o62h+5bYVxsLdsFQqAt2ZY9KnuZ5Etrm/P0tweo1moi2Pls5aKeDdTe5QrxX+RvAtWPUrtIwT1rj5mbi",
	"WNTLIk1b3u0MVUWr5cOVJZOwnj4av7V7NhtaQVFCLTr1+LTvqZPw6Wqm4RnVR5Sxyvs+1l/MVTibb+is",
	"K46Bdqc/DPMkcjvX20uIGGji9hLVgJKvnvq1v4Z/Ag78BzB0+z/m3v813Pg/l/8NAAD//zpWZAidKgAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
