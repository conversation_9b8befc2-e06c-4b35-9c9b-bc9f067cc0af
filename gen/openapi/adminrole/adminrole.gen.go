// Package adminrole provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package adminrole

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for AdminRoleCreateV1DTOType.
const (
	AdminRoleCreateV1DTOTypeCustom AdminRoleCreateV1DTOType = "custom"
	AdminRoleCreateV1DTOTypeSystem AdminRoleCreateV1DTOType = "system"
)

// Defines values for GetRolesAsAdminParamsType.
const (
	GetRolesAsAdminParamsTypeCustom GetRolesAsAdminParamsType = "custom"
	GetRolesAsAdminParamsTypeSystem GetRolesAsAdminParamsType = "system"
)

// Defines values for GetRolesAsAdminParamsStatus.
const (
	Active  GetRolesAsAdminParamsStatus = "active"
	Archive GetRolesAsAdminParamsStatus = "archive"
)

// Defines values for GetRolesAsAdminParamsSort.
const (
	GroupCount GetRolesAsAdminParamsSort = "groupCount"
	Name       GetRolesAsAdminParamsSort = "name"
	Product    GetRolesAsAdminParamsSort = "product"
	Type       GetRolesAsAdminParamsSort = "type"
	UserCount  GetRolesAsAdminParamsSort = "userCount"
)

// Defines values for GetRolesAsAdminParamsOrder.
const (
	Ascend  GetRolesAsAdminParamsOrder = "ascend"
	Descend GetRolesAsAdminParamsOrder = "descend"
)

// AdminRoleCollectionV1DTO defines model for AdminRoleCollectionV1DTO.
type AdminRoleCollectionV1DTO struct {
	Items []AdminRoleV1DTO `json:"items"`
	Meta  PaginationV1DTO  `json:"meta"`
}

// AdminRoleCreateV1DTO defines model for AdminRoleCreateV1DTO.
type AdminRoleCreateV1DTO struct {
	Type        AdminRoleCreateV1DTOType `json:"type"`
	Name        string                   `json:"name"`
	ProductID   *int64                   `json:"productID,omitempty"`
	Permissions []PermissionV1DTO        `json:"permissions"`
}

// AdminRoleCreateV1DTOType defines model for AdminRoleCreateV1DTO.Type.
type AdminRoleCreateV1DTOType string

// AdminRoleUpdateV1DTO defines model for AdminRoleUpdateV1DTO.
type AdminRoleUpdateV1DTO struct {
	Name        *string                 `json:"name,omitempty"`
	IsActive    *bool                   `json:"isActive,omitempty"`
	Users       *[]UserProductLinkV1DTO `json:"users,omitempty"`
	Groups      *[]GroupIDV1DTO         `json:"groups,omitempty"`
	Permissions *[]PermissionV1DTO      `json:"permissions,omitempty"`
}

// AdminRoleV1DTO defines model for AdminRoleV1DTO.
type AdminRoleV1DTO struct {
	ID         int64              `json:"id"`
	Name       string             `json:"name"`
	Type       string             `json:"type"`
	IsActive   bool               `json:"isActive"`
	Product    *ProductBasicV1DTO `json:"product,omitempty"`
	UserCount  int64              `json:"userCount"`
	GroupCount int64              `json:"groupCount"`
}

// CategoryWithCheckedV1DTO defines model for CategoryWithCheckedV1DTO.
type CategoryWithCheckedV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	IsActive bool   `json:"isActive"`
}

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupBasicV1DTO defines model for GroupBasicV1DTO.
type GroupBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// GroupIDV1DTO defines model for GroupIDV1DTO.
type GroupIDV1DTO struct {
	ID int64 `json:"id"`
}

// GroupWithProductV1DTO defines model for GroupWithProductV1DTO.
type GroupWithProductV1DTO struct {
	ID       int64              `json:"id"`
	Name     string             `json:"name"`
	Type     string             `json:"type"`
	IsActive bool               `json:"isActive"`
	Product  *ProductBasicV1DTO `json:"product,omitempty"`
}

// MethodV1DTO defines model for MethodV1DTO.
type MethodV1DTO struct {
	Name string `json:"name"`
}

// PaginationV1DTO defines model for PaginationV1DTO.
type PaginationV1DTO struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
	Total  int64 `json:"total"`
}

// PermissionV1DTO defines model for PermissionV1DTO.
type PermissionV1DTO struct {
	Label   string        `json:"label"`
	Name    string        `json:"name"`
	Methods []MethodV1DTO `json:"methods"`
}

// ProductBasicV1DTO defines model for ProductBasicV1DTO.
type ProductBasicV1DTO struct {
	ID       int64   `json:"id"`
	IID      *string `json:"iid,omitempty"`
	TechName string  `json:"techName"`
	Name     string  `json:"name"`
}

// RoleBasicV1DTO defines model for RoleBasicV1DTO.
type RoleBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// RoleViewAdminEntityV1DTO defines model for RoleViewAdminEntityV1DTO.
type RoleViewAdminEntityV1DTO struct {
	ID               int64                      `json:"id"`
	Name             string                     `json:"name"`
	Type             string                     `json:"type"`
	GroupCount       int64                      `json:"groupCount"`
	ParticipantCount int64                      `json:"participantCount"`
	UserCount        int64                      `json:"userCount"`
	Categories       []CategoryWithCheckedV1DTO `json:"categories"`
}

// RoleWithDetailsV1DTO defines model for RoleWithDetailsV1DTO.
type RoleWithDetailsV1DTO struct {
	ID          int64                   `json:"id"`
	Name        string                  `json:"name"`
	Type        string                  `json:"type"`
	IsActive    bool                    `json:"isActive"`
	CreatedAt   time.Time               `json:"createdAt"`
	Product     *ProductBasicV1DTO      `json:"product,omitempty"`
	Users       []UserWithProductV1DTO  `json:"users"`
	Groups      []GroupWithProductV1DTO `json:"groups"`
	Permissions []PermissionV1DTO       `json:"permissions"`
}

// UserBasicV1DTO defines model for UserBasicV1DTO.
type UserBasicV1DTO struct {
	ID       int64  `json:"id"`
	Email    string `json:"email"`
	FullName string `json:"fullName"`
}

// UserProductLinkV1DTO defines model for UserProductLinkV1DTO.
type UserProductLinkV1DTO struct {
	ID        int64  `json:"id"`
	ProductID *int64 `json:"productID,omitempty"`
}

// UserWithProductV1DTO defines model for UserWithProductV1DTO.
type UserWithProductV1DTO struct {
	ID       int64              `json:"id"`
	Email    string             `json:"email"`
	FullName string             `json:"fullName"`
	Product  *ProductBasicV1DTO `json:"product,omitempty"`
}

// GetRolesAsAdminParams defines parameters for GetRolesAsAdmin.
type GetRolesAsAdminParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`

	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`

	// Type Filter by type (custom or system).
	Type *GetRolesAsAdminParamsType `form:"type,omitempty" json:"type,omitempty"`

	// Status Status of the product.
	Status *GetRolesAsAdminParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Limit Limit the number of entities returned.
	Limit *int64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Offset the number of entities returned.
	Offset *int64 `form:"offset,omitempty" json:"offset,omitempty"`

	// Sort Sort the roles by a field.
	Sort *GetRolesAsAdminParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetRolesAsAdminParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetRolesAsAdminParamsType defines parameters for GetRolesAsAdmin.
type GetRolesAsAdminParamsType string

// GetRolesAsAdminParamsStatus defines parameters for GetRolesAsAdmin.
type GetRolesAsAdminParamsStatus string

// GetRolesAsAdminParamsSort defines parameters for GetRolesAsAdmin.
type GetRolesAsAdminParamsSort string

// GetRolesAsAdminParamsOrder defines parameters for GetRolesAsAdmin.
type GetRolesAsAdminParamsOrder string

// UpdateCategoriesVisibilityForRolesAsAdminJSONBody defines parameters for UpdateCategoriesVisibilityForRolesAsAdmin.
type UpdateCategoriesVisibilityForRolesAsAdminJSONBody = []RoleViewAdminEntityV1DTO

// CreateRoleAsAdminJSONRequestBody defines body for CreateRoleAsAdmin for application/json ContentType.
type CreateRoleAsAdminJSONRequestBody = AdminRoleCreateV1DTO

// UpdateCategoriesVisibilityForRolesAsAdminJSONRequestBody defines body for UpdateCategoriesVisibilityForRolesAsAdmin for application/json ContentType.
type UpdateCategoriesVisibilityForRolesAsAdminJSONRequestBody = UpdateCategoriesVisibilityForRolesAsAdminJSONBody

// UpdateRoleAsAdminJSONRequestBody defines body for UpdateRoleAsAdmin for application/json ContentType.
type UpdateRoleAsAdminJSONRequestBody = AdminRoleUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all roles as admin
	// (GET /v1/admin/roles)
	GetRolesAsAdmin(c *gin.Context, params GetRolesAsAdminParams)
	// Create role as admin
	// (POST /v1/admin/roles)
	CreateRoleAsAdmin(c *gin.Context)
	// Get roles with categories visibility as admin
	// (GET /v1/admin/roles/categories)
	GetRolesWithCategoriesVisibilityAsAdmin(c *gin.Context)
	// Update categories visibility for roles as admin
	// (PATCH /v1/admin/roles/categories)
	UpdateCategoriesVisibilityForRolesAsAdmin(c *gin.Context)
	// Delete role as admin
	// (DELETE /v1/admin/roles/{roleID})
	DeleteRoleAsAdmin(c *gin.Context, roleID int64)
	// Get role by id as admin
	// (GET /v1/admin/roles/{roleID})
	GetRoleAsAdmin(c *gin.Context, roleID int64)
	// Update role as admin
	// (PATCH /v1/admin/roles/{roleID})
	UpdateRoleAsAdmin(c *gin.Context, roleID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetRolesAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetRolesAsAdmin(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetRolesAsAdminParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", c.Request.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter type: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRolesAsAdmin(c, params)
}

// CreateRoleAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) CreateRoleAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateRoleAsAdmin(c)
}

// GetRolesWithCategoriesVisibilityAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetRolesWithCategoriesVisibilityAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRolesWithCategoriesVisibilityAsAdmin(c)
}

// UpdateCategoriesVisibilityForRolesAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateCategoriesVisibilityForRolesAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateCategoriesVisibilityForRolesAsAdmin(c)
}

// DeleteRoleAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) DeleteRoleAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteRoleAsAdmin(c, roleID)
}

// GetRoleAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetRoleAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRoleAsAdmin(c, roleID)
}

// UpdateRoleAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateRoleAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateRoleAsAdmin(c, roleID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/admin/roles", wrapper.GetRolesAsAdmin)
	router.POST(options.BaseURL+"/v1/admin/roles", wrapper.CreateRoleAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/roles/categories", wrapper.GetRolesWithCategoriesVisibilityAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/roles/categories", wrapper.UpdateCategoriesVisibilityForRolesAsAdmin)
	router.DELETE(options.BaseURL+"/v1/admin/roles/:roleID", wrapper.DeleteRoleAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/roles/:roleID", wrapper.GetRoleAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/roles/:roleID", wrapper.UpdateRoleAsAdmin)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xcW2/bOBb+KwJ3H1pAjePepuO3TNMOsjvdBEnaeSiCBSPRNhtJ1JCUu97A/33Ai0RK",
	"ImVJjVoXyFMd+vDw4+G5kse9BxFJc5KhjDOwuAcsWqMUyo8ncYqzS5KgtyRJUMQxyT7NT6/PxXc5JTmi",
	"HCNJiTlK6x/+SdESLMA/Zob7TLOeVXwVt10I+DZHYAEgpXALQvC/Z4TGiILFfBeCFHEoOMIkOV+Cxedu",
	"3hdwhTNooO5uLHbPd7sQUPRXgSmKweKzhqvXuKlwkNsvKOICmBEBRZAjz/YzmCLxr57OOMXZCtQWDkGO",
	"aIoZwyTrL6mLas5eUb0US1ASFxE/O7XA4IyjFaKSdEWemdHXL+3pLyrW9wBlRSqkw7aMoxSEICoYJ6kl",
	"oPYO503RSspQiaa++U45f8xjv5xXlBR5f+n9LsjPTnuJDrOTiOONfYy3hCQIZs1z3HvY8+kP+9UuBAVD",
	"tD/zjwzRC6Uef+Dsbu8KL3a7rmOqDqifVYopv0GGo3Jd58m+JUXGh6vuL0bzB/gJNcEGdeMQ8EhIr5vW",
	"YHiF9l7bpnCzC8FbyNGK0O2fmK/frlF0h2Kf34392JSegrPTfWDnvdX/RR/1bzvZ2PiBah2XE3hHKaGX",
	"iOUkYz4XEJF4v6tNEWNw1U0n9qKWrrnLNuGrivBa+0cvqXAkjEPeTfVaUxXMc3g+l6onhUoIZps1fNau",
	"SjAuWUvfaGn/j1atl/1UywSpjoPt0D8dlTrVsBY2ppJLG6MXinAD2l0NdLvNQ2773Yf2m28cYUO4tA+I",
	"r0k8Nn1qiUvOcAmsmf611kpwikc4dKHIZLlkaMRcqbWEw2RMVtbYuIJfYSkZO0XRSCfaooC3KNmbyaTy",
	"5PonGvZJd+YXY0KJglxZc4nNuf2Wok7o4mqcrH1YjDSnIYmk8IkcRev/7CN0u7xqaui3l0Zi9hgFapL5",
	"hNFXmfK+yzjmW19KotK1ZincZSTeDK/DYkSa+y1psqxyHuo4+1W8kHIc4RxmfCTmV/2O+mFzdYemWHJ3",
	"bCuspfeWOvj0Shz7KeIQJ+yhC6lI3lPEJ1IQS0JTyMECiJr6GcdmS00vpQcFydG1ojPn/arUvIGldytx",
	"6brpeTN91Tz/dYJKcf56VC0+SDa/NFXUnHK5dnVC++5aRDYmAHR5fZRCnOw172WRJD1CUzhdyhxqpBYW",
	"l8k57z6mC3U9buE0v4uKcm8K2a9ecGpWb01vqMX01cJ87iwXRH2MooJivr0SjNTiJwVfE4r/L7N7MXCL",
	"IEX0fenj/vXntah45QSRXchvjb9bc56DneCNsyWRR4N5Ik/hw1VwcnEGQrBBlEnmYDMX4iQ5ymCOwQK8",
	"ODo+eil9P19LNLPNfAZFajCrPOMubI3OarmBi+B+parNXfP7ui+sf6Wk6h2f3VcquHPQ5ITBhHm/kLPl",
	"R8d0ShJ9YacqoRixiOJcHQm4RLygGQtgkgSSMoAskBOPgJQmlYd3FoMF+B1xEczYCZMZloqrMEVcetLP",
	"TdYx5DBYEhowBGm0FtmbGP6rQHRbhusFqL5UCtj2TDuh1HXOWkeDs1Pm4VpJk9U4V76+CrOl3TaMvuna",
	"HSDe44QjGtxuA0EZPFE37oHYr7yEf3rkwaYTFIMqRktYJAKNcI/6Jl9f4IfllX7rJt8B6UreNgVkGfA1",
	"CrQMfDCqqykXEKiybAOnGhCnVU/A/Xj+EKWvxJIV6S2iAhkSeTlGLKBS81Dsw1fWzQ548+OwT8boQHQu",
	"i/CxkKoS3oFpNKQrQhUgZX632wAGS4wSLwpGqAdDmQKXh1bPiMtQEO6/2vYf6bkMAx7x6O+cCsUilMW2",
	"QpUDgr/45Fj9RkRQdbcsrfb58bG6UM44UnUDzPMER9JFzb4wFWTM8r1eNJsvpTLgNJykPBlWRBFiTOQt",
	"W6EqFKMNioWrePmAuBw36g5EZ9kGJjgORIKBGA+0P5NY5m0v/zGDOhSXgF+0id4TeovjGGWS4vmvbYpr",
	"QoIUZttyWSYoX/2AzXNEM5gEDNENogESs4DMQYo0hXSrYpUjpgljgCsRrMy7GBDpdU6YIziqF2TJoyMs",
	"KirBygRGLaDfSLydQGOth22PtgaSBpMsuFRIgJ2Mclqg3YS25SyYfUh1YVQzr0ejOkijctmDx6TaKWg9",
	"sXZno8JqlcV+xXwdmBnBBjN8ixPMtz0SVHlHV839VE217fMbVL/XjYH3MrKdWnriDcxikRdgasvhMQj9",
	"ZEGotzr7YxPk0dohTtl242Eqyq595Zxi4LKT94Q2Cr2x8WwCU3nYMDa5LZvLzobxFlL8j6Z7mKY70Lr6",
	"R8F78Y+6pgExSpDqPKkDPJXjbF/iqcjqiWfnjYzMt+TVpSzfcsjXpnpTuFp5ouNiprvA9RRtjaLXtgQl",
	"hlhu99EcDtIclKr1yvzC7tQuuN0GON6fxP0EKv1da6WaxawIf7SWw8/7Gro+LsPbGwYU2UHazISXIHbX",
	"ue9qQQfxQ74CuXKkhI+mffB54eArkLLdoD04uxf/1B7uXE+QZmx2rz9vPZNqBO5XSbTRPx6q/T1jpKAR",
	"ag0XGUUwNsPN91P/w+gd2kYJgXfN/TtBtR9Ju59HXd+2sHUQuQA7ydNtJ94ardVqNIR0dm/9tR9R19R+",
	"IujkUL4ZdzJoP0t303leqQdMmq0x40TY4ZjJ5icGIyZX3f8j5m4w+rpnZh+Bt+pHTdyY6yNr7qBhkcoX",
	"RQWlytvWhj0G4PNf9fGWPja+bpt9g6DaoNVlItOaRn/J5xuRdSjnrfKegia6j4QtZrPk7ihmRyjK745E",
	"KZ9xdHdEC9k04yN8FqNNB7GgJRFM1oTxxZvjN8eyWUYHg9bTaZm1sYCiRAZ6TgIlHPnsq2OJTsRkKJFN",
	"ecDxDOvkZR2Sj6G5jurPVZ+Pl6V+Vx7AT5rGE/a0g6Uk6c3TvJ07uF2qdKofJ6l7Pk4fmewN6cfJiuIV",
	"l7KLuDcTHagrBu/E371nl+HXzP93OdL7tOx4VrG5MKNjtNNiNF4fLSYDNVDbHFn6eQ0zPKV+XfwG6aAJ",
	"rTabYRZRtfdoBqo3qP902XVU7kdJzGImvx0mowZHWeM0GA43VMNBmaZsVRSVqMsBt9qjpESDJzGFSx7q",
	"xqaQZP+FeU7JBiYhRV9QxFEcqhEUPx3VTWW1vci1rDaqEFjryQpVrShI9Jq9+nJMN1q1L9WWdn4Zvrv8",
	"xl6080sQgneXvYCY3jz3/UJuNdD6rxj29ue1F5ba6F1WZ/rffq8R/pDblNaqZSDxrmxKwAlWF8bmXVml",
	"bROsagUdv3rZhcwUGErr6tBvnfhPs/qPb3wtDc0HQZvadAC0xfnWVzY33fKW6fkgGOObDsahNfuaCCQz",
	"Z52A+ADh6qq6ZRPljwx/jp7r6brcH7unh3RP16rU0Q3U1g9wS6Wwhhw/IyRfM5WGmv/pwfqhVwIZVzfG",
	"4u9eLfzlfqx7ge/ZDk5lR3TfbvCfq3ddoq1qKwvtcLD2IZeIa5HfAK4Uo1IVM3mQjM3NxFjU1U/uDGhr",
	"qAxaDR0udzII6/TW+L3V0xxoCUUw1ejEx+u6pg7CJ6OZhKeWHhHGSu37UP0PMyVO83/OaFU8uN95hIfx",
	"xrnndbPrXbPPi+YhvGXe7P4OAAD///KZYbA9UAAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
