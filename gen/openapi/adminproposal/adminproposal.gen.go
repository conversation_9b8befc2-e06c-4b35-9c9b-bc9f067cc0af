// Package adminproposal provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package adminproposal

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for GetProposalsAsAdminParamsType.
const (
	ER GetProposalsAsAdminParamsType = "ER"
	OR GetProposalsAsAdminParamsType = "OR"
)

// Defines values for GetProposalsAsAdminParamsStatus.
const (
	Approved   GetProposalsAsAdminParamsStatus = "approved"
	Archive    GetProposalsAsAdminParamsStatus = "archive"
	Draft      GetProposalsAsAdminParamsStatus = "draft"
	OnApproval GetProposalsAsAdminParamsStatus = "on_approval"
	Rejected   GetProposalsAsAdminParamsStatus = "rejected"
)

// Defines values for GetProposalsAsAdminParamsSort.
const (
	CreatedAt  GetProposalsAsAdminParamsSort = "createdAt"
	Owners     GetProposalsAsAdminParamsSort = "owners"
	Product    GetProposalsAsAdminParamsSort = "product"
	ProposalID GetProposalsAsAdminParamsSort = "proposalID"
	Status     GetProposalsAsAdminParamsSort = "status"
)

// Defines values for GetProposalsAsAdminParamsOrder.
const (
	Ascend  GetProposalsAsAdminParamsOrder = "ascend"
	Descend GetProposalsAsAdminParamsOrder = "descend"
)

// AdminProposalCollectionV1DTO defines model for AdminProposalCollectionV1DTO.
type AdminProposalCollectionV1DTO struct {
	Items []AdminProposalV1DTO `json:"items"`
	Meta  PaginationV1DTO      `json:"meta"`
}

// AdminProposalV1DTO defines model for AdminProposalV1DTO.
type AdminProposalV1DTO struct {
	ID             int64                  `json:"id"`
	PropSeq        int64                  `json:"propSeq"`
	ProductID      int64                  `json:"productID"`
	Price          float64                `json:"price"`
	Type           string                 `json:"type"`
	Status         string                 `json:"status"`
	CreatorID      int64                  `json:"creatorID"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
	ProposalNumber string                 `json:"proposalNumber"`
	Data           map[string]interface{} `json:"data"`
	ProductOwners  *[]string              `json:"productOwners,omitempty"`
	Product        *ProductBasicV1DTO     `json:"product,omitempty"`
}

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// PaginationV1DTO defines model for PaginationV1DTO.
type PaginationV1DTO struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
	Total  int64 `json:"total"`
}

// ProductBasicV1DTO defines model for ProductBasicV1DTO.
type ProductBasicV1DTO struct {
	ID       int64   `json:"id"`
	IID      *string `json:"iid,omitempty"`
	TechName string  `json:"techName"`
	Name     string  `json:"name"`
}

// ProposalV1DTO defines model for ProposalV1DTO.
type ProposalV1DTO struct {
	ID             int64                  `json:"id"`
	PropSeq        int64                  `json:"propSeq"`
	ProductID      int64                  `json:"productID"`
	Price          float64                `json:"price"`
	Type           string                 `json:"type"`
	Status         string                 `json:"status"`
	CreatorID      int64                  `json:"creatorID"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
	ProposalNumber *string                `json:"proposalNumber,omitempty"`
	Data           map[string]interface{} `json:"data"`
}

// GetProposalsAsAdminParams defines parameters for GetProposalsAsAdmin.
type GetProposalsAsAdminParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`

	// Type Filter by proposal type (OR,ER).
	Type *GetProposalsAsAdminParamsType `form:"type,omitempty" json:"type,omitempty"`

	// Status Status of the proposal (draft,archive,on_approval,rejected,approved).
	Status *GetProposalsAsAdminParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`

	// Sort Sort proposals by a field
	Sort *GetProposalsAsAdminParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetProposalsAsAdminParamsOrder `form:"order,omitempty" json:"order,omitempty"`

	// Limit Limit the number of entities returned.
	Limit *int64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Offset the number of entities returned.
	Offset *int64 `form:"offset,omitempty" json:"offset,omitempty"`
}

// GetProposalsAsAdminParamsType defines parameters for GetProposalsAsAdmin.
type GetProposalsAsAdminParamsType string

// GetProposalsAsAdminParamsStatus defines parameters for GetProposalsAsAdmin.
type GetProposalsAsAdminParamsStatus string

// GetProposalsAsAdminParamsSort defines parameters for GetProposalsAsAdmin.
type GetProposalsAsAdminParamsSort string

// GetProposalsAsAdminParamsOrder defines parameters for GetProposalsAsAdmin.
type GetProposalsAsAdminParamsOrder string

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all proposals by filters as admin
	// (GET /v1/admin/proposals)
	GetProposalsAsAdmin(c *gin.Context, params GetProposalsAsAdminParams)
	// Get proposal by proposalID as admin
	// (GET /v1/admin/proposals/{proposalID})
	GetProposalAsAdmin(c *gin.Context, proposalID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetProposalsAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetProposalsAsAdmin(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetProposalsAsAdminParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", c.Request.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter type: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProposalsAsAdmin(c, params)
}

// GetProposalAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetProposalAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProposalAsAdmin(c, proposalID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/admin/proposals", wrapper.GetProposalsAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/proposals/:proposalID", wrapper.GetProposalAsAdmin)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xaW3PcthX+Kxy0D84MrdXFSZ19Uy07o7aJNLLcPng0HYg8KyEiCRoAN93u8L93DgCS",
	"4AVccuN15I6flgsefOfDwbngwi2JeJrzDDIlyXJLZPQIKdWP53HKsmvBcy5p8oYnCUSK8eyfJxe3V/g+",
	"FzwHoRhoaaYgbT/8WcCKLMmfFo2GhYVftLANYhkStcmBLAkVgm5ISP7zkosYBFmelCFJQVFEpUlytSLL",
	"j+P41/SBZbShW945cKdlGRIBnwomICbLj5ay1XFX8+D3v0KkkNgA3Z4BIgFUQXyu8M+Ki5QqsiQxVfBS",
	"sRRIDSuVYNmDHt8Df2kbUeTo1sjVRF+XoYHl4vICYa0wyxQ8gOhAsEz98Mrt/pcyRP3U6WmH5NoWjcti",
	"P3pGU2y9vNilDoFywSLYyXSVcNrp/Ep35nERqRmzbDr8lUoWDczzyVkDOmY/O8LrWnLHQB3Yq98yEG23",
	"b8/ymFefGpz8PXyaQk7L7aBWQaKf/lKk99i2HXC8isJxGRKpqCrkqNwP9TBGhL4vQ1Lk8ecOgh970RqT",
	"xm7uBFfuZ5XUI3ODKHTi1KXbM5sNnaFs8FYILm5A5jyT4MsGPB631qnOaVLSh3E5dDajuuXCw+Y3gre7",
	"ZuqVnXXYOek933C9s477zgQ1ZkcjNMNs8XNGVZEZsnU3kfcMnbCUqfm5EZMVX60k7NEXp05xRZP5Xc+6",
	"tjL0ay4V8KApetmuX4Q/Vx5nLaR2sFZAFskxinmzw/EURI+/7BI8Gwz6uqtV5bHStyr9Bar0AQrqt0L4",
	"LAqhp+6hgSAqBFOb97j+MvF0XqhHLth/dYrGhnugAsS7asR/+9ct6tcdyNK+bUb/qFROSsRm2YprqzKV",
	"6Hn++X1wfn1JQrIGITU4WZ9ghPMcMpozsiRnR8dHOOs5VY+azWJ9sqC4Vl88CF7k2FaGvdZFRBU8cGFS",
	"wpDAVv9eXpTd9zmIlEmk0+tqze9tX2zrCSoHZLSn6kE8mJoUg4wEy41dyQ2oQmQyoEkS1NLB/SZYsUSB",
	"kAGVgcYi2kBCz8dlTJbkJ1BVTpTn8tzK5FTQFJRevH7sKkMPCFZcBBKoiB4Jzg5Zkk8FiE2Ve5ekfmmW",
	"4wNr3zLsIr/TbJF3NYgA+wQvrm7CtzffHXlUVW5cK4phRYsEHYyEBLIixXi4uiEheXvj+K6fyHsdEQFf",
	"BeoRGjIvYkFXKsSBsTWEPPs3zXPB1zQJBWAcQByaFoi9bOto28FX6yIhsdpw5hp9BGPdaEQRq3PS2Gze",
	"DS4vpIdh7YltlvUepk5YVV7tpOTurmbIwFyotqfSYMUgiX1G40J5TOYmqsp2FbLNdmbj6CQ6bjZmbpab",
	"YrornWqHGXL7bogilRFkscOvbkB8fJqi/R+4GNQOmemShe4JmWK4eAmEzgAQ+7yuWkkO0Ds5DqcsSobs",
	"oZel+1KqF7UDnKZSusM4MFst7Zqnx8dmf5UpyMxZQZ4nLNIJb/GrNFWoUTj5FKp7wqWrUi+wrDvLIopA",
	"ylWRJBs0g2CA0VmG5NVn5Dew0RxgdZmtacLiAJcGIFVgg1dzOelXkg8ZtTW7InzWF3rHxT2LY8i0xOmP",
	"fYlbzoOUZptKrUTJ7/+AwSsQGU0CCWINIgDsZRYrRZpSsTEVcFLdVPQBq2H7tI/clcOlWtdzm4PKHXW7",
	"KTBO5bu8mFS0J9bsSjzQGVFHIy6LWjm/yZjNMlKJAgZK+B8XlJ1TYX8UfgvCry0Id0fBpBAUPOmv3HXj",
	"yMrevN/iz8ACvJDmGLffuNjiT6vLkJKmbbG1zxtPp5bA8I4C1vY+pvV/IXkh9Ma93VxkAmjcNHf3Pv5N",
	"zRNsooTTp+74B0n1NzjjW5uhtz1uI0JDhAfF080o35ZsToViEctppuaILrbOv92MxrpOM8EoQicAhgGc",
	"LeUkuW49m99p8cik4hjt+3RuDsP36Fwf3ezRd83gtx09pxjcl2A6fX1i3RF0ItLkoqgQwuT0VrMnAHz5",
	"q93e88fO637YdwTqATonRHqB0jkb+niH6wZTIswKphCJPQOSy8UieTqK5RFE+dORZPe4AHk6EgXBTYlP",
	"8GUM6xFhlOURTR65VMvXx6+P9f2grTG9rU61AMN9TYJ7xkDxwBhHb19tibLLKV2hfsK3ZGDbNIjlTJIP",
	"8LoWmY5q58cLaTfHM/B0aLyQ341AmsI8FVO7iA/thicwGUn7ng/pg9RnE9OQnCpeo7yxhXkyiC3UNcBb",
	"/D+5d1V+m/5/r1omz5Zbz2qY66Z1H+90gPb3RwdkpgfamOMrP9a8wDPuN4Y3yweb0urCzIuI+qDKApjz",
	"yOndN1JBWo3HWMwB02/n2aiDiCbrAs4P1AbBhKb+OgP3kkMJ+P/5SPbZnHs3Z8PeU4L6Esl/SLDzfLiv",
	"WHujV61d6f/+k4meXnRar1qzCjuA1qqQeDU3W8ADaMdg82o2y7YDaHWKjt+93I3MITh8yVOw53nxUgWa",
	"j4INtcMRsBHn029i7nDqndDzUWiC74D3X91qhvO+V3mikTKlqL5bqhqqKjWvAumVs12A+Aix+tC5FxP3",
	"nCdAs3EdprhFhVQ8Dbiwq4vfWecMHMpptEnDPtxV9rfLugmU9C2wEwDuRfDR/Jtg5wOwyimcJqe4vOFF",
	"ptxr4PYXMPYyOaFSfai/fJn02UA1HudcYO/RZO2R2L82Fpo7bSzY1XgweZrnWWyb/feXJKttNJPtV/fF",
	"QG3j5mRiX9arIkk63u00VUWr48PVSGZxPXw0fmn3bCa0ooKglh0+3rY9dRY/Xc00PaN6jzJWed/P9bfQ",
	"Fc/m62jris/xO5XncJO64w517PZ0yr3pc7gxvSv/FwAA//9cdCdLkDUAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
