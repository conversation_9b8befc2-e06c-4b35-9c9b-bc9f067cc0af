// Package category provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package category

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// CategoryCreateV1DTO defines model for CategoryCreateV1DTO.
type CategoryCreateV1DTO struct {
	Name string `json:"name"`
}

// CategoryUpdateV1DTO defines model for CategoryUpdateV1DTO.
type CategoryUpdateV1DTO struct {
	Name string `json:"name"`
}

// CategoryV1DTO defines model for CategoryV1DTO.
type CategoryV1DTO struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// MethodV1DTO defines model for MethodV1DTO.
type MethodV1DTO struct {
	Name string `json:"name"`
}

// PermissionV1DTO defines model for PermissionV1DTO.
type PermissionV1DTO struct {
	Label   string        `json:"label"`
	Name    string        `json:"name"`
	Methods []MethodV1DTO `json:"methods"`
}

// CreateCategoryJSONRequestBody defines body for CreateCategory for application/json ContentType.
type CreateCategoryJSONRequestBody = CategoryCreateV1DTO

// UpdateCategoryJSONRequestBody defines body for UpdateCategory for application/json ContentType.
type UpdateCategoryJSONRequestBody = CategoryUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all categories
	// (GET /v1/categories)
	GetCategories(c *gin.Context)
	// Create new category
	// (POST /v1/categories)
	CreateCategory(c *gin.Context)
	// Delete category
	// (DELETE /v1/categories/{categoryID})
	DeleteCategory(c *gin.Context, categoryID int64)
	// Get category
	// (GET /v1/categories/{categoryID})
	GetCategory(c *gin.Context, categoryID int64)
	// Update category
	// (PATCH /v1/categories/{categoryID})
	UpdateCategory(c *gin.Context, categoryID int64)
	// Get category permissions
	// (GET /v1/categories/{categoryID}/permissions)
	GetCategoryPermissions(c *gin.Context, categoryID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetCategories operation middleware
func (siw *ServerInterfaceWrapper) GetCategories(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCategories(c)
}

// CreateCategory operation middleware
func (siw *ServerInterfaceWrapper) CreateCategory(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateCategory(c)
}

// DeleteCategory operation middleware
func (siw *ServerInterfaceWrapper) DeleteCategory(c *gin.Context) {

	var err error

	// ------------- Path parameter "categoryID" -------------
	var categoryID int64

	err = runtime.BindStyledParameterWithOptions("simple", "categoryID", c.Param("categoryID"), &categoryID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter categoryID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteCategory(c, categoryID)
}

// GetCategory operation middleware
func (siw *ServerInterfaceWrapper) GetCategory(c *gin.Context) {

	var err error

	// ------------- Path parameter "categoryID" -------------
	var categoryID int64

	err = runtime.BindStyledParameterWithOptions("simple", "categoryID", c.Param("categoryID"), &categoryID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter categoryID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCategory(c, categoryID)
}

// UpdateCategory operation middleware
func (siw *ServerInterfaceWrapper) UpdateCategory(c *gin.Context) {

	var err error

	// ------------- Path parameter "categoryID" -------------
	var categoryID int64

	err = runtime.BindStyledParameterWithOptions("simple", "categoryID", c.Param("categoryID"), &categoryID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter categoryID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateCategory(c, categoryID)
}

// GetCategoryPermissions operation middleware
func (siw *ServerInterfaceWrapper) GetCategoryPermissions(c *gin.Context) {

	var err error

	// ------------- Path parameter "categoryID" -------------
	var categoryID int64

	err = runtime.BindStyledParameterWithOptions("simple", "categoryID", c.Param("categoryID"), &categoryID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter categoryID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCategoryPermissions(c, categoryID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/categories", wrapper.GetCategories)
	router.POST(options.BaseURL+"/v1/categories", wrapper.CreateCategory)
	router.DELETE(options.BaseURL+"/v1/categories/:categoryID", wrapper.DeleteCategory)
	router.GET(options.BaseURL+"/v1/categories/:categoryID", wrapper.GetCategory)
	router.PATCH(options.BaseURL+"/v1/categories/:categoryID", wrapper.UpdateCategory)
	router.GET(options.BaseURL+"/v1/categories/:categoryID/permissions", wrapper.GetCategoryPermissions)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbW3PbuBX+Kxy0D9kZxrST7M5Wb26c7Lhtao/jtA8ZTwcmjyzEJMAFQKWqhv+9gwtJ",
	"8AKKlCtXu+MnSeC5fADOh3MAUFsUsyxnFKgUaLFFIl5BhvXX91jCA+Ob9xywhH+cXdxeqeacsxy4JKCF",
	"KM5AfcpNDmiBhOSEPqAQ/fs14wlwtDgryxBx+LUgHBK0+Go07sJKg91/g1iiMqz9fcmTZ/Xn8UQSxw+h",
	"Eh6Aa0cP7LVBgS4vqoZG7Kd3bTThbshvupBJgkI/7g+cM34DImdU+IYpZskunyHKQAj8MC73tgyt68uL",
	"UcEfa8FbLTIi+q4MkZBYjkv9ZKUK4ZkH34RbpdAMQtPNFj6nVxWYobH+BHLFksPH4jXwjAhBGPX4SvE9",
	"pDucqa4quCZ8JWT6yx85LNEC/SFqiB5Zlkdu98oaFuYcb7pBMD+KDWSr2WDrd19NNMQFJ3LzWQEzPT4v",
	"5Ipx8h8sCaOq4R4wB/6R8QxLtEB/+eetmjqtgBb2KaqNr6TMUalsE7pkGjmRqXpy/elzcH59iUK0Bi60",
	"cbQ+U/1nOVCcE7RAb09OTxSRcyxXGk20PotwkhEaPXBW5KqtDHutUWzWFDNpQwJb/Xl5UXaf53UE9FRz",
	"zpIilt72aGu/DZnlLGcCp8L7QGvrrwPqnKX9rujGka6a51v1MWCyEMDFYGO0VR8tlZYT9ABSfSQgYk5y",
	"ExfoBmTBqQhwmgaN+AnS08l19FwmaIF+Afm+saZC1ayg2vKb01OzbFIJVDvBeZ6SWKtH34SJQEObyexq",
	"J5guv3RstrvS4AtEEccgxLJI002wZAVNlIF3M2GOoRtIIwOQLukapyQJFLFByGBp6KexnPUn4wvFlrZg",
	"Ab/tC31k/J4kCVAt8eZPfYlbxoIM003lVijJH/8PnZfAKU4DAXwNPAClZdarIssw35iw6oSeWoPwg1BL",
	"YBUB6K4MUc7EQPia6koEOKDwvbKy6YevkasNmqUWhPwzSzb/s2EZKvn8cboJtBxhNLgxYJCbASQvoHwi",
	"z2bQawRmrLuTtEj1QqejpJOJvBYXhvnUSxDRtlIwGQQlkIKpMdswLnS7GOGakXC4lmOOM5A6cX3d+sJM",
	"15JENamyoap8FqjB1eNH6Azw8FajtbMoy7thQnkQtbKIGY+XPHKcgW9ibkfQh+MlkFw1BoLvRK50i8gh",
	"JksCSXB5MVYW/SYC/ZkzR4tAHCQnsH6h0BGXYrv4k2MZrwYGS585qSpsLn+M5vFS6HBFontON8YhI3fk",
	"JWKL6IVG/ELz46S5jaenlIed05bdWdWRVxPSWRf8laSTXa8dl7+nRDvpKKR7vjnhMMQZr5c0/FtNwy5x",
	"RokKa3sD1fodCVbwGHrNBeWAk6a5eybrP2x9hE2cMvzYPYYcPH7tH7yOH7kOPe1hGxEaAjwonm1G8bZk",
	"c8wliUmOqZwjGm2dX7sRjalOG4JRC51z6GED/aPucTnPyfcMpWhFhFRxvJdyc/+2h3J9NbaH7prA9x2a",
	"Uwbcd87f0fWJdXvQYaS5EogLzs3a1mr2EMB3jdBu78Vj53Gf9h2BuoPOzZVO4507q693KruapdLk+YKn",
	"9m5KLKIofTxJxAnE+eOJIPcqTT+e8AKVoV/wdQLrEWEly2KcrpiQi59Pfz5FCoFddrs55aoqVUTAIdUn",
	"pJIFZnCC+02gr2aaouNc/fxFPdVOp9hyyyaPwSbNT7dq58dr0jyfY09T45X4YcSkFplsU4eIz9oNS2Gy",
	"JR17PktfBPDJllpXFNZKnYmnGrGJujbwQf2erF2l30b/r1XL5Nly81lt5rpp3Sc6HUP7x6NjZGYEWs6x",
	"pd/WPOKZ8BuzNysGm9TqmpnHiPq1DGvgs/k9WX0jJGRVf8yIOcb003lj1LGohqxrcD5RGwuGmndq06W2",
	"OkMLsBkCNU16j2lHNHiVcLyUIebxiqwhZPRfOM85W+M05PANYglJaFog+eGk2iH+WoDeDFv39XA35X8C",
	"S1ykEi0QChHQIlOluPaFQmS9qR1s40/vLo1HJWJ9Oq9w2NdAyv4YfSSpBK7WrbpfSid4dXUTfrjxwpbm",
	"5ZwdoK9uUIg+3EwCYuPdu5euC5rRrfSyeu2kesWrs7cecKyj0evWVvpP37/3/Kqg9bo1VdgBvD7rmUXP",
	"uyKb17Mp2w7g1Uk6/vByNzKHwFCxayS+beF/GO+WXMJD6Jpe7bWoPjXaSazeSZGXaD4IlmqHA2AZ5/Nv",
	"OHc49w71fBAa8h0ORi+bqXnfKz3hWJpUVK33dUOVpeZlIF052wLEB4iIc1tf9zhxz1gKmI77MMktLoRk",
	"WcC4rS6emOeMOSWnrU3qdoIl1mfUAtRo+ca/etjrrd/y30hGpJ5cWmT3wNVUA5VEElCFkCw4hcTX4VQp",
	"D/f47DTcc/m5Wi4F7A2Jae1hTHtD+sy4dAlgtm7BkkDqBSIY98BAEuLV380LtFVQOE1OcnnPCqqMsO/U",
	"lKHNa9Dm7adz9TTFQpr7E/V7SjjV/XHOBfbuDW33xP60XLADhkKdsKv+qMXTfJ+Fttl/PydYPUYz0dZ7",
	"KwftfLDuJFeIW5m/AVwHRh0qjfKsMW5OJvZFvSzStBPdTpNzw+d2r+rJLKyHZ+Nzh2czoRUUZdSiU19v",
	"25E6C5/OZhqecb1HGqui71P994sKZ/OHDBuKU6Bd6X8ZeBZy+2ywlhAx0MStJaoGZV99G/Z+DDeKO+4S",
	"x24Rp9wfHsPN4V353wAAAP//l5bhsoI2AAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
