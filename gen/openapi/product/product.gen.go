// Package product provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package product

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// ProductCollectionV1DTO defines model for ProductCollectionV1DTO.
type ProductCollectionV1DTO struct {
	Items []ProductV1DTO   `json:"items"`
	Meta  ProductMetaV1DTO `json:"meta"`
}

// ProductCreateV1DTO defines model for ProductCreateV1DTO.
type ProductCreateV1DTO struct {
	IID         string   `json:"iid"`
	TechName    string   `json:"techName"`
	Name        string   `json:"name"`
	OwnerEmails []string `json:"ownerEmails"`
}

// ProductMetaV1DTO defines model for ProductMetaV1DTO.
type ProductMetaV1DTO struct {
	ActiveProductID int64 `json:"activeProductID"`
}

// ProductUpdateV1DTO defines model for ProductUpdateV1DTO.
type ProductUpdateV1DTO struct {
	IID      *string `json:"iid,omitempty"`
	TechName *string `json:"techName,omitempty"`
	Name     *string `json:"name,omitempty"`
	Desc     *string `json:"desc,omitempty"`
}

// ProductV1DTO defines model for ProductV1DTO.
type ProductV1DTO struct {
	ID        int64     `json:"id"`
	IID       string    `json:"iid"`
	TechName  string    `json:"techName"`
	Name      string    `json:"name"`
	Desc      string    `json:"desc"`
	CreatorID int64     `json:"creatorID"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// CreateProductJSONRequestBody defines body for CreateProduct for application/json ContentType.
type CreateProductJSONRequestBody = ProductCreateV1DTO

// UpdateProductJSONRequestBody defines body for UpdateProduct for application/json ContentType.
type UpdateProductJSONRequestBody = ProductUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all products
	// (GET /v1/products)
	GetProducts(c *gin.Context)
	// Create new product
	// (POST /v1/products)
	CreateProduct(c *gin.Context)
	// Delete product
	// (DELETE /v1/products/{productID})
	DeleteProduct(c *gin.Context, productID int64)
	// Get product
	// (GET /v1/products/{productID})
	GetProduct(c *gin.Context, productID int64)
	// Update product
	// (PATCH /v1/products/{productID})
	UpdateProduct(c *gin.Context, productID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetProducts operation middleware
func (siw *ServerInterfaceWrapper) GetProducts(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProducts(c)
}

// CreateProduct operation middleware
func (siw *ServerInterfaceWrapper) CreateProduct(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateProduct(c)
}

// DeleteProduct operation middleware
func (siw *ServerInterfaceWrapper) DeleteProduct(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteProduct(c, productID)
}

// GetProduct operation middleware
func (siw *ServerInterfaceWrapper) GetProduct(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProduct(c, productID)
}

// UpdateProduct operation middleware
func (siw *ServerInterfaceWrapper) UpdateProduct(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateProduct(c, productID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/products", wrapper.GetProducts)
	router.POST(options.BaseURL+"/v1/products", wrapper.CreateProduct)
	router.DELETE(options.BaseURL+"/v1/products/:productID", wrapper.DeleteProduct)
	router.GET(options.BaseURL+"/v1/products/:productID", wrapper.GetProduct)
	router.PATCH(options.BaseURL+"/v1/products/:productID", wrapper.UpdateProduct)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbX3PbuBH/Khy0D7kZ2rKT3DXVmxs7N26b2uM47UPG04HJlYWYJHgAqFT16Lt3Fn9I",
	"8A8oSo0S30yeLIKL3cXu/rCLJfxEEp6XvIBCSTJ/IjJZQk71zwshuLgBWfJCwj9Pz2+vcLQUvAShGGia",
	"hKeAf9W6BDInUglWPJCY/OeIixQEmb/cxCQHKenDON2rTUz4/WdI1OX5KOHPNeGtJhkhfb2JiVRUjVP9",
	"Yqkq6ZGxQsEDCJ/udLOJiYDfKiYgJfNPblJsjNAss6WftyqnzF3spJhXZBOTa8HTKlFveZZBohgvAvZm",
	"CvL2jz8KWJA5+cOs8ePMOnFmuRpem1osFYKu20tD7RVFfjTLrhZk/mkS5/egqOV+13J6x1RGWytkbP0C",
	"qArFGmPpsCMf+FFBcxy81GZurcu82RZ5XwoQFzllWdu47UkjFsRQU5As/7FNWt80LCXeXKtwW6URizUe",
	"6NmLJoqtwNK1UOVH9wM/akZ/eU26+nW5jOjysUzD3ktBJlvRekAXT/dOaHmhLVDHbHqm8GHBRU4VmRO0",
	"xJFi2pWDy7GDSHJ8a+hqNf60iQ1bLsb8Zi3ytqYc8mdnp9vqBtxfW14YFrpd2ule/nw5xZ+T0YaOr3RU",
	"flX/vOmBGDEcQrI2ue/Q2IsZX78+tDA1QVIJptYfcOc1EXdWqSUX7L8UEwUO3AMVIN65tf31X7eYbPQE",
	"Mrdvm3UulSrJBnmzYsG1/ZjK8M31+w/R2fUlickKhNTMyeoUIcBLKGjJyJy8Oj45Rj+XVC21NrPV6Yym",
	"OStmD4JXJY5t4t7oLKEKHrgwoBkieNJ/L8833fcliJxJVKc3tTTIDI7Pnkq3cW0GaEouaSaDL/Rs/XNg",
	"uuBZfyl6cGSp5v0T/hlgWUkQcnBw9oR/WlOGhDRjsyf7ex2Y1CIYNjGsbFHYep5JXokEesNVIYCmzXA3",
	"GMJefoR1knH62F3/oFKex8kDKJdZBCsNGMgNqEoUMqJZFjniY6IjWGjAXKZkTn4Fde04IZRNkau5vjw5",
	"MZVtoaDQAmhZZizRk2efpYGcKYQmFmDdsk6Dr6220yaSVZKAlIsqy9aRACUYrCBFDL7+iooN1PYDSl0W",
	"K5qxNMKtDqSK7O6pdTntm/5jQe3O5BR+1Sd6x8U9S1MoNMXLP/cpbjmPclqsnViJlD9/h8UrEAXNIgli",
	"BSICnGW25CrPqVibMGoFGm6y9EFiSrAOJXebmJRcDkSqKXZlRKMCvjgW/VA1ZI6dyTsg1V94uv7qceqV",
	"3+EYjTQZ40V0Y1QhfjJUooLN4SG1VckWjmy+/YGiZ4kiE3Y+CgZx1MkAndxOUsjAHPXbGpzrcYSZpY++",
	"MLWM1BIiWULCFgzS+tXleR+AhkEDwJIKmoPSuerTUyD8dJnHcAQLJVcMzkmtcw80sWf3ukx1VXWnFt/g",
	"cXsIYhOwYOz0AwvPEwsm2EZxEI8XPhjYI5E+FOFNNfTMw/v7ZZAfldjzr8TGQVNSlSwHTKWP31566MPD",
	"kDxHhBysEvRbeSMQMWS/nzrQ9lp+YPhZYthG0541YK/jMEI01IYYJM/Xo12IFm1JhWIJK2mra7KVdPbk",
	"PW3XaGzqNBOMcui0tYYZ9Dtn43SBRtoOk2ZLJhXHKNlncvMBco/J9bfBPeauGHzZMnOKwUNtw87cEFl3",
	"BZ0+m+kwJpUQZi9oDQcAEOpKtsd78dh53W/fdgjqBXqNcJ1zOy3wT3eYD83WYpJyJTLb6pbz2Sx7PE7l",
	"MSTl47Fk95hEH49FRTZxmPAohdUIMdLyhGZLLtX8zcmbE/0F1G5Y3T34ypUTMhKQYQaKFI+McaL7daQ7",
	"vU2JcIaPv+JbLXQKL89JIYbXNcl0rq4pGWJp9+Ud+GlovJA/jbDUJJN56hAJcbvhGUzmpGMvxOmjxKpr",
	"IievN19zeWvb7ZOZ2PZ7zeACnyfPdk31Zv7f3Mhkb/n5rGZz3YzuE50eo/3j0WOyYwRazPFFmNduwDPh",
	"N8ZvpxhsUqvPZjdE1PdSLIMP5nny9LVUkNctBG0xj5l+u5uNOhzRZF2GuwO14WCgqe+f4ClpaAM2JkA3",
	"2e6Itmj0IhV0oWIqkiVbQcyLf9OyFHxFs1jAZ0gUpLEZgfSnY3ee+60CsW7E1+ZuyuUUFrTK8PhGYgJF",
	"lWMRq2WRmFhpeMps5Omjk5GIJFam90XYXUHp2+gdyxQI3LfqdeGc6MXVTXxxE1RbmdtJW5S+uiExubiZ",
	"pMi3Ovn2BOtoDIq1lf6o0Gn3YnodN55BUKypwg4g1SWSoOTmw+4BpCPYgpJN2XYAqV7SCYeXf5A5hA4O",
	"XSPxbQv/w0i34JIBQNfwau9F9V22rcDq3G4bAVpIBQu1wylgEReSbzB3OPEe9EIqNOA7nBq9bObaljun",
	"J3O5z9vv6wGXpXbLQLpytgVISCEmz2x93cPEPecZ0GJchkluSSUVzyMubHXxf+Y5ww7pNLdJy06potEC",
	"NQC0Vsj+7mVvtWHOf2c5U9q5RZXfg0BXQ6GYYoCFkKpEAWlowRlOHl7x6Um85/ZztVhI2FslrmcP67S3",
	"Sh+4UD4AzNEtWjDIgopILgJq+Pf2XFB4Q15yecurQrkLupLE3j1w71ZfRqVtordv9oWdXq/H6wvsvZqi",
	"vRL7aLHQNFgxYbv14OZpfu+kbXP+/pbKahvtqG19tvK03V1Z38lO41bmbxSuA6MOlWbyTjZuOhP7ar2o",
	"sqwT3d6QS1qdGHYr2UnXw6PxW4dn41CnCjK12uHP23ak7qSfzmZaPSN6jzTmou99/f8nTs/mP1JsKE5R",
	"7UrfcQ5s5PbdYC0hEyhSv5ZwA8gffw1Lfw5f4LZ8exv76jble9tz+NJ2t/lfAAAA//+tmqwi6zUAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
