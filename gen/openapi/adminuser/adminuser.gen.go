// Package adminuser provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package adminuser

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for GetUsersAsAdminParamsSort.
const (
	Category  GetUsersAsAdminParamsSort = "category"
	CreatedAt GetUsersAsAdminParamsSort = "createdAt"
	FullName  GetUsersAsAdminParamsSort = "fullName"
	Product   GetUsersAsAdminParamsSort = "product"
)

// Defines values for GetUsersAsAdminParamsOrder.
const (
	Ascend  GetUsersAsAdminParamsOrder = "ascend"
	Descend GetUsersAsAdminParamsOrder = "descend"
)

// AdminUserCollectionV1DTO defines model for AdminUserCollectionV1DTO.
type AdminUserCollectionV1DTO struct {
	Items []AdminUserV1DTO `json:"items"`
	Meta  PaginationV1DTO  `json:"meta"`
}

// AdminUserUpdateV1DTO defines model for AdminUserUpdateV1DTO.
type AdminUserUpdateV1DTO struct {
	IsAdmin    *bool                    `json:"isAdmin,omitempty"`
	CategoryID *int64                   `json:"categoryID,omitempty"`
	Products   *[]ProductIDV1DTO        `json:"products,omitempty"`
	Roles      *[]RoleProductLinkV1DTO  `json:"roles,omitempty"`
	Groups     *[]GroupProductLinkV1DTO `json:"groups,omitempty"`
}

// AdminUserV1DTO defines model for AdminUserV1DTO.
type AdminUserV1DTO struct {
	ID          int64                `json:"id"`
	Email       string               `json:"email"`
	FullName    string               `json:"fullName"`
	Category    int64                `json:"category"`
	Position    string               `json:"position"`
	IsAdmin     bool                 `json:"isAdmin"`
	LastLoginAt *time.Time           `json:"lastLoginAt,omitempty"`
	CreatedAt   time.Time            `json:"createdAt"`
	Products    *[]ProductBasicV1DTO `json:"products,omitempty"`
}

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupBasicV1DTO defines model for GroupBasicV1DTO.
type GroupBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// GroupProductLinkV1DTO defines model for GroupProductLinkV1DTO.
type GroupProductLinkV1DTO struct {
	ID        int64  `json:"id"`
	ProductID *int64 `json:"productID,omitempty"`
}

// GroupWithProductIDV1DTO defines model for GroupWithProductIDV1DTO.
type GroupWithProductIDV1DTO struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Type      string `json:"type"`
	IsActive  bool   `json:"isActive"`
	ProductID *int64 `json:"productID,omitempty"`
}

// PaginationV1DTO defines model for PaginationV1DTO.
type PaginationV1DTO struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
	Total  int64 `json:"total"`
}

// ProductBasicV1DTO defines model for ProductBasicV1DTO.
type ProductBasicV1DTO struct {
	ID       int64   `json:"id"`
	IID      *string `json:"iid,omitempty"`
	TechName string  `json:"techName"`
	Name     string  `json:"name"`
}

// ProductIDV1DTO defines model for ProductIDV1DTO.
type ProductIDV1DTO struct {
	ID int64 `json:"id"`
}

// RoleBasicV1DTO defines model for RoleBasicV1DTO.
type RoleBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// RoleProductLinkV1DTO defines model for RoleProductLinkV1DTO.
type RoleProductLinkV1DTO struct {
	ID        int64  `json:"id"`
	ProductID *int64 `json:"productID,omitempty"`
}

// RoleWithProductIDV1DTO defines model for RoleWithProductIDV1DTO.
type RoleWithProductIDV1DTO struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Type      string `json:"type"`
	IsActive  bool   `json:"isActive"`
	ProductID *int64 `json:"productID,omitempty"`
}

// UserBasicV1DTO defines model for UserBasicV1DTO.
type UserBasicV1DTO struct {
	ID       int64  `json:"id"`
	Email    string `json:"email"`
	FullName string `json:"fullName"`
}

// UserWithDetailsV1DTO defines model for UserWithDetailsV1DTO.
type UserWithDetailsV1DTO struct {
	ID          int64                     `json:"id"`
	Email       string                    `json:"email"`
	FullName    string                    `json:"fullName"`
	Category    int64                     `json:"category"`
	Position    string                    `json:"position"`
	IsAdmin     bool                      `json:"isAdmin"`
	LastLoginAt *time.Time                `json:"lastLoginAt,omitempty"`
	CreatedAt   time.Time                 `json:"createdAt"`
	Photo       string                    `json:"photo"`
	Products    []ProductBasicV1DTO       `json:"products"`
	Roles       []RoleWithProductIDV1DTO  `json:"roles"`
	Groups      []GroupWithProductIDV1DTO `json:"groups"`
}

// GetUsersAsAdminParams defines parameters for GetUsersAsAdmin.
type GetUsersAsAdminParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`

	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`

	// CategoryIDs Category IDs
	CategoryIDs *[]int64 `form:"categoryIDs,omitempty" json:"categoryIDs,omitempty"`

	// IsAdmin Filter by admin status.
	IsAdmin *bool `form:"isAdmin,omitempty" json:"isAdmin,omitempty"`

	// Limit Limit the number of entities returned.
	Limit *int64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Offset the number of entities returned.
	Offset *int64 `form:"offset,omitempty" json:"offset,omitempty"`

	// Sort Sort the users by a field
	Sort *GetUsersAsAdminParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetUsersAsAdminParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetUsersAsAdminParamsSort defines parameters for GetUsersAsAdmin.
type GetUsersAsAdminParamsSort string

// GetUsersAsAdminParamsOrder defines parameters for GetUsersAsAdmin.
type GetUsersAsAdminParamsOrder string

// UpdateUserAsAdminJSONRequestBody defines body for UpdateUserAsAdmin for application/json ContentType.
type UpdateUserAsAdminJSONRequestBody = AdminUserUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all users by filters as admin
	// (GET /v1/admin/users)
	GetUsersAsAdmin(c *gin.Context, params GetUsersAsAdminParams)
	// Delete user as admin
	// (DELETE /v1/admin/users/{userID})
	DeleteUserAsAdmin(c *gin.Context, userID int64)
	// Get user
	// (GET /v1/admin/users/{userID})
	GetUserAsAdmin(c *gin.Context, userID int64)
	// Update user as admin
	// (PATCH /v1/admin/users/{userID})
	UpdateUserAsAdmin(c *gin.Context, userID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetUsersAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetUsersAsAdmin(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUsersAsAdminParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "categoryIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "categoryIDs", c.Request.URL.Query(), &params.CategoryIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter categoryIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "isAdmin" -------------

	err = runtime.BindQueryParameter("form", true, false, "isAdmin", c.Request.URL.Query(), &params.IsAdmin)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter isAdmin: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsersAsAdmin(c, params)
}

// DeleteUserAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) DeleteUserAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteUserAsAdmin(c, userID)
}

// GetUserAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetUserAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUserAsAdmin(c, userID)
}

// UpdateUserAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateUserAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "userID" -------------
	var userID int64

	err = runtime.BindStyledParameterWithOptions("simple", "userID", c.Param("userID"), &userID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateUserAsAdmin(c, userID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/admin/users", wrapper.GetUsersAsAdmin)
	router.DELETE(options.BaseURL+"/v1/admin/users/:userID", wrapper.DeleteUserAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/users/:userID", wrapper.GetUserAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/users/:userID", wrapper.UpdateUserAsAdmin)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xb3XPbuBH/VzhoH3IzjGUndprTWy5Obtzm6ozj9B4yng5MrizEFMEDQKWqh/97ZwHw",
	"G6BI2co5HT+JAhe7P+wnvnhHIr7KeAqpkmR+R2S0hBXVj2/iFUs/SxBveZJApBhP/3V0enmO7zLBMxCK",
	"gaZkClbth78KWJA5+cus5j6zrGcVX8OtCInaZEDmhApBNyQk/3nORQyCzI+KkKxAUeRIk+R8QeZfhnl/",
	"pDcspTXU4qrB7kVRhETAHzkTEJP5FwvXyriqcPDrrxApBFZB/ZzFVIFn+BFVcMPF5uwU/1kmLFVwA0IP",
	"54Y/r1tfHZMWppDcCJ5n4/X3K5J/FDzOI/WBpbdb1XhShIRJPZYGwGvOE6BpV9+ZYTwejkVydroVx0tU",
	"P09gPO8LnsCUkR4XxZAVK/uNcybs8guVLCrl+gw/3exHh0VIIgFUQfxGYf8FFyuqyJygoz1XbAWkGopU",
	"gqU3HZ5IcnBp6Gq+x6NtjZ6XUKk+8BuWPigGtHPGJcMwbIBocCgp7+NwLcv4U8hJN+grozVA1jprWqWf",
	"Ea6KkLwTgosLkBlPpTcj8BgGR/5CZzYp6c0wHarSCG8llz7hSUV4qUkGSNFFpKJqmOqVpcqlx7urnNHR",
	"r+0UGiXUw2zha4yqBOPKvzrVNQzdrzyxP/ZSusJWLWI4GE3MRIqtYThoUHWG7Rbbqm1GeNmrRTGxvG3v",
	"Biavanq5cX8Kyso0v5VfVRC2176+DrxD/Z2pZafUjE7kXTfqZ/IRo/ON4sRRczBPdKciPdMkbMXUDqUD",
	"I32xkKB2m20ormgyvWvPYQ38CkvJ2GXAfsbeYyC3OLWrVsnIcmooZWtQY+QriJb/3EboDuyqqxU1oKWG",
	"d+9FReNiDmdeT4nXp5nHlXfvm19xRPdJrx1f2Sm7Tq0dx56s25mz9+wCK8qSrV60yJNkRKSH+4vJ0CJt",
	"YHGZDoeLpjsFRVkityxQp7vQ8X6WKa+L8KEtscsi2uH0Q0uJFw9n8JGLtFf7WqP9DTPLkis+qNefxy7k",
	"Tva/jjvcafNgooVHRmK4dRFpdNuMnYaCyoFUTtuPbFx8QZQLpjafcDhm2G9yteSC/ZeWBrkGKkC8L13i",
	"779f4nJKd0CH0m9r91gqlZECebN0YSzPVKLT7m+fgjcfz0hI1iCkZk7WR6gqnkFKM0bm5OXB4QF6cUbV",
	"UqOZrY9mFMc7q2KvCHutM6sqk4pcBHf69+y06L7PQKyYRDi9rg1nc7bP7qqaUzhoMi5pIr0vdG/96Ohe",
	"OmG/cWCo5v0d/jhY5hKEVumNmdTHICPBMmNlcgEqF6kMaJIEmjK43gQLlih8pDKg1ucw42vPOIvJnPwK",
	"CouDfFP7JBV0BUqL+tIVElNFgwUXgQQqoiU6Mzb/kYN2cpvaqpcmxvo5ocDi3+Zswy84O5UerpWpZItz",
	"FeRVyisTaSf9diPaAeKtjdcBFPUu7v5gvNdWQ/tpowVmr+TAg6hOJz11l7XCIeMDrssCtYQgzVfXIAK+",
	"CCBVDCcDgdC+BLFPZLmoqwXGsKB5ojADh2MmDw5E53qFuCukan3pwLQzpE9cGEBVQNFgwSCJfY7PhQdC",
	"sypAmq+wargLhaMWNDK/P4DOdXHyKMe+cwGjMoI0bsCqGpA/PjmkX2H9M3ub2ulfHB6aDc1UQaqTE82y",
	"hEU60cy+SlOHavEPcjDUPXByRJbjHMdRRYuwo0udEgOZRxFIiWbaoP8JBmuIUczxxOEODcexUexAdJau",
	"acLiAMcDUgU2y2gsR/1i8DmldhJQAn7ZJ3rPxTWLY0g1xYuf+xSXnAcrmm5KsRIpT/6EwSsQKU0CCWIN",
	"IgDsZWY/+WpFccWChWxr6VP0BotafdJDrop+hZ3d4Y+pvySGBMwWeBvSqW6XWlwl4qBXXg0ZihpZYJE0",
	"0EsDHcc4i6rD2OAiTa9WIgdH4h/Oc57odSBphYDRxVMAPM4AMK7WdkiPz4fDM8iy4AXfmFrqfzKDiC0Y",
	"xMY1nVPIH8DBH8Rizr0UTw15KiE/WglBD/RGTUZVtHRoSt/62FoLDNmjDBVtmV94vHkwqzivxPiixNAE",
	"FwZHb0zFo4zmXIN+iuXHGcvWpcZUQzsDdO3K1G2zu3rhX2wlcO9Jwdpen2v9n0meiwh6zXkqgMZ1c3f3",
	"zL8tdgubKOH0ttowsu1OUP0tsuHNMdfbHrYBIhdgJ/lqM4i3RZtRoVjEMpqqKaSzu8a/7YiGuo5TwSCH",
	"zo6hm0F/U3KYzrNHOaHTbMmkMqdCO3Suby/t0Lm6WLRD3zWDb1t6jlG4b0e209dH1h1BJyLNejPKhTCp",
	"s9XsCYDeGtXZ3vPHzut+2HcIqgE2zhj0HKVzuvDlCqcQJhObSUwuEnuKIOezWXJ7EMsDiLLbA8mucS5y",
	"eyByffrrI3wew3qAGGl5RJMll2r++vD1ob6+azN7b1esnILJQECCBTtQPDDKqTZY61mVrgv60I84dtic",
	"vBpG8jH8WJGM52rt42VpdwYn8NOh8Uz+NMBSk4zmqV3Ex+2CJzCaU7296uCky/RYTo0qXnEpd/VHM7GF",
	"umLwDv+P7l2W37r/P8qW0dZq1rOKzce6dRfvbDDa3R8bTCZ6oI05vvDzmhZ4xv2G+E3ywbq0NtlMi4jq",
	"Uqtl8Mn8H919IxWsyvEYjTWY6bfTdNThiCrrMpweqDUHE5r64wlcVroSsFEBmkktoVJy8CwWdKFCKqIl",
	"W0PI03/TLBN8TZNQwFeIFMShaYH4J9+ZT6Vu17lG40RDyyIhsdJwZV7L08tNIxFJrMxRRy71OV01LuwT",
	"PDu/CN9deGHb62NbQJ9fkJC8uxgFpD48dW8WZI37Uv79gq0nl33B2hu9Yu1M//6bFD256LResWYWtgep",
	"jeNht+TGJz4PL/27bAf1XasuOn73ai5k9oGhjK4B/7YT//1I//NvJpSB5oNgQ21/AGzE+eSbmPu/v5nR",
	"q2Zo953KEzVXhxvH7mVDWaWmVaD93RSpZZjiFuVS8VXAhZ1d3LPOGXZIp7mNGvb+riE9XYuZci2mtUo1",
	"N2MOpl+NaXz1UDpFo6lRXN7yPNXfknxLzTS0/oiscWkmoVKZ7d/213l+o1fjaewL7DyatD2S9icD5YWe",
	"UBfscjyYPM3zJLT1+vt7gtU6moi2Wlvd6wpV08gl4lblrwFXjlG5iut7zRE6/gEufn3HaPze7lkbtISC",
	"TC06fLxse+okfLqaaXhG9A5lrPS+36qPV0uc9ees1hUf3RW+8HEcWG45qhw6pBxzPPkYDiaviv8FAAD/",
	"/xPtRnw/QwAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
