// Package adminproduct provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package adminproduct

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for AdminProductUpdateV1DTOStatus.
const (
	AdminProductUpdateV1DTOStatusActive  AdminProductUpdateV1DTOStatus = "active"
	AdminProductUpdateV1DTOStatusArchive AdminProductUpdateV1DTOStatus = "archive"
)

// Defines values for AdminProductV1DTOStatus.
const (
	AdminProductV1DTOStatusActive  AdminProductV1DTOStatus = "active"
	AdminProductV1DTOStatusArchive AdminProductV1DTOStatus = "archive"
)

// Defines values for ProductWithDetailsV1DTOStatus.
const (
	ProductWithDetailsV1DTOStatusActive  ProductWithDetailsV1DTOStatus = "active"
	ProductWithDetailsV1DTOStatusArchive ProductWithDetailsV1DTOStatus = "archive"
)

// Defines values for GetProductsAsAdminParamsStatus.
const (
	GetProductsAsAdminParamsStatusActive  GetProductsAsAdminParamsStatus = "active"
	GetProductsAsAdminParamsStatusArchive GetProductsAsAdminParamsStatus = "archive"
)

// Defines values for GetProductsAsAdminParamsSort.
const (
	CreatedAt        GetProductsAsAdminParamsSort = "createdAt"
	LastUpdatedAt    GetProductsAsAdminParamsSort = "lastUpdatedAt"
	Owners           GetProductsAsAdminParamsSort = "owners"
	ParticipantCount GetProductsAsAdminParamsSort = "participantCount"
	Status           GetProductsAsAdminParamsSort = "status"
	TechName         GetProductsAsAdminParamsSort = "techName"
)

// Defines values for GetProductsAsAdminParamsOrder.
const (
	Ascend  GetProductsAsAdminParamsOrder = "ascend"
	Descend GetProductsAsAdminParamsOrder = "descend"
)

// AdminProductCollectionV1DTO defines model for AdminProductCollectionV1DTO.
type AdminProductCollectionV1DTO struct {
	Items []AdminProductV1DTO `json:"items"`
	Meta  PaginationV1DTO     `json:"meta"`
}

// AdminProductUpdateV1DTO defines model for AdminProductUpdateV1DTO.
type AdminProductUpdateV1DTO struct {
	IID            *string                        `json:"iid,omitempty"`
	TechName       *string                        `json:"techName,omitempty"`
	Name           *string                        `json:"name,omitempty"`
	Desc           *string                        `json:"desc,omitempty"`
	Status         *AdminProductUpdateV1DTOStatus `json:"status,omitempty"`
	OwnerEmails    *[]string                      `json:"ownerEmails,omitempty"`
	RoleIDs        *[]int64                       `json:"roleIDs,omitempty"`
	GroupIDs       *[]int64                       `json:"groupIDs,omitempty"`
	ParticipantIDs *[]string                      `json:"participantIDs,omitempty"`
}

// AdminProductUpdateV1DTOStatus defines model for AdminProductUpdateV1DTO.Status.
type AdminProductUpdateV1DTOStatus string

// AdminProductV1DTO defines model for AdminProductV1DTO.
type AdminProductV1DTO struct {
	ID               int64                   `json:"id"`
	IID              string                  `json:"iid"`
	TechName         string                  `json:"techName"`
	Name             string                  `json:"name"`
	Desc             string                  `json:"desc"`
	CreatorID        int64                   `json:"creatorID"`
	CreatedAt        time.Time               `json:"createdAt"`
	UpdatedAt        time.Time               `json:"updatedAt"`
	Status           AdminProductV1DTOStatus `json:"status"`
	ParticipantCount int64                   `json:"participantCount"`
	Owners           []OwnerV1DTO            `json:"owners"`
}

// AdminProductV1DTOStatus defines model for AdminProductV1DTO.Status.
type AdminProductV1DTOStatus string

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupBasicV1DTO defines model for GroupBasicV1DTO.
type GroupBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// GroupWithCountsV1DTO defines model for GroupWithCountsV1DTO.
type GroupWithCountsV1DTO struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	IsActive         bool   `json:"isActive"`
	ParticipantCount int64  `json:"participantCount"`
	RoleCount        int64  `json:"roleCount"`
}

// OwnerV1DTO defines model for OwnerV1DTO.
type OwnerV1DTO struct {
	ID       int64  `json:"id"`
	Email    string `json:"email"`
	FullName string `json:"fullName"`
}

// PaginationV1DTO defines model for PaginationV1DTO.
type PaginationV1DTO struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
	Total  int64 `json:"total"`
}

// ParticipantShortV1DTO defines model for ParticipantShortV1DTO.
type ParticipantShortV1DTO struct {
	ID        int64     `json:"id"`
	UserID    int64     `json:"userID"`
	Email     string    `json:"email"`
	FullName  string    `json:"fullName"`
	IsOwner   bool      `json:"isOwner"`
	ProductID int64     `json:"productID"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// ProductCreateV1DTO defines model for ProductCreateV1DTO.
type ProductCreateV1DTO struct {
	IID         string   `json:"iid"`
	TechName    string   `json:"techName"`
	Name        string   `json:"name"`
	OwnerEmails []string `json:"ownerEmails"`
}

// ProductV1DTO defines model for ProductV1DTO.
type ProductV1DTO struct {
	ID        int64     `json:"id"`
	IID       string    `json:"iid"`
	TechName  string    `json:"techName"`
	Name      string    `json:"name"`
	Desc      string    `json:"desc"`
	CreatorID int64     `json:"creatorID"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// ProductWithDetailsV1DTO defines model for ProductWithDetailsV1DTO.
type ProductWithDetailsV1DTO struct {
	ID               int64                         `json:"id"`
	IID              string                        `json:"iid"`
	TechName         string                        `json:"techName"`
	Name             string                        `json:"name"`
	Desc             string                        `json:"desc"`
	CreatorID        int64                         `json:"creatorID"`
	CreatedAt        time.Time                     `json:"createdAt"`
	UpdatedAt        time.Time                     `json:"updatedAt"`
	Status           ProductWithDetailsV1DTOStatus `json:"status"`
	ParticipantCount int64                         `json:"participantCount"`
	Owners           []OwnerV1DTO                  `json:"owners"`
	Participants     []ParticipantShortV1DTO       `json:"participants"`
	Roles            []RoleWithCountsV1DTO         `json:"roles"`
	Groups           []GroupWithCountsV1DTO        `json:"groups"`
}

// ProductWithDetailsV1DTOStatus defines model for ProductWithDetailsV1DTO.Status.
type ProductWithDetailsV1DTOStatus string

// RoleBasicV1DTO defines model for RoleBasicV1DTO.
type RoleBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// RoleWithCountsV1DTO defines model for RoleWithCountsV1DTO.
type RoleWithCountsV1DTO struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	IsActive         bool   `json:"isActive"`
	ParticipantCount int64  `json:"participantCount"`
	GroupCount       int64  `json:"groupCount"`
}

// GetProductsAsAdminParams defines parameters for GetProductsAsAdmin.
type GetProductsAsAdminParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`

	// Status Status of the product.
	Status *GetProductsAsAdminParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Limit Limit the number of entities returned.
	Limit *int64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Offset the number of entities returned.
	Offset *int64 `form:"offset,omitempty" json:"offset,omitempty"`

	// Sort Sort the products by a field.
	Sort *GetProductsAsAdminParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetProductsAsAdminParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetProductsAsAdminParamsStatus defines parameters for GetProductsAsAdmin.
type GetProductsAsAdminParamsStatus string

// GetProductsAsAdminParamsSort defines parameters for GetProductsAsAdmin.
type GetProductsAsAdminParamsSort string

// GetProductsAsAdminParamsOrder defines parameters for GetProductsAsAdmin.
type GetProductsAsAdminParamsOrder string

// CreateProductAsAdminJSONRequestBody defines body for CreateProductAsAdmin for application/json ContentType.
type CreateProductAsAdminJSONRequestBody = ProductCreateV1DTO

// UpdateProductAsAdminJSONRequestBody defines body for UpdateProductAsAdmin for application/json ContentType.
type UpdateProductAsAdminJSONRequestBody = AdminProductUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all products as admin
	// (GET /v1/admin/products)
	GetProductsAsAdmin(c *gin.Context, params GetProductsAsAdminParams)
	// Create a new product as admin
	// (POST /v1/admin/products)
	CreateProductAsAdmin(c *gin.Context)
	// Get product by ID as admin
	// (GET /v1/admin/products/{productID})
	GetProductAsAdmin(c *gin.Context, productID int64)
	// Update product as admin
	// (PATCH /v1/admin/products/{productID})
	UpdateProductAsAdmin(c *gin.Context, productID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetProductsAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetProductsAsAdmin(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetProductsAsAdminParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProductsAsAdmin(c, params)
}

// CreateProductAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) CreateProductAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateProductAsAdmin(c)
}

// GetProductAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetProductAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProductAsAdmin(c, productID)
}

// UpdateProductAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateProductAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateProductAsAdmin(c, productID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/admin/products", wrapper.GetProductsAsAdmin)
	router.POST(options.BaseURL+"/v1/admin/products", wrapper.CreateProductAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/products/:productID", wrapper.GetProductAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/products/:productID", wrapper.UpdateProductAsAdmin)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbX3PbuBH/Khy0D7kZ2vLfNKc3X+zcuO3VHtvpPWQ8HZhcWYwpggFApaqH372DfyRI",
	"AhRFS6k756c44GJ3sdjf7mIBPaOILHKSQcYZmj4jFs1hgeWfZ/Eiya4piYuIfyRpChFPSPbPw/O7K/E5",
	"pyQHyhOQxAmHRfOPP1OYoSn606QWMNHcJzZrxbAMEV/lgKYIU4pXKET/3iM0Boqmh2WIFsCxYIrT9GqG",
	"pl/62V/jxyTDtbblvcXuqCxDROFbkVCI0fSL1ljLuK/0IA9fIeJCMVvbz3mMOXiMEAOLxL+aA+M0yR7t",
	"pZyUIXqkpMgvz5vG0jOSjMMjUDnlkezVo+9P+iz0oQxRksRu0Y9kL8MLMXh5ed62q/rSo/FxGSLyPQN6",
	"scBJ6lRaT+rR730ZohxTnkRJjjPuWfx6Pj+LnSMpbNd6fylDxDjmhd7DGS5SLggjniwBhQiyYiEcpRrA",
	"NJqLv2pn6RruVEiEaP6PdRaW/tjrc5W3DfT+Jq7aTip3czhUrwT5eoweNbf4IykyPnBrbDaHjb0YZfjD",
	"gzbANUOHgqGxRhf292WILigl9AZYTjLmw3xE4nX7K0ILY/hxANKk8MvzXsLTivBOkqyJN2L1/VTvG0Z3",
	"7FgVMHyGlUaol9nQz1qVUcYVZH8VYfEXzJLIl2Fivz+ZAHe+1sFEpGRnyp9qdg+EpICztumy9dg1K+nd",
	"2HbCiZHmrWdbOnlN83vC59Jp2YbhoG3YbkTYAm5PdWQeO/9920YOqNb83Wi1IlXHeUBkr7VbOSvS1B+v",
	"az/7ZOha8N2Si7rcRS3AUtHlKO26p2OGNFkkY7ZHRJzZjMGIuRIihON086kd4Cj1K10MY7cpKv+5nRPK",
	"fdGbAuYQn8mFzQhdYJH5RYm3x5MaoG0/0IOCZP8uaTqCKCfWu9vxWnez4tAWQ58ESX/kE1jOVQ3RyEQD",
	"d01kk0JWyVs1q6hxCwZ0jEpHTkhpbg5s1YayLRFa3mIv0el++tgkJ/gS2iuu2E82ql4bppW2reZWic5W",
	"qcdiPxanki3p9Sm9Ex8rygHuv/YceLpVUG/sR0dD/GiwCxzvCvAu0PrcS5rc3tARaBUl1jlw4aIb1liu",
	"fkbbheW5f/i5y1n09SD2qHUCGy7JnSr7RB3oam+4jBuSwiaLOeypBxky0kNjVHdBKIS+nShcDu/ajsHO",
	"3jKrx9PHnwTC7RxI1h8oLD1dDiROxRAVNOGrW7F0tbizgs8JTf4jy2wx8ACYAv1kYt5ff78T51w5QfiB",
	"/FrHvznnOSoF7ySbEbmwhKfiy/Vvt8HZ9SUK0RIok8zR8lDsFskhw3mCpuh4/2D/RDYy+FxqM1keTrAI",
	"PpMqvJRhZ3QSYQ6PhKr9cRE8665k2f6eA10kTKjTmaorI7Xl6mggojBNcmUadAO8oBkLcJoGhjjALJDT",
	"95FcGJV2vIzRFP0KXIdQdsZkRFUtG7wALjtWX9oCYsxxMCM0YIBpNBdeL4a/FUBXBhBTVH1UDuyog4QL",
	"Nznfys5GQGYBn4NRft8nwLRBagEv6iI69Pm7OPlIXbJi8QBUaAYZTwTkAirtDLFPP3Nscqh3eBAO6p52",
	"NbqSZ7CxKlUnOIdOo1W6JZTb+8WCh1WAg1kCqVcRRqhHDbvIMHtnDfl7iaHVF7NKkBQzfXvQLEP8m34l",
	"Y5nHgPqb0+VYBFlsu5wZEPzFXw7p9yJcqk6nRPTRwYFqb2YcVATGeZ4mkcTr5CtTwa8Wv63roPZNU7tK",
	"cN/edON3qevw2pwmuASsiCJgTBz3VsJTaQJLiIWkkw0X3bcoR/PYodRltsRpEgdiScB4oGtnqcthN6R+",
	"zrDOP0bh4y7RJ0IfkjiGTFIc/dyluCMkWOBsZcQyQXn6P1g8B5rhNGBAl0ADELPkDrNiscB0pbKCO4GI",
	"nIofRVpoXJYgUfnlhDnykTqNswAHGXw3DHsSkqLXfOuUpG32C4lXW7OXo19QNj2d0wLKF2J0gAadM5Af",
	"SE0c6VD3hqJXiSLlWB7P9yPJWe9NnqueWLm29jOyHlbB5fmg4m9g7WecUJ7QZIYURXGdIO2+XRNFdtKs",
	"WhXmBNGqPF6eF3eJubfc9fpzlxsAvckL82juMJ+sHUX2Wp+5FO0rBtT2M6jvkU4PlhRZcKO0Qf9P2Vb3",
	"M99w/ypxrx1rfJ7NCcOpq+GiPsgMLP90dGxMY7Y72NMGUt+f1eOqDsuCqRdD3cHJs7pFs6a4hNRjk2f9",
	"98ozqUHgbj/BUr9WbPx/wkhBI+gMFxkFHNfD7UaZvwP2BKsoJfipvX6nUlY3rDXSqpd6vnZ06yFyKewk",
	"X6x69W3Qtu4PhpJOnhuv+8oXTB1mgl4OLQC4GXQx1k/ngdwGkybzhHEi4sOYyfULshGTq8ddI+YuE/i+",
	"ZuYQg/sCTGuuj6y9ghYiVSyKCkpVFmgMewDgi1/N8Y4/tj53Yd8iqBZoXSfI6qt1kfDlXlRGKqmo8qyg",
	"qb4wYNPJJH3aj9k+RPnTPkseRDn1tE8LefviI9yLYdlDLGhJhNM5YXz64eDDgXwnrTNUpxdp6ksWUEhF",
	"7RFwEijjyDarTnC6WJT5TV5hIkdf08nL2iQfw+uKZDhXuxXsYqlz8Ab8JDTesZ96WEqSwTyli/i43ZAU",
	"BnOSvufj9JmJ+nsgJyuLV1w+6sQ8mIlO1BWDC/H/wbNN+q3n/82MDN6t5q2xZmNdeI/xTovReH+0mGzo",
	"gRpzZObntRnwlPv18dvIB+vUarPZDBHVBYpmoK7jhk9fMQ6Lqv6WFrOYya+b2ajFUZiszXBzoNYcFDTl",
	"r1TEedkVgDs3ktKiwbuY4hkP9V1iSLJ/4TynZInTkMJXiDjEoRqB+KdRF5jWPZKUZd1chsiSJ8/PSqIg",
	"0TIHXXR9SlIOVMStal1iTvDu6ia8uPGqrV86rFH66gaF6OJmkCI/qgfSESy90StWV/q9QsddmQqn9YpV",
	"VdgOpJpE4pVcHwF3IF2AzSu5ehS6balW0vG7l32Q2YUOBl09/q0L/91I1+BiHkBX8GrGouoOeS2wunfF",
	"PqD5VKh+nbcrBTTifPLNz9t2Jd6Cnk+FGny7U+O1va+pM5CsnHUB4lMoqTrqHUyYd3+9MlRyiwrGySIg",
	"VFcXL8xzip2gk9wGLXt3D6jeHiz9AR8sVeux+gKjV5M1V9J83aoNpn/F0fcLsQHa1ufvH6ms8/XpGm2r",
	"s5Wl7ebK2ptsNG5k/lrhyjEqV6knb2TjujMxVmvr1zlGaWvIJK2WD5uVbKTr7tH4o92z3lCjimCqtRN/",
	"3jU9dSP9ZDaT6inRI9KY8b7fqh8QGz3rnxRrV3x1DyfD13H3uubWte++dchN62u4Y70v/xsAAP//pfTV",
	"eK5EAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
