// Package proposal provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package proposal

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for ProposalHistoryEventStatusV1DTO.
const (
	ProposalHistoryEventStatusV1DTOApproved   ProposalHistoryEventStatusV1DTO = "approved"
	ProposalHistoryEventStatusV1DTOArchive    ProposalHistoryEventStatusV1DTO = "archive"
	ProposalHistoryEventStatusV1DTODraft      ProposalHistoryEventStatusV1DTO = "draft"
	ProposalHistoryEventStatusV1DTOOnApproval ProposalHistoryEventStatusV1DTO = "on_approval"
	ProposalHistoryEventStatusV1DTORejected   ProposalHistoryEventStatusV1DTO = "rejected"
)

// Defines values for ProposalHistoryMessageEventV1DTOType.
const (
	ProposalHistoryMessageEventV1DTOTypeMessage ProposalHistoryMessageEventV1DTOType = "message"
)

// Defines values for ProposalHistoryStatusEventV1DTOType.
const (
	Status ProposalHistoryStatusEventV1DTOType = "status"
)

// Defines values for ProposalStatusV1DTO.
const (
	ProposalStatusV1DTOApproved   ProposalStatusV1DTO = "approved"
	ProposalStatusV1DTOArchive    ProposalStatusV1DTO = "archive"
	ProposalStatusV1DTODraft      ProposalStatusV1DTO = "draft"
	ProposalStatusV1DTOOnApproval ProposalStatusV1DTO = "on_approval"
	ProposalStatusV1DTORejected   ProposalStatusV1DTO = "rejected"
)

// Defines values for GetProposalHistoryParamsType.
const (
	GetProposalHistoryParamsTypeMessage GetProposalHistoryParamsType = "message"
	GetProposalHistoryParamsTypeStatus  GetProposalHistoryParamsType = "status"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// ProposalChatMessageV1DTO defines model for ProposalChatMessageV1DTO.
type ProposalChatMessageV1DTO struct {
	Message string `json:"message"`
}

// ProposalCreateV1DTO defines model for ProposalCreateV1DTO.
type ProposalCreateV1DTO struct {
	Price  float64                `json:"price"`
	Type   *string                `json:"type,omitempty"`
	Status *string                `json:"status,omitempty"`
	Data   map[string]interface{} `json:"data"`
}

// ProposalHistoryContainerV1DTO defines model for ProposalHistoryContainerV1DTO.
type ProposalHistoryContainerV1DTO struct {
	Items []ProposalHistoryContainerV1DTO_Items_Item `json:"items"`
	Meta  ProposalLastViewedAtV1DTO                  `json:"meta"`
}

// ProposalHistoryContainerV1DTO_Items_Item defines model for ProposalHistoryContainerV1DTO.items.Item.
type ProposalHistoryContainerV1DTO_Items_Item struct {
	union json.RawMessage
}

// ProposalHistoryEventStatusV1DTO defines model for ProposalHistoryEventStatusV1DTO.
type ProposalHistoryEventStatusV1DTO string

// ProposalHistoryMessageEventV1DTO defines model for ProposalHistoryMessageEventV1DTO.
type ProposalHistoryMessageEventV1DTO struct {
	Type      ProposalHistoryMessageEventV1DTOType `json:"type"`
	CreatedAt time.Time                            `json:"createdAt"`
	Message   *string                              `json:"message,omitempty"`
	Meta      ProposalHistoryMessageMetaV1DTO      `json:"meta"`
	Status    *ProposalHistoryEventStatusV1DTO     `json:"status,omitempty"`
}

// ProposalHistoryMessageEventV1DTOType defines model for ProposalHistoryMessageEventV1DTO.Type.
type ProposalHistoryMessageEventV1DTOType string

// ProposalHistoryMessageMetaV1DTO defines model for ProposalHistoryMessageMetaV1DTO.
type ProposalHistoryMessageMetaV1DTO struct {
	UserID int64 `json:"userID"`
}

// ProposalHistoryStatusEventV1DTO defines model for ProposalHistoryStatusEventV1DTO.
type ProposalHistoryStatusEventV1DTO struct {
	Type      ProposalHistoryStatusEventV1DTOType `json:"type"`
	CreatedAt time.Time                           `json:"createdAt"`
	Message   *string                             `json:"message,omitempty"`
	Status    *ProposalHistoryEventStatusV1DTO    `json:"status,omitempty"`
	Meta      ProposalHistoryStatusMetaV1DTO      `json:"meta"`
}

// ProposalHistoryStatusEventV1DTOType defines model for ProposalHistoryStatusEventV1DTO.Type.
type ProposalHistoryStatusEventV1DTOType string

// ProposalHistoryStatusMetaV1DTO defines model for ProposalHistoryStatusMetaV1DTO.
type ProposalHistoryStatusMetaV1DTO struct {
	Status ProposalStatusV1DTO `json:"status"`
}

// ProposalLastViewedAtV1DTO defines model for ProposalLastViewedAtV1DTO.
type ProposalLastViewedAtV1DTO struct {
	LastViewedAt time.Time `json:"lastViewedAt"`
}

// ProposalStatusUpdateV1DTO defines model for ProposalStatusUpdateV1DTO.
type ProposalStatusUpdateV1DTO struct {
	Status  string  `json:"status"`
	Message *string `json:"message,omitempty"`
}

// ProposalStatusV1DTO defines model for ProposalStatusV1DTO.
type ProposalStatusV1DTO string

// ProposalUpdateV1DTO defines model for ProposalUpdateV1DTO.
type ProposalUpdateV1DTO struct {
	ID        int64                  `json:"id"`
	ProductID int64                  `json:"productID"`
	PropSeq   int64                  `json:"propSeq"`
	Price     float64                `json:"price"`
	Type      *string                `json:"type,omitempty"`
	Status    string                 `json:"status"`
	Data      map[string]interface{} `json:"data"`
}

// ProposalV1DTO defines model for ProposalV1DTO.
type ProposalV1DTO struct {
	ID             int64                  `json:"id"`
	PropSeq        int64                  `json:"propSeq"`
	ProductID      int64                  `json:"productID"`
	Price          float64                `json:"price"`
	Type           string                 `json:"type"`
	Status         string                 `json:"status"`
	CreatorID      int64                  `json:"creatorID"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
	ProposalNumber *string                `json:"proposalNumber,omitempty"`
	Data           map[string]interface{} `json:"data"`
}

// GetProposalHistoryParams defines parameters for GetProposalHistory.
type GetProposalHistoryParams struct {
	// Type Sort the type by a schema.
	Type *GetProposalHistoryParamsType `form:"type,omitempty" json:"type,omitempty"`
}

// GetProposalHistoryParamsType defines parameters for GetProposalHistory.
type GetProposalHistoryParamsType string

// CreateProposalJSONRequestBody defines body for CreateProposal for application/json ContentType.
type CreateProposalJSONRequestBody = ProposalCreateV1DTO

// UpdateProposalJSONRequestBody defines body for UpdateProposal for application/json ContentType.
type UpdateProposalJSONRequestBody = ProposalUpdateV1DTO

// SendProposalMessageJSONRequestBody defines body for SendProposalMessage for application/json ContentType.
type SendProposalMessageJSONRequestBody = ProposalChatMessageV1DTO

// UpdateProposalStatusJSONRequestBody defines body for UpdateProposalStatus for application/json ContentType.
type UpdateProposalStatusJSONRequestBody = ProposalStatusUpdateV1DTO

// MarkProposalAsViewedJSONRequestBody defines body for MarkProposalAsViewed for application/json ContentType.
type MarkProposalAsViewedJSONRequestBody = ProposalLastViewedAtV1DTO

// AsProposalHistoryMessageEventV1DTO returns the union data inside the ProposalHistoryContainerV1DTO_Items_Item as a ProposalHistoryMessageEventV1DTO
func (t ProposalHistoryContainerV1DTO_Items_Item) AsProposalHistoryMessageEventV1DTO() (ProposalHistoryMessageEventV1DTO, error) {
	var body ProposalHistoryMessageEventV1DTO
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromProposalHistoryMessageEventV1DTO overwrites any union data inside the ProposalHistoryContainerV1DTO_Items_Item as the provided ProposalHistoryMessageEventV1DTO
func (t *ProposalHistoryContainerV1DTO_Items_Item) FromProposalHistoryMessageEventV1DTO(v ProposalHistoryMessageEventV1DTO) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeProposalHistoryMessageEventV1DTO performs a merge with any union data inside the ProposalHistoryContainerV1DTO_Items_Item, using the provided ProposalHistoryMessageEventV1DTO
func (t *ProposalHistoryContainerV1DTO_Items_Item) MergeProposalHistoryMessageEventV1DTO(v ProposalHistoryMessageEventV1DTO) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsProposalHistoryStatusEventV1DTO returns the union data inside the ProposalHistoryContainerV1DTO_Items_Item as a ProposalHistoryStatusEventV1DTO
func (t ProposalHistoryContainerV1DTO_Items_Item) AsProposalHistoryStatusEventV1DTO() (ProposalHistoryStatusEventV1DTO, error) {
	var body ProposalHistoryStatusEventV1DTO
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromProposalHistoryStatusEventV1DTO overwrites any union data inside the ProposalHistoryContainerV1DTO_Items_Item as the provided ProposalHistoryStatusEventV1DTO
func (t *ProposalHistoryContainerV1DTO_Items_Item) FromProposalHistoryStatusEventV1DTO(v ProposalHistoryStatusEventV1DTO) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeProposalHistoryStatusEventV1DTO performs a merge with any union data inside the ProposalHistoryContainerV1DTO_Items_Item, using the provided ProposalHistoryStatusEventV1DTO
func (t *ProposalHistoryContainerV1DTO_Items_Item) MergeProposalHistoryStatusEventV1DTO(v ProposalHistoryStatusEventV1DTO) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ProposalHistoryContainerV1DTO_Items_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ProposalHistoryContainerV1DTO_Items_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all products proposals
	// (GET /v1/products/{productID}/proposals)
	GetProposals(c *gin.Context, productID int64)
	// Create new proposal
	// (POST /v1/products/{productID}/proposals)
	CreateProposal(c *gin.Context, productID int64)
	// Delete proposal
	// (DELETE /v1/products/{productID}/proposals/{proposalID})
	DeleteProposal(c *gin.Context, productID int64, proposalID int64)
	// Get product proposal
	// (GET /v1/products/{productID}/proposals/{proposalID})
	GetProposal(c *gin.Context, productID int64, proposalID int64)
	// Update product proposal
	// (PATCH /v1/products/{productID}/proposals/{proposalID})
	UpdateProposal(c *gin.Context, productID int64, proposalID int64)
	// Get proposal history
	// (GET /v1/products/{productID}/proposals/{proposalID}/history)
	GetProposalHistory(c *gin.Context, productID int64, proposalID int64, params GetProposalHistoryParams)
	// Send proposal message
	// (POST /v1/products/{productID}/proposals/{proposalID}/message)
	SendProposalMessage(c *gin.Context, productID int64, proposalID int64)
	// Update proposal status
	// (POST /v1/products/{productID}/proposals/{proposalID}/status)
	UpdateProposalStatus(c *gin.Context, productID int64, proposalID int64)
	// Mark proposal as viewed
	// (PUT /v1/products/{productID}/proposals/{proposalID}/view)
	MarkProposalAsViewed(c *gin.Context, productID int64, proposalID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetProposals operation middleware
func (siw *ServerInterfaceWrapper) GetProposals(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProposals(c, productID)
}

// CreateProposal operation middleware
func (siw *ServerInterfaceWrapper) CreateProposal(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateProposal(c, productID)
}

// DeleteProposal operation middleware
func (siw *ServerInterfaceWrapper) DeleteProposal(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteProposal(c, productID, proposalID)
}

// GetProposal operation middleware
func (siw *ServerInterfaceWrapper) GetProposal(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProposal(c, productID, proposalID)
}

// UpdateProposal operation middleware
func (siw *ServerInterfaceWrapper) UpdateProposal(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateProposal(c, productID, proposalID)
}

// GetProposalHistory operation middleware
func (siw *ServerInterfaceWrapper) GetProposalHistory(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetProposalHistoryParams

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", c.Request.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter type: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProposalHistory(c, productID, proposalID, params)
}

// SendProposalMessage operation middleware
func (siw *ServerInterfaceWrapper) SendProposalMessage(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.SendProposalMessage(c, productID, proposalID)
}

// UpdateProposalStatus operation middleware
func (siw *ServerInterfaceWrapper) UpdateProposalStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateProposalStatus(c, productID, proposalID)
}

// MarkProposalAsViewed operation middleware
func (siw *ServerInterfaceWrapper) MarkProposalAsViewed(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "proposalID" -------------
	var proposalID int64

	err = runtime.BindStyledParameterWithOptions("simple", "proposalID", c.Param("proposalID"), &proposalID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter proposalID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.MarkProposalAsViewed(c, productID, proposalID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/products/:productID/proposals", wrapper.GetProposals)
	router.POST(options.BaseURL+"/v1/products/:productID/proposals", wrapper.CreateProposal)
	router.DELETE(options.BaseURL+"/v1/products/:productID/proposals/:proposalID", wrapper.DeleteProposal)
	router.GET(options.BaseURL+"/v1/products/:productID/proposals/:proposalID", wrapper.GetProposal)
	router.PATCH(options.BaseURL+"/v1/products/:productID/proposals/:proposalID", wrapper.UpdateProposal)
	router.GET(options.BaseURL+"/v1/products/:productID/proposals/:proposalID/history", wrapper.GetProposalHistory)
	router.POST(options.BaseURL+"/v1/products/:productID/proposals/:proposalID/message", wrapper.SendProposalMessage)
	router.POST(options.BaseURL+"/v1/products/:productID/proposals/:proposalID/status", wrapper.UpdateProposalStatus)
	router.PUT(options.BaseURL+"/v1/products/:productID/proposals/:proposalID/view", wrapper.MarkProposalAsViewed)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xc3XPbuBH/VzhoH5IZxrKdj+b05ubjzu3l7LGd60Mm04HJlYUzCTAAKFf16H/v4IMk",
	"+AGKVCTbueopMrjYXSx2f7sEF7lHEUszRoFKgab3SERzSLH++YFzxi9AZIwK+P3o/dWZGs04y4BLApom",
	"YjGof+UyAzRFQnJCb1CI/vOC8Rg4mh6vQpSCEPimn+7lKkTs+g+I5On7XsLXJeGVJukhfbUKkZBY9lO9",
	"sVS5cMgIlXAD3KU7Wq1CxOFbTjjEaPqlmBQaI1TLrOnnrKpQ5mtYSDGP0CpE55xlTODk3RzLT4aPx+JD",
	"jNlStZjUK5oDlj6pMZbYEWlnN2ydcRKBx4g37IUdnSUMyzev6vp2bEG3j8h1m37cXLvRKjRL6DPAL0RI",
	"xpfvGJWYUOAeUxAJaf0Ho3A2Q9Mv9+ivHGZoiv4yqYJqYiNq0hBjd/nDAqg0klbhKAaX2mDu/K+lfRDm",
	"HC+bJk7BbCJOkhH6/oqF/J3AHcQnpaA+exuzWGkD7K1XYBZTWnychu5kZcS6b2izrDV+G9l0OMQnUv0x",
	"YzzFEk2VE8ELSVLlUG0XrJxckRxcGbqNsHCzvaov7xNI3LFjr2rRtpGA1p61BBRxCjRPuxFoAGxJg6DV",
	"Vgz3qpYJWhucC+C1XNMNV4S2wKqhpWU0QKlWyP6pvM6szuN0rx/D6azAh/K5pgFau7upAbyL9tUkfcq2",
	"8bylZ+KQfKcjNhWsse5T06z5cxb7i5IhXn08rLTYxJCNnFU4XczxTHkN5tGcLHQFSP+Ns4yzBU6QkqIY",
	"QaxI9CjEbQ91xPSaYG1d9rdViEjsRzmKUzWqy9M1sPcdBZ6pDlmcN8r7bmXOS8o1Oh0bttklfBvCVNOt",
	"YflymL+8GVKKvm6VRmrTM2d1WalUUaWWLxVry9WHyyBvVxYP2UYJU7ngWj89OvozO+rL7Ttq4fvKFX7L",
	"02s11gdwh1t17BDlGpa26mc/+eKljBE3cky82HRdvYuXflrP4ZW63tBSBoIo50QuL1UCNvF0kss54+S/",
	"WBJG1cA1YA78Y7Hif/zrSsnXE9DUPq1WP5cyQyvFm9AZ01YlMtH7/OkyODk/RSFaABeaOVocqQhnGVCc",
	"ETRFLw8OD9SuZ1jOtTaTxdEExymhkxvO8kyNrcLW6CTCEm4YN5DQRXCv/z19v2o+z4CnRCh1WlOt+b3j",
	"k/tyg1YdNNpThfeBnq1/dkznLGkvRQ/2LNU8v1f/dLBUVbvoHJzcm4remdIlpBqb3NvfS8+kGkG3iWFh",
	"T+Bqf08Ey7lGsvpwTjnguBpuOoN/l29hGSUM3zbX36lUe8f797rraUu3HqIuhTvJ02WvvjXaDHNJIpJh",
	"KseQTu6dv9Zr1Dd1mAl6OTQCoJuBE2PoBjQwxyAiTjKDXegCZM6pCHCSBAWLoJx2gDT0cI10pzGaop9B",
	"npdMFQpxnILUfvOlydvmwkAjL1EjCrRQiGxac8G7wnjJc7DoiWuJpMh3jVS5Uq8/3J5J64UeHx6ag2gq",
	"geo14yxLSKRXMflDGNCuJJSndkNevIozpfqxmobz1uqNmQKRRxEIMcuTZBlwkJyAqvBXIXo1UtM+BTvO",
	"5ju0OqULnJA4UPYGIQNrXa3LUds9PlNsk12h8Ms20UfGr0kcA9UUxz+1Ka4YC1JMl4VYoShfP8LiJXCK",
	"k0AAXwAPQM0yWT5PU8yXxsE9waBcD98oRy/3Fn1VJRcTHYFlzs5FgAMKdyWTmt3b0WUmldyfUnxptf/O",
	"4uXW9qzrK0NPHAWajjAaXBhlWqtafScOjAj/HjVtiVmL+n2sP8lYN55XC9DuIB+UY5vlKoohAfOlsa7a",
	"ez3uiqyjgHn+1FAgvPd5fI9ka45e0QOODrw5voG5tqSuZ1yzDft8+zRjsB0MnUm2t3iVcyjydZVp74ic",
	"6ycig4jMCMTB6fvegnYfaa+2UE1vJ4vua+YfrGZuxp+3WsYymncYTR/GqXJ500A2HPax3Irl3VXu7neo",
	"vmA2dE+8bq8hjj0b3uPNk8Qb60/DIGd87T6Zm4/33kMzi3fGbwrinsLil5Jkj0lt6ZeMS43vijC4XgY4",
	"MCwPCmW+5aCtZ7UpPvCUcmOY4TyRjqKfysbLZrdR6G0BeZjSp7ulsA+YrIPtS6IfsCRqQsR28Mnpcuk+",
	"fLwEGlfinS7kGkIpqvNWwOwhaucHns2O7r7ot4SB3tKnXUFZP6sDlVAK/H9jVA0YfKG5HWSo2jm6gaGq",
	"nGzRq+mDZ0JpJVlQNKWFgelJC+0IhAHI6OD5mreuy6LnYg8iuwaRdjNkX2ga6h/kRczoun8f+wHfx9wd",
	"3BqoLQjcaUjLO8+AI8ZjEdzNgQY4yAXwYKG7ic1ZktHoWiUiKQm90W8aUc45UBlIkoKQOM1auPYJ89tC",
	"6xNh2pP3uPYAuNZxt6gHKxR1YMg3BDdfLYP5rXIhYZ1pX8JU4a5iowqtykSj431IC5OvZa8x10dWFkT2",
	"70aPm+nus3DQHPa0lPk6AuvjrQ6vxuN2I12DoFyg04SqQafRfvrlqwpJg84GlXKe2DZTMZ1MktuDWBxA",
	"lN0eCHKtYvv2gOf6VpyP8EUMix5iRcsinMyZkNO3h28P9Q0Uu/dNZzorYFUEHBLdGSFZYIyjD3vilNAK",
	"qU7Unz+rp6iNc928nE3yMTwvSYZzLfp/fCzN8zH8dEg8E897WJqoGcpTu4iP2wVLYDAn7Xs+Tp+FSjsD",
	"OTl9sSWX4rv8YCa29bVkoO95DZ5dNLRW8/9ZjAzeLbdDtGRzXo1u4p0Oo8390WEy0gNtzLGZn9e4wDPu",
	"18dvlA+6jXbOxYdREVFWnpaBfSEdPH0pJKTlhwVtMYeZfjrORg2OymRNhuMDteJgQlPf/1NlWhcA21c/",
	"NivaJEzafqYvpYX2SlroXEgLi+toYXEZ7bnvJL40d9dZvHP4vp0LcG0bfSSJBK5wq1yX/ojw7Owi/HDx",
	"fIMPCI7SZxcoRB8uBinyaKW/9kavWNs7v4OPNsppvWJNFbYDqWWDl09ydaliB9JVsHkl2yvf25fqJB2/",
	"e7lXA3ahw6N+IKyCS3gCugyvOhaVjf1rA6vVzu8NNJ8KNtR2p4CNOJ98E3O7E++Enk+FKvh2p0Yrm6l9",
	"3yg94UiaVFTgfTlQZKlxGUhXzrYA8SlExImtr1sxcc1YApj2yzDJLcqFZGnAuK0uvjPPGXaKTnMbtOwY",
	"SxzMlAagrOWzf/GwtVo/519JSkw7ANVXWNVWA5VEElCFkMw5hdi34ERN7l7x0WG4IfyczWYCNlaJ6dnd",
	"Om2sUtkyUXtLDWYEEq8ignGPGkhCNP8Np24wOENOcnnHcqqYsDtqytD6JVt7mTbBQn4uL9cOcadyPc65",
	"wMarofWV2D9tLFiDoVAn7GI9CjzN71HaVu/fD6msttFIbct3K0fb8cq6m1xoXMv8lcKlY5SuUk0eZePq",
	"ZGJTrWd5kjS82xkqklbDh4uVjNJ199H40O5ZbWihimJqtVM/r+qeOkq/R+/3akO9/l8HPEBun3XWEiIC",
	"Gru1RDGg+Ktf3dKfwqeENR8R+j4fDPlk+RQ+Vn5d/S8AAP//BWyRnNRSAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
