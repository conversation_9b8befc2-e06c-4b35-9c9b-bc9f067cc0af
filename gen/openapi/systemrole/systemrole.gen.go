// Package systemrole provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package systemrole

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// MethodV1DTO defines model for MethodV1DTO.
type MethodV1DTO struct {
	Name string `json:"name"`
}

// PermissionV1DTO defines model for PermissionV1DTO.
type PermissionV1DTO struct {
	Label   string        `json:"label"`
	Name    string        `json:"name"`
	Methods []MethodV1DTO `json:"methods"`
}

// RoleCreateV1DTO defines model for RoleCreateV1DTO.
type RoleCreateV1DTO struct {
	Type        string            `json:"type"`
	Name        string            `json:"name"`
	Permissions []PermissionV1DTO `json:"permissions"`
}

// RoleFullV1DTO defines model for RoleFullV1DTO.
type RoleFullV1DTO struct {
	ID             int64             `json:"id"`
	Name           string            `json:"name"`
	Type           string            `json:"type"`
	ParticipantIDs *[]int64          `json:"participantIDs,omitempty"`
	GroupIDs       *[]int64          `json:"groupIDs,omitempty"`
	UserIDs        *[]int64          `json:"userIDs,omitempty"`
	Permissions    []PermissionV1DTO `json:"permissions"`
}

// RoleShortV1DTO defines model for RoleShortV1DTO.
type RoleShortV1DTO struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	GroupCount       int64  `json:"groupCount"`
	ParticipantCount int64  `json:"participantCount"`
	UserCount        int64  `json:"userCount"`
}

// RoleUpdateV1DTO defines model for RoleUpdateV1DTO.
type RoleUpdateV1DTO struct {
	Name           *string            `json:"name,omitempty"`
	Type           *string            `json:"type,omitempty"`
	ParticipantIDs *[]int64           `json:"participantIDs,omitempty"`
	GroupIDs       *[]int64           `json:"groupIDs,omitempty"`
	UserIDs        *[]int64           `json:"userIDs,omitempty"`
	Permissions    *[]PermissionV1DTO `json:"permissions,omitempty"`
}

// CreateSystemRoleJSONRequestBody defines body for CreateSystemRole for application/json ContentType.
type CreateSystemRoleJSONRequestBody = RoleCreateV1DTO

// UpdateSystemRoleJSONRequestBody defines body for UpdateSystemRole for application/json ContentType.
type UpdateSystemRoleJSONRequestBody = RoleUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get system roles
	// (GET /v1/roles)
	GetSystemRoles(c *gin.Context)
	// Create system role
	// (POST /v1/roles)
	CreateSystemRole(c *gin.Context)
	// Delete system role
	// (DELETE /v1/roles/{roleID})
	DeleteSystemRole(c *gin.Context, roleID int64)
	// Get system role permissions
	// (GET /v1/roles/{roleID})
	GetSystemRoleFull(c *gin.Context, roleID int64)
	// Update system role
	// (PATCH /v1/roles/{roleID})
	UpdateSystemRole(c *gin.Context, roleID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetSystemRoles operation middleware
func (siw *ServerInterfaceWrapper) GetSystemRoles(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSystemRoles(c)
}

// CreateSystemRole operation middleware
func (siw *ServerInterfaceWrapper) CreateSystemRole(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateSystemRole(c)
}

// DeleteSystemRole operation middleware
func (siw *ServerInterfaceWrapper) DeleteSystemRole(c *gin.Context) {

	var err error

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteSystemRole(c, roleID)
}

// GetSystemRoleFull operation middleware
func (siw *ServerInterfaceWrapper) GetSystemRoleFull(c *gin.Context) {

	var err error

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSystemRoleFull(c, roleID)
}

// UpdateSystemRole operation middleware
func (siw *ServerInterfaceWrapper) UpdateSystemRole(c *gin.Context) {

	var err error

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateSystemRole(c, roleID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/roles", wrapper.GetSystemRoles)
	router.POST(options.BaseURL+"/v1/roles", wrapper.CreateSystemRole)
	router.DELETE(options.BaseURL+"/v1/roles/:roleID", wrapper.DeleteSystemRole)
	router.GET(options.BaseURL+"/v1/roles/:roleID", wrapper.GetSystemRoleFull)
	router.PATCH(options.BaseURL+"/v1/roles/:roleID", wrapper.UpdateSystemRole)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbW3PbuhH+Kxy0DzkzjGk7TnqqNzeXM26b2iM77UPG04HIlYSYJHgAUKmq4X/v4EIS",
	"vIAilShRZ/xkCljsDd9ilwt6h0KaZDSFVHA02yEeriHB6vE9Y5TNgWc05fDPi3cPt3I0YzQDJggompBG",
	"IP+KbQZohrhgJF0hH/3nJWURMDS7LHyUAOd4NUz3qvARXXyBUNy8GyR8XRE+KJIB0qvCR1xgMUz1xlDl",
	"3CIjqYAVMJvuoih8xOD3nDCI0OxzucjXTqjNbOhnWVUq8+iXUvQUKnz0EcSaRg4npzgZNqGjmlrRJ+cO",
	"WEI4JzR1yIrxAuI9wqSpUl21gAhI1MMfGSzRDP0hqAEVGDQFtnlFpRZmDG/bINhr7GXbWK2yWVnr1mf+",
	"nMbwlgEWcKirJZ6zyonjPdB2/B4viH3Y7my50GgzTrBVdDniQx7HDjesGM2zm3dN83pCY0Vf1qNvroas",
	"knFLIjcf7Xmk4qTLuIW/cduEmSAhyXAqvq8pV8fHwJ/GYEACJefAvq91b9rIIlGNK4OyMfC6X1MmhvD1",
	"luapGKlvy/s/DUgH6vx6ynYeKGPMvll+7zHLFu/a1U9Z5D49j3BsXI3ZpItjRvur40f7mzHwuDxKtL8u",
	"is5Oy4IIwpwRsb2XxujNvc7FmjLyXywITeXAAjAD9oGyBAs0Q3/914MscdQCNDOzqGK+FiJDheRN0iVV",
	"ihMRy5m7j/fe9d0N8tEGGFfM0eZCKk0zSHFG0Ay9Ojs/u1KYFWulTbC5CHCUkDRQoJNjhd8ZDUIsYEWZ",
	"xmcfwc5gtmjPN7e8OcVolIfCOR7szFMfW0YzynHMnRNqtXrsWc5o3DVFDQ6Yqud38k8PS4kp3jsY7DTe",
	"rCV9QuqxYGeet45FDYJ+F8PGvIo0fgec5iyEznCeMsBRPdwGg3uXn2AbxhQ/te3vVaq748N73Tfb0W2A",
	"qE/hXvJkO6hvg9Y6IaeQBrvG0Vp8w9JxLhjk0AqAfgbdGBumc4TchEXBmnBB2fawxfUL8gGLq3fXA9Zu",
	"CHzds3KMw10HTLkWrUAVNBHwkJFMJxD0Gwju8S0XkHiK8gypI5+pDHMTaZJ7RTBXnGR9o9sRiuvl+bnu",
	"QaQCdMWEsywmoVoffOE6Tel8PDptt0rXdupUGaxpx30ehsD5Mo/jrbeiwjOe0UZJDlcTFR3Sr6cr06PT",
	"TbrBMYk8WQ8CF95SZ2mly0V3Kz6l2GR3iDTRqy7RB8oWJIogVRSXf+5SPFDqJTjdlmKV8a9/gvECWIpj",
	"jwPbAPNArtJlTZ4kWEapRFYDe7JQwSsu6+YacehR1n6U94BXdxEa+O3CVxNZ/HSBDlz8hUbb7+aVdlej",
	"D6NaTUnpKVJCU2+udUH2a4NgORTfGGf7dK0bD25NpUO9UBkVedyKsOd4Osl40vizw8EVUXZusHMGiiAG",
	"3attin+nxveEmiZqhFqGGU5AqOruc5upigTVIyDyp3yvKN+WZ0gr1YkL3/LqmLeu4rE/kNyAt4HuaX9E",
	"z4A/ScBrwI0BvD+u/PGsSn5PJSQP0FMG+A/KFEN1l+3N5wj6fyjBGjvmrsawCNc93lNtyT05QhOdYo44",
	"Tk1o92r31ISa9DQrwjs7rBsZMldaP2fI04xvg6nxJWG7k9HqzOmeZJgzpo1sDDsaYa4+ZnO805dqTXfb",
	"fy2Cqklitc7VgdJqmn9+lPGufaZPnJzFpjnOZ0EQP51F/AzC7OmMk4U8OJ7OWI4K3034MoLNALGkpSGO",
	"15SL2a/nv54jqYHZhTa4bsvjknsMYvXqJainneMttp7qDddH4LX8+ZucVULH8LI2ycWwvjcZz9Xsj5Ol",
	"np/CT7XIXvBfBlgqktE8FURc3FQcjOWksOfi9IkDG83J6uZXXN6aBv1oJqZhXzF4L3+PXl224ev1fytH",
	"Ru+W3deu2NzVo4eg02J0OB4tJhMRaGKOLt28pgWeht8Qv0kYrFvsNptpEVF9P2UY3Ovfo5frvFIW/cpj",
	"FjM1O81HLY4mYzUYTg/UmoMOzUdZBsoasO8A1i6Q2yTWUDnZexExvBQ+ZuGabMCn6b9xljG6wbHP4AuE",
	"AiJfj0D0y1lZr/6eA9vW4it313VABEucxwLNEPIRpHkiM7OShXxkpMkqupanKkMtUZIYmdbXAubeuuj6",
	"6AOJBTB5blV2yTXei9u5/37uVNt8ubBH6ds58tH7+ShFynrSVdlXFxuDxf2yvPcuP8RoVfs9ghUanWLN",
	"jd+3v1H4P+U9piO1TCROyfVV8BGky2BzStZl2xGkWknHDS/7QvMYOpTRNYBvcwF4HOkmuLgjoKvwap5F",
	"1eXc3sDq3Mg5A82lQvWd0rEUMBHnkq9j7njirdBzqVAH3/HU6GQzue8HpSccCp2KyvO+Giiz1LQMpCpn",
	"U4C4FCL82tTXnZhYUBoDTodl6OQW5lzQxKPMVBffmOc0O0mnuI0yO8ICe0upAUhvufxfTnasdXP+O0mI",
	"UJub5skCmNxqSAURBGQhJHKWQuQyOJaL+y2+OPcPPH5ul0sOB6tE1ep+nQ5W6Z4yYQeAfnXzlgRipyKc",
	"MocaSEC4/of+qLMEhTXU800n/ZrqMrT+fwV9rXotZ2PMhW7UyN9j4FTZY/UFDrYmbVrS+srYvDTZ36X6",
	"6vBsf6M6Qtv6/ftHKmt9cDta2+rdytJ2urL2JpcaNzJ/rXAFjAoq9eJJPq47E4dqvczjuIVua6hMWi0M",
	"l5ZM0vX40fij4VlvaKmKZGq0k48PTaRO0k9lM6WeFn1AGivR97H6P6lSz/o/pwwUx6h2qz5gdhzkZq63",
	"luAhpJFdS5QDkr986pd+ClcLey4Vhq4TxlwknMIVwmPxvwAAAP//hqQi1JM4AAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
