// Package status provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package status

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// StatusV1DTO defines model for StatusV1DTO.
type StatusV1DTO struct {
	Pms struct {
		Status  string `json:"status"`
		Version string `json:"version"`
	} `json:"pms"`
	Services []struct {
		Type    string `json:"type"`
		Name    string `json:"name"`
		Version string `json:"version"`
		Status  string `json:"status"`
	} `json:"services"`
}

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get status
	// (GET /v1/status)
	GetStatus(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetStatus operation middleware
func (siw *ServerInterfaceWrapper) GetStatus(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetStatus(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/status", wrapper.GetStatus)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/8xaW3PbuhH+Kxy0DzkztGnHPplTvXlOnIzbpvbYTvvg8XRgciUhJgFmASpRNfrvHVx4",
	"EUlQpFrl5EkksJdvF7vYBagNiUWWCw5cSTLbEBkvIaPm8RpR4D3IXHAJ/zx//3irR3MUOaBiYGhikYD+",
	"VescyIxIhYwvSEi+nwhMAMns7TYkGUhJF8N0F9uQiJcvEKub94OEv1aEj4ZkgPRyGxKpqBqmeueoCtkg",
	"Y1zBArBJd77dhgTha8EQEjJ7KplC64TazB18DatKMM9hqcVOkW1IHowsj5PzTHYHO5C7lp1vQ7IClEzw",
	"PWvks6zk7kBuaZGAKxZbYExBH15Os/2RMsIovahq38qPtfyibbmya2bAhiP8UIEhFJGuB72ql7Hhqq4s",
	"48i4QKbWDzoNreOuCrUUyP5DlbPmBSgCfhCYUUVm5K//etRiDQOZuVlSCV8qlZOtls34XBhvMJXqmbtP",
	"D8HV3U3DvBlZnWuTRA6c5ozMyMXp2eklCUlO1dKgiVbnEU0yxqMFiiLXY9uwMxrFVMFCoF37PoKN+b15",
	"v23P54AZkxpOhzVHkRSx8o5HG/fUJxZFLiRNpXfCcJvHHnYUadcUMzhgqp3f6J8ekYUElL2D0Ub/7LD0",
	"KanHoo17XnuYdgj6XQwrVwJ23iMpCoyhM1xwBJrUw+1g8K/yK6zjVNDXtv29oLorPrzWfbMdbANEfYB7",
	"ybP1IN4d2pyiYjHLKVdTSKNN420/oiHWcS4YlNBKgH4B3RwbpvOk3ASmaMmkErg+jLluTA5grmrVAbwr",
	"Bt/2cI5xuG+DafH6yOpquwClfxKQMbLcFhpyD6pALgO1hMCSBmJu31wNOyWmVqApTTcJmZGPoB7Koomu",
	"dTQa3p6d2X6RK+BGGc3zlMWGNfoibWmz3ad++jPCnMzIn6K6PY1cbxo1myVT2XZx2+lAFnEMUs6LNF0H",
	"CAoZrCDR5e3y/4ilp0nugXTDVzRlSaDbAZAqmNvibbCcdz3/mVNX9EvAF12iDwJfWJIANxRv/9KleBQi",
	"yChfl2qlpvz1DzBeAXKamrABDEBz2W6nyDKqk1fHTVB1W4oupG6YXCQ9b128tiqGrZVxgWgN2Rn2bNC+",
	"+ro73tkvW9PdstQiqBKw0dKR2VOnmXt63j7bltBY9rQhBaauaZOzKEpfTxN5CnH+eirZiz6UvJ5iQbah",
	"n/AkgdUAsaYVMU2XQqrZb2e/nRGNwHm8HUC3ZW7LACGlCpJAicA6J3hZB6ZnKdvlGbnSrx/1rFE6RlZj",
	"kXwC7yqS8VLd+nhF2vkp8szW/Ub+MiDSkIyWaULEJ+1epDBakok9n6TPEnC0pEaXWUn53TWOo4W4RrIS",
	"cK3fR3OX7WHN/7dyZPRqNfutSsxdPXpIdDYEHR6PDSETI9DlnJj7ZU1LPBt+Q/ImxWDd+jXFTMuIav93",
	"Atz2P5p9LRVkpT3WYw1hZnaaj1oStcvaAqcnai3BpuZzSL6f6LN63wb8sNN2lU4O3iRI5yqkGC/ZCkLB",
	"/03zHMWKpiHCF4gVJKEdgeQX3aQxLexrAbiu1Vfurmt9AnNapIrMCAkJ8CLTVdjoIiFx2nTLV+szbZ7V",
	"qEmczsbdhrtz2XZ99IGlClDvW5Vdmid4c3sfXt97Ybsbmj2gb+9JSK7vRwFx8R6YKzqjMqdqWWusGm7S",
	"vM9RWEATxry8j2Fcvbus71/Km8QexSYavWrdSXRQac9t5UKc1KPvLvv06qD1qrWnhCNoLQuJV3N9RXEE",
	"7TrZvJpt23YErY2i4w+v5kH7GBjK7BqIb3cwPY52l1zSk9BVeu3uRdU18t7Eal3BDiSaD4JLteMBcBnn",
	"029z7njqG6nng1An3/FgdKqZXveDyhONlS1F5X5fDZRValoFMp2za0B8gJi8cv11JydehEiB8mEdtrjF",
	"hVQiCwS67uJ/rHNWnKYz0kaZnVBFg7lGANpbPv+Xkx1r/ZL/zjKmzOLyInsB1EsNXDHFQDdCqkAOic/g",
	"VDP3W3x+Fh64/dzO5xIOhiQMdz+mgyE9CFTNBLBHt2DOIPUCkQI9MIiCePkP+7mqDIrGUKO4/C4KroWI",
	"b9y2ofX3SwTdol7p2ZRK9TlP3PuYcKrsadwLHGwN37XEvbpccA4joSnYpT1687TPk9DW5+8fCdb4aCLa",
	"6mzVQDsdbHORS8Q7lb8GXAVGFSo18yQf1zcTh6KeF2naiu7GUFm0WjFcWjIJ6/Gz8UeHZ72gJRQt1KHT",
	"j4+7kToJn6lmBp5VfUAZK6PvU/W/iRJn/U8KF4pjoN2az+6ejdzN9fYSMgaeNHuJckDL10/92n+Gzwd7",
	"PhwMfTIY87HgZ/hM8Lz9bwAAAP//qt+7saMkAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
