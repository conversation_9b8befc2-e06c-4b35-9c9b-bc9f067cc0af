// Package productrole provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package productrole

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// MethodV1DTO defines model for MethodV1DTO.
type MethodV1DTO struct {
	Name string `json:"name"`
}

// PermissionV1DTO defines model for PermissionV1DTO.
type PermissionV1DTO struct {
	Label   string        `json:"label"`
	Name    string        `json:"name"`
	Methods []MethodV1DTO `json:"methods"`
}

// RoleCreateV1DTO defines model for RoleCreateV1DTO.
type RoleCreateV1DTO struct {
	Type        string            `json:"type"`
	Name        string            `json:"name"`
	Permissions []PermissionV1DTO `json:"permissions"`
}

// RoleFullV1DTO defines model for RoleFullV1DTO.
type RoleFullV1DTO struct {
	ID             int64             `json:"id"`
	Name           string            `json:"name"`
	Type           string            `json:"type"`
	ParticipantIDs *[]int64          `json:"participantIDs,omitempty"`
	GroupIDs       *[]int64          `json:"groupIDs,omitempty"`
	UserIDs        *[]int64          `json:"userIDs,omitempty"`
	Permissions    []PermissionV1DTO `json:"permissions"`
}

// RoleShortV1DTO defines model for RoleShortV1DTO.
type RoleShortV1DTO struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	GroupCount       int64  `json:"groupCount"`
	ParticipantCount int64  `json:"participantCount"`
	UserCount        int64  `json:"userCount"`
}

// RoleUpdateV1DTO defines model for RoleUpdateV1DTO.
type RoleUpdateV1DTO struct {
	Name           *string            `json:"name,omitempty"`
	Type           *string            `json:"type,omitempty"`
	ParticipantIDs *[]int64           `json:"participantIDs,omitempty"`
	GroupIDs       *[]int64           `json:"groupIDs,omitempty"`
	UserIDs        *[]int64           `json:"userIDs,omitempty"`
	Permissions    *[]PermissionV1DTO `json:"permissions,omitempty"`
}

// CreateProductRoleJSONRequestBody defines body for CreateProductRole for application/json ContentType.
type CreateProductRoleJSONRequestBody = RoleCreateV1DTO

// UpdateProductRoleJSONRequestBody defines body for UpdateProductRole for application/json ContentType.
type UpdateProductRoleJSONRequestBody = RoleUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get product roles
	// (GET /v1/products/{productID}/roles)
	GetProductRoles(c *gin.Context, productID int64)
	// Create product role
	// (POST /v1/products/{productID}/roles)
	CreateProductRole(c *gin.Context, productID int64)
	// Delete product role
	// (DELETE /v1/products/{productID}/roles/{roleID})
	DeleteProductRole(c *gin.Context, productID int64, roleID int64)
	// Get product role with permissions and participant IDs
	// (GET /v1/products/{productID}/roles/{roleID})
	GetProductRoleFull(c *gin.Context, productID int64, roleID int64)
	// Update product role
	// (PATCH /v1/products/{productID}/roles/{roleID})
	UpdateProductRole(c *gin.Context, productID int64, roleID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetProductRoles operation middleware
func (siw *ServerInterfaceWrapper) GetProductRoles(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProductRoles(c, productID)
}

// CreateProductRole operation middleware
func (siw *ServerInterfaceWrapper) CreateProductRole(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateProductRole(c, productID)
}

// DeleteProductRole operation middleware
func (siw *ServerInterfaceWrapper) DeleteProductRole(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteProductRole(c, productID, roleID)
}

// GetProductRoleFull operation middleware
func (siw *ServerInterfaceWrapper) GetProductRoleFull(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProductRoleFull(c, productID, roleID)
}

// UpdateProductRole operation middleware
func (siw *ServerInterfaceWrapper) UpdateProductRole(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "roleID" -------------
	var roleID int64

	err = runtime.BindStyledParameterWithOptions("simple", "roleID", c.Param("roleID"), &roleID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter roleID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateProductRole(c, productID, roleID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/products/:productID/roles", wrapper.GetProductRoles)
	router.POST(options.BaseURL+"/v1/products/:productID/roles", wrapper.CreateProductRole)
	router.DELETE(options.BaseURL+"/v1/products/:productID/roles/:roleID", wrapper.DeleteProductRole)
	router.GET(options.BaseURL+"/v1/products/:productID/roles/:roleID", wrapper.GetProductRoleFull)
	router.PATCH(options.BaseURL+"/v1/products/:productID/roles/:roleID", wrapper.UpdateProductRole)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbW3PbuhH+Kxy0DzkzjGk7TnqqNzdOzrhtao/stA8ZTwcmVxJikuABQKWqhv+9gwtJ",
	"8AKKVKJYnfGTKWCx++1ibwToLQppktEUUsHRbIt4uIIEq8cPjFE2B57RlMM/z67ub+RoxmgGTBBQNCGN",
	"QP4VmwzQDHHBSLpEPvrPa8oiYGh2XvgoAc7xcpjuTeEj+vgVQnF9NUj4tiK8VyQDpBeFj7jAYpjqnaHK",
	"uUVGUgFLYDbdWVH4iMHvOWEQodmXcpGvjVCr2cBnaVWCefBLKXoKFT76BGJFI4eRU5wMq9CBplb0ybkF",
	"lhDOCU0dsmL8CPEOYVJVCVctIAIS9fBHBgs0Q38IaocKjDcFtnpFBQszhjdtJ9ip7HlbWQ3ZrKyx9ak/",
	"pzG8Z4AF7Gtq6c9ZZcTxFmgbfocVxC7f7my50N5mjGBDdBniYx7HDjMsGc2z66umej2hsaSv69F3F0Na",
	"ybglkZuPtjxScdJl3PK/cduEmSAhyXAqfqwqF4f3gT+N8QHpKDkH9mO1e9f2LBLVfmW8bIx73a0oE0P+",
	"9Z7mqRiJt2X9Z3OkPTG/nbKde8oYs2+W3XvUssW7dvVzFrmz5wHSxsWYTTo7ZLS/OXy0vxvjHucHifa3",
	"RdHZadkQQZgzIjZ3Uhm9uZe5WFFG/osFoakceATMgH2kLMECzdBf/3UvWxy1AM3MLKqYr4TIUCF5k3RB",
	"FXAiYjlz++nOu7y9Rj5aA+OKOVqfSdA0gxRnBM3Qm5PTkwvls2Kl0ATrswBHCUkD5XRyrPA7o0GIBSwp",
	"0/7ZR7A1Plu055tb3pxiNMpD4RwPtuapjy2jGeU45s4JtVo99ixnNO6qogYHVNXzW/mnh6X0Kd47GGy1",
	"v1lL+oTUY8HWPG8cixoE/SaGtXkVafwOOM1ZCJ3hPGWAo3q47QzuXX6CTRhT/NTWvxdUd8eH97pvtoNt",
	"gKgPcC95shnE26C1MuQU0mDbSK3FdywdZ4JBDq0A6GfQjbFhOkfITVgUrAgXlG32W1y/IO+xuHp33WPt",
	"msC3HStLg6MlqKYkAh4ykukigH4DwT1D7SnSE6TyNlNl4jrSNLeaYq54qVqNExAq7r60WRpaTzVwRI7I",
	"pF+2MjNUYUN2uyNYDqb6YAlzUZalsk9qlcmieJDL9eGG0u/89FSfaKQCdP+FsywmoVIk+Mp10asljGoC",
	"Wo1wuxCrethU/y4PQ+B8kcfxxltS0bSu5HAxEegQvp4znh5M1+kaxyTypLmBC88YV2E56zrF5xSbXgEi",
	"TfSmS/SRskcSRZAqivM/dynuKfUSnG5KsUr5t8+gvACW4tjjwNbAPJCrdJOUJwmWMS9dvLVPPhJ4Kd0b",
	"Wb6PHmQvSXlPIOlTiWYsdUNJU9kcjymY1C79hUabH7ZD7fOanu0pNZSknqIlNPXmGkxHq+I7g34X2PpM",
	"ZQCq3FsvVGpFHrfi/SW6jzK6tQc2QtMZ3ztLqd2Howhi0MfTTVRXanxXNtBUR5gN/LZgFZwuqdoegyLH",
	"vOO6CvpADNqx5+m9iF5i8ChjUPv6uBj0R/aq3jciVp71AuXhNPKsFw7v+mpnPysz/kvcPVtNHWyX7b19",
	"iev/i855VEwOdddYhKsew6pj610FVVO9FFQ7sA/T1NvXCLuaek17nC19I/3kCmjULFIvaecY047xqSkt",
	"fevk0XWo3j6Oax0v64P1MGdMW6Mx7DjNdR3GN8c7h6ut6e4ZdougUtC6/1E5r3Xz8+VBZgZtXJ0Ucxab",
	"Gx4+C4L46STiJxBmTyecPMoU83TCciSTmIvwdQTrAWJJS0McrygXs19Pfz1FEoHZrbYX3pQpnXsMYhWQ",
	"gnraON7jxlMXHHWyvJQ/f5OzqJtm+3nZ1cnBsL78G8/V7I+TpZ6fwk+d877ivwywVCSjeSoXcXGb63Q3",
	"jpPyPRenz1xWvZGcrCupist7c8s0mom5daoYfJC/R68u75Lq9X8rR0bvln05U7G5rUf38U6L0f7+aDGZ",
	"6IEm5ujCzWta4Gn3G+I3yQfreyKbzbSIqD4CNAzu9O/RyzdcQFLVIWUxi5manWajFkdT2RoMpwdqzUGH",
	"5oNsGGW32JeAtQnkNokVVEb2XkUML4SPWbgia/Bp+m+cZYyucewz+AqhgMjXIxD9clJ2tr/nwDa1+Mrc",
	"dcMQwQLnsWyfkY8gzRNZwZUs5CMjTXb6tTzVQmqJksTItD55MR9f9PTdH0ksgMm8Vekl13ivbub+h7kT",
	"tvn8Zgfomzny0Yf5KCDP9uahvNEp1lxbf/+7x/O88XSkloXEKbn+nuEA0mWwOSXrtu0AUm8bL9sO97Jv",
	"5Q+BoYyuAf82t9iHkW6CizsCugqvZi6q7oR3BlbnItgZaC4I1cd2hwJgIs4lX8fc4cRboeeCUAff4WB0",
	"qpnc973KEw6FLkVlvq8Gyio1rQKpztk0IC5AhF+a/roTE4+UxoDTYRm6uIU5FzTxKDPdxXfWOc1O0ilu",
	"o9SOsMDeQiIAaS2X/cvJjrZuzn8nCRFqc9M8eQQmtxpSQQQB2QiJnKUQuRSO5eJ+jc9O/T3Tz81iwWFv",
	"SFSt7se0N6Q7yoQdAPrVzVsQiJ1AOGUOGEhAuPqH/jK5dAprqOfDZPot1W1o/U83+v78Us7GmAt9oiN/",
	"j3GnSh/rXGBvbdKmJq1P5c1Lk/1xta+SZ/tD6xFo6/fvnwnW+mp8NNrq3cpCOx2svckl4kblrwFXjlG5",
	"Sr14ko3rk4l9US/yOG55tzVUFq2WD5eaTMJ6+Gj82e5Zb2gJRTI16OTjfdNTJ+FT1UzB06L3KGOl932q",
	"/tmvxFn/+59xxTHQbtRX+I5EbuZ6ewkeQhrZvUQ5IPnLp37px3AHseP2YejeYcyNwzHcNTwU/wsAAP//",
	"cvrqGVg7AAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
