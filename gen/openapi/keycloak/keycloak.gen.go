// Package keycloak provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package keycloak

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// KeycloakUserV1DTO defines model for KeycloakUserV1DTO.
type KeycloakUserV1DTO struct {
	ID        string `json:"id"`
	Email     string `json:"email"`
	Username  string `json:"username"`
	FullName  string `json:"fullName"`
	LastName  string `json:"lastName"`
	FirstName string `json:"firstName"`
	Position  string `json:"position"`
	Enabled   bool   `json:"enabled"`
	Photo     string `json:"photo"`
}

// GetKeycloakUsersParams defines parameters for GetKeycloakUsers.
type GetKeycloakUsersParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`
}

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Search users
	// (GET /v1/keycloak/users)
	GetKeycloakUsers(c *gin.Context, params GetKeycloakUsersParams)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetKeycloakUsers operation middleware
func (siw *ServerInterfaceWrapper) GetKeycloakUsers(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetKeycloakUsersParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetKeycloakUsers(c, params)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/keycloak/users", wrapper.GetKeycloakUsers)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/8xaX3PbuBH/Khy0D7kZWrQTX5rTm+eS3Li9qzO20z5kPB2IXEmISYIHgMqpGn73zgIg",
	"CZEETarV9Z5EAvvnt4td7ALUgcQ8K3gOuZJkeSAy3kJG9eMHIbi4B1nwXMI/rt4/3uFoIXgBQjHQNDFP",
	"AH/VvgCyJFIJlm9ISH674CIBQZavq5BkICXdjNO9qULCV18hVrfvRwm/bwgfNckI6XUVEqmoGqd6a6lK",
	"6ZCxXMEGhEt3VVUhEfBryQQkZPmlZgqNE1ozj/A5VtVgnsJai5kiVUj+Bvs45fT5swThcTVklKUv+hpy",
	"ukoRX0O34jwFmruE76qQrJmQ6u808zhnwy9yPUk+NoQdp63LNJ0koKbrLA1LXuLUTnMWICQpnQb6Z9rH",
	"jJFTbLnio078Aam4ZIrxfJTwL1VISgki98JpQrsTOCwhoV1OR4TjUMdOd6EcYO1C1zb1owrjGuJSMLV/",
	"wKw2YXRTqi0X7N+0NnAFVID4yEVGFVmSv/7zESNVM2D06FnSCN8qVZAKZbN8bTzJVIozn355CG4+3ZKQ",
	"7EBILZzsrjC4eQE5LRhZkjeLy8U1QqZqq9FEu6uIJhnLo43gZYFjVdgbjWKqYMOFyYQhgoP+vX1fdecL",
	"EBmTCKfHWgielLHyjkcH+zQkVvCCS5pK74Tm1o8D7IKnfVP04IipZv6APwMiMY7k4GB0wJ8jliEl7Vh0",
	"sM97D9MRwbCLYWcrytF7JHkpYugNl7kAmrTD3WDwr/Kz3Tgb+8kGFP4kIGPBChPl5AGoiLcggzUXgSYN",
	"vjG1DdQWAllAzNYMkkBqqiChii6Ijluh0+Q2IUvyEyh3l5Y6jAXNQGnFX7oqUYpWZ6QSzBiyJL+WIPYk",
	"JHajaiZN4e1vJFX1hLuHqcLawNeXl6b05gpybSstipTFGmr0VZq0buUxBZlm/LOANVmSP0VtyY9svY/6",
	"Bahqcp4KQfcm6Y9N1H4IZBnHICXuXvtgzcs8Qd7rmSjHwA10IgNobvMdTVkS4FYLUqHvcUvTWK76IfE5",
	"p3YrBAv4TZ/oIxcrliSQa4rXP/QpHjkPMprva7USKb//PxivsI6kgQSxAxEAcpkaUGYZFfsmCUz4445O",
	"Nxi3Te9BniqbVIMZ3d8uxzfKodleYo8QDWX7IHm2H8V7RFtQoVjMCpqrOaTRwXl7GdEY6zQXjEroVI9h",
	"Af0CNU7nqVczmKItk4pjpJ3C3B4STmBu+vcTeHcMvr3AOcXhvurc4fWRdS3olHNTyONSCLOfHA17EsBX",
	"/I/He/HYme6nfYegMdDpN3U57HSaX56wkpntydTLUqS2o5TLKEqfF4lcQFw8LyRb4QHseSFKUoV+wosE",
	"diPESMtjmm65VMt3l+8uCSKw2153H7+ri70MBKRUQRIoHhjnBKt9oBuqtm7f4OtPOKuVTpHlLJJP4KeG",
	"ZLpUuz5ekWZ+jjydGq/kdyMiNclkmTpEfNLueQqTJZnWzSMJ25HJkpwWuJHyo+1qJwuxXW4j4AO+T+au",
	"e9eWvynFk1fLrWeNmE/t6CnR6Qg6PR4dITMj0OYcX/tlzUs8E35j8mbFYFtaXTHzMqK5O7ICHsz7ZPa9",
	"VJDV9hiPOcL07DwfdSSiy7oC5ydqK8Gk5lNIfrvAQ8XQBmxcgMuEh7LaycGrRNC1CrFzZTsIef4vWhSC",
	"72gaCvgKsYIkNCOQfLfwnbJqd7ctdwJrWqaKLIm+TCkzbIW1LhISqw3PgK0+gucwoxFJrE7n4qU5sfV8",
	"9JGlCgTuW41dyBO8ursPP9x7YStzg/gC6Lt7EpIP95OA2HgP9M2aVllQtW01Ng0Nca+slCjBhbGuL4tY",
	"rt5et5dD9a3pgGIdjV61ttMfVTpwM7vhF+3o2+shvRi0XrWmCzuD1rqQeDW39ydn0I7J5tVs2rYzaHWK",
	"jj+83IPMOTDU2TUS37bxP492m1zSk9BNeh3vRc0NzYuJ1buT8SaaD4JNtfMBsBnn029y7nzqndTzQWiT",
	"73wwetUM1/2k8kRjZUpRvd83A3WVmleBdOdsGxAfICZvbH/dy4n6c9KoDlPc4lIqngVc2O7iv6xzRhzS",
	"aWmTzP6fXcL2JP/MMqb04uZltgKBSw25YooBNkKqFDkkPoNTZB62+OoyPHH7uVuvJZwMiWvuYUwnQ3rg",
	"QrkJYI5uwZpB6gUiufDAIArirf0SVgeFM+QUlx95maMQ/i03bWj7rVYAtqg3yn5k+1wk9n1KODX2OPcC",
	"J1uTH1tiX20uWIfZD4S1Pbh5mudZaNvz9+8JVvtoJtrmbOWgnQ/WXeQa8VHlbwE3gdGESss8y8ftzcSp",
	"qJ3PvzVoZ6guWp0Yri2ZhfX82fh7h2e7oDUUFGrR4ePjcaTOwqermYZnVJ9Qxuro+6X5j0iNs/3XiA3F",
	"KdDu9D8LPBu5nRvsJWQMeeL2EvUAysenYe1/hK94L3y/G/tyN+Wb3R/ha91T9Z8AAAD//2HAPWePJQAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
