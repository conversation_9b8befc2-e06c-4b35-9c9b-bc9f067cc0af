// Package adminpermission provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package adminpermission

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// CategoryWithCheckedV1DTO defines model for CategoryWithCheckedV1DTO.
type CategoryWithCheckedV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	IsActive bool   `json:"isActive"`
}

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// PermissionWithCategoriesV1DTO defines model for PermissionWithCategoriesV1DTO.
type PermissionWithCategoriesV1DTO struct {
	Name       string                     `json:"name"`
	Categories []CategoryWithCheckedV1DTO `json:"categories"`
}

// UpdateCategoriesPermissionsAsAdminJSONBody defines parameters for UpdateCategoriesPermissionsAsAdmin.
type UpdateCategoriesPermissionsAsAdminJSONBody = []PermissionWithCategoriesV1DTO

// UpdateCategoriesPermissionsAsAdminJSONRequestBody defines body for UpdateCategoriesPermissionsAsAdmin for application/json ContentType.
type UpdateCategoriesPermissionsAsAdminJSONRequestBody = UpdateCategoriesPermissionsAsAdminJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all permissions as admin
	// (GET /v1/admin/permissions)
	GetPermissionsAsAdmin(c *gin.Context)
	// Update categories permissions as admin
	// (PATCH /v1/admin/permissions)
	UpdateCategoriesPermissionsAsAdmin(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetPermissionsAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetPermissionsAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetPermissionsAsAdmin(c)
}

// UpdateCategoriesPermissionsAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateCategoriesPermissionsAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateCategoriesPermissionsAsAdmin(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/admin/permissions", wrapper.GetPermissionsAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/permissions", wrapper.UpdateCategoriesPermissionsAsAdmin)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xa3W7cthJ+FYHnXCSAsrLzh5y984mdwG1TG7bTXARGQUuzu4wlUiGpTbYLvXvBH0lc",
	"SZSlLTZ1gd54JXI4883wGw5JeYtiluWMApUCzbdIxCvIsH58iyUsGd98InL1dgXxPSS/HZ/eXKi+nLMc",
	"uCSgJUmi/spNDmiOCJWwBI5C9P3Zkj2jOFOt56dVQyP2+qVuYzwBjubHZYiIOIklWYOj7o6xFDB1JV+U",
	"ITJqaykhOaFLV+h5WYaIw9eCcEjQ/LMCaYc5dm7DSgO7+wKxRGWIzjhn/ApEzqgAj8cxSx4yH6IMhMDL",
	"YTnlizF9fjoo+KoWvNEiA6IvyxAJieWw1GsrVQjP5NXz0gqkHRSaIDRu7uBzvKrA9MX6EnhGhCCMapYZ",
	"xhEQvrDXApp2EjL98F8OCzRH/4kaLkeWyJGXxWUNB3OON+3Je5BgnbhYbjkYux6rmENccCI31wqgceSk",
	"kCvGyR9YEkZVwx1gDvwd4xmWaI5++nSjoqgHqJTQvahWvpIyR6XSTeiCadBEpqrn8sN1cHJ5jkK0Bi60",
	"crQ+Vq6zHCjOCZqjF7OjmUrEHMuVRhOtjyOcZIRGS86KXLWVYac12pmLPoGt/j0/Ldv9eT3p2t4SpPpJ",
	"QMSc5CYE6ApkwakIcJoGjnyARaCVzJD2geuQnSdojt6DbNgkTsSJEkNqikwma1vPj45M+lIJVJvFeZ6S",
	"WKuJvggTfsOd0RQbJnGbZ3qidp11cAeiiGMQYlGk6SbgIDmBNSRKy8uJ2Icg96xxPbjO6RqnJAkUy0HI",
	"YGEIqbEcd+fsI8WWyBXgF12hd4zfkSQBqiWe/68rccNYkGG6qcwKJfnqb3BeAqc4DQTwNfAA1CiTwUWW",
	"Yb4xnPMyVOUnXgq1MmgqNpOMbkudbvGqJ4Z5giUETXKNZL8Z13DPkwo6oP9nyeZRZEGzekpeQPnPztVC",
	"z8C/mfo4M3VcXg3m7G4N4ywpYtmpfVV7tLVPffWPs5wJnApvhx6tH3uGc5Z2a65uHKjJpn+rfnpUFgK4",
	"6G2MtupnZ0ifkaYt2trnjWfQjkBrL2CFYW2PIzvvkWAFj6HTXFAOOGma27sW/3bkHjZxyvB92/9eUN0Z",
	"H57rvt4OtgGhPsC94tlmEO+ObI65JDHJMZVTRKOt8/YwoqGh40IwqKGVAP0Kujk2LOdJuQmDohURkqnV",
	"Zp/BzWFxj8H1OW6PsWsC3x4YOSbgvgWmNdYn1vaglZFmLYoLzk1N2Wn2JIBv/dpt7/Cx1d1N+5ZA7aBz",
	"tkPzz51T3efb8lZJqBIltEDBU3t6E/MoSu9niZhBnN/PBLlTB/H7GS9QGfoFnyWwHhBWsizG6YoJOX9z",
	"9OYIKQS2wLVr+UW1nRQBh1TtZALJAhOc4G5T10d7n6PL43vVq42O0eUWXI9Cp96O1mrnx6vS9E/Rp1Pj",
	"iXg6oFKLjNapKeLTdsVSGK1Jc8+n6aMAPlqTU8VrLdVdyWgltlDXCs7U++jRVfltxv9ctYyeLbee1Wou",
	"m9Z92Oko2p+PjpKJDLQ5xxZ+XdMSz9BvSN8kDjal1VUzLSPqO0Sr4Nq8jx6+ERKyyh8TMUeZ7p0Wo5ZG",
	"FbK2wumJ2mgwqXkbou/6/rtvATYhUNMkV1AHOXiScLyQIebxiqwhZPR3nOecrXEacvgCsYQkNC2QPJ2h",
	"EBGl7GsBfNOYr8PdHLsSWOAilWiOUIiAFpk69GhbKETWGgqRY09fIBiLSsTadC457R1p2Y3RO5JK4Grd",
	"qv1SY4InF1fh2ZUXtjQ3yQ+AvrhCITq7GgXE8j3Q19LaZI7lqrFYb2hQ+3bChbGoLmarjxit2/Mew5qN",
	"XrN2pz9otP/zys7XlB67irRes2YXdgCrVSHxWm6OgAewrpLNa9ls2w5g1Sk6fnq5B5lDYKiya4DfduN/",
	"GOs2uYQnoev02l2L6pu7BxOrcz/nTTQfBJtqhwNgM85n3+Tc4cw7qeeD0CTf4WB0qpma973KEzZfapv1",
	"vm6oqtS0CqR3znYD4gNE6ivzTk5U36MHbZjiFhdCsixg3O4u/mKdM+qUnNY2yu0ESxwsFAJQ0fLFv+rs",
	"eOvX/AvJiNSTS4vsDriaaqCSSAJqIyQLTiHxOZyqwf0eHx+Fey4/F4uFgL0hMT26H9PekK4Zl24CmKNb",
	"sCCQeoEIxj0wkIR49av5ylyRwmlyistbVlClhH2jZhvafLPnoLaoJ6o3xUKae3H1PoZOtT/OvcDe3tBd",
	"T+yrzQUbMBTqgl35oxZP8zwJbXP+/pFgdYwmoq3PVg7a6WDdSa4Q71T+BnBNjJoqzeBJMW5uJvZFvSjS",
	"tMVup6kqWi0OV55Mwnr4bPzR9GwmtIKilFp06vFml6mT8OlqpuEZ03uUsYp9H+r/FapwNv89ZKk4BtqF",
	"/hccz0Ju+3r3EiIGmrh7iapB6VdP/dYfw5fcB77hDn29HfPd9jF8sb0t/wwAAP//ZkbTcI8oAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
