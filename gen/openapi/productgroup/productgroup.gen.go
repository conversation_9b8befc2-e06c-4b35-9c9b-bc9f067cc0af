// Package productgroup provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package productgroup

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupCreateV1DTO defines model for GroupCreateV1DTO.
type GroupCreateV1DTO struct {
	Name string `json:"name"`
}

// GroupFullV1DTO defines model for GroupFullV1DTO.
type GroupFullV1DTO struct {
	ID             int64    `json:"id"`
	Name           string   `json:"name"`
	Type           string   `json:"type"`
	ParticipantIDs *[]int64 `json:"participantIDs,omitempty"`
	UserIDs        *[]int64 `json:"userIDs,omitempty"`
	RoleIDs        *[]int64 `json:"roleIDs,omitempty"`
}

// GroupShortV1DTO defines model for GroupShortV1DTO.
type GroupShortV1DTO struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	RoleCount        int64  `json:"roleCount"`
	ParticipantCount int64  `json:"participantCount"`
	UserCount        int64  `json:"userCount"`
}

// GroupUpdateV1DTO defines model for GroupUpdateV1DTO.
type GroupUpdateV1DTO struct {
	Name           *string  `json:"name,omitempty"`
	Type           *string  `json:"type,omitempty"`
	ParticipantIDs *[]int64 `json:"participantIDs,omitempty"`
	RoleIDs        *[]int64 `json:"roleIDs,omitempty"`
}

// CreateProductGroupJSONRequestBody defines body for CreateProductGroup for application/json ContentType.
type CreateProductGroupJSONRequestBody = GroupCreateV1DTO

// UpdateProductGroupJSONRequestBody defines body for UpdateProductGroup for application/json ContentType.
type UpdateProductGroupJSONRequestBody = GroupUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get Product Groups
	// (GET /v1/products/{productID}/groups)
	GetProductGroups(c *gin.Context, productID int64)
	// Create Product Group
	// (POST /v1/products/{productID}/groups)
	CreateProductGroup(c *gin.Context, productID int64)
	// Delete product group
	// (DELETE /v1/products/{productID}/groups/{groupID})
	DeleteProductGroup(c *gin.Context, productID int64, groupID int64)
	// Get product group with roles and participant IDs
	// (GET /v1/products/{productID}/groups/{groupID})
	GetProductGroupFull(c *gin.Context, productID int64, groupID int64)
	// Update product group
	// (PATCH /v1/products/{productID}/groups/{groupID})
	UpdateProductGroup(c *gin.Context, productID int64, groupID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetProductGroups operation middleware
func (siw *ServerInterfaceWrapper) GetProductGroups(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProductGroups(c, productID)
}

// CreateProductGroup operation middleware
func (siw *ServerInterfaceWrapper) CreateProductGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateProductGroup(c, productID)
}

// DeleteProductGroup operation middleware
func (siw *ServerInterfaceWrapper) DeleteProductGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteProductGroup(c, productID, groupID)
}

// GetProductGroupFull operation middleware
func (siw *ServerInterfaceWrapper) GetProductGroupFull(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetProductGroupFull(c, productID, groupID)
}

// UpdateProductGroup operation middleware
func (siw *ServerInterfaceWrapper) UpdateProductGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateProductGroup(c, productID, groupID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/products/:productID/groups", wrapper.GetProductGroups)
	router.POST(options.BaseURL+"/v1/products/:productID/groups", wrapper.CreateProductGroup)
	router.DELETE(options.BaseURL+"/v1/products/:productID/groups/:groupID", wrapper.DeleteProductGroup)
	router.GET(options.BaseURL+"/v1/products/:productID/groups/:groupID", wrapper.GetProductGroupFull)
	router.PATCH(options.BaseURL+"/v1/products/:productID/groups/:groupID", wrapper.UpdateProductGroup)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbWXPbuhX+Kxy0D8kMI3qLJ9WbGy/jtqk9ttM+ZDwdmDySEJMEA4BKdTX873ewcCco",
	"Uo4S3Tt+MgUcnPMBZ8XiNfJplNAYYsHRdI24v4AIq88Lxii7A57QmMN/Ds8fbmRrwmgCTBBQND4NQP4V",
	"qwTQFHHBSDxHLvr/O8oCYGh6lLkoAs7xvJ/uOHMRffoKvrg+7yV8XxA+KJIe0pPMRVxg0U91aqhSXiEj",
	"sYA5sCrdYZa5iMG3lDAI0PRLPsjVi1BOs4avMqsczKObS9FdKHPRFaNp8pEBFraVjnHUP48WPjXCKuwy",
	"DUOLKBJYVmJO32kYSE1HNZRkpyd1OO5mzNI6EswE8UmCY3F9ruULiLgdQk1iVswPM4ZXTfUzGsKP5Xpa",
	"9G2w5pQD+7Gi3zc1TAJkFtkMsmr7fkGZ2Dd1f6RpLAYuSyMCSL1uOfxkjAK3lHG6UVPVKXQsSlW6Vamf",
	"k+BF8WKHvne8E98bpLqjLGstmAzx4KeMiNW9THB6ic5SsaCM/IYFobFseALMgF1SFmGBpugf/32QQVsN",
	"QFPTiwrmCyESlEneJJ5RhYmIUPbcfrp3zm6vkYuWwLhijpaHcmY0gRgnBE3R8eRgcqJULxYKjbc89HAQ",
	"kdibS+XKtsxttXo+FjCnTGu5i2Ct/l6fZ83+BFhEuITTGpowGqS+sLZ7a/PVxZbRhHIccmuHGq0+O4ZL",
	"K+GdjT1T1f1rbWEtltJ1eGejt9ZRuTKkS0jZ5q3N98oyqEbQvcSwNMVV7bfHacp8aDWnMQMclM1NY7Br",
	"+RlWfkjxc3P+naDaGu/XdVdviQ3NQUXJALjPSKL9CV2B4I4hdzTtBCkfYMrlrgNNdKtJrjQ3FZVwBEJN",
	"4kuTqSF2VEoiskV6UB5dp6iAh6oRWLAUjCtjCXSW+3geuhuBKcse5XBd+6oZHh0c6II3FqBTAk6SkPhq",
	"Jt5XriNIKaEIeX9lMENT9BevLLQ9U2V7zdzcjH0qutTnf5/6PnA+S8Nw5cypaCywZHEyEmofwo5NQAeo",
	"63iJQxI4csGBC8csr8Jy2DaMzzE2oRcCTXTcJrqk7IkEAcSK4uhvbYoHSp0Ix6tcrJr8+18weQEsxqHD",
	"gS2BOSBH6ZyTRhFmK23lTm65hZ0LPJcWjqr2jx5lWqa8w5309qDhUW2H0mQ1nvvkUkpTf6fB6odpqbV3",
	"6tBRbe0dTezcaTCtWWUvdP1RHm/HqvTr8Kq/+wp48Orke+nkxqxqtmZ3882JtZbkUQAh6KOMOrJz1b4x",
	"LGiyfQwLbqtuUIZvE2vWpFfokG2GLcP3pFutgqC+1K/OuJfOqO29oaqenDuwgnW+E7Fw1P7DwXHgVHbQ",
	"zvX55vr2Mg3DV8fbcX4tzzbHFNB1Pet96atv72s1PcYre2ttLPxFx6Kqs7WNWVWTvWbVhnPvqMivHnhu",
	"LPI18a8r8ocHoVQhfS0r/gihx1jVwLKir8aPVr0HczXaSjwbQ+qtawf82QuGts4hx3NonPR2M2gfJvfT",
	"Wc6WRwzyFoQLKpW7zeDybnuLwcW18xZjlwS+bxg5ZMFtJ+mNsTay5gwaR8/60N1PGdMuXGu2OIDtoL7e",
	"3rLHRnf7fLtBUEywcjek0nXjVujLo0xpOiLofJ6y0Nz+8Knnhc+TgE/AT54nnDzJ3Pg8YSmS+ddG+C6A",
	"ZQ+xpKU+DheUi+mHgw8HSCIwEaYZOm/ycoQ7DEKVRgQ1Z8HO08pRlx9lmj+TP69MfhnGq6IkG8PbgmQ4",
	"V6MfK0vdP4afco03/G0PS0UymKeuJy3c7mgIgzkp27Nx+sxlwTaQU+W6quDy0dxADWZibqQKBhfy9+DR",
	"+T1TOf6fectgbVXzWcHmtmzdxjorjLa3xwqTkRZofI7O7LzGOZ42vz5+o2ywTK1VNuM8onjyZBjc69+D",
	"h6+4gKhVPOXMVO+4NWpwlEvWZDjeUUsO2jUf5U5HbnO6ArBeAqkmsYBikZ03AcMz4WLmL8gSXBr/DycJ",
	"o0scugy+gi8gcHULBG8n+Z7sWwpsVYovlruscgOY4TSUOz/kIojTSFadShZykZEmd6mlPLXz0RIliZFZ",
	"eVVinlB0bBkvSSiAybhVzEuOcd7c3LkXd1bY5oHLBtA3d8hFF3eDgPzZN80tudJorWJ1FbYDqXkisUou",
	"3zrsQLp0NqtkXbbtQOpt7ajIYl7VjcwuMOTe1WPfpvDfjXTjXNzi0IV71WNR8cRho2O1njVYHc0Gwbja",
	"7gAYj7PJzx+z7Up8xfVsEErn2x2MVjaTet8qPWFf6FSUx/uiIc9S4zKQqpxNAWIDRPiZqa9bPvFEaQg4",
	"7pehk5ufckEjhzJTXbwwz2l2kk5xGzTtAAvszCQCkKtlW/+8szVbO+d/kYgIpdw4jZ6ASVVDLIggIAsh",
	"kbIYAtuEQzm4e8aHB+6W4edmNuOwNSSqRndj2hrSPWWi6gB66+bMCIRWIJwyCwwkwF/8W7/9zY2i0tTx",
	"9pd+j3UZWv6LgX74cSZ7Q8yFPoaUv4eYUzGfyrnA1rOJ6zOpP2o2C1Z7v1x96jwKbbn//plg1RqNRFvs",
	"rSpox4OtKjlHXMv8JeDCMApTKQePWuPyZGJb1LM0DBvWXWnKk1bDhvOZjMK6e2/82eZZKjSHIpkadPLz",
	"oW6po/CpbKbgadFbpLHc+j4V/9qU4yz/2cmY4hBoN+qFviWQm77OWoL7EAfVWiJvkPzlV7f0fbg423Bl",
	"1ndZNuSabB8uyB6z3wMAAP//9BqRI0Y4AAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
