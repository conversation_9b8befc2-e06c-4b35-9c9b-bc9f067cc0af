// Package admingroup provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package admingroup

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// Defines values for AdminGroupCreateV1DTOType.
const (
	AdminGroupCreateV1DTOTypeCustom AdminGroupCreateV1DTOType = "custom"
	AdminGroupCreateV1DTOTypeSystem AdminGroupCreateV1DTOType = "system"
)

// Defines values for GetGroupsAsAdminParamsType.
const (
	GetGroupsAsAdminParamsTypeCustom GetGroupsAsAdminParamsType = "custom"
	GetGroupsAsAdminParamsTypeSystem GetGroupsAsAdminParamsType = "system"
)

// Defines values for GetGroupsAsAdminParamsStatus.
const (
	Active  GetGroupsAsAdminParamsStatus = "active"
	Archive GetGroupsAsAdminParamsStatus = "archive"
)

// Defines values for GetGroupsAsAdminParamsSort.
const (
	Name      GetGroupsAsAdminParamsSort = "name"
	Product   GetGroupsAsAdminParamsSort = "product"
	RoleCount GetGroupsAsAdminParamsSort = "roleCount"
	Type      GetGroupsAsAdminParamsSort = "type"
	UserCount GetGroupsAsAdminParamsSort = "userCount"
)

// Defines values for GetGroupsAsAdminParamsOrder.
const (
	Ascend  GetGroupsAsAdminParamsOrder = "ascend"
	Descend GetGroupsAsAdminParamsOrder = "descend"
)

// AdminGroupCollectionV1DTO defines model for AdminGroupCollectionV1DTO.
type AdminGroupCollectionV1DTO struct {
	Items []AdminGroupV1DTO `json:"items"`
	Meta  PaginationV1DTO   `json:"meta"`
}

// AdminGroupCreateV1DTO defines model for AdminGroupCreateV1DTO.
type AdminGroupCreateV1DTO struct {
	Type      AdminGroupCreateV1DTOType `json:"type"`
	Name      string                    `json:"name"`
	ProductID *int64                    `json:"productID,omitempty"`
}

// AdminGroupCreateV1DTOType defines model for AdminGroupCreateV1DTO.Type.
type AdminGroupCreateV1DTOType string

// AdminGroupUpdateV1DTO defines model for AdminGroupUpdateV1DTO.
type AdminGroupUpdateV1DTO struct {
	Name     *string                 `json:"name,omitempty"`
	IsActive *bool                   `json:"isActive,omitempty"`
	Users    *[]UserProductLinkV1DTO `json:"users,omitempty"`
	Roles    *[]RoleIDV1DTO          `json:"roles,omitempty"`
}

// AdminGroupV1DTO defines model for AdminGroupV1DTO.
type AdminGroupV1DTO struct {
	ID        int64              `json:"id"`
	Name      string             `json:"name"`
	Type      string             `json:"type"`
	IsActive  bool               `json:"isActive"`
	Product   *ProductBasicV1DTO `json:"product,omitempty"`
	UserCount int64              `json:"userCount"`
	RoleCount int64              `json:"roleCount"`
}

// CategoryWithCheckedV1DTO defines model for CategoryWithCheckedV1DTO.
type CategoryWithCheckedV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	IsActive bool   `json:"isActive"`
}

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupBasicV1DTO defines model for GroupBasicV1DTO.
type GroupBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// GroupViewAdminEntityV1DTO defines model for GroupViewAdminEntityV1DTO.
type GroupViewAdminEntityV1DTO struct {
	ID               int64                      `json:"id"`
	Name             string                     `json:"name"`
	Type             string                     `json:"type"`
	RoleCount        int64                      `json:"roleCount"`
	ParticipantCount int64                      `json:"participantCount"`
	UserCount        int64                      `json:"userCount"`
	Categories       []CategoryWithCheckedV1DTO `json:"categories"`
}

// GroupWithDetailsV1DTO defines model for GroupWithDetailsV1DTO.
type GroupWithDetailsV1DTO struct {
	ID        int64                  `json:"id"`
	Name      string                 `json:"name"`
	Type      string                 `json:"type"`
	IsActive  bool                   `json:"isActive"`
	CreatedAt time.Time              `json:"createdAt"`
	Product   *ProductBasicV1DTO     `json:"product,omitempty"`
	Users     []UserWithProductV1DTO `json:"users"`
	Roles     []RoleWithProductV1DTO `json:"roles"`
}

// PaginationV1DTO defines model for PaginationV1DTO.
type PaginationV1DTO struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
	Total  int64 `json:"total"`
}

// ProductBasicV1DTO defines model for ProductBasicV1DTO.
type ProductBasicV1DTO struct {
	ID       int64   `json:"id"`
	IID      *string `json:"iid,omitempty"`
	TechName string  `json:"techName"`
	Name     string  `json:"name"`
}

// RoleBasicV1DTO defines model for RoleBasicV1DTO.
type RoleBasicV1DTO struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	IsActive bool   `json:"isActive"`
}

// RoleIDV1DTO defines model for RoleIDV1DTO.
type RoleIDV1DTO struct {
	ID int64 `json:"id"`
}

// RoleWithProductV1DTO defines model for RoleWithProductV1DTO.
type RoleWithProductV1DTO struct {
	ID       int64              `json:"id"`
	Name     string             `json:"name"`
	Type     string             `json:"type"`
	IsActive bool               `json:"isActive"`
	Product  *ProductBasicV1DTO `json:"product,omitempty"`
}

// UserBasicV1DTO defines model for UserBasicV1DTO.
type UserBasicV1DTO struct {
	ID       int64  `json:"id"`
	Email    string `json:"email"`
	FullName string `json:"fullName"`
}

// UserProductLinkV1DTO defines model for UserProductLinkV1DTO.
type UserProductLinkV1DTO struct {
	ID        int64  `json:"id"`
	ProductID *int64 `json:"productID,omitempty"`
}

// UserWithProductV1DTO defines model for UserWithProductV1DTO.
type UserWithProductV1DTO struct {
	ID       int64              `json:"id"`
	Email    string             `json:"email"`
	FullName string             `json:"fullName"`
	Product  *ProductBasicV1DTO `json:"product,omitempty"`
}

// GetGroupsAsAdminParams defines parameters for GetGroupsAsAdmin.
type GetGroupsAsAdminParams struct {
	// Search data for search
	Search *string `form:"search,omitempty" json:"search,omitempty"`

	// ProductIDs Product IDs
	ProductIDs *[]int64 `form:"productIDs,omitempty" json:"productIDs,omitempty"`

	// Type Filter by type (custom or system).
	Type *GetGroupsAsAdminParamsType `form:"type,omitempty" json:"type,omitempty"`

	// Status Status of the product.
	Status *GetGroupsAsAdminParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Limit Limit the number of entities returned.
	Limit *int64 `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Offset the number of entities returned.
	Offset *int64 `form:"offset,omitempty" json:"offset,omitempty"`

	// Sort Sort the groups by a field.
	Sort *GetGroupsAsAdminParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Order Order
	Order *GetGroupsAsAdminParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetGroupsAsAdminParamsType defines parameters for GetGroupsAsAdmin.
type GetGroupsAsAdminParamsType string

// GetGroupsAsAdminParamsStatus defines parameters for GetGroupsAsAdmin.
type GetGroupsAsAdminParamsStatus string

// GetGroupsAsAdminParamsSort defines parameters for GetGroupsAsAdmin.
type GetGroupsAsAdminParamsSort string

// GetGroupsAsAdminParamsOrder defines parameters for GetGroupsAsAdmin.
type GetGroupsAsAdminParamsOrder string

// UpdateCategoriesVisibilityForGroupsAsAdminJSONBody defines parameters for UpdateCategoriesVisibilityForGroupsAsAdmin.
type UpdateCategoriesVisibilityForGroupsAsAdminJSONBody = []GroupViewAdminEntityV1DTO

// CreateGroupAsAdminJSONRequestBody defines body for CreateGroupAsAdmin for application/json ContentType.
type CreateGroupAsAdminJSONRequestBody = AdminGroupCreateV1DTO

// UpdateCategoriesVisibilityForGroupsAsAdminJSONRequestBody defines body for UpdateCategoriesVisibilityForGroupsAsAdmin for application/json ContentType.
type UpdateCategoriesVisibilityForGroupsAsAdminJSONRequestBody = UpdateCategoriesVisibilityForGroupsAsAdminJSONBody

// UpdateGroupAsAdminJSONRequestBody defines body for UpdateGroupAsAdmin for application/json ContentType.
type UpdateGroupAsAdminJSONRequestBody = AdminGroupUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get groups and meta
	// (GET /v1/admin/groups)
	GetGroupsAsAdmin(c *gin.Context, params GetGroupsAsAdminParams)
	// Create group
	// (POST /v1/admin/groups)
	CreateGroupAsAdmin(c *gin.Context)
	// Get groups with categories visibility
	// (GET /v1/admin/groups/categories)
	GetGroupsWithCategoriesVisibilityAsAdmin(c *gin.Context)
	// Update categories visibility for groups
	// (PATCH /v1/admin/groups/categories)
	UpdateCategoriesVisibilityForGroupsAsAdmin(c *gin.Context)
	// Delete group
	// (DELETE /v1/admin/groups/{groupID})
	DeleteGroupAsAdmin(c *gin.Context, groupID int64)
	// Get group
	// (GET /v1/admin/groups/{groupID})
	GetGroupAsAdmin(c *gin.Context, groupID int64)
	// Update group
	// (PATCH /v1/admin/groups/{groupID})
	UpdateGroupAsAdmin(c *gin.Context, groupID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetGroupsAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetGroupsAsAdmin(c *gin.Context) {

	var err error

	c.Set(AuthorizationScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetGroupsAsAdminParams

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "productIDs" -------------

	err = runtime.BindQueryParameter("form", true, false, "productIDs", c.Request.URL.Query(), &params.ProductIDs)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productIDs: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", c.Request.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter type: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetGroupsAsAdmin(c, params)
}

// CreateGroupAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) CreateGroupAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateGroupAsAdmin(c)
}

// GetGroupsWithCategoriesVisibilityAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetGroupsWithCategoriesVisibilityAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetGroupsWithCategoriesVisibilityAsAdmin(c)
}

// UpdateCategoriesVisibilityForGroupsAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateCategoriesVisibilityForGroupsAsAdmin(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateCategoriesVisibilityForGroupsAsAdmin(c)
}

// DeleteGroupAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) DeleteGroupAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteGroupAsAdmin(c, groupID)
}

// GetGroupAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) GetGroupAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetGroupAsAdmin(c, groupID)
}

// UpdateGroupAsAdmin operation middleware
func (siw *ServerInterfaceWrapper) UpdateGroupAsAdmin(c *gin.Context) {

	var err error

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateGroupAsAdmin(c, groupID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/admin/groups", wrapper.GetGroupsAsAdmin)
	router.POST(options.BaseURL+"/v1/admin/groups", wrapper.CreateGroupAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/groups/categories", wrapper.GetGroupsWithCategoriesVisibilityAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/groups/categories", wrapper.UpdateCategoriesVisibilityForGroupsAsAdmin)
	router.DELETE(options.BaseURL+"/v1/admin/groups/:groupID", wrapper.DeleteGroupAsAdmin)
	router.GET(options.BaseURL+"/v1/admin/groups/:groupID", wrapper.GetGroupAsAdmin)
	router.PATCH(options.BaseURL+"/v1/admin/groups/:groupID", wrapper.UpdateGroupAsAdmin)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xcX2/bOBL/KoLuHraAGidt2u35Lduki9ztXoI03X0oggMjjWM2kqglKfd8hr/7gX8k",
	"UhIpS27UJkCeIlPD4Y/D+UdylE0Yk6wgOeSchfNNyOIlZEg+niQZzn+lpCzekzSFmGOS/3F0en0hXhaU",
	"FEA5BkmKOWTNh79TWITz8G8zw36mec8MY8VuG4V8XUA4DxGlaB1G4X9fEpoADedH2yjMgCPBEqXpxSKc",
	"f+5nfonucI4M1u2Nxe7VdhuFFP4qMYUknH/WePUYNzUOcvsFYi6AWUKggDh4BJCjDMRf3Z9xivO7sDFy",
	"JLokZczPTy1KnHO4AypJ78hL0/r22O7+upbRJoS8zAR2tmYcsjAK45Jxklnwu8MftScuKSOFu3/en4rE",
	"P2/MTmKOV/bcbwlJAeXtye+UkFhqSlIYrkdXJIXz0506dLyNwpIBHc74EwN6qRbrN5zf7xzh9XbbK8Ja",
	"eMNUWPb5BTEcVyO3xa41aYRVqA42U9su3mjZvydlzsdr589awnt2f9tWTgPF5ttV05ttFL5HHO4IXf+J",
	"+fL9EuJ7SHy6mvihKe0Mz093YRVaOkzpXw9R+q5DSiqrtMZxGegZpYReAStIznzWGZNkt1fKgDF0108n",
	"5qKGbjivLuGbmvBaeysvqTBLxhHvp3qrqUrmWTyfg9OdIiUEM80GPmtWFRiXrNsG+aNV63iYapmQ0bOw",
	"PfqnY0SvGir/huGr9HZnOcd87dNGZal4hIf3GnePMxbO6MHWY1hcR5TjGBco53t6wG/0v8fDlvphnbRD",
	"U2y/3RGKPXpkK4NXrcSqnwJHOGUPHkFjmc0lJ1ISC0IzxMN5KDKdlxybOTUEaaQjSA6uFV1jER86ML/d",
	"KykSgtOMd5rLu72yozEj/NzWHCP7auhqku4Q307pO54lxRneQ6WFgZPFgsEefaV3JRyl++TyLXko+DWW",
	"irHLLrrqMmEsanBq2kHFSHMak+dLZwXx8t+7CN2xqe7as3kRNvAcrn2SqbZMU4mlC9GHpONFBrvN1gpP",
	"v0M6dmzxhG8SzrBP1SBDON258IsyTQfYQzTdEkUaqYXFtWrOnfF09jXgxETzu6wpdzruYfrpjHKDtaml",
	"FtPr59GRU0HF7gnikmK+/igYqcFPSr4kFP9PxlTRcAuIAv1QpUH//PNa7IdkB+HS5FuTEi05L8Kt4I3z",
	"BZFLg3kqV+H3j8HJ5XkYhSugTDIPV0dCnKSAHBU4nIevDw4PjmWCyJcSzWx1NENi9zC7E2mbbLtTMTkB",
	"FlNcKJjhr8ADRRKgPAky4OgglKypnMl5oohk9sdOmNySqFQUZcBljvO5zTRBHAULQgMGiMZL4UBF818l",
	"0HXlW+dh/VItR9dOt2KJm5z1igXnp8zDtVZv1uBcZ2F1XlppccsE2kmXA8QHnHKgwe06EJTBT+qsMBDz",
	"lceHLw482HQ0MagSWKAyFWiEs9BnkProMaoOIztnkA5IH+XOPCCLgC8h0DLwwai38S4gSAU6A6duEKvV",
	"jIF+PL+J9EtiycvsFqhABmIji4EFFHhJc0h8+KrczQHv6DAakhk6EF3IRHBfSHUa6cC0N6SPhCpA2gBv",
	"1wEKFhhSLwxGqAdElbBUq9bMXyrP2Nwvmq3lkCW9kE7RIx79zqlQLIY8sRWqahD8xZNj9BsRT9Q5nLTa",
	"V4eH6vAt56B22qgoUhxLHzX7wpTLNcMPuypp38FI/9vyj03fGLAyjoExEdDXQmsohhUkwmscPyBEx0Gk",
	"A9t5vkIpTgIReYHxQLs2ieWo6+o/5UjHqArw6y7RB0JvcZJALile/aNLcU1IkKF8XQ3LBOWbHzB5DjRH",
	"acCAroAGIHqFMjiXWYbo2h3chE2gOxG0rLuEUGSdBWGO+KhuqBSXTlxULyULExm1WH4hyXoKlbVuzHzq",
	"GsjDAEzyaolCOzvjtITthOblPmbyYm0YlD7FeDanR2lOLVtw2tE26uSes+Yh9a409Cvmy8B0CVaY4Vuc",
	"Yr7uSUzlYXbd54+6i22W36Dvg87w/Mf23ZyyL87wJWBqS+A55jzNmOPX5J4ohHi8dMhRFgy4mcmdlhqy",
	"ayGqo8s0PhDa3tTtG7qmMI+HDVjTG/Al0AwzsT9vGWwpV+DZXB+nuQ40rDHhbiP/np9ulWBSUNfxTWyn",
	"st2TV6qXrbyy98RF5VLypE7uzwrEl2Z7pgF10kDH0Uv/FtazLWutfrWjDZaIBbcAedMglEieDeJxGkRL",
	"MT1hqj+J8+ZpT0Kbf/gu6DnFeyIp3r5pnNtI1MvHaidTHmrY5bBeE1F51BM40njO/55e/jf8TKMweX44",
	"3zRfqVN2b/tsU19ObR00BWEoZd4Xsrd8dHSv6pm6jc3TF8f7DZX1Cx2WVQFTt3G2EX8aXVyDmLbZRj+v",
	"PZ0aBG4Rw0p/TtH4PWOkpDF0msucAkpMc30L2vjdSNX1m3tYxylB9+35O0F1V7x/rV1vO9h6iFyAneTZ",
	"uhdvg9aqKhxDOttYv3Yj6us6TAS9HFoG4GbQtbF+Oo/Jjeg0W2LGifA0+3Q2ZeR7dK4rvPfou8LwdUfP",
	"IQL3OZhWXx9ZewYti1S+KC4pVfGk0ewxAJ//arZ39LH1umv2LYJ6glatiMzfWlUin29EcqXCk0rwSprq",
	"ahA2n83S+4OEHUBc3B8wfCuytPsDWsrSFx/hywRWPcSClsQoXRLG5+8O3x3Kkhcd9jpXvlV+ygIKqchi",
	"Ak7s22qdpOqE0wqajutjJy9rkXwMzcHacK56fbws9XX4CH7SNH5iL3pYSpLBPKWK+LhdkRQGc5K65+P0",
	"icmalmGcrChec6k+FxjMRAfqmsGZ+D24dxV+Tf9/VS2DV8uOZzWbS9O6j3ZajPbXR4vJSA3UNkcWfl7j",
	"DE+pXx+/UTpoQqvNZpxF1GVJmoGqaRreXVZLVfOpM/qKmXw7TkYtjkJkbYbjDdVwUKYpCw7FhtvlgDtl",
	"XVKiwU8JRQse6YKsiOT/QUVByQqlEYUvEHNIItUCyYu9qsCsch05llX+FYXWeHIbrkYUJHrMQfVEpoqu",
	"npcqp7u4is6uvrGG7uIqjMKzq0FATE2h+xylsMpg/ScpO+sKuwN/p+ObzrhCab3DqixsglGrQOId2WwB",
	"JxhdGJt3ZJW2TTCqFXT86mVvZKbAUFlXj37rxH+a0X98wW5laD4I2tSmA6Atzje+srnphrdMzwfBGN90",
	"MB5bkbKJQDJz1gmIDxCuz+Q7NlF9n/Q0asWnq85/rvoeU/Xd2KXuXfdtfbtXKYXV5PhimHzNVRpqvua3",
	"vh1NEePqTFz8HvTpwZOqYq/Rmv339wR7p8rOR6Gt91YW2vFg7UWuEDcivwFcK0atKqbzKBmbk4l9Udcf",
	"zhnQVlMVtFo6XM1kFNbprfF7q6dZ0AqKYKrRicfrpqaOwiejmYSnht4jjFXa93v9X0QqnOb/imhVfHTf",
	"p0SP4xZ3x/1t383tkDvbx3Bbe7P9fwAAAP//Rml5AE9NAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
