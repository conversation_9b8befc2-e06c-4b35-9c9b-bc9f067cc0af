// Package participant provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package participant

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// ParticipantCollectionV1DTO defines model for ParticipantCollectionV1DTO.
type ParticipantCollectionV1DTO struct {
	Items []ParticipantV1DTO `json:"items"`
}

// ParticipantCreateV1DTO defines model for ParticipantCreateV1DTO.
type ParticipantCreateV1DTO struct {
	Email string `json:"email"`
}

// ParticipantGroupsUpdateV1DTO defines model for ParticipantGroupsUpdateV1DTO.
type ParticipantGroupsUpdateV1DTO struct {
	GroupIDs *[]int64 `json:"groupIDs,omitempty"`
}

// ParticipantRolesUpdateV1DTO defines model for ParticipantRolesUpdateV1DTO.
type ParticipantRolesUpdateV1DTO struct {
	RoleIDs *[]int64 `json:"roleIDs,omitempty"`
}

// ParticipantV1DTO defines model for ParticipantV1DTO.
type ParticipantV1DTO struct {
	ID          int64      `json:"id"`
	ProductID   int64      `json:"productID"`
	UserID      int64      `json:"userID"`
	Email       string     `json:"email"`
	FullName    string     `json:"fullName"`
	Position    string     `json:"position"`
	IsOwner     bool       `json:"isOwner"`
	GroupIDs    []int64    `json:"groupIDs"`
	RoleIDs     []int64    `json:"roleIDs"`
	LastLoginAt *time.Time `json:"lastLoginAt,omitempty"`
	CreatedAt   time.Time  `json:"createdAt"`
	UpdatedAt   time.Time  `json:"updatedAt"`
}

// CreateParticipantJSONRequestBody defines body for CreateParticipant for application/json ContentType.
type CreateParticipantJSONRequestBody = ParticipantCreateV1DTO

// UpdateParticipantGroupsJSONRequestBody defines body for UpdateParticipantGroups for application/json ContentType.
type UpdateParticipantGroupsJSONRequestBody = ParticipantGroupsUpdateV1DTO

// UpdateParticipantRolesJSONRequestBody defines body for UpdateParticipantRoles for application/json ContentType.
type UpdateParticipantRolesJSONRequestBody = ParticipantRolesUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all product participants
	// (GET /v1/products/{productID}/participants)
	GetParticipants(c *gin.Context, productID int64)
	// Add new participant to product
	// (POST /v1/products/{productID}/participants)
	CreateParticipant(c *gin.Context, productID int64)
	// Delete product participant
	// (DELETE /v1/products/{productID}/participants/{participantID})
	DeleteParticipant(c *gin.Context, productID int64, participantID int64)
	// Get product participant
	// (GET /v1/products/{productID}/participants/{participantID})
	GetParticipant(c *gin.Context, productID int64, participantID int64)
	// Update product participant groups
	// (PATCH /v1/products/{productID}/participants/{participantID}/groups)
	UpdateParticipantGroups(c *gin.Context, productID int64, participantID int64)
	// Update product participant roles
	// (PATCH /v1/products/{productID}/participants/{participantID}/roles)
	UpdateParticipantRoles(c *gin.Context, productID int64, participantID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetParticipants operation middleware
func (siw *ServerInterfaceWrapper) GetParticipants(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetParticipants(c, productID)
}

// CreateParticipant operation middleware
func (siw *ServerInterfaceWrapper) CreateParticipant(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateParticipant(c, productID)
}

// DeleteParticipant operation middleware
func (siw *ServerInterfaceWrapper) DeleteParticipant(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "participantID" -------------
	var participantID int64

	err = runtime.BindStyledParameterWithOptions("simple", "participantID", c.Param("participantID"), &participantID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter participantID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteParticipant(c, productID, participantID)
}

// GetParticipant operation middleware
func (siw *ServerInterfaceWrapper) GetParticipant(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "participantID" -------------
	var participantID int64

	err = runtime.BindStyledParameterWithOptions("simple", "participantID", c.Param("participantID"), &participantID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter participantID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetParticipant(c, productID, participantID)
}

// UpdateParticipantGroups operation middleware
func (siw *ServerInterfaceWrapper) UpdateParticipantGroups(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "participantID" -------------
	var participantID int64

	err = runtime.BindStyledParameterWithOptions("simple", "participantID", c.Param("participantID"), &participantID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter participantID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateParticipantGroups(c, productID, participantID)
}

// UpdateParticipantRoles operation middleware
func (siw *ServerInterfaceWrapper) UpdateParticipantRoles(c *gin.Context) {

	var err error

	// ------------- Path parameter "productID" -------------
	var productID int64

	err = runtime.BindStyledParameterWithOptions("simple", "productID", c.Param("productID"), &productID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter productID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "participantID" -------------
	var participantID int64

	err = runtime.BindStyledParameterWithOptions("simple", "participantID", c.Param("participantID"), &participantID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter participantID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateParticipantRoles(c, productID, participantID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/products/:productID/participants", wrapper.GetParticipants)
	router.POST(options.BaseURL+"/v1/products/:productID/participants", wrapper.CreateParticipant)
	router.DELETE(options.BaseURL+"/v1/products/:productID/participants/:participantID", wrapper.DeleteParticipant)
	router.GET(options.BaseURL+"/v1/products/:productID/participants/:participantID", wrapper.GetParticipant)
	router.PATCH(options.BaseURL+"/v1/products/:productID/participants/:participantID/groups", wrapper.UpdateParticipantGroups)
	router.PATCH(options.BaseURL+"/v1/products/:productID/participants/:participantID/roles", wrapper.UpdateParticipantRoles)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbW1PcOPb/Ki79/w+ZKoeGJDOb6Td2SKbYnVkoQnYfUtSWsE/TCrblkeRmeyl/960j",
	"ybZ8kbE7NKGmeIKWz03n8juSJd+TiKc5zyBTkizviYzWkFL97wchuLgAmfNMwj+PTi7PcDQXPAehGGia",
	"iMeAf9U2B7IkUgmW3ZCQ/Oc1FzEIsnxThiQFKenNON3bMiT8+itE6vRklPDHmvBSk4yQvitDIhVV41Q/",
	"WapCOmQsU3ADwqU7KsuQCPijYAJisvxSMYXGCc00W/Y5s6qMuQorLeYRKUNyToViEctppn7hSQKRYjzz",
	"+JwpSNv//L+AFVmS/1s0sVzYQC4cyUZeWaunQtDt6BSNhocMFkCVL0EgpSwZ9X9PqWF5QOmvghe5/JzH",
	"ftU3SHJ60nbVQHxv+Otm9Kd3PQeV5bgpFzyBcUsET+AJDPHVqI5PfKzwx4qLlCqyJGjua8VSTNF+bBpT",
	"kOTg0tA1MTsqwwmhxQJcFUnyD5rCg0X9WOFyxL4vQ8Jiv5xM20V0bfYFt3I0JEye3WX4o5Z2zXkCNHMJ",
	"/1KGJKFS/cZvWPaoHj8sQ5JzyRAYHsSzXPC46EDpmA87gP1I+epI/bkMSaFL5HHzEK0tJIhdZvq2h3Yx",
	"cV1XS65S3UlmJxZNZjg53PgwdOrP9UEf37APQVQIprafELxN9R4Xas0F+y+tAn8NVID4WPnvb/+6xM6i",
	"GTAn9dPGl2ulclKibJatuHYSUwk+Of/9U3B8fkpCsgEhtXCyOcIg8hwymjOyJG8PDg/QZzlVa23NYnO0",
	"oHHKsoWeKo6VYW90EVEFN1wYABoiuLeeKrvPcxApk2hOj9VGxju+uK9jVw7Q5FzSRHofaG797wA7BlMO",
	"Do5M1Ty/N4nQE4m5JQcHF/cm7xyWISXN2OLe/r/1MLUIhl0MG7sCbP1eSF6ICHrDRSaAxs1wNxn8Ub6F",
	"bZRwetud/6BR/YiPx3roac+2EaIhgwfJ0+2ovS3avGnQZlkCGgBjkJFgualqcgGqEJkMaJIEljdwGQ+I",
	"LkuhUeA0JkvyK6hzVzIWqaApKO3WL10F51aohjOGI1jTJCS2B7qw12CiEgVYcKEt1K7AtIO4ZXmF7GbL",
	"oGf75vDQ7BMyBZmeOM3zhEV6Iouv0mBao2HiUra7SNYINzxj142BLKIIpEQc3wYClGCwgRhB790jGjqw",
	"cxow8DTb0ITFAbobpAqsc7UtR/0U+ZxR2woqg9/2iT5ycc3iGDJN8ebnPsUl50FKs22lViLlj99h8gpE",
	"RpNAgtiACAC5TA8s0pSKrUlxb0Fg7tEbzHR3BUyuzCppoMKO41gGNMjgbkhev77MrsaV/ZwqTIfurzze",
	"7qW4nA3dSGE5HIFmYTwLLoxpvTmWT4MLc9CgDQY0jl+A4JkCwXEcm8J1gqd4VcdeKJjalRf3zi/T/EkM",
	"CZgXR21zT/Q4IskAigR3TK0DtYZA5hCxFYM4aGAhoFncoj496cOOkf8MYSfsKW7NxKPc9euoAVO2l77F",
	"xcxKN6F9qfXnWeumAIaqa6Tlj66psR53K9bPEsRglbYX3y8l+mCJPp8+/7Lof/6L/jnFv2uTb94LYEFE",
	"6wHf63d1fvwwAr4FRoyG3qnGC5508GSvu53+SdLEPY9hDAzn3jY+33ZgNAkfbSK3YNK+qH4ByWcJkjbn",
	"/Kj0+HBZvQLfFS01/6OCpT53fcHKp8TK3lH3RKhEvj8BUJocfsHJPwNOCoseO8Bk/yxxnM5ztDiDabFm",
	"UnGc0i7Mzf2rHZjrq1E78G4Y3D3A2TlZ9dMMHKR2eH1k3Rl0Th7NmWtUCGEStzXsOejzndO2x3tHj53H",
	"/ePNDkE9QedqgO5xnUsBX64Q+00dmCZYiMQe/svlYpHcHsTyAKL89kCyawTG2wNREGxaPsLXMWxGiJGW",
	"RzRZc6mW7w/fHxK0wNZSFzDOqj4uAwEJwmWgeLXqvN4G+uy76YrH+FMvrkm/rQ7LcoLkE3hek0yXauPj",
	"FWnfP8+Qp0vjlfxhRKQmmSzTtCSPNGy7kyXp3PNJwlXZZEnObYVayi/2AsJkIfZCQi3gA/6ezF1dM2j4",
	"/16NTI5W+7TRinHbxA7Z6QjaPR8dITMz0NYcX/llzSs8k35j8mblYNNaXTHzKqK+lmsFfDK/J7NvpYK0",
	"XjJojznC9NN5PupIRJd1Bc4v1EaCKc0rXObimnYIgI0LMEx2d6Y9GryKBV2pkIpozTYQ8uzfNM8F39Ak",
	"FPAVIgVxaEYg/uGg2sL8UYDYNuprdzdruxhWtEhwu0RCAlmR4vpK6yIhsdpwe9fo05sBoxFJrE7njpy9",
	"Ejiwz/rIEgUCcaueF/IEr84uwg8XXrOVuZz9gNFnFyQkHy4mGfLddpo6G71q7Y2mb99d9vTqTZ1PrVmF",
	"7UFr1Ui8mpurbnvQbt9ODGuur4s+ttbv8C5hKL1NdY3kt13470e7LS7pKei6vNpYVL8VeLCw+pftfYXm",
	"M8G5/bsfA2zF+fQ3V473o94pPZ8JTfHtz4xeN8O479SeaKRMK6rwvh6outS8DqRXznYB4jOIyWO7vu7V",
	"RPU9wagO09yiQiqeBlzY1cU39jkjDum0tEnTjqmiwQotAPSWz//Vw95s/ZJ/YylTOrhZkV6DwFBDpphi",
	"gAshVYgMYt+EE2QenvHRYbgj/JytVhJ2Nolr7mGbdjbpExfKLQCzdQtWDBKvIZILjxlEQbS2XzZUSeEM",
	"5e7N20KfxvK7zCxDm8/gnO8cEirV54FvHfxBr+fjvBfYeTZZeyb2p62F5toYNuxqPgie5v9Z1jb776c0",
	"VvtoprX13sqxdr6xbpAri1udvzG4Tow6VRrmWT5u3kzsarXz3U5ltDNUNa1ODlczmWXr/qvxqdOzCWhl",
	"Cgq11uG/l+1MnWWf7mbaPKN6hzZWZd/v9ee3lZ3NB7k2FaeYdqY/DvMAuX02uJaQEWSxu5aoBlA+/jes",
	"/TkcFz1wUDR2RDTlcOg5HAtdlf8LAAD//wZ1u7vqPgAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
