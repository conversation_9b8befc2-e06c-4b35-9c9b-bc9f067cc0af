// Package systemgroup provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package systemgroup

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

const (
	AuthorizationScopes = "Authorization.Scopes"
)

// ErrorResponseV1DTO defines model for ErrorResponseV1DTO.
type ErrorResponseV1DTO struct {
	Status     int    `json:"status"`
	Code       string `json:"code"`
	Message    string `json:"message"`
	ObjectType string `json:"objectType"`
	ObjectID   string `json:"objectID"`
	State      string `json:"state"`
}

// GroupCreateV1DTO defines model for GroupCreateV1DTO.
type GroupCreateV1DTO struct {
	Name string `json:"name"`
}

// GroupFullV1DTO defines model for GroupFullV1DTO.
type GroupFullV1DTO struct {
	ID             int64    `json:"id"`
	Name           string   `json:"name"`
	Type           string   `json:"type"`
	ParticipantIDs *[]int64 `json:"participantIDs,omitempty"`
	UserIDs        *[]int64 `json:"userIDs,omitempty"`
	RoleIDs        *[]int64 `json:"roleIDs,omitempty"`
}

// GroupShortV1DTO defines model for GroupShortV1DTO.
type GroupShortV1DTO struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	RoleCount        int64  `json:"roleCount"`
	ParticipantCount int64  `json:"participantCount"`
	UserCount        int64  `json:"userCount"`
}

// GroupUpdateV1DTO defines model for GroupUpdateV1DTO.
type GroupUpdateV1DTO struct {
	Name           *string  `json:"name,omitempty"`
	Type           *string  `json:"type,omitempty"`
	ParticipantIDs *[]int64 `json:"participantIDs,omitempty"`
	RoleIDs        *[]int64 `json:"roleIDs,omitempty"`
}

// CreateSystemGroupJSONRequestBody defines body for CreateSystemGroup for application/json ContentType.
type CreateSystemGroupJSONRequestBody = GroupCreateV1DTO

// UpdateSystemGroupJSONRequestBody defines body for UpdateSystemGroup for application/json ContentType.
type UpdateSystemGroupJSONRequestBody = GroupUpdateV1DTO

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get System Groups
	// (GET /v1/groups)
	GetSystemGroups(c *gin.Context)
	// Create System Group
	// (POST /v1/groups)
	CreateSystemGroup(c *gin.Context)
	// Delete system group
	// (DELETE /v1/groups/{groupID})
	DeleteSystemGroup(c *gin.Context, groupID int64)
	// Get system Group Roles
	// (GET /v1/groups/{groupID})
	GetSystemGroupFull(c *gin.Context, groupID int64)
	// Update system group
	// (PATCH /v1/groups/{groupID})
	UpdateSystemGroup(c *gin.Context, groupID int64)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetSystemGroups operation middleware
func (siw *ServerInterfaceWrapper) GetSystemGroups(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSystemGroups(c)
}

// CreateSystemGroup operation middleware
func (siw *ServerInterfaceWrapper) CreateSystemGroup(c *gin.Context) {

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateSystemGroup(c)
}

// DeleteSystemGroup operation middleware
func (siw *ServerInterfaceWrapper) DeleteSystemGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteSystemGroup(c, groupID)
}

// GetSystemGroupFull operation middleware
func (siw *ServerInterfaceWrapper) GetSystemGroupFull(c *gin.Context) {

	var err error

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSystemGroupFull(c, groupID)
}

// UpdateSystemGroup operation middleware
func (siw *ServerInterfaceWrapper) UpdateSystemGroup(c *gin.Context) {

	var err error

	// ------------- Path parameter "groupID" -------------
	var groupID int64

	err = runtime.BindStyledParameterWithOptions("simple", "groupID", c.Param("groupID"), &groupID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter groupID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AuthorizationScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateSystemGroup(c, groupID)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/v1/groups", wrapper.GetSystemGroups)
	router.POST(options.BaseURL+"/v1/groups", wrapper.CreateSystemGroup)
	router.DELETE(options.BaseURL+"/v1/groups/:groupID", wrapper.DeleteSystemGroup)
	router.GET(options.BaseURL+"/v1/groups/:groupID", wrapper.GetSystemGroupFull)
	router.PATCH(options.BaseURL+"/v1/groups/:groupID", wrapper.UpdateSystemGroup)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xbX3PbuBH/Khy0D7kZRrQTJ3PVmxvHGbe92mM77UPG04HJlYSYJHgAqKuq4Xfv4A9J",
	"8A8oUrZyuhk/WQQXu79d7GIXC3qLQppkNIVUcDTfIh6uIMHq52fGKLsFntGUw79OL+6v5WjGaAZMEFA0",
	"IY1A/hWbDNAcccFIukQ++u9byiJgaP6u8FECnOPlMN37wkf08TuE4upikPBDRXivSAZIzwofcYHFMNVH",
	"Q5Vzi4ykApbAbLrTovARg19zwiBC82/lJF8boVazgc/SqgTz4JdS9CtU+OgLo3n2iQEWLkunOBnWo4NP",
	"zXAKu8zj2CGKRA5LLOlbDQMpddRATfbxrAnH341ZekeGmSAhyXAqri60fAEJd0NoSCwq/TBjeNNefkZj",
	"eFmuH6t3O7w558BeVvSH9gqTCBkjm0nO1b5bUSaObbk/0TwVI83S2gHkuu45/WzKAu4p4+POlbJV6DGK",
	"Ld25qF+z6Fn7xQFj7/1BYm/U0r0rio7B5BYPYc6I2NzJBKdNdJ6LFWXkf1gQmsqBR8AM2CVlCRZojv72",
	"73u5aasJaG7eoor5SogMFZI3SRdUYSIilm9ufrnzzm+ukI/WwLhijtanUjOaQYozgubo/exkdqaWXqwU",
	"mmB9GuAoIWmwlIsrxwq/MxqEWMCSMr3KfQRb9ffqomi/z4AlhEs4nakZo1EeCud4sDW/+tgymlGOY+58",
	"oWarnz3TpZfw3sEBVfX7rfawDksZOrx3MNjqXdma0iekHgu25vfGMalB0G9iWJviqvEccJqzEDrDecoA",
	"R/Vw5QxoCWonioCHjGTaZ9EXENy723ABiadJZ0i5GVNefRVpGk3xRfOSW5Ou6hTfdycnupRLBejNDmdZ",
	"TELFIPjOdWzowrARzH9msEBz9KegLiEDUz8G7azTjmoVN01V7vIwBM4XeRxvvCUVHrfVkhzOJiIdAthT",
	"3fZgukrXOCaRJ/dy4MJb6L1BYTntrsbXFJs9BSJN9L5LdEnZI4kiSBXFu790Ke4p9RKcbkqxSvkPv4Py",
	"AliKY48DWwPzQM7Sm2meJJhttG+V7ld5l8BLLpOe5XToQWYbyns8WFe9vLHaXR/WVDZHnWCBi7/SaPNi",
	"lukU4n2OaunraVrvVmNBdtoXLIfimcE2KcacUJVRPW4HWKhwR69hdZRhZbzK9jRnYDUSRSP9owhi0Mff",
	"JoQLNb4r5jRVM+YyzHACQiXYb51kpNxMHRaIfJa1TVn3zpEB1gkR37LumPqweOiPqYFsou0QNfR99fyj",
	"9HztdM2VcqcUd01kM/BUvbirMLrM4/i4Pfzlskbd/ZlaiGlbvgbP0VZj3K5ObtViDZRkWISrHsupzsKu",
	"/KCpjjQ/HKg0tHsuu0pDTfv7lYbjgzxXSF/z4x8gxI1TjcqPpjJ8gk0YU/zUboz0diu6raDhJlDf204H",
	"a4Cor1/VS55sBvE2aK2+5hTSYNtoiBbPmDrOBIMcWp2xfgbd5tswnaMXN2FSsCJcUOmL+0yu7wL3mFxd",
	"0+0xd03gtx0zxxjc1XlszXWRtTVoRaRuUoY5Y3rHaQw7AsDV2GyOd/yx9bob9i2CSkGrl65SfKuL/u1B",
	"5l+9gekaIGex6ZbzeRDET7OIzyDMnmacPMpE/jRjOSp8N+HbCNYDxJKWhjheUS7mP5/8fIIkArMjtnf6",
	"67KC4R6DWCU9QU2L0XvceKpZXNck5/Lxi0mH43hZi+RieFORjOdq1sfJUr+fwk+Fxhv+0wBLRTKap3IR",
	"FzdZiY7mpHzPxekrBzaak9Xer7h8Mh370UxMB79i8Fk+j55dpt96/t/LkdGrZeezis1NPbqPd1qM9vdH",
	"i8lEDzQxRxduXtMCT7vfEL9JPlinVpvNtIioPhExDO708+jpusgz+lTFXsnMqvX25ChN1mY4PVBrDjo0",
	"H+SxTJ7J+jZgbQK5TGIFlZG9NxHDC+FjFq7IGnya/gdnGaNrHPsMvkMoIPL1CEQ/zcoD5K85sE0tvjJ3",
	"XZRHsMB5LNAcIR9BmieySlaykI+MNHmwreWpc5qWKEmMTOsW3lw5F10bXZJYAJP7VqWXnOO9ub71P986",
	"YZsPAnaAvr5FPvp8OwqI8XfnUbsqaAYP24vyIrz8vqF1+u4R/INO+B250mmdYnUVdgCpZSJxSq7vhg8g",
	"XQabU7Iu2w4g1Uo6bveyDzKHwFBG14B/m8L/MNJNcHFHQFfh1dyLqovznYHVuSx3BpoLggm1wwEwEeeS",
	"X378cyjxVui5INTBdzgYnWwm132v9IRDoVNRud9XA2WWmpaBVOVsChAXIMLPTX3diYlHSmPA6bAMndzC",
	"nAuaeJSZ6uKZeU6zk3SK2yi1Iyywt5AIQFrLZf/yZUdbN+d/kIQItbhpnjwCk0sNqSCCgCyERM5SiFwK",
	"x3Jyv8anJ/6e28/1YsFhb0hUze7HtDekO8qEHQD66OYtCMROIJwyBwwkIFz9U38rWTqFNdTzrST9LdVl",
	"aP1Jtv664Vy+jTEXumsqn8e4U6WP1RfYW5u0qUnzI1BjsMb3nvanoZPQ1ufvHwlW2Wgi2upsZaGdDtZe",
	"5BJxI/PXgCvHqFylnjzJxnVnYl/UizyOW95tDZVJq+XDpSaTsB4+Gn+0e9YLWkKRTA06+fO+6amT8Kls",
	"puBp0XuksdL7fqn+FaTEWf9ziHHFMdCu1RfNjo3cvOutJXgIaWTXEuWA5C9/9Us/hnu+HTd8Q3d7Y271",
	"juE+76H4fwAAAP//sBIkqnY1AAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
