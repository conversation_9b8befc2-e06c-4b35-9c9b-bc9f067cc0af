version: '3'

# Глобальные переменные проекта
vars:
  GOLANGCI_LINT_VERSION: 'v2.3.0'
  GCI_VERSION: 'v0.13.7'
  GOFUMPT_VERSION: 'v0.8.0'
  
  PROJECT_PREFIX: 'git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms'

  BIN_DIR: '{{.ROOT_DIR}}/bin'
  GOLANGCI_LINT: '{{.BIN_DIR}}/golangci-lint'
  GCI: '{{.BIN_DIR}}/gci'
  GOFUMPT: '{{.BIN_DIR}}/gofumpt'

tasks:
  install-formatters:
    desc: "Устанавливает форматтеры gci и gofumpt в ./bin"
    cmds:
      - |
        [ -f {{.GOFUMPT}} ] || {
          echo '📦 Устанавливаем gofumpt {{.GOFUMPT_VERSION}}...'
          GOBIN={{.BIN_DIR}} go install mvdan.cc/gofumpt@{{.GOFUMPT_VERSION}}
        }
        [ -f {{.GCI}} ] || {
          echo '📦 Устанавливаем gci {{.GCI_VERSION}}...'
          GOBIN={{.BIN_DIR}} go install github.com/daixiang0/gci@{{.GCI_VERSION}}
        }
    status:
      - test -x {{.GOFUMPT}}
      - test -x {{.GCI}}

  format:
    desc: "Форматирует весь проект gofmt + gci"
    deps: [ install-formatters ]
    cmds:
      - |
        echo "🧼 Форматируем через gofmt ..."
        find . -type f -name '*.go' \
          ! -path '*/mocks/*' \
          ! -path './gen/*' \
          ! -name '*.gen.go' \
          ! -name '*_mock.go' \
          ! -name '*_test.go' \
          -exec gofmt -w {} +
      - |
        echo "🎯 Сортируем импорты через gci ..."
        find . -type f -name '*.go' \
          ! -path '*/mocks/*' \
          ! -path './gen/*' \
          ! -name '*.gen.go' \
          ! -name '*_mock.go' \
          ! -name '*_test.go' \
          -exec {{.GCI}} write -s standard -s default -s "prefix({{.PROJECT_PREFIX}})" {} +

  format-strict:
    desc: "Строгое форматирование через gofumpt (удаляет пустые строки)"
    deps: [ install-formatters ]
    cmds:
      - |
        echo "🧼 Строгое форматирование через gofumpt ..."
        find . -type f -name '*.go' \
          ! -path '*/mocks/*' \
          ! -path './gen/*' \
          ! -name '*.gen.go' \
          ! -name '*_mock.go' \
          ! -name '*_test.go' \
          -exec {{.GOFUMPT}} -w {} +
      - |
        echo "🎯 Сортируем импорты через gci ..."
        find . -type f -name '*.go' \
          ! -path '*/mocks/*' \
          ! -path './gen/*' \
          ! -name '*.gen.go' \
          ! -name '*_mock.go' \
          ! -name '*_test.go' \
          -exec {{.GCI}} write -s standard -s default -s "prefix({{.PROJECT_PREFIX}})" {} +

  install-golangci-lint:
    desc: "Устанавливает golangci-lint в каталог bin"
    cmds:
      - |
        [ -f {{.GOLANGCI_LINT}} ] || {
          mkdir -p {{.BIN_DIR}}
          echo "📦 Устанавливаем golangci-lint {{.GOLANGCI_LINT_VERSION}}..."
          GOBIN={{.BIN_DIR}} go install github.com/golangci/golangci-lint/v2/cmd/golangci-lint@{{.GOLANGCI_LINT_VERSION}}
        }
    status:
      - test -x {{.GOLANGCI_LINT}}

  lint:
    desc: "Запускает golangci-lint для всех модулей"
    deps: [ install-golangci-lint ]
    cmds:
      - '{{.GOLANGCI_LINT}} run ./... --config=.golangci.yml'

  lint-fix:
    desc: "Автоматически исправляет поддерживаемые ошибки staticcheck"
    deps: [ install-golangci-lint ]
    cmds:
      - '{{.GOLANGCI_LINT}} run --enable=staticcheck --fix ./...'

  clean:
    desc: "Очищает bin директорию и go кеши"
    cmds:
      - rm -rf {{.BIN_DIR}}
      - go clean -cache -testcache

  check:
    desc: "Полная проверка проекта: format, lint"
    deps: [ format, lint ]
    cmds:
      - rm -rf {{.BIN_DIR}}
