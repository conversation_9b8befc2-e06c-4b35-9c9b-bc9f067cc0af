<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Test - PMS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 16px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status {
            padding: 8px 12px;
            border-radius: 8px;
            margin: 8px 0;
            font-weight: 500;
            font-size: 14px;
        }
        .status.success { background: linear-gradient(135deg, #00c851, #20bf6b); color: white; }
        .status.error { background: linear-gradient(135deg, #ff4757, #ff3838); color: white; }
        .status.info { background: linear-gradient(135deg, #3742fa, #2f3542); color: white; }
        .status.warning { background: linear-gradient(135deg, #ffa502, #ff6348); color: white; }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        button:disabled { 
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .log-container {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 12px;
            height: 200px;
            overflow-y: auto;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #fff;
        }
        
        .form-group {
            margin: 8px 0;
        }
        
        h3 {
            font-weight: 600;
        }
        
        label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            font-size: 14px;
            color: #555;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        textarea {
            height: 60px;
            resize: vertical;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        .full-width {
            grid-column: 1 / -1;
        }
        
        .event-log {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        
        .message-log {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        
        .connection-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .connection-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4757;
            box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
            transition: all 0.3s ease;
        }
        
        .connection-status.connected {
            background: #00c851;
            box-shadow: 0 0 10px rgba(0, 200, 81, 0.5);
        }
        
        .status-option {
            padding: 5px;
            margin: 2px 0;
        }
        
        .status-draft { background-color: #e3f2fd; }
        .status-on_approval { background-color: #fff3e0; }
        .status-approved { background-color: #e8f5e8; }
        .status-rejected { background-color: #ffebee; }
        .status-archive { background-color: #f3e5f5; }
        
        .event {
            margin: 4px 0;
            padding: 8px 12px;
            border-left: 3px solid #667eea;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
            font-size: 13px;
            color: #e1e5e9;
        }
        .event.error {
            border-left-color: #ff4757;
            background: rgba(255, 71, 87, 0.15);
            color: #ff6b7d;
        }
        .event.success {
            border-left-color: #00c851;
            background: rgba(0, 200, 81, 0.15);
            color: #2ed573;
        }
        .event.warning {
            border-left-color: #ffa502;
            background: rgba(255, 165, 2, 0.15);
            color: #ffc048;
        }
        .event.message {
            border-left-color: #3742fa;
            background: rgba(55, 66, 250, 0.15);
            color: #5352ed;
        }
    </style>
</head>
<body>
    <div class="container" style="text-align: center; background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.3);">
        <h1 style="margin: 0; color: white; font-size: 28px; font-weight: 600;">🔔 SSE Testing Dashboard</h1>
        <p style="margin: 8px 0 0 0; color: rgba(255,255,255,0.8); font-size: 14px;">Real-time notifications testing for PMS</p>
    </div>
    
    <div class="container">
        <h3 style="margin: 0 0 12px 0; color: #333; font-size: 18px;">🔐 Подключение</h3>
        <div style="display: flex; gap: 12px; align-items: end;">
            <div style="flex: 1;">
                <label for="jwtToken" style="font-size: 14px; margin-bottom: 4px;">JWT Token:</label>
                <input type="text" id="jwtToken" placeholder="Вставьте ваш JWT токен" style="margin: 0;">
            </div>
            <button onclick="checkToken()">Проверить</button>
        </div>
        <div id="authStatus" style="margin-top: 8px;"></div>
        
        <hr style="margin: 16px 0; border: none; height: 1px; background: #e1e5e9;">
        
        <div style="display: flex; gap: 12px; align-items: center;">
            <div id="connectionStatus" class="connection-status"></div>
            <span id="connectionText" style="font-size: 14px; color: #666;">Отключено</span>
            <div style="margin-left: auto; display: flex; gap: 8px;">
                <button id="connectBtn" onclick="connectSSE()">Подключиться</button>
                <button id="disconnectBtn" onclick="disconnectSSE()" disabled>Отключиться</button>
            </div>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h3 style="margin: 0 0 12px 0; color: #333; font-size: 18px;">📝 Изменение статуса</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                <div>
                    <label for="productId" style="font-size: 12px; margin-bottom: 4px;">Продукт:</label>
                    <input type="number" id="productId" value="4" style="margin: 0;">
                </div>
                <div>
                    <label for="proposalId" style="font-size: 12px; margin-bottom: 4px;">Заявка:</label>
                    <input type="number" id="proposalId" value="1" style="margin: 0;">
                </div>
            </div>
            <div style="margin-bottom: 12px;">
                <label for="status" style="font-size: 12px; margin-bottom: 4px;">Статус:</label>
                <select id="status" style="margin: 0;">
                    <option value="draft">Черновик</option>
                    <option value="on_approval">На согласовании</option>
                    <option value="approved">Одобрено</option>
                    <option value="rejected">Отклонено</option>
                    <option value="archive">Архив</option>
                </select>
            </div>
            <div style="margin-bottom: 12px;">
                <label for="message" style="font-size: 12px; margin-bottom: 4px;">Комментарий:</label>
                <textarea id="message" placeholder="Комментарий к изменению..." style="margin: 0; height: 60px;"></textarea>
            </div>
            <button onclick="sendProposal()" style="width: 100%;">Изменить статус</button>
        </div>
        
        <div class="container">
            <h3 style="margin: 0 0 12px 0; color: #333; font-size: 18px;">💬 Отправка сообщения</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                <div>
                    <label for="msgProductId" style="font-size: 12px; margin-bottom: 4px;">Продукт:</label>
                    <input type="number" id="msgProductId" value="4" style="margin: 0;">
                </div>
                <div>
                    <label for="msgProposalId" style="font-size: 12px; margin-bottom: 4px;">Заявка:</label>
                    <input type="number" id="msgProposalId" value="1" style="margin: 0;">
                </div>
            </div>
            <div style="margin-bottom: 12px;">
                <label for="userMessage" style="font-size: 12px; margin-bottom: 4px;">Сообщение:</label>
                <textarea id="userMessage" placeholder="Введите ваше сообщение..." style="margin: 0; height: 60px;"></textarea>
            </div>
            <button onclick="sendUserMessage()" style="width: 100%;">Отправить сообщение</button>
        </div>
    </div>




    <div class="grid">
        <div class="container">
            <h3 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">🔔 Непросмотренные события</h3>
            <button onclick="getUnreadEvents()" style="width: 100%; margin-bottom: 8px;">Обновить список</button>
            <div id="unreadEvents" class="log-container" style="background: #2a2a2a; border: 2px solid #ff9800;"></div>
            <button onclick="clearUnreadEvents()" style="width: 100%; margin-top: 8px;">Очистить</button>
        </div>
        
        <div class="container">
            <h3 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">👁️ Отметка просмотра</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
                <div>
                    <label for="viewProductId" style="font-size: 12px; margin-bottom: 4px;">Продукт:</label>
                    <input type="number" id="viewProductId" value="4" style="margin: 0;">
                </div>
                <div>
                    <label for="viewProposalId" style="font-size: 12px; margin-bottom: 4px;">Заявка:</label>
                    <input type="number" id="viewProposalId" value="1" style="margin: 0;">
                </div>
            </div>
            <button onclick="markProposalAsViewed()" style="width: 100%;">Отметить как просмотренное</button>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h3 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">🗨️ Чат по заявкам (все recipients)</h3>
            <div id="chat" class="log-container" style="background: #1a1a2e; border: 2px solid #3742fa; height: 200px;"></div>
        <button onclick="clearChat()" style="width: 100%; margin-top: 8px;">Очистить чат</button>
        </div>
        
        <div class="container">
            <h3 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">🔍 Пользователь ID=5 (СИМУЛЯЦИЯ SSE)</h3>
            <div style="margin-bottom: 8px; padding: 8px; background: #e8f5e8; border: 1px solid #28a745; border-radius: 6px; font-size: 12px;">
                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                    <div id="user2ConnectionStatus" class="connection-status connected" style="width: 10px; height: 10px;"></div>
                    <span><strong>🟢 SSE СИМУЛЯЦИЯ:</strong> Пользователь ID=5 подключен (эмуляция)</span>
                    <small id="user2MessageCount" style="margin-left: auto; color: #666;">Получено: 0</small>
                </div>
                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                    <button onclick="toggleUser2Connection()" id="user2ToggleBtn" style="font-size: 11px; padding: 4px 8px;">🔌 Отключить ID=5</button>
                    <button onclick="simulateUser2Message()" style="font-size: 11px; padding: 4px 8px;">💬 Отправить от ID=5</button>
                    <button onclick="createRealUser2Connection()" id="realConnectionBtn" style="font-size: 11px; padding: 4px 8px; background: #ff9800;">🔗 РЕАЛЬНОЕ подключение</button>
                </div>
                <div style="margin-top: 4px; font-size: 11px;">
                    <input type="text" id="user2Token" placeholder="JWT токен для пользователя ID=5" style="width: 100%; font-size: 10px; padding: 2px 4px;">
                </div>
            </div>
            <div id="chatUser2" class="log-container" style="background: #2e1a1a; border: 2px solid #28a745; height: 200px;"></div>
            <button onclick="clearChatUser2()" style="width: 100%; margin-top: 8px;">Очистить чат ID=5</button>
        </div>
    </div>

    <script>
        let eventSource = null;
        let user2MessageCount = 0;
        let user2Connected = true; // Симуляция подключения ID=5
        let user2EventSource = null; // Реальное SSE подключение для ID=5

        function log(message, type = 'info') {
            // Простая функция логирования для консоли
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
        }



        function updateConnectionStatus(connected, text) {
            const statusDiv = document.getElementById('connectionStatus');
            const textSpan = document.getElementById('connectionText');
            
            if (connected) {
                statusDiv.classList.add('connected');
            } else {
                statusDiv.classList.remove('connected');
            }
            textSpan.textContent = text;
        }

        function connectSSE() {
            if (eventSource && eventSource.readyState !== EventSource.CLOSED) {
                log('SSE уже подключен', 'error');
                return;
            }
            
            // Закрываем предыдущее соединение если оно есть
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }

            const token = document.getElementById('jwtToken').value;
            if (!token) {
                log('Введите JWT токен', 'error');
                return;
            }

            log('Подключение к SSE...', 'info');
            updateConnectionStatus(false, 'Подключение...');
            
            // Устанавливаем куку с токеном
            document.cookie = `auth_token=${token}; path=/; SameSite=Strict`;
            log('Кука установлена: auth_token', 'info');
            
            // Пробуем подключиться
            eventSource = new EventSource('/v1/events/source');

            eventSource.addEventListener('connected', function(event) {
                log(`Подключение установлено: ${event.data}`, 'success');
                updateConnectionStatus(true, 'Подключен');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            });

            eventSource.addEventListener('proposalStatus', function(event) {
                log(`🔔 Событие получено: proposalStatus`);
                try {
                    const eventData = JSON.parse(event.data);
                    
                    // Отображаем изменение статуса в чате
                    const statusMessage = {
                        proposalID: eventData.meta?.proposalID || 'unknown',
                        productID: eventData.meta?.productID || 'unknown',
                        senderID: 'system',
                        senderName: 'Система',
                        message: `Статус заявки изменен на "${eventData.meta?.status || 'unknown'}"`,
                        timestamp: eventData.createdAt || new Date().toISOString(),
                        isAdmin: false,
                        isStatusChange: true
                    };
                    displayChatMessage(statusMessage);
                    
                    // Симулируем получение события изменения статуса для ID=2
                    checkAndDisplayForUser2(statusMessage, eventData);
                } catch (e) {
                    log(`Ошибка парсинга события статуса: ${e.message}`);
                }
            });

            eventSource.addEventListener('proposalMessage', function(event) {
                log(`💬 Получено сообщение по заявке`);
                try {
                    const eventData = JSON.parse(event.data);
                    
                    // Отображаем сообщение в основном чате
                    const chatMessage = {
                        proposalID: eventData.meta?.proposalID || 'unknown',
                        productID: eventData.meta?.productID || 'unknown',
                        senderID: eventData.meta?.userID,
                        senderName: eventData.meta?.userID ? `User ${eventData.meta.userID}` : 'Другой пользователь',
                        message: eventData.message || 'Пустое сообщение',
                        timestamp: eventData.createdAt || new Date().toISOString(),
                        isAdmin: false
                    };
                    displayChatMessage(chatMessage);
                    
                    // Логика для пользователя с ID=2
                    // Сообщение отображается в чате ID=2 только если пользователь с ID=2 
                    // должен получить это сообщение согласно логике getMessageRecipients
                    checkAndDisplayForUser2(chatMessage, eventData);
                } catch (e) {
                    log(`Ошибка парсинга сообщения: ${e.message}`);
                }
            });

            eventSource.addEventListener('keepalive', function(event) {
                // Не логируем keep-alive сообщения, чтобы не засорять лог
                console.debug('Keep-alive received:', event.data);
            });

            eventSource.onmessage = function(event) {
                log(`Общее сообщение: ${event.data}`);
            };

            eventSource.onerror = function(event) {
                console.error('SSE error:', event);
                console.error('EventSource readyState:', eventSource.readyState);
                console.error('EventSource url:', eventSource.url);
                
                if (eventSource.readyState === EventSource.CLOSED) {
                    log('SSE соединение закрыто, попытка переподключения...');
                    updateConnectionStatus(false, 'Переподключение...');
                    
                    // Автоматическое переподключение через 3 секунды
                    setTimeout(() => {
                        if (!eventSource || eventSource.readyState === EventSource.CLOSED) {
                            log('Автоматическое переподключение...');
                            connectSSE();
                        }
                    }, 3000);
                } else if (eventSource.readyState === EventSource.CONNECTING) {
                    log('Переподключение к SSE...');
                    updateConnectionStatus(false, 'Переподключение...');
                } else {
                    log('Ошибка SSE соединения');
                }
            };
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('SSE соединение закрыто');
                updateConnectionStatus(false, 'Отключено');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            }
        }

        async function sendProposal() {
            const productId = document.getElementById('productId').value;
            const proposalId = document.getElementById('proposalId').value;
            const status = document.getElementById('status').value;
            const message = document.getElementById('message').value;
            const token = document.getElementById('jwtToken').value;

            if (!productId || !proposalId) {
                log('Заполните Product ID и Proposal ID', 'error');
                return;
            }

            if (!token) {
                log('Введите JWT токен', 'error');
                return;
            }

            try {
                log(`Отправка заявки ${proposalId} для продукта ${productId}...`, 'info');
                
                const requestBody = {
                    status: status
                };
                
                if (message && message.trim()) {
                    requestBody.message = message.trim();
                }
                
                const response = await fetch(`/v1/products/${productId}/proposals/${proposalId}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include',
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Заявка отправлена успешно`, 'success');
                    
                    // Добавляем сообщение в чат об изменении статуса
                    const statusMessage = {
                        proposalID: proposalId,
                        productID: productId,
                        senderID: 'current_user',
                        senderName: 'Вы',
                        message: `Статус заявки изменен на "${status}"${message ? ` с комментарием: ${message}` : ''}`,
                        timestamp: new Date().toISOString(),
                        isAdmin: false,
                        isStatusChange: true
                    };
                    displayChatMessage(statusMessage);
                } else {
                    const errorText = await response.text();
                    log(`❌ Ошибка: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                log(`❌ Ошибка сети: ${error.message}`);
            }
        }

        function clearChat() {
            document.getElementById('chat').innerHTML = '';
        }

        function clearChatUser2() {
            document.getElementById('chatUser2').innerHTML = '';
            user2MessageCount = 0;
            updateUser2Status();
        }

        function toggleUser2Connection() {
            user2Connected = !user2Connected;
            updateUser2Status();
            
            const infoDiv = document.getElementById('chatUser2');
            if (infoDiv) {
                const statusMessage = document.createElement('div');
                statusMessage.className = `event ${user2Connected ? 'success' : 'warning'}`;
                statusMessage.style.fontSize = '12px';
                statusMessage.style.fontWeight = 'bold';
                statusMessage.style.textAlign = 'center';
                statusMessage.style.border = `2px solid ${user2Connected ? '#28a745' : '#ff9800'}`;
                statusMessage.style.padding = '8px';
                statusMessage.style.margin = '4px 0';
                statusMessage.innerHTML = user2Connected ? 
                    '🟢 ПОДКЛЮЧЕН: Симуляция SSE соединения для ID=5 АКТИВНА' :
                    '🔴 ОТКЛЮЧЕН: Симуляция SSE соединения для ID=5 НЕАКТИВНА';
                infoDiv.appendChild(statusMessage);
                infoDiv.scrollTop = infoDiv.scrollHeight;
            }
        }

        function updateUser2Status() {
            const statusDiv = document.getElementById('user2ConnectionStatus');
            const toggleBtn = document.getElementById('user2ToggleBtn');
            const countElement = document.getElementById('user2MessageCount');
            
            if (statusDiv) {
                if (user2Connected) {
                    statusDiv.classList.add('connected');
                } else {
                    statusDiv.classList.remove('connected');
                }
            }
            
            if (toggleBtn) {
                toggleBtn.textContent = user2Connected ? '🔌 Отключить ID=5' : '🔌 Подключить ID=5';
            }
            
            if (countElement) {
                countElement.textContent = `Получено: ${user2MessageCount}`;
                countElement.style.color = user2Connected ? '#28a745' : '#666';
                countElement.style.fontWeight = user2Connected ? 'bold' : 'normal';
            }
        }

        function simulateUser2Message() {
            if (!user2Connected) {
                alert('⚠️ Сначала подключите ID=5 к SSE');
                return;
            }
            
            // Симулируем отправку сообщения от пользователя ID=5
            const simulatedMessage = {
                proposalID: document.getElementById('msgProposalId').value || '1',
                productID: document.getElementById('msgProductId').value || '4',
                senderID: 5,
                senderName: 'User 5 (Тест)',
                message: 'Тестовое сообщение от пользователя ID=5',
                timestamp: new Date().toISOString(),
                isAdmin: true
            };
            
            const simulatedEventData = {
                meta: {
                    userID: 5,
                    proposalID: simulatedMessage.proposalID,
                    productID: simulatedMessage.productID
                }
            };
            
            // Отображаем в основном чате
            displayChatMessage(simulatedMessage);
            
            // Проверяем для ID=5 (должен НЕ получить, так как сам отправитель)
            checkAndDisplayForUser2(simulatedMessage, simulatedEventData);
        }

        function createRealUser2Connection() {
            const token = document.getElementById('user2Token').value;
            
            if (!token) {
                alert('⚠️ Введите JWT токен для пользователя ID=5');
                return;
            }
            
            if (user2EventSource && user2EventSource.readyState !== EventSource.CLOSED) {
                alert('👀 Реальное подключение ID=5 уже активно');
                return;
            }
            
            // Закрываем предыдущее соединение если есть
            if (user2EventSource) {
                user2EventSource.close();
                user2EventSource = null;
            }
            
            log('🔗 Создание РЕАЛЬНОГО SSE подключения для пользователя ID=5...');
            
            // Сохраняем текущую куку основного пользователя
            const currentCookie = document.cookie.match(/auth_token=([^;]+)/);
            const originalToken = currentCookie ? currentCookie[1] : null;
            
            // Временно устанавливаем куку с токеном для ID=5
            document.cookie = `auth_token=${token}; path=/; SameSite=Strict`;
            log(`🍪 Временно установлена кука auth_token для ID=5`);
            
            // Создаем новое SSE подключение с токеном ID=5
            user2EventSource = new EventSource('/v1/events/source');
            
            user2EventSource.addEventListener('connected', function(event) {
                log(`🟢 РЕАЛЬНОЕ подключение ID=5 установлено: ${event.data}`);
                
                const infoDiv = document.getElementById('chatUser2');
                if (infoDiv) {
                    const successMessage = document.createElement('div');
                    successMessage.className = 'event success';
                    successMessage.style.fontSize = '12px';
                    successMessage.style.fontWeight = 'bold';
                    successMessage.style.textAlign = 'center';
                    successMessage.style.border = '2px solid #28a745';
                    successMessage.style.padding = '8px';
                    successMessage.style.margin = '4px 0';
                    successMessage.innerHTML = '🎉 РЕАЛЬНОЕ SSE подключение для ID=5 АКТИВНО!<br/>Теперь сообщения будут доставляться без ошибок!';
                    infoDiv.appendChild(successMessage);
                    infoDiv.scrollTop = infoDiv.scrollHeight;
                }
                
                // Обновляем кнопку
                const btn = document.getElementById('realConnectionBtn');
                if (btn) {
                    btn.textContent = '✅ ID=5 ПОДКЛЮЧЕН';
                    btn.style.background = '#28a745';
                    btn.onclick = disconnectRealUser2;
                }
            });
            
            user2EventSource.addEventListener('proposalMessage', function(event) {
                log(`📨 РЕАЛЬНОЕ SSE событие получено для ID=5`);
                try {
                    const eventData = JSON.parse(event.data);
                    
                    // Отображаем РЕАЛЬНОЕ сообщение в чате ID=5
                    const realMessage = {
                        proposalID: eventData.meta?.proposalID || 'unknown',
                        productID: eventData.meta?.productID || 'unknown',
                        senderID: eventData.meta?.userID,
                        senderName: `РЕАЛЬНОЕ SSE → User ${eventData.meta?.userID}`,
                        message: eventData.message || 'Пустое сообщение',
                        timestamp: eventData.createdAt || new Date().toISOString(),
                        isAdmin: false
                    };
                    
                    displayChatMessageUser2(realMessage);
                    
                    // Обновляем счетчик РЕАЛЬНЫХ сообщений
                    user2MessageCount++;
                    const countElement = document.getElementById('user2MessageCount');
                    if (countElement) {
                        countElement.textContent = `РЕАЛЬНО получено: ${user2MessageCount}`;
                        countElement.style.color = '#ff9800';
                        countElement.style.fontWeight = 'bold';
                    }
                    
                    const infoDiv = document.getElementById('chatUser2');
                    if (infoDiv) {
                        const realDelivery = document.createElement('div');
                        realDelivery.className = 'event success';
                        realDelivery.style.fontSize = '11px';
                        realDelivery.style.fontStyle = 'italic';
                        realDelivery.innerHTML = `🎯 РЕАЛЬНАЯ доставка SSE - никаких ошибок в backend!`;
                        infoDiv.appendChild(realDelivery);
                        infoDiv.scrollTop = infoDiv.scrollHeight;
                    }
                } catch (e) {
                    log(`Ошибка парсинга реального SSE события: ${e.message}`);
                }
            });
            
            user2EventSource.addEventListener('proposalStatus', function(event) {
                log(`📊 РЕАЛЬНОЕ SSE событие статуса получено для ID=5`);
                // Аналогично обрабатываем изменения статуса
            });
            
            user2EventSource.onerror = function(event) {
                log('❌ Ошибка реального SSE подключения для ID=5');
                console.error('User2 SSE error:', event);
                
                const btn = document.getElementById('realConnectionBtn');
                if (btn) {
                    btn.textContent = '❌ ОШИБКА подключения';
                    btn.style.background = '#f44336';
                }
            };
        }
        
        function disconnectRealUser2() {
            if (user2EventSource) {
                user2EventSource.close();
                user2EventSource = null;
                log('🔴 РЕАЛЬНОЕ SSE подключение ID=5 закрыто');
                
                // Восстанавливаем оригинальный токен если он был
                const originalTokenInput = document.getElementById('jwtToken');
                if (originalTokenInput && originalTokenInput.value) {
                    document.cookie = `auth_token=${originalTokenInput.value}; path=/; SameSite=Strict`;
                    log('🔄 Восстановлен оригинальный токен');
                }
                
                const btn = document.getElementById('realConnectionBtn');
                if (btn) {
                    btn.textContent = '🔗 РЕАЛЬНОЕ подключение';
                    btn.style.background = '#ff9800';
                    btn.onclick = createRealUser2Connection;
                }
                
                const infoDiv = document.getElementById('chatUser2');
                if (infoDiv) {
                    const disconnectMessage = document.createElement('div');
                    disconnectMessage.className = 'event warning';
                    disconnectMessage.style.fontSize = '12px';
                    disconnectMessage.style.textAlign = 'center';
                    disconnectMessage.innerHTML = '🔴 РЕАЛЬНОЕ SSE подключение ID=5 отключено<br/>🔄 Оригинальный токен восстановлен';
                    infoDiv.appendChild(disconnectMessage);
                    infoDiv.scrollTop = infoDiv.scrollHeight;
                }
            }
        }

        function updateSimulationInfo() {
            // Обновляем информацию о ожидаемом поведении при изменении настроек
            const user2IsAdmin = document.getElementById('user2IsAdmin')?.checked || false;
            const user2IsCreator = document.getElementById('user2IsCreator')?.checked || false;
            const senderIsAdmin = document.getElementById('senderIsAdmin')?.checked || false;
            
            let expectedBehavior = '';
            
            if (senderIsAdmin) {
                // Отправители - админы
                if (user2IsCreator) {
                    expectedBehavior = '• От админов → ID=2 ПОЛУЧИТ (как создатель заявки)';
                } else {
                    expectedBehavior = '• От админов → ID=2 НЕ получит (не создатель заявки)';
                }
            } else {
                // Отправители - обычные пользователи
                if (user2IsAdmin || user2IsCreator) {
                    const roles = [];
                    if (user2IsAdmin) roles.push('админ');
                    if (user2IsCreator) roles.push('создатель');
                    expectedBehavior = `• От пользователей → ID=2 ПОЛУЧИТ (как ${roles.join(' и ')})`;
                } else {
                    expectedBehavior = '• От пользователей → ID=2 НЕ получит (не админ и не создатель)';
                }
            }
            
            console.log('Настройки симуляции изменены:', {
                user2IsAdmin,
                user2IsCreator,
                senderIsAdmin,
                expectedBehavior
            });
        }

        function clearUnreadEvents() {
            document.getElementById('unreadEvents').innerHTML = '';
        }

        async function getUnreadEvents() {
            const token = document.getElementById('jwtToken').value;
            
            if (!token) {
                log('Введите JWT токен для получения непросмотренных событий', 'error');
                return;
            }

            try {
                log('Получение непросмотренных событий...', 'info');
                
                const response = await fetch('/v1/events/unread', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('API response data:', data, 'Type:', typeof data, 'Is Array:', Array.isArray(data));
                    
                    // Проверяем, что получили корректные данные
                    const events = Array.isArray(data) ? data : [];
                    log(`✅ Получено ${events.length} непросмотренных событий`, 'success');
                    
                    const unreadDiv = document.getElementById('unreadEvents');
                    if (!unreadDiv) {
                        log('❌ Элемент unreadEvents не найден', 'error');
                        return;
                    }
                    
                    unreadDiv.innerHTML = ''; // Очищаем предыдущие результаты

                    if (events.length === 0) {
                        unreadDiv.innerHTML = '<div class="event info">📭 Нет непросмотренных событий</div>';
                    } else {
                        events.forEach((event, index) => {
                            const eventDiv = document.createElement('div');
                            const timestamp = new Date(event.createdAt).toLocaleString();
                            const eventType = event.type === 'message' ? '💬 Сообщение' : '📋 Изменение статуса';
                            
                            eventDiv.className = 'event';
                            eventDiv.innerHTML = `
                                <div style="margin-bottom: 5px;">
                                    <strong>${eventType}</strong> 
                                    <span style="float: right; font-size: 0.9em; color: #666;">${timestamp}</span>
                                </div>
                                <div>📋 Заявка #${event.meta?.proposalID || 'неизвестно'}</div>
                                ${event.message ? `<div>💬 ${event.message}</div>` : ''}
                                ${event.meta?.status ? `<div>📊 Статус: ${event.meta.status}</div>` : ''}
                                ${event.meta?.userID ? `<div>👤 Пользователь: ${event.meta.userID}</div>` : ''}
                            `;
                            unreadDiv.appendChild(eventDiv);
                        });
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ Ошибка получения событий: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                console.error('Detailed error:', error);
                log(`❌ Ошибка сети при получении событий: ${error.message}`, 'error');
            }
        }

        async function markProposalAsViewed() {
            const productId = document.getElementById('viewProductId').value;
            const proposalId = document.getElementById('viewProposalId').value;
            const token = document.getElementById('jwtToken').value;
            
            if (!productId || !proposalId) {
                log('Заполните Product ID и Proposal ID для отметки просмотра', 'error');
                return;
            }

            if (!token) {
                log('Введите JWT токен для отметки просмотра', 'error');
                return;
            }

            try {
                log(`Отметка заявки ${proposalId} как просмотренной...`, 'info');
                
                const response = await fetch(`/v1/products/${productId}/proposals/${proposalId}/view`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        lastViewedAt: new Date().toISOString()
                    })
                });

                if (response.ok) {
                    log(`✅ Заявка ${proposalId} отмечена как просмотренная`, 'success');
                    
                    // Добавляем визуальную индикацию в чат
                    const chatDiv = document.getElementById('chat');
                    if (chatDiv) {
                        const viewedDiv = document.createElement('div');
                        viewedDiv.className = 'event info';
                        viewedDiv.innerHTML = `
                            <div style="text-align: center; font-style: italic;">
                                👁️ Заявка #${proposalId} отмечена как просмотренная в ${new Date().toLocaleTimeString()}
                            </div>
                        `;
                        chatDiv.appendChild(viewedDiv);
                        chatDiv.scrollTop = chatDiv.scrollHeight;
                    }
                    
                    // Автоматически обновляем список непросмотренных событий
                    setTimeout(() => {
                        log('🔄 Автоматическое обновление непросмотренных событий...', 'info');
                        getUnreadEvents();
                    }, 1000);
                } else {
                    const errorText = await response.text();
                    log(`❌ Ошибка отметки просмотра: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ Ошибка сети при отметке просмотра: ${error.message}`, 'error');
            }
        }

        function displayChatMessage(messageData) {
            console.log('displayChatMessage received:', messageData);
            
            const chatDiv = document.getElementById('chat');
            if (!chatDiv) {
                console.error('Chat container not found');
                return;
            }
            
            if (!messageData) {
                console.error('Message data is null or undefined');
                return;
            }
            
            const timestamp = messageData.timestamp ? new Date(messageData.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            
            const isAdmin = messageData.isAdmin || false;
            const isStatusChange = messageData.isStatusChange || false;
            const senderName = messageData.senderName || (messageData.senderID ? `User ${messageData.senderID}` : 'Неизвестный пользователь');
            const proposalID = messageData.proposalID || messageData.id || 'unknown';
            const message = messageData.message || 'Пустое сообщение';
            
            let roleLabel, className;
            if (isStatusChange) {
                roleLabel = '📋 Изменение статуса';
                className = 'success';
            } else if (isAdmin) {
                roleLabel = '👑 Админ';
                className = 'warning';
            } else {
                roleLabel = '👤 Пользователь';
                className = 'info';
            }
            
            messageDiv.className = `event ${className}`;
            messageDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <strong>${roleLabel}: ${senderName}</strong>
                    <small>${timestamp}</small>
                </div>
                <div style="margin-left: 10px;">
                    <strong>Заявка #${proposalID}:</strong> ${message}
                </div>
            `;
            
            try {
                chatDiv.appendChild(messageDiv);
                chatDiv.scrollTop = chatDiv.scrollHeight;
            } catch (error) {
                console.error('Error appending message to chat:', error);
            }
        }

        function displayChatMessageUser2(messageData) {
            const chatDiv = document.getElementById('chatUser2');
            if (!chatDiv) {
                console.error('ChatUser2 container not found');
                return;
            }
            
            if (!messageData) {
                console.error('Message data is null or undefined');
                return;
            }
            
            const timestamp = messageData.timestamp ? new Date(messageData.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            
            const isAdmin = messageData.isAdmin || false;
            const isStatusChange = messageData.isStatusChange || false;
            const senderName = messageData.senderName || (messageData.senderID ? `User ${messageData.senderID}` : 'Неизвестный пользователь');
            const proposalID = messageData.proposalID || messageData.id || 'unknown';
            const message = messageData.message || 'Пустое сообщение';
            
            let roleLabel, className;
            if (isStatusChange) {
                roleLabel = '📋 Изменение статуса';
                className = 'success';
            } else if (isAdmin) {
                roleLabel = '👑 Админ';
                className = 'warning';
            } else {
                roleLabel = '👤 Пользователь';
                className = 'info';
            }
            
            messageDiv.className = `event ${className}`;
            messageDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <strong>🔔 SSE → ${roleLabel}: ${senderName}</strong>
                    <small>${timestamp}</small>
                </div>
                <div style="margin-left: 10px;">
                    <strong>Заявка #${proposalID}:</strong> ${message}
                </div>
            `;
            
            try {
                chatDiv.appendChild(messageDiv);
                chatDiv.scrollTop = chatDiv.scrollHeight;
            } catch (error) {
                console.error('Error appending message to chatUser2:', error);
            }
        }

        function checkAndDisplayForUser2(messageData, eventData) {
            // СИМУЛЯЦИЯ: Показываем как работает SSE когда ID=5 подключен
            
            const senderID = eventData.meta?.userID;
            const proposalID = eventData.meta?.proposalID;
            
            // Проверяем, должен ли ID=5 получить сообщение согласно логике
            let shouldReceiveMessage = false;
            let reason = '';
            
            if (senderID === 5) {
                shouldReceiveMessage = false;
                reason = 'Отправитель - сам пользователь ID=5 (не получает свои сообщения)';
            } else {
                // ID=5 админ + принудительно добавлен в код - ДОЛЖЕН получить
                shouldReceiveMessage = true;
                reason = 'ID=5 в recipients (админ + принудительно добавлен в код)';
            }
            
            console.log(`🔍 Симуляция SSE для ID=5:`, {
                senderID,
                proposalID,
                shouldReceiveMessage,
                user2Connected,
                reason
            });
            
            const infoDiv = document.getElementById('chatUser2');
            if (infoDiv) {
                if (shouldReceiveMessage) {
                    if (user2Connected) {
                        // УСПЕШНАЯ доставка (симуляция)
                        setTimeout(() => {
                            const messageForUser2 = {
                                ...messageData,
                                senderName: `${messageData.senderName}`
                            };
                            displayChatMessageUser2(messageForUser2);
                            
                            // Обновляем счетчик сообщений
                            user2MessageCount++;
                            const countElement = document.getElementById('user2MessageCount');
                            if (countElement) {
                                countElement.textContent = `Получено: ${user2MessageCount}`;
                                countElement.style.color = '#28a745';
                                countElement.style.fontWeight = 'bold';
                            }
                            
                            // Показываем статус успешной доставки
                            const successMessage = document.createElement('div');
                            successMessage.className = 'event success';
                            successMessage.style.fontSize = '11px';
                            successMessage.style.fontStyle = 'italic';
                            successMessage.innerHTML = `✅ SSE доставлено успешно: ${reason}`;
                            infoDiv.appendChild(successMessage);
                            infoDiv.scrollTop = infoDiv.scrollHeight;
                        }, 100 + Math.random() * 200); // Имитация сетевой задержки
                    } else {
                        // ID=2 НЕ подключен
                        const failMessage = document.createElement('div');
                        failMessage.className = 'event warning';
                        failMessage.style.fontSize = '12px';
                        failMessage.style.border = '1px solid #ff9800';
                        failMessage.style.padding = '6px';
                        failMessage.style.margin = '2px 0';
                        failMessage.innerHTML = `
                            <strong>❌ НЕ ДОСТАВЛЕНО</strong><br/>
                            От: User ${senderID}<br/>
                            Сообщение: "${messageData.message}"<br/>
                            Причина: ID=5 отключен от SSE
                        `;
                        infoDiv.appendChild(failMessage);
                        infoDiv.scrollTop = infoDiv.scrollHeight;
                    }
                } else {
                    // НЕ должен получать (отправитель сам ID=5)
                    const infoMessage = document.createElement('div');
                    infoMessage.className = 'event info';
                    infoMessage.style.fontSize = '11px';
                    infoMessage.style.fontStyle = 'italic';
                    infoMessage.innerHTML = `ℹ️ ${reason}`;
                    infoDiv.appendChild(infoMessage);
                    infoDiv.scrollTop = infoDiv.scrollHeight;
                }
            }
        }


         async function sendUserMessage() {
             const message = document.getElementById('userMessage').value;
             const token = document.getElementById('jwtToken').value;
             const productId = document.getElementById('msgProductId').value;
             const proposalId = document.getElementById('msgProposalId').value;

             if (!message || !message.trim()) {
                 log('Введите сообщение', 'error');
                 return;
             }

             if (!productId || !proposalId) {
                 log('Заполните Product ID и Proposal ID для отправки сообщения', 'error');
                 return;
             }

             if (!token) {
                 log('Введите JWT токен для отправки сообщения', 'error');
                 return;
             }

             try {
                 log(`Отправка сообщения к заявке ${proposalId}...`, 'info');
                 
                 const response = await fetch(`/v1/products/${productId}/proposals/${proposalId}/message`, {
                     method: 'POST',
                     headers: {
                         'Content-Type': 'application/json',
                         'Authorization': `Bearer ${token}`
                     },
                     credentials: 'include',
                     body: JSON.stringify({
                         message: message.trim()
                     })
                 });

                 if (response.ok) {
                     const data = await response.json();
                     log(`✅ Сообщение отправлено успешно`, 'success');
                     document.getElementById('userMessage').value = ''; // Очищаем поле ввода
                     
                     // Отображаем отправленное сообщение в чате
                     if (data.data) {
                         // Преобразуем SSE формат в формат чата
                         const apiMessage = data.data;
                         const messageData = {
                             proposalID: apiMessage.meta?.proposalID || proposalId,
                             productID: apiMessage.meta?.productID || productId,
                             senderID: apiMessage.meta?.userID,
                             senderName: 'Вы',
                             message: apiMessage.message || message.trim(),
                             timestamp: apiMessage.createdAt || new Date().toISOString(),
                             isAdmin: false
                         };
                         displayChatMessage(messageData);
                     } else {
                         // Создаем сообщение вручную, если API не вернул data
                         const messageData = {
                             proposalID: proposalId,
                             message: message.trim(),
                             timestamp: new Date().toISOString(),
                             senderName: 'Вы',
                             isAdmin: false
                         };
                         displayChatMessage(messageData);
                     }
                 } else {
                     const errorText = await response.text();
                     log(`❌ Ошибка отправки сообщения: ${response.status} - ${errorText}`, 'error');
                 }
             } catch (error) {
                 log(`❌ Ошибка сети при отправке сообщения: ${error.message}`, 'error');
             }
         }

        async function checkToken() {
            const token = document.getElementById('jwtToken').value;
            const authStatusDiv = document.getElementById('authStatus');
            
            if (!token) {
                authStatusDiv.innerHTML = '<div class="status error">Введите JWT токен для проверки</div>';
                return;
            }

            try {
                log('Проверка токена...', 'info');
                authStatusDiv.innerHTML = '<div class="status info">Проверка токена...</div>';
                
                // Тестируем токен на простом эндпоинте
                const response = await fetch('/v1/users/current', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    const userInfo = data.email || data.fullName || `ID: ${data.id}`;
                    log(`✅ Токен валидный! Пользователь: ${userInfo}`, 'success');
                    authStatusDiv.innerHTML = `<div class="status success">✅ Токен валидный! Пользователь: ${userInfo}</div>`;
                } else {
                    const errorText = await response.text();
                    log(`❌ Токен невалидный: ${response.status} - ${errorText}`, 'error');
                    authStatusDiv.innerHTML = `<div class="status error">❌ Токен невалидный: ${response.status}</div>`;
                }
            } catch (error) {
                log(`❌ Ошибка при проверке токена: ${error.message}`, 'error');
                authStatusDiv.innerHTML = `<div class="status error">❌ Ошибка при проверке токена: ${error.message}</div>`;
            }
        }

        // Автоматическое подключение при загрузке страницы
        window.addEventListener('load', function() {
            log('Страница загружена. Введите JWT токен и нажмите "Подключиться к SSE" для начала тестирования.');
            updateConnectionStatus(false, 'Отключено');
            
            // Добавляем информационное сообщение в чат ID=2
            const chatUser2 = document.getElementById('chatUser2');
            if (chatUser2) {
                const infoMessage = document.createElement('div');
                infoMessage.className = 'event info';
                infoMessage.style.fontSize = '12px';
                infoMessage.innerHTML = `
                    <strong>🎮 ДВОЙНОЕ ТЕСТИРОВАНИЕ: Симуляция + Реальность</strong><br/>
                    <br/>
                    <strong>🎯 Режимы работы:</strong><br/>
                    1. <strong>СИМУЛЯЦИЯ</strong> - показывает как должно работать<br/>
                    2. <strong>РЕАЛЬНОЕ подключение</strong> - исправляет проблему!<br/>
                    <br/>
                    <strong>🔗 Реальное решение проблемы:</strong><br/>
                    • Введите JWT токен пользователя с ID=5<br/>
                    • Нажмите "РЕАЛЬНОЕ подключение"<br/>
                    • Теперь ID=5 будет получать сообщения БЕЗ ошибок!<br/>
                    • В логах появится: "SSE connection established for user","userID":"5"<br/>
                    <br/>
                    <strong>💡 Тест:</strong><br/>
                    1. Сначала отправьте сообщение без реального подключения<br/>
                    2. Создайте реальное подключение с токеном<br/>
                    3. Отправьте сообщение снова - увидите разницу!
                `;
                infoMessage.style.background = 'rgba(40, 167, 69, 0.1)';
                infoMessage.style.border = '1px solid #28a745';
                chatUser2.appendChild(infoMessage);
            }
        });

        // Закрытие соединения при закрытии страницы
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>