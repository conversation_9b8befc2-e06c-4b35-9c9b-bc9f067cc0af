{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(make test:*)", "Bash(rm:*)", "<PERSON><PERSON>(make:*)", "Bash(go build:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(go test:*)", "Bash(go tool cover:*)", "Bash(psql:*)", "Ba<PERSON>(goose:*)", "Bash(go mod:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/check-endpoint-consistency.sh:*)", "Bash(awk:*)", "Bash(./scripts/check-endpoint-methods-consistency.sh:*)", "Bash(bash:*)", "<PERSON><PERSON>(echo:*)", "Bash(MINIMAL_CHECK=1 ./scripts/pre-push-check.sh)", "Bash(SKIP_ENDPOINT_CONSISTENCY=1 ./scripts/pre-push-check.sh)", "<PERSON><PERSON>(timeout:*)", "Bash(SKIP_TESTS=1 SKIP_COVERAGE=1 SKIP_SECURITY=1 SKIP_VULNERABILITIES=1 ./scripts/pre-push-check.sh)", "Bash(/dev/null)", "Bash(./scripts/e2e_tests.sh:*)", "Bash(./scripts/pre-push-check.sh:*)", "Bash(gofmt:*)", "<PERSON><PERSON>(mv:*)", "Bash(brew install:*)", "<PERSON><PERSON>(task:*)", "Bash(golangci-lint:*)", "Bash(gofumpt:*)", "Bash(gci:*)", "Bash(./bin/golangci-lint:*)", "Bash(./bin/gci:*)", "Bash(./bin/gofumpt -version)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(./scripts/remove-empty-lines-before-braces.sh:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)"], "deny": []}}