-- Скрипт для наполнения БД данными для тестов авторизации
-- Содержит пользователей с дефолтными ролями из authorization.yaml

-- Проверяем и создаем категории
INSERT INTO categories (name) 
SELECT 'Development' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Development')
UNION ALL
SELECT 'Management' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Management')
UNION ALL
SELECT 'QA' WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'QA');

-- Проверяем и создаем разрешения
INSERT INTO permissions (name, method) 
SELECT name, method FROM (VALUES
    ('product', 'create'),
    ('product', 'view'),
    ('product', 'update'),
    ('product', 'delete'),
    ('product_description', 'view'),
    ('product_description', 'update'),
    ('product_participant', 'create'),
    ('product_participant', 'view'),
    ('product_participant', 'delete'),
    ('product_proposal', 'create'),
    ('product_proposal', 'view'),
    ('product_proposal', 'update'),
    ('product_proposal', 'delete'),
    ('admin_user', 'view'),
    ('admin_user', 'update'),
    ('profile', 'view')
) AS v(name, method)
WHERE NOT EXISTS (SELECT 1 FROM permissions p WHERE p.name = v.name AND p.method = v.method);

-- Проверяем и создаем пользователей
INSERT INTO users (category_id, email, full_name, position, is_admin) 
SELECT category_id, email, full_name, position, is_admin FROM (VALUES
    (1, '<EMAIL>', 'Account Admin User', 'System Administrator', false),
    (1, '<EMAIL>', 'Account User', 'Regular User', false),
    (1, '<EMAIL>', 'Product Owner', 'Product Owner', false),
    (1, '<EMAIL>', 'Product Participant', 'Developer', false),
    (1, '<EMAIL>', 'Unauthorized User', 'No Role User', false)
) AS v(category_id, email, full_name, position, is_admin)
WHERE NOT EXISTS (SELECT 1 FROM users u WHERE u.email = v.email);

-- Проверяем и создаем системные роли
INSERT INTO roles (name, is_system) 
SELECT name, is_system FROM (VALUES
    ('account_admin', true),
    ('account_user', true),
    ('product_owner', true),
    ('product_participant', true)
) AS v(name, is_system)
WHERE NOT EXISTS (SELECT 1 FROM roles r WHERE r.name = v.name);

-- Назначаем дефолтные роли пользователям (только если не существуют)
INSERT INTO users_roles (user_id, role_id)
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.email = '<EMAIL>' AND r.name = 'account_admin'
AND NOT EXISTS (SELECT 1 FROM users_roles ur WHERE ur.user_id = u.id AND ur.role_id = r.id);

INSERT INTO users_roles (user_id, role_id)
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.email = '<EMAIL>' AND r.name = 'account_user'
AND NOT EXISTS (SELECT 1 FROM users_roles ur WHERE ur.user_id = u.id AND ur.role_id = r.id);

INSERT INTO users_roles (user_id, role_id)
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.email = '<EMAIL>' AND r.name = 'product_owner'
AND NOT EXISTS (SELECT 1 FROM users_roles ur WHERE ur.user_id = u.id AND ur.role_id = r.id);

INSERT INTO users_roles (user_id, role_id)
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.email = '<EMAIL>' AND r.name = 'product_participant'
AND NOT EXISTS (SELECT 1 FROM users_roles ur WHERE ur.user_id = u.id AND ur.role_id = r.id);

-- Создаем тестовые продукты
INSERT INTO products (iid, tech_name, name, description, creator_id) VALUES 
    ('AU01', 'AUTHPRD1', 'Authorization Test Product 1', 'Product for authorization testing', 
     (SELECT id FROM users WHERE email = '<EMAIL>')),
    ('AU02', 'AUTHPRD2', 'Authorization Test Product 2', 'Second product for authorization testing', 
     (SELECT id FROM users WHERE email = '<EMAIL>'));

-- Добавляем участников в продукты (только если не существуют)
INSERT INTO participants (product_id, user_id) 
SELECT 1, id FROM users WHERE email IN ('<EMAIL>', '<EMAIL>')
AND NOT EXISTS (SELECT 1 FROM participants p WHERE p.product_id = 1 AND p.user_id = users.id);

INSERT INTO participants (product_id, user_id)
SELECT 2, id FROM users WHERE email = '<EMAIL>'
AND NOT EXISTS (SELECT 1 FROM participants p WHERE p.product_id = 2 AND p.user_id = users.id);

-- Назначаем роли участникам продуктов (только если не существуют)
INSERT INTO participants_roles (participant_id, role_id)
SELECT p.id, r.id 
FROM participants p 
JOIN users u ON p.user_id = u.id 
JOIN roles r ON r.name = 'product_owner' 
WHERE p.product_id = 1 AND u.email = '<EMAIL>'
AND NOT EXISTS (SELECT 1 FROM participants_roles pr WHERE pr.participant_id = p.id AND pr.role_id = r.id);

INSERT INTO participants_roles (participant_id, role_id)
SELECT p.id, r.id 
FROM participants p 
JOIN users u ON p.user_id = u.id 
JOIN roles r ON r.name = 'product_participant' 
WHERE p.product_id IN (1, 2) AND u.email = '<EMAIL>'
AND NOT EXISTS (SELECT 1 FROM participants_roles pr WHERE pr.participant_id = p.id AND pr.role_id = r.id);

-- Создаем тестовые группы
INSERT INTO groups (name, product_id, is_system) VALUES
    ('Auth Test System Group', NULL, true),
    ('Auth Product 1 Group', 1, false),
    ('Auth Product 2 Group', 2, false);

-- Создаем тестовые proposals для тестирования прав
INSERT INTO proposals (product_id, creator_id, price, status, seq_num) VALUES
    (1, (SELECT id FROM users WHERE email = '<EMAIL>'), 
     100.0, 'draft', 1),
    (2, (SELECT id FROM users WHERE email = '<EMAIL>'), 
     200.0, 'draft', 1);

-- Комментарий: БД для тестов авторизации создана!
-- Содержит пользователей с дефолтными ролями и тестовые данные 