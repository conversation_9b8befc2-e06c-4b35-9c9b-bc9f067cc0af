-- Скрипт для тестирования удаления участников и ролей

\echo '=== ТЕСТИРОВАНИЕ УДАЛЕНИЯ УЧАСТНИКОВ И РОЛЕЙ ==='

-- Показываем текущее состояние продукта 1 перед тестами
\echo 'Текущее состояние продукта 1 перед тестами:'
SELECT 
    p.id as product_id,
    p.name as product_name,
    pt.id as participant_id,
    u.full_name as user_name,
    u.email,
    COALESCE(string_agg(r.name, ', '), 'No roles') as roles
FROM products p
JOIN participants pt ON p.id = pt.product_id
JOIN users u ON pt.user_id = u.id
LEFT JOIN participants_roles pr ON pt.id = pr.participant_id
LEFT JOIN roles r ON pr.role_id = r.id
WHERE p.id = 1
GROUP BY p.id, p.name, pt.id, u.full_name, u.email
ORDER BY pt.id;

-- Тест 1: Удаление обычного участника (без роли владельца)
\echo 'Тест 1: Удаляем обычного участника (должно пройти успешно)'
DELETE FROM participants 
WHERE product_id = 1 AND user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- Проверяем результат
\echo 'Состояние после удаления обычного участника:'
SELECT 
    pt.id as participant_id,
    u.full_name as user_name,
    u.email
FROM participants pt
JOIN users u ON pt.user_id = u.id
WHERE pt.product_id = 1
ORDER BY pt.id;

-- Тест 2: Попытка удаления роли владельца при наличии 2 владельцев (должно пройти успешно)
\echo 'Тест 2: Удаляем роль владельца при наличии других владельцев (должно пройти успешно)'
DELETE FROM participants_roles 
WHERE participant_id IN (
    SELECT p.id 
    FROM participants p 
    JOIN users u ON p.user_id = u.id 
    WHERE p.product_id = 1 AND u.email = '<EMAIL>'
) AND role_id = (SELECT id FROM roles WHERE name = 'product_owner');

-- Тест 3: Проверяем, что у нас остался только один владелец
\echo 'Количество владельцев после удаления роли:'
SELECT COUNT(*) as owner_count
FROM participants p
JOIN participants_roles pr ON p.id = pr.participant_id
JOIN roles r ON pr.role_id = r.id
WHERE p.product_id = 1 AND r.name = 'product_owner';

-- Тест 4: Попытка удаления роли у единственного владельца (должна завершиться ошибкой)
\echo 'Тест 4: Попытка удаления роли у единственного владельца (должна завершиться ошибкой)'
\set ON_ERROR_STOP off
DELETE FROM participants_roles 
WHERE participant_id IN (
    SELECT p.id 
    FROM participants p 
    JOIN users u ON p.user_id = u.id 
    WHERE p.product_id = 1 AND u.email = '<EMAIL>'
) AND role_id = (SELECT id FROM roles WHERE name = 'product_owner');
\set ON_ERROR_STOP on

-- Тест 5: Удаляем участника, который больше не является владельцем (должно пройти успешно)
\echo 'Тест 5: Удаляем участника, который больше не является владельцем (должно пройти успешно)'
DELETE FROM participants 
WHERE product_id = 1 AND user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- Тест 6: Попытка удаления последнего владельца (должна завершиться ошибкой)
\echo 'Тест 6: Попытка удаления последнего владельца (должна завершиться ошибкой)'
\set ON_ERROR_STOP off
DELETE FROM participants 
WHERE product_id = 1 AND user_id = (SELECT id FROM users WHERE email = '<EMAIL>');
\set ON_ERROR_STOP on

-- Итоговое состояние продукта 1
\echo 'Итоговое состояние продукта 1:'
SELECT 
    p.id as product_id,
    p.name as product_name,
    pt.id as participant_id,
    u.full_name as user_name,
    u.email,
    COALESCE(string_agg(r.name, ', '), 'No roles') as roles
FROM products p
JOIN participants pt ON p.id = pt.product_id
JOIN users u ON pt.user_id = u.id
LEFT JOIN participants_roles pr ON pt.id = pr.participant_id
LEFT JOIN roles r ON pr.role_id = r.id
WHERE p.id = 1
GROUP BY p.id, p.name, pt.id, u.full_name, u.email
ORDER BY pt.id;
