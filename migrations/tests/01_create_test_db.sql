-- Скрипт для наполнения тестовой БД данными
-- Предварительно база должна быть создана и миграции применены!

-- Создаем категории
INSERT INTO categories (name) VALUES 
    ('Development'),
    ('Management'),
    ('QA');

-- Создаем разрешения
INSERT INTO permissions (name, method) VALUES 
    ('product.create', 'POST'),
    ('product.read', 'GET'),
    ('product.update', 'PUT'),
    ('product.delete', 'DELETE'),
    ('participant.manage', 'POST'),
    ('participant.read', 'GET'),
    ('participant.delete', 'DELETE');

-- Создаем пользователей
INSERT INTO users (category_id, email, full_name, position, is_admin) VALUES 
    (1, '<EMAIL>', 'Владелец 1', 'Product Owner', false),
    (1, '<EMAIL>', 'Владелец 2', 'Product Owner', false),
    (1, '<EMAIL>', 'Разработчик 1', 'Senior Developer', false),
    (1, '<EMAIL>', 'Разработчик 2', 'Junior Developer', false),
    (2, '<EMAIL>', 'Менеджер', 'Project Manager', false),
    (3, '<EMAIL>', 'Тестировщик', 'QA Engineer', false),
    (1, '<EMAIL>', 'Администратор', 'System Admin', true);

-- Создаем продукты
INSERT INTO products (iid, tech_name, name, description, creator_id) VALUES 
    ('P001', 'TESTPROD1', 'Тестовый продукт 1', 'Продукт для тестирования функциональности участников', 1),
    ('P002', 'TESTPROD2', 'Тестовый продукт 2', 'Продукт для тестирования удаления', 2);

-- Получаем ID пользователей (они могут отличаться из-за уже существующих данных)
-- Добавляем участников в первый продукт
INSERT INTO participants (product_id, user_id) 
SELECT 1, id FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Добавляем участников во второй продукт  
INSERT INTO participants (product_id, user_id)
SELECT 2, id FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Получаем системные роли
-- Назначаем роли участникам первого продукта
INSERT INTO participants_roles (participant_id, role_id)
SELECT p.id, r.id 
FROM participants p 
JOIN users u ON p.user_id = u.id 
JOIN roles r ON r.name = 'product_owner' 
WHERE p.product_id = 1 AND u.email = '<EMAIL>';

-- Назначаем роли участникам второго продукта
INSERT INTO participants_roles (participant_id, role_id)
SELECT p.id, r.id 
FROM participants p 
JOIN users u ON p.user_id = u.id 
JOIN roles r ON r.name = 'product_owner' 
WHERE p.product_id = 2 AND u.email = '<EMAIL>';

-- Создаем тестовые системные группы
INSERT INTO groups (name, product_id, is_system) VALUES
    ('System Admins', NULL, true),
    ('System Developers', NULL, true),
    ('Test System Group', NULL, true);

-- Создаем тестовые продуктовые группы
INSERT INTO groups (name, product_id, is_system) VALUES
    ('Product 1 Admins', 1, false),
    ('Product 1 Developers', 1, false),
    ('Product 2 Admins', 2, false);

-- Комментарий: Тестовая база данных создана и наполнена!
-- Данные включают пользователей, продукты, участников и группы для тестирования
