-- Скрипт для тестирования добавления участников и ролей

\echo '=== ТЕСТИРОВАНИЕ ДОБАВЛЕНИЯ УЧАСТНИКОВ ==='

-- Тест 1: Добавление нового участника в продукт
\echo 'Тест 1: Добавляем нового участника в продукт 1'
INSERT INTO participants (product_id, user_id) 
SELECT 1, id FROM users WHERE email = '<EMAIL>';

-- Проверяем результат
SELECT 
    p.name as product_name,
    u.full_name as user_name,
    pt.id as participant_id
FROM products p
JOIN participants pt ON p.id = pt.product_id
JOIN users u ON pt.user_id = u.id
WHERE p.id = 1 AND u.email = '<EMAIL>';

-- Тест 2: Добавление роли владельца второму участнику
\echo 'Тест 2: Добавляем роль владельца участнику (должно пройти успешно)'
INSERT INTO participants_roles (participant_id, role_id)
SELECT p.id, r.id 
FROM participants p 
JOIN users u ON p.user_id = u.id 
JOIN roles r ON r.name = 'product_owner' 
WHERE p.product_id = 1 AND u.email = '<EMAIL>';

-- Проверяем количество владельцев в продукте 1
\echo 'Количество владельцев в продукте 1:'
SELECT COUNT(*) as owner_count
FROM participants p
JOIN participants_roles pr ON p.id = pr.participant_id
JOIN roles r ON pr.role_id = r.id
WHERE p.product_id = 1 AND r.name = 'product_owner';

-- Тест 3: Попытка добавить дублирующую роль (должно завершиться ошибкой)
\echo 'Тест 3: Попытка добавить дублирующую роль владельца (должна завершиться ошибкой)'
\set ON_ERROR_STOP off
INSERT INTO participants_roles (participant_id, role_id)
SELECT p.id, r.id 
FROM participants p 
JOIN users u ON p.user_id = u.id 
JOIN roles r ON r.name = 'product_owner' 
WHERE p.product_id = 1 AND u.email = '<EMAIL>';
\set ON_ERROR_STOP on

-- Итоговое состояние продукта 1
\echo 'Итоговое состояние продукта 1:'
SELECT 
    p.id as product_id,
    p.name as product_name,
    pt.id as participant_id,
    u.full_name as user_name,
    u.email,
    COALESCE(string_agg(r.name, ', '), 'No roles') as roles
FROM products p
JOIN participants pt ON p.id = pt.product_id
JOIN users u ON pt.user_id = u.id
LEFT JOIN participants_roles pr ON pt.id = pr.participant_id
LEFT JOIN roles r ON pr.role_id = r.id
WHERE p.id = 1
GROUP BY p.id, p.name, pt.id, u.full_name, u.email
ORDER BY pt.id;
