-- Скрипт для тестирования удаления продукта

\echo '=== ТЕСТИРОВАНИЕ УДАЛЕНИЯ ПРОДУКТА ==='

-- Показываем состояние продукта 2 перед удалением
\echo 'Состояние продукта 2 перед удалением:'
SELECT 
    p.id as product_id,
    p.name as product_name,
    pt.id as participant_id,
    u.full_name as user_name,
    u.email,
    COALESCE(string_agg(r.name, ', '), 'No roles') as roles
FROM products p
JOIN participants pt ON p.id = pt.product_id
JOIN users u ON pt.user_id = u.id
LEFT JOIN participants_roles pr ON pt.id = pr.participant_id
LEFT JOIN roles r ON pr.role_id = r.id
WHERE p.id = 2
GROUP BY p.id, p.name, pt.id, u.full_name, u.email
ORDER BY pt.id;

-- Показываем количество участников и ролей перед удалением
\echo 'Статистика перед удалением продукта 2:'
SELECT 
    'participants' as table_name,
    COUNT(*) as count
FROM participants WHERE product_id = 2
UNION ALL
SELECT 
    'participants_roles' as table_name,
    COUNT(*) as count
FROM participants_roles pr
JOIN participants p ON pr.participant_id = p.id
WHERE p.product_id = 2;

-- Тест: Удаление продукта (должно каскадно удалить всех участников и их роли)
\echo 'Тест: Удаляем продукт 2 (должно каскадно удалить всех участников независимо от ролей)'
DELETE FROM products WHERE id = 2;

-- Проверяем, что участники удалились
\echo 'Проверяем участников продукта 2 после удаления (должно быть пусто):'
SELECT COUNT(*) as participants_count 
FROM participants WHERE product_id = 2;

-- Проверяем, что роли участников удалились
\echo 'Проверяем роли участников продукта 2 после удаления (должно быть пусто):'
SELECT COUNT(*) as roles_count
FROM participants_roles pr
WHERE pr.participant_id IN (
    SELECT id FROM participants WHERE product_id = 2
);

-- Проверяем, что продукт удален
\echo 'Проверяем, что продукт 2 удален:'
SELECT COUNT(*) as product_count 
FROM products WHERE id = 2;

-- Показываем оставшиеся продукты
\echo 'Оставшиеся продукты:'
SELECT id, name, tech_name FROM products ORDER BY id;

\echo 'Удаление продукта завершено успешно!';
