-- +goose Up
-- +goose StatementBegin

/* =======================================================
   СОЗДАНИЕ ТАБЛИЦ
   =======================================================
   
   • categories - категории пользователей
   • permissions - разрешения системы
   • users - пользователи системы
   • products - продукты
   • groups - группы пользователей
   • roles - роли пользователей
   • categories_permissions - связь категорий с разрешениями
   • categories_groups - связь категорий с группами
   • categories_roles - связь категорий с ролями
   • users_groups - принадлежность пользователей к группам
   • users_roles - назначение ролей пользователям
   • participants - участники продуктов
   • participants_roles - роли участников продуктов
   • participants_groups - группы участников продуктов
   • groups_roles - связь групп с ролями
   • roles_permissions - связь ролей с разрешениями
   • sections - секции продуктов
   • proposals - заявки по продуктам
   • proposals_history - история изменений заявок
   • proposal_user_views - просмотры заявок пользователями
   
   ======================================================= */

CREATE TABLE categories
(
    id   SERIAL,
    name VARCHAR(30) NOT NULL UNIQUE,
    PRIMARY KEY (id)
);

CREATE TABLE permissions
(
    id     SERIAL,
    name   VARCHAR(30) NOT NULL,
    method VARCHAR(30) NOT NULL,
    CONSTRAINT unique_name_method UNIQUE (name, method),
    PRIMARY KEY (id)
);

CREATE TABLE users
(
    id                     SERIAL,
    category_id            INT         NOT NULL,
    email                  VARCHAR(60) NOT NULL UNIQUE,
    full_name              VARCHAR(60) NOT NULL,
    position               VARCHAR(255),
    is_admin               BOOLEAN     NOT NULL DEFAULT false,
    active_flg             BOOLEAN     NOT NULL DEFAULT true,
    last_active_product_id INT         NOT NULL DEFAULT 0,
    last_login_at          TIMESTAMPTZ          DEFAULT NULL,
    created_at             TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at             TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at             TIMESTAMPTZ          DEFAULT NULL,
    photo                  BYTEA,
    PRIMARY KEY (id),
    FOREIGN KEY (category_id) REFERENCES categories (id)
);

CREATE TABLE products
(
    id          SERIAL UNIQUE,
    iid         VARCHAR(4),
    tech_name   VARCHAR(9)  NOT NULL UNIQUE,
    name        VARCHAR(80) NOT NULL,
    description VARCHAR(255),
    creator_id  INT         NOT NULL,
    active_flg  BOOLEAN     NOT NULL DEFAULT true,
    created_at  TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at  TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at  TIMESTAMPTZ          DEFAULT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (creator_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE groups
(
    id              SERIAL,
    name            VARCHAR(30) NOT NULL,
    product_id      INT,
    is_system       BOOLEAN     NOT NULL DEFAULT false,
    active_flg      BOOLEAN     NOT NULL DEFAULT true,
    created_at      TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at      TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at      TIMESTAMPTZ          DEFAULT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
);

CREATE TABLE roles
(
    id             SERIAL,
    name           VARCHAR(30) NOT NULL,
    product_id     INT,
    is_system      BOOLEAN     NOT NULL DEFAULT false,
    active_flg     BOOLEAN     NOT NULL DEFAULT true,
    is_protected   BOOLEAN     NOT NULL DEFAULT false,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at     TIMESTAMPTZ          DEFAULT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
);

CREATE TABLE categories_permissions
(
    id            SERIAL,
    category_id   INT NOT NULL,
    permission_id INT NOT NULL,
    CONSTRAINT unique_category_permission UNIQUE (category_id, permission_id),
    PRIMARY KEY (id),
    FOREIGN KEY (category_id) REFERENCES categories (id),
    FOREIGN KEY (permission_id) REFERENCES permissions (id)
);

CREATE TABLE categories_groups
(
    id          SERIAL,
    category_id INT NOT NULL,
    group_id    INT NOT NULL,
    CONSTRAINT unique_category_group UNIQUE (category_id, group_id),
    PRIMARY KEY (id),
    FOREIGN KEY (category_id) REFERENCES categories (id),
    FOREIGN KEY (group_id) REFERENCES groups (id)
);

CREATE TABLE categories_roles
(
    id          SERIAL,
    category_id INT NOT NULL,
    role_id     INT NOT NULL,
    CONSTRAINT unique_category_role UNIQUE (category_id, role_id),
    PRIMARY KEY (id),
    FOREIGN KEY (category_id) REFERENCES categories (id),
    FOREIGN KEY (role_id) REFERENCES roles (id)
);

CREATE TABLE users_groups
(
    id         SERIAL,
    user_id    INT         NOT NULL,
    group_id   INT         NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_user_group UNIQUE (user_id, group_id),
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE
);

CREATE TABLE users_roles
(
    id         SERIAL,
    user_id    INT         NOT NULL,
    role_id    INT         NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_user_role UNIQUE (user_id, role_id),
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE
);

CREATE TABLE participants
(
    id         SERIAL,
    product_id INT         NOT NULL,
    user_id    INT         NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_product_user UNIQUE (product_id, user_id),
    PRIMARY KEY (id),
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE participants_roles
(
    id             SERIAL,
    participant_id INT         NOT NULL,
    role_id        INT         NOT NULL,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_participant_role UNIQUE (participant_id, role_id),
    PRIMARY KEY (id),
    FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
    FOREIGN KEY (participant_id) REFERENCES participants (id) ON DELETE CASCADE
);

CREATE TABLE participants_groups
(
    id             SERIAL,
    participant_id INT         NOT NULL,
    group_id       INT         NOT NULL,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_participant_group UNIQUE (participant_id, group_id),
    PRIMARY KEY (id),
    FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE,
    FOREIGN KEY (participant_id) REFERENCES participants (id) ON DELETE CASCADE
);

CREATE TABLE groups_roles
(
    id         SERIAL,
    group_id   INT         NOT NULL,
    role_id    INT         NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_group_role UNIQUE (group_id, role_id),
    PRIMARY KEY (id),
    FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups (id) ON DELETE CASCADE
);

CREATE TABLE roles_permissions
(
    id            SERIAL,
    role_id       INT         NOT NULL,
    permission_id INT         NOT NULL,
    created_at    TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_role_permission UNIQUE (role_id, permission_id),
    PRIMARY KEY (id),
    FOREIGN KEY (role_id) REFERENCES roles (id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions (id) ON DELETE CASCADE
);

CREATE TABLE sections
(
    id         SERIAL,
    product_id INT NOT NULL,
    seq_num    INT NOT NULL,
    num        INT NOT NULL,
    data       JSONB,
    CONSTRAINT unique_product_id_seq_num_num UNIQUE (product_id, seq_num, num),
    PRIMARY KEY (id),
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
);

CREATE TABLE proposals
(
    id         SERIAL,
    product_id INT         NOT NULL,
    seq_num    INT         NOT NULL,
    price      DECIMAL     NOT NULL,
    status     VARCHAR(20) NOT NULL CHECK (status IN ('draft', 'archive', 'on_approval', 'rejected', 'approved')) DEFAULT 'draft',
    type       VARCHAR(20) NOT NULL CHECK (type IN ('OR', 'ER')) DEFAULT 'OR',
    creator_id INT         NOT NULL DEFAULT 1,
    active_flg BOOLEAN     NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ          DEFAULT NULL,
    CONSTRAINT unique_product_id_seq_num UNIQUE (product_id, seq_num),
    PRIMARY KEY (id),
    FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users (id)
);

CREATE TABLE proposals_history
(
    id          SERIAL UNIQUE,
    proposal_id INT         NOT NULL,
    status      VARCHAR(20) NOT NULL,
    message     VARCHAR(255),
    event_type  VARCHAR(20) NOT NULL DEFAULT 'status' CHECK (event_type IN ('status', 'message')),
    user_id     BIGINT      DEFAULT NULL,
    created_at  TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id),
    FOREIGN KEY (proposal_id) REFERENCES proposals (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

CREATE TABLE proposal_user_views
(
    id          SERIAL,
    proposal_id INT      NOT NULL,
    user_id     INT      NOT NULL,
    viewed_at   TIMESTAMPTZ NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT unique_proposal_user UNIQUE (proposal_id, user_id),
    FOREIGN KEY (proposal_id) REFERENCES proposals (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

/* =======================================================
   СОЗДАНИЕ ИНДЕКСОВ
   =======================================================
   
   • idx_proposals_history_proposal_id_event_type - для быстрого поиска в истории заявок
   
   ======================================================= */

CREATE INDEX idx_proposals_history_proposal_id_event_type 
ON proposals_history (proposal_id, event_type);

/* =======================================================
   ЗАПОЛНЕНИЕ СПРАВОЧНЫХ ДАННЫХ
   =======================================================
   
   • categories - 4 базовые категории (category_1-4)
   • permissions - 68 разрешений системы (продукты, администрирование, сервисы)
   • users - 2 тестовых пользователя (default, test)
   • products - 1 продукт по умолчанию
   • roles - 4 системные роли (account_user, account_admin, product_owner, product_participant)
   
   ======================================================= */

INSERT INTO categories (name)
VALUES ('category_1'),
       ('category_2'),
       ('category_3'),
       ('category_4');

INSERT INTO permissions (name, method)
VALUES
    -- product
    ('product', 'view'),
    ('product', 'create'),
    -- product_participant
    ('product_participant', 'view'),
    ('product_participant', 'create'),
    ('product_participant', 'delete'),
    -- product_participant_role
    ('product_participant_role', 'view'),
    ('product_participant_role', 'update'),
    -- product_participant_group
    ('product_participant_group', 'view'),
    ('product_participant_group', 'update'),
    -- product_description
    ('product_description', 'view'),
    ('product_description', 'create'),
    ('product_description', 'update'),
    ('product_description', 'delete'),
    -- product_proposal
    ('product_proposal', 'view'),
    ('product_proposal', 'create'),
    ('product_proposal', 'update'),
    ('product_proposal', 'delete'),
    -- product_proposal_send_confirm
    ('product_proposal_send_confirm', 'view'),
    -- product_proposal_confirm
    ('product_proposal_confirm', 'view'),
    -- product_proposal_comment
    ('product_proposal_comment', 'view'),
    ('product_proposal_comment', 'create'),
    -- product_stand_C
    ('product_stand_C', 'view'),
    ('product_stand_C', 'update'),
    -- product_stand_C1
    ('product_stand_C1', 'view'),
    ('product_stand_C1', 'update'),
    -- product_stand_C2
    ('product_stand_C2', 'view'),
    ('product_stand_C2', 'update'),
    -- product_stand_C3
    ('product_stand_C3', 'view'),
    ('product_stand_C3', 'update'),
    -- product_role
    ('product_role', 'view'),
    ('product_role', 'create'),
    ('product_role', 'update'),
    ('product_role', 'delete'),
    -- product_role_page
    ('product_role_page', 'view'),
    -- product_group
    ('product_group', 'view'),
    ('product_group', 'create'),
    ('product_group', 'update'),
    ('product_group', 'delete'),
    -- product_group_page
    ('product_group_page', 'view'),
    -- product_archive
    ('product_archive', 'view'),
    ('product_archive', 'delete'),
    -- product_notification
    ('product_notification', 'view'),
    -- documentation
    ('documentation', 'view'),
    -- billing
    ('billing', 'view'),
    -- calculator
    ('calculator', 'view'),
    -- profile
    ('profile', 'view'),
    ('profile', 'update'),
    -- admin_product
    ('admin_product', 'view'),
    -- admin_user
    ('admin_user', 'view'),
    ('admin_user', 'update'),
    -- admin_product_attribute
    ('admin_product_attribute', 'view'),
    ('admin_product_attribute', 'create'),
    ('admin_product_attribute', 'update'),
    ('admin_product_attribute', 'delete'),
    -- admin_permission
    ('admin_permission', 'view'),
    ('admin_permission', 'create'),
    ('admin_permission', 'update'),
    ('admin_permission', 'delete'),
    -- admin_role_show
    ('admin_role_show', 'view'),
    ('admin_role_show', 'update'),
    -- admin_auto_category
    ('admin_auto_category', 'view'),
    ('admin_auto_category', 'create'),
    ('admin_auto_category', 'update'),
    ('admin_auto_category', 'delete'),
    -- admin_group_show
    ('admin_group_show', 'view'),
    ('admin_group_show', 'update'),
    -- admin_inventory
    ('admin_inventory', 'view'),
    ('admin_inventory', 'update'),
    -- admin_category
    ('admin_category', 'view'),
    ('admin_category', 'create'),
    ('admin_category', 'update'),
    ('admin_category', 'delete'),
    -- admin_system_role
    ('admin_system_role', 'view'),
    ('admin_system_role', 'create'),
    ('admin_system_role', 'update'),
    ('admin_system_role', 'delete'),
    -- admin_system_group
    ('admin_system_group', 'view'),
    ('admin_system_group', 'create'),
    ('admin_system_group', 'update'),
    ('admin_system_group', 'delete'),
    -- service_status
    ('service_status', 'view'),
    -- product_proposal_history
    ('product_proposal_history', 'view');

INSERT INTO users (category_id, email, full_name, position, is_admin)
VALUES (1, '<EMAIL>', 'Default User', 'default', false);

INSERT INTO users (category_id, email, full_name, position, is_admin, created_at)
VALUES (1, '<EMAIL>', 'Test User', 'test', false, now());

INSERT INTO products (iid, tech_name, name, description, creator_id)
VALUES ('0001', 'default', 'Default Product', 'Default Product', 1);

INSERT INTO roles (name, product_id, is_system)
VALUES ('account_user', 1, true),
       ('account_admin', 1, true);

INSERT INTO roles (name, is_system, is_protected)
VALUES ('product_owner', true, true),
       ('product_participant', true, true);

/* =======================================================
   НАСТРОЙКА РАЗРЕШЕНИЙ
   =======================================================
   
   • roles_permissions - разрешения для account_user (3 разрешения)
   • categories_permissions - разрешения для category_1 (3 разрешения)
   • roles_permissions - разрешения для product_owner (20+ разрешений)
   • roles_permissions - разрешения для product_participant (12+ разрешений)
   
   ======================================================= */

INSERT INTO roles_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
         JOIN permissions p ON (
    (p.name = 'profile' AND p.method = 'view') OR
    (p.name = 'product' AND p.method = 'create') OR
    (p.name = 'documentation' AND p.method = 'view')
    )
WHERE r.name = 'account_user';

INSERT INTO categories_permissions (category_id, permission_id)
SELECT c.id, p.id
FROM categories c
         JOIN permissions p ON (
    (p.name = 'product' AND p.method IN ('create', 'view')) OR
    (p.name = 'documentation' AND p.method = 'view')
    )
WHERE c.name = 'category_1';

INSERT INTO roles_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
         JOIN permissions p ON (
    (p.name = 'product' AND p.method IN ('view', 'create')) OR
    (p.name = 'product_description') OR
    (p.name = 'product_participant' AND p.method IN ('view', 'create', 'delete')) OR
    (p.name = 'product_participant_role' AND p.method IN ('view', 'update')) OR
    (p.name = 'product_participant_group' AND p.method IN ('view', 'update')) OR
    (p.name = 'product_role' AND p.method IN ('view', 'create', 'update', 'delete')) OR
    (p.name = 'product_group' AND p.method IN ('view', 'create', 'update', 'delete')) OR
    (p.name = 'product_proposal') OR
    (p.name = 'product_proposal_send_confirm' AND p.method = 'view') OR
    (p.name = 'product_proposal_history' AND p.method = 'view')
    )
WHERE r.name = 'product_owner';

INSERT INTO roles_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
         JOIN permissions p ON (
    (p.name = 'product' AND p.method IN ('view', 'create')) OR
    (p.name = 'product_description' AND p.method = 'view') OR
    (p.name = 'product_participant' AND p.method = 'view') OR
    (p.name = 'product_proposal') OR
    (p.name = 'product_proposal_send_confirm' AND p.method = 'view') OR
    (p.name = 'product_proposal_history' AND p.method = 'view')
    )
WHERE r.name = 'product_participant';

/* =======================================================
   СОЗДАНИЕ ФУНКЦИЙ
   =======================================================
   
   • add_default_user_role() - автоматическое назначение роли account_user
   • update_updated_at_column() - обновление поля updated_at
   • handle_product_deactivation() - обработка деактивации продуктов
   • update_product_updated_at() - синхронизация updated_at продуктов
   • check_product_owners() - проверка количества владельцев (1-2) 
   • check_participant_deletion() - проверка удаления единственных владельцев
   • assign_product_participant_role() - автоназначение роли product_participant
   • prevent_product_participant_role_deletion() - защита от удаления роли product_participant
   • check_system_role_name_uniqueness() - уникальность системных ролей
   • check_system_group_name_uniqueness() - уникальность системных групп
   
   ======================================================= */

/* ---------------------------------------------------
    Функции управления пользователями
 -------------------------------------------------- */

-- Функция автоматического добавления роли 'account_user' при создании пользователя
CREATE OR REPLACE FUNCTION add_default_user_role()
    RETURNS trigger AS
$$
DECLARE
    default_role_id INT;
BEGIN
    SELECT id
    INTO default_role_id
    FROM roles
    WHERE name = 'account_user'
      AND active_flg = true
    LIMIT 1;

    IF default_role_id IS NOT NULL THEN
        INSERT INTO users_roles (user_id, role_id)
        VALUES (NEW.id, default_role_id);
    ELSE
        RAISE EXCEPTION 'Role "account_user" not found in roles table.';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Функция обновления поля updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS trigger AS
$$
BEGIN
    NEW.updated_at := (NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Функция обновления last_active_product_id при деактивации продукта
CREATE OR REPLACE FUNCTION handle_product_deactivation()
    RETURNS trigger AS
$$
BEGIN
    IF NEW.active_flg = false AND OLD.active_flg = true THEN
        UPDATE users
        SET last_active_product_id = 0
        WHERE last_active_product_id = NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

/* ---------------------------------------------------
    Функции синхронизации продуктов
 -------------------------------------------------- */

-- Функция обновления updated_at продукта при изменениях в связанных таблицах
CREATE OR REPLACE FUNCTION update_product_updated_at()
    RETURNS trigger AS
$$
DECLARE
    product_id_val INT;
    role_id_val    INT;
BEGIN
    -- Определяем product_id в зависимости от таблицы
    IF TG_TABLE_NAME = 'participants' THEN
        product_id_val := NEW.product_id;
    ELSIF TG_TABLE_NAME = 'participants_roles' THEN
        SELECT p.product_id
        INTO product_id_val
        FROM participants p
        WHERE p.id = NEW.participant_id;
    ELSIF TG_TABLE_NAME = 'participants_groups' THEN
        SELECT p.product_id
        INTO product_id_val
        FROM participants p
        WHERE p.id = NEW.participant_id;
    ELSIF TG_TABLE_NAME = 'sections' THEN
        product_id_val := NEW.product_id;
    ELSIF TG_TABLE_NAME = 'proposals' THEN
        product_id_val := NEW.product_id;
    ELSIF TG_TABLE_NAME = 'groups_roles' THEN
        -- 1. Группа принадлежит продукту напрямую
        SELECT g.product_id
        INTO product_id_val
        FROM groups g
        WHERE g.id = NEW.group_id
          AND g.product_id IS NOT NULL;

        -- 2. Группа принадлежит участнику, который принадлежит продукту
        IF product_id_val IS NULL THEN
            SELECT p.product_id
            INTO product_id_val
            FROM participants p
                     JOIN participants_groups pg ON pg.participant_id = p.id
            WHERE pg.group_id = NEW.group_id
            LIMIT 1;
        END IF;
    ELSIF TG_TABLE_NAME = 'roles_permissions' THEN
        role_id_val := NEW.role_id;

        -- 1. Роль принадлежит продукту напрямую
        SELECT r.product_id
        INTO product_id_val
        FROM roles r
        WHERE r.id = role_id_val
          AND r.product_id IS NOT NULL;

        -- 2. Роль принадлежит участнику продукта
        IF product_id_val IS NULL THEN
            SELECT p.product_id
            INTO product_id_val
            FROM participants p
                     JOIN participants_roles pr ON pr.participant_id = p.id
            WHERE pr.role_id = role_id_val
            LIMIT 1;
        END IF;

        -- 3. Роль принадлежит группе продукта
        IF product_id_val IS NULL THEN
            SELECT g.product_id
            INTO product_id_val
            FROM groups g
                     JOIN groups_roles gr ON gr.group_id = g.id
            WHERE gr.role_id = role_id_val
              AND g.product_id IS NOT NULL
            LIMIT 1;
        END IF;

        -- 4. Роль принадлежит группе участника продукта
        IF product_id_val IS NULL THEN
            SELECT p.product_id
            INTO product_id_val
            FROM participants p
                     JOIN participants_groups pg ON pg.participant_id = p.id
                     JOIN groups_roles gr ON gr.group_id = pg.group_id
            WHERE gr.role_id = role_id_val
            LIMIT 1;
        END IF;
    END IF;

    -- Обработка удаления записи
    IF TG_OP = 'DELETE' THEN
        IF TG_TABLE_NAME = 'participants' THEN
            product_id_val := OLD.product_id;
        ELSIF TG_TABLE_NAME = 'participants_roles' THEN
            SELECT p.product_id
            INTO product_id_val
            FROM participants p
            WHERE p.id = OLD.participant_id;
        ELSIF TG_TABLE_NAME = 'participants_groups' THEN
            SELECT p.product_id
            INTO product_id_val
            FROM participants p
            WHERE p.id = OLD.participant_id;
        ELSIF TG_TABLE_NAME = 'sections' THEN
            product_id_val := OLD.product_id;
        ELSIF TG_TABLE_NAME = 'proposals' THEN
            product_id_val := OLD.product_id;
        ELSIF TG_TABLE_NAME = 'groups_roles' THEN
            SELECT g.product_id
            INTO product_id_val
            FROM groups g
            WHERE g.id = OLD.group_id
              AND g.product_id IS NOT NULL;

            IF product_id_val IS NULL THEN
                SELECT p.product_id
                INTO product_id_val
                FROM participants p
                         JOIN participants_groups pg ON pg.participant_id = p.id
                WHERE pg.group_id = OLD.group_id
                LIMIT 1;
            END IF;
        ELSIF TG_TABLE_NAME = 'roles_permissions' THEN
            role_id_val := OLD.role_id;

            SELECT r.product_id
            INTO product_id_val
            FROM roles r
            WHERE r.id = role_id_val
              AND r.product_id IS NOT NULL;

            IF product_id_val IS NULL THEN
                SELECT p.product_id
                INTO product_id_val
                FROM participants p
                         JOIN participants_roles pr ON pr.participant_id = p.id
                WHERE pr.role_id = role_id_val
                LIMIT 1;
            END IF;

            IF product_id_val IS NULL THEN
                SELECT g.product_id
                INTO product_id_val
                FROM groups g
                         JOIN groups_roles gr ON gr.group_id = g.id
                WHERE gr.role_id = role_id_val
                  AND g.product_id IS NOT NULL
                LIMIT 1;
            END IF;

            IF product_id_val IS NULL THEN
                SELECT p.product_id
                INTO product_id_val
                FROM participants p
                         JOIN participants_groups pg ON pg.participant_id = p.id
                         JOIN groups_roles gr ON gr.group_id = pg.group_id
                WHERE gr.role_id = role_id_val
                LIMIT 1;
            END IF;
        END IF;
    END IF;

    -- Обновляем updated_at в products, если найден product_id
    IF product_id_val IS NOT NULL THEN
        UPDATE products
        SET updated_at = NOW()
        WHERE id = product_id_val;
    END IF;

    -- Возвращаем соответствующее значение
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

/* ---------------------------------------------------
    Функции проверки владельцев продуктов
 -------------------------------------------------- */

-- Функция проверки количества владельцев продукта (1-2) с поддержкой CASCADE удалений
CREATE OR REPLACE FUNCTION check_product_owners()
    RETURNS trigger AS
$$
DECLARE
    owner_role_id INT;
    final_owner_count INT;
    owner_count_min INT := 1;
    owner_count_max INT := 2;
    current_product_id INT;
    participants_roles_table TEXT := 'participants_roles';
    participants_groups_table TEXT := 'participants_groups';
    owner_role_name TEXT := 'product_owner';
    participant_id_affected INT;
    is_relevant_change BOOLEAN := FALSE;
    is_cascade_delete BOOLEAN := FALSE;
BEGIN
    -- Получаем ID роли 'product_owner'
    SELECT id INTO owner_role_id
    FROM roles
    WHERE name = owner_role_name AND is_system = true AND is_protected = true
    LIMIT 1;

    -- Определяем затронутого участника и product_id
    IF TG_OP = 'INSERT' THEN
        participant_id_affected := NEW.participant_id;
        IF TG_TABLE_NAME = participants_roles_table THEN
            SELECT p.product_id INTO current_product_id FROM participants p WHERE p.id = NEW.participant_id;
        ELSIF TG_TABLE_NAME = participants_groups_table THEN
             SELECT p.product_id INTO current_product_id FROM participants p WHERE p.id = NEW.participant_id;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        participant_id_affected := OLD.participant_id;
        
        -- Пытаемся получить product_id из таблицы participants
        SELECT p.product_id INTO current_product_id FROM participants p WHERE p.id = OLD.participant_id;
        
        -- Если current_product_id равен NULL, это CASCADE удаление из таблицы participants
        IF current_product_id IS NULL THEN
            is_cascade_delete := TRUE;
            RAISE NOTICE '[CASCADE DELETE] Participant % being deleted from participants table, skipping roles check (handled by participants trigger)', OLD.participant_id;
            RETURN OLD;
        END IF;
    END IF;

    -- Проверяем, влияет ли изменение на роль владельца
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = participants_roles_table THEN
            IF NEW.role_id = owner_role_id THEN
                is_relevant_change := TRUE;
            END IF;
        ELSIF TG_TABLE_NAME = participants_groups_table THEN
            IF EXISTS (SELECT 1 FROM groups_roles gr WHERE gr.group_id = NEW.group_id AND gr.role_id = owner_role_id) THEN
                is_relevant_change := TRUE;
            END IF;
        END IF;
        
        IF NOT is_relevant_change THEN
            RAISE NOTICE '[EARLY EXIT INSERT] Change in table % for participant % does not affect owner role. Skipping count check.', TG_TABLE_NAME, participant_id_affected;
            RETURN NEW;
        END IF;

    ELSIF TG_OP = 'DELETE' THEN
        IF TG_TABLE_NAME = participants_roles_table THEN
            IF OLD.role_id = owner_role_id THEN
                is_relevant_change := TRUE;
            END IF;
        ELSIF TG_TABLE_NAME = participants_groups_table THEN
             IF EXISTS (SELECT 1 FROM groups_roles gr WHERE gr.group_id = OLD.group_id AND gr.role_id = owner_role_id) THEN
                is_relevant_change := TRUE;
            END IF;
        END IF;
        
        IF NOT is_relevant_change THEN
             RAISE NOTICE '[EARLY EXIT DELETE] Change in table % for participant % does not affect owner role. Skipping count check.', TG_TABLE_NAME, participant_id_affected;
            RETURN OLD;
        END IF;
    END IF;

    -- Если мы дошли до этого места, изменение ВАЖНО, продолжаем с вычислением и проверками
    RAISE NOTICE '[RELEVANT CHANGE] Operation: %, Table: %, Product ID: %, Participant: % affects owner role. Proceeding with count check.', TG_OP, TG_TABLE_NAME, current_product_id, participant_id_affected;

    -- Вычисляем количество владельцев и проверяем ограничения
    DECLARE
        owner_count_before INT;
    BEGIN
        -- Вычисляем текущее количество владельцев для продукта
        SELECT COUNT(DISTINCT p.id) INTO owner_count_before
        FROM participants p
        WHERE p.product_id = current_product_id
          AND (
              EXISTS (
                  SELECT 1 FROM participants_roles pr
                  WHERE pr.participant_id = p.id AND pr.role_id = owner_role_id
              )
              OR EXISTS (
                  SELECT 1 FROM participants_groups pg
                  JOIN groups_roles gr ON pg.group_id = gr.group_id
                  WHERE pg.participant_id = p.id AND gr.role_id = owner_role_id
              )
          );

        RAISE NOTICE '[COUNT BEFORE] Operation: %, Table: %, Product ID: %, Participant: %, Owner Count Before: %', TG_OP, TG_TABLE_NAME, current_product_id, participant_id_affected, owner_count_before;

        -- Вычисляем финальное количество после операции
        IF TG_OP = 'DELETE' THEN
            -- Проверяем, останется ли участник владельцем после удаления этой роли
            DECLARE
                remains_owner_after_delete BOOLEAN := FALSE;
            BEGIN
                -- Проверяем, остается ли участник владельцем через другие средства
                IF EXISTS (
                    SELECT 1 FROM participants_roles pr 
                    WHERE pr.participant_id = OLD.participant_id 
                    AND pr.role_id = owner_role_id 
                    AND pr.id != OLD.id
                ) OR EXISTS (
                    SELECT 1 FROM participants_groups pg
                    JOIN groups_roles gr ON pg.group_id = gr.group_id
                    WHERE pg.participant_id = OLD.participant_id 
                    AND gr.role_id = owner_role_id
                    AND (TG_TABLE_NAME != participants_groups_table OR pg.id != OLD.id)
                ) THEN
                    remains_owner_after_delete := TRUE;
                END IF;

                IF NOT remains_owner_after_delete THEN
                    final_owner_count := owner_count_before - 1;
                    RAISE NOTICE '[DELETE ADJUST] Participant % loses owner status. Final Count: %', participant_id_affected, final_owner_count;
                ELSE
                    final_owner_count := owner_count_before;
                    RAISE NOTICE '[DELETE ADJUST] Participant % remains owner through other means. Final Count: %', participant_id_affected, final_owner_count;
                END IF;
            END;
            
            -- Проверяем минимальное ограничение
            IF final_owner_count < owner_count_min THEN
                RAISE EXCEPTION 'Продукт должен иметь как минимум одного владельца. После удаления роли будет владельцев: %', final_owner_count;
            END IF;
            
        ELSIF TG_OP = 'INSERT' THEN
            -- Проверяем, является ли участник уже владельцем
            DECLARE
                is_already_owner BOOLEAN := FALSE;
            BEGIN
                IF EXISTS (
                    SELECT 1 FROM participants_roles pr
                    WHERE pr.participant_id = NEW.participant_id 
                    AND pr.role_id = owner_role_id
                ) OR EXISTS (
                    SELECT 1 FROM participants_groups pg
                    JOIN groups_roles gr ON pg.group_id = gr.group_id
                    WHERE pg.participant_id = NEW.participant_id 
                    AND gr.role_id = owner_role_id
                ) THEN
                    is_already_owner := TRUE;
                END IF;

                IF NOT is_already_owner THEN
                    final_owner_count := owner_count_before + 1;
                    RAISE NOTICE '[INSERT ADJUST] Participant % gains owner status. Final Count: %', participant_id_affected, final_owner_count;
                ELSE
                    final_owner_count := owner_count_before;
                    RAISE NOTICE '[INSERT ADJUST] Participant % already owner. Final Count: %', participant_id_affected, final_owner_count;
                END IF;
            END;
            
            -- Проверяем максимальное ограничение
            IF final_owner_count > owner_count_max THEN
                RAISE EXCEPTION 'Продукт не может иметь более двух владельцев. После добавления будет владельцев: %', final_owner_count;
            END IF;
        END IF;

        RAISE NOTICE '[FINAL CHECK] Operation: %, Product ID: %, Final Owner Count: %', TG_OP, current_product_id, final_owner_count;
    END;

    -- Возвращаем соответствующее значение
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Функция проверки удаления участников и предотвращения удаления единственных владельцев,
-- но разрешающая cascade удаление при удалении продукта
CREATE OR REPLACE FUNCTION check_participant_deletion()
    RETURNS trigger AS
$$
DECLARE
    owner_role_id INT;
    owners_count INT;
    participant_is_owner BOOLEAN := FALSE;
    product_exists BOOLEAN := TRUE;
BEGIN
    -- Проверяем, существует ли ещё продукт (если нет, это cascade удаление)
    SELECT EXISTS(SELECT 1 FROM products WHERE id = OLD.product_id) INTO product_exists;
    
    -- Если продукт не существует, это cascade удаление при удалении продукта.
    -- Разрешаем удаление без ограничений владельцев
    IF NOT product_exists THEN
        RAISE NOTICE '[CASCADE DELETE] Product % being deleted, allowing participant % deletion', OLD.product_id, OLD.id;
        RETURN OLD;
    END IF;
    
    -- Получаем ID роли 'product_owner'
    SELECT id INTO owner_role_id
    FROM roles
    WHERE name = 'product_owner' AND is_system = true AND is_protected = true
    LIMIT 1;
    
    -- Проверяем, является ли удаляемый участник владельцем
    SELECT EXISTS(
        SELECT 1 FROM participants_roles pr 
        WHERE pr.participant_id = OLD.id AND pr.role_id = owner_role_id
    ) OR EXISTS(
        SELECT 1 FROM participants_groups pg
        JOIN groups_roles gr ON pg.group_id = gr.group_id
        WHERE pg.participant_id = OLD.id AND gr.role_id = owner_role_id
    ) INTO participant_is_owner;
    
    -- Если участник не является владельцем, разрешаем удаление
    IF NOT participant_is_owner THEN
        RAISE NOTICE '[PARTICIPANT DELETE] Participant % is not an owner, deletion allowed', OLD.id;
        RETURN OLD;
    END IF;
    
    -- Считаем общее количество владельцев для этого продукта (исключая удаляемого)
    SELECT COUNT(DISTINCT p.id) INTO owners_count
    FROM participants p
    WHERE p.product_id = OLD.product_id
    AND p.id != OLD.id  -- Исключаем участника, который удаляется
    AND (
        EXISTS (
            SELECT 1 FROM participants_roles pr
            WHERE pr.participant_id = p.id AND pr.role_id = owner_role_id
        )
        OR EXISTS (
            SELECT 1 FROM participants_groups pg
            JOIN groups_roles gr ON pg.group_id = gr.group_id
            WHERE pg.participant_id = p.id AND gr.role_id = owner_role_id
        )
    );
    
    RAISE NOTICE '[PARTICIPANT DELETE] Product %, deleting owner %, remaining owners: %', OLD.product_id, OLD.id, owners_count;
    
    -- Если после удаления не останется владельцев, предотвращаем это
    IF owners_count < 1 THEN
        RAISE EXCEPTION 'Нельзя удалить участника: он является единственным владельцем продукта. После удаления владельцев останется: %', owners_count;
    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

/* ---------------------------------------------------
    Функции управления участниками
 -------------------------------------------------- */

-- Функция автоматического назначения роли product_participant
CREATE OR REPLACE FUNCTION assign_product_participant_role()
    RETURNS trigger AS
$$
DECLARE
    participant_role_id INT;
    new_participant_id INT;
BEGIN
    new_participant_id := NEW.id;
    
    SELECT id
    INTO participant_role_id
    FROM roles
    WHERE name = 'product_participant'
      AND is_system = true
      AND active_flg = true
    LIMIT 1;

    IF participant_role_id IS NOT NULL THEN
        INSERT INTO participants_roles (participant_id, role_id)
        VALUES (new_participant_id, participant_role_id)
        ON CONFLICT (participant_id, role_id) DO NOTHING;
    ELSE
        RAISE WARNING 'Role "product_participant" not found or inactive in roles table.';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Функция предотвращения удаления роли product_participant
CREATE OR REPLACE FUNCTION prevent_product_participant_role_deletion()
    RETURNS trigger AS
$$
DECLARE
    participant_role_id INT;
    old_participant_id INT;
    participant_exists BOOLEAN;
BEGIN
    old_participant_id := OLD.participant_id;
    
    SELECT id
    INTO participant_role_id
    FROM roles
    WHERE name = 'product_participant'
      AND is_system = true
      AND active_flg = true
    LIMIT 1;

    IF participant_role_id IS NOT NULL AND OLD.role_id = participant_role_id THEN
        SELECT EXISTS(SELECT 1 FROM participants WHERE id = old_participant_id) INTO participant_exists;
        
        IF participant_exists THEN
            RAISE EXCEPTION 'Cannot delete product_participant role directly. This role can only be removed when the participant is deleted (CASCADE).';
        END IF;
    END IF;

    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

/* ---------------------------------------------------
    Функции проверки уникальности имен
 -------------------------------------------------- */

-- Функция проверки уникальности имен для ролей
CREATE OR REPLACE FUNCTION check_system_role_name_uniqueness()
    RETURNS trigger AS
$$
DECLARE
    existing_system_role_count INT;
    new_role_name TEXT;
    new_role_id INT;
BEGIN
    new_role_name := NEW.name;
    new_role_id := NEW.id;
    
    SELECT COUNT(*)
    INTO existing_system_role_count
    FROM roles r
    WHERE r.name = new_role_name
      AND r.is_system = true
      AND r.id != COALESCE(new_role_id, -1);
    
    IF existing_system_role_count > 0 THEN
        RAISE EXCEPTION 'Cannot create/update role with name "%" because a system role with this name already exists.', new_role_name;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Функция проверки уникальности имен для групп
CREATE OR REPLACE FUNCTION check_system_group_name_uniqueness()
    RETURNS trigger AS
$$
DECLARE
    existing_system_group_count INT;
    new_group_name TEXT;
    new_group_id INT;
BEGIN
    new_group_name := NEW.name;
    new_group_id := NEW.id;
    
    SELECT COUNT(*)
    INTO existing_system_group_count
    FROM groups g
    WHERE g.name = new_group_name
      AND g.is_system = true
      AND g.id != COALESCE(new_group_id, -1);
    
    IF existing_system_group_count > 0 THEN
        RAISE EXCEPTION 'Cannot create/update group with name "%" because a system group with this name already exists.', new_group_name;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

/* =======================================================
   СОЗДАНИЕ ТРИГГЕРОВ
   =======================================================
   
   • trg_add_default_role - автоматическое назначение account_user при INSERT users
   • trg_update_*_updated_at - обновление updated_at (6 триггеров)
   • trg_handle_product_deactivation - обработка деактивации продуктов
   • trg_update_product_from_* - синхронизация updated_at продуктов (7 триггеров)
   • trg_check_product_owners_* - проверка количества владельцев (2 триггера)
   • trg_check_participant_deletion - проверка удаления единственных владельцев
   • trg_assign_product_participant_role - автоназначение product_participant
   • trg_prevent_product_participant_role_deletion - защита product_participant
   • trg_check_system_*_name_uniqueness - уникальность системных имен (2 триггера)
   
   Итого: 18 триггеров
   
   ======================================================= */

/* ---------------------------------------------------
    Триггеры для пользователей
 -------------------------------------------------- */

-- Триггер добавления роли по умолчанию
CREATE TRIGGER trg_add_default_role
    AFTER INSERT
    ON users
    FOR EACH ROW
EXECUTE FUNCTION add_default_user_role();

-- Триггеры обновления updated_at
CREATE TRIGGER trg_update_users_updated_at
    BEFORE UPDATE
    ON users
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_update_products_updated_at
    BEFORE UPDATE
    ON products
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_update_groups_updated_at
    BEFORE UPDATE
    ON groups
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_update_roles_updated_at
    BEFORE UPDATE
    ON roles
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_update_participants_updated_at
    BEFORE UPDATE
    ON participants
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_update_proposals_updated_at
    BEFORE UPDATE
    ON proposals
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Триггер обработки деактивации продуктов
CREATE TRIGGER trg_handle_product_deactivation
    AFTER UPDATE
    ON products
    FOR EACH ROW
EXECUTE FUNCTION handle_product_deactivation();

/* ---------------------------------------------------
    Триггеры синхронизации продуктов
 -------------------------------------------------- */

-- Триггеры обновления updated_at продуктов при изменении связанных данных
CREATE TRIGGER trg_update_product_from_participants
    AFTER INSERT OR UPDATE OR DELETE
    ON participants
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

CREATE TRIGGER trg_update_product_from_participants_roles
    AFTER INSERT OR UPDATE OR DELETE
    ON participants_roles
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

CREATE TRIGGER trg_update_product_from_participants_groups
    AFTER INSERT OR UPDATE OR DELETE
    ON participants_groups
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

CREATE TRIGGER trg_update_product_from_groups_roles
    AFTER INSERT OR UPDATE OR DELETE
    ON groups_roles
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

CREATE TRIGGER trg_update_product_from_roles_permissions
    AFTER INSERT OR UPDATE OR DELETE
    ON roles_permissions
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

CREATE TRIGGER trg_update_product_from_sections
    AFTER INSERT OR UPDATE OR DELETE
    ON sections
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

CREATE TRIGGER trg_update_product_from_proposals
    AFTER INSERT OR UPDATE OR DELETE
    ON proposals
    FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

/* ---------------------------------------------------
    Триггеры проверки владельцев продуктов
 -------------------------------------------------- */

-- Триггеры проверки количества владельцев продукта
CREATE TRIGGER trg_check_product_owners_roles
    BEFORE INSERT OR DELETE ON participants_roles
    FOR EACH ROW
    EXECUTE FUNCTION check_product_owners();

CREATE TRIGGER trg_check_product_owners_groups
    BEFORE INSERT OR DELETE ON participants_groups
    FOR EACH ROW
    EXECUTE FUNCTION check_product_owners();

-- Триггер проверки удаления участников на предмет ограничений владельцев
CREATE TRIGGER trg_check_participant_deletion
    BEFORE DELETE ON participants
    FOR EACH ROW
    EXECUTE FUNCTION check_participant_deletion();

/* ---------------------------------------------------
    Триггеры управления ролями участников
 -------------------------------------------------- */

-- Триггер автоматического назначения роли product_participant
CREATE TRIGGER trg_assign_product_participant_role
    AFTER INSERT
    ON participants
    FOR EACH ROW
EXECUTE FUNCTION assign_product_participant_role();

-- Триггер предотвращения удаления роли product_participant
CREATE TRIGGER trg_prevent_product_participant_role_deletion
    BEFORE DELETE
    ON participants_roles
    FOR EACH ROW
EXECUTE FUNCTION prevent_product_participant_role_deletion();

/* ---------------------------------------------------
    Триггеры проверки уникальности имен
 -------------------------------------------------- */

-- Триггеры проверки уникальности системных имен
CREATE TRIGGER trg_check_system_role_name_uniqueness
    BEFORE INSERT OR UPDATE OF name
    ON roles
    FOR EACH ROW
EXECUTE FUNCTION check_system_role_name_uniqueness();

CREATE TRIGGER trg_check_system_group_name_uniqueness
    BEFORE INSERT OR UPDATE OF name
    ON groups
    FOR EACH ROW
EXECUTE FUNCTION check_system_group_name_uniqueness();

-- +goose StatementEnd


-- +goose Down
-- +goose StatementBegin

/* =======================================================
   УДАЛЕНИЕ ТРИГГЕРОВ
   =======================================================
   
   • trg_check_system_*_name_uniqueness (2 триггера)
   • trg_*_product_participant_role_* (2 триггера)
   • trg_check_*_owners (3 триггера)
   • trg_update_product_from_* (7 триггеров)
   • trg_update_*_updated_at + trg_handle_product_deactivation (7 триггеров)
   • trg_add_default_role (1 триггер)
   
   ======================================================= */

-- Триггеры проверки уникальности имен
DROP TRIGGER IF EXISTS trg_check_system_group_name_uniqueness ON groups;
DROP TRIGGER IF EXISTS trg_check_system_role_name_uniqueness ON roles;

-- Триггеры управления ролями участников
DROP TRIGGER IF EXISTS trg_prevent_product_participant_role_deletion ON participants_roles;
DROP TRIGGER IF EXISTS trg_assign_product_participant_role ON participants;

-- Триггеры проверки владельцев продуктов
DROP TRIGGER IF EXISTS trg_check_participant_deletion ON participants;
DROP TRIGGER IF EXISTS trg_check_product_owners_groups ON participants_groups;
DROP TRIGGER IF EXISTS trg_check_product_owners_roles ON participants_roles;

-- Триггеры синхронизации продуктов
DROP TRIGGER IF EXISTS trg_update_product_from_proposals ON proposals;
DROP TRIGGER IF EXISTS trg_update_product_from_sections ON sections;
DROP TRIGGER IF EXISTS trg_update_product_from_roles_permissions ON roles_permissions;
DROP TRIGGER IF EXISTS trg_update_product_from_groups_roles ON groups_roles;
DROP TRIGGER IF EXISTS trg_update_product_from_participants_groups ON participants_groups;
DROP TRIGGER IF EXISTS trg_update_product_from_participants_roles ON participants_roles;
DROP TRIGGER IF EXISTS trg_update_product_from_participants ON participants;

-- Триггеры обновления updated_at
DROP TRIGGER IF EXISTS trg_handle_product_deactivation ON products;
DROP TRIGGER IF EXISTS trg_update_proposals_updated_at ON proposals;
DROP TRIGGER IF EXISTS trg_update_participants_updated_at ON participants;
DROP TRIGGER IF EXISTS trg_update_roles_updated_at ON roles;
DROP TRIGGER IF EXISTS trg_update_groups_updated_at ON groups;
DROP TRIGGER IF EXISTS trg_update_products_updated_at ON products;
DROP TRIGGER IF EXISTS trg_update_users_updated_at ON users;

-- Триггер добавления роли по умолчанию
DROP TRIGGER IF EXISTS trg_add_default_role ON users;

/* =======================================================
   УДАЛЕНИЕ ФУНКЦИЙ
   =======================================================
   
   • check_system_*_name_uniqueness() (2 функции)
   • *_product_participant_role_*() (2 функции)
   • check_*_owners() + check_participant_deletion() (2 функции)
   • update_product_updated_at() (1 функция)
   • функции управления пользователями (3 функции)
   
   ======================================================= */

-- Функции проверки уникальности имен
DROP FUNCTION IF EXISTS check_system_group_name_uniqueness();
DROP FUNCTION IF EXISTS check_system_role_name_uniqueness();

-- Функции управления участниками
DROP FUNCTION IF EXISTS prevent_product_participant_role_deletion();
DROP FUNCTION IF EXISTS assign_product_participant_role();

-- Функции проверки владельцев продуктов
DROP FUNCTION IF EXISTS check_participant_deletion();
DROP FUNCTION IF EXISTS check_product_owners();

-- Функции синхронизации продуктов
DROP FUNCTION IF EXISTS update_product_updated_at();

-- Функции управления пользователями
DROP FUNCTION IF EXISTS handle_product_deactivation();
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS add_default_user_role();

/* =======================================================
   УДАЛЕНИЕ ИНДЕКСОВ
   =======================================================
   
   • idx_proposals_history_proposal_id_event_type
   
   ======================================================= */

DROP INDEX IF EXISTS idx_proposals_history_proposal_id_event_type;

/* =======================================================
   УДАЛЕНИЕ ТАБЛИЦ (в обратном порядке зависимостей)
   =======================================================
   
   • proposal_user_views, proposals_history - служебные данные заявок
   • proposals, sections - контент продуктов
   • roles_permissions, groups_roles - связь ролей с разрешениями
   • participants_*, participants - участники продуктов (3 таблицы)
   • users_*, users - пользователи и связи (3 таблицы)
   • categories_* - связи категорий (3 таблицы)
   • roles, groups, products - основные сущности
   • permissions, categories - справочные данные
   
   ======================================================= */

-- Таблицы просмотров и истории
DROP TABLE IF EXISTS proposal_user_views;
DROP TABLE IF EXISTS proposals_history;

-- Таблицы контента продуктов
DROP TABLE IF EXISTS proposals;
DROP TABLE IF EXISTS sections;

-- Связующие таблицы - роли и разрешения
DROP TABLE IF EXISTS roles_permissions;
DROP TABLE IF EXISTS groups_roles;

-- Связующие таблицы - участники продуктов
DROP TABLE IF EXISTS participants_groups;
DROP TABLE IF EXISTS participants_roles;
DROP TABLE IF EXISTS participants;

-- Связующие таблицы - пользователи
DROP TABLE IF EXISTS users_roles;
DROP TABLE IF EXISTS users_groups;

-- Связующие таблицы - категории
DROP TABLE IF EXISTS categories_roles;
DROP TABLE IF EXISTS categories_groups;
DROP TABLE IF EXISTS categories_permissions;

-- Основные сущности
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS groups;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS users;

-- Справочные таблицы
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS categories;

-- +goose StatementEnd
