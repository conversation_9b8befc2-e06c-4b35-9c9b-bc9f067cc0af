-- +goose Up
-- +goose StatementBegin

-- Добавляем колонку number, где будут хрнаиться номера заявок
ALTER TABLE proposals
ADD COLUMN number VARCHAR(255) UNIQUE;

-- Для уже существующих заявок создаем их номера
UPDATE proposals
SET number = 
    CASE
        WHEN type = 'OR' THEN 'OR'
        ELSE 'ER'
    END || '-' ||
    LPAD(product_id::text, 3, '0') || '-' ||
    TO_CHAR(created_at, 'MM') || '-' ||
    LPAD(seq_num::text, 4, '0');

-- Функция для генерации номера заявок
CREATE OR REPLACE FUNCTION generate_proposal_number()
RETURNS TRIGGER AS $$
BEGIN
    -- Генерируем номер только при INSERT или при изменении type в UPDATE
    IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.type IS DISTINCT FROM NEW.type) THEN
        NEW.number := 
            CASE WHEN NEW.type = 'OR' THEN 'OR' ELSE 'ER' END || '-' ||
            LPAD(COALESCE(NEW.product_id::text, '000'), 3, '0') || '-' ||
            COALESCE(TO_CHAR(NEW.created_at, 'MM'), '00') || '-' ||
            LPAD(COALESCE(NEW.seq_num::text, '0000'), 4, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- Триггер, чтобы заполнять поле number автоматически при вставке и обновлении заявки
CREATE TRIGGER proposals_number_trigger
BEFORE INSERT OR UPDATE ON proposals
FOR EACH ROW
EXECUTE FUNCTION generate_proposal_number();

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

-- Удаляем триггер и функцию
DROP TRIGGER IF EXISTS proposals_number_trigger ON proposals;
DROP FUNCTION IF EXISTS generate_proposal_number();

-- Удаляем колонку
ALTER TABLE proposals
DROP COLUMN number;

-- +goose StatementEnd
