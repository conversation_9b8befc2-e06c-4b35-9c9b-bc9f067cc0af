-- +goose Up
-- +goose StatementBegin

-- Index for filtering active roles in permission queries
-- Covers: roles.id + roles.active_flg filtering
CREATE INDEX idx_roles_id_active_flg
    ON roles (id, active_flg)
    WHERE active_flg = true;

-- Index for filtering active groups in permission queries  
-- Covers: groups.id + groups.active_flg filtering
CREATE INDEX idx_groups_id_active_flg
    ON groups (id, active_flg)
    WHERE active_flg = true;

-- Index for filtering active users in permission queries
-- Covers: users.id + users.active_flg filtering  
CREATE INDEX idx_users_id_active_flg
    ON users (id, active_flg)
    WHERE active_flg = true;

-- Index for foreign key lookups in users_roles
-- PostgreSQL doesn't create FK indexes automatically
CREATE INDEX idx_users_roles_role_id
    ON users_roles (role_id);

-- Index for foreign key lookups in users_groups
-- PostgreSQL doesn't create FK indexes automatically  
CREATE INDEX idx_users_groups_group_id
    ON users_groups (group_id);

-- Index for foreign key lookups in groups_roles
-- PostgreSQL doesn't create FK indexes automatically
CREATE INDEX idx_groups_roles_role_id
    ON groups_roles (role_id);

-- Index for foreign key lookups in roles_permissions
-- PostgreSQL doesn't create FK indexes automatically
CREATE INDEX idx_roles_permissions_permission_id
    ON roles_permissions (permission_id);

-- Index for foreign key lookups in categories_permissions
-- PostgreSQL doesn't create FK indexes automatically
CREATE INDEX idx_categories_permissions_permission_id
    ON categories_permissions (permission_id);

-- Index for users category lookup (already has FK, but good for performance)
CREATE INDEX idx_users_category_id
    ON users (category_id);

-- +goose StatementEnd

-- +goose Down  
-- +goose StatementBegin

DROP INDEX IF EXISTS idx_users_category_id;
DROP INDEX IF EXISTS idx_categories_permissions_permission_id;
DROP INDEX IF EXISTS idx_roles_permissions_permission_id;
DROP INDEX IF EXISTS idx_groups_roles_role_id;
DROP INDEX IF EXISTS idx_users_groups_group_id;
DROP INDEX IF EXISTS idx_users_roles_role_id;
DROP INDEX IF EXISTS idx_users_id_active_flg;
DROP INDEX IF EXISTS idx_groups_id_active_flg;
DROP INDEX IF EXISTS idx_roles_id_active_flg;

-- +goose StatementEnd
