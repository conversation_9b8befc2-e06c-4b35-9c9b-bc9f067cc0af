# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Go-based Project Management System (PMS) that follows Domain-Driven Design (DDD) architecture with Clean Architecture principles. The system manages projects, proposals, users, groups, permissions, and products with role-based access control and Keycloak integration.

## Architecture

### Domain-Driven Design Structure
- **Domain Layer**: Core business logic, entities, aggregates, and domain services
- **Application Layer**: Use cases, application services, and DTOs
- **Infrastructure Layer**: Database repositories, external services, and technical concerns
- **Presentation Layer**: HTTP handlers, middleware, and API controllers

### Key Components
- **Authentication**: Keycloak integration with JWT tokens
- **Authorization**: Role-based access control with categories and permissions
- **Database**: PostgreSQL with SQLC for type-safe SQL queries
- **Cache**: Redis for caching frequently accessed data
- **Event Streaming**: Server-sent events for real-time notifications
- **API**: RESTful API with OpenAPI specification

## Common Development Commands

### Build and Run
```bash
# Build application
go build ./cmd/pms.go

# Run application
go run ./cmd/pms.go

# Run with configuration file
go run ./cmd/pms.go -config ./configs/pms.conf
```

### Testing
```bash
# Run all working tests (only packages that pass)
make test-working

# Run tests with coverage
make test-coverage

# Generate HTML coverage report
make test-coverage-html

# Show coverage summary
make test-coverage-summary

# Run tests with verbose output
make test-verbose

# Run specific test package
go test ./internal/application/user/service

# Run single test
go test -run TestUserService_GetByID_Success ./internal/application/user/service
```

### Code Generation
```bash
# Generate all API clients from OpenAPI spec
make generate-all

# Generate SQLC code from SQL queries
make generate-sqlc

# Generate mocks for testing
make generate-mocks

# Generate mocks for specific domain
make generate-mocks-user
make generate-mocks-product
```

### Code Quality
```bash
# Run pre-push checks (format, vet, test)
./scripts/pre-push-check.sh

# Fast pre-push check (skip slow checks)
FAST_CHECK=1 ./scripts/pre-push-check.sh

# Minimal check (only critical: formatting, static analysis, build)
MINIMAL_CHECK=1 ./scripts/pre-push-check.sh

# SQLC validation
make sqlc-vet
```

## Architecture Patterns

### Service Factory Pattern
The application uses a service factory pattern in `internal/application/shared/service/factory.go` that creates all application services with proper dependency injection.

### Repository Pattern
- **Prime DB**: Main database operations in `internal/infrastructure/repositories/primedb/`
- **Cache**: Redis caching layer in `internal/infrastructure/repositories/cache/`
- **Composed Repositories**: Combine prime DB and cache operations

### Domain Services
Each domain has its own service that handles business logic:
- User management with admin/regular user distinction
- Product and participant management
- Role and permission management
- Proposal lifecycle management

### Application Services
Application services coordinate between domain services and handle cross-cutting concerns:
- Data transformation (mappers)
- Query processing
- Pagination and filtering
- Integration with external services

## Key Development Guidelines

### Testing Standards
- Follow naming convention: `TestServiceName_MethodName_Scenario`
- Use Arrange-Act-Assert pattern
- Mock all external dependencies using minimock/v3
- Test happy path, edge cases, and error scenarios
- Achieve full coverage for all public methods
- **Working Tests**: Only packages that consistently pass are included in `make test-working`
- **Database Testing**: Use pgxmock for database layer testing
- **Test Guidelines**: Detailed testing practices in `TESTING_GUIDELINES.md`

#### Modern Test Patterns
- **Use testify/require**: Always use `require.NoError(t, err)`, `require.Equal(t, expected, actual)` for assertions
- **Mock Creation Helper**: Create helper functions like `createMocks(t)` to reduce code duplication
- **No Deprecated APIs**: Avoid `minimock.NewController(t)` and `mc.Finish()` - use direct mock creation with `t` parameter
- **Edge Cases Testing**: Include tests for nil values, empty slices, boundary conditions
- **Error Scenario Coverage**: Test every error path from dependencies
- **Follow TESTING_GUIDELINES.md**: Comprehensive testing standards document with examples and anti-patterns

### Code Generation
- **API clients** are generated from OpenAPI specifications in `api/` using oapi-codegen
- **Database queries** use SQLC for type-safe SQL operations with automatic line ending normalization
- **Mocks** are generated using minimock/v3 for all domain interfaces
- **Generator configs** are in `api/*.cfg.yaml` files for each API module

### Database Operations
- All database queries are in `internal/infrastructure/repositories/primedb/queries/`
- SQLC generates type-safe Go code from SQL queries
- Migrations are in `migrations/` directory
- Use database transactions for complex operations

### Error Handling
- Use custom error kit (`internal/errkit/`) for consistent error responses
- Extract HTTP status codes from domain errors
- Log errors with structured logging using slog

### Configuration
- Configuration files in `configs/` directory
- Environment-specific settings for database, Redis, and Keycloak
- Use `configs/example.conf` as template

## File Structure Guidelines

### Domain Layer Structure
```
internal/domain/{entity}/
├── entity/          # Domain entities
├── aggregate/       # Domain aggregates
├── repository/      # Repository interfaces
├── service/         # Domain services
├── mocks/          # Generated mocks
└── valueobject/    # Value objects
```

### Application Layer Structure
```
internal/application/{entity}/
├── service/        # Application services
├── mapper/         # Data mappers
└── query/          # Query objects
```

### Infrastructure Layer Structure
```
internal/infrastructure/
├── repositories/    # Repository implementations
│   ├── primedb/    # PostgreSQL repositories
│   └── cache/      # Redis cache repositories
├── authentication/ # Auth middleware
├── authorization/  # Authorization logic
├── identityprovider/ # External identity providers
└── sse/           # Server-sent events management
```

## Important Notes

### Environment Variables
Required environment variables:
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_HOST`, `REDIS_PORT`: Redis configuration
- `KEYCLOAK_URL`: Keycloak server URL
- Various Keycloak credentials

### Service Dependencies
The main service factory creates all application services with proper dependency injection. Key dependencies:
- Database repositories (prime DB + cache)
- Domain services
- Event streaming client
- Identity provider services

### Event Streaming (SSE)
The system includes server-sent events for real-time notifications:
- **SSE Manager**: `internal/infrastructure/sse/` handles connection management
- **Event Endpoints**: `/v1/events/source` for SSE connections, `/v1/events/unread` for polling
- **Event Types**: ProposalStatusEvent, ProposalMessageEvent with proper typing
- **Authentication**: SSE connections require JWT authentication
- **Keep-alive**: Automatic ping messages every 10 seconds to maintain connections

### Docker Support
The application supports Docker deployment with provided Dockerfile and docker-compose configurations.

## Development Workflow

1. **Code Changes**: Make changes following DDD principles
2. **Generate Code**: Run `make generate-all` and `make generate-sqlc` if needed
3. **Test**: Run `make test-working` to ensure tests pass
4. **Quality Check**: Run `./scripts/pre-push-check.sh` before commits
5. **API Updates**: Update OpenAPI specification in `api/openapi.yaml` if needed

## Security Considerations

- All API endpoints require authentication via Keycloak JWT tokens
- Role-based access control with category-level permissions
- Input validation on all endpoints
- SQL injection prevention through SQLC type-safe queries
- Rate limiting and CORS configuration

## Important Instructions for Claude Code

### General Guidelines
- Always answer in Russian.
- Write comments in the code in English.
- Write comments in the bash-scripts in Russian.
- Stick to DDD and TDD principles.
- Build the project architecture so that it is easy to maintain, modify, scale and change the business logic.

### Development Practices
- NEVER create files unless they're absolutely necessary for achieving your goal.
- ALWAYS prefer editing an existing file to creating a new one.
- NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
- Do what has been asked; nothing more, nothing less.

### Code Style
- DO NOT ADD any comments unless asked.
- Follow existing code patterns and conventions.
- Use existing libraries and utilities found in the codebase.
- Always check if required dependencies are already available in go.mod.

#### Go Code Idioms
- **Empty Slice Declaration**: Use `var slice []Type` instead of `slice := []Type{}` for empty slices to avoid linter warnings
- **Nil vs Empty Slice**: Prefer `var slice []Type` for nil slices, use `make([]Type, 0)` or literal `[]Type{item1, item2}` when initializing with data