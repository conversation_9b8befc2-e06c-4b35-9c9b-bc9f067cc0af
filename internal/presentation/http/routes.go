package http

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/admingroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminpermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminuser"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/category"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/event"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/keycloak"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/permission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/status"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/user"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/sse"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/presentation/api"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/presentation/http/middleware"
)

func SetRoutes(cc *ctx.Ctx) *gin.Engine {

	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	router.Use(middleware.Logger(cc.Log))
	router.Use(middleware.RequestSizeLimiter(cc.API.ClientMaxBodySizeBytes))
	router.Use(middleware.RequestRateLimiter(cc))
	router.Use(middleware.RequestID())
	router.Use(middleware.ExecutionTimeLogger())

	router.Use(
		cors.New(
			cors.Config{
				AllowOrigins: cc.API.CORS.AllowOrigins,
				AllowMethods: []string{"GET", "POST", "DELETE", "PUT", "PATCH", "OPTIONS"},
				AllowHeaders: []string{"Origin", "Authorization", "Content-Type", "Accept-Encoding"},
				ExposeHeaders: []string{
					"Content-Length",
					"Access-Control-Allow-Origin",
					"Access-Control-Allow-Credentials",
					"Access-Control-Allow-Headers",
					"Access-Control-Allow-Methods",
				},
				AllowCredentials: true,
				MaxAge:           12 * time.Hour,
			},
		),
	)

	router.Static("/v1/api", "./api")
	router.GET("/v1/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler, ginSwagger.URL("/v1/api/openapi.yaml")))
	router.StaticFile("/test-sse", "./test-sse.html")

	router.Use(middleware.NewAuthMiddleware(cc))
	router.Use(middleware.HandleAuthorizer(cc))

	sseManager := sse.NewSSEManager()

	event.RegisterHandlers(router, api.NewEventHandler(cc, sseManager))

	participant.RegisterHandlers(router, api.NewParticipantHandler(cc))
	adminpermission.RegisterHandlers(router, api.NewAdminHandler(cc))
	adminrole.RegisterHandlers(router, api.NewAdminRoleHandler(cc))
	admingroup.RegisterHandlers(router, api.NewAdminGroupHandler(cc))
	adminproduct.RegisterHandlers(router, api.NewAdminProductHandler(cc))
	adminproposal.RegisterHandlers(router, api.NewAdminProposalHandler(cc))
	adminuser.RegisterHandlers(router, api.NewAdminUserHandler(cc))
	category.RegisterHandlers(router, api.NewCategoryHandler(cc))
	keycloak.RegisterHandlers(router, api.NewKeycloakHandler(cc))
	permission.RegisterHandlers(router, api.NewPermissionHandler(cc))
	proposal.RegisterHandlers(router, api.NewProposalHandler(cc, sseManager))
	status.RegisterHandlers(router, api.NewStatusHandler(cc))
	productrole.RegisterHandlers(router, api.NewRoleHandler(cc))
	product.RegisterHandlers(router, api.NewProductHandler(cc))
	productgroup.RegisterHandlers(router, api.NewGroupHandler(cc))
	systemgroup.RegisterHandlers(router, api.NewSystemGroupHandler(cc))
	user.RegisterHandlers(router, api.NewUserHandler(cc))
	systemrole.RegisterHandlers(router, api.NewSystemRoleHandler(cc))

	return router
}
