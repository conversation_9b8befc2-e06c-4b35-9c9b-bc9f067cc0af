package middleware

import (
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

type RoleBasedAuthorizer struct {
	log logger.Interface
}

func NewRoleBasedAuthorizer(log logger.Interface) *RoleBasedAuthorizer {
	return &RoleBasedAuthorizer{
		log: log,
	}
}

func (r *RoleBasedAuthorizer) hasDefaultRoleAccess(cc *ctx.Ctx, c *gin.Context, userID int64) bool {
	path := r.standardizePath(c.FullPath())
	method := c.Request.Method
	productID := r.extractProductID(c)

	userRoles, err := r.getUserDefaultRoles(cc, userID, productID)
	if err != nil {
		r.log.Errorf("error getting user default roles: %v", err)
		return false
	}

	for _, role := range userRoles {
		if r.checkRoleAccess(cc, role.Name, path, method) {
			return true
		}
	}

	return false
}

func (r *RoleBasedAuthorizer) extractProductID(c *gin.Context) int64 {
	productIDStr := c.Param("productID")
	if productIDStr == "" {
		return 0
	}

	productID, err := strconv.ParseInt(productIDStr, 10, 64)
	if err != nil {
		r.log.Errorf("error converting param productID to int64: %v", err)
		return 0
	}

	return productID
}

func (r *RoleBasedAuthorizer) getUserDefaultRoles(cc *ctx.Ctx, userID, productID int64) ([]roleentity.Role, error) {
	var defaultRoles []roleentity.Role

	// Получаем системные роли пользователя
	systemRoles, err := cc.AppService.Role.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	defaultRoleNames := []string{"account_admin", "product_owner", "product_participant", "account_user"}

	// Добавляем системные роли
	for _, role := range systemRoles {
		for _, defaultName := range defaultRoleNames {
			if role.Name == defaultName {
				defaultRoles = append(defaultRoles, role)
				break
			}
		}
	}

	// Если указан productID, также проверяем роли в продукте
	if productID != 0 {
		productRoles, err := cc.AppService.Role.GetAllWithProductByUserID(userID)
		if err != nil {
			return nil, err
		}

		// Фильтруем роли по конкретному продукту
		for _, roleWithProduct := range productRoles {
			if roleWithProduct.Product != nil && roleWithProduct.Product.ID == productID {
				for _, defaultName := range defaultRoleNames {
					if roleWithProduct.Name == defaultName {
						// Создаем объект Role из RoleWithProduct
						role := roleentity.Role{
							ID:        roleWithProduct.ID,
							Name:      roleWithProduct.Name,
							ProductID: &roleWithProduct.Product.ID,
							IsSystem:  roleWithProduct.IsSystem,
							ActiveFlg: roleWithProduct.ActiveFlg,
							CreatedAt: roleWithProduct.CreatedAt,
							UpdatedAt: roleWithProduct.UpdatedAt,
							DeletedAt: roleWithProduct.DeletedAt,
						}
						defaultRoles = append(defaultRoles, role)
						break
					}
				}
			}
		}
	}

	return defaultRoles, nil
}

func (r *RoleBasedAuthorizer) checkRoleAccess(cc *ctx.Ctx, roleName, path, method string) bool {
	accessRules := cc.Authorization.GetDefaultRoleAccessRules(roleName)
	if accessRules == nil {
		return false
	}

	for _, rule := range accessRules {
		if r.pathMatches(rule.Path, path) && r.methodMatches(rule.Methods, method) {
			return true
		}
	}

	return false
}

func (r *RoleBasedAuthorizer) pathMatches(rulePath, requestPath string) bool {
	if rulePath == "*" {
		return true
	}

	if rulePath == requestPath {
		return true
	}

	// Convert path pattern to regex
	pattern := regexp.QuoteMeta(rulePath)
	// Replace escaped braces with parameter patterns
	pattern = strings.ReplaceAll(pattern, `\{`, `{`)
	pattern = strings.ReplaceAll(pattern, `\}`, `}`)
	// Now replace the parameter patterns with regex
	paramRegex := regexp.MustCompile(`\{[^}]+\}`)
	pattern = paramRegex.ReplaceAllString(pattern, `[^/]+`)
	pattern = "^" + pattern + "$"

	matched, err := regexp.MatchString(pattern, requestPath)
	if err != nil {
		r.log.Errorf("error matching path pattern %s with %s: %v", pattern, requestPath, err)
		return false
	}

	return matched
}

func (r *RoleBasedAuthorizer) methodMatches(allowedMethods []string, requestMethod string) bool {
	for _, method := range allowedMethods {
		if method == requestMethod {
			return true
		}
	}
	return false
}

func (r *RoleBasedAuthorizer) standardizePath(path string) string {
	if path == "" {
		return "/"
	}

	// Convert Gin format ":param" to our format "{param}"
	re := regexp.MustCompile(`:([^/]+)`)
	return re.ReplaceAllString(path, "{$1}")
}
