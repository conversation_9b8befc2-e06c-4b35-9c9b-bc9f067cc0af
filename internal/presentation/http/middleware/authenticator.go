package middleware

import (
	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/authentication"
)

// NewAuthMiddleware creates middleware for authentication
func NewAuthMiddleware(cc *ctx.Ctx) gin.HandlerFunc {
	authenticator := authentication.NewAuthenticator(cc)
	return authenticator.HandleAuthentication()
}

// Authenticator handles authentication operations.
type Authenticator struct {
	*ctx.Ctx
}
