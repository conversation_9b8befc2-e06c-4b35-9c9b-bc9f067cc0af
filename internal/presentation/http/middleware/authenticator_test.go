package middleware

import (
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	appservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/shared/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/authorization"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

func createMockContextWithAppService() *ctx.Ctx {
	return &ctx.Ctx{
		Log:           logger.NewTemporary(),
		Authorization: &authorization.Config{},
		AppService: &appservice.Service{
			// Поля могут быть nil для данного теста
			User:     nil,
			Keycloak: nil,
		},
	}
}

func TestNewAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cc := createMockContextWithAppService()

	// Поскольку NewAuthMiddleware вызывает authentication.NewAuthenticator
	// который обращается к cc.AppService.User и cc.AppService.Keycloak,
	// мы можем протестировать только что функция создается без паники
	assert.NotPanics(t, func() {
		middleware := NewAuthMiddleware(cc)
		assert.NotNil(t, middleware)
		assert.IsType(t, gin.HandlerFunc(nil), middleware)
	})
}

func TestAuthenticator_Struct(t *testing.T) {
	cc := &ctx.Ctx{
		Log:           logger.NewTemporary(),
		Authorization: &authorization.Config{},
	}

	authenticator := &Authenticator{
		Ctx: cc,
	}

	assert.NotNil(t, authenticator)
	assert.Equal(t, cc, authenticator.Ctx)
}
