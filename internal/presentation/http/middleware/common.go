package middleware

import (
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/ratelimiter"
)

// RateLimitConfig contains rate limiting configuration from context
type RateLimitConfig struct {
	PerIPLimit   int
	GlobalLimit  int
	GlobalWindow int
	BurstLimit   int
	BurstWindow  int
	Enabled      bool
}

func ExecutionTimeLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		timeStart := time.Now()
		defer func() {
			c.Set("response time", time.Since(timeStart).String())
		}()
		c.Next()
	}
}

func Logger(log logger.Interface) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		log.Info("request processed",
			slog.Int("code", c.Writer.Status()),
			slog.String("method", c.Request.Method),
			slog.String("uri", c.Request.RequestURI),
			slog.String("response time", c.GetString("response time")),
			slog.String("requestId", c.GetString("requestId")),
			slog.String("remote", c.RemoteIP()),
			slog.String("userAgent", c.Request.UserAgent()),
		)
	}
}

func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		xRequestID, err := uuid.NewV7()
		if err != nil {
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}
		c.Set("requestId", xRequestID.String())
		c.Next()
	}
}

// RequestRateLimiter provides DDoS protection with multiple strategies
func RequestRateLimiter(cc *ctx.Ctx) gin.HandlerFunc {
	if !cc.API.RateLimit.Enabled {
		// Return no-op middleware if rate limiting is disabled
		return func(c *gin.Context) {
			c.Next()
		}
	}

	perIPLimiter := ratelimiter.New(cc.API.RateLimit.PerIPLimit, time.Duration(cc.API.RateLimit.PerIPWindow)*time.Second)
	globalLimiter := ratelimiter.New(cc.API.RateLimit.GlobalLimit, time.Duration(cc.API.RateLimit.GlobalWindow)*time.Second)
	burstLimiter := ratelimiter.New(cc.API.RateLimit.BurstLimit, time.Duration(cc.API.RateLimit.BurstWindow)*time.Second)

	return func(c *gin.Context) {
		clientID := c.ClientIP()

		// 1. Check global server capacity (DDoS protection)
		if !globalLimiter.Allow("global") {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":       "Server overloaded",
				"message":     "Server is currently overloaded due to high traffic",
				"retry_after": cc.API.RateLimit.GlobalWindow,
			})
			c.Abort()
			return
		}

		// 2. Check burst protection (rapid requests from same IP)
		if !burstLimiter.Allow(clientID) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Burst limit exceeded",
				"message":     "Too many rapid requests from this IP",
				"retry_after": cc.API.RateLimit.BurstWindow,
			})
			c.Abort()
			return
		}

		// 3. Check per-IP limit
		if !perIPLimiter.Allow(clientID) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"message":     "Too many requests from this IP address",
				"retry_after": cc.API.RateLimit.PerIPWindow,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

func RequestSizeLimiter(limit int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > limit {
			c.AbortWithStatus(http.StatusRequestEntityTooLarge)
		}
	}
}
