package middleware

import (
	"net/http"
	"regexp"
	"strconv"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/authorization"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

type Authorizer struct {
	*authorization.Config
	log logger.Interface
}

func NewAuthorizer(cc *ctx.Ctx) *Authorizer {
	return &Authorizer{
		Config: cc.Authorization,
		log:    cc.Log,
	}
}

func HandleAuthorizer(cc *ctx.Ctx) gin.HandlerFunc {
	return func(c *gin.Context) {
		requestContext, exists := c.Get(constants.RequestContextKey)
		if !exists {
			cc.Log.Error("request context not found")
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		reqCtx, ok := requestContext.(entity.RequestContext)
		if !ok {
			cc.Log.Error("request context has wrong type")
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		a := NewAuthorizer(cc)
		if !a.isAuthorizedWithContext(cc, c, reqCtx.UserID) {
			c.AbortWithStatus(http.StatusForbidden)
			return
		}

		c.Next()
	}
}

func (a *Authorizer) getUserID(c *gin.Context) int64 {
	userIDInterface, exists := c.Get("userID")
	if !exists {
		return 0
	}

	userID, ok := userIDInterface.(int64)
	if !ok {
		a.log.Error("error converting userID to int64")
		return 0
	}

	return userID
}

func (a *Authorizer) isAuthorizedWithContext(cc *ctx.Ctx, c *gin.Context, userID int64) bool {
	path := a.standartPath(c.FullPath())
	method := c.Request.Method

	if userID == 0 {
		a.log.Warn("unauthorized request: userID is 0")
		return false
	}

	if a.checkRolesProtected(cc, c, method, path) {
		return false
	}

	return a.hasPermission(cc, c, userID)
}

func (a *Authorizer) standartPath(path string) string {
	if path == "" {
		return "/"
	}

	// Convert Gin format ":param" to our format "{param}"
	re := regexp.MustCompile(`:([^/]+)`)
	return re.ReplaceAllString(path, "{$1}")
}

func (a *Authorizer) checkRolesProtected(cc *ctx.Ctx, c *gin.Context, httpMethod, standartPath string) bool {
	if httpMethod != "DELETE" && httpMethod != "PATCH" {
		return false
	}

	if !a.IsProtectedPath(standartPath) {
		return false
	}

	roleID, err := a.extractRoleID(c)
	if err != nil {
		a.log.Errorf("error extracting roleID: %v", err)
		return false
	}

	isProtected, err := cc.AppService.Role.IsRoleProtectedByRoleID(roleID)
	if err != nil {
		a.log.Errorf("error during role protection check: %v", err)
		return false
	}

	return isProtected
}

func (a *Authorizer) extractRoleID(c *gin.Context) (int64, error) {
	roleIDStr := c.Param("roleID")
	return strconv.ParseInt(roleIDStr, 10, 64)
}

func (a *Authorizer) hasPermission(cc *ctx.Ctx, c *gin.Context, userID int64) bool {
	path := a.standartPath(c.FullPath())
	httpMethod := c.Request.Method

	if a.isAdmin(cc, userID) {
		return true
	}

	if a.IsExcludedPath(path, httpMethod) {
		return true
	}

	// Check default role access first
	roleBasedAuthorizer := NewRoleBasedAuthorizer(a.log)
	if roleBasedAuthorizer.hasDefaultRoleAccess(cc, c, userID) {
		return true
	}

	perm, exists := a.GetPermissionRule(path, httpMethod)
	if !exists {
		a.log.Warnf("unknown endpoint %s for role model", path)
		return false
	}

	productID := a.extractProductID(c)
	if productID != 0 {
		return a.hasPermProductSearch(cc, perm.Permission, perm.Method, userID, productID)
	}

	return a.hasPermNotProductSearch(cc, perm.Permission, perm.Method, userID)
}

func (a *Authorizer) isAdmin(cc *ctx.Ctx, userID int64) bool {
	userData, err := cc.AppService.User.GetByID(userID)
	if err != nil {
		a.log.Errorf("IsAdminByUserID: %v", err)
		return false
	}
	return userData.IsAdmin
}

func (a *Authorizer) extractProductID(c *gin.Context) int64 {
	productIDStr := c.Param("productID")
	if productIDStr == "" {
		return 0
	}

	productID, err := strconv.ParseInt(productIDStr, 10, 64)
	if err != nil {
		a.log.Errorf("error converting param productID to int64: %v", err)
		return 0
	}

	return productID
}

func (a *Authorizer) hasPermProductSearch(cc *ctx.Ctx, permName, permMethod string, userID, productID int64) bool {
	participant, err := cc.AppService.Participant.GetByUserIDAndProductID(userID, productID)
	if err != nil {
		a.log.Error(err.Error())
		return false
	}
	if participant.ID == 0 {
		return false
	}

	allRolesID := a.collectAllRoleIDs(cc, participant.ID)
	allPerms := a.getAllPermsByRolesID(cc, allRolesID)

	return a.checkPermissionExists(allPerms, permName, permMethod)
}

func (a *Authorizer) hasPermNotProductSearch(cc *ctx.Ctx, permName, permMethod string, userID int64) bool {
	if userID == 0 {
		a.log.Error("unknown user with userID=0")
		return false
	}

	allRolesID := a.collectUserRoleIDs(cc, userID)
	allPerms := a.getAllPermsByRolesID(cc, allRolesID)

	return a.checkPermissionExists(allPerms, permName, permMethod)
}

func (a *Authorizer) collectAllRoleIDs(cc *ctx.Ctx, participantID int64) []int64 {
	var allRolesID []int64

	groups, err := cc.AppService.Group.GetByParticipantID(participantID)
	if err != nil {
		a.log.Error(err.Error())
	} else {
		groupIDs := make([]int64, 0, len(groups))
		for _, group := range groups {
			groupIDs = append(groupIDs, group.GroupID)
		}
		allRolesID = append(allRolesID, a.getAllRolesIDByGroupsID(cc, groupIDs)...)
	}

	roles, err := cc.AppService.Role.GetByParticipantID(participantID)
	if err != nil {
		a.log.Error(err.Error())
	} else {
		for _, role := range roles {
			allRolesID = append(allRolesID, role.ID)
		}
	}

	return allRolesID
}

func (a *Authorizer) collectUserRoleIDs(cc *ctx.Ctx, userID int64) []int64 {
	var allRolesID []int64

	groups, err := cc.AppService.Group.GetByUserID(userID)
	if err != nil {
		a.log.Error(err.Error())
	} else {
		groupIDs := make([]int64, 0, len(groups))
		for _, group := range groups {
			groupIDs = append(groupIDs, group.ID)
		}
		allRolesID = append(allRolesID, a.getAllRolesIDByGroupsID(cc, groupIDs)...)
	}

	roles, err := cc.AppService.Role.GetByUserID(userID)
	if err != nil {
		a.log.Error(err.Error())
	} else {
		for _, role := range roles {
			allRolesID = append(allRolesID, role.ID)
		}
	}

	return allRolesID
}

func (a *Authorizer) getAllRolesIDByGroupsID(cc *ctx.Ctx, groupsID []int64) []int64 {
	var allRolesID []int64

	for _, groupID := range groupsID {
		if groupID != 0 {
			roles, err := cc.AppService.Role.GetByGroupID(groupID)
			if err != nil {
				a.log.Error(err.Error())
				continue
			}

			for _, role := range roles {
				allRolesID = append(allRolesID, role.ID)
			}
		}
	}

	return allRolesID
}

func (a *Authorizer) getAllPermsByRolesID(cc *ctx.Ctx, allRolesID []int64) []permissionentity.Permission {
	var allPerms []permissionentity.Permission

	for _, roleID := range allRolesID {
		if roleID == 0 {
			continue
		}

		perms, err := cc.AppService.Permission.GetByRoleID(roleID)
		if err != nil {
			a.log.Error(err.Error())
			continue
		}
		allPerms = append(allPerms, perms...)
	}

	return allPerms
}

func (a *Authorizer) checkPermissionExists(allPerms []permissionentity.Permission, permName, permMethod string) bool {
	for _, perm := range allPerms {
		if perm.Name == permName && perm.Method == permMethod {
			return true
		}
	}
	return false
}
