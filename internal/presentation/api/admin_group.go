package api

import (
	"context"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/admingroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/mapper"
)

type AdminGroupHandler struct {
	BaseHandler
}

func NewAdminGroupHandler(ctx *ctx.Ctx) AdminGroupHandler {
	return AdminGroupHandler{
		BaseHandler: BaseHandler{
			appService: ctx.AppService,
			log:        ctx.Log,
		},
	}
}

func (a AdminGroupHandler) CreateGroupAsAdmin(c *gin.Context) {

	var rx admingroup.AdminGroupCreateV1DTO
	if err := c.Bind<PERSON>(&rx); err != nil {
		a.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "group", rx)
		return
	}
	if err := validateGroupCreateAsAdminV1DTO(rx); err != nil {
		a.errorHandler(c, err, "validation failed", http.StatusBadRequest, "role", rx)
		return
	}

	userCtx, ok := a.getUserContextWithErrorHandling(c)
	if !ok {
		return
	}
	group, err := a.appService.Group.CreateAsAdmin(userCtx, mapper.ToEntityGroupFormAdminGroupCreateV1DTO(rx))
	if err != nil {
		a.errorHandler(c, err, "failed to get group", http.StatusInternalServerError, "group")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(group))
}

func (a AdminGroupHandler) GetGroupsWithCategoriesVisibilityAsAdmin(c *gin.Context) {
	rwCat, err := a.appService.Group.GetGroupsWithCategoryStats()
	if err != nil {
		a.errorHandler(c, err, "failed to get categories_groups", http.StatusInternalServerError, "admin", "categories_groups")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsGroupWithCategoryStatsFromModels(rwCat))
}

func (a AdminGroupHandler) GetGroupsAsAdmin(c *gin.Context, params admingroup.GetGroupsAsAdminParams) {

	query := mapper.ToQueryAdminGroupsFromGetGroupsAsAdminParams(params)

	groups, err := a.appService.Group.GetAllAsAdmin(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOAdminGroupCollectionFromModels(groups))
}

func (a AdminGroupHandler) GetGroupAsAdmin(c *gin.Context, groupID int64) {

	groups, err := a.appService.Group.GetAsAdminByID(groupID)
	if err != nil {
		a.errorHandler(c, err, "failed to get group", http.StatusInternalServerError, "group", groupID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(groups))
}

func (a AdminGroupHandler) GetAdminCategoriesGroups(c *gin.Context) {
	rwCat, err := a.appService.Group.GetGroupsWithCategoryStats()
	if err != nil {
		a.errorHandler(c, err, "failed to get group", http.StatusInternalServerError, "admin", "group")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsGroupWithCategoryStatsFromModels(rwCat))
}

func (a AdminGroupHandler) UpdateCategoriesVisibilityForGroupsAsAdmin(c *gin.Context) {

	var rx []admingroup.GroupViewAdminEntityV1DTO

	if err := c.ShouldBindJSON(&rx); err != nil {
		a.errorHandler(c, err, "failed to parse request body", http.StatusBadRequest, "admin", "categories_groups")
		return
	}

	if err := a.appService.Group.UpdateCategoriesGroups(mapper.ToEntitiesGroupWithCategoryStatsFromV1DTOs(rx)); err != nil {
		a.errorHandler(c, err, "failed to update categories_groups", http.StatusInternalServerError, "admin", "categories_groups")
		return
	}

	c.JSON(http.StatusOK, rx)
}

func (a AdminGroupHandler) UpdateGroupAsAdmin(c *gin.Context, groupID int64) {

	var rx admingroup.AdminGroupUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		a.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "group", rx)
		return
	}
	reqCtx, err := a.getRequestContext(c)
	if err != nil {
		a.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)

	updates, err := a.appService.Group.UpdateAsAdmin(userCtx, mapper.ToAdminGroupUpdateFromV1DTO(rx, groupID))
	if err != nil {
		a.errorHandler(c, err, "failed to update group", http.StatusInternalServerError, "admin", groupID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(updates))
}

func (a AdminGroupHandler) DeleteGroupAsAdmin(c *gin.Context, groupID int64) {
	err := a.appService.Group.DeleteAsAdmin(groupID)
	if err != nil {
		a.errorHandler(c, err, "failed to delete group", http.StatusInternalServerError, "group", groupID)
		return
	}

	c.JSON(http.StatusOK, "Group deleted successfully")
}

func validateGroupCreateAsAdminV1DTO(group admingroup.AdminGroupCreateV1DTO) error {
	if group.Name == "" {
		return errors.New("name is required")
	}
	if group.Type == "" {
		return errors.New("type is required")
	}
	return nil
}
