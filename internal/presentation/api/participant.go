package api

import (
	"context"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/participant/mapper"
)

type ParticipantHandler struct {
	BaseHandler
}

func NewParticipantHandler(cc *ctx.Ctx) *ParticipantHandler {
	return &ParticipantHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h ParticipantHandler) CreateParticipant(c *gin.Context, productID int64) {
	var rx participant.ParticipantCreateV1DTO
	if err := c.Bind<PERSON>(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "participant", rx)
		return
	}
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	createdParticipant, err := h.appService.Participant.Create(userCtx, mapper.ToEntityParticipantCreateDataFromParticipantCreateV1DTO(productID, rx))
	if err != nil {
		h.errorHandler(c, err, "failed to create product user", http.StatusInternalServerError, "participant", "")
		return
	}

	c.JSON(http.StatusOK, mapper.ToParticipantV1DTOFromEntityParticipantFull(createdParticipant))

}

func (h ParticipantHandler) GetParticipants(c *gin.Context, productID int64) {
	part, err := h.appService.Participant.GetParticipantFullByProductID(productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get product participants", http.StatusInternalServerError, "participant", "")
		return
	}

	c.JSON(http.StatusOK, mapper.ToParticipantCollectionV1DTOFromParticipantsEntity(part))
}

func (h ParticipantHandler) GetParticipant(c *gin.Context, productID, participantID int64) {
	part, err := h.appService.Participant.GetParticipantFullByParticipantIDAndProductID(participantID, productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get product participant", http.StatusInternalServerError, "participant", productID, participantID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToParticipantV1DTOFromEntityParticipantFull(part))
}

func (h ParticipantHandler) UpdateParticipantGroups(c *gin.Context, productID, participantID int64) {

	var rx participant.ParticipantGroupsUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "participant", productID, participantID)
		return
	}

	if rx.GroupIDs == nil {
		err := errors.New("product group IDs must be provided")
		h.errorHandler(c, err, "empty request", http.StatusBadRequest, "participant", productID, participantID)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	updatedGroupsID, err := h.appService.Participant.UpdateGroups(userCtx, participantID, *rx.GroupIDs)
	if err != nil {
		h.errorHandler(c, err, "failed to update product participant", http.StatusInternalServerError, "participant", productID, participantID)
		return
	}

	c.JSON(http.StatusOK, updatedGroupsID)
}

func (h ParticipantHandler) UpdateParticipantRoles(c *gin.Context, productID, participantID int64) {
	var rx participant.ParticipantRolesUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "participant", productID, participantID)
		return
	}

	if rx.RoleIDs == nil {
		err := errors.New("product role IDs must be provided")
		h.errorHandler(c, err, "empty request", http.StatusBadRequest, "participant", productID, participantID)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	updatedRolesID, err := h.appService.Role.UpdateParticipant(userCtx, participantID, productID, *rx.RoleIDs)
	if err != nil {
		h.errorHandler(c, err, "failed to update product participant roles", http.StatusInternalServerError, "participant", productID, participantID)
		return
	}

	c.JSON(http.StatusOK, updatedRolesID)

}

func (h ParticipantHandler) DeleteParticipant(c *gin.Context, productID, participantID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	err = h.appService.Participant.DeleteByParticipantIDAndProductID(userCtx, participantID, productID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete product participant", http.StatusInternalServerError, "participant", productID, participantID)
		return
	}

	c.JSON(http.StatusOK, "Participant deleted successfully")
}
