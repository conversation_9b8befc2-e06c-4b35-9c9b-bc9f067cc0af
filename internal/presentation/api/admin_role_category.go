package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/mapper"
)

func (h *AdminRoleHandler) GetRolesWithCategoriesVisibilityAsAdmin(c *gin.Context) {

	rwCat, err := h.appService.Role.GetAllWithCategoryStats()
	if err != nil {
		h.errorHandler(c, err, "failed to get categories_roles", http.StatusInternalServerError, "admin", "categories_roles")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsRolesWithCategoriesFromEntities(rwCat))
}

func (h *AdminRoleHandler) UpdateCategoriesVisibilityForRolesAsAdmin(c *gin.Context) {

	var rx []adminrole.RoleViewAdminEntityV1DTO

	if err := c.ShouldBindJSON(&rx); err != nil {
		h.errorHandler(c, err, "failed to parse request body", http.StatusBadRequest, "admin", "categories_roles")
		return
	}

	if err := h.appService.Role.UpdateWithCategories(mapper.ToEntitiesRolesWithCategoriesFromV1DTOs(rx)); err != nil {
		h.errorHandler(c, err, "failed to update categories_roles", http.StatusInternalServerError, "admin", "categories_roles")
		return
	}

	c.JSON(http.StatusOK, rx)
}
