package api

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/mapper"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type SystemRoleHandler struct {
	BaseHandler
}

func NewSystemRoleHandler(cc *ctx.Ctx) *SystemRoleHandler {
	return &SystemRoleHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h SystemRoleHandler) CreateSystemRole(c *gin.Context) {

	var rx systemrole.RoleCreateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "system_role", rx)
		return
	}

	userCtx, ok := h.getUserContextWithErrorHandling(c)
	if !ok {
		return
	}

	if err := validateRoleCreateV1DTO(rx); err != nil {
		h.errorHandler(c, err, "failed to create system role", http.StatusBadRequest, "system_role", rx)
		return
	}

	isSystemRole := rx.Type == constants.SystemType
	role, err := h.appService.Role.Create(userCtx, mapper.ToEntityRoleWithPermissionsFromV1DTOCreateSystem(rx, isSystemRole))
	if err != nil {
		h.errorHandler(c, err, "failed to create system role", http.StatusInternalServerError, "system_role", rx.Name)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleFullFromEntityRoleFull(role))
}

func (h SystemRoleHandler) GetSystemRoles(c *gin.Context) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	roles, err := h.appService.Role.GetSystemRoles(reqCtx.UserID, reqCtx.CategoryID)
	if err != nil {
		h.errorHandler(c, err, "failed to get system roles", http.StatusInternalServerError, "system_role")
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOFromEntitiesRoleWithStats(roles))
}

func (h SystemRoleHandler) GetSystemRoleFull(c *gin.Context, roleID int64) {
	rl, err := h.appService.Role.GetFull(0, roleID)
	if err != nil {
		h.errorHandler(c, err, "failed to get system roles permissions", http.StatusInternalServerError, "system_role", roleID, 10)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleFullFromEntityRoleFull(rl))
}

func (h SystemRoleHandler) UpdateSystemRole(c *gin.Context, roleID int64) {
	var rx systemrole.RoleUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "system_role", rx)
		return
	}
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	isSystemRole := true
	if rx.Type != nil {
		if *rx.Type != constants.SystemType {
			isSystemRole = false
		}
	}
	rl, err := h.appService.Role.UpdateByProductIDAndRoleFull(userCtx, 0, mapper.ToEntitiesSystemRoleFromV1DTO(rx, roleID, isSystemRole))
	if err != nil {
		h.errorHandler(c, err, "failed to update role", http.StatusInternalServerError, "role", "")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleFullFromEntityRoleFull(rl))
}

func (h SystemRoleHandler) DeleteSystemRole(c *gin.Context, roleID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	err = h.appService.Role.Delete(userCtx, 0, roleID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete role", http.StatusInternalServerError, "role", "")
		return
	}

	c.JSON(http.StatusOK, "system role successfully deleted")
}

func validateRoleCreateV1DTO(rx systemrole.RoleCreateV1DTO) error {
	if rx.Name == "" && rx.Type == "" {
		return errkit.NewObjectValidation(errkit.ObjectTypeRole, "Name and Type", errkit.StateEmptyField)
	}
	if rx.Name == "" {
		return errkit.NewObjectValidation(errkit.ObjectTypeRole, "Name", errkit.StateEmptyField)
	}
	if rx.Type == "" {
		return errkit.NewObjectValidation(errkit.ObjectTypeRole, "Type", errkit.StateEmptyField)
	}
	return nil
}
