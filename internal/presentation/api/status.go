package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/status/mapper"
)

type StatusHandler struct {
	*ctx.Ctx
}

func NewStatusHandler(ctx *ctx.Ctx) StatusHandler {
	return StatusHandler{ctx}
}

func (h StatusHandler) GetStatus(c *gin.Context) {
	status := h.AppService.Status.Get()
	c.JSO<PERSON>(http.StatusOK, mapper.ToV1DTOStatusFromModel(status))
}
