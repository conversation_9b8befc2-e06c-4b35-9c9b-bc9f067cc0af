package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/proposal/mapper"
)

type AdminProposalHandler struct {
	BaseHandler
}

func NewAdminProposalHandler(ctx *ctx.Ctx) AdminProposalHandler {
	return AdminProposalHandler{
		BaseHandler: BaseHandler{
			appService: ctx.AppService,
			log:        ctx.Log,
		},
	}
}

func (h AdminProposalHandler) GetProposalsAsAdmin(c *gin.Context, params adminproposal.GetProposalsAsAdminParams) {
	query := mapper.ToQueryAdminProposalFromGetProposalsAsAdminParams(params)
	paginatedResult, err := h.appService.Proposal.GetProposalsAsAdmin(query)
	if err != nil {
		h.errorHandler(c, err, "failed to get admin proposals", http.StatusInternalServerError, "proposal_admin_list", query)
		return
	}

	res, err := mapper.ToV1DTOAdminProposalCollectionFromModels(paginatedResult)
	if err != nil {
		h.errorHandler(c, err, "failed to get admin proposals", http.StatusInternalServerError, "proposal_admin_list", query)
	}

	c.JSON(http.StatusOK, res)

}

func (h AdminProposalHandler) GetProposalAsAdmin(c *gin.Context, proposalID int64) {
	result, err := h.appService.Proposal.GetByID(proposalID)
	if err != nil {
		h.errorHandler(c, err, "failed to get admin proposal", http.StatusInternalServerError, "proposal_admin", proposalID)
		return
	}

	res, err := mapper.ToV1DTOProposalFromEntityFull(result)
	if err != nil {
		h.errorHandler(c, err, "failed to convert proposal", http.StatusInternalServerError, "proposal", res.ID)
		return
	}
	c.JSON(http.StatusOK, res)

}
