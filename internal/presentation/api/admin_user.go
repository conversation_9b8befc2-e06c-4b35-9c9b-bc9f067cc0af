package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminuser"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/mapper"
)

type AdminUserHandler struct {
	BaseHandler
}

func NewAdminUserHandler(ctx *ctx.Ctx) AdminUserHandler {
	return AdminUserHandler{
		BaseHandler: BaseHandler{
			appService: ctx.AppService,
			log:        ctx.Log,
		},
	}
}

func (h AdminUserHandler) GetUsersAsAdmin(c *gin.Context, params adminuser.GetUsersAsAdminParams) {
	query := mapper.ToQueryAdminUsersFromGetUsersAsAdminParams(params)
	paginatedResult, err := h.appService.User.GetAllAsAdmin(query)
	if err != nil {
		h.errorHandler(c, err, "failed to get admin users", http.StatusInternalServerError, "user_admin_list", query)
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOAdminUserCollectionFromModels(paginatedResult))
}

func (h AdminUserHandler) GetUserAsAdmin(c *gin.Context, userID int64) {
	user, err := h.appService.User.GetAsAdminByID(userID)
	if err != nil {
		h.errorHandler(c, err, "failed to get user", http.StatusInternalServerError, "user", userID)
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOUserWithDetailsFromAggregate(user))
}

func (h AdminUserHandler) UpdateUserAsAdmin(c *gin.Context, userID int64) {

	var rx adminuser.AdminUserUpdateV1DTO
	if err := c.ShouldBindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "user", rx)
		return
	}

	userCtx, ok := h.getUserContextWithErrorHandling(c)
	if !ok {
		return
	}

	userDetails, err := h.appService.User.UpdateAsAdmin(userCtx, mapper.ToModelAdminUserUpdateFromV1DTO(userID, rx))
	if err != nil {
		h.errorHandler(c, err, "failed to update user", http.StatusInternalServerError, "user", userDetails)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOUserWithDetailsFromAggregate(userDetails))
}

func (h AdminUserHandler) DeleteUserAsAdmin(c *gin.Context, userID int64) {
	userCtx, ok := h.getUserContextWithErrorHandling(c)
	if !ok {
		return
	}

	if err := h.appService.User.DeleteAsAdmin(userCtx, userID); err != nil {
		h.errorHandler(c, err, "failed to delete user", http.StatusInternalServerError, "admin", "user")
		return
	}

	c.JSON(http.StatusOK, "user was successfully deleted")
}
