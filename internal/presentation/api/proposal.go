package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/proposal/mapper"
	proposalVO "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/sse"
)

type ProposalHandler struct {
	BaseHandler
	sseManager *sse.SSEManager
}

func NewProposalHandler(cc *ctx.Ctx, sseManager *sse.SSEManager) *ProposalHandler {
	return &ProposalHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
		sseManager: sseManager,
	}
}

func (h *ProposalHandler) CreateProposal(c *gin.Context, productID int64) {
	var rx proposal.ProposalCreateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "proposal", rx)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	prop, err := h.appService.Proposal.Create(mapper.ToEntityProposalAndValuesFromV1DTOCreate(productID, rx, reqCtx.UserID))
	if err != nil {
		h.errorHandler(c, err, "failed to create proposal", http.StatusInternalServerError, "proposal", rx)
		return
	}

	propDTO, err := mapper.ToV1DTOProposalFromEntityFull(prop)
	if err != nil {
		h.errorHandler(c, err, "failed to convert proposal", http.StatusInternalServerError, "proposal", rx)
		return
	}

	c.JSON(http.StatusOK, propDTO)
}

// getMessageRecipients определяет список получателей сообщения по заявке
func (h *ProposalHandler) getMessageRecipients(prop any, senderID int64, isAdmin bool) []int64 {
	// Простая логика:
	// - Если отправитель админ, то получатель - создатель заявки
	// - Если отправитель обычный пользователь, то получатели - все админы + создатель заявки (если это не он сам)

	var recipients []int64

	// Извлекаем CreatorID из заявки
	var creatorID int64
	if proposal, ok := prop.(proposalVO.ProposalFull); ok {
		creatorID = proposal.CreatorID
	} else {
		// Если не удалось извлечь CreatorID, возвращаем пустой список
		return recipients
	}

	if isAdmin {
		// Админ отправляет сообщение - получает создатель заявки
		if creatorID != senderID {
			recipients = append(recipients, creatorID)
		}
		h.log.Debug("Admin message recipients", "creatorID", creatorID, "senderID", senderID, "recipients", recipients)
	} else {
		// Обычный пользователь отправляет сообщение - получают админы
		adminUsers, err := h.appService.User.GetAdminUsers()
		if err != nil {
			h.log.Error("Failed to get admin users for message recipients", "error", err)
			// При ошибке получения админов, возвращаем только создателя заявки
			if creatorID != senderID {
				recipients = append(recipients, creatorID)
			}
			return recipients
		}

		h.log.Debug("Retrieved admin users", "adminCount", len(adminUsers), "adminUsers", adminUsers)

		// Добавляем всех админов в список получателей
		for _, admin := range adminUsers {
			if admin.ID != senderID {
				recipients = append(recipients, admin.ID)
			}
		}

		// Также добавляем создателя заявки, если это не отправитель
		if creatorID != senderID {
			recipients = append(recipients, creatorID)
		}

		h.log.Debug("User message recipients", "creatorID", creatorID, "senderID", senderID, "recipients", recipients)
	}

	return recipients
}

func (h *ProposalHandler) GetProposal(c *gin.Context, productID, proposalID int64) {
	prop, err := h.appService.Proposal.GetByProductIDAndProposalID(productID, proposalID)
	if err != nil {
		h.errorHandler(c, err, "failed to get proposal", http.StatusInternalServerError, "get proposal by id")
		return
	}

	propDTO, err := mapper.ToV1DTOProposalFromEntityFull(prop)
	if err != nil {
		h.errorHandler(c, err, "failed to convert proposal", http.StatusInternalServerError, "proposal", prop.ID)
		return
	}

	c.JSON(http.StatusOK, propDTO)
}

func (h *ProposalHandler) GetProposalHistory(c *gin.Context, productID, proposalID int64, params proposal.GetProposalHistoryParams) {
	history, err := h.appService.Proposal.GetHistory(proposalID)
	if err != nil {
		h.errorHandler(c, err, "failed to get proposal history", http.StatusInternalServerError, "get proposal history")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsHistoryRecordFromVOs(history))
}

func (h *ProposalHandler) GetProposals(c *gin.Context, productID int64) {

	props, err := h.appService.Proposal.GetByProductID(productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get proposals", http.StatusInternalServerError, "get proposals")
		return
	}

	propsDTO := mapper.ToV1DTOsProposalFromEntities(props)

	c.JSON(http.StatusOK, propsDTO)
}

func (h *ProposalHandler) UpdateProposal(c *gin.Context, productID, proposalID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)

	prop, err := h.appService.Proposal.GetByProductIDAndProposalID(productID, proposalID)
	if err != nil {
		h.errorHandler(c, err, "failed to get proposal", http.StatusInternalServerError, "update proposal")
		return
	}

	if prop.Status != constants.ProposalStatusDraft && prop.Status != constants.ProposalStatusRejected {
		h.errorHandler(c, errors.New("proposal is not in draft or rejected status"), "proposal is not in draft or rejected status", http.StatusBadRequest, "update proposal")
		return
	}

	var rx proposal.ProposalUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "proposal", rx)
		return
	}

	propRx, err := mapper.ToAggregateProposalUpdateFromV1DTO(productID, proposalID, rx)
	if err != nil {
		h.errorHandler(c, err, "validation failed", http.StatusBadRequest, "proposal", rx)
		return
	}

	propUpdated, err := h.appService.Proposal.Update(userCtx, *propRx)
	if err != nil {
		h.errorHandler(c, err, "failed to update proposal", http.StatusInternalServerError, "update proposal")
		return
	}

	propUpdatedDTO, err := mapper.ToV1DTOProposalFromAggregate(&propUpdated)
	if err != nil {
		h.errorHandler(c, err, "failed to convert proposal", http.StatusInternalServerError, "update proposal")
		return
	}

	c.JSON(http.StatusOK, propUpdatedDTO)
}

func (h *ProposalHandler) DeleteProposal(c *gin.Context, productID, proposalID int64) {
	err := h.appService.Proposal.Delete(mapper.ToEntityProposalFromV1DTO(proposal.ProposalV1DTO{
		ProductID: productID,
		ID:        proposalID,
	}))
	if err != nil {
		h.errorHandler(c, err, "failed to delete proposal", http.StatusInternalServerError, "delete proposal")
		return
	}

	c.JSON(http.StatusOK, "proposal deleted successfully")
}

func (h *ProposalHandler) MarkProposalAsViewed(c *gin.Context, productID, proposalID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	var rx proposal.ProposalLastViewedAtV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "proposal", rx)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	err = h.appService.Proposal.UpdateLastViewed(userCtx, mapper.ToEntityProposalLastViewedFromV1DTO(proposalID, reqCtx.UserID, rx.LastViewedAt))
	if err != nil {
		h.errorHandler(c, err, "failed to update proposal last viewed", http.StatusInternalServerError, "update proposal last viewed")
		return
	}

	c.JSON(http.StatusOK, "proposal last viewed updated successfully")
}

func (h *ProposalHandler) SendProposalMessage(c *gin.Context, productID, proposalID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	var rx proposal.ProposalChatMessageV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "proposal message", rx)
		return
	}

	if rx.Message == "" {
		h.errorHandler(c, errors.New("message cannot be empty"), "message cannot be empty", http.StatusBadRequest, "proposal message", rx)
		return
	}

	// Проверяем существование заявки
	prop, err := h.appService.Proposal.GetByProductIDAndProposalID(productID, proposalID)
	if err != nil {
		h.errorHandler(c, err, "failed to get proposal", http.StatusInternalServerError, "proposal message")
		return
	}

	// Сохраняем сообщение в базу данных
	if err := h.appService.Proposal.CreateMessage(proposalID, rx.Message, reqCtx.UserID); err != nil {
		h.errorHandler(c, err, "failed to save message to database", http.StatusInternalServerError, "proposal message")
		return
	}

	h.log.Info("Proposal message saved and will be sent via SSE",
		"proposalID", proposalID,
		"productID", productID,
		"senderID", reqCtx.UserID,
		"message", rx.Message)

	// Определяем получателей сообщения
	recipients := h.getMessageRecipients(prop, reqCtx.UserID, reqCtx.IsAdmin)
	// recipients = append(recipients, 5)
	h.log.Info("Message recipients determined",
		"proposalID", proposalID,
		"senderID", reqCtx.UserID,
		"isAdmin", reqCtx.IsAdmin,
		"creatorID", prop.CreatorID,
		"recipients", recipients,
		"recipientCount", len(recipients))

	// Формируем данные для SSE события
	messageData := map[string]any{
		"type":      "proposalMessage",
		"createdAt": time.Now().UTC().Format(time.RFC3339),
		"message":   rx.Message,
		"meta": map[string]any{
			"productID":  productID,
			"proposalID": proposalID,
			"userID":     reqCtx.UserID,
		},
	}

	messageJSON, _ := json.Marshal(messageData)

	// Отправляем SSE события всем получателям
	for _, recipientID := range recipients {
		userID := strconv.FormatInt(recipientID, 10)

		event := sse.SSEEvent{
			Type: "proposalMessage",
			Data: string(messageJSON),
			ID:   fmt.Sprintf("%d_%d_%d", time.Now().UTC().Unix(), proposalID, reqCtx.UserID),
		}

		sent := h.sseManager.SendToUser(userID, event)
		if sent {
			h.log.Info("Message SSE event sent successfully",
				"recipientID", recipientID,
				"senderID", reqCtx.UserID,
				"proposalID", proposalID,
				"connections", h.sseManager.GetConnectionCount(userID))
		} else {
			h.log.Warn("Failed to send message SSE event",
				"recipientID", recipientID,
				"senderID", reqCtx.UserID,
				"proposalID", proposalID)

		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Message sent successfully",
		"data":    messageData,
	})
}

// UpdateProposalStatus
// TODO: убрать излишний функционал, добавить проверку условий согласования. Относится ко всем слоям
func (h *ProposalHandler) UpdateProposalStatus(c *gin.Context, productID, proposalID int64) {

	prop, err := h.appService.Proposal.GetByProductIDAndProposalID(productID, proposalID)
	if err != nil {
		h.errorHandler(c, err, "failed to get proposal", http.StatusInternalServerError, "update proposal")
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	if prop.Status != constants.ProposalStatusDraft && prop.Status != constants.ProposalStatusRejected && prop.Status != constants.ProposalStatusOnApproval && !reqCtx.IsAdmin {
		h.errorHandler(c, errors.New("proposal is not in draft or rejected or on approval status"), "proposal is not in draft or rejected or on approval status", http.StatusBadRequest, "update proposal")
		return
	}

	var rx proposal.ProposalStatusUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "proposal", rx)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	propUpdated, err := h.appService.Proposal.SendProposalToApprovalOrReject(userCtx, mapper.ToAggregateProposalSendFromV1DTO(productID, proposalID, rx))
	if err != nil {
		h.errorHandler(c, err, "failed to update proposal", http.StatusInternalServerError, "update proposal")
		return
	}

	propUpdatedDTO, err := mapper.ToV1DTOProposalFromAggregate(&propUpdated)
	if err != nil {
		h.errorHandler(c, err, "failed to convert proposal", http.StatusInternalServerError, "update proposal")
		return
	}

	// Отправляем SSE событие пользователю
	userID := strconv.FormatInt(reqCtx.UserID, 10)

	// Формируем данные для SSE события
	statusData := map[string]any{
		"type":      "proposalStatus",
		"createdAt": time.Now().UTC().Format(time.RFC3339),
		"meta": map[string]any{
			"productID":  productID,
			"proposalID": proposalID,
			"status":     propUpdated.Proposal.Status,
		},
	}

	// Добавляем сообщение если есть
	if rx.Message != nil && *rx.Message != "" {
		statusData["message"] = *rx.Message
	}

	statusJSON, _ := json.Marshal(statusData)

	event := sse.SSEEvent{
		Type: "proposalStatus",
		Data: string(statusJSON),
		ID:   fmt.Sprintf("%d_%d", time.Now().UTC().Unix(), proposalID),
	}

	sent := h.sseManager.SendToUser(userID, event)
	if sent {
		h.log.Info("SSE event sent successfully", "userID", userID, "proposalID", proposalID, "event", event.Type, "newStatus", propUpdated.Proposal.Status, "connections", h.sseManager.GetConnectionCount(userID))
	} else {
		h.log.Warn("Failed to send SSE event - user not connected or connection failed", "userID", userID, "proposalID", proposalID, "connections", h.sseManager.GetConnectionCount(userID))

	}

	c.JSON(http.StatusOK, propUpdatedDTO)
}
