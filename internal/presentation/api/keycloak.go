package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/keycloak"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/keycloak/mapper"
)

const domain = "@sibintek.ru"

type KeycloakHandler struct {
	BaseHandler
}

func NewKeycloakHandler(cc *ctx.Ctx) *KeycloakHandler {
	return &KeycloakHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h KeycloakHandler) GetKeycloakUsers(c *gin.Context, params keycloak.GetKeycloakUsersParams) {

	if params.Search == nil {
		h.GetUsersByDomain(c, domain)
		return
	}

	users, err := h.appService.Keycloak.GetUsersBySearch(*params.Search)
	if err != nil {
		h.errorHandler(c, err, "failed to search user", http.StatusInternalServerError, "user", *params.Search)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsKeycloakUsersFromEntities(users))
}

func (h KeycloakHandler) GetUsersByDomain(c *gin.Context, domain string) {

	users, err := h.appService.Keycloak.GetUsersByEmail(domain)
	if err != nil {
		h.errorHandler(c, err, "failed to get users", http.StatusInternalServerError, "user", "all users")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsKeycloakUsersFromEntities(users))
}
