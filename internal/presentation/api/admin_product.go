package api

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	permissionmapper "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/permission/mapper"
	productmapper "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/mapper"
	appquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/query"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type AdminProductHandler struct {
	BaseHandler
}

func NewAdminProductHandler(ctx *ctx.Ctx) *AdminProductHandler {
	return &AdminProductHandler{
		BaseHandler: BaseHandler{
			appService: ctx.AppService,
			log:        ctx.Log,
		},
	}
}

func (h *AdminProductHandler) CreateProductAsAdmin(c *gin.Context) {

	var rx adminproduct.ProductCreateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product", rx)
		return
	}
	if err := validateProductCreateAsAdminV1DTO(rx); err != nil {
		h.errorHandler(c, err, "validation failed", http.StatusBadRequest, "product", rx)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	createdProduct, err := h.appService.Product.CreateAsAdmin(
		userCtx,
		productentity.ProductCreateData{
			IID:       rx.IID,
			TechName:  rx.TechName,
			Name:      rx.Name,
			CreatorID: reqCtx.UserID,
		},
		rx.OwnerEmails,
	)
	if err != nil {
		h.errorHandler(c, err, "failed to create product", http.StatusInternalServerError, "product", rx)
		return
	}

	c.JSON(http.StatusOK, productmapper.ToV1DTOProductWithDetailsFromAggregateProductWithDetails(createdProduct))
}

func (h *AdminProductHandler) GetProductsAsAdmin(c *gin.Context, params adminproduct.GetProductsAsAdminParams) {
	query := appquery.AdminProductsQuery{
		Search: params.Search,
		Status: (*string)(params.Status),
		Sort:   (*string)(params.Sort),
		Order:  (*string)(params.Order),
		Limit:  params.Limit,
		Offset: params.Offset,
	}

	paginatedResult, err := h.appService.Product.GetAllAsAdmin(query)
	if err != nil {
		h.errorHandler(c, err, "failed to get admin products", http.StatusInternalServerError, "product_admin_list", query)
		return
	}

	c.JSON(http.StatusOK, productmapper.ToV1DTOAdminProductCollectionFromModels(paginatedResult))
}

func (h *AdminProductHandler) GetProductAsAdmin(c *gin.Context, productID int64) {
	product, err := h.appService.Product.GetByIDToAdmin(productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get product", http.StatusInternalServerError, "product", productID)
		return
	}
	c.JSON(http.StatusOK, productmapper.ToV1DTOProductWithDetailsFromAggregateProductWithDetails(product))
}

func (h *AdminProductHandler) GetPermissionsAsAdmin(c *gin.Context) {
	permissionsWithCategories, err := h.appService.Permission.GetAllWithCategories()
	if err != nil {
		h.errorHandler(c, err, "failed to get permissions", http.StatusInternalServerError, "admin", "permissions")
		return
	}

	dto := permissionmapper.ToV1DTOsPermissionsWithCategoriesFromEntitiesMap(permissionsWithCategories)

	c.JSON(http.StatusOK, dto)
}

func (h *AdminProductHandler) UpdateProductAsAdmin(c *gin.Context, productID int64) {

	var product adminproduct.AdminProductUpdateV1DTO
	if err := c.ShouldBindJSON(&product); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product", product)
		return
	}

	updatedProduct, err := h.appService.Product.UpdateAsAdmin(context.Background(), productmapper.ToModelAdminProductUpdateDataFromV1DTO(productID, product))
	if err != nil {
		h.errorHandler(c, err, "failed to update product", http.StatusInternalServerError, "product", product)
		return
	}
	c.JSON(http.StatusOK, productmapper.ToV1DTOProductWithDetailsFromAggregateProductWithDetails(updatedProduct))
}

func validateProductCreateAsAdminV1DTO(rx adminproduct.ProductCreateV1DTO) error {
	if rx.TechName == "" {
		return errkit.NewObjectValidation("product", "TechName", "EmptyTechName")
	}
	if rx.Name == "" {
		return errkit.NewObjectValidation("product", "Name", "EmptyName")
	}
	return nil
}
