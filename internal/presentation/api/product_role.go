package api

import (
	"context"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/mapper"
)

type RoleHandler struct {
	BaseHandler
}

func NewRoleHandler(cc *ctx.Ctx) *RoleHandler {
	return &RoleHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h RoleHandler) CreateProductRole(c *gin.Context, productID int64) {

	var rx productrole.RoleCreateV1DTO
	if err := c.BindJ<PERSON>(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product role", rx)
		return
	}

	userCtx, ok := h.getUserContextWithErrorHandling(c)
	if !ok {
		return
	}

	for _, p := range rx.Permissions {
		if p.Name == "" {
			h.errorHandler(c, errors.New("permission name is required"), "permission name is required", http.StatusBadRequest, "product role", rx)
			return
		}
	}

	isSystemRole := rx.Type == constants.SystemType
	role, err := h.appService.Role.Create(userCtx, mapper.ToEntityRoleWithPermissionsFromV1DTOCreateCustom(rx, isSystemRole, productID))
	if err != nil {
		h.errorHandler(c, err, "failed to create product role", http.StatusInternalServerError, "product role", rx)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleFullFromEntityRoleFull(role))

}

func (h RoleHandler) GetProductRoles(c *gin.Context, productID int64) {
	roles, err := h.appService.Role.GetByProductID(productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get roles", http.StatusInternalServerError, "product role", productID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleShortFromEntityRoleWithStats(roles))
}

func (h RoleHandler) GetProductRoleFull(c *gin.Context, productID, roleID int64) {
	rl, err := h.appService.Role.GetFull(productID, roleID)
	if err != nil {
		h.errorHandler(c, err, "failed to get roles permissions", http.StatusInternalServerError, "product role", roleID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleFullFromEntityRoleFull(rl))

}

func (h RoleHandler) UpdateProductRole(c *gin.Context, productID, roleID int64) {

	var rx productrole.RoleUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product role", rx)
		return
	}
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		return
	}
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	rl, err := h.appService.Role.UpdateByProductIDAndRoleFull(userCtx, productID, mapper.ToEntityRoleFromV1DTO(rx, roleID, constants.CustomType))
	if err != nil {
		h.errorHandler(c, err, "failed to update product role", http.StatusInternalServerError, "product role", rx)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleFullFromEntityRoleFull(rl))
}

func (h RoleHandler) DeleteProductRole(c *gin.Context, productID, roleID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		return
	}
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	err = h.appService.Role.Delete(userCtx, productID, roleID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete product role", http.StatusInternalServerError, "product role", roleID)
		return
	}

	c.JSON(http.StatusOK, "product role successfully deleted")
}
