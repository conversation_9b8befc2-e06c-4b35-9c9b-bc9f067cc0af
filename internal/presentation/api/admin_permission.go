package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminpermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/permission/mapper"
)

type AdminPermissionHandler struct {
	BaseHandler
}

func NewAdminHandler(cc *ctx.Ctx) *AdminPermissionHandler {
	return &AdminPermissionHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h *AdminPermissionHandler) GetPermissionsAsAdmin(c *gin.Context) {
	permissionsWithCategories, err := h.appService.Permission.GetAllWithCategories()
	if err != nil {
		h.errorHandler(c, err, "failed to get permissions", http.StatusInternalServerError, "admin", "permissions")
		return
	}

	dto := mapper.ToV1DTOsPermissionsWithCategoriesFromEntitiesMap(permissionsWithCategories)

	c.JSON(http.StatusOK, dto)
}

func (h *AdminPermissionHandler) UpdateCategoriesPermissionsAsAdmin(c *gin.Context) {
	var rx []adminpermission.PermissionWithCategoriesV1DTO
	if err := c.ShouldBindJSON(&rx); err != nil {
		h.errorHandler(c, err, "failed to parse request body", http.StatusBadRequest, "admin", "update-categories-permissions")
		return
	}

	permissionsWithCategories := mapper.ToEntitiesPermissionsWithCategoriesFromV1DTOs(rx)

	err := h.appService.Permission.UpdateCategoryPermissions(permissionsWithCategories)
	if err != nil {
		h.errorHandler(c, err, "failed to update category permissions", http.StatusInternalServerError, "admin", "update-categories-permissions")
		return
	}

	c.JSON(http.StatusOK, rx)
}
