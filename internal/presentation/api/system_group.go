package api

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/mapper"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type SystemGroupHandler struct {
	BaseHandler
}

func NewSystemGroupHandler(cc *ctx.Ctx) *SystemGroupHandler {
	return &SystemGroupHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h SystemGroupHandler) CreateSystemGroup(c *gin.Context) {

	var rx systemgroup.GroupCreateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "system_group", rx)
		return
	}

	gr, err := h.appService.Group.Create(mapper.ToModelGroupFromV1DTOCreateSystemGroup(0, constants.SystemType, rx))
	if err != nil {
		h.errorHandler(c, err, "failed to create system group", http.StatusInternalServerError, "system_group", rx.Name)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOGroupShortFromModelSystemGroup(gr))
}

func (h SystemGroupHandler) GetSystemGroups(c *gin.Context) {

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	groups, err := h.appService.Group.GetSystemGroups(reqCtx.UserID, reqCtx.CategoryID)
	if err != nil {
		h.errorHandler(c, err, "failed to get system group roles", http.StatusInternalServerError, "system_group", "")
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOGroupShortFromModelGroupWithStats(groups))
}

func (h SystemGroupHandler) GetSystemGroupFull(c *gin.Context, groupID int64) {
	groupsRoles, err := h.appService.Group.GetByGroupIDAndProductID(groupID, 0)
	if err != nil {
		h.errorHandler(c, err, "failed to get system group roles", http.StatusNotFound, "system_group", "")
		return
	}

	c.JSON(http.StatusOK, mapper.ToDTOGroupFullFromModelGroupFull(groupsRoles))
}

func (h SystemGroupHandler) UpdateSystemGroup(c *gin.Context, groupID int64) {
	var rx systemgroup.GroupFullV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "system_group", rx)
		return
	}

	// Group type validation
	if rx.Type != "" && rx.Type != constants.SystemType && rx.Type != constants.CustomType {
		h.errorHandler(c, errkit.NewObjectInvalidData(errkit.ObjectTypeGroup, fmt.Sprintf("%d", groupID), errkit.StateInvalidData),
			"invalid group type", http.StatusBadRequest, "system_group", groupID)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	gr, err := h.appService.Group.UpdateByGroupFull(userCtx, mapper.ToEntityGroupFullFromV1DTOGroupFull(rx, groupID), 0)
	if err != nil {
		entity, extract := errkit.ExtractObjectType(err)
		if !extract {
			entity = "system_group"
		}
		h.errorHandler(c, err, "failed to update system group", http.StatusInternalServerError, entity, groupID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOFromEntityGroupFull(gr))
}

func (h SystemGroupHandler) DeleteSystemGroup(c *gin.Context, groupID int64) {
	err := h.appService.Group.Delete(0, groupID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete system group", http.StatusNotFound, "system_group", groupID)
		return
	}

	c.JSON(http.StatusOK, "system group successfully deleted")
}
