package api

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/mapper"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type ProductHandler struct {
	BaseHandler
}

func NewProductHandler(cc *ctx.Ctx) *ProductHandler {
	return &ProductHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h *ProductHandler) CreateProduct(c *gin.Context) {

	var rx product.ProductCreateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product", rx)
		return
	}

	if err := validateProductCreateV1DTO(rx); err != nil {
		h.errorHandler(c, err, "validation failed", http.StatusBadRequest, "product", rx)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	createdProduct, err := h.appService.Product.Create(
		userCtx,
		productentity.ProductCreateData{
			IID:       rx.IID,
			TechName:  rx.TechName,
			Name:      rx.Name,
			CreatorID: reqCtx.UserID,
		},
		rx.OwnerEmails,
	)
	if err != nil {
		h.errorHandler(c, err, "failed to create product", http.StatusInternalServerError, "product", rx)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOProductFromModel(createdProduct))
}

func (h *ProductHandler) GetProducts(c *gin.Context) {

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	user, err := h.appService.User.GetByID(reqCtx.UserID)
	if err != nil {
		h.errorHandler(c, err, "failed to get user", http.StatusInternalServerError, "product", nil)
		return
	}

	participants, err := h.appService.Participant.GetByUserID(reqCtx.UserID)
	if err != nil {
		h.errorHandler(c, err, "failed to get participants", http.StatusInternalServerError, "product", nil)
		return
	}

	if user.IsAdmin {
		products, err := h.appService.Product.GetAll()
		if err != nil {
			h.errorHandler(c, err, "failed to get product", http.StatusInternalServerError, "product", nil)
			return
		}

		productsDTO := make([]product.ProductV1DTO, len(products))
		for i, p := range products {
			productsDTO[i] = mapper.ToV1DTOProductFromModel(p)
		}
		c.JSON(http.StatusOK, mapper.ToV1DTOProductCollectionFromV1DTOsProduct(productsDTO, user.LastActiveProductID))

		return
	}

	products, err := h.appService.Product.GetAll()
	if err != nil {
		h.errorHandler(c, err, "failed to get product", http.StatusInternalServerError, "product", nil)
		return
	}

	productsFiltered := make([]product.ProductV1DTO, 0)
	for _, p := range products {
		for _, participant := range participants {
			if p.ID == participant.ProductID {
				productsFiltered = append(productsFiltered, mapper.ToV1DTOProductFromModel(p))
			}
		}
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOProductCollectionFromV1DTOsProduct(productsFiltered, user.LastActiveProductID))
}

func (h *ProductHandler) GetProduct(c *gin.Context, id int64) {
	productEntity, err := h.appService.Product.GetByID(id)
	if err != nil {
		h.errorHandler(c, err, "failed to get product", http.StatusInternalServerError, "product", id)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	_, err = h.appService.User.Update(context.Background(), userentity.UserUpdateData{
		ID:                  reqCtx.UserID,
		LastActiveProductID: &id,
	})
	if err != nil {
		h.errorHandler(c, err, "failed to update last active product ID", http.StatusInternalServerError, "product", id)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOProductFromModel(productEntity))
}

func (h *ProductHandler) UpdateProduct(c *gin.Context, id int64) {

	var productEntity product.ProductUpdateV1DTO
	if err := c.ShouldBindJSON(&productEntity); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product", productEntity)
		return
	}

	updatedProduct, err := h.appService.Product.Update(productentity.ProductUpdateData{
		ID:          id,
		IID:         productEntity.IID,
		TechName:    productEntity.TechName,
		Name:        productEntity.Name,
		Description: productEntity.Desc,
	})
	if err != nil {
		h.errorHandler(c, err, "failed to update product", http.StatusInternalServerError, "product", productEntity)
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOProductFromModel(updatedProduct))
}

func (h *ProductHandler) DeleteProduct(c *gin.Context, id int64) {

	err := h.appService.Product.Delete(id)
	if err != nil {
		h.errorHandler(c, err, "failed to delete product", http.StatusInternalServerError, "product", id)
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Product deleted successfully"})
}

func validateProductCreateV1DTO(rx product.ProductCreateV1DTO) error {
	if rx.TechName == "" {
		return errkit.NewObjectValidation(errkit.ObjectTypeProduct, "TechName", errkit.StateEmptyField)
	}
	if rx.Name == "" {
		return errkit.NewObjectValidation(errkit.ObjectTypeProduct, "Name", errkit.StateEmptyField)
	}
	return nil
}
