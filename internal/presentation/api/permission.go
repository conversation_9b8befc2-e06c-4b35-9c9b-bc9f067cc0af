package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/permission/mapper"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type PermissionHandler struct {
	BaseHandler
}

func NewPermissionHandler(cc *ctx.Ctx) *PermissionHandler {
	return &PermissionHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h PermissionHandler) GetPermissions(c *gin.Context) {

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	permissions, err := h.appService.Permission.GetAll()
	if err != nil {
		h.errorHandler(c, err, "failed to get permissions", http.StatusInternalServerError, "permission", "all")
		return
	}

	userData, err := h.appService.User.GetByID(reqCtx.UserID)
	if err != nil {
		h.errorHandler(c, err, "failed to get user", http.StatusInternalServerError, "user", reqCtx.UserID)
		return
	}

	if !userData.IsAdmin {

		userPermissions, err := h.appService.Permission.GetPermissionsByCategoryID(userData.CategoryID)
		if err != nil {
			h.errorHandler(c, err, "failed to get user permissions", http.StatusInternalServerError, "permission", "all")
			return
		}

		c.JSON(http.StatusOK, mapper.ToV1DTOsPermissionFromEntities(userPermissions))
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsPermissionFromEntities(permissions))
}

func (h PermissionHandler) GetUserPermissions(c *gin.Context) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	permissions, err := h.appService.Permission.GetByUserID(reqCtx.UserID, reqCtx.IsAdmin)
	if err != nil {
		h.errorHandler(c, err, "failed to get permissions", http.StatusInternalServerError, "permission", "user")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsPermissionFromEntities(permissions))
}

func (h PermissionHandler) GetParticipantPermissions(c *gin.Context, productID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	user, err := h.appService.User.GetByID(reqCtx.UserID)
	if err != nil {
		h.errorHandler(c, err, "failed to get user", http.StatusInternalServerError, "permission", "participant")
		return
	}

	_, err = h.appService.Product.GetByID(productID)
	if err != nil {
		if errkit.IsNotFoundError(err) {
			h.errorHandler(c, err, "product not found", http.StatusNotFound, "product", productID)
			return
		}
		h.errorHandler(c, err, "failed to get product", http.StatusInternalServerError, "product", productID)
		return
	}

	if user.IsAdmin {
		permissions, err := h.appService.Permission.GetAll()
		if err != nil {
			h.errorHandler(c, err, "failed to get permissions", http.StatusInternalServerError, "permission", "all")
			return
		}
		c.JSON(http.StatusOK, mapper.ToV1DTOsPermissionFromEntities(permissions))
		return
	}

	participant, err := h.appService.Participant.GetByUserIDAndProductID(reqCtx.UserID, productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get participant", http.StatusInternalServerError, "permission", "participant")
		return
	}

	permissions, err := h.appService.Permission.GetByParticipantID(participant.ID)
	if err != nil {
		h.errorHandler(c, err, "failed to get permissions", http.StatusInternalServerError, "permission", "participant")
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsPermissionFromEntities(permissions))
}
