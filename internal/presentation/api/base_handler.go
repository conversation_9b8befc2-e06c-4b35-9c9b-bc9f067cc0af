package api

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/shared/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

type BaseHandler struct {
	appService *service.Service
	log        logger.Interface
}

// errorHandler handles errors.
//
// Parameters:
//
//	c - The Gin context for the current HTTP request.
//	err - The error that occurred.
//	logMessage - A descriptive message for logging purposes.
//	defaultStatusCode - The HTTP status code to use if no specific code is provided by the error.
//	entity - The name of the entity related to the error (e.g., "product").
//	params - Additional parameters providing context about the error.
//
// The function logs the error details and sends an appropriate HTTP response to the client based on the error type.
func (h *BaseHandler) errorHandler(c *gin.Context, err error, logMessage string, defaultStatusCode int, entity string, params ...any) {
	h.log.With(slog.Any("details", err)).Error(logMessage)
	if err == nil {
		sendErrorResponse(c, defaultStatusCode, entity, fmt.Sprint(params...), "unknown error")
		return
	}
	if statusCode, ok := errkit.ExtractStatusCode(err); ok {
		c.JSON(statusCode, err)
	} else {
		paramsStr := fmt.Sprint(params...)
		sendErrorResponse(c, defaultStatusCode, entity, paramsStr, err.Error())
	}
}

func (h *BaseHandler) getRequestContext(c *gin.Context) (entity.RequestContext, error) {
	reqCtxValue, exists := c.Get(constants.RequestContextKey)
	if !exists {
		return entity.RequestContext{}, errors.New("request context not found")
	}

	reqCtx, ok := reqCtxValue.(entity.RequestContext)
	if !ok {
		h.log.Error("Invalid request context type in Gin context",
			slog.String("expected_type", "entity.RequestContext"),
			slog.Any("actual_type", fmt.Sprintf("%T", reqCtxValue)),
		)
		return entity.RequestContext{}, fmt.Errorf("invalid request context type: got %T", reqCtxValue)
	}

	return reqCtx, nil
}

func (h *BaseHandler) getUserContext(c *gin.Context) (context.Context, error) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		return nil, err
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	return userCtx, nil
}

func (h *BaseHandler) getUserContextWithErrorHandling(c *gin.Context) (context.Context, bool) {
	userCtx, err := h.getUserContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return nil, false
	}
	return userCtx, true
}

func (h *BaseHandler) getUserIDWithErrorHandling(c *gin.Context) (int64, bool) {
	userCtx, err := h.getUserContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get user context", http.StatusBadRequest, "user context", nil)
		return 0, false
	}
	return userCtx.Value(constants.UserIDKey).(int64), true
}

// sendErrorResponse sends an error response to the client.
func sendErrorResponse(c *gin.Context, status int, objectType, objectID, state string) {
	c.JSON(status, errkit.NewErrorResponseDTO(status, errkit.ObjectType(objectType), objectID, errkit.State(state)))
}
