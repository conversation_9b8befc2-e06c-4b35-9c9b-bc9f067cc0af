package api

import (
	"context"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/mapper"
)

type AdminRoleHandler struct {
	BaseHandler
}

func NewAdminRoleHandler(ctx *ctx.Ctx) *AdminRoleHandler {
	return &AdminRoleHandler{
		BaseHandler: BaseHandler{
			appService: ctx.AppService,
			log:        ctx.Log,
		},
	}
}

func (h *AdminRoleHandler) CreateRoleAsAdmin(c *gin.Context) {

	var rx adminrole.AdminRoleCreateV1DTO
	if err := c.BindJ<PERSON>N(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "role", rx)
		return
	}
	if err := validateRoleCreateAsAdminV1DTO(rx); err != nil {
		h.errorHandler(c, err, "validation failed", http.StatusBadRequest, "role", rx)
		return
	}

	userCtx, ok := h.getUserContextWithErrorHandling(c)
	if !ok {
		return
	}

	roleCreateData, perms := mapper.ToEntityRoleFormAdminRoleCreateV1DTO(rx)
	role, err := h.appService.Role.CreateAsAdmin(userCtx, roleCreateData, perms)
	if err != nil {
		h.errorHandler(c, err, "failed to create role", http.StatusInternalServerError, "role", rx)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleWithDetailsFromAggregateRoleWithDetails(role))
}

func (h *AdminRoleHandler) GetRolesAsAdmin(c *gin.Context, params adminrole.GetRolesAsAdminParams) {
	query := mapper.ToQueryAdminRolesFromGetRolesAsAdminParams(params)
	paginatedResult, err := h.appService.Role.GetAllAsAdmin(query)
	if err != nil {
		h.errorHandler(c, err, "failed to get admin roles", http.StatusInternalServerError, "role_admin_list", query)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOAdminRoleCollectionFromModels(paginatedResult))
}

func (h *AdminRoleHandler) GetRoleAsAdmin(c *gin.Context, roleID int64) {
	role, err := h.appService.Role.GetAsAdminByID(roleID)
	if err != nil {
		h.errorHandler(c, err, "failed to get roles", http.StatusInternalServerError, "admin", "roles")
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTORoleWithDetailsFromAggregateRoleWithDetails(role))
}

func (h *AdminRoleHandler) UpdateRoleAsAdmin(c *gin.Context, roleID int64) {

	var rx adminrole.AdminRoleUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "role", rx)
		return
	}
	if rx.Permissions != nil {
		for _, p := range *rx.Permissions {
			if p.Name == "" {
				h.errorHandler(c, errors.New("permission name is required"), "permission name is required", http.StatusBadRequest, "role", rx)
				return
			}
		}
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	roleData := mapper.ToAdminRoleUpdateDataFromV1DTO(roleID, rx)
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	role, err := h.appService.Role.UpdateAsAdmin(userCtx, roleData)
	if err != nil {
		h.errorHandler(c, err, "failed to create role", http.StatusInternalServerError, "role", rx)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleWithDetailsFromAggregateRoleWithDetails(role))
}

func (h *AdminRoleHandler) DeleteRoleAsAdmin(c *gin.Context, roleID int64) {

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	err = h.appService.Role.DeleteAsAdmin(userCtx, roleID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete role", http.StatusInternalServerError, "admin", "roles")
		return
	}

	c.JSON(http.StatusOK, "role was successfully deleted")
}

func validateRoleCreateAsAdminV1DTO(role adminrole.AdminRoleCreateV1DTO) error {
	if role.Type == "" {
		return errors.New("type is required")
	}
	if role.Name == "" {
		return errors.New("name is required")
	}
	if len(role.Permissions) == 0 {
		return errors.New("permissions are required")
	}
	return nil
}
