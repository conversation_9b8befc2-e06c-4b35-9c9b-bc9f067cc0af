package api

import (
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/mapper"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type GroupHandler struct {
	BaseHandler
}

func NewGroupHandler(cc *ctx.Ctx) *GroupHandler {
	return &GroupHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h *GroupHandler) CreateProductGroup(c *gin.Context, productID int64) {
	var rx productgroup.GroupCreateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product group", rx)
		return
	}

	group, err := h.appService.Group.Create(mapper.ToEntityGroupFromV1DTOCreate(productID, constants.CustomType, rx))
	if err != nil {
		h.errorHandler(c, err, "failed to create product group", http.StatusInternalServerError, "product group", rx.Name)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOGroupShortFromEntity(group))
}

func (h *GroupHandler) GetProductGroups(c *gin.Context, productID int64) {

	groups, err := h.appService.Group.GetWithStatsByProductID(productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get product groups", http.StatusInternalServerError, "product group", productID)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsGroupShortFromEntity(groups))
}

func (h *GroupHandler) GetProductGroupFull(c *gin.Context, productID, groupID int64) {
	groups, err := h.appService.Group.GetByGroupIDAndProductID(groupID, productID)
	if err != nil {
		h.errorHandler(c, err, "failed to get product group roles", http.StatusInternalServerError, "product group", strconv.FormatInt(productID, 10), strconv.FormatInt(groupID, 10))
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOGroupFullFromEntity(groups))
}

func (h *GroupHandler) UpdateProductGroup(c *gin.Context, productID, groupID int64) {
	var rx productgroup.GroupUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "product group", rx)
		return
	}

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	gr, err := h.appService.Group.UpdateByGroupFull(userCtx, mapper.ToEntityGroupFullFromV1DTOUpdate(groupID, rx), productID)
	if err != nil {
		entity, extract := errkit.ExtractObjectType(err)
		if !extract {
			entity = "product group"
		}
		h.errorHandler(c, err, "failed to update product group", http.StatusInternalServerError, entity, "productID="+strconv.FormatInt(productID, 10), ", ", "groupID="+strconv.FormatInt(groupID, 10))
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOFromEntityGroupFull(gr))
}

func (h *GroupHandler) DeleteProductGroup(c *gin.Context, productID, groupID int64) {
	err := h.appService.Group.Delete(productID, groupID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete product group", http.StatusInternalServerError, "product group", strconv.FormatInt(productID, 10), strconv.FormatInt(groupID, 10))
		return
	}

	c.JSON(http.StatusOK, "Product group deleted successfully")
}
