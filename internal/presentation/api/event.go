package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/sse"
)

type EventHandler struct {
	BaseHandler
	sseManager *sse.SSEManager
}

func NewEventHandler(cc *ctx.Ctx, sseManager *sse.SSEManager) EventHandler {
	return EventHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
		sseManager: sseManager,
	}
}

func (h EventHandler) CreateEvent(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "not implemented, status: 200 for testing"})
}

func (h EventHandler) GetUnreadEvents(c *gin.Context) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}

	events, err := h.appService.Proposal.GetUnreadEventsForUser(reqCtx.UserID)
	if err != nil {
		h.errorHandler(c, err, "failed to get unread events", http.StatusInternalServerError, "unread events")
		return
	}

	// Преобразуем события в формат API
	apiEvents := make([]map[string]any, 0)

	// Убеждаемся, что events не nil
	if events == nil {
		events = []valueobject.HistoryRecord{}
	}

	for _, event := range events {
		apiEvent := map[string]any{
			"type":      constants.ToExternalEventType(constants.ToInternalEventTypeFromString(event.EventType)),
			"createdAt": event.CreatedAt.Format(time.RFC3339),
			"meta": map[string]any{
				"proposalID": event.ProposalID,
			},
		}

		// Добавляем сообщение если есть
		if event.Message != nil {
			apiEvent["message"] = *event.Message
		}

		// Добавляем статус если есть
		if event.Status != "" {
			if statusMeta, ok := apiEvent["meta"].(map[string]any); ok {
				statusMeta["status"] = event.Status
			}
		}

		// Добавляем userID если есть
		if event.UserID != nil {
			if meta, ok := apiEvent["meta"].(map[string]any); ok {
				meta["userID"] = *event.UserID
			}
		}

		apiEvents = append(apiEvents, apiEvent)
	}

	c.JSON(http.StatusOK, apiEvents)
}

func (h EventHandler) GetEventSource(c *gin.Context) {
	h.log.Info("SSE connection attempt", "path", c.Request.URL.Path, "method", c.Request.Method)

	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.log.Error("Failed to get request context for SSE", "error", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userID := strconv.FormatInt(reqCtx.UserID, 10)
	h.log.Info("SSE connection authenticated", "userID", userID, "email", reqCtx.Email)

	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")
	c.Header("X-Accel-Buffering", "no") // Отключаем буферизацию в nginx
	c.Header("Transfer-Encoding", "chunked")

	w := c.Writer

	h.sseManager.AddConnection(userID, w)
	defer h.sseManager.RemoveConnection(userID, w)

	h.log.Info("SSE connection established for user", "userID", userID)

	if _, err := w.Write([]byte("event: connected\ndata: Connection established\nid: 1\n\n")); err != nil {
		h.log.Error("Failed to write SSE connection message", "error", err, "userID", userID)
		return
	}
	w.(http.Flusher).Flush()

	// Канал для keep-alive сообщений
	keepAlive := make(chan struct{})
	defer close(keepAlive)

	// Горутина для отправки keep-alive сообщений каждые 10 секунд
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		// Отправляем первый keep-alive сразу через 5 секунд
		firstKeepAlive := time.NewTimer(5 * time.Second)
		defer firstKeepAlive.Stop()

		for {
			select {
			case <-firstKeepAlive.C:
				// Первый keep-alive
				_, err := w.Write([]byte("event: keepalive\ndata: ping\n\n"))
				if err != nil {
					h.log.Warn("Failed to send first keep-alive, connection may be closed", "userID", userID, "error", err)
					return
				}
				if flusher, ok := w.(http.Flusher); ok {
					flusher.Flush()
				}
				h.log.Debug("First keep-alive sent", "userID", userID)
			case <-ticker.C:
				_, err := w.Write([]byte("event: keepalive\ndata: ping\n\n"))
				if err != nil {
					h.log.Warn("Failed to send keep-alive, connection may be closed", "userID", userID, "error", err)
					return
				}
				if flusher, ok := w.(http.Flusher); ok {
					flusher.Flush()
				}
				h.log.Debug("Keep-alive sent", "userID", userID)
			case <-keepAlive:
				return
			case <-c.Request.Context().Done():
				return
			}
		}
	}()

	<-c.Request.Context().Done()

	h.log.Info("SSE connection closed for user", "userID", userID)
}
