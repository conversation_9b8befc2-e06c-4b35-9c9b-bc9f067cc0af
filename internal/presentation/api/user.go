package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/user"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/mapper"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

type UserHandler struct {
	BaseHandler
}

func NewUserHandler(cc *ctx.Ctx) *UserHandler {
	return &UserHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h *UserHandler) CreateUser(c *gin.Context) {
	var rx user.UserCreateV1DTO
	if err := c.ShouldBind<PERSON>(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "user", rx)
		return
	}

	userCtx, err := h.BaseHandler.getUserContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get user context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	userNew, err := h.appService.User.Create(userCtx, mapper.ToModelUserFromV1DTOCreate(rx))
	if err != nil {
		h.errorHandler(c, err, "failed to create user", http.StatusInternalServerError, "user", rx)
		return
	}

	c.JSON(http.StatusCreated, mapper.ToV1DTOUserFromEntity(userNew))
}

func (h *UserHandler) GetUsers(c *gin.Context, params user.GetUsersParams) {
	query := mapper.ToQueryUsersFromGetUsersAsParams(params)

	users, err := h.appService.User.GetAll(query)
	if err != nil {
		h.errorHandler(c, err, "failed to get users", http.StatusInternalServerError, "user", nil)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOUserCollectionFromModels(users))
}

func (h *UserHandler) GetUser(c *gin.Context, userID int64) {
	userData, err := h.appService.User.GetByID(userID)
	if err != nil {
		h.errorHandler(c, err, "failed to get user", http.StatusInternalServerError, "user", userID)
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOUserFromEntity(userData))
}

func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	userData, err := h.appService.User.GetCurrentUser(reqCtx.UserID)
	if err != nil {
		h.errorHandler(c, err, "failed to get current user", http.StatusInternalServerError, "user", nil)
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOUserFromEntity(userData))
}

func (h *UserHandler) GetUserGroupsWithProducts(c *gin.Context, userID int64, params user.GetUserGroupsWithProductsParams) {

	userCtx, err := h.BaseHandler.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	if !userCtx.IsAdmin {
		return
	}

	query := mapper.ToUserFiltersDataFromGetUserGroupsAsParams(params)

	groupsWithProducts, err := h.appService.Group.GetWithProductsByUserFilter(userentity.UserFiltersData{ID: &userID, ProductIDs: query.ProductIDs})
	if err != nil {
		h.errorHandler(c, err, "failed to get user groups product IDs", http.StatusInternalServerError, "user", nil)
		return
	}
	c.JSON(http.StatusOK, mapper.ToV1DTOsGroupWithProductCollectionFromModels(groupsWithProducts))
}

func (h *UserHandler) GetUserProducts(c *gin.Context, userID int64, params user.GetUserProductsParams) {

	userCtx, err := h.BaseHandler.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	if !userCtx.IsAdmin {
		return
	}

	query := mapper.ToSortParamsFromGetUserProductsAsParams(params)

	products, err := h.appService.User.GetUserProduct(userID, &query)
	if err != nil {
		h.errorHandler(c, err, "failed to get user product", http.StatusInternalServerError, "user", nil)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOsUserProductFromModels(products))
}

func (h *UserHandler) GetUserRolesWithProducts(c *gin.Context, userID int64, params user.GetUserRolesWithProductsParams) {

	userCtx, err := h.BaseHandler.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get user context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	if !userCtx.IsAdmin {
		return
	}

	query := mapper.ToAdminRolesFromGetUserRolesWithProductsAsParams(params)

	roles, err := h.appService.Role.GetWithProductsByFilter(userID, query)
	if err != nil {
		h.errorHandler(c, err, "failed to get roles and product", http.StatusInternalServerError, "user", nil)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTORoleWithProductCollectionFromModel(roles))
}

func (h *UserHandler) UpdateUser(c *gin.Context, userID int64) {
	var userData user.UserUpdateV1DTO
	if err := c.ShouldBindJSON(&userData); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "user", userData)
		return
	}

	userCtx, err := h.BaseHandler.getUserContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get user context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	updatedUser, err := h.appService.User.Update(userCtx, mapper.ToModelUserUpdateFromV1DTOUpdate(userID, userData))
	if err != nil {
		h.errorHandler(c, err, "failed to update user", http.StatusInternalServerError, "user", userData)
		return
	}

	c.JSON(http.StatusOK, mapper.ToV1DTOUserFromEntity(updatedUser))
}

func (h *UserHandler) DeleteUserProducts(c *gin.Context, userID int64, params user.DeleteUserProductsParams) {

	userCtx, err := h.BaseHandler.getUserContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get user context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	if params.ProductIDs == nil {
		return
	}

	err = h.appService.User.DeleteUserProducts(userCtx, userID, *params.ProductIDs)
	if err != nil {
		h.errorHandler(c, err, "failed to delete user products", http.StatusInternalServerError, "user", nil)
		return
	}

	c.JSON(http.StatusOK, "User products deleted successfully")
}

func (h *UserHandler) DeleteUserGroups(c *gin.Context, userID int64, params user.DeleteUserGroupsParams) {

	userCtx, err := h.BaseHandler.getUserContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get user context", http.StatusInternalServerError, "user", userCtx)
		return
	}

	if params.GroupIDs == nil {
		return
	}

	err = h.appService.User.DeleteUserGroups(userCtx, userID, *params.GroupIDs)
	if err != nil {
		h.errorHandler(c, err, "failed to delete user groups", http.StatusInternalServerError, "user", nil)
		return
	}

	c.JSON(http.StatusOK, "User groups deleted successfully")
}

func (h *UserHandler) DeleteUserRoles(c *gin.Context, userID int64, params user.DeleteUserRolesParams) {

	userCtx, err := h.BaseHandler.getUserContext(c)
	if err != nil {
		return
	}

	if params.RoleIDs == nil {
		return
	}

	err = h.appService.User.DeleteUserRoles(userCtx, userID, *params.RoleIDs)
	if err != nil {
		h.errorHandler(c, err, "failed to delete user roles", http.StatusInternalServerError, "user", nil)
		return
	}

	c.JSON(http.StatusOK, "User roles deleted successfully")
}
