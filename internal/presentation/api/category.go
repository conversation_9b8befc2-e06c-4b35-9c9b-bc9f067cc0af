package api

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/category"
	categorymapper "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/category/mapper"
	permissionmapper "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/permission/mapper"
)

type CategoryHandler struct {
	BaseHandler
}

func NewCategoryHandler(cc *ctx.Ctx) *CategoryHandler {
	return &CategoryHandler{
		BaseHandler: BaseHandler{
			appService: cc.AppService,
			log:        cc.Log,
		},
	}
}

func (h *CategoryHandler) CreateCategory(c *gin.Context) {

	var rx category.CategoryCreateV1DTO
	if err := c.BindJ<PERSON>(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "category", rx)
		return
	}

	createdCategory, err := h.appService.Category.Create(categorymapper.ToEntityCategoryFromV1DTOCreate(rx))
	if err != nil {
		h.errorHandler(c, err, "failed to create category", http.StatusInternalServerError, "category", rx)
		return
	}

	c.JSON(http.StatusOK, categorymapper.ToV1DTOCategoryFromEntity(createdCategory))
	c.JSON(http.StatusOK, "action disabled")
}

func (h *CategoryHandler) GetCategories(c *gin.Context) {
	cats, err := h.appService.Category.GetAll()
	if err != nil {
		h.errorHandler(c, err, "failed to get categories", http.StatusInternalServerError, "category", "all")
		return
	}

	c.JSON(http.StatusOK, categorymapper.ToV1DTOsCategoriesFromEntity(cats))
}

func (h *CategoryHandler) GetCategory(c *gin.Context, categoryID int64) {
	cat, err := h.appService.Category.GetByID(categoryID)
	if err != nil {
		h.errorHandler(c, err, "failed to get category", http.StatusInternalServerError, "category", categoryID)
		return
	}

	c.JSON(http.StatusOK, categorymapper.ToV1DTOCategoryFromEntity(cat))

}

func (h *CategoryHandler) GetCategoryPermissions(c *gin.Context, categoryID int64) {
	permissions, err := h.appService.Permission.GetByCategoryID(categoryID)
	if err != nil {
		h.errorHandler(c, err, "failed to get category permissions", http.StatusInternalServerError, "permission", categoryID)
		return
	}

	c.JSON(http.StatusOK, permissionmapper.ToV1DTOsPermissionFromEntities(permissions))
}

func (h *CategoryHandler) UpdateCategory(c *gin.Context, categoryID int64) {
	var rx category.CategoryUpdateV1DTO
	if err := c.BindJSON(&rx); err != nil {
		h.errorHandler(c, err, "body parsing failed", http.StatusBadRequest, "category", rx)
		return
	}

	cat, err := h.appService.Category.Update(categorymapper.ToEntityCategoryFromV1DTOUpdate(categoryID, rx))
	if err != nil {
		h.errorHandler(c, err, "failed to update category", http.StatusInternalServerError, "category", rx)
		return
	}

	c.JSON(http.StatusOK, categorymapper.ToV1DTOCategoryFromEntity(cat))
}

func (h *CategoryHandler) DeleteCategory(c *gin.Context, categoryID int64) {
	reqCtx, err := h.getRequestContext(c)
	if err != nil {
		h.errorHandler(c, err, "failed to get request context", http.StatusBadRequest, "request context", nil)
		return
	}
	userCtx := context.WithValue(context.Background(), constants.UserIDKey, reqCtx.UserID)
	err = h.appService.Category.Delete(userCtx, categoryID)
	if err != nil {
		h.errorHandler(c, err, "failed to delete category", http.StatusInternalServerError, "category", categoryID)
		return
	}

	c.JSON(http.StatusOK, "category successfully deleted")
}
