package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/permission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

func ToV1DTOsPermissionFromEntities(permissions []entity.Permission) []permission.PermissionV1DTO {

	dtoTemp := make(map[string][]string)
	for _, perm := range permissions {
		dtoTemp[perm.Name] = append(dtoTemp[perm.Name], perm.Method)
	}

	var dto []permission.PermissionV1DTO
	for permName, methods := range dtoTemp {
		var dtoPerm permission.PermissionV1DTO
		dtoPerm.Label = constants.PermissionsRu[permName]
		dtoPerm.Name = permName
		dtoPerm.Methods = make([]permission.MethodV1DTO, 0)
		for _, method := range methods {
			dtoPerm.Methods = append(dtoPerm.Methods, permission.MethodV1DTO{
				Name: method,
			})
		}
		dto = append(dto, dtoPerm)
	}

	return dto
}
