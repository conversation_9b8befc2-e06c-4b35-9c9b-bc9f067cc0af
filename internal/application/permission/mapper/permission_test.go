package mapper

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/permission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

func TestToV1DTOsPermissionFromEntities(t *testing.T) {
	tests := []struct {
		name        string
		permissions []entity.Permission
		expected    []permission.PermissionV1DTO
	}{
		{
			name: "successful conversion with multiple permissions and methods",
			permissions: []entity.Permission{
				{
					ID:     1,
					Name:   "users",
					Method: "GET",
				},
				{
					ID:     2,
					Name:   "users",
					Method: "POST",
				},
				{
					ID:     3,
					Name:   "products",
					Method: "GET",
				},
				{
					ID:     4,
					Name:   "roles",
					Method: "DELETE",
				},
			},
			expected: []permission.PermissionV1DTO{
				{
					Name:    "users",
					Label:   constants.PermissionsRu["users"],
					Methods: []permission.MethodV1DTO{{Name: "GET"}, {Name: "POST"}},
				},
				{
					Name:    "products",
					Label:   constants.PermissionsRu["products"],
					Methods: []permission.MethodV1DTO{{Name: "GET"}},
				},
				{
					Name:    "roles",
					Label:   constants.PermissionsRu["roles"],
					Methods: []permission.MethodV1DTO{{Name: "DELETE"}},
				},
			},
		},
		{
			name: "conversion with single permission single method",
			permissions: []entity.Permission{
				{
					ID:     10,
					Name:   "categories",
					Method: "PUT",
				},
			},
			expected: []permission.PermissionV1DTO{
				{
					Name:    "categories",
					Label:   constants.PermissionsRu["categories"],
					Methods: []permission.MethodV1DTO{{Name: "PUT"}},
				},
			},
		},
		{
			name: "conversion with same permission multiple methods",
			permissions: []entity.Permission{
				{
					ID:     20,
					Name:   "admin",
					Method: "GET",
				},
				{
					ID:     21,
					Name:   "admin",
					Method: "POST",
				},
				{
					ID:     22,
					Name:   "admin",
					Method: "PUT",
				},
				{
					ID:     23,
					Name:   "admin",
					Method: "DELETE",
				},
			},
			expected: []permission.PermissionV1DTO{
				{
					Name:  "admin",
					Label: constants.PermissionsRu["admin"],
					Methods: []permission.MethodV1DTO{
						{Name: "GET"},
						{Name: "POST"},
						{Name: "PUT"},
						{Name: "DELETE"},
					},
				},
			},
		},
		{
			name:        "conversion with empty slice",
			permissions: []entity.Permission{},
			expected:    []permission.PermissionV1DTO{},
		},
		{
			name:        "conversion with nil slice",
			permissions: nil,
			expected:    []permission.PermissionV1DTO{},
		},
		{
			name: "conversion with permission not in constants",
			permissions: []entity.Permission{
				{
					ID:     30,
					Name:   "unknown_permission",
					Method: "GET",
				},
			},
			expected: []permission.PermissionV1DTO{
				{
					Name:    "unknown_permission",
					Label:   "", // empty because not in constants
					Methods: []permission.MethodV1DTO{{Name: "GET"}},
				},
			},
		},
		{
			name: "conversion with empty permission name",
			permissions: []entity.Permission{
				{
					ID:     40,
					Name:   "",
					Method: "GET",
				},
			},
			expected: []permission.PermissionV1DTO{
				{
					Name:    "",
					Label:   "",
					Methods: []permission.MethodV1DTO{{Name: "GET"}},
				},
			},
		},
		{
			name: "conversion with empty method name",
			permissions: []entity.Permission{
				{
					ID:     50,
					Name:   "test",
					Method: "",
				},
			},
			expected: []permission.PermissionV1DTO{
				{
					Name:    "test",
					Label:   constants.PermissionsRu["test"],
					Methods: []permission.MethodV1DTO{{Name: ""}},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToV1DTOsPermissionFromEntities(tt.permissions)

			// Check length first
			assert.Equal(t, len(tt.expected), len(result))

			if len(tt.expected) == 0 {
				assert.Empty(t, result, "Result should be empty for empty input")
				return
			}

			// Since map iteration order is not guaranteed, we need to match by Name
			resultMap := make(map[string]permission.PermissionV1DTO)
			for _, perm := range result {
				resultMap[perm.Name] = perm
			}

			expectedMap := make(map[string]permission.PermissionV1DTO)
			for _, perm := range tt.expected {
				expectedMap[perm.Name] = perm
			}

			assert.Equal(t, len(expectedMap), len(resultMap))

			for permName, expectedPerm := range expectedMap {
				actualPerm, exists := resultMap[permName]
				assert.True(t, exists, "Permission %s should exist in result", permName)

				assert.Equal(t, expectedPerm.Name, actualPerm.Name)
				assert.Equal(t, expectedPerm.Label, actualPerm.Label)

				// Check methods (order might differ, so convert to maps for comparison)
				expectedMethods := make(map[string]bool)
				for _, method := range expectedPerm.Methods {
					expectedMethods[method.Name] = true
				}

				actualMethods := make(map[string]bool)
				for _, method := range actualPerm.Methods {
					actualMethods[method.Name] = true
				}

				assert.Equal(t, expectedMethods, actualMethods, "Methods should match for permission %s", permName)
				assert.Equal(t, len(expectedPerm.Methods), len(actualPerm.Methods), "Method count should match for permission %s", permName)
			}
		})
	}
}
