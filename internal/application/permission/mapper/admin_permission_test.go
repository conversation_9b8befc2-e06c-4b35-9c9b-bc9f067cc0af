package mapper

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminpermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

func TestToV1DTOsPermissionsWithCategoriesFromEntitiesMap(t *testing.T) {
	tests := []struct {
		name                      string
		permissionsWithCategories map[string][]entity.CategoryActivity
		expected                  []adminpermission.PermissionWithCategoriesV1DTO
	}{
		{
			name: "successful conversion with multiple permissions and categories",
			permissionsWithCategories: map[string][]entity.CategoryActivity{
				"users": {
					{ID: 1, Name: "User Management", IsActive: true},
					{ID: 2, Name: "User Reports", IsActive: false},
				},
				"products": {
					{ID: 3, Name: "Product Catalog", IsActive: true},
				},
			},
			expected: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "users",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 1, Name: "User Management", IsActive: true},
						{ID: 2, Name: "User Reports", IsActive: false},
					},
				},
				{
					Name: "products",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 3, Name: "Product Catalog", IsActive: true},
					},
				},
			},
		},
		{
			name: "conversion with single permission multiple categories",
			permissionsWithCategories: map[string][]entity.CategoryActivity{
				"admin": {
					{ID: 10, Name: "System Admin", IsActive: true},
					{ID: 11, Name: "User Admin", IsActive: false},
					{ID: 12, Name: "Content Admin", IsActive: true},
				},
			},
			expected: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "admin",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 10, Name: "System Admin", IsActive: true},
						{ID: 11, Name: "User Admin", IsActive: false},
						{ID: 12, Name: "Content Admin", IsActive: true},
					},
				},
			},
		},
		{
			name: "conversion with permission having empty categories",
			permissionsWithCategories: map[string][]entity.CategoryActivity{
				"empty_permission": {},
			},
			expected: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name:       "empty_permission",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{},
				},
			},
		},
		{
			name: "conversion with permission having nil categories",
			permissionsWithCategories: map[string][]entity.CategoryActivity{
				"nil_permission": nil,
			},
			expected: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name:       "nil_permission",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{},
				},
			},
		},
		{
			name:                      "conversion with empty map",
			permissionsWithCategories: map[string][]entity.CategoryActivity{},
			expected:                  []adminpermission.PermissionWithCategoriesV1DTO{},
		},
		{
			name:                      "conversion with nil map",
			permissionsWithCategories: nil,
			expected:                  []adminpermission.PermissionWithCategoriesV1DTO{},
		},
		{
			name: "conversion with zero ID and empty names",
			permissionsWithCategories: map[string][]entity.CategoryActivity{
				"": {
					{ID: 0, Name: "", IsActive: false},
				},
			},
			expected: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 0, Name: "", IsActive: false},
					},
				},
			},
		},
		{
			name: "conversion with mixed active states",
			permissionsWithCategories: map[string][]entity.CategoryActivity{
				"mixed": {
					{ID: 100, Name: "Active Category", IsActive: true},
					{ID: 101, Name: "Inactive Category", IsActive: false},
					{ID: 102, Name: "Another Active", IsActive: true},
				},
			},
			expected: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "mixed",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 100, Name: "Active Category", IsActive: true},
						{ID: 101, Name: "Inactive Category", IsActive: false},
						{ID: 102, Name: "Another Active", IsActive: true},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToV1DTOsPermissionsWithCategoriesFromEntitiesMap(tt.permissionsWithCategories)

			// Check length first
			assert.Equal(t, len(tt.expected), len(result))

			if len(tt.expected) == 0 {
				assert.Empty(t, result, "Result should be empty for empty input")
				return
			}

			// Since map iteration order is not guaranteed, convert to maps for comparison
			resultMap := make(map[string]adminpermission.PermissionWithCategoriesV1DTO)
			for _, perm := range result {
				resultMap[perm.Name] = perm
			}

			expectedMap := make(map[string]adminpermission.PermissionWithCategoriesV1DTO)
			for _, perm := range tt.expected {
				expectedMap[perm.Name] = perm
			}

			assert.Equal(t, len(expectedMap), len(resultMap))

			for permName, expectedPerm := range expectedMap {
				actualPerm, exists := resultMap[permName]
				assert.True(t, exists, "Permission %s should exist in result", permName)

				assert.Equal(t, expectedPerm.Name, actualPerm.Name)
				assert.Equal(t, len(expectedPerm.Categories), len(actualPerm.Categories))

				// Convert categories to maps for comparison (order might differ)
				expectedCats := make(map[int64]adminpermission.CategoryWithCheckedV1DTO)
				for _, cat := range expectedPerm.Categories {
					expectedCats[cat.ID] = cat
				}

				actualCats := make(map[int64]adminpermission.CategoryWithCheckedV1DTO)
				for _, cat := range actualPerm.Categories {
					actualCats[cat.ID] = cat
				}

				assert.Equal(t, expectedCats, actualCats, "Categories should match for permission %s", permName)
			}
		})
	}
}

func TestToEntitiesPermissionsWithCategoriesFromV1DTOs(t *testing.T) {
	tests := []struct {
		name     string
		permCats []adminpermission.PermissionWithCategoriesV1DTO
		expected []entity.PermissionWithCategories
	}{
		{
			name: "successful conversion with multiple permissions and categories",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "users",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 1, Name: "User Management", IsActive: true},
						{ID: 2, Name: "User Reports", IsActive: false},
					},
				},
				{
					Name: "products",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 3, Name: "Product Catalog", IsActive: true},
					},
				},
			},
			expected: []entity.PermissionWithCategories{
				{
					PermissionName: "users",
					Categories: []entity.CategoryActivity{
						{ID: 1, Name: "User Management", IsActive: true},
						{ID: 2, Name: "User Reports", IsActive: false},
					},
				},
				{
					PermissionName: "products",
					Categories: []entity.CategoryActivity{
						{ID: 3, Name: "Product Catalog", IsActive: true},
					},
				},
			},
		},
		{
			name: "conversion with single permission multiple categories",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "admin",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 10, Name: "System Admin", IsActive: true},
						{ID: 11, Name: "User Admin", IsActive: false},
						{ID: 12, Name: "Content Admin", IsActive: true},
					},
				},
			},
			expected: []entity.PermissionWithCategories{
				{
					PermissionName: "admin",
					Categories: []entity.CategoryActivity{
						{ID: 10, Name: "System Admin", IsActive: true},
						{ID: 11, Name: "User Admin", IsActive: false},
						{ID: 12, Name: "Content Admin", IsActive: true},
					},
				},
			},
		},
		{
			name: "conversion with permission having empty categories",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name:       "empty_permission",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{},
				},
			},
			expected: []entity.PermissionWithCategories{
				{
					PermissionName: "empty_permission",
					Categories:     []entity.CategoryActivity{},
				},
			},
		},
		{
			name: "conversion with permission having nil categories",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name:       "nil_permission",
					Categories: nil,
				},
			},
			expected: []entity.PermissionWithCategories{
				{
					PermissionName: "nil_permission",
					Categories:     []entity.CategoryActivity{},
				},
			},
		},
		{
			name:     "conversion with empty slice",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{},
			expected: []entity.PermissionWithCategories{},
		},
		{
			name:     "conversion with nil slice",
			permCats: nil,
			expected: []entity.PermissionWithCategories{},
		},
		{
			name: "conversion with zero ID and empty names",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 0, Name: "", IsActive: false},
					},
				},
			},
			expected: []entity.PermissionWithCategories{
				{
					PermissionName: "",
					Categories: []entity.CategoryActivity{
						{ID: 0, Name: "", IsActive: false},
					},
				},
			},
		},
		{
			name: "conversion with mixed active states",
			permCats: []adminpermission.PermissionWithCategoriesV1DTO{
				{
					Name: "mixed",
					Categories: []adminpermission.CategoryWithCheckedV1DTO{
						{ID: 100, Name: "Active Category", IsActive: true},
						{ID: 101, Name: "Inactive Category", IsActive: false},
						{ID: 102, Name: "Another Active", IsActive: true},
					},
				},
			},
			expected: []entity.PermissionWithCategories{
				{
					PermissionName: "mixed",
					Categories: []entity.CategoryActivity{
						{ID: 100, Name: "Active Category", IsActive: true},
						{ID: 101, Name: "Inactive Category", IsActive: false},
						{ID: 102, Name: "Another Active", IsActive: true},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToEntitiesPermissionsWithCategoriesFromV1DTOs(tt.permCats)

			assert.Equal(t, len(tt.expected), len(result))

			if len(tt.expected) == 0 {
				assert.Empty(t, result, "Result should be empty for empty input")
				return
			}

			// Since slice order is preserved, we can compare directly
			for i, expectedPerm := range tt.expected {
				actualPerm := result[i]

				assert.Equal(t, expectedPerm.PermissionName, actualPerm.PermissionName)
				assert.Equal(t, len(expectedPerm.Categories), len(actualPerm.Categories))

				// Compare categories - order should be preserved
				for j, expectedCat := range expectedPerm.Categories {
					actualCat := actualPerm.Categories[j]
					assert.Equal(t, expectedCat.ID, actualCat.ID)
					assert.Equal(t, expectedCat.Name, actualCat.Name)
					assert.Equal(t, expectedCat.IsActive, actualCat.IsActive)
				}
			}
		})
	}
}
