package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminpermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

func ToEntitiesPermissionsWithCategoriesFromV1DTOs(permCats []adminpermission.PermissionWithCategoriesV1DTO) []entity.PermissionWithCategories {
	result := make([]entity.PermissionWithCategories, 0, len(permCats))

	for _, permCat := range permCats {
		categoryIDs := make([]entity.CategoryActivity, 0)
		for _, catDTO := range permCat.Categories {
			categoryIDs = append(categoryIDs, entity.CategoryActivity{
				ID:       catDTO.ID,
				Name:     catDTO.Name,
				IsActive: catDTO.IsActive,
			})
		}

		result = append(result, entity.PermissionWithCategories{
			PermissionName: permCat.Name,
			Categories:     categoryIDs,
		})
	}

	return result
}

func ToV1DTOsPermissionsWithCategoriesFromEntitiesMap(permissionsWithCategories map[string][]entity.CategoryActivity) []adminpermission.PermissionWithCategoriesV1DTO {
	result := make([]adminpermission.PermissionWithCategoriesV1DTO, 0, len(permissionsWithCategories))

	for permissionName, categories := range permissionsWithCategories {
		categoriesDTO := make([]adminpermission.CategoryWithCheckedV1DTO, 0, len(categories))
		for _, category := range categories {
			categoriesDTO = append(categoriesDTO, adminpermission.CategoryWithCheckedV1DTO{
				ID:       category.ID,
				Name:     category.Name,
				IsActive: category.IsActive,
			})
		}

		result = append(result, adminpermission.PermissionWithCategoriesV1DTO{
			Name:       permissionName,
			Categories: categoriesDTO,
		})
	}

	return result
}
