package service

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	permissionmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func TestNewPermissionAppService(t *testing.T) {
	mockCategoryDomain := categorymocks.NewCategoryDomainServiceMock(t)
	mockGroupDomain := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipantDomain := participantmocks.NewParticipantDomainServiceMock(t)
	mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
	mockRoleDomain := rolemocks.NewRoleDomainServiceMock(t)
	mockUserDomain := usermocks.NewUserDomainServiceMock(t)

	service := NewPermissionAppService(
		mockCategoryDomain,
		mockGroupDomain,
		mockParticipantDomain,
		mockPermissionDomain,
		mockRoleDomain,
		mockUserDomain,
	)

	require.NotNil(t, service)
	assert.Equal(t, mockCategoryDomain, service.categoryDomain)
	assert.Equal(t, mockGroupDomain, service.groupDomain)
	assert.Equal(t, mockParticipantDomain, service.participantDomain)
	assert.Equal(t, mockPermissionDomain, service.permissionDomain)
	assert.Equal(t, mockRoleDomain, service.roleDomain)
	assert.Equal(t, mockUserDomain, service.userDomain)
}

func TestPermissionAppService_Create(t *testing.T) {
	tests := []struct {
		name           string
		permission     permissionentity.Permission
		mockPermission permissionentity.Permission
		mockError      error
		expectedError  error
	}{
		{
			name: "успешное создание разрешения",
			permission: permissionentity.Permission{
				Name:   "test_permission",
				Method: "GET",
			},
			mockPermission: permissionentity.Permission{
				ID:     1,
				Name:   "test_permission",
				Method: "GET",
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "ошибка при создании разрешения",
			permission: permissionentity.Permission{
				Name:   "test_permission",
				Method: "GET",
			},
			mockPermission: permissionentity.Permission{},
			mockError:      errors.New("create error"),
			expectedError:  errors.New("create error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.CreateMock.Expect(tt.permission).Return(tt.mockPermission, tt.mockError)

			permission, err := service.Create(tt.permission)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermission, permission)
			}

			require.True(t, mockPermissionDomain.MinimockCreateDone())
		})
	}
}

func TestPermissionAppService_GetAll(t *testing.T) {
	tests := []struct {
		name            string
		mockPermissions []permissionentity.Permission
		mockError       error
		expectedError   error
	}{
		{
			name: "успешное получение всех разрешений",
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
				{ID: 2, Name: "perm2", Method: "POST"},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:            "ошибка при получении разрешений",
			mockPermissions: nil,
			mockError:       errors.New("get error"),
			expectedError:   errors.New("get error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.GetAllMock.Expect().Return(tt.mockPermissions, tt.mockError)

			permissions, err := service.GetAll()

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermissions, permissions)
			}

			require.True(t, mockPermissionDomain.MinimockGetAllDone())
		})
	}
}

func TestPermissionAppService_GetByID(t *testing.T) {
	tests := []struct {
		name           string
		id             int64
		mockPermission permissionentity.Permission
		mockError      error
		expectedError  error
	}{
		{
			name:           "успешное получение разрешения по ID",
			id:             1,
			mockPermission: permissionentity.Permission{ID: 1, Name: "perm1", Method: "GET"},
			mockError:      nil,
			expectedError:  nil,
		},
		{
			name:           "ошибка при получении разрешения по ID",
			id:             2,
			mockPermission: permissionentity.Permission{},
			mockError:      errors.New("not found"),
			expectedError:  errors.New("not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.GetByIDMock.Expect(tt.id).Return(tt.mockPermission, tt.mockError)

			permission, err := service.GetByID(tt.id)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermission, permission)
			}

			require.True(t, mockPermissionDomain.MinimockGetByIDDone())
		})
	}
}

func TestPermissionAppService_GetByNameAndMethod(t *testing.T) {
	tests := []struct {
		name           string
		nameParam      string
		methodParam    string
		mockPermission permissionentity.Permission
		mockError      error
		expectedError  error
	}{
		{
			name:           "успешное получение разрешения по имени и методу",
			nameParam:      "perm1",
			methodParam:    "GET",
			mockPermission: permissionentity.Permission{ID: 1, Name: "perm1", Method: "GET"},
			mockError:      nil,
			expectedError:  nil,
		},
		{
			name:           "ошибка при получении разрешения по имени и методу",
			nameParam:      "perm2",
			methodParam:    "POST",
			mockPermission: permissionentity.Permission{},
			mockError:      errors.New("not found"),
			expectedError:  errors.New("not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.GetByNameAndMethodMock.Expect(tt.nameParam, tt.methodParam).Return(tt.mockPermission, tt.mockError)

			permission, err := service.GetByNameAndMethod(tt.nameParam, tt.methodParam)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermission, permission)
			}

			require.True(t, mockPermissionDomain.MinimockGetByNameAndMethodDone())
		})
	}
}

func TestPermissionAppService_GetPermissionsByCategoryID(t *testing.T) {
	tests := []struct {
		name            string
		categoryID      int64
		mockPermissions []permissionentity.Permission
		mockError       error
		expectedError   error
	}{
		{
			name:       "успешное получение разрешений по ID категории",
			categoryID: 1,
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
				{ID: 2, Name: "perm2", Method: "POST"},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:            "ошибка при получении разрешений по ID категории",
			categoryID:      2,
			mockPermissions: nil,
			mockError:       errors.New("get error"),
			expectedError:   errors.New("get error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.GetByCategoryIDMock.Expect(tt.categoryID).Return(tt.mockPermissions, tt.mockError)

			permissions, err := service.GetPermissionsByCategoryID(tt.categoryID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermissions, permissions)
			}

			require.True(t, mockPermissionDomain.MinimockGetByCategoryIDDone())
		})
	}
}

func TestPermissionAppService_GetByCategoryID(t *testing.T) {
	tests := []struct {
		name            string
		categoryID      int64
		setupMocks      func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission
		expectedError   string
		expectedResults []permissionentity.Permission
	}{
		{
			name:       "успешное получение разрешений по категории",
			categoryID: 1,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				expectedPerms := []permissionentity.Permission{
					{ID: 1, Name: "perm1", Method: "GET"},
					{ID: 2, Name: "perm2", Method: "POST"},
					{ID: 3, Name: "perm3", Method: "PUT"},
				}

				// Настраиваем моки для получения всех необходимых данных
				mockGroupDomain.GetByCategoryIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByCategoryIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return([]permissionentity.Permission{{ID: 1, Name: "perm1", Method: "GET"}}, nil)
				mockPermissionDomain.GetByCategoryIDMock.Return([]permissionentity.Permission{{ID: 3, Name: "perm3", Method: "PUT"}}, nil)
				mockPermissionDomain.GetByIDsMock.Return(expectedPerms, nil)
				return expectedPerms
			},
			expectedError: "",
		},
		{
			name:       "ошибка при получении групп",
			categoryID: 2,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByCategoryIDMock.Return(nil, errors.New("get groups error"))
				return nil
			},
			expectedError: "get groups error",
		},
		{
			name:       "ошибка при получении ролей категории",
			categoryID: 3,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByCategoryIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByCategoryIDMock.Return(nil, errors.New("get roles error"))
				return nil
			},
			expectedError: "get roles error",
		},
		{
			name:       "ошибка при получении ролей группы",
			categoryID: 4,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByCategoryIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByCategoryIDMock.Return([]roleentity.Role{}, nil)
				mockRoleDomain.GetByGroupIDMock.Return(nil, errors.New("get group roles error"))
				return nil
			},
			expectedError: "get group roles error",
		},
		{
			name:       "ошибка при получении разрешений по роли",
			categoryID: 5,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByCategoryIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByCategoryIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return(nil, errors.New("get permissions error"))
				return nil
			},
			expectedError: "get permissions error",
		},
		{
			name:       "ошибка при получении разрешений по категории",
			categoryID: 6,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByCategoryIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByCategoryIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return([]permissionentity.Permission{}, nil)
				mockPermissionDomain.GetByCategoryIDMock.Return(nil, errors.New("get category permissions error"))
				return nil
			},
			expectedError: "get category permissions error",
		},
		{
			name:       "ошибка при получении финальных разрешений по ID",
			categoryID: 7,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByCategoryIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByCategoryIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return([]permissionentity.Permission{}, nil)
				mockPermissionDomain.GetByCategoryIDMock.Return([]permissionentity.Permission{}, nil)
				mockPermissionDomain.GetByIDsMock.Return(nil, errors.New("get final permissions error"))
				return nil
			},
			expectedError: "get final permissions error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockGroupDomain := groupmocks.NewGroupDomainServiceMock(t)
			mockRoleDomain := rolemocks.NewRoleDomainServiceMock(t)
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, mockGroupDomain, nil, mockPermissionDomain, mockRoleDomain, nil)

			expectedResults := tt.setupMocks(mockGroupDomain, mockRoleDomain, mockPermissionDomain)

			permissions, err := service.GetByCategoryID(tt.categoryID)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, expectedResults, permissions)
			}
		})
	}
}

func TestPermissionAppService_GetByRoleID(t *testing.T) {
	tests := []struct {
		name            string
		roleID          int64
		mockPermissions []permissionentity.Permission
		mockError       error
		expectedError   error
	}{
		{
			name:   "успешное получение разрешений по ID роли",
			roleID: 1,
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
				{ID: 2, Name: "perm2", Method: "POST"},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:            "ошибка при получении разрешений по ID роли",
			roleID:          2,
			mockPermissions: nil,
			mockError:       errors.New("get error"),
			expectedError:   errors.New("get error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.GetByRoleIDMock.Expect(tt.roleID).Return(tt.mockPermissions, tt.mockError)

			permissions, err := service.GetByRoleID(tt.roleID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermissions, permissions)
			}

			require.True(t, mockPermissionDomain.MinimockGetByRoleIDDone())
		})
	}
}

func TestPermissionAppService_GetByUserID(t *testing.T) {
	tests := []struct {
		name            string
		userID          int64
		isAdmin         bool
		mockPermissions []permissionentity.Permission
		mockPermError   error
		expectedError   error
		setupMocks      func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock)
	}{
		{
			name:    "успешное получение разрешений по ID пользователя (не админ)",
			userID:  1,
			isAdmin: false,
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
			},
			mockPermError: nil,
			expectedError: nil,
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetByUserIDMock.Expect(int64(1)).Return([]permissionentity.Permission{
					{ID: 1, Name: "perm1", Method: "GET"},
				}, nil)
			},
		},
		{
			name:            "ошибка при получении разрешений пользователя (не админ)",
			userID:          2,
			isAdmin:         false,
			mockPermissions: nil,
			mockPermError:   errors.New("permission not found"),
			expectedError:   errors.New("permission not found"),
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetByUserIDMock.Expect(int64(2)).Return(nil, errors.New("permission not found"))
			},
		},
		{
			name:    "успешное получение всех разрешений для админа",
			userID:  1,
			isAdmin: true,
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
				{ID: 2, Name: "perm2", Method: "POST"},
				{ID: 3, Name: "perm3", Method: "PUT"},
			},
			mockPermError: nil,
			expectedError: nil,
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllMock.Expect().Return([]permissionentity.Permission{
					{ID: 1, Name: "perm1", Method: "GET"},
					{ID: 2, Name: "perm2", Method: "POST"},
					{ID: 3, Name: "perm3", Method: "PUT"},
				}, nil)
			},
		},
		{
			name:            "ошибка при получении всех разрешений для админа",
			userID:          1,
			isAdmin:         true,
			mockPermissions: nil,
			mockPermError:   errors.New("get all permissions error"),
			expectedError:   errors.New("get all permissions error"),
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllMock.Expect().Return(nil, errors.New("get all permissions error"))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			tt.setupMocks(mockPermissionDomain)

			permissions, err := service.GetByUserID(tt.userID, tt.isAdmin)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermissions, permissions)
			}

			if tt.isAdmin {
				require.True(t, mockPermissionDomain.MinimockGetAllDone())
			} else {
				require.True(t, mockPermissionDomain.MinimockGetByUserIDDone())
			}
		})
	}
}

func TestPermissionAppService_GetByParticipantID(t *testing.T) {
	tests := []struct {
		name            string
		participantID   int64
		setupMocks      func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission
		expectedError   string
		expectedResults []permissionentity.Permission
	}{
		{
			name:          "успешное получение разрешений по ID участника",
			participantID: 1,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				expectedPerms := []permissionentity.Permission{
					{ID: 1, Name: "perm1", Method: "GET"},
					{ID: 2, Name: "perm2", Method: "POST"},
				}

				// Настраиваем моки для получения всех необходимых данных
				mockGroupDomain.GetByParticipantIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByParticipantIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return([]permissionentity.Permission{{ID: 1, Name: "perm1", Method: "GET"}}, nil)
				mockPermissionDomain.GetByIDsMock.Return(expectedPerms, nil)
				return expectedPerms
			},
			expectedError: "",
		},
		{
			name:          "ошибка при получении групп участника",
			participantID: 2,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByParticipantIDMock.Return(nil, errors.New("get groups error"))
				return nil
			},
			expectedError: "get groups error",
		},
		{
			name:          "ошибка при получении ролей группы участника",
			participantID: 3,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByParticipantIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return(nil, errors.New("get group roles error"))
				return nil
			},
			expectedError: "get group roles error",
		},
		{
			name:          "ошибка при получении ролей участника",
			participantID: 4,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByParticipantIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{}, nil)
				mockRoleDomain.GetByParticipantIDMock.Return(nil, errors.New("get participant roles error"))
				return nil
			},
			expectedError: "get participant roles error",
		},
		{
			name:          "ошибка при получении разрешений по роли участника",
			participantID: 5,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByParticipantIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockRoleDomain.GetByParticipantIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return(nil, errors.New("get permissions error"))
				return nil
			},
			expectedError: "get permissions error",
		},
		{
			name:          "ошибка при получении финальных разрешений по ID",
			participantID: 6,
			setupMocks: func(mockGroupDomain *groupmocks.GroupDomainServiceMock, mockRoleDomain *rolemocks.RoleDomainServiceMock, mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) []permissionentity.Permission {
				mockGroupDomain.GetByParticipantIDMock.Return([]groupentity.GroupWithStats{{GroupID: 1, GroupName: "group1"}}, nil)
				mockRoleDomain.GetByGroupIDMock.Return([]roleentity.Role{{ID: 1, Name: "role1"}}, nil)
				mockRoleDomain.GetByParticipantIDMock.Return([]roleentity.Role{{ID: 2, Name: "role2"}}, nil)
				mockPermissionDomain.GetByRoleIDMock.Return([]permissionentity.Permission{}, nil)
				mockPermissionDomain.GetByIDsMock.Return(nil, errors.New("get final permissions error"))
				return nil
			},
			expectedError: "get final permissions error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockGroupDomain := groupmocks.NewGroupDomainServiceMock(t)
			mockRoleDomain := rolemocks.NewRoleDomainServiceMock(t)
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, mockGroupDomain, nil, mockPermissionDomain, mockRoleDomain, nil)

			expectedResults := tt.setupMocks(mockGroupDomain, mockRoleDomain, mockPermissionDomain)

			permissions, err := service.GetByParticipantID(tt.participantID)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, expectedResults, permissions)
			}
		})
	}
}

func TestPermissionAppService_Update(t *testing.T) {
	tests := []struct {
		name           string
		permission     permissionentity.Permission
		mockPermission permissionentity.Permission
		mockError      error
		expectedError  error
	}{
		{
			name: "успешное обновление разрешения",
			permission: permissionentity.Permission{
				ID:     1,
				Name:   "test_permission",
				Method: "GET",
			},
			mockPermission: permissionentity.Permission{
				ID:     1,
				Name:   "test_permission_updated",
				Method: "GET",
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "ошибка при обновлении разрешения",
			permission: permissionentity.Permission{
				ID:     2,
				Name:   "test_permission",
				Method: "GET",
			},
			mockPermission: permissionentity.Permission{},
			mockError:      errors.New("update error"),
			expectedError:  errors.New("update error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.UpdateMock.Expect(tt.permission).Return(tt.mockPermission, tt.mockError)

			permission, err := service.Update(tt.permission)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockPermission, permission)
			}

			require.True(t, mockPermissionDomain.MinimockUpdateDone())
		})
	}
}

func TestPermissionAppService_GetAllWithCategories(t *testing.T) {
	tests := []struct {
		name               string
		mockPermissions    []permissionentity.Permission
		mockCategories     []categoryentity.Category
		mockCatPermissions []categoryentity.CategoryPermission
		mockErrors         map[string]error
		expectedResult     map[string][]permissionentity.CategoryActivity
		expectedError      error
	}{
		{
			name: "успешное получение разрешений с категориями",
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
				{ID: 2, Name: "perm2", Method: "POST"},
			},
			mockCategories: []categoryentity.Category{
				{ID: 1, Name: "cat1"},
				{ID: 2, Name: "cat2"},
			},
			mockCatPermissions: []categoryentity.CategoryPermission{
				{CategoryID: 1, PermissionID: 1},
				{CategoryID: 2, PermissionID: 2},
			},
			mockErrors: map[string]error{},
			expectedResult: map[string][]permissionentity.CategoryActivity{
				"perm1": {{ID: 1, Name: "cat1", IsActive: true}, {ID: 2, Name: "cat2", IsActive: false}},
				"perm2": {{ID: 1, Name: "cat1", IsActive: false}, {ID: 2, Name: "cat2", IsActive: true}},
			},
			expectedError: nil,
		},
		{
			name:               "ошибка при получении разрешений",
			mockPermissions:    nil,
			mockCategories:     nil,
			mockCatPermissions: nil,
			mockErrors: map[string]error{
				"permissions": errors.New("get permissions error"),
			},
			expectedResult: nil,
			expectedError:  errors.New("get permissions error"),
		},
		{
			name: "ошибка при получении категорий",
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
			},
			mockCategories:     nil,
			mockCatPermissions: nil,
			mockErrors: map[string]error{
				"categories": errors.New("get categories error"),
			},
			expectedResult: nil,
			expectedError:  errors.New("get categories error"),
		},
		{
			name: "ошибка при получении разрешений категорий",
			mockPermissions: []permissionentity.Permission{
				{ID: 1, Name: "perm1", Method: "GET"},
			},
			mockCategories: []categoryentity.Category{
				{ID: 1, Name: "cat1"},
			},
			mockCatPermissions: nil,
			mockErrors: map[string]error{
				"catPermissions": errors.New("get category permissions error"),
			},
			expectedResult: nil,
			expectedError:  errors.New("get category permissions error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			mockCategoryDomain := categorymocks.NewCategoryDomainServiceMock(t)
			service := NewPermissionAppService(mockCategoryDomain, nil, nil, mockPermissionDomain, nil, nil)

			mockPermissionDomain.GetAllMock.Expect().Return(tt.mockPermissions, tt.mockErrors["permissions"])

			if tt.mockErrors["permissions"] == nil {
				mockCategoryDomain.GetAllMock.Expect().Return(tt.mockCategories, tt.mockErrors["categories"])

				if tt.mockErrors["categories"] == nil {
					mockPermissionDomain.GetAllCategoryPermissionsMock.Expect().Return(tt.mockCatPermissions, tt.mockErrors["catPermissions"])
				}
			}

			result, err := service.GetAllWithCategories()

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}

			require.True(t, mockPermissionDomain.MinimockGetAllDone())
			if tt.mockErrors["permissions"] == nil {
				require.True(t, mockCategoryDomain.MinimockGetAllDone())
				if tt.mockErrors["categories"] == nil {
					require.True(t, mockPermissionDomain.MinimockGetAllCategoryPermissionsDone())
				}
			}
		})
	}
}

func TestPermissionAppService_UpdateCategoryPermissions(t *testing.T) {
	tests := []struct {
		name          string
		permissions   []permissionentity.PermissionWithCategories
		setupMocks    func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock)
		expectedError string
	}{
		{
			name: "успешное обновление разрешений категорий",
			permissions: []permissionentity.PermissionWithCategories{
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 1, Name: "cat1", IsActive: true}},
				},
			},
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					[]categoryentity.CategoryPermission{{CategoryID: 2, PermissionID: 2}},
					nil,
				)
				mockPermissionDomain.GetIDsByNameMock.Return([]int64{1}, nil)
				mockPermissionDomain.AssignPermissionToCategoryMock.Return(nil)
			},
			expectedError: "",
		},
		{
			name:        "ошибка при получении существующих разрешений категорий",
			permissions: nil,
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					nil,
					errors.New("get category permissions error"),
				)
			},
			expectedError: "get category permissions error",
		},
		{
			name: "ошибка при обработке разрешения",
			permissions: []permissionentity.PermissionWithCategories{
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 1, Name: "cat1", IsActive: true}},
				},
			},
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					[]categoryentity.CategoryPermission{{CategoryID: 1, PermissionID: 1}},
					nil,
				)
				mockPermissionDomain.GetIDsByNameMock.Return(nil, errors.New("get permission IDs error"))
			},
			expectedError: "get permission IDs error",
		},
		{
			name: "ошибка при обработке категории - назначение разрешения",
			permissions: []permissionentity.PermissionWithCategories{
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 1, Name: "cat1", IsActive: true}},
				},
			},
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					[]categoryentity.CategoryPermission{{CategoryID: 2, PermissionID: 2}},
					nil,
				)
				mockPermissionDomain.GetIDsByNameMock.Return([]int64{1}, nil)
				mockPermissionDomain.AssignPermissionToCategoryMock.Return(errors.New("assign permission error"))
			},
			expectedError: "assign permission error",
		},
		{
			name: "ошибка при обработке категории - снятие разрешения",
			permissions: []permissionentity.PermissionWithCategories{
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 1, Name: "cat1", IsActive: false}},
				},
			},
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					[]categoryentity.CategoryPermission{{CategoryID: 1, PermissionID: 1}},
					nil,
				)
				mockPermissionDomain.GetIDsByNameMock.Return([]int64{1}, nil)
				mockPermissionDomain.UnassignPermissionFromCategoryMock.Return(errors.New("unassign permission error"))
			},
			expectedError: "unassign permission error",
		},
		{
			name: "ошибка при получении ID разрешений - пустой список",
			permissions: []permissionentity.PermissionWithCategories{
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 1, Name: "cat1", IsActive: true}},
				},
			},
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					[]categoryentity.CategoryPermission{{CategoryID: 1, PermissionID: 1}},
					nil,
				)
				mockPermissionDomain.GetIDsByNameMock.Return([]int64{}, nil)
			},
			expectedError: "no permissions found with the provided name",
		},
		{
			name: "использование кэшированных ID разрешений",
			permissions: []permissionentity.PermissionWithCategories{
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 1, Name: "cat1", IsActive: true}},
				},
				{
					PermissionName: "perm1",
					Categories:     []permissionentity.CategoryActivity{{ID: 2, Name: "cat2", IsActive: true}},
				},
			},
			setupMocks: func(mockPermissionDomain *permissionmocks.PermissionDomainServiceMock) {
				mockPermissionDomain.GetAllCategoryPermissionsMock.Return(
					[]categoryentity.CategoryPermission{{CategoryID: 3, PermissionID: 3}},
					nil,
				)
				// GetIDsByName должен вызываться только один раз для "perm1"
				mockPermissionDomain.GetIDsByNameMock.When("perm1").Then([]int64{1}, nil)

				// Настраиваем ожидания для обоих вызовов AssignPermissionToCategory
				mockPermissionDomain.AssignPermissionToCategoryMock.When(1, int64(1)).Then(nil)
				mockPermissionDomain.AssignPermissionToCategoryMock.When(2, int64(1)).Then(nil)
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPermissionDomain := permissionmocks.NewPermissionDomainServiceMock(t)
			service := NewPermissionAppService(nil, nil, nil, mockPermissionDomain, nil, nil)

			tt.setupMocks(mockPermissionDomain)

			err := service.UpdateCategoryPermissions(tt.permissions)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPopulatePermissionNameToCategoryIDs(t *testing.T) {
	t.Run("обработка отсутствующего имени разрешения", func(t *testing.T) {
		// Подготовка данных
		categoryPermissions := []categoryentity.CategoryPermission{
			{CategoryID: 1, PermissionID: 1},
			{CategoryID: 2, PermissionID: 2},
			{CategoryID: 3, PermissionID: 999}, // ID, которого нет в мапе permissionIDToName
		}

		permissionIDToName := map[int64]string{
			1: "perm1",
			2: "perm2",
			// ID 999 отсутствует
		}

		permissionNameToCategoryIDs := map[string]map[int64]struct{}{
			"perm1": make(map[int64]struct{}),
			"perm2": make(map[int64]struct{}),
		}

		// Вызов тестируемой функции
		result := populatePermissionNameToCategoryIDs(categoryPermissions, permissionIDToName, permissionNameToCategoryIDs)

		// Проверка результатов
		assert.Equal(t, 2, len(result))
		assert.Contains(t, result, "perm1")
		assert.Contains(t, result, "perm2")
		assert.Contains(t, result["perm1"], int64(1))
		assert.Contains(t, result["perm2"], int64(2))
		assert.Equal(t, 1, len(result["perm1"]))
		assert.Equal(t, 1, len(result["perm2"]))
	})
}
