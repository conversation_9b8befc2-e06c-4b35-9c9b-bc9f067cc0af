package service

import (
	"errors"

	categoryaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/aggregate"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categoryservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	participantaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/aggregate"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	permissionservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type PermissionAppService struct {
	categoryDomain    categoryservice.CategoryDomainService
	groupDomain       groupservice.GroupDomainService
	participantDomain participantservice.ParticipantDomainService
	permissionDomain  permissionservice.PermissionDomainService
	roleDomain        roleservice.RoleDomainService
	userDomain        userservice.UserDomainService
}

func NewPermissionAppService(
	categoryDomain categoryservice.CategoryDomainService,
	groupDomain groupservice.GroupDomainService,
	participantDomain participantservice.ParticipantDomainService,
	permissionDomain permissionservice.PermissionDomainService,
	roleDomain roleservice.RoleDomainService,
	userDomain userservice.UserDomainService,
) *PermissionAppService {
	return &PermissionAppService{
		categoryDomain:    categoryDomain,
		groupDomain:       groupDomain,
		participantDomain: participantDomain,
		permissionDomain:  permissionDomain,
		roleDomain:        roleDomain,
		userDomain:        userDomain,
	}
}

func (s *PermissionAppService) Create(permission permissionentity.Permission) (permissionentity.Permission, error) {
	return s.permissionDomain.Create(permission)
}

func (s *PermissionAppService) GetAll() ([]permissionentity.Permission, error) {
	return s.permissionDomain.GetAll()
}

func (s *PermissionAppService) GetByID(id int64) (permissionentity.Permission, error) {
	return s.permissionDomain.GetByID(id)
}

func (s *PermissionAppService) GetByNameAndMethod(name, method string) (permissionentity.Permission, error) {
	return s.permissionDomain.GetByNameAndMethod(name, method)
}

func (s *PermissionAppService) GetPermissionsByCategoryID(categoryID int64) ([]permissionentity.Permission, error) {
	return s.permissionDomain.GetByCategoryID(categoryID)
}

func (s *PermissionAppService) GetByCategoryID(categoryID int64) ([]permissionentity.Permission, error) {

	aggregate := categoryaggregate.NewCategoryPermissionAggregate(categoryID)

	groups, err := s.groupDomain.GetByCategoryID(categoryID)
	if err != nil {
		return nil, err
	}

	roles, err := s.roleDomain.GetByCategoryID(categoryID)
	if err != nil {
		return nil, err
	}

	for _, group := range groups {
		groupRoles, err := s.roleDomain.GetByGroupID(group.GroupID)
		if err != nil {
			return nil, err
		}
		for _, role := range groupRoles {
			aggregate.AddRoleID(role.ID)
		}
	}

	for _, role := range roles {
		aggregate.AddRoleID(role.ID)
	}

	for _, roleID := range aggregate.GetRoleIDs() {
		permissions, err := s.permissionDomain.GetByRoleID(roleID)
		if err != nil {
			return nil, err
		}
		for _, perm := range permissions {
			aggregate.AddPermissionID(perm.ID)
		}
	}

	permissions, err := s.permissionDomain.GetByCategoryID(categoryID)
	if err != nil {
		return nil, err
	}

	for _, perm := range permissions {
		aggregate.AddPermissionID(perm.ID)
	}

	return s.permissionDomain.GetByIDs(aggregate.GetPermissionIDs())
}

func (s *PermissionAppService) GetByRoleID(roleID int64) ([]permissionentity.Permission, error) {
	return s.permissionDomain.GetByRoleID(roleID)
}

func (s *PermissionAppService) GetByUserID(userID int64, isAdmin bool) ([]permissionentity.Permission, error) {
	if isAdmin {
		return s.permissionDomain.GetAll()
	}

	return s.permissionDomain.GetByUserID(userID)
}

func (s *PermissionAppService) GetByParticipantID(participantID int64) ([]permissionentity.Permission, error) {
	aggregate := participantaggregate.NewParticipantPermissionAggregate(participantID)

	groups, err := s.groupDomain.GetByParticipantID(participantID)
	if err != nil {
		return nil, err
	}

	for _, group := range groups {
		groupRoles, err := s.roleDomain.GetByGroupID(group.GroupID)
		if err != nil {
			return nil, err
		}
		for _, role := range groupRoles {
			aggregate.AddRoleID(role.ID)
		}
	}

	participantRoles, err := s.roleDomain.GetByParticipantID(participantID)
	if err != nil {
		return nil, err
	}
	for _, role := range participantRoles {
		aggregate.AddRoleID(role.ID)
	}

	for _, roleID := range aggregate.GetRoleIDs() {
		permissions, err := s.permissionDomain.GetByRoleID(roleID)
		if err != nil {
			return nil, err
		}
		for _, perm := range permissions {
			aggregate.AddPermissionID(perm.ID)
		}
	}

	return s.permissionDomain.GetByIDs(aggregate.GetPermissionIDs())
}

func (s *PermissionAppService) Update(permission permissionentity.Permission) (permissionentity.Permission, error) {
	return s.permissionDomain.Update(permission)
}

func (s *PermissionAppService) GetAllWithCategories() (map[string][]permissionentity.CategoryActivity, error) {

	permissions, err := s.permissionDomain.GetAll()
	if err != nil {
		return nil, err
	}

	categories, err := s.categoryDomain.GetAll()
	if err != nil {
		return nil, err
	}

	categoryPermissions, err := s.permissionDomain.GetAllCategoryPermissions()
	if err != nil {
		return nil, err
	}

	permissionIDToName := mapPermissionIDToName(permissions)
	permissionNameToCategoryIDs := initializePermissionNameToCategoryIDs(permissions)
	permissionNameToCategoryIDs = populatePermissionNameToCategoryIDs(
		categoryPermissions,
		permissionIDToName,
		permissionNameToCategoryIDs,
	)

	result := buildPermissionCategoriesMap(permissionNameToCategoryIDs, categories)

	return result, nil
}

func (s *PermissionAppService) UpdateCategoryPermissions(permissions []permissionentity.PermissionWithCategories) error {
	aggregate := categoryaggregate.NewCategoryPermissionAggregate(0)

	existingRelations, err := s.getExistingCategoryPermissions()
	if err != nil {
		return err
	}
	aggregate.CacheExistingPermissions(existingRelations)

	for _, perm := range permissions {
		if err := s.processPermission(aggregate, perm); err != nil {
			return err
		}
	}
	return nil
}

func (s *PermissionAppService) getExistingCategoryPermissions() (map[int64]map[int64]struct{}, error) {
	allRelations, err := s.permissionDomain.GetAllCategoryPermissions()
	if err != nil {
		return nil, err
	}

	result := make(map[int64]map[int64]struct{})
	for _, rel := range allRelations {
		if result[rel.CategoryID] == nil {
			result[rel.CategoryID] = make(map[int64]struct{})
		}
		result[rel.CategoryID][rel.PermissionID] = struct{}{}
	}
	return result, nil
}

func (s *PermissionAppService) processPermission(agg *categoryaggregate.CategoryPermissionAggregate, perm permissionentity.PermissionWithCategories) error {
	permIDs, err := s.resolvePermissionIDs(agg, perm.PermissionName)
	if err != nil {
		return err
	}

	for _, category := range perm.Categories {
		if err := s.processCategory(agg, category, permIDs); err != nil {
			return err
		}
	}
	return nil
}

func (s *PermissionAppService) resolvePermissionIDs(agg *categoryaggregate.CategoryPermissionAggregate, name string) ([]int64, error) {
	if ids, exists := agg.GetCachedPermissionIDs(name); exists {
		return ids, nil
	}

	ids, err := s.permissionDomain.GetIDsByName(name)
	if err != nil {
		return nil, err
	}
	if len(ids) == 0 {
		return nil, errors.New("no permissions found with the provided name")
	}

	agg.CachePermissionIDs(name, ids)
	return ids, nil
}

func (s *PermissionAppService) processCategory(agg *categoryaggregate.CategoryPermissionAggregate, category permissionentity.CategoryActivity, permIDs []int64) error {
	for _, permID := range permIDs {
		hasPermission := agg.HasExistingPermission(category.ID, permID)

		if category.IsActive && !hasPermission {
			if err := s.permissionDomain.AssignPermissionToCategory(category.ID, permID); err != nil {
				return err
			}
		} else if !category.IsActive && hasPermission {
			if err := s.permissionDomain.UnassignPermissionFromCategory(category.ID, permID); err != nil {
				return err
			}
		}
	}
	return nil
}

func mapPermissionIDToName(permissions []permissionentity.Permission) map[int64]string {
	permissionIDToName := make(map[int64]string, len(permissions))
	for _, perm := range permissions {
		permissionIDToName[perm.ID] = perm.Name
	}
	return permissionIDToName
}

func initializePermissionNameToCategoryIDs(permissions []permissionentity.Permission) map[string]map[int64]struct{} {
	permissionNameToCategoryIDs := make(map[string]map[int64]struct{}, len(permissions))
	for _, perm := range permissions {
		permissionNameToCategoryIDs[perm.Name] = make(map[int64]struct{})
	}
	return permissionNameToCategoryIDs
}

func populatePermissionNameToCategoryIDs(
	categoryPermissions []categoryentity.CategoryPermission,
	permissionIDToName map[int64]string,
	permissionNameToCategoryIDs map[string]map[int64]struct{},
) map[string]map[int64]struct{} {
	for _, cp := range categoryPermissions {
		permName, exists := permissionIDToName[cp.PermissionID]
		if !exists {
			continue
		}
		permissionNameToCategoryIDs[permName][cp.CategoryID] = struct{}{}
	}
	return permissionNameToCategoryIDs
}

func buildPermissionCategoriesMap(
	permissionNameToCategoryIDs map[string]map[int64]struct{},
	categories []categoryentity.Category,
) map[string][]permissionentity.CategoryActivity {
	result := make(map[string][]permissionentity.CategoryActivity, len(permissionNameToCategoryIDs))
	for permName, categorySet := range permissionNameToCategoryIDs {
		checkedCategories := make([]permissionentity.CategoryActivity, 0, len(categories))
		for _, cat := range categories {
			_, isChecked := categorySet[cat.ID]
			checkedCategories = append(checkedCategories, permissionentity.CategoryActivity{
				ID:       cat.ID,
				Name:     cat.Name,
				IsActive: isChecked,
			})
		}
		result[permName] = checkedCategories
	}
	return result
}
