package service

import (
	"sort"
	"strings"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func compareUsers(u1, u2 userentity.AdminUser, sortField string) bool {
	switch sortField {
	case sharedentity.SortFieldFullName.String():
		return strings.ToLower(u1.FullName) < strings.ToLower(u2.FullName)
	case sharedentity.SortFieldCategory.String():
		return u1.CategoryID < u2.CategoryID
	case sharedentity.SortFieldCreatedAt.String():
		return u1.CreatedAt.Before(u2.CreatedAt)
	case sharedentity.SortFieldProduct.String():
		return compareUsersByProduct(u1, u2)
	default:
		return strings.ToLower(u1.FullName) < strings.ToLower(u2.FullName)
	}
}

func compareUsersByProduct(u1, u2 userentity.AdminUser) bool {
	hasProducts1 := len(u1.Products) > 0
	hasProducts2 := len(u2.Products) > 0

	// One of the users does not have products
	if !hasProducts1 && hasProducts2 {
		return true
	}
	if hasProducts1 && !hasProducts2 {
		return false
	}

	// At this point, both groups are in the same state regarding products
	if !hasProducts1 { // which means !hasProducts2 is also true
		return strings.ToLower(u1.FullName) < strings.ToLower(u2.FullName)
	}

	// Both users have products - compare the first product
	return u1.Products[0].TechName < u2.Products[0].TechName
}

func filterUsers(users []userentity.AdminUser, query query.AdminUser) []userentity.AdminUser {

	filtered := make([]userentity.AdminUser, 0, len(users))

	var allowedProductIDs map[int64]bool
	if len(query.ProductIDs) > 0 {
		allowedProductIDs = make(map[int64]bool, len(query.ProductIDs))
		for _, id := range query.ProductIDs {
			allowedProductIDs[id] = true
		}
	}

	var allowedCategoryIDs map[int64]bool
	if len(query.CategoryIDs) > 0 {
		allowedCategoryIDs = make(map[int64]bool, len(query.CategoryIDs))
		for _, id := range query.CategoryIDs {
			allowedCategoryIDs[id] = true
		}
	}

	var searchLower string
	if query.Search != nil && *query.Search != "" {
		searchLower = strings.ToLower(*query.Search)
	}

	for _, user := range users {
		keep := true

		// Search filter
		if searchLower != "" {
			if !strings.Contains(strings.ToLower(user.Email), searchLower) &&
				!strings.Contains(strings.ToLower(user.FullName), searchLower) {
				keep = false
			}
		}

		// IsAdmin filter
		if keep && query.IsAdmin != nil {
			keep = user.IsAdmin == *query.IsAdmin
		}

		// Category filter
		if keep && allowedCategoryIDs != nil {
			keep = allowedCategoryIDs[user.CategoryID]
		}

		// Product filter
		if keep && allowedProductIDs != nil {
			hasProduct := false
			for _, product := range user.Products {
				if allowedProductIDs[product.ID] {
					hasProduct = true
					break
				}
			}
			keep = hasProduct
		}

		if keep {
			filtered = append(filtered, user)
		}
	}

	return filtered
}

func sortUsers(users []userentity.AdminUser, query query.AdminUser) []userentity.AdminUser {
	if query.Sort == nil {
		return users
	}

	sorted := make([]userentity.AdminUser, len(users))
	copy(sorted, users)

	sortField := *query.Sort
	orderAsc := true
	if query.Order != nil && *query.Order == sharedentity.SortOrderDescend.String() {
		orderAsc = false
	}

	sort.SliceStable(sorted, func(i, j int) bool {
		less := compareUsers(sorted[i], sorted[j], sortField)

		if !orderAsc {
			return !less
		}
		return less
	})

	return sorted
}
