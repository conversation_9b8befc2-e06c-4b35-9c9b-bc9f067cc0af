package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	useraggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/aggregate"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func TestNewUserAppService(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	require.NotNil(t, svc)
	require.Equal(t, mockGroup, svc.groupDomain)
	require.Equal(t, mockParticipant, svc.participantDomain)
	require.Equal(t, mockProduct, svc.productDomain)
	require.Equal(t, mockRole, svc.roleDomain)
	require.Equal(t, mockUser, svc.userDomain)
}

func TestUserAppService_GetAllAsAdmin(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "User One",
			CategoryID: 1,
			Position:   "Developer",
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products: []productentity.ProductBasic{
				{ID: 1, TechName: "product1"},
			},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "User Two",
			CategoryID: 2,
			Position:   "Manager",
			IsAdmin:    true,
			CreatedAt:  time.Now(),
			Products: []productentity.ProductBasic{
				{ID: 2, TechName: "product2"},
			},
		},
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(query.AdminUser{})
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(2), result.Total)
	require.Equal(t, int64(1), result.Items[0].ID)
	require.Equal(t, "<EMAIL>", result.Items[0].Email)
}

// Helper функция для создания всех моков
func createMocks(t *testing.T) (*groupmocks.GroupDomainServiceMock, *participantmocks.ParticipantDomainServiceMock, *productmocks.ProductDomainServiceMock, *rolemocks.RoleDomainServiceMock, *usermocks.UserDomainServiceMock) {
	return groupmocks.NewGroupDomainServiceMock(t),
		participantmocks.NewParticipantDomainServiceMock(t),
		productmocks.NewProductDomainServiceMock(t),
		rolemocks.NewRoleDomainServiceMock(t),
		usermocks.NewUserDomainServiceMock(t)
}

func TestUserAppService_GetAllAsAdmin_WithFilters(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	// Все пользователи, которые возвращает домен-сервис (до фильтрации)
	allUsers := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "Admin User",
			CategoryID: 1,
			Position:   "Administrator",
			IsAdmin:    true,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "Regular User",
			CategoryID: 2,
			Position:   "Developer",
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 2, TechName: "product2"}},
		},
	}

	search := "admin"
	isAdmin := true
	q := query.AdminUser{
		Search:  &search,
		IsAdmin: &isAdmin,
	}

	// Мок должен вернуть все данные, фильтрация произойдет в application-сервисе
	mockUser.GetAllAsAdminMock.Expect().Return(allUsers, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)

	// Проверяем, что фильтрация работает корректно:
	// должен остаться только 1 пользователь (admin с isAdmin=true и search="admin")
	require.NoError(t, err)
	require.Len(t, result.Items, 1)
	require.Equal(t, int64(1), result.Total)
	require.Equal(t, int64(1), result.Items[0].ID)
	require.Equal(t, "<EMAIL>", result.Items[0].Email)
	require.True(t, result.Items[0].IsAdmin)

	// Проверяем дополнительные аспекты маппинга
	require.Equal(t, allUsers[0].Position, result.Items[0].Position)
	require.Equal(t, allUsers[0].CategoryID, result.Items[0].CategoryID)
	require.Equal(t, len(allUsers[0].Products), len(result.Items[0].Products))

	// Проверяем, что домен-сервис был вызван ровно один раз
	require.Equal(t, uint64(1), mockUser.GetAllAsAdminAfterCounter())
}

func TestUserAppService_GetAllAsAdmin_WithPagination(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "User Three", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 4, Email: "<EMAIL>", FullName: "User Four", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 5, Email: "<EMAIL>", FullName: "User Five", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	limit := int64(2)
	offset := int64(1)
	q := query.AdminUser{
		Limit:  &limit,
		Offset: &offset,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(5), result.Total)
	require.Equal(t, int64(2), result.Limit)
	require.Equal(t, int64(1), result.Offset)
	require.Equal(t, int64(2), result.Items[0].ID)
	require.Equal(t, int64(3), result.Items[1].ID)
}

func TestUserAppService_GetAllAsAdmin_WithSorting(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Charlie", CategoryID: 3, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "Alice", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "Bob", CategoryID: 2, IsAdmin: false, CreatedAt: time.Now()},
	}

	sort := "fullName"
	order := "ascend"
	q := query.AdminUser{
		Sort:  &sort,
		Order: &order,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 3)
	require.Equal(t, "Alice", result.Items[0].FullName)
	require.Equal(t, "Bob", result.Items[1].FullName)
	require.Equal(t, "Charlie", result.Items[2].FullName)
}

func TestUserAppService_GetAllAsAdmin_WithCategoryFilter(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 2, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "User Three", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	q := query.AdminUser{
		CategoryIDs: []int64{1},
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(2), result.Total)
	require.Equal(t, int64(1), result.Items[0].CategoryID)
	require.Equal(t, int64(1), result.Items[1].CategoryID)
}

func TestUserAppService_GetAllAsAdmin_WithProductFilter(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "User One",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "User Two",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 2, TechName: "product2"}},
		},
		{
			ID:         3,
			Email:      "<EMAIL>",
			FullName:   "User Three",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
	}

	q := query.AdminUser{
		ProductIDs: []int64{1},
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(2), result.Total)
	require.Equal(t, int64(1), result.Items[0].ID)
	require.Equal(t, int64(3), result.Items[1].ID)
}

func TestUserAppService_GetAllAsAdmin_EmptyResult(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	var users []userentity.AdminUser

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(query.AdminUser{})
	require.NoError(t, err)
	require.Len(t, result.Items, 0)
	require.Equal(t, int64(0), result.Total)
}

func TestUserAppService_GetAllAsAdmin_SearchByEmail(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "John Doe", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "Jane Smith", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "Bob Johnson", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	search := "john"
	q := query.AdminUser{
		Search: &search,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(2), result.Total)
}

func TestUserAppService_GetAllAsAdmin_SearchByFullName(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Alice Developer", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "Bob Manager", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "Charlie Developer", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	search := "developer"
	q := query.AdminUser{
		Search: &search,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(2), result.Total)
}

func TestUserAppService_GetAllAsAdmin_SortByCategory(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 3, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "User Three", CategoryID: 2, IsAdmin: false, CreatedAt: time.Now()},
	}

	sort := "category"
	order := "ascend"
	q := query.AdminUser{
		Sort:  &sort,
		Order: &order,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 3)
	require.Equal(t, int64(1), result.Items[0].CategoryID)
	require.Equal(t, int64(2), result.Items[1].CategoryID)
	require.Equal(t, int64(3), result.Items[2].CategoryID)
}

func TestUserAppService_GetAllAsAdmin_SortByCreatedAt(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	now := time.Now()
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 1, IsAdmin: false, CreatedAt: now.Add(2 * time.Hour)},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 1, IsAdmin: false, CreatedAt: now},
		{ID: 3, Email: "<EMAIL>", FullName: "User Three", CategoryID: 1, IsAdmin: false, CreatedAt: now.Add(1 * time.Hour)},
	}

	sort := "createdAt"
	order := "ascend"
	q := query.AdminUser{
		Sort:  &sort,
		Order: &order,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 3)
	require.Equal(t, int64(2), result.Items[0].ID)
	require.Equal(t, int64(3), result.Items[1].ID)
	require.Equal(t, int64(1), result.Items[2].ID)
}

func TestUserAppService_GetAllAsAdmin_SortByProduct(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "User One",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "zebra"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "User Two",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 2, TechName: "alpha"}},
		},
		{
			ID:         3,
			Email:      "<EMAIL>",
			FullName:   "User Three",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{},
		},
	}

	sort := "product"
	order := "ascend"
	q := query.AdminUser{
		Sort:  &sort,
		Order: &order,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 3)
	require.Equal(t, int64(3), result.Items[0].ID)
	require.Equal(t, int64(2), result.Items[1].ID)
	require.Equal(t, int64(1), result.Items[2].ID)
}

func TestUserAppService_GetAllAsAdmin_DescendingOrder(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Alice", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "Bob", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "Charlie", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	sort := "fullName"
	order := "descend"
	q := query.AdminUser{
		Sort:  &sort,
		Order: &order,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 3)
	require.Equal(t, "Charlie", result.Items[0].FullName)
	require.Equal(t, "Bob", result.Items[1].FullName)
	require.Equal(t, "Alice", result.Items[2].FullName)
}

func TestUserAppService_GetAllAsAdmin_CombinedFilters(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "Admin One",
			CategoryID: 1,
			IsAdmin:    true,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "Admin Two",
			CategoryID: 2,
			IsAdmin:    true,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         3,
			Email:      "<EMAIL>",
			FullName:   "Regular User",
			CategoryID: 1,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
	}

	search := "admin"
	isAdmin := true
	limit := int64(1)
	offset := int64(0)
	q := query.AdminUser{
		Search:      &search,
		IsAdmin:     &isAdmin,
		CategoryIDs: []int64{1},
		ProductIDs:  []int64{1},
		Limit:       &limit,
		Offset:      &offset,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(users, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 1)
	require.Equal(t, int64(1), result.Total)
	require.Equal(t, int64(1), result.Items[0].ID)
	require.Equal(t, "<EMAIL>", result.Items[0].Email)
	require.True(t, result.Items[0].IsAdmin)
	require.Equal(t, int64(1), result.Items[0].CategoryID)
}

func TestUserAppService_GetAllAsAdmin_Error(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	expectedError := errors.New("database error")
	mockUser.GetAllAsAdminMock.Expect().Return(nil, expectedError)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	_, err := svc.GetAllAsAdmin(query.AdminUser{})
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_GetAsAdminByID_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	now := time.Now()
	photoBytes := []byte("photo")

	user := userentity.User{
		ID:          userID,
		CategoryID:  10,
		Email:       "<EMAIL>",
		FullName:    "Test User",
		Position:    "Developer",
		IsAdmin:     false,
		LastLoginAt: &now,
		Photo:       &photoBytes,
		CreatedAt:   now,
	}

	groups := []groupentity.GroupWithProduct{
		{
			ID:        1,
			Name:      "Test Group",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   &productentity.Product{ID: 100},
		},
	}

	roles := []roleentity.RoleWithProduct{
		{
			ID:        2,
			Name:      "Test Role",
			IsSystem:  false,
			ActiveFlg: true,
			Product:   &productentity.Product{ID: 200},
		},
	}

	iid := "test-iid"
	products := []productentity.ProductBasic{
		{
			ID:       300,
			IID:      &iid,
			Name:     "Test Product",
			TechName: "test_product",
		},
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(roles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(products, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAsAdminByID(userID)
	require.NoError(t, err)
	require.Equal(t, user, result.User)
	require.Equal(t, groups, result.Groups)
	require.Equal(t, roles, result.Roles)
	require.Equal(t, products, result.Products)
}

func TestUserAppService_GetAsAdminByID_UserNotFound(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(999)
	expectedError := errors.New("user not found")

	mockUser.GetByIDMock.Expect(userID).Return(userentity.User{}, expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	_, err := svc.GetAsAdminByID(userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_GetAsAdminByID_GroupsError(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	user := userentity.User{ID: userID, Email: "<EMAIL>"}
	expectedError := errors.New("groups error")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(nil, expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	_, err := svc.GetAsAdminByID(userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_GetAsAdminByID_RolesError(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	user := userentity.User{ID: userID, Email: "<EMAIL>"}
	var groups []groupentity.GroupWithProduct
	expectedError := errors.New("roles error")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(nil, expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	_, err := svc.GetAsAdminByID(userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_GetAsAdminByID_ProductsError(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	user := userentity.User{ID: userID, Email: "<EMAIL>"}
	var groups []groupentity.GroupWithProduct
	var roles []roleentity.RoleWithProduct
	expectedError := errors.New("products error")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(roles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(nil, expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	_, err := svc.GetAsAdminByID(userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_GetAsAdminByID_EmptyData(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	user := userentity.User{
		ID:       userID,
		Email:    "<EMAIL>",
		FullName: "Test User",
	}

	var emptyGroups []groupentity.GroupWithProduct
	var emptyRoles []roleentity.RoleWithProduct
	var emptyProducts []productentity.ProductBasic

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(emptyGroups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(emptyRoles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(emptyProducts, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAsAdminByID(userID)
	require.NoError(t, err)
	require.Equal(t, user, result.User)
	require.Empty(t, result.Groups)
	require.Empty(t, result.Roles)
	require.Empty(t, result.Products)
}

func TestUserAppService_GetAsAdminByID_WithNilProductInGroup(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	user := userentity.User{ID: userID, Email: "<EMAIL>"}

	groups := []groupentity.GroupWithProduct{
		{
			ID:        1,
			Name:      "Group without product",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   nil,
		},
	}

	var roles []roleentity.RoleWithProduct
	var products []productentity.ProductBasic

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(roles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(products, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAsAdminByID(userID)
	require.NoError(t, err)
	require.Equal(t, user, result.User)
	require.Len(t, result.Groups, 1)
	require.Equal(t, "Group without product", result.Groups[0].Name)
	require.Nil(t, result.Groups[0].Product)
}

func TestUserAppService_GetAsAdminByID_WithNilProductInRole(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	user := userentity.User{ID: userID, Email: "<EMAIL>"}

	var groups []groupentity.GroupWithProduct
	roles := []roleentity.RoleWithProduct{
		{
			ID:        1,
			Name:      "Role without product",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   nil,
		},
	}
	var products []productentity.ProductBasic

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(roles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(products, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAsAdminByID(userID)
	require.NoError(t, err)
	require.Equal(t, user, result.User)
	require.Len(t, result.Roles, 1)
	require.Equal(t, "Role without product", result.Roles[0].Name)
	require.Nil(t, result.Roles[0].Product)
}

func TestUserAppService_GetAsAdminByID_CompleteUserData(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	now := time.Now()
	photoBytes := []byte("user photo data")

	user := userentity.User{
		ID:                  userID,
		CategoryID:          5,
		Email:               "<EMAIL>",
		FullName:            "Complete User",
		Position:            "Senior Developer",
		IsAdmin:             true,
		ActiveFlg:           true,
		LastActiveProductID: 100,
		LastLoginAt:         &now,
		CreatedAt:           now,
		Photo:               &photoBytes,
	}

	groups := []groupentity.GroupWithProduct{
		{
			ID:        10,
			Name:      "Admin Group",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   &productentity.Product{ID: 100, Name: "Admin Product"},
		},
		{
			ID:        20,
			Name:      "Custom Group",
			IsSystem:  false,
			ActiveFlg: false,
			Product:   nil,
		},
	}

	roles := []roleentity.RoleWithProduct{
		{
			ID:        30,
			Name:      "Admin Role",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   &productentity.Product{ID: 200, Name: "Role Product"},
		},
		{
			ID:        40,
			Name:      "User Role",
			IsSystem:  false,
			ActiveFlg: true,
			Product:   nil,
		},
	}

	iid1 := "prod-1"
	iid2 := "prod-2"
	products := []productentity.ProductBasic{
		{
			ID:       300,
			IID:      &iid1,
			Name:     "Product One",
			TechName: "product_one",
		},
		{
			ID:       400,
			IID:      &iid2,
			Name:     "Product Two",
			TechName: "product_two",
		},
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(roles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(products, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAsAdminByID(userID)
	require.NoError(t, err)

	// Проверяем пользователя
	require.Equal(t, user, result.User)
	require.Equal(t, userID, result.ID)
	require.Equal(t, "<EMAIL>", result.Email)
	require.Equal(t, "Complete User", result.FullName)
	require.True(t, result.IsAdmin)

	// Проверяем группы
	require.Len(t, result.Groups, 2)
	require.Equal(t, "Admin Group", result.Groups[0].Name)
	require.True(t, result.Groups[0].IsSystem)
	require.NotNil(t, result.Groups[0].Product)
	require.Equal(t, int64(100), result.Groups[0].Product.ID)

	require.Equal(t, "Custom Group", result.Groups[1].Name)
	require.False(t, result.Groups[1].IsSystem)
	require.Nil(t, result.Groups[1].Product)

	// Проверяем роли
	require.Len(t, result.Roles, 2)
	require.Equal(t, "Admin Role", result.Roles[0].Name)
	require.True(t, result.Roles[0].IsSystem)
	require.NotNil(t, result.Roles[0].Product)
	require.Equal(t, int64(200), result.Roles[0].Product.ID)

	require.Equal(t, "User Role", result.Roles[1].Name)
	require.False(t, result.Roles[1].IsSystem)
	require.Nil(t, result.Roles[1].Product)

	// Проверяем продукты
	require.Len(t, result.Products, 2)
	require.Equal(t, "Product One", result.Products[0].Name)
	require.Equal(t, &iid1, result.Products[0].IID)
	require.Equal(t, "Product Two", result.Products[1].Name)
	require.Equal(t, &iid2, result.Products[1].IID)
}

func TestUserAppService_DeleteAsAdmin_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	ctx := context.Background()

	mockUser.DeleteAsAdminMock.Expect(ctx, userID).Return(nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteAsAdmin(ctx, userID)
	require.NoError(t, err)
}

func TestUserAppService_DeleteAsAdmin_Error(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	ctx := context.Background()
	expectedError := errors.New("delete failed")

	mockUser.DeleteAsAdminMock.Expect(ctx, userID).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteAsAdmin(ctx, userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_DeleteAsAdmin_UserNotFound(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(999)
	ctx := context.Background()
	expectedError := errors.New("user not found")

	mockUser.DeleteAsAdminMock.Expect(ctx, userID).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteAsAdmin(ctx, userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_DeleteAsAdmin_DatabaseError(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	ctx := context.Background()
	expectedError := errors.New("database connection failed")

	mockUser.DeleteAsAdminMock.Expect(ctx, userID).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteAsAdmin(ctx, userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_DeleteAsAdmin_WithDifferentContexts(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)

	tests := []struct {
		name string
		ctx  context.Context
	}{
		{
			name: "background context",
			ctx:  context.Background(),
		},
		{
			name: "todo context",
			ctx:  context.TODO(),
		},
		{
			name: "context with value",
			ctx:  context.WithValue(context.Background(), "key", "value"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUser.DeleteAsAdminMock.Expect(tt.ctx, userID).Return(nil)

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			err := svc.DeleteAsAdmin(tt.ctx, userID)
			require.NoError(t, err)
		})
	}
}

func TestUserAppService_DeleteAsAdmin_WithDifferentUserIDs(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	ctx := context.Background()

	tests := []struct {
		name   string
		userID int64
	}{
		{
			name:   "positive user ID",
			userID: 1,
		},
		{
			name:   "large user ID",
			userID: 999999,
		},
		{
			name:   "zero user ID",
			userID: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUser.DeleteAsAdminMock.Expect(ctx, tt.userID).Return(nil)

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			err := svc.DeleteAsAdmin(ctx, tt.userID)
			require.NoError(t, err)
		})
	}
}

func TestUserAppService_DeleteAsAdmin_PermissionDenied(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	ctx := context.Background()
	expectedError := errors.New("permission denied")

	mockUser.DeleteAsAdminMock.Expect(ctx, userID).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteAsAdmin(ctx, userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_DeleteAsAdmin_CascadeDeleteError(t *testing.T) {
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)

	userID := int64(1)
	ctx := context.Background()
	expectedError := errors.New("failed to delete related records")

	mockUser.DeleteAsAdminMock.Expect(ctx, userID).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteAsAdmin(ctx, userID)
	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestUserAppService_GetAllAsAdmin_WithLimitAndOffset(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	allUsers := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 3, Email: "<EMAIL>", FullName: "User Three", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 4, Email: "<EMAIL>", FullName: "User Four", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 5, Email: "<EMAIL>", FullName: "User Five", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	limit := int64(2)
	offset := int64(1)
	q := query.AdminUser{
		Limit:  &limit,
		Offset: &offset,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(allUsers, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 2)
	require.Equal(t, int64(5), result.Total)
	require.Equal(t, int64(2), result.Limit)
	require.Equal(t, int64(1), result.Offset)
	require.Equal(t, int64(2), result.Items[0].ID)
	require.Equal(t, int64(3), result.Items[1].ID)
}

func TestUserAppService_GetAllAsAdmin_ZeroResults(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	// Данные есть, но фильтр исключает всех пользователей
	allUsers := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
		{ID: 2, Email: "<EMAIL>", FullName: "Another User", CategoryID: 1, IsAdmin: false, CreatedAt: time.Now()},
	}

	search := "nonexistent"
	q := query.AdminUser{
		Search: &search,
	}

	mockUser.GetAllAsAdminMock.Expect().Return(allUsers, nil)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAllAsAdmin(q)
	require.NoError(t, err)
	require.Len(t, result.Items, 0)
	require.Equal(t, int64(0), result.Total)
}

func TestUserAppService_GetAsAdminByID_CompleteData(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	now := time.Now()
	photoBytes := []byte("test photo")

	user := userentity.User{
		ID:                  userID,
		CategoryID:          10,
		Email:               "<EMAIL>",
		FullName:            "Complete User",
		Position:            "Senior Developer",
		IsAdmin:             true,
		ActiveFlg:           true,
		LastActiveProductID: 100,
		LastLoginAt:         &now,
		CreatedAt:           now,
		Photo:               &photoBytes,
	}

	groups := []groupentity.GroupWithProduct{
		{
			ID:        1,
			Name:      "Admin Group",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   &productentity.Product{ID: 100, Name: "Admin Product"},
		},
		{
			ID:        20,
			Name:      "Custom Group",
			IsSystem:  false,
			ActiveFlg: false,
			Product:   nil,
		},
	}

	roles := []roleentity.RoleWithProduct{
		{
			ID:        30,
			Name:      "Admin Role",
			IsSystem:  true,
			ActiveFlg: true,
			Product:   &productentity.Product{ID: 200, Name: "Role Product"},
		},
		{
			ID:        40,
			Name:      "User Role",
			IsSystem:  false,
			ActiveFlg: true,
			Product:   nil,
		},
	}

	iid1 := "prod-1"
	iid2 := "prod-2"
	products := []productentity.ProductBasic{
		{
			ID:       300,
			IID:      &iid1,
			Name:     "Product One",
			TechName: "product_one",
		},
		{
			ID:       400,
			IID:      &iid2,
			Name:     "Product Two",
			TechName: "product_two",
		},
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return(groups, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return(roles, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return(products, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetAsAdminByID(userID)
	require.NoError(t, err)
	require.Equal(t, user, result.User)
	require.Equal(t, groups, result.Groups)
	require.Equal(t, roles, result.Roles)
	require.Equal(t, products, result.Products)

	// Проверяем конкретные поля
	require.Equal(t, userID, result.ID)
	require.Equal(t, "<EMAIL>", result.Email)
	require.True(t, result.IsAdmin)
	require.Equal(t, "Senior Developer", result.Position)
	require.Equal(t, int64(10), result.CategoryID)
	require.NotNil(t, result.LastLoginAt)
	require.NotNil(t, result.Photo)
	require.Len(t, result.Groups, 2)
	require.Len(t, result.Roles, 2)
	require.Len(t, result.Products, 2)
}

func TestUserAppService_UpdateAsAdmin_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := true
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Updated User",
		Position:   "Manager",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	expectedUserWithDetails := useraggregate.UserWithDetails{
		User:     updatedUser,
		Groups:   []groupentity.GroupWithProduct{},
		Roles:    []roleentity.RoleWithProduct{},
		Products: []productentity.ProductBasic{},
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, expectedUserWithDetails.User.ID, result.User.ID)
	require.Equal(t, expectedUserWithDetails.User.CategoryID, result.User.CategoryID)
	require.Equal(t, expectedUserWithDetails.User.IsAdmin, result.User.IsAdmin)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithProductIDs(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := false
	productIDs := []int64{1, 2, 3}
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		ProductIDs: &productIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Test User",
		Position:   "Developer",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithProductsMock.Expect(ctx, userID, productIDs).Return(nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithProductsAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithRoleIDs(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := true
	roleIDs := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
		{RoleID: 2, ProductID: &[]int64{100}[0]},
	}
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		RoleIDs:    &roleIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Admin User",
		Position:   "Administrator",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithRolesMock.Expect(ctx, userID, roleIDs).Return(nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithRolesAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithGroupIDs(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(5)
	isAdmin := false
	groupIDs := []groupentity.GroupProductLink{
		{GroupID: 10, ProductID: nil},
		{GroupID: 20, ProductID: &[]int64{200}[0]},
	}
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		GroupIDs:   &groupIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Regular User",
		Position:   "Developer",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithGroupsMock.Expect(ctx, userID, groupIDs).Return(nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithGroupsAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithAllLinks(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(15)
	isAdmin := true
	productIDs := []int64{1, 2}
	roleIDs := []roleentity.RoleProductLink{
		{RoleID: 5, ProductID: nil},
	}
	groupIDs := []groupentity.GroupProductLink{
		{GroupID: 10, ProductID: &[]int64{100}[0]},
	}
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		ProductIDs: &productIDs,
		RoleIDs:    &roleIDs,
		GroupIDs:   &groupIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Super Admin",
		Position:   "System Administrator",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithProductsMock.Expect(ctx, userID, productIDs).Return(nil)
	mockUser.UpdateLinksWithRolesMock.Expect(ctx, userID, roleIDs).Return(nil)
	mockUser.UpdateLinksWithGroupsMock.Expect(ctx, userID, groupIDs).Return(nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithProductsAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithRolesAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithGroupsAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithNilValues(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: nil,
		IsAdmin:    nil,
		ProductIDs: nil,
		RoleIDs:    nil,
		GroupIDs:   nil,
	}

	updatedUser := userentity.User{
		ID:        userID,
		Email:     "<EMAIL>",
		FullName:  "Test User",
		Position:  "Developer",
		IsAdmin:   false,
		CreatedAt: time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: nil,
		IsAdmin:    nil,
	}).Return(updatedUser, nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(0), mockUser.UpdateLinksWithProductsAfterCounter())
	require.Equal(t, uint64(0), mockUser.UpdateLinksWithRolesAfterCounter())
	require.Equal(t, uint64(0), mockUser.UpdateLinksWithGroupsAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithEmptySlices(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(5)
	isAdmin := false
	var emptyProductIDs []int64
	var emptyRoleIDs []roleentity.RoleProductLink
	var emptyGroupIDs []groupentity.GroupProductLink
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		ProductIDs: &emptyProductIDs,
		RoleIDs:    &emptyRoleIDs,
		GroupIDs:   &emptyGroupIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Test User",
		Position:   "Developer",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithProductsMock.Expect(ctx, userID, emptyProductIDs).Return(nil)
	mockUser.UpdateLinksWithRolesMock.Expect(ctx, userID, emptyRoleIDs).Return(nil)
	mockUser.UpdateLinksWithGroupsMock.Expect(ctx, userID, emptyGroupIDs).Return(nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithProductsAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithRolesAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithGroupsAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_UserUpdateError(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := true
	ctx := context.Background()
	expectedError := errors.New("user update failed")

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(userentity.User{}, expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, useraggregate.UserWithDetails{}, result)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(0), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_ProductLinksUpdateError(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := false
	productIDs := []int64{1, 2}
	ctx := context.Background()
	expectedError := errors.New("product links update failed")

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		ProductIDs: &productIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Test User",
		Position:   "Developer",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithProductsMock.Expect(ctx, userID, productIDs).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, useraggregate.UserWithDetails{}, result)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithProductsAfterCounter())
	require.Equal(t, uint64(0), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_RoleLinksUpdateError(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := true
	roleIDs := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
		{RoleID: 2, ProductID: &[]int64{100}[0]},
	}
	ctx := context.Background()
	expectedError := errors.New("role links update failed")

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		RoleIDs:    &roleIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Admin User",
		Position:   "Administrator",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithRolesMock.Expect(ctx, userID, roleIDs).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, useraggregate.UserWithDetails{}, result)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithRolesAfterCounter())
	require.Equal(t, uint64(0), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_GroupLinksUpdateError(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(5)
	isAdmin := false
	groupIDs := []groupentity.GroupProductLink{
		{GroupID: 10, ProductID: nil},
	}
	ctx := context.Background()
	expectedError := errors.New("group links update failed")

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		GroupIDs:   &groupIDs,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Regular User",
		Position:   "Developer",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.UpdateLinksWithGroupsMock.Expect(ctx, userID, groupIDs).Return(expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, useraggregate.UserWithDetails{}, result)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.UpdateLinksWithGroupsAfterCounter())
	require.Equal(t, uint64(0), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_GetAsAdminByIDError(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	categoryID := int64(10)
	isAdmin := true
	ctx := context.Background()
	expectedError := errors.New("failed to get user details")

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Admin User",
		Position:   "Administrator",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.GetByIDMock.Expect(userID).Return(userentity.User{}, expectedError)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, useraggregate.UserWithDetails{}, result)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_BoundaryValues(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(999999)
	categoryID := int64(0)
	isAdmin := false
	ctx := context.Background()

	inputData := useraggregate.AdminUserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}

	updatedUser := userentity.User{
		ID:         userID,
		CategoryID: categoryID,
		Email:      "<EMAIL>",
		FullName:   "Boundary User",
		Position:   "Tester",
		IsAdmin:    isAdmin,
		CreatedAt:  time.Now(),
	}

	mockUser.UpdateMock.Expect(userentity.UserUpdateData{
		ID:         userID,
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}).Return(updatedUser, nil)

	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
	mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
	mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
	mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
	result, err := svc.UpdateAsAdmin(ctx, inputData)

	require.NoError(t, err)
	require.Equal(t, userID, result.User.ID)
	require.Equal(t, categoryID, result.User.CategoryID)
	require.Equal(t, isAdmin, result.User.IsAdmin)
	require.Equal(t, uint64(1), mockUser.UpdateAfterCounter())
	require.Equal(t, uint64(1), mockUser.GetByIDAfterCounter())
}

func TestUserAppService_UpdateAsAdmin_WithDifferentContexts(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	tests := []struct {
		name string
		ctx  context.Context
	}{
		{
			name: "background context",
			ctx:  context.Background(),
		},
		{
			name: "todo context",
			ctx:  context.TODO(),
		},
		{
			name: "context with value",
			ctx:  context.WithValue(context.Background(), "key", "value"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userID := int64(1)
			categoryID := int64(10)
			isAdmin := true
			productIDs := []int64{1}

			inputData := useraggregate.AdminUserUpdateData{
				ID:         userID,
				CategoryID: &categoryID,
				IsAdmin:    &isAdmin,
				ProductIDs: &productIDs,
			}

			updatedUser := userentity.User{
				ID:         userID,
				CategoryID: categoryID,
				Email:      "<EMAIL>",
				FullName:   "Context User",
				Position:   "Developer",
				IsAdmin:    isAdmin,
				CreatedAt:  time.Now(),
			}

			mockUser.UpdateMock.Expect(userentity.UserUpdateData{
				ID:         userID,
				CategoryID: &categoryID,
				IsAdmin:    &isAdmin,
			}).Return(updatedUser, nil)

			mockUser.UpdateLinksWithProductsMock.Expect(tt.ctx, userID, productIDs).Return(nil)

			mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)
			mockGroup.GetWithProductByUserIDMock.Expect(userID).Return([]groupentity.GroupWithProduct{}, nil)
			mockRole.GetAllWithProductByUserIDMock.Expect(userID).Return([]roleentity.RoleWithProduct{}, nil)
			mockProduct.GetBasicByUserIDMock.Expect(userID).Return([]productentity.ProductBasic{}, nil)

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)
			result, err := svc.UpdateAsAdmin(tt.ctx, inputData)

			require.NoError(t, err)
			require.Equal(t, userID, result.User.ID)
		})
	}
}
