package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestFilterUsers(t *testing.T) {
	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "Admin User",
			CategoryID: 1,
			IsAdmin:    true,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "Regular User",
			CategoryID: 2,
			IsAdmin:    false,
			CreatedAt:  time.Now(),
			Products:   []productentity.ProductBasic{{ID: 2, TechName: "product2"}},
		},
	}

	result := filterUsers(users, query.AdminUser{})
	require.Len(t, result, 2)
	require.Equal(t, users, result)
}

func TestFilterUsers_SearchByEmail(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin User", CategoryID: 1, IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 2, IsAdmin: false},
		{ID: 3, Email: "<EMAIL>", FullName: "Test User", CategoryID: 1, IsAdmin: false},
	}

	search := "admin"
	q := query.AdminUser{Search: &search}

	result := filterUsers(users, q)
	require.Len(t, result, 1)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, "<EMAIL>", result[0].Email)
}

func TestFilterUsers_SearchByFullName(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Alice Developer", CategoryID: 1, IsAdmin: false},
		{ID: 2, Email: "<EMAIL>", FullName: "Bob Manager", CategoryID: 2, IsAdmin: false},
		{ID: 3, Email: "<EMAIL>", FullName: "Charlie Developer", CategoryID: 1, IsAdmin: false},
	}

	search := "developer"
	q := query.AdminUser{Search: &search}

	result := filterUsers(users, q)
	require.Len(t, result, 2)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, int64(3), result[1].ID)
}

func TestFilterUsers_SearchCaseInsensitive(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "ADMIN USER", CategoryID: 1, IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 2, IsAdmin: false},
	}

	search := "admin"
	q := query.AdminUser{Search: &search}

	result := filterUsers(users, q)
	require.Len(t, result, 1)
	require.Equal(t, int64(1), result[0].ID)
}

func TestFilterUsers_SearchEmptyString(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin User", CategoryID: 1, IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 2, IsAdmin: false},
	}

	search := ""
	q := query.AdminUser{Search: &search}

	result := filterUsers(users, q)
	require.Len(t, result, 2)
}

func TestFilterUsers_IsAdminFilter(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin User", CategoryID: 1, IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 2, IsAdmin: false},
		{ID: 3, Email: "<EMAIL>", FullName: "Admin Two", CategoryID: 1, IsAdmin: true},
	}

	isAdmin := true
	q := query.AdminUser{IsAdmin: &isAdmin}

	result := filterUsers(users, q)
	require.Len(t, result, 2)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, int64(3), result[1].ID)
	require.True(t, result[0].IsAdmin)
	require.True(t, result[1].IsAdmin)
}

func TestFilterUsers_IsAdminFilterFalse(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin User", CategoryID: 1, IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 2, IsAdmin: false},
	}

	isAdmin := false
	q := query.AdminUser{IsAdmin: &isAdmin}

	result := filterUsers(users, q)
	require.Len(t, result, 1)
	require.Equal(t, int64(2), result[0].ID)
	require.False(t, result[0].IsAdmin)
}

func TestFilterUsers_CategoryFilter(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 1, IsAdmin: false},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 2, IsAdmin: false},
		{ID: 3, Email: "<EMAIL>", FullName: "User Three", CategoryID: 1, IsAdmin: false},
		{ID: 4, Email: "<EMAIL>", FullName: "User Four", CategoryID: 3, IsAdmin: false},
	}

	q := query.AdminUser{CategoryIDs: []int64{1, 3}}

	result := filterUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, int64(3), result[1].ID)
	require.Equal(t, int64(4), result[2].ID)
}

func TestFilterUsers_CategoryFilterEmpty(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User One", CategoryID: 1, IsAdmin: false},
		{ID: 2, Email: "<EMAIL>", FullName: "User Two", CategoryID: 2, IsAdmin: false},
	}

	q := query.AdminUser{CategoryIDs: []int64{}}

	result := filterUsers(users, q)
	require.Len(t, result, 2)
}

func TestFilterUsers_ProductFilter(t *testing.T) {
	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "User One",
			CategoryID: 1,
			IsAdmin:    false,
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "User Two",
			CategoryID: 1,
			IsAdmin:    false,
			Products:   []productentity.ProductBasic{{ID: 2, TechName: "product2"}},
		},
		{
			ID:         3,
			Email:      "<EMAIL>",
			FullName:   "User Three",
			CategoryID: 1,
			IsAdmin:    false,
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}, {ID: 3, TechName: "product3"}},
		},
	}

	q := query.AdminUser{ProductIDs: []int64{1}}

	result := filterUsers(users, q)
	require.Len(t, result, 2)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, int64(3), result[1].ID)
}

func TestFilterUsers_ProductFilterNoProducts(t *testing.T) {
	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "User One",
			CategoryID: 1,
			IsAdmin:    false,
			Products:   []productentity.ProductBasic{},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "User Two",
			CategoryID: 1,
			IsAdmin:    false,
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
	}

	q := query.AdminUser{ProductIDs: []int64{1}}

	result := filterUsers(users, q)
	require.Len(t, result, 1)
	require.Equal(t, int64(2), result[0].ID)
}

func TestFilterUsers_CombinedFilters(t *testing.T) {
	users := []userentity.AdminUser{
		{
			ID:         1,
			Email:      "<EMAIL>",
			FullName:   "Admin One",
			CategoryID: 1,
			IsAdmin:    true,
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         2,
			Email:      "<EMAIL>",
			FullName:   "Admin Two",
			CategoryID: 2,
			IsAdmin:    true,
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
		{
			ID:         3,
			Email:      "<EMAIL>",
			FullName:   "Regular User",
			CategoryID: 1,
			IsAdmin:    false,
			Products:   []productentity.ProductBasic{{ID: 1, TechName: "product1"}},
		},
	}

	search := "admin"
	isAdmin := true
	q := query.AdminUser{
		Search:      &search,
		IsAdmin:     &isAdmin,
		CategoryIDs: []int64{1},
		ProductIDs:  []int64{1},
	}

	result := filterUsers(users, q)
	require.Len(t, result, 1)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, "<EMAIL>", result[0].Email)
}

func TestSortUsers_NoSort(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, FullName: "Charlie"},
		{ID: 2, FullName: "Alice"},
		{ID: 3, FullName: "Bob"},
	}

	result := sortUsers(users, query.AdminUser{})
	require.Len(t, result, 3)
	require.Equal(t, users, result)
}

func TestSortUsers_ByFullNameAscending(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, FullName: "Charlie"},
		{ID: 2, FullName: "Alice"},
		{ID: 3, FullName: "Bob"},
	}

	sort := "fullName"
	order := "ascend"
	q := query.AdminUser{Sort: &sort, Order: &order}

	result := sortUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, "Alice", result[0].FullName)
	require.Equal(t, "Bob", result[1].FullName)
	require.Equal(t, "Charlie", result[2].FullName)
}

func TestSortUsers_ByFullNameDescending(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, FullName: "Alice"},
		{ID: 2, FullName: "Bob"},
		{ID: 3, FullName: "Charlie"},
	}

	sort := "fullName"
	order := "descend"
	q := query.AdminUser{Sort: &sort, Order: &order}

	result := sortUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, "Charlie", result[0].FullName)
	require.Equal(t, "Bob", result[1].FullName)
	require.Equal(t, "Alice", result[2].FullName)
}

func TestSortUsers_ByCategoryAscending(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, FullName: "User One", CategoryID: 3},
		{ID: 2, FullName: "User Two", CategoryID: 1},
		{ID: 3, FullName: "User Three", CategoryID: 2},
	}

	sort := "category"
	order := "ascend"
	q := query.AdminUser{Sort: &sort, Order: &order}

	result := sortUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, int64(1), result[0].CategoryID)
	require.Equal(t, int64(2), result[1].CategoryID)
	require.Equal(t, int64(3), result[2].CategoryID)
}

func TestSortUsers_ByCreatedAtAscending(t *testing.T) {
	now := time.Now()
	users := []userentity.AdminUser{
		{ID: 1, FullName: "User One", CreatedAt: now.Add(2 * time.Hour)},
		{ID: 2, FullName: "User Two", CreatedAt: now},
		{ID: 3, FullName: "User Three", CreatedAt: now.Add(1 * time.Hour)},
	}

	sort := "createdAt"
	order := "ascend"
	q := query.AdminUser{Sort: &sort, Order: &order}

	result := sortUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, int64(2), result[0].ID)
	require.Equal(t, int64(3), result[1].ID)
	require.Equal(t, int64(1), result[2].ID)
}

func TestSortUsers_ByProductAscending(t *testing.T) {
	users := []userentity.AdminUser{
		{
			ID:       1,
			FullName: "User One",
			Products: []productentity.ProductBasic{{ID: 1, TechName: "zebra"}},
		},
		{
			ID:       2,
			FullName: "User Two",
			Products: []productentity.ProductBasic{{ID: 2, TechName: "alpha"}},
		},
		{
			ID:       3,
			FullName: "User Three",
			Products: []productentity.ProductBasic{},
		},
	}

	sort := "product"
	order := "ascend"
	q := query.AdminUser{Sort: &sort, Order: &order}

	result := sortUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, int64(3), result[0].ID)
	require.Equal(t, int64(2), result[1].ID)
	require.Equal(t, int64(1), result[2].ID)
}

func TestSortUsers_DefaultSort(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, FullName: "Charlie"},
		{ID: 2, FullName: "Alice"},
		{ID: 3, FullName: "Bob"},
	}

	sort := "unknown"
	order := "ascend"
	q := query.AdminUser{Sort: &sort, Order: &order}

	result := sortUsers(users, q)
	require.Len(t, result, 3)
	require.Equal(t, "Alice", result[0].FullName)
	require.Equal(t, "Bob", result[1].FullName)
	require.Equal(t, "Charlie", result[2].FullName)
}

func TestCompareUsers_ByFullName(t *testing.T) {
	u1 := userentity.AdminUser{FullName: "Alice"}
	u2 := userentity.AdminUser{FullName: "Bob"}

	result := compareUsers(u1, u2, sharedentity.SortFieldFullName.String())
	require.True(t, result)

	result = compareUsers(u2, u1, sharedentity.SortFieldFullName.String())
	require.False(t, result)
}

func TestCompareUsers_ByFullNameCaseInsensitive(t *testing.T) {
	u1 := userentity.AdminUser{FullName: "alice"}
	u2 := userentity.AdminUser{FullName: "BOB"}

	result := compareUsers(u1, u2, sharedentity.SortFieldFullName.String())
	require.True(t, result)
}

func TestCompareUsers_ByCategory(t *testing.T) {
	u1 := userentity.AdminUser{CategoryID: 1}
	u2 := userentity.AdminUser{CategoryID: 2}

	result := compareUsers(u1, u2, sharedentity.SortFieldCategory.String())
	require.True(t, result)

	result = compareUsers(u2, u1, sharedentity.SortFieldCategory.String())
	require.False(t, result)
}

func TestCompareUsers_ByCreatedAt(t *testing.T) {
	now := time.Now()
	u1 := userentity.AdminUser{CreatedAt: now}
	u2 := userentity.AdminUser{CreatedAt: now.Add(1 * time.Hour)}

	result := compareUsers(u1, u2, sharedentity.SortFieldCreatedAt.String())
	require.True(t, result)

	result = compareUsers(u2, u1, sharedentity.SortFieldCreatedAt.String())
	require.False(t, result)
}

func TestCompareUsers_ByProduct(t *testing.T) {
	u1 := userentity.AdminUser{
		Products: []productentity.ProductBasic{{ID: 1, TechName: "alpha"}},
	}
	u2 := userentity.AdminUser{
		Products: []productentity.ProductBasic{{ID: 2, TechName: "beta"}},
	}

	result := compareUsers(u1, u2, sharedentity.SortFieldProduct.String())
	require.True(t, result)

	result = compareUsers(u2, u1, sharedentity.SortFieldProduct.String())
	require.False(t, result)
}

func TestCompareUsers_DefaultField(t *testing.T) {
	u1 := userentity.AdminUser{FullName: "Alice"}
	u2 := userentity.AdminUser{FullName: "Bob"}

	result := compareUsers(u1, u2, "unknown")
	require.True(t, result)
}

func TestCompareUsersByProduct_BothHaveProducts(t *testing.T) {
	u1 := userentity.AdminUser{
		FullName: "User One",
		Products: []productentity.ProductBasic{{ID: 1, TechName: "alpha"}},
	}
	u2 := userentity.AdminUser{
		FullName: "User Two",
		Products: []productentity.ProductBasic{{ID: 2, TechName: "beta"}},
	}

	result := compareUsersByProduct(u1, u2)
	require.True(t, result)

	result = compareUsersByProduct(u2, u1)
	require.False(t, result)
}

func TestCompareUsersByProduct_OneHasNoProducts(t *testing.T) {
	u1 := userentity.AdminUser{
		FullName: "User One",
		Products: []productentity.ProductBasic{},
	}
	u2 := userentity.AdminUser{
		FullName: "User Two",
		Products: []productentity.ProductBasic{{ID: 1, TechName: "alpha"}},
	}

	result := compareUsersByProduct(u1, u2)
	require.True(t, result)

	result = compareUsersByProduct(u2, u1)
	require.False(t, result)
}

func TestCompareUsersByProduct_BothHaveNoProducts(t *testing.T) {
	u1 := userentity.AdminUser{
		FullName: "Alice",
		Products: []productentity.ProductBasic{},
	}
	u2 := userentity.AdminUser{
		FullName: "Bob",
		Products: []productentity.ProductBasic{},
	}

	result := compareUsersByProduct(u1, u2)
	require.True(t, result)

	result = compareUsersByProduct(u2, u1)
	require.False(t, result)
}
