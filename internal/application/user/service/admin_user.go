package service

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/pagination"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	useraggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/aggregate"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func (s *UserAppService) GetAllAsAdmin(query query.AdminUser) (sharedentity.PaginatedResult[userentity.AdminUser], error) {
	users, err := s.userDomain.GetAllAsAdmin()
	if err != nil {
		return sharedentity.PaginatedResult[userentity.AdminUser]{}, err
	}

	filteredUsers := filterUsers(users, query)
	sortedUsers := sortUsers(filteredUsers, query)
	paginatedUsers, limit, offset := pagination.Apply(sortedUsers, query)

	return sharedentity.PaginatedResult[userentity.AdminUser]{
		Items:  paginatedUsers,
		Total:  int64(len(filteredUsers)),
		Limit:  limit,
		Offset: offset,
	}, nil
}

func (s *UserAppService) GetAsAdminByID(userID int64) (useraggregate.UserWithDetails, error) {
	user, err := s.userDomain.GetByID(userID)
	if err != nil {
		return useraggregate.UserWithDetails{}, err
	}

	groups, err := s.groupDomain.GetWithProductByUserID(userID)
	if err != nil {
		return useraggregate.UserWithDetails{}, err
	}

	roles, err := s.roleDomain.GetAllWithProductByUserID(userID)
	if err != nil {
		return useraggregate.UserWithDetails{}, err
	}

	products, err := s.productDomain.GetBasicByUserID(userID)
	if err != nil {
		return useraggregate.UserWithDetails{}, err
	}

	return useraggregate.UserWithDetails{
		User:     user,
		Groups:   groups,
		Roles:    roles,
		Products: products,
	}, nil
}

func (s *UserAppService) UpdateAsAdmin(ctx context.Context, user useraggregate.AdminUserUpdateData) (useraggregate.UserWithDetails, error) {

	updatedUser, err := s.userDomain.Update(userentity.UserUpdateData{
		ID:         user.ID,
		CategoryID: user.CategoryID,
		IsAdmin:    user.IsAdmin,
	})
	if err != nil {
		return useraggregate.UserWithDetails{}, err
	}

	if user.ProductIDs != nil {
		err = s.userDomain.UpdateLinksWithProducts(ctx, user.ID, *user.ProductIDs)
		if err != nil {
			return useraggregate.UserWithDetails{}, err
		}
	}

	if user.RoleIDs != nil {
		err = s.userDomain.UpdateLinksWithRoles(ctx, user.ID, *user.RoleIDs)
		if err != nil {
			return useraggregate.UserWithDetails{}, err
		}
	}

	if user.GroupIDs != nil {
		err = s.userDomain.UpdateLinksWithGroups(ctx, user.ID, *user.GroupIDs)
		if err != nil {
			return useraggregate.UserWithDetails{}, err
		}
	}

	return s.GetAsAdminByID(updatedUser.ID)
}

func (s *UserAppService) DeleteAsAdmin(ctx context.Context, userID int64) error {
	return s.userDomain.DeleteAsAdmin(ctx, userID)
}
