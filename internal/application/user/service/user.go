package service

import (
	"context"
	"sort"
	"strings"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/pagination"

	appquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type UserAppService struct {
	groupDomain       groupservice.GroupDomainService
	participantDomain participantservice.ParticipantDomainService
	productDomain     productservice.ProductDomainService
	roleDomain        roleservice.RoleDomainService
	userDomain        userservice.UserDomainService
}

func NewUserAppService(
	groupDomain groupservice.GroupDomainService,
	participantDomain participantservice.ParticipantDomainService,
	productDomain productservice.ProductDomainService,
	roleDomain roleservice.RoleDomainService,
	userDomain userservice.UserDomainService,
) *UserAppService {
	return &UserAppService{
		groupDomain:       groupDomain,
		participantDomain: participantDomain,
		productDomain:     productDomain,
		roleDomain:        roleDomain,
		userDomain:        userDomain,
	}
}

func (s *UserAppService) Create(ctx context.Context, user userentity.User) (userentity.User, error) {
	return s.userDomain.CreateByEmail(ctx, user.Email)
}

func (s *UserAppService) GetAll(query appquery.Users) (sharedentity.PaginatedResult[userentity.User], error) {

	var (
		users []userentity.User
		err   error
	)

	filters := userentity.UserFiltersData{
		Email:       query.Search,
		FullName:    query.Search,
		CategoryIDs: query.CategoryIDs,
		IsAdmin:     query.IsAdmin,
		ProductIDs:  query.ProductIDs,
	}

	if s.isEmptyFilters(filters) {
		users, err = s.userDomain.GetAll()
	} else {
		users, err = s.userDomain.GetByFilters(filters)
	}
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	if query.Sort != nil && query.Order != nil {
		users = s.sortUsers(users, sharedentity.SortParams{Field: *query.Sort, Order: *query.Order})
	}

	paginatedUsers, limit, offset := pagination.Apply(users, query)

	return sharedentity.PaginatedResult[userentity.User]{
		Items:  paginatedUsers,
		Total:  int64(len(users)),
		Limit:  limit,
		Offset: offset,
	}, nil
}

func (s *UserAppService) GetByEmail(email string) (userentity.User, error) {
	return s.userDomain.GetByEmail(email)
}

func (s *UserAppService) GetByID(id int64) (userentity.User, error) {
	return s.userDomain.GetByID(id)
}

func (s *UserAppService) GetCurrentUser(id int64) (userentity.User, error) {
	return s.userDomain.GetByID(id)
}

func (s *UserAppService) GetUserDetailsByID(id int64) (userentity.UserDetails, error) {
	user, err := s.userDomain.GetByID(id)
	if err != nil {
		return userentity.UserDetails{}, err
	}

	userProducts := make([]userentity.UserProduct, len(user.Products))
	for i, product := range user.Products {
		userProducts[i] = userentity.UserProduct{
			ID:       product.ID,
			IID:      product.IID,
			Name:     product.Name,
			TechName: product.TechName,
		}
	}

	systemRoles, err := s.roleDomain.GetByUserID(id)
	if err != nil {
		return userentity.UserDetails{}, err
	}

	productRoles, err := s.roleDomain.GetByUserIDByProductIDs(id, []int64{})
	if err != nil {
		return userentity.UserDetails{}, err
	}

	userRoles := make([]userentity.UserRole, 0)
	for _, role := range systemRoles {
		userRoles = append(userRoles, userentity.UserRole{
			RoleID:    role.ID,
			Name:      role.Name,
			IsActive:  role.ActiveFlg,
			Type:      constants.IsSystemToConstant(role.IsSystem),
			ProductID: role.ProductID,
		})
	}
	for _, role := range productRoles {
		userRoles = append(userRoles, userentity.UserRole{
			RoleID:    role.ID,
			Name:      role.Name,
			IsActive:  role.ActiveFlg,
			Type:      constants.IsSystemToConstant(role.IsSystem),
			ProductID: role.ProductID,
		})
	}

	userGroups, err := s.userDomain.GetUserGroupsByUserID(id)
	if err != nil {
		return userentity.UserDetails{}, err
	}

	return userentity.UserDetails{
		ID:          user.ID,
		CategoryID:  user.CategoryID,
		Email:       user.Email,
		FullName:    user.FullName,
		Position:    user.Position,
		IsAdmin:     user.IsAdmin,
		LastLoginAt: user.LastLoginAt,
		Photo:       user.Photo,
		CreatedAt:   user.CreatedAt,
		Products:    userProducts,
		Roles:       userRoles,
		Groups:      userGroups,
	}, nil
}

func (s *UserAppService) GetUserProduct(userID int64, query *sharedentity.SortParams) ([]userentity.UserProduct, error) {
	products, err := s.productDomain.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	userProducts := make([]userentity.UserProduct, len(products))
	for i, product := range products {
		userProducts[i] = userentity.UserProduct{
			ID:       product.ID,
			IID:      product.IID,
			Name:     product.Name,
			TechName: product.TechName,
		}
	}

	if query != nil && query.Field != "" && query.Order != "" {
		userProducts = s.sortProducts(userProducts, sharedentity.SortParams{Field: query.Field, Order: query.Order})
	}

	return userProducts, nil
}

func (s *UserAppService) isEmptyFilters(filters userentity.UserFiltersData) bool {
	if filters.Email != nil && *filters.Email != "" {
		return false
	}

	if filters.FullName != nil && *filters.FullName != "" {
		return false
	}

	if filters.CategoryIDs != nil && len(*filters.CategoryIDs) > 0 {
		return false
	}

	if filters.IsAdmin != nil {
		return false
	}

	if filters.ProductIDs != nil && len(*filters.ProductIDs) > 0 {
		return false
	}

	return true
}

func (s *UserAppService) paginateUsers(users []userentity.User, paginationParams sharedentity.PaginationParams) []userentity.User {
	if len(users) == 0 {
		return users
	}

	start := paginationParams.Offset
	if start >= int64(len(users)) {
		return nil
	}

	end := start + paginationParams.Limit
	if end > int64(len(users)) {
		end = int64(len(users))
	}

	return users[start:end]
}

func (s *UserAppService) sortProducts(users []userentity.UserProduct, sortParams sharedentity.SortParams) []userentity.UserProduct {
	ascending := sortParams.Order == string(sharedentity.SortOrderAscend)

	switch sortParams.Field {
	case string(sharedentity.SortFieldTechName):
		sort.Slice(users, func(i, j int) bool {
			if ascending {
				return strings.ToLower(users[i].TechName) < strings.ToLower(users[j].TechName)
			}

			return strings.ToLower(users[i].TechName) > strings.ToLower(users[j].TechName)
		})
	}

	return users
}

func (s *UserAppService) sortUsers(users []userentity.User, sortParams sharedentity.SortParams) []userentity.User {
	ascending := sortParams.Order == string(sharedentity.SortOrderAscend)

	switch sortParams.Field {
	case string(sharedentity.SortFieldCategory):
		sort.Slice(users, func(i, j int) bool {
			if ascending {
				return users[i].CategoryID < users[j].CategoryID
			}

			return users[i].CategoryID > users[j].CategoryID
		})
	case string(sharedentity.SortFieldCreatedAt):
		sort.Slice(users, func(i, j int) bool {
			if ascending {
				return users[i].CreatedAt.Before(users[j].CreatedAt)
			}

			return users[i].CreatedAt.After(users[j].CreatedAt)
		})
	case string(sharedentity.SortFieldFullName):
		sort.Slice(users, func(i, j int) bool {
			if ascending {
				return strings.ToLower(users[i].FullName) < strings.ToLower(users[j].FullName)
			}

			return strings.ToLower(users[i].FullName) > strings.ToLower(users[j].FullName)
		})
	case string(sharedentity.SortFieldProduct):
		users = s.sortUsersByProducts(users, ascending)
	}

	return users
}

func (s *UserAppService) sortUsersByProducts(users []userentity.User, ascending bool) []userentity.User {
	// sort products within each user alphabetically
	for i := range users {
		sort.Slice(users[i].Products, func(a, b int) bool {
			return users[i].Products[a].TechName < users[i].Products[b].TechName
		})
	}
	// sort users by first product
	sort.Slice(users, func(i, j int) bool {
		if len(users[i].Products) == 0 && len(users[j].Products) > 0 {
			return !ascending
		}

		if len(users[i].Products) > 0 && len(users[j].Products) == 0 {
			return ascending
		}

		if len(users[i].Products) == 0 && len(users[j].Products) == 0 {
			return false
		}

		if ascending {
			return users[i].Products[0].TechName < users[j].Products[0].TechName
		}

		return users[i].Products[0].TechName > users[j].Products[0].TechName
	})

	return users
}

func (s *UserAppService) Update(ctx context.Context, user userentity.UserUpdateData) (userentity.User, error) {
	_, err := s.userDomain.Update(user)
	if err != nil {
		return userentity.User{}, err
	}

	if err = s.participantDomain.UpdateParticipantByUserUpdateData(user.ID, user); err != nil {
		return userentity.User{}, err
	}

	if err = s.groupDomain.UpdateUserGroups(ctx, user.ID, user); err != nil {
		return userentity.User{}, err
	}

	if err = s.roleDomain.UpdateUserRoles(ctx, user.ID, user); err != nil {
		return userentity.User{}, err
	}

	return s.userDomain.GetByID(user.ID)
}

func (s *UserAppService) DeleteUserGroups(ctx context.Context, id int64, groupIDs []int64) error {
	return s.userDomain.DeleteByUserIDAndGroupIDs(ctx, id, groupIDs)
}

func (s *UserAppService) DeleteUserProducts(ctx context.Context, id int64, productIDs []int64) error {
	return s.participantDomain.DeleteByUserIDAndProductIDs(ctx, id, productIDs)
}

func (s *UserAppService) DeleteUserRoles(ctx context.Context, id int64, roleIDs []int64) error {
	return s.userDomain.DeleteByUserIDAndRoleIDs(ctx, id, roleIDs)
}

func (s *UserAppService) GetAdminUsers() ([]userentity.User, error) {
	return s.userDomain.GetAdmins()
}
