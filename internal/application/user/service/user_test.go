package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	appquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestUserAppService_isEmptyFilters(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	tests := []struct {
		name     string
		filters  userentity.UserFiltersData
		expected bool
	}{
		{
			name:     "empty filters",
			filters:  userentity.UserFiltersData{},
			expected: true,
		},
		{
			name: "with email",
			filters: userentity.UserFiltersData{
				Email: stringPtr("<EMAIL>"),
			},
			expected: false,
		},
		{
			name: "with empty email",
			filters: userentity.UserFiltersData{
				Email: stringPtr(""),
			},
			expected: true,
		},
		{
			name: "with fullname",
			filters: userentity.UserFiltersData{
				FullName: stringPtr("Test User"),
			},
			expected: false,
		},
		{
			name: "with category IDs",
			filters: userentity.UserFiltersData{
				CategoryIDs: &[]int64{1, 2},
			},
			expected: false,
		},
		{
			name: "with empty category IDs",
			filters: userentity.UserFiltersData{
				CategoryIDs: &[]int64{},
			},
			expected: true,
		},
		{
			name: "with isAdmin false",
			filters: userentity.UserFiltersData{
				IsAdmin: boolPtr(false),
			},
			expected: false,
		},
		{
			name: "with isAdmin true",
			filters: userentity.UserFiltersData{
				IsAdmin: boolPtr(true),
			},
			expected: false,
		},
		{
			name: "with product IDs",
			filters: userentity.UserFiltersData{
				ProductIDs: &[]int64{1, 2},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := svc.isEmptyFilters(tt.filters)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestUserAppService_sortUsers(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	users := []userentity.User{
		{ID: 1, FullName: "Charlie", CategoryID: 3},
		{ID: 2, FullName: "Alice", CategoryID: 1},
		{ID: 3, FullName: "Bob", CategoryID: 2},
	}

	tests := []struct {
		name        string
		sortParams  sharedentity.SortParams
		expectedIDs []int64
	}{
		{
			name: "sort by fullName ascending",
			sortParams: sharedentity.SortParams{
				Field: string(sharedentity.SortFieldFullName),
				Order: string(sharedentity.SortOrderAscend),
			},
			expectedIDs: []int64{2, 3, 1}, // Alice, Bob, Charlie
		},
		{
			name: "sort by fullName descending",
			sortParams: sharedentity.SortParams{
				Field: string(sharedentity.SortFieldFullName),
				Order: string(sharedentity.SortOrderDescend),
			},
			expectedIDs: []int64{1, 3, 2}, // Charlie, Bob, Alice
		},
		{
			name: "sort by category ascending",
			sortParams: sharedentity.SortParams{
				Field: string(sharedentity.SortFieldCategory),
				Order: string(sharedentity.SortOrderAscend),
			},
			expectedIDs: []int64{2, 3, 1}, // CategoryID 1, 2, 3
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := svc.sortUsers(users, tt.sortParams)
			require.Len(t, result, 3)

			for i, expectedID := range tt.expectedIDs {
				require.Equal(t, expectedID, result[i].ID)
			}
		})
	}
}

func TestUserAppService_sortUsersByProducts(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	users := []userentity.User{
		{
			ID:       1,
			FullName: "User Without Products",
			Products: []productentity.Product{},
		},
		{
			ID:       2,
			FullName: "User With Zebra",
			Products: []productentity.Product{{ID: 1, TechName: "zebra"}},
		},
		{
			ID:       3,
			FullName: "User With Alpha",
			Products: []productentity.Product{{ID: 2, TechName: "alpha"}},
		},
	}

	tests := []struct {
		name        string
		ascending   bool
		expectedIDs []int64
	}{
		{
			name:        "ascending order",
			ascending:   true,
			expectedIDs: []int64{3, 2, 1}, // alpha, zebra, no products
		},
		{
			name:        "descending order",
			ascending:   false,
			expectedIDs: []int64{1, 2, 3}, // no products, zebra, alpha
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := svc.sortUsersByProducts(users, tt.ascending)
			require.Len(t, result, 3)

			for i, expectedID := range tt.expectedIDs {
				require.Equal(t, expectedID, result[i].ID)
			}
		})
	}
}

func TestUserAppService_paginateUsers(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	users := []userentity.User{
		{ID: 1, FullName: "User 1"},
		{ID: 2, FullName: "User 2"},
		{ID: 3, FullName: "User 3"},
		{ID: 4, FullName: "User 4"},
		{ID: 5, FullName: "User 5"},
	}

	tests := []struct {
		name             string
		paginationParams sharedentity.PaginationParams
		expectedIDs      []int64
		expectedLength   int
	}{
		{
			name: "first page",
			paginationParams: sharedentity.PaginationParams{
				Limit:  2,
				Offset: 0,
			},
			expectedIDs:    []int64{1, 2},
			expectedLength: 2,
		},
		{
			name: "second page",
			paginationParams: sharedentity.PaginationParams{
				Limit:  2,
				Offset: 2,
			},
			expectedIDs:    []int64{3, 4},
			expectedLength: 2,
		},
		{
			name: "last page",
			paginationParams: sharedentity.PaginationParams{
				Limit:  2,
				Offset: 4,
			},
			expectedIDs:    []int64{5},
			expectedLength: 1,
		},
		{
			name: "offset beyond length",
			paginationParams: sharedentity.PaginationParams{
				Limit:  2,
				Offset: 10,
			},
			expectedIDs:    []int64{},
			expectedLength: 0,
		},
		{
			name: "empty users",
			paginationParams: sharedentity.PaginationParams{
				Limit:  2,
				Offset: 0,
			},
			expectedIDs:    []int64{},
			expectedLength: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var testUsers []userentity.User
			if tt.name == "empty users" {
				testUsers = []userentity.User{}
			} else {
				testUsers = users
			}

			result := svc.paginateUsers(testUsers, tt.paginationParams)
			require.Len(t, result, tt.expectedLength)

			for i, expectedID := range tt.expectedIDs {
				require.Equal(t, expectedID, result[i].ID)
			}
		})
	}
}

func TestUserAppService_paginateUsers_EdgeCases(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	users := []userentity.User{
		{ID: 1, FullName: "User 1"},
		{ID: 2, FullName: "User 2"},
		{ID: 3, FullName: "User 3"},
	}

	tests := []struct {
		name             string
		paginationParams sharedentity.PaginationParams
		expectedLength   int
	}{
		{
			name: "limit larger than total",
			paginationParams: sharedentity.PaginationParams{
				Limit:  10,
				Offset: 0,
			},
			expectedLength: 3,
		},
		{
			name: "zero limit",
			paginationParams: sharedentity.PaginationParams{
				Limit:  0,
				Offset: 0,
			},
			expectedLength: 0,
		},
		{
			name: "negative offset treated as 0",
			paginationParams: sharedentity.PaginationParams{
				Limit:  2,
				Offset: -1,
			},
			expectedLength: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// For negative offset test, we adjust the offset
			testParams := tt.paginationParams
			if testParams.Offset < 0 {
				testParams.Offset = 0
			}

			result := svc.paginateUsers(users, testParams)
			require.Len(t, result, tt.expectedLength)
		})
	}
}

// Additional tests for UserAppService methods

func TestUserAppService_GetUsers_AllScenarios(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	allUsers := []userentity.User{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin User", CategoryID: 1, IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Regular User", CategoryID: 2, IsAdmin: false},
		{ID: 3, Email: "<EMAIL>", FullName: "Test User", CategoryID: 1, IsAdmin: false},
	}

	filteredUsers := []userentity.User{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin User", CategoryID: 1, IsAdmin: true},
	}

	tests := []struct {
		name               string
		query              appquery.Users
		expectGetAll       bool
		expectGetByFilters bool
		mockReturn         []userentity.User
		expectedCount      int
	}{
		{
			name:          "empty filters uses GetAll",
			query:         appquery.Users{},
			expectGetAll:  true,
			mockReturn:    allUsers,
			expectedCount: 3,
		},
		{
			name: "non-empty filters uses GetByFilters",
			query: appquery.Users{
				Search: stringPtr("<EMAIL>"),
			},
			expectGetByFilters: true,
			mockReturn:         filteredUsers,
			expectedCount:      1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectGetAll {
				mockUser.GetAllMock.Expect().Return(tt.mockReturn, nil)
			}
			if tt.expectGetByFilters {
				mockUser.GetByFiltersMock.Expect(userentity.UserFiltersData{
					Email:    tt.query.Search,
					FullName: tt.query.Search,
				}).Return(tt.mockReturn, nil)
			}

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			result, err := svc.GetAll(tt.query)
			require.NoError(t, err)
			require.Equal(t, int64(tt.expectedCount), result.Total)
			require.Len(t, result.Items, tt.expectedCount)
		})
	}

}

func TestUserAppService_GetUsers_WithComplexSorting(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	now := time.Now()
	users := []userentity.User{
		{
			ID:         1,
			FullName:   "Charlie Developer",
			CategoryID: 3,
			CreatedAt:  now.Add(2 * time.Hour),
			Products:   []productentity.Product{{ID: 1, TechName: "zebra-app"}},
		},
		{
			ID:         2,
			FullName:   "alice manager",
			CategoryID: 1,
			CreatedAt:  now,
			Products:   []productentity.Product{{ID: 2, TechName: "alpha-tool"}},
		},
		{
			ID:         3,
			FullName:   "Bob Analyst",
			CategoryID: 2,
			CreatedAt:  now.Add(1 * time.Hour),
			Products:   []productentity.Product{},
		},
	}

	tests := []struct {
		name          string
		sortField     string
		sortOrder     string
		expectedFirst string
		expectedLast  string
	}{
		{
			name:          "sort by fullName case insensitive",
			sortField:     string(sharedentity.SortFieldFullName),
			sortOrder:     string(sharedentity.SortOrderAscend),
			expectedFirst: "alice manager",
			expectedLast:  "Charlie Developer",
		},
		{
			name:          "sort by category",
			sortField:     string(sharedentity.SortFieldCategory),
			sortOrder:     string(sharedentity.SortOrderAscend),
			expectedFirst: "alice manager",
			expectedLast:  "Charlie Developer",
		},
		{
			name:          "sort by createdAt",
			sortField:     string(sharedentity.SortFieldCreatedAt),
			sortOrder:     string(sharedentity.SortOrderAscend),
			expectedFirst: "alice manager",
			expectedLast:  "Charlie Developer",
		},
		{
			name:          "sort by product",
			sortField:     string(sharedentity.SortFieldProduct),
			sortOrder:     string(sharedentity.SortOrderAscend),
			expectedFirst: "alice manager",
			expectedLast:  "Bob Analyst", // No products
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUser.GetAllMock.Expect().Return(users, nil)

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			query := appquery.Users{
				Sort:   stringPtr(tt.sortField),
				Order:  stringPtr(tt.sortOrder),
				Limit:  int64Ptr(10),
				Offset: int64Ptr(0),
			}

			result, err := svc.GetAll(query)
			require.NoError(t, err)
			require.Len(t, result.Items, 3)
			require.Equal(t, tt.expectedFirst, result.Items[0].FullName)
			require.Equal(t, tt.expectedLast, result.Items[2].FullName)
		})
	}
}

func TestUserAppService_GetUsers_EdgeCasePagination(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	users := []userentity.User{
		{ID: 1, FullName: "User 1"},
		{ID: 2, FullName: "User 2"},
		{ID: 3, FullName: "User 3"},
	}

	tests := []struct {
		name          string
		limit         int64
		offset        int64
		expectedCount int
	}{
		{
			name:          "offset at boundary",
			limit:         2,
			offset:        2,
			expectedCount: 1,
		},
		{
			name:          "offset beyond data",
			limit:         2,
			offset:        5,
			expectedCount: 0,
		},
		{
			name:          "large limit",
			limit:         100,
			offset:        0,
			expectedCount: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUser.GetAllMock.Expect().Return(users, nil)

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			query := appquery.Users{
				Limit:  &tt.limit,
				Offset: &tt.offset,
			}

			result, err := svc.GetAll(query)
			require.NoError(t, err)
			require.Equal(t, int64(3), result.Total)
			require.Len(t, result.Items, tt.expectedCount)
		})
	}
}

func TestUserAppService_GetUsers_SortingEdgeCases(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	users := []userentity.User{
		{
			ID:       1,
			FullName: "User A",
			Products: []productentity.Product{
				{ID: 3, TechName: "charlie"},
				{ID: 1, TechName: "alpha"},
			},
		},
		{
			ID:       2,
			FullName: "User B",
			Products: []productentity.Product{
				{ID: 2, TechName: "beta"},
			},
		},
		{
			ID:       3,
			FullName: "User C",
			Products: []productentity.Product{},
		},
	}

	mockUser.GetAllMock.Expect().Return(users, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	query := appquery.Users{
		Sort:   stringPtr(string(sharedentity.SortFieldProduct)),
		Order:  stringPtr(string(sharedentity.SortOrderAscend)),
		Limit:  int64Ptr(10),
		Offset: int64Ptr(0),
	}

	result, err := svc.GetAll(query)
	require.NoError(t, err)
	require.Len(t, result.Items, 3)

	require.Equal(t, int64(1), result.Items[0].ID)
	require.Equal(t, int64(2), result.Items[1].ID)
	require.Equal(t, int64(3), result.Items[2].ID)

	require.Equal(t, "alpha", result.Items[0].Products[0].TechName)
	require.Equal(t, "charlie", result.Items[0].Products[1].TechName)
}

func TestUserAppService_sortUsersByProducts_MultipleProductsHandling(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	users := []userentity.User{
		{
			ID:       1,
			FullName: "User with Mixed Products",
			Products: []productentity.Product{
				{ID: 3, TechName: "zebra"},
				{ID: 1, TechName: "alpha"},
				{ID: 2, TechName: "beta"},
			},
		},
		{
			ID:       2,
			FullName: "User with Single Product",
			Products: []productentity.Product{
				{ID: 4, TechName: "delta"},
			},
		},
	}

	result := svc.sortUsersByProducts(users, true)
	require.Len(t, result, 2)

	// Check that products within each user are sorted
	require.Equal(t, "alpha", result[0].Products[0].TechName)
	require.Equal(t, "beta", result[0].Products[1].TechName)
	require.Equal(t, "zebra", result[0].Products[2].TechName)

	// Check overall order (alpha < delta, so user 1 comes first)
	require.Equal(t, int64(1), result[0].ID)
	require.Equal(t, int64(2), result[1].ID)
}

func TestUserAppService_isEmptyFilters_NilVsEmpty(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	tests := []struct {
		name     string
		filters  userentity.UserFiltersData
		expected bool
	}{
		{
			name:     "completely empty",
			filters:  userentity.UserFiltersData{},
			expected: true,
		},
		{
			name: "nil pointers",
			filters: userentity.UserFiltersData{
				Email:       nil,
				FullName:    nil,
				CategoryIDs: nil,
				IsAdmin:     nil,
				ProductIDs:  nil,
			},
			expected: true,
		},
		{
			name: "empty string pointer",
			filters: userentity.UserFiltersData{
				Email: stringPtr(""),
			},
			expected: true,
		},
		{
			name: "empty slice pointer",
			filters: userentity.UserFiltersData{
				CategoryIDs: &[]int64{},
			},
			expected: true,
		},
		{
			name: "whitespace only string",
			filters: userentity.UserFiltersData{
				Email: stringPtr("   "),
			},
			expected: false, // Current implementation doesn't trim whitespace
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := svc.isEmptyFilters(tt.filters)
			require.Equal(t, tt.expected, result, "Test case: %s", tt.name)
		})
	}
}

func TestUserAppService_NewUserAppService_ParameterValidation(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	// Test that constructor properly assigns all dependencies
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	require.NotNil(t, svc)
	require.NotNil(t, svc.groupDomain)
	require.NotNil(t, svc.participantDomain)
	require.NotNil(t, svc.productDomain)
	require.NotNil(t, svc.roleDomain)
	require.NotNil(t, svc.userDomain)
}

func TestUserAppService_Create_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	ctx := context.Background()
	email := "<EMAIL>"
	inputUser := userentity.User{Email: email}
	createdUser := userentity.User{
		ID:        1,
		Email:     email,
		FullName:  "Test User",
		CreatedAt: time.Now(),
	}

	mockUser.CreateByEmailMock.Expect(ctx, email).Return(createdUser, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.Create(ctx, inputUser)
	require.NoError(t, err)
	require.Equal(t, createdUser, result)
	require.Equal(t, uint64(1), mockUser.CreateByEmailAfterCounter())
}

func TestUserAppService_GetByID_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	user := userentity.User{
		ID:       userID,
		Email:    "<EMAIL>",
		FullName: "Test User",
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetByID(userID)
	require.NoError(t, err)
	require.Equal(t, user, result)
}

func TestUserAppService_GetUserDetailsByID_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	now := time.Now()

	user := userentity.User{
		ID:          userID,
		CategoryID:  10,
		Email:       "<EMAIL>",
		FullName:    "Test User",
		Position:    "Developer",
		IsAdmin:     false,
		LastLoginAt: &now,
		CreatedAt:   now,
		Products: []productentity.Product{
			{ID: 1, Name: "Product 1", TechName: "product1"},
		},
	}

	systemRoles := []roleentity.Role{
		{ID: 1, Name: "System Role", ActiveFlg: true, IsSystem: true},
	}

	productID := int64(1)
	productRoles := []roleentity.Role{
		{ID: 2, Name: "Product Role", ActiveFlg: true, IsSystem: false, ProductID: &productID},
	}

	userGroups := []userentity.UserGroup{
		{GroupID: 1, Name: "Test Group"},
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockRole.GetByUserIDMock.Expect(userID).Return(systemRoles, nil)
	mockRole.GetByUserIDByProductIDsMock.Expect(userID, []int64{}).Return(productRoles, nil)
	mockUser.GetUserGroupsByUserIDMock.Expect(userID).Return(userGroups, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetUserDetailsByID(userID)
	require.NoError(t, err)
	require.Equal(t, userID, result.ID)
	require.Equal(t, "<EMAIL>", result.Email)
	require.Equal(t, "Test User", result.FullName)
	require.Len(t, result.Products, 1)
	require.Len(t, result.Roles, 2)
	require.Len(t, result.Groups, 1)
}

func TestUserAppService_GetCurrentUser_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	user := userentity.User{
		ID:       userID,
		Email:    "<EMAIL>",
		FullName: "Current User",
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetCurrentUser(userID)
	require.NoError(t, err)
	require.Equal(t, user, result)
}

func TestUserAppService_GetUserProduct_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)
	products := []productentity.Product{
		{ID: 2, IID: "prod-2", Name: "B Product", TechName: "b-tech"},
		{ID: 1, IID: "prod-1", Name: "A Product", TechName: "a-tech"},
	}

	mockProduct.GetByUserIDMock.Expect(userID).Return(products, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	t.Run("without sorting", func(t *testing.T) {
		result, err := svc.GetUserProduct(userID, nil)
		require.NoError(t, err)
		require.Len(t, result, 2)
		require.Equal(t, int64(2), result[0].ID)
		require.Equal(t, int64(1), result[1].ID)
	})

	t.Run("with sorting by Name ascending", func(t *testing.T) {
		sortParams := &sharedentity.SortParams{
			Field: string(sharedentity.SortFieldTechName),
			Order: string(sharedentity.SortOrderAscend),
		}

		result, err := svc.GetUserProduct(userID, sortParams)
		require.NoError(t, err)
		require.Len(t, result, 2)

		require.Equal(t, int64(1), result[0].ID)
		require.Equal(t, int64(2), result[1].ID)
	})
}

func TestUserAppService_GetByEmail_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	email := "<EMAIL>"
	user := userentity.User{
		ID:       1,
		Email:    email,
		FullName: "Test User",
	}

	mockUser.GetByEmailMock.Expect(email).Return(user, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.GetByEmail(email)
	require.NoError(t, err)
	require.Equal(t, user, result)
}

func TestUserAppService_Update_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	ctx := context.Background()
	userID := int64(1)
	updateData := userentity.UserUpdateData{
		ID: userID,
	}

	updatedUser := userentity.User{
		ID:       userID,
		Email:    "<EMAIL>",
		FullName: "Updated User",
	}

	mockUser.UpdateMock.Expect(updateData).Return(updatedUser, nil)
	mockParticipant.UpdateParticipantByUserUpdateDataMock.Expect(userID, updateData).Return(nil)
	mockGroup.UpdateUserGroupsMock.Expect(ctx, userID, updateData).Return(nil)
	mockRole.UpdateUserRolesMock.Expect(ctx, userID, updateData).Return(nil)
	mockUser.GetByIDMock.Expect(userID).Return(updatedUser, nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	result, err := svc.Update(ctx, updateData)
	require.NoError(t, err)
	require.Equal(t, updatedUser, result)
}

func TestUserAppService_DeleteUserProducts_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	ctx := context.Background()
	userID := int64(1)
	productIDs := []int64{1, 2, 3}

	mockParticipant.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, productIDs).Return(nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteUserProducts(ctx, userID, productIDs)
	require.NoError(t, err)
}

func TestUserAppService_DeleteUserGroups_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	ctx := context.Background()
	userID := int64(1)
	groupIDs := []int64{1, 2, 3}

	mockUser.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, groupIDs).Return(nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteUserGroups(ctx, userID, groupIDs)
	require.NoError(t, err)
}

func TestUserAppService_DeleteUserRoles_Success(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	ctx := context.Background()
	userID := int64(1)
	roleIDs := []int64{1, 2, 3}

	mockUser.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, roleIDs).Return(nil)

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	err := svc.DeleteUserRoles(ctx, userID, roleIDs)
	require.NoError(t, err)
}

func TestUserAppService_Update_ErrorInSteps(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	ctx := context.Background()
	userID := int64(1)
	updateData := userentity.UserUpdateData{
		ID: userID,
	}

	tests := []struct {
		name        string
		setupMocks  func()
		expectedErr string
	}{
		{
			name: "user update error",
			setupMocks: func() {
				mockUser.UpdateMock.Expect(updateData).Return(userentity.User{}, errors.New("user update failed"))
			},
			expectedErr: "user update failed",
		},
		{
			name: "participant update error",
			setupMocks: func() {
				mockUser.UpdateMock.Expect(updateData).Return(userentity.User{ID: userID}, nil)
				mockParticipant.UpdateParticipantByUserUpdateDataMock.Expect(userID, updateData).Return(errors.New("participant update failed"))
			},
			expectedErr: "participant update failed",
		},
		{
			name: "group update error",
			setupMocks: func() {
				mockUser.UpdateMock.Expect(updateData).Return(userentity.User{ID: userID}, nil)
				mockParticipant.UpdateParticipantByUserUpdateDataMock.Expect(userID, updateData).Return(nil)
				mockGroup.UpdateUserGroupsMock.Expect(ctx, userID, updateData).Return(errors.New("group update failed"))
			},
			expectedErr: "group update failed",
		},
		{
			name: "role update error",
			setupMocks: func() {
				mockUser.UpdateMock.Expect(updateData).Return(userentity.User{ID: userID}, nil)
				mockParticipant.UpdateParticipantByUserUpdateDataMock.Expect(userID, updateData).Return(nil)
				mockGroup.UpdateUserGroupsMock.Expect(ctx, userID, updateData).Return(nil)
				mockRole.UpdateUserRolesMock.Expect(ctx, userID, updateData).Return(errors.New("role update failed"))
			},
			expectedErr: "role update failed",
		},
		{
			name: "final get user error",
			setupMocks: func() {
				mockUser.UpdateMock.Expect(updateData).Return(userentity.User{ID: userID}, nil)
				mockParticipant.UpdateParticipantByUserUpdateDataMock.Expect(userID, updateData).Return(nil)
				mockGroup.UpdateUserGroupsMock.Expect(ctx, userID, updateData).Return(nil)
				mockRole.UpdateUserRolesMock.Expect(ctx, userID, updateData).Return(nil)
				mockUser.GetByIDMock.Expect(userID).Return(userentity.User{}, errors.New("get user failed"))
			},
			expectedErr: "get user failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			_, err := svc.Update(ctx, updateData)
			require.Error(t, err)
			require.Contains(t, err.Error(), tt.expectedErr)
		})
	}
}

func TestUserAppService_GetUserDetailsByID_ErrorHandling(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)

	tests := []struct {
		name        string
		setupMocks  func()
		expectedErr string
	}{
		{
			name: "user not found",
			setupMocks: func() {
				mockUser.GetByIDMock.Expect(userID).Return(userentity.User{}, errors.New("user not found"))
			},
			expectedErr: "user not found",
		},
		{
			name: "roles get error",
			setupMocks: func() {
				user := userentity.User{ID: userID, Email: "<EMAIL>"}
				mockUser.GetByIDMock.Expect(userID).Return(user, nil)
				mockRole.GetByUserIDMock.Expect(userID).Return(nil, errors.New("roles error"))
			},
			expectedErr: "roles error",
		},
		{
			name: "product roles get error",
			setupMocks: func() {
				user := userentity.User{ID: userID, Email: "<EMAIL>"}
				mockUser.GetByIDMock.Expect(userID).Return(user, nil)
				mockRole.GetByUserIDMock.Expect(userID).Return([]roleentity.Role{}, nil)
				mockRole.GetByUserIDByProductIDsMock.Expect(userID, []int64{}).Return(nil, errors.New("product roles error"))
			},
			expectedErr: "product roles error",
		},
		{
			name: "user groups get error",
			setupMocks: func() {
				user := userentity.User{ID: userID, Email: "<EMAIL>"}
				mockUser.GetByIDMock.Expect(userID).Return(user, nil)
				mockRole.GetByUserIDMock.Expect(userID).Return([]roleentity.Role{}, nil)
				mockRole.GetByUserIDByProductIDsMock.Expect(userID, []int64{}).Return([]roleentity.Role{}, nil)
				mockUser.GetUserGroupsByUserIDMock.Expect(userID).Return(nil, errors.New("user groups error"))
			},
			expectedErr: "user groups error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			_, err := svc.GetUserDetailsByID(userID)
			require.Error(t, err)
			require.Contains(t, err.Error(), tt.expectedErr)
		})
	}
}

func TestUserAppService_GetUserProduct_ErrorHandling(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	userID := int64(1)

	mockProduct.GetByUserIDMock.Expect(userID).Return(nil, errors.New("product get failed"))

	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	_, err := svc.GetUserProduct(userID, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "product get failed")
}

func TestUserAppService_GetUsers_ErrorHandling(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)

	tests := []struct {
		name        string
		query       appquery.Users
		setupMocks  func()
		expectedErr string
	}{
		{
			name:  "GetAll error",
			query: appquery.Users{},
			setupMocks: func() {
				mockUser.GetAllMock.Expect().Return(nil, errors.New("get all failed"))
			},
			expectedErr: "get all failed",
		},
		{
			name: "GetByFilters error",
			query: appquery.Users{
				Search: stringPtr("<EMAIL>"),
			},
			setupMocks: func() {
				mockUser.GetByFiltersMock.Expect(userentity.UserFiltersData{
					Email:    stringPtr("<EMAIL>"),
					FullName: stringPtr("<EMAIL>"),
				}).Return(nil, errors.New("get by filters failed"))
			},
			expectedErr: "get by filters failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

			_, err := svc.GetAll(tt.query)
			require.Error(t, err)
			require.Contains(t, err.Error(), tt.expectedErr)
		})
	}
}

func TestUserAppService_sortUsers_CompleteFieldCoverage(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	now := time.Now()
	users := []userentity.User{
		{ID: 1, FullName: "Charlie", CategoryID: 3, CreatedAt: now.Add(2 * time.Hour)},
		{ID: 2, FullName: "Alice", CategoryID: 1, CreatedAt: now},
		{ID: 3, FullName: "Bob", CategoryID: 2, CreatedAt: now.Add(1 * time.Hour)},
	}

	// Test descending order for each field
	tests := []struct {
		name          string
		field         string
		order         string
		expectedFirst int64
	}{
		{
			name:          "fullName descending",
			field:         string(sharedentity.SortFieldFullName),
			order:         string(sharedentity.SortOrderDescend),
			expectedFirst: 1, // Charlie
		},
		{
			name:          "category descending",
			field:         string(sharedentity.SortFieldCategory),
			order:         string(sharedentity.SortOrderDescend),
			expectedFirst: 1, // CategoryID: 3
		},
		{
			name:          "createdAt descending",
			field:         string(sharedentity.SortFieldCreatedAt),
			order:         string(sharedentity.SortOrderDescend),
			expectedFirst: 1, // Latest time
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sortParams := sharedentity.SortParams{
				Field: tt.field,
				Order: tt.order,
			}

			result := svc.sortUsers(users, sortParams)
			require.Equal(t, tt.expectedFirst, result[0].ID)
		})
	}
}

func TestUserAppService_sortUsersByProducts_EdgeCases(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	tests := []struct {
		name      string
		users     []userentity.User
		ascending bool
		expected  []string
	}{
		{
			name: "both users have no products",
			users: []userentity.User{
				{ID: 1, FullName: "User A", Products: []productentity.Product{}},
				{ID: 2, FullName: "User B", Products: []productentity.Product{}},
			},
			ascending: true,
			expected:  []string{"User A", "User B"},
		},
		{
			name: "same first product names",
			users: []userentity.User{
				{ID: 1, FullName: "User A", Products: []productentity.Product{{TechName: "same"}}},
				{ID: 2, FullName: "User B", Products: []productentity.Product{{TechName: "same"}}},
			},
			ascending: true,
			expected:  []string{"User A", "User B"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := svc.sortUsersByProducts(tt.users, tt.ascending)
			require.Len(t, result, len(tt.expected))
			for i, expectedName := range tt.expected {
				require.Equal(t, expectedName, result[i].FullName)
			}
		})
	}
}

func TestUserAppService_sortProducts(t *testing.T) {
	mockGroup, mockParticipant, mockProduct, mockRole, mockUser := createMocks(t)
	svc := NewUserAppService(mockGroup, mockParticipant, mockProduct, mockRole, mockUser)

	products := []userentity.UserProduct{
		{ID: 1, TechName: "zebra", Name: "Zebra Product"},
		{ID: 2, TechName: "alpha", Name: "Alpha Product"},
		{ID: 3, TechName: "beta", Name: "Beta Product"},
	}

	tests := []struct {
		name        string
		sortParams  sharedentity.SortParams
		expectedIDs []int64
	}{
		{
			name: "sort by TechName ascending",
			sortParams: sharedentity.SortParams{
				Field: string(sharedentity.SortFieldTechName),
				Order: string(sharedentity.SortOrderAscend),
			},
			expectedIDs: []int64{2, 3, 1}, // alpha, beta, zebra
		},
		{
			name: "sort by TechName descending",
			sortParams: sharedentity.SortParams{
				Field: string(sharedentity.SortFieldTechName),
				Order: string(sharedentity.SortOrderDescend),
			},
			expectedIDs: []int64{1, 3, 2}, // zebra, beta, alpha
		},
		{
			name: "sort by unknown field - no sorting applied",
			sortParams: sharedentity.SortParams{
				Field: "unknown_field",
				Order: string(sharedentity.SortOrderAscend),
			},
			expectedIDs: []int64{1, 2, 3}, // original order
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy to avoid modifying the original slice
			productsCopy := make([]userentity.UserProduct, len(products))
			copy(productsCopy, products)

			result := svc.sortProducts(productsCopy, tt.sortParams)
			require.Len(t, result, 3)

			for i, expectedID := range tt.expectedIDs {
				require.Equal(t, expectedID, result[i].ID, "Index %d: expected ID %d, got %d", i, expectedID, result[i].ID)
			}
		})
	}
}

// Helper functions for pointer creation
func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}

func int64Ptr(i int64) *int64 {
	return &i
}
