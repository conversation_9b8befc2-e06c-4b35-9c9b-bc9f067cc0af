package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminuser"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/user"
	roleappquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	appquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/query"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	useraggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/aggregate"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func checkPhoto(photo *[]byte) string {
	if photo != nil {
		return string(*photo)
	}
	return ""
}

func ToAdminRolesFromGetUserRolesWithProductsAsParams(params user.GetUserRolesWithProductsParams) roleappquery.AdminRoles {

	productIDs := make([]int64, 0)
	if params.ProductIDs != nil {
		productIDs = *params.ProductIDs
	}

	query := roleappquery.AdminRoles{
		ProductIDs: productIDs,
		Type:       (*string)(params.Type),
		Sort:       (*string)(params.Sort),
		Order:      (*string)(params.Order),
	}

	return query
}

func ToModelAdminUserUpdateFromV1DTO(userID int64, userData adminuser.AdminUserUpdateV1DTO) useraggregate.AdminUserUpdateData {
	var roles *[]roleentity.RoleProductLink
	if userData.Roles != nil {
		r := make([]roleentity.RoleProductLink, len(*userData.Roles))
		for i, role := range *userData.Roles {
			r[i] = roleentity.RoleProductLink{
				RoleID:    role.ID,
				ProductID: role.ProductID,
			}
		}
		roles = &r
	}

	var groups *[]groupentity.GroupProductLink
	if userData.Groups != nil {
		g := make([]groupentity.GroupProductLink, len(*userData.Groups))
		for i, group := range *userData.Groups {
			g[i] = groupentity.GroupProductLink{
				GroupID:   group.ID,
				ProductID: group.ProductID,
			}
		}
		groups = &g
	}

	var products *[]int64
	if userData.Products != nil {
		p := make([]int64, len(*userData.Products))
		for i, product := range *userData.Products {
			p[i] = product.ID
		}
		products = &p
	}

	return useraggregate.AdminUserUpdateData{
		ID:         userID,
		IsAdmin:    userData.IsAdmin,
		CategoryID: userData.CategoryID,
		RoleIDs:    roles,
		GroupIDs:   groups,
		ProductIDs: products,
	}
}

func ToModelUserFromV1DTOCreate(u user.UserCreateV1DTO) userentity.User {
	return userentity.User{
		Email: u.Email,
	}
}

func ToModelUserUpdateFromV1DTOAdminUserUpdate(userID int64, userData adminuser.AdminUserUpdateV1DTO) userentity.UserUpdateData {
	var roles *[]userentity.UserRoleUpdateData
	// if userData.Roles != nil {
	// 	r := make([]userentity.UserRoleUpdateData, len(*userData.Roles))
	// 	for i, role := range *userData.Roles {
	// 		r[i] = userentity.UserRoleUpdateData{
	// 			UserID:    &userID,
	// 			RoleID:    &role.Id,
	// 			ProductID: role.ProductID,
	// 		}
	// 	}
	// 	roles = &r
	// }

	var groups *[]userentity.UserGroupUpdateData
	// if userData.Groups != nil {
	// 	g := make([]userentity.UserGroupUpdateData, len(*userData.Groups))
	// 	for i, group := range *userData.Groups {
	// 		g[i] = userentity.UserGroupUpdateData{
	// 			UserID:    &userID,
	// 			GroupID:   &group.Id,
	// 			ProductID: group.ProductID,
	// 		}
	// 	}
	// 	groups = &g
	// }

	var products *[]int64
	// if userData.Products != nil {
	// 	p := make([]int64, len(*userData.Products))
	// 	for i, product := range *userData.Products {
	// 		p[i] = product.Id
	// 	}
	// 	products = &p
	// }

	return userentity.UserUpdateData{
		ID:         userID,
		CategoryID: userData.CategoryID,
		IsAdmin:    userData.IsAdmin,
		ProductIDs: products,
		Roles:      roles,
		Groups:     groups,
	}
}

func ToModelUserUpdateFromV1DTOUpdate(userID int64, u user.UserUpdateV1DTO) userentity.UserUpdateData {
	return userentity.UserUpdateData{
		ID:         userID,
		CategoryID: u.CategoryID,
		IsAdmin:    u.IsAdmin,
		RoleIDs:    u.RoleIDs,
		GroupIDs:   u.GroupIDs,
		ProductIDs: u.ProductIDs,
	}
}

func ToQueryAdminUsersFromGetUsersAsAdminParams(params adminuser.GetUsersAsAdminParams) appquery.AdminUser {

	productIDs := make([]int64, 0)
	if params.ProductIDs != nil {
		productIDs = *params.ProductIDs
	}

	categoryIDs := make([]int64, 0)
	if params.CategoryIDs != nil {
		categoryIDs = *params.CategoryIDs
	}

	query := appquery.AdminUser{
		Search:      params.Search,
		ProductIDs:  productIDs,
		CategoryIDs: categoryIDs,
		IsAdmin:     params.IsAdmin,
		Sort:        (*string)(params.Sort),
		Order:       (*string)(params.Order),
		Limit:       params.Limit,
		Offset:      params.Offset,
	}

	return query
}

func ToQueryUsersFromGetUsersAsParams(params user.GetUsersParams) appquery.Users {

	var productIDs *[]int64
	if params.ProductIDs != nil {
		copied := make([]int64, len(*params.ProductIDs))
		copy(copied, *params.ProductIDs)
		productIDs = &copied
	}

	var categoryIDs *[]int64
	if params.CategoryIDs != nil {
		copied := make([]int64, len(*params.CategoryIDs))
		copy(copied, *params.CategoryIDs)
		categoryIDs = &copied
	}

	query := appquery.Users{
		Search:      params.Search,
		ProductIDs:  productIDs,
		CategoryIDs: categoryIDs,
		IsAdmin:     params.IsAdmin,
		Sort:        (*string)(params.Sort),
		Order:       (*string)(params.Order),
		Limit:       params.Limit,
		Offset:      params.Offset,
	}

	return query
}

func ToSortParamsFromGetUserProductsAsParams(params user.GetUserProductsParams) sharedentity.SortParams {

	var sortParams sharedentity.SortParams
	if params.Sort != nil && params.Order != nil {
		sortParams = sharedentity.SortParams{
			Field: string(*params.Sort),
			Order: string(*params.Order),
		}
	}

	return sortParams
}

func ToUserFiltersDataFromGetUserGroupsAsParams(params user.GetUserGroupsWithProductsParams) userentity.UserFiltersData {

	var productIDs *[]int64
	if params.ProductIDs != nil {
		copied := make([]int64, len(*params.ProductIDs))
		copy(copied, *params.ProductIDs)
		productIDs = &copied
	}

	data := userentity.UserFiltersData{
		ProductIDs: productIDs,
	}

	return data
}

func ToV1DTOAdminUserCollectionFromModels(paginatedResult sharedentity.PaginatedResult[userentity.AdminUser]) adminuser.AdminUserCollectionV1DTO {
	var items []adminuser.AdminUserV1DTO
	for _, adminUser := range paginatedResult.Items {
		items = append(items, ToV1DTOAdminUserFromEntity(adminUser))
	}
	return adminuser.AdminUserCollectionV1DTO{
		Items: items,
		Meta: adminuser.PaginationV1DTO{
			Limit:  paginatedResult.Limit,
			Offset: paginatedResult.Offset,
			Total:  paginatedResult.Total,
		},
	}
}

func ToV1DTOAdminUserFromEntity(u userentity.AdminUser) adminuser.AdminUserV1DTO {
	var products *[]adminuser.ProductBasicV1DTO
	if len(u.Products) > 0 {
		productsList := make([]adminuser.ProductBasicV1DTO, 0, len(u.Products))
		for _, p := range u.Products {
			ad := adminuser.ProductBasicV1DTO{
				ID:       p.ID,
				IID:      p.IID,
				Name:     p.Name,
				TechName: p.TechName,
			}
			productsList = append(productsList, ad)
		}
		products = &productsList
	}

	return adminuser.AdminUserV1DTO{
		ID:          u.ID,
		Category:    u.CategoryID,
		Email:       u.Email,
		FullName:    u.FullName,
		Position:    u.Position,
		IsAdmin:     u.IsAdmin,
		LastLoginAt: u.LastLoginAt,
		CreatedAt:   u.CreatedAt,
		Products:    products,
	}
}

func ToV1DTOGroupWithProductCollectionFromModel(group groupentity.GroupWithProductCollection) user.GroupWithProductCollectionV1DTO {
	var product *user.ProductBasicV1DTO
	if group.Product != nil {
		product = ToV1DTOUserProductFromModel(*group.Product)
	}
	return user.GroupWithProductCollectionV1DTO{
		ID:      group.GroupID,
		Name:    group.GroupName,
		Type:    constants.IsSystemToConstant(group.IsSystem),
		Product: product,
	}
}

func ToV1DTOGroupWithProductIDFromEntity(g []groupentity.GroupWithProduct) []adminuser.GroupWithProductIDV1DTO {
	groups := make([]adminuser.GroupWithProductIDV1DTO, len(g))
	for i, group := range g {
		var productID *int64
		if group.Product != nil {
			productID = &group.Product.ID
		}
		groups[i] = adminuser.GroupWithProductIDV1DTO{
			ID:        group.ID,
			Name:      group.Name,
			Type:      constants.IsSystemToConstant(group.IsSystem),
			IsActive:  group.ActiveFlg,
			ProductID: productID,
		}
	}
	return groups
}

func ToV1DTOProductBasicFromEntity(p []productentity.ProductBasic) []adminuser.ProductBasicV1DTO {
	products := make([]adminuser.ProductBasicV1DTO, len(p))
	for i, product := range p {
		adProduct := adminuser.ProductBasicV1DTO{
			ID:       product.ID,
			Name:     product.Name,
			TechName: product.TechName,
		}

		if product.IID != nil {
			adProduct.IID = product.IID
		}

		products[i] = adProduct
	}
	return products
}

func ToV1DTORoleWithProductCollectionFromModel(roles []roleentity.AdminRole) user.RolesWithProductsCollectionV1DTO {
	var dto user.RolesWithProductsCollectionV1DTO
	for _, role := range roles {
		var product *user.ProductBasicV1DTO
		if role.Product != nil {
			product = &user.ProductBasicV1DTO{
				ID:       role.Product.ID,
				IID:      role.Product.IID,
				Name:     role.Product.Name,
				TechName: role.Product.TechName,
			}
		}
		dto.Items = append(dto.Items, user.RoleWithProductV1DTO{
			ID:      role.ID,
			Name:    role.Name,
			Type:    constants.IsSystemToConstant(role.IsSystem),
			Product: product,
		})
	}
	return dto
}

func ToV1DTORoleWithProductIDFromEntity(r []roleentity.RoleWithProduct) []adminuser.RoleWithProductIDV1DTO {
	roles := make([]adminuser.RoleWithProductIDV1DTO, len(r))
	for i, role := range r {
		var productID *int64
		if role.Product != nil {
			productID = &role.Product.ID
		}
		roles[i] = adminuser.RoleWithProductIDV1DTO{
			ID:        role.ID,
			Name:      role.Name,
			Type:      constants.IsSystemToConstant(role.IsSystem),
			IsActive:  role.ActiveFlg,
			ProductID: productID,
		}
	}
	return roles
}

func ToV1DTOsAdminUsersFromModels(users []userentity.AdminUser) []adminuser.AdminUserV1DTO {
	usersDTO := make([]adminuser.AdminUserV1DTO, len(users))
	for i, u := range users {
		usersDTO[i] = ToV1DTOAdminUserFromEntity(u)
	}
	return usersDTO
}

func ToV1DTOsGroupWithProductCollectionFromModels(groups []groupentity.GroupWithProductCollection) []user.GroupWithProductCollectionV1DTO {
	dtos := make([]user.GroupWithProductCollectionV1DTO, len(groups))
	for i, group := range groups {
		dtos[i] = ToV1DTOGroupWithProductCollectionFromModel(group)
	}
	return dtos
}

func ToV1DTOsUserFromModels(users []userentity.User) []user.UserV1DTO {
	usersDTO := make([]user.UserV1DTO, len(users))
	for i, u := range users {
		usersDTO[i] = ToV1DTOUserFromEntity(u)
	}
	return usersDTO
}

func ToV1DTOsUserProductFromModels(products []userentity.UserProduct) []user.ProductBasicV1DTO {
	userProducts := make([]user.ProductBasicV1DTO, len(products))
	for i, p := range products {
		userProducts[i] = *ToV1DTOUserProductFromModel(p)
	}
	return userProducts
}

func ToV1DTOUserCollectionFromModels(paginatedResult sharedentity.PaginatedResult[userentity.User]) user.UserCollectionV1DTO {
	var items []user.UserV1DTO
	for _, adminUser := range paginatedResult.Items {
		items = append(items, ToV1DTOUserFromEntity(adminUser))
	}
	return user.UserCollectionV1DTO{
		Items: items,
		Meta: user.PaginationV1DTO{
			Limit:  paginatedResult.Limit,
			Offset: paginatedResult.Offset,
			Total:  paginatedResult.Total,
		},
	}
}

func ToV1DTOUserFromEntity(u userentity.User) user.UserV1DTO {
	products := make([]user.ProductBasicV1DTO, 0, len(u.Products))
	for _, p := range u.Products {
		products = append(products, user.ProductBasicV1DTO{
			ID:       p.ID,
			IID:      &p.IID,
			Name:     p.Name,
			TechName: p.TechName,
		})
	}
	return user.UserV1DTO{
		ID:          u.ID,
		Category:    u.CategoryID,
		Email:       u.Email,
		FullName:    u.FullName,
		Position:    u.Position,
		IsAdmin:     u.IsAdmin,
		LastLoginAt: u.LastLoginAt,
		Photo:       checkPhoto(u.Photo),
		CreatedAt:   u.CreatedAt,
		Products:    &products,
	}
}

func ToV1DTOUserProductCollectionFromModels(products []userentity.UserProduct) user.UserProductCollectionV1DTO {
	return user.UserProductCollectionV1DTO{
		Items: ToV1DTOsUserProductFromModels(products),
	}
}

func ToV1DTOUserProductFromModel(product userentity.UserProduct) *user.ProductBasicV1DTO {
	return &user.ProductBasicV1DTO{
		ID:       product.ID,
		IID:      &product.IID,
		Name:     product.Name,
		TechName: product.TechName,
	}
}

func ToV1DTOUserWithDetailsFromAggregate(u useraggregate.UserWithDetails) adminuser.UserWithDetailsV1DTO {
	return adminuser.UserWithDetailsV1DTO{
		ID:          u.ID,
		Category:    u.CategoryID,
		Email:       u.Email,
		FullName:    u.FullName,
		Position:    u.Position,
		IsAdmin:     u.IsAdmin,
		LastLoginAt: u.LastLoginAt,
		Photo:       checkPhoto(u.Photo),
		CreatedAt:   u.CreatedAt,
		Products:    ToV1DTOProductBasicFromEntity(u.Products),
		Groups:      ToV1DTOGroupWithProductIDFromEntity(u.Groups),
		Roles:       ToV1DTORoleWithProductIDFromEntity(u.Roles),
	}
}
