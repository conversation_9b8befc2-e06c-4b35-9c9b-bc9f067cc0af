package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminuser"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/user"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	useraggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/aggregate"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestToV1DTOUserFromEntity_Success(t *testing.T) {
	lastLoginAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	photo := []byte("photo_data")

	user := userentity.User{
		ID:          123,
		CategoryID:  456,
		Email:       "<EMAIL>",
		FullName:    "Test User",
		Position:    "Developer",
		IsAdmin:     true,
		LastLoginAt: &lastLoginAt,
		Photo:       &photo,
		CreatedAt:   time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
		Products: []productentity.Product{
			{ID: 1, IID: "P001", Name: "Product 1", TechName: "product1"},
			{ID: 2, IID: "P002", Name: "Product 2", TechName: "product2"},
		},
	}

	result := ToV1DTOUserFromEntity(user)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, int64(456), result.Category)
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, "Test User", result.FullName)
	assert.Equal(t, "Developer", result.Position)
	assert.True(t, result.IsAdmin)
	assert.Equal(t, &lastLoginAt, result.LastLoginAt)
	assert.Equal(t, "photo_data", result.Photo)
	assert.Equal(t, time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC), result.CreatedAt)

	require.NotNil(t, result.Products)
	require.Len(t, *result.Products, 2)
	assert.Equal(t, int64(1), (*result.Products)[0].ID)
	assert.Equal(t, "P001", *(*result.Products)[0].IID)
	assert.Equal(t, "Product 1", (*result.Products)[0].Name)
	assert.Equal(t, "product1", (*result.Products)[0].TechName)
}

func TestToV1DTOUserFromEntity_EmptyProducts(t *testing.T) {
	user := userentity.User{
		ID:       123,
		Email:    "<EMAIL>",
		Products: []productentity.Product{},
	}

	result := ToV1DTOUserFromEntity(user)

	require.NotNil(t, result.Products)
	assert.Len(t, *result.Products, 0)
}

func TestToV1DTOUserFromEntity_NilPhoto(t *testing.T) {
	user := userentity.User{
		ID:    123,
		Email: "<EMAIL>",
		Photo: nil,
	}

	result := ToV1DTOUserFromEntity(user)

	assert.Equal(t, "", result.Photo)
}

func TestToQueryAdminUsersFromGetUsersAsAdminParams_Success(t *testing.T) {
	search := "test"
	sort := "name"
	order := "asc"
	productIDs := []int64{1, 2, 3}
	categoryIDs := []int64{4, 5, 6}
	isAdmin := true
	limit := int64(10)
	offset := int64(20)

	params := adminuser.GetUsersAsAdminParams{
		Search:      &search,
		ProductIDs:  &productIDs,
		CategoryIDs: &categoryIDs,
		IsAdmin:     &isAdmin,
		Sort:        (*adminuser.GetUsersAsAdminParamsSort)(&sort),
		Order:       (*adminuser.GetUsersAsAdminParamsOrder)(&order),
		Limit:       &limit,
		Offset:      &offset,
	}

	result := ToQueryAdminUsersFromGetUsersAsAdminParams(params)

	assert.Equal(t, &search, result.Search)
	assert.Equal(t, productIDs, result.ProductIDs)
	assert.Equal(t, categoryIDs, result.CategoryIDs)
	assert.Equal(t, &isAdmin, result.IsAdmin)
	assert.Equal(t, &sort, result.Sort)
	assert.Equal(t, &order, result.Order)
	assert.Equal(t, &limit, result.Limit)
	assert.Equal(t, &offset, result.Offset)
}

func TestToQueryAdminUsersFromGetUsersAsAdminParams_NilSlices(t *testing.T) {
	limit := int64(10)
	offset := int64(0)

	params := adminuser.GetUsersAsAdminParams{
		ProductIDs:  nil,
		CategoryIDs: nil,
		Limit:       &limit,
		Offset:      &offset,
	}

	result := ToQueryAdminUsersFromGetUsersAsAdminParams(params)

	assert.Empty(t, result.ProductIDs)
	assert.Empty(t, result.CategoryIDs)
	assert.Nil(t, result.Search)
	assert.Nil(t, result.IsAdmin)
}

func TestToV1DTOsGroupWithProductCollectionFromModels_Success(t *testing.T) {
	groups := []groupentity.GroupWithProductCollection{
		{
			GroupID:   1,
			GroupName: "Group 1",
			IsSystem:  true,
			Product: &userentity.UserProduct{
				ID:       10,
				IID:      "P001",
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			GroupID:   2,
			GroupName: "Group 2",
			IsSystem:  false,
			Product:   nil,
		},
	}

	result := ToV1DTOsGroupWithProductCollectionFromModels(groups)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	require.NotNil(t, result[0].Product)
	assert.Equal(t, int64(10), result[0].Product.ID)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Group 2", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
	assert.Nil(t, result[1].Product)
}

func TestToV1DTOsGroupWithProductCollectionFromModels_Empty(t *testing.T) {
	var groups []groupentity.GroupWithProductCollection

	result := ToV1DTOsGroupWithProductCollectionFromModels(groups)

	assert.Empty(t, result)
}

func TestToV1DTOUserProductFromModel_Success(t *testing.T) {
	product := userentity.UserProduct{
		ID:       123,
		IID:      "P001",
		Name:     "Test Product",
		TechName: "test_product",
	}

	result := ToV1DTOUserProductFromModel(product)

	require.NotNil(t, result)
	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "P001", *result.IID)
	assert.Equal(t, "Test Product", result.Name)
	assert.Equal(t, "test_product", result.TechName)
}

func TestToV1DTOsUserProductFromModels_Success(t *testing.T) {
	products := []userentity.UserProduct{
		{ID: 1, IID: "P001", Name: "Product 1", TechName: "product1"},
		{ID: 2, IID: "P002", Name: "Product 2", TechName: "product2"},
	}

	result := ToV1DTOsUserProductFromModels(products)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "P001", *result[0].IID)
	assert.Equal(t, "Product 1", result[0].Name)
	assert.Equal(t, "product1", result[0].TechName)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "P002", *result[1].IID)
	assert.Equal(t, "Product 2", result[1].Name)
	assert.Equal(t, "product2", result[1].TechName)
}

func TestToV1DTOUserProductCollectionFromModels_Success(t *testing.T) {
	products := []userentity.UserProduct{
		{ID: 1, IID: "P001", Name: "Product 1", TechName: "product1"},
		{ID: 2, IID: "P002", Name: "Product 2", TechName: "product2"},
	}

	result := ToV1DTOUserProductCollectionFromModels(products)

	require.Len(t, result.Items, 2)
	assert.Equal(t, int64(1), result.Items[0].ID)
	assert.Equal(t, "Product 1", result.Items[0].Name)
}

func TestToModelUserFromV1DTOCreate_Success(t *testing.T) {
	dto := user.UserCreateV1DTO{
		Email: "<EMAIL>",
	}

	result := ToModelUserFromV1DTOCreate(dto)

	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, int64(0), result.ID)
}

func TestToV1DTOsUserFromModels_Success(t *testing.T) {
	users := []userentity.User{
		{ID: 1, Email: "<EMAIL>", FullName: "User 1"},
		{ID: 2, Email: "<EMAIL>", FullName: "User 2"},
	}

	result := ToV1DTOsUserFromModels(users)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "<EMAIL>", result[0].Email)
	assert.Equal(t, "User 1", result[0].FullName)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "<EMAIL>", result[1].Email)
	assert.Equal(t, "User 2", result[1].FullName)
}

func TestToV1DTOsAdminUsersFromModels_Success(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "Admin 1", IsAdmin: true},
		{ID: 2, Email: "<EMAIL>", FullName: "Admin 2", IsAdmin: false},
	}

	result := ToV1DTOsAdminUsersFromModels(users)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "<EMAIL>", result[0].Email)
	assert.Equal(t, "Admin 1", result[0].FullName)
	assert.True(t, result[0].IsAdmin)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "<EMAIL>", result[1].Email)
	assert.Equal(t, "Admin 2", result[1].FullName)
	assert.False(t, result[1].IsAdmin)
}

func TestToV1DTOAdminUserCollectionFromModels_Success(t *testing.T) {
	users := []userentity.AdminUser{
		{ID: 1, Email: "<EMAIL>", FullName: "User 1"},
		{ID: 2, Email: "<EMAIL>", FullName: "User 2"},
	}

	paginatedResult := sharedentity.PaginatedResult[userentity.AdminUser]{
		Items:  users,
		Limit:  10,
		Offset: 20,
		Total:  100,
	}

	result := ToV1DTOAdminUserCollectionFromModels(paginatedResult)

	require.Len(t, result.Items, 2)
	assert.Equal(t, int64(1), result.Items[0].ID)
	assert.Equal(t, "<EMAIL>", result.Items[0].Email)

	assert.Equal(t, int64(10), result.Meta.Limit)
	assert.Equal(t, int64(20), result.Meta.Offset)
	assert.Equal(t, int64(100), result.Meta.Total)
}

func TestToV1DTOAdminUserFromEntity_WithProducts(t *testing.T) {
	lastLoginAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)

	user := userentity.AdminUser{
		ID:          123,
		CategoryID:  456,
		Email:       "<EMAIL>",
		FullName:    "Admin User",
		Position:    "Administrator",
		IsAdmin:     true,
		LastLoginAt: &lastLoginAt,
		CreatedAt:   time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
		Products: []productentity.ProductBasic{
			{ID: 1, IID: stringPtr("P001"), Name: "Product 1", TechName: "product1"},
			{ID: 2, IID: stringPtr("P002"), Name: "Product 2", TechName: "product2"},
		},
	}

	result := ToV1DTOAdminUserFromEntity(user)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, int64(456), result.Category)
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, "Admin User", result.FullName)
	assert.Equal(t, "Administrator", result.Position)
	assert.True(t, result.IsAdmin)

	require.NotNil(t, result.Products)
	require.Len(t, *result.Products, 2)
	assert.Equal(t, int64(1), (*result.Products)[0].ID)
	assert.Equal(t, stringPtr("P001"), (*result.Products)[0].IID)
	assert.Equal(t, "Product 1", (*result.Products)[0].Name)
	assert.Equal(t, "product1", (*result.Products)[0].TechName)
}

func TestToV1DTOAdminUserFromEntity_WithoutProducts(t *testing.T) {
	user := userentity.AdminUser{
		ID:       123,
		Email:    "<EMAIL>",
		Products: []productentity.ProductBasic{},
	}

	result := ToV1DTOAdminUserFromEntity(user)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Nil(t, result.Products)
}

func TestToModelUserUpdateFromV1DTOUpdate_Success(t *testing.T) {
	userID := int64(123)
	categoryID := int64(456)
	isAdmin := true
	roleIDs := []int64{1, 2, 3}
	groupIDs := []int64{4, 5, 6}
	productIDs := []int64{7, 8, 9}

	dto := user.UserUpdateV1DTO{
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		RoleIDs:    &roleIDs,
		GroupIDs:   &groupIDs,
		ProductIDs: &productIDs,
	}

	result := ToModelUserUpdateFromV1DTOUpdate(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Equal(t, &categoryID, result.CategoryID)
	assert.Equal(t, &isAdmin, result.IsAdmin)
	assert.Equal(t, &roleIDs, result.RoleIDs)
	assert.Equal(t, &groupIDs, result.GroupIDs)
	assert.Equal(t, &productIDs, result.ProductIDs)
}

func TestToV1DTOUserWithDetailsFromAggregate_Success(t *testing.T) {
	lastLoginAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	photo := []byte("photo_data")

	user := useraggregate.UserWithDetails{
		User: userentity.User{
			ID:          123,
			CategoryID:  456,
			Email:       "<EMAIL>",
			FullName:    "Test User",
			Position:    "Developer",
			IsAdmin:     true,
			LastLoginAt: &lastLoginAt,
			Photo:       &photo,
			CreatedAt:   time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
		},
		Products: []productentity.ProductBasic{
			{ID: 1, IID: stringPtr("P001"), Name: "Product 1", TechName: "product1"},
		},
		Groups: []groupentity.GroupWithProduct{
			{ID: 1, Name: "Group 1", IsSystem: true, ActiveFlg: true},
		},
		Roles: []roleentity.RoleWithProduct{
			{ID: 1, Name: "Role 1", IsSystem: true, ActiveFlg: true},
		},
	}

	result := ToV1DTOUserWithDetailsFromAggregate(user)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, int64(456), result.Category)
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, "Test User", result.FullName)
	assert.Equal(t, "Developer", result.Position)
	assert.True(t, result.IsAdmin)
	assert.Equal(t, "photo_data", result.Photo)
	assert.Len(t, result.Products, 1)
	assert.Len(t, result.Groups, 1)
	assert.Len(t, result.Roles, 1)
}

func TestCheckPhoto_WithData(t *testing.T) {
	photo := []byte("photo_data")

	result := checkPhoto(&photo)

	assert.Equal(t, "photo_data", result)
}

func TestCheckPhoto_Nil(t *testing.T) {
	result := checkPhoto(nil)

	assert.Equal(t, "", result)
}

func TestToV1DTOGroupWithProductCollectionFromModel_WithProduct(t *testing.T) {
	group := groupentity.GroupWithProductCollection{
		GroupID:   1,
		GroupName: "Test Group",
		IsSystem:  true,
		Product: &userentity.UserProduct{
			ID:       10,
			IID:      "P001",
			Name:     "Product 1",
			TechName: "product1",
		},
	}

	result := ToV1DTOGroupWithProductCollectionFromModel(group)

	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	require.NotNil(t, result.Product)
	assert.Equal(t, int64(10), result.Product.ID)
	assert.Equal(t, "P001", *result.Product.IID)
}

func TestToV1DTOGroupWithProductCollectionFromModel_WithoutProduct(t *testing.T) {
	group := groupentity.GroupWithProductCollection{
		GroupID:   2,
		GroupName: "Test Group",
		IsSystem:  false,
		Product:   nil,
	}

	result := ToV1DTOGroupWithProductCollectionFromModel(group)

	assert.Equal(t, int64(2), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.Nil(t, result.Product)
}

func TestToV1DTOsUserProductFromModels_Empty(t *testing.T) {
	var products []userentity.UserProduct

	result := ToV1DTOsUserProductFromModels(products)

	assert.Empty(t, result)
}

func TestToV1DTOsUserFromModels_Empty(t *testing.T) {
	var users []userentity.User

	result := ToV1DTOsUserFromModels(users)

	assert.Empty(t, result)
}

func TestToModelUserUpdateFromV1DTOUpdate_NilValues(t *testing.T) {
	userID := int64(123)

	dto := user.UserUpdateV1DTO{
		CategoryID: nil,
		IsAdmin:    nil,
		RoleIDs:    nil,
		GroupIDs:   nil,
		ProductIDs: nil,
	}

	result := ToModelUserUpdateFromV1DTOUpdate(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Nil(t, result.CategoryID)
	assert.Nil(t, result.IsAdmin)
	assert.Nil(t, result.RoleIDs)
	assert.Nil(t, result.GroupIDs)
	assert.Nil(t, result.ProductIDs)
}

func TestToV1DTOProductBasicFromEntity_Success(t *testing.T) {
	products := []productentity.ProductBasic{
		{ID: 1, IID: stringPtr("P001"), Name: "Product 1", TechName: "product1"},
		{ID: 2, IID: nil, Name: "Product 2", TechName: "product2"},
	}

	result := ToV1DTOProductBasicFromEntity(products)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, stringPtr("P001"), result[0].IID)
	assert.Equal(t, "Product 1", result[0].Name)
	assert.Equal(t, "product1", result[0].TechName)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Nil(t, result[1].IID)
	assert.Equal(t, "Product 2", result[1].Name)
	assert.Equal(t, "product2", result[1].TechName)
}

func TestToV1DTOProductBasicFromEntity_Empty(t *testing.T) {
	var products []productentity.ProductBasic

	result := ToV1DTOProductBasicFromEntity(products)

	assert.Empty(t, result)
}

func TestToV1DTOGroupWithProductIDFromEntity_Empty(t *testing.T) {
	var groups []groupentity.GroupWithProduct

	result := ToV1DTOGroupWithProductIDFromEntity(groups)

	assert.Empty(t, result)
}

func TestToV1DTORoleWithProductIDFromEntity_Empty(t *testing.T) {
	var roles []roleentity.RoleWithProduct

	result := ToV1DTORoleWithProductIDFromEntity(roles)

	assert.Empty(t, result)
}

func TestToModelAdminUserUpdateFromV1DTO_Success(t *testing.T) {
	userID := int64(123)
	categoryID := int64(456)
	isAdmin := true

	roles := []adminuser.RoleProductLinkV1DTO{
		{ID: 1, ProductID: int64Ptr(10)},
		{ID: 2, ProductID: nil},
	}
	groups := []adminuser.GroupProductLinkV1DTO{
		{ID: 3, ProductID: int64Ptr(20)},
		{ID: 4, ProductID: nil},
	}
	products := []adminuser.ProductIDV1DTO{
		{ID: 5},
		{ID: 6},
	}

	dto := adminuser.AdminUserUpdateV1DTO{
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
		Roles:      &roles,
		Groups:     &groups,
		Products:   &products,
	}

	result := ToModelAdminUserUpdateFromV1DTO(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Equal(t, &categoryID, result.CategoryID)
	assert.Equal(t, &isAdmin, result.IsAdmin)

	require.NotNil(t, result.RoleIDs)
	require.Len(t, *result.RoleIDs, 2)
	assert.Equal(t, int64(1), (*result.RoleIDs)[0].RoleID)
	assert.Equal(t, int64Ptr(10), (*result.RoleIDs)[0].ProductID)
	assert.Equal(t, int64(2), (*result.RoleIDs)[1].RoleID)
	assert.Nil(t, (*result.RoleIDs)[1].ProductID)

	require.NotNil(t, result.GroupIDs)
	require.Len(t, *result.GroupIDs, 2)
	assert.Equal(t, int64(3), (*result.GroupIDs)[0].GroupID)
	assert.Equal(t, int64Ptr(20), (*result.GroupIDs)[0].ProductID)

	require.NotNil(t, result.ProductIDs)
	require.Len(t, *result.ProductIDs, 2)
	assert.Equal(t, int64(5), (*result.ProductIDs)[0])
	assert.Equal(t, int64(6), (*result.ProductIDs)[1])
}

func TestToModelAdminUserUpdateFromV1DTO_NilValues(t *testing.T) {
	userID := int64(123)

	dto := adminuser.AdminUserUpdateV1DTO{
		CategoryID: nil,
		IsAdmin:    nil,
		Roles:      nil,
		Groups:     nil,
		Products:   nil,
	}

	result := ToModelAdminUserUpdateFromV1DTO(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Nil(t, result.CategoryID)
	assert.Nil(t, result.IsAdmin)
	assert.Nil(t, result.RoleIDs)
	assert.Nil(t, result.GroupIDs)
	assert.Nil(t, result.ProductIDs)
}

func TestToModelAdminUserUpdateFromV1DTO_EmptySlices(t *testing.T) {
	userID := int64(123)
	categoryID := int64(456)

	var roles []adminuser.RoleProductLinkV1DTO
	var groups []adminuser.GroupProductLinkV1DTO
	var products []adminuser.ProductIDV1DTO

	dto := adminuser.AdminUserUpdateV1DTO{
		CategoryID: &categoryID,
		Roles:      &roles,
		Groups:     &groups,
		Products:   &products,
	}

	result := ToModelAdminUserUpdateFromV1DTO(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Equal(t, &categoryID, result.CategoryID)

	require.NotNil(t, result.RoleIDs)
	assert.Empty(t, *result.RoleIDs)

	require.NotNil(t, result.GroupIDs)
	assert.Empty(t, *result.GroupIDs)

	require.NotNil(t, result.ProductIDs)
	assert.Empty(t, *result.ProductIDs)
}

func TestToModelUserUpdateFromV1DTOAdminUserUpdate_Success(t *testing.T) {
	userID := int64(123)
	categoryID := int64(456)
	isAdmin := true

	dto := adminuser.AdminUserUpdateV1DTO{
		CategoryID: &categoryID,
		IsAdmin:    &isAdmin,
	}

	result := ToModelUserUpdateFromV1DTOAdminUserUpdate(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Equal(t, &categoryID, result.CategoryID)
	assert.Equal(t, &isAdmin, result.IsAdmin)
	assert.Nil(t, result.ProductIDs)
	assert.Nil(t, result.Roles)
	assert.Nil(t, result.Groups)
}

func TestToModelUserUpdateFromV1DTOAdminUserUpdate_NilValues(t *testing.T) {
	userID := int64(123)

	dto := adminuser.AdminUserUpdateV1DTO{
		CategoryID: nil,
		IsAdmin:    nil,
	}

	result := ToModelUserUpdateFromV1DTOAdminUserUpdate(userID, dto)

	assert.Equal(t, userID, result.ID)
	assert.Nil(t, result.CategoryID)
	assert.Nil(t, result.IsAdmin)
	assert.Nil(t, result.ProductIDs)
	assert.Nil(t, result.Roles)
	assert.Nil(t, result.Groups)
}

func TestToV1DTOAdminUserCollectionFromModels_Empty(t *testing.T) {
	paginatedResult := sharedentity.PaginatedResult[userentity.AdminUser]{
		Items:  []userentity.AdminUser{},
		Limit:  10,
		Offset: 0,
		Total:  0,
	}

	result := ToV1DTOAdminUserCollectionFromModels(paginatedResult)

	assert.Empty(t, result.Items)
	assert.Equal(t, int64(10), result.Meta.Limit)
	assert.Equal(t, int64(0), result.Meta.Offset)
	assert.Equal(t, int64(0), result.Meta.Total)
}

func TestToV1DTOUserProductCollectionFromModels_Empty(t *testing.T) {
	var products []userentity.UserProduct

	result := ToV1DTOUserProductCollectionFromModels(products)

	assert.Empty(t, result.Items)
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func int64Ptr(i int64) *int64 {
	return &i
}

func TestToV1DTORoleWithProductCollectionFromModel_Success(t *testing.T) {
	iid := "P001"
	roles := []roleentity.AdminRole{
		{
			ID:       1,
			Name:     "Admin",
			IsSystem: true,
			Product: &productentity.ProductBasic{
				ID:       100,
				IID:      &iid,
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:       2,
			Name:     "User",
			IsSystem: false,
			Product:  nil,
		},
	}

	result := ToV1DTORoleWithProductCollectionFromModel(roles)

	require.Len(t, result.Items, 2)

	assert.Equal(t, int64(1), result.Items[0].ID)
	assert.Equal(t, "Admin", result.Items[0].Name)
	assert.Equal(t, constants.SystemType, result.Items[0].Type)
	require.NotNil(t, result.Items[0].Product)
	assert.Equal(t, int64(100), result.Items[0].Product.ID)
	assert.Equal(t, &iid, result.Items[0].Product.IID)
	assert.Equal(t, "Product 1", result.Items[0].Product.Name)
	assert.Equal(t, "product1", result.Items[0].Product.TechName)

	assert.Equal(t, int64(2), result.Items[1].ID)
	assert.Equal(t, "User", result.Items[1].Name)
	assert.Equal(t, constants.CustomType, result.Items[1].Type)
	assert.Nil(t, result.Items[1].Product)
}

func TestToV1DTORoleWithProductCollectionFromModel_Empty(t *testing.T) {
	var roles []roleentity.AdminRole

	result := ToV1DTORoleWithProductCollectionFromModel(roles)

	assert.Empty(t, result.Items)
}

func TestToSortParamsFromGetUserProductsAsParams_WithSortAndOrder(t *testing.T) {
	sort := user.GetUserProductsParamsSort("name")
	order := user.GetUserProductsParamsOrder("asc")
	params := user.GetUserProductsParams{
		Sort:  &sort,
		Order: &order,
	}

	result := ToSortParamsFromGetUserProductsAsParams(params)

	assert.Equal(t, "name", result.Field)
	assert.Equal(t, "asc", result.Order)
}

func TestToSortParamsFromGetUserProductsAsParams_NilSortAndOrder(t *testing.T) {
	params := user.GetUserProductsParams{
		Sort:  nil,
		Order: nil,
	}

	result := ToSortParamsFromGetUserProductsAsParams(params)

	assert.Equal(t, "", result.Field)
	assert.Equal(t, "", result.Order)
}

func TestToSortParamsFromGetUserProductsAsParams_OnlySort(t *testing.T) {
	sort := user.GetUserProductsParamsSort("name")
	params := user.GetUserProductsParams{
		Sort:  &sort,
		Order: nil,
	}

	result := ToSortParamsFromGetUserProductsAsParams(params)

	assert.Equal(t, "", result.Field)
	assert.Equal(t, "", result.Order)
}

func TestToAdminRolesFromGetUserRolesWithProductsAsParams_Success(t *testing.T) {
	productIDs := []int64{1, 2, 3}
	roleType := "system"
	sort := "name"
	order := "desc"

	params := user.GetUserRolesWithProductsParams{
		ProductIDs: &productIDs,
		Type:       (*user.GetUserRolesWithProductsParamsType)(&roleType),
		Sort:       (*user.GetUserRolesWithProductsParamsSort)(&sort),
		Order:      (*user.GetUserRolesWithProductsParamsOrder)(&order),
	}

	result := ToAdminRolesFromGetUserRolesWithProductsAsParams(params)

	assert.Equal(t, productIDs, result.ProductIDs)
	assert.Equal(t, &roleType, result.Type)
	assert.Equal(t, &sort, result.Sort)
	assert.Equal(t, &order, result.Order)
}

func TestToAdminRolesFromGetUserRolesWithProductsAsParams_NilValues(t *testing.T) {
	params := user.GetUserRolesWithProductsParams{
		ProductIDs: nil,
		Type:       nil,
		Sort:       nil,
		Order:      nil,
	}

	result := ToAdminRolesFromGetUserRolesWithProductsAsParams(params)

	assert.Empty(t, result.ProductIDs)
	assert.Nil(t, result.Type)
	assert.Nil(t, result.Sort)
	assert.Nil(t, result.Order)
}

func TestToQueryUsersFromGetUsersAsParams_Success(t *testing.T) {
	search := "test"
	productIDs := []int64{1, 2, 3}
	categoryIDs := []int64{4, 5, 6}
	isAdmin := true
	sort := "name"
	order := "asc"
	limit := int64(10)
	offset := int64(20)

	params := user.GetUsersParams{
		Search:      &search,
		ProductIDs:  &productIDs,
		CategoryIDs: &categoryIDs,
		IsAdmin:     &isAdmin,
		Sort:        (*user.GetUsersParamsSort)(&sort),
		Order:       (*user.GetUsersParamsOrder)(&order),
		Limit:       &limit,
		Offset:      &offset,
	}

	result := ToQueryUsersFromGetUsersAsParams(params)

	assert.Equal(t, &search, result.Search)
	require.NotNil(t, result.ProductIDs)
	assert.Equal(t, productIDs, *result.ProductIDs)
	require.NotNil(t, result.CategoryIDs)
	assert.Equal(t, categoryIDs, *result.CategoryIDs)
	assert.Equal(t, &isAdmin, result.IsAdmin)
	assert.Equal(t, &sort, result.Sort)
	assert.Equal(t, &order, result.Order)
	assert.Equal(t, &limit, result.Limit)
	assert.Equal(t, &offset, result.Offset)
}

func TestToQueryUsersFromGetUsersAsParams_NilValues(t *testing.T) {
	limit := int64(10)
	offset := int64(0)

	params := user.GetUsersParams{
		Search:      nil,
		ProductIDs:  nil,
		CategoryIDs: nil,
		IsAdmin:     nil,
		Sort:        nil,
		Order:       nil,
		Limit:       &limit,
		Offset:      &offset,
	}

	result := ToQueryUsersFromGetUsersAsParams(params)

	assert.Nil(t, result.Search)
	assert.Nil(t, result.ProductIDs)
	assert.Nil(t, result.CategoryIDs)
	assert.Nil(t, result.IsAdmin)
	assert.Nil(t, result.Sort)
	assert.Nil(t, result.Order)
	assert.Equal(t, &limit, result.Limit)
	assert.Equal(t, &offset, result.Offset)
}

func TestToUserFiltersDataFromGetUserGroupsAsParams_Success(t *testing.T) {
	productIDs := []int64{1, 2, 3}

	params := user.GetUserGroupsWithProductsParams{
		ProductIDs: &productIDs,
	}

	result := ToUserFiltersDataFromGetUserGroupsAsParams(params)

	require.NotNil(t, result.ProductIDs)
	assert.Equal(t, productIDs, *result.ProductIDs)
}

func TestToUserFiltersDataFromGetUserGroupsAsParams_NilProductIDs(t *testing.T) {
	params := user.GetUserGroupsWithProductsParams{
		ProductIDs: nil,
	}

	result := ToUserFiltersDataFromGetUserGroupsAsParams(params)

	assert.Nil(t, result.ProductIDs)
}

func TestToV1DTOUserCollectionFromModels_Success(t *testing.T) {
	users := []userentity.User{
		{ID: 1, Email: "<EMAIL>", FullName: "User 1"},
		{ID: 2, Email: "<EMAIL>", FullName: "User 2"},
	}

	paginatedResult := sharedentity.PaginatedResult[userentity.User]{
		Items:  users,
		Limit:  10,
		Offset: 0,
		Total:  2,
	}

	result := ToV1DTOUserCollectionFromModels(paginatedResult)

	require.Len(t, result.Items, 2)
	assert.Equal(t, int64(1), result.Items[0].ID)
	assert.Equal(t, "<EMAIL>", result.Items[0].Email)
	assert.Equal(t, int64(2), result.Items[1].ID)
	assert.Equal(t, "<EMAIL>", result.Items[1].Email)

	assert.Equal(t, int64(10), result.Meta.Limit)
	assert.Equal(t, int64(0), result.Meta.Offset)
	assert.Equal(t, int64(2), result.Meta.Total)
}

func TestToV1DTOUserCollectionFromModels_Empty(t *testing.T) {
	paginatedResult := sharedentity.PaginatedResult[userentity.User]{
		Items:  []userentity.User{},
		Limit:  10,
		Offset: 0,
		Total:  0,
	}

	result := ToV1DTOUserCollectionFromModels(paginatedResult)

	assert.Empty(t, result.Items)
	assert.Equal(t, int64(10), result.Meta.Limit)
	assert.Equal(t, int64(0), result.Meta.Offset)
	assert.Equal(t, int64(0), result.Meta.Total)
}

func TestToV1DTOGroupWithProductIDFromEntity_WithProducts(t *testing.T) {
	groups := []groupentity.GroupWithProduct{
		{
			ID:        1,
			Name:      "Group 1",
			IsSystem:  true,
			ActiveFlg: true,
			Product: &productentity.Product{
				ID:       100,
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:        2,
			Name:      "Group 2",
			IsSystem:  false,
			ActiveFlg: false,
			Product:   nil,
		},
	}

	result := ToV1DTOGroupWithProductIDFromEntity(groups)

	require.Len(t, result, 2)

	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.True(t, result[0].IsActive)
	require.NotNil(t, result[0].ProductID)
	assert.Equal(t, int64(100), *result[0].ProductID)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Group 2", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
	assert.False(t, result[1].IsActive)
	assert.Nil(t, result[1].ProductID)
}

func TestToV1DTORoleWithProductIDFromEntity_WithProducts(t *testing.T) {
	roles := []roleentity.RoleWithProduct{
		{
			ID:        1,
			Name:      "Role 1",
			IsSystem:  true,
			ActiveFlg: true,
			Product: &productentity.Product{
				ID:       100,
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:        2,
			Name:      "Role 2",
			IsSystem:  false,
			ActiveFlg: false,
			Product:   nil,
		},
	}

	result := ToV1DTORoleWithProductIDFromEntity(roles)

	require.Len(t, result, 2)

	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Role 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.True(t, result[0].IsActive)
	require.NotNil(t, result[0].ProductID)
	assert.Equal(t, int64(100), *result[0].ProductID)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Role 2", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
	assert.False(t, result[1].IsActive)
	assert.Nil(t, result[1].ProductID)
}
