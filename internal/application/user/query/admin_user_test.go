package query

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestAdminUser_GetLimit(t *testing.T) {
	tests := []struct {
		name     string
		query    AdminUser
		expected *int64
	}{
		{
			name:     "nil limit",
			query:    AdminUser{},
			expected: nil,
		},
		{
			name: "with limit",
			query: AdminUser{
				Limit: func() *int64 { v := int64(10); return &v }(),
			},
			expected: func() *int64 { v := int64(10); return &v }(),
		},
		{
			name: "zero limit",
			query: AdminUser{
				Limit: func() *int64 { v := int64(0); return &v }(),
			},
			expected: func() *int64 { v := int64(0); return &v }(),
		},
		{
			name: "large limit",
			query: AdminUser{
				Limit: func() *int64 { v := int64(1000); return &v }(),
			},
			expected: func() *int64 { v := int64(1000); return &v }(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.query.GetLimit()
			if tt.expected == nil {
				require.Nil(t, result)
			} else {
				require.NotNil(t, result)
				require.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestAdminUser_GetOffset(t *testing.T) {
	tests := []struct {
		name     string
		query    AdminUser
		expected *int64
	}{
		{
			name:     "nil offset",
			query:    AdminUser{},
			expected: nil,
		},
		{
			name: "with offset",
			query: AdminUser{
				Offset: func() *int64 { v := int64(20); return &v }(),
			},
			expected: func() *int64 { v := int64(20); return &v }(),
		},
		{
			name: "zero offset",
			query: AdminUser{
				Offset: func() *int64 { v := int64(0); return &v }(),
			},
			expected: func() *int64 { v := int64(0); return &v }(),
		},
		{
			name: "large offset",
			query: AdminUser{
				Offset: func() *int64 { v := int64(5000); return &v }(),
			},
			expected: func() *int64 { v := int64(5000); return &v }(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.query.GetOffset()
			if tt.expected == nil {
				require.Nil(t, result)
			} else {
				require.NotNil(t, result)
				require.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestAdminUser_StructFields(t *testing.T) {
	search := "test search"
	productIDs := []int64{1, 2, 3}
	categoryIDs := []int64{4, 5}
	isAdmin := true
	sort := "name"
	order := "ascend"
	limit := int64(10)
	offset := int64(20)

	query := AdminUser{
		Search:      &search,
		ProductIDs:  productIDs,
		CategoryIDs: categoryIDs,
		IsAdmin:     &isAdmin,
		Sort:        &sort,
		Order:       &order,
		Limit:       &limit,
		Offset:      &offset,
	}

	require.Equal(t, &search, query.Search)
	require.Equal(t, productIDs, query.ProductIDs)
	require.Equal(t, categoryIDs, query.CategoryIDs)
	require.Equal(t, &isAdmin, query.IsAdmin)
	require.Equal(t, &sort, query.Sort)
	require.Equal(t, &order, query.Order)
	require.Equal(t, &limit, query.Limit)
	require.Equal(t, &offset, query.Offset)
}

func TestAdminUser_EmptyStruct(t *testing.T) {
	query := AdminUser{}

	require.Nil(t, query.Search)
	require.Nil(t, query.ProductIDs)
	require.Nil(t, query.CategoryIDs)
	require.Nil(t, query.IsAdmin)
	require.Nil(t, query.Sort)
	require.Nil(t, query.Order)
	require.Nil(t, query.Limit)
	require.Nil(t, query.Offset)
}

func TestAdminUser_PartialFields(t *testing.T) {
	search := "partial test"
	isAdmin := false

	query := AdminUser{
		Search:  &search,
		IsAdmin: &isAdmin,
	}

	require.Equal(t, &search, query.Search)
	require.Equal(t, &isAdmin, query.IsAdmin)
	require.Nil(t, query.ProductIDs)
	require.Nil(t, query.CategoryIDs)
	require.Nil(t, query.Sort)
	require.Nil(t, query.Order)
	require.Nil(t, query.Limit)
	require.Nil(t, query.Offset)
}

func TestAdminUser_EmptySlices(t *testing.T) {
	query := AdminUser{
		ProductIDs:  []int64{},
		CategoryIDs: []int64{},
	}

	require.Empty(t, query.ProductIDs)
	require.Empty(t, query.CategoryIDs)
	require.NotNil(t, query.ProductIDs)
	require.NotNil(t, query.CategoryIDs)
}

func TestAdminUser_BooleanValues(t *testing.T) {
	tests := []struct {
		name    string
		isAdmin *bool
	}{
		{
			name:    "admin true",
			isAdmin: func() *bool { v := true; return &v }(),
		},
		{
			name:    "admin false",
			isAdmin: func() *bool { v := false; return &v }(),
		},
		{
			name:    "admin nil",
			isAdmin: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query := AdminUser{
				IsAdmin: tt.isAdmin,
			}

			if tt.isAdmin == nil {
				require.Nil(t, query.IsAdmin)
			} else {
				require.NotNil(t, query.IsAdmin)
				require.Equal(t, *tt.isAdmin, *query.IsAdmin)
			}
		})
	}
}

func TestAdminUser_SortingFields(t *testing.T) {
	tests := []struct {
		name  string
		sort  *string
		order *string
	}{
		{
			name:  "both nil",
			sort:  nil,
			order: nil,
		},
		{
			name:  "sort only",
			sort:  func() *string { v := "name"; return &v }(),
			order: nil,
		},
		{
			name:  "order only",
			sort:  nil,
			order: func() *string { v := "ascend"; return &v }(),
		},
		{
			name:  "both set",
			sort:  func() *string { v := "email"; return &v }(),
			order: func() *string { v := "descend"; return &v }(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query := AdminUser{
				Sort:  tt.sort,
				Order: tt.order,
			}

			if tt.sort == nil {
				require.Nil(t, query.Sort)
			} else {
				require.NotNil(t, query.Sort)
				require.Equal(t, *tt.sort, *query.Sort)
			}

			if tt.order == nil {
				require.Nil(t, query.Order)
			} else {
				require.NotNil(t, query.Order)
				require.Equal(t, *tt.order, *query.Order)
			}
		})
	}
}

func TestAdminUser_PaginationMethods(t *testing.T) {
	limit := int64(50)
	offset := int64(100)

	query := AdminUser{
		Limit:  &limit,
		Offset: &offset,
	}

	resultLimit := query.GetLimit()
	resultOffset := query.GetOffset()

	require.NotNil(t, resultLimit)
	require.NotNil(t, resultOffset)
	require.Equal(t, limit, *resultLimit)
	require.Equal(t, offset, *resultOffset)

	// Проверяем, что методы возвращают те же указатели
	require.Equal(t, query.Limit, resultLimit)
	require.Equal(t, query.Offset, resultOffset)
}
