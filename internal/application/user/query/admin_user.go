package query

// AdminUser contains the parameters for querying the list of users by administrators
type AdminUser struct {
	// Filtering parameters
	Search      *string // Search text
	ProductIDs  []int64 // Filter by product IDs
	CategoryIDs []int64 // Filter by category IDs
	IsAdmin     *bool   // Filter by admin status

	// Sorting parameters
	Sort  *string // Field for sorting ("name", "email", etc.)
	Order *string // Sort order ("ascend", "descend")

	// Pagination parameters
	Limit  *int64 // Number of items per page
	Offset *int64 // Offset (number of items to skip)
}

func (q AdminUser) GetLimit() *int64 {
	return q.Limit
}

func (q AdminUser) GetOffset() *int64 {
	return q.Offset
}
