package query

import (
	"testing"
)

func TestUsers_GetLimit(t *testing.T) {
	tests := []struct {
		name     string
		limit    *int64
		expected *int64
	}{
		{
			name:     "nil limit",
			limit:    nil,
			expected: nil,
		},
		{
			name:     "with limit value",
			limit:    int64Ptr(10),
			expected: int64Ptr(10),
		},
		{
			name:     "zero limit",
			limit:    int64Ptr(0),
			expected: int64Ptr(0),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			q := Users{Limit: tt.limit}
			result := q.GetLimit()

			if (result == nil && tt.expected != nil) || (result != nil && tt.expected == nil) {
				t.<PERSON><PERSON>("GetLimit() = %v, want %v", result, tt.expected)
				return
			}

			if result != nil && tt.expected != nil && *result != *tt.expected {
				t.<PERSON><PERSON><PERSON>("GetLimit() = %v, want %v", *result, *tt.expected)
			}
		})
	}
}

func TestUsers_GetOffset(t *testing.T) {
	tests := []struct {
		name     string
		offset   *int64
		expected *int64
	}{
		{
			name:     "nil offset",
			offset:   nil,
			expected: nil,
		},
		{
			name:     "with offset value",
			offset:   int64Ptr(20),
			expected: int64Ptr(20),
		},
		{
			name:     "zero offset",
			offset:   int64Ptr(0),
			expected: int64Ptr(0),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			q := Users{Offset: tt.offset}
			result := q.GetOffset()

			if (result == nil && tt.expected != nil) || (result != nil && tt.expected == nil) {
				t.Errorf("GetOffset() = %v, want %v", result, tt.expected)
				return
			}

			if result != nil && tt.expected != nil && *result != *tt.expected {
				t.Errorf("GetOffset() = %v, want %v", *result, *tt.expected)
			}
		})
	}
}

func TestUsers_AllFields(t *testing.T) {
	search := "test"
	productIDs := []int64{1, 2, 3}
	categoryIDs := []int64{4, 5}
	isAdmin := true
	sort := "name"
	order := "asc"
	limit := int64(10)
	offset := int64(20)

	q := Users{
		Search:      &search,
		ProductIDs:  &productIDs,
		CategoryIDs: &categoryIDs,
		IsAdmin:     &isAdmin,
		Sort:        &sort,
		Order:       &order,
		Limit:       &limit,
		Offset:      &offset,
	}

	if q.Search == nil || *q.Search != search {
		t.Errorf("Search = %v, want %v", q.Search, search)
	}

	if q.ProductIDs == nil || len(*q.ProductIDs) != 3 {
		t.Errorf("ProductIDs length = %v, want 3", len(*q.ProductIDs))
	}

	if q.CategoryIDs == nil || len(*q.CategoryIDs) != 2 {
		t.Errorf("CategoryIDs length = %v, want 2", len(*q.CategoryIDs))
	}

	if q.IsAdmin == nil || *q.IsAdmin != isAdmin {
		t.Errorf("IsAdmin = %v, want %v", q.IsAdmin, isAdmin)
	}

	if q.Sort == nil || *q.Sort != sort {
		t.Errorf("Sort = %v, want %v", q.Sort, sort)
	}

	if q.Order == nil || *q.Order != order {
		t.Errorf("Order = %v, want %v", q.Order, order)
	}

	if q.GetLimit() == nil || *q.GetLimit() != limit {
		t.Errorf("GetLimit() = %v, want %v", q.GetLimit(), limit)
	}

	if q.GetOffset() == nil || *q.GetOffset() != offset {
		t.Errorf("GetOffset() = %v, want %v", q.GetOffset(), offset)
	}
}

func TestUsers_EmptyStruct(t *testing.T) {
	q := Users{}

	if q.GetLimit() != nil {
		t.Errorf("GetLimit() = %v, want nil", q.GetLimit())
	}

	if q.GetOffset() != nil {
		t.Errorf("GetOffset() = %v, want nil", q.GetOffset())
	}

	if q.Search != nil {
		t.Errorf("Search = %v, want nil", q.Search)
	}

	if q.ProductIDs != nil {
		t.Errorf("ProductIDs = %v, want nil", q.ProductIDs)
	}

	if q.CategoryIDs != nil {
		t.Errorf("CategoryIDs = %v, want nil", q.CategoryIDs)
	}

	if q.IsAdmin != nil {
		t.Errorf("IsAdmin = %v, want nil", q.IsAdmin)
	}

	if q.Sort != nil {
		t.Errorf("Sort = %v, want nil", q.Sort)
	}

	if q.Order != nil {
		t.Errorf("Order = %v, want nil", q.Order)
	}
}

func int64Ptr(i int64) *int64 {
	return &i
}
