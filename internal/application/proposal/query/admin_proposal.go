package query

type AdminProposal struct {
	// Filtering parameters
	Search     *string // Search text (proposalNumber)
	Type       *string
	Status     *string
	ProductIDs []int64 // Filter by product IDs

	// Sorting parameters
	Sort  *string // Field for sorting ("name", "email", etc.)
	Order *string // Sort order ("ascend", "descend")

	// Pagination parameters
	Limit  *int64 // Number of items per page
	Offset *int64 // Offset (number of items to skip)
}

func (q AdminProposal) GetLimit() *int64 {
	return q.Limit
}

func (q AdminProposal) GetOffset() *int64 {
	return q.Offset
}
