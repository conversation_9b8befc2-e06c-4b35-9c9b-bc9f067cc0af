package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/proposal/query"
	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/mocks"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

func TestNewProposalAppService(t *testing.T) {
	mockProposalDomain := mocks.NewProposalDomainServiceMock(t)

	service := NewProposalAppService(mockProposalDomain)

	require.NotNil(t, service)
	assert.Equal(t, mockProposalDomain, service.proposalDomain)
}

func TestProposalAppService_Create(t *testing.T) {
	tests := []struct {
		name          string
		data          proposalentity.Proposal
		values        proposalvalueobject.Values
		mockResult    proposalvalueobject.ProposalFull
		mockError     error
		expectedError error
	}{
		{
			name: "successful proposal creation",
			data: proposalentity.Proposal{
				ProductID: 1,
				Price:     100.0,
				Type:      "standard",
				Status:    "draft",
				CreatorID: 1,
			},
			values: proposalvalueobject.Values{
				[]byte(`{"general": "data"}`),
				[]byte(`{"catalog": "item1"}`),
			},
			mockResult: proposalvalueobject.ProposalFull{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 1,
					Price:     100.0,
					Type:      "standard",
					Status:    "draft",
					CreatorID: 1,
				},
				Data: []byte(`{"general": "data", "catalog": ["item1"]}`),
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "error creating proposal",
			data: proposalentity.Proposal{
				ProductID: 4,
				Price:     400.0,
				CreatorID: 4,
			},
			values:        proposalvalueobject.Values{[]byte(`{"test": "data"}`)},
			mockResult:    proposalvalueobject.ProposalFull{},
			mockError:     errors.New("creation error"),
			expectedError: errors.New("creation error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.CreateMock.Expect(tt.data, tt.values).Return(tt.mockResult, tt.mockError)

			result, err := service.Create(tt.data, tt.values)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockCreateDone())
		})
	}
}

func TestProposalAppService_GetProposalsAsAdmin(t *testing.T) {
	TestSearch, TestType, TestStatus, TestSort, TestOrder := "OR-002-07-0001", "OR", "draft", "status", "ascend"
	WrongStatus := "invalid_status"
	var TestLimit, TestOffset int64 = 10, 0

	tests := []struct {
		name          string
		query         query.AdminProposal
		mockResult    []proposalvalueobject.ProposalFull
		mockError     error
		expectedError error
	}{
		{
			name: "successful retrieval with valid query",
			query: query.AdminProposal{
				Search:     &TestSearch,
				Type:       &TestType,
				Status:     &TestStatus,
				ProductIDs: []int64{15},
				Sort:       &TestSort,
				Order:      &TestOrder,
				Limit:      &TestLimit,
				Offset:     &TestOffset,
			},
			mockResult: []proposalvalueobject.ProposalFull{
				{
					Proposal: proposalentity.Proposal{
						ID:        12,
						ProductID: 15,
						Type:      TestType,
						Status:    TestStatus,
						Number:    "OR-002-07-0001",
					},
					Data: []byte(`{"general": "data", "catalog": ["item1"]}`),
				},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "retrieval with invalid query",
			query: query.AdminProposal{
				Status: &WrongStatus,
			},
			mockResult:    nil,
			mockError:     errors.New("retrieval error"),
			expectedError: errors.New("retrieval error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.GetAllAsAdminMock.Expect().Return(tt.mockResult, tt.mockError)

			result, err := service.GetProposalsAsAdmin(tt.query)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result.Items)
			}

			require.True(t, mockProposalDomain.MinimockGetAllAsAdminDone())
		})
	}
}

func TestProposalAppService_GetByID(t *testing.T) {
	tests := []struct {
		name          string
		id            int64
		activeFlg     bool
		mockResult    proposalvalueobject.ProposalFull
		mockError     error
		expectedError error
	}{
		{
			name: "successful get by ID",
			id:   1,
			mockResult: proposalvalueobject.ProposalFull{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 10,
					Price:     150.0,
					Type:      "premium",
					Status:    "approved",
					CreatorID: 5,
				},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "successful get by ID (active flag false)",
			id:   1,
			mockResult: proposalvalueobject.ProposalFull{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 10,
					Price:     150.0,
					Type:      "premium",
					Status:    "approved",
					CreatorID: 5,
					ActiveFlg: false,
				},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "proposal not found",
			id:            9999,
			mockResult:    proposalvalueobject.ProposalFull{},
			mockError:     errors.New("proposal not found"),
			expectedError: errors.New("proposal not found"),
		},
		{
			name:          "proposal not found (active flag false)",
			id:            9999,
			mockResult:    proposalvalueobject.ProposalFull{},
			mockError:     errors.New("proposal not found"),
			expectedError: errors.New("proposal not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.GetByIDMock.Expect(tt.id).Return(tt.mockResult, tt.mockError)

			result, err := service.GetByID(tt.id)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockGetByIDDone())
		})
	}
}

func TestProposalAppService_GetByProductID(t *testing.T) {
	tests := []struct {
		name          string
		productID     int64
		mockResult    []proposalentity.Proposal
		mockError     error
		expectedError error
	}{
		{
			name:      "successful get by product ID",
			productID: 1,
			mockResult: []proposalentity.Proposal{
				{ID: 1, ProductID: 1, Price: 100.0, Status: "draft"},
				{ID: 2, ProductID: 1, Price: 200.0, Status: "approved"},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "error getting proposals",
			productID:     4,
			mockResult:    nil,
			mockError:     errors.New("database error"),
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.GetByProductIDMock.Expect(tt.productID).Return(tt.mockResult, tt.mockError)

			result, err := service.GetByProductID(tt.productID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockGetByProductIDDone())
		})
	}
}

func TestProposalAppService_GetByProductIDAndProposalID(t *testing.T) {
	tests := []struct {
		name          string
		productID     int64
		proposalID    int64
		mockResult    proposalvalueobject.ProposalFull
		mockError     error
		expectedError error
	}{
		{
			name:       "successful get by product and proposal ID",
			productID:  1,
			proposalID: 10,
			mockResult: proposalvalueobject.ProposalFull{
				Proposal: proposalentity.Proposal{
					ID:        10,
					ProductID: 1,
					Price:     500.0,
					Status:    "approved",
				},
				Data: []byte(`{"general": "data", "catalog": []}`),
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "proposal not found",
			productID:     2,
			proposalID:    20,
			mockResult:    proposalvalueobject.ProposalFull{},
			mockError:     errors.New("proposal not found"),
			expectedError: errors.New("proposal not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.GetByProductIDAndProposalIDMock.Expect(tt.productID, tt.proposalID).Return(tt.mockResult, tt.mockError)

			result, err := service.GetByProductIDAndProposalID(tt.productID, tt.proposalID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockGetByProductIDAndProposalIDDone())
		})
	}
}

func TestProposalAppService_GetHistory(t *testing.T) {
	tests := []struct {
		name          string
		proposalID    int64
		mockResult    []proposalvalueobject.HistoryRecord
		mockError     error
		expectedError error
	}{
		{
			name:       "successful get history",
			proposalID: 1,
			mockResult: []proposalvalueobject.HistoryRecord{
				{
					EventType: "status",
					Status:    "draft",
					Message:   func() *string { s := "Proposal created"; return &s }(),
				},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "error getting history",
			proposalID:    4,
			mockResult:    nil,
			mockError:     errors.New("database error"),
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.GetHistoryMock.Expect(tt.proposalID).Return(tt.mockResult, tt.mockError)

			result, err := service.GetHistory(tt.proposalID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockGetHistoryDone())
		})
	}
}

func TestProposalAppService_Update(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		data          proposalaggregate.ProposalUpdate
		mockResult    proposalaggregate.Proposal
		mockError     error
		expectedError error
	}{
		{
			name: "successful update",
			ctx:  context.Background(),
			data: proposalaggregate.ProposalUpdate{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 10,
					Price:     250.0,
					Status:    "approved",
				},
				Message: func() *string { s := "Updated proposal"; return &s }(),
			},
			mockResult: proposalaggregate.Proposal{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 10,
					Price:     250.0,
					Status:    "approved",
				},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "error updating proposal",
			ctx:  context.Background(),
			data: proposalaggregate.ProposalUpdate{
				Proposal: proposalentity.Proposal{
					ID:        3,
					ProductID: 30,
				},
			},
			mockResult:    proposalaggregate.Proposal{},
			mockError:     errors.New("update error"),
			expectedError: errors.New("update error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.UpdateMock.Expect(tt.ctx, tt.data).Return(tt.mockResult, tt.mockError)

			result, err := service.Update(tt.ctx, tt.data)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockUpdateDone())
		})
	}
}

func TestProposalAppService_UpdateLastViewed(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		data          proposalvalueobject.UserView
		mockError     error
		expectedError error
	}{
		{
			name: "successful update last viewed",
			ctx:  context.Background(),
			data: proposalvalueobject.UserView{
				ProposalID: 1,
				UserID:     10,
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "error updating last viewed",
			ctx:  context.Background(),
			data: proposalvalueobject.UserView{
				ProposalID: 2,
				UserID:     20,
			},
			mockError:     errors.New("update error"),
			expectedError: errors.New("update error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.UpdateLastViewedMock.Expect(tt.ctx, tt.data).Return(tt.mockError)

			err := service.UpdateLastViewed(tt.ctx, tt.data)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			require.True(t, mockProposalDomain.MinimockUpdateLastViewedDone())
		})
	}
}

func TestProposalAppService_SendProposalToApprovalOrReject(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		data          proposalaggregate.ProposalSend
		mockResult    proposalaggregate.Proposal
		mockError     error
		expectedError error
	}{
		{
			name: "successful send to approval",
			ctx:  context.Background(),
			data: proposalaggregate.ProposalSend{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 10,
					Status:    "on_approval",
				},
				Message: func() *string { s := "Send to approval"; return &s }(),
			},
			mockResult: proposalaggregate.Proposal{
				Proposal: proposalentity.Proposal{
					ID:        1,
					ProductID: 10,
					Status:    "on_approval",
				},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "error sending proposal",
			ctx:  context.Background(),
			data: proposalaggregate.ProposalSend{
				Proposal: proposalentity.Proposal{
					ID:        3,
					ProductID: 30,
				},
				Message: func() *string { s := "Error case"; return &s }(),
			},
			mockResult:    proposalaggregate.Proposal{},
			mockError:     errors.New("send error"),
			expectedError: errors.New("send error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			expectedUpdateData := proposalaggregate.ProposalUpdate{
				Proposal: tt.data.Proposal,
				Message:  tt.data.Message,
			}

			mockProposalDomain.UpdateMock.Expect(tt.ctx, expectedUpdateData).Return(tt.mockResult, tt.mockError)

			result, err := service.SendProposalToApprovalOrReject(tt.ctx, tt.data)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockResult, result)
			}

			require.True(t, mockProposalDomain.MinimockUpdateDone())
		})
	}
}

func TestProposalAppService_Delete(t *testing.T) {
	tests := []struct {
		name          string
		data          proposalentity.Proposal
		mockError     error
		expectedError error
	}{
		{
			name: "successful delete",
			data: proposalentity.Proposal{
				ID:        1,
				ProductID: 10,
				Status:    "draft",
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "error deleting proposal",
			data: proposalentity.Proposal{
				ID:        2,
				ProductID: 20,
			},
			mockError:     errors.New("database error"),
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockProposalDomain := mocks.NewProposalDomainServiceMock(t)
			service := NewProposalAppService(mockProposalDomain)

			mockProposalDomain.DeleteMock.Expect(tt.data).Return(tt.mockError)

			err := service.Delete(tt.data)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			require.True(t, mockProposalDomain.MinimockDeleteDone())
		})
	}
}
