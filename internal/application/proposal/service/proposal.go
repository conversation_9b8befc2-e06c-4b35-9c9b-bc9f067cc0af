package service

import (
	"context"
	"sort"
	"strings"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/pagination"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/proposal/query"
	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/service"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

type ProposalAppService struct {
	proposalDomain proposalservice.ProposalDomainService
}

func NewProposalAppService(domainService proposalservice.ProposalDomainService) *ProposalAppService {
	return &ProposalAppService{proposalDomain: domainService}
}

func (s *ProposalAppService) Create(data proposalentity.Proposal, values proposalvalueobject.Values) (proposalvalueobject.ProposalFull, error) {
	return s.proposalDomain.Create(data, values)
}

func (s *ProposalAppService) GetByID(id int64) (proposalvalueobject.ProposalFull, error) {
	return s.proposalDomain.GetByID(id)
}

func (s *ProposalAppService) CreateMessage(proposalID int64, message string, userID int64) error {
	return s.proposalDomain.CreateMessage(proposalID, message, userID)
}

func (s *ProposalAppService) GetByProductID(productID int64) ([]proposalentity.Proposal, error) {
	return s.proposalDomain.GetByProductID(productID)
}

func (s *ProposalAppService) GetByProductIDAndProposalID(productID, proposalID int64) (proposalvalueobject.ProposalFull, error) {
	return s.proposalDomain.GetByProductIDAndProposalID(productID, proposalID)
}

func (s *ProposalAppService) GetHistory(proposalID int64) ([]proposalvalueobject.HistoryRecord, error) {
	return s.proposalDomain.GetHistory(proposalID)
}

func (s *ProposalAppService) GetProposalsAsAdmin(query query.AdminProposal) (sharedentity.PaginatedResult[proposalvalueobject.ProposalFull], error) {
	proposalsFull, err := s.proposalDomain.GetAllAsAdmin()
	if err != nil {
		return sharedentity.PaginatedResult[proposalvalueobject.ProposalFull]{}, err
	}

	filteredProposals := filterProposals(proposalsFull, query)
	sortedProposals := sortProposals(filteredProposals, query)
	paginatedProposals, limit, offset := pagination.Apply(sortedProposals, query)

	return sharedentity.PaginatedResult[proposalvalueobject.ProposalFull]{
		Items:  paginatedProposals,
		Total:  int64(len(filteredProposals)),
		Limit:  limit,
		Offset: offset,
	}, nil
}

func (s *ProposalAppService) GetUnreadEventsForUser(userID int64) ([]proposalvalueobject.HistoryRecord, error) {
	return s.proposalDomain.GetUnreadEventsForUser(userID)
}

func (s *ProposalAppService) Update(ctx context.Context, data proposalaggregate.ProposalUpdate) (proposalaggregate.Proposal, error) {
	return s.proposalDomain.Update(ctx, data)
}

func (s *ProposalAppService) UpdateLastViewed(ctx context.Context, data proposalvalueobject.UserView) error {
	return s.proposalDomain.UpdateLastViewed(ctx, data)
}

func (s *ProposalAppService) Delete(data proposalentity.Proposal) error {
	return s.proposalDomain.Delete(data)
}

func (s *ProposalAppService) SendProposalToApprovalOrReject(ctx context.Context, data proposalaggregate.ProposalSend) (proposalaggregate.Proposal, error) {
	// data.Proposal.Status = constants.ProposalStatusOnApproval

	updateData := proposalaggregate.ProposalUpdate{
		Proposal: data.Proposal,
		Message:  data.Message,
	}

	return s.proposalDomain.Update(ctx, updateData)
}

func compareProposals(p1, p2 proposalvalueobject.ProposalFull, sortField string) bool {
	switch sortField {
	case sharedentity.SortFieldCreatedAt.String():
		return p1.CreatedAt.Before(p2.CreatedAt)
	case sharedentity.SortFieldProduct.String():
		return p1.Product.TechName < p2.Product.TechName
	case sharedentity.SortFieldStatus.String():
		return p1.Status < p2.Status
	case sharedentity.SortFieldOwners.String():
		return len(p1.ProductOwners) < len(p2.ProductOwners)
	default:
		return p1.ID < p2.ID
	}
}

func filterProposals(proposals []proposalvalueobject.ProposalFull, query query.AdminProposal) []proposalvalueobject.ProposalFull {

	filtered := make([]proposalvalueobject.ProposalFull, 0, len(proposals))

	var allowedProductIDs map[int64]bool
	if len(query.ProductIDs) > 0 {
		allowedProductIDs = make(map[int64]bool, len(query.ProductIDs))
		for _, id := range query.ProductIDs {
			allowedProductIDs[id] = true
		}
	}

	var searchLower string
	if query.Search != nil && *query.Search != "" {
		searchLower = strings.ToLower(*query.Search)
	}

	for _, proposal := range proposals {
		keep := true

		// ProposalID filter
		if searchLower != "" {
			if !strings.Contains(strings.ToLower(proposal.Number), searchLower) {
				keep = false
			}
		}

		// Type filter
		if keep && query.Type != nil {
			keep = proposal.Type == *query.Type
		}

		// Status filter
		if keep && query.Status != nil {
			keep = proposal.Status == *query.Status
		}

		if keep && len(query.ProductIDs) > 0 {
			keep = allowedProductIDs[proposal.ProductID]
		}

		if keep {
			filtered = append(filtered, proposal)
		}
	}

	return filtered
}

func sortProposals(proposals []proposalvalueobject.ProposalFull, query query.AdminProposal) []proposalvalueobject.ProposalFull {
	if query.Sort == nil {
		return proposals
	}

	sorted := make([]proposalvalueobject.ProposalFull, len(proposals))
	copy(sorted, proposals)

	sortField := *query.Sort
	orderAsc := true
	if query.Order != nil && *query.Order == sharedentity.SortOrderDescend.String() {
		orderAsc = false
	}

	sort.SliceStable(sorted, func(i, j int) bool {
		less := compareProposals(sorted[i], sorted[j], sortField)

		if !orderAsc {
			return !less
		}
		return less
	})

	return sorted
}
