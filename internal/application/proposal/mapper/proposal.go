package mapper

import (
	"encoding/json"
	"time"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/proposal/query"
	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func ToAggregateProposalSendFromV1DTO(productID, proposalID int64, p proposal.ProposalStatusUpdateV1DTO) proposalaggregate.ProposalSend {
	return proposalaggregate.NewProposalSend(
		proposalentity.Proposal{
			ID:        proposalID,
			ProductID: productID,
			Status:    p.Status,
		},
		p.Message,
	)
}

func ToAggregateProposalUpdateFromV1DTO(productID, proposalID int64, p proposal.ProposalUpdateV1DTO) (*proposalaggregate.ProposalUpdate, error) {
	propEntity := createProposalEntity(productID, proposalID, p)

	stands, err := createStandsFromData(productID, p.Data)
	if err != nil {
		return &proposalaggregate.ProposalUpdate{}, err
	}

	return proposalaggregate.NewProposalUpdate(propEntity, stands), nil
}

func ToEntityProposalAndValuesFromV1DTOCreate(productID int64, p proposal.ProposalCreateV1DTO, creatorID int64) (proposalentity.Proposal, proposalvalueobject.Values) {

	var standsData proposalvalueobject.StandsData
	data, err := json.Marshal(p.Data)
	if err != nil {
		return proposalentity.Proposal{}, nil
	}
	if err := json.Unmarshal(data, &standsData); err != nil {
		return proposalentity.Proposal{}, nil
	}

	values := make(proposalvalueobject.Values, len(standsData.Catalog)+1)

	jsonData, err := json.Marshal(standsData.GeneralStand)
	if err != nil {
		return proposalentity.Proposal{}, nil
	}

	values[constants.ProposalGeneralStandNum-1] = jsonData // general stand must be first

	for i, stand := range standsData.Catalog {
		jsonData, err := json.Marshal(stand)
		if err != nil {
			return proposalentity.Proposal{}, nil
		}
		values[i+1] = jsonData
	}

	var proposalType string
	if p.Type != nil {
		proposalType = *p.Type
	}

	var proposalStatus string
	if p.Status != nil {
		proposalStatus = *p.Status
	}
	proposalStatus = proposalvalueobject.NewStatus(proposalStatus).String()

	return proposalentity.Proposal{
		ProductID: productID,
		Price:     p.Price,
		Type:      proposalType,
		Status:    proposalStatus,
		CreatorID: creatorID,
	}, values
}

func ToEntityProposalFromV1DTO(p proposal.ProposalV1DTO) proposalentity.Proposal {
	return proposalentity.Proposal{
		ID:          p.ID,
		ProdPropSeq: p.PropSeq,
		ProductID:   p.ProductID,
	}
}

func ToEntityProposalLastViewedFromV1DTO(proposalID, userID int64, viewedAt time.Time) proposalvalueobject.UserView {
	return proposalvalueobject.UserView{
		ProposalID: proposalID,
		UserID:     userID,
		ViewedAt:   viewedAt.UTC(),
	}
}

func ToQueryAdminProposalFromGetProposalsAsAdminParams(params adminproposal.GetProposalsAsAdminParams) query.AdminProposal {
	productIDs := make([]int64, 0)
	if params.ProductIDs != nil {
		productIDs = *params.ProductIDs
	}

	return query.AdminProposal{
		Search:     params.Search,
		Type:       (*string)(params.Type),
		Status:     (*string)(params.Status),
		ProductIDs: productIDs,
		Sort:       (*string)(params.Sort),
		Order:      (*string)(params.Order),
		Limit:      params.Limit,
		Offset:     params.Offset,
	}
}

func ToV1DTOAdminProposalCollectionFromModels(paginatedResult entity.PaginatedResult[valueobject.ProposalFull]) (adminproposal.AdminProposalCollectionV1DTO, error) {
	var items []adminproposal.AdminProposalV1DTO
	for _, voProposal := range paginatedResult.Items {
		item, err := ToV1DTOAdminProposalFromEntity(voProposal)
		if err != nil {
			return adminproposal.AdminProposalCollectionV1DTO{}, err
		}
		items = append(items, item)
	}
	return adminproposal.AdminProposalCollectionV1DTO{
		Items: items,
		Meta: adminproposal.PaginationV1DTO{
			Limit:  paginatedResult.Limit,
			Offset: paginatedResult.Offset,
			Total:  paginatedResult.Total,
		},
	}, nil
}

func ToV1DTOAdminProposalFromEntity(p proposalvalueobject.ProposalFull) (adminproposal.AdminProposalV1DTO, error) {
	var jsonData map[string]any
	if err := json.Unmarshal(p.Data, &jsonData); err != nil {
		return adminproposal.AdminProposalV1DTO{}, err
	}

	return adminproposal.AdminProposalV1DTO{
		ID:            p.ID,
		PropSeq:       p.ProdPropSeq,
		ProductID:     p.ProductID,
		Price:         p.Price,
		Type:          p.Type,
		Status:        p.Status,
		CreatorID:     p.CreatorID,
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
		Data:          jsonData,
		ProductOwners: &p.ProductOwners,
		Product: &adminproposal.ProductBasicV1DTO{
			ID:       p.Product.ID,
			IID:      &p.Product.IID,
			TechName: p.Product.TechName,
			Name:     p.Product.Name,
		},
		ProposalNumber: p.Number,
	}, nil
}

func ToV1DTOProposalFromAggregate(pa *proposalaggregate.Proposal) (proposal.ProposalV1DTO, error) {

	standsDataBytes, err := getDataBytesFromStands(pa.Stands)
	if err != nil {
		return proposal.ProposalV1DTO{}, err
	}

	var jsonData map[string]any
	if err := json.Unmarshal(standsDataBytes, &jsonData); err != nil {
		return proposal.ProposalV1DTO{}, err
	}

	return proposal.ProposalV1DTO{
		ID:             pa.Proposal.ID,
		PropSeq:        pa.Proposal.ProdPropSeq,
		ProductID:      pa.Proposal.ProductID,
		Price:          pa.Proposal.Price,
		Type:           pa.Proposal.Type,
		Status:         pa.Proposal.Status,
		CreatorID:      pa.Proposal.CreatorID,
		ProposalNumber: &pa.Proposal.Number,
		Data:           jsonData,
		CreatedAt:      pa.Proposal.CreatedAt,
		UpdatedAt:      pa.Proposal.UpdatedAt,
	}, nil
}

func ToV1DTOProposalFromEntity(p proposalentity.Proposal) proposal.ProposalV1DTO {
	return proposal.ProposalV1DTO{
		ID:             p.ID,
		PropSeq:        p.ProdPropSeq,
		ProductID:      p.ProductID,
		Price:          p.Price,
		Type:           p.Type,
		Status:         p.Status,
		CreatorID:      p.CreatorID,
		CreatedAt:      p.CreatedAt,
		UpdatedAt:      p.UpdatedAt,
		ProposalNumber: &p.Number,
	}
}

func ToV1DTOProposalFromEntityFull(p proposalvalueobject.ProposalFull) (proposal.ProposalV1DTO, error) {
	var jsonData map[string]any
	if err := json.Unmarshal(p.Data, &jsonData); err != nil {
		return proposal.ProposalV1DTO{}, err
	}

	return proposal.ProposalV1DTO{
		ID:             p.Proposal.ID,
		PropSeq:        p.Proposal.ProdPropSeq,
		ProductID:      p.Proposal.ProductID,
		Price:          p.Proposal.Price,
		Type:           p.Proposal.Type,
		Status:         p.Proposal.Status,
		CreatorID:      p.Proposal.CreatorID,
		Data:           jsonData,
		CreatedAt:      p.Proposal.CreatedAt,
		UpdatedAt:      p.Proposal.UpdatedAt,
		ProposalNumber: &p.Number,
	}, nil
}

func ToV1DTOsProposalFromEntities(proposals []proposalentity.Proposal) []proposal.ProposalV1DTO {
	result := make([]proposal.ProposalV1DTO, len(proposals))
	for i, p := range proposals {
		result[i] = ToV1DTOProposalFromEntity(p)
	}
	return result
}

func createProposalEntity(productID, proposalID int64, p proposal.ProposalUpdateV1DTO) proposalentity.Proposal {
	var proposalType string
	if p.Type != nil {
		proposalType = *p.Type
	}

	return proposalentity.Proposal{
		ID:        proposalID,
		ProductID: productID,
		Price:     p.Price,
		Type:      proposalType,
		Status:    p.Status,
	}
}

func createStandsFromData(productID int64, data map[string]any) ([]proposalentity.Stand, error) {
	if data == nil {
		return nil, nil
	}

	standsData, err := parseStandsData(data)
	if err != nil {
		return nil, err
	}

	values, err := createValuesFromStandsData(standsData)
	if err != nil {
		return nil, err
	}

	return createStandsFromValues(productID, values), nil
}

func createStandsFromValues(productID int64, values proposalvalueobject.Values) []proposalentity.Stand {
	stands := make([]proposalentity.Stand, len(values))
	for i, value := range values {
		stands[i] = proposalentity.Stand{
			ProductID: productID,
			Data:      value,
		}
	}
	stands[0].Num = constants.ProposalGeneralStandNum // general stand must be first

	return stands
}

func createValuesFromStandsData(standsData proposalvalueobject.StandsData) (proposalvalueobject.Values, error) {
	var values proposalvalueobject.Values

	jsonData, err := json.Marshal(standsData.GeneralStand)
	if err != nil {
		return nil, err
	}
	values = append(values, jsonData) // general stand must be first

	for _, stand := range standsData.Catalog {
		jsonData, err := json.Marshal(stand)
		if err != nil {
			return nil, err
		}
		values = append(values, jsonData)
	}

	return values, nil
}

func getDataBytesFromStands(stands []proposalentity.Stand) ([]byte, error) {

	var generalStand valueobject.GeneralStand
	for i, stand := range stands {
		if stand.Num == constants.ProposalGeneralStandNum {
			if err := json.Unmarshal(stands[i].Data, &generalStand); err != nil {
				return nil, err
			}
			break
		}
	}

	createdData := valueobject.StandsData{
		GeneralStand: generalStand,
		Catalog:      make([]json.RawMessage, 0),
	}

	for i := 1; i < len(stands); i++ {
		createdData.Catalog = append(createdData.Catalog, stands[i].Data)
	}
	createdDataBytes, err := json.Marshal(createdData)
	if err != nil {
		return nil, err
	}

	return createdDataBytes, nil

}

func parseStandsData(data map[string]any) (proposalvalueobject.StandsData, error) {
	var standsData proposalvalueobject.StandsData

	dataBytes, err := json.Marshal(data)
	if err != nil {
		return standsData, err
	}

	if err := json.Unmarshal(dataBytes, &standsData); err != nil {
		return standsData, err
	}

	return standsData, nil
}
