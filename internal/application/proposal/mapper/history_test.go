package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

func TestToV1DTOsHistoryRecordFromVOs(t *testing.T) {
	tests := []struct {
		name     string
		history  []proposalvalueobject.HistoryRecord
		expected proposal.ProposalHistoryContainerV1DTO
	}{
		{
			name: "successful conversion with status and message events",
			history: []proposalvalueobject.HistoryRecord{
				{
					EventType: "status",
					Status:    "draft",
					Message:   func() *string { s := "Proposal created"; return &s }(),
					CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
				},
				{
					EventType: "message",
					Message:   func() *string { s := "Review comment"; return &s }(),
					UserID:    func() *int64 { id := int64(5); return &id }(),
					CreatedAt: time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					// First item is status event
					{},
					// Second item is message event
					{},
				},
			},
		},
		{
			name: "conversion with status event only",
			history: []proposalvalueobject.HistoryRecord{
				{
					EventType: "status",
					Status:    "approved",
					Message:   func() *string { s := "Proposal approved"; return &s }(),
					CreatedAt: time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					{},
				},
			},
		},
		{
			name: "conversion with message event only",
			history: []proposalvalueobject.HistoryRecord{
				{
					EventType: "message",
					Message:   func() *string { s := "Comment added"; return &s }(),
					UserID:    func() *int64 { id := int64(10); return &id }(),
					CreatedAt: time.Date(2023, 3, 1, 14, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					{},
				},
			},
		},
		{
			name: "conversion with status event without explicit EventType",
			history: []proposalvalueobject.HistoryRecord{
				{
					Status:    "rejected",
					Message:   func() *string { s := "Proposal rejected"; return &s }(),
					CreatedAt: time.Date(2023, 4, 1, 16, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					{},
				},
			},
		},
		{
			name: "conversion with message event having nil UserID",
			history: []proposalvalueobject.HistoryRecord{
				{
					EventType: "message",
					Message:   func() *string { s := "System message"; return &s }(),
					UserID:    nil,
					CreatedAt: time.Date(2023, 5, 1, 18, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					{},
				},
			},
		},
		{
			name: "conversion with empty status event",
			history: []proposalvalueobject.HistoryRecord{
				{
					EventType: "status",
					Status:    "",
					Message:   func() *string { s := "Empty status"; return &s }(),
					CreatedAt: time.Date(2023, 6, 1, 20, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					{},
				},
			},
		},
		{
			name:    "conversion with empty history",
			history: []proposalvalueobject.HistoryRecord{},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{},
			},
		},
		{
			name:    "conversion with nil history",
			history: nil,
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{},
			},
		},
		{
			name: "conversion with zero UserID",
			history: []proposalvalueobject.HistoryRecord{
				{
					EventType: "message",
					Message:   func() *string { s := "Zero user ID"; return &s }(),
					UserID:    func() *int64 { id := int64(0); return &id }(),
					CreatedAt: time.Date(2023, 7, 1, 22, 0, 0, 0, time.UTC),
				},
			},
			expected: proposal.ProposalHistoryContainerV1DTO{
				Items: []proposal.ProposalHistoryContainerV1DTO_Items_Item{
					{},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToV1DTOsHistoryRecordFromVOs(tt.history)

			// Check items count
			assert.Equal(t, len(tt.expected.Items), len(result.Items))

			// Check that meta contains LastViewedAt (it's set to time.Now())
			assert.NotNil(t, result.Meta.LastViewedAt)

			// For complex union types, we mainly verify the structure is created correctly
			// The actual event contents would require more detailed inspection of the union types
			if len(tt.history) > 0 {
				assert.NotEmpty(t, result.Items)

				for i := range result.Items {
					// Each item should be properly initialized
					// The exact content verification would require checking union type discriminators
					assert.NotNil(t, result.Items[i])
				}
			} else {
				assert.Empty(t, result.Items)
			}
		})
	}
}

func TestToV1DTOsHistoryRecordFromVOs_DetailedEventChecking(t *testing.T) {
	// More specific test to verify event content
	history := []proposalvalueobject.HistoryRecord{
		{
			EventType: "status",
			Status:    "approved",
			Message:   func() *string { s := "Status changed"; return &s }(),
			CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
		},
	}

	result := ToV1DTOsHistoryRecordFromVOs(history)

	assert.Len(t, result.Items, 1)
	assert.NotNil(t, result.Meta.LastViewedAt)

	// The item should be created but verifying exact union type content
	// requires more complex checking due to the generated union type structure
	item := result.Items[0]
	assert.NotNil(t, item)
}

func TestToV1DTOsHistoryRecordFromVOs_MessageEventWithUserID(t *testing.T) {
	userID := int64(42)
	history := []proposalvalueobject.HistoryRecord{
		{
			EventType: "message",
			Message:   func() *string { s := "User comment"; return &s }(),
			UserID:    &userID,
			CreatedAt: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
		},
	}

	result := ToV1DTOsHistoryRecordFromVOs(history)

	assert.Len(t, result.Items, 1)
	assert.NotNil(t, result.Meta.LastViewedAt)

	// The item should be created as a message event
	item := result.Items[0]
	assert.NotNil(t, item)
}
