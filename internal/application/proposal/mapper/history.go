package mapper

import (
	"time"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

func ToV1DTOsHistoryRecordFromVOs(history []proposalvalueobject.HistoryRecord) proposal.ProposalHistoryContainerV1DTO {
	items := make([]proposal.ProposalHistoryContainerV1DTO_Items_Item, len(history))

	for i, h := range history {
		var item proposal.ProposalHistoryContainerV1DTO_Items_Item

		// Определяем тип события по наличию полей
		if h.EventType == "status" || h.Status != "" {
			// Это событие изменения статуса
			event := proposal.ProposalHistoryStatusEventV1DTO{
				Type:      proposal.ProposalHistoryStatusEventV1DTOType("status"),
				CreatedAt: h.<PERSON>t,
				Message:   h.Message,
				Meta: proposal.ProposalHistoryStatusMetaV1DTO{
					Status: proposal.ProposalStatusV1DTO(h.Status),
				},
			}
			if h.Status != "" {
				status := proposal.ProposalHistoryEventStatusV1DTO(h.Status)
				event.Status = &status
			}
			_ = item.FromProposalHistoryStatusEventV1DTO(event)
		} else {
			// Это событие сообщения
			event := proposal.ProposalHistoryMessageEventV1DTO{
				Type:      proposal.ProposalHistoryMessageEventV1DTOType("message"),
				CreatedAt: h.CreatedAt,
				Message:   h.Message,
				Meta: proposal.ProposalHistoryMessageMetaV1DTO{
					UserID: 0, // TODO: получить UserID из h.UserID если оно не nil
				},
			}
			if h.UserID != nil {
				event.Meta.UserID = *h.UserID
			}
			_ = item.FromProposalHistoryMessageEventV1DTO(event)
		}

		items[i] = item
	}

	return proposal.ProposalHistoryContainerV1DTO{
		Items: items,
		Meta: proposal.ProposalLastViewedAtV1DTO{
			LastViewedAt: time.Now().UTC(), // TODO: получить реальное время последнего просмотра
		},
	}
}
