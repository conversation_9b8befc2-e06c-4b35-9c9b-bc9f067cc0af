package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/query"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func ptr[T any](v T) *T {
	return &v
}

func TestFilterProducts(t *testing.T) {
	products := []entity.AdminProduct{
		{Product: entity.Product{ID: 1, Name: "Product One", TechName: "prod_one", Description: "First product description", ActiveFlg: true}, Status: constants.ProductStatusActive, ParticipantCount: 10, Owners: []entity.Owner{}},
		{Product: entity.Product{ID: 2, Name: "Product Two", TechName: "prod_two", Description: "Second product description", ActiveFlg: false}, Status: constants.ProductStatusArchive, ParticipantCount: 5, Owners: []entity.Owner{}},
		{Product: entity.Product{ID: 3, Name: "Product Three", TechName: "prod_three", Description: "Third product description", ActiveFlg: true}, Status: constants.ProductStatusActive, ParticipantCount: 8, Owners: []entity.Owner{}},
		{Product: entity.Product{ID: 4, Name: "Product Four", TechName: "prod_four", Description: "Fourth product description", ActiveFlg: false}, Status: constants.ProductStatusArchive, ParticipantCount: 12, Owners: []entity.Owner{}},
	}

	testCases := []struct {
		name     string
		query    query.AdminProductsQuery
		expected []entity.AdminProduct
	}{
		{
			name:     "No filters",
			query:    query.AdminProductsQuery{},
			expected: products,
		},
		{
			name:     "Filter by search (case insensitive, matches Name)",
			query:    query.AdminProductsQuery{Search: ptr("one")},
			expected: []entity.AdminProduct{products[0]},
		},
		{
			name:     "Filter by search (case insensitive, matches TechName)",
			query:    query.AdminProductsQuery{Search: ptr("two")},
			expected: []entity.AdminProduct{products[1]},
		},
		{
			name:     "Filter by search (case insensitive, matches Description)",
			query:    query.AdminProductsQuery{Search: ptr("third")},
			expected: []entity.AdminProduct{products[2]},
		},
		{
			name:     "Filter by search (no match)",
			query:    query.AdminProductsQuery{Search: ptr("nomatch")},
			expected: []entity.AdminProduct{},
		},
		{
			name:     "Filter by search (empty string)",
			query:    query.AdminProductsQuery{Search: ptr("")},
			expected: products,
		},
		{
			name:     "Filter by status (active)",
			query:    query.AdminProductsQuery{Status: ptr("active")},
			expected: []entity.AdminProduct{products[0], products[2]},
		},
		{
			name:     "Filter by status (archive)",
			query:    query.AdminProductsQuery{Status: ptr("archive")},
			expected: []entity.AdminProduct{products[1], products[3]},
		},
		{
			name:     "Filter by status (unknown status, should return all)",
			query:    query.AdminProductsQuery{Status: ptr("unknown")},
			expected: products,
		},
		{
			name: "Combined filter (Search and Status)",
			query: query.AdminProductsQuery{
				Search: ptr("product"),
				Status: ptr("active"),
			},
			expected: []entity.AdminProduct{products[0], products[2]},
		},
		{
			name: "Combined filter (Search and Status, no match)",
			query: query.AdminProductsQuery{
				Search: ptr("one"),
				Status: ptr("archive"),
			},
			expected: []entity.AdminProduct{},
		},
		{
			name:     "Empty input products",
			query:    query.AdminProductsQuery{Search: ptr("test")},
			expected: []entity.AdminProduct{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var inputProducts []entity.AdminProduct
			if tc.name == "Empty input products" {
				inputProducts = []entity.AdminProduct{}
			} else {
				inputProducts = make([]entity.AdminProduct, len(products))
				copy(inputProducts, products)
			}
			result := filterProducts(inputProducts, tc.query)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestSortProducts(t *testing.T) {
	now := time.Now()
	products := []entity.AdminProduct{
		{Product: entity.Product{ID: 1, Name: "Charlie", TechName: "charlie_tech", CreatedAt: now.Add(-3 * time.Hour), UpdatedAt: now.Add(-1 * time.Hour), ActiveFlg: true}, Status: constants.ProductStatusActive, ParticipantCount: 10, Owners: []entity.Owner{{FullName: "Owner1"}}},
		{Product: entity.Product{ID: 2, Name: "Alice", TechName: "alice_tech", CreatedAt: now.Add(-1 * time.Hour), UpdatedAt: now.Add(-3 * time.Hour), ActiveFlg: false}, Status: constants.ProductStatusArchive, ParticipantCount: 5, Owners: []entity.Owner{}},
		{Product: entity.Product{ID: 3, Name: "Bob", TechName: "bob_tech", CreatedAt: now.Add(-2 * time.Hour), UpdatedAt: now.Add(-2 * time.Hour), ActiveFlg: true}, Status: constants.ProductStatusActive, ParticipantCount: 8, Owners: []entity.Owner{{FullName: "Owner1"}, {FullName: "Owner2"}}},
		{Product: entity.Product{ID: 4, Name: "David", TechName: "david_tech", CreatedAt: now.Add(-4 * time.Hour), UpdatedAt: now, ActiveFlg: false}, Status: constants.ProductStatusArchive, ParticipantCount: 12, Owners: []entity.Owner{{FullName: "Owner1"}, {FullName: "Owner2"}, {FullName: "Owner3"}}},
	}

	// Expected orders (Ascending)
	expectedOrderTechNameAsc := []entity.AdminProduct{products[1], products[2], products[0], products[3]}         // alice_tech, bob_tech, charlie_tech, david_tech
	expectedOrderParticipantCountAsc := []entity.AdminProduct{products[1], products[2], products[0], products[3]} // 5, 8, 10, 12
	expectedOrderOwnersAsc := []entity.AdminProduct{products[1], products[0], products[2], products[3]}           // 0, 1, 2, 3
	expectedOrderStatusAsc := []entity.AdminProduct{products[0], products[2], products[1], products[3]}           // Active, Active, Archive, Archive
	expectedOrderCreatedAtAsc := []entity.AdminProduct{products[3], products[0], products[2], products[1]}        // oldest to newest
	expectedOrderUpdatedAtAsc := []entity.AdminProduct{products[1], products[2], products[0], products[3]}        // oldest to newest

	// Expected orders (Descending)
	expectedOrderTechNameDesc := reverseProducts(expectedOrderTechNameAsc)
	expectedOrderParticipantCountDesc := reverseProducts(expectedOrderParticipantCountAsc)
	expectedOrderOwnersDesc := reverseProducts(expectedOrderOwnersAsc)
	expectedOrderStatusDesc := reverseProducts(expectedOrderStatusAsc)
	expectedOrderCreatedAtDesc := reverseProducts(expectedOrderCreatedAtAsc)
	expectedOrderUpdatedAtDesc := reverseProducts(expectedOrderUpdatedAtAsc)

	testCases := []struct {
		name     string
		query    query.AdminProductsQuery
		expected []entity.AdminProduct
	}{
		{
			name:     "No sort",
			query:    query.AdminProductsQuery{},
			expected: products,
		},
		{
			name:     "Sort by TechName Asc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldTechName.String()), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderTechNameAsc,
		},
		{
			name:     "Sort by TechName Desc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldTechName.String()), Order: ptr(sharedentity.SortOrderDescend.String())},
			expected: expectedOrderTechNameDesc,
		},
		{
			name:     "Sort by ParticipantCount Asc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldParticipantCount.String()), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderParticipantCountAsc,
		},
		{
			name:     "Sort by ParticipantCount Desc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldParticipantCount.String()), Order: ptr(sharedentity.SortOrderDescend.String())},
			expected: expectedOrderParticipantCountDesc,
		},
		{
			name:     "Sort by Owners Asc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldOwners.String()), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderOwnersAsc,
		},
		{
			name:     "Sort by Owners Desc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldOwners.String()), Order: ptr(sharedentity.SortOrderDescend.String())},
			expected: expectedOrderOwnersDesc,
		},
		{
			name:     "Sort by Status Asc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldStatus.String()), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderStatusAsc,
		},
		{
			name:     "Sort by Status Desc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldStatus.String()), Order: ptr(sharedentity.SortOrderDescend.String())},
			expected: expectedOrderStatusDesc,
		},
		{
			name:     "Sort by CreatedAt Asc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldCreatedAt.String()), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderCreatedAtAsc,
		},
		{
			name:     "Sort by CreatedAt Desc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldCreatedAt.String()), Order: ptr(sharedentity.SortOrderDescend.String())},
			expected: expectedOrderCreatedAtDesc,
		},
		{
			name:     "Sort by UpdatedAt Asc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldUpdatedAt.String()), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderUpdatedAtAsc,
		},
		{
			name:     "Sort by UpdatedAt Desc",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldUpdatedAt.String()), Order: ptr(sharedentity.SortOrderDescend.String())},
			expected: expectedOrderUpdatedAtDesc,
		},
		{
			name:     "Sort by invalid field (defaults to TechName Asc)",
			query:    query.AdminProductsQuery{Sort: ptr("invalidField"), Order: ptr(sharedentity.SortOrderAscend.String())},
			expected: expectedOrderTechNameAsc,
		},
		{
			name:     "Sort by TechName Asc (nil Order defaults to Asc)",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldTechName.String())},
			expected: expectedOrderTechNameAsc,
		},
		{
			name:     "Empty input products",
			query:    query.AdminProductsQuery{Sort: ptr(sharedentity.SortFieldTechName.String())},
			expected: []entity.AdminProduct{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var inputProducts []entity.AdminProduct
			if tc.name == "Empty input products" {
				inputProducts = []entity.AdminProduct{}
			} else {
				inputProducts = make([]entity.AdminProduct, len(products))
				copy(inputProducts, products)
			}
			result := sortProducts(inputProducts, tc.query)
			assert.Equal(t, tc.expected, result)
			if tc.query.Sort != nil && tc.name != "Empty input products" {
				assert.NotEqual(t, products, result, "Original slice should not be modified")
				originalCopy := make([]entity.AdminProduct, len(products))
				copy(originalCopy, products)
				assert.Equal(t, originalCopy, inputProducts, "Original slice content should remain unchanged")
			} else if tc.name != "Empty input products" {
				assert.Equal(t, inputProducts, result, "Should return the same slice instance if no sorting is applied")
			}
		})
	}
}

func TestApplyPagination(t *testing.T) {
	products := make([]entity.AdminProduct, 25)
	for i := 0; i < 25; i++ {
		products[i] = entity.AdminProduct{Product: entity.Product{ID: int64(i + 1)}}
	}

	testCases := []struct {
		name              string
		query             query.AdminProductsQuery
		expectedProducts  []entity.AdminProduct
		expectedLimit     int64
		expectedOffset    int64
		inputProductCount int
	}{
		{
			name:              "Default pagination (Limit=10, Offset=0)",
			query:             query.AdminProductsQuery{},
			expectedProducts:  products[0:10],
			expectedLimit:     constants.DefaultLimit,
			expectedOffset:    constants.DefaultOffset,
			inputProductCount: 25,
		},
		{
			name:              "Specific Limit and Offset",
			query:             query.AdminProductsQuery{Limit: ptr(int64(5)), Offset: ptr(int64(10))},
			expectedProducts:  products[10:15],
			expectedLimit:     5,
			expectedOffset:    10,
			inputProductCount: 25,
		},

		{
			name:              "Limit greater than remaining items",
			query:             query.AdminProductsQuery{Limit: ptr(int64(10)), Offset: ptr(int64(20))},
			expectedProducts:  products[20:25],
			expectedLimit:     10,
			expectedOffset:    20,
			inputProductCount: 25,
		},
		{
			name:              "Zero Limit",
			query:             query.AdminProductsQuery{Limit: ptr(int64(0)), Offset: ptr(int64(5))},
			expectedProducts:  []entity.AdminProduct{},
			expectedLimit:     0,
			expectedOffset:    5,
			inputProductCount: 25,
		},
		{
			name:              "Negative Offset (should be treated as 0)",
			query:             query.AdminProductsQuery{Limit: ptr(int64(5)), Offset: ptr(int64(-5))},
			expectedProducts:  []entity.AdminProduct{},
			expectedLimit:     5,
			expectedOffset:    -5,
			inputProductCount: 25,
		},
		{
			name:              "Nil Limit (use default)",
			query:             query.AdminProductsQuery{Offset: ptr(int64(5))},
			expectedProducts:  products[5:15],
			expectedLimit:     constants.DefaultLimit,
			expectedOffset:    5,
			inputProductCount: 25,
		},
		{
			name:              "Nil Offset (use default)",
			query:             query.AdminProductsQuery{Limit: ptr(int64(8))},
			expectedProducts:  products[0:8],
			expectedLimit:     8,
			expectedOffset:    constants.DefaultOffset,
			inputProductCount: 25,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			inputProducts := products[:tc.inputProductCount]
			if tc.inputProductCount == 0 {
				inputProducts = []entity.AdminProduct{}
			}

			paginatedProducts, limit, offset := applyPagination(inputProducts, tc.query)

			assert.Equal(t, tc.expectedProducts, paginatedProducts)
			assert.Equal(t, tc.expectedLimit, limit)
			assert.Equal(t, tc.expectedOffset, offset)
		})
	}
}

func TestApplyPagination_EmptyResult(t *testing.T) {
	products := []entity.AdminProduct{
		{Product: entity.Product{ID: 1}},
		{Product: entity.Product{ID: 2}},
	}

	// Test case where offset >= total should return nil
	query := query.AdminProductsQuery{
		Offset: ptr(int64(10)), // offset > len(products)
		Limit:  ptr(int64(5)),
	}

	result, limit, offset := applyPagination(products, query)

	assert.Nil(t, result)
	assert.Equal(t, int64(5), limit)
	assert.Equal(t, int64(10), offset)
}

// Helper function to reverse a slice for Desc order checks
func reverseProducts(s []entity.AdminProduct) []entity.AdminProduct {
	reversed := make([]entity.AdminProduct, len(s))
	for i, j := 0, len(s)-1; i <= j; i, j = i+1, j-1 {
		reversed[i], reversed[j] = s[j], s[i]
	}
	return reversed
}

func TestBoolPointer(t *testing.T) {
	trueVal := true
	falseVal := false
	assert.Equal(t, &trueVal, boolPointer(true))
	assert.Equal(t, &falseVal, boolPointer(false))
}
