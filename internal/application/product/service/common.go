package service

import (
	"sort"
	"strings"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/query"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func applyPagination(products []entity.AdminProduct, params query.AdminProductsQuery) ([]entity.AdminProduct, int64, int64) {
	limit := constants.DefaultLimit
	offset := constants.DefaultOffset

	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	total := int64(len(products))
	start := offset
	end := offset + limit

	if start < 0 {
		start = 0
	}
	if start >= total {
		return nil, limit, offset
	}

	if end > total {
		end = total
	}

	return products[start:end], limit, offset
}

func boolPointer(b bool) *bool {
	return &b
}

func filterProducts(products []entity.AdminProduct, params query.AdminProductsQuery) []entity.AdminProduct {
	filtered := make([]entity.AdminProduct, 0, len(products))

	for _, product := range products {
		keep := true

		// Search filter
		if params.Search != nil && *params.Search != "" {
			searchLower := strings.ToLower(*params.Search)
			if !strings.Contains(strings.ToLower(product.Name), searchLower) &&
				!strings.Contains(strings.ToLower(product.TechName), searchLower) &&
				!strings.Contains(strings.ToLower(product.Description), searchLower) {
				keep = false
			}
		}

		// Status filter
		if keep && params.Status != nil {
			paramStatus := constants.ToProductStatusFromString(*params.Status)
			if paramStatus != constants.ProductStatusUnknown && product.Status != paramStatus {
				keep = false
			}
		}

		if keep {
			filtered = append(filtered, product)
		}
	}
	return filtered
}

func sortProducts(products []entity.AdminProduct, params query.AdminProductsQuery) []entity.AdminProduct {
	if params.Sort == nil {
		return products
	}

	sorted := make([]entity.AdminProduct, len(products))
	copy(sorted, products)

	sortField := *params.Sort
	orderAsc := true
	if params.Order != nil && *params.Order == sharedentity.SortOrderDescend.String() {
		orderAsc = false
	}

	sort.SliceStable(sorted, func(i, j int) bool {
		var less bool
		p1 := sorted[i]
		p2 := sorted[j]

		switch sortField {
		// TechName
		case sharedentity.SortFieldTechName.String():
			less = p1.TechName < p2.TechName
		// ParticipantCount
		case sharedentity.SortFieldParticipantCount.String():
			less = p1.ParticipantCount < p2.ParticipantCount
		// Owners
		case sharedentity.SortFieldOwners.String():
			less = len(p1.Owners) < len(p2.Owners)
		// Status
		case sharedentity.SortFieldStatus.String():
			less = p1.Status < p2.Status
		// CreatedAt
		case sharedentity.SortFieldCreatedAt.String():
			less = p1.CreatedAt.Before(p2.CreatedAt)
		// UpdatedAt
		case sharedentity.SortFieldUpdatedAt.String():
			less = p1.UpdatedAt.Before(p2.UpdatedAt)
		default:
			less = p1.TechName < p2.TechName
		}

		if !orderAsc {
			return !less
		}
		return less
	})

	return sorted
}
