package service

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/query"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/aggregate"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type ProductAppService struct {
	productDomain     productservice.ProductDomainService
	participantDomain participantservice.ParticipantDomainService
	userDomain        userservice.UserDomainService
	roleDomain        roleservice.RoleDomainService
	groupDomain       groupservice.GroupDomainService
}

func NewProductAppService(
	productDomain productservice.ProductDomainService,
	participantDomain participantservice.ParticipantDomainService,
	userDomain userservice.UserDomainService,
	roleDomain roleservice.RoleDomainService,
	groupDomain groupservice.GroupDomainService,
) *ProductAppService {
	return &ProductAppService{
		productDomain:     productDomain,
		participantDomain: participantDomain,
		userDomain:        userDomain,
		roleDomain:        roleDomain,
		groupDomain:       groupDomain,
	}
}

func (s *ProductAppService) Create(ctx context.Context, product productentity.ProductCreateData, ownerEmails []string) (productentity.Product, error) {
	return s.productDomain.Create(ctx, product, ownerEmails)
}

func (s *ProductAppService) CreateAsAdmin(ctx context.Context, product productentity.ProductCreateData, ownerEmails []string) (aggregate.ProductWithDetails, error) {
	productCreated, err := s.Create(ctx, product, ownerEmails)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	productWithDetails, err := s.GetByIDToAdmin(productCreated.ID)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	return productWithDetails, nil
}

func (s *ProductAppService) GetAll() ([]productentity.Product, error) {
	return s.productDomain.GetAll()
}

func (s *ProductAppService) GetAllAsAdmin(query query.AdminProductsQuery) (sharedentity.PaginatedResult[productentity.AdminProduct], error) {
	products, err := s.productDomain.GetAllAsAdmin()
	if err != nil {
		return sharedentity.PaginatedResult[productentity.AdminProduct]{}, err
	}

	filteredProducts := filterProducts(products, query)
	sortedProducts := sortProducts(filteredProducts, query)
	paginatedProducts, limit, offset := applyPagination(sortedProducts, query)

	return sharedentity.PaginatedResult[productentity.AdminProduct]{
		Items:  paginatedProducts,
		Total:  int64(len(filteredProducts)),
		Limit:  limit,
		Offset: offset,
	}, nil
}

func (s *ProductAppService) GetByID(productID int64) (productentity.Product, error) {
	return s.productDomain.GetByID(productID)
}

func (s *ProductAppService) GetByIDToAdmin(productID int64) (aggregate.ProductWithDetails, error) {
	product, err := s.GetByID(productID)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	participants, err := s.participantDomain.GetByProductID(productID)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	owners, err := s.participantDomain.GetOwnersByProductIDs([]int64{productID})
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	productOwners := make(map[int64]bool)
	for _, owner := range owners[productID] {
		productOwners[owner.UserID] = true
	}

	participantShort := make([]participantentity.ParticipantShort, len(participants))
	for i, participant := range participants {
		user, err := s.userDomain.GetByID(participant.UserID)
		if err != nil {
			return aggregate.ProductWithDetails{}, err
		}

		participantShort[i] = participantentity.ParticipantShort{
			ID:        participant.ID,
			ProductID: participant.ProductID,
			UserID:    participant.UserID,
			Email:     user.Email,
			FullName:  user.FullName,
			IsOwner:   productOwners[participant.UserID],
			CreatedAt: participant.CreatedAt,
			UpdatedAt: participant.UpdatedAt,
		}
	}

	var status constants.ProductStatus
	if product.ActiveFlg {
		status = constants.ProductStatusActive
	} else {
		status = constants.ProductStatusArchive
	}

	roles, err := s.roleDomain.GetWithCountsByProductID(productID)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	groups, err := s.groupDomain.GetWithCountsByProductID(productID)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	return aggregate.ProductWithDetails{
		Product:          product,
		Status:           status,
		ParticipantCount: int64(len(participants)),
		Owners:           owners[productID],
		Participants:     participantShort,
		Roles:            roles,
		Groups:           groups,
	}, nil
}

func (s *ProductAppService) GetByIID(productIID string) (productentity.Product, error) {
	return s.productDomain.GetByIID(productIID)
}

func (s *ProductAppService) Update(data productentity.ProductUpdateData) (productentity.Product, error) {
	return s.productDomain.Update(data)
}

func (s *ProductAppService) UpdateAsAdmin(ctx context.Context, data productentity.AdminProductUpdateData) (aggregate.ProductWithDetails, error) {

	// TODO: implement status check
	if data.Status != nil {
		if *data.Status == constants.ProductStatusActive {
			data.ActiveFlg = boolPointer(true)
		} else {
			data.ActiveFlg = boolPointer(false)
		}
	}

	product, err := s.productDomain.Update(productentity.ProductUpdateData{
		ID:          data.ID,
		IID:         data.IID,
		Name:        data.Name,
		TechName:    data.TechName,
		Description: data.Description,
		ActiveFlg:   data.ActiveFlg,
	})
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	if data.OwnerEmails != nil {
		err := s.participantDomain.UpdateOwners(data.ID, *data.OwnerEmails)
		if err != nil {
			return aggregate.ProductWithDetails{}, err
		}
	}

	if data.RoleIDs != nil {
		err = s.productDomain.RemoveLinksWithRoles(ctx, data.ID, *data.RoleIDs)
		if err != nil {
			return aggregate.ProductWithDetails{}, err
		}
	}

	if data.GroupIDs != nil {
		err = s.productDomain.RemoveLinksWithGroups(ctx, data.ID, *data.GroupIDs)
		if err != nil {
			return aggregate.ProductWithDetails{}, err
		}
	}

	if data.ParticipantIDs != nil {
		err = s.productDomain.UpdateLinksWithParticipants(ctx, data.ID, *data.ParticipantIDs)
		if err != nil {
			return aggregate.ProductWithDetails{}, err
		}
	}

	productWithDetails, err := s.GetByIDToAdmin(product.ID)
	if err != nil {
		return aggregate.ProductWithDetails{}, err
	}

	return productWithDetails, nil
}

func (s *ProductAppService) Delete(productID int64) error {
	_, err := s.productDomain.Update(productentity.ProductUpdateData{
		ID:        productID,
		ActiveFlg: boolPointer(false),
	})
	return err
}
