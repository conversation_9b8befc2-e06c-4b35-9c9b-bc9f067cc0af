package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/query"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func TestNewProductAppService(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	require.NotNil(t, svc)
	require.Equal(t, mockProduct, svc.productDomain)
	require.Equal(t, mockParticipant, svc.participantDomain)
	require.Equal(t, mockUser, svc.userDomain)
	require.Equal(t, mockRole, svc.roleDomain)
	require.Equal(t, mockGroup, svc.groupDomain)
}

func TestProductAppService_Create(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.CreateMock.Expect(ctx, productentity.ProductCreateData{Name: "p1"}, []string{}).Return(productentity.Product{ID: 1, Name: "p1"}, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	res, err := svc.Create(ctx, productentity.ProductCreateData{Name: "p1"}, []string{})
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
	require.Equal(t, "p1", res.Name)
}

func TestProductAppService_Create_Error(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.CreateMock.Expect(ctx, productentity.ProductCreateData{Name: "fail"}, []string{}).Return(productentity.Product{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.Create(ctx, productentity.ProductCreateData{Name: "fail"}, []string{})
	require.Error(t, err)
	require.Equal(t, "fail", err.Error())
}

func TestProductAppService_CreateAsAdmin_CreateError(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.CreateMock.Expect(ctx, productentity.ProductCreateData{Name: "fail"}, []string{}).Return(productentity.Product{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.CreateAsAdmin(ctx, productentity.ProductCreateData{Name: "fail"}, []string{})
	require.Error(t, err)
}

func TestProductAppService_CreateAsAdmin_GetByIDError(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.CreateMock.Expect(ctx, productentity.ProductCreateData{Name: "p2"}, []string{}).Return(productentity.Product{ID: 2, Name: "p2"}, nil)
	mockProduct.GetByIDMock.Expect(int64(2)).Return(productentity.Product{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.CreateAsAdmin(ctx, productentity.ProductCreateData{Name: "p2"}, []string{})
	require.Error(t, err)
}

func TestProductAppService_GetAll(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetAllMock.Expect().Return([]productentity.Product{{ID: 1}}, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	res, err := svc.GetAll()
	require.NoError(t, err)
	require.Len(t, res, 1)
	require.Equal(t, int64(1), res[0].ID)
}

func TestProductAppService_GetAllAsAdmin(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	products := []productentity.AdminProduct{
		{
			Product: productentity.Product{ID: 1, ActiveFlg: true},
			Owners: []productentity.Owner{
				{ProductID: 1, Email: "a@b.c"},
				{ProductID: 1, Email: "b@b.c"},
			},
			ParticipantCount: 2,
		},
		{
			Product:          productentity.Product{ID: 2, ActiveFlg: false},
			Owners:           []productentity.Owner{},
			ParticipantCount: 0,
		},
	}

	mockProduct.GetAllAsAdminMock.Expect().Return(products, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	res, err := svc.GetAllAsAdmin(query.AdminProductsQuery{})
	require.NoError(t, err)
	require.Len(t, res.Items, 2)
	require.Equal(t, int64(1), res.Items[0].ID)
	require.Equal(t, 2, len(res.Items[0].Owners))
	require.Equal(t, int64(2), res.Items[0].ParticipantCount)
	require.Equal(t, int64(0), res.Items[1].ParticipantCount)
}

func TestProductAppService_GetByID(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetByIDMock.Expect(int64(1)).Return(productentity.Product{ID: 1}, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	res, err := svc.GetByID(1)
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
}

func TestProductAppService_GetByIID(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetByIIDMock.Expect("iid1").Return(productentity.Product{ID: 1, IID: "iid1"}, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	res, err := svc.GetByIID("iid1")
	require.NoError(t, err)
	require.Equal(t, "iid1", res.IID)
}

func TestProductAppService_Update(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 1}).Return(productentity.Product{ID: 1}, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	res, err := svc.Update(productentity.ProductUpdateData{ID: 1})
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
}

func TestProductAppService_Delete(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	inactive := false
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 1, ActiveFlg: &inactive}).Return(productentity.Product{ID: 1, ActiveFlg: false}, nil)
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	err := svc.Delete(1)
	require.NoError(t, err)
}

// Дополнительные тесты для полного покрытия ошибок
func TestProductAppService_GetAllAsAdmin_Error(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetAllAsAdminMock.Expect().Return(nil, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.GetAllAsAdmin(query.AdminProductsQuery{})
	require.Error(t, err)
}

func TestProductAppService_GetByIDToAdmin_GetByIDError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetByIDMock.Expect(int64(2)).Return(productentity.Product{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.GetByIDToAdmin(2)
	require.Error(t, err)
}

func TestProductAppService_GetByIDToAdmin_GetByProductIDError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetByIDMock.Expect(int64(3)).Return(productentity.Product{ID: 3, ActiveFlg: true}, nil)
	mockParticipant.GetByProductIDMock.Expect(int64(3)).Return(nil, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.GetByIDToAdmin(3)
	require.Error(t, err)
}

func TestProductAppService_GetByIDToAdmin_GetOwnersError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.GetByIDMock.Expect(int64(4)).Return(productentity.Product{ID: 4, ActiveFlg: true}, nil)
	mockParticipant.GetByProductIDMock.Expect(int64(4)).Return([]participantentity.Participant{{ID: 1}}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{4}).Return(nil, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.GetByIDToAdmin(4)
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_UpdateError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 2}).Return(productentity.Product{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), productentity.AdminProductUpdateData{ProductUpdateData: productentity.ProductUpdateData{ID: 2}})
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_UpdateOwnersError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	ownerEmails := []string{"a@b.c", "b@b.c"}
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 3}).Return(productentity.Product{ID: 3}, nil)
	mockParticipant.UpdateOwnersMock.Expect(int64(3), ownerEmails).Return(errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), productentity.AdminProductUpdateData{ProductUpdateData: productentity.ProductUpdateData{ID: 3}, OwnerEmails: &ownerEmails})
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_GetByIDError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	ownerEmails := []string{"a@b.c", "b@b.c"}
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 4}).Return(productentity.Product{ID: 4}, nil)
	mockParticipant.UpdateOwnersMock.Expect(int64(4), ownerEmails).Return(nil)
	mockProduct.GetByIDMock.Expect(int64(4)).Return(productentity.Product{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), productentity.AdminProductUpdateData{ProductUpdateData: productentity.ProductUpdateData{ID: 4}, OwnerEmails: &ownerEmails})
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_GetParticipantsError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	ownerEmails := []string{"a@b.c", "b@b.c"}
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 5}).Return(productentity.Product{ID: 5}, nil)
	mockParticipant.UpdateOwnersMock.Expect(int64(5), ownerEmails).Return(nil)
	mockProduct.GetByIDMock.Expect(int64(5)).Return(productentity.Product{ID: 5}, nil)
	mockParticipant.GetByProductIDMock.Expect(int64(5)).Return(nil, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), productentity.AdminProductUpdateData{ProductUpdateData: productentity.ProductUpdateData{ID: 5}, OwnerEmails: &ownerEmails})
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_GetOwnersErrorAfterUpdate(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	ownerEmails := []string{"a@b.c", "b@b.c"}
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 6}).Return(productentity.Product{ID: 6}, nil)
	mockParticipant.UpdateOwnersMock.Expect(int64(6), ownerEmails).Return(nil)
	mockProduct.GetByIDMock.Expect(int64(6)).Return(productentity.Product{ID: 6}, nil)
	mockParticipant.GetByProductIDMock.Expect(int64(6)).Return([]participantentity.Participant{{ID: 1}}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{6}).Return(nil, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), productentity.AdminProductUpdateData{ProductUpdateData: productentity.ProductUpdateData{ID: 6}, OwnerEmails: &ownerEmails})
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_GetUserError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	ownerEmails := []string{"a@b.c", "b@b.c"}
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: 7}).Return(productentity.Product{ID: 7}, nil)
	mockParticipant.UpdateOwnersMock.Expect(int64(7), ownerEmails).Return(nil)
	mockProduct.GetByIDMock.Expect(int64(7)).Return(productentity.Product{ID: 7}, nil)
	mockParticipant.GetByProductIDMock.Expect(int64(7)).Return([]participantentity.Participant{{ID: 1, UserID: 123, ProductID: 7}}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{7}).Return(map[int64][]productentity.Owner{7: {{ProductID: 7, Email: "a@b.c", UserID: 123}}}, nil)
	mockUser.GetByIDMock.Expect(int64(123)).Return(userentity.User{}, errors.New("fail"))
	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), productentity.AdminProductUpdateData{ProductUpdateData: productentity.ProductUpdateData{ID: 7}, OwnerEmails: &ownerEmails})
	require.Error(t, err)
}

func TestProductAppService_UpdateAsAdmin_UpdateRolesError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(1)
	roleIDs := []int64{1, 2, 3}
	data := productentity.AdminProductUpdateData{
		ProductUpdateData: productentity.ProductUpdateData{
			ID: productID,
		},
		RoleIDs: &roleIDs,
	}

	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: productID}).Return(productentity.Product{ID: productID}, nil)

	mockProduct.RemoveLinksWithRolesMock.Expect(context.Background(), productID, roleIDs).
		Return(errors.New("fail"))

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(context.Background(), data)

	require.Error(t, err)
}

func TestProductAppService_CreateAsAdmin_Success(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	createData := productentity.ProductCreateData{
		Name:      "Test Product",
		TechName:  "test_product",
		CreatorID: 1,
	}
	ownerEmails := []string{"<EMAIL>"}

	createdProduct := productentity.Product{
		ID:        123,
		Name:      "Test Product",
		TechName:  "test_product",
		CreatorID: 1,
		ActiveFlg: true,
	}

	// Mocks for Create
	mockProduct.CreateMock.Expect(ctx, createData, ownerEmails).Return(createdProduct, nil)

	// Mocks for GetByIDToAdmin
	mockProduct.GetByIDMock.Expect(createdProduct.ID).Return(createdProduct, nil)
	mockParticipant.GetByProductIDMock.Expect(createdProduct.ID).Return([]participantentity.Participant{}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{createdProduct.ID}).Return(map[int64][]productentity.Owner{createdProduct.ID: {}}, nil)
	mockRole.GetWithCountsByProductIDMock.Expect(createdProduct.ID).Return([]roleentity.AdminRole{}, nil)
	mockGroup.GetWithCountsByProductIDMock.Expect(createdProduct.ID).Return([]groupentity.AdminGroup{}, nil)

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	result, err := svc.CreateAsAdmin(ctx, createData, ownerEmails)

	require.NoError(t, err)
	assert.Equal(t, createdProduct.ID, result.Product.ID)
	assert.Equal(t, createdProduct.Name, result.Product.Name)
	assert.Equal(t, constants.ProductStatusActive, result.Status)
}

func TestProductAppService_GetByIDToAdmin_Success(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	user := userentity.User{
		ID:       1,
		Email:    "<EMAIL>",
		FullName: "Test User",
	}

	product := productentity.Product{
		ID:        productID,
		Name:      "Test Product",
		ActiveFlg: true,
	}

	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID},
	}

	owners := map[int64][]productentity.Owner{
		productID: {{UserID: 1, Email: "<EMAIL>", FullName: "Test User"}},
	}

	mockProduct.GetByIDMock.Expect(productID).Return(product, nil)
	mockParticipant.GetByProductIDMock.Expect(productID).Return(participants, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{productID}).Return(owners, nil)
	mockUser.GetByIDMock.Expect(int64(1)).Return(user, nil)
	mockRole.GetWithCountsByProductIDMock.Expect(productID).Return([]roleentity.AdminRole{}, nil)
	mockGroup.GetWithCountsByProductIDMock.Expect(productID).Return([]groupentity.AdminGroup{}, nil)

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	result, err := svc.GetByIDToAdmin(productID)

	require.NoError(t, err)
	assert.Equal(t, productID, result.Product.ID)
	assert.Equal(t, constants.ProductStatusActive, result.Status)
	assert.Equal(t, int64(1), result.ParticipantCount)
	require.Len(t, result.Participants, 1)
	assert.Equal(t, int64(1), result.Participants[0].UserID)
	assert.True(t, result.Participants[0].IsOwner)
}

func TestProductAppService_GetByIDToAdmin_GetRolesError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	product := productentity.Product{ID: productID, ActiveFlg: true}

	mockProduct.GetByIDMock.Expect(productID).Return(product, nil)
	mockParticipant.GetByProductIDMock.Expect(productID).Return([]participantentity.Participant{}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{productID}).Return(map[int64][]productentity.Owner{productID: {}}, nil)
	mockRole.GetWithCountsByProductIDMock.Expect(productID).Return([]roleentity.AdminRole{}, errors.New("role error"))

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.GetByIDToAdmin(productID)

	require.Error(t, err)
	assert.Equal(t, "role error", err.Error())
}

func TestProductAppService_GetByIDToAdmin_GetGroupsError(t *testing.T) {
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	product := productentity.Product{ID: productID, ActiveFlg: true}

	mockProduct.GetByIDMock.Expect(productID).Return(product, nil)
	mockParticipant.GetByProductIDMock.Expect(productID).Return([]participantentity.Participant{}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{productID}).Return(map[int64][]productentity.Owner{productID: {}}, nil)
	mockRole.GetWithCountsByProductIDMock.Expect(productID).Return([]roleentity.AdminRole{}, nil)
	mockGroup.GetWithCountsByProductIDMock.Expect(productID).Return([]groupentity.AdminGroup{}, errors.New("group error"))

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.GetByIDToAdmin(productID)

	require.Error(t, err)
	assert.Equal(t, "group error", err.Error())
}

func TestProductAppService_UpdateAsAdmin_Success(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	activeStatus := constants.ProductStatusActive
	name := "Updated Product"

	updateData := productentity.AdminProductUpdateData{
		ProductUpdateData: productentity.ProductUpdateData{
			ID:   productID,
			Name: &name,
		},
		Status: &activeStatus,
	}

	updatedProduct := productentity.Product{
		ID:        productID,
		Name:      "Updated Product",
		ActiveFlg: true,
	}

	// Mocks for Update
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{
		ID:        productID,
		Name:      &name,
		ActiveFlg: boolPointer(true),
	}).Return(updatedProduct, nil)

	// Mocks for GetByIDToAdmin
	mockProduct.GetByIDMock.Expect(productID).Return(updatedProduct, nil)
	mockParticipant.GetByProductIDMock.Expect(productID).Return([]participantentity.Participant{}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{productID}).Return(map[int64][]productentity.Owner{productID: {}}, nil)
	mockRole.GetWithCountsByProductIDMock.Expect(productID).Return([]roleentity.AdminRole{}, nil)
	mockGroup.GetWithCountsByProductIDMock.Expect(productID).Return([]groupentity.AdminGroup{}, nil)

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	assert.Equal(t, productID, result.Product.ID)
	assert.Equal(t, "Updated Product", result.Product.Name)
	assert.Equal(t, constants.ProductStatusActive, result.Status)
}

func TestProductAppService_UpdateAsAdmin_ArchiveStatus(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	archiveStatus := constants.ProductStatusArchive

	updateData := productentity.AdminProductUpdateData{
		ProductUpdateData: productentity.ProductUpdateData{
			ID: productID,
		},
		Status: &archiveStatus,
	}

	updatedProduct := productentity.Product{
		ID:        productID,
		ActiveFlg: false,
	}

	// Mocks for Update
	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{
		ID:        productID,
		ActiveFlg: boolPointer(false),
	}).Return(updatedProduct, nil)

	// Mocks for GetByIDToAdmin
	mockProduct.GetByIDMock.Expect(productID).Return(updatedProduct, nil)
	mockParticipant.GetByProductIDMock.Expect(productID).Return([]participantentity.Participant{}, nil)
	mockParticipant.GetOwnersByProductIDsMock.Expect([]int64{productID}).Return(map[int64][]productentity.Owner{productID: {}}, nil)
	mockRole.GetWithCountsByProductIDMock.Expect(productID).Return([]roleentity.AdminRole{}, nil)
	mockGroup.GetWithCountsByProductIDMock.Expect(productID).Return([]groupentity.AdminGroup{}, nil)

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	assert.Equal(t, constants.ProductStatusArchive, result.Status)
}

func TestProductAppService_UpdateAsAdmin_RemoveGroupsError(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	groupIDs := []int64{1, 2}

	updateData := productentity.AdminProductUpdateData{
		ProductUpdateData: productentity.ProductUpdateData{
			ID: productID,
		},
		GroupIDs: &groupIDs,
	}

	updatedProduct := productentity.Product{ID: productID}

	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: productID}).Return(updatedProduct, nil)
	mockProduct.RemoveLinksWithGroupsMock.Expect(ctx, productID, groupIDs).Return(errors.New("group error"))

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	assert.Equal(t, "group error", err.Error())
}

func TestProductAppService_UpdateAsAdmin_UpdateParticipantsError(t *testing.T) {
	ctx := context.Background()
	mockProduct := productmocks.NewProductDomainServiceMock(t)
	mockParticipant := participantmocks.NewParticipantDomainServiceMock(t)
	mockUser := usermocks.NewUserDomainServiceMock(t)
	mockRole := rolemocks.NewRoleDomainServiceMock(t)
	mockGroup := groupmocks.NewGroupDomainServiceMock(t)

	productID := int64(123)
	participantIDs := []string{"1", "2"}

	updateData := productentity.AdminProductUpdateData{
		ProductUpdateData: productentity.ProductUpdateData{
			ID: productID,
		},
		ParticipantIDs: &participantIDs,
	}

	updatedProduct := productentity.Product{ID: productID}

	mockProduct.UpdateMock.Expect(productentity.ProductUpdateData{ID: productID}).Return(updatedProduct, nil)
	mockProduct.UpdateLinksWithParticipantsMock.Expect(ctx, productID, participantIDs).Return(errors.New("participant error"))

	svc := NewProductAppService(mockProduct, mockParticipant, mockUser, mockRole, mockGroup)
	_, err := svc.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	assert.Equal(t, "participant error", err.Error())
}
