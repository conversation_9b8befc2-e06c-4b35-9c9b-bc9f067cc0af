package query

// AdminProductsQuery contains parameters for querying the list of products by administrator.
type AdminProductsQuery struct {
	// Filtering parameters
	Search *string // Search text (by Name, TechName, Description)
	Status *string // Desired product status ("active", "archive")

	// Sorting parameters
	Sort  *string // Field for sorting ("techName", "participantCount", "owners", "status", "createdAt", "updatedAt")
	Order *string // Sort order ("ascend", "descend")

	// Pagination parameters
	Limit  *int64 // Number of items per page
	Offset *int64 // Offset (number of items to skip)
}
