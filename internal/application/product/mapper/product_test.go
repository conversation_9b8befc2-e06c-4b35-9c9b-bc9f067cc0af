package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

func TestToV1DTOProductFromModel(t *testing.T) {
	createdAt := time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)

	p := productentity.Product{
		ID:          123,
		IID:         "test-iid",
		TechName:    "test_product",
		Name:        "Test Product",
		Description: "Test Description",
		CreatorID:   456,
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
	}

	result := ToV1DTOProductFromModel(p)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "test-iid", result.IID)
	assert.Equal(t, "test_product", result.TechName)
	assert.Equal(t, "Test Product", result.Name)
	assert.Equal(t, "Test Description", result.Desc)
	assert.Equal(t, int64(456), result.CreatorID)
	assert.Equal(t, createdAt, result.CreatedAt)
	assert.Equal(t, updatedAt, result.UpdatedAt)
}

func TestToV1DTOProductCollectionFromV1DTOsProduct(t *testing.T) {
	products := []product.ProductV1DTO{
		{ID: 1, Name: "Product 1"},
		{ID: 2, Name: "Product 2"},
	}
	activeProductID := int64(1)

	result := ToV1DTOProductCollectionFromV1DTOsProduct(products, activeProductID)

	require.Len(t, result.Items, 2)
	assert.Equal(t, int64(1), result.Items[0].ID)
	assert.Equal(t, "Product 1", result.Items[0].Name)
	assert.Equal(t, int64(2), result.Items[1].ID)
	assert.Equal(t, "Product 2", result.Items[1].Name)
	assert.Equal(t, activeProductID, result.Meta.ActiveProductID)
}

func TestToV1DTOParticipantShortFromModels(t *testing.T) {
	createdAt := time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)

	participants := []participantentity.ParticipantShort{
		{
			ID:        1,
			UserID:    10,
			Email:     "<EMAIL>",
			FullName:  "User 1",
			IsOwner:   true,
			ProductID: 100,
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
		},
		{
			ID:        2,
			UserID:    20,
			Email:     "<EMAIL>",
			FullName:  "User 2",
			IsOwner:   false,
			ProductID: 100,
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
		},
	}

	result := ToV1DTOParticipantShortFromModels(participants)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, int64(10), result[0].UserID)
	assert.Equal(t, "<EMAIL>", result[0].Email)
	assert.Equal(t, "User 1", result[0].FullName)
	assert.True(t, result[0].IsOwner)
	assert.Equal(t, int64(100), result[0].ProductID)
	assert.Equal(t, createdAt, result[0].CreatedAt)
	assert.Equal(t, updatedAt, result[0].UpdatedAt)
}

func TestToV1DTOParticipantShortFromModels_Empty(t *testing.T) {
	var participants []participantentity.ParticipantShort

	result := ToV1DTOParticipantShortFromModels(participants)

	assert.Empty(t, result)
}
