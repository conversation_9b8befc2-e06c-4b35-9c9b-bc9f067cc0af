package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/aggregate"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func TestToV1DTOAdminProductCollectionFromModels(t *testing.T) {
	adminProducts := []productentity.AdminProduct{
		{
			Product: productentity.Product{
				ID:   1,
				Name: "Product 1",
			},
			Status: constants.ProductStatusActive,
		},
		{
			Product: productentity.Product{
				ID:   2,
				Name: "Product 2",
			},
			Status: constants.ProductStatusArchive,
		},
	}

	paginatedResult := entity.PaginatedResult[productentity.AdminProduct]{
		Items:  adminProducts,
		Limit:  10,
		Offset: 0,
		Total:  2,
	}

	result := ToV1DTOAdminProductCollectionFromModels(paginatedResult)

	require.Len(t, result.Items, 2)
	assert.Equal(t, int64(1), result.Items[0].ID)
	assert.Equal(t, "Product 1", result.Items[0].Name)
	assert.Equal(t, adminproduct.AdminProductV1DTOStatus("active"), result.Items[0].Status)
	assert.Equal(t, int64(2), result.Items[1].ID)
	assert.Equal(t, "Product 2", result.Items[1].Name)
	assert.Equal(t, adminproduct.AdminProductV1DTOStatus("archive"), result.Items[1].Status)

	assert.Equal(t, int64(10), result.Meta.Limit)
	assert.Equal(t, int64(0), result.Meta.Offset)
	assert.Equal(t, int64(2), result.Meta.Total)
}

func TestToV1DTOAdminProductFromModelAdminProduct(t *testing.T) {
	createdAt := time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)

	owners := []productentity.Owner{
		{UserID: 1, Email: "<EMAIL>", FullName: "Owner 1"},
		{UserID: 2, Email: "<EMAIL>", FullName: "Owner 2"},
	}

	adminProduct := productentity.AdminProduct{
		Product: productentity.Product{
			ID:          123,
			IID:         "test-iid",
			TechName:    "test_product",
			Name:        "Test Product",
			Description: "Test Description",
			CreatorID:   456,
			CreatedAt:   createdAt,
			UpdatedAt:   updatedAt,
		},
		Status:           constants.ProductStatusActive,
		ParticipantCount: 5,
		Owners:           owners,
	}

	result := ToV1DTOAdminProductFromModelAdminProduct(adminProduct)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "test-iid", result.IID)
	assert.Equal(t, "test_product", result.TechName)
	assert.Equal(t, "Test Product", result.Name)
	assert.Equal(t, "Test Description", result.Desc)
	assert.Equal(t, int64(456), result.CreatorID)
	assert.Equal(t, createdAt, result.CreatedAt)
	assert.Equal(t, updatedAt, result.UpdatedAt)
	assert.Equal(t, adminproduct.AdminProductV1DTOStatus("active"), result.Status)
	assert.Equal(t, int64(5), result.ParticipantCount)
	require.Len(t, result.Owners, 2)
	assert.Equal(t, int64(1), result.Owners[0].ID)
	assert.Equal(t, "<EMAIL>", result.Owners[0].Email)
}

func TestToV1DTOProductWithDetailsFromAggregateProductWithDetails(t *testing.T) {
	createdAt := time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC)

	participants := []participantentity.ParticipantShort{
		{ID: 1, UserID: 1, Email: "<EMAIL>", FullName: "User 1", IsOwner: true, ProductID: 123, CreatedAt: createdAt, UpdatedAt: updatedAt},
	}

	roles := []roleentity.AdminRole{
		{ID: 1, Name: "Admin", IsSystem: true, IsActive: true, ParticipantCount: 2, GroupCount: 1},
	}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admins", Type: "system", ParticipantCount: 2, RoleCount: 1, IsActive: true},
	}

	owners := []productentity.Owner{
		{UserID: 1, Email: "<EMAIL>", FullName: "Owner"},
	}

	productWithDetails := aggregate.ProductWithDetails{
		Product: productentity.Product{
			ID:          123,
			IID:         "test-iid",
			TechName:    "test_product",
			Name:        "Test Product",
			Description: "Test Description",
			CreatorID:   456,
			CreatedAt:   createdAt,
			UpdatedAt:   updatedAt,
		},
		Status:           constants.ProductStatusActive,
		ParticipantCount: 5,
		Owners:           owners,
		Participants:     participants,
		Roles:            roles,
		Groups:           groups,
	}

	result := ToV1DTOProductWithDetailsFromAggregateProductWithDetails(productWithDetails)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "test-iid", result.IID)
	assert.Equal(t, "test_product", result.TechName)
	assert.Equal(t, "Test Product", result.Name)
	assert.Equal(t, adminproduct.ProductWithDetailsV1DTOStatus("active"), result.Status)
	require.Len(t, result.Participants, 1)
	require.Len(t, result.Roles, 1)
	require.Len(t, result.Groups, 1)
	require.Len(t, result.Owners, 1)
}

func TestToV1DTOsRoleWithCountsFromEntities_Empty(t *testing.T) {
	var roles []roleentity.AdminRole

	result := ToV1DTOsRoleWithCountsFromEntities(roles)

	assert.Empty(t, result)
}

func TestToV1DTOsGroupWithCountsFromEntities(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{
			ID:               1,
			Name:             "Admins",
			Type:             "system",
			ParticipantCount: 5,
			RoleCount:        2,
			IsActive:         true,
		},
		{
			ID:               2,
			Name:             "Users",
			Type:             "custom",
			ParticipantCount: 10,
			RoleCount:        3,
			IsActive:         false,
		},
	}

	result := ToV1DTOsGroupWithCountsFromEntities(groups)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Admins", result[0].Name)
	assert.Equal(t, "system", result[0].Type)
	assert.Equal(t, int64(5), result[0].ParticipantCount)
	assert.Equal(t, int64(2), result[0].RoleCount)
	assert.True(t, result[0].IsActive)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Users", result[1].Name)
	assert.Equal(t, "custom", result[1].Type)
	assert.Equal(t, int64(10), result[1].ParticipantCount)
	assert.Equal(t, int64(3), result[1].RoleCount)
	assert.False(t, result[1].IsActive)
}

func TestToV1DTOsGroupWithCountsFromEntities_Empty(t *testing.T) {
	var groups []groupentity.AdminGroup

	result := ToV1DTOsGroupWithCountsFromEntities(groups)

	assert.Empty(t, result)
}

func TestToV1DTOAdminProductOwnersFromModels(t *testing.T) {
	owners := []productentity.Owner{
		{
			UserID:   1,
			Email:    "<EMAIL>",
			FullName: "Owner 1",
		},
		{
			UserID:   2,
			Email:    "<EMAIL>",
			FullName: "Owner 2",
		},
	}

	result := ToV1DTOAdminProductOwnersFromModels(owners)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "<EMAIL>", result[0].Email)
	assert.Equal(t, "Owner 1", result[0].FullName)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "<EMAIL>", result[1].Email)
	assert.Equal(t, "Owner 2", result[1].FullName)
}

func TestToV1DTOAdminProductOwnersFromModels_Empty(t *testing.T) {
	var owners []productentity.Owner

	result := ToV1DTOAdminProductOwnersFromModels(owners)

	assert.Empty(t, result)
}

func TestToV1DTOsRoleWithCountsFromEntities(t *testing.T) {
	roles := []roleentity.AdminRole{
		{
			ID:               1,
			Name:             "Admin",
			IsSystem:         true,
			IsActive:         true,
			ParticipantCount: 5,
			GroupCount:       2,
		},
		{
			ID:               2,
			Name:             "User",
			IsSystem:         false,
			IsActive:         false,
			ParticipantCount: 10,
			GroupCount:       3,
		},
	}

	result := ToV1DTOsRoleWithCountsFromEntities(roles)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Admin", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.True(t, result[0].IsActive)
	assert.Equal(t, int64(5), result[0].ParticipantCount)
	assert.Equal(t, int64(2), result[0].GroupCount)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "User", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
	assert.False(t, result[1].IsActive)
	assert.Equal(t, int64(10), result[1].ParticipantCount)
	assert.Equal(t, int64(3), result[1].GroupCount)
}

func TestToModelAdminProductUpdateDataFromV1DTO(t *testing.T) {
	id := int64(123)
	iid := "updated-iid"
	techName := "updated_product"
	name := "Updated Product"
	desc := "Updated Description"
	status := adminproduct.AdminProductUpdateV1DTOStatus("active")
	ownerEmails := []string{"<EMAIL>", "<EMAIL>"}
	roleIDs := []int64{1, 2}
	groupIDs := []int64{3, 4}
	participantIDs := []string{"participant1", "participant2"}

	dto := adminproduct.AdminProductUpdateV1DTO{
		IID:            &iid,
		TechName:       &techName,
		Name:           &name,
		Desc:           &desc,
		Status:         &status,
		OwnerEmails:    &ownerEmails,
		RoleIDs:        &roleIDs,
		GroupIDs:       &groupIDs,
		ParticipantIDs: &participantIDs,
	}

	result := ToModelAdminProductUpdateDataFromV1DTO(id, dto)

	assert.Equal(t, id, result.ProductUpdateData.ID)
	assert.Equal(t, &iid, result.ProductUpdateData.IID)
	assert.Equal(t, &techName, result.ProductUpdateData.TechName)
	assert.Equal(t, &name, result.ProductUpdateData.Name)
	assert.Equal(t, &desc, result.ProductUpdateData.Description)
	require.NotNil(t, result.Status)
	assert.Equal(t, constants.ProductStatusActive, *result.Status)
	assert.Equal(t, &ownerEmails, result.OwnerEmails)
	assert.Equal(t, &roleIDs, result.RoleIDs)
	assert.Equal(t, &groupIDs, result.GroupIDs)
	assert.Equal(t, &participantIDs, result.ParticipantIDs)
}

func TestToModelAdminProductUpdateDataFromV1DTO_NilStatus(t *testing.T) {
	id := int64(123)

	dto := adminproduct.AdminProductUpdateV1DTO{
		Status: nil,
	}

	result := ToModelAdminProductUpdateDataFromV1DTO(id, dto)

	assert.Equal(t, id, result.ProductUpdateData.ID)
	assert.Nil(t, result.Status)
}

func TestToProductStatusFromString(t *testing.T) {
	tests := []struct {
		name     string
		input    adminproduct.AdminProductUpdateV1DTOStatus
		expected constants.ProductStatus
	}{
		{
			name:     "active status",
			input:    "active",
			expected: constants.ProductStatusActive,
		},
		{
			name:     "archive status",
			input:    "archive",
			expected: constants.ProductStatusArchive,
		},
		{
			name:     "unknown status",
			input:    "unknown",
			expected: constants.ProductStatusUnknown,
		},
		{
			name:     "invalid status",
			input:    "invalid",
			expected: constants.ProductStatusUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToProductStatusFromString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
