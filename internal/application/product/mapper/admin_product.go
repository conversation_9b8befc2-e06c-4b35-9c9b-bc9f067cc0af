package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/aggregate"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func ToModelAdminProductUpdateDataFromV1DTO(id int64, p adminproduct.AdminProductUpdateV1DTO) productentity.AdminProductUpdateData {

	var status *constants.ProductStatus
	if p.Status != nil {
		status2 := ToProductStatusFromString(*p.Status)
		status = &status2
	}

	return productentity.AdminProductUpdateData{
		ProductUpdateData: productentity.ProductUpdateData{
			ID:          id,
			IID:         p.IID,
			TechName:    p.TechName,
			Name:        p.Name,
			Description: p.Desc,
		},
		Status:         status,
		OwnerEmails:    p.OwnerEmails,
		RoleIDs:        p.RoleIDs,
		GroupIDs:       p.GroupIDs,
		ParticipantIDs: p.ParticipantIDs,
	}
}

func ToProductStatusFromString(s adminproduct.AdminProductUpdateV1DTOStatus) constants.ProductStatus {
	switch s {
	case "active":
		return constants.ProductStatusActive
	case "archive":
		return constants.ProductStatusArchive
	default:
		return constants.ProductStatusUnknown
	}
}

func ToV1DTOAdminProductCollectionFromModels(paginatedResult entity.PaginatedResult[productentity.AdminProduct]) adminproduct.AdminProductCollectionV1DTO {

	var items []adminproduct.AdminProductV1DTO
	for _, product := range paginatedResult.Items {
		items = append(items, ToV1DTOAdminProductFromModelAdminProduct(product))
	}

	return adminproduct.AdminProductCollectionV1DTO{
		Items: items,
		Meta: adminproduct.PaginationV1DTO{
			Limit:  paginatedResult.Limit,
			Offset: paginatedResult.Offset,
			Total:  paginatedResult.Total,
		},
	}
}

func ToV1DTOAdminProductFromModelAdminProduct(p productentity.AdminProduct) adminproduct.AdminProductV1DTO {
	return adminproduct.AdminProductV1DTO{
		ID:               p.ID,
		Status:           adminproduct.AdminProductV1DTOStatus(p.Status.String()),
		IID:              p.IID,
		ParticipantCount: p.ParticipantCount,
		Owners:           ToV1DTOAdminProductOwnersFromModels(p.Owners),
		TechName:         p.TechName,
		Name:             p.Name,
		Desc:             p.Description,
		CreatorID:        p.CreatorID,
		CreatedAt:        p.CreatedAt,
		UpdatedAt:        p.UpdatedAt,
	}
}

func ToV1DTOAdminProductOwnersFromModels(owners []productentity.Owner) []adminproduct.OwnerV1DTO {
	var items []adminproduct.OwnerV1DTO
	for _, owner := range owners {
		items = append(items, adminproduct.OwnerV1DTO{
			ID:       owner.UserID,
			Email:    owner.Email,
			FullName: owner.FullName,
		})
	}
	return items
}

func ToV1DTOParticipantShortFromModels(participants []participantentity.ParticipantShort) []adminproduct.ParticipantShortV1DTO {
	var items []adminproduct.ParticipantShortV1DTO
	for _, participant := range participants {
		items = append(items, adminproduct.ParticipantShortV1DTO{
			ID:        participant.ID,
			UserID:    participant.UserID,
			Email:     participant.Email,
			FullName:  participant.FullName,
			IsOwner:   participant.IsOwner,
			ProductID: participant.ProductID,
			CreatedAt: participant.CreatedAt,
			UpdatedAt: participant.UpdatedAt,
		})
	}
	return items
}

func ToV1DTOProductWithDetailsFromAggregateProductWithDetails(p aggregate.ProductWithDetails) adminproduct.ProductWithDetailsV1DTO {
	return adminproduct.ProductWithDetailsV1DTO{
		ID:               p.ID,
		IID:              p.IID,
		TechName:         p.TechName,
		Name:             p.Name,
		Desc:             p.Description,
		CreatorID:        p.CreatorID,
		CreatedAt:        p.CreatedAt,
		UpdatedAt:        p.UpdatedAt,
		Status:           adminproduct.ProductWithDetailsV1DTOStatus(p.Status.String()),
		ParticipantCount: p.ParticipantCount,
		Owners:           ToV1DTOAdminProductOwnersFromModels(p.Owners),
		Participants:     ToV1DTOParticipantShortFromModels(p.Participants),
		Roles:            ToV1DTOsRoleWithCountsFromEntities(p.Roles),
		Groups:           ToV1DTOsGroupWithCountsFromEntities(p.Groups),
	}
}

func ToV1DTOsGroupWithCountsFromEntities(groups []groupentity.AdminGroup) []adminproduct.GroupWithCountsV1DTO {
	var items []adminproduct.GroupWithCountsV1DTO
	for _, group := range groups {
		items = append(items, adminproduct.GroupWithCountsV1DTO{
			ID:               group.ID,
			Name:             group.Name,
			Type:             group.Type,
			ParticipantCount: group.ParticipantCount,
			RoleCount:        group.RoleCount,
			IsActive:         group.IsActive,
		})
	}
	return items
}

func ToV1DTOsRoleWithCountsFromEntities(roles []roleentity.AdminRole) []adminproduct.RoleWithCountsV1DTO {
	var items []adminproduct.RoleWithCountsV1DTO
	for _, role := range roles {
		items = append(items, adminproduct.RoleWithCountsV1DTO{
			ID:               role.ID,
			Name:             role.Name,
			Type:             constants.IsSystemToConstant(role.IsSystem),
			IsActive:         role.IsActive,
			ParticipantCount: role.ParticipantCount,
			GroupCount:       role.GroupCount,
		})
	}
	return items
}
