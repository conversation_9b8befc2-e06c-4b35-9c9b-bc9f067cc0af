package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

func ToV1DTOProductCollectionFromV1DTOsProduct(products []product.ProductV1DTO, lastActiveProductID int64) product.ProductCollectionV1DTO {
	return product.ProductCollectionV1DTO{
		Items: products,
		Meta: product.ProductMetaV1DTO{
			ActiveProductID: lastActiveProductID,
		},
	}
}

func ToV1DTOProductFromModel(p productentity.Product) product.ProductV1DTO {
	return product.ProductV1DTO{
		ID:        p.ID,
		IID:       p.IID,
		TechName:  p.TechName,
		Name:      p.Name,
		Desc:      p.Description,
		CreatorID: p.CreatorID,
		CreatedAt: p.CreatedAt,
		UpdatedAt: p.UpdatedAt,
	}
}
