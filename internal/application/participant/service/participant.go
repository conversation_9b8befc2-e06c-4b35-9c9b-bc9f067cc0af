package service

import (
	"context"

	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	identityprovideresrvice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/service"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type ParticipantAppService struct {
	groupDomain       groupservice.GroupDomainService
	keycloakDomain    identityprovideresrvice.KeycloakDomainService
	participantDomain participantservice.ParticipantDomainService
	productDomain     productservice.ProductDomainService
	roleDomain        roleservice.RoleDomainService
	userDomain        userservice.UserDomainService
}

func NewParticipantAppService(
	groupDomain groupservice.GroupDomainService,
	keycloakDomain identityprovideresrvice.KeycloakDomainService,
	participantDomain participantservice.ParticipantDomainService,
	productDomain productservice.ProductDomainService,
	roleDomain roleservice.RoleDomainService,
	userDomain userservice.UserDomainService,
) *ParticipantAppService {
	return &ParticipantAppService{
		groupDomain:       groupDomain,
		keycloakDomain:    keycloakDomain,
		participantDomain: participantDomain,
		productDomain:     productDomain,
		roleDomain:        roleDomain,
		userDomain:        userDomain,
	}
}

func (s *ParticipantAppService) Create(ctx context.Context, participant participantentity.ParticipantCreateData) (participantentity.ParticipantFull, error) {
	return s.participantDomain.CreateParticipant(ctx, participant)
}

func (s *ParticipantAppService) GetByUserID(userID int64) ([]participantentity.Participant, error) {
	return s.participantDomain.GetByUserID(userID)
}

func (s *ParticipantAppService) GetByUserIDAndProductID(userID int64, productID int64) (participantentity.Participant, error) {
	return s.participantDomain.GetByUserIDAndProductID(userID, productID)
}

func (s *ParticipantAppService) GetByUserIDAndProductIDs(userID int64, productIDs []int64) ([]participantentity.Participant, error) {
	return s.participantDomain.GetByUserIDAndProductIDs(userID, productIDs)
}

func (s *ParticipantAppService) GetParticipantFullByParticipantIDAndProductID(participantID, productID int64) (participantentity.ParticipantFull, error) {
	return s.participantDomain.GetParticipantFullByParticipantIDAndProductID(participantID, productID)

}

func (s *ParticipantAppService) GetParticipantFullByProductID(productID int64) ([]participantentity.ParticipantFull, error) {
	return s.participantDomain.GetParticipantFullByProductID(productID)

}

func (s *ParticipantAppService) ExistByParticipantIDAndProductID(participantID, productID int64) (bool, error) {
	return s.participantDomain.ExistByParticipantIDAndProductID(participantID, productID)
}

func (s *ParticipantAppService) UpdateGroups(ctx context.Context, participantID int64, groupIDs []int64) ([]int64, error) {
	return s.participantDomain.UpdateGroups(ctx, participantID, groupIDs)
}

func (s *ParticipantAppService) DeleteByParticipantIDAndProductID(ctx context.Context, participantID int64, productID int64) error {
	return s.participantDomain.DeleteByParticipantIDAndProductID(ctx, participantID, productID)

}

func (s *ParticipantAppService) DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error {
	return s.participantDomain.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)
}
