package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	identityprovidermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/mocks"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	rolesmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func TestNewParticipantAppService(t *testing.T) {
	mockGroupDomain := groupmocks.NewGroupDomainServiceMock(t)
	mockKeycloakDomain := identityprovidermocks.NewKeycloakDomainServiceMock(t)
	mockParticipantDomain := participantmocks.NewParticipantDomainServiceMock(t)
	mockProductDomain := productmocks.NewProductDomainServiceMock(t)
	mockRoleDomain := rolesmocks.NewRoleDomainServiceMock(t)
	mockUserDomain := usermocks.NewUserDomainServiceMock(t)

	service := NewParticipantAppService(
		mockGroupDomain,
		mockKeycloakDomain,
		mockParticipantDomain,
		mockProductDomain,
		mockRoleDomain,
		mockUserDomain,
	)

	require.NotNil(t, service)
	assert.Equal(t, mockGroupDomain, service.groupDomain)
	assert.Equal(t, mockKeycloakDomain, service.keycloakDomain)
	assert.Equal(t, mockParticipantDomain, service.participantDomain)
	assert.Equal(t, mockProductDomain, service.productDomain)
	assert.Equal(t, mockRoleDomain, service.roleDomain)
	assert.Equal(t, mockUserDomain, service.userDomain)
}

func TestParticipantAppService_Create(t *testing.T) {
	tests := []struct {
		name            string
		participant     participantentity.ParticipantCreateData
		mockParticipant participantentity.ParticipantFull
		mockError       error
		expectedError   error
	}{
		{
			name: "успешное создание участника",
			participant: participantentity.ParticipantCreateData{
				UserID:    1,
				ProductID: 1,
			},
			mockParticipant: participantentity.ParticipantFull{
				ID:        1,
				UserID:    1,
				ProductID: 1,
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name: "ошибка при создании участника",
			participant: participantentity.ParticipantCreateData{
				UserID:    1,
				ProductID: 1,
			},
			mockParticipant: participantentity.ParticipantFull{},
			mockError:       errors.New("create error"),
			expectedError:   errors.New("create error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.CreateParticipantMock.Expect(context.Background(), tt.participant).Return(tt.mockParticipant, tt.mockError)

			participant, err := service.Create(context.Background(), tt.participant)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockParticipant, participant)
			}

			require.True(t, mockParticipantDomain.MinimockCreateParticipantDone())
		})
	}
}

func TestParticipantAppService_GetByUserID(t *testing.T) {
	tests := []struct {
		name             string
		userID           int64
		mockParticipants []participantentity.Participant
		mockError        error
		expectedError    error
	}{
		{
			name:   "успешное получение участников",
			userID: 1,
			mockParticipants: []participantentity.Participant{
				{ID: 1, UserID: 1, ProductID: 1},
				{ID: 2, UserID: 1, ProductID: 2},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:             "пустой список участников",
			userID:           1,
			mockParticipants: []participantentity.Participant{},
			mockError:        nil,
			expectedError:    nil,
		},
		{
			name:             "ошибка при получении участников",
			userID:           1,
			mockParticipants: nil,
			mockError:        errors.New("get error"),
			expectedError:    errors.New("get error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.GetByUserIDMock.Expect(tt.userID).Return(tt.mockParticipants, tt.mockError)

			participants, err := service.GetByUserID(tt.userID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockParticipants, participants)
			}

			require.True(t, mockParticipantDomain.MinimockGetByUserIDDone())
		})
	}
}

func TestParticipantAppService_GetByUserIDAndProductID(t *testing.T) {
	tests := []struct {
		name            string
		userID          int64
		productID       int64
		mockParticipant participantentity.Participant
		mockError       error
		expectedError   error
	}{
		{
			name:      "успешное получение участника",
			userID:    1,
			productID: 1,
			mockParticipant: participantentity.Participant{
				ID:        1,
				UserID:    1,
				ProductID: 1,
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:            "участник не найден",
			userID:          1,
			productID:       1,
			mockParticipant: participantentity.Participant{},
			mockError:       errors.New("not found"),
			expectedError:   errors.New("not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.GetByUserIDAndProductIDMock.Expect(tt.userID, tt.productID).Return(tt.mockParticipant, tt.mockError)

			participant, err := service.GetByUserIDAndProductID(tt.userID, tt.productID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockParticipant, participant)
			}

			require.True(t, mockParticipantDomain.MinimockGetByUserIDAndProductIDDone())
		})
	}
}

func TestParticipantAppService_GetByUserIDAndProductIDs(t *testing.T) {
	tests := []struct {
		name             string
		userID           int64
		productIDs       []int64
		mockParticipants []participantentity.Participant
		mockError        error
		expectedError    error
	}{
		{
			name:       "успешное получение участников",
			userID:     1,
			productIDs: []int64{1, 2},
			mockParticipants: []participantentity.Participant{
				{ID: 1, UserID: 1, ProductID: 1},
				{ID: 2, UserID: 1, ProductID: 2},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:             "пустой список участников",
			userID:           1,
			productIDs:       []int64{1, 2},
			mockParticipants: []participantentity.Participant{},
			mockError:        nil,
			expectedError:    nil,
		},
		{
			name:             "ошибка при получении участников",
			userID:           1,
			productIDs:       []int64{1, 2},
			mockParticipants: nil,
			mockError:        errors.New("get error"),
			expectedError:    errors.New("get error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.GetByUserIDAndProductIDsMock.Expect(tt.userID, tt.productIDs).Return(tt.mockParticipants, tt.mockError)

			participants, err := service.GetByUserIDAndProductIDs(tt.userID, tt.productIDs)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockParticipants, participants)
			}

			require.True(t, mockParticipantDomain.MinimockGetByUserIDAndProductIDsDone())
		})
	}
}

func TestParticipantAppService_DeleteByParticipantIDAndProductID(t *testing.T) {
	tests := []struct {
		name          string
		participantID int64
		productID     int64
		mockError     error
		expectedError error
	}{
		{
			name:          "успешное удаление участника",
			participantID: 1,
			productID:     1,
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "ошибка при удалении участника",
			participantID: 1,
			productID:     1,
			mockError:     errors.New("delete error"),
			expectedError: errors.New("delete error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.DeleteByParticipantIDAndProductIDMock.Expect(context.Background(), tt.participantID, tt.productID).Return(tt.mockError)

			err := service.DeleteByParticipantIDAndProductID(context.Background(), tt.participantID, tt.productID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			require.True(t, mockParticipantDomain.MinimockDeleteByParticipantIDAndProductIDDone())
		})
	}
}

func TestParticipantAppService_DeleteByUserIDAndProductIDs(t *testing.T) {
	tests := []struct {
		name          string
		userID        int64
		productIDs    []int64
		mockError     error
		expectedError error
	}{
		{
			name:          "успешное удаление участника по нескольким продуктам",
			userID:        1,
			productIDs:    []int64{1, 2, 3},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "удаление по одному продукту",
			userID:        1,
			productIDs:    []int64{1},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "пустой список продуктов",
			userID:        1,
			productIDs:    []int64{},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "nil список продуктов",
			userID:        1,
			productIDs:    nil,
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:          "ошибка при удалении",
			userID:        1,
			productIDs:    []int64{1, 2},
			mockError:     errors.New("delete error"),
			expectedError: errors.New("delete error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.DeleteByUserIDAndProductIDsMock.Expect(context.Background(), tt.userID, tt.productIDs).Return(tt.mockError)

			err := service.DeleteByUserIDAndProductIDs(context.Background(), tt.userID, tt.productIDs)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			require.True(t, mockParticipantDomain.MinimockDeleteByUserIDAndProductIDsDone())
		})
	}
}

func TestParticipantAppService_GetParticipantFullByProductID(t *testing.T) {
	tests := []struct {
		name             string
		productID        int64
		mockParticipants []participantentity.ParticipantFull
		mockError        error
		expectedError    error
	}{
		{
			name:      "успешное получение участников",
			productID: 1,
			mockParticipants: []participantentity.ParticipantFull{
				{ID: 1, UserID: 1, ProductID: 1},
				{ID: 2, UserID: 2, ProductID: 1},
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:             "пустой список участников",
			productID:        1,
			mockParticipants: []participantentity.ParticipantFull{},
			mockError:        nil,
			expectedError:    nil,
		},
		{
			name:             "ошибка при получении участников",
			productID:        1,
			mockParticipants: nil,
			mockError:        errors.New("get error"),
			expectedError:    errors.New("get error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.GetParticipantFullByProductIDMock.Expect(tt.productID).Return(tt.mockParticipants, tt.mockError)

			participants, err := service.GetParticipantFullByProductID(tt.productID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockParticipants, participants)
			}

			require.True(t, mockParticipantDomain.MinimockGetParticipantFullByProductIDDone())
		})
	}
}

func TestParticipantAppService_GetParticipantFullByParticipantIDAndProductID(t *testing.T) {
	tests := []struct {
		name            string
		participantID   int64
		productID       int64
		mockParticipant participantentity.ParticipantFull
		mockError       error
		expectedError   error
	}{
		{
			name:          "успешное получение участника",
			participantID: 1,
			productID:     1,
			mockParticipant: participantentity.ParticipantFull{
				ID:        1,
				UserID:    1,
				ProductID: 1,
			},
			mockError:     nil,
			expectedError: nil,
		},
		{
			name:            "участник не найден",
			participantID:   1,
			productID:       1,
			mockParticipant: participantentity.ParticipantFull{},
			mockError:       errors.New("not found"),
			expectedError:   errors.New("not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.GetParticipantFullByParticipantIDAndProductIDMock.Expect(tt.participantID, tt.productID).Return(tt.mockParticipant, tt.mockError)

			participant, err := service.GetParticipantFullByParticipantIDAndProductID(tt.participantID, tt.productID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.mockParticipant, participant)
			}

			require.True(t, mockParticipantDomain.MinimockGetParticipantFullByParticipantIDAndProductIDDone())
		})
	}
}

func TestParticipantAppService_ExistByParticipantIDAndProductID(t *testing.T) {
	tests := []struct {
		name           string
		participantID  int64
		productID      int64
		mockExists     bool
		mockError      error
		expectedExists bool
		expectedError  error
	}{
		{
			name:           "участник существует",
			participantID:  1,
			productID:      1,
			mockExists:     true,
			mockError:      nil,
			expectedExists: true,
			expectedError:  nil,
		},
		{
			name:           "участник не существует",
			participantID:  1,
			productID:      1,
			mockExists:     false,
			mockError:      nil,
			expectedExists: false,
			expectedError:  nil,
		},
		{
			name:           "ошибка при проверке существования",
			participantID:  1,
			productID:      1,
			mockExists:     false,
			mockError:      errors.New("check error"),
			expectedExists: false,
			expectedError:  errors.New("check error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.ExistByParticipantIDAndProductIDMock.Expect(tt.participantID, tt.productID).Return(tt.mockExists, tt.mockError)

			exists, err := service.ExistByParticipantIDAndProductID(tt.participantID, tt.productID)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedExists, exists)
			}

			require.True(t, mockParticipantDomain.MinimockExistByParticipantIDAndProductIDDone())
		})
	}
}

func TestParticipantAppService_UpdateGroups(t *testing.T) {
	tests := []struct {
		name             string
		participantID    int64
		groupIDs         []int64
		mockGroupIDs     []int64
		mockError        error
		expectedGroupIDs []int64
		expectedError    error
	}{
		{
			name:             "успешное обновление групп",
			participantID:    1,
			groupIDs:         []int64{1, 2},
			mockGroupIDs:     []int64{1, 2},
			mockError:        nil,
			expectedGroupIDs: []int64{1, 2},
			expectedError:    nil,
		},
		{
			name:             "пустой список групп",
			participantID:    1,
			groupIDs:         []int64{},
			mockGroupIDs:     []int64{},
			mockError:        nil,
			expectedGroupIDs: []int64{},
			expectedError:    nil,
		},
		{
			name:             "ошибка при обновлении групп",
			participantID:    1,
			groupIDs:         []int64{1, 2},
			mockGroupIDs:     nil,
			mockError:        errors.New("update error"),
			expectedGroupIDs: nil,
			expectedError:    errors.New("update error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockParticipantDomain := mocks.NewParticipantDomainServiceMock(t)
			service := NewParticipantAppService(
				nil,
				nil,
				mockParticipantDomain,
				nil,
				nil,
				nil,
			)

			mockParticipantDomain.UpdateGroupsMock.Expect(context.Background(), tt.participantID, tt.groupIDs).Return(tt.mockGroupIDs, tt.mockError)

			groupIDs, err := service.UpdateGroups(context.Background(), tt.participantID, tt.groupIDs)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedGroupIDs, groupIDs)
			}

			require.True(t, mockParticipantDomain.MinimockUpdateGroupsDone())
		})
	}
}
