package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
)

func ToEntityParticipantCreateDataFromParticipantCreateV1DTO(productID int64, dto participant.ParticipantCreateV1DTO) entity.ParticipantCreateData {
	return entity.ParticipantCreateData{
		Email:     dto.Email,
		ProductID: productID,
	}
}

func ToParticipantCollectionV1DTOFromParticipantsEntity(parts []entity.ParticipantFull) participant.ParticipantCollectionV1DTO {
	partDTO := make([]participant.ParticipantV1DTO, 0, len(parts))

	for _, v := range parts {

		partDTO = append(partDTO, participant.ParticipantV1DTO{
			ID:          v.ID,
			ProductID:   v.ProductID,
			UserID:      v.UserID,
			Email:       v.Email,
			FullName:    v.FullName,
			Position:    v.Position,
			IsOwner:     v.Is<PERSON>wn<PERSON>,
			GroupIDs:    v.GroupIDs,
			RoleIDs:     v.Role<PERSON>s,
			LastLoginAt: v.LastLoginAt,
			CreatedAt:   v.CreatedAt,
			UpdatedAt:   v.UpdatedAt,
		})
	}
	return participant.ParticipantCollectionV1DTO{
		Items: partDTO,
	}
}

func ToParticipantV1DTOFromEntityParticipantFull(part entity.ParticipantFull) participant.ParticipantV1DTO {
	vx := participant.ParticipantV1DTO{
		ID:          part.ID,
		ProductID:   part.ProductID,
		UserID:      part.UserID,
		Email:       part.Email,
		FullName:    part.FullName,
		Position:    part.Position,
		IsOwner:     part.IsOwner,
		GroupIDs:    part.GroupIDs,
		RoleIDs:     part.RoleIDs,
		LastLoginAt: part.LastLoginAt,
		CreatedAt:   part.CreatedAt,
		UpdatedAt:   part.UpdatedAt,
	}
	return vx
}
