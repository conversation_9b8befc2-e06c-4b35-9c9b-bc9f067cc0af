package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
)

func TestToEntityParticipantCreateDataFromParticipantCreateV1DTO(t *testing.T) {
	tests := []struct {
		name      string
		productID int64
		dto       participant.ParticipantCreateV1DTO
		expected  entity.ParticipantCreateData
	}{
		{
			name:      "successful conversion with email",
			productID: 123,
			dto: participant.ParticipantCreateV1DTO{
				Email: "<EMAIL>",
			},
			expected: entity.ParticipantCreateData{
				Email:     "<EMAIL>",
				ProductID: 123,
			},
		},
		{
			name:      "conversion with empty email",
			productID: 456,
			dto: participant.ParticipantCreateV1DTO{
				Email: "",
			},
			expected: entity.ParticipantCreateData{
				Email:     "",
				ProductID: 456,
			},
		},
		{
			name:      "conversion with special characters in email",
			productID: 789,
			dto: participant.ParticipantCreateV1DTO{
				Email: "<EMAIL>",
			},
			expected: entity.ParticipantCreateData{
				Email:     "<EMAIL>",
				ProductID: 789,
			},
		},
		{
			name:      "conversion with zero product ID",
			productID: 0,
			dto: participant.ParticipantCreateV1DTO{
				Email: "<EMAIL>",
			},
			expected: entity.ParticipantCreateData{
				Email:     "<EMAIL>",
				ProductID: 0,
			},
		},
		{
			name:      "conversion with negative product ID",
			productID: -1,
			dto: participant.ParticipantCreateV1DTO{
				Email: "<EMAIL>",
			},
			expected: entity.ParticipantCreateData{
				Email:     "<EMAIL>",
				ProductID: -1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToEntityParticipantCreateDataFromParticipantCreateV1DTO(tt.productID, tt.dto)

			assert.Equal(t, tt.expected, result)
			assert.Equal(t, tt.expected.Email, result.Email)
			assert.Equal(t, tt.expected.ProductID, result.ProductID)
		})
	}
}

func TestToParticipantV1DTOFromEntityParticipantFull(t *testing.T) {
	tests := []struct {
		name     string
		entity   entity.ParticipantFull
		expected participant.ParticipantV1DTO
	}{
		{
			name: "successful conversion with all fields",
			entity: entity.ParticipantFull{
				ID:          1,
				ProductID:   123,
				UserID:      456,
				Email:       "<EMAIL>",
				FullName:    "John Doe",
				Position:    "Developer",
				IsOwner:     true,
				GroupIDs:    []int64{1, 2, 3},
				RoleIDs:     []int64{10, 20},
				LastLoginAt: func() *time.Time { t := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC); return &t }(),
				CreatedAt:   time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
				UpdatedAt:   time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
			},
			expected: participant.ParticipantV1DTO{
				ID:          1,
				ProductID:   123,
				UserID:      456,
				Email:       "<EMAIL>",
				FullName:    "John Doe",
				Position:    "Developer",
				IsOwner:     true,
				GroupIDs:    []int64{1, 2, 3},
				RoleIDs:     []int64{10, 20},
				LastLoginAt: func() *time.Time { t := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC); return &t }(),
				CreatedAt:   time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
				UpdatedAt:   time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
			},
		},
		{
			name: "conversion with minimal fields",
			entity: entity.ParticipantFull{
				ID:        10,
				ProductID: 100,
				UserID:    200,
				Email:     "<EMAIL>",
				IsOwner:   false,
			},
			expected: participant.ParticipantV1DTO{
				ID:        10,
				ProductID: 100,
				UserID:    200,
				Email:     "<EMAIL>",
				IsOwner:   false,
			},
		},
		{
			name: "conversion with empty string fields",
			entity: entity.ParticipantFull{
				ID:        5,
				ProductID: 50,
				UserID:    100,
				Email:     "",
				FullName:  "",
				Position:  "",
				IsOwner:   false,
				GroupIDs:  []int64{},
				RoleIDs:   []int64{},
			},
			expected: participant.ParticipantV1DTO{
				ID:        5,
				ProductID: 50,
				UserID:    100,
				Email:     "",
				FullName:  "",
				Position:  "",
				IsOwner:   false,
				GroupIDs:  []int64{},
				RoleIDs:   []int64{},
			},
		},
		{
			name: "conversion with nil slices",
			entity: entity.ParticipantFull{
				ID:        7,
				ProductID: 70,
				UserID:    140,
				Email:     "<EMAIL>",
				IsOwner:   true,
				GroupIDs:  nil,
				RoleIDs:   nil,
			},
			expected: participant.ParticipantV1DTO{
				ID:        7,
				ProductID: 70,
				UserID:    140,
				Email:     "<EMAIL>",
				IsOwner:   true,
				GroupIDs:  nil,
				RoleIDs:   nil,
			},
		},
		{
			name: "conversion with nil LastLoginAt",
			entity: entity.ParticipantFull{
				ID:          3,
				ProductID:   30,
				UserID:      60,
				Email:       "<EMAIL>",
				IsOwner:     false,
				LastLoginAt: nil,
				CreatedAt:   time.Date(2023, 5, 1, 9, 0, 0, 0, time.UTC),
				UpdatedAt:   time.Date(2023, 5, 1, 10, 0, 0, 0, time.UTC),
			},
			expected: participant.ParticipantV1DTO{
				ID:          3,
				ProductID:   30,
				UserID:      60,
				Email:       "<EMAIL>",
				IsOwner:     false,
				LastLoginAt: nil,
				CreatedAt:   time.Date(2023, 5, 1, 9, 0, 0, 0, time.UTC),
				UpdatedAt:   time.Date(2023, 5, 1, 10, 0, 0, 0, time.UTC),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToParticipantV1DTOFromEntityParticipantFull(tt.entity)

			assert.Equal(t, tt.expected, result)

			// Verify field by field mapping
			assert.Equal(t, tt.expected.ID, result.ID)
			assert.Equal(t, tt.expected.ProductID, result.ProductID)
			assert.Equal(t, tt.expected.UserID, result.UserID)
			assert.Equal(t, tt.expected.Email, result.Email)
			assert.Equal(t, tt.expected.FullName, result.FullName)
			assert.Equal(t, tt.expected.Position, result.Position)
			assert.Equal(t, tt.expected.IsOwner, result.IsOwner)
			assert.Equal(t, tt.expected.GroupIDs, result.GroupIDs)
			assert.Equal(t, tt.expected.RoleIDs, result.RoleIDs)
			assert.Equal(t, tt.expected.LastLoginAt, result.LastLoginAt)
			assert.Equal(t, tt.expected.CreatedAt, result.CreatedAt)
			assert.Equal(t, tt.expected.UpdatedAt, result.UpdatedAt)
		})
	}
}

func TestToParticipantCollectionV1DTOFromParticipantsEntity(t *testing.T) {
	tests := []struct {
		name     string
		entities []entity.ParticipantFull
		expected participant.ParticipantCollectionV1DTO
	}{
		{
			name: "successful conversion with multiple participants",
			entities: []entity.ParticipantFull{
				{
					ID:        1,
					ProductID: 10,
					UserID:    100,
					Email:     "<EMAIL>",
					FullName:  "User One",
					Position:  "Manager",
					IsOwner:   true,
					GroupIDs:  []int64{1, 2},
					RoleIDs:   []int64{5},
					CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
					UpdatedAt: time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
				},
				{
					ID:        2,
					ProductID: 10,
					UserID:    200,
					Email:     "<EMAIL>",
					FullName:  "User Two",
					Position:  "Developer",
					IsOwner:   false,
					GroupIDs:  []int64{3},
					RoleIDs:   []int64{6, 7},
					CreatedAt: time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC),
					UpdatedAt: time.Date(2023, 1, 2, 11, 0, 0, 0, time.UTC),
				},
			},
			expected: participant.ParticipantCollectionV1DTO{
				Items: []participant.ParticipantV1DTO{
					{
						ID:        1,
						ProductID: 10,
						UserID:    100,
						Email:     "<EMAIL>",
						FullName:  "User One",
						Position:  "Manager",
						IsOwner:   true,
						GroupIDs:  []int64{1, 2},
						RoleIDs:   []int64{5},
						CreatedAt: time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
						UpdatedAt: time.Date(2023, 1, 1, 11, 0, 0, 0, time.UTC),
					},
					{
						ID:        2,
						ProductID: 10,
						UserID:    200,
						Email:     "<EMAIL>",
						FullName:  "User Two",
						Position:  "Developer",
						IsOwner:   false,
						GroupIDs:  []int64{3},
						RoleIDs:   []int64{6, 7},
						CreatedAt: time.Date(2023, 1, 2, 10, 0, 0, 0, time.UTC),
						UpdatedAt: time.Date(2023, 1, 2, 11, 0, 0, 0, time.UTC),
					},
				},
			},
		},
		{
			name: "conversion with single participant",
			entities: []entity.ParticipantFull{
				{
					ID:        999,
					ProductID: 1000,
					UserID:    2000,
					Email:     "<EMAIL>",
					FullName:  "Single User",
					IsOwner:   true,
				},
			},
			expected: participant.ParticipantCollectionV1DTO{
				Items: []participant.ParticipantV1DTO{
					{
						ID:        999,
						ProductID: 1000,
						UserID:    2000,
						Email:     "<EMAIL>",
						FullName:  "Single User",
						IsOwner:   true,
					},
				},
			},
		},
		{
			name:     "conversion with empty slice",
			entities: []entity.ParticipantFull{},
			expected: participant.ParticipantCollectionV1DTO{
				Items: []participant.ParticipantV1DTO{},
			},
		},
		{
			name:     "conversion with nil slice",
			entities: nil,
			expected: participant.ParticipantCollectionV1DTO{
				Items: []participant.ParticipantV1DTO{},
			},
		},
		{
			name: "conversion with participants having empty fields",
			entities: []entity.ParticipantFull{
				{
					ID:        0,
					ProductID: 0,
					UserID:    0,
					Email:     "",
					FullName:  "",
					Position:  "",
					IsOwner:   false,
				},
			},
			expected: participant.ParticipantCollectionV1DTO{
				Items: []participant.ParticipantV1DTO{
					{
						ID:        0,
						ProductID: 0,
						UserID:    0,
						Email:     "",
						FullName:  "",
						Position:  "",
						IsOwner:   false,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToParticipantCollectionV1DTOFromParticipantsEntity(tt.entities)

			assert.Equal(t, tt.expected, result)

			// Additional checks
			assert.Equal(t, len(tt.expected.Items), len(result.Items))

			if len(tt.entities) == 0 {
				assert.Empty(t, result.Items, "Items should be empty for empty input")
			} else {
				assert.Len(t, result.Items, len(tt.entities), "Items length should match input length")

				// Verify each item mapping
				for i, expectedItem := range tt.expected.Items {
					actualItem := result.Items[i]
					assert.Equal(t, expectedItem.ID, actualItem.ID, "ID should match")
					assert.Equal(t, expectedItem.ProductID, actualItem.ProductID, "ProductID should match")
					assert.Equal(t, expectedItem.UserID, actualItem.UserID, "UserID should match")
					assert.Equal(t, expectedItem.Email, actualItem.Email, "Email should match")
					assert.Equal(t, expectedItem.FullName, actualItem.FullName, "FullName should match")
					assert.Equal(t, expectedItem.Position, actualItem.Position, "Position should match")
					assert.Equal(t, expectedItem.IsOwner, actualItem.IsOwner, "IsOwner should match")
					assert.Equal(t, expectedItem.GroupIDs, actualItem.GroupIDs, "GroupIDs should match")
					assert.Equal(t, expectedItem.RoleIDs, actualItem.RoleIDs, "RoleIDs should match")
					assert.Equal(t, expectedItem.LastLoginAt, actualItem.LastLoginAt, "LastLoginAt should match")
					assert.Equal(t, expectedItem.CreatedAt, actualItem.CreatedAt, "CreatedAt should match")
					assert.Equal(t, expectedItem.UpdatedAt, actualItem.UpdatedAt, "UpdatedAt should match")
				}
			}
		})
	}
}
