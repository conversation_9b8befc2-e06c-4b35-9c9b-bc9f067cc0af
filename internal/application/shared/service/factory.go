package service

import (
	categoryservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	identityproviderservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/service"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	permissionservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	proposalservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	statusservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/service"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/identityprovider"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/cache"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/primedb"
)

func NewFromDependencies(
	db primedb.Registry,
	caches cache.Registry,
	identityRegistry identityprovider.Registry,
	version string,
) *Service {
	categoryDomainService := categoryservice.NewCategoryDomainService(db.CategoryGroup, db.CategoryPermission, db.Category, db.CategoryRole, db.Group, db.Role, db.User)
	groupDomainService := groupservice.NewGroupDomainService(db.CategoryGroup, db.Category, db.CategoryRole, caches.Group, db.Group, db.GroupRole, db.ParticipantGroup, db.Participant, db.Product, db.Role, db.UserGroup, db.User)
	keycloakDomainService := identityproviderservice.NewKeycloakDomainService(identityRegistry.Identity)
	participantDomainService := participantservice.NewParticipantDomainService(db.ParticipantGroup, db.Participant, db.ParticipantRole, db.Product, db.Role, db.User, identityRegistry.Identity, db.Group)
	permissionDomainService := permissionservice.NewPermissionDomainService(db.CategoryPermission, db.Permission, db.RolePermission, db.UserRole)
	productDomainService := productservice.NewProductDomainService(identityRegistry.Identity, db.Participant, db.Product, db.User, db.Role, db.Group)
	proposalDomainService := proposalservice.NewProposalDomainService(db.Proposal, db.Section, db.ProposalUserView, db.ProposalHistory, db.Product, db.Participant)
	roleDomainService := roleservice.NewRoleDomainService(db.Role, db.CategoryRole, db.GroupRole, db.ParticipantRole, db.Permission, db.RolePermission, db.Category, db.UserRole, db.ParticipantGroup, db.UserGroup, db.Participant, db.Product, db.User)
	statusDomainService := statusservice.NewStatusDomainService(caches.Status, identityRegistry.Identity, db.DBVersion, version)
	userDomainService := userservice.NewUserDomainService(identityRegistry.Identity, db.Product, db.UserGroup, db.User, db.UserRole, db.GroupRole, db.Participant, db.ParticipantRole, db.ParticipantGroup)
	return New(
		categoryDomainService,
		groupDomainService,
		keycloakDomainService,
		participantDomainService,
		permissionDomainService,
		productDomainService,
		proposalDomainService,
		roleDomainService,
		statusDomainService,
		userDomainService,
	)
}
