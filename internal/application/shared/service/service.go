package service

import (
	categoryapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/category/service"
	groupapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/service"
	keycloakapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/keycloak/service"
	participantapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/participant/service"
	permissionapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/permission/service"
	productapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/product/service"
	proposalapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/proposal/service"
	roleapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/service"
	statusapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/status/service"
	userapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/service"
	categoryservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	identityproviderservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/service"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	permissionservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	proposalservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	statusservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/service"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type Service struct {
	Category    *categoryapp.CategoryAppService
	Group       *groupapp.GroupAppService
	Keycloak    *keycloakapp.KeycloakAppService
	Participant *participantapp.ParticipantAppService
	Permission  *permissionapp.PermissionAppService
	Product     *productapp.ProductAppService
	Proposal    *proposalapp.ProposalAppService
	Role        *roleapp.RoleAppService
	Status      *statusapp.StatusAppService
	User        *userapp.UserAppService
}

func New(
	categoryDomainService categoryservice.CategoryDomainService,
	groupDomainService groupservice.GroupDomainService,
	keycloakDomainService identityproviderservice.KeycloakDomainService,
	participantDomainService participantservice.ParticipantDomainService,
	permissionDomainService permissionservice.PermissionDomainService,
	productDomainService productservice.ProductDomainService,
	proposalDomainService proposalservice.ProposalDomainService,
	roleDomainService roleservice.RoleDomainService,
	statusDomainService statusservice.StatusDomainService,
	userDomainService userservice.UserDomainService,
) *Service {
	return &Service{
		Category:    categoryapp.NewCategoryAppService(categoryDomainService, permissionDomainService),
		Group:       groupapp.NewGroupAppService(categoryDomainService, groupDomainService, productDomainService, userDomainService, roleDomainService),
		Keycloak:    keycloakapp.NewKeycloakAppService(keycloakDomainService),
		Participant: participantapp.NewParticipantAppService(groupDomainService, keycloakDomainService, participantDomainService, productDomainService, roleDomainService, userDomainService),
		Permission:  permissionapp.NewPermissionAppService(categoryDomainService, groupDomainService, participantDomainService, permissionDomainService, roleDomainService, userDomainService),
		Product:     productapp.NewProductAppService(productDomainService, participantDomainService, userDomainService, roleDomainService, groupDomainService),
		Proposal:    proposalapp.NewProposalAppService(proposalDomainService),
		Role:        roleapp.NewRoleAppService(participantDomainService, productDomainService, roleDomainService, groupDomainService, userDomainService, categoryDomainService, permissionDomainService),
		Status:      statusapp.NewStatusAppService(statusDomainService),
		User:        userapp.NewUserAppService(groupDomainService, participantDomainService, productDomainService, roleDomainService, userDomainService),
	}
}
