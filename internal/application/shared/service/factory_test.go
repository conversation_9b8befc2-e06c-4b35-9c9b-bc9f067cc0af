package service

import (
	"testing"

	"github.com/stretchr/testify/assert"

	categoryservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	identityproviderservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/service"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	permissionservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	proposalservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/service"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	statusservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/service"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/identityprovider"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/cache"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/primedb"
)

func TestNewFromDependencies_Success(t *testing.T) {
	srv := NewFromDependencies(
		primedb.Registry{},
		cache.Registry{},
		identityprovider.Registry{},
		"v1.0.0",
	)

	assert.NotNil(t, srv, "Service instance should not be nil")

	assert.NotNil(t, srv.Category, "Category service not initialized")
	assert.NotNil(t, srv.Group, "Group service not initialized")
	assert.NotNil(t, srv.Keycloak, "Keycloak service not initialized")
	assert.NotNil(t, srv.Participant, "Participant service not initialized")
	assert.NotNil(t, srv.Permission, "Permission service not initialized")
	assert.NotNil(t, srv.Product, "Product service not initialized")
	assert.NotNil(t, srv.Proposal, "Proposal service not initialized")
	assert.NotNil(t, srv.Role, "Role service not initialized")
	assert.NotNil(t, srv.Status, "Status service not initialized")
	assert.NotNil(t, srv.User, "User service not initialized")
}

func TestNew_Success(t *testing.T) {
	// Create mock domain services (using nil for simplicity in this test)
	var (
		categoryDomainService    categoryservice.CategoryDomainService
		groupDomainService       groupservice.GroupDomainService
		keycloakDomainService    identityproviderservice.KeycloakDomainService
		participantDomainService participantservice.ParticipantDomainService
		permissionDomainService  permissionservice.PermissionDomainService
		productDomainService     productservice.ProductDomainService
		proposalDomainService    proposalservice.ProposalDomainService
		roleDomainService        roleservice.RoleDomainService
		statusDomainService      statusservice.StatusDomainService
		userDomainService        userservice.UserDomainService
	)

	srv := New(
		categoryDomainService,
		groupDomainService,
		keycloakDomainService,
		participantDomainService,
		permissionDomainService,
		productDomainService,
		proposalDomainService,
		roleDomainService,
		statusDomainService,
		userDomainService,
	)

	assert.NotNil(t, srv, "Service instance should not be nil")

	assert.NotNil(t, srv.Category, "Category service not initialized")
	assert.NotNil(t, srv.Group, "Group service not initialized")
	assert.NotNil(t, srv.Keycloak, "Keycloak service not initialized")
	assert.NotNil(t, srv.Participant, "Participant service not initialized")
	assert.NotNil(t, srv.Permission, "Permission service not initialized")
	assert.NotNil(t, srv.Product, "Product service not initialized")
	assert.NotNil(t, srv.Proposal, "Proposal service not initialized")
	assert.NotNil(t, srv.Role, "Role service not initialized")
	assert.NotNil(t, srv.Status, "Status service not initialized")
	assert.NotNil(t, srv.User, "User service not initialized")
}
