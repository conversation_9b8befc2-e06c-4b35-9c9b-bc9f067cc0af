package mapper

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/keycloak"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
)

func TestToV1DTOsKeycloakUsersFromEntities(t *testing.T) {
	tests := []struct {
		name     string
		input    []entity.KeycloakUser
		expected []keycloak.KeycloakUserV1DTO
	}{
		{
			name: "successful conversion with full user data",
			input: []entity.KeycloakUser{
				{
					ID:        "user1",
					Email:     "<EMAIL>",
					Username:  "username1",
					FullName:  "<PERSON>",
					LastName:  "Doe",
					FirstName: "John",
					Position:  "Developer",
					Enabled:   true,
					Photo:     "photo1.jpg",
				},
				{
					ID:        "user2",
					Email:     "<EMAIL>",
					Username:  "username2",
					FullName:  "<PERSON>",
					LastName:  "<PERSON>",
					FirstName: "<PERSON>",
					Position:  "Manager",
					Enabled:   false,
					Photo:     "photo2.jpg",
				},
			},
			expected: []keycloak.KeycloakUserV1DTO{
				{
					ID:        "user1",
					Email:     "<EMAIL>",
					Username:  "username1",
					FullName:  "John Doe",
					LastName:  "Doe",
					FirstName: "John",
					Position:  "Developer",
					Enabled:   true,
					Photo:     "photo1.jpg",
				},
				{
					ID:        "user2",
					Email:     "<EMAIL>",
					Username:  "username2",
					FullName:  "Jane Smith",
					LastName:  "Smith",
					FirstName: "Jane",
					Position:  "Manager",
					Enabled:   false,
					Photo:     "photo2.jpg",
				},
			},
		},
		{
			name: "successful conversion with minimal user data",
			input: []entity.KeycloakUser{
				{
					ID:       "user3",
					Email:    "<EMAIL>",
					Username: "username3",
					Enabled:  true,
				},
			},
			expected: []keycloak.KeycloakUserV1DTO{
				{
					ID:       "user3",
					Email:    "<EMAIL>",
					Username: "username3",
					Enabled:  true,
				},
			},
		},
		{
			name:     "empty slice input",
			input:    []entity.KeycloakUser{},
			expected: nil,
		},
		{
			name:     "nil slice input",
			input:    nil,
			expected: nil,
		},
		{
			name: "users with empty string fields",
			input: []entity.KeycloakUser{
				{
					ID:        "",
					Email:     "",
					Username:  "",
					FullName:  "",
					LastName:  "",
					FirstName: "",
					Position:  "",
					Enabled:   false,
					Photo:     "",
				},
			},
			expected: []keycloak.KeycloakUserV1DTO{
				{
					ID:        "",
					Email:     "",
					Username:  "",
					FullName:  "",
					LastName:  "",
					FirstName: "",
					Position:  "",
					Enabled:   false,
					Photo:     "",
				},
			},
		},
		{
			name: "single user with all fields populated",
			input: []entity.KeycloakUser{
				{
					ID:        "admin-user",
					Email:     "<EMAIL>",
					Username:  "admin",
					FullName:  "System Administrator",
					LastName:  "Administrator",
					FirstName: "System",
					Position:  "System Admin",
					Enabled:   true,
					Photo:     "admin-photo.png",
				},
			},
			expected: []keycloak.KeycloakUserV1DTO{
				{
					ID:        "admin-user",
					Email:     "<EMAIL>",
					Username:  "admin",
					FullName:  "System Administrator",
					LastName:  "Administrator",
					FirstName: "System",
					Position:  "System Admin",
					Enabled:   true,
					Photo:     "admin-photo.png",
				},
			},
		},
		{
			name: "users with special characters in fields",
			input: []entity.KeycloakUser{
				{
					ID:        "user-123",
					Email:     "<EMAIL>",
					Username:  "test.user_123",
					FullName:  "José María García-López",
					LastName:  "García-López",
					FirstName: "José María",
					Position:  "Senior Software Engineer & Architect",
					Enabled:   true,
					Photo:     "user-123.jpg",
				},
			},
			expected: []keycloak.KeycloakUserV1DTO{
				{
					ID:        "user-123",
					Email:     "<EMAIL>",
					Username:  "test.user_123",
					FullName:  "José María García-López",
					LastName:  "García-López",
					FirstName: "José María",
					Position:  "Senior Software Engineer & Architect",
					Enabled:   true,
					Photo:     "user-123.jpg",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToV1DTOsKeycloakUsersFromEntities(tt.input)
			assert.Equal(t, tt.expected, result)

			// Additional checks for edge cases
			if len(tt.input) == 0 {
				assert.Nil(t, result, "Result should be nil for empty input")
			} else {
				assert.Len(t, result, len(tt.input), "Result length should match input length")

				// Verify field-by-field mapping for non-empty results
				for i, expectedUser := range tt.expected {
					actualUser := result[i]
					assert.Equal(t, expectedUser.ID, actualUser.ID, "ID should match")
					assert.Equal(t, expectedUser.Email, actualUser.Email, "Email should match")
					assert.Equal(t, expectedUser.Username, actualUser.Username, "Username should match")
					assert.Equal(t, expectedUser.FullName, actualUser.FullName, "FullName should match")
					assert.Equal(t, expectedUser.LastName, actualUser.LastName, "LastName should match")
					assert.Equal(t, expectedUser.FirstName, actualUser.FirstName, "FirstName should match")
					assert.Equal(t, expectedUser.Position, actualUser.Position, "Position should match")
					assert.Equal(t, expectedUser.Enabled, actualUser.Enabled, "Enabled should match")
					assert.Equal(t, expectedUser.Photo, actualUser.Photo, "Photo should match")
				}
			}
		})
	}
}
