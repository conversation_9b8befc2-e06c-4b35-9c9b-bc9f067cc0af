package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/keycloak"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
)

func ToV1DTOsKeycloakUsersFromEntities(us []entity.KeycloakUser) []keycloak.KeycloakUserV1DTO {

	var users []keycloak.KeycloakUserV1DTO

	for _, u := range us {
		users = append(users, keycloak.KeycloakUserV1DTO{
			ID:        u.ID,
			Email:     u.Email,
			Username:  u.Username,
			FullName:  u.FullName,
			LastName:  u.LastName,
			FirstName: u.FirstName,
			Position:  u.Position,
			Enabled:   u.Enabled,
			Photo:     u.Photo,
		})
	}

	return users
}
