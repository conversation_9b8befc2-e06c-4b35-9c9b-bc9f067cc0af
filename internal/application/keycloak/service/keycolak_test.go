package service

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/mocks"
)

func TestNewKeycloakAppService(t *testing.T) {
	mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
	service := NewKeycloakAppService(mockDomainService)
	require.NotNil(t, service)
	assert.Equal(t, mockDomainService, service.keycloakDomain)
}

func TestKeycloakAppService_SearchUsers(t *testing.T) {
	tests := []struct {
		name          string
		search        string
		mockUsers     []entity.KeycloakUser
		mockError     error
		expectedUsers []entity.KeycloakUser
		expectedError error
	}{
		{
			name:   "успешный поиск пользователей",
			search: "test",
			mockUsers: []entity.KeycloakUser{
				{ID: "1", Username: "test1"},
				{ID: "2", Username: "test2"},
			},
			mockError: nil,
			expectedUsers: []entity.KeycloakUser{
				{ID: "1", Username: "test1"},
				{ID: "2", Username: "test2"},
			},
			expectedError: nil,
		},
		{
			name:          "пустой поисковый запрос",
			search:        "",
			mockUsers:     []entity.KeycloakUser{},
			mockError:     nil,
			expectedUsers: []entity.KeycloakUser{},
			expectedError: nil,
		},
		{
			name:          "ошибка при поиске",
			search:        "test",
			mockUsers:     nil,
			mockError:     errors.New("search error"),
			expectedUsers: nil,
			expectedError: errors.New("search error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
			service := NewKeycloakAppService(mockDomainService)

			mockDomainService.GetUsersBySearchMock.Expect(tt.search).Return(tt.mockUsers, tt.mockError)

			users, err := service.GetUsersBySearch(tt.search)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedUsers, users)
			}

			require.True(t, mockDomainService.MinimockGetUsersBySearchDone())
		})
	}
}

func TestKeycloakAppService_GetUsersByEmail(t *testing.T) {
	tests := []struct {
		name          string
		email         string
		mockUsers     []entity.KeycloakUser
		mockError     error
		expectedUsers []entity.KeycloakUser
		expectedError error
	}{
		{
			name:  "успешный поиск по email",
			email: "<EMAIL>",
			mockUsers: []entity.KeycloakUser{
				{ID: "1", Email: "<EMAIL>"},
			},
			mockError: nil,
			expectedUsers: []entity.KeycloakUser{
				{ID: "1", Email: "<EMAIL>"},
			},
			expectedError: nil,
		},
		{
			name:          "пустой email",
			email:         "",
			mockUsers:     []entity.KeycloakUser{},
			mockError:     nil,
			expectedUsers: []entity.KeycloakUser{},
			expectedError: nil,
		},
		{
			name:          "ошибка при поиске по email",
			email:         "<EMAIL>",
			mockUsers:     nil,
			mockError:     errors.New("email search error"),
			expectedUsers: nil,
			expectedError: errors.New("email search error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
			service := NewKeycloakAppService(mockDomainService)

			mockDomainService.GetUsersByEmailMock.Expect(tt.email).Return(tt.mockUsers, tt.mockError)

			users, err := service.GetUsersByEmail(tt.email)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedUsers, users)
			}

			require.True(t, mockDomainService.MinimockGetUsersByEmailDone())
		})
	}
}

func TestKeycloakAppService_GetVersion(t *testing.T) {
	tests := []struct {
		name            string
		mockVersion     string
		mockError       error
		expectedVersion string
		expectedError   error
	}{
		{
			name:            "успешное получение версии",
			mockVersion:     "1.0.0",
			mockError:       nil,
			expectedVersion: "1.0.0",
			expectedError:   nil,
		},
		{
			name:            "ошибка при получении версии",
			mockVersion:     "",
			mockError:       errors.New("version error"),
			expectedVersion: "",
			expectedError:   errors.New("version error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
			service := NewKeycloakAppService(mockDomainService)

			mockDomainService.GetVersionMock.Expect().Return(tt.mockVersion, tt.mockError)

			version, err := service.GetVersion()

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedVersion, version)
			}

			require.True(t, mockDomainService.MinimockGetVersionDone())
		})
	}
}

func TestKeycloakAppService_GetURL(t *testing.T) {
	mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
	service := NewKeycloakAppService(mockDomainService)

	expectedURL := "http://keycloak.example.com"
	mockDomainService.GetURLMock.Expect().Return(expectedURL)

	url := service.GetURL()
	assert.Equal(t, expectedURL, url)
	require.True(t, mockDomainService.MinimockGetURLDone())
}

func TestKeycloakAppService_GetClientID(t *testing.T) {
	mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
	service := NewKeycloakAppService(mockDomainService)

	expectedClientID := "test-client"
	mockDomainService.GetClientIDMock.Expect().Return(expectedClientID)

	clientID := service.GetClientID()
	assert.Equal(t, expectedClientID, clientID)
	require.True(t, mockDomainService.MinimockGetClientIDDone())
}

func TestKeycloakAppService_GetRealm(t *testing.T) {
	mockDomainService := mocks.NewKeycloakDomainServiceMock(t)
	service := NewKeycloakAppService(mockDomainService)

	expectedRealm := "test-realm"
	mockDomainService.GetRealmMock.Expect().Return(expectedRealm)

	realm := service.GetRealm()
	assert.Equal(t, expectedRealm, realm)
	require.True(t, mockDomainService.MinimockGetRealmDone())
}
