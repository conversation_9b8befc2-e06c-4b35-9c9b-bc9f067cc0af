package service

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/service"
)

type KeycloakAppService struct {
	keycloakDomain service.KeycloakDomainService
}

func NewKeycloakAppService(
	keycloakDomain service.KeycloakDomainService,
) *KeycloakAppService {
	return &KeycloakAppService{
		keycloakDomain: keycloakDomain,
	}
}

func (s *KeycloakAppService) GetClientID() string {
	return s.keycloakDomain.GetClientID()
}

func (s *KeycloakAppService) GetRealm() string {
	return s.keycloakDomain.GetRealm()
}

func (s *KeycloakAppService) GetURL() string {
	return s.keycloakDomain.GetURL()
}

func (s *KeycloakAppService) GetUsersByEmail(email string) ([]entity.KeycloakUser, error) {
	return s.keycloakDomain.GetUsersByEmail(email)
}

func (s *KeycloakAppService) GetUsersBySearch(search string) ([]entity.KeycloakUser, error) {
	return s.keycloakDomain.GetUsersBySearch(search)
}

func (s *KeycloakAppService) GetVersion() (string, error) {
	return s.keycloakDomain.GetVersion()
}
