package service_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/status/service"
	statusentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/entity"
	statusmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/mocks" // Предполагаемый путь к мокам
	statusservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/service"
)

func TestNewStatusAppService(t *testing.T) {
	mockStatusDomain := statusmocks.NewStatusDomainServiceMock(t) // Используем генератор моков

	svc := service.NewStatusAppService(mockStatusDomain)
	require.NotNil(t, svc)
	// Используем созданный метод StatusDomain() для доступа к зависимости
	accessor, ok := svc.StatusDomain().(statusservice.StatusDomainService)
	require.True(t, ok, "StatusDomain() should return the correct type")
	require.Equal(t, mockStatusDomain, accessor)
}

func TestStatusAppService_Get(t *testing.T) {
	mockStatusDomain := statusmocks.NewStatusDomainServiceMock(t)
	expectedStatus := statusentity.Status{
		PMS: statusentity.PMS{
			Status:  "OK",
			Version: "v1.0.0-test",
		},
		Services: []statusentity.Service{
			{
				Name:    "ServiceA",
				Version: "v0.1.0",
				Status:  "Running",
				Type:    "External",
			},
		},
	}

	// Настраиваем мок
	mockStatusDomain.GetMock.Expect().Return(expectedStatus)

	svc := service.NewStatusAppService(mockStatusDomain)
	actualStatus := svc.Get()

	require.Equal(t, expectedStatus, actualStatus)
}

func TestStatusAppService_Get_Empty(t *testing.T) {
	mockStatusDomain := statusmocks.NewStatusDomainServiceMock(t)
	// Ожидаем пустую структуру Status, поля которой будут иметь значения по умолчанию для своих типов
	expectedStatus := statusentity.Status{}

	// Настраиваем мок
	mockStatusDomain.GetMock.Expect().Return(expectedStatus)

	svc := service.NewStatusAppService(mockStatusDomain)
	actualStatus := svc.Get()

	require.Equal(t, expectedStatus, actualStatus)
	// Проверяем, что внутренние структуры также пусты или имеют значения по умолчанию
	require.Empty(t, actualStatus.PMS.Status)
	require.Empty(t, actualStatus.PMS.Version)
	require.Len(t, actualStatus.Services, 0)
}

// Убедитесь, что пакет statusmocks существует и содержит NewStatusDomainServiceMock,
// а также что StatusDomainServiceMock имеет поле GetMock с методом Expect.
