package service

import (
	statusentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/entity"
	statusservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/service"
)

type StatusAppService struct {
	statusDomain statusservice.StatusDomainService
}

func NewStatusAppService(
	statusDomain statusservice.StatusDomainService,
) *StatusAppService {
	return &StatusAppService{
		statusDomain: statusDomain,
	}
}

func (s *StatusAppService) Get() statusentity.Status {
	return s.statusDomain.Get()
}

func (s *StatusAppService) StatusDomain() statusservice.StatusDomainService {
	return s.statusDomain
}
