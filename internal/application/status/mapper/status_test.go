package mapper

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/status"
	statusentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/entity"
)

func TestToV1DTOStatusFromModel_Success(t *testing.T) {
	model := statusentity.Status{
		PMS: statusentity.PMS{
			Status:  "OK",
			Version: "v1.2.3",
		},
		Services: []statusentity.Service{
			{
				Name:    "AuthService",
				Status:  "Running",
				Version: "v1.0.0",
				Type:    "Internal",
			},
			{
				Name:    "PaymentService",
				Status:  "Degraded",
				Version: "v0.9.1",
				Type:    "External",
			},
		},
	}

	actualDTO := ToV1DTOStatusFromModel(model)

	// Проверяем PMS
	require.Equal(t, model.PMS.Status, actualDTO.Pms.Status)
	require.Equal(t, model.PMS.Version, actualDTO.Pms.Version)

	// Проверяем Services
	require.Len(t, actualDTO.Services, len(model.Services))
	for i, modelService := range model.Services {
		dtoService := actualDTO.Services[i]
		require.Equal(t, modelService.Name, dtoService.Name)
		require.Equal(t, modelService.Status, dtoService.Status)
		require.Equal(t, modelService.Version, dtoService.Version)
		require.Equal(t, modelService.Type, dtoService.Type)
	}
}

func TestToV1DTOStatusFromModel_EmptyInput(t *testing.T) {
	model := statusentity.Status{}

	// Ожидаем, что все поля DTO будут иметь нулевые значения для своих типов
	expectedDTO := status.StatusV1DTO{
		Pms: struct {
			Status  string "json:\"status\""
			Version string "json:\"version\""
		}{},
		Services: nil, // Пустой срез в модели отобразится в nil или пустой срез в DTO, в зависимости от реализации маппера
		// В текущей реализации маппера это будет nil, так как срез инициализируется как var services []struct{...}
	}

	actualDTO := ToV1DTOStatusFromModel(model)

	require.Equal(t, expectedDTO, actualDTO)
	require.Empty(t, actualDTO.Pms.Status)
	require.Empty(t, actualDTO.Pms.Version)
	require.Nil(t, actualDTO.Services) // или require.Empty(t, actualDTO.Services) если маппер создает пустой срез
}

func TestToV1DTOStatusFromModel_PartialInput_NoServices(t *testing.T) {
	model := statusentity.Status{
		PMS: statusentity.PMS{
			Status:  "Warning",
			Version: "v1.2.4",
		},
		Services: nil, // или []statusentity.Service{}
	}

	expectedDTO := status.StatusV1DTO{
		Pms: struct {
			Status  string "json:\"status\""
			Version string "json:\"version\""
		}{
			Status:  "Warning",
			Version: "v1.2.4",
		},
		Services: nil, // Аналогично предыдущему тесту
	}

	actualDTO := ToV1DTOStatusFromModel(model)

	require.Equal(t, expectedDTO, actualDTO)
	require.Equal(t, "Warning", actualDTO.Pms.Status)
	require.Equal(t, "v1.2.4", actualDTO.Pms.Version)
	require.Nil(t, actualDTO.Services) // или require.Empty(t, actualDTO.Services)
}
