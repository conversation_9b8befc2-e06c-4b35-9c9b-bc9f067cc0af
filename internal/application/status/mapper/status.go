package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/status"
	statusentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/entity"
)

func ToV1DTOStatusFromModel(s statusentity.Status) status.StatusV1DTO {

	pms := struct {
		Status  string `json:"status"`
		Version string `json:"version"`
	}{
		Status:  s.PMS.Status,
		Version: s.PMS.Version,
	}

	var services []struct {
		Type    string `json:"type"`
		Name    string `json:"name"`
		Version string `json:"version"`
		Status  string `json:"status"`
	}

	for _, svc := range s.Services {
		services = append(services, struct {
			Type    string `json:"type"`
			Name    string `json:"name"`
			Version string `json:"version"`
			Status  string `json:"status"`
		}{
			Name:    svc.Name,
			Status:  svc.Status,
			Version: svc.Version,
			Type:    svc.Type,
		})
	}

	return status.StatusV1DTO{
		Pms:      pms,
		Services: services,
	}
}
