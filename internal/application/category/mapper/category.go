package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/category"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
)

func ToEntityCategoryFromV1DTOCreate(dto category.CategoryCreateV1DTO) entity.Category {
	return entity.Category{
		Name: dto.Name,
	}
}

func ToEntityCategoryFromV1DTOUpdate(id int64, dto category.CategoryUpdateV1DTO) entity.Category {
	return entity.Category{
		ID:   id,
		Name: dto.Name,
	}
}

func ToV1DTOCategoryFromEntity(cat entity.Category) category.CategoryV1DTO {
	return category.CategoryV1DTO{
		ID:   cat.ID,
		Name: cat.Name,
	}
}

func ToV1DTOsCategoriesFromEntity(cats []entity.Category) []category.CategoryV1DTO {
	var DTOs []category.CategoryV1DTO
	for _, cat := range cats {
		DTOs = append(DTOs, ToV1DTOCategoryFromEntity(cat))
	}
	return DTOs
}
