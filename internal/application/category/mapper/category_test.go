package mapper

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/category"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
)

func TestToEntityCategoryFromV1DTOCreate(t *testing.T) {
	dto := category.CategoryCreateV1DTO{
		Name: "Test Category",
	}

	result := ToEntityCategoryFromV1DTOCreate(dto)

	assert.Equal(t, int64(0), result.ID) // ID should be zero for create
	assert.Equal(t, "Test Category", result.Name)
}

func TestToEntityCategoryFromV1DTOUpdate(t *testing.T) {
	id := int64(123)
	dto := category.CategoryUpdateV1DTO{
		Name: "Updated Category",
	}

	result := ToEntityCategoryFromV1DTOUpdate(id, dto)

	assert.Equal(t, id, result.ID)
	assert.Equal(t, "Updated Category", result.Name)
}

func TestToV1DTOCategoryFromEntity(t *testing.T) {
	cat := entity.Category{
		ID:   456,
		Name: "Entity Category",
	}

	result := ToV1DTOCategoryFromEntity(cat)

	assert.Equal(t, int64(456), result.ID)
	assert.Equal(t, "Entity Category", result.Name)
}

func TestToV1DTOsCategoriesFromEntity_Success(t *testing.T) {
	cats := []entity.Category{
		{ID: 1, Name: "Category 1"},
		{ID: 2, Name: "Category 2"},
		{ID: 3, Name: "Category 3"},
	}

	result := ToV1DTOsCategoriesFromEntity(cats)

	require.Len(t, result, 3)

	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Category 1", result[0].Name)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Category 2", result[1].Name)

	assert.Equal(t, int64(3), result[2].ID)
	assert.Equal(t, "Category 3", result[2].Name)
}

func TestToV1DTOsCategoriesFromEntity_Empty(t *testing.T) {
	var cats []entity.Category

	result := ToV1DTOsCategoriesFromEntity(cats)

	assert.Empty(t, result)
}

func TestToV1DTOsCategoriesFromEntity_Nil(t *testing.T) {
	var cats []entity.Category

	result := ToV1DTOsCategoriesFromEntity(cats)

	assert.Empty(t, result)
}
