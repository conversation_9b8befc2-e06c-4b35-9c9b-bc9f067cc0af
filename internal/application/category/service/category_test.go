package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	permissionmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/mocks"
)

func TestNewCategoryAppService(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	svc := NewCategoryAppService(mockCategory, mockPermission)
	require.NotNil(t, svc)
	require.Equal(t, mockCategory, svc.categoryDomain)
	require.Equal(t, mockPermission, svc.permissionDomain)
}

func TestCategoryAppService_Create(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.CreateMock.Expect(entity.Category{Name: "cat1"}).Return(entity.Category{ID: 1, Name: "cat1"}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.Create(entity.Category{Name: "cat1"})
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
	require.Equal(t, "cat1", res.Name)
}

func TestCategoryAppService_Create_Error(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.CreateMock.Expect(entity.Category{Name: "fail"}).Return(entity.Category{}, errors.New("create error"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.Create(entity.Category{Name: "fail"})
	require.Error(t, err)
	require.Equal(t, "create error", err.Error())
}

func TestCategoryAppService_Create_EmptyData(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.CreateMock.Expect(entity.Category{}).Return(entity.Category{ID: 1}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.Create(entity.Category{})
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
}

func TestCategoryAppService_GetAll(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetAllMock.Expect().Return([]entity.Category{{ID: 1, Name: "cat1"}, {ID: 2, Name: "cat2"}}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.GetAll()
	require.NoError(t, err)
	require.Len(t, res, 2)
	require.Equal(t, int64(1), res[0].ID)
	require.Equal(t, int64(2), res[1].ID)
}

func TestCategoryAppService_GetAll_Error(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetAllMock.Expect().Return(nil, errors.New("get all error"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.GetAll()
	require.Error(t, err)
	require.Equal(t, "get all error", err.Error())
}

func TestCategoryAppService_GetAll_EmptyResult(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetAllMock.Expect().Return([]entity.Category{}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.GetAll()
	require.NoError(t, err)
	require.Len(t, res, 0)
}

func TestCategoryAppService_GetByID(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetByIDMock.Expect(int64(1)).Return(entity.Category{ID: 1, Name: "cat1"}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.GetByID(1)
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
	require.Equal(t, "cat1", res.Name)
}

func TestCategoryAppService_GetByID_Error(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetByIDMock.Expect(int64(2)).Return(entity.Category{}, errors.New("not found"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.GetByID(2)
	require.Error(t, err)
	require.Equal(t, "not found", err.Error())
}

func TestCategoryAppService_GetByID_ZeroID(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetByIDMock.Expect(int64(0)).Return(entity.Category{}, errors.New("invalid id"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.GetByID(0)
	require.Error(t, err)
	require.Equal(t, "invalid id", err.Error())
}

func TestCategoryAppService_GetByName(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetByNameMock.Expect("cat1").Return(entity.Category{ID: 1, Name: "cat1"}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.GetByName("cat1")
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
	require.Equal(t, "cat1", res.Name)
}

func TestCategoryAppService_GetByName_Error(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetByNameMock.Expect("none").Return(entity.Category{}, errors.New("not found"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.GetByName("none")
	require.Error(t, err)
	require.Equal(t, "not found", err.Error())
}

func TestCategoryAppService_GetByName_EmptyName(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.GetByNameMock.Expect("").Return(entity.Category{}, errors.New("empty name"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.GetByName("")
	require.Error(t, err)
	require.Equal(t, "empty name", err.Error())
}

func TestCategoryAppService_Update(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.UpdateMock.Expect(entity.Category{ID: 1, Name: "updated"}).Return(entity.Category{ID: 1, Name: "updated"}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.Update(entity.Category{ID: 1, Name: "updated"})
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
	require.Equal(t, "updated", res.Name)
}

func TestCategoryAppService_Update_Error(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.UpdateMock.Expect(entity.Category{ID: 2}).Return(entity.Category{}, errors.New("update error"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.Update(entity.Category{ID: 2})
	require.Error(t, err)
	require.Equal(t, "update error", err.Error())
}

func TestCategoryAppService_Update_ZeroID(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.UpdateMock.Expect(entity.Category{ID: 0}).Return(entity.Category{}, errors.New("invalid id"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	_, err := svc.Update(entity.Category{ID: 0})
	require.Error(t, err)
	require.Equal(t, "invalid id", err.Error())
}

func TestCategoryAppService_Update_EmptyData(t *testing.T) {
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.UpdateMock.Expect(entity.Category{ID: 1}).Return(entity.Category{ID: 1}, nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	res, err := svc.Update(entity.Category{ID: 1})
	require.NoError(t, err)
	require.Equal(t, int64(1), res.ID)
}

func TestCategoryAppService_Delete(t *testing.T) {
	ctx := context.Background()
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.DeleteMock.Expect(ctx, int64(1)).Return(nil)
	svc := NewCategoryAppService(mockCategory, mockPermission)
	err := svc.Delete(ctx, 1)
	require.NoError(t, err)
}

func TestCategoryAppService_Delete_Error(t *testing.T) {
	ctx := context.Background()
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.DeleteMock.Expect(ctx, int64(2)).Return(errors.New("delete error"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	err := svc.Delete(ctx, 2)
	require.Error(t, err)
	require.Equal(t, "delete error", err.Error())
}

func TestCategoryAppService_Delete_ZeroID(t *testing.T) {
	ctx := context.Background()
	mockCategory := categorymocks.NewCategoryDomainServiceMock(t)
	mockPermission := permissionmocks.NewPermissionDomainServiceMock(t)

	mockCategory.DeleteMock.Expect(ctx, int64(0)).Return(errors.New("invalid id"))
	svc := NewCategoryAppService(mockCategory, mockPermission)
	err := svc.Delete(ctx, 0)
	require.Error(t, err)
	require.Equal(t, "invalid id", err.Error())
}
