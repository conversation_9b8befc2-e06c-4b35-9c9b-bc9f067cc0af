package service

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorieservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	permissionservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service"
)

type CategoryAppService struct {
	categoryDomain   categorieservice.CategoryDomainService
	permissionDomain permissionservice.PermissionDomainService
}

func NewCategoryAppService(
	categoryDomain categorieservice.CategoryDomainService,
	permissionDomain permissionservice.PermissionDomainService,
) *CategoryAppService {
	return &CategoryAppService{
		categoryDomain:   categoryDomain,
		permissionDomain: permissionDomain,
	}
}

func (s *CategoryAppService) Create(data entity.Category) (entity.Category, error) {
	return s.categoryDomain.Create(data)
}

func (s *CategoryAppService) GetAll() ([]entity.Category, error) {
	return s.categoryDomain.GetAll()
}

func (s *CategoryAppService) GetByID(id int64) (entity.Category, error) {
	return s.categoryDomain.GetByID(id)
}

func (s *CategoryAppService) GetByName(name string) (entity.Category, error) {
	return s.categoryDomain.GetByName(name)
}

func (s *CategoryAppService) Update(data entity.Category) (entity.Category, error) {
	return s.categoryDomain.Update(data)
}

func (s *CategoryAppService) Delete(ctx context.Context, id int64) error {
	return s.categoryDomain.Delete(ctx, id)
}
