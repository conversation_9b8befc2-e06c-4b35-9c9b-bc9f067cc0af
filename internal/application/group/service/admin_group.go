package service

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/query"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/pagination"
	groupaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/aggregate"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func (s *GroupAppService) CreateAsAdmin(ctx context.Context, data groupentity.Group) (groupaggregate.GroupWithDetails, error) {
	groupCreated, err := s.groupDomain.CreateAsAdmin(ctx, data)
	if err != nil {
		return groupaggregate.GroupWithDetails{}, err
	}

	return s.GetAsAdminByID(groupCreated.ID)
}

func (s *GroupAppService) GetAllAsAdmin(query query.AdminGroup) (sharedentity.PaginatedResult[groupentity.AdminGroup], error) {
	groups, err := s.groupDomain.GetAllAsAdmin()
	if err != nil {
		return sharedentity.PaginatedResult[groupentity.AdminGroup]{}, err
	}

	filteredGroups := filterGroups(groups, query)
	sortedGroups := sortGroups(filteredGroups, query)
	paginatedGroups, limit, offset := pagination.Apply(sortedGroups, query)

	return sharedentity.PaginatedResult[groupentity.AdminGroup]{
		Items:  paginatedGroups,
		Total:  sharedentity.SliceLen(filteredGroups),
		Limit:  limit,
		Offset: offset,
	}, nil
}

func (s *GroupAppService) GetAsAdminByID(groupID int64) (groupaggregate.GroupWithDetails, error) {

	group, err := s.groupDomain.GetByID(groupID)
	if err != nil {
		return groupaggregate.GroupWithDetails{}, err
	}

	var product productentity.Product
	if group.ProductID != nil && !group.IsSystem {
		product, err = s.productDomain.GetByID(*group.ProductID)
		if err != nil {
			return groupaggregate.GroupWithDetails{}, err
		}
	}

	usersWithoutProduct, err := s.userDomain.GetUserGroupsByGroupID(groupID)
	if err != nil {
		return groupaggregate.GroupWithDetails{}, err
	}

	usersWithProduct, err := s.userDomain.GetUsersWithProductsByGroupID(groupID)
	if err != nil {
		return groupaggregate.GroupWithDetails{}, err
	}

	usersAll := make([]userentity.UserWithProduct, 0)
	usersAll = append(usersAll, usersWithProduct...)
	for _, user := range usersWithoutProduct {
		usersAll = append(usersAll, userentity.UserWithProduct{
			ID:       user.ID,
			Email:    user.Email,
			FullName: user.FullName,
		})
	}

	rolesWithProduct, err := s.roleDomain.GetWithProductByGroupID(groupID)
	if err != nil {
		return groupaggregate.GroupWithDetails{}, err
	}

	return groupaggregate.GroupWithDetails{
		Group:   group,
		Product: product.ProductOrNil(),
		Users:   usersAll,
		Roles:   rolesWithProduct,
	}, nil
}

func (s *GroupAppService) UpdateAsAdmin(ctx context.Context, data groupaggregate.AdminGroupUpdate) (groupaggregate.GroupWithDetails, error) {

	updatedGroup, err := s.groupDomain.Update(groupentity.GroupUpdateData{
		ID:        data.ID,
		Name:      data.Name,
		ActiveFlg: data.IsActive,
	})
	if err != nil {
		return groupaggregate.GroupWithDetails{}, err
	}

	if data.Users != nil {
		err = s.groupDomain.UpdateLinksWithUsers(ctx, data.ID, *data.Users)
		if err != nil {
			return groupaggregate.GroupWithDetails{}, err
		}
	}

	if data.Roles != nil {
		err = s.groupDomain.UpdateLinksWithRoles(ctx, data.ID, *data.Roles)
		if err != nil {
			return groupaggregate.GroupWithDetails{}, err
		}
	}

	return s.GetAsAdminByID(updatedGroup.ID)
}

func (s *GroupAppService) DeleteAsAdmin(groupID int64) error {
	return s.groupDomain.DeleteAsAdmin(groupID)
}
