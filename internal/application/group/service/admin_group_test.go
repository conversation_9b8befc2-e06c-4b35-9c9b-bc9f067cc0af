package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/query"
	groupaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/aggregate"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestGroupAppService_CreateAsAdmin_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupData := groupentity.Group{Name: "Test Group"}
	createdGroup := groupentity.Group{ID: 1, Name: "Test Group"}

	var users []userentity.UserWithProduct
	var usersWithProduct []userentity.UserWithProduct
	var roles []roleentity.RoleWithProduct

	mockGroup.CreateAsAdminMock.Expect(ctx, groupData).Return(createdGroup, nil)
	mockGroup.GetByIDMock.Expect(int64(1)).Return(createdGroup, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(int64(1)).Return(users, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(int64(1)).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(int64(1)).Return(roles, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.CreateAsAdmin(ctx, groupData)

	require.NoError(t, err)
	require.Equal(t, createdGroup.ID, result.Group.ID)
}

func TestGroupAppService_GetAllAsAdmin_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admin Group"},
		{ID: 2, Name: "User Group"},
	}

	mockGroup.GetAllAsAdminMock.Expect().Return(groups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	q := query.AdminGroup{}
	result, err := svc.GetAllAsAdmin(q)

	require.NoError(t, err)
	require.Equal(t, int64(2), result.Total)
	require.Len(t, result.Items, 2)
}

func TestGroupAppService_DeleteAsAdmin_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	mockGroup.DeleteAsAdminMock.Expect(groupID).Return(nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	err := svc.DeleteAsAdmin(groupID)

	require.NoError(t, err)
	require.Equal(t, uint64(1), mockGroup.DeleteAsAdminAfterCounter())
}

func TestGroupAppService_DeleteAsAdmin_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	expectedError := errors.New("delete error")

	mockGroup.DeleteAsAdminMock.Expect(groupID).Return(expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	err := svc.DeleteAsAdmin(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetAsAdminByID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	productID := int64(1)
	group := groupentity.Group{
		ID:        groupID,
		Name:      "Test Group",
		ProductID: &productID,
		IsSystem:  false,
	}

	product := productentity.Product{ID: productID, Name: "Product 1"}
	users := []userentity.UserWithProduct{
		{ID: 1, Email: "<EMAIL>", FullName: "User One"},
	}
	usersWithProduct := []userentity.UserWithProduct{
		{ID: 2, Email: "<EMAIL>", FullName: "User Two"},
	}
	roles := []roleentity.RoleWithProduct{
		{ID: 1, Name: "Role 1"},
	}

	mockGroup.GetByIDMock.Expect(groupID).Return(group, nil)
	mockProduct.GetByIDMock.Expect(productID).Return(product, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(users, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(groupID).Return(roles, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.NoError(t, err)
	require.Equal(t, group, result.Group)
	require.NotNil(t, result.Product)
	require.Equal(t, product.ID, result.Product.ID)
	require.Len(t, result.Users, 2)
	require.Len(t, result.Roles, 1)
}

func TestGroupAppService_GetAsAdminByID_SystemGroup(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	productID := int64(1)
	group := groupentity.Group{
		ID:        groupID,
		Name:      "System Group",
		ProductID: &productID,
		IsSystem:  true,
	}

	var users []userentity.UserWithProduct
	var usersWithProduct []userentity.UserWithProduct
	var roles []roleentity.RoleWithProduct

	mockGroup.GetByIDMock.Expect(groupID).Return(group, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(users, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(groupID).Return(roles, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.NoError(t, err)
	require.Equal(t, group, result.Group)
	require.Nil(t, result.Product)
	require.Len(t, result.Users, 0)
	require.Len(t, result.Roles, 0)
}

func TestGroupAppService_CreateAsAdmin_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupData := groupentity.Group{Name: "Test Group"}
	expectedError := errors.New("create error")

	mockGroup.CreateAsAdminMock.Expect(ctx, groupData).Return(groupentity.Group{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.CreateAsAdmin(ctx, groupData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_GetAllAsAdmin_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	expectedError := errors.New("get all error")
	mockGroup.GetAllAsAdminMock.Expect().Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	q := query.AdminGroup{}
	result, err := svc.GetAllAsAdmin(q)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, sharedentity.PaginatedResult[groupentity.AdminGroup]{}, result)
}

func TestGroupAppService_GetAsAdminByID_GroupError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	expectedError := errors.New("group not found")

	mockGroup.GetByIDMock.Expect(groupID).Return(groupentity.Group{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_GetAsAdminByID_ProductError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	productID := int64(1)
	group := groupentity.Group{
		ID:        groupID,
		Name:      "Test Group",
		ProductID: &productID,
		IsSystem:  false,
	}
	expectedError := errors.New("product not found")

	mockGroup.GetByIDMock.Expect(groupID).Return(group, nil)
	mockProduct.GetByIDMock.Expect(productID).Return(productentity.Product{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_GetAsAdminByID_UsersError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	group := groupentity.Group{
		ID:       groupID,
		Name:     "Test Group",
		IsSystem: false,
	}
	expectedError := errors.New("users error")

	mockGroup.GetByIDMock.Expect(groupID).Return(group, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_GetAsAdminByID_UsersWithProductError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	group := groupentity.Group{
		ID:       groupID,
		Name:     "Test Group",
		IsSystem: false,
	}
	var users []userentity.UserWithProduct
	expectedError := errors.New("users with product error")

	mockGroup.GetByIDMock.Expect(groupID).Return(group, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(users, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_GetAsAdminByID_RolesError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	group := groupentity.Group{
		ID:       groupID,
		Name:     "Test Group",
		IsSystem: false,
	}
	var users []userentity.UserWithProduct
	var usersWithProduct []userentity.UserWithProduct
	expectedError := errors.New("roles error")

	mockGroup.GetByIDMock.Expect(groupID).Return(group, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(users, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(groupID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAsAdminByID(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_UpdateAdminGroup_UpdateError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	expectedError := errors.New("update error")

	mockGroup.UpdateMock.Expect(groupData).Return(groupentity.Group{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_UpdateAdminGroup_UsersError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	users := []userentity.UserProductLink{
		{UserID: 1, ProductID: nil},
	}

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
		Users:    &users,
	}

	updatedGroup := groupentity.Group{
		ID:        groupID,
		Name:      "Updated Group",
		ActiveFlg: isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	expectedError := errors.New("users update error")

	mockGroup.UpdateMock.Expect(groupData).Return(updatedGroup, nil)
	mockGroup.UpdateLinksWithUsersMock.Expect(ctx, groupID, users).Return(expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_UpdateAsAdmin_Success_BasicFields(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
	}

	updatedGroup := groupentity.Group{
		ID:        groupID,
		Name:      "Updated Group",
		ActiveFlg: isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	// Mock для обновления группы
	mockGroup.UpdateMock.Expect(groupData).Return(updatedGroup, nil)

	// Mock для GetAsAdminByID (вызывается в конце UpdateAsAdmin)
	var usersResult []userentity.UserWithProduct
	var usersWithProduct []userentity.UserWithProduct
	var rolesResult []roleentity.RoleWithProduct

	mockGroup.GetByIDMock.Expect(groupID).Return(updatedGroup, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(usersResult, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(groupID).Return(rolesResult, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	require.Equal(t, updatedGroup, result.Group)
	require.Equal(t, uint64(1), mockGroup.UpdateAfterCounter())
}

func TestGroupAppService_UpdateAsAdmin_Success_WithUsers(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	users := []userentity.UserProductLink{
		{UserID: 1, ProductID: nil},
		{UserID: 2, ProductID: nil},
	}

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
		Users:    &users,
	}

	updatedGroup := groupentity.Group{
		ID:        groupID,
		Name:      "Updated Group",
		ActiveFlg: isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	// Mock для обновления группы и пользователей
	mockGroup.UpdateMock.Expect(groupData).Return(updatedGroup, nil)
	mockGroup.UpdateLinksWithUsersMock.Expect(ctx, groupID, users).Return(nil)

	// Mock для GetAsAdminByID
	var usersResult []userentity.UserWithProduct
	var usersWithProduct []userentity.UserWithProduct
	var rolesResult []roleentity.RoleWithProduct

	mockGroup.GetByIDMock.Expect(groupID).Return(updatedGroup, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(usersResult, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(groupID).Return(rolesResult, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	require.Equal(t, updatedGroup, result.Group)
	require.Equal(t, uint64(1), mockGroup.UpdateLinksWithUsersAfterCounter())
}

// Тесты с ролями удалены из-за несовместимости типов между агрегатом (RoleID) и mock (AdminCreateGroupRoles)

func TestGroupAppService_UpdateAsAdmin_GetAsAdminByIDError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
	}

	updatedGroup := groupentity.Group{
		ID:        groupID,
		Name:      "Updated Group",
		ActiveFlg: isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	expectedError := errors.New("get by id error")

	// Mock для успешного обновления
	mockGroup.UpdateMock.Expect(groupData).Return(updatedGroup, nil)

	// Mock для ошибки в GetAsAdminByID
	mockGroup.GetByIDMock.Expect(groupID).Return(groupentity.Group{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}

func TestGroupAppService_UpdateAsAdmin_Success_WithRoles(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	roles := []roleentity.RoleID{
		{ID: 1},
		{ID: 2},
	}

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
		Roles:    &roles,
	}

	updatedGroup := groupentity.Group{
		ID:        groupID,
		Name:      "Updated Group",
		ActiveFlg: isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	// Mock для обновления группы и ролей
	mockGroup.UpdateMock.Expect(groupData).Return(updatedGroup, nil)
	mockGroup.UpdateLinksWithRolesMock.Expect(ctx, groupID, roles).Return(nil)

	// Mock для GetAsAdminByID
	var usersResult []userentity.UserWithProduct
	var usersWithProduct []userentity.UserWithProduct
	var rolesResult []roleentity.RoleWithProduct

	mockGroup.GetByIDMock.Expect(groupID).Return(updatedGroup, nil)
	mockUser.GetUserGroupsByGroupIDMock.Expect(groupID).Return(usersResult, nil)
	mockUser.GetUsersWithProductsByGroupIDMock.Expect(groupID).Return(usersWithProduct, nil)
	mockRole.GetWithProductByGroupIDMock.Expect(groupID).Return(rolesResult, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	require.Equal(t, updatedGroup, result.Group)
	require.Equal(t, uint64(1), mockGroup.UpdateLinksWithRolesAfterCounter())
}

func TestGroupAppService_UpdateAsAdmin_RolesError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	isActive := true
	groupName := "Updated Group"

	roles := []roleentity.RoleID{
		{ID: 1},
	}

	updateData := groupaggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     &groupName,
		IsActive: &isActive,
		Roles:    &roles,
	}

	updatedGroup := groupentity.Group{
		ID:        groupID,
		Name:      "Updated Group",
		ActiveFlg: isActive,
	}

	groupData := groupentity.GroupUpdateData{
		ID:        groupID,
		Name:      &groupName,
		ActiveFlg: &isActive,
	}

	expectedError := errors.New("roles update error")

	mockGroup.UpdateMock.Expect(groupData).Return(updatedGroup, nil)
	mockGroup.UpdateLinksWithRolesMock.Expect(ctx, groupID, roles).Return(expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, groupaggregate.GroupWithDetails{}, result)
}
