package service

import (
	"context"

	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"

	categiryservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type GroupAppService struct {
	categoryDomain categiryservice.CategoryDomainService
	groupDomain    groupservice.GroupDomainService
	productDomain  productservice.ProductDomainService
	userDomain     userservice.UserDomainService
	roleDomain     roleservice.RoleDomainService
}

func NewGroupAppService(
	categoryDomain categiryservice.CategoryDomainService,
	groupDomain groupservice.GroupDomainService,
	productDomain productservice.ProductDomainService,
	userDomain userservice.UserDomainService,
	roleDomain roleservice.RoleDomainService,
) *GroupAppService {
	return &GroupAppService{
		categoryDomain: categoryDomain,
		groupDomain:    groupDomain,
		productDomain:  productDomain,
		userDomain:     userDomain,
		roleDomain:     roleDomain,
	}
}

func (s *GroupAppService) Create(group groupentity.Group) (groupentity.GroupWithStats, error) {
	return s.groupDomain.Create(group)
}

func (s *GroupAppService) GetAll() ([]groupentity.Group, error) {
	return s.groupDomain.GetAll()
}

func (s *GroupAppService) GetByGroupIDAndProductID(groupID, productID int64) (groupentity.GroupFull, error) {
	return s.groupDomain.GetByGroupIDAndProductID(groupID, productID)
}

func (s *GroupAppService) GetByID(id int64) (groupentity.Group, error) {
	return s.groupDomain.GetByID(id)
}

func (s *GroupAppService) GetByParticipantID(participantID int64) ([]groupentity.GroupWithStats, error) {
	return s.groupDomain.GetByParticipantID(participantID)
}

func (s *GroupAppService) GetByProductID(productID int64) ([]groupentity.Group, error) {
	return s.groupDomain.GetByProductID(productID)
}

func (s *GroupAppService) GetByUserID(userID int64) ([]groupentity.Group, error) {
	return s.groupDomain.GetByUserID(userID)
}

func (s *GroupAppService) GetGroupsWithCategoryStats() ([]groupentity.GroupWithCategoryStats, error) {
	return s.groupDomain.GetGroupsWithCategoryStats()
}

func (s *GroupAppService) GetSystemGroups(userID int64, categoryID int64) ([]groupentity.GroupWithStats, error) {
	userData, err := s.userDomain.GetByID(userID)
	if err != nil {
		return nil, err
	}

	category, err := s.categoryDomain.GetCategoryFull(categoryID)
	if err != nil {
		return nil, err
	}

	groups, err := s.groupDomain.GetSystemGroupsWithStats()
	if err != nil {
		return nil, err
	}

	if !userData.IsAdmin {
		userGroups, err := s.userDomain.GetUserGroups(userID)
		if err != nil {
			return nil, err
		}
		return s.groupDomain.GetAvailableSystemGroups(category, groups, userGroups)
	}

	return groups, nil
}

func (s *GroupAppService) GetWithProductsByUserFilter(filter userentity.UserFiltersData) ([]groupentity.GroupWithProductCollection, error) {

	userGroups, err := s.groupDomain.GetByUserID(*filter.ID)
	if err != nil {
		return nil, err
	}

	userGroupIDs := make([]int64, len(userGroups))
	for i, group := range userGroups {
		userGroupIDs[i] = group.ID
	}

	participantGroups, err := s.groupDomain.GetParticipantGroupsWithProductByUserID(*filter.ID)
	if err != nil {
		return nil, err
	}

	participantGroupIDs := make([]int64, len(participantGroups))
	for i, group := range participantGroups {
		participantGroupIDs[i] = group.ID
	}

	uniqueGroups := mergeUnique(userGroups, participantGroups)

	groupResponses, err := s.buildGroupResponses(uniqueGroups, userGroupIDs, participantGroupIDs, filter)
	if err != nil {
		return nil, err
	}

	return groupResponses, nil
}

func (s *GroupAppService) GetWithStatsByProductID(productID int64) ([]groupentity.GroupWithStats, error) {
	return s.groupDomain.GetWithStatsByProductID(productID)
}

func (s *GroupAppService) buildGroupResponses(groups []groupentity.Group, userGroupIDs, participantGroupIDs []int64, filter userentity.UserFiltersData) ([]groupentity.GroupWithProductCollection, error) {

	systemGroupSet := sharedentity.ToSet(userGroupIDs)
	participantGroupSet := sharedentity.ToSet(participantGroupIDs)
	productIDSet := make(map[int64]struct{})
	if filter.ProductIDs != nil {
		productIDSet = sharedentity.ToSet(*filter.ProductIDs)
	}

	var groupResponses []groupentity.GroupWithProductCollection

	for _, group := range groups {
		if filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet) {
			continue
		}

		groupResponse := groupentity.GroupWithProductCollection{
			GroupID:   group.ID,
			GroupName: group.Name,
			IsSystem:  group.IsSystem,
		}

		if group.ProductID != nil {
			product, err := s.productDomain.GetByID(*group.ProductID)
			if err != nil {
				return nil, err
			}
			groupResponse.Product = &userentity.UserProduct{
				ID:       product.ID,
				IID:      product.IID,
				TechName: product.TechName,
				Name:     product.Name,
			}
		}

		groupResponses = append(groupResponses, groupResponse)
	}

	return groupResponses, nil
}

func filteredGroup(participantGroupSet map[int64]struct{}, systemGroupSet map[int64]struct{}, group groupentity.Group, productIDSet map[int64]struct{}) bool {

	if len(productIDSet) == 0 {
		return false
	}
	result := true

	_, isParticipant := participantGroupSet[group.ID]
	_, isSystem := systemGroupSet[group.ID]

	if isSystem || isParticipant {
		if group.ProductID != nil {
			if _, match := productIDSet[*group.ProductID]; match {
				result = false
			}
		}
	}

	return result
}

func mergeUnique(userGroups, participantGroups []groupentity.Group) []groupentity.Group {
	uniqueMap := make(map[int64]groupentity.Group)

	for _, group := range userGroups {
		uniqueMap[group.ID] = group
	}

	for _, group := range participantGroups {
		uniqueMap[group.ID] = group
	}

	uniqueGroups := make([]groupentity.Group, 0, len(uniqueMap))
	for _, group := range uniqueMap {
		uniqueGroups = append(uniqueGroups, group)
	}

	return uniqueGroups
}

func (s *GroupAppService) Update(group groupentity.GroupUpdateData) (groupentity.Group, error) {
	return s.groupDomain.Update(group)
}

func (s *GroupAppService) UpdateByGroupFull(ctx context.Context, group groupentity.GroupFull, productID int64) (groupentity.GroupFull, error) {
	return s.groupDomain.UpdateByGroupFull(ctx, group, productID)
}

func (s *GroupAppService) UpdateCategoriesGroups(groupsWithCategories []groupentity.GroupWithCategoryStats) error {
	return s.groupDomain.UpdateCategoryStates(groupsWithCategories)
}

func (s *GroupAppService) UpdateParticipantGroups(ctx context.Context, groupID int64, participantIDs []int64) ([]int64, error) {
	return s.groupDomain.UpdateParticipantGroups(ctx, groupID, participantIDs)
}

func (s *GroupAppService) Delete(productID, groupID int64) error {
	return s.groupDomain.Delete(productID, groupID)
}
