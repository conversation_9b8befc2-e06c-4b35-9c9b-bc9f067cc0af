package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func createMocks(t *testing.T) (*categorymocks.CategoryDomainServiceMock, *groupmocks.GroupDomainServiceMock, *productmocks.ProductDomainServiceMock, *usermocks.UserDomainServiceMock, *rolemocks.RoleDomainServiceMock) {
	return categorymocks.NewCategoryDomainServiceMock(t),
		groupmocks.NewGroupDomainServiceMock(t),
		productmocks.NewProductDomainServiceMock(t),
		usermocks.NewUserDomainServiceMock(t),
		rolemocks.NewRoleDomainServiceMock(t)
}

func TestGroupAppService_Create_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	group := groupentity.Group{Name: "test group"}
	expectedResult := groupentity.GroupWithStats{GroupID: 1, GroupName: "test group"}

	mockGroup.CreateMock.Expect(group).Return(expectedResult, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.Create(group)

	require.NoError(t, err)
	require.Equal(t, expectedResult, result)
	require.Equal(t, uint64(1), mockGroup.CreateAfterCounter())
}

func TestGroupAppService_Create_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	group := groupentity.Group{Name: "test group"}
	expectedError := errors.New("create error")

	mockGroup.CreateMock.Expect(group).Return(groupentity.GroupWithStats{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.Create(group)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetAll_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	expectedGroups := []groupentity.Group{
		{ID: 1, Name: "group1"},
		{ID: 2, Name: "group2"},
	}

	mockGroup.GetAllMock.Expect().Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetAll()

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
	require.Equal(t, uint64(1), mockGroup.GetAllAfterCounter())
}

func TestGroupAppService_GetAll_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	expectedError := errors.New("get all error")
	mockGroup.GetAllMock.Expect().Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetAll()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetByID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	expectedGroup := groupentity.Group{ID: groupID, Name: "test group"}

	mockGroup.GetByIDMock.Expect(groupID).Return(expectedGroup, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetByID(groupID)

	require.NoError(t, err)
	require.Equal(t, expectedGroup, result)
}

func TestGroupAppService_GetByID_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	expectedError := errors.New("not found")

	mockGroup.GetByIDMock.Expect(groupID).Return(groupentity.Group{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetByID(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetByProductID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	productID := int64(1)
	expectedGroups := []groupentity.Group{
		{ID: 1, Name: "group1"},
		{ID: 2, Name: "group2"},
	}

	mockGroup.GetByProductIDMock.Expect(productID).Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetByProductID(productID)

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetByProductID_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	productID := int64(1)
	expectedError := errors.New("get error")

	mockGroup.GetByProductIDMock.Expect(productID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetByProductID(productID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetWithStatsByProductID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	productID := int64(1)
	expectedGroups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
		{GroupID: 2, GroupName: "group2"},
	}

	mockGroup.GetWithStatsByProductIDMock.Expect(productID).Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithStatsByProductID(productID)

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetWithStatsByProductID_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	productID := int64(1)
	expectedError := errors.New("get error")

	mockGroup.GetWithStatsByProductIDMock.Expect(productID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetWithStatsByProductID(productID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetGroupsWithCategoryStats_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	expectedGroups := []groupentity.GroupWithCategoryStats{
		{GroupID: 1, GroupName: "group1"},
		{GroupID: 2, GroupName: "group2"},
	}

	mockGroup.GetGroupsWithCategoryStatsMock.Expect().Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetGroupsWithCategoryStats()

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetGroupsWithCategoryStats_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	expectedError := errors.New("get error")
	mockGroup.GetGroupsWithCategoryStatsMock.Expect().Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetGroupsWithCategoryStats()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetByUserID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	expectedGroups := []groupentity.Group{
		{ID: 1, Name: "group1"},
		{ID: 2, Name: "group2"},
	}

	mockGroup.GetByUserIDMock.Expect(userID).Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetByUserID(userID)

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetByUserID_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	expectedError := errors.New("get error")

	mockGroup.GetByUserIDMock.Expect(userID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetByUserID(userID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetByGroupIDAndProductID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	productID := int64(1)
	expectedGroup := groupentity.GroupFull{ID: groupID, Name: "test group"}

	mockGroup.GetByGroupIDAndProductIDMock.Expect(groupID, productID).Return(expectedGroup, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetByGroupIDAndProductID(groupID, productID)

	require.NoError(t, err)
	require.Equal(t, expectedGroup, result)
}

func TestGroupAppService_GetByGroupIDAndProductID_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupID := int64(1)
	productID := int64(1)
	expectedError := errors.New("get error")

	mockGroup.GetByGroupIDAndProductIDMock.Expect(groupID, productID).Return(groupentity.GroupFull{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetByGroupIDAndProductID(groupID, productID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetByParticipantID_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	participantID := int64(1)
	expectedGroups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
		{GroupID: 2, GroupName: "group2"},
	}

	mockGroup.GetByParticipantIDMock.Expect(participantID).Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetByParticipantID(participantID)

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetByParticipantID_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	participantID := int64(1)
	expectedError := errors.New("get error")

	mockGroup.GetByParticipantIDMock.Expect(participantID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetByParticipantID(participantID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_UpdateCategoriesGroups_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groups := []groupentity.GroupWithCategoryStats{
		{GroupID: 1, GroupName: "group1"},
		{GroupID: 2, GroupName: "group2"},
	}

	mockGroup.UpdateCategoryStatesMock.Expect(groups).Return(nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	err := svc.UpdateCategoriesGroups(groups)

	require.NoError(t, err)
	require.Equal(t, uint64(1), mockGroup.UpdateCategoryStatesAfterCounter())
}

func TestGroupAppService_UpdateCategoriesGroups_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groups := []groupentity.GroupWithCategoryStats{
		{GroupID: 1, GroupName: "group1"},
	}
	expectedError := errors.New("update error")

	mockGroup.UpdateCategoryStatesMock.Expect(groups).Return(expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	err := svc.UpdateCategoriesGroups(groups)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_Update_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupName := "updated group"
	updateData := groupentity.GroupUpdateData{ID: 1, Name: &groupName}
	expectedGroup := groupentity.Group{ID: 1, Name: "updated group"}

	mockGroup.UpdateMock.Expect(updateData).Return(expectedGroup, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.Update(updateData)

	require.NoError(t, err)
	require.Equal(t, expectedGroup, result)
}

func TestGroupAppService_Update_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	groupName := "updated group"
	updateData := groupentity.GroupUpdateData{ID: 1, Name: &groupName}
	expectedError := errors.New("update error")

	mockGroup.UpdateMock.Expect(updateData).Return(groupentity.Group{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.Update(updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_UpdateByGroupFull_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupFull := groupentity.GroupFull{ID: 1, Name: "test group"}
	productID := int64(1)
	expectedResult := groupentity.GroupFull{ID: 1, Name: "updated group"}

	mockGroup.UpdateByGroupFullMock.Expect(ctx, groupFull, productID).Return(expectedResult, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateByGroupFull(ctx, groupFull, productID)

	require.NoError(t, err)
	require.Equal(t, expectedResult, result)
}

func TestGroupAppService_UpdateByGroupFull_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupFull := groupentity.GroupFull{ID: 1, Name: "test group"}
	productID := int64(1)
	expectedError := errors.New("update error")

	mockGroup.UpdateByGroupFullMock.Expect(ctx, groupFull, productID).Return(groupentity.GroupFull{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.UpdateByGroupFull(ctx, groupFull, productID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_UpdateParticipantGroups_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	participantIDs := []int64{1, 2}
	expectedResult := []int64{1, 2}

	mockGroup.UpdateParticipantGroupsMock.Expect(ctx, groupID, participantIDs).Return(expectedResult, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.UpdateParticipantGroups(ctx, groupID, participantIDs)

	require.NoError(t, err)
	require.Equal(t, expectedResult, result)
}

func TestGroupAppService_UpdateParticipantGroups_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	ctx := context.Background()
	groupID := int64(1)
	participantIDs := []int64{1, 2}
	expectedError := errors.New("update error")

	mockGroup.UpdateParticipantGroupsMock.Expect(ctx, groupID, participantIDs).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.UpdateParticipantGroups(ctx, groupID, participantIDs)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetSystemGroups_AdminUser_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	user := userentity.User{ID: userID, IsAdmin: true}
	category := categoryentity.CategoryFull{ID: categoryID}
	expectedGroups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
		{GroupID: 2, GroupName: "group2"},
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockCategory.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	mockGroup.GetSystemGroupsWithStatsMock.Expect().Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetSystemGroups(userID, categoryID)

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetSystemGroups_NonAdminUser_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	user := userentity.User{ID: userID, IsAdmin: false}
	category := categoryentity.CategoryFull{ID: categoryID}
	groups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
		{GroupID: 2, GroupName: "group2"},
	}
	userGroups := []int64{1}
	expectedGroups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
	}

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockCategory.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	mockGroup.GetSystemGroupsWithStatsMock.Expect().Return(groups, nil)
	mockUser.GetUserGroupsMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetAvailableSystemGroupsMock.Expect(category, groups, userGroups).Return(expectedGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetSystemGroups(userID, categoryID)

	require.NoError(t, err)
	require.Equal(t, expectedGroups, result)
}

func TestGroupAppService_GetSystemGroups_UserNotFound(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	expectedError := errors.New("user not found")

	mockUser.GetByIDMock.Expect(userID).Return(userentity.User{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetSystemGroups(userID, categoryID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetSystemGroups_CategoryNotFound(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	user := userentity.User{ID: userID, IsAdmin: true}
	expectedError := errors.New("category not found")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockCategory.GetCategoryFullMock.Expect(categoryID).Return(categoryentity.CategoryFull{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetSystemGroups(userID, categoryID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetSystemGroups_GetSystemGroupsError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	user := userentity.User{ID: userID, IsAdmin: true}
	category := categoryentity.CategoryFull{ID: categoryID}
	expectedError := errors.New("get groups error")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockCategory.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	mockGroup.GetSystemGroupsWithStatsMock.Expect().Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetSystemGroups(userID, categoryID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetSystemGroups_GetUserGroupsError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	user := userentity.User{ID: userID, IsAdmin: false}
	category := categoryentity.CategoryFull{ID: categoryID}
	groups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
	}
	expectedError := errors.New("get user groups error")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockCategory.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	mockGroup.GetSystemGroupsWithStatsMock.Expect().Return(groups, nil)
	mockUser.GetUserGroupsMock.Expect(userID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetSystemGroups(userID, categoryID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetSystemGroups_GetAvailableSystemGroupsError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	categoryID := int64(1)
	user := userentity.User{ID: userID, IsAdmin: false}
	category := categoryentity.CategoryFull{ID: categoryID}
	groups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "group1"},
	}
	userGroups := []int64{1}
	expectedError := errors.New("get available groups error")

	mockUser.GetByIDMock.Expect(userID).Return(user, nil)
	mockCategory.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	mockGroup.GetSystemGroupsWithStatsMock.Expect().Return(groups, nil)
	mockUser.GetUserGroupsMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetAvailableSystemGroupsMock.Expect(category, groups, userGroups).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	_, err := svc.GetSystemGroups(userID, categoryID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_Delete_Success(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	productID := int64(1)
	groupID := int64(1)

	mockGroup.DeleteMock.Expect(productID, groupID).Return(nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	err := svc.Delete(productID, groupID)

	require.NoError(t, err)
	require.Equal(t, uint64(1), mockGroup.DeleteAfterCounter())
}

func TestGroupAppService_Delete_Error(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	productID := int64(1)
	groupID := int64(1)
	expectedError := errors.New("delete error")

	mockGroup.DeleteMock.Expect(productID, groupID).Return(expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	err := svc.Delete(productID, groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupAppService_GetWithProductsByUserFilter_Success_WithProducts(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	productIDs := []int64{1}
	filter := userentity.UserFiltersData{ID: &userID, ProductIDs: &productIDs}
	productID := int64(1)
	userGroups := []groupentity.Group{{ID: 1, Name: "group1", ProductID: &productID, IsSystem: false}}
	var participantGroups []groupentity.Group
	product := productentity.Product{ID: productID, IID: "P001", TechName: "tech", Name: "Product 1"}

	mockGroup.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetParticipantGroupsWithProductByUserIDMock.Expect(userID).Return(participantGroups, nil)
	mockProduct.GetByIDMock.Expect(productID).Return(product, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.NoError(t, err)
	require.Len(t, result, 1)
	require.Equal(t, "group1", result[0].GroupName)
	require.NotNil(t, result[0].Product)
	require.Equal(t, productID, result[0].Product.ID)
}

func TestGroupAppService_GetWithProductsByUserFilter_Success_WithoutProducts(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	filter := userentity.UserFiltersData{ID: &userID}
	userGroups := []groupentity.Group{{ID: 1, Name: "group1", ProductID: nil, IsSystem: true}}
	var participantGroups []groupentity.Group

	mockGroup.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetParticipantGroupsWithProductByUserIDMock.Expect(userID).Return(participantGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.NoError(t, err)
	require.Len(t, result, 1)
	require.Equal(t, "group1", result[0].GroupName)
	require.Nil(t, result[0].Product)
	require.True(t, result[0].IsSystem)
}

func TestMergeUnique(t *testing.T) {
	userGroups := []groupentity.Group{
		{ID: 1, Name: "group1"},
		{ID: 2, Name: "group2"},
	}
	participantGroups := []groupentity.Group{
		{ID: 2, Name: "group2"},
		{ID: 3, Name: "group3"},
	}

	result := mergeUnique(userGroups, participantGroups)

	require.Len(t, result, 3)
}

func TestFilteredGroup_NoProductFilter(t *testing.T) {
	participantGroupSet := map[int64]struct{}{1: {}}
	systemGroupSet := map[int64]struct{}{1: {}}
	group := groupentity.Group{ID: 1}
	productIDSet := map[int64]struct{}{}

	result := filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet)

	require.False(t, result)
}

func TestFilteredGroup_WithProductFilter_Match(t *testing.T) {
	participantGroupSet := map[int64]struct{}{1: {}}
	systemGroupSet := map[int64]struct{}{}
	productID := int64(1)
	group := groupentity.Group{ID: 1, ProductID: &productID}
	productIDSet := map[int64]struct{}{1: {}}

	result := filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet)

	require.False(t, result)
}

func TestFilteredGroup_WithProductFilter_NoMatch(t *testing.T) {
	participantGroupSet := map[int64]struct{}{1: {}}
	systemGroupSet := map[int64]struct{}{}
	productID := int64(1)
	group := groupentity.Group{ID: 1, ProductID: &productID}
	productIDSet := map[int64]struct{}{2: {}}

	result := filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet)

	require.True(t, result)
}

func TestFilteredGroup_NotInUserOrParticipantGroups(t *testing.T) {
	participantGroupSet := map[int64]struct{}{}
	systemGroupSet := map[int64]struct{}{}
	productID := int64(1)
	group := groupentity.Group{ID: 1, ProductID: &productID}
	productIDSet := map[int64]struct{}{2: {}}

	result := filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet)

	require.True(t, result)
}

func TestGroupAppService_GetWithProductsByUserFilter_GetByUserIDError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	filter := userentity.UserFiltersData{ID: &userID}
	expectedError := errors.New("get by user id error")

	mockGroup.GetByUserIDMock.Expect(userID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
}

func TestGroupAppService_GetWithProductsByUserFilter_GetParticipantGroupsError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	filter := userentity.UserFiltersData{ID: &userID}
	userGroups := []groupentity.Group{{ID: 1, Name: "group1"}}
	expectedError := errors.New("get participant groups error")

	mockGroup.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetParticipantGroupsWithProductByUserIDMock.Expect(userID).Return(nil, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
}

func TestGroupAppService_GetWithProductsByUserFilter_ProductError(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	filter := userentity.UserFiltersData{ID: &userID}
	productID := int64(1)
	userGroups := []groupentity.Group{{ID: 1, Name: "group1", ProductID: &productID, IsSystem: false}}
	var participantGroups []groupentity.Group
	expectedError := errors.New("product error")

	mockGroup.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetParticipantGroupsWithProductByUserIDMock.Expect(userID).Return(participantGroups, nil)
	mockProduct.GetByIDMock.Expect(productID).Return(productentity.Product{}, expectedError)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
}

func TestFilteredGroup_SystemGroupWithNilProductID(t *testing.T) {
	participantGroupSet := map[int64]struct{}{}
	systemGroupSet := map[int64]struct{}{1: {}}
	group := groupentity.Group{ID: 1, ProductID: nil}
	productIDSet := map[int64]struct{}{1: {}}

	result := filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet)

	require.True(t, result)
}

func TestFilteredGroup_ParticipantGroupWithNilProductID(t *testing.T) {
	participantGroupSet := map[int64]struct{}{1: {}}
	systemGroupSet := map[int64]struct{}{}
	group := groupentity.Group{ID: 1, ProductID: nil}
	productIDSet := map[int64]struct{}{1: {}}

	result := filteredGroup(participantGroupSet, systemGroupSet, group, productIDSet)

	require.True(t, result)
}

func TestGroupAppService_GetWithProductsByUserFilter_FilteredGroup(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	productIDs := []int64{2}
	filter := userentity.UserFiltersData{ID: &userID, ProductIDs: &productIDs}
	productID := int64(1)
	userGroups := []groupentity.Group{{ID: 1, Name: "group1", ProductID: &productID, IsSystem: false}}
	var participantGroups []groupentity.Group

	mockGroup.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetParticipantGroupsWithProductByUserIDMock.Expect(userID).Return(participantGroups, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.NoError(t, err)
	require.Len(t, result, 0)
}

func TestGroupAppService_GetWithProductsByUserFilter_WithDuplicateGroups(t *testing.T) {
	mockCategory, mockGroup, mockProduct, mockUser, mockRole := createMocks(t)

	userID := int64(1)
	filter := userentity.UserFiltersData{ID: &userID}
	productID := int64(1)
	userGroups := []groupentity.Group{{ID: 1, Name: "group1", ProductID: &productID, IsSystem: false}}
	participantGroups := []groupentity.Group{{ID: 1, Name: "group1", ProductID: &productID, IsSystem: false}}
	product := productentity.Product{ID: productID, IID: "P001", TechName: "tech", Name: "Product 1"}

	mockGroup.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	mockGroup.GetParticipantGroupsWithProductByUserIDMock.Expect(userID).Return(participantGroups, nil)
	mockProduct.GetByIDMock.Expect(productID).Return(product, nil)

	svc := NewGroupAppService(mockCategory, mockGroup, mockProduct, mockUser, mockRole)
	result, err := svc.GetWithProductsByUserFilter(filter)

	require.NoError(t, err)
	require.Len(t, result, 1)
}
