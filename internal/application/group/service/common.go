package service

import (
	"sort"
	"strings"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/query"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func compareGroups(g1, g2 groupentity.AdminGroup, sortField string) bool {
	switch sortField {
	case sharedentity.SortFieldName.String():
		return g1.Name < g2.Name
	case sharedentity.SortFieldType.String():
		return g1.Type < g2.Type
	case sharedentity.SortFieldProduct.String():
		return compareGroupsByProduct(g1, g2)
	case sharedentity.SortFieldUserCount.String():
		return g1.UserCount < g2.UserCount
	case sharedentity.SortFieldRoleCount.String():
		return g1.RoleCount < g2.RoleCount
	default:
		return g1.Name < g2.Name
	}
}

func compareGroupsByProduct(g1, g2 groupentity.AdminGroup) bool {
	hasProducts1 := g1.Product != nil
	hasProducts2 := g2.Product != nil

	// One of the groups does not have products
	if !hasProducts1 && hasProducts2 {
		return true
	}
	if hasProducts1 && !hasProducts2 {
		return false
	}

	// At this point, both groups are in the same state regarding products
	if !hasProducts1 { // which means !hasProducts2 is also true
		return strings.ToLower(g1.Name) < strings.ToLower(g2.Name)
	}

	// Both groups have products - compare the first product
	return g1.Product.Name < g2.Product.Name
}

func filterGroups(groups []groupentity.AdminGroup, query query.AdminGroup) []groupentity.AdminGroup {

	filtered := make([]groupentity.AdminGroup, 0, len(groups))

	var allowedProductIDs map[int64]bool
	if len(query.ProductIDs) > 0 {
		allowedProductIDs = make(map[int64]bool, len(query.ProductIDs))
		for _, id := range query.ProductIDs {
			allowedProductIDs[id] = true
		}
	}

	var searchLower string
	if query.Search != nil && *query.Search != "" {
		searchLower = strings.ToLower(*query.Search)
	}

	for _, group := range groups {
		keep := true

		// Search filter
		if searchLower != "" {
			if !strings.Contains(strings.ToLower(group.Name), searchLower) {
				keep = false
			}
		}

		// Type filter
		if keep && query.Type != nil {
			if *query.Type == constants.SystemType {
				keep = group.Type == constants.SystemType
			} else {
				keep = group.Type == constants.CustomType
			}
		}

		// Status filter
		if keep && query.Status != nil {
			switch *query.Status {
			case constants.RoleActive:
				keep = group.IsActive == constants.Active
			case constants.RoleArchive:
				keep = group.IsActive == constants.Archive
			}
		}

		// Product filter
		if keep && allowedProductIDs != nil {
			if group.Product == nil {
				keep = false
			} else {
				_, ok := allowedProductIDs[group.Product.ID]
				keep = ok
			}
		}

		if keep {
			filtered = append(filtered, group)
		}
	}

	return filtered
}

func sortGroups(groups []groupentity.AdminGroup, query query.AdminGroup) []groupentity.AdminGroup {

	if query.Sort == nil {
		return groups
	}

	sorted := make([]groupentity.AdminGroup, len(groups))
	copy(sorted, groups)

	sortField := *query.Sort
	orderAsc := true
	if query.Order != nil && *query.Order == sharedentity.SortOrderDescend.String() {
		orderAsc = false
	}

	sort.SliceStable(sorted, func(i, j int) bool {
		less := compareGroups(sorted[i], sorted[j], sortField)

		if !orderAsc {
			return !less
		}
		return less
	})

	return sorted
}
