package service

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/query"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func TestFilterGroups_SearchFilter(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admin Group", Type: constants.SystemType},
		{ID: 2, Name: "User Group", Type: constants.CustomType},
		{ID: 3, Name: "Test Admin", Type: constants.SystemType},
	}

	search := "admin"
	q := query.AdminGroup{Search: &search}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "Admin Group", result[0].Name)
	require.Equal(t, "Test Admin", result[1].Name)
}

func TestFilterGroups_SearchFilter_CaseInsensitive(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "ADMIN Group", Type: constants.SystemType},
		{ID: 2, Name: "user group", Type: constants.CustomType},
	}

	search := "admin"
	q := query.AdminGroup{Search: &search}

	result := filterGroups(groups, q)

	require.Len(t, result, 1)
	require.Equal(t, "ADMIN Group", result[0].Name)
}

func TestFilterGroups_SearchFilter_EmptySearch(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admin Group", Type: constants.SystemType},
		{ID: 2, Name: "User Group", Type: constants.CustomType},
	}

	search := ""
	q := query.AdminGroup{Search: &search}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
}

func TestFilterGroups_SearchFilter_NoMatch(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admin Group", Type: constants.SystemType},
		{ID: 2, Name: "User Group", Type: constants.CustomType},
	}

	search := "nonexistent"
	q := query.AdminGroup{Search: &search}

	result := filterGroups(groups, q)

	require.Empty(t, result)
}

func TestFilterGroups_TypeFilter_SystemType(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "System Group", Type: constants.SystemType},
		{ID: 2, Name: "Custom Group", Type: constants.CustomType},
		{ID: 3, Name: "Another System", Type: constants.SystemType},
	}

	groupType := constants.SystemType
	q := query.AdminGroup{Type: &groupType}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, constants.SystemType, result[0].Type)
	require.Equal(t, constants.SystemType, result[1].Type)
}

func TestFilterGroups_TypeFilter_CustomType(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "System Group", Type: constants.SystemType},
		{ID: 2, Name: "Custom Group", Type: constants.CustomType},
		{ID: 3, Name: "Another Custom", Type: constants.CustomType},
	}

	groupType := constants.CustomType
	q := query.AdminGroup{Type: &groupType}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, constants.CustomType, result[0].Type)
	require.Equal(t, constants.CustomType, result[1].Type)
}

func TestFilterGroups_ProductFilter(t *testing.T) {
	product1 := &productentity.Product{ID: 1, Name: "Product 1"}
	product2 := &productentity.Product{ID: 2, Name: "Product 2"}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", Product: product1},
		{ID: 2, Name: "Group 2", Product: product2},
		{ID: 3, Name: "Group 3", Product: nil},
		{ID: 4, Name: "Group 4", Product: product1},
	}

	productIDs := []int64{1}
	q := query.AdminGroup{ProductIDs: productIDs}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "Group 1", result[0].Name)
	require.Equal(t, "Group 4", result[1].Name)
}

func TestFilterGroups_ProductFilter_EmptyProductIDs(t *testing.T) {
	product1 := &productentity.Product{ID: 1, Name: "Product 1"}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", Product: product1},
		{ID: 2, Name: "Group 2", Product: nil},
	}

	var productIDs []int64
	q := query.AdminGroup{ProductIDs: productIDs}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
}

func TestFilterGroups_ProductFilter_NilProduct(t *testing.T) {
	product1 := &productentity.Product{ID: 1, Name: "Product 1"}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", Product: product1},
		{ID: 2, Name: "Group 2", Product: nil},
	}

	productIDs := []int64{1}
	q := query.AdminGroup{ProductIDs: productIDs}

	result := filterGroups(groups, q)

	require.Len(t, result, 1)
	require.Equal(t, "Group 1", result[0].Name)
}

func TestFilterGroups_CombinedFilters(t *testing.T) {
	product1 := &productentity.Product{ID: 1, Name: "Product 1"}
	product2 := &productentity.Product{ID: 2, Name: "Product 2"}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admin System", Type: constants.SystemType, Product: product1},
		{ID: 2, Name: "Admin Custom", Type: constants.CustomType, Product: product1},
		{ID: 3, Name: "User System", Type: constants.SystemType, Product: product2},
		{ID: 4, Name: "User Custom", Type: constants.CustomType, Product: product1},
	}

	search := "admin"
	groupType := constants.CustomType
	productIDs := []int64{1}
	q := query.AdminGroup{
		Search:     &search,
		Type:       &groupType,
		ProductIDs: productIDs,
	}

	result := filterGroups(groups, q)

	require.Len(t, result, 1)
	require.Equal(t, "Admin Custom", result[0].Name)
}

func TestFilterGroups_CombinedFilters_WithStatus(t *testing.T) {
	product1 := &productentity.Product{ID: 1, Name: "Product 1"}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Admin Active", Type: constants.CustomType, Product: product1, IsActive: constants.Active},
		{ID: 2, Name: "Admin Archive", Type: constants.CustomType, Product: product1, IsActive: constants.Archive},
		{ID: 3, Name: "User Active", Type: constants.SystemType, Product: product1, IsActive: constants.Active},
	}

	search := "admin"
	groupType := constants.CustomType
	status := constants.RoleActive
	productIDs := []int64{1}
	q := query.AdminGroup{
		Search:     &search,
		Type:       &groupType,
		Status:     &status,
		ProductIDs: productIDs,
	}

	result := filterGroups(groups, q)

	require.Len(t, result, 1)
	require.Equal(t, "Admin Active", result[0].Name)
}

func TestFilterGroups_StatusFilter_ActiveStatus(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Active Group", IsActive: constants.Active},
		{ID: 2, Name: "Archive Group", IsActive: constants.Archive},
		{ID: 3, Name: "Another Active", IsActive: constants.Active},
	}

	status := constants.RoleActive
	q := query.AdminGroup{Status: &status}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "Active Group", result[0].Name)
	require.Equal(t, "Another Active", result[1].Name)
}

func TestFilterGroups_StatusFilter_ArchiveStatus(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Active Group", IsActive: constants.Active},
		{ID: 2, Name: "Archive Group", IsActive: constants.Archive},
		{ID: 3, Name: "Another Archive", IsActive: constants.Archive},
	}

	status := constants.RoleArchive
	q := query.AdminGroup{Status: &status}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "Archive Group", result[0].Name)
	require.Equal(t, "Another Archive", result[1].Name)
}

func TestFilterGroups_NoFilters(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", Type: constants.SystemType},
		{ID: 2, Name: "Group 2", Type: constants.CustomType},
	}

	q := query.AdminGroup{}

	result := filterGroups(groups, q)

	require.Len(t, result, 2)
}

func TestSortGroups_NoSort(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Z Group"},
		{ID: 2, Name: "A Group"},
	}

	q := query.AdminGroup{}

	result := sortGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "Z Group", result[0].Name)
	require.Equal(t, "A Group", result[1].Name)
}

func TestSortGroups_SortByName_Ascending(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Z Group"},
		{ID: 2, Name: "A Group"},
		{ID: 3, Name: "M Group"},
	}

	sortField := sharedentity.SortFieldName.String()
	order := sharedentity.SortOrderAscend.String()
	q := query.AdminGroup{Sort: &sortField, Order: &order}

	result := sortGroups(groups, q)

	require.Len(t, result, 3)
	require.Equal(t, "A Group", result[0].Name)
	require.Equal(t, "M Group", result[1].Name)
	require.Equal(t, "Z Group", result[2].Name)
}

func TestSortGroups_SortByName_Descending(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "A Group"},
		{ID: 2, Name: "Z Group"},
		{ID: 3, Name: "M Group"},
	}

	sortField := sharedentity.SortFieldName.String()
	order := sharedentity.SortOrderDescend.String()
	q := query.AdminGroup{Sort: &sortField, Order: &order}

	result := sortGroups(groups, q)

	require.Len(t, result, 3)
	require.Equal(t, "Z Group", result[0].Name)
	require.Equal(t, "M Group", result[1].Name)
	require.Equal(t, "A Group", result[2].Name)
}

func TestSortGroups_SortByType(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", Type: constants.SystemType},
		{ID: 2, Name: "Group 2", Type: constants.CustomType},
		{ID: 3, Name: "Group 3", Type: constants.SystemType},
	}

	sortField := sharedentity.SortFieldType.String()
	q := query.AdminGroup{Sort: &sortField}

	result := sortGroups(groups, q)

	require.Len(t, result, 3)
	require.Equal(t, constants.CustomType, result[0].Type)
	require.Equal(t, constants.SystemType, result[1].Type)
	require.Equal(t, constants.SystemType, result[2].Type)
}

func TestSortGroups_SortByUserCount(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", UserCount: 10},
		{ID: 2, Name: "Group 2", UserCount: 5},
		{ID: 3, Name: "Group 3", UserCount: 15},
	}

	sortField := sharedentity.SortFieldUserCount.String()
	q := query.AdminGroup{Sort: &sortField}

	result := sortGroups(groups, q)

	require.Len(t, result, 3)
	require.Equal(t, int64(5), result[0].UserCount)
	require.Equal(t, int64(10), result[1].UserCount)
	require.Equal(t, int64(15), result[2].UserCount)
}

func TestSortGroups_SortByRoleCount(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", RoleCount: 10},
		{ID: 2, Name: "Group 2", RoleCount: 5},
		{ID: 3, Name: "Group 3", RoleCount: 15},
	}

	sortField := sharedentity.SortFieldRoleCount.String()
	q := query.AdminGroup{Sort: &sortField}

	result := sortGroups(groups, q)

	require.Len(t, result, 3)
	require.Equal(t, int64(5), result[0].RoleCount)
	require.Equal(t, int64(10), result[1].RoleCount)
	require.Equal(t, int64(15), result[2].RoleCount)
}

func TestSortGroups_SortByProduct(t *testing.T) {
	product1 := &productentity.Product{ID: 1, Name: "A Product"}
	product2 := &productentity.Product{ID: 2, Name: "Z Product"}

	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Group 1", Product: product2},
		{ID: 2, Name: "Group 2", Product: product1},
		{ID: 3, Name: "Group 3", Product: nil},
	}

	sortField := sharedentity.SortFieldProduct.String()
	q := query.AdminGroup{Sort: &sortField}

	result := sortGroups(groups, q)

	require.Len(t, result, 3)
	require.Nil(t, result[0].Product)
	require.Equal(t, "A Product", result[1].Product.Name)
	require.Equal(t, "Z Product", result[2].Product.Name)
}

func TestSortGroups_SortByProduct_BothNilProducts(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Z Group", Product: nil},
		{ID: 2, Name: "A Group", Product: nil},
	}

	sortField := sharedentity.SortFieldProduct.String()
	q := query.AdminGroup{Sort: &sortField}

	result := sortGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "A Group", result[0].Name)
	require.Equal(t, "Z Group", result[1].Name)
}

func TestSortGroups_SortByUnknownField_DefaultsToName(t *testing.T) {
	groups := []groupentity.AdminGroup{
		{ID: 1, Name: "Z Group"},
		{ID: 2, Name: "A Group"},
	}

	sortField := "unknown_field"
	q := query.AdminGroup{Sort: &sortField}

	result := sortGroups(groups, q)

	require.Len(t, result, 2)
	require.Equal(t, "A Group", result[0].Name)
	require.Equal(t, "Z Group", result[1].Name)
}

func TestCompareGroups_Name(t *testing.T) {
	g1 := groupentity.AdminGroup{Name: "A Group"}
	g2 := groupentity.AdminGroup{Name: "B Group"}

	result := compareGroups(g1, g2, sharedentity.SortFieldName.String())
	require.True(t, result)

	result = compareGroups(g2, g1, sharedentity.SortFieldName.String())
	require.False(t, result)
}

func TestCompareGroups_Type(t *testing.T) {
	g1 := groupentity.AdminGroup{Type: constants.CustomType}
	g2 := groupentity.AdminGroup{Type: constants.SystemType}

	result := compareGroups(g1, g2, sharedentity.SortFieldType.String())
	require.True(t, result)

	result = compareGroups(g2, g1, sharedentity.SortFieldType.String())
	require.False(t, result)
}

func TestCompareGroups_UserCount(t *testing.T) {
	g1 := groupentity.AdminGroup{UserCount: 5}
	g2 := groupentity.AdminGroup{UserCount: 10}

	result := compareGroups(g1, g2, sharedentity.SortFieldUserCount.String())
	require.True(t, result)

	result = compareGroups(g2, g1, sharedentity.SortFieldUserCount.String())
	require.False(t, result)
}

func TestCompareGroups_RoleCount(t *testing.T) {
	g1 := groupentity.AdminGroup{RoleCount: 3}
	g2 := groupentity.AdminGroup{RoleCount: 7}

	result := compareGroups(g1, g2, sharedentity.SortFieldRoleCount.String())
	require.True(t, result)

	result = compareGroups(g2, g1, sharedentity.SortFieldRoleCount.String())
	require.False(t, result)
}

func TestCompareGroupsByProduct_BothHaveProducts(t *testing.T) {
	product1 := &productentity.Product{Name: "A Product"}
	product2 := &productentity.Product{Name: "B Product"}

	g1 := groupentity.AdminGroup{Product: product1}
	g2 := groupentity.AdminGroup{Product: product2}

	result := compareGroupsByProduct(g1, g2)
	require.True(t, result)

	result = compareGroupsByProduct(g2, g1)
	require.False(t, result)
}

func TestCompareGroupsByProduct_OneNilProduct(t *testing.T) {
	product := &productentity.Product{Name: "Product"}

	g1 := groupentity.AdminGroup{Product: nil}
	g2 := groupentity.AdminGroup{Product: product}

	result := compareGroupsByProduct(g1, g2)
	require.True(t, result)

	result = compareGroupsByProduct(g2, g1)
	require.False(t, result)
}

func TestCompareGroupsByProduct_BothNilProducts(t *testing.T) {
	g1 := groupentity.AdminGroup{Name: "A Group", Product: nil}
	g2 := groupentity.AdminGroup{Name: "B Group", Product: nil}

	result := compareGroupsByProduct(g1, g2)
	require.True(t, result)

	result = compareGroupsByProduct(g2, g1)
	require.False(t, result)
}
