package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/admingroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemgroup"
	appquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/group/query"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/aggregate"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func ToAdminGroupUpdateFromV1DTO(rx admingroup.AdminGroupUpdateV1DTO, groupID int64) aggregate.AdminGroupUpdate {
	result := aggregate.AdminGroupUpdate{
		ID:       groupID,
		Name:     rx.Name,
		IsActive: rx.IsActive,
	}
	resultUsers := make([]userentity.UserProductLink, 0)
	if rx.Users != nil {
		for _, u := range *rx.Users {
			link := userentity.UserProductLink{
				UserID:    u.ID,
				ProductID: u.ProductID,
			}
			resultUsers = append(resultUsers, link)
		}
	}
	result.Users = &resultUsers

	if rx.Roles != nil {
		resultRoles := make([]roleentity.RoleID, 0)
		for _, u := range *rx.Roles {
			resultRoles = append(resultRoles, roleentity.RoleID{
				ID: u.ID,
			})
		}
		result.Roles = &resultRoles
	}

	return result
}

func ToDTOGroupFullFromModelGroupFull(serviceGroup groupentity.GroupFull) systemgroup.GroupFullV1DTO {

	var dto systemgroup.GroupFullV1DTO
	dto.ID = serviceGroup.ID
	dto.Name = serviceGroup.Name
	dto.Type = constants.IsSystemToConstant(serviceGroup.IsSystem)

	if serviceGroup.UserIDs != nil {
		dto.UserIDs = serviceGroup.UserIDs
	}

	if serviceGroup.ParticipantIDs != nil {
		dto.ParticipantIDs = serviceGroup.ParticipantIDs
	}

	if serviceGroup.RoleIDs != nil {
		dto.RoleIDs = serviceGroup.RoleIDs
	}

	return dto
}

func ToEntitiesCategoryStatusFromV1DTOs(categories []admingroup.CategoryWithCheckedV1DTO) []groupentity.GroupCategoryStates {
	var croups []groupentity.GroupCategoryStates
	for _, category := range categories {
		croups = append(croups, groupentity.GroupCategoryStates{
			CategoryID:   category.ID,
			CategoryName: category.Name,
			IsActive:     category.IsActive,
		})
	}
	return croups
}

func ToEntitiesGroupWithCategoryStatsFromV1DTOs(dtos []admingroup.GroupViewAdminEntityV1DTO) []groupentity.GroupWithCategoryStats {
	var groups []groupentity.GroupWithCategoryStats
	for _, dto := range dtos {
		groups = append(groups, groupentity.GroupWithCategoryStats{
			GroupID:          dto.ID,
			GroupName:        dto.Name,
			IsSystem:         constants.ToBoolFromSystemType(dto.Type),
			RoleCount:        dto.RoleCount,
			ParticipantCount: dto.ParticipantCount,
			UserCount:        dto.UserCount,
			CategoryStates:   ToEntitiesCategoryStatusFromV1DTOs(dto.Categories),
		})
	}
	return groups
}

func ToEntityGroupFormAdminGroupCreateV1DTO(rx admingroup.AdminGroupCreateV1DTO) groupentity.Group {
	return groupentity.Group{
		Name:      rx.Name,
		ProductID: rx.ProductID,
		IsSystem:  rx.Type == constants.SystemType,
	}
}

func ToEntityGroupFromV1DTOCreate(productID int64, groupType string, dto productgroup.GroupCreateV1DTO) groupentity.Group {
	return groupentity.Group{
		Name:      dto.Name,
		ProductID: &productID,
		IsSystem:  groupType == constants.SystemType,
	}
}

func ToEntityGroupFullFromV1DTOGroupFull(dto systemgroup.GroupFullV1DTO, groupID int64) groupentity.GroupFull {
	var usersIDs *[]int64
	if dto.UserIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.UserIDs...)
		usersIDs = &IDs
	}

	var roleIDs *[]int64
	if dto.RoleIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.RoleIDs...)
		roleIDs = &IDs
	}

	return groupentity.GroupFull{
		ID:       groupID,
		Name:     dto.Name,
		IsSystem: constants.ToBoolFromSystemType(dto.Type),
		RoleIDs:  roleIDs,
		UserIDs:  usersIDs,
	}
}

func ToEntityGroupFullFromV1DTOUpdate(groupID int64, dto productgroup.GroupUpdateV1DTO) groupentity.GroupFull {

	var result groupentity.GroupFull
	result.ID = groupID
	if dto.Name != nil {
		result.Name = *dto.Name
	}

	if dto.Type != nil {
		result.IsSystem = constants.ToBoolFromSystemType(*dto.Type)
	}

	if dto.ParticipantIDs != nil {
		var participantIDs *[]int64
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.ParticipantIDs...)
		participantIDs = &IDs
		result.ParticipantIDs = participantIDs
	}

	if dto.RoleIDs != nil {
		var roleIDs *[]int64
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.RoleIDs...)
		roleIDs = &IDs
		result.RoleIDs = roleIDs
	}

	// TODO: process pointers
	return result
}

func ToModelGroupFromV1DTOCreateSystemGroup(productID int64, groupType string, dto systemgroup.GroupCreateV1DTO) groupentity.Group {
	// Need to set productID to nil for system groups
	var productIDPtr *int64
	if groupType != constants.SystemType {
		productIDPtr = &productID
	}

	return groupentity.Group{
		Name:      dto.Name,
		ProductID: productIDPtr,
		IsSystem:  groupType == constants.SystemType,
	}
}

func ToQueryAdminGroupsFromGetGroupsAsAdminParams(params admingroup.GetGroupsAsAdminParams) appquery.AdminGroup {

	productIDs := make([]int64, 0)
	if params.ProductIDs != nil {
		productIDs = *params.ProductIDs
	}

	query := appquery.AdminGroup{
		Search:     params.Search,
		ProductIDs: productIDs,
		Type:       (*string)(params.Type),
		Status:     (*string)(params.Status),
		Sort:       (*string)(params.Sort),
		Order:      (*string)(params.Order),
		Limit:      params.Limit,
		Offset:     params.Offset,
	}

	return query
}

func ToV1DTOAdminGroupCollectionFromModels(paginatedResult sharedentity.PaginatedResult[groupentity.AdminGroup]) admingroup.AdminGroupCollectionV1DTO {
	var items []admingroup.AdminGroupV1DTO
	for _, group := range paginatedResult.Items {
		items = append(items, ToV1DTOFromEntityAdminGroups(group))
	}

	return admingroup.AdminGroupCollectionV1DTO{
		Items: items,
		Meta: admingroup.PaginationV1DTO{
			Limit:  paginatedResult.Limit,
			Offset: paginatedResult.Offset,
			Total:  paginatedResult.Total,
		},
	}
}

func ToV1DTOFromEntityAdminGroups(groups groupentity.AdminGroup) admingroup.AdminGroupV1DTO {

	var product *admingroup.ProductBasicV1DTO

	if groups.Product != nil {
		product = &admingroup.ProductBasicV1DTO{
			ID:       groups.Product.ID,
			IID:      &groups.Product.IID,
			TechName: groups.Product.TechName,
			Name:     groups.Product.Name,
		}
	}

	return admingroup.AdminGroupV1DTO{
		ID:        groups.ID,
		Name:      groups.Name,
		Type:      groups.Type,
		IsActive:  groups.IsActive,
		Product:   product,
		UserCount: groups.UserCount,
		RoleCount: groups.RoleCount,
	}
}

func ToV1DTOFromEntityGroupFull(serviceGroup groupentity.GroupFull) systemgroup.GroupFullV1DTO {

	var dto systemgroup.GroupFullV1DTO
	dto.ID = serviceGroup.ID
	dto.Name = serviceGroup.Name
	dto.Type = constants.IsSystemToConstant(serviceGroup.IsSystem)

	if serviceGroup.UserIDs != nil {
		dto.UserIDs = serviceGroup.UserIDs
	}

	if serviceGroup.ParticipantIDs != nil {
		dto.ParticipantIDs = serviceGroup.ParticipantIDs
	}

	if serviceGroup.RoleIDs != nil {
		dto.RoleIDs = serviceGroup.RoleIDs
	}

	return dto
}

func ToV1DTOGroupFullFromEntity(group groupentity.GroupFull) productgroup.GroupFullV1DTO {

	var dto productgroup.GroupFullV1DTO
	dto.ID = group.ID
	dto.Name = group.Name
	dto.Type = constants.IsSystemToConstant(group.IsSystem)

	if group.ParticipantIDs != nil {
		dto.ParticipantIDs = group.ParticipantIDs
	}

	if group.RoleIDs != nil {
		dto.RoleIDs = group.RoleIDs
	}

	if group.UserIDs != nil {
		dto.UserIDs = group.UserIDs
	}

	return dto
}

func ToV1DTOGroupShortFromEntity(group groupentity.GroupWithStats) productgroup.GroupShortV1DTO {
	return productgroup.GroupShortV1DTO{
		ID:               group.GroupID,
		Name:             group.GroupName,
		Type:             constants.IsSystemToConstant(group.IsSystem),
		RoleCount:        group.RoleCount,
		ParticipantCount: group.ParticipantCount,
		UserCount:        group.UserCount,
	}
}

func ToV1DTOGroupShortFromModelGroupWithStats(serviceGroups []groupentity.GroupWithStats) []systemgroup.GroupShortV1DTO {
	dto := make([]systemgroup.GroupShortV1DTO, 0)
	for _, serviceGroup := range serviceGroups {
		dto = append(dto, ToV1DTOGroupShortFromModelSystemGroup(serviceGroup))
	}
	return dto
}

func ToV1DTOGroupShortFromModelSystemGroup(group groupentity.GroupWithStats) systemgroup.GroupShortV1DTO {
	return systemgroup.GroupShortV1DTO{
		ID:               group.GroupID,
		Name:             group.GroupName,
		Type:             constants.IsSystemToConstant(group.IsSystem),
		RoleCount:        group.RoleCount,
		ParticipantCount: group.ParticipantCount,
		UserCount:        group.UserCount,
	}
}

func ToV1DTOGroupWithCategoryStatsFromEntity(group groupentity.GroupWithCategoryStats) admingroup.GroupViewAdminEntityV1DTO {
	return admingroup.GroupViewAdminEntityV1DTO{
		ID:               group.GroupID,
		Name:             group.GroupName,
		Type:             constants.IsSystemToConstant(group.IsSystem),
		RoleCount:        group.RoleCount,
		ParticipantCount: group.ParticipantCount,
		UserCount:        group.UserCount,
		Categories:       ToV1DTOsCategoryStatusFromEntities(group.CategoryStates),
	}
}

func ToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(group aggregate.GroupWithDetails) admingroup.GroupWithDetailsV1DTO {

	var (
		groupUserDTO []admingroup.UserWithProductV1DTO
		groupRoleDTO []admingroup.RoleWithProductV1DTO
	)

	dto := admingroup.GroupWithDetailsV1DTO{
		ID:        group.Group.ID,
		Name:      group.Group.Name,
		Type:      constants.IsSystemToConstant(group.Group.IsSystem),
		IsActive:  group.Group.ActiveFlg,
		CreatedAt: group.Group.CreatedAt,
	}

	if group.Product != nil {
		dto.Product = &admingroup.ProductBasicV1DTO{
			ID:       group.Product.ID,
			IID:      &group.Product.IID,
			Name:     group.Product.Name,
			TechName: group.Product.TechName,
		}
	}

	for _, user := range group.Users {

		if user.Product != nil {

			p := user.Product

			productDTO := &admingroup.ProductBasicV1DTO{
				ID:       p.ID,
				IID:      &p.IID,
				Name:     p.Name,
				TechName: p.TechName,
			}

			userDTO := admingroup.UserWithProductV1DTO{
				ID:       user.ID,
				Email:    user.Email,
				FullName: user.FullName,
				Product:  productDTO,
			}

			groupUserDTO = append(groupUserDTO, userDTO)
		} else {
			userDTO := admingroup.UserWithProductV1DTO{
				ID:       user.ID,
				Email:    user.Email,
				FullName: user.FullName,
			}

			groupUserDTO = append(groupUserDTO, userDTO)
		}
	}

	dto.Users = groupUserDTO

	for _, role := range group.Roles {

		var productDTO *admingroup.ProductBasicV1DTO
		if role.Product != nil && role.Product.ID != 0 {
			productDTO = &admingroup.ProductBasicV1DTO{
				ID:       role.Product.ID,
				IID:      &role.Product.IID,
				Name:     role.Product.Name,
				TechName: role.Product.TechName,
			}
		}

		roleDTO := admingroup.RoleWithProductV1DTO{
			ID:       role.ID,
			Name:     role.Name,
			IsActive: role.ActiveFlg,
			Type:     constants.IsSystemToConstant(role.IsSystem),
			Product:  productDTO,
		}

		groupRoleDTO = append(groupRoleDTO, roleDTO)
	}

	dto.Roles = groupRoleDTO
	dto.Users = groupUserDTO

	return dto
}

func ToV1DTOsCategoryStatusFromEntities(categories []groupentity.GroupCategoryStates) []admingroup.CategoryWithCheckedV1DTO {
	var dtos []admingroup.CategoryWithCheckedV1DTO
	for _, category := range categories {
		dtos = append(dtos, admingroup.CategoryWithCheckedV1DTO{
			ID:       category.CategoryID,
			Name:     category.CategoryName,
			IsActive: category.IsActive,
		})
	}
	return dtos
}

func ToV1DTOsGroupShortFromEntity(groups []groupentity.GroupWithStats) []productgroup.GroupShortV1DTO {
	var dtos []productgroup.GroupShortV1DTO
	for _, group := range groups {
		dtos = append(dtos, ToV1DTOGroupShortFromEntity(group))
	}
	return dtos
}

func ToV1DTOsGroupWithCategoryStatsFromModels(groups []groupentity.GroupWithCategoryStats) []admingroup.GroupViewAdminEntityV1DTO {
	var dtos []admingroup.GroupViewAdminEntityV1DTO
	for _, group := range groups {
		dtos = append(dtos, ToV1DTOGroupWithCategoryStatsFromEntity(group))
	}
	return dtos
}
