package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/admingroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/aggregate"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestToEntityGroupFromV1DTOCreate(t *testing.T) {
	dto := productgroup.GroupCreateV1DTO{
		Name: "Test Group",
	}
	productID := int64(123)
	groupType := constants.SystemType

	result := ToEntityGroupFromV1DTOCreate(productID, groupType, dto)

	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, &productID, result.ProductID)
	assert.True(t, result.IsSystem)
}

func TestToEntityGroupFromV1DTOCreate_CustomType(t *testing.T) {
	dto := productgroup.GroupCreateV1DTO{
		Name: "Test Group",
	}
	productID := int64(123)
	groupType := constants.CustomType

	result := ToEntityGroupFromV1DTOCreate(productID, groupType, dto)

	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, &productID, result.ProductID)
	assert.False(t, result.IsSystem)
}

func TestToEntityGroupFullFromV1DTOUpdate(t *testing.T) {
	groupID := int64(456)
	name := "Updated Group"
	groupType := constants.SystemType
	participantIDs := []int64{1, 2, 3}
	roleIDs := []int64{4, 5, 6}

	dto := productgroup.GroupUpdateV1DTO{
		Name:           &name,
		Type:           &groupType,
		ParticipantIDs: &participantIDs,
		RoleIDs:        &roleIDs,
	}

	result := ToEntityGroupFullFromV1DTOUpdate(groupID, dto)

	assert.Equal(t, groupID, result.ID)
	assert.Equal(t, "Updated Group", result.Name)
	assert.True(t, result.IsSystem)
	require.NotNil(t, result.ParticipantIDs)
	assert.Equal(t, participantIDs, *result.ParticipantIDs)
	require.NotNil(t, result.RoleIDs)
	assert.Equal(t, roleIDs, *result.RoleIDs)
}

func TestToEntityGroupFullFromV1DTOUpdate_NilValues(t *testing.T) {
	groupID := int64(456)
	dto := productgroup.GroupUpdateV1DTO{}

	result := ToEntityGroupFullFromV1DTOUpdate(groupID, dto)

	assert.Equal(t, groupID, result.ID)
	assert.Empty(t, result.Name)
	assert.False(t, result.IsSystem)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.RoleIDs)
}

func TestToV1DTOGroupFullFromEntity(t *testing.T) {
	participantIDs := []int64{1, 2, 3}
	roleIDs := []int64{4, 5, 6}
	userIDs := []int64{7, 8, 9}

	group := groupentity.GroupFull{
		ID:             123,
		Name:           "Test Group",
		IsSystem:       true,
		ParticipantIDs: &participantIDs,
		RoleIDs:        &roleIDs,
		UserIDs:        &userIDs,
	}

	result := ToV1DTOGroupFullFromEntity(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, &participantIDs, result.ParticipantIDs)
	assert.Equal(t, &roleIDs, result.RoleIDs)
	assert.Equal(t, &userIDs, result.UserIDs)
}

func TestToV1DTOGroupFullFromEntity_NilValues(t *testing.T) {
	group := groupentity.GroupFull{
		ID:       123,
		Name:     "Test Group",
		IsSystem: false,
	}

	result := ToV1DTOGroupFullFromEntity(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.RoleIDs)
	assert.Nil(t, result.UserIDs)
}

func TestToV1DTOGroupShortFromEntity(t *testing.T) {
	group := groupentity.GroupWithStats{
		GroupID:          123,
		GroupName:        "Test Group",
		IsSystem:         true,
		RoleCount:        5,
		ParticipantCount: 10,
		UserCount:        15,
	}

	result := ToV1DTOGroupShortFromEntity(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, int64(5), result.RoleCount)
	assert.Equal(t, int64(10), result.ParticipantCount)
	assert.Equal(t, int64(15), result.UserCount)
}

func TestToV1DTOsGroupShortFromEntity(t *testing.T) {
	groups := []groupentity.GroupWithStats{
		{
			GroupID:          123,
			GroupName:        "Group 1",
			IsSystem:         true,
			RoleCount:        5,
			ParticipantCount: 10,
			UserCount:        15,
		},
		{
			GroupID:          456,
			GroupName:        "Group 2",
			IsSystem:         false,
			RoleCount:        3,
			ParticipantCount: 7,
			UserCount:        12,
		},
	}

	result := ToV1DTOsGroupShortFromEntity(groups)

	require.Len(t, result, 2)
	assert.Equal(t, int64(123), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.Equal(t, int64(456), result[1].ID)
	assert.Equal(t, "Group 2", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
}

func TestToV1DTOsGroupShortFromEntity_Empty(t *testing.T) {
	var groups []groupentity.GroupWithStats

	result := ToV1DTOsGroupShortFromEntity(groups)

	assert.Empty(t, result)
}

func TestToV1DTOsGroupWithCategoryStatsFromModels(t *testing.T) {
	groups := []groupentity.GroupWithCategoryStats{
		{
			GroupID:          123,
			GroupName:        "Group 1",
			IsSystem:         true,
			RoleCount:        5,
			ParticipantCount: 10,
			UserCount:        15,
			CategoryStates: []groupentity.GroupCategoryStates{
				{
					CategoryID:   1,
					CategoryName: "Category 1",
					IsActive:     true,
				},
			},
		},
	}

	result := ToV1DTOsGroupWithCategoryStatsFromModels(groups)

	require.Len(t, result, 1)
	assert.Equal(t, int64(123), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	require.Len(t, result[0].Categories, 1)
	assert.Equal(t, int64(1), result[0].Categories[0].ID)
	assert.Equal(t, "Category 1", result[0].Categories[0].Name)
	assert.True(t, result[0].Categories[0].IsActive)
}

func TestToV1DTOsCategoryStatusFromEntities(t *testing.T) {
	categories := []groupentity.GroupCategoryStates{
		{
			CategoryID:   1,
			CategoryName: "Category 1",
			IsActive:     true,
		},
		{
			CategoryID:   2,
			CategoryName: "Category 2",
			IsActive:     false,
		},
	}

	result := ToV1DTOsCategoryStatusFromEntities(categories)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Category 1", result[0].Name)
	assert.True(t, result[0].IsActive)
	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Category 2", result[1].Name)
	assert.False(t, result[1].IsActive)
}

func TestToEntitiesGroupWithCategoryStatsFromV1DTOs(t *testing.T) {
	dtos := []admingroup.GroupViewAdminEntityV1DTO{
		{
			ID:               123,
			Name:             "Group 1",
			Type:             constants.SystemType,
			RoleCount:        5,
			ParticipantCount: 10,
			UserCount:        15,
			Categories: []admingroup.CategoryWithCheckedV1DTO{
				{
					ID:       1,
					Name:     "Category 1",
					IsActive: true,
				},
			},
		},
	}

	result := ToEntitiesGroupWithCategoryStatsFromV1DTOs(dtos)

	require.Len(t, result, 1)
	assert.Equal(t, int64(123), result[0].GroupID)
	assert.Equal(t, "Group 1", result[0].GroupName)
	assert.True(t, result[0].IsSystem)
	assert.Equal(t, int64(5), result[0].RoleCount)
	require.Len(t, result[0].CategoryStates, 1)
	assert.Equal(t, int64(1), result[0].CategoryStates[0].CategoryID)
}

func TestToEntitiesCategoryStatusFromV1DTOs(t *testing.T) {
	categories := []admingroup.CategoryWithCheckedV1DTO{
		{
			ID:       1,
			Name:     "Category 1",
			IsActive: true,
		},
		{
			ID:       2,
			Name:     "Category 2",
			IsActive: false,
		},
	}

	result := ToEntitiesCategoryStatusFromV1DTOs(categories)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].CategoryID)
	assert.Equal(t, "Category 1", result[0].CategoryName)
	assert.True(t, result[0].IsActive)
	assert.Equal(t, int64(2), result[1].CategoryID)
	assert.Equal(t, "Category 2", result[1].CategoryName)
	assert.False(t, result[1].IsActive)
}

func TestToModelGroupFromV1DTOCreateSystemGroup(t *testing.T) {
	dto := systemgroup.GroupCreateV1DTO{
		Name: "System Group",
	}
	productID := int64(789)
	groupType := constants.SystemType

	result := ToModelGroupFromV1DTOCreateSystemGroup(productID, groupType, dto)

	assert.Equal(t, "System Group", result.Name)
	assert.Nil(t, result.ProductID) // Для системных групп должно быть nil
	assert.True(t, result.IsSystem)
}

func TestToModelGroupFromV1DTOCreateSystemGroup_ProductGroup(t *testing.T) {
	dto := systemgroup.GroupCreateV1DTO{
		Name: "Product Group",
	}
	productID := int64(789)
	groupType := constants.CustomType

	result := ToModelGroupFromV1DTOCreateSystemGroup(productID, groupType, dto)

	assert.Equal(t, "Product Group", result.Name)
	assert.Equal(t, &productID, result.ProductID) // Для продуктовых групп должно быть установлено
	assert.False(t, result.IsSystem)
}

func TestToV1DTOGroupShortFromModelSystemGroup(t *testing.T) {
	group := groupentity.GroupWithStats{
		GroupID:          123,
		GroupName:        "System Group",
		IsSystem:         true,
		RoleCount:        3,
		ParticipantCount: 6,
		UserCount:        9,
	}

	result := ToV1DTOGroupShortFromModelSystemGroup(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "System Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, int64(3), result.RoleCount)
	assert.Equal(t, int64(6), result.ParticipantCount)
	assert.Equal(t, int64(9), result.UserCount)
}

func TestToV1DTOGroupShortFromModelGroupWithStats(t *testing.T) {
	groups := []groupentity.GroupWithStats{
		{
			GroupID:          123,
			GroupName:        "Group 1",
			IsSystem:         true,
			RoleCount:        3,
			ParticipantCount: 6,
			UserCount:        9,
		},
		{
			GroupID:          456,
			GroupName:        "Group 2",
			IsSystem:         false,
			RoleCount:        2,
			ParticipantCount: 4,
			UserCount:        6,
		},
	}

	result := ToV1DTOGroupShortFromModelGroupWithStats(groups)

	require.Len(t, result, 2)
	assert.Equal(t, int64(123), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.Equal(t, int64(456), result[1].ID)
	assert.Equal(t, "Group 2", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
}

func TestToDTOGroupFullFromModelGroupFull(t *testing.T) {
	userIDs := []int64{1, 2, 3}
	participantIDs := []int64{4, 5, 6}
	roleIDs := []int64{7, 8, 9}

	group := groupentity.GroupFull{
		ID:             123,
		Name:           "Full Group",
		IsSystem:       true,
		UserIDs:        &userIDs,
		ParticipantIDs: &participantIDs,
		RoleIDs:        &roleIDs,
	}

	result := ToDTOGroupFullFromModelGroupFull(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Full Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, &userIDs, result.UserIDs)
	assert.Equal(t, &participantIDs, result.ParticipantIDs)
	assert.Equal(t, &roleIDs, result.RoleIDs)
}

func TestToDTOGroupFullFromModelGroupFull_NilValues(t *testing.T) {
	group := groupentity.GroupFull{
		ID:       123,
		Name:     "Full Group",
		IsSystem: false,
	}

	result := ToDTOGroupFullFromModelGroupFull(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Full Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.Nil(t, result.UserIDs)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.RoleIDs)
}

func TestToV1DTOFromEntityGroupFull(t *testing.T) {
	userIDs := []int64{1, 2, 3}
	participantIDs := []int64{4, 5, 6}
	roleIDs := []int64{7, 8, 9}

	group := groupentity.GroupFull{
		ID:             123,
		Name:           "Test Group",
		IsSystem:       true,
		UserIDs:        &userIDs,
		ParticipantIDs: &participantIDs,
		RoleIDs:        &roleIDs,
	}

	result := ToV1DTOFromEntityGroupFull(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, &userIDs, result.UserIDs)
	assert.Equal(t, &participantIDs, result.ParticipantIDs)
	assert.Equal(t, &roleIDs, result.RoleIDs)
}

func TestToV1DTOFromEntityGroupFull_NilValues(t *testing.T) {
	group := groupentity.GroupFull{
		ID:       123,
		Name:     "Test Group",
		IsSystem: false,
	}

	result := ToV1DTOFromEntityGroupFull(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.Nil(t, result.UserIDs)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.RoleIDs)
}

func TestToEntityGroupFullFromV1DTOGroupFull(t *testing.T) {
	userIDs := []int64{1, 2, 3}
	roleIDs := []int64{4, 5, 6}

	dto := systemgroup.GroupFullV1DTO{
		ID:      123,
		Name:    "Test Group",
		Type:    constants.SystemType,
		UserIDs: &userIDs,
		RoleIDs: &roleIDs,
	}
	groupID := int64(456)

	result := ToEntityGroupFullFromV1DTOGroupFull(dto, groupID)

	assert.Equal(t, groupID, result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.True(t, result.IsSystem)
	require.NotNil(t, result.UserIDs)
	assert.Equal(t, userIDs, *result.UserIDs)
	require.NotNil(t, result.RoleIDs)
	assert.Equal(t, roleIDs, *result.RoleIDs)
}

func TestToEntityGroupFullFromV1DTOGroupFull_NilValues(t *testing.T) {
	dto := systemgroup.GroupFullV1DTO{
		ID:   123,
		Name: "Test Group",
		Type: constants.CustomType,
	}
	groupID := int64(456)

	result := ToEntityGroupFullFromV1DTOGroupFull(dto, groupID)

	assert.Equal(t, groupID, result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.False(t, result.IsSystem)
	assert.Nil(t, result.UserIDs)
	assert.Nil(t, result.RoleIDs)
}

func TestToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(t *testing.T) {
	now := time.Now()
	productID := int64(100)

	users := []userentity.UserWithProduct{
		{
			ID:       1,
			Email:    "<EMAIL>",
			FullName: "John Doe",
			Product: &productentity.Product{
				ID:       productID,
				IID:      "P001",
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:       2,
			Email:    "<EMAIL>",
			FullName: "Jane Smith",
		},
	}

	roles := []roleentity.RoleWithProduct{
		{
			ID:        1,
			Name:      "Admin Role",
			IsSystem:  true,
			ActiveFlg: true,
			Product: &productentity.Product{
				ID:       productID,
				IID:      "P001",
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:        2,
			Name:      "User Role",
			IsSystem:  false,
			ActiveFlg: false,
		},
	}

	group := aggregate.GroupWithDetails{
		Group: groupentity.Group{
			ID:        123,
			Name:      "Detailed Group",
			IsSystem:  true,
			ActiveFlg: true,
			CreatedAt: now,
		},
		Product: &productentity.Product{
			ID:       productID,
			IID:      "P001",
			Name:     "Product 1",
			TechName: "product1",
		},
		Users: users,
		Roles: roles,
	}

	result := ToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Detailed Group", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.True(t, result.IsActive)
	assert.Equal(t, now, result.CreatedAt)

	// Проверяем Product
	require.NotNil(t, result.Product)
	assert.Equal(t, productID, result.Product.ID)
	assert.Equal(t, "P001", *result.Product.IID)
	assert.Equal(t, "Product 1", result.Product.Name)
	assert.Equal(t, "product1", result.Product.TechName)

	// Проверяем Users
	require.Len(t, result.Users, 2)
	assert.Equal(t, int64(1), result.Users[0].ID)
	assert.Equal(t, "<EMAIL>", result.Users[0].Email)
	assert.Equal(t, "John Doe", result.Users[0].FullName)
	require.NotNil(t, result.Users[0].Product)
	assert.Equal(t, productID, result.Users[0].Product.ID)

	assert.Equal(t, int64(2), result.Users[1].ID)
	assert.Equal(t, "<EMAIL>", result.Users[1].Email)
	assert.Equal(t, "Jane Smith", result.Users[1].FullName)
	assert.Nil(t, result.Users[1].Product)

	// Проверяем Roles
	require.Len(t, result.Roles, 2)
	assert.Equal(t, int64(1), result.Roles[0].ID)
	assert.Equal(t, "Admin Role", result.Roles[0].Name)
	assert.True(t, result.Roles[0].IsActive)
	assert.Equal(t, constants.SystemType, result.Roles[0].Type)
	require.NotNil(t, result.Roles[0].Product)

	assert.Equal(t, int64(2), result.Roles[1].ID)
	assert.Equal(t, "User Role", result.Roles[1].Name)
	assert.False(t, result.Roles[1].IsActive)
	assert.Equal(t, constants.CustomType, result.Roles[1].Type)
	assert.Nil(t, result.Roles[1].Product)
}

func TestToV1DTOGroupWithDetailsFromAggregateGroupWithDetails_NilProduct(t *testing.T) {
	now := time.Now()

	group := aggregate.GroupWithDetails{
		Group: groupentity.Group{
			ID:        123,
			Name:      "Detailed Group",
			IsSystem:  false,
			ActiveFlg: false,
			CreatedAt: now,
		},
		Users: []userentity.UserWithProduct{},
		Roles: []roleentity.RoleWithProduct{},
	}

	result := ToV1DTOGroupWithDetailsFromAggregateGroupWithDetails(group)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Detailed Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.False(t, result.IsActive)
	assert.Nil(t, result.Product)
	assert.Empty(t, result.Users)
	assert.Empty(t, result.Roles)
}

func TestToV1DTOAdminGroupCollectionFromModels(t *testing.T) {
	product := &productentity.Product{
		ID:       100,
		IID:      "P001",
		Name:     "Product 1",
		TechName: "product1",
	}

	adminGroups := []groupentity.AdminGroup{
		{
			ID:               123,
			Name:             "Admin Group 1",
			Type:             constants.SystemType,
			IsActive:         true,
			Product:          product,
			ParticipantCount: 5,
			UserCount:        10,
			RoleCount:        3,
		},
	}

	paginatedResult := sharedentity.PaginatedResult[groupentity.AdminGroup]{
		Items:  adminGroups,
		Total:  1,
		Offset: 0,
		Limit:  10,
	}

	result := ToV1DTOAdminGroupCollectionFromModels(paginatedResult)

	require.Len(t, result.Items, 1)
	assert.Equal(t, int64(123), result.Items[0].ID)
	assert.Equal(t, "Admin Group 1", result.Items[0].Name)
	assert.Equal(t, constants.SystemType, result.Items[0].Type)
	assert.True(t, result.Items[0].IsActive)
	require.NotNil(t, result.Items[0].Product)
	assert.Equal(t, int64(100), result.Items[0].Product.ID)
	assert.Equal(t, int64(1), result.Meta.Total)
	assert.Equal(t, int64(0), result.Meta.Offset)
	assert.Equal(t, int64(10), result.Meta.Limit)
}

func TestToV1DTOFromEntityAdminGroups(t *testing.T) {
	product := &productentity.Product{
		ID:       100,
		IID:      "P001",
		Name:     "Product 1",
		TechName: "product1",
	}

	adminGroup := groupentity.AdminGroup{
		ID:               456,
		Name:             "Admin Group",
		Type:             constants.CustomType,
		IsActive:         false,
		Product:          product,
		ParticipantCount: 5,
		UserCount:        10,
		RoleCount:        7,
	}

	result := ToV1DTOFromEntityAdminGroups(adminGroup)

	assert.Equal(t, int64(456), result.ID)
	assert.Equal(t, "Admin Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.False(t, result.IsActive)
	require.NotNil(t, result.Product)
	assert.Equal(t, int64(100), result.Product.ID)
	assert.Equal(t, "P001", *result.Product.IID)
	assert.Equal(t, "Product 1", result.Product.Name)
	assert.Equal(t, "product1", result.Product.TechName)
	assert.Equal(t, int64(10), result.UserCount)
	assert.Equal(t, int64(7), result.RoleCount)
}

func TestToV1DTOFromEntityAdminGroups_NilProduct(t *testing.T) {
	adminGroup := groupentity.AdminGroup{
		ID:               456,
		Name:             "Admin Group",
		Type:             constants.CustomType,
		IsActive:         true,
		ParticipantCount: 5,
		UserCount:        10,
		RoleCount:        7,
	}

	result := ToV1DTOFromEntityAdminGroups(adminGroup)

	assert.Equal(t, int64(456), result.ID)
	assert.Equal(t, "Admin Group", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.True(t, result.IsActive)
	assert.Nil(t, result.Product)
	assert.Equal(t, int64(10), result.UserCount)
	assert.Equal(t, int64(7), result.RoleCount)
}

func TestToQueryAdminGroupsFromGetGroupsAsAdminParams(t *testing.T) {
	search := "test search"
	productIDs := []int64{1, 2, 3}
	groupType := admingroup.GetGroupsAsAdminParamsTypeSystem
	sortBy := admingroup.Name
	order := admingroup.Ascend
	limit := int64(20)
	offset := int64(5)

	params := admingroup.GetGroupsAsAdminParams{
		Search:     &search,
		ProductIDs: &productIDs,
		Type:       &groupType,
		Sort:       &sortBy,
		Order:      &order,
		Limit:      &limit,
		Offset:     &offset,
	}

	result := ToQueryAdminGroupsFromGetGroupsAsAdminParams(params)

	assert.Equal(t, &search, result.Search)
	assert.Equal(t, productIDs, result.ProductIDs)
	assert.Equal(t, (*string)(&groupType), result.Type)
	assert.Equal(t, (*string)(&sortBy), result.Sort)
	assert.Equal(t, (*string)(&order), result.Order)
	assert.Equal(t, &limit, result.Limit)
	assert.Equal(t, &offset, result.Offset)
}

func TestToQueryAdminGroupsFromGetGroupsAsAdminParams_NilProductIDs(t *testing.T) {
	params := admingroup.GetGroupsAsAdminParams{}

	result := ToQueryAdminGroupsFromGetGroupsAsAdminParams(params)

	assert.Nil(t, result.Search)
	assert.Empty(t, result.ProductIDs)
	assert.Nil(t, result.Type)
	assert.Nil(t, result.Sort)
	assert.Nil(t, result.Order)
	assert.Nil(t, result.Limit)
	assert.Nil(t, result.Offset)
}

func TestToEntityGroupFormAdminGroupCreateV1DTO(t *testing.T) {
	productID := int64(100)
	dto := admingroup.AdminGroupCreateV1DTO{
		Name:      "New Admin Group",
		Type:      admingroup.AdminGroupCreateV1DTOTypeSystem,
		ProductID: &productID,
	}

	result := ToEntityGroupFormAdminGroupCreateV1DTO(dto)

	assert.Equal(t, "New Admin Group", result.Name)
	assert.Equal(t, &productID, result.ProductID)
	assert.True(t, result.IsSystem)
}

func TestToEntityGroupFormAdminGroupCreateV1DTO_CustomType(t *testing.T) {
	dto := admingroup.AdminGroupCreateV1DTO{
		Name: "New Admin Group",
		Type: admingroup.AdminGroupCreateV1DTOTypeCustom,
	}

	result := ToEntityGroupFormAdminGroupCreateV1DTO(dto)

	assert.Equal(t, "New Admin Group", result.Name)
	assert.Nil(t, result.ProductID)
	assert.False(t, result.IsSystem)
}

func TestToAdminGroupUpdateFromV1DTO(t *testing.T) {
	name := "Updated Admin Group"
	isActive := false
	users := []admingroup.UserProductLinkV1DTO{
		{ID: 1, ProductID: nil},
		{ID: 2, ProductID: func() *int64 { id := int64(100); return &id }()},
	}
	roles := []admingroup.RoleIDV1DTO{
		{ID: 1},
		{ID: 2},
	}

	dto := admingroup.AdminGroupUpdateV1DTO{
		Name:     &name,
		IsActive: &isActive,
		Users:    &users,
		Roles:    &roles,
	}
	groupID := int64(789)

	result := ToAdminGroupUpdateFromV1DTO(dto, groupID)

	assert.Equal(t, groupID, result.ID)
	assert.Equal(t, &name, result.Name)
	assert.Equal(t, &isActive, result.IsActive)

	require.NotNil(t, result.Users)
	require.Len(t, *result.Users, 2)
	assert.Equal(t, int64(1), (*result.Users)[0].UserID)
	assert.Nil(t, (*result.Users)[0].ProductID)
	assert.Equal(t, int64(2), (*result.Users)[1].UserID)
	assert.Equal(t, int64(100), *(*result.Users)[1].ProductID)

	require.NotNil(t, result.Roles)
	require.Len(t, *result.Roles, 2)
	assert.Equal(t, int64(1), (*result.Roles)[0].ID)
	assert.Equal(t, int64(2), (*result.Roles)[1].ID)
}

func TestToAdminGroupUpdateFromV1DTO_NilValues(t *testing.T) {
	dto := admingroup.AdminGroupUpdateV1DTO{}
	groupID := int64(789)

	result := ToAdminGroupUpdateFromV1DTO(dto, groupID)

	assert.Equal(t, groupID, result.ID)
	assert.Nil(t, result.Name)
	assert.Nil(t, result.IsActive)
	require.NotNil(t, result.Users)
	assert.Empty(t, *result.Users)
	assert.Nil(t, result.Roles)
}

func TestToAdminGroupUpdateFromV1DTO_EmptyUsers(t *testing.T) {
	var users []admingroup.UserProductLinkV1DTO
	dto := admingroup.AdminGroupUpdateV1DTO{
		Users: &users,
	}
	groupID := int64(789)

	result := ToAdminGroupUpdateFromV1DTO(dto, groupID)

	assert.Equal(t, groupID, result.ID)
	require.NotNil(t, result.Users)
	assert.Empty(t, *result.Users)
}
