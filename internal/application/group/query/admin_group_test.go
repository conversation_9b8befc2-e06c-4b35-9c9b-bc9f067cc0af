package query

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestAdminGroup_GetLimit(t *testing.T) {
	t.Run("GetLimit_WithValue", func(t *testing.T) {
		limit := int64(10)
		q := AdminGroup{
			Limit: &limit,
		}

		result := q.GetLimit()

		require.NotNil(t, result)
		require.Equal(t, int64(10), *result)
	})

	t.Run("GetLimit_Nil", func(t *testing.T) {
		q := AdminGroup{
			Limit: nil,
		}

		result := q.GetLimit()

		require.Nil(t, result)
	})

	t.Run("GetLimit_Zero", func(t *testing.T) {
		limit := int64(0)
		q := AdminGroup{
			Limit: &limit,
		}

		result := q.GetLimit()

		require.NotNil(t, result)
		require.Equal(t, int64(0), *result)
	})

	t.Run("GetLimit_NegativeValue", func(t *testing.T) {
		limit := int64(-1)
		q := AdminGroup{
			Limit: &limit,
		}

		result := q.GetLimit()

		require.NotNil(t, result)
		require.Equal(t, int64(-1), *result)
	})
}

func TestAdminGroup_GetOffset(t *testing.T) {
	t.Run("GetOffset_WithValue", func(t *testing.T) {
		offset := int64(20)
		q := AdminGroup{
			Offset: &offset,
		}

		result := q.GetOffset()

		require.NotNil(t, result)
		require.Equal(t, int64(20), *result)
	})

	t.Run("GetOffset_Nil", func(t *testing.T) {
		q := AdminGroup{
			Offset: nil,
		}

		result := q.GetOffset()

		require.Nil(t, result)
	})

	t.Run("GetOffset_Zero", func(t *testing.T) {
		offset := int64(0)
		q := AdminGroup{
			Offset: &offset,
		}

		result := q.GetOffset()

		require.NotNil(t, result)
		require.Equal(t, int64(0), *result)
	})

	t.Run("GetOffset_LargeValue", func(t *testing.T) {
		offset := int64(1000000)
		q := AdminGroup{
			Offset: &offset,
		}

		result := q.GetOffset()

		require.NotNil(t, result)
		require.Equal(t, int64(1000000), *result)
	})
}

func TestAdminGroup_StructInitialization(t *testing.T) {
	t.Run("EmptyStruct", func(t *testing.T) {
		q := AdminGroup{}

		require.Nil(t, q.Search)
		require.Empty(t, q.ProductIDs)
		require.Nil(t, q.Type)
		require.Nil(t, q.Sort)
		require.Nil(t, q.Order)
		require.Nil(t, q.Limit)
		require.Nil(t, q.Offset)
		require.Nil(t, q.GetLimit())
		require.Nil(t, q.GetOffset())
	})

	t.Run("FullyPopulatedStruct", func(t *testing.T) {
		search := "test search"
		productIDs := []int64{1, 2, 3}
		groupType := "system"
		sort := "name"
		order := "ascend"
		limit := int64(50)
		offset := int64(100)

		q := AdminGroup{
			Search:     &search,
			ProductIDs: productIDs,
			Type:       &groupType,
			Sort:       &sort,
			Order:      &order,
			Limit:      &limit,
			Offset:     &offset,
		}

		require.NotNil(t, q.Search)
		require.Equal(t, "test search", *q.Search)
		require.Len(t, q.ProductIDs, 3)
		require.Equal(t, []int64{1, 2, 3}, q.ProductIDs)
		require.NotNil(t, q.Type)
		require.Equal(t, "system", *q.Type)
		require.NotNil(t, q.Sort)
		require.Equal(t, "name", *q.Sort)
		require.NotNil(t, q.Order)
		require.Equal(t, "ascend", *q.Order)
		require.NotNil(t, q.GetLimit())
		require.Equal(t, int64(50), *q.GetLimit())
		require.NotNil(t, q.GetOffset())
		require.Equal(t, int64(100), *q.GetOffset())
	})

	t.Run("PartiallyPopulatedStruct", func(t *testing.T) {
		search := "partial"
		limit := int64(25)

		q := AdminGroup{
			Search: &search,
			Limit:  &limit,
		}

		require.NotNil(t, q.Search)
		require.Equal(t, "partial", *q.Search)
		require.Empty(t, q.ProductIDs)
		require.Nil(t, q.Type)
		require.Nil(t, q.Sort)
		require.Nil(t, q.Order)
		require.NotNil(t, q.GetLimit())
		require.Equal(t, int64(25), *q.GetLimit())
		require.Nil(t, q.GetOffset())
	})
}

func TestAdminGroup_ProductIDs(t *testing.T) {
	t.Run("ProductIDs_EmptySlice", func(t *testing.T) {
		q := AdminGroup{
			ProductIDs: []int64{},
		}

		require.NotNil(t, q.ProductIDs)
		require.Empty(t, q.ProductIDs)
		require.Len(t, q.ProductIDs, 0)
	})

	t.Run("ProductIDs_SingleValue", func(t *testing.T) {
		q := AdminGroup{
			ProductIDs: []int64{42},
		}

		require.Len(t, q.ProductIDs, 1)
		require.Equal(t, int64(42), q.ProductIDs[0])
	})

	t.Run("ProductIDs_MultipleValues", func(t *testing.T) {
		q := AdminGroup{
			ProductIDs: []int64{1, 5, 10, 100},
		}

		require.Len(t, q.ProductIDs, 4)
		require.Equal(t, int64(1), q.ProductIDs[0])
		require.Equal(t, int64(5), q.ProductIDs[1])
		require.Equal(t, int64(10), q.ProductIDs[2])
		require.Equal(t, int64(100), q.ProductIDs[3])
	})

	t.Run("ProductIDs_DuplicateValues", func(t *testing.T) {
		q := AdminGroup{
			ProductIDs: []int64{1, 1, 2, 2, 3},
		}

		require.Len(t, q.ProductIDs, 5)
		require.Contains(t, q.ProductIDs, int64(1))
		require.Contains(t, q.ProductIDs, int64(2))
		require.Contains(t, q.ProductIDs, int64(3))
	})
}

func TestAdminGroup_StringFields(t *testing.T) {
	t.Run("Search_EmptyString", func(t *testing.T) {
		search := ""
		q := AdminGroup{
			Search: &search,
		}

		require.NotNil(t, q.Search)
		require.Equal(t, "", *q.Search)
	})

	t.Run("Search_WithSpaces", func(t *testing.T) {
		search := "  test search  "
		q := AdminGroup{
			Search: &search,
		}

		require.NotNil(t, q.Search)
		require.Equal(t, "  test search  ", *q.Search)
	})

	t.Run("Type_SystemType", func(t *testing.T) {
		groupType := "system"
		q := AdminGroup{
			Type: &groupType,
		}

		require.NotNil(t, q.Type)
		require.Equal(t, "system", *q.Type)
	})

	t.Run("Type_CustomType", func(t *testing.T) {
		groupType := "custom"
		q := AdminGroup{
			Type: &groupType,
		}

		require.NotNil(t, q.Type)
		require.Equal(t, "custom", *q.Type)
	})

	t.Run("Sort_ValidFields", func(t *testing.T) {
		testCases := []string{"name", "type", "user_count", "role_count", "product"}

		for _, sortField := range testCases {
			t.Run("Sort_"+sortField, func(t *testing.T) {
				sort := sortField
				q := AdminGroup{
					Sort: &sort,
				}

				require.NotNil(t, q.Sort)
				require.Equal(t, sortField, *q.Sort)
			})
		}
	})

	t.Run("Order_ValidValues", func(t *testing.T) {
		testCases := []string{"ascend", "descend", "asc", "desc"}

		for _, orderValue := range testCases {
			t.Run("Order_"+orderValue, func(t *testing.T) {
				order := orderValue
				q := AdminGroup{
					Order: &order,
				}

				require.NotNil(t, q.Order)
				require.Equal(t, orderValue, *q.Order)
			})
		}
	})
}

func TestAdminGroup_BoundaryValues(t *testing.T) {
	t.Run("MaxInt64Values", func(t *testing.T) {
		const maxInt64 int64 = 9223372036854775807
		limit := maxInt64
		offset := maxInt64

		q := AdminGroup{
			Limit:  &limit,
			Offset: &offset,
		}

		require.Equal(t, maxInt64, *q.GetLimit())
		require.Equal(t, maxInt64, *q.GetOffset())
	})

	t.Run("MinInt64Values", func(t *testing.T) {
		const minInt64 int64 = -9223372036854775808
		limit := minInt64
		offset := minInt64

		q := AdminGroup{
			Limit:  &limit,
			Offset: &offset,
		}

		require.Equal(t, minInt64, *q.GetLimit())
		require.Equal(t, minInt64, *q.GetOffset())
	})
}
