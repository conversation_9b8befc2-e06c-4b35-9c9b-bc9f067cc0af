package query

// AdminGroup contains the parameters for querying the list of groups by administrators
type AdminGroup struct {
	// Filtering parameters
	Search     *string // Search text
	ProductIDs []int64 // Filter by product IDs
	Type       *string
	Status     *string // Filter by role status ("active", "archive"")

	// Sorting parameters
	Sort  *string // Field for sorting ("name", "email", etc.)
	Order *string // Sort order ("ascend", "descend")

	// Pagination parameters
	Limit  *int64 // Number of items per page
	Offset *int64 // Offset (number of items to skip)
}

func (q AdminGroup) GetLimit() *int64 {
	return q.Limit
}

func (q AdminGroup) GetOffset() *int64 {
	return q.Offset
}
