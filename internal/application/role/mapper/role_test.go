package mapper

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemrole"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/aggregate"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestToV1DTOsRolesWithCategoriesFromEntities(t *testing.T) {
	roles := []roleentity.RoleWithCategoryStates{
		{
			RoleID:           123,
			RoleName:         "Admin Role",
			IsSystem:         true,
			GroupCount:       5,
			ParticipantCount: 10,
			UserCount:        15,
			CategoryStates: []roleentity.RoleCategoryStates{
				{
					CategoryID:   1,
					CategoryName: "Category 1",
					IsActive:     true,
				},
			},
		},
	}

	result := ToV1DTOsRolesWithCategoriesFromEntities(roles)

	require.Len(t, result, 1)
	assert.Equal(t, int64(123), result[0].ID)
	assert.Equal(t, "Admin Role", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.Equal(t, int64(5), result[0].GroupCount)
	assert.Equal(t, int64(10), result[0].ParticipantCount)
	assert.Equal(t, int64(15), result[0].UserCount)
	require.Len(t, result[0].Categories, 1)
	assert.Equal(t, int64(1), result[0].Categories[0].ID)
	assert.Equal(t, "Category 1", result[0].Categories[0].Name)
	assert.True(t, result[0].Categories[0].IsActive)
}

func TestToV1DTOsRolesWithCategoriesFromEntities_Empty(t *testing.T) {
	var roles []roleentity.RoleWithCategoryStates

	result := ToV1DTOsRolesWithCategoriesFromEntities(roles)

	assert.Empty(t, result)
}

func TestToEntitiesRolesWithCategoriesFromV1DTOs(t *testing.T) {
	roles := []adminrole.RoleViewAdminEntityV1DTO{
		{
			ID:               123,
			Name:             "Admin Role",
			Type:             constants.SystemType,
			GroupCount:       5,
			ParticipantCount: 10,
			UserCount:        15,
			Categories: []adminrole.CategoryWithCheckedV1DTO{
				{
					ID:       1,
					Name:     "Category 1",
					IsActive: true,
				},
			},
		},
	}

	result := ToEntitiesRolesWithCategoriesFromV1DTOs(roles)

	require.Len(t, result, 1)
	assert.Equal(t, int64(123), result[0].RoleID)
	assert.Equal(t, "Admin Role", result[0].RoleName)
	assert.True(t, result[0].IsSystem)
	assert.Equal(t, int64(5), result[0].GroupCount)
	assert.Equal(t, int64(10), result[0].ParticipantCount)
	assert.Equal(t, int64(15), result[0].UserCount)
	require.Len(t, result[0].CategoryStates, 1)
	assert.Equal(t, int64(1), result[0].CategoryStates[0].CategoryID)
	assert.Equal(t, "Category 1", result[0].CategoryStates[0].CategoryName)
	assert.True(t, result[0].CategoryStates[0].IsActive)
}

func TestToV1DTOCategoryWithCheckedFromEntities(t *testing.T) {
	categories := []roleentity.RoleCategoryStates{
		{
			CategoryID:   1,
			CategoryName: "Category 1",
			IsActive:     true,
		},
		{
			CategoryID:   2,
			CategoryName: "Category 2",
			IsActive:     false,
		},
	}

	result := ToV1DTOCategoryWithCheckedFromEntities(categories)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Category 1", result[0].Name)
	assert.True(t, result[0].IsActive)
	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Category 2", result[1].Name)
	assert.False(t, result[1].IsActive)
}

func TestToEntitiesRoleCategoryStatesFromV1DTOs(t *testing.T) {
	categories := []adminrole.CategoryWithCheckedV1DTO{
		{
			ID:       1,
			Name:     "Category 1",
			IsActive: true,
		},
		{
			ID:       2,
			Name:     "Category 2",
			IsActive: false,
		},
	}

	result := ToEntitiesRoleCategoryStatesFromV1DTOs(categories)

	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].CategoryID)
	assert.Equal(t, "Category 1", result[0].CategoryName)
	assert.True(t, result[0].IsActive)
	assert.Equal(t, int64(2), result[1].CategoryID)
	assert.Equal(t, "Category 2", result[1].CategoryName)
	assert.False(t, result[1].IsActive)
}

func TestToEntityRoleWithPermissionsFromV1DTOCreateSystem(t *testing.T) {
	dto := systemrole.RoleCreateV1DTO{
		Name: "System Role",
		Permissions: []systemrole.PermissionV1DTO{
			{
				Name: "users",
				Methods: []systemrole.MethodV1DTO{
					{Name: "GET"},
					{Name: "POST"},
				},
			},
		},
	}

	result := ToEntityRoleWithPermissionsFromV1DTOCreateSystem(dto, true)

	assert.Equal(t, "System Role", result.Name)
	assert.True(t, result.IsSystem)
	require.Len(t, result.Permissions, 2)
	assert.Equal(t, "users", result.Permissions[0].Name)
	assert.Equal(t, "GET", result.Permissions[0].Method)
	assert.Equal(t, "users", result.Permissions[1].Name)
	assert.Equal(t, "POST", result.Permissions[1].Method)
}

func TestToEntityRoleWithPermissionsFromV1DTOCreateSystem_NoPermissions(t *testing.T) {
	dto := systemrole.RoleCreateV1DTO{
		Name:        "System Role",
		Permissions: []systemrole.PermissionV1DTO{},
	}

	result := ToEntityRoleWithPermissionsFromV1DTOCreateSystem(dto, false)

	assert.Equal(t, "System Role", result.Name)
	assert.Nil(t, result.ProductID)
	assert.False(t, result.IsSystem)
	assert.Empty(t, result.Permissions)
}

func TestToEntityRoleFormAdminRoleCreateV1DTO(t *testing.T) {
	productID := int64(100)
	dto := adminrole.AdminRoleCreateV1DTO{
		Name:      "Admin Role",
		Type:      constants.SystemType,
		ProductID: &productID,
		Permissions: []adminrole.PermissionV1DTO{
			{
				Name: "users",
				Methods: []adminrole.MethodV1DTO{
					{Name: "GET"},
					{Name: "POST"},
				},
			},
		},
	}

	role, permissions := ToEntityRoleFormAdminRoleCreateV1DTO(dto)

	assert.Equal(t, "Admin Role", role.Name)
	assert.Equal(t, &productID, role.ProductID)
	assert.True(t, role.IsSystem)
	require.Len(t, permissions, 2)
	assert.Equal(t, "users", permissions[0].Name)
	assert.Equal(t, "GET", permissions[0].Method)
	assert.Equal(t, "users", permissions[1].Name)
	assert.Equal(t, "POST", permissions[1].Method)
}

func TestToEntityRoleFormAdminRoleCreateV1DTO_CustomType(t *testing.T) {
	dto := adminrole.AdminRoleCreateV1DTO{
		Name:        "Custom Role",
		Type:        constants.CustomType,
		Permissions: []adminrole.PermissionV1DTO{},
	}

	role, permissions := ToEntityRoleFormAdminRoleCreateV1DTO(dto)

	assert.Equal(t, "Custom Role", role.Name)
	assert.Nil(t, role.ProductID)
	assert.False(t, role.IsSystem)
	assert.Empty(t, permissions)
}

func TestToEntityRoleFromV1DTO(t *testing.T) {
	name := "Updated Role"
	participantIDs := []int64{1, 2, 3}
	groupIDs := []int64{4, 5, 6}
	userIDs := []int64{7, 8, 9}
	permissions := []productrole.PermissionV1DTO{
		{
			Name: "products",
			Methods: []productrole.MethodV1DTO{
				{Name: "GET"},
				{Name: "DELETE"},
			},
		},
	}

	dto := productrole.RoleUpdateV1DTO{
		Name:           &name,
		ParticipantIDs: &participantIDs,
		GroupIDs:       &groupIDs,
		UserIDs:        &userIDs,
		Permissions:    &permissions,
	}
	roleID := int64(123)
	roleType := constants.SystemType

	result := ToEntityRoleFromV1DTO(dto, roleID, roleType)

	assert.Equal(t, roleID, result.ID)
	assert.Equal(t, "Updated Role", result.Name)
	assert.True(t, result.IsSystem)
	require.NotNil(t, result.ParticipantIDs)
	assert.Equal(t, participantIDs, *result.ParticipantIDs)
	require.NotNil(t, result.GroupIDs)
	assert.Equal(t, groupIDs, *result.GroupIDs)
	require.NotNil(t, result.UserIDs)
	assert.Equal(t, userIDs, *result.UserIDs)
	require.Len(t, result.Permissions, 2)
	assert.Equal(t, "products", result.Permissions[0].Name)
	assert.Equal(t, "GET", result.Permissions[0].Method)
	assert.Equal(t, "products", result.Permissions[1].Name)
	assert.Equal(t, "DELETE", result.Permissions[1].Method)
}

func TestToEntityRoleFromV1DTO_NilValues(t *testing.T) {
	dto := productrole.RoleUpdateV1DTO{}
	roleID := int64(123)
	roleType := constants.CustomType

	result := ToEntityRoleFromV1DTO(dto, roleID, roleType)

	assert.Equal(t, roleID, result.ID)
	assert.Empty(t, result.Name)
	assert.False(t, result.IsSystem)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.GroupIDs)
	assert.Nil(t, result.UserIDs)
	assert.Empty(t, result.Permissions)
}

func TestToEntityRoleWithPermissionsFromV1DTOCreateCustom(t *testing.T) {
	productID := int64(200)
	dto := productrole.RoleCreateV1DTO{
		Name: "Custom Role",
		Permissions: []productrole.PermissionV1DTO{
			{
				Name: "roles",
				Methods: []productrole.MethodV1DTO{
					{Name: "PUT"},
				},
			},
		},
	}

	result := ToEntityRoleWithPermissionsFromV1DTOCreateCustom(dto, false, productID)

	assert.Equal(t, "Custom Role", result.Name)
	assert.Equal(t, &productID, result.ProductID)
	assert.False(t, result.IsSystem)
	require.Len(t, result.Permissions, 1)
	assert.Equal(t, "roles", result.Permissions[0].Name)
	assert.Equal(t, "PUT", result.Permissions[0].Method)
}

func TestToV1DTORoleShortFromEntityRoleWithStats(t *testing.T) {
	roles := []roleentity.RoleWithStats{
		{
			RoleID:           123,
			RoleName:         "Role 1",
			IsSystem:         true,
			GroupCount:       5,
			ParticipantCount: 10,
			UserCount:        15,
		},
		{
			RoleID:           456,
			RoleName:         "Role 2",
			IsSystem:         false,
			GroupCount:       3,
			ParticipantCount: 7,
			UserCount:        12,
		},
	}

	result := ToV1DTORoleShortFromEntityRoleWithStats(roles)

	require.Len(t, result, 2)
	assert.Equal(t, int64(123), result[0].ID)
	assert.Equal(t, "Role 1", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.Equal(t, int64(5), result[0].GroupCount)
	assert.Equal(t, int64(10), result[0].ParticipantCount)
	assert.Equal(t, int64(15), result[0].UserCount)

	assert.Equal(t, int64(456), result[1].ID)
	assert.Equal(t, "Role 2", result[1].Name)
	assert.Equal(t, constants.CustomType, result[1].Type)
	assert.Equal(t, int64(3), result[1].GroupCount)
	assert.Equal(t, int64(7), result[1].ParticipantCount)
	assert.Equal(t, int64(12), result[1].UserCount)
}

func TestToV1DTORoleShortFromEntityRoleWithStats_Empty(t *testing.T) {
	var roles []roleentity.RoleWithStats

	result := ToV1DTORoleShortFromEntityRoleWithStats(roles)

	assert.Empty(t, result)
}

func TestToV1DTORoleFullFromEntityRoleFull(t *testing.T) {
	participantIDs := []int64{1, 2, 3}
	userIDs := []int64{4, 5, 6}
	groupIDs := []int64{7, 8, 9}

	role := roleentity.RoleFull{
		ID:       123,
		Name:     "Full Role",
		IsSystem: true,
		Permissions: []permissionentity.Permission{
			{Name: "users", Method: "GET"},
			{Name: "users", Method: "POST"},
			{Name: "products", Method: "DELETE"},
		},
		ParticipantIDs: &participantIDs,
		UserIDs:        &userIDs,
		GroupIDs:       &groupIDs,
	}

	result := ToV1DTORoleFullFromEntityRoleFull(role)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Full Role", result.Name)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, &participantIDs, result.ParticipantIDs)
	assert.Equal(t, &userIDs, result.UserIDs)
	assert.Equal(t, &groupIDs, result.GroupIDs)
	require.Len(t, result.Permissions, 2)

	// Проверяем группировку пермишенов
	permissionMap := make(map[string][]string)
	for _, perm := range result.Permissions {
		for _, method := range perm.Methods {
			permissionMap[perm.Name] = append(permissionMap[perm.Name], method.Name)
		}
	}

	assert.Contains(t, permissionMap, "users")
	assert.Contains(t, permissionMap, "products")
	assert.Len(t, permissionMap["users"], 2)
	assert.Len(t, permissionMap["products"], 1)
}

func TestToV1DTORoleFullFromEntityRoleFull_NilValues(t *testing.T) {
	role := roleentity.RoleFull{
		ID:          123,
		Name:        "Simple Role",
		IsSystem:    false,
		Permissions: []permissionentity.Permission{},
	}

	result := ToV1DTORoleFullFromEntityRoleFull(role)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Simple Role", result.Name)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.UserIDs)
	assert.Nil(t, result.GroupIDs)
	assert.Empty(t, result.Permissions)
}

func TestToV1DTOFromEntitiesRoleWithStats(t *testing.T) {
	roles := []roleentity.RoleWithStats{
		{
			RoleID:           789,
			RoleName:         "Stats Role",
			IsSystem:         true,
			GroupCount:       2,
			ParticipantCount: 4,
			UserCount:        6,
		},
	}

	result := ToV1DTOFromEntitiesRoleWithStats(roles)

	require.Len(t, result, 1)
	assert.Equal(t, int64(789), result[0].ID)
	assert.Equal(t, "Stats Role", result[0].Name)
	assert.Equal(t, constants.SystemType, result[0].Type)
	assert.Equal(t, int64(2), result[0].GroupCount)
	assert.Equal(t, int64(4), result[0].ParticipantCount)
	assert.Equal(t, int64(6), result[0].UserCount)
}

func TestToQueryAdminRolesFromGetRolesAsAdminParams(t *testing.T) {
	search := "test search"
	productIDs := []int64{1, 2, 3}
	roleType := adminrole.GetRolesAsAdminParamsTypeSystem
	sortBy := adminrole.Name
	order := adminrole.Ascend
	status := adminrole.Active
	limit := int64(20)
	offset := int64(5)

	params := adminrole.GetRolesAsAdminParams{
		Search:     &search,
		ProductIDs: &productIDs,
		Type:       &roleType,
		Sort:       &sortBy,
		Order:      &order,
		Status:     &status,
		Limit:      &limit,
		Offset:     &offset,
	}

	result := ToQueryAdminRolesFromGetRolesAsAdminParams(params)

	assert.Equal(t, &search, result.Search)
	assert.Equal(t, productIDs, result.ProductIDs)
	assert.Equal(t, (*string)(&roleType), result.Type)
	assert.Equal(t, (*string)(&sortBy), result.Sort)
	assert.Equal(t, (*string)(&order), result.Order)
	assert.Equal(t, (*string)(&status), result.Status)
	assert.Equal(t, &limit, result.Limit)
	assert.Equal(t, &offset, result.Offset)
}

func TestToQueryAdminRolesFromGetRolesAsAdminParams_NilProductIDs(t *testing.T) {
	params := adminrole.GetRolesAsAdminParams{}

	result := ToQueryAdminRolesFromGetRolesAsAdminParams(params)

	assert.Nil(t, result.Search)
	assert.Empty(t, result.ProductIDs)
	assert.Nil(t, result.Type)
	assert.Nil(t, result.Sort)
	assert.Nil(t, result.Order)
	assert.Nil(t, result.Status)
	assert.Nil(t, result.Limit)
	assert.Nil(t, result.Offset)
}

func TestToV1DTOAdminRoleCollectionFromModels(t *testing.T) {
	iid := "P001"
	product := &productentity.ProductBasic{
		ID:       100,
		IID:      &iid,
		Name:     "Product 1",
		TechName: "product1",
	}

	adminRoles := []roleentity.AdminRole{
		{
			ID:               123,
			Name:             "Admin Role 1",
			IsActive:         true,
			IsSystem:         true,
			Product:          product,
			UserCount:        10,
			ParticipantCount: 5,
			GroupCount:       3,
		},
	}

	paginatedResult := entity.PaginatedResult[roleentity.AdminRole]{
		Items:  adminRoles,
		Total:  1,
		Offset: 0,
		Limit:  10,
	}

	result := ToV1DTOAdminRoleCollectionFromModels(paginatedResult)

	require.Len(t, result.Items, 1)
	assert.Equal(t, int64(123), result.Items[0].ID)
	assert.Equal(t, "Admin Role 1", result.Items[0].Name)
	assert.True(t, result.Items[0].IsActive)
	assert.Equal(t, constants.SystemType, result.Items[0].Type)
	require.NotNil(t, result.Items[0].Product)
	assert.Equal(t, int64(100), result.Items[0].Product.ID)
	assert.Equal(t, int64(10), result.Items[0].UserCount)
	assert.Equal(t, int64(3), result.Items[0].GroupCount)
	assert.Equal(t, int64(1), result.Meta.Total)
	assert.Equal(t, int64(0), result.Meta.Offset)
	assert.Equal(t, int64(10), result.Meta.Limit)
}

func TestToV1DTOAdminRoleFromModelAdminRole(t *testing.T) {
	iid := "P001"
	product := &productentity.ProductBasic{
		ID:       100,
		IID:      &iid,
		Name:     "Product 1",
		TechName: "product1",
	}

	adminRole := roleentity.AdminRole{
		ID:               456,
		Name:             "Test Admin Role",
		IsActive:         false,
		IsSystem:         false,
		Product:          product,
		UserCount:        7,
		ParticipantCount: 3,
		GroupCount:       2,
	}

	result := ToV1DTOAdminRoleFromModelAdminRole(adminRole)

	assert.Equal(t, int64(456), result.ID)
	assert.Equal(t, "Test Admin Role", result.Name)
	assert.False(t, result.IsActive)
	assert.Equal(t, constants.CustomType, result.Type)
	require.NotNil(t, result.Product)
	assert.Equal(t, int64(100), result.Product.ID)
	assert.Equal(t, "P001", *result.Product.IID)
	assert.Equal(t, "Product 1", result.Product.Name)
	assert.Equal(t, "product1", result.Product.TechName)
	assert.Equal(t, int64(7), result.UserCount)
	assert.Equal(t, int64(2), result.GroupCount)
}

func TestToV1DTOAdminRoleFromModelAdminRole_NilProduct(t *testing.T) {
	adminRole := roleentity.AdminRole{
		ID:               456,
		Name:             "Test Admin Role",
		IsActive:         true,
		IsSystem:         true,
		UserCount:        7,
		ParticipantCount: 3,
		GroupCount:       2,
	}

	result := ToV1DTOAdminRoleFromModelAdminRole(adminRole)

	assert.Equal(t, int64(456), result.ID)
	assert.Equal(t, "Test Admin Role", result.Name)
	assert.True(t, result.IsActive)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Nil(t, result.Product)
	assert.Equal(t, int64(7), result.UserCount)
	assert.Equal(t, int64(2), result.GroupCount)
}

func TestToV1DTORoleWithDetailsFromAggregateRoleWithDetails(t *testing.T) {
	now := time.Now()
	productID := int64(100)

	users := []userentity.UserWithProduct{
		{
			ID:       1,
			Email:    "<EMAIL>",
			FullName: "John Doe",
			Product: &productentity.Product{
				ID:       productID,
				IID:      "P001",
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:       2,
			Email:    "<EMAIL>",
			FullName: "Jane Smith",
		},
	}

	groups := []groupentity.GroupWithProduct{
		{
			ID:        1,
			Name:      "Admin Group",
			IsSystem:  true,
			ActiveFlg: true,
			Product: &productentity.Product{
				ID:       productID,
				IID:      "P001",
				Name:     "Product 1",
				TechName: "product1",
			},
		},
		{
			ID:        2,
			Name:      "User Group",
			IsSystem:  false,
			ActiveFlg: false,
		},
	}

	permissions := []permissionentity.Permission{
		{Name: "users", Method: "GET"},
		{Name: "users", Method: "POST"},
	}

	role := aggregate.RoleWithDetails{
		Role: roleentity.Role{
			ID:        123,
			Name:      "Detailed Role",
			IsSystem:  true,
			ActiveFlg: true,
			CreatedAt: now,
		},
		Product: &productentity.Product{
			ID:       productID,
			IID:      "P001",
			Name:     "Product 1",
			TechName: "product1",
		},
		Users:       users,
		Groups:      groups,
		Permissions: permissions,
	}

	result := ToV1DTORoleWithDetailsFromAggregateRoleWithDetails(role)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Detailed Role", result.Name)
	assert.True(t, result.IsActive)
	assert.Equal(t, constants.SystemType, result.Type)
	assert.Equal(t, now, result.CreatedAt)

	// Проверяем Product
	require.NotNil(t, result.Product)
	assert.Equal(t, productID, result.Product.ID)
	assert.Equal(t, "P001", *result.Product.IID)
	assert.Equal(t, "Product 1", result.Product.Name)
	assert.Equal(t, "product1", result.Product.TechName)

	// Проверяем Users
	require.Len(t, result.Users, 2)
	assert.Equal(t, int64(1), result.Users[0].ID)
	assert.Equal(t, "<EMAIL>", result.Users[0].Email)
	assert.Equal(t, "John Doe", result.Users[0].FullName)
	require.NotNil(t, result.Users[0].Product)
	assert.Equal(t, productID, result.Users[0].Product.ID)

	assert.Equal(t, int64(2), result.Users[1].ID)
	assert.Equal(t, "<EMAIL>", result.Users[1].Email)
	assert.Equal(t, "Jane Smith", result.Users[1].FullName)
	assert.Nil(t, result.Users[1].Product)

	// Проверяем Groups
	require.Len(t, result.Groups, 2)
	assert.Equal(t, int64(1), result.Groups[0].ID)
	assert.Equal(t, "Admin Group", result.Groups[0].Name)
	assert.Equal(t, constants.SystemType, result.Groups[0].Type)
	assert.True(t, result.Groups[0].IsActive)
	require.NotNil(t, result.Groups[0].Product)

	assert.Equal(t, int64(2), result.Groups[1].ID)
	assert.Equal(t, "User Group", result.Groups[1].Name)
	assert.Equal(t, constants.CustomType, result.Groups[1].Type)
	assert.False(t, result.Groups[1].IsActive)
	assert.Nil(t, result.Groups[1].Product)

	// Проверяем Permissions
	require.Len(t, result.Permissions, 1)
	assert.Equal(t, "users", result.Permissions[0].Name)
	require.Len(t, result.Permissions[0].Methods, 2)
}

func TestToV1DTORoleWithDetailsFromAggregateRoleWithDetails_NilProduct(t *testing.T) {
	now := time.Now()

	role := aggregate.RoleWithDetails{
		Role: roleentity.Role{
			ID:        123,
			Name:      "Simple Role",
			IsSystem:  false,
			ActiveFlg: false,
			CreatedAt: now,
		},
		Users:       []userentity.UserWithProduct{},
		Groups:      []groupentity.GroupWithProduct{},
		Permissions: []permissionentity.Permission{},
	}

	result := ToV1DTORoleWithDetailsFromAggregateRoleWithDetails(role)

	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Simple Role", result.Name)
	assert.False(t, result.IsActive)
	assert.Equal(t, constants.CustomType, result.Type)
	assert.Nil(t, result.Product)
	assert.Empty(t, result.Users)
	assert.Empty(t, result.Groups)
	assert.Empty(t, result.Permissions)
}

func TestToV1DTOPermissions(t *testing.T) {
	perms := []permissionentity.Permission{
		{Name: "users", Method: "GET"},
		{Name: "users", Method: "POST"},
		{Name: "products", Method: "DELETE"},
	}

	result := toV1DTOPermissions(perms)

	require.Len(t, result, 2)

	// Проверяем группировку пермишенов
	permissionMap := make(map[string][]string)
	for _, perm := range result {
		for _, method := range perm.Methods {
			permissionMap[perm.Name] = append(permissionMap[perm.Name], method.Name)
		}
	}

	assert.Contains(t, permissionMap, "users")
	assert.Contains(t, permissionMap, "products")
	assert.Len(t, permissionMap["users"], 2)
	assert.Len(t, permissionMap["products"], 1)
}

func TestToV1DTOPermissions_Empty(t *testing.T) {
	var perms []permissionentity.Permission

	result := toV1DTOPermissions(perms)

	assert.Empty(t, result)
}

func TestToEntitiesSystemRoleFromV1DTO(t *testing.T) {
	name := "System Role Update"
	participantIDs := []int64{1, 2, 3}
	groupIDs := []int64{4, 5, 6}
	userIDs := []int64{7, 8, 9}
	permissions := []systemrole.PermissionV1DTO{
		{
			Name: "roles",
			Methods: []systemrole.MethodV1DTO{
				{Name: "PUT"},
				{Name: "DELETE"},
			},
		},
	}

	dto := systemrole.RoleUpdateV1DTO{
		Name:           &name,
		ParticipantIDs: &participantIDs,
		GroupIDs:       &groupIDs,
		UserIDs:        &userIDs,
		Permissions:    &permissions,
	}
	roleID := int64(789)

	result := ToEntitiesSystemRoleFromV1DTO(dto, roleID, true)

	assert.Equal(t, roleID, result.ID)
	assert.Equal(t, "System Role Update", result.Name)
	assert.True(t, result.IsSystem)
	require.NotNil(t, result.ParticipantIDs)
	assert.Equal(t, participantIDs, *result.ParticipantIDs)
	require.NotNil(t, result.GroupIDs)
	assert.Equal(t, groupIDs, *result.GroupIDs)
	require.NotNil(t, result.UserIDs)
	assert.Equal(t, userIDs, *result.UserIDs)
	require.Len(t, result.Permissions, 2)
	assert.Equal(t, "roles", result.Permissions[0].Name)
	assert.Equal(t, "PUT", result.Permissions[0].Method)
	assert.Equal(t, "roles", result.Permissions[1].Name)
	assert.Equal(t, "DELETE", result.Permissions[1].Method)
}

func TestToEntitiesSystemRoleFromV1DTO_NilValues(t *testing.T) {
	dto := systemrole.RoleUpdateV1DTO{}
	roleID := int64(789)

	result := ToEntitiesSystemRoleFromV1DTO(dto, roleID, false)

	assert.Equal(t, roleID, result.ID)
	assert.Empty(t, result.Name)
	assert.False(t, result.IsSystem)
	assert.Nil(t, result.ParticipantIDs)
	assert.Nil(t, result.GroupIDs)
	assert.Nil(t, result.UserIDs)
	assert.Empty(t, result.Permissions)
}

func TestToAdminRoleUpdateDataFromV1DTO(t *testing.T) {
	name := "Updated Admin Role"
	isActive := false
	users := []adminrole.UserProductLinkV1DTO{
		{ID: 1, ProductID: nil},
		{ID: 2, ProductID: func() *int64 { id := int64(100); return &id }()},
	}
	groups := []adminrole.GroupIDV1DTO{
		{ID: 1},
		{ID: 2},
	}
	permissions := []adminrole.PermissionV1DTO{
		{
			Name: "groups",
			Methods: []adminrole.MethodV1DTO{
				{Name: "GET"},
				{Name: "POST"},
			},
		},
	}

	dto := adminrole.AdminRoleUpdateV1DTO{
		Name:        &name,
		IsActive:    &isActive,
		Users:       &users,
		Groups:      &groups,
		Permissions: &permissions,
	}
	roleID := int64(999)

	result := ToAdminRoleUpdateDataFromV1DTO(roleID, dto)

	assert.Equal(t, roleID, result.RoleUpdateData.ID)
	assert.Equal(t, &name, result.RoleUpdateData.Name)
	assert.Equal(t, &isActive, result.RoleUpdateData.ActiveFlg)

	require.NotNil(t, result.UsersForUpdate)
	require.Len(t, *result.UsersForUpdate, 2)
	assert.Equal(t, int64(1), (*result.UsersForUpdate)[0].UserID)
	assert.Nil(t, (*result.UsersForUpdate)[0].ProductID)
	assert.Equal(t, int64(2), (*result.UsersForUpdate)[1].UserID)
	assert.Equal(t, int64(100), *(*result.UsersForUpdate)[1].ProductID)

	require.NotNil(t, result.GroupsForUpdate)
	require.Len(t, *result.GroupsForUpdate, 2)
	assert.Equal(t, int64(1), (*result.GroupsForUpdate)[0].ID)
	assert.Equal(t, int64(2), (*result.GroupsForUpdate)[1].ID)

	require.NotNil(t, result.Permissions)
	require.Len(t, *result.Permissions, 2)
	assert.Equal(t, "groups", (*result.Permissions)[0].Name)
	assert.Equal(t, "GET", (*result.Permissions)[0].Method)
	assert.Equal(t, "groups", (*result.Permissions)[1].Name)
	assert.Equal(t, "POST", (*result.Permissions)[1].Method)
}

func TestToAdminRoleUpdateDataFromV1DTO_NilValues(t *testing.T) {
	dto := adminrole.AdminRoleUpdateV1DTO{}
	roleID := int64(999)

	result := ToAdminRoleUpdateDataFromV1DTO(roleID, dto)

	assert.Equal(t, roleID, result.RoleUpdateData.ID)
	assert.Nil(t, result.RoleUpdateData.Name)
	assert.Nil(t, result.RoleUpdateData.ActiveFlg)
	assert.Nil(t, result.UsersForUpdate)
	assert.Nil(t, result.GroupsForUpdate)
	assert.Nil(t, result.Permissions)
}

func TestToAdminRoleUpdateDataFromV1DTO_EmptySlices(t *testing.T) {
	var users []adminrole.UserProductLinkV1DTO
	var groups []adminrole.GroupIDV1DTO
	var permissions []adminrole.PermissionV1DTO

	dto := adminrole.AdminRoleUpdateV1DTO{
		Users:       &users,
		Groups:      &groups,
		Permissions: &permissions,
	}
	roleID := int64(999)

	result := ToAdminRoleUpdateDataFromV1DTO(roleID, dto)

	assert.Equal(t, roleID, result.RoleUpdateData.ID)
	require.NotNil(t, result.UsersForUpdate)
	assert.Empty(t, *result.UsersForUpdate)
	require.NotNil(t, result.GroupsForUpdate)
	assert.Empty(t, *result.GroupsForUpdate)
	require.NotNil(t, result.Permissions)
	assert.Empty(t, *result.Permissions)
}
