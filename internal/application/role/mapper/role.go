package mapper

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemrole"
	appquery "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/aggregate"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func ToAdminRoleUpdateDataFromV1DTO(id int64, dto adminrole.AdminRoleUpdateV1DTO) aggregate.AdminRoleUpdate {
	var userProductLinks *[]userentity.UserProductLink
	if dto.Users != nil {
		links := make([]userentity.UserProductLink, len(*dto.Users))
		for i, user := range *dto.Users {
			var productID *int64
			if user.ProductID != nil {
				productID = user.ProductID
			}
			links[i] = userentity.UserProductLink{
				UserID:    user.ID,
				ProductID: productID,
			}
		}
		userProductLinks = &links
	}

	var permissions *[]permissionentity.Permission
	if dto.Permissions != nil {
		perms := make([]permissionentity.Permission, 0)
		for _, permDTO := range *dto.Permissions {
			for _, methodDTO := range permDTO.Methods {
				permission := permissionentity.Permission{
					Name:   permDTO.Name,
					Method: methodDTO.Name,
				}
				perms = append(perms, permission)
			}
		}
		permissions = &perms
	}

	var groupProductLinks *[]groupentity.GroupID
	if dto.Groups != nil {
		groups := make([]groupentity.GroupID, len(*dto.Groups))
		for i, group := range *dto.Groups {
			groups[i] = groupentity.GroupID{
				ID: group.ID,
			}
		}
		groupProductLinks = &groups
	}

	return aggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        id,
			Name:      dto.Name,
			ActiveFlg: dto.IsActive,
		},
		UsersForUpdate:  userProductLinks,
		GroupsForUpdate: groupProductLinks,
		Permissions:     permissions,
	}
}

func ToEntitiesRoleCategoryStatesFromV1DTOs(categories []adminrole.CategoryWithCheckedV1DTO) []roleentity.RoleCategoryStates {

	var result []roleentity.RoleCategoryStates

	for _, category := range categories {
		result = append(result, roleentity.RoleCategoryStates{
			CategoryID:   category.ID,
			CategoryName: category.Name,
			IsActive:     category.IsActive,
		})
	}

	return result
}

func ToEntitiesRolesWithCategoriesFromV1DTOs(roles []adminrole.RoleViewAdminEntityV1DTO) []roleentity.RoleWithCategoryStates {

	var result []roleentity.RoleWithCategoryStates

	for _, role := range roles {
		result = append(result, roleentity.RoleWithCategoryStates{
			RoleID:           role.ID,
			RoleName:         role.Name,
			IsSystem:         constants.ToBoolFromSystemType(role.Type),
			GroupCount:       role.GroupCount,
			ParticipantCount: role.ParticipantCount,
			UserCount:        role.UserCount,
			CategoryStates:   ToEntitiesRoleCategoryStatesFromV1DTOs(role.Categories),
		})
	}

	return result
}

func ToEntitiesSystemRoleFromV1DTO(dto systemrole.RoleUpdateV1DTO, roleID int64, roleType bool) roleentity.RoleFull {

	var participantIDs *[]int64
	if dto.ParticipantIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.ParticipantIDs...)
		participantIDs = &IDs
	}

	var groupIDs *[]int64
	if dto.GroupIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.GroupIDs...)
		groupIDs = &IDs
	}

	var userIDs *[]int64
	if dto.UserIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.UserIDs...)
		userIDs = &IDs
	}

	var permissions []permissionentity.Permission
	if dto.Permissions != nil {
		for _, perm := range *dto.Permissions {
			for _, method := range perm.Methods {
				permission := permissionentity.Permission{
					Name:   perm.Name,
					Method: method.Name,
				}
				permissions = append(permissions, permission)
			}
		}
	}

	var roleName string
	if dto.Name != nil {
		roleName = *dto.Name
	}

	return roleentity.RoleFull{
		ID:             roleID,
		Name:           roleName,
		IsSystem:       roleType,
		ParticipantIDs: participantIDs,
		GroupIDs:       groupIDs,
		UserIDs:        userIDs,
		Permissions:    permissions,
	}
}

func ToEntityRoleFormAdminRoleCreateV1DTO(dto adminrole.AdminRoleCreateV1DTO) (roleentity.Role, []permissionentity.Permission) {

	role := roleentity.Role{
		Name:      dto.Name,
		ProductID: dto.ProductID,
		IsSystem:  dto.Type == constants.SystemType,
	}

	perms := make([]permissionentity.Permission, 0)
	for _, perm := range dto.Permissions {
		for _, method := range perm.Methods {
			permission := permissionentity.Permission{
				Name:   perm.Name,
				Method: method.Name,
			}
			perms = append(perms, permission)
		}
	}

	return role, perms
}

func ToEntityRoleFromV1DTO(dto productrole.RoleUpdateV1DTO, roleID int64, roleType string) roleentity.RoleFull {

	var participantIDs *[]int64
	if dto.ParticipantIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.ParticipantIDs...)
		participantIDs = &IDs
	}

	var groupIDs *[]int64
	if dto.GroupIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.GroupIDs...)
		groupIDs = &IDs
	}

	var userIDs *[]int64
	if dto.UserIDs != nil {
		IDs := make([]int64, 0)
		IDs = append(IDs, *dto.UserIDs...)
		userIDs = &IDs
	}

	var permissions []permissionentity.Permission
	if dto.Permissions != nil {
		for _, perm := range *dto.Permissions {
			for _, method := range perm.Methods {
				permission := permissionentity.Permission{
					Name:   perm.Name,
					Method: method.Name,
				}
				permissions = append(permissions, permission)
			}
		}
	}

	var roleName string
	if dto.Name != nil {
		roleName = *dto.Name
	}

	return roleentity.RoleFull{
		ID:             roleID,
		Name:           roleName,
		IsSystem:       constants.ToBoolFromSystemType(roleType),
		ParticipantIDs: participantIDs,
		GroupIDs:       groupIDs,
		UserIDs:        userIDs,
		Permissions:    permissions,
	}
}

func ToEntityRoleWithPermissionsFromV1DTOCreateCustom(dto productrole.RoleCreateV1DTO, roleType bool, productID int64) roleentity.RoleWithPermissions {

	var permissions []permissionentity.Permission
	for _, perm := range dto.Permissions {
		for _, method := range perm.Methods {
			permission := permissionentity.Permission{
				Name:   perm.Name,
				Method: method.Name,
			}
			permissions = append(permissions, permission)
		}
	}

	return roleentity.RoleWithPermissions{
		Name:        dto.Name,
		ProductID:   &productID,
		IsSystem:    roleType,
		Permissions: permissions,
	}
}

func ToEntityRoleWithPermissionsFromV1DTOCreateSystem(dto systemrole.RoleCreateV1DTO, roleType bool) roleentity.RoleWithPermissions {

	var permissions []permissionentity.Permission
	for _, perm := range dto.Permissions {
		for _, method := range perm.Methods {
			permission := permissionentity.Permission{
				Name:   perm.Name,
				Method: method.Name,
			}
			permissions = append(permissions, permission)
		}
	}

	return roleentity.RoleWithPermissions{
		Name:        dto.Name,
		IsSystem:    roleType,
		Permissions: permissions,
	}
}

func ToQueryAdminRolesFromGetRolesAsAdminParams(params adminrole.GetRolesAsAdminParams) appquery.AdminRoles {

	productIDs := make([]int64, 0)
	if params.ProductIDs != nil {
		productIDs = *params.ProductIDs
	}

	query := appquery.AdminRoles{
		Search:     params.Search,
		ProductIDs: productIDs,
		Type:       (*string)(params.Type),
		Sort:       (*string)(params.Sort),
		Order:      (*string)(params.Order),
		Status:     (*string)(params.Status),
		Limit:      params.Limit,
		Offset:     params.Offset,
	}

	return query
}

func ToV1DTOAdminRoleCollectionFromModels(paginatedResult entity.PaginatedResult[roleentity.AdminRole]) adminrole.AdminRoleCollectionV1DTO {

	var items []adminrole.AdminRoleV1DTO
	for _, role := range paginatedResult.Items {
		items = append(items, ToV1DTOAdminRoleFromModelAdminRole(role))
	}

	return adminrole.AdminRoleCollectionV1DTO{
		Items: items,
		Meta: adminrole.PaginationV1DTO{
			Limit:  paginatedResult.Limit,
			Offset: paginatedResult.Offset,
			Total:  paginatedResult.Total,
		},
	}
}

func ToV1DTOAdminRoleFromModelAdminRole(role roleentity.AdminRole) adminrole.AdminRoleV1DTO {
	var product *adminrole.ProductBasicV1DTO
	if role.Product != nil {
		product = &adminrole.ProductBasicV1DTO{
			ID:       role.Product.ID,
			IID:      role.Product.IID,
			Name:     role.Product.Name,
			TechName: role.Product.TechName,
		}
	}

	return adminrole.AdminRoleV1DTO{
		ID:         role.ID,
		Name:       role.Name,
		IsActive:   role.IsActive,
		Type:       constants.IsSystemToConstant(role.IsSystem),
		Product:    product,
		UserCount:  role.UserCount,
		GroupCount: role.GroupCount,
	}
}

func ToV1DTOCategoryWithCheckedFromEntities(categories []roleentity.RoleCategoryStates) []adminrole.CategoryWithCheckedV1DTO {

	var result []adminrole.CategoryWithCheckedV1DTO

	for _, category := range categories {
		result = append(result, adminrole.CategoryWithCheckedV1DTO{
			ID:       category.CategoryID,
			Name:     category.CategoryName,
			IsActive: category.IsActive,
		})
	}

	return result
}

func ToV1DTOFromEntitiesRoleWithStats(roles []roleentity.RoleWithStats) []productrole.RoleShortV1DTO {

	var dto []productrole.RoleShortV1DTO
	for _, r := range roles {
		dto = append(dto, productrole.RoleShortV1DTO{
			ID:               r.RoleID,
			Name:             r.RoleName,
			Type:             constants.IsSystemToConstant(r.IsSystem),
			GroupCount:       r.GroupCount,
			ParticipantCount: r.ParticipantCount,
			UserCount:        r.UserCount,
		})
	}

	return dto
}

func ToV1DTORoleFullFromEntityRoleFull(role roleentity.RoleFull) productrole.RoleFullV1DTO {

	var dto productrole.RoleFullV1DTO
	dto.ID = role.ID
	dto.Name = role.Name
	dto.Type = constants.IsSystemToConstant(role.IsSystem)

	permMap := make(map[string][]string)
	for _, perm := range role.Permissions {
		permMap[perm.Name] = append(permMap[perm.Name], perm.Method)
	}

	for permName, methods := range permMap {
		var dtoPerm productrole.PermissionV1DTO
		dtoPerm.Label = constants.PermissionsRu[permName]
		dtoPerm.Name = permName
		for _, method := range methods {
			dtoPerm.Methods = append(dtoPerm.Methods,
				productrole.MethodV1DTO{
					Name: method,
				},
			)
		}
		dto.Permissions = append(dto.Permissions, dtoPerm)
	}

	if role.ParticipantIDs != nil {
		dto.ParticipantIDs = role.ParticipantIDs
	}

	if role.UserIDs != nil {
		dto.UserIDs = role.UserIDs
	}

	if role.GroupIDs != nil {
		dto.GroupIDs = role.GroupIDs
	}

	return dto
}

func ToV1DTORoleShortFromEntityRoleWithStats(roles []roleentity.RoleWithStats) []productrole.RoleShortV1DTO {

	var dto []productrole.RoleShortV1DTO
	for _, r := range roles {
		dto = append(dto, productrole.RoleShortV1DTO{
			ID:               r.RoleID,
			Name:             r.RoleName,
			Type:             constants.IsSystemToConstant(r.IsSystem),
			GroupCount:       r.GroupCount,
			ParticipantCount: r.ParticipantCount,
			UserCount:        r.UserCount,
		})
	}

	return dto
}

func ToV1DTORoleWithDetailsFromAggregateRoleWithDetails(role aggregate.RoleWithDetails) adminrole.RoleWithDetailsV1DTO {

	var (
		roleUserDTO  []adminrole.UserWithProductV1DTO
		roleGroupDTO []adminrole.GroupWithProductV1DTO
	)

	dto := adminrole.RoleWithDetailsV1DTO{
		ID:        role.Role.ID,
		Name:      role.Role.Name,
		IsActive:  role.Role.ActiveFlg,
		Type:      constants.IsSystemToConstant(role.Role.IsSystem),
		CreatedAt: role.Role.CreatedAt,
	}

	if role.Product != nil {
		dto.Product = &adminrole.ProductBasicV1DTO{
			ID:       role.Product.ID,
			IID:      &role.Product.IID,
			Name:     role.Product.Name,
			TechName: role.Product.TechName,
		}
	}

	for _, i := range role.Users {

		if i.Product != nil {

			p := i.Product

			productDTO := &adminrole.ProductBasicV1DTO{
				ID:       p.ID,
				IID:      &p.IID,
				Name:     p.Name,
				TechName: p.TechName,
			}

			userDTO := adminrole.UserWithProductV1DTO{
				ID:       i.ID,
				Email:    i.Email,
				FullName: i.FullName,
				Product:  productDTO,
			}

			roleUserDTO = append(roleUserDTO, userDTO)
		} else {
			userDTO := adminrole.UserWithProductV1DTO{
				ID:       i.ID,
				Email:    i.Email,
				FullName: i.FullName,
			}

			roleUserDTO = append(roleUserDTO, userDTO)
		}
	}

	dto.Users = roleUserDTO

	for _, i := range role.Groups {

		var productDTO *adminrole.ProductBasicV1DTO
		if i.Product != nil && i.Product.ID != 0 {
			productDTO = &adminrole.ProductBasicV1DTO{
				ID:       i.Product.ID,
				IID:      &i.Product.IID,
				Name:     i.Product.Name,
				TechName: i.Product.TechName,
			}
		}

		userDTO := adminrole.GroupWithProductV1DTO{
			ID:       i.ID,
			Type:     constants.IsSystemToConstant(i.IsSystem),
			IsActive: i.ActiveFlg,
			Name:     i.Name,
			Product:  productDTO,
		}

		roleGroupDTO = append(roleGroupDTO, userDTO)
	}

	dto.Groups = roleGroupDTO
	dto.Users = roleUserDTO
	dto.Permissions = toV1DTOPermissions(role.Permissions)

	return dto
}

func ToV1DTOsRolesWithCategoriesFromEntities(roles []roleentity.RoleWithCategoryStates) []adminrole.RoleViewAdminEntityV1DTO {

	var result []adminrole.RoleViewAdminEntityV1DTO

	for _, role := range roles {
		result = append(result, adminrole.RoleViewAdminEntityV1DTO{
			ID:               role.RoleID,
			Name:             role.RoleName,
			Type:             constants.IsSystemToConstant(role.IsSystem),
			GroupCount:       role.GroupCount,
			ParticipantCount: role.ParticipantCount,
			UserCount:        role.UserCount,
			Categories:       ToV1DTOCategoryWithCheckedFromEntities(role.CategoryStates),
		})
	}

	return result
}

func toV1DTOPermissions(perms []permissionentity.Permission) []adminrole.PermissionV1DTO {

	var permissions []adminrole.PermissionV1DTO

	permissionCombination := make(map[string][]adminrole.MethodV1DTO)
	for _, perm := range perms {
		permissionCombination[perm.Name] = append(permissionCombination[perm.Name], adminrole.MethodV1DTO{
			Name: perm.Method,
		})
	}

	for permName, methods := range permissionCombination {
		permissions = append(permissions, adminrole.PermissionV1DTO{
			Name:    permName,
			Label:   constants.PermissionsRu[permName],
			Methods: methods,
		})
	}

	return permissions
}
