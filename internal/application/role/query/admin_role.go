package query

// AdminRoles contains parameters for querying the list of roles by administrator.
type AdminRoles struct {
	// Filtering parameters
	Search     *string // Search text
	ProductIDs []int64 // Filter by product IDs
	Type       *string // Filter by role type ("system", "custom")
	Status     *string // Filter by role status ("active", "archive"")

	// Sorting parameters
	Sort  *string // Field for sorting ("name", "type", "product", "userCount", "groupCount")
	Order *string // Sort order ("ascend", "descend")

	// Pagination parameters
	Limit  *int64 // Number of items per page
	Offset *int64 // Offset (number of items to skip)
}

func (q AdminRoles) GetLimit() *int64 {
	return q.Limit
}

func (q AdminRoles) GetOffset() *int64 {
	return q.Offset
}
