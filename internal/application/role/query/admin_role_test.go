package query

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAdminRoles_GetLimit(t *testing.T) {
	tests := []struct {
		name     string
		query    AdminRoles
		expected *int64
	}{
		{
			name:     "nil limit",
			query:    AdminRoles{Limit: nil},
			expected: nil,
		},
		{
			name:     "with limit value",
			query:    AdminRoles{Limit: int64Ptr(10)},
			expected: int64Ptr(10),
		},
		{
			name:     "zero limit",
			query:    AdminRoles{Limit: int64Ptr(0)},
			expected: int64Ptr(0),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.query.GetLimit()
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestAdminRoles_GetOffset(t *testing.T) {
	tests := []struct {
		name     string
		query    AdminRoles
		expected *int64
	}{
		{
			name:     "nil offset",
			query:    AdminRoles{Offset: nil},
			expected: nil,
		},
		{
			name:     "with offset value",
			query:    AdminRoles{Offset: int64Ptr(20)},
			expected: int64Ptr(20),
		},
		{
			name:     "zero offset",
			query:    AdminRoles{Offset: int64Ptr(0)},
			expected: int64Ptr(0),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.query.GetOffset()
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestAdminRoles_AllFields(t *testing.T) {
	search := "test search"
	roleType := "system"
	status := "active"
	sort := "name"
	order := "ascend"
	limit := int64(10)
	offset := int64(20)
	productIDs := []int64{1, 2, 3}

	query := AdminRoles{
		Search:     &search,
		ProductIDs: productIDs,
		Type:       &roleType,
		Status:     &status,
		Sort:       &sort,
		Order:      &order,
		Limit:      &limit,
		Offset:     &offset,
	}

	// Test that all fields are properly set and accessible
	assert.Equal(t, &search, query.Search)
	assert.Equal(t, productIDs, query.ProductIDs)
	assert.Equal(t, &roleType, query.Type)
	assert.Equal(t, &status, query.Status)
	assert.Equal(t, &sort, query.Sort)
	assert.Equal(t, &order, query.Order)
	assert.Equal(t, &limit, query.GetLimit())
	assert.Equal(t, &offset, query.GetOffset())
}

func TestAdminRoles_EmptyFields(t *testing.T) {
	query := AdminRoles{}

	// Test that all fields are nil/empty when not set
	assert.Nil(t, query.Search)
	assert.Nil(t, query.ProductIDs)
	assert.Nil(t, query.Type)
	assert.Nil(t, query.Status)
	assert.Nil(t, query.Sort)
	assert.Nil(t, query.Order)
	assert.Nil(t, query.GetLimit())
	assert.Nil(t, query.GetOffset())
}

// Helper function to create int64 pointer
func int64Ptr(i int64) *int64 {
	return &i
}
