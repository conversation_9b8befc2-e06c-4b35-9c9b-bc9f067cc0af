package service

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func stringPtr(s string) *string {
	return &s
}

func TestFilterRoles(t *testing.T) {
	roles := []entity.AdminRole{
		{
			ID:         1,
			Name:       "Admin Role",
			IsSystem:   true,
			IsActive:   constants.Active,
			UserCount:  5,
			GroupCount: 2,
			Product: &productentity.ProductBasic{
				ID:       1,
				TechName: "product1",
				Name:     "Product 1",
			},
		},
		{
			ID:         2,
			Name:       "User Role",
			IsSystem:   false,
			IsActive:   constants.Archive,
			UserCount:  10,
			GroupCount: 3,
			Product: &productentity.ProductBasic{
				ID:       2,
				TechName: "product2",
				Name:     "Product 2",
			},
		},
		{
			ID:         3,
			Name:       "Guest Role",
			IsSystem:   false,
			IsActive:   constants.Active,
			UserCount:  1,
			GroupCount: 0,
			Product:    nil,
		},
		{
			ID:         4,
			Name:       "Manager Role",
			IsSystem:   true,
			IsActive:   constants.Active,
			UserCount:  3,
			GroupCount: 1,
			Product: &productentity.ProductBasic{
				ID:       3,
				TechName: "product3",
				Name:     "Product 3",
			},
		},
	}

	tests := []struct {
		name     string
		query    query.AdminRoles
		expected []entity.AdminRole
	}{
		{
			name:     "no filters",
			query:    query.AdminRoles{},
			expected: roles,
		},
		{
			name: "search filter - case insensitive",
			query: query.AdminRoles{
				Search: stringPtr("admin"),
			},
			expected: []entity.AdminRole{roles[0]},
		},
		{
			name: "search filter - no match",
			query: query.AdminRoles{
				Search: stringPtr("nonexistent"),
			},
			expected: []entity.AdminRole{},
		},
		{
			name: "search filter - empty string",
			query: query.AdminRoles{
				Search: stringPtr(""),
			},
			expected: roles,
		},
		{
			name: "type filter - system",
			query: query.AdminRoles{
				Type: stringPtr(constants.SystemType),
			},
			expected: []entity.AdminRole{roles[0], roles[3]},
		},
		{
			name: "type filter - custom",
			query: query.AdminRoles{
				Type: stringPtr(constants.CustomType),
			},
			expected: []entity.AdminRole{roles[1], roles[2]},
		},
		{
			name: "status filter - active",
			query: query.AdminRoles{
				Status: stringPtr(constants.RoleActive),
			},
			expected: []entity.AdminRole{roles[0], roles[2], roles[3]},
		},
		{
			name: "status filter - archive",
			query: query.AdminRoles{
				Status: stringPtr(constants.RoleArchive),
			},
			expected: []entity.AdminRole{roles[1]},
		},
		{
			name: "product filter - specific products",
			query: query.AdminRoles{
				ProductIDs: []int64{1, 3},
			},
			expected: []entity.AdminRole{roles[0], roles[3]},
		},
		{
			name: "product filter - no matching products",
			query: query.AdminRoles{
				ProductIDs: []int64{999},
			},
			expected: []entity.AdminRole{},
		},
		{
			name: "product filter - empty list",
			query: query.AdminRoles{
				ProductIDs: []int64{},
			},
			expected: roles,
		},
		{
			name: "combined filters",
			query: query.AdminRoles{
				Search:     stringPtr("role"),
				Type:       stringPtr(constants.SystemType),
				Status:     stringPtr(constants.RoleActive),
				ProductIDs: []int64{1, 2, 3},
			},
			expected: []entity.AdminRole{roles[0], roles[3]},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := filterRoles(roles, tt.query)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSortRoles(t *testing.T) {
	roles := []entity.AdminRole{
		{
			ID:         1,
			Name:       "Zebra Role",
			IsSystem:   false,
			UserCount:  10,
			GroupCount: 3,
			Product: &productentity.ProductBasic{
				ID:       constants.ZeroProductID,
				TechName: "zebra_product",
				Name:     "Zebra Product",
			},
		},
		{
			ID:         2,
			Name:       "Alpha Role",
			IsSystem:   true,
			UserCount:  5,
			GroupCount: 1,
			Product: &productentity.ProductBasic{
				ID:       constants.DefaultProductID,
				TechName: "alpha_product",
				Name:     "Alpha Product",
			},
		},
		{
			ID:         3,
			Name:       "Beta Role",
			IsSystem:   false,
			UserCount:  8,
			GroupCount: 2,
			Product: &productentity.ProductBasic{
				ID:       2,
				TechName: "beta_product",
				Name:     "Beta Product",
			},
		},
		{
			ID:         4,
			Name:       "Gamma Role",
			IsSystem:   true,
			UserCount:  3,
			GroupCount: 4,
			Product:    nil,
		},
	}

	tests := []struct {
		name     string
		query    query.AdminRoles
		expected []entity.AdminRole
	}{
		{
			name:     "no sort",
			query:    query.AdminRoles{},
			expected: roles,
		},
		{
			name: "sort by name ascending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldName.String()),
				Order: stringPtr(sharedentity.SortOrderAscend.String()),
			},
			expected: []entity.AdminRole{roles[1], roles[2], roles[3], roles[0]},
		},
		{
			name: "sort by name descending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldName.String()),
				Order: stringPtr(sharedentity.SortOrderDescend.String()),
			},
			expected: []entity.AdminRole{roles[0], roles[3], roles[2], roles[1]},
		},
		{
			name: "sort by type ascending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldType.String()),
				Order: stringPtr(sharedentity.SortOrderAscend.String()),
			},
			expected: []entity.AdminRole{roles[0], roles[2], roles[1], roles[3]},
		},
		{
			name: "sort by type descending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldType.String()),
				Order: stringPtr(sharedentity.SortOrderDescend.String()),
			},
			expected: []entity.AdminRole{roles[3], roles[1], roles[2], roles[0]},
		},
		{
			name: "sort by tech name ascending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldTechName.String()),
				Order: stringPtr(sharedentity.SortOrderAscend.String()),
			},
			expected: []entity.AdminRole{roles[0], roles[1], roles[2], roles[3]},
		},
		{
			name: "sort by user count ascending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldUserCount.String()),
				Order: stringPtr(sharedentity.SortOrderAscend.String()),
			},
			expected: []entity.AdminRole{roles[3], roles[1], roles[2], roles[0]},
		},
		{
			name: "sort by group count ascending",
			query: query.AdminRoles{
				Sort:  stringPtr(sharedentity.SortFieldGroupCount.String()),
				Order: stringPtr(sharedentity.SortOrderAscend.String()),
			},
			expected: []entity.AdminRole{roles[1], roles[2], roles[0], roles[3]},
		},
		{
			name: "sort by unknown field (defaults to name)",
			query: query.AdminRoles{
				Sort:  stringPtr("unknown_field"),
				Order: stringPtr(sharedentity.SortOrderAscend.String()),
			},
			expected: []entity.AdminRole{roles[1], roles[2], roles[3], roles[0]},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := sortRoles(roles, tt.query)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareRoles(t *testing.T) {
	role1 := entity.AdminRole{Name: "Alpha", IsSystem: true, UserCount: 5, GroupCount: 2}
	role2 := entity.AdminRole{Name: "Beta", IsSystem: false, UserCount: 10, GroupCount: 1}

	tests := []struct {
		name      string
		sortField string
		expected  bool
	}{
		{
			name:      "compare by name",
			sortField: sharedentity.SortFieldName.String(),
			expected:  true, // Alpha < Beta
		},
		{
			name:      "compare by type",
			sortField: sharedentity.SortFieldType.String(),
			expected:  false, // custom < system
		},
		{
			name:      "compare by user count",
			sortField: sharedentity.SortFieldUserCount.String(),
			expected:  true, // 5 < 10
		},
		{
			name:      "compare by group count",
			sortField: sharedentity.SortFieldGroupCount.String(),
			expected:  false, // 2 > 1
		},
		{
			name:      "compare by unknown field (defaults to name)",
			sortField: "unknown",
			expected:  true, // Alpha < Beta
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := compareRoles(role1, role2, tt.sortField)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareRolesByTechName(t *testing.T) {
	roleWithProduct1 := entity.AdminRole{
		Name: "Role 1",
		Product: &productentity.ProductBasic{
			ID:       1,
			TechName: "alpha",
		},
	}

	roleWithProduct2 := entity.AdminRole{
		Name: "Role 2",
		Product: &productentity.ProductBasic{
			ID:       2,
			TechName: "beta",
		},
	}

	roleWithoutProduct1 := entity.AdminRole{Name: "Role 3", Product: nil}
	roleWithoutProduct2 := entity.AdminRole{Name: "Role 4", Product: nil}

	tests := []struct {
		name     string
		r1       entity.AdminRole
		r2       entity.AdminRole
		expected bool
	}{
		{
			name:     "both products exist - r1 < r2",
			r1:       roleWithProduct1,
			r2:       roleWithProduct2,
			expected: true,
		},
		{
			name:     "both products exist - r1 > r2",
			r1:       roleWithProduct2,
			r2:       roleWithProduct1,
			expected: false,
		},
		{
			name:     "r1 has product, r2 doesn't",
			r1:       roleWithProduct1,
			r2:       roleWithoutProduct1,
			expected: true,
		},
		{
			name:     "r1 doesn't have product, r2 has",
			r1:       roleWithoutProduct1,
			r2:       roleWithProduct1,
			expected: false,
		},
		{
			name:     "both don't have products - sort by name",
			r1:       roleWithoutProduct1, // Role 3
			r2:       roleWithoutProduct2, // Role 4
			expected: true,                // Role 3 < Role 4
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := compareRolesByTechName(tt.r1, tt.r2)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareProductsByTechName(t *testing.T) {
	roleWithNilProduct := entity.AdminRole{
		Name:    "Role 1",
		Product: nil,
	}

	roleWithZeroProduct := entity.AdminRole{
		Name: "Role 2",
		Product: &productentity.ProductBasic{
			ID:       constants.ZeroProductID,
			TechName: "zero_product",
		},
	}

	roleWithDefaultProduct1 := entity.AdminRole{
		Name: "Role 3",
		Product: &productentity.ProductBasic{
			ID:       constants.DefaultProductID,
			TechName: "default_product",
		},
	}

	roleWithDefaultProduct2 := entity.AdminRole{
		Name: "Role 4",
		Product: &productentity.ProductBasic{
			ID:       constants.DefaultProductID,
			TechName: "default_product",
		},
	}

	roleWithNormalProduct := entity.AdminRole{
		Name: "Role 5",
		Product: &productentity.ProductBasic{
			ID:       5,
			TechName: "normal_product",
		},
	}

	roleWithNormalProduct2 := entity.AdminRole{
		Name: "Role 6",
		Product: &productentity.ProductBasic{
			ID:       6,
			TechName: "zebra_product",
		},
	}

	tests := []struct {
		name     string
		r1       entity.AdminRole
		r2       entity.AdminRole
		expected bool
	}{
		{
			name:     "r1 has nil product",
			r1:       roleWithNilProduct,
			r2:       roleWithNormalProduct,
			expected: true, // Role 1 < Role 5 by name
		},
		{
			name:     "r2 has nil product",
			r1:       roleWithNormalProduct,
			r2:       roleWithNilProduct,
			expected: false, // Role 5 > Role 1 by name
		},
		{
			name:     "r1 has ZeroProductID, r2 has normal ID",
			r1:       roleWithZeroProduct,
			r2:       roleWithNormalProduct,
			expected: true,
		},
		{
			name:     "r1 has normal ID, r2 has ZeroProductID",
			r1:       roleWithNormalProduct,
			r2:       roleWithZeroProduct,
			expected: false,
		},
		{
			name:     "both have DefaultProductID - sort by name",
			r1:       roleWithDefaultProduct1, // Role 3
			r2:       roleWithDefaultProduct2, // Role 4
			expected: true,                    // Role 3 < Role 4
		},
		{
			name:     "normal comparison by TechName",
			r1:       roleWithNormalProduct,  // normal_product
			r2:       roleWithNormalProduct2, // zebra_product
			expected: true,                   // normal_product < zebra_product
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := compareProductsByTechName(tt.r1, tt.r2)
			assert.Equal(t, tt.expected, result)
		})
	}
}
