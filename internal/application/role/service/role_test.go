package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	permissionmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func createRoleMocks(t *testing.T) (*participantmocks.ParticipantDomainServiceMock, *productmocks.ProductDomainServiceMock, *rolemocks.RoleDomainServiceMock, *groupmocks.GroupDomainServiceMock, *usermocks.UserDomainServiceMock, *categorymocks.CategoryDomainServiceMock, *permissionmocks.PermissionDomainServiceMock) {
	return participantmocks.NewParticipantDomainServiceMock(t),
		productmocks.NewProductDomainServiceMock(t),
		rolemocks.NewRoleDomainServiceMock(t),
		groupmocks.NewGroupDomainServiceMock(t),
		usermocks.NewUserDomainServiceMock(t),
		categorymocks.NewCategoryDomainServiceMock(t),
		permissionmocks.NewPermissionDomainServiceMock(t)
}

func TestNewRoleAppService(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)

	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	require.NotNil(t, service)
	require.Equal(t, participantMock, service.participantDomain)
	require.Equal(t, productMock, service.productDomain)
	require.Equal(t, roleMock, service.roleDomain)
	require.Equal(t, groupMock, service.groupDomain)
	require.Equal(t, userMock, service.userDomain)
	require.Equal(t, categoryMock, service.categoryDomain)
	require.Equal(t, permissionMock, service.permissionDomain)
}

func TestRoleAppService_Create(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleWithPermissions := roleentity.RoleWithPermissions{
		ID:   1,
		Name: "Test Role",
	}
	expectedRoleFull := roleentity.RoleFull{
		ID:   1,
		Name: "Test Role",
	}

	roleMock.CreateMock.Expect(ctx, roleWithPermissions).Return(expectedRoleFull, nil)

	result, err := service.Create(ctx, roleWithPermissions)

	require.NoError(t, err)
	require.Equal(t, expectedRoleFull, result)
	require.Equal(t, uint64(1), roleMock.CreateAfterCounter())
}

func TestRoleAppService_GetSystemRoles_AdminUser(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	categoryID := int64(1)

	categoryRoles := []roleentity.RoleWithStats{
		{RoleID: 1, IsSystem: true},
		{RoleID: 2, IsSystem: true},
		{RoleID: 3, IsSystem: false},
	}

	category := categoryentity.CategoryFull{
		ID:    categoryID,
		Roles: &categoryRoles,
	}

	systemActiveRoles := []roleentity.RoleWithStats{
		{RoleID: 1},
		{RoleID: 2},
	}

	user := userentity.User{
		ID:      userID,
		IsAdmin: true,
	}

	categoryMock.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	roleMock.GetBySystemTypeAndActiveMock.Expect(true, true).Return(systemActiveRoles, nil)
	userMock.GetByIDMock.Expect(userID).Return(user, nil)

	result, err := service.GetSystemRoles(userID, categoryID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, uint64(1), categoryMock.GetCategoryFullAfterCounter())
	require.Equal(t, uint64(1), roleMock.GetBySystemTypeAndActiveAfterCounter())
	require.Equal(t, uint64(1), userMock.GetByIDAfterCounter())
}

func TestRoleAppService_GetSystemRoles_NonAdminUser(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	categoryID := int64(1)

	categoryRoles := []roleentity.RoleWithStats{
		{RoleID: 1, IsSystem: true},
		{RoleID: 2, IsSystem: true},
	}

	category := categoryentity.CategoryFull{
		ID:    categoryID,
		Roles: &categoryRoles,
	}

	systemActiveRoles := []roleentity.RoleWithStats{
		{RoleID: 1},
		{RoleID: 2},
	}

	user := userentity.User{
		ID:      userID,
		IsAdmin: false,
	}

	userRoleIDs := []int64{1}

	categoryMock.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	roleMock.GetBySystemTypeAndActiveMock.Expect(true, true).Return(systemActiveRoles, nil)
	userMock.GetByIDMock.Expect(userID).Return(user, nil)
	roleMock.GetRolesIDByUserIDMock.Expect(userID).Return(userRoleIDs, nil)

	result, err := service.GetSystemRoles(userID, categoryID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, uint64(1), roleMock.GetRolesIDByUserIDAfterCounter())
}

func TestRoleAppService_GetAllWithCategoryStats(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	expectedRoles := []roleentity.RoleWithCategoryStates{
		{RoleID: 1, RoleName: "Role 1"},
		{RoleID: 2, RoleName: "Role 2"},
	}

	roleMock.GetAllWithCategoryStatsMock.Expect().Return(expectedRoles, nil)

	result, err := service.GetAllWithCategoryStats()

	require.NoError(t, err)
	require.Equal(t, expectedRoles, result)
	require.Equal(t, uint64(1), roleMock.GetAllWithCategoryStatsAfterCounter())
}

func TestRoleAppService_GetByParticipantID(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	participantID := int64(1)
	expectedRoles := []roleentity.Role{
		{ID: 1, Name: "Role 1"},
		{ID: 2, Name: "Role 2"},
	}

	roleMock.GetByParticipantIDMock.Expect(participantID).Return(expectedRoles, nil)

	result, err := service.GetByParticipantID(participantID)

	require.NoError(t, err)
	require.Equal(t, expectedRoles, result)
	require.Equal(t, uint64(1), roleMock.GetByParticipantIDAfterCounter())
}

func TestRoleAppService_GetByUserID(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	expectedRoles := []roleentity.Role{
		{ID: 1, Name: "Role 1"},
		{ID: 2, Name: "Role 2"},
	}

	roleMock.GetByUserIDMock.Expect(userID).Return(expectedRoles, nil)

	result, err := service.GetByUserID(userID)

	require.NoError(t, err)
	require.Equal(t, expectedRoles, result)
	require.Equal(t, uint64(1), roleMock.GetByUserIDAfterCounter())
}

func TestRoleAppService_GetByGroupID(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	groupID := int64(1)
	expectedRoles := []roleentity.Role{
		{ID: 1, Name: "Role 1"},
		{ID: 2, Name: "Role 2"},
	}

	roleMock.GetByGroupIDMock.Expect(groupID).Return(expectedRoles, nil)

	result, err := service.GetByGroupID(groupID)

	require.NoError(t, err)
	require.Equal(t, expectedRoles, result)
	require.Equal(t, uint64(1), roleMock.GetByGroupIDAfterCounter())
}

func TestRoleAppService_IsRoleProtectedByRoleID_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:          roleID,
		IsProtected: true,
	}

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)

	result, err := service.IsRoleProtectedByRoleID(roleID)

	require.NoError(t, err)
	require.True(t, result)
	require.Equal(t, uint64(1), roleMock.GetByIDAfterCounter())
}

func TestRoleAppService_IsRoleProtectedByRoleID_Error(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)

	roleMock.GetByIDMock.Expect(roleID).Return(roleentity.Role{}, errors.New("role not found"))

	result, err := service.IsRoleProtectedByRoleID(roleID)

	require.Error(t, err)
	require.False(t, result)
}

func TestRoleAppService_GetWithProductsByFilter_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	query := query.AdminRoles{}

	userRoles := []roleentity.RoleWithProduct{
		{
			ID:        1,
			Name:      "Role 1",
			IsSystem:  true,
			ActiveFlg: constants.Active,
			Product: &productentity.Product{
				ID:       1,
				IID:      "product1",
				TechName: "product1",
				Name:     "Product 1",
			},
		},
		{
			ID:        2,
			Name:      "Role 2",
			IsSystem:  false,
			ActiveFlg: constants.Archive,
			Product:   nil,
		},
	}

	roleMock.GetAllWithProductByUserIDMock.Expect(userID).Return(userRoles, nil)

	result, err := service.GetWithProductsByFilter(userID, query)

	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, "Role 1", result[0].Name)
	require.Equal(t, "Role 2", result[1].Name)
	require.NotNil(t, result[0].Product)
	require.Nil(t, result[1].Product)
	require.Equal(t, uint64(1), roleMock.GetAllWithProductByUserIDAfterCounter())
}

func TestRoleAppService_GetWithProductsByFilter_Error(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	query := query.AdminRoles{}

	roleMock.GetAllWithProductByUserIDMock.Expect(userID).Return(nil, errors.New("database error"))

	result, err := service.GetWithProductsByFilter(userID, query)

	require.Error(t, err)
	require.Empty(t, result)
}

func TestRoleAppService_GetByProductID_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	productID := int64(1)

	domainRoles := []roleentity.RoleWithStats{
		{RoleID: 1, IsSystem: true},
		{RoleID: 2, IsSystem: false},
		{RoleID: 3, IsSystem: false},
	}

	roleMock.GetByProductIDMock.Expect(productID).Return(domainRoles, nil)

	result, err := service.GetByProductID(productID)

	require.NoError(t, err)
	require.Len(t, result, 2) // Only non-system roles should be returned
	require.Equal(t, uint64(1), roleMock.GetByProductIDAfterCounter())
}

func TestRoleAppService_GetByProductID_Error(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	productID := int64(1)

	roleMock.GetByProductIDMock.Expect(productID).Return(nil, errors.New("database error"))

	result, err := service.GetByProductID(productID)

	require.Error(t, err)
	require.Nil(t, result)
	require.Contains(t, err.Error(), "failed to get roles")
}

func TestRoleAppService_GetFull(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	productID := int64(1)
	roleID := int64(1)
	expectedRoleFull := roleentity.RoleFull{
		ID:   roleID,
		Name: "Test Role",
	}

	roleMock.GetFullMock.Expect(productID, roleID).Return(expectedRoleFull, nil)

	result, err := service.GetFull(productID, roleID)

	require.NoError(t, err)
	require.Equal(t, expectedRoleFull, result)
	require.Equal(t, uint64(1), roleMock.GetFullAfterCounter())
}

func TestRoleAppService_GetAvailableSystemRolesByUserAndCategory_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	inputRoles := []roleentity.RoleWithStats{
		{RoleID: 1, IsSystem: true},
		{RoleID: 2, IsSystem: true},
	}
	categoryRoles := []roleentity.RoleWithStats{
		{RoleID: 3, IsSystem: true},
		{RoleID: 4, IsSystem: false},
	}
	category := categoryentity.CategoryFull{
		Roles: &categoryRoles,
	}
	userRoleIDs := []int64{1}

	roleMock.GetRolesIDByUserIDMock.Expect(userID).Return(userRoleIDs, nil)

	result, err := service.GetAvailableSystemRolesByUserAndCategory(userID, inputRoles, category)

	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, uint64(1), roleMock.GetRolesIDByUserIDAfterCounter())
}

func TestRoleAppService_GetAvailableSystemRolesByUserAndCategory_Error(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	var inputRoles []roleentity.RoleWithStats
	category := categoryentity.CategoryFull{}

	roleMock.GetRolesIDByUserIDMock.Expect(userID).Return(nil, errors.New("database error"))

	result, err := service.GetAvailableSystemRolesByUserAndCategory(userID, inputRoles, category)

	require.Error(t, err)
	require.Nil(t, result)
}

func TestRoleAppService_UpdateWithCategories(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	rolesWithCategories := []roleentity.RoleWithCategoryStates{
		{RoleID: 1, RoleName: "Role 1"},
		{RoleID: 2, RoleName: "Role 2"},
	}

	roleMock.UpdateWithCategoriesMock.Expect(rolesWithCategories).Return(nil)

	err := service.UpdateWithCategories(rolesWithCategories)

	require.NoError(t, err)
	require.Equal(t, uint64(1), roleMock.UpdateWithCategoriesAfterCounter())
}

func TestRoleAppService_UpdateParticipant(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	id := int64(1)
	participantID := int64(2)
	groupIDs := []int64{1, 2, 3}
	expectedResult := []int64{1, 2, 3}

	roleMock.UpdateParticipantMock.Expect(ctx, id, participantID, groupIDs).Return(expectedResult, nil)

	result, err := service.UpdateParticipant(ctx, id, participantID, groupIDs)

	require.NoError(t, err)
	require.Equal(t, expectedResult, result)
	require.Equal(t, uint64(1), roleMock.UpdateParticipantAfterCounter())
}

func TestRoleAppService_UpdateByProductIDAndRoleFull(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	productID := int64(1)
	roleFull := roleentity.RoleFull{
		ID:   1,
		Name: "Updated Role",
	}
	expectedResult := roleentity.RoleFull{
		ID:   1,
		Name: "Updated Role",
	}

	roleMock.UpdateByProductIDAndRoleFullMock.Expect(ctx, productID, roleFull).Return(expectedResult, nil)

	result, err := service.UpdateByProductIDAndRoleFull(ctx, productID, roleFull)

	require.NoError(t, err)
	require.Equal(t, expectedResult, result)
	require.Equal(t, uint64(1), roleMock.UpdateByProductIDAndRoleFullAfterCounter())
}

func TestRoleAppService_Delete(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	productID := int64(1)
	roleID := int64(1)

	roleMock.DeleteMock.Expect(ctx, productID, roleID).Return(nil)

	err := service.Delete(ctx, productID, roleID)

	require.NoError(t, err)
	require.Equal(t, uint64(1), roleMock.DeleteAfterCounter())
}

// Тесты для сценариев ошибок
func TestRoleAppService_GetSystemRoles_CategoryError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	categoryID := int64(1)

	categoryMock.GetCategoryFullMock.Expect(categoryID).Return(categoryentity.CategoryFull{}, errors.New("category error"))

	result, err := service.GetSystemRoles(userID, categoryID)

	require.Error(t, err)
	require.Nil(t, result)
	require.Contains(t, err.Error(), "failed to get category")
}

func TestRoleAppService_GetSystemRoles_SystemRolesError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	categoryID := int64(1)

	category := categoryentity.CategoryFull{
		ID:    categoryID,
		Roles: &[]roleentity.RoleWithStats{},
	}

	categoryMock.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	roleMock.GetBySystemTypeAndActiveMock.Expect(true, true).Return(nil, errors.New("system roles error"))

	result, err := service.GetSystemRoles(userID, categoryID)

	require.Error(t, err)
	require.Nil(t, result)
	require.Contains(t, err.Error(), "failed to get system roles")
}

func TestRoleAppService_GetSystemRoles_UserError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	userID := int64(1)
	categoryID := int64(1)

	category := categoryentity.CategoryFull{
		ID:    categoryID,
		Roles: &[]roleentity.RoleWithStats{},
	}

	var systemActiveRoles []roleentity.RoleWithStats

	categoryMock.GetCategoryFullMock.Expect(categoryID).Return(category, nil)
	roleMock.GetBySystemTypeAndActiveMock.Expect(true, true).Return(systemActiveRoles, nil)
	userMock.GetByIDMock.Expect(userID).Return(userentity.User{}, errors.New("user error"))

	result, err := service.GetSystemRoles(userID, categoryID)

	require.Error(t, err)
	require.Nil(t, result)
	require.Contains(t, err.Error(), "failed to get user")
}
