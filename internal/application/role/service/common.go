package service

import (
	"sort"
	"strings"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

func compareProductsByTechName(r1, r2 entity.AdminRole) bool {

	if r1.Product == nil || r2.Product == nil {
		return r1.Name < r2.Name
	}

	// Check special product IDs
	if r1.Product.ID == constants.ZeroProductID && r2.Product.ID != constants.ZeroProductID {
		return true
	}
	if r1.Product.ID != constants.ZeroProductID && r2.Product.ID == constants.ZeroProductID {
		return false
	}

	// Both products have DefaultProductID
	if r1.Product.ID == constants.DefaultProductID && r2.Product.ID == constants.DefaultProductID {
		return r1.Name < r2.Name
	}

	// Normal comparison by TechName
	return r1.Product.TechName < r2.Product.TechName
}

func compareRoles(r1, r2 entity.AdminRole, sortField string) bool {
	switch sortField {
	case sharedentity.SortFieldName.String():
		return r1.Name < r2.Name
	case sharedentity.SortFieldType.String():
		return constants.IsSystemToConstant(r1.IsSystem) < constants.IsSystemToConstant(r2.IsSystem)
	case sharedentity.SortFieldTechName.String():
		return compareRolesByTechName(r1, r2)
	case sharedentity.SortFieldUserCount.String():
		return r1.UserCount < r2.UserCount
	case sharedentity.SortFieldGroupCount.String():
		return r1.GroupCount < r2.GroupCount
	default:
		return r1.Name < r2.Name
	}
}

func compareRolesByTechName(r1, r2 entity.AdminRole) bool {
	// Both products exist
	if r1.Product != nil && r2.Product != nil {
		return compareProductsByTechName(r1, r2)
	}

	// One of the products is missing
	if r1.Product != nil && r2.Product == nil {
		return true
	}
	if r1.Product == nil && r2.Product != nil {
		return false
	}

	// Both products are missing - sort by role name
	return r1.Name < r2.Name
}

func filterRoles(roles []entity.AdminRole, query query.AdminRoles) []entity.AdminRole {

	filtered := make([]entity.AdminRole, 0, len(roles))

	var allowedProductIDs map[int64]bool
	if len(query.ProductIDs) > 0 {
		allowedProductIDs = make(map[int64]bool, len(query.ProductIDs))
		for _, id := range query.ProductIDs {
			allowedProductIDs[id] = true
		}
	}

	var searchLower string
	if query.Search != nil && *query.Search != "" {
		searchLower = strings.ToLower(*query.Search)
	}

	for _, role := range roles {
		keep := true

		// Search filter
		if searchLower != "" {
			if !strings.Contains(strings.ToLower(role.Name), searchLower) {
				keep = false
			}
		}

		// Type filter
		if keep && query.Type != nil {
			if *query.Type == constants.SystemType {
				keep = role.IsSystem
			} else {
				keep = !role.IsSystem
			}
		}

		// Status filter
		if keep && query.Status != nil {
			switch *query.Status {
			case constants.RoleActive:
				keep = role.IsActive == constants.Active
			case constants.RoleArchive:
				keep = role.IsActive == constants.Archive
			}
		}

		// Product filter
		if keep && allowedProductIDs != nil {
			keep = role.Product != nil && allowedProductIDs[role.Product.ID]
		}

		if keep {
			filtered = append(filtered, role)
		}
	}

	return filtered
}

func sortRoles(roles []entity.AdminRole, query query.AdminRoles) []entity.AdminRole {
	if query.Sort == nil {
		return roles
	}

	sorted := make([]entity.AdminRole, len(roles))
	copy(sorted, roles)

	sortField := *query.Sort
	orderAsc := true
	if query.Order != nil && *query.Order == sharedentity.SortOrderDescend.String() {
		orderAsc = false
	}

	sort.SliceStable(sorted, func(i, j int) bool {
		less := compareRoles(sorted[i], sorted[j], sortField)

		if !orderAsc {
			return !less
		}
		return less
	})

	return sorted
}
