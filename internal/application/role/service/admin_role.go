package service

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/pagination"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/aggregate"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func (s *RoleAppService) CreateAsAdmin(ctx context.Context, role roleentity.Role, perms []permissionentity.Permission) (roleaggregate.RoleWithDetails, error) {
	roleCreated, err := s.roleDomain.CreateAsAdmin(ctx, role, perms)
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	return s.GetAsAdminByID(roleCreated.ID)
}

func (s *RoleAppService) GetAllAsAdmin(query query.AdminRoles) (sharedentity.PaginatedResult[roleentity.AdminRole], error) {
	roles, err := s.roleDomain.GetAllAsAdmin()
	if err != nil {
		return sharedentity.PaginatedResult[roleentity.AdminRole]{}, err
	}

	filteredRoles := filterRoles(roles, query)
	sortedRoles := sortRoles(filteredRoles, query)
	paginatedRoles, limit, offset := pagination.Apply(sortedRoles, query)

	return sharedentity.PaginatedResult[roleentity.AdminRole]{
		Items:  paginatedRoles,
		Total:  sharedentity.SliceLen(filteredRoles),
		Limit:  limit,
		Offset: offset,
	}, nil
}

func (s *RoleAppService) GetAsAdminByID(roleID int64) (roleaggregate.RoleWithDetails, error) {

	role, err := s.roleDomain.GetByID(roleID)
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	var product productentity.Product
	if role.ProductID != nil && !role.IsSystem {
		product, err = s.productDomain.GetByID(*role.ProductID)
		if err != nil {
			return roleaggregate.RoleWithDetails{}, err
		}
	}

	usersWithoutProduct, err := s.userDomain.GetUserRolesByRoleID(roleID)
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	usersWithProduct, err := s.userDomain.GetUsersWithProductsByRoleID(roleID)
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	usersAll := make([]userentity.UserWithProduct, 0)
	usersAll = append(usersAll, usersWithProduct...)
	for _, user := range usersWithoutProduct {
		usersAll = append(usersAll, userentity.UserWithProduct{
			ID:       user.ID,
			Email:    user.Email,
			FullName: user.FullName,
		})
	}

	groupsWithProduct, err := s.groupDomain.GetWithProductsByRoleID(roleID)
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	permissions, err := s.permissionDomain.GetByRoleID(roleID)
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	return roleaggregate.RoleWithDetails{
		Role:        role,
		Product:     product.ProductOrNil(),
		Users:       usersAll,
		Groups:      groupsWithProduct,
		Permissions: permissions,
	}, nil
}

func (s *RoleAppService) UpdateAsAdmin(ctx context.Context, data roleaggregate.AdminRoleUpdate) (roleaggregate.RoleWithDetails, error) {

	updatedRole, err := s.roleDomain.Update(ctx, roleentity.RoleUpdateData{
		ID:        data.ID,
		Name:      data.Name,
		ActiveFlg: data.ActiveFlg,
	})
	if err != nil {
		return roleaggregate.RoleWithDetails{}, err
	}

	if data.UsersForUpdate != nil {
		err = s.roleDomain.UpdateLinksWithUsers(ctx, data.ID, *data.UsersForUpdate)
		if err != nil {
			return roleaggregate.RoleWithDetails{}, err
		}
	}

	if data.GroupsForUpdate != nil {
		err = s.roleDomain.UpdateLinksWithGroups(ctx, data.ID, *data.GroupsForUpdate)
		if err != nil {
			return roleaggregate.RoleWithDetails{}, err
		}
	}

	if data.Permissions != nil {
		err = s.roleDomain.UpdateLinksWithPermissions(ctx, data.ID, *data.Permissions)
		if err != nil {
			return roleaggregate.RoleWithDetails{}, err
		}
	}

	return s.GetAsAdminByID(updatedRole.ID)
}

func (s *RoleAppService) DeleteAsAdmin(ctx context.Context, roleID int64) error {
	return s.roleDomain.DeleteAsAdmin(ctx, roleID)
}
