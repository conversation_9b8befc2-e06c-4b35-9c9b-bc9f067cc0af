package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/aggregate"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestRoleAppService_CreateAsAdmin_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	role := roleentity.Role{
		ID:   1,
		Name: "Test Role",
	}
	permissions := []permissionentity.Permission{
		{ID: 1, Name: "perm1"},
		{ID: 2, Name: "perm2"},
	}
	createdRole := roleentity.Role{
		ID:   1,
		Name: "Test Role",
	}
	roleWithDetails := roleaggregate.RoleWithDetails{
		Role:        createdRole,
		Product:     nil,
		Users:       []userentity.UserWithProduct{},
		Groups:      []groupentity.GroupWithProduct{},
		Permissions: permissions,
	}

	roleMock.CreateAsAdminMock.Expect(ctx, role, permissions).Return(createdRole, nil)
	roleMock.GetByIDMock.Expect(createdRole.ID).Return(createdRole, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(createdRole.ID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(createdRole.ID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(createdRole.ID).Return([]groupentity.GroupWithProduct{}, nil)
	permissionMock.GetByRoleIDMock.Expect(createdRole.ID).Return(permissions, nil)

	result, err := service.CreateAsAdmin(ctx, role, permissions)

	require.NoError(t, err)
	require.Equal(t, roleWithDetails, result)
}

func TestRoleAppService_CreateAsAdmin_CreateError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	role := roleentity.Role{Name: "Test Role"}
	var permissions []permissionentity.Permission
	expectedError := errors.New("create error")

	roleMock.CreateAsAdminMock.Expect(ctx, role, permissions).Return(roleentity.Role{}, expectedError)

	result, err := service.CreateAsAdmin(ctx, role, permissions)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_CreateAsAdmin_GetByIDError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	role := roleentity.Role{Name: "Test Role"}
	var permissions []permissionentity.Permission
	createdRole := roleentity.Role{ID: 1, Name: "Test Role"}
	expectedError := errors.New("get error")

	roleMock.CreateAsAdminMock.Expect(ctx, role, permissions).Return(createdRole, nil)
	roleMock.GetByIDMock.Expect(createdRole.ID).Return(roleentity.Role{}, expectedError)

	result, err := service.CreateAsAdmin(ctx, role, permissions)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_GetAllAsAdmin_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roles := []roleentity.AdminRole{
		{ID: 1, Name: "Role 1", IsSystem: false},
		{ID: 2, Name: "Role 2", IsSystem: true},
	}
	queryParams := query.AdminRoles{
		Limit:  int64Ptr(10),
		Offset: int64Ptr(0),
	}

	roleMock.GetAllAsAdminMock.Expect().Return(roles, nil)

	result, err := service.GetAllAsAdmin(queryParams)

	require.NoError(t, err)
	require.Equal(t, int64(2), result.Total)
	require.Equal(t, int64(10), result.Limit)
	require.Equal(t, int64(0), result.Offset)
	require.Len(t, result.Items, 2)
}

func TestRoleAppService_GetAllAsAdmin_Error(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	queryParams := query.AdminRoles{}
	expectedError := errors.New("db error")

	roleMock.GetAllAsAdminMock.Expect().Return([]roleentity.AdminRole{}, expectedError)

	result, err := service.GetAllAsAdmin(queryParams)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, sharedentity.PaginatedResult[roleentity.AdminRole]{}, result)
}

func TestRoleAppService_GetAsAdminByID_Success_SystemRole(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:       roleID,
		Name:     "System Role",
		IsSystem: true,
	}
	usersWithoutProduct := []userentity.User{
		{ID: 1, Email: "<EMAIL>", FullName: "User 1"},
	}
	usersWithProduct := []userentity.UserWithProduct{
		{ID: 2, Email: "<EMAIL>", FullName: "User 2"},
	}
	groups := []groupentity.GroupWithProduct{
		{ID: 1, Name: "Group 1"},
	}
	permissions := []permissionentity.Permission{
		{ID: 1, Name: "permission1"},
	}

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return(usersWithoutProduct, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return(usersWithProduct, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return(groups, nil)
	permissionMock.GetByRoleIDMock.Expect(roleID).Return(permissions, nil)

	result, err := service.GetAsAdminByID(roleID)

	require.NoError(t, err)
	require.Equal(t, role, result.Role)
	require.Nil(t, result.Product)
	require.Len(t, result.Users, 2)
	require.Equal(t, groups, result.Groups)
	require.Equal(t, permissions, result.Permissions)
}

func TestRoleAppService_GetAsAdminByID_Success_CustomRoleWithProduct(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	productID := int64(1)
	role := roleentity.Role{
		ID:        roleID,
		Name:      "Custom Role",
		IsSystem:  false,
		ProductID: &productID,
	}
	product := productentity.Product{
		ID:       productID,
		Name:     "Test Product",
		TechName: "test-product",
	}

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	productMock.GetByIDMock.Expect(productID).Return(product, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return([]groupentity.GroupWithProduct{}, nil)
	permissionMock.GetByRoleIDMock.Expect(roleID).Return([]permissionentity.Permission{}, nil)

	result, err := service.GetAsAdminByID(roleID)

	require.NoError(t, err)
	require.Equal(t, role, result.Role)
	require.NotNil(t, result.Product)
	require.Equal(t, product, *result.Product)
}

func TestRoleAppService_GetAsAdminByID_Success_CustomRoleWithoutProduct(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:        roleID,
		Name:      "Custom Role",
		IsSystem:  false,
		ProductID: nil,
	}

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return([]groupentity.GroupWithProduct{}, nil)
	permissionMock.GetByRoleIDMock.Expect(roleID).Return([]permissionentity.Permission{}, nil)

	result, err := service.GetAsAdminByID(roleID)

	require.NoError(t, err)
	require.Equal(t, role, result.Role)
	require.Nil(t, result.Product)
}

func TestRoleAppService_GetAsAdminByID_GetRoleError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	expectedError := errors.New("role not found")

	roleMock.GetByIDMock.Expect(roleID).Return(roleentity.Role{}, expectedError)

	result, err := service.GetAsAdminByID(roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_GetAsAdminByID_GetProductError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	productID := int64(1)
	role := roleentity.Role{
		ID:        roleID,
		Name:      "Custom Role",
		IsSystem:  false,
		ProductID: &productID,
	}
	expectedError := errors.New("product not found")

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	productMock.GetByIDMock.Expect(productID).Return(productentity.Product{}, expectedError)

	result, err := service.GetAsAdminByID(roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_GetAsAdminByID_GetUsersWithoutProductError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:       roleID,
		Name:     "System Role",
		IsSystem: true,
	}
	expectedError := errors.New("users error")

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, expectedError)

	result, err := service.GetAsAdminByID(roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_GetAsAdminByID_GetUsersWithProductError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:       roleID,
		Name:     "System Role",
		IsSystem: true,
	}
	expectedError := errors.New("users with product error")

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, expectedError)

	result, err := service.GetAsAdminByID(roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_GetAsAdminByID_GetGroupsError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:       roleID,
		Name:     "System Role",
		IsSystem: true,
	}
	expectedError := errors.New("groups error")

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return([]groupentity.GroupWithProduct{}, expectedError)

	result, err := service.GetAsAdminByID(roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_GetAsAdminByID_GetPermissionsError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	roleID := int64(1)
	role := roleentity.Role{
		ID:       roleID,
		Name:     "System Role",
		IsSystem: true,
	}
	expectedError := errors.New("permissions error")

	roleMock.GetByIDMock.Expect(roleID).Return(role, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return([]groupentity.GroupWithProduct{}, nil)
	permissionMock.GetByRoleIDMock.Expect(roleID).Return([]permissionentity.Permission{}, expectedError)

	result, err := service.GetAsAdminByID(roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_DeleteAsAdmin_Success(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)

	roleMock.DeleteAsAdminMock.Expect(ctx, roleID).Return(nil)

	err := service.DeleteAsAdmin(ctx, roleID)

	require.NoError(t, err)
}

func TestRoleAppService_DeleteAsAdmin_Error(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)
	expectedError := errors.New("delete error")

	roleMock.DeleteAsAdminMock.Expect(ctx, roleID).Return(expectedError)

	err := service.DeleteAsAdmin(ctx, roleID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestRoleAppService_UpdateAsAdmin_Success_AllFields(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)
	usersForUpdate := []userentity.UserProductLink{
		{UserID: 1, ProductID: int64Ptr(1)},
	}
	groupsForUpdate := []groupentity.GroupID{
		{ID: 1}, {ID: 2},
	}
	permissions := []permissionentity.Permission{
		{ID: 1, Name: "perm1"},
		{ID: 2, Name: "perm2"},
	}

	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
		UsersForUpdate:  &usersForUpdate,
		GroupsForUpdate: &groupsForUpdate,
		Permissions:     &permissions,
	}

	updatedRole := roleentity.Role{
		ID:        roleID,
		Name:      "Updated Role",
		ActiveFlg: true,
	}

	roleWithDetails := roleaggregate.RoleWithDetails{
		Role:        updatedRole,
		Product:     nil,
		Users:       []userentity.UserWithProduct{},
		Groups:      []groupentity.GroupWithProduct{},
		Permissions: []permissionentity.Permission{},
	}

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(updatedRole, nil)
	roleMock.UpdateLinksWithUsersMock.Expect(ctx, roleID, usersForUpdate).Return(nil)
	roleMock.UpdateLinksWithGroupsMock.Expect(ctx, roleID, groupsForUpdate).Return(nil)
	roleMock.UpdateLinksWithPermissionsMock.Expect(ctx, roleID, permissions).Return(nil)
	roleMock.GetByIDMock.Expect(roleID).Return(updatedRole, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return([]groupentity.GroupWithProduct{}, nil)
	permissionMock.GetByRoleIDMock.Expect(roleID).Return([]permissionentity.Permission{}, nil)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	require.Equal(t, roleWithDetails, result)
}

func TestRoleAppService_UpdateAsAdmin_Success_OnlyBasicFields(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)

	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
		UsersForUpdate:  nil,
		GroupsForUpdate: nil,
		Permissions:     nil,
	}

	updatedRole := roleentity.Role{
		ID:        roleID,
		Name:      "Updated Role",
		ActiveFlg: true,
	}

	roleWithDetails := roleaggregate.RoleWithDetails{
		Role:        updatedRole,
		Product:     nil,
		Users:       []userentity.UserWithProduct{},
		Groups:      []groupentity.GroupWithProduct{},
		Permissions: []permissionentity.Permission{},
	}

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(updatedRole, nil)
	roleMock.GetByIDMock.Expect(roleID).Return(updatedRole, nil)
	userMock.GetUserRolesByRoleIDMock.Expect(roleID).Return([]userentity.User{}, nil)
	userMock.GetUsersWithProductsByRoleIDMock.Expect(roleID).Return([]userentity.UserWithProduct{}, nil)
	groupMock.GetWithProductsByRoleIDMock.Expect(roleID).Return([]groupentity.GroupWithProduct{}, nil)
	permissionMock.GetByRoleIDMock.Expect(roleID).Return([]permissionentity.Permission{}, nil)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.NoError(t, err)
	require.Equal(t, roleWithDetails, result)
}

func TestRoleAppService_UpdateAsAdmin_UpdateError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)
	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
	}
	expectedError := errors.New("update error")

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(roleentity.Role{}, expectedError)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_UpdateAsAdmin_UpdateUsersError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)
	usersForUpdate := []userentity.UserProductLink{
		{UserID: 1, ProductID: int64Ptr(1)},
	}

	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
		UsersForUpdate: &usersForUpdate,
	}

	updatedRole := roleentity.Role{
		ID:        roleID,
		Name:      "Updated Role",
		ActiveFlg: true,
	}
	expectedError := errors.New("update users error")

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(updatedRole, nil)
	roleMock.UpdateLinksWithUsersMock.Expect(ctx, roleID, usersForUpdate).Return(expectedError)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_UpdateAsAdmin_UpdateGroupsError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)
	groupsForUpdate := []groupentity.GroupID{
		{ID: 1},
	}

	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
		GroupsForUpdate: &groupsForUpdate,
	}

	updatedRole := roleentity.Role{
		ID:        roleID,
		Name:      "Updated Role",
		ActiveFlg: true,
	}
	expectedError := errors.New("update groups error")

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(updatedRole, nil)
	roleMock.UpdateLinksWithGroupsMock.Expect(ctx, roleID, groupsForUpdate).Return(expectedError)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_UpdateAsAdmin_UpdatePermissionsError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)
	permissions := []permissionentity.Permission{
		{ID: 1, Name: "perm1"},
	}

	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
		Permissions: &permissions,
	}

	updatedRole := roleentity.Role{
		ID:        roleID,
		Name:      "Updated Role",
		ActiveFlg: true,
	}
	expectedError := errors.New("update permissions error")

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(updatedRole, nil)
	roleMock.UpdateLinksWithPermissionsMock.Expect(ctx, roleID, permissions).Return(expectedError)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

func TestRoleAppService_UpdateAsAdmin_GetByIDError(t *testing.T) {
	participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock := createRoleMocks(t)
	service := NewRoleAppService(participantMock, productMock, roleMock, groupMock, userMock, categoryMock, permissionMock)

	ctx := context.Background()
	roleID := int64(1)

	updateData := roleaggregate.AdminRoleUpdate{
		RoleUpdateData: roleentity.RoleUpdateData{
			ID:        roleID,
			Name:      stringPtr("Updated Role"),
			ActiveFlg: boolPtr(true),
		},
	}

	updatedRole := roleentity.Role{
		ID:        roleID,
		Name:      "Updated Role",
		ActiveFlg: true,
	}
	expectedError := errors.New("get by id error")

	roleMock.UpdateMock.Expect(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		Name:      stringPtr("Updated Role"),
		ActiveFlg: boolPtr(true),
	}).Return(updatedRole, nil)
	roleMock.GetByIDMock.Expect(roleID).Return(roleentity.Role{}, expectedError)

	result, err := service.UpdateAsAdmin(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, roleaggregate.RoleWithDetails{}, result)
}

// helper functions
func int64Ptr(i int64) *int64 {
	return &i
}

func boolPtr(b bool) *bool {
	return &b
}
