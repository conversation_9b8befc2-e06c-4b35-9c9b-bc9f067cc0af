package service

import (
	"context"
	"errors"
	"slices"
	"strconv"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/role/query"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categoryservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service"
	groupservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service"
	participantservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service"
	permissionservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	roleservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/service"
	userservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service"
)

type RoleAppService struct {
	participantDomain participantservice.ParticipantDomainService
	productDomain     productservice.ProductDomainService
	roleDomain        roleservice.RoleDomainService
	groupDomain       groupservice.GroupDomainService
	userDomain        userservice.UserDomainService
	categoryDomain    categoryservice.CategoryDomainService
	permissionDomain  permissionservice.PermissionDomainService
}

func NewRoleAppService(
	participantDomain participantservice.ParticipantDomainService,
	productDomain productservice.ProductDomainService,
	roleDomain roleservice.RoleDomainService,
	groupDomain groupservice.GroupDomainService,
	userDomain userservice.UserDomainService,
	categoryDomain categoryservice.CategoryDomainService,
	permissionDomain permissionservice.PermissionDomainService,
) *RoleAppService {
	return &RoleAppService{
		participantDomain: participantDomain,
		productDomain:     productDomain,
		roleDomain:        roleDomain,
		groupDomain:       groupDomain,
		userDomain:        userDomain,
		categoryDomain:    categoryDomain,
		permissionDomain:  permissionDomain,
	}
}

func (s *RoleAppService) Create(ctx context.Context, role roleentity.RoleWithPermissions) (roleentity.RoleFull, error) {
	return s.roleDomain.Create(ctx, role)
}

func (s *RoleAppService) GetAllWithCategoryStats() ([]roleentity.RoleWithCategoryStates, error) {
	return s.roleDomain.GetAllWithCategoryStats()
}

func (s *RoleAppService) GetAvailableSystemRolesByUserAndCategory(userID int64, roles []roleentity.RoleWithStats, category entity.CategoryFull) ([]roleentity.RoleWithStats, error) {
	userRoles, err := s.roleDomain.GetRolesIDByUserID(userID)
	if err != nil {
		return nil, err

	}

	var availableUserRoles []roleentity.RoleWithStats
	for _, r := range roles {
		if slices.Contains(userRoles, r.RoleID) && r.IsSystem {
			availableUserRoles = append(availableUserRoles, r)
		}
	}

	for _, r := range *category.Roles {
		if !slices.Contains(availableUserRoles, r) && r.IsSystem {
			availableUserRoles = append(availableUserRoles, r)
		}
	}
	return availableUserRoles, nil
}

func (s *RoleAppService) GetByGroupID(groupID int64) ([]roleentity.Role, error) {
	return s.roleDomain.GetByGroupID(groupID)
}

func (s *RoleAppService) GetByParticipantID(participantID int64) ([]roleentity.Role, error) {
	return s.roleDomain.GetByParticipantID(participantID)
}

func (s *RoleAppService) GetByProductID(productID int64) ([]roleentity.RoleWithStats, error) {
	rl, err := s.roleDomain.GetByProductID(productID)
	if err != nil {
		return nil, errors.New("failed to get roles:" + strconv.FormatInt(productID, 10))
	}

	var roles []roleentity.RoleWithStats
	for _, r := range rl {
		if !r.IsSystem {
			roles = append(roles, r)
		}
	}
	return roles, nil
}

func (s *RoleAppService) GetByUserID(userID int64) ([]roleentity.Role, error) {
	return s.roleDomain.GetByUserID(userID)
}

func (s *RoleAppService) GetAllWithProductByUserID(userID int64) ([]roleentity.RoleWithProduct, error) {
	return s.roleDomain.GetAllWithProductByUserID(userID)
}

func (s *RoleAppService) GetFull(productID int64, roleID int64) (roleentity.RoleFull, error) {
	return s.roleDomain.GetFull(productID, roleID)
}

func (s *RoleAppService) GetSystemRoles(userID, categoryID int64) ([]roleentity.RoleWithStats, error) {

	category, err := s.categoryDomain.GetCategoryFull(categoryID)
	if err != nil {
		return nil, errors.New(err.Error() + "failed to get category" + strconv.FormatInt(categoryID, 10))
	}

	systemActiveRoles, err := s.roleDomain.GetBySystemTypeAndActive(true, true)
	if err != nil {
		return nil, errors.New(err.Error() + "failed to get system roles")
	}

	activeRoleIDs := make(map[int64]struct{})
	for _, r := range systemActiveRoles {
		activeRoleIDs[r.RoleID] = struct{}{}
	}

	var roles []roleentity.RoleWithStats
	for _, r := range *category.Roles {
		if _, exists := activeRoleIDs[r.RoleID]; exists && r.IsSystem {
			roles = append(roles, r)
		}
	}

	userData, err := s.userDomain.GetByID(userID)
	if err != nil {
		return nil, errors.New(err.Error() + "failed to get user" + strconv.FormatInt(userID, 10))
	}

	if !userData.IsAdmin {
		return s.GetAvailableSystemRolesByUserAndCategory(userID, roles, category)
	}

	return roles, nil
}

func (s *RoleAppService) GetWithProductsByFilter(userID int64, query query.AdminRoles) ([]roleentity.AdminRole, error) {
	userRoles, err := s.roleDomain.GetAllWithProductByUserID(userID)
	if err != nil {
		return []roleentity.AdminRole{}, err
	}

	roles := make([]roleentity.AdminRole, 0, len(userRoles))
	for _, i := range userRoles {

		role := roleentity.AdminRole{
			ID:       i.ID,
			Name:     i.Name,
			IsSystem: i.IsSystem,
			IsActive: i.ActiveFlg,
		}

		if i.Product != nil {
			role.Product = &productentity.ProductBasic{
				ID:       i.Product.ID,
				IID:      &i.Product.IID,
				TechName: i.Product.TechName,
				Name:     i.Product.Name,
			}
		}

		roles = append(roles, role)
	}

	filteredRoles := filterRoles(roles, query)
	sortedRoles := sortRoles(filteredRoles, query)

	return sortedRoles, nil
}

func (s *RoleAppService) IsRoleProtectedByRoleID(roleID int64) (bool, error) {
	role, err := s.roleDomain.GetByID(roleID)
	if err != nil {
		return false, err
	}
	return role.IsProtected, nil
}

func (s *RoleAppService) UpdateByProductIDAndRoleFull(ctx context.Context, productID int64, ru roleentity.RoleFull) (roleentity.RoleFull, error) {
	return s.roleDomain.UpdateByProductIDAndRoleFull(ctx, productID, ru)
}

func (s *RoleAppService) UpdateParticipant(ctx context.Context, id int64, participantID int64, groupIDs []int64) ([]int64, error) {
	return s.roleDomain.UpdateParticipant(ctx, id, participantID, groupIDs)
}

func (s *RoleAppService) UpdateWithCategories(rolesWithCategories []roleentity.RoleWithCategoryStates) error {
	return s.roleDomain.UpdateWithCategories(rolesWithCategories)
}

func (s *RoleAppService) Delete(ctx context.Context, productID, roleID int64) error {
	return s.roleDomain.Delete(ctx, productID, roleID)
}
