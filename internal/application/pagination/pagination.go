package pagination

import "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"

// Query is an interface for queries with pagination.
type Query interface {
	GetLimit() *int64
	GetOffset() *int64
}

// Apply applies pagination to any type of slice.
func Apply[T any](items []T, query Query) ([]T, int64, int64) {

	// Extract pagination parameters
	limit, offset := extractParams(query.GetLimit(), query.GetOffset())

	// Calculate boundaries
	total := int64(len(items))
	start, end := calculateBounds(total, limit, offset)

	// Check if out of bounds
	if start >= total {
		return nil, limit, offset
	}

	return items[start:end], limit, offset
}

// calculateBounds calculates the boundaries for the slice
func calculateBounds(total, limit, offset int64) (start, end int64) {
	start = offset
	if start < 0 {
		start = 0
	}

	end = start + limit
	if end > total {
		end = total
	}

	return start, end
}

// extractParams extracts and validates pagination parameters
func extractParams(queryLimit, queryOffset *int64) (limit, offset int64) {
	limit = constants.DefaultLimit
	offset = constants.DefaultOffset

	if queryLimit != nil {
		limit = *queryLimit
	}
	if queryOffset != nil {
		offset = *queryOffset
	}

	return limit, offset
}
