package pagination

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
)

type mockQuery struct {
	limit  *int64
	offset *int64
}

func (m mockQuery) GetLimit() *int64 {
	return m.limit
}

func (m mockQuery) GetOffset() *int64 {
	return m.offset
}

// Helper functions
func int64Ptr(v int64) *int64 {
	return &v
}

func TestApply_SuccessfulPagination(t *testing.T) {
	tests := []struct {
		name           string
		items          []int
		limit          *int64
		offset         *int64
		expectedResult []int
		expectedLimit  int64
		expectedOffset int64
	}{
		{
			name:           "basic pagination with limit and offset",
			items:          []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
			limit:          int64Ptr(3),
			offset:         int64Ptr(2),
			expectedResult: []int{3, 4, 5},
			expectedLimit:  3,
			expectedOffset: 2,
		},
		{
			name:           "pagination with only limit",
			items:          []int{1, 2, 3, 4, 5},
			limit:          int64Ptr(3),
			offset:         nil,
			expectedResult: []int{1, 2, 3},
			expectedLimit:  3,
			expectedOffset: constants.DefaultOffset,
		},
		{
			name:           "pagination with only offset",
			items:          []int{1, 2, 3, 4, 5},
			limit:          nil,
			offset:         int64Ptr(1),
			expectedResult: []int{2, 3, 4, 5},
			expectedLimit:  constants.DefaultLimit,
			expectedOffset: 1,
		},
		{
			name:           "limit exceeds available items",
			items:          []int{1, 2, 3},
			limit:          int64Ptr(10),
			offset:         int64Ptr(1),
			expectedResult: []int{2, 3},
			expectedLimit:  10,
			expectedOffset: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query := mockQuery{limit: tt.limit, offset: tt.offset}
			result, resultLimit, resultOffset := Apply(tt.items, query)

			require.Equal(t, tt.expectedResult, result)
			require.Equal(t, tt.expectedLimit, resultLimit)
			require.Equal(t, tt.expectedOffset, resultOffset)
		})
	}
}

func TestApply_DefaultValues(t *testing.T) {
	items := []int{1, 2, 3, 4, 5}
	query := mockQuery{} // nil limit and offset

	result, resultLimit, resultOffset := Apply(items, query)

	require.Equal(t, items, result)
	require.Equal(t, constants.DefaultLimit, resultLimit)
	require.Equal(t, constants.DefaultOffset, resultOffset)
}

func TestApply_EdgeCases(t *testing.T) {
	tests := []struct {
		name           string
		items          []int
		limit          *int64
		offset         *int64
		expectedResult []int
		expectedLimit  int64
		expectedOffset int64
		description    string
	}{
		{
			name:           "offset out of bounds",
			items:          []int{1, 2, 3},
			limit:          int64Ptr(5),
			offset:         int64Ptr(10),
			expectedResult: nil,
			expectedLimit:  5,
			expectedOffset: 10,
			description:    "should return nil when offset exceeds slice length",
		},
		{
			name:           "empty slice",
			items:          []int{},
			limit:          int64Ptr(5),
			offset:         int64Ptr(0),
			expectedResult: nil,
			expectedLimit:  5,
			expectedOffset: 0,
			description:    "should return nil for empty slice",
		},
		{
			name:           "negative offset",
			items:          []int{1, 2, 3, 4, 5},
			limit:          int64Ptr(3),
			offset:         int64Ptr(-5),
			expectedResult: []int{1, 2, 3},
			expectedLimit:  3,
			expectedOffset: -5,
			description:    "should treat negative offset as 0",
		},
		{
			name:           "zero limit",
			items:          []int{1, 2, 3, 4, 5},
			limit:          int64Ptr(0),
			offset:         int64Ptr(2),
			expectedResult: []int{},
			expectedLimit:  0,
			expectedOffset: 2,
			description:    "should return empty slice for zero limit",
		},
		{
			name:           "offset equals total length",
			items:          []int{1, 2, 3},
			limit:          int64Ptr(2),
			offset:         int64Ptr(3),
			expectedResult: nil,
			expectedLimit:  2,
			expectedOffset: 3,
			description:    "should return nil when offset equals total length",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query := mockQuery{limit: tt.limit, offset: tt.offset}
			result, resultLimit, resultOffset := Apply(tt.items, query)

			if tt.expectedResult == nil {
				require.Nil(t, result, tt.description)
			} else {
				require.Equal(t, tt.expectedResult, result, tt.description)
			}
			require.Equal(t, tt.expectedLimit, resultLimit)
			require.Equal(t, tt.expectedOffset, resultOffset)
		})
	}
}

func TestApply_TypeSafety(t *testing.T) {
	t.Run("different types work correctly", func(t *testing.T) {
		// Test with strings
		stringItems := []string{"a", "b", "c", "d", "e"}
		stringQuery := mockQuery{limit: int64Ptr(2), offset: int64Ptr(1)}
		stringResult, _, _ := Apply(stringItems, stringQuery)
		require.Equal(t, []string{"b", "c"}, stringResult)

		// Test with integers
		intItems := []int{10, 20, 30, 40, 50}
		intQuery := mockQuery{limit: int64Ptr(2), offset: int64Ptr(1)}
		intResult, _, _ := Apply(intItems, intQuery)
		require.Equal(t, []int{20, 30}, intResult)

		// Test with custom struct
		type testStruct struct {
			ID   int
			Name string
		}
		structItems := []testStruct{
			{ID: 1, Name: "first"},
			{ID: 2, Name: "second"},
			{ID: 3, Name: "third"},
		}
		structQuery := mockQuery{limit: int64Ptr(1), offset: int64Ptr(1)}
		structResult, _, _ := Apply(structItems, structQuery)
		require.Equal(t, []testStruct{{ID: 2, Name: "second"}}, structResult)
	})
}

func TestExtractParams_AllScenarios(t *testing.T) {
	tests := []struct {
		name           string
		inputLimit     *int64
		inputOffset    *int64
		expectedLimit  int64
		expectedOffset int64
	}{
		{
			name:           "both limit and offset provided",
			inputLimit:     int64Ptr(10),
			inputOffset:    int64Ptr(5),
			expectedLimit:  10,
			expectedOffset: 5,
		},
		{
			name:           "nil limit and offset",
			inputLimit:     nil,
			inputOffset:    nil,
			expectedLimit:  constants.DefaultLimit,
			expectedOffset: constants.DefaultOffset,
		},
		{
			name:           "nil limit only",
			inputLimit:     nil,
			inputOffset:    int64Ptr(3),
			expectedLimit:  constants.DefaultLimit,
			expectedOffset: 3,
		},
		{
			name:           "nil offset only",
			inputLimit:     int64Ptr(7),
			inputOffset:    nil,
			expectedLimit:  7,
			expectedOffset: constants.DefaultOffset,
		},
		{
			name:           "zero values",
			inputLimit:     int64Ptr(0),
			inputOffset:    int64Ptr(0),
			expectedLimit:  0,
			expectedOffset: 0,
		},
		{
			name:           "large values",
			inputLimit:     int64Ptr(1000),
			inputOffset:    int64Ptr(999),
			expectedLimit:  1000,
			expectedOffset: 999,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resultLimit, resultOffset := extractParams(tt.inputLimit, tt.inputOffset)
			require.Equal(t, tt.expectedLimit, resultLimit)
			require.Equal(t, tt.expectedOffset, resultOffset)
		})
	}
}

func TestCalculateBounds_AllScenarios(t *testing.T) {
	tests := []struct {
		name          string
		total         int64
		limit         int64
		offset        int64
		expectedStart int64
		expectedEnd   int64
	}{
		{
			name:          "normal bounds",
			total:         10,
			limit:         3,
			offset:        2,
			expectedStart: 2,
			expectedEnd:   5,
		},
		{
			name:          "negative offset becomes zero",
			total:         10,
			limit:         3,
			offset:        -5,
			expectedStart: 0,
			expectedEnd:   3,
		},
		{
			name:          "end exceeds total",
			total:         5,
			limit:         10,
			offset:        2,
			expectedStart: 2,
			expectedEnd:   5,
		},
		{
			name:          "zero total",
			total:         0,
			limit:         5,
			offset:        0,
			expectedStart: 0,
			expectedEnd:   0,
		},
		{
			name:          "zero limit",
			total:         10,
			limit:         0,
			offset:        3,
			expectedStart: 3,
			expectedEnd:   3,
		},
		{
			name:          "offset equals total",
			total:         5,
			limit:         3,
			offset:        5,
			expectedStart: 5,
			expectedEnd:   5,
		},
		{
			name:          "offset exceeds total",
			total:         3,
			limit:         2,
			offset:        10,
			expectedStart: 10,
			expectedEnd:   3,
		},
		{
			name:          "boundary case - offset at last position",
			total:         5,
			limit:         1,
			offset:        4,
			expectedStart: 4,
			expectedEnd:   5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end := calculateBounds(tt.total, tt.limit, tt.offset)
			require.Equal(t, tt.expectedStart, start)
			require.Equal(t, tt.expectedEnd, end)
		})
	}
}
