package identityprovider

import (
	"context"
	"time"

	"github.com/Nerzal/gocloak/v13"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type IdentityProvider struct {
	context     context.Context
	client      *gocloak.GoCloak
	token       *gocloak.JWT
	tokenExpiry time.Time
	url         string
	realm       string
	clientID    string
	username    string
	password    string
	grantType   string
	searchLimit int
}

type Settings struct {
	ProviderURL string
	Realm       string
	ClientID    string
	Username    string
	Password    string
	GrantType   string
	SearchLimit int
}

func NewIdentityProvider(c context.Context, s Settings) repository.IdentityProvider {
	return &IdentityProvider{
		context:     c,
		client:      gocloak.NewClient(s.ProviderURL),
		url:         s.ProviderURL,
		realm:       s.Realm,
		clientID:    s.<PERSON>lientID,
		username:    s.Username,
		password:    s.Password,
		grantType:   s.GrantType,
		searchLimit: s.SearchLimit,
	}
}

func (k *IdentityProvider) GetURL() string {
	return k.url
}

func (k *IdentityProvider) GetClientID() string {
	return k.clientID
}

func (k *IdentityProvider) GetRealm() string {
	return k.realm
}

func (k *IdentityProvider) NewToken() (*gocloak.JWT, error) {

	jwt, err := k.client.GetToken(
		k.context,
		k.realm,
		gocloak.TokenOptions{
			ClientID:  &k.clientID,
			Username:  &k.username,
			Password:  &k.password,
			GrantType: &k.grantType,
		},
	)
	if err != nil {
		return nil, err
	}

	k.tokenExpiry = time.Now().Add(time.Second * time.Duration(jwt.ExpiresIn))

	return jwt, nil
}

func (k *IdentityProvider) AccessToken() string {
	return k.token.AccessToken
}

func (k *IdentityProvider) EnsureToken() error {
	if time.Now().After(k.tokenExpiry) {
		newToken, err := k.NewToken()
		if err != nil {
			return err
		}
		k.token = newToken
	}
	return nil
}

func (k *IdentityProvider) GetVersion() (string, error) {
	err := k.EnsureToken()
	if err != nil {
		return "", err
	}

	serverInfo, err := k.client.GetServerInfo(k.context, k.token.AccessToken)
	if err != nil {
		return "", errkit.NewInternalErrorString(errkit.ObjectTypeIdentityProvider, "GetServerInfo", err.Error())
	}

	return *serverInfo.SystemInfo.Version, nil
}
