package identityprovider

import (
	"github.com/Nerzal/gocloak/v13"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

func (k *IdentityProvider) GetUsersBySearch(search string) ([]entity.KeycloakUser, error) {

	if err := k.EnsureToken(); err != nil {
		return nil, err
	}

	users, err := k.client.GetUsers(
		k.context,
		k.token.AccessToken,
		k.realm,
		gocloak.GetUsersParams{Search: &search},
	)
	if err != nil {
		return nil, errkit.NewInternalErrorString(errkit.ObjectTypeIdentityProvider, "GetUsers", err.Error())
	}

	return k.filterAndMapUsers(users), nil
}

func (k *IdentityProvider) filterAndMapUsers(users []*gocloak.User) []entity.KeycloakUser {
	result := make([]entity.KeycloakUser, 0, len(users))
	for _, u := range users {
		if !k.isValidUser(u) {
			continue
		}

		result = append(result, k.mapUser(u))
	}
	return result
}

func (k *IdentityProvider) isValidUser(u *gocloak.User) bool {
	if u.Enabled == nil ||
		u.ID == nil || u.Email == nil ||
		u.Username == nil || u.FirstName == nil ||
		u.LastName == nil {
		return false
	}
	return *u.Enabled
}

func (k *IdentityProvider) mapUser(u *gocloak.User) entity.KeycloakUser {
	var fullName, position, photo string
	if u.Attributes != nil {
		if title, ok := (*u.Attributes)["title"]; ok {
			position = title[0]
		}
		if photos, ok := (*u.Attributes)["photo"]; ok {
			photo = photos[0]
		}
		if fn, ok := (*u.Attributes)["fullName"]; ok {
			fullName = fn[0]
		}
	}

	return entity.KeycloakUser{
		ID:        *u.ID,
		Email:     *u.Email,
		Username:  *u.Username,
		FullName:  fullName,
		LastName:  *u.LastName,
		FirstName: *u.FirstName,
		Position:  position,
		Enabled:   *u.Enabled,
		Photo:     photo,
	}
}

func (k *IdentityProvider) GetUsersByEmail(email string) ([]entity.KeycloakUser, error) {

	if err := k.EnsureToken(); err != nil {
		return nil, err
	}

	users, err := k.client.GetUsers(
		k.context,
		k.token.AccessToken,
		k.realm,
		gocloak.GetUsersParams{
			Email: &email,
			Max:   &k.searchLimit,
		},
	)
	if err != nil {
		return nil, errkit.NewInternalErrorString(errkit.ObjectTypeIdentityProvider, "GetUsersByEmail", err.Error())
	}

	return k.filterAndMapUsers(users), nil
}
