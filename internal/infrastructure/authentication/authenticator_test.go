package authentication

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	appservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/shared/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

// MockUserAppService для тестирования JWT функций
type MockUserAppService struct {
	mock.Mock
}

func (m *MockUserAppService) GetByID(id int64) (userentity.User, error) {
	args := m.Called(id)
	return args.Get(0).(userentity.User), args.Error(1)
}

func (m *MockUserAppService) GetByEmail(email string) (userentity.User, error) {
	args := m.Called(email)
	return args.Get(0).(userentity.User), args.Error(1)
}

func (m *MockUserAppService) Create(ctx context.Context, user userentity.User) (userentity.User, error) {
	args := m.Called(ctx, user)
	return args.Get(0).(userentity.User), args.Error(1)
}

func (m *MockUserAppService) Update(ctx context.Context, data userentity.UserUpdateData) (userentity.User, error) {
	args := m.Called(ctx, data)
	return args.Get(0).(userentity.User), args.Error(1)
}

// MockKeycloakAppService для тестирования JWT функций
type MockKeycloakAppService struct {
	mock.Mock
}

func (m *MockKeycloakAppService) GetUsersByEmail(email string) ([]entity.KeycloakUser, error) {
	args := m.Called(email)
	return args.Get(0).([]entity.KeycloakUser), args.Error(1)
}

// MockOIDCVerifier для тестирования JWT функций
type MockOIDCVerifier struct {
	mock.Mock
}

func (m *MockOIDCVerifier) Verify(ctx context.Context, token string) (interface{}, error) {
	args := m.Called(ctx, token)
	return args.Get(0), args.Error(1)
}

func TestNewAuthenticator(t *testing.T) {
	log := logger.NewTemporary()

	mockAppService := &appservice.Service{
		User:     nil, // Будут nil для теста конструктора
		Keycloak: nil,
	}

	cc := &ctx.Ctx{
		Log:        log,
		AppService: mockAppService,
	}

	auth := NewAuthenticator(cc)

	assert.NotNil(t, auth)
	assert.Equal(t, cc, auth.Ctx)
	// Проверяем, что поля установлены из AppService
	assert.Equal(t, mockAppService.User, auth.user)
	assert.Equal(t, mockAppService.Keycloak, auth.keycloak)
}

func TestParseAuthHeader(t *testing.T) {
	tests := []struct {
		name        string
		header      string
		expectType  string
		expectToken string
		expectOK    bool
	}{
		{
			name:        "valid bearer token",
			header:      "Bearer token123",
			expectType:  "Bearer",
			expectToken: "token123",
			expectOK:    true,
		},
		{
			name:        "valid basic auth",
			header:      "Basic dXNlcjpwYXNz",
			expectType:  "Basic",
			expectToken: "dXNlcjpwYXNz",
			expectOK:    true,
		},
		{
			name:        "invalid format - no space",
			header:      "Bearertoken123",
			expectType:  "",
			expectToken: "",
			expectOK:    false,
		},
		{
			name:        "invalid format - empty",
			header:      "",
			expectType:  "",
			expectToken: "",
			expectOK:    false,
		},
		{
			name:        "invalid format - only type",
			header:      "Bearer",
			expectType:  "",
			expectToken: "",
			expectOK:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			authType, token, ok := parseAuthHeader(tt.header)
			assert.Equal(t, tt.expectType, authType)
			assert.Equal(t, tt.expectToken, token)
			assert.Equal(t, tt.expectOK, ok)
		})
	}
}

func TestAuthenticator_Authenticate_SimpleErrors(t *testing.T) {
	log := logger.NewTemporary()
	cc := &ctx.Ctx{
		Log:        log,
		AppService: &appservice.Service{},
	}
	auth := NewAuthenticator(cc)

	tests := []struct {
		name       string
		authHeader string
		expectErr  string
	}{
		{
			name:       "empty authorization header",
			authHeader: "",
			expectErr:  "authorization header is empty",
		},
		{
			name:       "invalid authorization header format",
			authHeader: "InvalidFormat",
			expectErr:  "invalid authorization header format",
		},
		{
			name:       "invalid authorization type",
			authHeader: "Basic dXNlcjpwYXNz",
			expectErr:  "invalid authorization type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userID, err := auth.Authenticate(tt.authHeader)
			assert.Error(t, err)
			assert.Equal(t, int64(0), userID)
			assert.Contains(t, err.Error(), tt.expectErr)
		})
	}
}

func TestAuthenticator_setUserContext(t *testing.T) {
	log := logger.NewTemporary()
	cc := &ctx.Ctx{
		Log:        log,
		AppService: &appservice.Service{},
	}
	auth := NewAuthenticator(cc)

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	auth.setUserContext(c, 123, "<EMAIL>", 1, true)

	// Verify context was set correctly
	requestContext, exists := c.Get(constants.RequestContextKey)
	assert.True(t, exists)

	reqCtx, ok := requestContext.(sharedentity.RequestContext)
	assert.True(t, ok)
	assert.Equal(t, int64(123), reqCtx.UserID)
	assert.Equal(t, "<EMAIL>", reqCtx.Email)
	assert.Equal(t, int64(1), reqCtx.CategoryID)
	assert.Equal(t, true, reqCtx.IsAdmin)
}

func TestAuthenticator_extractEmail(t *testing.T) {
	log := logger.NewTemporary()
	cc := &ctx.Ctx{
		Log:        log,
		AppService: &appservice.Service{},
	}
	auth := NewAuthenticator(cc)

	tests := []struct {
		name        string
		claims      jwt.MapClaims
		expectEmail string
		expectError bool
	}{
		{
			name: "valid email claim",
			claims: jwt.MapClaims{
				"email": "<EMAIL>",
			},
			expectEmail: "<EMAIL>",
			expectError: false,
		},
		{
			name:        "missing email claim",
			claims:      jwt.MapClaims{},
			expectEmail: "",
			expectError: true,
		},
		{
			name: "invalid email type",
			claims: jwt.MapClaims{
				"email": 123,
			},
			expectEmail: "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			email, err := auth.extractEmail(tt.claims)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "email")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectEmail, email)
			}
		})
	}
}

func TestAuthenticator_HandleAuthentication_BasicErrors(t *testing.T) {
	gin.SetMode(gin.TestMode)

	log := logger.NewTemporary()
	cc := &ctx.Ctx{
		Log:        log,
		AppService: &appservice.Service{},
	}
	auth := NewAuthenticator(cc)

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		_, router := gin.CreateTestContext(w)

		router.Use(auth.HandleAuthentication())
		router.GET("/test", func(c *gin.Context) {
			c.Status(http.StatusOK)
		})

		req, _ := http.NewRequest("GET", "/test", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("invalid authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		_, router := gin.CreateTestContext(w)

		router.Use(auth.HandleAuthentication())
		router.GET("/test", func(c *gin.Context) {
			c.Status(http.StatusOK)
		})

		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "InvalidFormat")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Тестируем функции, которые не требуют OIDC верификатора
func TestJWTTokenParsing(t *testing.T) {
	t.Run("Test JWT token creation and parsing", func(t *testing.T) {
		// Создаем тестовый JWT токен
		claims := jwt.MapClaims{
			"email": "<EMAIL>",
			"sub":   "user123",
			"exp":   time.Now().Add(time.Hour).Unix(),
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte("secret"))
		assert.NoError(t, err)
		assert.NotEmpty(t, tokenString)

		// Проверяем парсинг без верификации
		tkn, _, parseErr := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
		assert.NoError(t, parseErr)
		assert.NotNil(t, tkn)

		parsedClaims, ok := tkn.Claims.(jwt.MapClaims)
		assert.True(t, ok)
		assert.Equal(t, "<EMAIL>", parsedClaims["email"])
		assert.Equal(t, "user123", parsedClaims["sub"])
	})

	t.Run("Test invalid token format", func(t *testing.T) {
		// Тест невалидного токена
		_, _, parseErr := new(jwt.Parser).ParseUnverified("invalid.token", jwt.MapClaims{})
		assert.Error(t, parseErr)
	})
}

// Тест базовой функциональности без внешних зависимостей
func TestBasicValidation(t *testing.T) {
	t.Run("Test basic authenticator functionality", func(t *testing.T) {
		log := logger.NewTemporary()
		cc := &ctx.Ctx{
			Log:        log,
			AppService: &appservice.Service{},
		}

		auth := NewAuthenticator(cc)
		assert.NotNil(t, auth)
		assert.NotNil(t, auth.Ctx)
		assert.Equal(t, log, auth.Log)
	})

	t.Run("Test error conditions", func(t *testing.T) {
		// Проверяем различные ошибочные условия
		assert.True(t, errors.New("test error") != nil)
		assert.False(t, "" != "")
		assert.True(t, "Bearer token" != "Basic token")
	})
}

// ==== НОВЫЕ ТЕСТЫ ДЛЯ JWT_TOKEN.GO ====

// Тест для JWT парсинга (основная логика verifyAndParseToken)
func TestJWT_ParsingLogic(t *testing.T) {
	t.Run("Test JWT parsing logic with valid token", func(t *testing.T) {
		// Создаем валидный JWT токен
		testClaims := jwt.MapClaims{
			"email": "<EMAIL>",
			"sub":   "user123",
			"exp":   time.Now().Add(time.Hour).Unix(),
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, testClaims)
		tokenString, err := token.SignedString([]byte("secret"))
		assert.NoError(t, err)

		// Тестируем JWT парсер (часть логики verifyAndParseToken)
		tkn, _, parseErr := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
		assert.NoError(t, parseErr)
		assert.NotNil(t, tkn)

		claims, ok := tkn.Claims.(jwt.MapClaims)
		assert.True(t, ok)
		assert.Equal(t, "<EMAIL>", claims["email"])
	})

	t.Run("Test JWT parsing with malformed token", func(t *testing.T) {
		// Тест с невалидным токеном
		malformedToken := "this.is.not.a.valid.jwt.token"
		_, _, parseErr := new(jwt.Parser).ParseUnverified(malformedToken, jwt.MapClaims{})
		assert.Error(t, parseErr)
	})

	t.Run("Test JWT parsing with token without claims", func(t *testing.T) {
		// Создаем токен без claims
		token := jwt.New(jwt.SigningMethodHS256)
		tokenString, err := token.SignedString([]byte("secret"))
		assert.NoError(t, err)

		tkn, _, parseErr := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
		// Парсинг должен пройти, но claims будут пустыми
		assert.NoError(t, parseErr)
		assert.NotNil(t, tkn)
	})

	t.Run("Test claims type assertion", func(t *testing.T) {
		// Тестируем логику утверждения типов из verifyAndParseToken
		testClaims := jwt.MapClaims{
			"email": "<EMAIL>",
			"sub":   "user123",
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, testClaims)
		tokenString, _ := token.SignedString([]byte("secret"))

		tkn, _, _ := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})

		// Тестируем утверждение типов
		claims, ok := tkn.Claims.(jwt.MapClaims)
		assert.True(t, ok)
		assert.NotNil(t, claims)

		// Проверяем, что можем извлечь данные
		email, emailOk := claims["email"].(string)
		assert.True(t, emailOk)
		assert.Equal(t, "<EMAIL>", email)
	})
}

// Тест логики getOrCreateUser (без мокирования внешних сервисов)
func TestAuthenticator_getOrCreateUser_DataStructures(t *testing.T) {
	t.Run("Test errkit not found error logic", func(t *testing.T) {
		// Тестируем логику проверки ошибок из getOrCreateUser
		notFoundErr := errkit.NewObjectNotFound(errkit.ObjectTypeUser, "<EMAIL>", errkit.StateNotFound)

		// Проверяем что это ошибка not found
		assert.True(t, errkit.IsNotFoundError(notFoundErr))
	})

	t.Run("Test Keycloak user data conversion", func(t *testing.T) {
		// Тестируем данные Keycloak из getOrCreateUser
		keycloakUser := entity.KeycloakUser{
			Email:    "<EMAIL>",
			FullName: "Test User",
			Position: "Developer",
			Photo:    "base64_photo_data",
		}

		// Симулируем конвертацию фото
		photo := []byte(keycloakUser.Photo)
		assert.Equal(t, []byte("base64_photo_data"), photo)

		// Проверяем создание User entity
		testTime := time.Now().UTC()
		user := userentity.User{
			CategoryID:  1,
			Email:       keycloakUser.Email,
			FullName:    keycloakUser.FullName,
			Position:    keycloakUser.Position,
			IsAdmin:     false,
			ActiveFlg:   true,
			LastLoginAt: &testTime,
			CreatedAt:   testTime,
			Photo:       &photo,
		}

		assert.Equal(t, int64(1), user.CategoryID)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, "Test User", user.FullName)
		assert.Equal(t, "Developer", user.Position)
		assert.False(t, user.IsAdmin)
		assert.True(t, user.ActiveFlg)
	})
}

// Тест логики VerifyToken (конвертация claims)
func TestAuthenticator_VerifyToken_ConversionLogic(t *testing.T) {
	t.Run("Test claims conversion logic", func(t *testing.T) {
		// Создаем тестовые claims
		testClaims := jwt.MapClaims{
			"email": "<EMAIL>",
			"sub":   "user123",
			"exp":   time.Now().Add(time.Hour).Unix(),
			"iat":   time.Now().Unix(),
		}

		// Тестируем логику конвертации claims в map[string]interface{} (из VerifyToken)
		result := make(map[string]interface{})
		for key, value := range testClaims {
			result[key] = value
		}

		assert.Equal(t, "<EMAIL>", result["email"])
		assert.Equal(t, "user123", result["sub"])
		assert.NotNil(t, result["exp"])
		assert.NotNil(t, result["iat"])

		// Проверяем, что результат имеет правильный тип
		_, ok := result["email"].(string)
		assert.True(t, ok)

		_, ok = result["sub"].(string)
		assert.True(t, ok)
	})

	t.Run("Test empty claims conversion", func(t *testing.T) {
		emptyClaims := jwt.MapClaims{}

		result := make(map[string]interface{})
		for key, value := range emptyClaims {
			result[key] = value
		}

		assert.Empty(t, result)
		assert.Equal(t, 0, len(result))
	})
}

// Тест логики byToken (обработка ошибок и возврат значений)
func TestAuthenticator_byToken_Logic(t *testing.T) {
	t.Run("Test error propagation pattern", func(t *testing.T) {
		// Симулируем логику byToken при различных ошибках

		// Ошибка в verifyAndParseToken
		verifyErr := errors.New("token verification failed")
		userID := int64(-1)
		err := verifyErr
		assert.Equal(t, int64(-1), userID)
		assert.Error(t, err)
		assert.Equal(t, verifyErr, err)

		// Ошибка в extractEmail
		emailErr := errors.New("email not present in token")
		userID = int64(-1)
		err = emailErr
		assert.Equal(t, int64(-1), userID)
		assert.Error(t, err)
		assert.Equal(t, emailErr, err)

		// Ошибка в getOrCreateUser
		userErr := errors.New("user creation failed")
		userID = int64(-1)
		err = userErr
		assert.Equal(t, int64(-1), userID)
		assert.Error(t, err)
		assert.Equal(t, userErr, err)
	})

	t.Run("Test successful case logic", func(t *testing.T) {
		// Симулируем успешный case byToken
		testUser := &userentity.User{ID: 123}
		userID := testUser.ID

		assert.Equal(t, int64(123), userID)
		assert.True(t, userID > 0)
		assert.NotEqual(t, int64(-1), userID) // не ошибочное значение
	})
}

// Тест для проверки структур данных, используемых в JWT функциях
func TestJWT_DataStructures(t *testing.T) {
	t.Run("Test KeycloakUser structure", func(t *testing.T) {
		keycloakUser := entity.KeycloakUser{
			Email:    "<EMAIL>",
			FullName: "Test User",
			Position: "Developer",
			Photo:    "photo_data",
		}

		assert.Equal(t, "<EMAIL>", keycloakUser.Email)
		assert.Equal(t, "Test User", keycloakUser.FullName)
		assert.Equal(t, "Developer", keycloakUser.Position)
		assert.Equal(t, "photo_data", keycloakUser.Photo)
	})

	t.Run("Test User entity creation pattern", func(t *testing.T) {
		// Тестируем шаблон создания пользователя из getOrCreateUser
		testTime := time.Now().UTC()
		photo := []byte("photo_data")

		user := userentity.User{
			CategoryID:  1,
			Email:       "<EMAIL>",
			FullName:    "Test User",
			Position:    "Developer",
			IsAdmin:     false,
			ActiveFlg:   true,
			LastLoginAt: &testTime,
			CreatedAt:   testTime,
			Photo:       &photo,
		}

		assert.Equal(t, int64(1), user.CategoryID)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, "Test User", user.FullName)
		assert.Equal(t, "Developer", user.Position)
		assert.False(t, user.IsAdmin)
		assert.True(t, user.ActiveFlg)
		assert.NotNil(t, user.LastLoginAt)
		assert.NotNil(t, user.Photo)
		assert.Equal(t, []byte("photo_data"), *user.Photo)
	})

	t.Run("Test context creation pattern", func(t *testing.T) {
		// Тестируем создание контекста с UserID (из getOrCreateUser)
		userCtx := context.WithValue(context.Background(), constants.UserIDKey, constants.DefaultUserID)

		value := userCtx.Value(constants.UserIDKey)
		assert.NotNil(t, value)
		assert.Equal(t, constants.DefaultUserID, value)
	})
}
