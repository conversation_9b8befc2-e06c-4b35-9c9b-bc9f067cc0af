package authentication

import (
	"context"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

// byToken authenticates user by their JWT token.
// It returns user's ID upon successful authentication.
func (a *Authenticator) byToken(token string) (int64, error) {
	claims, err := a.verifyAndParseToken(token)
	if err != nil {
		return -1, err
	}

	email, err := a.extractEmail(claims)
	if err != nil {
		return -1, err
	}

	user, err := a.getOrCreateUser(email)
	if err != nil {
		return -1, err
	}

	return user.ID, nil
}

// verifyAndParseToken verifies token and parses its claims.
func (a *Authenticator) verifyAndParseToken(token string) (jwt.MapClaims, error) {
	// Verify token's signature and validity
	_, err := a.AuthOIDC.Verifier.Verify(a.AuthOIDC.CtxOIDC, token)
	if err != nil {
		return nil, err
	}

	// Parse token without verification to extract claims
	tkn, _, err := new(jwt.Parser).ParseUnverified(token, jwt.MapClaims{})
	if err != nil {
		a.Log.Errorf("parsing token: %v", err)
		return nil, errors.New("invalid token format")
	}

	// Assert claims as jwt.MapClaims
	claims, ok := tkn.Claims.(jwt.MapClaims)
	if !ok {
		a.Log.Error("invalid token claims structure")
		return nil, errors.New("invalid token claims")
	}

	return claims, nil
}

// extractEmail retrieves email from the token claims.
func (a *Authenticator) extractEmail(claims jwt.MapClaims) (string, error) {
	email, exists := claims["email"]
	if !exists {
		a.Log.Error("email not found in token claims")
		return "", errors.New("email not present in token")
	}

	emailStr, ok := email.(string)
	if !ok {
		a.Log.Error("email in token is not a string")
		return "", errors.New("invalid email format in token")
	}

	return emailStr, nil
}

// getOrCreateUser retrieves user by email or creates new one if not found.
func (a *Authenticator) getOrCreateUser(email string) (*userentity.User, error) {
	user, err := a.user.GetByEmail(email)
	if err == nil {
		return &user, nil
	}

	// Check if the error is due to the user not being found
	if !errkit.IsNotFoundError(err) {
		return nil, err
	}

	// Fetch user details from Keycloak
	userKC, errKC := a.keycloak.GetUsersByEmail(email)
	if errKC != nil {
		return nil, errKC
	}

	if len(userKC) == 0 {
		return nil, errors.New("no users found in Keycloak with the provided email")
	}

	// Create new user based on Keycloak data
	t := time.Now().UTC()
	photo := []byte(userKC[0].Photo)

	userCtx := context.WithValue(context.Background(), constants.UserIDKey, constants.DefaultUserID)
	userNew, errNew := a.user.Create(userCtx, userentity.User{
		CategoryID:  1, // default category
		Email:       userKC[0].Email,
		FullName:    userKC[0].FullName,
		Position:    userKC[0].Position,
		IsAdmin:     false,
		ActiveFlg:   true,
		LastLoginAt: &t,
		CreatedAt:   t,
		Photo:       &photo,
	})
	if errNew != nil {
		return nil, errNew
	}

	return &userNew, nil
}

// VerifyToken validates a token and returns claims or error
func (a *Authenticator) VerifyToken(token string) (map[string]any, error) {
	claims, err := a.verifyAndParseToken(token)
	if err != nil {
		return nil, err
	}

	// Convert to map[string]any
	result := make(map[string]any)
	for key, value := range claims {
		result[key] = value
	}

	return result, nil
}
