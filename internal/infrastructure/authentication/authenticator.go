package authentication

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	keycloakapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/keycloak/service"
	userapp "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/user/service"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

// Authenticator implements the authentication service
type Authenticator struct {
	*ctx.Ctx
	user     *userapp.UserAppService
	keycloak *keycloakapp.KeycloakAppService
}

// NewAuthenticator creates a new Authenticator instance
func NewAuthenticator(
	cc *ctx.Ctx,
) *Authenticator {
	return &Authenticator{
		Ctx:      cc,
		user:     cc.AppService.User,
		keycloak: cc.AppService.Keycloak,
	}
}

// Authenticate validates the authentication header and returns user ID
func (a *Authenticator) Authenticate(authHeader string) (int64, error) {
	if authHeader == "" {
		return 0, errors.New("authorization header is empty")
	}

	authType, token, ok := parseAuthHeader(authHeader)
	if !ok {
		return 0, errors.New("invalid authorization header format")
	}

	var userID int64
	var err error

	switch authType {
	case "Bearer":
		userID, err = a.byToken(token)
	default:
		return 0, errors.New("invalid authorization type")
	}

	if err != nil {
		return 0, err
	}

	return userID, nil
}

// setUserContext sets the user context in the Gin context
func (a *Authenticator) setUserContext(c *gin.Context, userID int64, email string, categoryID int64, isAdmin bool) {
	c.Set(constants.RequestContextKey, entity.RequestContext{
		UserID:     userID,
		Email:      email,
		CategoryID: categoryID,
		IsAdmin:    isAdmin,
	})
}

// parseAuthHeader parses the authorization header
func parseAuthHeader(header string) (authType, token string, ok bool) {
	parts := strings.SplitN(header, " ", 2)
	if len(parts) != 2 {
		return "", "", false
	}
	return parts[0], parts[1], true
}

// HandleAuthentication returns a middleware that handles authentication
func (a *Authenticator) HandleAuthentication() gin.HandlerFunc {
	return func(c *gin.Context) {
		var userID int64
		var err error

		// Сначала пробуем получить токен из заголовка Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			userID, err = a.Authenticate(authHeader)
		} else {
			// Если заголовка нет, пробуем получить токен из куки
			cookie, cookieErr := c.Cookie("auth_token")
			if cookieErr == nil && cookie != "" {
				a.Log.Info("Using auth_token cookie for authentication", "path", c.Request.URL.Path)
				userID, err = a.byToken(cookie)
			} else {
				a.Log.Warn("No authorization header or auth_token cookie found",
					"path", c.Request.URL.Path,
					"cookieErr", cookieErr,
					"hasAuthHeader", authHeader != "")
				err = errors.New("no authorization header or auth_token cookie found")
			}
		}

		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		user, err := a.user.GetByID(userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to retrieve user"})
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		a.setUserContext(c, userID, user.Email, user.CategoryID, user.IsAdmin)

		now := time.Now().UTC()
		_, err = a.user.Update(context.Background(), userentity.UserUpdateData{ID: userID, LastLoginAt: &now})
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update login time"})
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		c.Next()
	}
}
