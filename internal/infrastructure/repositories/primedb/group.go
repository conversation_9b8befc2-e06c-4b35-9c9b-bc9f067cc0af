package primedb

import (
	"context"
	"errors"
	"strconv"

	"github.com/jackc/pgx/v5"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/group"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type GroupDB struct {
	*db
	queries *sqlc.Queries
}

func NewGroupPrimeDB(db *db) repository.GroupPrimeDB {
	return &GroupDB{db, sqlc.New(db.client)}
}

func (g *GroupDB) Create(data entity.Group) (entity.Group, error) {

	groupDB, err := g.queries.CreateGroup(context.Background(), sqlc.CreateGroupParams{
		Name:      data.Name,
		ProductID: data.ProductID,
		IsSystem:  data.IsSystem,
	})
	if err != nil {
		// TODO: need custom error
		return entity.Group{}, err
	}

	createdGroup := entity.Group{
		ID:        groupDB.ID,
		Name:      groupDB.Name,
		ProductID: groupDB.ProductID,
		IsSystem:  groupDB.IsSystem,
		ActiveFlg: groupDB.ActiveFlg,
		CreatedAt: groupDB.CreatedAt,
		UpdatedAt: groupDB.UpdatedAt,
	}

	return createdGroup, nil
}

func (g *GroupDB) GetAll() ([]entity.Group, error) {

	groupsDB, err := g.queries.GetAllGroups(context.Background())
	if err != nil {
		return nil, err
	}

	groupsModels := make([]entity.Group, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groupsModels = append(groupsModels, entity.Group{
			ID:        groupDB.ID,
			Name:      groupDB.Name,
			ProductID: groupDB.ProductID,
			IsSystem:  groupDB.IsSystem,
			ActiveFlg: groupDB.ActiveFlg,
			CreatedAt: groupDB.CreatedAt,
			UpdatedAt: groupDB.UpdatedAt,
		})
	}

	return groupsModels, nil
}

func (g *GroupDB) GetByID(id int64) (entity.Group, error) {

	groupDB, err := g.queries.GetGroup(context.Background(), id)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.Group{}, errkit.NewObjectNotFound(errkit.ObjectTypeGroup, strconv.FormatInt(id, 10), errkit.StateNotFound)
		}
		return entity.Group{}, err
	}

	return entity.Group{
		ID:        groupDB.ID,
		Name:      groupDB.Name,
		ProductID: groupDB.ProductID,
		IsSystem:  groupDB.IsSystem,
		ActiveFlg: groupDB.ActiveFlg,
		CreatedAt: groupDB.CreatedAt,
		UpdatedAt: groupDB.UpdatedAt,
	}, nil
}

func (g *GroupDB) ExistByName(name string) (bool, error) {
	exist, err := g.queries.ExistByName(context.Background(), name)
	if err != nil {
		return false, err
	}
	return exist, nil

}

func (g *GroupDB) GetByProductID(productID int64) ([]entity.Group, error) {

	groupsDB, err := g.queries.GetByProductID(context.Background(), &productID)
	if err != nil {
		return nil, err
	}

	groups := make([]entity.Group, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groups = append(groups, entity.Group{
			ID:        groupDB.ID,
			Name:      groupDB.Name,
			ProductID: groupDB.ProductID,
			IsSystem:  groupDB.IsSystem,
			ActiveFlg: groupDB.ActiveFlg,
			CreatedAt: groupDB.CreatedAt,
			UpdatedAt: groupDB.UpdatedAt,
		})
	}

	return groups, nil
}

func (g *GroupDB) GetByProductIDAndIsActive(productID int64, isActive bool) ([]entity.AdminGroup, error) {
	groupsDB, err := g.queries.GetGroupsShortsAsAdminByProductID(context.Background(), sqlc.GetGroupsShortsAsAdminByProductIDParams{
		ProductID: &productID,
		ActiveFlg: isActive,
	})
	if err != nil {
		return nil, err
	}

	groups := make([]entity.AdminGroup, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groups = append(groups, entity.AdminGroup{
			ID:        groupDB.GroupID,
			Name:      groupDB.GroupName,
			Type:      groupDB.GroupType,
			IsActive:  groupDB.IsActive,
			UserCount: groupDB.UserCount,
			RoleCount: groupDB.RoleCount,
		})
	}

	return groups, nil
}

func (g *GroupDB) GetSystemGroupsWithStats() ([]entity.GroupWithStats, error) {

	groupsDB, err := g.queries.GetSystemGroupsWithStats(context.Background())
	if err != nil {
		return nil, err
	}

	groupsModels := make([]entity.GroupWithStats, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groupsModels = append(groupsModels, entity.GroupWithStats{
			GroupID:          groupDB.GroupID,
			GroupName:        groupDB.GroupName,
			IsSystem:         groupDB.IsSystem,
			RoleCount:        groupDB.RoleCount,
			ParticipantCount: groupDB.ParticipantCount,
			UserCount:        groupDB.UserCount,
		})
	}

	return groupsModels, nil
}

func (g *GroupDB) GetWithStatsByUserID(userID int64) ([]entity.GroupWithStats, error) {

	groupsDB, err := g.queries.GetWithStatsByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	groupsModels := make([]entity.GroupWithStats, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groupsModels = append(groupsModels, entity.GroupWithStats{
			GroupID:          groupDB.GroupID,
			GroupName:        groupDB.GroupName,
			IsSystem:         groupDB.IsSystem,
			RoleCount:        groupDB.RoleCount,
			ParticipantCount: groupDB.ParticipantCount,
			UserCount:        groupDB.UserCount,
		})
	}

	return groupsModels, nil
}

func (g *GroupDB) GetSystemGroups() ([]entity.Group, error) {
	groupsDB, err := g.queries.GetSystemGroups(context.Background())
	if err != nil {
		return nil, err
	}

	groupsModels := make([]entity.Group, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groupsModels = append(groupsModels, entity.Group{
			ID:        groupDB.ID,
			Name:      groupDB.Name,
			ProductID: groupDB.ProductID,
			IsSystem:  groupDB.IsSystem,
			ActiveFlg: groupDB.ActiveFlg,
			CreatedAt: groupDB.CreatedAt,
			UpdatedAt: groupDB.UpdatedAt,
			DeletedAt: groupDB.DeletedAt,
		})
	}

	return groupsModels, nil
}

func (g *GroupDB) GetAllByUserID(userID int64) ([]entity.Group, error) {
	groupsDB, err := g.queries.GetAllByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	groups := make([]entity.Group, 0, len(groupsDB))
	for _, groupDB := range groupsDB {
		groups = append(groups, entity.Group{
			ID:        groupDB.ID,
			Name:      groupDB.Name,
			ProductID: groupDB.ProductID,
			IsSystem:  groupDB.IsSystem,
			ActiveFlg: groupDB.ActiveFlg,
			CreatedAt: groupDB.CreatedAt,
			UpdatedAt: groupDB.UpdatedAt,
			DeletedAt: groupDB.DeletedAt,
		})
	}

	return groups, nil
}

func (g *GroupDB) GetAllWithProductIDByUserID(userID int64) ([]entity.GroupWithProductID, error) {
	rows, err := g.queries.GetAllWithProductIDByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	groups := make([]entity.GroupWithProductID, 0, len(rows))
	for _, row := range rows {
		groups = append(groups, entity.GroupWithProductID{
			ID:        row.ID,
			Name:      row.Name,
			IsSystem:  row.IsSystem,
			ActiveFlg: row.ActiveFlg,
			ProductID: row.ProductID,
		})
	}

	return groups, nil
}

func (g *GroupDB) GetGroupCategoryStates() ([]entity.GroupCategoryLink, error) {

	groupCategoryLinkDB, err := g.queries.GetGroupCategoryStates(context.Background())
	if err != nil {
		return nil, err
	}

	groupCategoryLinkModels := make([]entity.GroupCategoryLink, 0, len(groupCategoryLinkDB))
	for _, groupCategoryLinkDB := range groupCategoryLinkDB {
		groupCategoryLinkModels = append(groupCategoryLinkModels, entity.GroupCategoryLink{
			GroupID:      groupCategoryLinkDB.GroupID,
			CategoryID:   groupCategoryLinkDB.CategoryID,
			CategoryName: groupCategoryLinkDB.CategoryName,
			IsActive:     groupCategoryLinkDB.IsActive,
		})
	}

	return groupCategoryLinkModels, nil
}

func (g *GroupDB) GetByGroupIDAndProductID(groupID, productID int64) (entity.Group, error) {
	group, err := g.queries.GetByGroupIDAndProductID(context.Background(), sqlc.GetByGroupIDAndProductIDParams{
		ID:        groupID,
		ProductID: &productID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.Group{}, errkit.NewObjectNotFound(errkit.ObjectTypeGroup, strconv.FormatInt(groupID, 10), errkit.StateNotFound)
		}
		return entity.Group{}, err
	}

	return entity.Group{
		ID:        group.ID,
		Name:      group.Name,
		ProductID: group.ProductID,
		IsSystem:  group.IsSystem,
		ActiveFlg: group.ActiveFlg,
		CreatedAt: group.CreatedAt,
		UpdatedAt: group.UpdatedAt,
		DeletedAt: group.DeletedAt,
	}, nil
}

func (g *GroupDB) GetByFiltersAndPagination(filters entity.AdminGroupsFilter, paginationParams entity.PaginationParams) ([]entity.Group, int64, error) {

	group, err := g.queries.ListGroups(context.Background(), sqlc.ListGroupsParams{
		Search:     filters.Search,
		IsSystem:   IsSystemType(filters.Type),
		ProductIds: filters.Products,
		Status:     IsActive(filters.Status),
		Offset:     paginationParams.Offset,
		Limit:      paginationParams.Limit,
	})

	if err != nil {
		return nil, 0, err
	}

	groupTotal, err := g.queries.CountGroups(context.Background(), sqlc.CountGroupsParams{
		Search:     filters.Search,
		IsSystem:   IsSystemType(filters.Type),
		Status:     IsActive(filters.Status),
		ProductIds: filters.Products,
	})
	if err != nil {
		return nil, 0, err
	}

	groups := make([]entity.Group, 0, len(group))

	for _, g := range group {
		tmp := entity.Group{
			ID:        g.ID,
			Name:      g.Name,
			ProductID: g.ProductID,
			IsSystem:  g.IsSystem,
			ActiveFlg: g.ActiveFlg,
			CreatedAt: g.CreatedAt,
			UpdatedAt: g.UpdatedAt,
			DeletedAt: g.DeletedAt,
		}
		groups = append(groups, tmp)
	}
	return groups, groupTotal, nil
}

func (g *GroupDB) Update(data entity.GroupUpdateData) (entity.Group, error) {

	params := sqlc.UpdateGroupParams{
		ID: data.ID,
	}

	if data.Name != nil {
		params.Name = data.Name
	}
	if data.ProductID != nil {
		if *data.ProductID != 0 {
			params.ProductID = data.ProductID
		}
	}
	if data.IsSystem != nil {
		params.IsSystem = data.IsSystem
	}
	if data.ActiveFlg != nil {
		params.ActiveFlg = data.ActiveFlg
	}

	groupDB, err := g.queries.UpdateGroup(context.Background(), params)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.Group{}, errkit.NewObjectNotFound(errkit.ObjectTypeGroup, strconv.FormatInt(data.ID, 10), errkit.StateNotFound)
		}
		return entity.Group{}, err
	}

	updatedGroup := entity.Group{
		ID:        groupDB.ID,
		Name:      groupDB.Name,
		ProductID: groupDB.ProductID,
		IsSystem:  groupDB.IsSystem,
		ActiveFlg: groupDB.ActiveFlg,
		CreatedAt: groupDB.CreatedAt,
		UpdatedAt: groupDB.UpdatedAt,
	}

	return updatedGroup, nil
}

func IsSystemType(currentType string) *bool {
	if currentType == constants.SystemType {
		trueVal := true
		return &trueVal
	}
	if currentType == constants.CustomType {
		falseVal := false
		return &falseVal
	}
	return nil
}

func IsActive(status string) *bool {
	if status == constants.GroupActive {
		trueVal := true
		return &trueVal
	}
	if status == constants.GroupArchive {
		falseVal := false
		return &falseVal
	}
	return nil
}

func (g *GroupDB) DeleteGroup(groupID int64) error {
	return g.queries.DeleteByID(context.Background(), groupID)
}

func (g *GroupDB) DeactivateByIDs(groupIDs []int64) error {
	return g.queries.DeactivateByIDs(context.Background(), groupIDs)
}
