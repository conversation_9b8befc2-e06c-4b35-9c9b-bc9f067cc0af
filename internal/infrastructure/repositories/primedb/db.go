package primedb

import (
	"context"
	"fmt"
	"regexp"

	"github.com/jackc/pgx/v5/pgxpool"
)

type db struct {
	client   *pgxpool.Pool
	settings Settings
}

type Settings struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	SSLMode  string
}

var postgresVersionRegex = regexp.MustCompile(`PostgreSQL (\d+\.\d+)`)

func Connect(s Settings) (db, error) {
	connStr := fmt.Sprintf("user=%s password=%s dbname=%s host=%s port=%d sslmode=%s TimeZone=UTC",
		s.User,
		s.Password,
		s.Database,
		s.Host,
		s.Port,
		s.SSLMode,
	)
	pool, err := pgxpool.New(context.Background(), connStr)
	if err != nil {
		return db{}, err
	}

	return db{
		client:   pool,
		settings: s,
	}, nil
}

func (db *db) Close() error {
	db.client.Close()
	return nil
}

func extractVersion(fullVersion string) string {
	match := postgresVersionRegex.FindStringSubmatch(fullVersion)
	if len(match) < 2 {
		return "unknown"
	}
	return match[1]
}
