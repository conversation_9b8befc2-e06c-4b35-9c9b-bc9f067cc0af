package primedb

import (
	"context"
	"encoding/json"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/section"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

type SectionDB struct {
	*db
	queries *sqlc.Queries
}

func NewSectionPrimeDB(db *db) repository.SectionPrimeDB {
	return &SectionDB{db, sqlc.New(db.client)}
}

func (s *SectionDB) GetSectionsByProposalID(productID int64, proposalID int64) ([]entity.Stand, error) {
	sectionsDB, err := s.queries.GetSectionsByProposalID(context.Background(), sqlc.GetSectionsByProposalIDParams{
		ProductID: productID,
		SeqNum:    proposalID,
	})
	if err != nil {
		return nil, err
	}

	sections := make([]entity.Stand, 0, len(sectionsDB))
	for _, sectionDB := range sectionsDB {
		sections = append(sections, entity.Stand{
			ID:        sectionDB.ID,
			ProductID: sectionDB.ProductID,
			SeqNum:    sectionDB.SeqNum,
			Num:       sectionDB.Num,
			Data:      sectionDB.Data,
		})
	}

	return sections, nil
}

func (s *SectionDB) UpdateSectionsByProposalID(productID int64, proposalID int64, sections valueobject.Values) ([]entity.Stand, error) {
	valuesJSON, err := json.Marshal(sections)
	if err != nil {
		return nil, err
	}

	updatedSectionDB, err := s.queries.UpdateSectionsByProposalID(context.Background(), sqlc.UpdateSectionsByProposalIDParams{
		ProductID: productID,
		SeqNum:    proposalID,
		Values:    valuesJSON,
	})
	if err != nil {
		return nil, err
	}

	return []entity.Stand{
		{
			ID:        updatedSectionDB.ID,
			ProductID: updatedSectionDB.ProductID,
			SeqNum:    updatedSectionDB.SeqNum,
			Num:       updatedSectionDB.Num,
			Data:      updatedSectionDB.Data,
		},
	}, nil
}
