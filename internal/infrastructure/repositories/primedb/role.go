package primedb

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/role"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type RoleDB struct {
	*db
	queries *sqlc.Queries
}

func NewRolePrimeDB(db *db) repository.RolePrimeDB {
	return &RoleDB{db, sqlc.New(db.client)}
}

func (r *RoleDB) Create(ctx context.Context, data roleentity.Role) (roleentity.Role, error) {
	userID, ok := ctx.Value(constants.UserIDKey).(int64)
	if !ok {
		return roleentity.Role{}, fmt.Errorf("user ID is missing in context")
	}

	_, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	if err != nil {
		return roleentity.Role{}, fmt.Errorf("failed to set audit user_id: %w", err)
	}
	roleDB, err := r.queries.CreateRole(context.Background(), sqlc.CreateRoleParams{
		Name:      data.Name,
		IsSystem:  data.IsSystem,
		ProductID: data.ProductID,
	})
	if err != nil {
		// TODO: need custom error
		return roleentity.Role{}, err
	}

	createdRole := roleentity.Role{
		ID:        roleDB.ID,
		Name:      roleDB.Name,
		IsSystem:  roleDB.IsSystem,
		ActiveFlg: roleDB.ActiveFlg,
		CreatedAt: roleDB.CreatedAt,
		UpdatedAt: roleDB.UpdatedAt,
	}

	return createdRole, nil
}

func (r *RoleDB) GetAll() ([]roleentity.Role, error) {

	rolesDB, err := r.queries.GetAllRoles(context.Background())
	if err != nil {
		return nil, err
	}

	rolesModels := make([]roleentity.Role, 0, len(rolesDB))
	for _, roleDB := range rolesDB {
		rolesModels = append(rolesModels, roleentity.Role{
			ID:          roleDB.ID,
			Name:        roleDB.Name,
			ProductID:   roleDB.ProductID,
			IsSystem:    roleDB.IsSystem,
			ActiveFlg:   roleDB.ActiveFlg,
			CreatedAt:   roleDB.CreatedAt,
			UpdatedAt:   roleDB.UpdatedAt,
			DeletedAt:   roleDB.DeletedAt,
			IsProtected: roleDB.IsProtected,
		})
	}

	return rolesModels, nil
}

func (r *RoleDB) GetByID(id int64) (roleentity.Role, error) {

	roleDB, err := r.queries.GetRoleByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return roleentity.Role{}, errkit.NewObjectNotFound(errkit.ObjectTypeRole, strconv.FormatInt(id, 10), errkit.StateNotFound)
		}
		return roleentity.Role{}, err
	}

	return roleentity.Role{
		ID:          roleDB.ID,
		Name:        roleDB.Name,
		ProductID:   roleDB.ProductID,
		IsSystem:    roleDB.IsSystem,
		ActiveFlg:   roleDB.ActiveFlg,
		IsProtected: roleDB.IsProtected,
		CreatedAt:   roleDB.CreatedAt,
		UpdatedAt:   roleDB.UpdatedAt,
		DeletedAt:   roleDB.DeletedAt,
	}, nil
}

func (r *RoleDB) GetRolesWithProductByParticipantIDs(participantIDs []int64) ([]roleentity.Role, error) {

	roleDB, err := r.queries.GetRolesWithProductByParticipantIDs(context.Background(), participantIDs)
	if err != nil {
		return nil, err
	}

	var roles []roleentity.Role
	for _, role := range roleDB {
		roles = append(roles, roleentity.Role{
			ID:        role.ID,
			Name:      role.Name,
			ProductID: &role.ProductID,
			IsSystem:  role.IsSystem,
			ActiveFlg: role.ActiveFlg,
			CreatedAt: role.CreatedAt,
			UpdatedAt: role.UpdatedAt,
			DeletedAt: role.DeletedAt,
		})
	}

	return roles, nil
}

func (r *RoleDB) GetRoleWithProductByParticipantID(participantID int64) ([]roleentity.Role, error) {

	roleDB, err := r.queries.GetRoleWithProductByParticipantID(context.Background(), participantID)
	if err != nil {
		return nil, err
	}

	var roles []roleentity.Role
	for _, role := range roleDB {
		roles = append(roles, roleentity.Role{
			ID:        role.ID,
			Name:      role.Name,
			ProductID: &role.ProductID,
			IsSystem:  role.IsSystem,
			ActiveFlg: role.ActiveFlg,
			CreatedAt: role.CreatedAt,
			UpdatedAt: role.UpdatedAt,
			DeletedAt: role.DeletedAt,
		})
	}

	return roles, nil
}

func (r *RoleDB) GetByProductID(productID int64) ([]roleentity.Role, error) {

	rolesDB, err := r.queries.GetRolesByProductID(context.Background(), &productID)
	if err != nil {
		return nil, err
	}

	roles := make([]roleentity.Role, 0, len(rolesDB))
	for _, roleDB := range rolesDB {
		roles = append(roles, roleentity.Role{
			ID:          roleDB.ID,
			Name:        roleDB.Name,
			ProductID:   roleDB.ProductID,
			IsSystem:    roleDB.IsSystem,
			ActiveFlg:   roleDB.ActiveFlg,
			IsProtected: roleDB.IsProtected,
			CreatedAt:   roleDB.CreatedAt,
			UpdatedAt:   roleDB.UpdatedAt,
			DeletedAt:   roleDB.DeletedAt,
		})
	}

	return roles, nil
}

func (r *RoleDB) GetByRoleIDAndProductID(roleID, productID int64) (roleentity.Role, error) {
	role, err := r.queries.GetRoleByIDAndProductID(context.Background(), sqlc.GetRoleByIDAndProductIDParams{
		ID:        roleID,
		ProductID: &productID,
	})
	if err != nil {
		return roleentity.Role{}, err
	}

	return roleentity.Role{
		ID:          role.ID,
		Name:        role.Name,
		ProductID:   role.ProductID,
		IsSystem:    role.IsSystem,
		ActiveFlg:   role.ActiveFlg,
		IsProtected: role.IsProtected,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
		DeletedAt:   role.DeletedAt,
	}, nil
}

func (r *RoleDB) GetBySystemType(isSystemRole bool) ([]roleentity.Role, error) {
	roles, err := r.queries.GetRolesBySystemType(context.Background(), isSystemRole)
	if err != nil {
		return nil, err
	}

	rolesModels := make([]roleentity.Role, 0, len(roles))
	for _, role := range roles {
		rolesModels = append(rolesModels, roleentity.Role{
			ID:          role.ID,
			Name:        role.Name,
			ProductID:   role.ProductID,
			IsSystem:    role.IsSystem,
			ActiveFlg:   role.ActiveFlg,
			IsProtected: role.IsProtected,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
			DeletedAt:   role.DeletedAt,
		})
	}

	return rolesModels, nil
}

func (r *RoleDB) GetOwnerRole() (roleentity.Role, error) {
	role, err := r.queries.GetOwnerRole(context.Background())
	if err != nil {
		return roleentity.Role{}, err
	}
	return roleentity.Role{
		ID:          role.ID,
		Name:        role.Name,
		ProductID:   role.ProductID,
		IsSystem:    role.IsSystem,
		ActiveFlg:   role.ActiveFlg,
		IsProtected: role.IsProtected,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
		DeletedAt:   role.DeletedAt,
	}, nil
}

func (r *RoleDB) GetSystemRolesWithStats(ctx context.Context) ([]roleentity.RoleWithStats, error) {
	rows, err := r.queries.GetSystemRolesWithStats(ctx)
	if err != nil {
		return nil, err
	}

	result := make([]roleentity.RoleWithStats, 0, len(rows))
	for _, row := range rows {
		result = append(result, roleentity.RoleWithStats{
			RoleID:           row.RoleID,
			RoleName:         row.RoleName,
			IsSystem:         constants.ToBoolFromSystemType(row.RoleType),
			GroupCount:       row.GroupCount,
			ParticipantCount: row.ParticipantCount,
			UserCount:        row.UserCount,
		})
	}
	return result, nil
}

func (r *RoleDB) GetAllByUserID(userID int64) ([]roleentity.Role, error) {
	rolesDB, err := r.queries.GetAllByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	roles := make([]roleentity.Role, 0, len(rolesDB))
	for _, roleDB := range rolesDB {
		roles = append(roles, roleentity.Role{
			ID:        roleDB.ID,
			Name:      roleDB.Name,
			ProductID: roleDB.ProductID,
			IsSystem:  roleDB.IsSystem,
			ActiveFlg: roleDB.ActiveFlg,
			CreatedAt: roleDB.CreatedAt,
			UpdatedAt: roleDB.UpdatedAt,
			DeletedAt: roleDB.DeletedAt,
		})
	}

	return roles, nil
}

func (r *RoleDB) GetAllWithProductIDByUserID(userID int64) ([]roleentity.RoleWithProductID, error) {
	rows, err := r.queries.GetAllWithProductIDByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	result := make([]roleentity.RoleWithProductID, 0, len(rows))
	for _, row := range rows {
		result = append(result, roleentity.RoleWithProductID{
			ID:        row.ID,
			Name:      row.Name,
			IsSystem:  row.IsSystem,
			ActiveFlg: row.ActiveFlg,
			ProductID: row.ProductID,
		})
	}

	return result, nil
}

func (r *RoleDB) GetRoleCategoryLinks(ctx context.Context) ([]roleentity.RoleCategoryLink, error) {
	rows, err := r.queries.GetRoleCategoryStates(ctx)
	if err != nil {
		return nil, err
	}

	result := make([]roleentity.RoleCategoryLink, 0, len(rows))
	for _, row := range rows {
		result = append(result, roleentity.RoleCategoryLink{
			RoleID:       row.RoleID,
			CategoryID:   row.CategoryID,
			CategoryName: row.CategoryName,
			IsActive:     row.IsActive,
		})
	}
	return result, nil
}

func (r *RoleDB) ExistByName(name string) (bool, error) {
	exists, err := r.queries.ExistByName(context.Background(), name)
	if err != nil {
		return false, err
	}
	return exists, nil
}

func (r *RoleDB) Update(ctx context.Context, data roleentity.RoleUpdateData) (roleentity.Role, error) {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return roleentity.Role{}, fmt.Errorf("user ID is missing in context")
	// }

	// _, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	// if err != nil {
	// 	return roleentity.Role{}, fmt.Errorf("failed to set audit user_id: %w", err)
	// }

	params := sqlc.UpdateRoleParams{
		ID: data.ID,
	}

	if data.Name != nil {
		params.Name = data.Name
	}

	if data.ProductID != nil {
		if *data.ProductID != 0 {
			params.ProductID = data.ProductID
		}
	}

	if data.IsSystem != nil {
		params.IsSystem = data.IsSystem
	}

	if data.ActiveFlg != nil {
		params.ActiveFlg = data.ActiveFlg
	}

	isProtected, err := r.queries.IsRoleProtected(context.Background(), data.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return roleentity.Role{}, errkit.NewObjectNotFound("role", strconv.FormatInt(data.ID, 10), "is not found")
		}
		return roleentity.Role{}, err
	}

	if isProtected && data.ActiveFlg != nil && !*data.ActiveFlg {
		return roleentity.Role{}, errkit.NewObjectProtected("role", strconv.FormatInt(data.ID, 10), "is protected")
	}

	role, err := r.queries.UpdateRole(context.Background(), params)
	if err != nil {
		return roleentity.Role{}, err
	}

	return roleentity.Role{
		ID:          role.ID,
		Name:        role.Name,
		ProductID:   role.ProductID,
		IsSystem:    role.IsSystem,
		ActiveFlg:   role.ActiveFlg,
		IsProtected: role.IsProtected,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
		DeletedAt:   role.DeletedAt,
	}, nil
}

func (r *RoleDB) GetRolesWithStats(productID int64) ([]roleentity.RoleWithStats, error) {
	rolesDB, err := r.queries.GetRolesShorts(context.Background(), &productID)
	if err != nil {
		return nil, err
	}

	roles := make([]roleentity.RoleWithStats, 0, len(rolesDB))
	for _, roleDB := range rolesDB {
		roles = append(roles, roleentity.RoleWithStats{
			RoleID:           roleDB.RoleID,
			RoleName:         roleDB.RoleName,
			IsSystem:         constants.ToBoolFromSystemType(roleDB.RoleType),
			GroupCount:       roleDB.GroupCount,
			ParticipantCount: roleDB.ParticipantCount,
			UserCount:        roleDB.UserCount,
		})
	}

	return roles, nil
}

func (r *RoleDB) GetByProductIDAndIsActive(productID int64, isActive bool) ([]roleentity.AdminRole, error) {
	rolesDB, err := r.queries.GetRolesShortsAsAdminByProductID(context.Background(), sqlc.GetRolesShortsAsAdminByProductIDParams{
		ProductID: &productID,
		ActiveFlg: isActive,
	})
	if err != nil {
		return nil, err
	}

	roles := make([]roleentity.AdminRole, 0, len(rolesDB))
	for _, roleDB := range rolesDB {
		roles = append(roles, roleentity.AdminRole{
			ID:         roleDB.RoleID,
			Name:       roleDB.RoleName,
			IsActive:   roleDB.IsActive,
			IsSystem:   constants.ToBoolFromSystemType(roleDB.RoleType),
			UserCount:  roleDB.UserCount,
			GroupCount: roleDB.GroupCount,
		})
	}

	return roles, nil
}

func (r *RoleDB) DeactivateByIDs(roleIDs []int64) error {
	return r.queries.DeactivateByIDs(context.Background(), roleIDs)
}
