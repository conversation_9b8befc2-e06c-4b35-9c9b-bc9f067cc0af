package primedb

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlcproposalhistory "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/proposalhistory"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository"
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

type ProposalHistoryDB struct {
	*db
	queries *sqlcproposalhistory.Queries
}

func NewProposalHistoryDB(db *db) repository.ProposalHistoryDB {
	return &ProposalHistoryDB{db, sqlcproposalhistory.New(db.client)}
}

func (p *ProposalHistoryDB) CreateMessage(proposalID int64, message string, userID int64) error {
	_, err := p.queries.Create(context.Background(), sqlcproposalhistory.CreateParams{
		ProposalID: proposalID,
		EventType:  constants.InternalEventMessage.String(),
		Status:     "", // For messages, status is empty
		Message:    &message,
		UserID:     &userID,
	})
	return err
}

func (p *ProposalHistoryDB) GetByProposalID(proposalID int64) ([]voproposal.HistoryRecord, error) {
	proposalHistoryDB, err := p.queries.GetByProposalID(context.Background(), proposalID)
	if err != nil {
		return nil, err
	}

	proposalHistory := make([]voproposal.HistoryRecord, len(proposalHistoryDB))
	for i, history := range proposalHistoryDB {
		proposalHistory[i] = voproposal.HistoryRecord{
			ID:         history.ID,
			ProposalID: history.ProposalID,
			Status:     history.Status,
			Message:    history.Message,
			EventType:  history.EventType,
			UserID:     history.UserID,
			CreatedAt:  history.CreatedAt,
		}
	}

	return proposalHistory, nil
}

func (p *ProposalHistoryDB) GetByUserID(userID int64) ([]voproposal.HistoryRecord, error) {
	proposalHistoryDB, err := p.queries.GetProposalIDAndUserID(context.Background(), &userID)
	if err != nil {
		return nil, err
	}

	proposalHistory := make([]voproposal.HistoryRecord, len(proposalHistoryDB))
	for i, history := range proposalHistoryDB {
		proposalHistory[i] = voproposal.HistoryRecord{
			ID:         history.ID,
			ProposalID: history.ProposalID,
			Status:     history.Status,
			Message:    history.Message,
			EventType:  history.EventType,
			UserID:     history.UserID,
			CreatedAt:  history.CreatedAt,
		}
	}

	return proposalHistory, nil
}

func (p *ProposalHistoryDB) GetUnreadEventsForUser(userID int64) ([]voproposal.HistoryRecord, error) {
	proposalHistoryDB, err := p.queries.GetUnreadEventsForUser(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	proposalHistory := make([]voproposal.HistoryRecord, len(proposalHistoryDB))
	for i, history := range proposalHistoryDB {
		proposalHistory[i] = voproposal.HistoryRecord{
			ID:         history.ID,
			ProposalID: history.ProposalID,
			Status:     history.Status,
			Message:    history.Message,
			EventType:  history.EventType,
			UserID:     history.UserID,
			CreatedAt:  history.CreatedAt,
		}
	}

	return proposalHistory, nil
}
