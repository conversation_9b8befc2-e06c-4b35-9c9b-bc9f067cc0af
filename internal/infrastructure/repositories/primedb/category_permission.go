package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/categorypermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
)

type CategoryPermissionDB struct {
	*db
	queries *sqlc.Queries
}

func NewCategoryPermissionPrimeDB(db *db) repository.CategoryPermissionPrimeDB {
	return &CategoryPermissionDB{db, sqlc.New(db.client)}
}

func (c *CategoryPermissionDB) Create(data entity.CategoryPermission) (entity.CategoryPermission, error) {
	categoryPermission, err := c.queries.CreateCategoryPermission(context.Background(), sqlc.CreateCategoryPermissionParams{
		CategoryID:   data.CategoryID,
		PermissionID: data.PermissionID,
	})
	if err != nil {
		return entity.CategoryPermission{}, err
	}

	return entity.CategoryPermission{
		ID:           categoryPermission.ID,
		CategoryID:   categoryPermission.CategoryID,
		PermissionID: categoryPermission.PermissionID,
	}, nil
}

func (c *CategoryPermissionDB) GetAll() ([]entity.CategoryPermission, error) {
	categoryPermissionsDB, err := c.queries.GetAllCategoryPermissions(context.Background())
	if err != nil {
		return nil, err
	}

	var categoryPermissions []entity.CategoryPermission
	for _, categoryPermissionDB := range categoryPermissionsDB {
		categoryPermissions = append(categoryPermissions, entity.CategoryPermission{
			ID:           categoryPermissionDB.ID,
			CategoryID:   categoryPermissionDB.CategoryID,
			PermissionID: categoryPermissionDB.PermissionID,
		})
	}
	return categoryPermissions, nil
}

func (c *CategoryPermissionDB) GetByID(id int64) (entity.CategoryPermission, error) {
	categoryPermissionDB, err := c.queries.GetCategoryPermissionByID(context.Background(), id)
	if err != nil {
		return entity.CategoryPermission{}, err
	}
	return entity.CategoryPermission{
		ID:           categoryPermissionDB.ID,
		CategoryID:   categoryPermissionDB.CategoryID,
		PermissionID: categoryPermissionDB.PermissionID,
	}, nil
}

func (c *CategoryPermissionDB) GetByCategoryID(categoryID int64) ([]entity.CategoryPermission, error) {
	categoryPermissionsDB, err := c.queries.GetCategoryPermissionsByCategoryID(context.Background(), categoryID)
	if err != nil {
		return nil, err
	}

	var categoryPermissions []entity.CategoryPermission
	for _, categoryPermissionDB := range categoryPermissionsDB {
		categoryPermissions = append(categoryPermissions, entity.CategoryPermission{
			ID:           categoryPermissionDB.ID,
			CategoryID:   categoryPermissionDB.CategoryID,
			PermissionID: categoryPermissionDB.PermissionID,
		})
	}
	return categoryPermissions, nil
}

func (c *CategoryPermissionDB) HasCategoryPermission(categoryID int64, permissionID int64) (bool, error) {
	return c.queries.HasCategoryPermission(context.Background(), sqlc.HasCategoryPermissionParams{
		CategoryID:   categoryID,
		PermissionID: permissionID,
	})
}

func (c *CategoryPermissionDB) DeleteCategoryPermission(categoryID int64, permissionID int64) error {
	return c.queries.DeleteCategoryPermission(context.Background(), sqlc.DeleteCategoryPermissionParams{
		CategoryID:   categoryID,
		PermissionID: permissionID,
	})
}
