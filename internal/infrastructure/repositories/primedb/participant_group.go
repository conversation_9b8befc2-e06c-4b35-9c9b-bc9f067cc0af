package primedb

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"github.com/jackc/pgx/v5"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/participantgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type ParticipantGroupDB struct {
	*db
	queries *sqlc.Queries
}

func NewParticipantGroupPrimeDB(db *db) repository.ParticipantGroupPrimeDB {
	return &ParticipantGroupDB{db, sqlc.New(db.client)}
}

func (r *ParticipantGroupDB) Create(ctx context.Context, participantID, groupID int64) (entity.ParticipantGroup, error) {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return &entity.ParticipantGroup{}, fmt.Errorf("user ID is missing in context")
	// }

	// _, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	pg, err := r.queries.CreateParticipantGroup(context.Background(), sqlc.CreateParticipantGroupParams{
		ParticipantID: participantID,
		GroupID:       groupID,
	})
	if err != nil {
		return entity.ParticipantGroup{}, err
	}
	return convertParticipantGroup(pg), nil
}

func (r *ParticipantGroupDB) GetAll() ([]entity.ParticipantGroup, error) {
	pgs, err := r.queries.GetAllParticipantGroups(context.Background())
	if err != nil {
		return nil, err
	}
	return convertParticipantGroups(pgs), nil
}

func (r *ParticipantGroupDB) GetByID(id int64) (entity.ParticipantGroup, error) {
	pg, err := r.queries.GetParticipantGroupByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.ParticipantGroup{}, errkit.NewObjectNotFound("participant_group", strconv.FormatInt(id, 10), "not found")
		}
		return entity.ParticipantGroup{}, err
	}
	return convertParticipantGroup(pg), nil
}

func (r *ParticipantGroupDB) GetByParticipantID(participantID int64) ([]entity.ParticipantGroup, error) {
	pgs, err := r.queries.GetParticipantGroupsByParticipantID(context.Background(), participantID)
	if err != nil {
		return nil, err
	}
	return convertParticipantGroups(pgs), nil
}

func (r *ParticipantGroupDB) GetByParticipantIDs(participantIDs []int64) ([]entity.ParticipantGroup, error) {
	pgs, err := r.queries.GetParticipantGroupsByParticipantIDs(context.Background(), participantIDs)
	if err != nil {
		return nil, err
	}
	return convertParticipantGroups(pgs), nil
}

func (r *ParticipantGroupDB) GetByGroupID(groupID int64) ([]entity.ParticipantGroup, error) {
	pgs, err := r.queries.GetParticipantGroupsByGroupID(context.Background(), groupID)
	if err != nil {
		return nil, err
	}
	return convertParticipantGroups(pgs), nil
}

func (r *ParticipantGroupDB) GetByParticipantIDAndGroupID(participantID, groupID int64) (entity.ParticipantGroup, error) {
	pg, err := r.queries.GetParticipantGroupByParticipantAndGroupID(context.Background(), sqlc.GetParticipantGroupByParticipantAndGroupIDParams{
		ParticipantID: participantID,
		GroupID:       groupID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.ParticipantGroup{}, errkit.NewObjectNotFound("participant_group", "participant_id="+strconv.FormatInt(participantID, 10)+", group_id="+strconv.FormatInt(groupID, 10), "not found")
		}
		return entity.ParticipantGroup{}, err
	}
	return convertParticipantGroup(pg), nil
}

func (r *ParticipantGroupDB) Update(ctx context.Context, id int64, participantID, groupID *int64) (entity.ParticipantGroup, error) {

	params := sqlc.UpdateParticipantGroupParams{
		ID: id,
	}
	if participantID != nil {
		params.ParticipantID = participantID
	}
	if groupID != nil {
		params.GroupID = groupID
	}
	userID, ok := ctx.Value(constants.UserIDKey).(int64)
	if !ok {
		return entity.ParticipantGroup{}, fmt.Errorf("user ID is missing in context")
	}

	_, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	if err != nil {
		return entity.ParticipantGroup{}, fmt.Errorf("failed to set audit user_id: %w", err)
	}
	pg, err := r.queries.UpdateParticipantGroup(context.Background(), params)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.ParticipantGroup{}, errkit.NewObjectNotFound("participant_group", strconv.FormatInt(id, 10), "not found")
		}
		return entity.ParticipantGroup{}, err
	}
	return convertParticipantGroup(pg), nil
}

func (r *ParticipantGroupDB) DeleteByParticipantID(ctx context.Context, participantID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	// if err != nil {
	// 	return fmt.Errorf("failed to set audit user_id: %w", err)
	// }
	return r.queries.DeleteParticipantGroupsByParticipantID(context.Background(), participantID)
}

func (r *ParticipantGroupDB) DeleteByGroupID(ctx context.Context, groupID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	// if err != nil {
	// 	return fmt.Errorf("failed to set audit user_id: %w", err)
	// }
	return r.queries.DeleteParticipantGroupsByGroupID(context.Background(), groupID)
}

func (r *ParticipantGroupDB) DeleteByParticipantIDs(ctx context.Context, participantIDs []int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	// if err != nil {
	// 	return fmt.Errorf("failed to set audit user_id: %w", err)
	// }
	return r.queries.DeleteParticipantGroupsByParticipantIDs(context.Background(), participantIDs)
}

func (r *ParticipantGroupDB) DeleteByParticipantAndGroupID(ctx context.Context, participantID, groupID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, _ = r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))

	return r.queries.DeleteParticipantGroupByParticipantAndGroupID(context.Background(), sqlc.DeleteParticipantGroupByParticipantAndGroupIDParams{
		ParticipantID: participantID,
		GroupID:       groupID,
	})
}

func (r *ParticipantGroupDB) DeleteByRoleIDAndParticipantIDs(ctx context.Context, groupID int64, participantIDs []int64) error {
	return r.queries.DeleteParticipantGroupsByGroupIDAndParticipantIDs(ctx, sqlc.DeleteParticipantGroupsByGroupIDAndParticipantIDsParams{
		GroupID:        groupID,
		ParticipantIds: participantIDs,
	})
}

func (r *ParticipantGroupDB) CreateByRoleIDAndParticipantIDs(ctx context.Context, groupID int64, participantIDs []int64) error {
	return r.queries.CreateByGroupIDAndParticipantIDs(context.Background(), sqlc.CreateByGroupIDAndParticipantIDsParams{
		GroupID:        groupID,
		ParticipantIds: participantIDs,
	})
}

func convertParticipantGroup(pg sqlc.ParticipantsGroup) entity.ParticipantGroup {
	return entity.ParticipantGroup{
		ID:            pg.ID,
		ParticipantID: pg.ParticipantID,
		GroupID:       pg.GroupID,
		CreatedAt:     pg.CreatedAt,
	}
}

func convertParticipantGroups(pgs []sqlc.ParticipantsGroup) []entity.ParticipantGroup {
	result := make([]entity.ParticipantGroup, len(pgs))
	for i, pg := range pgs {
		result[i] = convertParticipantGroup(pg)
	}
	return result
}
