package primedb

import (
	categoryrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	participantrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	permissionrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository"
	productrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	proposalrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository"
	rolerepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	sharedrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/repository"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

type Registry struct {
	Category           categoryrepository.CategoryPrimeDB
	CategoryGroup      categoryrepository.CategoryGroupPrimeDB
	CategoryPermission categoryrepository.CategoryPermissionPrimeDB
	CategoryRole       categoryrepository.CategoryRolePrimeDB
	DBVersion          sharedrepository.DBVersionPrimeDB
	Group              grouprepository.GroupPrimeDB
	GroupRole          grouprepository.GroupRolePrimeDB
	Permission         permissionrepository.PermissionPrimeDB
	Product            productrepository.ProductPrimeDB
	Proposal           proposalrepository.ProposalPrimeDB
	ProposalUserView   proposalrepository.UserViewDB
	ProposalHistory    proposalrepository.ProposalHistoryDB
	Section            proposalrepository.SectionPrimeDB
	Participant        participantrepository.ParticipantPrimeDB
	ParticipantGroup   participantrepository.ParticipantGroupPrimeDB
	ParticipantRole    participantrepository.ParticipantRolePrimeDB
	Role               rolerepository.RolePrimeDB
	RolePermission     rolerepository.RolePermissionPrimeDB
	User               userrepository.UserPrimeDB
	UserGroup          userrepository.UserGroupPrimeDB
	UserRole           userrepository.UserRolePrimeDB
	// Status             interfaces.StatusPrimeDB
}

func NewRegistry(db *db) *Registry {
	return &Registry{
		Category:           NewCategoryPrimeDB(db),
		CategoryGroup:      NewCategoryGroupPrimeDB(db),
		CategoryPermission: NewCategoryPermissionPrimeDB(db),
		CategoryRole:       NewCategoryRolePrimeDB(db),
		DBVersion:          NewDBVersionPrimeDB(db),
		Group:              NewGroupPrimeDB(db),
		GroupRole:          NewGroupRolePrimeDB(db),
		Permission:         NewPermissionPrimeDB(db),
		Product:            NewProductPrimeDB(db),
		Proposal:           NewProposalPrimeDB(db),
		ProposalHistory:    NewProposalHistoryDB(db),
		ProposalUserView:   NewUserViewDB(db),
		Section:            NewSectionPrimeDB(db),
		Participant:        NewParticipantPrimeDB(db),
		ParticipantGroup:   NewParticipantGroupPrimeDB(db),
		ParticipantRole:    NewParticipantRolePrimeDB(db),
		Role:               NewRolePrimeDB(db),
		RolePermission:     NewRolePermissionPrimeDB(db),
		User:               NewUserPrimeDB(db),
		UserGroup:          NewUserGroupDB(db),
		UserRole:           NewUserRoleDB(db),
		// Status:             NewStatusPrimeDB(db),
	}
}
