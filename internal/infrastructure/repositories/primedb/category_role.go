package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/categoryrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
)

type CategoryRoleDB struct {
	*db
	queries *sqlc.Queries
}

func NewCategoryRolePrimeDB(db *db) repository.CategoryRolePrimeDB {
	return &CategoryRoleDB{db, sqlc.New(db.client)}
}

func (c *CategoryRoleDB) Create(data entity.CategoryRoleLink) (entity.CategoryRoleLink, error) {
	categoryRole, err := c.queries.Create(context.Background(), sqlc.CreateParams{
		CategoryID: data.CategoryID,
		RoleID:     data.RoleID,
	})
	if err != nil {
		return entity.CategoryRoleLink{}, err
	}

	return entity.CategoryRoleLink{
		ID:         categoryRole.ID,
		CategoryID: categoryRole.CategoryID,
		RoleID:     categoryRole.RoleID,
	}, nil
}

func (c *CategoryRoleDB) GetAll() ([]entity.CategoryRoleLink, error) {
	categoryRolesDB, err := c.queries.GetAll(context.Background())
	if err != nil {
		return nil, err
	}

	var categoryRoles []entity.CategoryRoleLink
	for _, categoryRoleDB := range categoryRolesDB {
		categoryRoles = append(categoryRoles, entity.CategoryRoleLink{
			ID:         categoryRoleDB.ID,
			CategoryID: categoryRoleDB.CategoryID,
			RoleID:     categoryRoleDB.RoleID,
		})
	}
	return categoryRoles, nil
}

func (c *CategoryRoleDB) GetByID(id int64) (entity.CategoryRoleLink, error) {
	categoryRoleDB, err := c.queries.GetByID(context.Background(), id)
	if err != nil {
		return entity.CategoryRoleLink{}, err
	}
	return entity.CategoryRoleLink{
		ID:         categoryRoleDB.ID,
		CategoryID: categoryRoleDB.CategoryID,
		RoleID:     categoryRoleDB.RoleID,
	}, nil
}

func (c *CategoryRoleDB) GetByCategoryID(categoryID int64) ([]entity.CategoryRoleLink, error) {
	categoryRolesDB, err := c.queries.GetByCategoryID(context.Background(), categoryID)
	if err != nil {
		return nil, err
	}

	var categoryRoles []entity.CategoryRoleLink
	for _, categoryRoleDB := range categoryRolesDB {
		categoryRoles = append(categoryRoles, entity.CategoryRoleLink{
			ID:         categoryRoleDB.ID,
			CategoryID: categoryRoleDB.CategoryID,
			RoleID:     categoryRoleDB.RoleID,
		})
	}
	return categoryRoles, nil
}

func (c *CategoryRoleDB) GetByRoleID(roleID int64) ([]entity.CategoryRoleLink, error) {
	categoryRolesDB, err := c.queries.GetByRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}

	var categoryRoles []entity.CategoryRoleLink
	for _, categoryRoleDB := range categoryRolesDB {
		categoryRoles = append(categoryRoles, entity.CategoryRoleLink{
			ID:         categoryRoleDB.ID,
			CategoryID: categoryRoleDB.CategoryID,
			RoleID:     categoryRoleDB.RoleID,
		})
	}
	return categoryRoles, nil
}

func (c *CategoryRoleDB) HasCategoryRole(categoryID int64, roleID int64) (bool, error) {
	return c.queries.HasCategoryRole(context.Background(), sqlc.HasCategoryRoleParams{
		CategoryID: categoryID,
		RoleID:     roleID,
	})
}

func (c *CategoryRoleDB) Delete(categoryID int64, roleID int64) error {
	return c.queries.Delete(context.Background(), sqlc.DeleteParams{
		CategoryID: categoryID,
		RoleID:     roleID,
	})
}
