package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/rolepermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
)

type RolePermissionDB struct {
	*db
	queries *sqlc.Queries
}

func NewRolePermissionPrimeDB(db *db) repository.RolePermissionPrimeDB {
	return &RolePermissionDB{db, sqlc.New(db.client)}
}

func (r *RolePermissionDB) Create(ctx context.Context, data entity.RolePermission) (entity.RolePermission, error) {
	rolePermission, err := r.queries.CreateRolePermission(context.Background(), sqlc.CreateRolePermissionParams{
		RoleID:       data.RoleID,
		PermissionID: data.PermissionID,
	})
	if err != nil {
		return entity.RolePermission{}, err
	}

	return entity.RolePermission{
		ID:           rolePermission.ID,
		RoleID:       rolePermission.RoleID,
		PermissionID: rolePermission.PermissionID,
		CreatedAt:    rolePermission.CreatedAt,
	}, nil
}

func (r *RolePermissionDB) CreateByRoleIDAndPermissionIDs(ctx context.Context, roleID int64, permissionIDs []int64) error {
	return r.queries.CreateRolePermissionsByRoleIDAndPermissionIDs(context.Background(), sqlc.CreateRolePermissionsByRoleIDAndPermissionIDsParams{
		RoleID:        roleID,
		PermissionIds: permissionIDs,
	})
}

func (r *RolePermissionDB) GetAll() ([]entity.RolePermission, error) {
	rolePermissions, err := r.queries.GetAllRolePermissions(context.Background())
	if err != nil {
		return nil, err
	}

	result := make([]entity.RolePermission, len(rolePermissions))
	for i, rp := range rolePermissions {
		result[i] = entity.RolePermission{
			ID:           rp.ID,
			RoleID:       rp.RoleID,
			PermissionID: rp.PermissionID,
			CreatedAt:    rp.CreatedAt,
		}
	}
	return result, nil
}

func (r *RolePermissionDB) GetByID(id int64) (entity.RolePermission, error) {
	rolePermission, err := r.queries.GetRolePermissionByID(context.Background(), id)
	if err != nil {
		return entity.RolePermission{}, err
	}

	return entity.RolePermission{
		ID:           rolePermission.ID,
		RoleID:       rolePermission.RoleID,
		PermissionID: rolePermission.PermissionID,
		CreatedAt:    rolePermission.CreatedAt,
	}, nil
}

func (r *RolePermissionDB) GetByRoleID(roleID int64) ([]entity.RolePermission, error) {
	rolePermissions, err := r.queries.GetRolePermissionsByRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}

	result := make([]entity.RolePermission, len(rolePermissions))
	for i, rp := range rolePermissions {
		result[i] = entity.RolePermission{
			ID:           rp.ID,
			RoleID:       rp.RoleID,
			PermissionID: rp.PermissionID,
			CreatedAt:    rp.CreatedAt,
		}
	}
	return result, nil
}

func (r *RolePermissionDB) GetByPermissionID(permissionID int64) ([]entity.RolePermission, error) {
	rolePermissions, err := r.queries.GetRolePermissionsByPermissionID(context.Background(), permissionID)
	if err != nil {
		return nil, err
	}

	result := make([]entity.RolePermission, len(rolePermissions))
	for i, rp := range rolePermissions {
		result[i] = entity.RolePermission{
			ID:           rp.ID,
			RoleID:       rp.RoleID,
			PermissionID: rp.PermissionID,
			CreatedAt:    rp.CreatedAt,
		}
	}
	return result, nil
}

func (r *RolePermissionDB) GetByRolePermissionID(roleID, permissionID int64) (entity.RolePermission, error) {
	rolePermission, err := r.queries.GetRolePermissionByRolePermissionID(context.Background(), sqlc.GetRolePermissionByRolePermissionIDParams{
		RoleID:       roleID,
		PermissionID: permissionID,
	})
	if err != nil {
		return entity.RolePermission{}, err
	}

	return entity.RolePermission{
		ID:           rolePermission.ID,
		RoleID:       rolePermission.RoleID,
		PermissionID: rolePermission.PermissionID,
		CreatedAt:    rolePermission.CreatedAt,
	}, nil
}

func (r *RolePermissionDB) DeleteByID(id int64) error {
	return r.queries.DeleteByID(context.Background(), id)
}

func (r *RolePermissionDB) DeleteByRoleIDAndPermissionIDs(ctx context.Context, roleID int64, permissionIDs []int64) error {
	return r.queries.DeleteRolePermissionsByRoleIDAndPermissionIDs(context.Background(), sqlc.DeleteRolePermissionsByRoleIDAndPermissionIDsParams{
		RoleID:        roleID,
		PermissionIds: permissionIDs,
	})
}

func (r *RolePermissionDB) DeleteByRoleID(ctx context.Context, roleID int64) error {
	return r.queries.DeleteByRoleID(context.Background(), roleID)
}

func (r *RolePermissionDB) DeleteByPermissionID(permissionID int64) error {
	return r.queries.DeleteByPermissionID(context.Background(), permissionID)
}

func (r *RolePermissionDB) DeleteByRolePermissionID(roleID, permissionID int64) error {
	return r.queries.DeleteByRoleIDAndPermissionID(context.Background(), sqlc.DeleteByRoleIDAndPermissionIDParams{
		RoleID:       roleID,
		PermissionID: permissionID,
	})
}
