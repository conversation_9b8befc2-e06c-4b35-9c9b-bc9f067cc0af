package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/participant"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

type ParticipantDB struct {
	*db
	queries *sqlc.Queries
}

func NewParticipantPrimeDB(db *db) repository.ParticipantPrimeDB {
	return &ParticipantDB{db, sqlc.New(db.client)}
}

func (p *ParticipantDB) Create(ctx context.Context, data participantentity.ParticipantCreateData) (participantentity.Participant, error) {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return models.Participant{}, fmt.Errorf("user ID is missing in context")
	// }

	// _, err := p.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	// if err != nil {
	// 	return models.Participant{}, fmt.Errorf("failed to set audit user_id: %w", err)
	// }
	participantDB, err := p.queries.Create(context.Background(), sqlc.CreateParams{
		ProductID: data.ProductID,
		UserID:    data.UserID,
	})
	if err != nil {
		return participantentity.Participant{}, err
	}

	return participantentity.Participant{
		ID:        participantDB.ID,
		ProductID: participantDB.ProductID,
		UserID:    participantDB.UserID,
		CreatedAt: participantDB.CreatedAt,
		UpdatedAt: participantDB.UpdatedAt,
	}, nil
}

func (p *ParticipantDB) CreateByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error {
	return p.queries.CreateByUserIDAndProductIDs(ctx, sqlc.CreateByUserIDAndProductIDsParams{
		ProductIds: productIDs,
		UserID:     userID,
	})
}

func (p *ParticipantDB) GetByProductID(productID int64) ([]participantentity.Participant, error) {

	participantsDB, err := p.queries.GetByProductID(context.Background(), productID)
	if err != nil {
		return nil, err
	}

	var participants []participantentity.Participant
	for _, participantDB := range participantsDB {
		participants = append(participants, participantentity.Participant{
			ID:        participantDB.ID,
			ProductID: participantDB.ProductID,
			UserID:    participantDB.UserID,
			CreatedAt: participantDB.CreatedAt,
			UpdatedAt: participantDB.UpdatedAt,
		})
	}

	return participants, nil
}

func (p *ParticipantDB) ExistByParticipantIDAndProductID(participantID, productID int64) (bool, error) {

	participantDB, err := p.queries.GetByID(context.Background(), participantID)
	if err != nil {
		return false, err
	}

	result := participantDB.ID != 0 && participantDB.ProductID == productID
	return result, nil
}

func (p *ParticipantDB) ExistByUserIDAndProductID(userID, productID int64) (bool, error) {

	participantDB, err := p.queries.GetByUserIDAndProductID(context.Background(), sqlc.GetByUserIDAndProductIDParams{
		UserID:    userID,
		ProductID: productID,
	})
	if err != nil {
		return false, err
	}

	return participantDB.ID != 0, nil
}

func (p *ParticipantDB) GetByParticipantIDAndProductID(participantID, productID int64) (participantentity.Participant, error) {

	participantDB, err := p.queries.GetByIDAndProductID(context.Background(), sqlc.GetByIDAndProductIDParams{
		ID:        participantID,
		ProductID: productID,
	})
	if err != nil {
		return participantentity.Participant{}, err
	}

	return participantentity.Participant{
		ID:        participantDB.ID,
		ProductID: participantDB.ProductID,
		UserID:    participantDB.UserID,
		CreatedAt: participantDB.CreatedAt,
		UpdatedAt: participantDB.UpdatedAt,
	}, nil
}

func (p *ParticipantDB) GetByUserID(userID int64) ([]participantentity.Participant, error) {

	participantsDB, err := p.queries.GetByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	var participants []participantentity.Participant
	for _, participantDB := range participantsDB {
		participants = append(participants, participantentity.Participant{
			ID:        participantDB.ID,
			ProductID: participantDB.ProductID,
			UserID:    participantDB.UserID,
			CreatedAt: participantDB.CreatedAt,
			UpdatedAt: participantDB.UpdatedAt,
		})
	}

	return participants, nil
}

func (p *ParticipantDB) GetByUserIDAndProductID(userID, productID int64) (participantentity.Participant, error) {

	participantDB, err := p.queries.GetByUserIDAndProductID(context.Background(), sqlc.GetByUserIDAndProductIDParams{
		UserID:    userID,
		ProductID: productID,
	})
	if err != nil {
		return participantentity.Participant{}, err
	}

	return participantentity.Participant{
		ID:        participantDB.ID,
		ProductID: participantDB.ProductID,
		UserID:    participantDB.UserID,
		CreatedAt: participantDB.CreatedAt,
		UpdatedAt: participantDB.UpdatedAt,
	}, nil
}

func (p *ParticipantDB) GetByUserIDAndProductIDs(userID int64, productIDs []int64) ([]participantentity.Participant, error) {

	participantsDB, err := p.queries.GetByUserIDAndProductIDs(context.Background(), sqlc.GetByUserIDAndProductIDsParams{
		UserID:    userID,
		ProductID: productIDs,
	})
	if err != nil {
		return nil, err
	}

	var participants []participantentity.Participant
	for _, participantDB := range participantsDB {
		participants = append(participants, participantentity.Participant{
			ID:        participantDB.ID,
			ProductID: participantDB.ProductID,
			UserID:    participantDB.UserID,
			CreatedAt: participantDB.CreatedAt,
			UpdatedAt: participantDB.UpdatedAt,
		})
	}

	return participants, nil
}

func (p *ParticipantDB) GetByProductIDAndUserEmail(productID int64, email string) (participantentity.Participant, error) {
	participantDB, err := p.queries.GetByParticipantByProductIDAndUserEmail(context.Background(), sqlc.GetByParticipantByProductIDAndUserEmailParams{
		ProductID: productID,
		Email:     email,
	})
	if err != nil {
		return participantentity.Participant{}, err
	}
	return participantentity.Participant{
		ID:        participantDB.ID,
		ProductID: participantDB.ProductID,
		UserID:    participantDB.UserID,
	}, nil
}

func (p *ParticipantDB) GetOwnersByProductID(productID int64) ([]productentity.Owner, error) {
	prs, err := p.queries.GetOwnersByProductID(context.Background(), productID)
	if err != nil {
		return nil, err
	}
	owners := make([]productentity.Owner, len(prs))
	for i, pr := range prs {
		owners[i] = productentity.Owner{
			ProductID:     pr.ProductID,
			ParticipantID: pr.ID,
			UserID:        pr.UserID,
			FullName:      pr.FullName,
			Email:         pr.Email,
		}
	}
	return owners, nil
}

func (p *ParticipantDB) GetByRoleID(roleID int64) ([]participantentity.Participant, error) {
	participantsDB, err := p.queries.GetByRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}

	var participants []participantentity.Participant
	for _, participantDB := range participantsDB {
		participants = append(participants, participantentity.Participant{
			ID:        participantDB.ID,
			ProductID: participantDB.ProductID,
			UserID:    participantDB.UserID,
			CreatedAt: participantDB.CreatedAt,
			UpdatedAt: participantDB.UpdatedAt,
		})
	}

	return participants, nil
}

func (p *ParticipantDB) GetByGroupID(groupID int64) ([]participantentity.Participant, error) {
	participantsDB, err := p.queries.GetByGroupID(context.Background(), groupID)
	if err != nil {
		return nil, err
	}

	var participants []participantentity.Participant
	for _, participantDB := range participantsDB {
		participants = append(participants, participantentity.Participant{
			ID:        participantDB.ID,
			ProductID: participantDB.ProductID,
			UserID:    participantDB.UserID,
			CreatedAt: participantDB.CreatedAt,
			UpdatedAt: participantDB.UpdatedAt,
		})
	}

	return participants, nil
}

func (p *ParticipantDB) Delete(ctx context.Context, participantID, productID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return errors.New("user ID is missing in context")
	// }

	// _, err := p.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	return p.queries.DeleteByIDAndProductID(context.Background(), sqlc.DeleteByIDAndProductIDParams{
		ID:        participantID,
		ProductID: productID,
	})
}

func (p *ParticipantDB) DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error {
	return p.queries.DeleteByUserIDAndProductIDs(ctx, sqlc.DeleteByUserIDAndProductIDsParams{
		UserID:    userID,
		ProductID: productIDs,
	})
}

func (p *ParticipantDB) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error {
	return p.queries.DeleteByUserIDAndGroupIDs(ctx, sqlc.DeleteByUserIDAndGroupIDsParams{
		UserID:  userID,
		GroupID: groupIDs,
	})
}

func (p *ParticipantDB) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error {
	return p.queries.DeleteByUserIDAndRoleIDs(ctx, sqlc.DeleteByUserIDAndRoleIDsParams{
		UserID: userID,
		RoleID: roleIDs,
	})
}

func (p *ParticipantDB) DeleteByUserIDsAndProductID(ctx context.Context, productID int64, userID []int64) error {
	return p.queries.DeleteByUserIDsAndProductID(ctx, sqlc.DeleteByUserIDsAndProductIDParams{
		ProductID: productID,
		UserID:    userID,
	})
}
