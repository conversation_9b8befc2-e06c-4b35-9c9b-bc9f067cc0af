package primedb

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/jackc/pgx/v5"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/proposal"
	sqlcproposalhistory "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/proposalhistory"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository"
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type ProposalDB struct {
	*db
	queries                *sqlc.Queries
	queriesProposalhistory *sqlcproposalhistory.Queries
}

func NewProposalPrimeDB(db *db) repository.ProposalPrimeDB {
	return &ProposalDB{db, sqlc.New(db.client), sqlcproposalhistory.New(db.client)}
}

func (p *ProposalDB) Create(data entity.Proposal, values voproposal.Values) (entity.Proposal, error) {

	tx, err := p.client.BeginTx(context.Background(), pgx.TxOptions{})
	if err != nil {
		return entity.Proposal{}, err
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(context.Background())
			panic(r)
		} else if err != nil {
			_ = tx.Rollback(context.Background())
		} else {
			err = tx.Commit(context.Background())
		}
	}()

	valuesJSON, err := json.Marshal(values)
	if err != nil {
		return entity.Proposal{}, err
	}

	proposalType := voproposal.NewType(data.Type)
	proposalStatus := voproposal.NewStatus(data.Status)

	proposalDB, err := p.queries.WithTx(tx).CreateProposal(context.Background(), sqlc.CreateProposalParams{
		ProductID: data.ProductID,
		Price:     data.Price,
		Type:      proposalType.String(),
		Status:    proposalStatus.String(),
		CreatorID: data.CreatorID,
		Values:    valuesJSON,
	})
	if err != nil {
		return entity.Proposal{}, err
	}

	proposalNumber := ""
	if proposalDB.Number != nil {
		proposalNumber = *proposalDB.Number
	}

	createdProposal := entity.Proposal{
		ID:          proposalDB.ID,
		ProductID:   proposalDB.ProductID,
		ProdPropSeq: proposalDB.SeqNum,
		Price:       proposalDB.Price,
		Type:        proposalDB.Type,
		Status:      proposalDB.Status,
		CreatorID:   proposalDB.CreatorID,
		CreatedAt:   proposalDB.CreatedAt,
		UpdatedAt:   proposalDB.UpdatedAt,
		ActiveFlg:   proposalDB.ActiveFlg,
		Number:      proposalNumber,
	}

	message := "Proposal created"
	createProposalHistoryParams := sqlcproposalhistory.CreateParams{
		ProposalID: proposalDB.ID,
		EventType:  constants.InternalEventStatus.String(),
		Status:     proposalStatus.String(),
		Message:    &message,
		UserID:     &data.CreatorID,
	}
	_, err = p.queriesProposalhistory.WithTx(tx).Create(context.Background(), createProposalHistoryParams)
	if err != nil {
		return entity.Proposal{}, err
	}

	return createdProposal, nil
}

func (p *ProposalDB) GetAll() ([]entity.Proposal, error) {
	proposalDB, err := p.queries.GetAll(context.Background())
	if err != nil {
		return []entity.Proposal{}, err
	}

	ret := make([]entity.Proposal, 0)
	for _, p := range proposalDB {
		ret = append(ret, entity.Proposal{
			ID:          p.ID,
			ProductID:   p.ProductID,
			ProdPropSeq: p.SeqNum,
			Price:       p.Price,
			Type:        p.Type,
			Status:      p.Status,
			CreatorID:   p.CreatorID,
			CreatedAt:   p.CreatedAt,
			UpdatedAt:   p.UpdatedAt,
			ActiveFlg:   p.ActiveFlg,
			Number:      *p.Number,
		})
	}
	return ret, nil
}

func (p *ProposalDB) GetByID(id int64) (entity.Proposal, error) {
	proposalDB, err := p.queries.GetProposalByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return entity.Proposal{}, errkit.NewObjectNotFound(errkit.ObjectTypeProposal, strconv.FormatInt(id, 10), errkit.StateNotFound)
		}
		return entity.Proposal{}, err
	}

	return entity.Proposal{
		ID:          proposalDB.ID,
		ProductID:   proposalDB.ProductID,
		ProdPropSeq: proposalDB.SeqNum,
		Price:       proposalDB.Price,
		Type:        proposalDB.Type,
		Status:      proposalDB.Status,
		CreatorID:   proposalDB.CreatorID,
		CreatedAt:   proposalDB.CreatedAt,
		UpdatedAt:   proposalDB.UpdatedAt,
		ActiveFlg:   proposalDB.ActiveFlg,
		Number:      *proposalDB.Number,
	}, nil
}

func (p *ProposalDB) GetByProductID(productID int64) ([]entity.Proposal, error) {
	proposalsDB, err := p.queries.GetProposalsByProductID(context.Background(), productID)
	if err != nil {
		return nil, err
	}

	var proposalsActive []entity.Proposal
	for _, proposalDB := range proposalsDB {
		if !proposalDB.ActiveFlg {
			continue
		}
		proposalsActive = append(proposalsActive, entity.Proposal{
			ID:          proposalDB.ID,
			ProductID:   proposalDB.ProductID,
			ProdPropSeq: proposalDB.SeqNum,
			Price:       proposalDB.Price,
			Type:        proposalDB.Type,
			Status:      proposalDB.Status,
			CreatorID:   proposalDB.CreatorID,
			CreatedAt:   proposalDB.CreatedAt,
			UpdatedAt:   proposalDB.UpdatedAt,
			ActiveFlg:   proposalDB.ActiveFlg,
			Number:      *proposalDB.Number,
		})
	}

	return proposalsActive, nil
}

func (p *ProposalDB) GetByProductIDAndProposalID(productID, proposalID int64) (entity.Proposal, error) {
	proposalDB, err := p.queries.GetProposalByProductIDAndProposalID(context.Background(), sqlc.GetProposalByProductIDAndProposalIDParams{
		ProductID: productID,
		ID:        proposalID,
	})
	if err != nil {
		return entity.Proposal{}, err
	}

	if !proposalDB.ActiveFlg {
		return entity.Proposal{}, errkit.NewObjectNotFound(errkit.ObjectTypeProposal, strconv.FormatInt(proposalID, 10), errkit.StateNotFound)
	}

	return entity.Proposal{
		ID:          proposalDB.ID,
		ProductID:   proposalDB.ProductID,
		ProdPropSeq: proposalDB.SeqNum,
		Price:       proposalDB.Price,
		Type:        proposalDB.Type,
		Status:      proposalDB.Status,
		CreatorID:   proposalDB.CreatorID,
		CreatedAt:   proposalDB.CreatedAt,
		UpdatedAt:   proposalDB.UpdatedAt,
		ActiveFlg:   proposalDB.ActiveFlg,
		Number:      *proposalDB.Number,
	}, nil
}

func (p *ProposalDB) Update(ctx context.Context, data aggregate.ProposalUpdate) (aggregate.Proposal, error) {

	var (
		updProposal          sqlc.UpdateProposalParams
		changedStatus        string
		changedStatusMessage string
	)

	userID, ok := ctx.Value(constants.UserIDKey).(int64)
	if !ok {
		return aggregate.Proposal{}, fmt.Errorf("user ID is missing in context")
	}

	updProposal.ID = data.Proposal.ID
	updProposal.ProductID = data.Proposal.ProductID
	if data.Proposal.Price != 0 {
		updProposal.Price = &data.Proposal.Price
	}

	tx, err := p.client.BeginTx(context.Background(), pgx.TxOptions{})
	if err != nil {
		return aggregate.Proposal{}, err
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(context.Background())
			panic(r)
		} else if err != nil {
			_ = tx.Rollback(context.Background())
		} else {
			err = tx.Commit(context.Background())
		}
	}()

	if data.Proposal.Status != "" {
		changedStatus = data.Proposal.Status
		updProposal.Status = &changedStatus
		proposalCurrent, err := p.GetByID(data.Proposal.ID)
		if err != nil {
			return aggregate.Proposal{}, err
		}
		if proposalCurrent.Status != data.Proposal.Status {
			if data.Message != nil {
				changedStatusMessage = *data.Message
			}
		}
		_, err = p.queriesProposalhistory.WithTx(tx).Create(context.Background(), sqlcproposalhistory.CreateParams{
			ProposalID: data.Proposal.ID,
			Status:     changedStatus,
			Message:    &changedStatusMessage,
			EventType:  constants.InternalEventStatus.String(),
			UserID:     &userID,
		})
		if err != nil {
			return aggregate.Proposal{}, err
		}
	}

	if len(data.Stands) > 0 {
		values := make(voproposal.Values, len(data.Stands))
		for i, stand := range data.Stands {
			values[i] = stand.Data
		}

		valuesJSON, err := json.Marshal(values)
		if err != nil {
			return aggregate.Proposal{}, err
		}
		updProposal.Values = valuesJSON
	}

	updatedProposal, err := p.queries.WithTx(tx).UpdateProposal(context.Background(), updProposal)
	if err != nil {
		return aggregate.Proposal{}, err
	}

	proposalNumber := ""
	if updatedProposal.Number != nil {
		proposalNumber = *updatedProposal.Number
	}

	return aggregate.Proposal{
		Proposal: entity.Proposal{
			ID:          updatedProposal.ID,
			ProductID:   updatedProposal.ProductID,
			ProdPropSeq: updatedProposal.SeqNum,
			Price:       updatedProposal.Price,
			Type:        updatedProposal.Type,
			Status:      updatedProposal.Status,
			CreatorID:   updatedProposal.CreatorID,
			CreatedAt:   updatedProposal.CreatedAt,
			UpdatedAt:   updatedProposal.UpdatedAt,
			ActiveFlg:   updatedProposal.ActiveFlg,
			Number:      proposalNumber,
		},
		Stands: data.Stands,
	}, nil
}

func (p *ProposalDB) Delete(data entity.Proposal) error {
	_, err := p.queries.DeleteProposal(context.Background(), sqlc.DeleteProposalParams{
		ID:        data.ID,
		ProductID: data.ProductID,
	})
	if err != nil {
		return err
	}

	return nil
}
