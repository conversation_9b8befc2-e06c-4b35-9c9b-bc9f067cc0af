package primedb

import (
	"context"
	"errors"
	"strconv"

	"github.com/jackc/pgx/v5"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/participantrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type ParticipantRoleDB struct {
	*db
	queries *sqlc.Queries
}

func NewParticipantRolePrimeDB(db *db) repository.ParticipantRolePrimeDB {
	return &ParticipantRoleDB{db, sqlc.New(db.client)}
}

func (r *ParticipantRoleDB) Create(ctx context.Context, participantID, roleID int64) (entity.ParticipantRole, error) {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return &models.ParticipantRole{}, fmt.Errorf("user ID is missing in context")
	// }
	// _, err := r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))

	pr, err := r.queries.CreateParticipantRole(context.Background(), sqlc.CreateParticipantRoleParams{
		ParticipantID: participantID,
		RoleID:        roleID,
	})
	if err != nil {
		return entity.ParticipantRole{}, err
	}
	return convertParticipantRole(pr), nil
}

func (r *ParticipantRoleDB) CreateByRoleIDAndParticipantIDs(ctx context.Context, roleID int64, participantIDs []int64) error {
	return r.queries.CreateByRoleIDAndParticipantIDs(context.Background(), sqlc.CreateByRoleIDAndParticipantIDsParams{
		RoleID:         roleID,
		ParticipantIds: participantIDs,
	})
}

func (r *ParticipantRoleDB) GetAll() ([]entity.ParticipantRole, error) {
	prs, err := r.queries.GetAllParticipantRoles(context.Background())
	if err != nil {
		return nil, err
	}
	return convertParticipantRoles(prs), nil
}

func (r *ParticipantRoleDB) GetByID(id int64) (entity.ParticipantRole, error) {
	pr, err := r.queries.GetParticipantRoleByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.ParticipantRole{}, errkit.NewObjectNotFound("participant_role", strconv.FormatInt(id, 10), "not found")
		}
		return entity.ParticipantRole{}, err
	}
	return convertParticipantRole(pr), nil
}

func (r *ParticipantRoleDB) GetByParticipantID(participantID int64) ([]entity.ParticipantRole, error) {
	prs, err := r.queries.GetParticipantRolesByParticipantID(context.Background(), participantID)
	if err != nil {
		return nil, err
	}
	return convertParticipantRoles(prs), nil
}

func (r *ParticipantRoleDB) GetByParticipantIDs(participantIDs []int64) ([]entity.ParticipantRole, error) {
	prs, err := r.queries.GetParticipantRolesByParticipantIDs(context.Background(), participantIDs)
	if err != nil {
		return nil, err
	}
	return convertParticipantRoles(prs), nil
}

func (r *ParticipantRoleDB) GetByRoleID(roleID int64) ([]entity.ParticipantRole, error) {
	prs, err := r.queries.GetParticipantRolesByRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}
	return convertParticipantRoles(prs), nil
}

func (r *ParticipantRoleDB) GetByParticipantAndRoleID(participantID, roleID int64) (entity.ParticipantRole, error) {
	pr, err := r.queries.GetParticipantRoleByParticipantAndRoleID(context.Background(), sqlc.GetParticipantRoleByParticipantAndRoleIDParams{
		ParticipantID: participantID,
		RoleID:        roleID,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.ParticipantRole{}, errkit.NewObjectNotFound("participant_role", "participant_id="+strconv.FormatInt(participantID, 10)+", role_id="+strconv.FormatInt(roleID, 10), "not found")
		}
		return entity.ParticipantRole{}, err
	}
	return convertParticipantRole(pr), nil
}

func (r *ParticipantRoleDB) GetByRoleIDAndParticipantIDs(roleID int64, participantIDs []int64) ([]entity.ParticipantRole, error) {
	prs, err := r.queries.GetParticipantRolesByRoleIDAndParticipantIDs(context.Background(), sqlc.GetParticipantRolesByRoleIDAndParticipantIDsParams{
		RoleID:         roleID,
		ParticipantIds: participantIDs,
	})
	if err != nil {
		return nil, err
	}
	return convertParticipantRoles(prs), nil
}

func (r *ParticipantRoleDB) GetProductOwnersByParticipantID(participantID int64) ([]entity.ParticipantRole, error) {
	prs, err := r.queries.GetProductOwnersByParticipantID(context.Background(), participantID)
	if err != nil {
		return nil, err
	}
	return convertParticipantRoles(prs), nil
}

func (r *ParticipantRoleDB) IsOwner(participantID int64) (bool, error) {
	isOwner, err := r.queries.IsParticipantOwner(context.Background(), participantID)
	if err != nil {
		return false, err
	}
	return isOwner, nil
}

func (r *ParticipantRoleDB) DeleteByParticipantID(participantID int64) error {
	return r.queries.DeleteParticipantRolesByParticipantID(context.Background(), participantID)
}

func (r *ParticipantRoleDB) DeleteByRoleID(ctx context.Context, roleID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, _ = r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))

	return r.queries.DeleteParticipantRolesByRoleID(context.Background(), roleID)
}

func (r *ParticipantRoleDB) DeleteByParticipantAndRoleID(ctx context.Context, participantID, roleID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, _ = r.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))

	return r.queries.DeleteParticipantRoleByParticipantAndRoleID(context.Background(), sqlc.DeleteParticipantRoleByParticipantAndRoleIDParams{
		ParticipantID: participantID,
		RoleID:        roleID,
	})
}

func (r *ParticipantRoleDB) DeleteByParticipantIDs(participantIDs []int64) error {
	return r.queries.DeleteParticipantRolesByParticipantIDs(context.Background(), participantIDs)
}

func (r *ParticipantRoleDB) DeleteByRoleIDAndParticipantIDs(ctx context.Context, roleID int64, participantIDs []int64) error {
	return r.queries.DeleteParticipantRolesByRoleIDAndParticipantIDs(ctx, sqlc.DeleteParticipantRolesByRoleIDAndParticipantIDsParams{
		RoleID:         roleID,
		ParticipantIds: participantIDs,
	})
}

func convertParticipantRole(pr sqlc.ParticipantsRole) entity.ParticipantRole {
	return entity.ParticipantRole{
		ID:            pr.ID,
		ParticipantID: pr.ParticipantID,
		RoleID:        pr.RoleID,
		CreatedAt:     pr.CreatedAt,
	}
}

func convertParticipantRoles(prs []sqlc.ParticipantsRole) []entity.ParticipantRole {
	result := make([]entity.ParticipantRole, len(prs))
	for i, pr := range prs {
		result[i] = convertParticipantRole(pr)
	}
	return result
}
