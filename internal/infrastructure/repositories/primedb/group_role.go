package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/grouprole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
)

type GroupRoleDB struct {
	*db
	queries *sqlc.Queries
}

func NewGroupRolePrimeDB(db *db) repository.GroupRolePrimeDB {
	return &GroupRoleDB{db, sqlc.New(db.client)}
}

func (g *GroupRoleDB) Create(data entity.GroupRole) (entity.GroupRole, error) {

	groupRole, err := g.queries.CreateGroupRole(context.Background(), sqlc.CreateGroupRoleParams{
		GroupID: data.GroupID,
		RoleID:  data.RoleID,
	})
	if err != nil {
		return entity.GroupRole{}, err
	}

	return entity.GroupRole{
		ID:        groupRole.ID,
		GroupID:   groupRole.GroupID,
		RoleID:    groupRole.RoleID,
		CreatedAt: groupRole.CreatedAt,
	}, nil
}

func (g *GroupRoleDB) CreateByRoleIDAndGroupIDs(ctx context.Context, roleID int64, groupIDs []int64) error {
	return g.queries.CreateGroupRolesByRoleIDAndGroupIDs(ctx, sqlc.CreateGroupRolesByRoleIDAndGroupIDsParams{
		GroupIds: groupIDs,
		RoleID:   roleID,
	})
}

func (g *GroupRoleDB) CreateByGroupIDAndRoleIDs(ctx context.Context, groupID int64, roleIDs []int64) error {
	return g.queries.CreateRoleGroupsByGroupIDAndRoleIDs(ctx, sqlc.CreateRoleGroupsByGroupIDAndRoleIDsParams{
		RoleIds: roleIDs,
		GroupID: groupID,
	})
}

func (g *GroupRoleDB) GetAll() ([]entity.GroupRole, error) {
	groupRoles, err := g.queries.GetAllGroupRoles(context.Background())
	if err != nil {
		return nil, err
	}

	result := make([]entity.GroupRole, len(groupRoles))
	for i, gr := range groupRoles {
		result[i] = entity.GroupRole{
			ID:        gr.ID,
			GroupID:   gr.GroupID,
			RoleID:    gr.RoleID,
			CreatedAt: gr.CreatedAt,
		}
	}
	return result, nil
}

func (g *GroupRoleDB) GetByID(id int64) (entity.GroupRole, error) {
	groupRole, err := g.queries.GetGroupRoleByID(context.Background(), id)
	if err != nil {
		return entity.GroupRole{}, err
	}

	return entity.GroupRole{
		ID:        groupRole.ID,
		GroupID:   groupRole.GroupID,
		RoleID:    groupRole.RoleID,
		CreatedAt: groupRole.CreatedAt,
	}, nil
}

func (g *GroupRoleDB) GetByGroupID(groupID int64) ([]entity.GroupRole, error) {
	groupRoles, err := g.queries.GetGroupRolesByGroupID(context.Background(), groupID)
	if err != nil {
		return nil, err
	}

	result := make([]entity.GroupRole, len(groupRoles))
	for i, gr := range groupRoles {
		result[i] = entity.GroupRole{
			ID:        gr.ID,
			GroupID:   gr.GroupID,
			RoleID:    gr.RoleID,
			CreatedAt: gr.CreatedAt,
		}
	}
	return result, nil
}

func (g *GroupRoleDB) GetByRoleID(roleID int64) ([]entity.GroupRole, error) {
	groupRoles, err := g.queries.GetGroupRolesByRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}

	result := make([]entity.GroupRole, len(groupRoles))
	for i, gr := range groupRoles {
		result[i] = entity.GroupRole{
			ID:        gr.ID,
			GroupID:   gr.GroupID,
			RoleID:    gr.RoleID,
			CreatedAt: gr.CreatedAt,
		}
	}
	return result, nil
}

func (g *GroupRoleDB) GetByGroupRoleID(groupID, roleID int64) (entity.GroupRole, error) {
	groupRole, err := g.queries.GetGroupRoleByGroupRoleID(context.Background(), sqlc.GetGroupRoleByGroupRoleIDParams{
		GroupID: groupID,
		RoleID:  roleID,
	})
	if err != nil {
		return entity.GroupRole{}, err
	}

	return entity.GroupRole{
		ID:        groupRole.ID,
		GroupID:   groupRole.GroupID,
		RoleID:    groupRole.RoleID,
		CreatedAt: groupRole.CreatedAt,
	}, nil
}

func (g *GroupRoleDB) DeleteByID(id int64) error {
	return g.queries.DeleteGroupRole(context.Background(), id)
}

func (g *GroupRoleDB) DeleteByGroupID(ctx context.Context, groupID int64) error {
	return g.queries.DeleteGroupRolesByGroupID(context.Background(), groupID)
}

func (g *GroupRoleDB) DeleteByGroupIDsAndRoleID(ctx context.Context, groupIDs []int64, roleID int64) error {
	return g.queries.DeleteGroupRolesByGroupIDsAndRoleID(ctx, sqlc.DeleteGroupRolesByGroupIDsAndRoleIDParams{
		GroupIds: groupIDs,
		RoleID:   roleID,
	})
}

func (g *GroupRoleDB) DeleteByRoleIDsAndGroupID(ctx context.Context, roleIDs []int64, groupID int64) error {
	return g.queries.DeleteRoleGroupsByRoleIDsAndGroupID(ctx, sqlc.DeleteRoleGroupsByRoleIDsAndGroupIDParams{
		GroupID: groupID,
		RoleIds: roleIDs,
	})
}

func (g *GroupRoleDB) DeleteByGroupRoleID(groupID, roleID int64) error {
	return g.queries.DeleteGroupRoleByGroupRoleID(context.Background(), sqlc.DeleteGroupRoleByGroupRoleIDParams{
		GroupID: groupID,
		RoleID:  roleID,
	})
}

func (g *GroupRoleDB) DeleteByRoleIDs(ctx context.Context, roleIDs []int64) error {
	return g.queries.DeleteGroupRolesByRoleIDs(ctx, roleIDs)
}
