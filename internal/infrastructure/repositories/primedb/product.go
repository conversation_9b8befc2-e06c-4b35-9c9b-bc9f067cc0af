package primedb

import (
	"context"
	"database/sql"
	"errors"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type productDB struct {
	*db
	queries *sqlc.Queries
}

func NewProductPrimeDB(db *db) repository.ProductPrimeDB {
	return &productDB{db, sqlc.New(db.client)}
}

func (db *productDB) Create(data entity.ProductCreateData, ownerIDs []int64) (entity.Product, error) {

	tx, err := db.client.BeginTx(context.Background(), pgx.TxOptions{})
	if err != nil {
		return entity.Product{}, err
	}
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback(context.Background())
			panic(r)
		} else if err != nil {
			_ = tx.Rollback(context.Background())
		} else {
			err = tx.Commit(context.Background())
		}
	}()

	createProductParams := sqlc.CreateProductParams{
		Iid:       stringPointerOrNil(&data.IID),
		TechName:  data.TechName,
		Name:      data.Name,
		CreatorID: data.CreatorID,
	}
	productCreated, err := db.queries.WithTx(tx).CreateProduct(context.Background(), createProductParams)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return entity.Product{}, errkit.NewObjectAlreadyExist(errkit.ObjectTypeProduct, data.TechName, errkit.StateAlreadyExists)
		}
		return entity.Product{}, err
	}

	for _, ownerID := range ownerIDs {
		err = db.addParticipantAndRoles(tx, productCreated.ID, ownerID)
		if err != nil {
			return entity.Product{}, err
		}
	}

	createdProduct := entity.Product{
		ID:          productCreated.ID,
		IID:         stringFromPointerOrNil(productCreated.Iid),
		TechName:    productCreated.TechName,
		Name:        productCreated.Name,
		Description: stringFromPointerOrNil(productCreated.Description),
		CreatorID:   productCreated.CreatorID,
		ActiveFlg:   productCreated.ActiveFlg,
		CreatedAt:   productCreated.CreatedAt,
		UpdatedAt:   productCreated.UpdatedAt,
		DeletedAt:   productCreated.DeletedAt,
	}

	return createdProduct, err
}

func (db *productDB) addParticipantAndRoles(tx pgx.Tx, productID int64, ownerID int64) error {

	participant, err := db.queries.WithTx(tx).AddParticipant(context.Background(), sqlc.AddParticipantParams{
		ProductID: productID,
		UserID:    ownerID,
	})
	if err != nil {
		return err
	}

	// GetByID role product_owner
	role, err := db.queries.WithTx(tx).GetRoleByName(context.Background(), "product_owner")
	if err != nil {
		return err
	}

	// Add record to participant_roles
	addParticipantRoleParams := sqlc.AddParticipantRoleParams{
		ParticipantID: participant.ID,
		RoleID:        role.ID,
	}
	err = db.queries.WithTx(tx).AddParticipantRole(context.Background(), addParticipantRoleParams)
	if err != nil {
		return err
	}

	// GetByID role product_participant
	role, err = db.queries.WithTx(tx).GetRoleByName(context.Background(), "product_participant")
	if err != nil {
		return err
	}

	// Add record to participant_roles
	addParticipantRoleParams = sqlc.AddParticipantRoleParams{
		ParticipantID: participant.ID,
		RoleID:        role.ID,
	}
	err = db.queries.WithTx(tx).AddParticipantRole(context.Background(), addParticipantRoleParams)
	if err != nil {
		return err
	}

	return nil
}

func (db *productDB) GetAll() ([]entity.Product, error) {

	productsDB, err := db.queries.GetAllProducts(context.Background())
	if err != nil {
		return nil, err
	}

	products := make([]entity.Product, 0, len(productsDB))
	for _, productDB := range productsDB {
		products = append(products, entity.Product{
			ID:          productDB.ID,
			IID:         stringFromPointerOrNil(productDB.Iid),
			TechName:    productDB.TechName,
			Name:        productDB.Name,
			Description: stringFromPointerOrNil(productDB.Description),
			CreatorID:   productDB.CreatorID,
			ActiveFlg:   productDB.ActiveFlg,
			CreatedAt:   productDB.CreatedAt,
			UpdatedAt:   productDB.UpdatedAt,
			DeletedAt:   productDB.DeletedAt,
		})
	}

	return products, nil
}

func (db *productDB) GetByID(id int64) (entity.Product, error) {

	productDB, err := db.queries.GetProductByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.Product{}, errkit.NewObjectNotFound(errkit.ObjectTypeProduct, strconv.FormatInt(id, 10), errkit.StateNotFound)
		}
		return entity.Product{}, err
	}

	return entity.Product{
		ID:          productDB.ID,
		IID:         stringFromPointerOrNil(productDB.Iid),
		TechName:    productDB.TechName,
		Name:        productDB.Name,
		Description: stringFromPointerOrNil(productDB.Description),
		CreatorID:   productDB.CreatorID,
		ActiveFlg:   productDB.ActiveFlg,
		CreatedAt:   productDB.CreatedAt,
		UpdatedAt:   productDB.UpdatedAt,
		DeletedAt:   productDB.DeletedAt,
	}, nil
}

func (db *productDB) GetByIID(iid string) (entity.Product, error) {

	productDB, err := db.queries.GetProductByIID(context.Background(), &iid)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return entity.Product{}, errkit.NewObjectNotFound(errkit.ObjectTypeProduct, iid, errkit.StateNotFound)
		}
		return entity.Product{}, err
	}

	return entity.Product{
		ID:          productDB.ID,
		IID:         stringFromPointerOrNil(productDB.Iid),
		TechName:    productDB.TechName,
		Name:        productDB.Name,
		Description: stringFromPointerOrNil(productDB.Description),
		CreatorID:   productDB.CreatorID,
		ActiveFlg:   productDB.ActiveFlg,
		CreatedAt:   productDB.CreatedAt,
		UpdatedAt:   productDB.UpdatedAt,
		DeletedAt:   productDB.DeletedAt,
	}, nil
}

func (db *productDB) GetByUserID(userID int64) ([]entity.Product, error) {

	productsDB, err := db.queries.GetProductsByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	var products []entity.Product
	for _, productDB := range productsDB {
		products = append(products, entity.Product{
			ID:          productDB.ID,
			IID:         stringFromPointerOrNil(productDB.Iid),
			TechName:    productDB.TechName,
			Name:        productDB.Name,
			Description: stringFromPointerOrNil(productDB.Description),
			CreatorID:   productDB.CreatorID,
			ActiveFlg:   productDB.ActiveFlg,
			CreatedAt:   productDB.CreatedAt,
			UpdatedAt:   productDB.UpdatedAt,
			DeletedAt:   productDB.DeletedAt,
		})
	}

	return products, nil
}

func stringPointerOrNil(s *string) *string {
	if s == nil || *s == "" {
		return nil
	}
	return s
}

func stringFromPointerOrNil(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func getDeletedAt(activeFlg *bool) *time.Time {
	if activeFlg != nil && !*activeFlg {
		t := time.Now().UTC()
		return &t
	}
	return nil
}

func (db *productDB) Update(data entity.ProductUpdateData) (entity.Product, error) {

	productNew, err := db.queries.UpdateProduct(
		context.Background(),
		sqlc.UpdateProductParams{
			ID:          data.ID,
			Iid:         stringPointerOrNil(data.IID),
			TechName:    stringPointerOrNil(data.TechName),
			Name:        stringPointerOrNil(data.Name),
			Description: data.Description,
			ActiveFlg:   data.ActiveFlg,
			DeletedAt:   getDeletedAt(data.ActiveFlg),
		},
	)
	if err != nil {
		return entity.Product{}, err
	}

	var product entity.Product

	product.ID = productNew.ID
	if productNew.Iid != nil {
		product.IID = *productNew.Iid
	}
	product.TechName = productNew.TechName
	product.Name = productNew.Name
	if productNew.Description != nil {
		product.Description = *productNew.Description
	}
	product.CreatorID = productNew.CreatorID
	product.ActiveFlg = productNew.ActiveFlg
	product.CreatedAt = productNew.CreatedAt
	product.UpdatedAt = productNew.UpdatedAt
	product.DeletedAt = productNew.DeletedAt

	return product, nil
}
