package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/categorygroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
)

type CategoryGroupDB struct {
	*db
	queries *sqlc.Queries
}

func NewCategoryGroupPrimeDB(db *db) repository.CategoryGroupPrimeDB {
	return &CategoryGroupDB{db, sqlc.New(db.client)}
}

func (c *CategoryGroupDB) Create(data entity.CategoryGroupLink) (entity.CategoryGroupLink, error) {

	categoryGroup, err := c.queries.CreateCategoryGroup(context.Background(), sqlc.CreateCategoryGroupParams{
		CategoryID: data.CategoryID,
		GroupID:    data.GroupID,
	})
	if err != nil {
		return entity.CategoryGroupLink{}, err
	}

	return entity.CategoryGroupLink{
		ID:         categoryGroup.ID,
		CategoryID: categoryGroup.CategoryID,
		GroupID:    categoryGroup.GroupID,
	}, nil

}

func (c *CategoryGroupDB) GetAll() ([]entity.CategoryGroupLink, error) {
	categoryGroupsDB, err := c.queries.GetAllCategoryGroups(context.Background())
	if err != nil {
		return nil, err
	}

	var categoryGroups []entity.CategoryGroupLink
	for _, categoryGroupDB := range categoryGroupsDB {
		categoryGroups = append(categoryGroups, entity.CategoryGroupLink{
			ID:         categoryGroupDB.ID,
			CategoryID: categoryGroupDB.CategoryID,
			GroupID:    categoryGroupDB.GroupID,
		})
	}

	return categoryGroups, nil
}

func (c *CategoryGroupDB) GetByID(id int64) (entity.CategoryGroupLink, error) {
	categoryGroupDB, err := c.queries.GetCategoryGroupByID(context.Background(), id)
	if err != nil {
		return entity.CategoryGroupLink{}, err
	}
	return entity.CategoryGroupLink{
		ID:         categoryGroupDB.ID,
		CategoryID: categoryGroupDB.CategoryID,
		GroupID:    categoryGroupDB.GroupID,
	}, nil
}

func (c *CategoryGroupDB) GetByCategoryID(categoryID int64) ([]entity.CategoryGroupLink, error) {
	categoryGroupsDB, err := c.queries.GetCategoryGroupsByCategoryID(context.Background(), categoryID)
	if err != nil {
		return nil, err
	}

	var categoryGroups []entity.CategoryGroupLink
	for _, categoryGroupDB := range categoryGroupsDB {
		categoryGroups = append(categoryGroups, entity.CategoryGroupLink{
			ID:         categoryGroupDB.ID,
			CategoryID: categoryGroupDB.CategoryID,
			GroupID:    categoryGroupDB.GroupID,
		})
	}
	return categoryGroups, nil
}

func (c *CategoryGroupDB) HasCategoryGroup(categoryID int64, groupID int64) (bool, error) {
	return c.queries.HasCategoryGroup(context.Background(), sqlc.HasCategoryGroupParams{
		CategoryID: categoryID,
		GroupID:    groupID,
	})
}

func (c *CategoryGroupDB) Delete(categoryID int64, groupID int64) error {
	return c.queries.Delete(context.Background(), sqlc.DeleteParams{
		CategoryID: categoryID,
		GroupID:    groupID,
	})
}
