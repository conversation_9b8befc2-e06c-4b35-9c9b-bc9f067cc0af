package primedb

import (
	"context"
	"fmt"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/category"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
)

type CategoryDB struct {
	*db
	queries *sqlc.Queries
}

func NewCategoryPrimeDB(db *db) repository.CategoryPrimeDB {
	return &CategoryDB{db, sqlc.New(db.client)}
}

func (c *CategoryDB) Create(data entity.Category) (entity.Category, error) {
	category, err := c.queries.CreateCategory(context.Background(), data.Name)
	if err != nil {
		return entity.Category{}, err
	}
	return entity.Category{
		ID:   category.ID,
		Name: category.Name,
	}, nil
}

func (c *CategoryDB) GetAll() ([]entity.Category, error) {
	categoriesDB, err := c.queries.GetAllCategories(context.Background())
	if err != nil {
		return nil, err
	}

	var categories []entity.Category
	for _, categoryDB := range categoriesDB {
		categories = append(categories, entity.Category{
			ID:   categoryDB.ID,
			Name: categoryDB.Name,
		})
	}
	return categories, nil
}

func (c *CategoryDB) GetByID(id int64) (entity.Category, error) {
	categoryDB, err := c.queries.GetCategoryByID(context.Background(), id)
	if err != nil {
		return entity.Category{}, err
	}
	return entity.Category{
		ID:   categoryDB.ID,
		Name: categoryDB.Name,
	}, nil
}

func (c *CategoryDB) GetByName(name string) (entity.Category, error) {
	categoryDB, err := c.queries.GetCategoryByName(context.Background(), name)
	if err != nil {
		return entity.Category{}, err
	}
	return entity.Category{
		ID:   categoryDB.ID,
		Name: categoryDB.Name,
	}, nil
}

func (c *CategoryDB) Update(data entity.Category) (entity.Category, error) {
	category, err := c.queries.UpdateCategory(context.Background(), sqlc.UpdateCategoryParams{
		ID:   data.ID,
		Name: data.Name,
	})
	if err != nil {
		return entity.Category{}, err
	}
	return entity.Category{
		ID:   category.ID,
		Name: category.Name,
	}, nil
}

func (c *CategoryDB) Delete(ctx context.Context, id int64) error {

	userID, ok := ctx.Value(constants.UserIDKey).(int64)
	if !ok {
		return fmt.Errorf("user ID is missing in context")
	}

	_, err := c.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	if err != nil {
		return fmt.Errorf("failed to set audit user_id: %w", err)
	}

	err = c.queries.DeleteCategory(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}

	return nil
}
