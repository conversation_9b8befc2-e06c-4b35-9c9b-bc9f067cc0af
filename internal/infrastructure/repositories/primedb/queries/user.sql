-- name: CreateUser :one
INSERT INTO users (category_id, email, full_name, position, photo)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT (email) DO NOTHING
RETURNING id, category_id, email, full_name, position, is_admin, active_flg, last_active_product_id, last_login_at, created_at, updated_at, deleted_at, photo;

-- name: GetAllUsers :many
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users;

-- name: GetUserByID :one
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
WHERE id = $1
LIMIT 1;

-- name: GetUsersByCategoryID :many
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
WHERE category_id = $1;

-- name: GetUserByEmail :one
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
WHERE email = $1
LIMIT 1;

-- name: GetUsersByFilters :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON u.id = p.user_id
WHERE (
    (sqlc.narg(email)::text IS NULL OR u.email ILIKE '%' || sqlc.narg(email) || '%')
        OR (sqlc.narg(full_name)::text IS NULL OR u.full_name ILIKE '%' || sqlc.narg(full_name) || '%')
    )
  AND (sqlc.narg(is_admin)::boolean IS NULL OR u.is_admin = sqlc.narg(is_admin))
  AND (sqlc.narg(category_ids)::bigint[] IS NULL OR u.category_id = ANY (sqlc.narg(category_ids)))
  AND (sqlc.narg(product_ids)::bigint[] IS NULL OR p.product_id = ANY (sqlc.narg(product_ids)));


-- name: UpdateUser :one
UPDATE users
SET category_id            = COALESCE(sqlc.narg(category_id), category_id),
    email                  = COALESCE(sqlc.narg(email), email),
    full_name              = COALESCE(sqlc.narg(full_name), full_name),
    position               = COALESCE(sqlc.narg(position), position),
    is_admin               = COALESCE(sqlc.narg(is_admin), is_admin),
    active_flg             = COALESCE(sqlc.narg(active_flg), active_flg),
    last_active_product_id = COALESCE(sqlc.narg(last_active_product_id), last_active_product_id),
    last_login_at          = COALESCE(sqlc.narg(last_login_at), last_login_at),
    deleted_at             = COALESCE(sqlc.narg(deleted_at), deleted_at),
    photo                  = COALESCE(sqlc.narg(photo), photo)
WHERE id = $1
RETURNING id, category_id, email, full_name, position, is_admin, active_flg, last_active_product_id, last_login_at, created_at, updated_at, deleted_at, photo;

-- name: GetAllUsersPaginated :many
SELECT id,
       category_id,
       email,
       full_name,
       position,
       is_admin,
       active_flg,
       last_active_product_id,
       last_login_at,
       created_at,
       updated_at,
       deleted_at,
       photo
FROM users
LIMIT sqlc.arg(limit_value)::bigint OFFSET sqlc.arg(offset_value)::bigint;

-- name: GetAllUsersTotalCount :one
SELECT COUNT(*)
FROM users;

-- name: GetUsersByFiltersPaginated :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON u.id = p.user_id
WHERE (
    (sqlc.narg(email)::text IS NULL OR u.email ILIKE '%' || sqlc.narg(email) || '%')
        OR (sqlc.narg(full_name)::text IS NULL OR u.full_name ILIKE '%' || sqlc.narg(full_name) || '%')
    )
  AND (sqlc.narg(is_admin)::boolean IS NULL OR u.is_admin = sqlc.narg(is_admin))
  AND (sqlc.narg(category_ids)::bigint[] IS NULL OR u.category_id = ANY (sqlc.narg(category_ids)))
  AND (sqlc.narg(product_ids)::bigint[] IS NULL OR p.product_id = ANY (sqlc.narg(product_ids)))
LIMIT sqlc.arg(limit_value)::bigint OFFSET sqlc.arg(offset_value)::bigint;

-- name: GetUsersByFiltersTotalCount :one
SELECT COUNT(DISTINCT u.id)
FROM users u
         LEFT JOIN participants p ON u.id = p.user_id
WHERE (
    (sqlc.narg(email)::text IS NULL OR u.email ILIKE '%' || sqlc.narg(email) || '%')
        OR (sqlc.narg(full_name)::text IS NULL OR u.full_name ILIKE '%' || sqlc.narg(full_name) || '%')
    )
  AND (sqlc.narg(is_admin)::boolean IS NULL OR u.is_admin = sqlc.narg(is_admin))
  AND (sqlc.narg(category_ids)::bigint[] IS NULL OR u.category_id = ANY (sqlc.narg(category_ids)))
  AND (sqlc.narg(product_ids)::bigint[] IS NULL OR p.product_id = ANY (sqlc.narg(product_ids)));

-- name: GetUsersByParticipantRoleID :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON p.user_id = u.id
         LEFT JOIN participants_roles pr ON pr.participant_id = p.id
WHERE pr.role_id = $1;

-- name: GetUsersByParticipantGroupID :many
SELECT DISTINCT u.id,
                u.category_id,
                u.email,
                u.full_name,
                u.position,
                u.is_admin,
                u.active_flg,
                u.last_active_product_id,
                u.last_login_at,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.photo
FROM users u
         LEFT JOIN participants p ON p.user_id = u.id
         LEFT JOIN participants_groups pg ON pg.participant_id = p.id
WHERE pg.group_id = $1;

-- name: GetByGroupID :many
SELECT DISTINCT u.*
FROM public.users u
WHERE u.id IN (

    SELECT p.user_id
    FROM public.participants p
             JOIN public.participants_groups pg ON p.id = pg.participant_id
    WHERE pg.group_id = $1

    UNION

    SELECT ug.user_id
    FROM public.users_groups ug
    WHERE ug.group_id = $1
)
ORDER BY u.id;
