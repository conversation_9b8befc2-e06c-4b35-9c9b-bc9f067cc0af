-- name: CreatePermission :one
INSERT INTO permissions (name, method)
VALUES ($1, $2)
ON CONFLICT (name, method) DO NOTHING
RETURNING *;

-- name: GetAllPermissions :many
SELECT *
FROM permissions;

-- name: GetAllUniqueNames :many
SELECT DISTINCT name
FROM permissions;

-- name: GetPermissionByID :one
SELECT *
FROM permissions
WHERE id = $1;

-- name: GetPermissionByNameMethod :one
SELECT *
FROM permissions
WHERE name = $1
  AND method = $2;

-- name: GetIDsByName :many
SELECT id
FROM permissions
WHERE name = $1;

-- name: UpdatePermission :one
UPDATE permissions
SET name   = COALESCE($1, name),
    method = COALESCE($2, method)
WHERE id = $3
RETURNING *;

-- name: GetPermissionsByUserID :many
SELECT DISTINCT p.id, p.name, p.method
FROM permissions p
WHERE p.id IN (
    -- Permissions through direct user roles
    SELECT rp.permission_id
    FROM users_roles ur
    JOIN roles r ON ur.role_id = r.id
    JOIN roles_permissions rp ON r.id = rp.role_id
    WHERE ur.user_id = $1
      AND r.active_flg = true

    UNION

    -- Permissions through user groups and group roles
    SELECT rp.permission_id
    FROM users_groups ug
    JOIN groups g ON ug.group_id = g.id
    JOIN groups_roles gr ON g.id = gr.group_id
    JOIN roles r ON gr.role_id = r.id
    JOIN roles_permissions rp ON r.id = rp.role_id
    WHERE ug.user_id = $1
      AND g.active_flg = true
      AND r.active_flg = true
)
ORDER BY p.name, p.method;