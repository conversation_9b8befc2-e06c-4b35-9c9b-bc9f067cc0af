-- name: Create :one
WITH ins AS (
    INSERT INTO categories_roles (category_id, role_id)
        VALUES ($1, $2)
        ON CONFLICT (category_id, role_id) DO NOTHING
        RETURNING id, category_id, role_id)
SELECT id, category_id, role_id
FROM ins
UNION ALL
SELECT id, category_id, role_id
FROM categories_roles
WHERE category_id = $1
  AND role_id = $2
LIMIT 1;

-- name: GetAll :many
SELECT id, category_id, role_id
FROM categories_roles;

-- name: GetByID :one
SELECT id, category_id, role_id
FROM categories_roles
WHERE id = $1
LIMIT 1;

-- name: GetByCategoryID :many
SELECT id, category_id, role_id
FROM categories_roles
WHERE category_id = $1;

-- name: GetByRoleID :many
SELECT id, category_id, role_id
FROM categories_roles
WHERE role_id = $1;

-- name: HasCategoryRole :one
SELECT EXISTS(SELECT 1
              FROM categories_roles
              WHERE category_id = $1
                AND role_id = $2) AS exists;

-- name: Delete :exec
DELETE
FROM categories_roles
WHERE category_id = $1
  AND role_id = $2;
