-- name: CreateParticipantGroup :one
INSERT INTO participants_groups (participant_id, group_id)
VALUES ($1, $2)
ON CONFLICT (participant_id, group_id) DO NOTHING
RETURNING id, participant_id, group_id, created_at;

-- name: GetAllParticipantGroups :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups;

-- name: GetParticipantGroupByID :one
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE id = $1
LIMIT 1;

-- name: GetParticipantGroupsByParticipantID :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE participant_id = $1;

-- name: GetParticipantGroupsByParticipantIDs :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE participant_id = ANY ($1::BIGINT[]);

-- name: GetParticipantGroupsByGroupID :many
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE group_id = $1;

-- name: GetParticipantGroupByParticipantAndGroupID :one
SELECT id, participant_id, group_id, created_at
FROM participants_groups
WHERE participant_id = $1
  AND group_id = $2
LIMIT 1;

-- name: UpdateParticipantGroup :one
UPDATE participants_groups
SET participant_id = COALESCE(sqlc.narg(participant_id)::BIGINT, participant_id),
    group_id       = COALESCE(sqlc.narg(group_id)::BIGINT, group_id)
WHERE id = $1
RETURNING id, participant_id, group_id, created_at;

-- name: DeleteParticipantGroupsByParticipantID :exec
DELETE
FROM participants_groups
WHERE participant_id = $1;

-- name: DeleteParticipantGroupsByGroupID :exec
DELETE
FROM participants_groups
WHERE group_id = $1;

-- name: DeleteParticipantGroupsByParticipantIDs :exec
DELETE
FROM participants_groups
WHERE participant_id = ANY ($1::BIGINT[]); 

-- name: DeleteParticipantGroupByParticipantAndGroupID :exec
DELETE
FROM participants_groups
WHERE participant_id = $1
  AND group_id = $2;

-- name: DeleteParticipantGroupsByGroupIDAndParticipantIDs :exec
DELETE
FROM participants_groups
WHERE group_id = $1
  AND participant_id = ANY (sqlc.arg(participant_ids)::BIGINT[]);

-- name: CreateByGroupIDAndParticipantIDs :exec
INSERT INTO participants_groups (participant_id, group_id)
SELECT unnest(sqlc.arg(participant_ids)::BIGINT[]), sqlc.arg(group_id)
    ON CONFLICT (participant_id, group_id) DO NOTHING;
