-- name: CreateRolePermission :one
INSERT INTO roles_permissions (role_id,
                               permission_id)
VALUES ($1, $2)
ON CONFLICT (role_id, permission_id) DO NOTHING
RETURNING id, role_id, permission_id, created_at;

-- name: CreateRolePermissionsByRoleIDAndPermissionIDs :exec
INSERT INTO roles_permissions (role_id, permission_id)
SELECT sqlc.arg(role_id)::BIGINT, unnest(sqlc.arg(permission_ids)::BIGINT[])
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- name: GetAllRolePermissions :many
SELECT id, role_id, permission_id, created_at
FROM roles_permissions;

-- name: GetRolePermissionByID :one
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE id = $1;

-- name: GetRolePermissionsByRoleID :many
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE role_id = $1;

-- name: GetRolePermissionsByPermissionID :many
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE permission_id = $1;

-- name: GetRolePermissionByRolePermissionID :one
SELECT id, role_id, permission_id, created_at
FROM roles_permissions
WHERE role_id = $1
  AND permission_id = $2;

-- name: DeleteByID :exec
DELETE
FROM roles_permissions
WHERE id = $1;

-- name: DeleteByRoleID :exec
DELETE
FROM roles_permissions
WHERE role_id = $1;

-- name: DeleteByRoleIDAndPermissionID :exec
DELETE
FROM roles_permissions
WHERE role_id = $1
  AND permission_id = $2;

-- name: DeleteByPermissionID :exec
DELETE
FROM roles_permissions
WHERE permission_id = $1;


-- name: DeleteRolePermissionsByRoleIDAndPermissionIDs :exec
DELETE
FROM roles_permissions
WHERE role_id = sqlc.arg(role_id)::BIGINT
  AND permission_id = ANY(sqlc.arg(permission_ids)::BIGINT[]);