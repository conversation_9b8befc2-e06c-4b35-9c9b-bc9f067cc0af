-- name: <PERSON>reateParticipantRole :one
INSERT INTO participants_roles (participant_id, role_id)
VALUES ($1, $2)
ON CONFLICT (participant_id, role_id) DO NOTHING
RETURNING id, participant_id, role_id, created_at;

-- name: <PERSON>reate<PERSON>yRoleIDAndParticipantIDs :exec
INSERT INTO participants_roles (participant_id, role_id)
SELECT unnest(sqlc.arg(participant_ids)::BIGINT[]), sqlc.arg(role_id)
ON CONFLICT (participant_id, role_id) DO NOTHING;



-- name: GetAllParticipantRoles :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles;

-- name: GetParticipantRoleByID :one
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE id = $1
LIMIT 1;

-- name: GetParticipantRolesByParticipantID :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE participant_id = $1;

-- name: GetParticipantRolesByRoleID :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE role_id = $1;

-- name: GetParticipantRolesByRoleIDAndParticipantIDs :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE role_id = $1
  AND participant_id = ANY (sqlc.arg(participant_ids)::BIGINT[]);

-- name: GetParticipantRoleByParticipantAndRoleID :one
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE participant_id = $1
  AND role_id = $2
LIMIT 1;

-- name: GetProductOwnersByParticipantID :many
SELECT pr.*
FROM participants_roles pr
WHERE pr.role_id = (SELECT id
                    FROM roles
                    WHERE name = 'product_owner')
  AND pr.participant_id IN (SELECT p.id
                            FROM participants p
                            WHERE p.product_id IN (SELECT p2.product_id
                                                   FROM participants p2
                                                   WHERE p2.id = $1));

-- name: IsParticipantOwner :one
SELECT COUNT(*) > 0 as is_owner
FROM participants_roles
WHERE role_id = (SELECT id
                 FROM roles
                 WHERE name = 'product_owner')
  AND participant_id = $1;

-- name: DeleteParticipantRolesByParticipantID :exec
DELETE
FROM participants_roles
WHERE participant_id = $1;

-- name: DeleteParticipantRolesByRoleID :exec
DELETE
FROM participants_roles
WHERE role_id = $1;

-- name: DeleteParticipantRoleByParticipantAndRoleID :exec
DELETE
FROM participants_roles
WHERE participant_id = $1
  AND role_id = $2;

-- name: GetParticipantRolesByParticipantIDs :many
SELECT id, participant_id, role_id, created_at
FROM participants_roles
WHERE participant_id = ANY ($1::BIGINT[]);

-- name: DeleteParticipantRolesByParticipantIDs :exec
DELETE
FROM participants_roles
WHERE participant_id = ANY ($1::BIGINT[]);


-- name: DeleteParticipantRolesByRoleIDAndParticipantIDs :exec
DELETE
FROM participants_roles
WHERE role_id = $1
  AND participant_id = ANY (sqlc.arg(participant_ids)::BIGINT[]);


