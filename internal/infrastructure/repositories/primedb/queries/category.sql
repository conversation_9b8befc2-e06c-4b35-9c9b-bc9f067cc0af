-- name: CreateCategory :one
INSERT INTO categories (name)
VALUES ($1)
ON CONFLICT (name) DO NOTHING
RETURNING id, name;

-- name: GetAllCategories :many
SELECT id, name
FROM categories;

-- name: GetCategoryByID :one
SELECT id, name
FROM categories
WHERE id = $1
LIMIT 1;

-- name: GetCategoryByName :one
SELECT id, name
FROM categories
WHERE name = $1
LIMIT 1;

-- name: UpdateCategory :one
UPDATE categories
SET name = $2
WHERE id = $1
RETURNING id, name;

-- name: DeleteCategory :exec
DELETE
FROM categories
WHERE id = $1; 