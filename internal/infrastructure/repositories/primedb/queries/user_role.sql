-- name: <PERSON><PERSON><PERSON><PERSON>Role :one
INSERT INTO users_roles (user_id, role_id)
VALUES ($1, $2)
ON CONFLICT (user_id, role_id) DO NOTHING
RETURNING id, user_id, role_id, created_at;

-- name: CreateUserRolesByUserIDAndRoleIDs :exec
INSERT INTO users_roles (user_id, role_id)
SELECT sqlc.arg(user_id), unnest(sqlc.arg(role_ids)::BIGINT[])
ON CONFLICT (user_id, role_id) DO NOTHING;

-- name: CreateUserRolesByRoleIDAndUserIDs :exec
INSERT INTO users_roles (user_id, role_id)
SELECT unnest(sqlc.arg(user_ids)::BIGINT[]), sqlc.arg(role_id)
ON CONFLICT (user_id, role_id) DO NOTHING;

-- name: GetAllUserRoles :many
SELECT id, user_id, role_id, created_at
FROM users_roles;

-- name: GetUserRoleByID :one
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE id = $1
LIMIT 1;

-- name: GetUserRolesByUserID :many
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE user_id = $1;

-- name: GetUserRoleIDs :many
SELECT role_id
FROM users_roles
WHERE user_id = $1;

-- name: GetUserRolesByRoleID :many
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE role_id = $1;

-- name: GetUserRoleByUserIDRoleID :one
SELECT id, user_id, role_id, created_at
FROM users_roles
WHERE user_id = $1
  AND role_id = $2
LIMIT 1;

-- name: UpdateUserRole :one
UPDATE users_roles
SET user_id = COALESCE($2, user_id),
    role_id = COALESCE($3, role_id)
WHERE id = $1
RETURNING id, user_id, role_id, created_at;

-- name: DeleteUserRolesByUserID :exec
DELETE
FROM users_roles
WHERE user_id = $1;

-- name: DeleteUserRolesByRoleID :exec
DELETE
FROM users_roles
WHERE role_id = $1;

-- name: DeleteUserRolesByUserIDAndRoleIDs :exec
DELETE
FROM users_roles
WHERE user_id = $1
  AND role_id = ANY (sqlc.arg(role_ids)::BIGINT[]);

-- name: DeleteUserRoleByUserIDAndRoleID :exec
DELETE
FROM users_roles
WHERE user_id = $1
  AND role_id = $2;

-- name: DeleteUserRolesByRoleIDAndUserIDs :exec
DELETE
FROM users_roles
WHERE role_id = $1
  AND user_id = ANY (sqlc.arg(user_ids)::BIGINT[]);


