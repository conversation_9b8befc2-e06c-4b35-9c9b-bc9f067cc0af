-- name: Create :one
INSERT INTO proposals_history (proposal_id, event_type, status, message, user_id)
VALUES ($1, $2, $3, $4, $5)
RETURNING *;

-- name: GetByProposalID :many
SELECT *
FROM proposals_history
WHERE proposal_id = $1
ORDER BY created_at DESC;

-- name: GetProposalIDAndUserID :many
SELECT *
FROM proposals_history
WHERE user_id = $1
ORDER BY created_at DESC;

-- name: GetUnreadEventsForUser :many
SELECT ph.*
FROM proposals_history ph
LEFT JOIN proposal_user_views puv ON ph.proposal_id = puv.proposal_id AND puv.user_id = $1
WHERE (puv.viewed_at IS NULL OR ph.created_at > puv.viewed_at)
ORDER BY ph.created_at DESC;
