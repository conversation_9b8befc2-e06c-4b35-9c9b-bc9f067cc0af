-- name: Create :one
INSERT INTO participants (product_id, user_id)
VALUES ($1, $2)
ON CONFLICT (product_id, user_id) DO NOTHING
RETURNING id, product_id, user_id, created_at, updated_at;

-- name: CreateByUserIDAndProductIDs :exec
INSERT INTO participants (product_id, user_id)
SELECT unnest(sqlc.arg(product_ids)::BIGINT[]), sqlc.arg(user_id)
ON CONFLICT (product_id, user_id) DO NOTHING;

-- name: GetByProductID :many
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE product_id = sqlc.arg(product_id)::BIGINT;

-- name: GetByID :one
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE id = sqlc.arg(id)::BIGINT
LIMIT 1;

-- name: GetByUserID :many
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE user_id = sqlc.arg(user_id)::BIGINT;

-- name: GetByIDAndProductID :one
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE id = sqlc.arg(id)::BIGINT
  AND product_id = sqlc.arg(product_id)::BIGINT
LIMIT 1;

-- name: GetByUserIDAndProductID :one
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE user_id = sqlc.arg(user_id)::BIGINT
  AND product_id = sqlc.arg(product_id)::BIGINT
LIMIT 1;

-- name: GetByUserIDAndProductIDs :many
SELECT id, product_id, user_id, created_at, updated_at
FROM participants
WHERE user_id = sqlc.arg(user_id)::BIGINT
  AND product_id = ANY (sqlc.arg(product_id)::BIGINT[]);

-- name: GetOwnersByProductID :many
SELECT p.product_id, p.id, p.user_id, u.full_name, u.email
FROM participants p
         JOIN users u ON p.user_id = u.id
         JOIN participants_roles pr ON p.id = pr.participant_id
         JOIN roles r ON pr.role_id = r.id
WHERE p.product_id = sqlc.arg(product_id)::BIGINT
  AND r.name = 'product_owner';

-- name: GetByParticipantByProductIDAndUserEmail :one
SELECT p.product_id, p.id, p.user_id, u.full_name, u.email
FROM participants p
         JOIN users u ON p.user_id = u.id
WHERE p.product_id = sqlc.arg(product_id)::BIGINT
  AND u.email = sqlc.arg(email)::TEXT
LIMIT 1;

-- name: GetByRoleID :many
SELECT p.id, p.user_id, p.product_id, p.created_at, p.updated_at
FROM participants p
JOIN participants_roles pr ON p.id = pr.participant_id
WHERE pr.role_id = sqlc.arg(role_id)::BIGINT

UNION

SELECT p.id, p.user_id, p.product_id, p.created_at, p.updated_at
FROM participants p
JOIN participants_groups pg ON p.id = pg.participant_id
JOIN groups_roles gr ON pg.group_id = gr.group_id
WHERE gr.role_id = sqlc.arg(role_id)::BIGINT;

-- name: GetByGroupID :many
SELECT p.id, p.user_id, p.product_id, p.created_at, p.updated_at
FROM participants p
JOIN participants_groups pg ON p.id = pg.participant_id
WHERE pg.group_id = sqlc.arg(group_id)::BIGINT;

-- name: DeleteByIDAndProductID :exec
DELETE
FROM participants
WHERE id = sqlc.arg(id)::BIGINT
  AND product_id = sqlc.arg(product_id)::BIGINT;

-- name: DeleteByUserIDAndProductIDs :exec
DELETE
FROM participants
WHERE user_id = sqlc.arg(user_id)::BIGINT
  AND product_id = ANY (sqlc.arg(product_id)::BIGINT[]);

-- name: DeleteByUserIDAndGroupIDs :exec
DELETE
FROM participants_groups
WHERE participant_id IN (SELECT p.id
                         FROM participants p
                         WHERE p.user_id = sqlc.arg(user_id)::BIGINT)
  AND group_id = ANY (sqlc.arg(group_id)::BIGINT[]);

-- name: DeleteByUserIDAndRoleIDs :exec
DELETE
FROM participants_roles
WHERE participant_id IN (SELECT p.id
                         FROM participants p
                         WHERE p.user_id = sqlc.arg(user_id)::BIGINT)
  AND role_id = ANY (sqlc.arg(role_id)::BIGINT[]);

-- name: DeleteByUserIDsAndProductID :exec
DELETE
FROM participants
WHERE product_id = sqlc.arg(product_id)::BIGINT AND user_id = ANY (sqlc.arg(user_id)::BIGINT[]);