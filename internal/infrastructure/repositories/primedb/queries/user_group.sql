-- name: CreateUserGroup :one
INSERT INTO users_groups (user_id, group_id)
VALUES ($1, $2)
ON CONFLICT (user_id, group_id) DO NOTHING
RETURNING id, user_id, group_id, created_at;

-- name: AssignUserToGroups :exec
/* @param group_ids BIGINT[] */
INSERT INTO users_groups (user_id, group_id)
SELECT $1, unnest(sqlc.arg(group_ids)::BIGINT[])
ON CONFLICT (user_id, group_id) DO NOTHING;

-- name: GetUserGroupsByUserID :many
SELECT id, user_id, group_id, created_at
FROM users_groups
WHERE user_id = $1;

-- name: GetUserGroupsByGroupID :many
SELECT id, user_id, group_id, created_at
FROM users_groups
WHERE group_id = $1;

-- name: GetUserGroups :many
SELECT group_id
FROM users_groups
WHERE user_id = $1;

-- name: GetParticipantGroups :many
WITH user_groups AS (SELECT group_id
                     FROM users_groups
                     WHERE user_id = $1),
     participant_groups AS (SELECT group_id
                            FROM participants_groups
                            WHERE participant_id = $1)
SELECT group_id
FROM user_groups
UNION
SELECT group_id
FROM participant_groups;

-- name: GetParticipantGroupsWithProductByUserID :many
WITH group_data AS (
    SELECT g.*
    FROM groups g
             INNER JOIN participants_groups pg ON pg.group_id = g.id
             INNER JOIN participants p ON p.id = pg.participant_id
    WHERE p.user_id = $1
),
     missing_product_ids AS (
         SELECT DISTINCT g.id AS group_id, p.product_id
         FROM group_data g
                  INNER JOIN participants_groups pg ON pg.group_id = g.id
                  INNER JOIN participants p ON p.id = pg.participant_id
         WHERE g.product_id IS NULL AND p.user_id = $1
     )
SELECT gd.id, gd.name,
       COALESCE(mpi.product_id, gd.product_id) AS product_id,
       gd.is_system, gd.active_flg, gd.created_at, gd.updated_at, gd.deleted_at
FROM group_data gd
         LEFT JOIN missing_product_ids mpi ON mpi.group_id = gd.id;


-- name: GetGroupsByIDs :many
SELECT id,
       name,
       product_id,
       is_system,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM groups
WHERE id = ANY ($1::BIGINT[]);

-- name: GetProductsByIDs :many
SELECT id, name, active_flg, created_at, updated_at, deleted_at
FROM products
WHERE id = ANY ($1::BIGINT[]);

-- name: DeleteUserGroupsByGroupID :exec
DELETE
FROM users_groups
WHERE group_id = $1;

-- name: DeleteUserGroupsByUserID :exec
DELETE
FROM users_groups
WHERE user_id = $1;

-- name: DeleteUserGroupsByUserIDAndGroupIDs :exec
DELETE
FROM users_groups
WHERE user_id = sqlc.arg(user_id)
  AND group_id = ANY (sqlc.arg(group_ids)::BIGINT[]);

-- name: DeleteUserGroupsByGroupIDAndUserIDs :exec
DELETE
FROM users_groups
WHERE group_id = $1
  AND user_id = ANY (sqlc.arg(user_ids)::BIGINT[]);

-- name: CreateUserGroupsByGroupIDAndUserIDs :exec
INSERT INTO users_groups (user_id, group_id)
SELECT unnest(sqlc.arg(user_ids)::BIGINT[]), sqlc.arg(group_id)
    ON CONFLICT (user_id, group_id) DO NOTHING;