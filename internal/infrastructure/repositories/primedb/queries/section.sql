-- name: GetSectionsByProposalID :many
SELECT *
FROM sections
WHERE product_id = $1
  AND seq_num = $2
ORDER BY num;

-- name: UpdateSectionsByProposalID :one
WITH deleted_sections AS (
    DELETE FROM sections s
        WHERE s.product_id = $1 AND s.seq_num = $2),
     data_arr AS (SELECT CASE
                             WHEN jsonb_typeof(sqlc.narg(values)::jsonb) = 'array' THEN sqlc.narg(values)::jsonb
                             ELSE jsonb_build_array(sqlc.narg(values)::jsonb)
                             END AS arr),
     inserted_sections AS (
         INSERT INTO sections (product_id, seq_num, num, data)
             SELECT $1,
                    $2,
                    gs + 1,
                    da.arr -> gs
             FROM data_arr da,
                  generate_series(0, jsonb_array_length(da.arr) - 1) gs
             RETURNING *)
SELECT *
FROM inserted_sections;