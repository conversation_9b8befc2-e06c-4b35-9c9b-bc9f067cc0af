-- name: CreateProposal :one
WITH new_proposal AS (
    INSERT INTO proposals (product_id, seq_num, price, type, status, active_flg, creator_id)
        VALUES ($1,
                COALESCE((SELECT MAX(seq_num) FROM proposals WHERE product_id = $1), 0) + 1,
                $2,
                $3,
                $4,
                true,
                $5)
        RETURNING *),
     data_arr AS (SELECT CASE
                             WHEN jsonb_typeof(sqlc.narg(values)::jsonb) = 'array' THEN sqlc.narg(values)::jsonb
                             ELSE jsonb_build_array(sqlc.narg(values)::jsonb)
                             END AS arr),
     inserted_sections AS (
         INSERT INTO sections (product_id, seq_num, num, data)
             SELECT $1,
                    np.seq_num,
                    gs + 1,
                    da.arr -> gs
             FROM new_proposal np,
                  data_arr da,
                  generate_series(0, jsonb_array_length(da.arr) - 1) gs
             RETURNING *)
SELECT *
FROM new_proposal;

-- name: GetAll :many
SELECT *
FROM proposals
WHERE active_flg = true;

-- name: GetProposalByID :one
SELECT *
FROM proposals
WHERE id = $1;

-- name: GetProposalsByProductID :many
SELECT *
FROM proposals
WHERE product_id = $1
  AND active_flg = true
ORDER BY seq_num;

-- name: GetProposalByProductIDAndProposalID :one
SELECT *
FROM proposals
WHERE product_id = $1
  AND id = $2
  AND active_flg = true;

-- name: UpdateProposal :one
WITH updated_proposal AS (
    UPDATE proposals p SET
        price = COALESCE(sqlc.narg(price), p.price),
        type = COALESCE(sqlc.narg(type), p.type),
        status = COALESCE(sqlc.narg(status), p.status),
        updated_at = NOW()
        WHERE p.id = $1 AND p.product_id = $2 AND p.active_flg = true
        RETURNING p.id, p.product_id, p.seq_num, p.price, p.type, p.status, p.active_flg, p.created_at, p.updated_at, p.deleted_at, p.creator_id, p.number),
     deleted_sections AS (
         DELETE FROM sections
             WHERE product_id = $2
               AND seq_num = (SELECT seq_num FROM updated_proposal)
               AND sqlc.narg(values)::jsonb IS NOT NULL),
     data_arr AS (SELECT CASE
                           WHEN jsonb_typeof(sqlc.narg(values)::jsonb) = 'array' THEN sqlc.narg(values)::jsonb
                           ELSE jsonb_build_array(sqlc.narg(values)::jsonb)
                           END AS arr
                  WHERE sqlc.narg(values)::jsonb IS NOT NULL),
     inserted_sections AS (
         INSERT INTO sections (product_id, seq_num, num, data)
             SELECT $2,
                    (SELECT seq_num FROM updated_proposal),
                    gs + 1,
                    da.arr -> gs
             FROM data_arr da,
                  generate_series(0, jsonb_array_length(da.arr) - 1) gs
             ON CONFLICT (product_id, seq_num, num) DO UPDATE SET data = EXCLUDED.data
             RETURNING *)
SELECT *
FROM updated_proposal;

-- name: DeleteProposal :one
UPDATE proposals
SET active_flg = false,
    updated_at = NOW()
WHERE id = $1
  AND product_id = $2
RETURNING *;
