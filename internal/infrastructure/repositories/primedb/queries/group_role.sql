-- name: CreateGroupRole :one
INSERT INTO groups_roles (group_id,
                          role_id)
VALUES ($1, $2)
ON CONFLICT (group_id, role_id) DO NOTHING
RETURNING id, group_id, role_id, created_at;

-- name: CreateGroupRolesByRoleIDAndGroupIDs :exec
INSERT INTO groups_roles (group_id, role_id)
SELECT unnest(sqlc.arg(group_ids)::BIGINT[]), sqlc.arg(role_id)::BIGINT
ON CONFLICT (group_id, role_id) DO NOTHING;

-- name: CreateRoleGroupsByGroupIDAndRoleIDs :exec
INSERT INTO groups_roles (role_id, group_id)
SELECT unnest(sqlc.arg(role_ids)::BIGINT[]), sqlc.arg(group_id)::BIGINT
    ON CONFLICT (group_id, role_id) DO NOTHING;

-- name: GetAllGroupRoles :many
SELECT id, group_id, role_id, created_at
FROM groups_roles;

-- name: GetGroupRoleByID :one
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE id = $1;

-- name: GetGroupRolesByGroupID :many
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE group_id = $1;

-- name: GetGroupRolesByRoleID :many
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE role_id = $1;

-- name: GetGroupRoleByGroupRoleID :one
SELECT id, group_id, role_id, created_at
FROM groups_roles
WHERE group_id = $1
  AND role_id = $2;

-- name: DeleteGroupRole :exec
DELETE
FROM groups_roles
WHERE id = $1;

-- name: DeleteGroupRolesByGroupID :exec
DELETE
FROM groups_roles
WHERE group_id = $1;

-- name: DeleteGroupRoleByGroupRoleID :exec
DELETE
FROM groups_roles
WHERE group_id = $1
  AND role_id = $2;

-- name: DeleteGroupRolesByRoleIDs :exec
DELETE
From groups_roles
WHERE role_id = ANY ($1::BIGINT[]);

-- name: DeleteGroupRolesByGroupIDsAndRoleID :exec
DELETE
FROM groups_roles
WHERE role_id = sqlc.arg(role_id)::BIGINT
  AND group_id = ANY (sqlc.arg(group_ids)::BIGINT[]);

-- name: DeleteRoleGroupsByRoleIDsAndGroupID :exec
DELETE
FROM groups_roles
WHERE group_id = sqlc.arg(group_id)::BIGINT
  AND role_id = ANY (sqlc.arg(role_ids)::BIGINT[]);
