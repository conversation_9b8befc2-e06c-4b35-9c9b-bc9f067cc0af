-- name: CreateCategoryPermission :one
INSERT INTO categories_permissions (category_id, permission_id)
VALUES ($1, $2)
ON CONFLICT (category_id, permission_id) DO NOTHING
RETURNING id, category_id, permission_id;

-- name: GetAllCategoryPermissions :many
SELECT id, category_id, permission_id
FROM categories_permissions;

-- name: GetCategoryPermissionByID :one
SELECT id, category_id, permission_id
FROM categories_permissions
WHERE id = $1
LIMIT 1;

-- name: GetCategoryPermissionsByCategoryID :many
SELECT id, category_id, permission_id
FROM categories_permissions
WHERE category_id = $1;

-- name: HasCategoryPermission :one
SELECT EXISTS(SELECT 1
              FROM categories_permissions
              WHERE category_id = $1
                AND permission_id = $2) AS exists;

-- name: UpdateCategoryPermission :one
UPDATE categories_permissions
SET permission_id = $2
WHERE category_id = $1
RETURNING id, category_id, permission_id;

-- name: DeleteCategoryPermission :exec
DELETE
FROM categories_permissions
WHERE category_id = $1
  AND permission_id = $2;

