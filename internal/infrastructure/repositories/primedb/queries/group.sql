-- name: CreateGroup :one
INSERT INTO groups (name,
                    product_id,
                    is_system,
                    active_flg)
VALUES ($1,
        $2,
        $3,
        true)
RETURNING *;

-- name: GetAllGroups :many
SELECT *
FROM groups
ORDER BY updated_at DESC;

-- name: GetGroup :one
SELECT *
FROM groups
WHERE id = $1
ORDER BY updated_at DESC;

-- name: GetByProductID :many
SELECT *
FROM groups
WHERE product_id = $1
ORDER BY updated_at DESC;

-- name: GetByGroupIDAndProductID :one
SELECT *
FROM groups
WHERE id = $1
  AND product_id = $2
ORDER BY updated_at DESC;

-- name: GetBySystemType :many
SELECT *
FROM groups
WHERE is_system = $1
  AND active_flg = true
ORDER BY updated_at DESC;

-- name: UpdateGroup :one
UPDATE groups SET
    name = COALESCE(sqlc.narg(name)::TEXT, name),
    product_id = COALESCE(sqlc.narg(product_id)::BIGINT, product_id),
    is_system = COALESCE(sqlc.narg(is_system)::BOOLEAN, is_system),
    active_flg = COALESCE(sqlc.narg(active_flg)::BOOLEAN, active_flg),
    deleted_at = COALESCE(sqlc.narg(deleted_at)::TimeSTAMP, deleted_at),
    updated_at = NOW()
WHERE id = $1
RETURNING *;

-- name: GetSystemGroupsWithStats :many
SELECT g.id                     AS group_id,
       g.name                   AS group_name,
       g.is_system        AS is_system,
       COUNT(gr.role_id)        AS role_count,
       COUNT(pg.participant_id) AS participant_count,
       COUNT(ug.user_id)        AS user_count
FROM groups g
         LEFT JOIN groups_roles gr ON g.id = gr.group_id
         LEFT JOIN participants_groups pg ON g.id = pg.group_id
         LEFT JOIN users_groups ug ON g.id = ug.group_id
WHERE g.is_system = true
  AND g.active_flg = true
GROUP BY g.id, g.name
ORDER BY g.id, g.name;

-- name: GetWithStatsByUserID :many
SELECT
    g.id AS group_id,
    g.name AS group_name,
    g.is_system AS is_system,
    COUNT(gr.role_id) AS role_count,
    COUNT(pg.participant_id) AS participant_count,
    COUNT(ug.user_id) AS user_count
FROM groups g
LEFT JOIN groups_roles gr ON g.id = gr.group_id
LEFT JOIN participants_groups pg ON g.id = pg.group_id
LEFT JOIN users_groups ug ON g.id = ug.group_id
WHERE g.is_system = true AND
      g.active_flg = true AND ug.user_id = $1
GROUP BY g.id, g.name
ORDER BY g.id, g.name;

-- name: GetSystemGroups :many
SELECT *
FROM groups g
WHERE g.is_system = true AND
      g.active_flg = true
GROUP BY g.id, g.name
ORDER BY g.id, g.name;

-- name: GetGroupCategoryStates :many
SELECT g.id                                  AS group_id,
       c.id                                  AS category_id,
       c.name                                AS category_name,
       (cg.category_id IS NOT NULL)::boolean AS is_active
FROM groups g
         CROSS JOIN categories c
         LEFT JOIN categories_groups cg ON
    cg.group_id = g.id AND
    cg.category_id = c.id
WHERE g.is_system = true
  AND g.active_flg = true
ORDER BY g.id, g.name;


-- name: GetGroupsShortsAsAdminByProductID :many
SELECT g.id AS group_id,
       g.name AS group_name,
       g.active_flg AS is_active,
       CASE
           WHEN g.is_system THEN 'system'
           ELSE 'custom'
       END AS group_type,
       COUNT(gr.role_id) AS role_count,
       COUNT(pg.participant_id) AS participant_count,
       COUNT(ug.user_id) AS user_count
FROM groups g
         LEFT JOIN groups_roles gr ON g.id = gr.group_id
         LEFT JOIN participants_groups pg ON g.id = pg.group_id
         LEFT JOIN users_groups ug ON g.id = ug.group_id
WHERE g.product_id = $1 AND g.active_flg = $2
GROUP BY g.id, g.name
ORDER BY g.id, g.name;

-- name: GetAllByUserID :many
WITH user_groups AS (SELECT ug.group_id
                     FROM users_groups ug
                     WHERE ug.user_id = $1),
     user_participants AS (SELECT id AS participant_id
                           FROM participants
                           WHERE user_id = $1),
     participant_groups AS (SELECT pg.group_id
                            FROM participants_groups pg
                            INNER JOIN user_participants up ON pg.participant_id = up.participant_id),
     all_group_ids AS (
         SELECT group_id
         FROM user_groups
         UNION
         SELECT group_id
         FROM participant_groups
     )
SELECT g.id,
       g.name,
       g.product_id,
       g.is_system,
       g.active_flg,
       g.created_at,
       g.updated_at,
       g.deleted_at
FROM groups g
         INNER JOIN all_group_ids agi ON g.id = agi.group_id;

-- name: GetAllWithProductIDByUserID :many
WITH user_direct_groups AS (
    SELECT ug.group_id, NULL::integer as product_id
    FROM users_groups ug
    WHERE ug.user_id = $1
),
participant_groups_with_products AS (
    SELECT pg.group_id, p.product_id
    FROM participants_groups pg
    INNER JOIN participants p ON pg.participant_id = p.id
    WHERE p.user_id = $1
),
all_user_groups AS (
    SELECT group_id, product_id FROM user_direct_groups
    UNION
    SELECT group_id, product_id FROM participant_groups_with_products
)
SELECT g.id,
       g.name,
       aug.product_id,
       g.is_system,
       g.active_flg,
       g.created_at,
       g.updated_at,
       g.deleted_at
FROM groups g
INNER JOIN all_user_groups aug ON g.id = aug.group_id
ORDER BY g.id, aug.product_id NULLS FIRST;

-- name: ListGroups :many
SELECT 
    id, 
    name, 
    product_id, 
    is_system, 
    active_flg, 
    created_at, 
    updated_at, 
    deleted_at
FROM groups
WHERE 
    name ILIKE ('%' || sqlc.arg('search')::TEXT || '%') AND
    (sqlc.narg('is_system')::BOOLEAN IS NULL OR is_system = sqlc.narg('is_system')) AND
    (sqlc.narg(product_ids)::bigint[] IS NULL OR product_id = ANY (sqlc.narg(product_ids))) AND
    (sqlc.narg('status')::BOOLEAN IS NULL OR active_flg = sqlc.narg('status'))
ORDER BY updated_at DESC
LIMIT sqlc.arg('limit')::BIGINT OFFSET sqlc.arg('offset')::BIGINT;

-- name: DeleteByID :exec
UPDATE groups SET
active_flg = false
WHERE id = sqlc.arg(id)::BIGINT
RETURNING *;

-- name: CountGroups :one
SELECT COUNT(*)
FROM groups
WHERE 
    name ILIKE ('%' || sqlc.arg('search')::TEXT || '%') AND
    (sqlc.narg('is_system')::BOOLEAN IS NULL OR is_system = sqlc.narg('is_system')) AND
    (sqlc.narg(product_ids)::bigint[] IS NULL OR product_id = ANY (sqlc.narg(product_ids))) AND
     (sqlc.narg('status')::BOOLEAN IS NULL OR active_flg = sqlc.narg('status'));

-- name: ExistByName :one
SELECT COUNT(*) > 0 AS exists
FROM groups
WHERE name = $1;

-- name: DeactivateByIDs :exec
UPDATE groups SET
active_flg = false
WHERE id =  ANY (sqlc.narg('groups_ids')::bigint[]);