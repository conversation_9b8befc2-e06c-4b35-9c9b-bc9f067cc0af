-- name: CreateRole :one
INSERT INTO roles (name,
                   product_id,
                   is_system)
VALUES ($1,
        $2,
        $3)
RETURNING *;

-- name: GetAllRoles :many
SELECT *
FROM roles;

-- name: GetRoleByID :one
SELECT *
FROM roles
WHERE id = $1;

-- name: GetRolesByProductID :many
SELECT *
FROM roles
WHERE product_id = $1;

-- name: GetRoleByIDAndProductID :one
SELECT *
FROM roles
WHERE id = $1
  AND product_id = $2;

-- name: GetRolesBySystemType :many
SELECT *
FROM roles
WHERE is_system = $1;

-- name: GetOwnerRole :one
SELECT *
FROM roles
WHERE name = 'product_owner'
LIMIT 1;

-- name: UpdateRole :one
UPDATE roles
SET name           = COALESCE(sqlc.narg(name)::VARCHAR, name),
    product_id     = COALESCE(sqlc.narg(product_id)::BIGINT, product_id),
    is_system = COALESCE(sqlc.narg(is_system)::BOOLEAN, is_system),
    active_flg     = COALESCE(sqlc.narg(active_flg)::BOOLEAN, active_flg),
    updated_at     = NOW()
WHERE id = $1
RETURNING *;

-- name: IsRoleProtected :one
SELECT is_protected
FROM roles
WHERE id = $1;

-- name: GetSystemRolesWithStats :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       CASE
           WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                  AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(pr.participant_id) AS participant_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN participants_roles pr ON r.id = pr.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
WHERE r.is_system = true
  AND r.active_flg = true
GROUP BY r.id, r.name
ORDER BY r.id, r.name;

-- name: GetRoleCategoryStates :many
SELECT r.id                                  AS role_id,
       c.id                                  AS category_id,
       c.name                                AS category_name,
       (cr.category_id IS NOT NULL)::boolean AS is_active
FROM roles r
         CROSS JOIN categories c
         LEFT JOIN categories_roles cr ON
    cr.role_id = r.id AND
    cr.category_id = c.id
WHERE r.is_system = true
  AND r.active_flg = true
ORDER BY r.id, c.id;

-- name: GetRolesShorts :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       CASE
           WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                  AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(pr.participant_id) AS participant_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN participants_roles pr ON r.id = pr.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
WHERE r.active_flg = true
  AND r.product_id = $1
GROUP BY r.id, r.name
ORDER BY r.id, r.name;

-- name: GetRolesShortsAsAdminByProductID :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       r.active_flg             AS is_active,
       CASE
           WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                  AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(pr.participant_id) AS participant_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN participants_roles pr ON r.id = pr.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
WHERE r.product_id = $1 AND r.active_flg = $2
GROUP BY r.id, r.name
ORDER BY r.id, r.name;

SELECT *
FROM roles
WHERE product_id = $1;


-- name: GetRolesWithProductByParticipantIDs :many
WITH role_data AS (
    SELECT
        r.id,
        r.name,
        COALESCE(p.product_id, r.product_id) AS product_id,
        r.is_system,
        r.active_flg,
        r.created_at,
        r.updated_at,
        r.deleted_at,
        pr.participant_id
    FROM roles r
             INNER JOIN participants_roles pr ON pr.role_id = r.id
             LEFT JOIN participants p ON p.id = pr.participant_id
    WHERE pr.participant_id  = ANY ($1::BIGINT[])
)
SELECT
    rd.id,
    rd.name,
    rd.participant_id,
    rd.product_id,
    rd.is_system,
    rd.active_flg,
    rd.created_at,
    rd.updated_at,
    rd.deleted_at
FROM role_data rd;

-- name: GetAllRolesTotalCount :one
SELECT COUNT(*)
FROM roles;

-- name: GetAllRolesPaginated :many
SELECT r.id                     AS role_id,
       r.name                   AS role_name,
       r.active_flg             AS is_active,
       p.id                     AS product_id,
       p.iid                    AS product_iid,
       p.name                   AS product_name,
       p.tech_name              AS tech_name,
       CASE
        WHEN r.is_system THEN 'system'
           ELSE 'custom'
           END                 AS role_type,
       COUNT(rg.group_id)       AS group_count,
       COUNT(ur.user_id)        AS user_count
FROM roles r
         LEFT JOIN groups_roles rg ON r.id = rg.role_id
         LEFT JOIN users_roles ur ON r.id = ur.role_id
         LEFT JOIN products p ON r.product_id = p.id
GROUP BY r.id, r.name, r.active_flg, p.id, p.iid, p.name, p.tech_name, r.is_system
ORDER BY r.id, r.name
LIMIT sqlc.arg(limit_value)::bigint OFFSET sqlc.arg(offset_value)::bigint;

-- name: GetRoleWithProductByParticipantID :many
WITH role_data AS (
    SELECT
        r.id,
        r.name,
        COALESCE(p.product_id, r.product_id) AS product_id,
        r.is_system,
        r.active_flg,
        r.created_at,
        r.updated_at,
        r.deleted_at,
        pr.participant_id
    FROM roles r
             INNER JOIN participants_roles pr ON pr.role_id = r.id
             LEFT JOIN participants p ON p.id = pr.participant_id
    WHERE pr.participant_id  = $1
)
SELECT
    rd.id,
    rd.name,
    rd.participant_id,
    rd.product_id,
    rd.is_system,
    rd.active_flg,
    rd.created_at,
    rd.updated_at,
    rd.deleted_at
FROM role_data rd;

-- name: GetAllByUserID :many
WITH user_participants AS (SELECT p.id AS participant_id
                           FROM participants p
                           WHERE p.user_id = $1),
     user_roles AS (SELECT role_id
                    FROM users_roles
                    WHERE user_id = $1),
     participant_roles AS (SELECT pr.role_id
                           FROM participants_roles pr
                                    INNER JOIN user_participants up ON pr.participant_id = up.participant_id),
     user_groups AS (SELECT group_id
                     FROM users_groups
                     WHERE user_id = $1),
     participant_groups AS (SELECT pg.group_id
                            FROM participants_groups pg
                                     INNER JOIN user_participants up ON pg.participant_id = up.participant_id),
     all_group_ids AS (
         SELECT group_id
         FROM user_groups
         UNION
         SELECT group_id
         FROM participant_groups
     ),
     group_roles AS (SELECT role_id
                     FROM groups_roles gr
                              INNER JOIN all_group_ids agi ON gr.group_id = agi.group_id),
     all_role_ids AS (
         SELECT role_id
         FROM user_roles
         UNION
         SELECT role_id
         FROM participant_roles
         UNION
         SELECT role_id
         FROM group_roles
     )
SELECT r.id,
       r.name,
       r.product_id,
       r.is_system,
       r.active_flg,
       r.created_at,
       r.updated_at,
       r.deleted_at
FROM roles r
         INNER JOIN all_role_ids ari ON r.id = ari.role_id;


-- name: GetAllWithProductIDByUserID :many
WITH user_direct_roles AS (
    SELECT ur.role_id, NULL::integer as product_id
    FROM users_roles ur
    WHERE ur.user_id = $1
),
participant_roles_with_products AS (
    SELECT pr.role_id, p.product_id
    FROM participants_roles pr
    INNER JOIN participants p ON pr.participant_id = p.id
    WHERE p.user_id = $1
),
all_user_roles AS (
    SELECT role_id, product_id FROM user_direct_roles
    UNION
    SELECT role_id, product_id FROM participant_roles_with_products
)
SELECT r.id,
       r.name,
       aur.product_id,
       r.is_system,
       r.active_flg
FROM roles r
INNER JOIN all_user_roles aur ON r.id = aur.role_id
ORDER BY r.id, aur.product_id NULLS FIRST;


-- name: DeactivateByIDs :exec
UPDATE roles
SET active_flg     = false,
    updated_at     = NOW()
WHERE id =  ANY (sqlc.narg('role_ids')::bigint[]);

-- name: ExistByName :one
SELECT COUNT(*) > 0 AS exists
FROM roles
WHERE name = $1;