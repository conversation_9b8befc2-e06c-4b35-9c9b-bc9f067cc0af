-- name: CreateCategoryGroup :one
INSERT INTO categories_groups (category_id, group_id)
VALUES ($1, $2)
ON CONFLICT (category_id, group_id) DO NOTHING
RETURNING id, category_id, group_id;

-- name: GetAllCategoryGroups :many
SELECT id, category_id, group_id
FROM categories_groups;

-- name: GetCategoryGroupByID :one
SELECT id, category_id, group_id
FROM categories_groups
WHERE id = $1
LIMIT 1;

-- name: GetCategoryGroupsByCategoryID :many
SELECT id, category_id, group_id
FROM categories_groups
WHERE category_id = $1;

-- name: HasCategoryGroup :one
SELECT EXISTS(SELECT 1
              FROM categories_groups
              WHERE category_id = $1
                AND group_id = $2) AS exists;

-- name: UpdateCategoryGroup :one
UPDATE categories_groups
SET group_id = $2
WHERE category_id = $1
RETURNING id, category_id, group_id;

-- name: Delete :exec
DELETE
FROM categories_groups
WHERE category_id = $1
  AND group_id = $2;

