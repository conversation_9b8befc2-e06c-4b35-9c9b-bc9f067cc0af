-- name: CreateProduct :one
INSERT INTO products (iid, tech_name, name, creator_id, active_flg)
VALUES ($1, $2, $3, $4, TRUE)
ON CONFLICT (tech_name) DO NOTHING
RETURNING
    id,
    iid,
    tech_name,
    name,
    description,
    creator_id,
    active_flg,
    created_at,
    updated_at,
    deleted_at;

-- name: AddParticipant :one
INSERT INTO participants (product_id, user_id)
VALUES ($1, $2)
ON CONFLICT (product_id, user_id) DO NOTHING
RETURNING id, product_id, user_id, created_at, updated_at;

-- name: GetRoleByName :one
SELECT id,
       name,
       product_id,
       is_system,
       active_flg,
       is_protected,
       created_at,
       updated_at,
       deleted_at
FROM roles
WHERE name = $1
LIMIT 1;

-- name: AddParticipantRole :exec
INSERT INTO participants_roles (participant_id, role_id)
VALUES ($1, $2)
ON CONFLICT (participant_id, role_id) DO NOTHING;

-- name: GetAllProducts :many
SELECT id,
       iid,
       tech_name,
       name,
       description,
       creator_id,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM products;

-- name: GetProductByID :one
SELECT id,
       iid,
       tech_name,
       name,
       description,
       creator_id,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM products
WHERE id = $1
LIMIT 1;

-- name: GetProductByIID :one
SELECT id,
       iid,
       tech_name,
       name,
       description,
       creator_id,
       active_flg,
       created_at,
       updated_at,
       deleted_at
FROM products
WHERE iid = $1
LIMIT 1;

-- name: GetProductsByUserID :many
SELECT p.id,
       p.iid,
       p.tech_name,
       p.name,
       p.description,
       p.creator_id,
       p.active_flg,
       p.created_at,
       p.updated_at,
       p.deleted_at
FROM products p
         INNER JOIN participants pt ON pt.product_id = p.id
WHERE pt.user_id = $1;

-- name: UpdateProduct :one
UPDATE products
SET iid         = COALESCE(sqlc.narg(iid)::VARCHAR, iid),
    tech_name   = COALESCE(sqlc.narg(tech_name)::VARCHAR, tech_name),
    name        = COALESCE(sqlc.narg(name)::VARCHAR, name),
    description = COALESCE(sqlc.narg(description)::VARCHAR, description),
    active_flg  = COALESCE(sqlc.narg(active_flg)::BOOLEAN, active_flg),
    deleted_at  = COALESCE(sqlc.narg(deleted_at)::TIMESTAMPTZ, deleted_at)
WHERE id = $1
RETURNING
    id,
    iid,
    tech_name,
    name,
    description,
    creator_id,
    active_flg,
    created_at,
    updated_at,
    deleted_at;