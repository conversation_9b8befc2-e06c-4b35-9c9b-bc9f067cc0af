package primedb

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/permission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type PermissionDB struct {
	*db
	queries *sqlc.Queries
}

func NewPermissionPrimeDB(db *db) repository.PermissionPrimeDB {
	return &PermissionDB{db, sqlc.New(db.client)}
}

func (p *PermissionDB) Create(data entity.Permission) (entity.Permission, error) {
	permissionDB, err := p.queries.CreatePermission(context.Background(), sqlc.CreatePermissionParams{
		Name:   data.Name,
		Method: data.Method,
	})
	if err != nil {
		return entity.Permission{}, err
	}

	return entity.Permission{
		ID:     permissionDB.ID,
		Name:   permissionDB.Name,
		Method: permissionDB.Method,
	}, nil
}

func (p *PermissionDB) GetAll() ([]entity.Permission, error) {

	permissionsDB, err := p.queries.GetAllPermissions(context.Background())
	if err != nil {
		return nil, err
	}

	permissions := make([]entity.Permission, 0, len(permissionsDB))
	for _, permissionDB := range permissionsDB {
		permissions = append(permissions, entity.Permission{
			ID:     permissionDB.ID,
			Name:   permissionDB.Name,
			Method: permissionDB.Method,
		})
	}

	return permissions, nil
}

func (p *PermissionDB) GetAllUniqueNames() (map[string]struct{}, error) {
	permissionsDB, err := p.queries.GetAllUniqueNames(context.Background())
	if err != nil {
		return nil, err
	}

	names := make(map[string]struct{}, len(permissionsDB))
	for _, permissionDB := range permissionsDB {
		names[permissionDB] = struct{}{}
	}
	return names, nil
}

func (p *PermissionDB) GetByID(id int64) (entity.Permission, error) {

	permissionDB, err := p.queries.GetPermissionByID(context.Background(), id)
	if err != nil {
		return entity.Permission{}, err
	}

	return entity.Permission{
		ID:     permissionDB.ID,
		Name:   permissionDB.Name,
		Method: permissionDB.Method,
	}, nil
}

func (p *PermissionDB) GetIDsByName(name string) ([]int64, error) {
	IDs, err := p.queries.GetIDsByName(context.Background(), name)
	if err != nil {
		return nil, err
	}

	return IDs, nil
}

func (p *PermissionDB) GetByNameAndMethod(name, method string) (entity.Permission, error) {
	permissionDB, err := p.queries.GetPermissionByNameMethod(context.Background(), sqlc.GetPermissionByNameMethodParams{
		Name:   name,
		Method: method,
	})
	if err != nil {

	}
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return entity.Permission{}, errkit.NewObjectNotFound(errkit.ObjectTypePermission, fmt.Sprintf("name:%s and method:%s", name, method), errkit.StateNotFound)
		}

		return entity.Permission{}, err
	}

	return entity.Permission{
		ID:     permissionDB.ID,
		Name:   permissionDB.Name,
		Method: permissionDB.Method,
	}, nil
}

func (p *PermissionDB) GetByUserID(userID int64) ([]entity.Permission, error) {
	permissionsDB, err := p.queries.GetPermissionsByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	permissions := make([]entity.Permission, 0, len(permissionsDB))
	for _, permissionDB := range permissionsDB {
		permissions = append(permissions, entity.Permission{
			ID:     permissionDB.ID,
			Name:   permissionDB.Name,
			Method: permissionDB.Method,
		})
	}

	return permissions, nil
}

func (p *PermissionDB) Update(data entity.Permission) (entity.Permission, error) {
	permissionDB, err := p.queries.UpdatePermission(context.Background(), sqlc.UpdatePermissionParams{
		ID:     data.ID,
		Name:   data.Name,
		Method: data.Method,
	})
	if err != nil {
		return entity.Permission{}, err
	}

	return entity.Permission{
		ID:     permissionDB.ID,
		Name:   permissionDB.Name,
		Method: permissionDB.Method,
	}, nil
}
