package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/userrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

type UserRoleDB struct {
	*db
	queries *sqlc.Queries
}

func NewUserRoleDB(db *db) repository.UserRolePrimeDB {
	return &UserRoleDB{db, sqlc.New(db.client)}
}

func (db *UserRoleDB) Create(ctx context.Context, data entity.UserRoleCreateData) (entity.UserRole, error) {

	userRoleCreated, err := db.queries.CreateUserRole(context.Background(), sqlc.CreateUserRoleParams{
		UserID: data.UserID,
		RoleID: data.RoleID,
	})
	if err != nil {
		return entity.UserRole{}, err
	}

	createdUserRole := entity.UserRole{
		ID:        userRoleCreated.ID,
		UserID:    userRoleCreated.UserID,
		RoleID:    userRoleCreated.RoleID,
		CreatedAt: userRoleCreated.CreatedAt,
	}

	return createdUserRole, nil
}

func (db *UserRoleDB) CreateByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error {
	return db.queries.CreateUserRolesByUserIDAndRoleIDs(context.Background(), sqlc.CreateUserRolesByUserIDAndRoleIDsParams{
		UserID:  userID,
		RoleIds: roleIDs,
	})
}

func (db *UserRoleDB) CreateByRoleIDAndUserIDs(ctx context.Context, roleID int64, userIDs []int64) error {
	return db.queries.CreateUserRolesByRoleIDAndUserIDs(context.Background(), sqlc.CreateUserRolesByRoleIDAndUserIDsParams{
		RoleID:  roleID,
		UserIds: userIDs,
	})
}

func (db *UserRoleDB) GetAll() ([]entity.UserRole, error) {

	userRoles, err := db.queries.GetAllUserRoles(context.Background())
	if err != nil {
		return nil, err
	}

	userRolesModels := make([]entity.UserRole, len(userRoles))
	for i, userRole := range userRoles {
		userRolesModels[i] = entity.UserRole{
			ID:        userRole.ID,
			UserID:    userRole.UserID,
			RoleID:    userRole.RoleID,
			CreatedAt: userRole.CreatedAt,
		}
	}

	return userRolesModels, nil
}

func (db *UserRoleDB) GetByID(id int64) (entity.UserRole, error) {

	userRole, err := db.queries.GetUserRoleByID(context.Background(), id)
	if err != nil {
		return entity.UserRole{}, err
	}

	return entity.UserRole{
		ID:        userRole.ID,
		UserID:    userRole.UserID,
		RoleID:    userRole.RoleID,
		CreatedAt: userRole.CreatedAt,
	}, nil
}

func (db *UserRoleDB) GetByUserID(userID int64) ([]entity.UserRole, error) {

	userRoles, err := db.queries.GetUserRolesByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	userRolesModels := make([]entity.UserRole, len(userRoles))
	for i, userRole := range userRoles {
		userRolesModels[i] = entity.UserRole{
			ID:        userRole.ID,
			UserID:    userRole.UserID,
			RoleID:    userRole.RoleID,
			CreatedAt: userRole.CreatedAt,
		}
	}

	return userRolesModels, nil
}

func (db *UserRoleDB) GetUserRoleIDs(userID int64) ([]int64, error) {

	userRoles, err := db.queries.GetUserRoleIDs(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	return userRoles, nil
}

func (db *UserRoleDB) GetUserRolesByRoleID(roleID int64) ([]entity.UserRole, error) {

	userRoles, err := db.queries.GetUserRolesByRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}

	userRolesModels := make([]entity.UserRole, len(userRoles))
	for i, userRole := range userRoles {
		userRolesModels[i] = entity.UserRole{
			ID:        userRole.ID,
			UserID:    userRole.UserID,
			RoleID:    userRole.RoleID,
			CreatedAt: userRole.CreatedAt,
		}
	}

	return userRolesModels, nil
}

func (db *UserRoleDB) GetByUserIDAndRoleID(userID, roleID int64) (entity.UserRole, error) {

	userRole, err := db.queries.GetUserRoleByUserIDRoleID(context.Background(), sqlc.GetUserRoleByUserIDRoleIDParams{
		UserID: userID,
		RoleID: roleID,
	})
	if err != nil {
		return entity.UserRole{}, err
	}

	return entity.UserRole{
		ID:        userRole.ID,
		UserID:    userRole.UserID,
		RoleID:    userRole.RoleID,
		CreatedAt: userRole.CreatedAt,
	}, nil
}

func (db *UserRoleDB) Update(data entity.UserRoleUpdateData) (entity.UserRole, error) {

	userRole, err := db.queries.UpdateUserRole(context.Background(), sqlc.UpdateUserRoleParams{
		ID:     data.ID,
		UserID: *data.UserID,
		RoleID: *data.RoleID,
	})
	if err != nil {
		return entity.UserRole{}, err
	}

	return entity.UserRole{
		ID:        userRole.ID,
		UserID:    userRole.UserID,
		RoleID:    userRole.RoleID,
		CreatedAt: userRole.CreatedAt,
	}, nil
}

func (db *UserRoleDB) DeleteByUserID(userID int64) error {

	err := db.queries.DeleteUserRolesByUserID(context.Background(), userID)
	if err != nil {
		return err
	}

	return nil
}

func (db *UserRoleDB) DeleteByRoleID(ctx context.Context, roleID int64) error {
	// userID, ok := ctx.Value(constants.UserIDKey).(int64)
	// if !ok {
	// 	return fmt.Errorf("user ID is missing in context")
	// }

	// _, _ = db.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	err := db.queries.DeleteUserRolesByRoleID(context.Background(), roleID)
	if err != nil {
		return err
	}

	return nil
}

func (db *UserRoleDB) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error {
	return db.queries.DeleteUserRolesByUserIDAndRoleIDs(context.Background(), sqlc.DeleteUserRolesByUserIDAndRoleIDsParams{
		UserID:  userID,
		RoleIds: roleIDs,
	})
}

func (db *UserRoleDB) DeleteByUserIDAndRoleID(ctx context.Context, userID int64, roleID int64) error {
	return db.queries.DeleteUserRoleByUserIDAndRoleID(context.Background(), sqlc.DeleteUserRoleByUserIDAndRoleIDParams{
		UserID: userID,
		RoleID: roleID,
	})
}

func (db *UserRoleDB) DeleteByRoleIDAndUserIDs(ctx context.Context, roleID int64, userIDs []int64) error {
	return db.queries.DeleteUserRolesByRoleIDAndUserIDs(context.Background(), sqlc.DeleteUserRolesByRoleIDAndUserIDsParams{
		RoleID:  roleID,
		UserIds: userIDs,
	})
}
