package primedb

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"github.com/jackc/pgx/v5"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/user"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

type UserDB struct {
	*db
	queries *sqlc.Queries
}

func NewUserPrimeDB(db *db) repository.UserPrimeDB {
	return &UserDB{db, sqlc.New(db.client)}
}

func (db *UserDB) Create(ctx context.Context, data userentity.User) (userentity.User, error) {
	userID, ok := ctx.Value(constants.UserIDKey).(int64)
	if !ok {
		return userentity.User{}, fmt.Errorf("user ID is missing in context")
	}

	_, err := db.db.client.Exec(ctx, fmt.Sprintf("SET audit.user_id = %d", userID))
	if err != nil {
		return userentity.User{}, fmt.Errorf("failed to set audit user_id: %w", err)
	}

	userCreated, err := db.queries.CreateUser(context.Background(), sqlc.CreateUserParams{
		CategoryID: data.CategoryID,
		Email:      data.Email,
		FullName:   data.FullName,
		Position:   &data.Position,
		Photo:      data.Photo,
	})
	if err != nil {
		return userentity.User{}, err
	}

	createdUser := userentity.User{
		ID:                  userCreated.ID,
		CategoryID:          userCreated.CategoryID,
		Email:               userCreated.Email,
		FullName:            userCreated.FullName,
		Position:            *userCreated.Position,
		IsAdmin:             userCreated.IsAdmin,
		ActiveFlg:           userCreated.ActiveFlg,
		LastActiveProductID: userCreated.LastActiveProductID,
		LastLoginAt:         userCreated.LastLoginAt,
		CreatedAt:           userCreated.CreatedAt,
		DeletedAt:           userCreated.DeletedAt,
		Photo:               userCreated.Photo,
	}

	return createdUser, nil
}

func (db *UserDB) GetAll() ([]userentity.User, error) {
	usersDB, err := db.queries.GetAllUsers(context.Background())
	if err != nil {
		return nil, err
	}

	users := make([]userentity.User, 0, len(usersDB))
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            *userDB.Position,
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return users, nil
}

func (db *UserDB) GetByID(id int64) (userentity.User, error) {
	userDB, err := db.queries.GetUserByID(context.Background(), id)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return userentity.User{}, errkit.NewObjectNotFound(errkit.ObjectTypeUser, strconv.FormatInt(id, 10), errkit.StateNotFound)
		}
		return userentity.User{}, err
	}

	return userentity.User{
		ID:                  userDB.ID,
		CategoryID:          userDB.CategoryID,
		Email:               userDB.Email,
		FullName:            userDB.FullName,
		Position:            stringFromPointerOrNil(userDB.Position),
		IsAdmin:             userDB.IsAdmin,
		ActiveFlg:           userDB.ActiveFlg,
		LastActiveProductID: userDB.LastActiveProductID,
		LastLoginAt:         userDB.LastLoginAt,
		CreatedAt:           userDB.CreatedAt,
		DeletedAt:           userDB.DeletedAt,
		Photo:               userDB.Photo,
	}, nil
}

func (db *UserDB) GetByGroupID(id int64) ([]userentity.User, error) {

	usersDB, err := db.queries.GetByGroupID(context.Background(), id)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errkit.NewObjectNotFound(errkit.ObjectTypeUser, strconv.FormatInt(id, 10), errkit.StateNotFound)
		}
		return nil, err
	}

	var users []userentity.User
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            stringFromPointerOrNil(userDB.Position),
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return users, nil
}

func (db *UserDB) GetByCategoryID(categoryID int64) ([]userentity.User, error) {
	usersDB, err := db.queries.GetUsersByCategoryID(context.Background(), categoryID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errkit.NewObjectNotFound(errkit.ObjectTypeUser, strconv.FormatInt(categoryID, 10), errkit.StateNotFound)
		}
		return nil, err
	}

	var users []userentity.User
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            stringFromPointerOrNil(userDB.Position),
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return users, nil
}

func (db *UserDB) GetByEmail(email string) (userentity.User, error) {
	userDB, err := db.queries.GetUserByEmail(context.Background(), email)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return userentity.User{}, errkit.NewObjectNotFound(errkit.ObjectTypeUser, email, errkit.StateNotFound)
		}
		return userentity.User{}, err
	}

	return userentity.User{
		ID:                  userDB.ID,
		CategoryID:          userDB.CategoryID,
		Email:               userDB.Email,
		FullName:            userDB.FullName,
		Position:            *userDB.Position,
		IsAdmin:             userDB.IsAdmin,
		ActiveFlg:           userDB.ActiveFlg,
		LastActiveProductID: userDB.LastActiveProductID,
		LastLoginAt:         userDB.LastLoginAt,
		CreatedAt:           userDB.CreatedAt,
		DeletedAt:           userDB.DeletedAt,
		Photo:               userDB.Photo,
	}, nil
}

func (db *UserDB) GetByFilters(userFilters userentity.UserFiltersData) ([]userentity.User, error) {
	var (
		email       *string
		fullName    *string
		categoryIDs []int64
		productIDs  []int64
	)

	if userFilters.Email != nil && *userFilters.Email != "" {
		email = userFilters.Email
	}

	if userFilters.FullName != nil && *userFilters.FullName != "" {
		fullName = userFilters.FullName
	}

	if userFilters.CategoryIDs != nil && len(*userFilters.CategoryIDs) > 0 {
		categoryIDs = *userFilters.CategoryIDs
	}

	if userFilters.ProductIDs != nil && len(*userFilters.ProductIDs) > 0 {
		productIDs = *userFilters.ProductIDs
	}

	usersDB, err := db.queries.GetUsersByFiltersPaginated(context.Background(), sqlc.GetUsersByFiltersPaginatedParams{
		Email:       email,
		FullName:    fullName,
		IsAdmin:     userFilters.IsAdmin,
		CategoryIds: categoryIDs,
		ProductIds:  productIDs,
		LimitValue:  1000,
		OffsetValue: 0,
	})
	if err != nil {
		return nil, err
	}

	users := make([]userentity.User, 0, len(usersDB))
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            *userDB.Position,
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return users, nil
}

func (db *UserDB) GetUsersByParticipantRoleID(roleID int64) ([]userentity.User, error) {
	usersDB, err := db.queries.GetUsersByParticipantRoleID(context.Background(), roleID)
	if err != nil {
		return nil, err
	}

	users := make([]userentity.User, 0, len(usersDB))
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            *userDB.Position,
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return users, nil
}

func (db *UserDB) GetUsersByParticipantGroupID(groupID int64) ([]userentity.User, error) {
	usersDB, err := db.queries.GetUsersByParticipantGroupID(context.Background(), groupID)
	if err != nil {
		return nil, err
	}

	users := make([]userentity.User, 0, len(usersDB))
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            *userDB.Position,
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return users, nil
}

func (db *UserDB) Update(data userentity.UserUpdateData) (userentity.User, error) {

	user, err := db.queries.UpdateUser(context.Background(), sqlc.UpdateUserParams{
		ID:                  data.ID,
		CategoryID:          data.CategoryID,
		Email:               data.Email,
		FullName:            data.FullName,
		Position:            data.Position,
		Photo:               data.Photo,
		IsAdmin:             data.IsAdmin,
		ActiveFlg:           data.ActiveFlg,
		LastActiveProductID: data.LastActiveProductID,
		LastLoginAt:         data.LastLoginAt,
	})
	if err != nil {
		return userentity.User{}, err
	}

	return userentity.User{
		ID:                  user.ID,
		CategoryID:          user.CategoryID,
		Email:               user.Email,
		FullName:            user.FullName,
		Position:            *user.Position,
		IsAdmin:             user.IsAdmin,
		ActiveFlg:           user.ActiveFlg,
		LastActiveProductID: user.LastActiveProductID,
		LastLoginAt:         user.LastLoginAt,
		CreatedAt:           user.CreatedAt,
		DeletedAt:           user.DeletedAt,
		Photo:               user.Photo,
	}, nil
}

func (db *UserDB) GetAllPaginated(pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error) {
	var totalCount int64
	totalCount, err := db.queries.GetAllUsersTotalCount(context.Background())
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	usersDB, err := db.queries.GetAllUsersPaginated(context.Background(), sqlc.GetAllUsersPaginatedParams{
		LimitValue:  pagination.Limit,
		OffsetValue: pagination.Offset,
	})
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	users := make([]userentity.User, 0, len(usersDB))
	for _, userDB := range usersDB {

		var position string
		if userDB.Position != nil {
			position = *userDB.Position
		}

		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            position,
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return sharedentity.PaginatedResult[userentity.User]{
		Items:  users,
		Total:  totalCount,
		Limit:  pagination.Limit,
		Offset: pagination.Offset,
	}, nil
}

func (db *UserDB) GetByFiltersPaginated(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error) {
	var (
		email       *string
		fullName    *string
		categoryIDs []int64
		productIDs  []int64
	)

	if userFilters.Email != nil && *userFilters.Email != "" {
		email = userFilters.Email
	}

	if userFilters.FullName != nil && *userFilters.FullName != "" {
		fullName = userFilters.FullName
	}

	if userFilters.CategoryIDs != nil && len(*userFilters.CategoryIDs) > 0 {
		categoryIDs = *userFilters.CategoryIDs
	}

	if userFilters.ProductIDs != nil && len(*userFilters.ProductIDs) > 0 {
		productIDs = *userFilters.ProductIDs
	}

	ctx := context.Background()
	params := sqlc.GetUsersByFiltersTotalCountParams{
		Email:       email,
		FullName:    fullName,
		IsAdmin:     userFilters.IsAdmin,
		CategoryIds: categoryIDs,
		ProductIds:  productIDs,
	}

	totalCount, err := db.queries.GetUsersByFiltersTotalCount(ctx, params)
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	paramsPaginated := sqlc.GetUsersByFiltersPaginatedParams{
		Email:       email,
		FullName:    fullName,
		IsAdmin:     userFilters.IsAdmin,
		CategoryIds: categoryIDs,
		ProductIds:  productIDs,
		LimitValue:  pagination.Limit,
		OffsetValue: pagination.Offset,
	}
	usersDB, err := db.queries.GetUsersByFiltersPaginated(ctx, paramsPaginated)
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	users := make([]userentity.User, 0, len(usersDB))
	for _, userDB := range usersDB {
		users = append(users, userentity.User{
			ID:                  userDB.ID,
			CategoryID:          userDB.CategoryID,
			Email:               userDB.Email,
			FullName:            userDB.FullName,
			Position:            *userDB.Position,
			IsAdmin:             userDB.IsAdmin,
			ActiveFlg:           userDB.ActiveFlg,
			LastActiveProductID: userDB.LastActiveProductID,
			LastLoginAt:         userDB.LastLoginAt,
			CreatedAt:           userDB.CreatedAt,
			DeletedAt:           userDB.DeletedAt,
			Photo:               userDB.Photo,
		})
	}

	return sharedentity.PaginatedResult[userentity.User]{
		Items:  users,
		Total:  totalCount,
		Limit:  pagination.Limit,
		Offset: pagination.Offset,
	}, nil
}

func (db *UserDB) GetAdmins() ([]userentity.User, error) {
	isAdmin := true
	adminFilter := userentity.UserFiltersData{
		IsAdmin: &isAdmin,
	}

	return db.GetByFilters(adminFilter)
}
