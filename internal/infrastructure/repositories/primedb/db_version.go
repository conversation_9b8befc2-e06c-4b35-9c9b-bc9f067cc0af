package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/dbversion"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/repository"
)

type DBVersion struct {
	*db
	queries *sqlc.Queries
}

func NewDBVersionPrimeDB(db *db) repository.DBVersionPrimeDB {
	return &DBVersion{db, sqlc.New(db.client)}
}

func (r *DBVersion) GetVersion(ctx context.Context) (string, error) {
	version, err := r.queries.GetVersion(ctx)
	if err != nil {
		return "", err
	}
	return extractVersion(version), nil
}
