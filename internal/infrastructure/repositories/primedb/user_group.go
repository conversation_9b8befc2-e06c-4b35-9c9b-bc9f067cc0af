package primedb

import (
	"context"

	sqlc "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/usergroup"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

type UserGroupDB struct {
	*db
	queries *sqlc.Queries
}

func NewUserGroupDB(db *db) repository.UserGroupPrimeDB {
	return &UserGroupDB{db, sqlc.New(db.client)}
}

func (db *UserGroupDB) Create(ctx context.Context, data userentity.UserGroupCreateData) (userentity.UserGroup, error) {

	userGroupCreated, err := db.queries.CreateUserGroup(context.Background(), sqlc.CreateUserGroupParams{
		UserID:  data.UserID,
		GroupID: data.GroupID,
	})
	if err != nil {
		return userentity.UserGroup{}, err
	}

	createdUserGroup := userentity.UserGroup{
		ID:        userGroupCreated.ID,
		UserID:    userGroupCreated.UserID,
		GroupID:   userGroupCreated.GroupID,
		CreatedAt: userGroupCreated.CreatedAt,
	}

	return createdUserGroup, nil
}

func (db *UserGroupDB) AssignUserToGroups(userID int64, groupIDs []int64) error {
	return db.queries.AssignUserToGroups(context.Background(), sqlc.AssignUserToGroupsParams{
		UserID:   userID,
		GroupIds: groupIDs,
	})
}

func (db *UserGroupDB) GetByUserID(userID int64) ([]userentity.UserGroup, error) {

	userGroupsDB, err := db.queries.GetUserGroupsByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	userGroups := make([]userentity.UserGroup, len(userGroupsDB))
	for i, userGroup := range userGroupsDB {
		userGroups[i] = userentity.UserGroup{
			ID:        userGroup.ID,
			UserID:    userGroup.UserID,
			GroupID:   userGroup.GroupID,
			CreatedAt: userGroup.CreatedAt,
		}
	}

	return userGroups, nil
}

func (db *UserGroupDB) GetByGroupID(groupID int64) ([]userentity.UserGroup, error) {

	userGroupsDB, err := db.queries.GetUserGroupsByGroupID(context.Background(), groupID)
	if err != nil {
		return nil, err
	}

	userGroups := make([]userentity.UserGroup, len(userGroupsDB))
	for i, userGroup := range userGroupsDB {
		userGroups[i] = userentity.UserGroup{
			ID:        userGroup.ID,
			UserID:    userGroup.UserID,
			GroupID:   userGroup.GroupID,
			CreatedAt: userGroup.CreatedAt,
		}
	}

	return userGroups, nil
}

func (db *UserGroupDB) GetUserGroups(userID int64) ([]int64, error) {

	userGroups, err := db.queries.GetUserGroups(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	return userGroups, nil
}

func (db *UserGroupDB) GetGroupsByIDs(groupIDs []int64) ([]groupentity.Group, error) {

	groupsDB, err := db.queries.GetGroupsByIDs(context.Background(), groupIDs)
	if err != nil {
		return nil, err
	}

	groups := make([]groupentity.Group, len(groupsDB))
	for i, group := range groupsDB {
		groups[i] = groupentity.Group{
			ID:        group.ID,
			Name:      group.Name,
			ProductID: group.ProductID,
			ActiveFlg: group.ActiveFlg,
			CreatedAt: group.CreatedAt,
			UpdatedAt: group.UpdatedAt,
			DeletedAt: group.DeletedAt,
		}
	}

	return groups, nil
}

func (db *UserGroupDB) GetParticipantGroupsByUserID(userID int64) ([]groupentity.Group, error) {

	participantGroups, err := db.queries.GetParticipantGroupsWithProductByUserID(context.Background(), userID)
	if err != nil {
		return nil, err
	}

	groups := make([]groupentity.Group, len(participantGroups))
	for i, group := range participantGroups {
		groups[i] = groupentity.Group{
			ID:        group.ID,
			Name:      group.Name,
			ProductID: &group.ProductID,
			IsSystem:  group.IsSystem,
			ActiveFlg: group.ActiveFlg,
			CreatedAt: group.CreatedAt,
			UpdatedAt: group.UpdatedAt,
			DeletedAt: group.DeletedAt,
		}
	}

	return groups, nil
}

func (db *UserGroupDB) GetProductsByIDs(productIDs []int64) ([]productentity.Product, error) {

	productsDB, err := db.queries.GetProductsByIDs(context.Background(), productIDs)
	if err != nil {
		return nil, err
	}

	products := make([]productentity.Product, len(productsDB))
	for i, product := range productsDB {
		products[i] = productentity.Product{
			ID:        product.ID,
			Name:      product.Name,
			ActiveFlg: product.ActiveFlg,
			CreatedAt: product.CreatedAt,
			UpdatedAt: product.UpdatedAt,
			DeletedAt: product.DeletedAt,
		}
	}

	return products, nil
}

func (db *UserGroupDB) DeleteByGroupID(ctx context.Context, groupID int64) error {

	err := db.queries.DeleteUserGroupsByGroupID(ctx, groupID)
	if err != nil {
		return err
	}

	return nil
}

func (db *UserGroupDB) DeleteByUserID(ctx context.Context, userID int64) error {

	err := db.queries.DeleteUserGroupsByUserID(ctx, userID)
	if err != nil {
		return err
	}

	return nil
}

func (db *UserGroupDB) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error {

	err := db.queries.DeleteUserGroupsByUserIDAndGroupIDs(ctx, sqlc.DeleteUserGroupsByUserIDAndGroupIDsParams{
		UserID:   userID,
		GroupIds: groupIDs,
	})
	if err != nil {
		return err
	}

	return nil
}

func (db *UserGroupDB) DeleteByGroupIDAndUserIDs(ctx context.Context, groupID int64, userIDs []int64) error {
	return db.queries.DeleteUserGroupsByGroupIDAndUserIDs(context.Background(), sqlc.DeleteUserGroupsByGroupIDAndUserIDsParams{
		GroupID: groupID,
		UserIds: userIDs,
	})
}

func (db *UserGroupDB) CreateByGroupIDAndUserIDs(ctx context.Context, groupID int64, userIDs []int64) error {
	return db.queries.CreateUserGroupsByGroupIDAndUserIDs(context.Background(), sqlc.CreateUserGroupsByGroupIDAndUserIDsParams{
		GroupID: groupID,
		UserIds: userIDs,
	})
}
