package primedb

import (
	"context"

	sqlcproposaluserviews "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/sqlc/proposaluserviews"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository"
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

type UserViewDB struct {
	*db
	queries *sqlcproposaluserviews.Queries
}

func NewUserViewDB(db *db) repository.UserViewDB {
	return &UserViewDB{db, sqlcproposaluserviews.New(db.client)}
}

func (u *UserViewDB) GetByProposalIDAndUserID(proposalID int64, userID int64) (voproposal.UserView, error) {
	uv, err := u.queries.GetByProposalIDAndUserID(context.Background(), sqlcproposaluserviews.GetByProposalIDAndUserIDParams{
		ProposalID: proposalID,
		UserID:     userID,
	})
	if err != nil {
		return voproposal.UserView{}, err
	}

	return voproposal.UserView{
		ID:         uv.ID,
		ProposalID: uv.ProposalID,
		UserID:     uv.UserID,
		ViewedAt:   uv.ViewedAt,
	}, nil
}

func (u *UserViewDB) Update(data voproposal.UserView) (voproposal.UserView, error) {
	uv, err := u.queries.Update(context.Background(), sqlcproposaluserviews.UpdateParams{
		ProposalID: data.ProposalID,
		UserID:     data.UserID,
		ViewedAt:   data.ViewedAt,
	})
	if err != nil {
		return voproposal.UserView{}, err
	}

	return voproposal.UserView{
		ID:         uv.ID,
		ProposalID: uv.ProposalID,
		UserID:     uv.UserID,
		ViewedAt:   uv.ViewedAt,
	}, nil
}
