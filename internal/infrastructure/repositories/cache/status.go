package cache

import (
	"context"
	"fmt"
	"regexp"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/repository"
)

type StatusCache struct {
	baseCache
}

func NewStatusCache(addr, password string) repository.StatusCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})
	return &StatusCache{baseCache: baseCache{client: client}}
}

func (c *StatusCache) GetVersion(ctx context.Context) (string, error) {
	infoResp, err := c.client.Info(ctx, "server").Result()
	if err != nil {
		return "", err
	}

	regex := regexp.MustCompile(`(?m)^redis_version:(\S+)`)
	matches := regex.FindStringSubmatch(infoResp)
	if len(matches) < 2 {
		return "", fmt.<PERSON><PERSON><PERSON>("version not found in INFO")
	}

	return matches[1], nil
}
