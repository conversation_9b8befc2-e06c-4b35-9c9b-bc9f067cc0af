package cache

import (
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	statusrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/repository"
)

type Registry struct {
	Status statusrepository.StatusCache
	Group  grouprepository.GroupCache
	// Product   interfaces.ProductCache
	// User      interfaces.UserCache
	// Permission interfaces.PermissionCache
	// Role      interfaces.RoleCache
	// Category  interfaces.CategoryCache
	// Другие кэши можно добавить при необходимости
}

func NewRegistry(redisAddr, redisPassword string) *Registry {
	return &Registry{
		Status: NewStatusCache(redisAddr, redisPassword),
		Group:  NewGroupCache(redisAddr, redisPassword),
		// Product:   NewProductCache(redisAddr, redisPassword),
		// User:      NewUserCache(redisAddr, redisPassword),
		// Permission: NewPermissionCache(redisAddr, redisPassword),
		// Role:      NewRoleCache(redisAddr, redisPassword),
		// Category:  NewCategoryCache(redisAddr, redisPassword),
	}
}
