package cache

import (
	"context"
	"strconv"

	"github.com/redis/go-redis/v9"
)

const (
	scriptVersion = "v5.4"
)

type baseCache struct {
	client *redis.Client
}

func NewCacheClient(addr, password string) *baseCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})
	return &baseCache{client: client}
}

func (c *baseCache) getIntSet(ctx context.Context, key string) ([]int64, error) {
	result, err := c.client.SMembers(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	ids := make([]int64, 0, len(result))
	for _, s := range result {
		id, _ := strconv.ParseInt(s, 10, 64)
		ids = append(ids, id)
	}
	return ids, nil
}
