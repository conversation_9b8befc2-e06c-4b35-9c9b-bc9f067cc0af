package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
)

const (
	productKeyPrefix      = "product:"
	userProductsKeyPrefix = "user_products:"
	roleProductsKeyPrefix = "role_products:"
	productRolesKeyPrefix = "product_roles:"

	allProductsSet = "all_product_ids"

	setProduct     = "setProduct"
	setProducts    = "setProducts"
	deleteProducts = "deleteProducts"
)

type ProductCache struct {
	baseCache
	scripts map[string]*redis.Script
}

func productKey(id int64) string {
	return productKeyPrefix + strconv.FormatInt(id, 10)
}

func userProductsKey(userID int64) string {
	return userProductsKeyPrefix + strconv.FormatInt(userID, 10)
}

func roleProductsKey(roleID int64) string {
	return roleProductsKeyPrefix + strconv.FormatInt(roleID, 10)
}

func productRolesKey(productID int64) string {
	return productRolesKeyPrefix + strconv.FormatInt(productID, 10)
}

func NewProductCache(addr, password string) repository.ProductCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})

	cache := &ProductCache{
		baseCache: baseCache{client: client},
		scripts:   make(map[string]*redis.Script),
	}

	cache.registerScripts()
	return cache
}

func (c *ProductCache) registerScripts() {
	c.scripts[setProduct] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local key = KEYS[1]
		local set_key = KEYS[2]
		local data = ARGV[1]
		
		redis.call('SET', key, data)
		redis.call('SADD', set_key, key)
		return 1
	`, scriptVersion))

	c.scripts[setProducts] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		
		for i = 1, #KEYS - 1 do
			local pkey = KEYS[i + 1]
			local pdata = ARGV[i]
			redis.call('SET', pkey, pdata)
		end
		
		redis.call('SADD', set_key, unpack(KEYS, 2, #KEYS))
		return #KEYS - 1
	`, scriptVersion))

	c.scripts[deleteProducts] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		local deleted = 0
		
		for i = 2, #KEYS do
			redis.call('DEL', KEYS[i])
			deleted = deleted + redis.call('SREM', set_key, KEYS[i])
		end
		
		return deleted
	`, scriptVersion))
}

func (c *ProductCache) SetProduct(ctx context.Context, product entity.Product) error {
	data, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("marshal error: %w", err)
	}

	_, err = c.scripts[setProduct].Run(
		ctx,
		c.client,
		[]string{productKey(product.ID), allProductsSet},
		data,
	).Result()

	return err
}

func (c *ProductCache) GetProduct(ctx context.Context, id int64) (entity.Product, error) {
	data, err := c.client.Get(ctx, productKey(id)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.Product{}, fmt.Errorf("product not found: %w", err)
		}
		return entity.Product{}, fmt.Errorf("failed to get product: %w", err)
	}

	var product entity.Product
	if err := json.Unmarshal(data, &product); err != nil {
		return entity.Product{}, fmt.Errorf("failed to unmarshal product: %w", err)
	}

	return product, nil
}

func (c *ProductCache) SetProducts(ctx context.Context, products []entity.Product) error {
	if len(products) == 0 {
		return nil
	}

	keys := make([]string, 0, len(products)+1)
	args := make([]any, 0, len(products)+1)

	keys = append(keys, allProductsSet)

	for _, p := range products {
		data, err := json.Marshal(p)
		if err != nil {
			return fmt.Errorf("marshal error for product %d: %w", p.ID, err)
		}
		keys = append(keys, productKey(p.ID))
		args = append(args, data)
	}

	_, err := c.scripts[setProducts].Run(
		ctx,
		c.client,
		keys,
		args...,
	).Result()

	return err
}

func (c *ProductCache) GetProducts(ctx context.Context) ([]entity.Product, error) {
	ids, err := c.client.SMembers(ctx, allProductsSet).Result()
	if err != nil {
		return nil, err
	}

	keys := make([]string, len(ids))
	copy(keys, ids)

	values, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}

	products := make([]entity.Product, 0, len(values))
	for _, val := range values {
		if val == nil {
			continue
		}

		s, ok := val.(string)
		if !ok {
			continue
		}

		var p entity.Product
		if err := json.Unmarshal([]byte(s), &p); err != nil {
			continue
		}
		products = append(products, p)
	}

	return products, nil
}

func (c *ProductCache) DeleteProduct(ctx context.Context, id int64) error {
	return c.DeleteProducts(ctx, []int64{id})
}

func (c *ProductCache) DeleteProducts(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	keys := make([]string, len(ids)+1)
	keys[0] = allProductsSet
	for i, id := range ids {
		keys[i+1] = productKey(id)
	}

	_, err := c.scripts[deleteProducts].Run(
		ctx,
		c.client,
		keys,
	).Result()

	return err
}

func (c *UserCache) SetUserProducts(ctx context.Context, userID int64, products []entity.Product) error {
	data, err := json.Marshal(products)
	if err != nil {
		return fmt.Errorf("failed to marshal products: %w", err)
	}
	return c.client.Set(ctx, userProductsKey(userID), data, 0).Err()
}

func (c *UserCache) GetUserProducts(ctx context.Context, userID int64) ([]entity.Product, error) {
	data, err := c.client.Get(ctx, userProductsKey(userID)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get products: %w", err)
	}

	var products []entity.Product
	if err = json.Unmarshal(data, &products); err != nil {
		return nil, fmt.Errorf("failed to unmarshal products: %w", err)
	}
	return products, err
}
