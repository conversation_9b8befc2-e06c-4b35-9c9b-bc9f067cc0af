package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
)

const (
	categoryKeyPrefix = "category:"
	allCategoriesSet  = "all_category_ids"

	setCategory      = "setCategory"
	setCategories    = "setCategories"
	deleteCategories = "deleteCategories"
)

type CategoryCache struct {
	baseCache
	scripts map[string]*redis.Script
}

func categoryKey(id int64) string {
	return categoryKeyPrefix + strconv.FormatInt(id, 10)
}

func NewCategoryCache(addr, password string) repository.CategoryCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})

	cache := &CategoryCache{
		baseCache: baseCache{client: client},
		scripts:   make(map[string]*redis.Script),
	}

	cache.registerScripts()
	return cache
}

func (c *CategoryCache) registerScripts() {
	c.scripts[setCategory] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local key = KEYS[1]
		local set_key = KEYS[2]
		local data = ARGV[1]
		
		redis.call('SET', key, data)
		redis.call('SADD', set_key, key)
		return 1
	`, scriptVersion))

	c.scripts[setCategories] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		
		for i = 1, #KEYS - 1 do
			local pkey = KEYS[i + 1]
			local pdata = ARGV[i]
			redis.call('SET', pkey, pdata)
		end
		
		redis.call('SADD', set_key, unpack(KEYS, 2, #KEYS))
		return #KEYS - 1
	`, scriptVersion))

	c.scripts[deleteCategories] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		local deleted = 0
		
		for i = 2, #KEYS do
			redis.call('DEL', KEYS[i])
			deleted = deleted + redis.call('SREM', set_key, KEYS[i])
		end
		
		return deleted
	`, scriptVersion))
}

func (c *CategoryCache) SetCategory(ctx context.Context, category entity.Category) error {
	data, err := json.Marshal(category)
	if err != nil {
		return fmt.Errorf("marshal error: %w", err)
	}

	_, err = c.scripts[setCategory].Run(
		ctx,
		c.client,
		[]string{categoryKey(category.ID), allCategoriesSet},
		data,
	).Result()

	return err
}

func (c *CategoryCache) GetCategory(ctx context.Context, id int64) (entity.Category, error) {
	data, err := c.client.Get(ctx, categoryKey(id)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.Category{}, fmt.Errorf("category not found: %w", err)
		}
		return entity.Category{}, fmt.Errorf("failed to get category: %w", err)
	}

	var category entity.Category
	if err := json.Unmarshal(data, &category); err != nil {
		return entity.Category{}, fmt.Errorf("failed to unmarshal category: %w", err)
	}

	return category, nil
}

func (c *CategoryCache) SetCategories(ctx context.Context, categories []entity.Category) error {
	if len(categories) == 0 {
		return nil
	}

	keys := make([]string, 0, len(categories)+1)
	args := make([]any, 0, len(categories)+1)

	keys = append(keys, allCategoriesSet)

	for _, category := range categories {
		data, err := json.Marshal(category)
		if err != nil {
			return fmt.Errorf("marshal error for category %d: %w", category.ID, err)
		}
		keys = append(keys, categoryKey(category.ID))
		args = append(args, data)
	}

	_, err := c.scripts[setCategories].Run(
		ctx,
		c.client,
		keys,
		args...,
	).Result()

	return err
}

func (c *CategoryCache) GetCategories(ctx context.Context) ([]entity.Category, error) {
	ids, err := c.client.SMembers(ctx, allCategoriesSet).Result()
	if err != nil {
		return nil, err
	}

	keys := make([]string, len(ids))
	copy(keys, ids)

	values, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	categories := make([]entity.Category, 0, len(values))
	for _, val := range values {
		if val == nil {
			continue
		}

		s, ok := val.(string)
		if !ok {
			continue
		}

		var category entity.Category
		if err := json.Unmarshal([]byte(s), &category); err != nil {
			continue
		}
		categories = append(categories, category)
	}

	return categories, nil
}

func (c *CategoryCache) DeleteCategory(ctx context.Context, id int64) error {
	return c.DeleteCategories(ctx, []int64{id})
}

func (c *CategoryCache) DeleteCategories(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	keys := make([]string, len(ids)+1)
	keys[0] = allCategoriesSet
	for i, id := range ids {
		keys[i+1] = categoryKey(id)
	}

	_, err := c.scripts[deleteCategories].Run(
		ctx,
		c.client,
		keys,
	).Result()

	return err
}
