package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
)

const (
	roleKeyPrefix      = "role:"
	roleUsersKeyPrefix = "role_users:"
	userRolesKeyPrefix = "user_roles:"

	allRolesSet = "all_roles"

	setRoleScript           = "setRoleScript"
	setRolesScript          = "setRolesScript"
	deleteRolesScript       = "deleteRolesScript"
	assignUserRoleScript    = "assignUserRoleScript"
	removeUserRoleScript    = "removeUserRoleScript"
	getUserRolesScript      = "getUserRolesScript"
	getProductRolesScript   = "getProductRolesScript"
	assignProductRoleScript = "assignProductRoleScript"
	removeProductRoleScript = "removeProductRoleScript"
)

type RoleCache struct {
	baseCache
	scripts map[string]*redis.Script
}

func roleKey(id int64) string {
	return roleKeyPrefix + strconv.FormatInt(id, 10)
}

func roleUsersKey(id int64) string {
	return roleUsersKeyPrefix + strconv.FormatInt(id, 10)
}

func userRolesKey(userID int64) string {
	return userRolesKeyPrefix + strconv.FormatInt(userID, 10)
}

func NewRoleCache(addr, password string) repository.RoleCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})

	cache := &RoleCache{
		baseCache: baseCache{client: client},
		scripts:   make(map[string]*redis.Script),
	}

	cache.registerScripts()
	return cache
}

func (c *RoleCache) registerScripts() {
	c.scripts[setRoleScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local key = KEYS[1]
		local set_key = KEYS[2]
		local data = ARGV[1]
		
		redis.call('SET', key, data)
		redis.call('SADD', set_key, key)
		return 1
	`, scriptVersion))

	c.scripts[setRolesScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		
		for i = 1, #KEYS - 1 do
			local ukey = KEYS[i + 1]
			local udata = ARGV[i]
			redis.call('SET', ukey, udata)
		end
		
		redis.call('SADD', set_key, unpack(KEYS, 2, #KEYS))
		return #KEYS - 1
	`, scriptVersion))

	c.scripts[deleteRolesScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		local deleted = 0
		
		for i = 2, #KEYS do
			redis.call('DEL', KEYS[i])
			deleted = deleted + redis.call('SREM', set_key, KEYS[i])
		end
		
		return deleted
	`, scriptVersion))

	c.scripts[assignUserRoleScript] = redis.NewScript(fmt.Sprintf(`
	-- Version: %s
	local user_key = KEYS[1]
	local role_key = KEYS[2]
	local role_id = ARGV[1]
	local user_id = ARGV[2]
	
	redis.call('SADD', user_key, role_id)
	redis.call('SADD', role_key, user_id)
	return 1
`, scriptVersion))

	c.scripts[removeUserRoleScript] = redis.NewScript(fmt.Sprintf(`
	-- Version: %s
	local user_key = KEYS[1]
	local role_key = KEYS[2]
	local role_id = ARGV[1]
	local user_id = ARGV[2]
	
	redis.call('SREM', user_key, role_id)
	redis.call('SREM', role_key, user_id)
	return 1
`, scriptVersion))

	c.scripts[getUserRolesScript] = redis.NewScript(fmt.Sprintf(`
	-- Version: %s
	local user_key = KEYS[1]
	local role_ids = redis.call('SMEMBERS', user_key)
	local roles = {}

	for i, role_id in ipairs(role_ids) do
		local role_data = redis.call('GET', 'role:'..role_id)
		if role_data then
			roles[#roles+1] = role_data
		end
	end

	return roles
`, scriptVersion))

	c.scripts[getProductRolesScript] = redis.NewScript(fmt.Sprintf(`
	-- Version: %s
	local product_key = KEYS[1]
	local role_ids = redis.call('SMEMBERS', product_key)
	local roles = {}

	for i, role_id in ipairs(role_ids) do
		local role_data = redis.call('GET', 'role:'..role_id)
		if role_data then
			roles[#roles+1] = role_data
		end
	end

	return roles
`, scriptVersion))
}

func (c *RoleCache) SetRole(ctx context.Context, role entity.Role) error {
	data, err := json.Marshal(role)
	if err != nil {
		return err
	}

	_, err = c.scripts[setRoleScript].Run(
		ctx,
		c.client,
		[]string{roleKey(role.ID), allRolesSet},
		data,
	).Result()

	return err
}

func (c *RoleCache) GetRole(ctx context.Context, id int64) (entity.Role, error) {
	data, err := c.client.Get(ctx, roleKey(id)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.Role{}, fmt.Errorf("role not found: %w", err)
		}
		return entity.Role{}, fmt.Errorf("failed to get role: %w", err)
	}

	var role entity.Role
	if err = json.Unmarshal(data, &role); err != nil {
		return entity.Role{}, fmt.Errorf("failed to unmarshal role: %w", err)
	}
	return role, nil
}

func (c *RoleCache) SetRoles(ctx context.Context, roles []entity.Role) error {
	if len(roles) == 0 {
		return nil
	}

	keys := make([]string, 0, len(roles)+1)
	args := make([]any, 0, len(roles))

	keys = append(keys, allRolesSet)

	for _, role := range roles {
		data, err := json.Marshal(role)
		if err != nil {
			return err
		}
		keys = append(keys, roleKey(role.ID))
		args = append(args, data)
	}

	_, err := c.scripts[setRolesScript].Run(
		ctx,
		c.client,
		keys,
		args...,
	).Result()

	return err
}

func (c *RoleCache) GetRoles(ctx context.Context) ([]entity.Role, error) {
	ids, err := c.client.SMembers(ctx, allRolesSet).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get role ids: %w", err)
	}

	keys := make([]string, len(ids))
	copy(keys, ids)

	values, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get roles: %w", err)
	}

	roles := make([]entity.Role, 0, len(values))
	for _, value := range values {
		if value == nil {
			continue
		}

		s, ok := value.(string)
		if !ok {
			continue
		}

		var role entity.Role
		if err := json.Unmarshal([]byte(s), &role); err != nil {
			continue
		}
		roles = append(roles, role)
	}

	return roles, nil
}

func (c *RoleCache) DeleteRole(ctx context.Context, id int64) error {
	return c.DeleteRoles(ctx, []int64{id})
}

func (c *RoleCache) DeleteRoles(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	keys := make([]string, len(ids)+1)
	keys[0] = allRolesSet
	for i, id := range ids {
		keys[i+1] = roleKey(id)
	}

	_, err := c.scripts[deleteRolesScript].Run(
		ctx,
		c.client,
		keys,
	).Result()

	return err
}

func (c *RoleCache) AssignUserRole(ctx context.Context, userID, roleID int64) error {
	_, err := c.scripts[assignUserRoleScript].Run(
		ctx,
		c.client,
		[]string{userRolesKey(userID), roleUsersKey(roleID)},
		roleID, userID,
	).Result()
	return err
}

func (c *RoleCache) RemoveUserRole(ctx context.Context, userID, roleID int64) error {
	_, err := c.scripts[removeUserRoleScript].Run(
		ctx,
		c.client,
		[]string{userRolesKey(userID), roleUsersKey(roleID)},
		roleID, userID,
	).Result()
	return err
}

func (c *RoleCache) GetUserRoleIDs(ctx context.Context, userID int64) ([]int64, error) {
	return c.getIntSet(ctx, userRolesKey(userID))
}

func (c *RoleCache) GetUserRoles(ctx context.Context, userID int64) ([]entity.Role, error) {
	result, err := c.scripts[getUserRolesScript].Run(
		ctx,
		c.client,
		[]string{userRolesKey(userID)},
	).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	dataSlice, ok := result.([]any)
	if !ok {
		return nil, nil
	}

	roles := make([]entity.Role, 0, len(dataSlice))
	for _, item := range dataSlice {
		if s, ok := item.(string); ok {
			var role entity.Role
			if err := json.Unmarshal([]byte(s), &role); err == nil {
				roles = append(roles, role)
			}
		}
	}

	return roles, nil
}

func (c *RoleCache) GetProductRoles(ctx context.Context, productID int64) ([]entity.Role, error) {
	result, err := c.scripts[getProductRolesScript].Run(
		ctx,
		c.client,
		[]string{productRolesKey(productID)},
	).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get product roles: %w", err)
	}

	dataSlice, ok := result.([]any)
	if !ok {
		return nil, nil
	}

	roles := make([]entity.Role, 0, len(dataSlice))
	for _, item := range dataSlice {
		if s, ok := item.(string); ok {
			var role entity.Role
			if err := json.Unmarshal([]byte(s), &role); err == nil {
				roles = append(roles, role)
			}
		}
	}

	return roles, nil
}

func (c *RoleCache) AssignProductRole(ctx context.Context, roleID, productID int64) error {
	_, err := c.scripts[assignProductRoleScript].Run(
		ctx,
		c.client,
		[]string{productRolesKey(productID), roleProductsKey(roleID)},
		roleID, productID,
	).Result()
	return err
}

func (c *RoleCache) RemoveProductRole(ctx context.Context, roleID, productID int64) error {
	_, err := c.scripts[removeProductRoleScript].Run(
		ctx,
		c.client,
		[]string{productRolesKey(productID), roleProductsKey(roleID)},
		roleID, productID,
	).Result()
	return err
}

func (c *RoleCache) SetProductRoles(ctx context.Context, productID int64, roles []entity.Role) error {
	data, err := json.Marshal(roles)
	if err != nil {
		return err
	}
	return c.client.Set(ctx, productRolesKey(productID), data, 0).Err()
}

// func (c *RoleCache) GetUserRoleIDs(ctx context.Context, userID int64) ([]int64, error) {
// 	roleIDs, err := c.client.SMembers(ctx, userRolesKey(userID)).Result()
// 	if err != nil {
// 		if err == redis.Nil {
// 			return nil, nil
// 		}
// 		return nil, fmt.Errorf("failed to get role IDs: %w", err)
// 	}
//
// 	roles := make([]int64, 0, len(roleIDs))
// 	for _, roleIDStr := range roleIDs {
// 		roleID, err := strconv.ParseInt(roleIDStr, 10, 64)
// 		if err != nil {
// 			continue
// 		}
// 		roles = append(roles, roleID)
// 	}
// 	return roles, nil
// }

func (c *RoleCache) GetRoleUsers(ctx context.Context, roleID int64) ([]int64, error) {
	return c.getIntSet(ctx, roleUsersKey(roleID))
}
