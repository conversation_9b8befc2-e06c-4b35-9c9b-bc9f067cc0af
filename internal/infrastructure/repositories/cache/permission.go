package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository"
)

const (
	permissionKeyPrefix = "permission:"
	allPermissionsSet   = "all_permissions"

	setPermissionScript  = "setPermissionScript"
	setPermissionsScript = "setPermissionsScript"
)

type PermissionCache struct {
	baseCache
	scripts map[string]*redis.Script
}

func permissionKey(id int64) string {
	return permissionKeyPrefix + strconv.FormatInt(id, 10)
}

func NewPermissionCache(addr, password string) repository.PermissionCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       0,
	})

	cache := &PermissionCache{
		baseCache: baseCache{client: client},
		scripts:   make(map[string]*redis.Script),
	}

	cache.registerScripts()
	return cache
}

func (c *PermissionCache) registerScripts() {
	c.scripts[setPermissionScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local permission_key = KEYS[1]
		local permission_data = ARGV[1]
		redis.call('SET', permission_key, permission_data)
		return 1
	`, scriptVersion))

	c.scripts[setPermissionsScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		for i = 1, #KEYS - 1 do
			local ukey = KEYS[i + 1]
			local udata = ARGV[i]
			redis.call('SET', ukey, udata)
		end
	`, scriptVersion))
}

func (c *PermissionCache) SetPermission(ctx context.Context, permission entity.Permission) error {
	data, err := json.Marshal(permission)
	if err != nil {
		return err
	}

	_, err = c.scripts[setPermissionScript].Run(
		ctx,
		c.client,
		[]string{permissionKey(permission.ID), allPermissionsSet},
		data,
	).Result()
	if err != nil {
		return err
	}

	return nil
}

func (c *PermissionCache) GetPermission(ctx context.Context, id int64) (entity.Permission, error) {
	data, err := c.client.Get(ctx, permissionKey(id)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.Permission{}, fmt.Errorf("permission not found: %w", err)
		}
		return entity.Permission{}, fmt.Errorf("failed to get permission: %w", err)
	}

	var permission entity.Permission
	if err = json.Unmarshal(data, &permission); err != nil {
		return entity.Permission{}, fmt.Errorf("failed to unmarshal permission: %w", err)
	}
	return permission, nil
}

func (c *PermissionCache) SetPermissions(ctx context.Context, permissions []entity.Permission) error {
	if len(permissions) == 0 {
		return nil
	}

	keys := make([]string, 0, len(permissions)+1)
	args := make([]any, 0, len(permissions))

	keys = append(keys, allPermissionsSet)

	for _, permission := range permissions {
		data, err := json.Marshal(permission)
		if err != nil {
			return err
		}
		keys = append(keys, permissionKey(permission.ID))
		args = append(args, data)
	}

	_, err := c.scripts[setPermissionsScript].Run(
		ctx,
		c.client,
		keys,
		args...,
	).Result()

	return err
}

func (c *PermissionCache) GetPermissions(ctx context.Context) ([]entity.Permission, error) {
	ids, err := c.client.SMembers(ctx, allPermissionsSet).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get all permissions: %w", err)
	}

	keys := make([]string, 0, len(ids))
	copy(keys, ids)

	values, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get all permissions: %w", err)
	}

	permissions := make([]entity.Permission, 0, len(values))
	for _, value := range values {
		if value == nil {
			continue
		}

		s, ok := value.(string)
		if !ok {
			continue
		}

		var permission entity.Permission
		if err := json.Unmarshal([]byte(s), &permission); err == nil {
			permissions = append(permissions, permission)
		}
	}

	return permissions, nil
}
