package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

const (
	userKeyPrefix = "user:"

	allUsersSet = "all_user_ids"

	setUser     = "setUser"
	setUsers    = "setUsers"
	deleteUsers = "deleteUsers"
)

type UserCache struct {
	baseCache
	scripts map[string]*redis.Script
}

func userKey(id int64) string {
	return userKeyPrefix + strconv.FormatInt(id, 10)
}

func NewUserCache(addr, password string) repository.UserCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})

	cache := &UserCache{
		baseCache: baseCache{client: client},
		scripts:   make(map[string]*redis.Script),
	}

	cache.registerScripts()
	return cache
}

func (c *UserCache) registerScripts() {
	c.scripts[setUser] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local key = KEYS[1]
		local set_key = KEYS[2]
		local data = ARGV[1]
		
		redis.call('SET', key, data)
		redis.call('SADD', set_key, key)
		return 1
	`, scriptVersion))

	c.scripts[setUsers] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		
		for i = 1, #KEYS - 1 do
			local ukey = KEYS[i + 1]
			local udata = ARGV[i]
			redis.call('SET', ukey, udata)
		end
		
		redis.call('SADD', set_key, unpack(KEYS, 2, #KEYS))
		return #KEYS - 1
	`, scriptVersion))

	c.scripts[deleteUsers] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		local deleted = 0
		
		for i = 2, #KEYS do
			redis.call('DEL', KEYS[i])
			deleted = deleted + redis.call('SREM', set_key, KEYS[i])
		end
		
		return deleted
	`, scriptVersion))
}

func (c *UserCache) SetUser(ctx context.Context, user entity.User) error {
	data, err := json.Marshal(user)
	if err != nil {
		return fmt.Errorf("marshal error: %w", err)
	}

	_, err = c.scripts[setUser].Run(
		ctx,
		c.client,
		[]string{userKey(user.ID), allUsersSet},
		data,
	).Result()

	return err
}

func (c *UserCache) GetUser(ctx context.Context, id int64) (entity.User, error) {
	data, err := c.client.Get(ctx, userKey(id)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.User{}, fmt.Errorf("user not found: %w", err)
		}
		return entity.User{}, fmt.Errorf("failed to get user: %w", err)
	}

	var user entity.User
	if err = json.Unmarshal(data, &user); err != nil {
		return entity.User{}, fmt.Errorf("failed to unmarshal user: %w", err)
	}
	return user, nil
}

func (c *UserCache) SetUsers(ctx context.Context, users []entity.User) error {
	if len(users) == 0 {
		return nil
	}

	keys := make([]string, 0, len(users)+1)
	args := make([]any, 0, len(users))

	keys = append(keys, allUsersSet)

	for _, u := range users {
		data, err := json.Marshal(u)
		if err != nil {
			return fmt.Errorf("marshal error for user %d: %w", u.ID, err)
		}
		keys = append(keys, userKey(u.ID))
		args = append(args, data)
	}

	_, err := c.scripts[setUsers].Run(
		ctx,
		c.client,
		keys,
		args...,
	).Result()

	return err
}

func (c *UserCache) GetUsers(ctx context.Context) ([]entity.User, error) {
	ids, err := c.client.SMembers(ctx, allUsersSet).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user ids: %w", err)
	}

	keys := make([]string, len(ids))
	copy(keys, ids)

	values, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	users := make([]entity.User, 0, len(values))
	for _, value := range values {
		if value == nil {
			continue
		}

		s, ok := value.(string)
		if !ok {
			continue
		}

		var user entity.User
		if err := json.Unmarshal([]byte(s), &user); err != nil {
			continue
		}
		users = append(users, user)
	}

	return users, nil
}

func (c *UserCache) DeleteUser(ctx context.Context, id int64) error {
	return c.DeleteUsers(ctx, []int64{id})
}

func (c *UserCache) DeleteUsers(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	keys := make([]string, len(ids)+1)
	keys[0] = allUsersSet
	for i, id := range ids {
		keys[i+1] = userKey(id)
	}

	_, err := c.scripts[deleteUsers].Run(
		ctx,
		c.client,
		keys,
	).Result()

	return err
}
