package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/redis/go-redis/v9"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
)

const (
	groupKeyPrefix         = "group:"
	groupUsersKeyPrefix    = "group_users:"
	userGroupsKeyPrefix    = "user_groups:"
	productGroupsKeyPrefix = "product_groups:"
	allGroupsSet           = "all_groups"

	setGroupScript           = "setGroupScript"
	setGroupsScript          = "setGroupsScript"
	deleteGroupsScript       = "deleteGroupsScript"
	assignUserGroupScript    = "assignUserGroupScript"
	removeUserGroupScript    = "removeUserGroupScript"
	getUserGroupsScript      = "getUserGroupsScript"
	getProductGroupsScript   = "getProductGroupsScript"
	assignProductGroupScript = "assignProductGroupScript"
	removeProductGroupScript = "removeProductGroupScript"
)

type GroupCache struct {
	baseCache
	scripts map[string]*redis.Script
}

func groupKey(id int64) string {
	return groupKeyPrefix + strconv.FormatInt(id, 10)
}

func productGroupsKey(productID int64) string {
	return productGroupsKeyPrefix + strconv.FormatInt(productID, 10)
}

func NewGroupCache(addr, password string) repository.GroupCache {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
	})

	cache := &GroupCache{
		baseCache: baseCache{client: client},
		scripts:   make(map[string]*redis.Script),
	}

	cache.registerScripts()
	return cache
}

func (c *GroupCache) registerScripts() {
	c.scripts[setGroupScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local key = KEYS[1]
		local data = ARGV[1]
		redis.call('SET', key, data)
		return 1
	`, scriptVersion))

	c.scripts[setGroupsScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		for i = 1, #KEYS - 1 do
			local ukey = KEYS[i + 1]
			local udata = ARGV[i]
			redis.call('SET', ukey, udata)
		end
	`, scriptVersion))

	c.scripts[deleteGroupsScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local set_key = KEYS[1]
		redis.call('DEL', set_key)
		return 1
	`, scriptVersion))

	c.scripts[assignUserGroupScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local user_key = KEYS[1]
		local group_key = KEYS[2]
		local user_id = ARGV[1]
		local group_id = ARGV[2]
		redis.call('SADD', user_key, group_id)
		redis.call('SADD', group_key, user_id)
		return 1
	`, scriptVersion))

	c.scripts[removeUserGroupScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local user_key = KEYS[1]
		local group_key = KEYS[2]
		local user_id = ARGV[1]
		local group_id = ARGV[2]
		redis.call('SREM', user_key, group_id)
		redis.call('SREM', group_key, user_id)
		return 1
	`, scriptVersion))

	c.scripts[getUserGroupsScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local user_key = KEYS[1]
		local group_ids = redis.call('SMEMBERS', user_key)
		local groups = {}

		for i, group_id in ipairs(group_ids) do
			local group_data = redis.call('GET', 'group:'..group_id)
			if group_data then
				groups[#groups+1] = group_data
			end
		end

		return groups
	`, scriptVersion))

	c.scripts[getProductGroupsScript] = redis.NewScript(fmt.Sprintf(`
		-- Version: %s
		local product_key = KEYS[1]
		local group_ids = redis.call('SMEMBERS', product_key)
		local groups = {}

		for i, group_id in ipairs(group_ids) do
			local group_data = redis.call('GET', 'group:'..group_id)
			if group_data then
				groups[#groups+1] = group_data
			end
		end

		return groups
	`, scriptVersion))
}

func (c *GroupCache) SetGroup(ctx context.Context, group entity.Group) error {
	data, err := json.Marshal(group)
	if err != nil {
		return err
	}

	_, err = c.scripts[setGroupScript].Run(
		ctx,
		c.client,
		[]string{groupKey(group.ID), allGroupsSet},
		data,
	).Result()
	if err != nil {
		return err
	}

	return nil
}

// GetGroup returns a group from the cache.
func (c *GroupCache) GetGroup(ctx context.Context, id int64) (entity.Group, error) {
	data, err := c.client.Get(ctx, groupKey(id)).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.Group{}, fmt.Errorf("group not found: %w", err)
		}
		return entity.Group{}, fmt.Errorf("failed to get group: %w", err)
	}

	var group entity.Group
	if err = json.Unmarshal(data, &group); err != nil {
		return entity.Group{}, fmt.Errorf("failed to unmarshal group: %w", err)
	}
	return group, nil
}

func (c *GroupCache) SetGroups(ctx context.Context, groups []entity.Group) error {
	if len(groups) == 0 {
		return nil
	}

	keys := make([]string, 0, len(groups)+1)
	args := make([]any, 0, len(groups))

	keys = append(keys, allGroupsSet)

	for _, group := range groups {
		data, err := json.Marshal(group)
		if err != nil {
			return err
		}
		keys = append(keys, groupKey(group.ID))
		args = append(args, data)
	}

	_, err := c.scripts[setGroupsScript].Run(
		ctx,
		c.client,
		keys,
		args...,
	).Result()

	return err
}

// GetGroups returns a list of groups from the cache.
func (c *GroupCache) GetGroups(ctx context.Context) ([]entity.Group, error) {
	ids, err := c.client.SMembers(ctx, allGroupsSet).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get group ids: %w", err)
	}

	keys := make([]string, len(ids))
	copy(keys, ids)

	values, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get groups: %w", err)
	}

	groups := make([]entity.Group, 0, len(values))
	for _, value := range values {
		if value == nil {
			continue
		}

		s, ok := value.(string)
		if !ok {
			continue
		}

		var group entity.Group
		if err := json.Unmarshal([]byte(s), &group); err != nil {
			continue
		}
		groups = append(groups, group)
	}

	return groups, nil
}

func (c *GroupCache) DeleteGroup(ctx context.Context, id int64) error {
	return c.DeleteGroups(ctx, []int64{id})
}

func (c *GroupCache) DeleteGroups(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	keys := make([]string, len(ids)+1)
	keys[0] = allGroupsSet
	for i, id := range ids {
		keys[i+1] = groupKey(id)
	}

	_, err := c.scripts[deleteGroupsScript].Run(
		ctx,
		c.client,
		keys,
	).Result()

	return err
}

func (c *GroupCache) SetProductGroups(ctx context.Context, productID int64, groups []entity.Group) error {
	data, err := json.Marshal(groups)
	if err != nil {
		return err
	}

	return c.client.Set(ctx, productGroupsKey(productID), data, 0).Err()
}

func (c *GroupCache) GetProductGroups(ctx context.Context, productID int64) ([]entity.Group, error) {
	result, err := c.scripts[getProductGroupsScript].Run(
		ctx,
		c.client,
		[]string{productGroupsKey(productID)},
	).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get product groups: %w", err)
	}

	dataSlice, ok := result.([]any)
	if !ok {
		return nil, nil
	}

	groups := make([]entity.Group, 0, len(dataSlice))
	for _, item := range dataSlice {
		if s, ok := item.(string); ok {
			var group entity.Group
			if err := json.Unmarshal([]byte(s), &group); err == nil {
				groups = append(groups, group)
			}
		}
	}

	return groups, nil
}
