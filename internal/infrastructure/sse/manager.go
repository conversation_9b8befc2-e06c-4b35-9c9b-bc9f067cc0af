package sse

import (
	"fmt"
	"net/http"
	"sync"
)

type SSEEvent struct {
	Type string `json:"type"`
	Data string `json:"data"`
	ID   string `json:"id"`
}

type SSEManager struct {
	connections map[string][]http.ResponseWriter
	mutex       sync.RWMutex
}

func NewSSEManager() *SSEManager {
	return &SSEManager{
		connections: make(map[string][]http.ResponseWriter),
	}
}

func (m *SSEManager) AddConnection(userID string, w http.ResponseWriter) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.connections[userID] = append(m.connections[userID], w)
}

func (m *SSEManager) RemoveConnection(userID string, w http.ResponseWriter) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	connections := m.connections[userID]
	for i, conn := range connections {
		if conn == w {
			m.connections[userID] = append(connections[:i], connections[i+1:]...)
			break
		}
	}

	if len(m.connections[userID]) == 0 {
		delete(m.connections, userID)
	}
}

func (m *SSEManager) SendToUser(userID string, event SSEEvent) bool {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	connections, exists := m.connections[userID]
	if !exists || len(connections) == 0 {
		return false
	}

	var validConnections []http.ResponseWriter
	eventSent := false

	for _, w := range connections {
		// Проверяем, можем ли мы записать в соединение
		if m.isConnectionAlive(w) {
			_, err := fmt.Fprintf(w, "event: %s\ndata: %s\nid: %s\n\n",
				event.Type, event.Data, event.ID)
			if err == nil {
				if flusher, ok := w.(http.Flusher); ok {
					flusher.Flush()
				}
				validConnections = append(validConnections, w)
				eventSent = true
			}
		}
	}

	// Обновляем список соединений, удаляя мертвые
	if len(validConnections) != len(connections) {
		if len(validConnections) == 0 {
			delete(m.connections, userID)
		} else {
			m.connections[userID] = validConnections
		}
	}

	return eventSent
}

// isConnectionAlive проверяет, живо ли соединение
func (m *SSEManager) isConnectionAlive(w http.ResponseWriter) bool {
	// Пытаемся записать пустой комментарий для проверки соединения
	_, err := fmt.Fprintf(w, ": heartbeat\n")
	return err == nil
}

func (m *SSEManager) GetConnectionCount(userID string) int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.connections[userID])
}
