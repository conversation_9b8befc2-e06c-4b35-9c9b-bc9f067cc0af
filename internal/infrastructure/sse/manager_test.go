package sse

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"

	"github.com/stretchr/testify/require"
)

// mockResponseWriter implements http.ResponseWriter for testing error scenarios
type mockResponseWriter struct {
	writeError    bool
	heartbeatFail bool
}

func (m *mockResponseWriter) Header() http.Header {
	return make(http.Header)
}

func (m *mockResponseWriter) Write(data []byte) (int, error) {
	dataStr := string(data)

	// Check if this is a heartbeat message
	if dataStr == ": heartbeat\n" && m.heartbeatFail {
		return 0, fmt.Errorf("heartbeat error")
	}

	// For all other writes, check writeError flag
	if m.writeError && dataStr != ": heartbeat\n" {
		return 0, fmt.Errorf("write error")
	}
	return len(data), nil
}

func (m *mockResponseWriter) WriteHeader(statusCode int) {}

func (m *mockResponseWriter) Flush() {
	// Implement Flusher interface if needed
}

// TestNewSSEManager tests the constructor
func TestNewSSEManager_Success(t *testing.T) {
	manager := NewSSEManager()

	require.NotNil(t, manager)
	require.NotNil(t, manager.connections)
	require.Equal(t, 0, len(manager.connections))
}

// TestSSEManager_AddConnection tests adding connections
func TestSSEManager_AddConnection_Success(t *testing.T) {
	manager := NewSSEManager()
	userID := "user123"

	t.Run("add single connection", func(t *testing.T) {
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)

		require.Equal(t, 1, manager.GetConnectionCount(userID))
		require.Contains(t, manager.connections, userID)
		require.Len(t, manager.connections[userID], 1)
		require.Equal(t, w, manager.connections[userID][0])
	})

	t.Run("add multiple connections for same user", func(t *testing.T) {
		w2 := httptest.NewRecorder()
		w3 := httptest.NewRecorder()

		manager.AddConnection(userID, w2)
		manager.AddConnection(userID, w3)

		require.Equal(t, 3, manager.GetConnectionCount(userID))
		require.Len(t, manager.connections[userID], 3)
	})

	t.Run("add connections for different users", func(t *testing.T) {
		userID2 := "user456"
		w4 := httptest.NewRecorder()

		manager.AddConnection(userID2, w4)

		require.Equal(t, 3, manager.GetConnectionCount(userID))
		require.Equal(t, 1, manager.GetConnectionCount(userID2))
		require.Len(t, manager.connections, 2)
	})
}

// TestSSEManager_AddConnection_EdgeCases tests edge cases for adding connections
func TestSSEManager_AddConnection_EdgeCases(t *testing.T) {
	manager := NewSSEManager()

	t.Run("add connection with empty userID", func(t *testing.T) {
		w := httptest.NewRecorder()
		manager.AddConnection("", w)

		require.Equal(t, 1, manager.GetConnectionCount(""))
		require.Contains(t, manager.connections, "")
	})

	t.Run("add nil ResponseWriter", func(t *testing.T) {
		userID := "user123"
		manager.AddConnection(userID, nil)

		require.Equal(t, 1, manager.GetConnectionCount(userID))
		require.Contains(t, manager.connections, userID)
		require.Nil(t, manager.connections[userID][0])
	})
}

// TestSSEManager_RemoveConnection tests removing connections
func TestSSEManager_RemoveConnection_Success(t *testing.T) {
	t.Run("remove existing connection", func(t *testing.T) {
		manager := NewSSEManager()
		userID := "user123"
		w1 := httptest.NewRecorder()
		w2 := httptest.NewRecorder()

		manager.AddConnection(userID, w1)
		manager.AddConnection(userID, w2)
		require.Equal(t, 2, manager.GetConnectionCount(userID))

		manager.RemoveConnection(userID, w1)

		require.Equal(t, 1, manager.GetConnectionCount(userID))
		require.Len(t, manager.connections[userID], 1)
		require.Equal(t, w2, manager.connections[userID][0])
	})

	t.Run("remove last connection removes user entry", func(t *testing.T) {
		manager := NewSSEManager()
		userID := "user456" // Different userID to avoid interference
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)

		manager.RemoveConnection(userID, w)

		require.Equal(t, 0, manager.GetConnectionCount(userID))
		require.NotContains(t, manager.connections, userID)
	})
}

// TestSSEManager_RemoveConnection_EdgeCases tests edge cases for removing connections
func TestSSEManager_RemoveConnection_EdgeCases(t *testing.T) {
	manager := NewSSEManager()
	userID := "user123"

	t.Run("remove non-existing connection", func(t *testing.T) {
		w1 := httptest.NewRecorder()
		w2 := httptest.NewRecorder()

		manager.AddConnection(userID, w1)
		initialCount := manager.GetConnectionCount(userID)

		// Try to remove connection that was never added
		manager.RemoveConnection(userID, w2)

		require.Equal(t, initialCount, manager.GetConnectionCount(userID))
	})

	t.Run("remove connection from non-existing user", func(t *testing.T) {
		w := httptest.NewRecorder()

		// Should not panic
		manager.RemoveConnection("nonexistent", w)

		require.Equal(t, 0, manager.GetConnectionCount("nonexistent"))
	})

	t.Run("remove nil connection", func(t *testing.T) {
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)
		manager.AddConnection(userID, nil)

		initialCount := manager.GetConnectionCount(userID)

		manager.RemoveConnection(userID, nil)

		require.Equal(t, initialCount-1, manager.GetConnectionCount(userID))
	})
}

// TestSSEManager_SendToUser tests sending events to users
func TestSSEManager_SendToUser_Success(t *testing.T) {
	manager := NewSSEManager()
	userID := "user123"

	t.Run("send event to existing user", func(t *testing.T) {
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)

		event := SSEEvent{
			Type: "test",
			Data: "test data",
			ID:   "123",
		}

		result := manager.SendToUser(userID, event)

		require.True(t, result)
		require.Contains(t, w.Body.String(), "event: test")
		require.Contains(t, w.Body.String(), "data: test data")
		require.Contains(t, w.Body.String(), "id: 123")
	})

	t.Run("send event to user with multiple connections", func(t *testing.T) {
		w1 := httptest.NewRecorder()
		w2 := httptest.NewRecorder()

		manager.AddConnection(userID, w1)
		manager.AddConnection(userID, w2)

		event := SSEEvent{
			Type: "broadcast",
			Data: "broadcast data",
			ID:   "456",
		}

		result := manager.SendToUser(userID, event)

		require.True(t, result)
		require.Contains(t, w1.Body.String(), "event: broadcast")
		require.Contains(t, w2.Body.String(), "event: broadcast")
	})
}

// TestSSEManager_SendToUser_EdgeCases tests edge cases for sending events
func TestSSEManager_SendToUser_EdgeCases(t *testing.T) {
	manager := NewSSEManager()

	t.Run("send to non-existing user", func(t *testing.T) {
		event := SSEEvent{Type: "test", Data: "data", ID: "1"}

		result := manager.SendToUser("nonexistent", event)

		require.False(t, result)
	})

	t.Run("send to user with no connections", func(t *testing.T) {
		userID := "user123"
		// Add and then remove connection to create empty slice
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)
		manager.RemoveConnection(userID, w)

		event := SSEEvent{Type: "test", Data: "data", ID: "1"}

		result := manager.SendToUser(userID, event)

		require.False(t, result)
	})

	t.Run("send empty event", func(t *testing.T) {
		userID := "user123"
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)

		event := SSEEvent{} // Empty event

		result := manager.SendToUser(userID, event)

		require.True(t, result)
		require.Contains(t, w.Body.String(), "event: ")
		require.Contains(t, w.Body.String(), "data: ")
		require.Contains(t, w.Body.String(), "id: ")
	})
}

// TestSSEManager_SendToUser_ErrorCases tests error scenarios
func TestSSEManager_SendToUser_ErrorCases(t *testing.T) {
	t.Run("send to connection with write error", func(t *testing.T) {
		manager := NewSSEManager()
		userID := "user123"

		// Add a good connection and a bad one
		goodWriter := httptest.NewRecorder()
		badWriter := &mockResponseWriter{writeError: true}

		manager.AddConnection(userID, goodWriter)
		manager.AddConnection(userID, badWriter)

		event := SSEEvent{Type: "test", Data: "data", ID: "1"}

		result := manager.SendToUser(userID, event)

		// Should return true because at least one connection succeeded
		require.True(t, result)
		require.Contains(t, goodWriter.Body.String(), "event: test")

		// Bad connection should be removed
		require.Equal(t, 1, manager.GetConnectionCount(userID))
	})

	t.Run("send to all bad connections removes user", func(t *testing.T) {
		manager := NewSSEManager()
		userID := "user456" // Different userID to avoid interference

		// Create connections that pass isConnectionAlive but fail on actual write
		badWriter1 := &mockResponseWriter{writeError: true, heartbeatFail: false}
		badWriter2 := &mockResponseWriter{writeError: true, heartbeatFail: false}

		manager.AddConnection(userID, badWriter1)
		manager.AddConnection(userID, badWriter2)

		event := SSEEvent{Type: "test", Data: "data", ID: "1"}

		result := manager.SendToUser(userID, event)

		require.False(t, result)
		require.Equal(t, 0, manager.GetConnectionCount(userID))
		require.NotContains(t, manager.connections, userID)
	})
}

// TestSSEManager_GetConnectionCount tests connection counting
func TestSSEManager_GetConnectionCount_Success(t *testing.T) {
	manager := NewSSEManager()
	userID := "user123"

	t.Run("count for non-existing user", func(t *testing.T) {
		count := manager.GetConnectionCount("nonexistent")
		require.Equal(t, 0, count)
	})

	t.Run("count after adding connections", func(t *testing.T) {
		require.Equal(t, 0, manager.GetConnectionCount(userID))

		w1 := httptest.NewRecorder()
		manager.AddConnection(userID, w1)
		require.Equal(t, 1, manager.GetConnectionCount(userID))

		w2 := httptest.NewRecorder()
		manager.AddConnection(userID, w2)
		require.Equal(t, 2, manager.GetConnectionCount(userID))
	})
}

// TestSSEManager_isConnectionAlive tests connection liveness check
func TestSSEManager_isConnectionAlive_Success(t *testing.T) {
	manager := NewSSEManager()

	t.Run("alive connection", func(t *testing.T) {
		w := httptest.NewRecorder()
		alive := manager.isConnectionAlive(w)

		require.True(t, alive)
		require.Contains(t, w.Body.String(), ": heartbeat")
	})

	t.Run("dead connection", func(t *testing.T) {
		badWriter := &mockResponseWriter{heartbeatFail: true}
		alive := manager.isConnectionAlive(badWriter)

		require.False(t, alive)
	})
}

// TestSSEManager_ConcurrentAccess tests concurrent operations
func TestSSEManager_ConcurrentAccess_Success(t *testing.T) {
	manager := NewSSEManager()

	t.Run("concurrent add and remove connections", func(t *testing.T) {
		const numGoroutines = 50
		const operationsPerGoroutine = 10

		var wg sync.WaitGroup

		// Launch goroutines that add and remove connections concurrently
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				userID := fmt.Sprintf("user%d", goroutineID)
				connections := make([]http.ResponseWriter, operationsPerGoroutine)

				// Add connections
				for j := 0; j < operationsPerGoroutine; j++ {
					w := httptest.NewRecorder()
					connections[j] = w
					manager.AddConnection(userID, w)
				}

				// Remove connections
				for j := 0; j < operationsPerGoroutine; j++ {
					manager.RemoveConnection(userID, connections[j])
				}
			}(i)
		}

		wg.Wait()

		// All connections should be removed
		for i := 0; i < numGoroutines; i++ {
			userID := fmt.Sprintf("user%d", i)
			require.Equal(t, 0, manager.GetConnectionCount(userID))
		}
	})

	t.Run("concurrent send to users", func(t *testing.T) {
		const numUsers = 10
		const numSenders = 20

		// Setup users with connections
		for i := 0; i < numUsers; i++ {
			userID := fmt.Sprintf("user%d", i)
			w := httptest.NewRecorder()
			manager.AddConnection(userID, w)
		}

		var wg sync.WaitGroup
		successCount := make(chan bool, numSenders*numUsers)

		// Launch goroutines that send events concurrently
		for i := 0; i < numSenders; i++ {
			wg.Add(1)
			go func(senderID int) {
				defer wg.Done()

				for j := 0; j < numUsers; j++ {
					userID := fmt.Sprintf("user%d", j)
					event := SSEEvent{
						Type: "concurrent",
						Data: fmt.Sprintf("sender%d", senderID),
						ID:   fmt.Sprintf("%d-%d", senderID, j),
					}

					result := manager.SendToUser(userID, event)
					successCount <- result
				}
			}(i)
		}

		wg.Wait()
		close(successCount)

		// Count successful sends
		successful := 0
		for result := range successCount {
			if result {
				successful++
			}
		}

		// All sends should be successful
		require.Equal(t, numSenders*numUsers, successful)
	})

	t.Run("concurrent read operations", func(t *testing.T) {
		const numReaders = 100
		userID := "testuser"

		// Setup a user with connection
		w := httptest.NewRecorder()
		manager.AddConnection(userID, w)

		var wg sync.WaitGroup
		counts := make(chan int, numReaders)

		// Launch goroutines that read connection count concurrently
		for i := 0; i < numReaders; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				count := manager.GetConnectionCount(userID)
				counts <- count
			}()
		}

		wg.Wait()
		close(counts)

		// All reads should return the same count
		for count := range counts {
			require.Equal(t, 1, count)
		}
	})
}

// TestSSEManager_ComplexScenarios tests complex real-world scenarios
func TestSSEManager_ComplexScenarios_Success(t *testing.T) {
	manager := NewSSEManager()

	t.Run("user with multiple connections receives events", func(t *testing.T) {
		userID := "multiuser"
		connections := make([]*httptest.ResponseRecorder, 3)

		// Add multiple connections for the same user
		for i := 0; i < 3; i++ {
			connections[i] = httptest.NewRecorder()
			manager.AddConnection(userID, connections[i])
		}

		// Send event to user
		event := SSEEvent{
			Type: "notification",
			Data: "Important message",
			ID:   "notif-1",
		}

		result := manager.SendToUser(userID, event)

		require.True(t, result)
		require.Equal(t, 3, manager.GetConnectionCount(userID))

		// All connections should receive the event
		for i, conn := range connections {
			body := conn.Body.String()
			require.Contains(t, body, "event: notification", "Connection %d should receive event", i)
			require.Contains(t, body, "data: Important message", "Connection %d should receive data", i)
			require.Contains(t, body, "id: notif-1", "Connection %d should receive ID", i)
		}
	})

	t.Run("mixed healthy and dead connections", func(t *testing.T) {
		userID := "mixeduser"

		// Add healthy connections
		goodConn1 := httptest.NewRecorder()
		goodConn2 := httptest.NewRecorder()
		manager.AddConnection(userID, goodConn1)
		manager.AddConnection(userID, goodConn2)

		// Add dead connections
		badConn1 := &mockResponseWriter{heartbeatFail: true}
		badConn2 := &mockResponseWriter{heartbeatFail: true}
		manager.AddConnection(userID, badConn1)
		manager.AddConnection(userID, badConn2)

		require.Equal(t, 4, manager.GetConnectionCount(userID))

		// Send event
		event := SSEEvent{
			Type: "cleanup",
			Data: "test cleanup",
			ID:   "cleanup-1",
		}

		result := manager.SendToUser(userID, event)

		// Should succeed because some connections are healthy
		require.True(t, result)

		// Dead connections should be removed
		require.Equal(t, 2, manager.GetConnectionCount(userID))

		// Good connections should receive the event
		require.Contains(t, goodConn1.Body.String(), "event: cleanup")
		require.Contains(t, goodConn2.Body.String(), "event: cleanup")
	})

	t.Run("all connections die during send", func(t *testing.T) {
		userID := "deaduser"

		// Add only dead connections
		badConn1 := &mockResponseWriter{heartbeatFail: true}
		badConn2 := &mockResponseWriter{heartbeatFail: true}
		manager.AddConnection(userID, badConn1)
		manager.AddConnection(userID, badConn2)

		require.Equal(t, 2, manager.GetConnectionCount(userID))

		// Send event
		event := SSEEvent{
			Type: "test",
			Data: "test data",
			ID:   "test-1",
		}

		result := manager.SendToUser(userID, event)

		// Should fail because no connections are healthy
		require.False(t, result)

		// User should be completely removed
		require.Equal(t, 0, manager.GetConnectionCount(userID))
		require.NotContains(t, manager.connections, userID)
	})
}
