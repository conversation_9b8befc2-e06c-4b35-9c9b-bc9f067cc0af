package authorization

import (
	"errors"
	"testing"

	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

type mockPermissionService struct {
	permissions             []permissionentity.Permission
	getByNameAndMethodError error
	getAllError             error
}

func (m *mockPermissionService) GetAll() ([]permissionentity.Permission, error) {
	if m.getAllError != nil {
		return nil, m.getAllError
	}
	return m.permissions, nil
}

func (m *mockPermissionService) GetByNameAndMethod(name, method string) (permissionentity.Permission, error) {
	if m.getByNameAndMethodError != nil {
		return permissionentity.Permission{}, m.getByNameAndMethodError
	}

	for _, perm := range m.permissions {
		if perm.Name == name && perm.Method == method {
			return perm, nil
		}
	}

	return permissionentity.Permission{}, errors.New("permission not found")
}

func TestNewServiceValidator(t *testing.T) {
	mockService := &mockPermissionService{}
	validator := NewServiceValidator(mockService)

	if validator == nil {
		t.Fatal("Expected validator to be created, got nil")
	}

	if validator.permissionAppService != mockService {
		t.Error("Expected permissionAppService to be set correctly")
	}
}

func TestServiceValidator_GetAllPermissions(t *testing.T) {
	tests := []struct {
		name          string
		permissions   []permissionentity.Permission
		getAllError   error
		expectedCount int
		expectError   bool
	}{
		{
			name: "Success - multiple permissions",
			permissions: []permissionentity.Permission{
				{Name: "user", Method: "create"},
				{Name: "user", Method: "view"},
				{Name: "product", Method: "delete"},
			},
			expectedCount: 3,
			expectError:   false,
		},
		{
			name:          "Success - empty permissions",
			permissions:   []permissionentity.Permission{},
			expectedCount: 0,
			expectError:   false,
		},
		{
			name:        "Error - service failure",
			getAllError: errors.New("database error"),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &mockPermissionService{
				permissions: tt.permissions,
				getAllError: tt.getAllError,
			}
			validator := NewServiceValidator(mockService)

			result, err := validator.GetAllPermissions()

			if tt.expectError {
				if err == nil {
					t.Fatal("Expected error, got nil")
				}
				if result != nil {
					t.Error("Expected nil result on error")
				}
				return
			}

			if err != nil {
				t.Fatalf("Unexpected error: %v", err)
			}

			if len(result) != tt.expectedCount {
				t.Errorf("Expected %d permissions, got %d", tt.expectedCount, len(result))
			}

			for i, perm := range result {
				expected := tt.permissions[i]
				if perm.Name != expected.Name || perm.Method != expected.Method {
					t.Errorf("Permission %d: expected {%s, %s}, got {%s, %s}",
						i, expected.Name, expected.Method, perm.Name, perm.Method)
				}
			}
		})
	}
}

func TestServiceValidator_ValidatePermission(t *testing.T) {
	tests := []struct {
		name                    string
		permissions             []permissionentity.Permission
		getByNameAndMethodError error
		validateName            string
		validateMethod          string
		expectError             bool
		expectedErrorPattern    string
	}{
		{
			name: "Success - permission exists",
			permissions: []permissionentity.Permission{
				{Name: "user", Method: "create"},
			},
			validateName:   "user",
			validateMethod: "create",
			expectError:    false,
		},
		{
			name:                    "Error - permission not found",
			permissions:             []permissionentity.Permission{},
			validateName:            "user",
			validateMethod:          "create",
			getByNameAndMethodError: errors.New("not found"),
			expectError:             true,
			expectedErrorPattern:    "permission not found: user:create",
		},
		{
			name:                    "Error - service error",
			permissions:             []permissionentity.Permission{},
			validateName:            "user",
			validateMethod:          "create",
			getByNameAndMethodError: errors.New("database connection failed"),
			expectError:             true,
			expectedErrorPattern:    "permission not found: user:create",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &mockPermissionService{
				permissions:             tt.permissions,
				getByNameAndMethodError: tt.getByNameAndMethodError,
			}
			validator := NewServiceValidator(mockService)

			err := validator.ValidatePermission(tt.validateName, tt.validateMethod)

			if tt.expectError {
				if err == nil {
					t.Fatal("Expected error, got nil")
				}
				if tt.expectedErrorPattern != "" && !contains(err.Error(), tt.expectedErrorPattern) {
					t.Errorf("Expected error to contain '%s', got: %s", tt.expectedErrorPattern, err.Error())
				}
			} else {
				if err != nil {
					t.Fatalf("Unexpected error: %v", err)
				}
			}
		})
	}
}

func TestConfig_validateAgainstDB(t *testing.T) {
	tests := []struct {
		name           string
		config         *Config
		permissions    []permissionentity.Permission
		getAllError    error
		expectedValid  bool
		expectedErrors int
	}{
		{
			name: "Success - all permissions exist",
			config: &Config{
				ProtectedPaths: []string{"/v1/users"},
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET":  {Permission: "user", Method: "view"},
						"POST": {Permission: "user", Method: "create"},
					},
				},
			},
			permissions: []permissionentity.Permission{
				{Name: "user", Method: "view"},
				{Name: "user", Method: "create"},
			},
			expectedValid:  true,
			expectedErrors: 0,
		},
		{
			name: "Error - validator not set",
			config: &Config{
				ProtectedPaths: []string{"/v1/users"},
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET": {Permission: "user", Method: "view"},
					},
				},
			},
			expectedValid:  false,
			expectedErrors: 1,
		},
		{
			name: "Error - database fetch failure",
			config: &Config{
				ProtectedPaths: []string{"/v1/users"},
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET": {Permission: "user", Method: "view"},
					},
				},
			},
			getAllError:    errors.New("database error"),
			expectedValid:  false,
			expectedErrors: 1,
		},
		{
			name: "Error - permission not in database",
			config: &Config{
				ProtectedPaths: []string{"/v1/users"},
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET":  {Permission: "user", Method: "view"},
						"POST": {Permission: "admin", Method: "create"},
					},
				},
			},
			permissions: []permissionentity.Permission{
				{Name: "user", Method: "view"},
			},
			expectedValid:  false,
			expectedErrors: 1,
		},
		{
			name: "Error - invalid base config",
			config: &Config{
				ProtectedPaths:      []string{""},
				EndpointPermissions: map[string]map[string]PermissionRule{},
			},
			expectedValid:  false,
			expectedErrors: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.permissions != nil || tt.getAllError != nil {
				mockService := &mockPermissionService{
					permissions: tt.permissions,
					getAllError: tt.getAllError,
				}
				validator := NewServiceValidator(mockService)
				tt.config.SetValidator(validator)
			}

			result := tt.config.validateAgainstDB()

			if result.Valid != tt.expectedValid {
				t.Errorf("Expected valid=%v, got valid=%v", tt.expectedValid, result.Valid)
			}

			if len(result.Errors) != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d errors: %v", tt.expectedErrors, len(result.Errors), result.Errors)
			}
		})
	}
}

func TestConfig_validateDetailed(t *testing.T) {
	tests := []struct {
		name           string
		config         *Config
		expectedValid  bool
		expectedErrors int
	}{
		{
			name: "Success - valid config",
			config: &Config{
				ProtectedPaths: []string{"/v1/users"},
				ExcludedPaths: map[string][]string{
					"/v1/health": {"GET"},
				},
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET": {Permission: "user", Method: "view"},
					},
				},
			},
			expectedValid:  true,
			expectedErrors: 0,
		},
		{
			name: "Error - multiple validation errors",
			config: &Config{
				ProtectedPaths: []string{""},
				ExcludedPaths: map[string][]string{
					"": {"GET"},
				},
				EndpointPermissions: map[string]map[string]PermissionRule{
					"": {
						"INVALID": {Permission: "", Method: ""},
					},
				},
			},
			expectedValid:  false,
			expectedErrors: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.validateDetailed()

			if result.Valid != tt.expectedValid {
				t.Errorf("Expected valid=%v, got valid=%v", tt.expectedValid, result.Valid)
			}

			if len(result.Errors) != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d errors: %v", tt.expectedErrors, len(result.Errors), result.Errors)
			}
		})
	}
}

func TestConfig_validateEndpointPermissions(t *testing.T) {
	tests := []struct {
		name           string
		config         *Config
		expectedErrors int
	}{
		{
			name: "Success - valid endpoint permissions",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET":  {Permission: "user", Method: "view"},
						"POST": {Permission: "user", Method: "create"},
					},
					"/v1/products": {
						"DELETE": {Permission: "product", Method: "delete"},
					},
				},
			},
			expectedErrors: 0,
		},
		{
			name: "Error - empty path",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"": {
						"GET": {Permission: "user", Method: "view"},
					},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - invalid path format",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/api/users": {
						"GET": {Permission: "user", Method: "view"},
					},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - invalid HTTP method",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"INVALID": {Permission: "user", Method: "view"},
					},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - empty permission",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET": {Permission: "", Method: "view"},
					},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - empty method",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET": {Permission: "user", Method: ""},
					},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - invalid permission method",
			config: &Config{
				EndpointPermissions: map[string]map[string]PermissionRule{
					"/v1/users": {
						"GET": {Permission: "user", Method: "invalid"},
					},
				},
			},
			expectedErrors: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := tt.config.validateEndpointPermissions()

			if len(errors) != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d errors: %v", tt.expectedErrors, len(errors), errors)
			}
		})
	}
}

func TestConfig_validateExcludedPaths(t *testing.T) {
	tests := []struct {
		name           string
		config         *Config
		expectedErrors int
	}{
		{
			name: "Success - valid excluded paths",
			config: &Config{
				ExcludedPaths: map[string][]string{
					"/v1/health": {"GET"},
					"/v1/status": {"GET", "POST"},
				},
			},
			expectedErrors: 0,
		},
		{
			name: "Error - empty path",
			config: &Config{
				ExcludedPaths: map[string][]string{
					"": {"GET"},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - invalid path format",
			config: &Config{
				ExcludedPaths: map[string][]string{
					"/api/health": {"GET"},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - invalid HTTP method",
			config: &Config{
				ExcludedPaths: map[string][]string{
					"/v1/health": {"INVALID"},
				},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - multiple errors",
			config: &Config{
				ExcludedPaths: map[string][]string{
					"":           {"GET"},
					"/api/users": {"INVALID"},
				},
			},
			expectedErrors: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := tt.config.validateExcludedPaths()

			if len(errors) != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d errors: %v", tt.expectedErrors, len(errors), errors)
			}
		})
	}
}

func TestConfig_validateProtectedPaths(t *testing.T) {
	tests := []struct {
		name           string
		config         *Config
		expectedErrors int
	}{
		{
			name: "Success - valid protected paths",
			config: &Config{
				ProtectedPaths: []string{
					"/v1/users",
					"/v1/products",
					"/v1/admin/settings",
				},
			},
			expectedErrors: 0,
		},
		{
			name: "Error - empty path",
			config: &Config{
				ProtectedPaths: []string{""},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - invalid path format",
			config: &Config{
				ProtectedPaths: []string{"/api/users"},
			},
			expectedErrors: 1,
		},
		{
			name: "Error - multiple errors",
			config: &Config{
				ProtectedPaths: []string{
					"",
					"/api/users",
					"/invalid",
				},
			},
			expectedErrors: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := tt.config.validateProtectedPaths()

			if len(errors) != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d errors: %v", tt.expectedErrors, len(errors), errors)
			}
		})
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsMiddle(s, substr))))
}

func containsMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
