package authorization

import (
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
)

func TestValidationError_Error(t *testing.T) {
	err := ValidationError{
		Field:   "test_field",
		Value:   "test_value",
		Message: "test message",
	}

	expected := "validation error in test_field='test_value': test message"
	assert.Equal(t, expected, err.Error())
}

func TestValidationResult_Error(t *testing.T) {
	tests := []struct {
		name     string
		result   ValidationResult
		expected string
	}{
		{
			name: "valid result",
			result: ValidationResult{
				Valid:  true,
				Errors: nil,
			},
			expected: "",
		},
		{
			name: "invalid result with single error",
			result: ValidationResult{
				Valid: false,
				Errors: []ValidationError{
					{Field: "path", Value: "/invalid", Message: "invalid format"},
				},
			},
			expected: "validation failed:\n  - validation error in path='/invalid': invalid format\n",
		},
		{
			name: "invalid result with multiple errors",
			result: ValidationResult{
				Valid: false,
				Errors: []ValidationError{
					{Field: "path", Value: "/invalid", Message: "invalid format"},
					{Field: "method", Value: "INVALID", Message: "unsupported method"},
				},
			},
			expected: "validation failed:\n  - validation error in path='/invalid': invalid format\n  - validation error in method='INVALID': unsupported method\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.result.Error())
		})
	}
}

func TestConfig_GetPermissionRule(t *testing.T) {
	config := &Config{
		EndpointPermissions: map[string]map[string]PermissionRule{
			"/v1/users": {
				"GET":  {Permission: "user", Method: "view"},
				"POST": {Permission: "user", Method: "create"},
			},
			"/v1/products": {
				"GET": {Permission: "product", Method: "view"},
			},
		},
	}

	tests := []struct {
		name         string
		path         string
		method       string
		expectedRule PermissionRule
		exists       bool
	}{
		{
			name:         "existing rule",
			path:         "/v1/users",
			method:       "GET",
			expectedRule: PermissionRule{Permission: "user", Method: "view"},
			exists:       true,
		},
		{
			name:         "existing path but different method",
			path:         "/v1/users",
			method:       "DELETE",
			expectedRule: PermissionRule{},
			exists:       false,
		},
		{
			name:         "non-existing path",
			path:         "/v1/nonexistent",
			method:       "GET",
			expectedRule: PermissionRule{},
			exists:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rule, exists := config.GetPermissionRule(tt.path, tt.method)
			assert.Equal(t, tt.expectedRule, rule)
			assert.Equal(t, tt.exists, exists)
		})
	}
}

func TestConfig_GetProtectedPathsMap(t *testing.T) {
	config := &Config{
		ProtectedPaths: []string{"/v1/admin", "/v1/system"},
	}

	result := config.GetProtectedPathsMap()

	expected := map[string]struct{}{
		"/v1/admin":  {},
		"/v1/system": {},
	}

	assert.Equal(t, expected, result)
}

func TestConfig_GetUsedPermissions(t *testing.T) {
	config := &Config{
		EndpointPermissions: map[string]map[string]PermissionRule{
			"/v1/users": {
				"GET":  {Permission: "user", Method: "view"},
				"POST": {Permission: "user", Method: "create"},
			},
			"/v1/products": {
				"GET":    {Permission: "product", Method: "view"},
				"DELETE": {Permission: "user", Method: "view"}, // Duplicate permission
			},
		},
	}

	permissions := config.GetUsedPermissions()

	// Should have 3 unique permissions
	assert.Len(t, permissions, 3)

	// Convert to map for easier checking
	permMap := make(map[string]Permission)
	for _, perm := range permissions {
		key := fmt.Sprintf("%s:%s", perm.Name, perm.Method)
		permMap[key] = perm
	}

	expectedPerms := map[string]Permission{
		"user:view":    {Name: "user", Method: "view"},
		"user:create":  {Name: "user", Method: "create"},
		"product:view": {Name: "product", Method: "view"},
	}

	assert.Equal(t, expectedPerms, permMap)
}

func TestConfig_IsExcludedPath(t *testing.T) {
	config := &Config{
		ExcludedPaths: map[string][]string{
			"/v1/public": {"GET", "POST"},
			"/v1/health": {"GET"},
		},
	}

	tests := []struct {
		name     string
		path     string
		method   string
		expected bool
	}{
		{
			name:     "excluded path and method",
			path:     "/v1/public",
			method:   "GET",
			expected: true,
		},
		{
			name:     "excluded path but different method",
			path:     "/v1/public",
			method:   "DELETE",
			expected: false,
		},
		{
			name:     "non-excluded path",
			path:     "/v1/private",
			method:   "GET",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := config.IsExcludedPath(tt.path, tt.method)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConfig_IsProtectedPath(t *testing.T) {
	config := &Config{
		ProtectedPaths: []string{"/v1/admin", "/v1/system"},
	}

	tests := []struct {
		name     string
		path     string
		expected bool
	}{
		{
			name:     "protected path",
			path:     "/v1/admin",
			expected: true,
		},
		{
			name:     "non-protected path",
			path:     "/v1/public",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := config.IsProtectedPath(tt.path)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConfig_SetValidator(t *testing.T) {
	config := &Config{}

	// Mock validator (nil is also valid)
	var mockValidator PermissionValidator

	config.SetValidator(mockValidator)
	assert.Equal(t, mockValidator, config.validator)
}

func TestConfig_Validate(t *testing.T) {
	t.Run("validation without validator", func(t *testing.T) {
		config := &Config{
			ProtectedPaths: []string{"/v1/admin"},
			ExcludedPaths: map[string][]string{
				"/v1/public": {"GET"},
			},
			EndpointPermissions: map[string]map[string]PermissionRule{
				"/v1/users": {
					"GET": {Permission: "user", Method: constants.PermissionMethodView},
				},
			},
		}

		err := config.Validate()
		// Should pass basic validation
		assert.NoError(t, err)
	})

	t.Run("validation with invalid config", func(t *testing.T) {
		config := &Config{
			ProtectedPaths: []string{"invalid-path"}, // Invalid path format
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validation failed")
	})

	t.Run("validation with validator - success", func(t *testing.T) {
		config := &Config{
			ProtectedPaths: []string{"/v1/admin"},
			EndpointPermissions: map[string]map[string]PermissionRule{
				"/v1/users": {
					"GET": {Permission: "user", Method: constants.PermissionMethodView},
				},
			},
		}

		// Mock validator that returns valid permissions
		mockValidator := &mockPermissionValidator{
			permissions: []Permission{
				{Name: "user", Method: constants.PermissionMethodView},
			},
		}
		config.SetValidator(mockValidator)

		err := config.Validate()
		assert.NoError(t, err)
	})

	t.Run("validation with validator - database error", func(t *testing.T) {
		config := &Config{
			ProtectedPaths: []string{"/v1/admin"},
			EndpointPermissions: map[string]map[string]PermissionRule{
				"/v1/users": {
					"GET": {Permission: "user", Method: constants.PermissionMethodView},
				},
			},
		}

		// Mock validator that returns error
		mockValidator := &mockPermissionValidator{
			getAllError: errors.New("database connection failed"),
		}
		config.SetValidator(mockValidator)

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to fetch permissions from database")
	})

	t.Run("validation with validator - permission not found", func(t *testing.T) {
		config := &Config{
			ProtectedPaths: []string{"/v1/admin"},
			EndpointPermissions: map[string]map[string]PermissionRule{
				"/v1/users": {
					"GET": {Permission: "missing", Method: constants.PermissionMethodView},
				},
			},
		}

		// Mock validator that doesn't have the required permission
		mockValidator := &mockPermissionValidator{
			permissions: []Permission{
				{Name: "user", Method: constants.PermissionMethodView},
			},
		}
		config.SetValidator(mockValidator)

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "does not exist in database")
	})

	t.Run("validation with validator - invalid base config", func(t *testing.T) {
		config := &Config{
			ProtectedPaths: []string{""}, // Invalid path
		}

		// Mock validator
		mockValidator := &mockPermissionValidator{
			permissions: []Permission{},
		}
		config.SetValidator(mockValidator)

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validation failed")
	})
}

type mockPermissionValidator struct {
	permissions []Permission
	getAllError error
}

func (m *mockPermissionValidator) ValidatePermission(name, method string) error {
	for _, perm := range m.permissions {
		if perm.Name == name && perm.Method == method {
			return nil
		}
	}
	return errors.New("permission not found")
}

func (m *mockPermissionValidator) GetAllPermissions() ([]Permission, error) {
	if m.getAllError != nil {
		return nil, m.getAllError
	}
	return m.permissions, nil
}

func TestConstants(t *testing.T) {
	t.Run("ValidHTTPMethods contains expected methods", func(t *testing.T) {
		expected := []string{"POST", "GET", "PATCH", "PUT", "DELETE"}
		assert.Equal(t, expected, ValidHTTPMethods)
	})

	t.Run("ValidPermissionMethods contains expected methods", func(t *testing.T) {
		expected := []string{
			constants.PermissionMethodCreate,
			constants.PermissionMethodView,
			constants.PermissionMethodUpdate,
			constants.PermissionMethodDelete,
		}
		assert.Equal(t, expected, ValidPermissionMethods)
	})

	t.Run("PathValidationRegex validates paths", func(t *testing.T) {
		validPaths := []string{
			"/v1/users",
			"/v1/users/{id}",
			"/v1/products/123",
			"/v1/admin_panel",
			"/v1/some-resource",
		}

		invalidPaths := []string{
			"/v2/users",    // wrong version
			"/users",       // missing version
			"v1/users",     // missing leading slash
			"/v1/",         // incomplete
			"/v1/users%20", // invalid characters
		}

		for _, path := range validPaths {
			assert.True(t, PathValidationRegex.MatchString(path), "Path should be valid: %s", path)
		}

		for _, path := range invalidPaths {
			assert.False(t, PathValidationRegex.MatchString(path), "Path should be invalid: %s", path)
		}
	})
}
