package authorization

import (
	"fmt"
	"net/http"
	"regexp"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
)

var (
	ValidHTTPMethods = []string{
		http.MethodPost,
		http.MethodGet,
		http.MethodPatch,
		http.MethodPut,
		http.MethodDelete,
	}

	ValidPermissionMethods = []string{
		constants.PermissionMethodCreate,
		constants.PermissionMethodView,
		constants.PermissionMethodUpdate,
		constants.PermissionMethodDelete,
	}

	PathValidationRegex = regexp.MustCompile(`^/v1/[a-zA-Z0-9_\-/{}\[\]]+$`)
)

// PermissionRule describes permission rule for endpoint
type PermissionRule struct {
	Permission string `yaml:"permission"`
	Method     string `yaml:"method"`
}

// ValidationError contains detailed validation error information
type ValidationError struct {
	Field   string
	Value   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error in %s='%s': %s", e.Field, e.Value, e.Message)
}

// ValidationResult contains validation result
type ValidationResult struct {
	Valid  bool
	Errors []ValidationError
}

func (r ValidationResult) Error() string {
	if r.Valid {
		return ""
	}

	result := "validation failed:\n"
	for _, err := range r.Errors {
		result += fmt.Sprintf("  - %s\n", err.Error())
	}

	return result
}

// Permission represents permission from database
type Permission struct {
	Name   string
	Method string
}

// DefaultRoleAccessRule represents access rule for default role
type DefaultRoleAccessRule struct {
	Path    string   `yaml:"path"`
	Methods []string `yaml:"methods"`
}

// Config contains authorization configuration
type Config struct {
	ProtectedPaths      []string                             `yaml:"protected_paths"`
	ExcludedPaths       map[string][]string                  `yaml:"excluded_paths"`
	EndpointPermissions map[string]map[string]PermissionRule `yaml:"endpoint_permissions"`
	DefaultRoleAccess   map[string][]DefaultRoleAccessRule   `yaml:"default_role_access"`
	validator           PermissionValidator
}

// GetPermissionRule returns permission rule for specified path and method
func (c *Config) GetPermissionRule(path, method string) (PermissionRule, bool) {
	methods, exists := c.EndpointPermissions[path]
	if !exists {
		return PermissionRule{}, false
	}

	rule, exists := methods[method]
	return rule, exists
}

// GetProtectedPathsMap returns protected paths as map for fast lookup
func (c *Config) GetProtectedPathsMap() map[string]struct{} {
	result := make(map[string]struct{}, len(c.ProtectedPaths))
	for _, path := range c.ProtectedPaths {
		result[path] = struct{}{}
	}
	return result
}

// GetUsedPermissions returns list of all permissions used in configuration
func (c *Config) GetUsedPermissions() []Permission {
	permissionSet := make(map[string]Permission)

	for _, methods := range c.EndpointPermissions {
		for _, rule := range methods {
			key := fmt.Sprintf("%s:%s", rule.Permission, rule.Method)
			permissionSet[key] = Permission{
				Name:   rule.Permission,
				Method: rule.Method,
			}
		}
	}

	var permissions []Permission
	for _, perm := range permissionSet {
		permissions = append(permissions, perm)
	}

	return permissions
}

// IsExcludedPath checks if path is excluded for specified method
func (c *Config) IsExcludedPath(path, method string) bool {
	methods, exists := c.ExcludedPaths[path]
	if !exists {
		return false
	}

	for _, excludedMethod := range methods {
		if excludedMethod == method {
			return true
		}
	}
	return false
}

// IsProtectedPath checks if path is protected
func (c *Config) IsProtectedPath(path string) bool {
	for _, protectedPath := range c.ProtectedPaths {
		if protectedPath == path {
			return true
		}
	}
	return false
}

// SetValidator устанавливает validator для проверки разрешений против БД
func (c *Config) SetValidator(validator PermissionValidator) {
	c.validator = validator
}

// GetDefaultRoleAccessRules returns access rules for specified default role
func (c *Config) GetDefaultRoleAccessRules(roleName string) []DefaultRoleAccessRule {
	rules, exists := c.DefaultRoleAccess[roleName]
	if !exists {
		return nil
	}
	return rules
}

// Validate checks configuration correctness including database validation
func (c *Config) Validate() error {
	detailedResult := c.validateDetailed()
	if !detailedResult.Valid {
		return fmt.Errorf("%s", detailedResult.Error())
	}

	if c.validator == nil {
		return nil
	}

	dbResult := c.validateAgainstDB()
	if !dbResult.Valid {
		return fmt.Errorf("%s", dbResult.Error())
	}

	return nil
}
