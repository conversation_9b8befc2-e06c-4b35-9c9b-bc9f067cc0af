package authorization

import (
	"fmt"
	"slices"

	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

type PermissionValidator interface {
	ValidatePermission(name, method string) error
	GetAllPermissions() ([]Permission, error)
}

func NewServiceValidator(permissionAppService interface {
	GetAll() ([]permissionentity.Permission, error)
	GetByNameAndMethod(name, method string) (permissionentity.Permission, error)
}) *ServiceValidator {
	return &ServiceValidator{
		permissionAppService: permissionAppService,
	}
}

type ServiceValidator struct {
	permissionAppService interface {
		GetAll() ([]permissionentity.Permission, error)
		GetByNameAndMethod(name, method string) (permissionentity.Permission, error)
	}
}

func (v *ServiceValidator) GetAllPermissions() ([]Permission, error) {
	entities, err := v.permissionAppService.GetAll()
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions from database: %w", err)
	}

	permissions := make([]Permission, 0, len(entities))
	for _, entity := range entities {
		permissions = append(permissions, Permission{
			Name:   entity.Name,
			Method: entity.Method,
		})
	}

	return permissions, nil
}

func (v *ServiceValidator) ValidatePermission(name, method string) error {
	_, err := v.permissionAppService.GetByNameAndMethod(name, method)
	if err != nil {
		return fmt.Errorf("permission not found: %s:%s - %w", name, method, err)
	}
	return nil
}

func (c *Config) validateAgainstDB() ValidationResult {
	var errors []ValidationError

	baseResult := c.validateDetailed()
	if !baseResult.Valid {
		return baseResult
	}

	if c.validator == nil {
		errors = append(errors, ValidationError{
			Field:   "validator",
			Value:   "",
			Message: "permission validator is not set",
		})
		return ValidationResult{Valid: false, Errors: errors}
	}

	dbPermissions, err := c.validator.GetAllPermissions()
	if err != nil {
		errors = append(errors, ValidationError{
			Field:   "database",
			Value:   "",
			Message: fmt.Sprintf("failed to fetch permissions from database: %v", err),
		})
		return ValidationResult{Valid: false, Errors: errors}
	}

	permissionMap := make(map[string]bool)
	for _, perm := range dbPermissions {
		key := fmt.Sprintf("%s:%s", perm.Name, perm.Method)
		permissionMap[key] = true
	}

	for path, methods := range c.EndpointPermissions {
		for httpMethod, rule := range methods {
			key := fmt.Sprintf("%s:%s", rule.Permission, rule.Method)
			if !permissionMap[key] {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("endpoint_permissions[%s][%s]", path, httpMethod),
					Value:   key,
					Message: fmt.Sprintf("permission '%s' with method '%s' does not exist in database", rule.Permission, rule.Method),
				})
			}
		}
	}

	return ValidationResult{
		Valid:  len(errors) == 0,
		Errors: errors,
	}
}

func (c *Config) validateDetailed() ValidationResult {
	var errors []ValidationError

	errors = append(errors, c.validateProtectedPaths()...)
	errors = append(errors, c.validateExcludedPaths()...)
	errors = append(errors, c.validateEndpointPermissions()...)

	return ValidationResult{
		Valid:  len(errors) == 0,
		Errors: errors,
	}
}

func (c *Config) validateEndpointPermissions() []ValidationError {
	var errors []ValidationError

	for path, methods := range c.EndpointPermissions {
		if path == "" {
			errors = append(errors, ValidationError{
				Field:   "endpoint_permissions",
				Value:   path,
				Message: "path cannot be empty",
			})
			continue
		}

		if !PathValidationRegex.MatchString(path) {
			errors = append(errors, ValidationError{
				Field:   "endpoint_permissions",
				Value:   path,
				Message: "path must start with '/v1/' and contain valid characters",
			})
		}

		for httpMethod, rule := range methods {
			if !slices.Contains(ValidHTTPMethods, httpMethod) {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("endpoint_permissions[%s]", path),
					Value:   httpMethod,
					Message: fmt.Sprintf("invalid HTTP method, allowed: %v", ValidHTTPMethods),
				})
			}

			if rule.Permission == "" {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("endpoint_permissions[%s][%s].permission", path, httpMethod),
					Value:   rule.Permission,
					Message: "permission is required",
				})
			}

			if rule.Method == "" {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("endpoint_permissions[%s][%s].method", path, httpMethod),
					Value:   rule.Method,
					Message: "method is required",
				})
			} else if !slices.Contains(ValidPermissionMethods, rule.Method) {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("endpoint_permissions[%s][%s].method", path, httpMethod),
					Value:   rule.Method,
					Message: fmt.Sprintf("invalid permission method, allowed: %v", ValidPermissionMethods),
				})
			}
		}
	}

	return errors
}

func (c *Config) validateExcludedPaths() []ValidationError {
	var errors []ValidationError

	for path, methods := range c.ExcludedPaths {
		if path == "" {
			errors = append(errors, ValidationError{
				Field:   "excluded_paths",
				Value:   path,
				Message: "path cannot be empty",
			})
			continue
		}

		if !PathValidationRegex.MatchString(path) {
			errors = append(errors, ValidationError{
				Field:   "excluded_paths",
				Value:   path,
				Message: "path must start with '/v1/' and contain valid characters",
			})
		}

		for _, method := range methods {
			if !slices.Contains(ValidHTTPMethods, method) {
				errors = append(errors, ValidationError{
					Field:   fmt.Sprintf("excluded_paths[%s]", path),
					Value:   method,
					Message: fmt.Sprintf("invalid HTTP method, allowed: %v", ValidHTTPMethods),
				})
			}
		}
	}

	return errors
}

func (c *Config) validateProtectedPaths() []ValidationError {
	var errors []ValidationError

	for _, path := range c.ProtectedPaths {
		if path == "" {
			errors = append(errors, ValidationError{
				Field:   "protected_paths",
				Value:   path,
				Message: "path cannot be empty",
			})
			continue
		}

		if !PathValidationRegex.MatchString(path) {
			errors = append(errors, ValidationError{
				Field:   "protected_paths",
				Value:   path,
				Message: "path must start with '/v1/' and contain valid characters",
			})
		}
	}

	return errors
}
