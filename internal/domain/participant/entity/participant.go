package entity

import "time"

type Participant struct {
	ID        int64
	ProductID int64
	UserID    int64
	CreatedAt time.Time
	UpdatedAt time.Time
}

type ParticipantCreateData struct {
	ProductID int64
	UserID    int64
	Email     string
}

type ParticipantFull struct {
	ID          int64
	ProductID   int64
	UserID      int64
	Email       string
	FullName    string
	Position    string
	IsOwner     bool
	GroupIDs    []int64
	RoleIDs     []int64
	LastLoginAt *time.Time
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type ParticipantShort struct {
	ID        int64
	ProductID int64
	UserID    int64
	Email     string
	FullName  string
	IsOwner   bool
	CreatedAt time.Time
	UpdatedAt time.Time
}

type ParticipantGroup struct {
	ID            int64
	ParticipantID int64
	GroupID       int64
	CreatedAt     time.Time
}

type ParticipantRole struct {
	ID            int64
	ParticipantID int64
	RoleID        int64
	CreatedAt     time.Time
}

type ParticipantRoleCreateData struct {
	ParticipantID int64
	RoleID        int64
}

type ParticipantRoleUpdateData struct {
	ID            int64
	ParticipantID *int64
	RoleID        *int64
}
