package aggregate

type ParticipantPermissionAggregate struct {
	participantID int64
	permissions   map[int64]struct{}
	roles         map[int64]struct{}
}

func NewParticipantPermissionAggregate(participantID int64) *ParticipantPermissionAggregate {
	return &ParticipantPermissionAggregate{
		participantID: participantID,
		permissions:   make(map[int64]struct{}),
		roles:         make(map[int64]struct{}),
	}
}

func (a *ParticipantPermissionAggregate) AddRoleID(roleID int64) {
	a.roles[roleID] = struct{}{}
}

func (a *ParticipantPermissionAggregate) AddPermissionID(permissionID int64) {
	a.permissions[permissionID] = struct{}{}
}

func (a *ParticipantPermissionAggregate) GetPermissionIDs() []int64 {
	ids := make([]int64, 0, len(a.permissions))
	for id := range a.permissions {
		ids = append(ids, id)
	}
	return ids
}

func (a *ParticipantPermissionAggregate) GetRoleIDs() []int64 {
	ids := make([]int64, 0, len(a.roles))
	for id := range a.roles {
		ids = append(ids, id)
	}
	return ids
}
