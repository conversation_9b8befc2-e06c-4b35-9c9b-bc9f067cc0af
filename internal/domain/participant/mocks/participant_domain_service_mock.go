// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/service.ParticipantDomainService -o participant_domain_service_mock.go -n ParticipantDomainServiceMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// ParticipantDomainServiceMock implements mm_service.ParticipantDomainService
type ParticipantDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.Participant, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, participant participantentity.ParticipantCreateData)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mParticipantDomainServiceMockCreate

	funcCreateParticipant          func(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.ParticipantFull, err error)
	funcCreateParticipantOrigin    string
	inspectFuncCreateParticipant   func(ctx context.Context, participant participantentity.ParticipantCreateData)
	afterCreateParticipantCounter  uint64
	beforeCreateParticipantCounter uint64
	CreateParticipantMock          mParticipantDomainServiceMockCreateParticipant

	funcDelete          func(ctx context.Context, participantID int64, productID int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(ctx context.Context, participantID int64, productID int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mParticipantDomainServiceMockDelete

	funcDeleteByParticipantIDAndProductID          func(ctx context.Context, participantID int64, productID int64) (err error)
	funcDeleteByParticipantIDAndProductIDOrigin    string
	inspectFuncDeleteByParticipantIDAndProductID   func(ctx context.Context, participantID int64, productID int64)
	afterDeleteByParticipantIDAndProductIDCounter  uint64
	beforeDeleteByParticipantIDAndProductIDCounter uint64
	DeleteByParticipantIDAndProductIDMock          mParticipantDomainServiceMockDeleteByParticipantIDAndProductID

	funcDeleteByUserIDAndGroupIDs          func(ctx context.Context, userID int64, groupIDs []int64) (err error)
	funcDeleteByUserIDAndGroupIDsOrigin    string
	inspectFuncDeleteByUserIDAndGroupIDs   func(ctx context.Context, userID int64, groupIDs []int64)
	afterDeleteByUserIDAndGroupIDsCounter  uint64
	beforeDeleteByUserIDAndGroupIDsCounter uint64
	DeleteByUserIDAndGroupIDsMock          mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs

	funcDeleteByUserIDAndProductIDs          func(ctx context.Context, userID int64, productIDs []int64) (err error)
	funcDeleteByUserIDAndProductIDsOrigin    string
	inspectFuncDeleteByUserIDAndProductIDs   func(ctx context.Context, userID int64, productIDs []int64)
	afterDeleteByUserIDAndProductIDsCounter  uint64
	beforeDeleteByUserIDAndProductIDsCounter uint64
	DeleteByUserIDAndProductIDsMock          mParticipantDomainServiceMockDeleteByUserIDAndProductIDs

	funcDeleteByUserIDAndRoleIDs          func(ctx context.Context, userID int64, roleIDs []int64) (err error)
	funcDeleteByUserIDAndRoleIDsOrigin    string
	inspectFuncDeleteByUserIDAndRoleIDs   func(ctx context.Context, userID int64, roleIDs []int64)
	afterDeleteByUserIDAndRoleIDsCounter  uint64
	beforeDeleteByUserIDAndRoleIDsCounter uint64
	DeleteByUserIDAndRoleIDsMock          mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs

	funcExistByParticipantIDAndProductID          func(participantID int64, productID int64) (b1 bool, err error)
	funcExistByParticipantIDAndProductIDOrigin    string
	inspectFuncExistByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterExistByParticipantIDAndProductIDCounter  uint64
	beforeExistByParticipantIDAndProductIDCounter uint64
	ExistByParticipantIDAndProductIDMock          mParticipantDomainServiceMockExistByParticipantIDAndProductID

	funcGetByParticipantIDAndProductID          func(participantID int64, productID int64) (p1 participantentity.ParticipantFull, err error)
	funcGetByParticipantIDAndProductIDOrigin    string
	inspectFuncGetByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterGetByParticipantIDAndProductIDCounter  uint64
	beforeGetByParticipantIDAndProductIDCounter uint64
	GetByParticipantIDAndProductIDMock          mParticipantDomainServiceMockGetByParticipantIDAndProductID

	funcGetByProductID          func(productID int64) (pa1 []participantentity.Participant, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mParticipantDomainServiceMockGetByProductID

	funcGetByUserID          func(userID int64) (pa1 []participantentity.Participant, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mParticipantDomainServiceMockGetByUserID

	funcGetByUserIDAndProductID          func(userID int64, productID int64) (p1 participantentity.Participant, err error)
	funcGetByUserIDAndProductIDOrigin    string
	inspectFuncGetByUserIDAndProductID   func(userID int64, productID int64)
	afterGetByUserIDAndProductIDCounter  uint64
	beforeGetByUserIDAndProductIDCounter uint64
	GetByUserIDAndProductIDMock          mParticipantDomainServiceMockGetByUserIDAndProductID

	funcGetByUserIDAndProductIDs          func(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error)
	funcGetByUserIDAndProductIDsOrigin    string
	inspectFuncGetByUserIDAndProductIDs   func(userID int64, productIDs []int64)
	afterGetByUserIDAndProductIDsCounter  uint64
	beforeGetByUserIDAndProductIDsCounter uint64
	GetByUserIDAndProductIDsMock          mParticipantDomainServiceMockGetByUserIDAndProductIDs

	funcGetOwnersByProductIDs          func(productIDs []int64) (m1 map[int64][]productentity.Owner, err error)
	funcGetOwnersByProductIDsOrigin    string
	inspectFuncGetOwnersByProductIDs   func(productIDs []int64)
	afterGetOwnersByProductIDsCounter  uint64
	beforeGetOwnersByProductIDsCounter uint64
	GetOwnersByProductIDsMock          mParticipantDomainServiceMockGetOwnersByProductIDs

	funcGetParticipantFullByParticipantIDAndProductID          func(participantID int64, productID int64) (p1 participantentity.ParticipantFull, err error)
	funcGetParticipantFullByParticipantIDAndProductIDOrigin    string
	inspectFuncGetParticipantFullByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterGetParticipantFullByParticipantIDAndProductIDCounter  uint64
	beforeGetParticipantFullByParticipantIDAndProductIDCounter uint64
	GetParticipantFullByParticipantIDAndProductIDMock          mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID

	funcGetParticipantFullByProductID          func(productID int64) (pa1 []participantentity.ParticipantFull, err error)
	funcGetParticipantFullByProductIDOrigin    string
	inspectFuncGetParticipantFullByProductID   func(productID int64)
	afterGetParticipantFullByProductIDCounter  uint64
	beforeGetParticipantFullByProductIDCounter uint64
	GetParticipantFullByProductIDMock          mParticipantDomainServiceMockGetParticipantFullByProductID

	funcUpdateGroups          func(ctx context.Context, participantID int64, groupIDs []int64) (ia1 []int64, err error)
	funcUpdateGroupsOrigin    string
	inspectFuncUpdateGroups   func(ctx context.Context, participantID int64, groupIDs []int64)
	afterUpdateGroupsCounter  uint64
	beforeUpdateGroupsCounter uint64
	UpdateGroupsMock          mParticipantDomainServiceMockUpdateGroups

	funcUpdateOwners          func(productID int64, ownerEmails []string) (err error)
	funcUpdateOwnersOrigin    string
	inspectFuncUpdateOwners   func(productID int64, ownerEmails []string)
	afterUpdateOwnersCounter  uint64
	beforeUpdateOwnersCounter uint64
	UpdateOwnersMock          mParticipantDomainServiceMockUpdateOwners

	funcUpdateParticipantByUserUpdateData          func(userID int64, updData userentity.UserUpdateData) (err error)
	funcUpdateParticipantByUserUpdateDataOrigin    string
	inspectFuncUpdateParticipantByUserUpdateData   func(userID int64, updData userentity.UserUpdateData)
	afterUpdateParticipantByUserUpdateDataCounter  uint64
	beforeUpdateParticipantByUserUpdateDataCounter uint64
	UpdateParticipantByUserUpdateDataMock          mParticipantDomainServiceMockUpdateParticipantByUserUpdateData
}

// NewParticipantDomainServiceMock returns a mock for mm_service.ParticipantDomainService
func NewParticipantDomainServiceMock(t minimock.Tester) *ParticipantDomainServiceMock {
	m := &ParticipantDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mParticipantDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*ParticipantDomainServiceMockCreateParams{}

	m.CreateParticipantMock = mParticipantDomainServiceMockCreateParticipant{mock: m}
	m.CreateParticipantMock.callArgs = []*ParticipantDomainServiceMockCreateParticipantParams{}

	m.DeleteMock = mParticipantDomainServiceMockDelete{mock: m}
	m.DeleteMock.callArgs = []*ParticipantDomainServiceMockDeleteParams{}

	m.DeleteByParticipantIDAndProductIDMock = mParticipantDomainServiceMockDeleteByParticipantIDAndProductID{mock: m}
	m.DeleteByParticipantIDAndProductIDMock.callArgs = []*ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams{}

	m.DeleteByUserIDAndGroupIDsMock = mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs{mock: m}
	m.DeleteByUserIDAndGroupIDsMock.callArgs = []*ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams{}

	m.DeleteByUserIDAndProductIDsMock = mParticipantDomainServiceMockDeleteByUserIDAndProductIDs{mock: m}
	m.DeleteByUserIDAndProductIDsMock.callArgs = []*ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams{}

	m.DeleteByUserIDAndRoleIDsMock = mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs{mock: m}
	m.DeleteByUserIDAndRoleIDsMock.callArgs = []*ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams{}

	m.ExistByParticipantIDAndProductIDMock = mParticipantDomainServiceMockExistByParticipantIDAndProductID{mock: m}
	m.ExistByParticipantIDAndProductIDMock.callArgs = []*ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams{}

	m.GetByParticipantIDAndProductIDMock = mParticipantDomainServiceMockGetByParticipantIDAndProductID{mock: m}
	m.GetByParticipantIDAndProductIDMock.callArgs = []*ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams{}

	m.GetByProductIDMock = mParticipantDomainServiceMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*ParticipantDomainServiceMockGetByProductIDParams{}

	m.GetByUserIDMock = mParticipantDomainServiceMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*ParticipantDomainServiceMockGetByUserIDParams{}

	m.GetByUserIDAndProductIDMock = mParticipantDomainServiceMockGetByUserIDAndProductID{mock: m}
	m.GetByUserIDAndProductIDMock.callArgs = []*ParticipantDomainServiceMockGetByUserIDAndProductIDParams{}

	m.GetByUserIDAndProductIDsMock = mParticipantDomainServiceMockGetByUserIDAndProductIDs{mock: m}
	m.GetByUserIDAndProductIDsMock.callArgs = []*ParticipantDomainServiceMockGetByUserIDAndProductIDsParams{}

	m.GetOwnersByProductIDsMock = mParticipantDomainServiceMockGetOwnersByProductIDs{mock: m}
	m.GetOwnersByProductIDsMock.callArgs = []*ParticipantDomainServiceMockGetOwnersByProductIDsParams{}

	m.GetParticipantFullByParticipantIDAndProductIDMock = mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID{mock: m}
	m.GetParticipantFullByParticipantIDAndProductIDMock.callArgs = []*ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams{}

	m.GetParticipantFullByProductIDMock = mParticipantDomainServiceMockGetParticipantFullByProductID{mock: m}
	m.GetParticipantFullByProductIDMock.callArgs = []*ParticipantDomainServiceMockGetParticipantFullByProductIDParams{}

	m.UpdateGroupsMock = mParticipantDomainServiceMockUpdateGroups{mock: m}
	m.UpdateGroupsMock.callArgs = []*ParticipantDomainServiceMockUpdateGroupsParams{}

	m.UpdateOwnersMock = mParticipantDomainServiceMockUpdateOwners{mock: m}
	m.UpdateOwnersMock.callArgs = []*ParticipantDomainServiceMockUpdateOwnersParams{}

	m.UpdateParticipantByUserUpdateDataMock = mParticipantDomainServiceMockUpdateParticipantByUserUpdateData{mock: m}
	m.UpdateParticipantByUserUpdateDataMock.callArgs = []*ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mParticipantDomainServiceMockCreate struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockCreateExpectation
	expectations       []*ParticipantDomainServiceMockCreateExpectation

	callArgs []*ParticipantDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockCreateExpectation specifies expectation struct of the ParticipantDomainService.Create
type ParticipantDomainServiceMockCreateExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockCreateParams
	paramPtrs          *ParticipantDomainServiceMockCreateParamPtrs
	expectationOrigins ParticipantDomainServiceMockCreateExpectationOrigins
	results            *ParticipantDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockCreateParams contains parameters of the ParticipantDomainService.Create
type ParticipantDomainServiceMockCreateParams struct {
	ctx         context.Context
	participant participantentity.ParticipantCreateData
}

// ParticipantDomainServiceMockCreateParamPtrs contains pointers to parameters of the ParticipantDomainService.Create
type ParticipantDomainServiceMockCreateParamPtrs struct {
	ctx         *context.Context
	participant *participantentity.ParticipantCreateData
}

// ParticipantDomainServiceMockCreateResults contains results of the ParticipantDomainService.Create
type ParticipantDomainServiceMockCreateResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantDomainServiceMockCreateOrigins contains origins of expectations of the ParticipantDomainService.Create
type ParticipantDomainServiceMockCreateExpectationOrigins struct {
	origin            string
	originCtx         string
	originParticipant string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mParticipantDomainServiceMockCreate) Optional() *mParticipantDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ParticipantDomainService.Create
func (mmCreate *mParticipantDomainServiceMockCreate) Expect(ctx context.Context, participant participantentity.ParticipantCreateData) *mParticipantDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ParticipantDomainServiceMockCreateParams{ctx, participant}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.Create
func (mmCreate *mParticipantDomainServiceMockCreate) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectParticipantParam2 sets up expected param participant for ParticipantDomainService.Create
func (mmCreate *mParticipantDomainServiceMockCreate) ExpectParticipantParam2(participant participantentity.ParticipantCreateData) *mParticipantDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.participant = &participant
	mmCreate.defaultExpectation.expectationOrigins.originParticipant = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.Create
func (mmCreate *mParticipantDomainServiceMockCreate) Inspect(f func(ctx context.Context, participant participantentity.ParticipantCreateData)) *mParticipantDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ParticipantDomainService.Create
func (mmCreate *mParticipantDomainServiceMockCreate) Return(p1 participantentity.Participant, err error) *ParticipantDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ParticipantDomainServiceMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ParticipantDomainService.Create method
func (mmCreate *mParticipantDomainServiceMockCreate) Set(f func(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.Participant, err error)) *ParticipantDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ParticipantDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mParticipantDomainServiceMockCreate) When(ctx context.Context, participant participantentity.ParticipantCreateData) *ParticipantDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ParticipantDomainServiceMockCreateParams{ctx, participant},
		expectationOrigins: ParticipantDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.Create return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockCreateExpectation) Then(p1 participantentity.Participant, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.Create should be invoked
func (mmCreate *mParticipantDomainServiceMockCreate) Times(n uint64) *mParticipantDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ParticipantDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mParticipantDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.ParticipantDomainService
func (mmCreate *ParticipantDomainServiceMock) Create(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, participant)
	}

	mm_params := ParticipantDomainServiceMockCreateParams{ctx, participant}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockCreateParams{ctx, participant}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("ParticipantDomainServiceMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participant != nil && !minimock.Equal(*mm_want_ptrs.participant, mm_got.participant) {
				mmCreate.t.Errorf("ParticipantDomainServiceMock.Create got unexpected parameter participant, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originParticipant, *mm_want_ptrs.participant, mm_got.participant, minimock.Diff(*mm_want_ptrs.participant, mm_got.participant))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ParticipantDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ParticipantDomainServiceMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, participant)
	}
	mmCreate.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.Create. %v %v", ctx, participant)
	return
}

// CreateAfterCounter returns a count of finished ParticipantDomainServiceMock.Create invocations
func (mmCreate *ParticipantDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ParticipantDomainServiceMock.Create invocations
func (mmCreate *ParticipantDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mParticipantDomainServiceMockCreate) Calls() []*ParticipantDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mParticipantDomainServiceMockCreateParticipant struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockCreateParticipantExpectation
	expectations       []*ParticipantDomainServiceMockCreateParticipantExpectation

	callArgs []*ParticipantDomainServiceMockCreateParticipantParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockCreateParticipantExpectation specifies expectation struct of the ParticipantDomainService.CreateParticipant
type ParticipantDomainServiceMockCreateParticipantExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockCreateParticipantParams
	paramPtrs          *ParticipantDomainServiceMockCreateParticipantParamPtrs
	expectationOrigins ParticipantDomainServiceMockCreateParticipantExpectationOrigins
	results            *ParticipantDomainServiceMockCreateParticipantResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockCreateParticipantParams contains parameters of the ParticipantDomainService.CreateParticipant
type ParticipantDomainServiceMockCreateParticipantParams struct {
	ctx         context.Context
	participant participantentity.ParticipantCreateData
}

// ParticipantDomainServiceMockCreateParticipantParamPtrs contains pointers to parameters of the ParticipantDomainService.CreateParticipant
type ParticipantDomainServiceMockCreateParticipantParamPtrs struct {
	ctx         *context.Context
	participant *participantentity.ParticipantCreateData
}

// ParticipantDomainServiceMockCreateParticipantResults contains results of the ParticipantDomainService.CreateParticipant
type ParticipantDomainServiceMockCreateParticipantResults struct {
	p1  participantentity.ParticipantFull
	err error
}

// ParticipantDomainServiceMockCreateParticipantOrigins contains origins of expectations of the ParticipantDomainService.CreateParticipant
type ParticipantDomainServiceMockCreateParticipantExpectationOrigins struct {
	origin            string
	originCtx         string
	originParticipant string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Optional() *mParticipantDomainServiceMockCreateParticipant {
	mmCreateParticipant.optional = true
	return mmCreateParticipant
}

// Expect sets up expected params for ParticipantDomainService.CreateParticipant
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Expect(ctx context.Context, participant participantentity.ParticipantCreateData) *mParticipantDomainServiceMockCreateParticipant {
	if mmCreateParticipant.mock.funcCreateParticipant != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Set")
	}

	if mmCreateParticipant.defaultExpectation == nil {
		mmCreateParticipant.defaultExpectation = &ParticipantDomainServiceMockCreateParticipantExpectation{}
	}

	if mmCreateParticipant.defaultExpectation.paramPtrs != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by ExpectParams functions")
	}

	mmCreateParticipant.defaultExpectation.params = &ParticipantDomainServiceMockCreateParticipantParams{ctx, participant}
	mmCreateParticipant.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateParticipant.expectations {
		if minimock.Equal(e.params, mmCreateParticipant.defaultExpectation.params) {
			mmCreateParticipant.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateParticipant.defaultExpectation.params)
		}
	}

	return mmCreateParticipant
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.CreateParticipant
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockCreateParticipant {
	if mmCreateParticipant.mock.funcCreateParticipant != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Set")
	}

	if mmCreateParticipant.defaultExpectation == nil {
		mmCreateParticipant.defaultExpectation = &ParticipantDomainServiceMockCreateParticipantExpectation{}
	}

	if mmCreateParticipant.defaultExpectation.params != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Expect")
	}

	if mmCreateParticipant.defaultExpectation.paramPtrs == nil {
		mmCreateParticipant.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockCreateParticipantParamPtrs{}
	}
	mmCreateParticipant.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateParticipant.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateParticipant
}

// ExpectParticipantParam2 sets up expected param participant for ParticipantDomainService.CreateParticipant
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) ExpectParticipantParam2(participant participantentity.ParticipantCreateData) *mParticipantDomainServiceMockCreateParticipant {
	if mmCreateParticipant.mock.funcCreateParticipant != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Set")
	}

	if mmCreateParticipant.defaultExpectation == nil {
		mmCreateParticipant.defaultExpectation = &ParticipantDomainServiceMockCreateParticipantExpectation{}
	}

	if mmCreateParticipant.defaultExpectation.params != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Expect")
	}

	if mmCreateParticipant.defaultExpectation.paramPtrs == nil {
		mmCreateParticipant.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockCreateParticipantParamPtrs{}
	}
	mmCreateParticipant.defaultExpectation.paramPtrs.participant = &participant
	mmCreateParticipant.defaultExpectation.expectationOrigins.originParticipant = minimock.CallerInfo(1)

	return mmCreateParticipant
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.CreateParticipant
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Inspect(f func(ctx context.Context, participant participantentity.ParticipantCreateData)) *mParticipantDomainServiceMockCreateParticipant {
	if mmCreateParticipant.mock.inspectFuncCreateParticipant != nil {
		mmCreateParticipant.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.CreateParticipant")
	}

	mmCreateParticipant.mock.inspectFuncCreateParticipant = f

	return mmCreateParticipant
}

// Return sets up results that will be returned by ParticipantDomainService.CreateParticipant
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Return(p1 participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	if mmCreateParticipant.mock.funcCreateParticipant != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Set")
	}

	if mmCreateParticipant.defaultExpectation == nil {
		mmCreateParticipant.defaultExpectation = &ParticipantDomainServiceMockCreateParticipantExpectation{mock: mmCreateParticipant.mock}
	}
	mmCreateParticipant.defaultExpectation.results = &ParticipantDomainServiceMockCreateParticipantResults{p1, err}
	mmCreateParticipant.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateParticipant.mock
}

// Set uses given function f to mock the ParticipantDomainService.CreateParticipant method
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Set(f func(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.ParticipantFull, err error)) *ParticipantDomainServiceMock {
	if mmCreateParticipant.defaultExpectation != nil {
		mmCreateParticipant.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.CreateParticipant method")
	}

	if len(mmCreateParticipant.expectations) > 0 {
		mmCreateParticipant.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.CreateParticipant method")
	}

	mmCreateParticipant.mock.funcCreateParticipant = f
	mmCreateParticipant.mock.funcCreateParticipantOrigin = minimock.CallerInfo(1)
	return mmCreateParticipant.mock
}

// When sets expectation for the ParticipantDomainService.CreateParticipant which will trigger the result defined by the following
// Then helper
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) When(ctx context.Context, participant participantentity.ParticipantCreateData) *ParticipantDomainServiceMockCreateParticipantExpectation {
	if mmCreateParticipant.mock.funcCreateParticipant != nil {
		mmCreateParticipant.mock.t.Fatalf("ParticipantDomainServiceMock.CreateParticipant mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockCreateParticipantExpectation{
		mock:               mmCreateParticipant.mock,
		params:             &ParticipantDomainServiceMockCreateParticipantParams{ctx, participant},
		expectationOrigins: ParticipantDomainServiceMockCreateParticipantExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateParticipant.expectations = append(mmCreateParticipant.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.CreateParticipant return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockCreateParticipantExpectation) Then(p1 participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockCreateParticipantResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.CreateParticipant should be invoked
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Times(n uint64) *mParticipantDomainServiceMockCreateParticipant {
	if n == 0 {
		mmCreateParticipant.mock.t.Fatalf("Times of ParticipantDomainServiceMock.CreateParticipant mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateParticipant.expectedInvocations, n)
	mmCreateParticipant.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateParticipant
}

func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) invocationsDone() bool {
	if len(mmCreateParticipant.expectations) == 0 && mmCreateParticipant.defaultExpectation == nil && mmCreateParticipant.mock.funcCreateParticipant == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateParticipant.mock.afterCreateParticipantCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateParticipant.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateParticipant implements mm_service.ParticipantDomainService
func (mmCreateParticipant *ParticipantDomainServiceMock) CreateParticipant(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.ParticipantFull, err error) {
	mm_atomic.AddUint64(&mmCreateParticipant.beforeCreateParticipantCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateParticipant.afterCreateParticipantCounter, 1)

	mmCreateParticipant.t.Helper()

	if mmCreateParticipant.inspectFuncCreateParticipant != nil {
		mmCreateParticipant.inspectFuncCreateParticipant(ctx, participant)
	}

	mm_params := ParticipantDomainServiceMockCreateParticipantParams{ctx, participant}

	// Record call args
	mmCreateParticipant.CreateParticipantMock.mutex.Lock()
	mmCreateParticipant.CreateParticipantMock.callArgs = append(mmCreateParticipant.CreateParticipantMock.callArgs, &mm_params)
	mmCreateParticipant.CreateParticipantMock.mutex.Unlock()

	for _, e := range mmCreateParticipant.CreateParticipantMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreateParticipant.CreateParticipantMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateParticipant.CreateParticipantMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateParticipant.CreateParticipantMock.defaultExpectation.params
		mm_want_ptrs := mmCreateParticipant.CreateParticipantMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockCreateParticipantParams{ctx, participant}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateParticipant.t.Errorf("ParticipantDomainServiceMock.CreateParticipant got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateParticipant.CreateParticipantMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participant != nil && !minimock.Equal(*mm_want_ptrs.participant, mm_got.participant) {
				mmCreateParticipant.t.Errorf("ParticipantDomainServiceMock.CreateParticipant got unexpected parameter participant, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateParticipant.CreateParticipantMock.defaultExpectation.expectationOrigins.originParticipant, *mm_want_ptrs.participant, mm_got.participant, minimock.Diff(*mm_want_ptrs.participant, mm_got.participant))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateParticipant.t.Errorf("ParticipantDomainServiceMock.CreateParticipant got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateParticipant.CreateParticipantMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateParticipant.CreateParticipantMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateParticipant.t.Fatal("No results are set for the ParticipantDomainServiceMock.CreateParticipant")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreateParticipant.funcCreateParticipant != nil {
		return mmCreateParticipant.funcCreateParticipant(ctx, participant)
	}
	mmCreateParticipant.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.CreateParticipant. %v %v", ctx, participant)
	return
}

// CreateParticipantAfterCounter returns a count of finished ParticipantDomainServiceMock.CreateParticipant invocations
func (mmCreateParticipant *ParticipantDomainServiceMock) CreateParticipantAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateParticipant.afterCreateParticipantCounter)
}

// CreateParticipantBeforeCounter returns a count of ParticipantDomainServiceMock.CreateParticipant invocations
func (mmCreateParticipant *ParticipantDomainServiceMock) CreateParticipantBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateParticipant.beforeCreateParticipantCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.CreateParticipant.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateParticipant *mParticipantDomainServiceMockCreateParticipant) Calls() []*ParticipantDomainServiceMockCreateParticipantParams {
	mmCreateParticipant.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockCreateParticipantParams, len(mmCreateParticipant.callArgs))
	copy(argCopy, mmCreateParticipant.callArgs)

	mmCreateParticipant.mutex.RUnlock()

	return argCopy
}

// MinimockCreateParticipantDone returns true if the count of the CreateParticipant invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockCreateParticipantDone() bool {
	if m.CreateParticipantMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateParticipantMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateParticipantMock.invocationsDone()
}

// MinimockCreateParticipantInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockCreateParticipantInspect() {
	for _, e := range m.CreateParticipantMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.CreateParticipant at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateParticipantCounter := mm_atomic.LoadUint64(&m.afterCreateParticipantCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateParticipantMock.defaultExpectation != nil && afterCreateParticipantCounter < 1 {
		if m.CreateParticipantMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.CreateParticipant at\n%s", m.CreateParticipantMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.CreateParticipant at\n%s with params: %#v", m.CreateParticipantMock.defaultExpectation.expectationOrigins.origin, *m.CreateParticipantMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateParticipant != nil && afterCreateParticipantCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.CreateParticipant at\n%s", m.funcCreateParticipantOrigin)
	}

	if !m.CreateParticipantMock.invocationsDone() && afterCreateParticipantCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.CreateParticipant at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateParticipantMock.expectedInvocations), m.CreateParticipantMock.expectedInvocationsOrigin, afterCreateParticipantCounter)
	}
}

type mParticipantDomainServiceMockDelete struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockDeleteExpectation
	expectations       []*ParticipantDomainServiceMockDeleteExpectation

	callArgs []*ParticipantDomainServiceMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockDeleteExpectation specifies expectation struct of the ParticipantDomainService.Delete
type ParticipantDomainServiceMockDeleteExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockDeleteParams
	paramPtrs          *ParticipantDomainServiceMockDeleteParamPtrs
	expectationOrigins ParticipantDomainServiceMockDeleteExpectationOrigins
	results            *ParticipantDomainServiceMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockDeleteParams contains parameters of the ParticipantDomainService.Delete
type ParticipantDomainServiceMockDeleteParams struct {
	ctx           context.Context
	participantID int64
	productID     int64
}

// ParticipantDomainServiceMockDeleteParamPtrs contains pointers to parameters of the ParticipantDomainService.Delete
type ParticipantDomainServiceMockDeleteParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	productID     *int64
}

// ParticipantDomainServiceMockDeleteResults contains results of the ParticipantDomainService.Delete
type ParticipantDomainServiceMockDeleteResults struct {
	err error
}

// ParticipantDomainServiceMockDeleteOrigins contains origins of expectations of the ParticipantDomainService.Delete
type ParticipantDomainServiceMockDeleteExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mParticipantDomainServiceMockDelete) Optional() *mParticipantDomainServiceMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for ParticipantDomainService.Delete
func (mmDelete *mParticipantDomainServiceMockDelete) Expect(ctx context.Context, participantID int64, productID int64) *mParticipantDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &ParticipantDomainServiceMockDeleteParams{ctx, participantID, productID}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.Delete
func (mmDelete *mParticipantDomainServiceMockDelete) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.ctx = &ctx
	mmDelete.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantDomainService.Delete
func (mmDelete *mParticipantDomainServiceMockDelete) ExpectParticipantIDParam2(participantID int64) *mParticipantDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.participantID = &participantID
	mmDelete.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectProductIDParam3 sets up expected param productID for ParticipantDomainService.Delete
func (mmDelete *mParticipantDomainServiceMockDelete) ExpectProductIDParam3(productID int64) *mParticipantDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.productID = &productID
	mmDelete.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.Delete
func (mmDelete *mParticipantDomainServiceMockDelete) Inspect(f func(ctx context.Context, participantID int64, productID int64)) *mParticipantDomainServiceMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by ParticipantDomainService.Delete
func (mmDelete *mParticipantDomainServiceMockDelete) Return(err error) *ParticipantDomainServiceMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantDomainServiceMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &ParticipantDomainServiceMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the ParticipantDomainService.Delete method
func (mmDelete *mParticipantDomainServiceMockDelete) Set(f func(ctx context.Context, participantID int64, productID int64) (err error)) *ParticipantDomainServiceMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the ParticipantDomainService.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mParticipantDomainServiceMockDelete) When(ctx context.Context, participantID int64, productID int64) *ParticipantDomainServiceMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantDomainServiceMock.Delete mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &ParticipantDomainServiceMockDeleteParams{ctx, participantID, productID},
		expectationOrigins: ParticipantDomainServiceMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.Delete return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockDeleteExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockDeleteResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.Delete should be invoked
func (mmDelete *mParticipantDomainServiceMockDelete) Times(n uint64) *mParticipantDomainServiceMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of ParticipantDomainServiceMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mParticipantDomainServiceMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_service.ParticipantDomainService
func (mmDelete *ParticipantDomainServiceMock) Delete(ctx context.Context, participantID int64, productID int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(ctx, participantID, productID)
	}

	mm_params := ParticipantDomainServiceMockDeleteParams{ctx, participantID, productID}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockDeleteParams{ctx, participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDelete.t.Errorf("ParticipantDomainServiceMock.Delete got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDelete.t.Errorf("ParticipantDomainServiceMock.Delete got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmDelete.t.Errorf("ParticipantDomainServiceMock.Delete got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("ParticipantDomainServiceMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the ParticipantDomainServiceMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(ctx, participantID, productID)
	}
	mmDelete.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.Delete. %v %v %v", ctx, participantID, productID)
	return
}

// DeleteAfterCounter returns a count of finished ParticipantDomainServiceMock.Delete invocations
func (mmDelete *ParticipantDomainServiceMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of ParticipantDomainServiceMock.Delete invocations
func (mmDelete *ParticipantDomainServiceMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mParticipantDomainServiceMockDelete) Calls() []*ParticipantDomainServiceMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mParticipantDomainServiceMockDeleteByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantDomainService.DeleteByParticipantIDAndProductID
type ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams contains parameters of the ParticipantDomainService.DeleteByParticipantIDAndProductID
type ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams struct {
	ctx           context.Context
	participantID int64
	productID     int64
}

// ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.DeleteByParticipantIDAndProductID
type ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	productID     *int64
}

// ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDResults contains results of the ParticipantDomainService.DeleteByParticipantIDAndProductID
type ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDResults struct {
	err error
}

// ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantDomainService.DeleteByParticipantIDAndProductID
type ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Optional() *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	mmDeleteByParticipantIDAndProductID.optional = true
	return mmDeleteByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantDomainService.DeleteByParticipantIDAndProductID
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Expect(ctx context.Context, participantID int64, productID int64) *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	if mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Set")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation{}
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantIDAndProductID.defaultExpectation.params = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams{ctx, participantID, productID}
	mmDeleteByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantIDAndProductID.defaultExpectation.params) {
			mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantIDAndProductID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.DeleteByParticipantIDAndProductID
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	if mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Set")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation{}
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParamPtrs{}
	}
	mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByParticipantIDAndProductID
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantDomainService.DeleteByParticipantIDAndProductID
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) ExpectParticipantIDParam2(participantID int64) *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	if mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Set")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation{}
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParamPtrs{}
	}
	mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmDeleteByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDeleteByParticipantIDAndProductID
}

// ExpectProductIDParam3 sets up expected param productID for ParticipantDomainService.DeleteByParticipantIDAndProductID
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) ExpectProductIDParam3(productID int64) *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	if mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Set")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation{}
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParamPtrs{}
	}
	mmDeleteByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmDeleteByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmDeleteByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.DeleteByParticipantIDAndProductID
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Inspect(f func(ctx context.Context, participantID int64, productID int64)) *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	if mmDeleteByParticipantIDAndProductID.mock.inspectFuncDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID")
	}

	mmDeleteByParticipantIDAndProductID.mock.inspectFuncDeleteByParticipantIDAndProductID = f

	return mmDeleteByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantDomainService.DeleteByParticipantIDAndProductID
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Return(err error) *ParticipantDomainServiceMock {
	if mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Set")
	}

	if mmDeleteByParticipantIDAndProductID.defaultExpectation == nil {
		mmDeleteByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation{mock: mmDeleteByParticipantIDAndProductID.mock}
	}
	mmDeleteByParticipantIDAndProductID.defaultExpectation.results = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDResults{err}
	mmDeleteByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.DeleteByParticipantIDAndProductID method
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Set(f func(ctx context.Context, participantID int64, productID int64) (err error)) *ParticipantDomainServiceMock {
	if mmDeleteByParticipantIDAndProductID.defaultExpectation != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.DeleteByParticipantIDAndProductID method")
	}

	if len(mmDeleteByParticipantIDAndProductID.expectations) > 0 {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.DeleteByParticipantIDAndProductID method")
	}

	mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID = f
	mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantDomainService.DeleteByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) When(ctx context.Context, participantID int64, productID int64) *ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation {
	if mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation{
		mock:               mmDeleteByParticipantIDAndProductID.mock,
		params:             &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams{ctx, participantID, productID},
		expectationOrigins: ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantIDAndProductID.expectations = append(mmDeleteByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.DeleteByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.DeleteByParticipantIDAndProductID should be invoked
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Times(n uint64) *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID {
	if n == 0 {
		mmDeleteByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantIDAndProductID.expectedInvocations, n)
	mmDeleteByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDAndProductID
}

func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmDeleteByParticipantIDAndProductID.expectations) == 0 && mmDeleteByParticipantIDAndProductID.defaultExpectation == nil && mmDeleteByParticipantIDAndProductID.mock.funcDeleteByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantIDAndProductID.mock.afterDeleteByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantIDAndProductID implements mm_service.ParticipantDomainService
func (mmDeleteByParticipantIDAndProductID *ParticipantDomainServiceMock) DeleteByParticipantIDAndProductID(ctx context.Context, participantID int64, productID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantIDAndProductID.beforeDeleteByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantIDAndProductID.afterDeleteByParticipantIDAndProductIDCounter, 1)

	mmDeleteByParticipantIDAndProductID.t.Helper()

	if mmDeleteByParticipantIDAndProductID.inspectFuncDeleteByParticipantIDAndProductID != nil {
		mmDeleteByParticipantIDAndProductID.inspectFuncDeleteByParticipantIDAndProductID(ctx, participantID, productID)
	}

	mm_params := ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams{ctx, participantID, productID}

	// Record call args
	mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.mutex.Lock()
	mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.callArgs = append(mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams{ctx, participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDeleteByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmDeleteByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantIDAndProductID.DeleteByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantIDAndProductID.funcDeleteByParticipantIDAndProductID != nil {
		return mmDeleteByParticipantIDAndProductID.funcDeleteByParticipantIDAndProductID(ctx, participantID, productID)
	}
	mmDeleteByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID. %v %v %v", ctx, participantID, productID)
	return
}

// DeleteByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID invocations
func (mmDeleteByParticipantIDAndProductID *ParticipantDomainServiceMock) DeleteByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantIDAndProductID.afterDeleteByParticipantIDAndProductIDCounter)
}

// DeleteByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID invocations
func (mmDeleteByParticipantIDAndProductID *ParticipantDomainServiceMock) DeleteByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantIDAndProductID.beforeDeleteByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantIDAndProductID *mParticipantDomainServiceMockDeleteByParticipantIDAndProductID) Calls() []*ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams {
	mmDeleteByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockDeleteByParticipantIDAndProductIDParams, len(mmDeleteByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmDeleteByParticipantIDAndProductID.callArgs)

	mmDeleteByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantIDAndProductIDDone returns true if the count of the DeleteByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockDeleteByParticipantIDAndProductIDDone() bool {
	if m.DeleteByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockDeleteByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockDeleteByParticipantIDAndProductIDInspect() {
	for _, e := range m.DeleteByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantIDAndProductIDMock.defaultExpectation != nil && afterDeleteByParticipantIDAndProductIDCounter < 1 {
		if m.DeleteByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID at\n%s", m.DeleteByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID at\n%s with params: %#v", m.DeleteByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantIDAndProductID != nil && afterDeleteByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID at\n%s", m.funcDeleteByParticipantIDAndProductIDOrigin)
	}

	if !m.DeleteByParticipantIDAndProductIDMock.invocationsDone() && afterDeleteByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.DeleteByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantIDAndProductIDMock.expectedInvocations), m.DeleteByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterDeleteByParticipantIDAndProductIDCounter)
	}
}

type mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation
	expectations       []*ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation

	callArgs []*ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation specifies expectation struct of the ParticipantDomainService.DeleteByUserIDAndGroupIDs
type ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams
	paramPtrs          *ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs
	expectationOrigins ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectationOrigins
	results            *ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams contains parameters of the ParticipantDomainService.DeleteByUserIDAndGroupIDs
type ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams struct {
	ctx      context.Context
	userID   int64
	groupIDs []int64
}

// ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs contains pointers to parameters of the ParticipantDomainService.DeleteByUserIDAndGroupIDs
type ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs struct {
	ctx      *context.Context
	userID   *int64
	groupIDs *[]int64
}

// ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsResults contains results of the ParticipantDomainService.DeleteByUserIDAndGroupIDs
type ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsResults struct {
	err error
}

// ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsOrigins contains origins of expectations of the ParticipantDomainService.DeleteByUserIDAndGroupIDs
type ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectationOrigins struct {
	origin         string
	originCtx      string
	originUserID   string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Optional() *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	mmDeleteByUserIDAndGroupIDs.optional = true
	return mmDeleteByUserIDAndGroupIDs
}

// Expect sets up expected params for ParticipantDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Expect(ctx context.Context, userID int64, groupIDs []int64) *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndGroupIDs.defaultExpectation.params = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndGroupIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndGroupIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndGroupIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) ExpectUserIDParam2(userID int64) *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for ParticipantDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) ExpectGroupIDsParam3(groupIDs []int64) *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Inspect(f func(ctx context.Context, userID int64, groupIDs []int64)) *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs")
	}

	mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs = f

	return mmDeleteByUserIDAndGroupIDs
}

// Return sets up results that will be returned by ParticipantDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Return(err error) *ParticipantDomainServiceMock {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{mock: mmDeleteByUserIDAndGroupIDs.mock}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.results = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsResults{err}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// Set uses given function f to mock the ParticipantDomainService.DeleteByUserIDAndGroupIDs method
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Set(f func(ctx context.Context, userID int64, groupIDs []int64) (err error)) *ParticipantDomainServiceMock {
	if mmDeleteByUserIDAndGroupIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.DeleteByUserIDAndGroupIDs method")
	}

	if len(mmDeleteByUserIDAndGroupIDs.expectations) > 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.DeleteByUserIDAndGroupIDs method")
	}

	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs = f
	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// When sets expectation for the ParticipantDomainService.DeleteByUserIDAndGroupIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) When(ctx context.Context, userID int64, groupIDs []int64) *ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{
		mock:               mmDeleteByUserIDAndGroupIDs.mock,
		params:             &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs},
		expectationOrigins: ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndGroupIDs.expectations = append(mmDeleteByUserIDAndGroupIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.DeleteByUserIDAndGroupIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.DeleteByUserIDAndGroupIDs should be invoked
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Times(n uint64) *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs {
	if n == 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Times of ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations, n)
	mmDeleteByUserIDAndGroupIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs
}

func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndGroupIDs.expectations) == 0 && mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil && mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.mock.afterDeleteByUserIDAndGroupIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndGroupIDs implements mm_service.ParticipantDomainService
func (mmDeleteByUserIDAndGroupIDs *ParticipantDomainServiceMock) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter, 1)

	mmDeleteByUserIDAndGroupIDs.t.Helper()

	if mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}

	mm_params := ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

	// Record call args
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Lock()
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs = append(mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndGroupIDs.t.Fatal("No results are set for the ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs != nil {
		return mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}
	mmDeleteByUserIDAndGroupIDs.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs. %v %v %v", ctx, userID, groupIDs)
	return
}

// DeleteByUserIDAndGroupIDsAfterCounter returns a count of finished ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *ParticipantDomainServiceMock) DeleteByUserIDAndGroupIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter)
}

// DeleteByUserIDAndGroupIDsBeforeCounter returns a count of ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *ParticipantDomainServiceMock) DeleteByUserIDAndGroupIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndGroupIDs *mParticipantDomainServiceMockDeleteByUserIDAndGroupIDs) Calls() []*ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams {
	mmDeleteByUserIDAndGroupIDs.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockDeleteByUserIDAndGroupIDsParams, len(mmDeleteByUserIDAndGroupIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndGroupIDs.callArgs)

	mmDeleteByUserIDAndGroupIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndGroupIDsDone returns true if the count of the DeleteByUserIDAndGroupIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockDeleteByUserIDAndGroupIDsDone() bool {
	if m.DeleteByUserIDAndGroupIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndGroupIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndGroupIDsInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockDeleteByUserIDAndGroupIDsInspect() {
	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndGroupIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndGroupIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndGroupIDs != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s", m.funcDeleteByUserIDAndGroupIDsOrigin)
	}

	if !m.DeleteByUserIDAndGroupIDsMock.invocationsDone() && afterDeleteByUserIDAndGroupIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndGroupIDsMock.expectedInvocations), m.DeleteByUserIDAndGroupIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndGroupIDsCounter)
	}
}

type mParticipantDomainServiceMockDeleteByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation
	expectations       []*ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation

	callArgs []*ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantDomainService.DeleteByUserIDAndProductIDs
type ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams
	paramPtrs          *ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantDomainServiceMockDeleteByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams contains parameters of the ParticipantDomainService.DeleteByUserIDAndProductIDs
type ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams struct {
	ctx        context.Context
	userID     int64
	productIDs []int64
}

// ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantDomainService.DeleteByUserIDAndProductIDs
type ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParamPtrs struct {
	ctx        *context.Context
	userID     *int64
	productIDs *[]int64
}

// ParticipantDomainServiceMockDeleteByUserIDAndProductIDsResults contains results of the ParticipantDomainService.DeleteByUserIDAndProductIDs
type ParticipantDomainServiceMockDeleteByUserIDAndProductIDsResults struct {
	err error
}

// ParticipantDomainServiceMockDeleteByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantDomainService.DeleteByUserIDAndProductIDs
type ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originCtx        string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Optional() *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	mmDeleteByUserIDAndProductIDs.optional = true
	return mmDeleteByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantDomainService.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Expect(ctx context.Context, userID int64, productIDs []int64) *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndProductIDs.defaultExpectation.params = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndProductIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndProductIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantDomainService.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) ExpectUserIDParam2(userID int64) *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// ExpectProductIDsParam3 sets up expected param productIDs for ParticipantDomainService.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) ExpectProductIDsParam3(productIDs []int64) *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Inspect(f func(ctx context.Context, userID int64, productIDs []int64)) *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.inspectFuncDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs")
	}

	mmDeleteByUserIDAndProductIDs.mock.inspectFuncDeleteByUserIDAndProductIDs = f

	return mmDeleteByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantDomainService.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Return(err error) *ParticipantDomainServiceMock {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation{mock: mmDeleteByUserIDAndProductIDs.mock}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.results = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsResults{err}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantDomainService.DeleteByUserIDAndProductIDs method
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Set(f func(ctx context.Context, userID int64, productIDs []int64) (err error)) *ParticipantDomainServiceMock {
	if mmDeleteByUserIDAndProductIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.DeleteByUserIDAndProductIDs method")
	}

	if len(mmDeleteByUserIDAndProductIDs.expectations) > 0 {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.DeleteByUserIDAndProductIDs method")
	}

	mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs = f
	mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantDomainService.DeleteByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) When(ctx context.Context, userID int64, productIDs []int64) *ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation{
		mock:               mmDeleteByUserIDAndProductIDs.mock,
		params:             &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs},
		expectationOrigins: ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndProductIDs.expectations = append(mmDeleteByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.DeleteByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockDeleteByUserIDAndProductIDsExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockDeleteByUserIDAndProductIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.DeleteByUserIDAndProductIDs should be invoked
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Times(n uint64) *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs {
	if n == 0 {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndProductIDs.expectedInvocations, n)
	mmDeleteByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs
}

func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndProductIDs.expectations) == 0 && mmDeleteByUserIDAndProductIDs.defaultExpectation == nil && mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.mock.afterDeleteByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndProductIDs implements mm_service.ParticipantDomainService
func (mmDeleteByUserIDAndProductIDs *ParticipantDomainServiceMock) DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.beforeDeleteByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.afterDeleteByUserIDAndProductIDsCounter, 1)

	mmDeleteByUserIDAndProductIDs.t.Helper()

	if mmDeleteByUserIDAndProductIDs.inspectFuncDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.inspectFuncDeleteByUserIDAndProductIDs(ctx, userID, productIDs)
	}

	mm_params := ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs}

	// Record call args
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.mutex.Lock()
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.callArgs = append(mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndProductIDs.funcDeleteByUserIDAndProductIDs != nil {
		return mmDeleteByUserIDAndProductIDs.funcDeleteByUserIDAndProductIDs(ctx, userID, productIDs)
	}
	mmDeleteByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs. %v %v %v", ctx, userID, productIDs)
	return
}

// DeleteByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs invocations
func (mmDeleteByUserIDAndProductIDs *ParticipantDomainServiceMock) DeleteByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.afterDeleteByUserIDAndProductIDsCounter)
}

// DeleteByUserIDAndProductIDsBeforeCounter returns a count of ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs invocations
func (mmDeleteByUserIDAndProductIDs *ParticipantDomainServiceMock) DeleteByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.beforeDeleteByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndProductIDs *mParticipantDomainServiceMockDeleteByUserIDAndProductIDs) Calls() []*ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams {
	mmDeleteByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockDeleteByUserIDAndProductIDsParams, len(mmDeleteByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndProductIDs.callArgs)

	mmDeleteByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndProductIDsDone returns true if the count of the DeleteByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockDeleteByUserIDAndProductIDsDone() bool {
	if m.DeleteByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockDeleteByUserIDAndProductIDsInspect() {
	for _, e := range m.DeleteByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndProductIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndProductIDsCounter < 1 {
		if m.DeleteByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs at\n%s", m.DeleteByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs at\n%s with params: %#v", m.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndProductIDs != nil && afterDeleteByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs at\n%s", m.funcDeleteByUserIDAndProductIDsOrigin)
	}

	if !m.DeleteByUserIDAndProductIDsMock.invocationsDone() && afterDeleteByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.DeleteByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndProductIDsMock.expectedInvocations), m.DeleteByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndProductIDsCounter)
	}
}

type mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation
	expectations       []*ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation

	callArgs []*ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation specifies expectation struct of the ParticipantDomainService.DeleteByUserIDAndRoleIDs
type ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams
	paramPtrs          *ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs
	expectationOrigins ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectationOrigins
	results            *ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams contains parameters of the ParticipantDomainService.DeleteByUserIDAndRoleIDs
type ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams struct {
	ctx     context.Context
	userID  int64
	roleIDs []int64
}

// ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs contains pointers to parameters of the ParticipantDomainService.DeleteByUserIDAndRoleIDs
type ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs struct {
	ctx     *context.Context
	userID  *int64
	roleIDs *[]int64
}

// ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsResults contains results of the ParticipantDomainService.DeleteByUserIDAndRoleIDs
type ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsResults struct {
	err error
}

// ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsOrigins contains origins of expectations of the ParticipantDomainService.DeleteByUserIDAndRoleIDs
type ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originUserID  string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Optional() *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	mmDeleteByUserIDAndRoleIDs.optional = true
	return mmDeleteByUserIDAndRoleIDs
}

// Expect sets up expected params for ParticipantDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Expect(ctx context.Context, userID int64, roleIDs []int64) *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndRoleIDs.defaultExpectation.params = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndRoleIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndRoleIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndRoleIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) ExpectUserIDParam2(userID int64) *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for ParticipantDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) ExpectRoleIDsParam3(roleIDs []int64) *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Inspect(f func(ctx context.Context, userID int64, roleIDs []int64)) *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs")
	}

	mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs = f

	return mmDeleteByUserIDAndRoleIDs
}

// Return sets up results that will be returned by ParticipantDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Return(err error) *ParticipantDomainServiceMock {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{mock: mmDeleteByUserIDAndRoleIDs.mock}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.results = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsResults{err}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// Set uses given function f to mock the ParticipantDomainService.DeleteByUserIDAndRoleIDs method
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Set(f func(ctx context.Context, userID int64, roleIDs []int64) (err error)) *ParticipantDomainServiceMock {
	if mmDeleteByUserIDAndRoleIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.DeleteByUserIDAndRoleIDs method")
	}

	if len(mmDeleteByUserIDAndRoleIDs.expectations) > 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.DeleteByUserIDAndRoleIDs method")
	}

	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs = f
	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// When sets expectation for the ParticipantDomainService.DeleteByUserIDAndRoleIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) When(ctx context.Context, userID int64, roleIDs []int64) *ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{
		mock:               mmDeleteByUserIDAndRoleIDs.mock,
		params:             &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs},
		expectationOrigins: ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndRoleIDs.expectations = append(mmDeleteByUserIDAndRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.DeleteByUserIDAndRoleIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.DeleteByUserIDAndRoleIDs should be invoked
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Times(n uint64) *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs {
	if n == 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Times of ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations, n)
	mmDeleteByUserIDAndRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs
}

func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndRoleIDs.expectations) == 0 && mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil && mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.mock.afterDeleteByUserIDAndRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndRoleIDs implements mm_service.ParticipantDomainService
func (mmDeleteByUserIDAndRoleIDs *ParticipantDomainServiceMock) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter, 1)

	mmDeleteByUserIDAndRoleIDs.t.Helper()

	if mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}

	mm_params := ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

	// Record call args
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Lock()
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs = append(mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndRoleIDs.t.Fatal("No results are set for the ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs != nil {
		return mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}
	mmDeleteByUserIDAndRoleIDs.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs. %v %v %v", ctx, userID, roleIDs)
	return
}

// DeleteByUserIDAndRoleIDsAfterCounter returns a count of finished ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *ParticipantDomainServiceMock) DeleteByUserIDAndRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter)
}

// DeleteByUserIDAndRoleIDsBeforeCounter returns a count of ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *ParticipantDomainServiceMock) DeleteByUserIDAndRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndRoleIDs *mParticipantDomainServiceMockDeleteByUserIDAndRoleIDs) Calls() []*ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams {
	mmDeleteByUserIDAndRoleIDs.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockDeleteByUserIDAndRoleIDsParams, len(mmDeleteByUserIDAndRoleIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndRoleIDs.callArgs)

	mmDeleteByUserIDAndRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndRoleIDsDone returns true if the count of the DeleteByUserIDAndRoleIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockDeleteByUserIDAndRoleIDsDone() bool {
	if m.DeleteByUserIDAndRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndRoleIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndRoleIDsInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockDeleteByUserIDAndRoleIDsInspect() {
	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndRoleIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndRoleIDs != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s", m.funcDeleteByUserIDAndRoleIDsOrigin)
	}

	if !m.DeleteByUserIDAndRoleIDsMock.invocationsDone() && afterDeleteByUserIDAndRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndRoleIDsMock.expectedInvocations), m.DeleteByUserIDAndRoleIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndRoleIDsCounter)
	}
}

type mParticipantDomainServiceMockExistByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantDomainService.ExistByParticipantIDAndProductID
type ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantDomainServiceMockExistByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockExistByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams contains parameters of the ParticipantDomainService.ExistByParticipantIDAndProductID
type ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantDomainServiceMockExistByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.ExistByParticipantIDAndProductID
type ParticipantDomainServiceMockExistByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantDomainServiceMockExistByParticipantIDAndProductIDResults contains results of the ParticipantDomainService.ExistByParticipantIDAndProductID
type ParticipantDomainServiceMockExistByParticipantIDAndProductIDResults struct {
	b1  bool
	err error
}

// ParticipantDomainServiceMockExistByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantDomainService.ExistByParticipantIDAndProductID
type ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Optional() *mParticipantDomainServiceMockExistByParticipantIDAndProductID {
	mmExistByParticipantIDAndProductID.optional = true
	return mmExistByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantDomainService.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantDomainServiceMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmExistByParticipantIDAndProductID.defaultExpectation.params = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams{participantID, productID}
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmExistByParticipantIDAndProductID.defaultExpectation.params) {
			mmExistByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmExistByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantDomainService.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantDomainServiceMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDParamPtrs{}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmExistByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantDomainService.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantDomainServiceMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDParamPtrs{}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmExistByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantDomainServiceMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.inspectFuncExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.ExistByParticipantIDAndProductID")
	}

	mmExistByParticipantIDAndProductID.mock.inspectFuncExistByParticipantIDAndProductID = f

	return mmExistByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantDomainService.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Return(b1 bool, err error) *ParticipantDomainServiceMock {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation{mock: mmExistByParticipantIDAndProductID.mock}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.results = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDResults{b1, err}
	mmExistByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.ExistByParticipantIDAndProductID method
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (b1 bool, err error)) *ParticipantDomainServiceMock {
	if mmExistByParticipantIDAndProductID.defaultExpectation != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.ExistByParticipantIDAndProductID method")
	}

	if len(mmExistByParticipantIDAndProductID.expectations) > 0 {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.ExistByParticipantIDAndProductID method")
	}

	mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID = f
	mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantDomainService.ExistByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation{
		mock:               mmExistByParticipantIDAndProductID.mock,
		params:             &ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByParticipantIDAndProductID.expectations = append(mmExistByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.ExistByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockExistByParticipantIDAndProductIDExpectation) Then(b1 bool, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockExistByParticipantIDAndProductIDResults{b1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.ExistByParticipantIDAndProductID should be invoked
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Times(n uint64) *mParticipantDomainServiceMockExistByParticipantIDAndProductID {
	if n == 0 {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.ExistByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByParticipantIDAndProductID.expectedInvocations, n)
	mmExistByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID
}

func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmExistByParticipantIDAndProductID.expectations) == 0 && mmExistByParticipantIDAndProductID.defaultExpectation == nil && mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.mock.afterExistByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByParticipantIDAndProductID implements mm_service.ParticipantDomainService
func (mmExistByParticipantIDAndProductID *ParticipantDomainServiceMock) ExistByParticipantIDAndProductID(participantID int64, productID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.beforeExistByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.afterExistByParticipantIDAndProductIDCounter, 1)

	mmExistByParticipantIDAndProductID.t.Helper()

	if mmExistByParticipantIDAndProductID.inspectFuncExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.inspectFuncExistByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.mutex.Lock()
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.callArgs = append(mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmExistByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmExistByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.ExistByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.ExistByParticipantIDAndProductID")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByParticipantIDAndProductID.funcExistByParticipantIDAndProductID != nil {
		return mmExistByParticipantIDAndProductID.funcExistByParticipantIDAndProductID(participantID, productID)
	}
	mmExistByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// ExistByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.ExistByParticipantIDAndProductID invocations
func (mmExistByParticipantIDAndProductID *ParticipantDomainServiceMock) ExistByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.afterExistByParticipantIDAndProductIDCounter)
}

// ExistByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.ExistByParticipantIDAndProductID invocations
func (mmExistByParticipantIDAndProductID *ParticipantDomainServiceMock) ExistByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.beforeExistByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByParticipantIDAndProductID *mParticipantDomainServiceMockExistByParticipantIDAndProductID) Calls() []*ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams {
	mmExistByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockExistByParticipantIDAndProductIDParams, len(mmExistByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmExistByParticipantIDAndProductID.callArgs)

	mmExistByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockExistByParticipantIDAndProductIDDone returns true if the count of the ExistByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockExistByParticipantIDAndProductIDDone() bool {
	if m.ExistByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockExistByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockExistByParticipantIDAndProductIDInspect() {
	for _, e := range m.ExistByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterExistByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByParticipantIDAndProductIDMock.defaultExpectation != nil && afterExistByParticipantIDAndProductIDCounter < 1 {
		if m.ExistByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID at\n%s", m.ExistByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID at\n%s with params: %#v", m.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.ExistByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByParticipantIDAndProductID != nil && afterExistByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID at\n%s", m.funcExistByParticipantIDAndProductIDOrigin)
	}

	if !m.ExistByParticipantIDAndProductIDMock.invocationsDone() && afterExistByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.ExistByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByParticipantIDAndProductIDMock.expectedInvocations), m.ExistByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterExistByParticipantIDAndProductIDCounter)
	}
}

type mParticipantDomainServiceMockGetByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantDomainService.GetByParticipantIDAndProductID
type ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantDomainServiceMockGetByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockGetByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams contains parameters of the ParticipantDomainService.GetByParticipantIDAndProductID
type ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantDomainServiceMockGetByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.GetByParticipantIDAndProductID
type ParticipantDomainServiceMockGetByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantDomainServiceMockGetByParticipantIDAndProductIDResults contains results of the ParticipantDomainService.GetByParticipantIDAndProductID
type ParticipantDomainServiceMockGetByParticipantIDAndProductIDResults struct {
	p1  participantentity.ParticipantFull
	err error
}

// ParticipantDomainServiceMockGetByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantDomainService.GetByParticipantIDAndProductID
type ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Optional() *mParticipantDomainServiceMockGetByParticipantIDAndProductID {
	mmGetByParticipantIDAndProductID.optional = true
	return mmGetByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantDomainService.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantDomainServiceMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantIDAndProductID.defaultExpectation.params = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams{participantID, productID}
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantIDAndProductID.defaultExpectation.params) {
			mmGetByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantDomainService.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantDomainServiceMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantDomainService.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantDomainServiceMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantDomainServiceMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.inspectFuncGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetByParticipantIDAndProductID")
	}

	mmGetByParticipantIDAndProductID.mock.inspectFuncGetByParticipantIDAndProductID = f

	return mmGetByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantDomainService.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Return(p1 participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation{mock: mmGetByParticipantIDAndProductID.mock}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.results = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDResults{p1, err}
	mmGetByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetByParticipantIDAndProductID method
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (p1 participantentity.ParticipantFull, err error)) *ParticipantDomainServiceMock {
	if mmGetByParticipantIDAndProductID.defaultExpectation != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetByParticipantIDAndProductID method")
	}

	if len(mmGetByParticipantIDAndProductID.expectations) > 0 {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetByParticipantIDAndProductID method")
	}

	mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID = f
	mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantDomainService.GetByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation{
		mock:               mmGetByParticipantIDAndProductID.mock,
		params:             &ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantIDAndProductID.expectations = append(mmGetByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetByParticipantIDAndProductIDExpectation) Then(p1 participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetByParticipantIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetByParticipantIDAndProductID should be invoked
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Times(n uint64) *mParticipantDomainServiceMockGetByParticipantIDAndProductID {
	if n == 0 {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantIDAndProductID.expectedInvocations, n)
	mmGetByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID
}

func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmGetByParticipantIDAndProductID.expectations) == 0 && mmGetByParticipantIDAndProductID.defaultExpectation == nil && mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.mock.afterGetByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantIDAndProductID implements mm_service.ParticipantDomainService
func (mmGetByParticipantIDAndProductID *ParticipantDomainServiceMock) GetByParticipantIDAndProductID(participantID int64, productID int64) (p1 participantentity.ParticipantFull, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.beforeGetByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.afterGetByParticipantIDAndProductIDCounter, 1)

	mmGetByParticipantIDAndProductID.t.Helper()

	if mmGetByParticipantIDAndProductID.inspectFuncGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.inspectFuncGetByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.mutex.Lock()
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.callArgs = append(mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetByParticipantIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByParticipantIDAndProductID.funcGetByParticipantIDAndProductID != nil {
		return mmGetByParticipantIDAndProductID.funcGetByParticipantIDAndProductID(participantID, productID)
	}
	mmGetByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// GetByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.GetByParticipantIDAndProductID invocations
func (mmGetByParticipantIDAndProductID *ParticipantDomainServiceMock) GetByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.afterGetByParticipantIDAndProductIDCounter)
}

// GetByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.GetByParticipantIDAndProductID invocations
func (mmGetByParticipantIDAndProductID *ParticipantDomainServiceMock) GetByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.beforeGetByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantIDAndProductID *mParticipantDomainServiceMockGetByParticipantIDAndProductID) Calls() []*ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams {
	mmGetByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetByParticipantIDAndProductIDParams, len(mmGetByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmGetByParticipantIDAndProductID.callArgs)

	mmGetByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDAndProductIDDone returns true if the count of the GetByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetByParticipantIDAndProductIDDone() bool {
	if m.GetByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockGetByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetByParticipantIDAndProductIDInspect() {
	for _, e := range m.GetByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDAndProductIDMock.defaultExpectation != nil && afterGetByParticipantIDAndProductIDCounter < 1 {
		if m.GetByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByParticipantIDAndProductID at\n%s", m.GetByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByParticipantIDAndProductID at\n%s with params: %#v", m.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantIDAndProductID != nil && afterGetByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByParticipantIDAndProductID at\n%s", m.funcGetByParticipantIDAndProductIDOrigin)
	}

	if !m.GetByParticipantIDAndProductIDMock.invocationsDone() && afterGetByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDAndProductIDMock.expectedInvocations), m.GetByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterGetByParticipantIDAndProductIDCounter)
	}
}

type mParticipantDomainServiceMockGetByProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetByProductIDExpectation
	expectations       []*ParticipantDomainServiceMockGetByProductIDExpectation

	callArgs []*ParticipantDomainServiceMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetByProductIDExpectation specifies expectation struct of the ParticipantDomainService.GetByProductID
type ParticipantDomainServiceMockGetByProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetByProductIDParams
	paramPtrs          *ParticipantDomainServiceMockGetByProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetByProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetByProductIDParams contains parameters of the ParticipantDomainService.GetByProductID
type ParticipantDomainServiceMockGetByProductIDParams struct {
	productID int64
}

// ParticipantDomainServiceMockGetByProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.GetByProductID
type ParticipantDomainServiceMockGetByProductIDParamPtrs struct {
	productID *int64
}

// ParticipantDomainServiceMockGetByProductIDResults contains results of the ParticipantDomainService.GetByProductID
type ParticipantDomainServiceMockGetByProductIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantDomainServiceMockGetByProductIDOrigins contains origins of expectations of the ParticipantDomainService.GetByProductID
type ParticipantDomainServiceMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Optional() *mParticipantDomainServiceMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for ParticipantDomainService.GetByProductID
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Expect(productID int64) *mParticipantDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantDomainServiceMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &ParticipantDomainServiceMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantDomainService.GetByProductID
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) ExpectProductIDParam1(productID int64) *mParticipantDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantDomainServiceMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetByProductID
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Inspect(f func(productID int64)) *mParticipantDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by ParticipantDomainService.GetByProductID
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Return(pa1 []participantentity.Participant, err error) *ParticipantDomainServiceMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantDomainServiceMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &ParticipantDomainServiceMockGetByProductIDResults{pa1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetByProductID method
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Set(f func(productID int64) (pa1 []participantentity.Participant, err error)) *ParticipantDomainServiceMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the ParticipantDomainService.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) When(productID int64) *ParticipantDomainServiceMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &ParticipantDomainServiceMockGetByProductIDParams{productID},
		expectationOrigins: ParticipantDomainServiceMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetByProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetByProductIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetByProductID should be invoked
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Times(n uint64) *mParticipantDomainServiceMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_service.ParticipantDomainService
func (mmGetByProductID *ParticipantDomainServiceMock) GetByProductID(productID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := ParticipantDomainServiceMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("ParticipantDomainServiceMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("ParticipantDomainServiceMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.GetByProductID invocations
func (mmGetByProductID *ParticipantDomainServiceMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.GetByProductID invocations
func (mmGetByProductID *ParticipantDomainServiceMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mParticipantDomainServiceMockGetByProductID) Calls() []*ParticipantDomainServiceMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mParticipantDomainServiceMockGetByUserID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetByUserIDExpectation
	expectations       []*ParticipantDomainServiceMockGetByUserIDExpectation

	callArgs []*ParticipantDomainServiceMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetByUserIDExpectation specifies expectation struct of the ParticipantDomainService.GetByUserID
type ParticipantDomainServiceMockGetByUserIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetByUserIDParams
	paramPtrs          *ParticipantDomainServiceMockGetByUserIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetByUserIDExpectationOrigins
	results            *ParticipantDomainServiceMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetByUserIDParams contains parameters of the ParticipantDomainService.GetByUserID
type ParticipantDomainServiceMockGetByUserIDParams struct {
	userID int64
}

// ParticipantDomainServiceMockGetByUserIDParamPtrs contains pointers to parameters of the ParticipantDomainService.GetByUserID
type ParticipantDomainServiceMockGetByUserIDParamPtrs struct {
	userID *int64
}

// ParticipantDomainServiceMockGetByUserIDResults contains results of the ParticipantDomainService.GetByUserID
type ParticipantDomainServiceMockGetByUserIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantDomainServiceMockGetByUserIDOrigins contains origins of expectations of the ParticipantDomainService.GetByUserID
type ParticipantDomainServiceMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Optional() *mParticipantDomainServiceMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for ParticipantDomainService.GetByUserID
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Expect(userID int64) *mParticipantDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &ParticipantDomainServiceMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantDomainService.GetByUserID
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) ExpectUserIDParam1(userID int64) *mParticipantDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetByUserID
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Inspect(f func(userID int64)) *mParticipantDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by ParticipantDomainService.GetByUserID
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Return(pa1 []participantentity.Participant, err error) *ParticipantDomainServiceMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &ParticipantDomainServiceMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetByUserID method
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Set(f func(userID int64) (pa1 []participantentity.Participant, err error)) *ParticipantDomainServiceMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the ParticipantDomainService.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) When(userID int64) *ParticipantDomainServiceMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &ParticipantDomainServiceMockGetByUserIDParams{userID},
		expectationOrigins: ParticipantDomainServiceMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetByUserID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetByUserIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetByUserID should be invoked
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Times(n uint64) *mParticipantDomainServiceMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_service.ParticipantDomainService
func (mmGetByUserID *ParticipantDomainServiceMock) GetByUserID(userID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := ParticipantDomainServiceMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("ParticipantDomainServiceMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("ParticipantDomainServiceMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished ParticipantDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *ParticipantDomainServiceMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of ParticipantDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *ParticipantDomainServiceMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mParticipantDomainServiceMockGetByUserID) Calls() []*ParticipantDomainServiceMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mParticipantDomainServiceMockGetByUserIDAndProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation
	expectations       []*ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation

	callArgs []*ParticipantDomainServiceMockGetByUserIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation specifies expectation struct of the ParticipantDomainService.GetByUserIDAndProductID
type ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetByUserIDAndProductIDParams
	paramPtrs          *ParticipantDomainServiceMockGetByUserIDAndProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetByUserIDAndProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockGetByUserIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDParams contains parameters of the ParticipantDomainService.GetByUserIDAndProductID
type ParticipantDomainServiceMockGetByUserIDAndProductIDParams struct {
	userID    int64
	productID int64
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.GetByUserIDAndProductID
type ParticipantDomainServiceMockGetByUserIDAndProductIDParamPtrs struct {
	userID    *int64
	productID *int64
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDResults contains results of the ParticipantDomainService.GetByUserIDAndProductID
type ParticipantDomainServiceMockGetByUserIDAndProductIDResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDOrigins contains origins of expectations of the ParticipantDomainService.GetByUserIDAndProductID
type ParticipantDomainServiceMockGetByUserIDAndProductIDExpectationOrigins struct {
	origin          string
	originUserID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Optional() *mParticipantDomainServiceMockGetByUserIDAndProductID {
	mmGetByUserIDAndProductID.optional = true
	return mmGetByUserIDAndProductID
}

// Expect sets up expected params for ParticipantDomainService.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Expect(userID int64, productID int64) *mParticipantDomainServiceMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndProductID.defaultExpectation.params = &ParticipantDomainServiceMockGetByUserIDAndProductIDParams{userID, productID}
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndProductID.defaultExpectation.params) {
			mmGetByUserIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndProductID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantDomainService.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) ExpectUserIDParam1(userID int64) *mParticipantDomainServiceMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.params != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Expect")
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByUserIDAndProductIDParamPtrs{}
	}
	mmGetByUserIDAndProductID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantDomainService.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantDomainServiceMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.params != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Expect")
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByUserIDAndProductIDParamPtrs{}
	}
	mmGetByUserIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Inspect(f func(userID int64, productID int64)) *mParticipantDomainServiceMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.inspectFuncGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetByUserIDAndProductID")
	}

	mmGetByUserIDAndProductID.mock.inspectFuncGetByUserIDAndProductID = f

	return mmGetByUserIDAndProductID
}

// Return sets up results that will be returned by ParticipantDomainService.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Return(p1 participantentity.Participant, err error) *ParticipantDomainServiceMock {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation{mock: mmGetByUserIDAndProductID.mock}
	}
	mmGetByUserIDAndProductID.defaultExpectation.results = &ParticipantDomainServiceMockGetByUserIDAndProductIDResults{p1, err}
	mmGetByUserIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetByUserIDAndProductID method
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Set(f func(userID int64, productID int64) (p1 participantentity.Participant, err error)) *ParticipantDomainServiceMock {
	if mmGetByUserIDAndProductID.defaultExpectation != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetByUserIDAndProductID method")
	}

	if len(mmGetByUserIDAndProductID.expectations) > 0 {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetByUserIDAndProductID method")
	}

	mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID = f
	mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID.mock
}

// When sets expectation for the ParticipantDomainService.GetByUserIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) When(userID int64, productID int64) *ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation{
		mock:               mmGetByUserIDAndProductID.mock,
		params:             &ParticipantDomainServiceMockGetByUserIDAndProductIDParams{userID, productID},
		expectationOrigins: ParticipantDomainServiceMockGetByUserIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndProductID.expectations = append(mmGetByUserIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetByUserIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetByUserIDAndProductIDExpectation) Then(p1 participantentity.Participant, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetByUserIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetByUserIDAndProductID should be invoked
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Times(n uint64) *mParticipantDomainServiceMockGetByUserIDAndProductID {
	if n == 0 {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetByUserIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndProductID.expectedInvocations, n)
	mmGetByUserIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID
}

func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) invocationsDone() bool {
	if len(mmGetByUserIDAndProductID.expectations) == 0 && mmGetByUserIDAndProductID.defaultExpectation == nil && mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.mock.afterGetByUserIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndProductID implements mm_service.ParticipantDomainService
func (mmGetByUserIDAndProductID *ParticipantDomainServiceMock) GetByUserIDAndProductID(userID int64, productID int64) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndProductID.beforeGetByUserIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndProductID.afterGetByUserIDAndProductIDCounter, 1)

	mmGetByUserIDAndProductID.t.Helper()

	if mmGetByUserIDAndProductID.inspectFuncGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.inspectFuncGetByUserIDAndProductID(userID, productID)
	}

	mm_params := ParticipantDomainServiceMockGetByUserIDAndProductIDParams{userID, productID}

	// Record call args
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.mutex.Lock()
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.callArgs = append(mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.callArgs, &mm_params)
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetByUserIDAndProductIDParams{userID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetByUserIDAndProductID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByUserIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetByUserIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetByUserIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetByUserIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByUserIDAndProductID.funcGetByUserIDAndProductID != nil {
		return mmGetByUserIDAndProductID.funcGetByUserIDAndProductID(userID, productID)
	}
	mmGetByUserIDAndProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetByUserIDAndProductID. %v %v", userID, productID)
	return
}

// GetByUserIDAndProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.GetByUserIDAndProductID invocations
func (mmGetByUserIDAndProductID *ParticipantDomainServiceMock) GetByUserIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.afterGetByUserIDAndProductIDCounter)
}

// GetByUserIDAndProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.GetByUserIDAndProductID invocations
func (mmGetByUserIDAndProductID *ParticipantDomainServiceMock) GetByUserIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.beforeGetByUserIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetByUserIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndProductID *mParticipantDomainServiceMockGetByUserIDAndProductID) Calls() []*ParticipantDomainServiceMockGetByUserIDAndProductIDParams {
	mmGetByUserIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetByUserIDAndProductIDParams, len(mmGetByUserIDAndProductID.callArgs))
	copy(argCopy, mmGetByUserIDAndProductID.callArgs)

	mmGetByUserIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndProductIDDone returns true if the count of the GetByUserIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetByUserIDAndProductIDDone() bool {
	if m.GetByUserIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndProductIDMock.invocationsDone()
}

// MinimockGetByUserIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetByUserIDAndProductIDInspect() {
	for _, e := range m.GetByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndProductIDMock.defaultExpectation != nil && afterGetByUserIDAndProductIDCounter < 1 {
		if m.GetByUserIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductID at\n%s", m.GetByUserIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductID at\n%s with params: %#v", m.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndProductID != nil && afterGetByUserIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductID at\n%s", m.funcGetByUserIDAndProductIDOrigin)
	}

	if !m.GetByUserIDAndProductIDMock.invocationsDone() && afterGetByUserIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetByUserIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndProductIDMock.expectedInvocations), m.GetByUserIDAndProductIDMock.expectedInvocationsOrigin, afterGetByUserIDAndProductIDCounter)
	}
}

type mParticipantDomainServiceMockGetByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation
	expectations       []*ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation

	callArgs []*ParticipantDomainServiceMockGetByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantDomainService.GetByUserIDAndProductIDs
type ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetByUserIDAndProductIDsParams
	paramPtrs          *ParticipantDomainServiceMockGetByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantDomainServiceMockGetByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDsParams contains parameters of the ParticipantDomainService.GetByUserIDAndProductIDs
type ParticipantDomainServiceMockGetByUserIDAndProductIDsParams struct {
	userID     int64
	productIDs []int64
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantDomainService.GetByUserIDAndProductIDs
type ParticipantDomainServiceMockGetByUserIDAndProductIDsParamPtrs struct {
	userID     *int64
	productIDs *[]int64
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDsResults contains results of the ParticipantDomainService.GetByUserIDAndProductIDs
type ParticipantDomainServiceMockGetByUserIDAndProductIDsResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantDomainServiceMockGetByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantDomainService.GetByUserIDAndProductIDs
type ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Optional() *mParticipantDomainServiceMockGetByUserIDAndProductIDs {
	mmGetByUserIDAndProductIDs.optional = true
	return mmGetByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantDomainService.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Expect(userID int64, productIDs []int64) *mParticipantDomainServiceMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndProductIDs.defaultExpectation.params = &ParticipantDomainServiceMockGetByUserIDAndProductIDsParams{userID, productIDs}
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndProductIDs.defaultExpectation.params) {
			mmGetByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndProductIDs
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantDomainService.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) ExpectUserIDParam1(userID int64) *mParticipantDomainServiceMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByUserIDAndProductIDsParamPtrs{}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductIDs
}

// ExpectProductIDsParam2 sets up expected param productIDs for ParticipantDomainService.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) ExpectProductIDsParam2(productIDs []int64) *mParticipantDomainServiceMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetByUserIDAndProductIDsParamPtrs{}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Inspect(f func(userID int64, productIDs []int64)) *mParticipantDomainServiceMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.inspectFuncGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetByUserIDAndProductIDs")
	}

	mmGetByUserIDAndProductIDs.mock.inspectFuncGetByUserIDAndProductIDs = f

	return mmGetByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantDomainService.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Return(pa1 []participantentity.Participant, err error) *ParticipantDomainServiceMock {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation{mock: mmGetByUserIDAndProductIDs.mock}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.results = &ParticipantDomainServiceMockGetByUserIDAndProductIDsResults{pa1, err}
	mmGetByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetByUserIDAndProductIDs method
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Set(f func(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error)) *ParticipantDomainServiceMock {
	if mmGetByUserIDAndProductIDs.defaultExpectation != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetByUserIDAndProductIDs method")
	}

	if len(mmGetByUserIDAndProductIDs.expectations) > 0 {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetByUserIDAndProductIDs method")
	}

	mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs = f
	mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantDomainService.GetByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) When(userID int64, productIDs []int64) *ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation{
		mock:               mmGetByUserIDAndProductIDs.mock,
		params:             &ParticipantDomainServiceMockGetByUserIDAndProductIDsParams{userID, productIDs},
		expectationOrigins: ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndProductIDs.expectations = append(mmGetByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetByUserIDAndProductIDsExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetByUserIDAndProductIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetByUserIDAndProductIDs should be invoked
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Times(n uint64) *mParticipantDomainServiceMockGetByUserIDAndProductIDs {
	if n == 0 {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndProductIDs.expectedInvocations, n)
	mmGetByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs
}

func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmGetByUserIDAndProductIDs.expectations) == 0 && mmGetByUserIDAndProductIDs.defaultExpectation == nil && mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.mock.afterGetByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndProductIDs implements mm_service.ParticipantDomainService
func (mmGetByUserIDAndProductIDs *ParticipantDomainServiceMock) GetByUserIDAndProductIDs(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.beforeGetByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.afterGetByUserIDAndProductIDsCounter, 1)

	mmGetByUserIDAndProductIDs.t.Helper()

	if mmGetByUserIDAndProductIDs.inspectFuncGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.inspectFuncGetByUserIDAndProductIDs(userID, productIDs)
	}

	mm_params := ParticipantDomainServiceMockGetByUserIDAndProductIDsParams{userID, productIDs}

	// Record call args
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.mutex.Lock()
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.callArgs = append(mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetByUserIDAndProductIDsParams{userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmGetByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndProductIDs.t.Errorf("ParticipantDomainServiceMock.GetByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetByUserIDAndProductIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserIDAndProductIDs.funcGetByUserIDAndProductIDs != nil {
		return mmGetByUserIDAndProductIDs.funcGetByUserIDAndProductIDs(userID, productIDs)
	}
	mmGetByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetByUserIDAndProductIDs. %v %v", userID, productIDs)
	return
}

// GetByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantDomainServiceMock.GetByUserIDAndProductIDs invocations
func (mmGetByUserIDAndProductIDs *ParticipantDomainServiceMock) GetByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.afterGetByUserIDAndProductIDsCounter)
}

// GetByUserIDAndProductIDsBeforeCounter returns a count of ParticipantDomainServiceMock.GetByUserIDAndProductIDs invocations
func (mmGetByUserIDAndProductIDs *ParticipantDomainServiceMock) GetByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.beforeGetByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndProductIDs *mParticipantDomainServiceMockGetByUserIDAndProductIDs) Calls() []*ParticipantDomainServiceMockGetByUserIDAndProductIDsParams {
	mmGetByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetByUserIDAndProductIDsParams, len(mmGetByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmGetByUserIDAndProductIDs.callArgs)

	mmGetByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndProductIDsDone returns true if the count of the GetByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetByUserIDAndProductIDsDone() bool {
	if m.GetByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockGetByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetByUserIDAndProductIDsInspect() {
	for _, e := range m.GetByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndProductIDsMock.defaultExpectation != nil && afterGetByUserIDAndProductIDsCounter < 1 {
		if m.GetByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductIDs at\n%s", m.GetByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductIDs at\n%s with params: %#v", m.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndProductIDs != nil && afterGetByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetByUserIDAndProductIDs at\n%s", m.funcGetByUserIDAndProductIDsOrigin)
	}

	if !m.GetByUserIDAndProductIDsMock.invocationsDone() && afterGetByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndProductIDsMock.expectedInvocations), m.GetByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterGetByUserIDAndProductIDsCounter)
	}
}

type mParticipantDomainServiceMockGetOwnersByProductIDs struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetOwnersByProductIDsExpectation
	expectations       []*ParticipantDomainServiceMockGetOwnersByProductIDsExpectation

	callArgs []*ParticipantDomainServiceMockGetOwnersByProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetOwnersByProductIDsExpectation specifies expectation struct of the ParticipantDomainService.GetOwnersByProductIDs
type ParticipantDomainServiceMockGetOwnersByProductIDsExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetOwnersByProductIDsParams
	paramPtrs          *ParticipantDomainServiceMockGetOwnersByProductIDsParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetOwnersByProductIDsExpectationOrigins
	results            *ParticipantDomainServiceMockGetOwnersByProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetOwnersByProductIDsParams contains parameters of the ParticipantDomainService.GetOwnersByProductIDs
type ParticipantDomainServiceMockGetOwnersByProductIDsParams struct {
	productIDs []int64
}

// ParticipantDomainServiceMockGetOwnersByProductIDsParamPtrs contains pointers to parameters of the ParticipantDomainService.GetOwnersByProductIDs
type ParticipantDomainServiceMockGetOwnersByProductIDsParamPtrs struct {
	productIDs *[]int64
}

// ParticipantDomainServiceMockGetOwnersByProductIDsResults contains results of the ParticipantDomainService.GetOwnersByProductIDs
type ParticipantDomainServiceMockGetOwnersByProductIDsResults struct {
	m1  map[int64][]productentity.Owner
	err error
}

// ParticipantDomainServiceMockGetOwnersByProductIDsOrigins contains origins of expectations of the ParticipantDomainService.GetOwnersByProductIDs
type ParticipantDomainServiceMockGetOwnersByProductIDsExpectationOrigins struct {
	origin           string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Optional() *mParticipantDomainServiceMockGetOwnersByProductIDs {
	mmGetOwnersByProductIDs.optional = true
	return mmGetOwnersByProductIDs
}

// Expect sets up expected params for ParticipantDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Expect(productIDs []int64) *mParticipantDomainServiceMockGetOwnersByProductIDs {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	if mmGetOwnersByProductIDs.defaultExpectation == nil {
		mmGetOwnersByProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetOwnersByProductIDsExpectation{}
	}

	if mmGetOwnersByProductIDs.defaultExpectation.paramPtrs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetOwnersByProductIDs mock is already set by ExpectParams functions")
	}

	mmGetOwnersByProductIDs.defaultExpectation.params = &ParticipantDomainServiceMockGetOwnersByProductIDsParams{productIDs}
	mmGetOwnersByProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetOwnersByProductIDs.expectations {
		if minimock.Equal(e.params, mmGetOwnersByProductIDs.defaultExpectation.params) {
			mmGetOwnersByProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetOwnersByProductIDs.defaultExpectation.params)
		}
	}

	return mmGetOwnersByProductIDs
}

// ExpectProductIDsParam1 sets up expected param productIDs for ParticipantDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) ExpectProductIDsParam1(productIDs []int64) *mParticipantDomainServiceMockGetOwnersByProductIDs {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	if mmGetOwnersByProductIDs.defaultExpectation == nil {
		mmGetOwnersByProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetOwnersByProductIDsExpectation{}
	}

	if mmGetOwnersByProductIDs.defaultExpectation.params != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetOwnersByProductIDs mock is already set by Expect")
	}

	if mmGetOwnersByProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetOwnersByProductIDs.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetOwnersByProductIDsParamPtrs{}
	}
	mmGetOwnersByProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmGetOwnersByProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmGetOwnersByProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Inspect(f func(productIDs []int64)) *mParticipantDomainServiceMockGetOwnersByProductIDs {
	if mmGetOwnersByProductIDs.mock.inspectFuncGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetOwnersByProductIDs")
	}

	mmGetOwnersByProductIDs.mock.inspectFuncGetOwnersByProductIDs = f

	return mmGetOwnersByProductIDs
}

// Return sets up results that will be returned by ParticipantDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Return(m1 map[int64][]productentity.Owner, err error) *ParticipantDomainServiceMock {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	if mmGetOwnersByProductIDs.defaultExpectation == nil {
		mmGetOwnersByProductIDs.defaultExpectation = &ParticipantDomainServiceMockGetOwnersByProductIDsExpectation{mock: mmGetOwnersByProductIDs.mock}
	}
	mmGetOwnersByProductIDs.defaultExpectation.results = &ParticipantDomainServiceMockGetOwnersByProductIDsResults{m1, err}
	mmGetOwnersByProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductIDs.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetOwnersByProductIDs method
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Set(f func(productIDs []int64) (m1 map[int64][]productentity.Owner, err error)) *ParticipantDomainServiceMock {
	if mmGetOwnersByProductIDs.defaultExpectation != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetOwnersByProductIDs method")
	}

	if len(mmGetOwnersByProductIDs.expectations) > 0 {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetOwnersByProductIDs method")
	}

	mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs = f
	mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDsOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductIDs.mock
}

// When sets expectation for the ParticipantDomainService.GetOwnersByProductIDs which will trigger the result defined by the following
// Then helper
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) When(productIDs []int64) *ParticipantDomainServiceMockGetOwnersByProductIDsExpectation {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ParticipantDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetOwnersByProductIDsExpectation{
		mock:               mmGetOwnersByProductIDs.mock,
		params:             &ParticipantDomainServiceMockGetOwnersByProductIDsParams{productIDs},
		expectationOrigins: ParticipantDomainServiceMockGetOwnersByProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetOwnersByProductIDs.expectations = append(mmGetOwnersByProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetOwnersByProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetOwnersByProductIDsExpectation) Then(m1 map[int64][]productentity.Owner, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetOwnersByProductIDsResults{m1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetOwnersByProductIDs should be invoked
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Times(n uint64) *mParticipantDomainServiceMockGetOwnersByProductIDs {
	if n == 0 {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetOwnersByProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetOwnersByProductIDs.expectedInvocations, n)
	mmGetOwnersByProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductIDs
}

func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) invocationsDone() bool {
	if len(mmGetOwnersByProductIDs.expectations) == 0 && mmGetOwnersByProductIDs.defaultExpectation == nil && mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.mock.afterGetOwnersByProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetOwnersByProductIDs implements mm_service.ParticipantDomainService
func (mmGetOwnersByProductIDs *ParticipantDomainServiceMock) GetOwnersByProductIDs(productIDs []int64) (m1 map[int64][]productentity.Owner, err error) {
	mm_atomic.AddUint64(&mmGetOwnersByProductIDs.beforeGetOwnersByProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetOwnersByProductIDs.afterGetOwnersByProductIDsCounter, 1)

	mmGetOwnersByProductIDs.t.Helper()

	if mmGetOwnersByProductIDs.inspectFuncGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.inspectFuncGetOwnersByProductIDs(productIDs)
	}

	mm_params := ParticipantDomainServiceMockGetOwnersByProductIDsParams{productIDs}

	// Record call args
	mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.mutex.Lock()
	mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.callArgs = append(mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.callArgs, &mm_params)
	mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.mutex.Unlock()

	for _, e := range mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.m1, e.results.err
		}
	}

	if mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetOwnersByProductIDsParams{productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmGetOwnersByProductIDs.t.Errorf("ParticipantDomainServiceMock.GetOwnersByProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetOwnersByProductIDs.t.Errorf("ParticipantDomainServiceMock.GetOwnersByProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetOwnersByProductIDs.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetOwnersByProductIDs")
		}
		return (*mm_results).m1, (*mm_results).err
	}
	if mmGetOwnersByProductIDs.funcGetOwnersByProductIDs != nil {
		return mmGetOwnersByProductIDs.funcGetOwnersByProductIDs(productIDs)
	}
	mmGetOwnersByProductIDs.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetOwnersByProductIDs. %v", productIDs)
	return
}

// GetOwnersByProductIDsAfterCounter returns a count of finished ParticipantDomainServiceMock.GetOwnersByProductIDs invocations
func (mmGetOwnersByProductIDs *ParticipantDomainServiceMock) GetOwnersByProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.afterGetOwnersByProductIDsCounter)
}

// GetOwnersByProductIDsBeforeCounter returns a count of ParticipantDomainServiceMock.GetOwnersByProductIDs invocations
func (mmGetOwnersByProductIDs *ParticipantDomainServiceMock) GetOwnersByProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.beforeGetOwnersByProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetOwnersByProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetOwnersByProductIDs *mParticipantDomainServiceMockGetOwnersByProductIDs) Calls() []*ParticipantDomainServiceMockGetOwnersByProductIDsParams {
	mmGetOwnersByProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetOwnersByProductIDsParams, len(mmGetOwnersByProductIDs.callArgs))
	copy(argCopy, mmGetOwnersByProductIDs.callArgs)

	mmGetOwnersByProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetOwnersByProductIDsDone returns true if the count of the GetOwnersByProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetOwnersByProductIDsDone() bool {
	if m.GetOwnersByProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetOwnersByProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetOwnersByProductIDsMock.invocationsDone()
}

// MinimockGetOwnersByProductIDsInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetOwnersByProductIDsInspect() {
	for _, e := range m.GetOwnersByProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetOwnersByProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetOwnersByProductIDsCounter := mm_atomic.LoadUint64(&m.afterGetOwnersByProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetOwnersByProductIDsMock.defaultExpectation != nil && afterGetOwnersByProductIDsCounter < 1 {
		if m.GetOwnersByProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetOwnersByProductIDs at\n%s", m.GetOwnersByProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetOwnersByProductIDs at\n%s with params: %#v", m.GetOwnersByProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetOwnersByProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetOwnersByProductIDs != nil && afterGetOwnersByProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetOwnersByProductIDs at\n%s", m.funcGetOwnersByProductIDsOrigin)
	}

	if !m.GetOwnersByProductIDsMock.invocationsDone() && afterGetOwnersByProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetOwnersByProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetOwnersByProductIDsMock.expectedInvocations), m.GetOwnersByProductIDsMock.expectedInvocationsOrigin, afterGetOwnersByProductIDsCounter)
	}
}

type mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
type ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams contains parameters of the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
type ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
type ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDResults contains results of the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
type ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDResults struct {
	p1  participantentity.ParticipantFull
	err error
}

// ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
type ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Optional() *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID {
	mmGetParticipantFullByParticipantIDAndProductID.optional = true
	return mmGetParticipantFullByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID {
	if mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation{}
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.params = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams{participantID, productID}
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetParticipantFullByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.params) {
			mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetParticipantFullByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID {
	if mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation{}
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetParticipantFullByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID {
	if mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation{}
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetParticipantFullByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID {
	if mmGetParticipantFullByParticipantIDAndProductID.mock.inspectFuncGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID")
	}

	mmGetParticipantFullByParticipantIDAndProductID.mock.inspectFuncGetParticipantFullByParticipantIDAndProductID = f

	return mmGetParticipantFullByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Return(p1 participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	if mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation{mock: mmGetParticipantFullByParticipantIDAndProductID.mock}
	}
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.results = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDResults{p1, err}
	mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetParticipantFullByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID method
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (p1 participantentity.ParticipantFull, err error)) *ParticipantDomainServiceMock {
	if mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID method")
	}

	if len(mmGetParticipantFullByParticipantIDAndProductID.expectations) > 0 {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID method")
	}

	mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID = f
	mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetParticipantFullByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation {
	if mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation{
		mock:               mmGetParticipantFullByParticipantIDAndProductID.mock,
		params:             &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetParticipantFullByParticipantIDAndProductID.expectations = append(mmGetParticipantFullByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDExpectation) Then(p1 participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetParticipantFullByParticipantIDAndProductID should be invoked
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Times(n uint64) *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID {
	if n == 0 {
		mmGetParticipantFullByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetParticipantFullByParticipantIDAndProductID.expectedInvocations, n)
	mmGetParticipantFullByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetParticipantFullByParticipantIDAndProductID
}

func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmGetParticipantFullByParticipantIDAndProductID.expectations) == 0 && mmGetParticipantFullByParticipantIDAndProductID.defaultExpectation == nil && mmGetParticipantFullByParticipantIDAndProductID.mock.funcGetParticipantFullByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetParticipantFullByParticipantIDAndProductID.mock.afterGetParticipantFullByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetParticipantFullByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetParticipantFullByParticipantIDAndProductID implements mm_service.ParticipantDomainService
func (mmGetParticipantFullByParticipantIDAndProductID *ParticipantDomainServiceMock) GetParticipantFullByParticipantIDAndProductID(participantID int64, productID int64) (p1 participantentity.ParticipantFull, err error) {
	mm_atomic.AddUint64(&mmGetParticipantFullByParticipantIDAndProductID.beforeGetParticipantFullByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetParticipantFullByParticipantIDAndProductID.afterGetParticipantFullByParticipantIDAndProductIDCounter, 1)

	mmGetParticipantFullByParticipantIDAndProductID.t.Helper()

	if mmGetParticipantFullByParticipantIDAndProductID.inspectFuncGetParticipantFullByParticipantIDAndProductID != nil {
		mmGetParticipantFullByParticipantIDAndProductID.inspectFuncGetParticipantFullByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.mutex.Lock()
	mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.callArgs = append(mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetParticipantFullByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetParticipantFullByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetParticipantFullByParticipantIDAndProductID.t.Errorf("ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetParticipantFullByParticipantIDAndProductID.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetParticipantFullByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetParticipantFullByParticipantIDAndProductID.funcGetParticipantFullByParticipantIDAndProductID != nil {
		return mmGetParticipantFullByParticipantIDAndProductID.funcGetParticipantFullByParticipantIDAndProductID(participantID, productID)
	}
	mmGetParticipantFullByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// GetParticipantFullByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID invocations
func (mmGetParticipantFullByParticipantIDAndProductID *ParticipantDomainServiceMock) GetParticipantFullByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantFullByParticipantIDAndProductID.afterGetParticipantFullByParticipantIDAndProductIDCounter)
}

// GetParticipantFullByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID invocations
func (mmGetParticipantFullByParticipantIDAndProductID *ParticipantDomainServiceMock) GetParticipantFullByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantFullByParticipantIDAndProductID.beforeGetParticipantFullByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetParticipantFullByParticipantIDAndProductID *mParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductID) Calls() []*ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams {
	mmGetParticipantFullByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetParticipantFullByParticipantIDAndProductIDParams, len(mmGetParticipantFullByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmGetParticipantFullByParticipantIDAndProductID.callArgs)

	mmGetParticipantFullByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetParticipantFullByParticipantIDAndProductIDDone returns true if the count of the GetParticipantFullByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetParticipantFullByParticipantIDAndProductIDDone() bool {
	if m.GetParticipantFullByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetParticipantFullByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetParticipantFullByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockGetParticipantFullByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetParticipantFullByParticipantIDAndProductIDInspect() {
	for _, e := range m.GetParticipantFullByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetParticipantFullByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetParticipantFullByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation != nil && afterGetParticipantFullByParticipantIDAndProductIDCounter < 1 {
		if m.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID at\n%s", m.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID at\n%s with params: %#v", m.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetParticipantFullByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetParticipantFullByParticipantIDAndProductID != nil && afterGetParticipantFullByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID at\n%s", m.funcGetParticipantFullByParticipantIDAndProductIDOrigin)
	}

	if !m.GetParticipantFullByParticipantIDAndProductIDMock.invocationsDone() && afterGetParticipantFullByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetParticipantFullByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetParticipantFullByParticipantIDAndProductIDMock.expectedInvocations), m.GetParticipantFullByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterGetParticipantFullByParticipantIDAndProductIDCounter)
	}
}

type mParticipantDomainServiceMockGetParticipantFullByProductID struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation
	expectations       []*ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation

	callArgs []*ParticipantDomainServiceMockGetParticipantFullByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation specifies expectation struct of the ParticipantDomainService.GetParticipantFullByProductID
type ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockGetParticipantFullByProductIDParams
	paramPtrs          *ParticipantDomainServiceMockGetParticipantFullByProductIDParamPtrs
	expectationOrigins ParticipantDomainServiceMockGetParticipantFullByProductIDExpectationOrigins
	results            *ParticipantDomainServiceMockGetParticipantFullByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockGetParticipantFullByProductIDParams contains parameters of the ParticipantDomainService.GetParticipantFullByProductID
type ParticipantDomainServiceMockGetParticipantFullByProductIDParams struct {
	productID int64
}

// ParticipantDomainServiceMockGetParticipantFullByProductIDParamPtrs contains pointers to parameters of the ParticipantDomainService.GetParticipantFullByProductID
type ParticipantDomainServiceMockGetParticipantFullByProductIDParamPtrs struct {
	productID *int64
}

// ParticipantDomainServiceMockGetParticipantFullByProductIDResults contains results of the ParticipantDomainService.GetParticipantFullByProductID
type ParticipantDomainServiceMockGetParticipantFullByProductIDResults struct {
	pa1 []participantentity.ParticipantFull
	err error
}

// ParticipantDomainServiceMockGetParticipantFullByProductIDOrigins contains origins of expectations of the ParticipantDomainService.GetParticipantFullByProductID
type ParticipantDomainServiceMockGetParticipantFullByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Optional() *mParticipantDomainServiceMockGetParticipantFullByProductID {
	mmGetParticipantFullByProductID.optional = true
	return mmGetParticipantFullByProductID
}

// Expect sets up expected params for ParticipantDomainService.GetParticipantFullByProductID
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Expect(productID int64) *mParticipantDomainServiceMockGetParticipantFullByProductID {
	if mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductID != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByProductID mock is already set by Set")
	}

	if mmGetParticipantFullByProductID.defaultExpectation == nil {
		mmGetParticipantFullByProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation{}
	}

	if mmGetParticipantFullByProductID.defaultExpectation.paramPtrs != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByProductID mock is already set by ExpectParams functions")
	}

	mmGetParticipantFullByProductID.defaultExpectation.params = &ParticipantDomainServiceMockGetParticipantFullByProductIDParams{productID}
	mmGetParticipantFullByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetParticipantFullByProductID.expectations {
		if minimock.Equal(e.params, mmGetParticipantFullByProductID.defaultExpectation.params) {
			mmGetParticipantFullByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetParticipantFullByProductID.defaultExpectation.params)
		}
	}

	return mmGetParticipantFullByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantDomainService.GetParticipantFullByProductID
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) ExpectProductIDParam1(productID int64) *mParticipantDomainServiceMockGetParticipantFullByProductID {
	if mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductID != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByProductID mock is already set by Set")
	}

	if mmGetParticipantFullByProductID.defaultExpectation == nil {
		mmGetParticipantFullByProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation{}
	}

	if mmGetParticipantFullByProductID.defaultExpectation.params != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByProductID mock is already set by Expect")
	}

	if mmGetParticipantFullByProductID.defaultExpectation.paramPtrs == nil {
		mmGetParticipantFullByProductID.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockGetParticipantFullByProductIDParamPtrs{}
	}
	mmGetParticipantFullByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetParticipantFullByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetParticipantFullByProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.GetParticipantFullByProductID
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Inspect(f func(productID int64)) *mParticipantDomainServiceMockGetParticipantFullByProductID {
	if mmGetParticipantFullByProductID.mock.inspectFuncGetParticipantFullByProductID != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.GetParticipantFullByProductID")
	}

	mmGetParticipantFullByProductID.mock.inspectFuncGetParticipantFullByProductID = f

	return mmGetParticipantFullByProductID
}

// Return sets up results that will be returned by ParticipantDomainService.GetParticipantFullByProductID
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Return(pa1 []participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	if mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductID != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByProductID mock is already set by Set")
	}

	if mmGetParticipantFullByProductID.defaultExpectation == nil {
		mmGetParticipantFullByProductID.defaultExpectation = &ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation{mock: mmGetParticipantFullByProductID.mock}
	}
	mmGetParticipantFullByProductID.defaultExpectation.results = &ParticipantDomainServiceMockGetParticipantFullByProductIDResults{pa1, err}
	mmGetParticipantFullByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetParticipantFullByProductID.mock
}

// Set uses given function f to mock the ParticipantDomainService.GetParticipantFullByProductID method
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Set(f func(productID int64) (pa1 []participantentity.ParticipantFull, err error)) *ParticipantDomainServiceMock {
	if mmGetParticipantFullByProductID.defaultExpectation != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.GetParticipantFullByProductID method")
	}

	if len(mmGetParticipantFullByProductID.expectations) > 0 {
		mmGetParticipantFullByProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.GetParticipantFullByProductID method")
	}

	mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductID = f
	mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetParticipantFullByProductID.mock
}

// When sets expectation for the ParticipantDomainService.GetParticipantFullByProductID which will trigger the result defined by the following
// Then helper
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) When(productID int64) *ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation {
	if mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductID != nil {
		mmGetParticipantFullByProductID.mock.t.Fatalf("ParticipantDomainServiceMock.GetParticipantFullByProductID mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation{
		mock:               mmGetParticipantFullByProductID.mock,
		params:             &ParticipantDomainServiceMockGetParticipantFullByProductIDParams{productID},
		expectationOrigins: ParticipantDomainServiceMockGetParticipantFullByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetParticipantFullByProductID.expectations = append(mmGetParticipantFullByProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.GetParticipantFullByProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockGetParticipantFullByProductIDExpectation) Then(pa1 []participantentity.ParticipantFull, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockGetParticipantFullByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.GetParticipantFullByProductID should be invoked
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Times(n uint64) *mParticipantDomainServiceMockGetParticipantFullByProductID {
	if n == 0 {
		mmGetParticipantFullByProductID.mock.t.Fatalf("Times of ParticipantDomainServiceMock.GetParticipantFullByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetParticipantFullByProductID.expectedInvocations, n)
	mmGetParticipantFullByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetParticipantFullByProductID
}

func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) invocationsDone() bool {
	if len(mmGetParticipantFullByProductID.expectations) == 0 && mmGetParticipantFullByProductID.defaultExpectation == nil && mmGetParticipantFullByProductID.mock.funcGetParticipantFullByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetParticipantFullByProductID.mock.afterGetParticipantFullByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetParticipantFullByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetParticipantFullByProductID implements mm_service.ParticipantDomainService
func (mmGetParticipantFullByProductID *ParticipantDomainServiceMock) GetParticipantFullByProductID(productID int64) (pa1 []participantentity.ParticipantFull, err error) {
	mm_atomic.AddUint64(&mmGetParticipantFullByProductID.beforeGetParticipantFullByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetParticipantFullByProductID.afterGetParticipantFullByProductIDCounter, 1)

	mmGetParticipantFullByProductID.t.Helper()

	if mmGetParticipantFullByProductID.inspectFuncGetParticipantFullByProductID != nil {
		mmGetParticipantFullByProductID.inspectFuncGetParticipantFullByProductID(productID)
	}

	mm_params := ParticipantDomainServiceMockGetParticipantFullByProductIDParams{productID}

	// Record call args
	mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.mutex.Lock()
	mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.callArgs = append(mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.callArgs, &mm_params)
	mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.mutex.Unlock()

	for _, e := range mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockGetParticipantFullByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetParticipantFullByProductID.t.Errorf("ParticipantDomainServiceMock.GetParticipantFullByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetParticipantFullByProductID.t.Errorf("ParticipantDomainServiceMock.GetParticipantFullByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetParticipantFullByProductID.GetParticipantFullByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetParticipantFullByProductID.t.Fatal("No results are set for the ParticipantDomainServiceMock.GetParticipantFullByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetParticipantFullByProductID.funcGetParticipantFullByProductID != nil {
		return mmGetParticipantFullByProductID.funcGetParticipantFullByProductID(productID)
	}
	mmGetParticipantFullByProductID.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.GetParticipantFullByProductID. %v", productID)
	return
}

// GetParticipantFullByProductIDAfterCounter returns a count of finished ParticipantDomainServiceMock.GetParticipantFullByProductID invocations
func (mmGetParticipantFullByProductID *ParticipantDomainServiceMock) GetParticipantFullByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantFullByProductID.afterGetParticipantFullByProductIDCounter)
}

// GetParticipantFullByProductIDBeforeCounter returns a count of ParticipantDomainServiceMock.GetParticipantFullByProductID invocations
func (mmGetParticipantFullByProductID *ParticipantDomainServiceMock) GetParticipantFullByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantFullByProductID.beforeGetParticipantFullByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.GetParticipantFullByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetParticipantFullByProductID *mParticipantDomainServiceMockGetParticipantFullByProductID) Calls() []*ParticipantDomainServiceMockGetParticipantFullByProductIDParams {
	mmGetParticipantFullByProductID.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockGetParticipantFullByProductIDParams, len(mmGetParticipantFullByProductID.callArgs))
	copy(argCopy, mmGetParticipantFullByProductID.callArgs)

	mmGetParticipantFullByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetParticipantFullByProductIDDone returns true if the count of the GetParticipantFullByProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockGetParticipantFullByProductIDDone() bool {
	if m.GetParticipantFullByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetParticipantFullByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetParticipantFullByProductIDMock.invocationsDone()
}

// MinimockGetParticipantFullByProductIDInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockGetParticipantFullByProductIDInspect() {
	for _, e := range m.GetParticipantFullByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetParticipantFullByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetParticipantFullByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetParticipantFullByProductIDMock.defaultExpectation != nil && afterGetParticipantFullByProductIDCounter < 1 {
		if m.GetParticipantFullByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByProductID at\n%s", m.GetParticipantFullByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByProductID at\n%s with params: %#v", m.GetParticipantFullByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetParticipantFullByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetParticipantFullByProductID != nil && afterGetParticipantFullByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.GetParticipantFullByProductID at\n%s", m.funcGetParticipantFullByProductIDOrigin)
	}

	if !m.GetParticipantFullByProductIDMock.invocationsDone() && afterGetParticipantFullByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.GetParticipantFullByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetParticipantFullByProductIDMock.expectedInvocations), m.GetParticipantFullByProductIDMock.expectedInvocationsOrigin, afterGetParticipantFullByProductIDCounter)
	}
}

type mParticipantDomainServiceMockUpdateGroups struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockUpdateGroupsExpectation
	expectations       []*ParticipantDomainServiceMockUpdateGroupsExpectation

	callArgs []*ParticipantDomainServiceMockUpdateGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockUpdateGroupsExpectation specifies expectation struct of the ParticipantDomainService.UpdateGroups
type ParticipantDomainServiceMockUpdateGroupsExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockUpdateGroupsParams
	paramPtrs          *ParticipantDomainServiceMockUpdateGroupsParamPtrs
	expectationOrigins ParticipantDomainServiceMockUpdateGroupsExpectationOrigins
	results            *ParticipantDomainServiceMockUpdateGroupsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockUpdateGroupsParams contains parameters of the ParticipantDomainService.UpdateGroups
type ParticipantDomainServiceMockUpdateGroupsParams struct {
	ctx           context.Context
	participantID int64
	groupIDs      []int64
}

// ParticipantDomainServiceMockUpdateGroupsParamPtrs contains pointers to parameters of the ParticipantDomainService.UpdateGroups
type ParticipantDomainServiceMockUpdateGroupsParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	groupIDs      *[]int64
}

// ParticipantDomainServiceMockUpdateGroupsResults contains results of the ParticipantDomainService.UpdateGroups
type ParticipantDomainServiceMockUpdateGroupsResults struct {
	ia1 []int64
	err error
}

// ParticipantDomainServiceMockUpdateGroupsOrigins contains origins of expectations of the ParticipantDomainService.UpdateGroups
type ParticipantDomainServiceMockUpdateGroupsExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originGroupIDs      string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Optional() *mParticipantDomainServiceMockUpdateGroups {
	mmUpdateGroups.optional = true
	return mmUpdateGroups
}

// Expect sets up expected params for ParticipantDomainService.UpdateGroups
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Expect(ctx context.Context, participantID int64, groupIDs []int64) *mParticipantDomainServiceMockUpdateGroups {
	if mmUpdateGroups.mock.funcUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Set")
	}

	if mmUpdateGroups.defaultExpectation == nil {
		mmUpdateGroups.defaultExpectation = &ParticipantDomainServiceMockUpdateGroupsExpectation{}
	}

	if mmUpdateGroups.defaultExpectation.paramPtrs != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by ExpectParams functions")
	}

	mmUpdateGroups.defaultExpectation.params = &ParticipantDomainServiceMockUpdateGroupsParams{ctx, participantID, groupIDs}
	mmUpdateGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateGroups.expectations {
		if minimock.Equal(e.params, mmUpdateGroups.defaultExpectation.params) {
			mmUpdateGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateGroups.defaultExpectation.params)
		}
	}

	return mmUpdateGroups
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantDomainService.UpdateGroups
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) ExpectCtxParam1(ctx context.Context) *mParticipantDomainServiceMockUpdateGroups {
	if mmUpdateGroups.mock.funcUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Set")
	}

	if mmUpdateGroups.defaultExpectation == nil {
		mmUpdateGroups.defaultExpectation = &ParticipantDomainServiceMockUpdateGroupsExpectation{}
	}

	if mmUpdateGroups.defaultExpectation.params != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Expect")
	}

	if mmUpdateGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateGroups.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateGroupsParamPtrs{}
	}
	mmUpdateGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateGroups
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantDomainService.UpdateGroups
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) ExpectParticipantIDParam2(participantID int64) *mParticipantDomainServiceMockUpdateGroups {
	if mmUpdateGroups.mock.funcUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Set")
	}

	if mmUpdateGroups.defaultExpectation == nil {
		mmUpdateGroups.defaultExpectation = &ParticipantDomainServiceMockUpdateGroupsExpectation{}
	}

	if mmUpdateGroups.defaultExpectation.params != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Expect")
	}

	if mmUpdateGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateGroups.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateGroupsParamPtrs{}
	}
	mmUpdateGroups.defaultExpectation.paramPtrs.participantID = &participantID
	mmUpdateGroups.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmUpdateGroups
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for ParticipantDomainService.UpdateGroups
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) ExpectGroupIDsParam3(groupIDs []int64) *mParticipantDomainServiceMockUpdateGroups {
	if mmUpdateGroups.mock.funcUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Set")
	}

	if mmUpdateGroups.defaultExpectation == nil {
		mmUpdateGroups.defaultExpectation = &ParticipantDomainServiceMockUpdateGroupsExpectation{}
	}

	if mmUpdateGroups.defaultExpectation.params != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Expect")
	}

	if mmUpdateGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateGroups.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateGroupsParamPtrs{}
	}
	mmUpdateGroups.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmUpdateGroups.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmUpdateGroups
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.UpdateGroups
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Inspect(f func(ctx context.Context, participantID int64, groupIDs []int64)) *mParticipantDomainServiceMockUpdateGroups {
	if mmUpdateGroups.mock.inspectFuncUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.UpdateGroups")
	}

	mmUpdateGroups.mock.inspectFuncUpdateGroups = f

	return mmUpdateGroups
}

// Return sets up results that will be returned by ParticipantDomainService.UpdateGroups
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Return(ia1 []int64, err error) *ParticipantDomainServiceMock {
	if mmUpdateGroups.mock.funcUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Set")
	}

	if mmUpdateGroups.defaultExpectation == nil {
		mmUpdateGroups.defaultExpectation = &ParticipantDomainServiceMockUpdateGroupsExpectation{mock: mmUpdateGroups.mock}
	}
	mmUpdateGroups.defaultExpectation.results = &ParticipantDomainServiceMockUpdateGroupsResults{ia1, err}
	mmUpdateGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateGroups.mock
}

// Set uses given function f to mock the ParticipantDomainService.UpdateGroups method
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Set(f func(ctx context.Context, participantID int64, groupIDs []int64) (ia1 []int64, err error)) *ParticipantDomainServiceMock {
	if mmUpdateGroups.defaultExpectation != nil {
		mmUpdateGroups.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.UpdateGroups method")
	}

	if len(mmUpdateGroups.expectations) > 0 {
		mmUpdateGroups.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.UpdateGroups method")
	}

	mmUpdateGroups.mock.funcUpdateGroups = f
	mmUpdateGroups.mock.funcUpdateGroupsOrigin = minimock.CallerInfo(1)
	return mmUpdateGroups.mock
}

// When sets expectation for the ParticipantDomainService.UpdateGroups which will trigger the result defined by the following
// Then helper
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) When(ctx context.Context, participantID int64, groupIDs []int64) *ParticipantDomainServiceMockUpdateGroupsExpectation {
	if mmUpdateGroups.mock.funcUpdateGroups != nil {
		mmUpdateGroups.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateGroups mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockUpdateGroupsExpectation{
		mock:               mmUpdateGroups.mock,
		params:             &ParticipantDomainServiceMockUpdateGroupsParams{ctx, participantID, groupIDs},
		expectationOrigins: ParticipantDomainServiceMockUpdateGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateGroups.expectations = append(mmUpdateGroups.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.UpdateGroups return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockUpdateGroupsExpectation) Then(ia1 []int64, err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockUpdateGroupsResults{ia1, err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.UpdateGroups should be invoked
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Times(n uint64) *mParticipantDomainServiceMockUpdateGroups {
	if n == 0 {
		mmUpdateGroups.mock.t.Fatalf("Times of ParticipantDomainServiceMock.UpdateGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateGroups.expectedInvocations, n)
	mmUpdateGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateGroups
}

func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) invocationsDone() bool {
	if len(mmUpdateGroups.expectations) == 0 && mmUpdateGroups.defaultExpectation == nil && mmUpdateGroups.mock.funcUpdateGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateGroups.mock.afterUpdateGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateGroups implements mm_service.ParticipantDomainService
func (mmUpdateGroups *ParticipantDomainServiceMock) UpdateGroups(ctx context.Context, participantID int64, groupIDs []int64) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmUpdateGroups.beforeUpdateGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateGroups.afterUpdateGroupsCounter, 1)

	mmUpdateGroups.t.Helper()

	if mmUpdateGroups.inspectFuncUpdateGroups != nil {
		mmUpdateGroups.inspectFuncUpdateGroups(ctx, participantID, groupIDs)
	}

	mm_params := ParticipantDomainServiceMockUpdateGroupsParams{ctx, participantID, groupIDs}

	// Record call args
	mmUpdateGroups.UpdateGroupsMock.mutex.Lock()
	mmUpdateGroups.UpdateGroupsMock.callArgs = append(mmUpdateGroups.UpdateGroupsMock.callArgs, &mm_params)
	mmUpdateGroups.UpdateGroupsMock.mutex.Unlock()

	for _, e := range mmUpdateGroups.UpdateGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmUpdateGroups.UpdateGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateGroups.UpdateGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateGroups.UpdateGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateGroups.UpdateGroupsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockUpdateGroupsParams{ctx, participantID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateGroups.t.Errorf("ParticipantDomainServiceMock.UpdateGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateGroups.UpdateGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmUpdateGroups.t.Errorf("ParticipantDomainServiceMock.UpdateGroups got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateGroups.UpdateGroupsMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmUpdateGroups.t.Errorf("ParticipantDomainServiceMock.UpdateGroups got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateGroups.UpdateGroupsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateGroups.t.Errorf("ParticipantDomainServiceMock.UpdateGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateGroups.UpdateGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateGroups.UpdateGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateGroups.t.Fatal("No results are set for the ParticipantDomainServiceMock.UpdateGroups")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmUpdateGroups.funcUpdateGroups != nil {
		return mmUpdateGroups.funcUpdateGroups(ctx, participantID, groupIDs)
	}
	mmUpdateGroups.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.UpdateGroups. %v %v %v", ctx, participantID, groupIDs)
	return
}

// UpdateGroupsAfterCounter returns a count of finished ParticipantDomainServiceMock.UpdateGroups invocations
func (mmUpdateGroups *ParticipantDomainServiceMock) UpdateGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateGroups.afterUpdateGroupsCounter)
}

// UpdateGroupsBeforeCounter returns a count of ParticipantDomainServiceMock.UpdateGroups invocations
func (mmUpdateGroups *ParticipantDomainServiceMock) UpdateGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateGroups.beforeUpdateGroupsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.UpdateGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateGroups *mParticipantDomainServiceMockUpdateGroups) Calls() []*ParticipantDomainServiceMockUpdateGroupsParams {
	mmUpdateGroups.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockUpdateGroupsParams, len(mmUpdateGroups.callArgs))
	copy(argCopy, mmUpdateGroups.callArgs)

	mmUpdateGroups.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateGroupsDone returns true if the count of the UpdateGroups invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockUpdateGroupsDone() bool {
	if m.UpdateGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateGroupsMock.invocationsDone()
}

// MinimockUpdateGroupsInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockUpdateGroupsInspect() {
	for _, e := range m.UpdateGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateGroupsCounter := mm_atomic.LoadUint64(&m.afterUpdateGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateGroupsMock.defaultExpectation != nil && afterUpdateGroupsCounter < 1 {
		if m.UpdateGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateGroups at\n%s", m.UpdateGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateGroups at\n%s with params: %#v", m.UpdateGroupsMock.defaultExpectation.expectationOrigins.origin, *m.UpdateGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateGroups != nil && afterUpdateGroupsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateGroups at\n%s", m.funcUpdateGroupsOrigin)
	}

	if !m.UpdateGroupsMock.invocationsDone() && afterUpdateGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.UpdateGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateGroupsMock.expectedInvocations), m.UpdateGroupsMock.expectedInvocationsOrigin, afterUpdateGroupsCounter)
	}
}

type mParticipantDomainServiceMockUpdateOwners struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockUpdateOwnersExpectation
	expectations       []*ParticipantDomainServiceMockUpdateOwnersExpectation

	callArgs []*ParticipantDomainServiceMockUpdateOwnersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockUpdateOwnersExpectation specifies expectation struct of the ParticipantDomainService.UpdateOwners
type ParticipantDomainServiceMockUpdateOwnersExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockUpdateOwnersParams
	paramPtrs          *ParticipantDomainServiceMockUpdateOwnersParamPtrs
	expectationOrigins ParticipantDomainServiceMockUpdateOwnersExpectationOrigins
	results            *ParticipantDomainServiceMockUpdateOwnersResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockUpdateOwnersParams contains parameters of the ParticipantDomainService.UpdateOwners
type ParticipantDomainServiceMockUpdateOwnersParams struct {
	productID   int64
	ownerEmails []string
}

// ParticipantDomainServiceMockUpdateOwnersParamPtrs contains pointers to parameters of the ParticipantDomainService.UpdateOwners
type ParticipantDomainServiceMockUpdateOwnersParamPtrs struct {
	productID   *int64
	ownerEmails *[]string
}

// ParticipantDomainServiceMockUpdateOwnersResults contains results of the ParticipantDomainService.UpdateOwners
type ParticipantDomainServiceMockUpdateOwnersResults struct {
	err error
}

// ParticipantDomainServiceMockUpdateOwnersOrigins contains origins of expectations of the ParticipantDomainService.UpdateOwners
type ParticipantDomainServiceMockUpdateOwnersExpectationOrigins struct {
	origin            string
	originProductID   string
	originOwnerEmails string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Optional() *mParticipantDomainServiceMockUpdateOwners {
	mmUpdateOwners.optional = true
	return mmUpdateOwners
}

// Expect sets up expected params for ParticipantDomainService.UpdateOwners
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Expect(productID int64, ownerEmails []string) *mParticipantDomainServiceMockUpdateOwners {
	if mmUpdateOwners.mock.funcUpdateOwners != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Set")
	}

	if mmUpdateOwners.defaultExpectation == nil {
		mmUpdateOwners.defaultExpectation = &ParticipantDomainServiceMockUpdateOwnersExpectation{}
	}

	if mmUpdateOwners.defaultExpectation.paramPtrs != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by ExpectParams functions")
	}

	mmUpdateOwners.defaultExpectation.params = &ParticipantDomainServiceMockUpdateOwnersParams{productID, ownerEmails}
	mmUpdateOwners.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateOwners.expectations {
		if minimock.Equal(e.params, mmUpdateOwners.defaultExpectation.params) {
			mmUpdateOwners.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateOwners.defaultExpectation.params)
		}
	}

	return mmUpdateOwners
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantDomainService.UpdateOwners
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) ExpectProductIDParam1(productID int64) *mParticipantDomainServiceMockUpdateOwners {
	if mmUpdateOwners.mock.funcUpdateOwners != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Set")
	}

	if mmUpdateOwners.defaultExpectation == nil {
		mmUpdateOwners.defaultExpectation = &ParticipantDomainServiceMockUpdateOwnersExpectation{}
	}

	if mmUpdateOwners.defaultExpectation.params != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Expect")
	}

	if mmUpdateOwners.defaultExpectation.paramPtrs == nil {
		mmUpdateOwners.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateOwnersParamPtrs{}
	}
	mmUpdateOwners.defaultExpectation.paramPtrs.productID = &productID
	mmUpdateOwners.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmUpdateOwners
}

// ExpectOwnerEmailsParam2 sets up expected param ownerEmails for ParticipantDomainService.UpdateOwners
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) ExpectOwnerEmailsParam2(ownerEmails []string) *mParticipantDomainServiceMockUpdateOwners {
	if mmUpdateOwners.mock.funcUpdateOwners != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Set")
	}

	if mmUpdateOwners.defaultExpectation == nil {
		mmUpdateOwners.defaultExpectation = &ParticipantDomainServiceMockUpdateOwnersExpectation{}
	}

	if mmUpdateOwners.defaultExpectation.params != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Expect")
	}

	if mmUpdateOwners.defaultExpectation.paramPtrs == nil {
		mmUpdateOwners.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateOwnersParamPtrs{}
	}
	mmUpdateOwners.defaultExpectation.paramPtrs.ownerEmails = &ownerEmails
	mmUpdateOwners.defaultExpectation.expectationOrigins.originOwnerEmails = minimock.CallerInfo(1)

	return mmUpdateOwners
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.UpdateOwners
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Inspect(f func(productID int64, ownerEmails []string)) *mParticipantDomainServiceMockUpdateOwners {
	if mmUpdateOwners.mock.inspectFuncUpdateOwners != nil {
		mmUpdateOwners.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.UpdateOwners")
	}

	mmUpdateOwners.mock.inspectFuncUpdateOwners = f

	return mmUpdateOwners
}

// Return sets up results that will be returned by ParticipantDomainService.UpdateOwners
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Return(err error) *ParticipantDomainServiceMock {
	if mmUpdateOwners.mock.funcUpdateOwners != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Set")
	}

	if mmUpdateOwners.defaultExpectation == nil {
		mmUpdateOwners.defaultExpectation = &ParticipantDomainServiceMockUpdateOwnersExpectation{mock: mmUpdateOwners.mock}
	}
	mmUpdateOwners.defaultExpectation.results = &ParticipantDomainServiceMockUpdateOwnersResults{err}
	mmUpdateOwners.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateOwners.mock
}

// Set uses given function f to mock the ParticipantDomainService.UpdateOwners method
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Set(f func(productID int64, ownerEmails []string) (err error)) *ParticipantDomainServiceMock {
	if mmUpdateOwners.defaultExpectation != nil {
		mmUpdateOwners.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.UpdateOwners method")
	}

	if len(mmUpdateOwners.expectations) > 0 {
		mmUpdateOwners.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.UpdateOwners method")
	}

	mmUpdateOwners.mock.funcUpdateOwners = f
	mmUpdateOwners.mock.funcUpdateOwnersOrigin = minimock.CallerInfo(1)
	return mmUpdateOwners.mock
}

// When sets expectation for the ParticipantDomainService.UpdateOwners which will trigger the result defined by the following
// Then helper
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) When(productID int64, ownerEmails []string) *ParticipantDomainServiceMockUpdateOwnersExpectation {
	if mmUpdateOwners.mock.funcUpdateOwners != nil {
		mmUpdateOwners.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateOwners mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockUpdateOwnersExpectation{
		mock:               mmUpdateOwners.mock,
		params:             &ParticipantDomainServiceMockUpdateOwnersParams{productID, ownerEmails},
		expectationOrigins: ParticipantDomainServiceMockUpdateOwnersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateOwners.expectations = append(mmUpdateOwners.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.UpdateOwners return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockUpdateOwnersExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockUpdateOwnersResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.UpdateOwners should be invoked
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Times(n uint64) *mParticipantDomainServiceMockUpdateOwners {
	if n == 0 {
		mmUpdateOwners.mock.t.Fatalf("Times of ParticipantDomainServiceMock.UpdateOwners mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateOwners.expectedInvocations, n)
	mmUpdateOwners.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateOwners
}

func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) invocationsDone() bool {
	if len(mmUpdateOwners.expectations) == 0 && mmUpdateOwners.defaultExpectation == nil && mmUpdateOwners.mock.funcUpdateOwners == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateOwners.mock.afterUpdateOwnersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateOwners.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateOwners implements mm_service.ParticipantDomainService
func (mmUpdateOwners *ParticipantDomainServiceMock) UpdateOwners(productID int64, ownerEmails []string) (err error) {
	mm_atomic.AddUint64(&mmUpdateOwners.beforeUpdateOwnersCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateOwners.afterUpdateOwnersCounter, 1)

	mmUpdateOwners.t.Helper()

	if mmUpdateOwners.inspectFuncUpdateOwners != nil {
		mmUpdateOwners.inspectFuncUpdateOwners(productID, ownerEmails)
	}

	mm_params := ParticipantDomainServiceMockUpdateOwnersParams{productID, ownerEmails}

	// Record call args
	mmUpdateOwners.UpdateOwnersMock.mutex.Lock()
	mmUpdateOwners.UpdateOwnersMock.callArgs = append(mmUpdateOwners.UpdateOwnersMock.callArgs, &mm_params)
	mmUpdateOwners.UpdateOwnersMock.mutex.Unlock()

	for _, e := range mmUpdateOwners.UpdateOwnersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateOwners.UpdateOwnersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateOwners.UpdateOwnersMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateOwners.UpdateOwnersMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateOwners.UpdateOwnersMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockUpdateOwnersParams{productID, ownerEmails}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmUpdateOwners.t.Errorf("ParticipantDomainServiceMock.UpdateOwners got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateOwners.UpdateOwnersMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.ownerEmails != nil && !minimock.Equal(*mm_want_ptrs.ownerEmails, mm_got.ownerEmails) {
				mmUpdateOwners.t.Errorf("ParticipantDomainServiceMock.UpdateOwners got unexpected parameter ownerEmails, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateOwners.UpdateOwnersMock.defaultExpectation.expectationOrigins.originOwnerEmails, *mm_want_ptrs.ownerEmails, mm_got.ownerEmails, minimock.Diff(*mm_want_ptrs.ownerEmails, mm_got.ownerEmails))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateOwners.t.Errorf("ParticipantDomainServiceMock.UpdateOwners got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateOwners.UpdateOwnersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateOwners.UpdateOwnersMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateOwners.t.Fatal("No results are set for the ParticipantDomainServiceMock.UpdateOwners")
		}
		return (*mm_results).err
	}
	if mmUpdateOwners.funcUpdateOwners != nil {
		return mmUpdateOwners.funcUpdateOwners(productID, ownerEmails)
	}
	mmUpdateOwners.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.UpdateOwners. %v %v", productID, ownerEmails)
	return
}

// UpdateOwnersAfterCounter returns a count of finished ParticipantDomainServiceMock.UpdateOwners invocations
func (mmUpdateOwners *ParticipantDomainServiceMock) UpdateOwnersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateOwners.afterUpdateOwnersCounter)
}

// UpdateOwnersBeforeCounter returns a count of ParticipantDomainServiceMock.UpdateOwners invocations
func (mmUpdateOwners *ParticipantDomainServiceMock) UpdateOwnersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateOwners.beforeUpdateOwnersCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.UpdateOwners.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateOwners *mParticipantDomainServiceMockUpdateOwners) Calls() []*ParticipantDomainServiceMockUpdateOwnersParams {
	mmUpdateOwners.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockUpdateOwnersParams, len(mmUpdateOwners.callArgs))
	copy(argCopy, mmUpdateOwners.callArgs)

	mmUpdateOwners.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateOwnersDone returns true if the count of the UpdateOwners invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockUpdateOwnersDone() bool {
	if m.UpdateOwnersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateOwnersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateOwnersMock.invocationsDone()
}

// MinimockUpdateOwnersInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockUpdateOwnersInspect() {
	for _, e := range m.UpdateOwnersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateOwners at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateOwnersCounter := mm_atomic.LoadUint64(&m.afterUpdateOwnersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateOwnersMock.defaultExpectation != nil && afterUpdateOwnersCounter < 1 {
		if m.UpdateOwnersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateOwners at\n%s", m.UpdateOwnersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateOwners at\n%s with params: %#v", m.UpdateOwnersMock.defaultExpectation.expectationOrigins.origin, *m.UpdateOwnersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateOwners != nil && afterUpdateOwnersCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateOwners at\n%s", m.funcUpdateOwnersOrigin)
	}

	if !m.UpdateOwnersMock.invocationsDone() && afterUpdateOwnersCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.UpdateOwners at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateOwnersMock.expectedInvocations), m.UpdateOwnersMock.expectedInvocationsOrigin, afterUpdateOwnersCounter)
	}
}

type mParticipantDomainServiceMockUpdateParticipantByUserUpdateData struct {
	optional           bool
	mock               *ParticipantDomainServiceMock
	defaultExpectation *ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation
	expectations       []*ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation

	callArgs []*ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation specifies expectation struct of the ParticipantDomainService.UpdateParticipantByUserUpdateData
type ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation struct {
	mock               *ParticipantDomainServiceMock
	params             *ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams
	paramPtrs          *ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParamPtrs
	expectationOrigins ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectationOrigins
	results            *ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams contains parameters of the ParticipantDomainService.UpdateParticipantByUserUpdateData
type ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams struct {
	userID  int64
	updData userentity.UserUpdateData
}

// ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParamPtrs contains pointers to parameters of the ParticipantDomainService.UpdateParticipantByUserUpdateData
type ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParamPtrs struct {
	userID  *int64
	updData *userentity.UserUpdateData
}

// ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataResults contains results of the ParticipantDomainService.UpdateParticipantByUserUpdateData
type ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataResults struct {
	err error
}

// ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataOrigins contains origins of expectations of the ParticipantDomainService.UpdateParticipantByUserUpdateData
type ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectationOrigins struct {
	origin        string
	originUserID  string
	originUpdData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Optional() *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData {
	mmUpdateParticipantByUserUpdateData.optional = true
	return mmUpdateParticipantByUserUpdateData
}

// Expect sets up expected params for ParticipantDomainService.UpdateParticipantByUserUpdateData
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Expect(userID int64, updData userentity.UserUpdateData) *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData {
	if mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Set")
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation == nil {
		mmUpdateParticipantByUserUpdateData.defaultExpectation = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation{}
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by ExpectParams functions")
	}

	mmUpdateParticipantByUserUpdateData.defaultExpectation.params = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams{userID, updData}
	mmUpdateParticipantByUserUpdateData.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateParticipantByUserUpdateData.expectations {
		if minimock.Equal(e.params, mmUpdateParticipantByUserUpdateData.defaultExpectation.params) {
			mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateParticipantByUserUpdateData.defaultExpectation.params)
		}
	}

	return mmUpdateParticipantByUserUpdateData
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantDomainService.UpdateParticipantByUserUpdateData
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) ExpectUserIDParam1(userID int64) *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData {
	if mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Set")
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation == nil {
		mmUpdateParticipantByUserUpdateData.defaultExpectation = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation{}
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation.params != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Expect")
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs == nil {
		mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParamPtrs{}
	}
	mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs.userID = &userID
	mmUpdateParticipantByUserUpdateData.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmUpdateParticipantByUserUpdateData
}

// ExpectUpdDataParam2 sets up expected param updData for ParticipantDomainService.UpdateParticipantByUserUpdateData
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) ExpectUpdDataParam2(updData userentity.UserUpdateData) *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData {
	if mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Set")
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation == nil {
		mmUpdateParticipantByUserUpdateData.defaultExpectation = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation{}
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation.params != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Expect")
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs == nil {
		mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParamPtrs{}
	}
	mmUpdateParticipantByUserUpdateData.defaultExpectation.paramPtrs.updData = &updData
	mmUpdateParticipantByUserUpdateData.defaultExpectation.expectationOrigins.originUpdData = minimock.CallerInfo(1)

	return mmUpdateParticipantByUserUpdateData
}

// Inspect accepts an inspector function that has same arguments as the ParticipantDomainService.UpdateParticipantByUserUpdateData
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Inspect(f func(userID int64, updData userentity.UserUpdateData)) *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData {
	if mmUpdateParticipantByUserUpdateData.mock.inspectFuncUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("Inspect function is already set for ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData")
	}

	mmUpdateParticipantByUserUpdateData.mock.inspectFuncUpdateParticipantByUserUpdateData = f

	return mmUpdateParticipantByUserUpdateData
}

// Return sets up results that will be returned by ParticipantDomainService.UpdateParticipantByUserUpdateData
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Return(err error) *ParticipantDomainServiceMock {
	if mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Set")
	}

	if mmUpdateParticipantByUserUpdateData.defaultExpectation == nil {
		mmUpdateParticipantByUserUpdateData.defaultExpectation = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation{mock: mmUpdateParticipantByUserUpdateData.mock}
	}
	mmUpdateParticipantByUserUpdateData.defaultExpectation.results = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataResults{err}
	mmUpdateParticipantByUserUpdateData.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateParticipantByUserUpdateData.mock
}

// Set uses given function f to mock the ParticipantDomainService.UpdateParticipantByUserUpdateData method
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Set(f func(userID int64, updData userentity.UserUpdateData) (err error)) *ParticipantDomainServiceMock {
	if mmUpdateParticipantByUserUpdateData.defaultExpectation != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("Default expectation is already set for the ParticipantDomainService.UpdateParticipantByUserUpdateData method")
	}

	if len(mmUpdateParticipantByUserUpdateData.expectations) > 0 {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("Some expectations are already set for the ParticipantDomainService.UpdateParticipantByUserUpdateData method")
	}

	mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData = f
	mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateDataOrigin = minimock.CallerInfo(1)
	return mmUpdateParticipantByUserUpdateData.mock
}

// When sets expectation for the ParticipantDomainService.UpdateParticipantByUserUpdateData which will trigger the result defined by the following
// Then helper
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) When(userID int64, updData userentity.UserUpdateData) *ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation {
	if mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock is already set by Set")
	}

	expectation := &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation{
		mock:               mmUpdateParticipantByUserUpdateData.mock,
		params:             &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams{userID, updData},
		expectationOrigins: ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateParticipantByUserUpdateData.expectations = append(mmUpdateParticipantByUserUpdateData.expectations, expectation)
	return expectation
}

// Then sets up ParticipantDomainService.UpdateParticipantByUserUpdateData return parameters for the expectation previously defined by the When method
func (e *ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataExpectation) Then(err error) *ParticipantDomainServiceMock {
	e.results = &ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataResults{err}
	return e.mock
}

// Times sets number of times ParticipantDomainService.UpdateParticipantByUserUpdateData should be invoked
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Times(n uint64) *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData {
	if n == 0 {
		mmUpdateParticipantByUserUpdateData.mock.t.Fatalf("Times of ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateParticipantByUserUpdateData.expectedInvocations, n)
	mmUpdateParticipantByUserUpdateData.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateParticipantByUserUpdateData
}

func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) invocationsDone() bool {
	if len(mmUpdateParticipantByUserUpdateData.expectations) == 0 && mmUpdateParticipantByUserUpdateData.defaultExpectation == nil && mmUpdateParticipantByUserUpdateData.mock.funcUpdateParticipantByUserUpdateData == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateParticipantByUserUpdateData.mock.afterUpdateParticipantByUserUpdateDataCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateParticipantByUserUpdateData.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateParticipantByUserUpdateData implements mm_service.ParticipantDomainService
func (mmUpdateParticipantByUserUpdateData *ParticipantDomainServiceMock) UpdateParticipantByUserUpdateData(userID int64, updData userentity.UserUpdateData) (err error) {
	mm_atomic.AddUint64(&mmUpdateParticipantByUserUpdateData.beforeUpdateParticipantByUserUpdateDataCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateParticipantByUserUpdateData.afterUpdateParticipantByUserUpdateDataCounter, 1)

	mmUpdateParticipantByUserUpdateData.t.Helper()

	if mmUpdateParticipantByUserUpdateData.inspectFuncUpdateParticipantByUserUpdateData != nil {
		mmUpdateParticipantByUserUpdateData.inspectFuncUpdateParticipantByUserUpdateData(userID, updData)
	}

	mm_params := ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams{userID, updData}

	// Record call args
	mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.mutex.Lock()
	mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.callArgs = append(mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.callArgs, &mm_params)
	mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.mutex.Unlock()

	for _, e := range mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.paramPtrs

		mm_got := ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams{userID, updData}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmUpdateParticipantByUserUpdateData.t.Errorf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.updData != nil && !minimock.Equal(*mm_want_ptrs.updData, mm_got.updData) {
				mmUpdateParticipantByUserUpdateData.t.Errorf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData got unexpected parameter updData, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.expectationOrigins.originUpdData, *mm_want_ptrs.updData, mm_got.updData, minimock.Diff(*mm_want_ptrs.updData, mm_got.updData))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateParticipantByUserUpdateData.t.Errorf("ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateParticipantByUserUpdateData.UpdateParticipantByUserUpdateDataMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateParticipantByUserUpdateData.t.Fatal("No results are set for the ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData")
		}
		return (*mm_results).err
	}
	if mmUpdateParticipantByUserUpdateData.funcUpdateParticipantByUserUpdateData != nil {
		return mmUpdateParticipantByUserUpdateData.funcUpdateParticipantByUserUpdateData(userID, updData)
	}
	mmUpdateParticipantByUserUpdateData.t.Fatalf("Unexpected call to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData. %v %v", userID, updData)
	return
}

// UpdateParticipantByUserUpdateDataAfterCounter returns a count of finished ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData invocations
func (mmUpdateParticipantByUserUpdateData *ParticipantDomainServiceMock) UpdateParticipantByUserUpdateDataAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateParticipantByUserUpdateData.afterUpdateParticipantByUserUpdateDataCounter)
}

// UpdateParticipantByUserUpdateDataBeforeCounter returns a count of ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData invocations
func (mmUpdateParticipantByUserUpdateData *ParticipantDomainServiceMock) UpdateParticipantByUserUpdateDataBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateParticipantByUserUpdateData.beforeUpdateParticipantByUserUpdateDataCounter)
}

// Calls returns a list of arguments used in each call to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateParticipantByUserUpdateData *mParticipantDomainServiceMockUpdateParticipantByUserUpdateData) Calls() []*ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams {
	mmUpdateParticipantByUserUpdateData.mutex.RLock()

	argCopy := make([]*ParticipantDomainServiceMockUpdateParticipantByUserUpdateDataParams, len(mmUpdateParticipantByUserUpdateData.callArgs))
	copy(argCopy, mmUpdateParticipantByUserUpdateData.callArgs)

	mmUpdateParticipantByUserUpdateData.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateParticipantByUserUpdateDataDone returns true if the count of the UpdateParticipantByUserUpdateData invocations corresponds
// the number of defined expectations
func (m *ParticipantDomainServiceMock) MinimockUpdateParticipantByUserUpdateDataDone() bool {
	if m.UpdateParticipantByUserUpdateDataMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateParticipantByUserUpdateDataMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateParticipantByUserUpdateDataMock.invocationsDone()
}

// MinimockUpdateParticipantByUserUpdateDataInspect logs each unmet expectation
func (m *ParticipantDomainServiceMock) MinimockUpdateParticipantByUserUpdateDataInspect() {
	for _, e := range m.UpdateParticipantByUserUpdateDataMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateParticipantByUserUpdateDataCounter := mm_atomic.LoadUint64(&m.afterUpdateParticipantByUserUpdateDataCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateParticipantByUserUpdateDataMock.defaultExpectation != nil && afterUpdateParticipantByUserUpdateDataCounter < 1 {
		if m.UpdateParticipantByUserUpdateDataMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData at\n%s", m.UpdateParticipantByUserUpdateDataMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData at\n%s with params: %#v", m.UpdateParticipantByUserUpdateDataMock.defaultExpectation.expectationOrigins.origin, *m.UpdateParticipantByUserUpdateDataMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateParticipantByUserUpdateData != nil && afterUpdateParticipantByUserUpdateDataCounter < 1 {
		m.t.Errorf("Expected call to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData at\n%s", m.funcUpdateParticipantByUserUpdateDataOrigin)
	}

	if !m.UpdateParticipantByUserUpdateDataMock.invocationsDone() && afterUpdateParticipantByUserUpdateDataCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantDomainServiceMock.UpdateParticipantByUserUpdateData at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateParticipantByUserUpdateDataMock.expectedInvocations), m.UpdateParticipantByUserUpdateDataMock.expectedInvocationsOrigin, afterUpdateParticipantByUserUpdateDataCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ParticipantDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateParticipantInspect()

			m.MinimockDeleteInspect()

			m.MinimockDeleteByParticipantIDAndProductIDInspect()

			m.MinimockDeleteByUserIDAndGroupIDsInspect()

			m.MinimockDeleteByUserIDAndProductIDsInspect()

			m.MinimockDeleteByUserIDAndRoleIDsInspect()

			m.MinimockExistByParticipantIDAndProductIDInspect()

			m.MinimockGetByParticipantIDAndProductIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetByUserIDAndProductIDInspect()

			m.MinimockGetByUserIDAndProductIDsInspect()

			m.MinimockGetOwnersByProductIDsInspect()

			m.MinimockGetParticipantFullByParticipantIDAndProductIDInspect()

			m.MinimockGetParticipantFullByProductIDInspect()

			m.MinimockUpdateGroupsInspect()

			m.MinimockUpdateOwnersInspect()

			m.MinimockUpdateParticipantByUserUpdateDataInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ParticipantDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ParticipantDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateParticipantDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockDeleteByParticipantIDAndProductIDDone() &&
		m.MinimockDeleteByUserIDAndGroupIDsDone() &&
		m.MinimockDeleteByUserIDAndProductIDsDone() &&
		m.MinimockDeleteByUserIDAndRoleIDsDone() &&
		m.MinimockExistByParticipantIDAndProductIDDone() &&
		m.MinimockGetByParticipantIDAndProductIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetByUserIDAndProductIDDone() &&
		m.MinimockGetByUserIDAndProductIDsDone() &&
		m.MinimockGetOwnersByProductIDsDone() &&
		m.MinimockGetParticipantFullByParticipantIDAndProductIDDone() &&
		m.MinimockGetParticipantFullByProductIDDone() &&
		m.MinimockUpdateGroupsDone() &&
		m.MinimockUpdateOwnersDone() &&
		m.MinimockUpdateParticipantByUserUpdateDataDone()
}
