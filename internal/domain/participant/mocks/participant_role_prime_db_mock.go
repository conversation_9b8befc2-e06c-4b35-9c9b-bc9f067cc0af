// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository.ParticipantRolePrimeDB -o participant_role_prime_db_mock.go -n ParticipantRolePrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"github.com/gojuno/minimock/v3"
)

// ParticipantRolePrimeDBMock implements mm_repository.ParticipantRolePrimeDB
type ParticipantRolePrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, participantID int64, roleID int64) (p1 participantentity.ParticipantRole, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, participantID int64, roleID int64)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mParticipantRolePrimeDBMockCreate

	funcCreateByRoleIDAndParticipantIDs          func(ctx context.Context, roleID int64, participantIDs []int64) (err error)
	funcCreateByRoleIDAndParticipantIDsOrigin    string
	inspectFuncCreateByRoleIDAndParticipantIDs   func(ctx context.Context, roleID int64, participantIDs []int64)
	afterCreateByRoleIDAndParticipantIDsCounter  uint64
	beforeCreateByRoleIDAndParticipantIDsCounter uint64
	CreateByRoleIDAndParticipantIDsMock          mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs

	funcDeleteByParticipantAndRoleID          func(ctx context.Context, participantID int64, roleID int64) (err error)
	funcDeleteByParticipantAndRoleIDOrigin    string
	inspectFuncDeleteByParticipantAndRoleID   func(ctx context.Context, participantID int64, roleID int64)
	afterDeleteByParticipantAndRoleIDCounter  uint64
	beforeDeleteByParticipantAndRoleIDCounter uint64
	DeleteByParticipantAndRoleIDMock          mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID

	funcDeleteByParticipantID          func(participantID int64) (err error)
	funcDeleteByParticipantIDOrigin    string
	inspectFuncDeleteByParticipantID   func(participantID int64)
	afterDeleteByParticipantIDCounter  uint64
	beforeDeleteByParticipantIDCounter uint64
	DeleteByParticipantIDMock          mParticipantRolePrimeDBMockDeleteByParticipantID

	funcDeleteByParticipantIDs          func(participantIDs []int64) (err error)
	funcDeleteByParticipantIDsOrigin    string
	inspectFuncDeleteByParticipantIDs   func(participantIDs []int64)
	afterDeleteByParticipantIDsCounter  uint64
	beforeDeleteByParticipantIDsCounter uint64
	DeleteByParticipantIDsMock          mParticipantRolePrimeDBMockDeleteByParticipantIDs

	funcDeleteByRoleID          func(ctx context.Context, roleID int64) (err error)
	funcDeleteByRoleIDOrigin    string
	inspectFuncDeleteByRoleID   func(ctx context.Context, roleID int64)
	afterDeleteByRoleIDCounter  uint64
	beforeDeleteByRoleIDCounter uint64
	DeleteByRoleIDMock          mParticipantRolePrimeDBMockDeleteByRoleID

	funcDeleteByRoleIDAndParticipantIDs          func(ctx context.Context, roleID int64, participantIDs []int64) (err error)
	funcDeleteByRoleIDAndParticipantIDsOrigin    string
	inspectFuncDeleteByRoleIDAndParticipantIDs   func(ctx context.Context, roleID int64, participantIDs []int64)
	afterDeleteByRoleIDAndParticipantIDsCounter  uint64
	beforeDeleteByRoleIDAndParticipantIDsCounter uint64
	DeleteByRoleIDAndParticipantIDsMock          mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs

	funcGetAll          func() (pa1 []participantentity.ParticipantRole, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mParticipantRolePrimeDBMockGetAll

	funcGetByID          func(id int64) (p1 participantentity.ParticipantRole, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mParticipantRolePrimeDBMockGetByID

	funcGetByParticipantAndRoleID          func(participantID int64, roleID int64) (p1 participantentity.ParticipantRole, err error)
	funcGetByParticipantAndRoleIDOrigin    string
	inspectFuncGetByParticipantAndRoleID   func(participantID int64, roleID int64)
	afterGetByParticipantAndRoleIDCounter  uint64
	beforeGetByParticipantAndRoleIDCounter uint64
	GetByParticipantAndRoleIDMock          mParticipantRolePrimeDBMockGetByParticipantAndRoleID

	funcGetByParticipantID          func(participantID int64) (pa1 []participantentity.ParticipantRole, err error)
	funcGetByParticipantIDOrigin    string
	inspectFuncGetByParticipantID   func(participantID int64)
	afterGetByParticipantIDCounter  uint64
	beforeGetByParticipantIDCounter uint64
	GetByParticipantIDMock          mParticipantRolePrimeDBMockGetByParticipantID

	funcGetByParticipantIDs          func(participantIDs []int64) (pa1 []participantentity.ParticipantRole, err error)
	funcGetByParticipantIDsOrigin    string
	inspectFuncGetByParticipantIDs   func(participantIDs []int64)
	afterGetByParticipantIDsCounter  uint64
	beforeGetByParticipantIDsCounter uint64
	GetByParticipantIDsMock          mParticipantRolePrimeDBMockGetByParticipantIDs

	funcGetByRoleID          func(roleID int64) (pa1 []participantentity.ParticipantRole, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mParticipantRolePrimeDBMockGetByRoleID

	funcGetByRoleIDAndParticipantIDs          func(roleID int64, participantIDs []int64) (pa1 []participantentity.ParticipantRole, err error)
	funcGetByRoleIDAndParticipantIDsOrigin    string
	inspectFuncGetByRoleIDAndParticipantIDs   func(roleID int64, participantIDs []int64)
	afterGetByRoleIDAndParticipantIDsCounter  uint64
	beforeGetByRoleIDAndParticipantIDsCounter uint64
	GetByRoleIDAndParticipantIDsMock          mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs

	funcGetProductOwnersByParticipantID          func(participantID int64) (pa1 []participantentity.ParticipantRole, err error)
	funcGetProductOwnersByParticipantIDOrigin    string
	inspectFuncGetProductOwnersByParticipantID   func(participantID int64)
	afterGetProductOwnersByParticipantIDCounter  uint64
	beforeGetProductOwnersByParticipantIDCounter uint64
	GetProductOwnersByParticipantIDMock          mParticipantRolePrimeDBMockGetProductOwnersByParticipantID

	funcIsOwner          func(participantID int64) (b1 bool, err error)
	funcIsOwnerOrigin    string
	inspectFuncIsOwner   func(participantID int64)
	afterIsOwnerCounter  uint64
	beforeIsOwnerCounter uint64
	IsOwnerMock          mParticipantRolePrimeDBMockIsOwner
}

// NewParticipantRolePrimeDBMock returns a mock for mm_repository.ParticipantRolePrimeDB
func NewParticipantRolePrimeDBMock(t minimock.Tester) *ParticipantRolePrimeDBMock {
	m := &ParticipantRolePrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mParticipantRolePrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*ParticipantRolePrimeDBMockCreateParams{}

	m.CreateByRoleIDAndParticipantIDsMock = mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs{mock: m}
	m.CreateByRoleIDAndParticipantIDsMock.callArgs = []*ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams{}

	m.DeleteByParticipantAndRoleIDMock = mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID{mock: m}
	m.DeleteByParticipantAndRoleIDMock.callArgs = []*ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams{}

	m.DeleteByParticipantIDMock = mParticipantRolePrimeDBMockDeleteByParticipantID{mock: m}
	m.DeleteByParticipantIDMock.callArgs = []*ParticipantRolePrimeDBMockDeleteByParticipantIDParams{}

	m.DeleteByParticipantIDsMock = mParticipantRolePrimeDBMockDeleteByParticipantIDs{mock: m}
	m.DeleteByParticipantIDsMock.callArgs = []*ParticipantRolePrimeDBMockDeleteByParticipantIDsParams{}

	m.DeleteByRoleIDMock = mParticipantRolePrimeDBMockDeleteByRoleID{mock: m}
	m.DeleteByRoleIDMock.callArgs = []*ParticipantRolePrimeDBMockDeleteByRoleIDParams{}

	m.DeleteByRoleIDAndParticipantIDsMock = mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs{mock: m}
	m.DeleteByRoleIDAndParticipantIDsMock.callArgs = []*ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams{}

	m.GetAllMock = mParticipantRolePrimeDBMockGetAll{mock: m}

	m.GetByIDMock = mParticipantRolePrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ParticipantRolePrimeDBMockGetByIDParams{}

	m.GetByParticipantAndRoleIDMock = mParticipantRolePrimeDBMockGetByParticipantAndRoleID{mock: m}
	m.GetByParticipantAndRoleIDMock.callArgs = []*ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams{}

	m.GetByParticipantIDMock = mParticipantRolePrimeDBMockGetByParticipantID{mock: m}
	m.GetByParticipantIDMock.callArgs = []*ParticipantRolePrimeDBMockGetByParticipantIDParams{}

	m.GetByParticipantIDsMock = mParticipantRolePrimeDBMockGetByParticipantIDs{mock: m}
	m.GetByParticipantIDsMock.callArgs = []*ParticipantRolePrimeDBMockGetByParticipantIDsParams{}

	m.GetByRoleIDMock = mParticipantRolePrimeDBMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*ParticipantRolePrimeDBMockGetByRoleIDParams{}

	m.GetByRoleIDAndParticipantIDsMock = mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs{mock: m}
	m.GetByRoleIDAndParticipantIDsMock.callArgs = []*ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams{}

	m.GetProductOwnersByParticipantIDMock = mParticipantRolePrimeDBMockGetProductOwnersByParticipantID{mock: m}
	m.GetProductOwnersByParticipantIDMock.callArgs = []*ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams{}

	m.IsOwnerMock = mParticipantRolePrimeDBMockIsOwner{mock: m}
	m.IsOwnerMock.callArgs = []*ParticipantRolePrimeDBMockIsOwnerParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mParticipantRolePrimeDBMockCreate struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockCreateExpectation
	expectations       []*ParticipantRolePrimeDBMockCreateExpectation

	callArgs []*ParticipantRolePrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockCreateExpectation specifies expectation struct of the ParticipantRolePrimeDB.Create
type ParticipantRolePrimeDBMockCreateExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockCreateParams
	paramPtrs          *ParticipantRolePrimeDBMockCreateParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockCreateExpectationOrigins
	results            *ParticipantRolePrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockCreateParams contains parameters of the ParticipantRolePrimeDB.Create
type ParticipantRolePrimeDBMockCreateParams struct {
	ctx           context.Context
	participantID int64
	roleID        int64
}

// ParticipantRolePrimeDBMockCreateParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.Create
type ParticipantRolePrimeDBMockCreateParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	roleID        *int64
}

// ParticipantRolePrimeDBMockCreateResults contains results of the ParticipantRolePrimeDB.Create
type ParticipantRolePrimeDBMockCreateResults struct {
	p1  participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockCreateOrigins contains origins of expectations of the ParticipantRolePrimeDB.Create
type ParticipantRolePrimeDBMockCreateExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originRoleID        string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mParticipantRolePrimeDBMockCreate) Optional() *mParticipantRolePrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ParticipantRolePrimeDB.Create
func (mmCreate *mParticipantRolePrimeDBMockCreate) Expect(ctx context.Context, participantID int64, roleID int64) *mParticipantRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ParticipantRolePrimeDBMockCreateParams{ctx, participantID, roleID}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantRolePrimeDB.Create
func (mmCreate *mParticipantRolePrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mParticipantRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantRolePrimeDB.Create
func (mmCreate *mParticipantRolePrimeDBMockCreate) ExpectParticipantIDParam2(participantID int64) *mParticipantRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.participantID = &participantID
	mmCreate.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectRoleIDParam3 sets up expected param roleID for ParticipantRolePrimeDB.Create
func (mmCreate *mParticipantRolePrimeDBMockCreate) ExpectRoleIDParam3(roleID int64) *mParticipantRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.roleID = &roleID
	mmCreate.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.Create
func (mmCreate *mParticipantRolePrimeDBMockCreate) Inspect(f func(ctx context.Context, participantID int64, roleID int64)) *mParticipantRolePrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.Create
func (mmCreate *mParticipantRolePrimeDBMockCreate) Return(p1 participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantRolePrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ParticipantRolePrimeDBMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.Create method
func (mmCreate *mParticipantRolePrimeDBMockCreate) Set(f func(ctx context.Context, participantID int64, roleID int64) (p1 participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ParticipantRolePrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mParticipantRolePrimeDBMockCreate) When(ctx context.Context, participantID int64, roleID int64) *ParticipantRolePrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantRolePrimeDBMock.Create mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ParticipantRolePrimeDBMockCreateParams{ctx, participantID, roleID},
		expectationOrigins: ParticipantRolePrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockCreateExpectation) Then(p1 participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.Create should be invoked
func (mmCreate *mParticipantRolePrimeDBMockCreate) Times(n uint64) *mParticipantRolePrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mParticipantRolePrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.ParticipantRolePrimeDB
func (mmCreate *ParticipantRolePrimeDBMock) Create(ctx context.Context, participantID int64, roleID int64) (p1 participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, participantID, roleID)
	}

	mm_params := ParticipantRolePrimeDBMockCreateParams{ctx, participantID, roleID}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockCreateParams{ctx, participantID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("ParticipantRolePrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmCreate.t.Errorf("ParticipantRolePrimeDBMock.Create got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmCreate.t.Errorf("ParticipantRolePrimeDBMock.Create got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ParticipantRolePrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, participantID, roleID)
	}
	mmCreate.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.Create. %v %v %v", ctx, participantID, roleID)
	return
}

// CreateAfterCounter returns a count of finished ParticipantRolePrimeDBMock.Create invocations
func (mmCreate *ParticipantRolePrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ParticipantRolePrimeDBMock.Create invocations
func (mmCreate *ParticipantRolePrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mParticipantRolePrimeDBMockCreate) Calls() []*ParticipantRolePrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation
	expectations       []*ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation

	callArgs []*ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation specifies expectation struct of the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams
	paramPtrs          *ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectationOrigins
	results            *ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams contains parameters of the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams struct {
	ctx            context.Context
	roleID         int64
	participantIDs []int64
}

// ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs struct {
	ctx            *context.Context
	roleID         *int64
	participantIDs *[]int64
}

// ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsResults contains results of the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsResults struct {
	err error
}

// ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsOrigins contains origins of expectations of the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectationOrigins struct {
	origin               string
	originCtx            string
	originRoleID         string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Optional() *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	mmCreateByRoleIDAndParticipantIDs.optional = true
	return mmCreateByRoleIDAndParticipantIDs
}

// Expect sets up expected params for ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Expect(ctx context.Context, roleID int64, participantIDs []int64) *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by ExpectParams functions")
	}

	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByRoleIDAndParticipantIDs.expectations {
		if minimock.Equal(e.params, mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params) {
			mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params)
		}
	}

	return mmCreateByRoleIDAndParticipantIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) ExpectCtxParam1(ctx context.Context) *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndParticipantIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) ExpectRoleIDParam2(roleID int64) *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndParticipantIDs
}

// ExpectParticipantIDsParam3 sets up expected param participantIDs for ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) ExpectParticipantIDsParam3(participantIDs []int64) *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Inspect(f func(ctx context.Context, roleID int64, participantIDs []int64)) *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.inspectFuncCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs")
	}

	mmCreateByRoleIDAndParticipantIDs.mock.inspectFuncCreateByRoleIDAndParticipantIDs = f

	return mmCreateByRoleIDAndParticipantIDs
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Return(err error) *ParticipantRolePrimeDBMock {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{mock: mmCreateByRoleIDAndParticipantIDs.mock}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.results = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsResults{err}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs method
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Set(f func(ctx context.Context, roleID int64, participantIDs []int64) (err error)) *ParticipantRolePrimeDBMock {
	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs method")
	}

	if len(mmCreateByRoleIDAndParticipantIDs.expectations) > 0 {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs method")
	}

	mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs = f
	mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndParticipantIDs.mock
}

// When sets expectation for the ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) When(ctx context.Context, roleID int64, participantIDs []int64) *ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{
		mock:               mmCreateByRoleIDAndParticipantIDs.mock,
		params:             &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs},
		expectationOrigins: ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByRoleIDAndParticipantIDs.expectations = append(mmCreateByRoleIDAndParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsExpectation) Then(err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.CreateByRoleIDAndParticipantIDs should be invoked
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Times(n uint64) *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs {
	if n == 0 {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByRoleIDAndParticipantIDs.expectedInvocations, n)
	mmCreateByRoleIDAndParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndParticipantIDs
}

func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) invocationsDone() bool {
	if len(mmCreateByRoleIDAndParticipantIDs.expectations) == 0 && mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil && mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.mock.afterCreateByRoleIDAndParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByRoleIDAndParticipantIDs implements mm_repository.ParticipantRolePrimeDB
func (mmCreateByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) CreateByRoleIDAndParticipantIDs(ctx context.Context, roleID int64, participantIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByRoleIDAndParticipantIDs.beforeCreateByRoleIDAndParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByRoleIDAndParticipantIDs.afterCreateByRoleIDAndParticipantIDsCounter, 1)

	mmCreateByRoleIDAndParticipantIDs.t.Helper()

	if mmCreateByRoleIDAndParticipantIDs.inspectFuncCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.inspectFuncCreateByRoleIDAndParticipantIDs(ctx, roleID, participantIDs)
	}

	mm_params := ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs}

	// Record call args
	mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.mutex.Lock()
	mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.callArgs = append(mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.callArgs, &mm_params)
	mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.mutex.Unlock()

	for _, e := range mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByRoleIDAndParticipantIDs.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByRoleIDAndParticipantIDs.funcCreateByRoleIDAndParticipantIDs != nil {
		return mmCreateByRoleIDAndParticipantIDs.funcCreateByRoleIDAndParticipantIDs(ctx, roleID, participantIDs)
	}
	mmCreateByRoleIDAndParticipantIDs.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs. %v %v %v", ctx, roleID, participantIDs)
	return
}

// CreateByRoleIDAndParticipantIDsAfterCounter returns a count of finished ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs invocations
func (mmCreateByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) CreateByRoleIDAndParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.afterCreateByRoleIDAndParticipantIDsCounter)
}

// CreateByRoleIDAndParticipantIDsBeforeCounter returns a count of ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs invocations
func (mmCreateByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) CreateByRoleIDAndParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.beforeCreateByRoleIDAndParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDs) Calls() []*ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams {
	mmCreateByRoleIDAndParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockCreateByRoleIDAndParticipantIDsParams, len(mmCreateByRoleIDAndParticipantIDs.callArgs))
	copy(argCopy, mmCreateByRoleIDAndParticipantIDs.callArgs)

	mmCreateByRoleIDAndParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByRoleIDAndParticipantIDsDone returns true if the count of the CreateByRoleIDAndParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockCreateByRoleIDAndParticipantIDsDone() bool {
	if m.CreateByRoleIDAndParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByRoleIDAndParticipantIDsMock.invocationsDone()
}

// MinimockCreateByRoleIDAndParticipantIDsInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockCreateByRoleIDAndParticipantIDsInspect() {
	for _, e := range m.CreateByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByRoleIDAndParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByRoleIDAndParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation != nil && afterCreateByRoleIDAndParticipantIDsCounter < 1 {
		if m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s", m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s with params: %#v", m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByRoleIDAndParticipantIDs != nil && afterCreateByRoleIDAndParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s", m.funcCreateByRoleIDAndParticipantIDsOrigin)
	}

	if !m.CreateByRoleIDAndParticipantIDsMock.invocationsDone() && afterCreateByRoleIDAndParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByRoleIDAndParticipantIDsMock.expectedInvocations), m.CreateByRoleIDAndParticipantIDsMock.expectedInvocationsOrigin, afterCreateByRoleIDAndParticipantIDsCounter)
	}
}

type mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation
	expectations       []*ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation

	callArgs []*ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
type ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams
	paramPtrs          *ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams contains parameters of the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
type ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams struct {
	ctx           context.Context
	participantID int64
	roleID        int64
}

// ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
type ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	roleID        *int64
}

// ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDResults contains results of the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
type ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDResults struct {
	err error
}

// ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
type ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originRoleID        string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Optional() *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	mmDeleteByParticipantAndRoleID.optional = true
	return mmDeleteByParticipantAndRoleID
}

// Expect sets up expected params for ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Expect(ctx context.Context, participantID int64, roleID int64) *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	if mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Set")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation{}
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantAndRoleID.defaultExpectation.params = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams{ctx, participantID, roleID}
	mmDeleteByParticipantAndRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantAndRoleID.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantAndRoleID.defaultExpectation.params) {
			mmDeleteByParticipantAndRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantAndRoleID.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantAndRoleID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) ExpectCtxParam1(ctx context.Context) *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	if mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Set")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation{}
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.params != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Expect")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParamPtrs{}
	}
	mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByParticipantAndRoleID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByParticipantAndRoleID
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) ExpectParticipantIDParam2(participantID int64) *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	if mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Set")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation{}
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.params != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Expect")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParamPtrs{}
	}
	mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs.participantID = &participantID
	mmDeleteByParticipantAndRoleID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDeleteByParticipantAndRoleID
}

// ExpectRoleIDParam3 sets up expected param roleID for ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) ExpectRoleIDParam3(roleID int64) *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	if mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Set")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation{}
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.params != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Expect")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParamPtrs{}
	}
	mmDeleteByParticipantAndRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByParticipantAndRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByParticipantAndRoleID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Inspect(f func(ctx context.Context, participantID int64, roleID int64)) *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	if mmDeleteByParticipantAndRoleID.mock.inspectFuncDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID")
	}

	mmDeleteByParticipantAndRoleID.mock.inspectFuncDeleteByParticipantAndRoleID = f

	return mmDeleteByParticipantAndRoleID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.DeleteByParticipantAndRoleID
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Return(err error) *ParticipantRolePrimeDBMock {
	if mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Set")
	}

	if mmDeleteByParticipantAndRoleID.defaultExpectation == nil {
		mmDeleteByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation{mock: mmDeleteByParticipantAndRoleID.mock}
	}
	mmDeleteByParticipantAndRoleID.defaultExpectation.results = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDResults{err}
	mmDeleteByParticipantAndRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantAndRoleID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID method
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Set(f func(ctx context.Context, participantID int64, roleID int64) (err error)) *ParticipantRolePrimeDBMock {
	if mmDeleteByParticipantAndRoleID.defaultExpectation != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID method")
	}

	if len(mmDeleteByParticipantAndRoleID.expectations) > 0 {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID method")
	}

	mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID = f
	mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantAndRoleID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.DeleteByParticipantAndRoleID which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) When(ctx context.Context, participantID int64, roleID int64) *ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation {
	if mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation{
		mock:               mmDeleteByParticipantAndRoleID.mock,
		params:             &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams{ctx, participantID, roleID},
		expectationOrigins: ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantAndRoleID.expectations = append(mmDeleteByParticipantAndRoleID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.DeleteByParticipantAndRoleID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDExpectation) Then(err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.DeleteByParticipantAndRoleID should be invoked
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Times(n uint64) *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID {
	if n == 0 {
		mmDeleteByParticipantAndRoleID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantAndRoleID.expectedInvocations, n)
	mmDeleteByParticipantAndRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantAndRoleID
}

func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) invocationsDone() bool {
	if len(mmDeleteByParticipantAndRoleID.expectations) == 0 && mmDeleteByParticipantAndRoleID.defaultExpectation == nil && mmDeleteByParticipantAndRoleID.mock.funcDeleteByParticipantAndRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantAndRoleID.mock.afterDeleteByParticipantAndRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantAndRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantAndRoleID implements mm_repository.ParticipantRolePrimeDB
func (mmDeleteByParticipantAndRoleID *ParticipantRolePrimeDBMock) DeleteByParticipantAndRoleID(ctx context.Context, participantID int64, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantAndRoleID.beforeDeleteByParticipantAndRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantAndRoleID.afterDeleteByParticipantAndRoleIDCounter, 1)

	mmDeleteByParticipantAndRoleID.t.Helper()

	if mmDeleteByParticipantAndRoleID.inspectFuncDeleteByParticipantAndRoleID != nil {
		mmDeleteByParticipantAndRoleID.inspectFuncDeleteByParticipantAndRoleID(ctx, participantID, roleID)
	}

	mm_params := ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams{ctx, participantID, roleID}

	// Record call args
	mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.mutex.Lock()
	mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.callArgs = append(mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.callArgs, &mm_params)
	mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams{ctx, participantID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDeleteByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantAndRoleID.DeleteByParticipantAndRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantAndRoleID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantAndRoleID.funcDeleteByParticipantAndRoleID != nil {
		return mmDeleteByParticipantAndRoleID.funcDeleteByParticipantAndRoleID(ctx, participantID, roleID)
	}
	mmDeleteByParticipantAndRoleID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID. %v %v %v", ctx, participantID, roleID)
	return
}

// DeleteByParticipantAndRoleIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID invocations
func (mmDeleteByParticipantAndRoleID *ParticipantRolePrimeDBMock) DeleteByParticipantAndRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantAndRoleID.afterDeleteByParticipantAndRoleIDCounter)
}

// DeleteByParticipantAndRoleIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID invocations
func (mmDeleteByParticipantAndRoleID *ParticipantRolePrimeDBMock) DeleteByParticipantAndRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantAndRoleID.beforeDeleteByParticipantAndRoleIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantAndRoleID *mParticipantRolePrimeDBMockDeleteByParticipantAndRoleID) Calls() []*ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams {
	mmDeleteByParticipantAndRoleID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockDeleteByParticipantAndRoleIDParams, len(mmDeleteByParticipantAndRoleID.callArgs))
	copy(argCopy, mmDeleteByParticipantAndRoleID.callArgs)

	mmDeleteByParticipantAndRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantAndRoleIDDone returns true if the count of the DeleteByParticipantAndRoleID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByParticipantAndRoleIDDone() bool {
	if m.DeleteByParticipantAndRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantAndRoleIDMock.invocationsDone()
}

// MinimockDeleteByParticipantAndRoleIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByParticipantAndRoleIDInspect() {
	for _, e := range m.DeleteByParticipantAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantAndRoleIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantAndRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantAndRoleIDMock.defaultExpectation != nil && afterDeleteByParticipantAndRoleIDCounter < 1 {
		if m.DeleteByParticipantAndRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID at\n%s", m.DeleteByParticipantAndRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID at\n%s with params: %#v", m.DeleteByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantAndRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantAndRoleID != nil && afterDeleteByParticipantAndRoleIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID at\n%s", m.funcDeleteByParticipantAndRoleIDOrigin)
	}

	if !m.DeleteByParticipantAndRoleIDMock.invocationsDone() && afterDeleteByParticipantAndRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.DeleteByParticipantAndRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantAndRoleIDMock.expectedInvocations), m.DeleteByParticipantAndRoleIDMock.expectedInvocationsOrigin, afterDeleteByParticipantAndRoleIDCounter)
	}
}

type mParticipantRolePrimeDBMockDeleteByParticipantID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation
	expectations       []*ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation

	callArgs []*ParticipantRolePrimeDBMockDeleteByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.DeleteByParticipantID
type ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockDeleteByParticipantIDParams
	paramPtrs          *ParticipantRolePrimeDBMockDeleteByParticipantIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockDeleteByParticipantIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockDeleteByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDParams contains parameters of the ParticipantRolePrimeDB.DeleteByParticipantID
type ParticipantRolePrimeDBMockDeleteByParticipantIDParams struct {
	participantID int64
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.DeleteByParticipantID
type ParticipantRolePrimeDBMockDeleteByParticipantIDParamPtrs struct {
	participantID *int64
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDResults contains results of the ParticipantRolePrimeDB.DeleteByParticipantID
type ParticipantRolePrimeDBMockDeleteByParticipantIDResults struct {
	err error
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.DeleteByParticipantID
type ParticipantRolePrimeDBMockDeleteByParticipantIDExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Optional() *mParticipantRolePrimeDBMockDeleteByParticipantID {
	mmDeleteByParticipantID.optional = true
	return mmDeleteByParticipantID
}

// Expect sets up expected params for ParticipantRolePrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Expect(participantID int64) *mParticipantRolePrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation{}
	}

	if mmDeleteByParticipantID.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantID mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantID.defaultExpectation.params = &ParticipantRolePrimeDBMockDeleteByParticipantIDParams{participantID}
	mmDeleteByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantID.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantID.defaultExpectation.params) {
			mmDeleteByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantID.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantRolePrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) ExpectParticipantIDParam1(participantID int64) *mParticipantRolePrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation{}
	}

	if mmDeleteByParticipantID.defaultExpectation.params != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantID mock is already set by Expect")
	}

	if mmDeleteByParticipantID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByParticipantIDParamPtrs{}
	}
	mmDeleteByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmDeleteByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDeleteByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Inspect(f func(participantID int64)) *mParticipantRolePrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.inspectFuncDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.DeleteByParticipantID")
	}

	mmDeleteByParticipantID.mock.inspectFuncDeleteByParticipantID = f

	return mmDeleteByParticipantID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Return(err error) *ParticipantRolePrimeDBMock {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation{mock: mmDeleteByParticipantID.mock}
	}
	mmDeleteByParticipantID.defaultExpectation.results = &ParticipantRolePrimeDBMockDeleteByParticipantIDResults{err}
	mmDeleteByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.DeleteByParticipantID method
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Set(f func(participantID int64) (err error)) *ParticipantRolePrimeDBMock {
	if mmDeleteByParticipantID.defaultExpectation != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.DeleteByParticipantID method")
	}

	if len(mmDeleteByParticipantID.expectations) > 0 {
		mmDeleteByParticipantID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.DeleteByParticipantID method")
	}

	mmDeleteByParticipantID.mock.funcDeleteByParticipantID = f
	mmDeleteByParticipantID.mock.funcDeleteByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.DeleteByParticipantID which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) When(participantID int64) *ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation{
		mock:               mmDeleteByParticipantID.mock,
		params:             &ParticipantRolePrimeDBMockDeleteByParticipantIDParams{participantID},
		expectationOrigins: ParticipantRolePrimeDBMockDeleteByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantID.expectations = append(mmDeleteByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.DeleteByParticipantID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockDeleteByParticipantIDExpectation) Then(err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockDeleteByParticipantIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.DeleteByParticipantID should be invoked
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Times(n uint64) *mParticipantRolePrimeDBMockDeleteByParticipantID {
	if n == 0 {
		mmDeleteByParticipantID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.DeleteByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantID.expectedInvocations, n)
	mmDeleteByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantID
}

func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) invocationsDone() bool {
	if len(mmDeleteByParticipantID.expectations) == 0 && mmDeleteByParticipantID.defaultExpectation == nil && mmDeleteByParticipantID.mock.funcDeleteByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantID.mock.afterDeleteByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantID implements mm_repository.ParticipantRolePrimeDB
func (mmDeleteByParticipantID *ParticipantRolePrimeDBMock) DeleteByParticipantID(participantID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantID.beforeDeleteByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantID.afterDeleteByParticipantIDCounter, 1)

	mmDeleteByParticipantID.t.Helper()

	if mmDeleteByParticipantID.inspectFuncDeleteByParticipantID != nil {
		mmDeleteByParticipantID.inspectFuncDeleteByParticipantID(participantID)
	}

	mm_params := ParticipantRolePrimeDBMockDeleteByParticipantIDParams{participantID}

	// Record call args
	mmDeleteByParticipantID.DeleteByParticipantIDMock.mutex.Lock()
	mmDeleteByParticipantID.DeleteByParticipantIDMock.callArgs = append(mmDeleteByParticipantID.DeleteByParticipantIDMock.callArgs, &mm_params)
	mmDeleteByParticipantID.DeleteByParticipantIDMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantID.DeleteByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockDeleteByParticipantIDParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDeleteByParticipantID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.DeleteByParticipantID")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantID.funcDeleteByParticipantID != nil {
		return mmDeleteByParticipantID.funcDeleteByParticipantID(participantID)
	}
	mmDeleteByParticipantID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.DeleteByParticipantID. %v", participantID)
	return
}

// DeleteByParticipantIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.DeleteByParticipantID invocations
func (mmDeleteByParticipantID *ParticipantRolePrimeDBMock) DeleteByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantID.afterDeleteByParticipantIDCounter)
}

// DeleteByParticipantIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.DeleteByParticipantID invocations
func (mmDeleteByParticipantID *ParticipantRolePrimeDBMock) DeleteByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantID.beforeDeleteByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.DeleteByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantID *mParticipantRolePrimeDBMockDeleteByParticipantID) Calls() []*ParticipantRolePrimeDBMockDeleteByParticipantIDParams {
	mmDeleteByParticipantID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockDeleteByParticipantIDParams, len(mmDeleteByParticipantID.callArgs))
	copy(argCopy, mmDeleteByParticipantID.callArgs)

	mmDeleteByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantIDDone returns true if the count of the DeleteByParticipantID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByParticipantIDDone() bool {
	if m.DeleteByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantIDMock.invocationsDone()
}

// MinimockDeleteByParticipantIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByParticipantIDInspect() {
	for _, e := range m.DeleteByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantIDMock.defaultExpectation != nil && afterDeleteByParticipantIDCounter < 1 {
		if m.DeleteByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantID at\n%s", m.DeleteByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantID at\n%s with params: %#v", m.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantID != nil && afterDeleteByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantID at\n%s", m.funcDeleteByParticipantIDOrigin)
	}

	if !m.DeleteByParticipantIDMock.invocationsDone() && afterDeleteByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.DeleteByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantIDMock.expectedInvocations), m.DeleteByParticipantIDMock.expectedInvocationsOrigin, afterDeleteByParticipantIDCounter)
	}
}

type mParticipantRolePrimeDBMockDeleteByParticipantIDs struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation
	expectations       []*ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation

	callArgs []*ParticipantRolePrimeDBMockDeleteByParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation specifies expectation struct of the ParticipantRolePrimeDB.DeleteByParticipantIDs
type ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockDeleteByParticipantIDsParams
	paramPtrs          *ParticipantRolePrimeDBMockDeleteByParticipantIDsParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectationOrigins
	results            *ParticipantRolePrimeDBMockDeleteByParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDsParams contains parameters of the ParticipantRolePrimeDB.DeleteByParticipantIDs
type ParticipantRolePrimeDBMockDeleteByParticipantIDsParams struct {
	participantIDs []int64
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDsParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.DeleteByParticipantIDs
type ParticipantRolePrimeDBMockDeleteByParticipantIDsParamPtrs struct {
	participantIDs *[]int64
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDsResults contains results of the ParticipantRolePrimeDB.DeleteByParticipantIDs
type ParticipantRolePrimeDBMockDeleteByParticipantIDsResults struct {
	err error
}

// ParticipantRolePrimeDBMockDeleteByParticipantIDsOrigins contains origins of expectations of the ParticipantRolePrimeDB.DeleteByParticipantIDs
type ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectationOrigins struct {
	origin               string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Optional() *mParticipantRolePrimeDBMockDeleteByParticipantIDs {
	mmDeleteByParticipantIDs.optional = true
	return mmDeleteByParticipantIDs
}

// Expect sets up expected params for ParticipantRolePrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Expect(participantIDs []int64) *mParticipantRolePrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation{}
	}

	if mmDeleteByParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantIDs.defaultExpectation.params = &ParticipantRolePrimeDBMockDeleteByParticipantIDsParams{participantIDs}
	mmDeleteByParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantIDs.defaultExpectation.params) {
			mmDeleteByParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantIDs
}

// ExpectParticipantIDsParam1 sets up expected param participantIDs for ParticipantRolePrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) ExpectParticipantIDsParam1(participantIDs []int64) *mParticipantRolePrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation{}
	}

	if mmDeleteByParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByParticipantIDsParamPtrs{}
	}
	mmDeleteByParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmDeleteByParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmDeleteByParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Inspect(f func(participantIDs []int64)) *mParticipantRolePrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.inspectFuncDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.DeleteByParticipantIDs")
	}

	mmDeleteByParticipantIDs.mock.inspectFuncDeleteByParticipantIDs = f

	return mmDeleteByParticipantIDs
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Return(err error) *ParticipantRolePrimeDBMock {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation{mock: mmDeleteByParticipantIDs.mock}
	}
	mmDeleteByParticipantIDs.defaultExpectation.results = &ParticipantRolePrimeDBMockDeleteByParticipantIDsResults{err}
	mmDeleteByParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.DeleteByParticipantIDs method
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Set(f func(participantIDs []int64) (err error)) *ParticipantRolePrimeDBMock {
	if mmDeleteByParticipantIDs.defaultExpectation != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.DeleteByParticipantIDs method")
	}

	if len(mmDeleteByParticipantIDs.expectations) > 0 {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.DeleteByParticipantIDs method")
	}

	mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs = f
	mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDs.mock
}

// When sets expectation for the ParticipantRolePrimeDB.DeleteByParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) When(participantIDs []int64) *ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation{
		mock:               mmDeleteByParticipantIDs.mock,
		params:             &ParticipantRolePrimeDBMockDeleteByParticipantIDsParams{participantIDs},
		expectationOrigins: ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantIDs.expectations = append(mmDeleteByParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.DeleteByParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockDeleteByParticipantIDsExpectation) Then(err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockDeleteByParticipantIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.DeleteByParticipantIDs should be invoked
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Times(n uint64) *mParticipantRolePrimeDBMockDeleteByParticipantIDs {
	if n == 0 {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.DeleteByParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantIDs.expectedInvocations, n)
	mmDeleteByParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDs
}

func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) invocationsDone() bool {
	if len(mmDeleteByParticipantIDs.expectations) == 0 && mmDeleteByParticipantIDs.defaultExpectation == nil && mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.mock.afterDeleteByParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantIDs implements mm_repository.ParticipantRolePrimeDB
func (mmDeleteByParticipantIDs *ParticipantRolePrimeDBMock) DeleteByParticipantIDs(participantIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantIDs.beforeDeleteByParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantIDs.afterDeleteByParticipantIDsCounter, 1)

	mmDeleteByParticipantIDs.t.Helper()

	if mmDeleteByParticipantIDs.inspectFuncDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.inspectFuncDeleteByParticipantIDs(participantIDs)
	}

	mm_params := ParticipantRolePrimeDBMockDeleteByParticipantIDsParams{participantIDs}

	// Record call args
	mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.mutex.Lock()
	mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.callArgs = append(mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.callArgs, &mm_params)
	mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockDeleteByParticipantIDsParams{participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmDeleteByParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.DeleteByParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantIDs.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.DeleteByParticipantIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantIDs.funcDeleteByParticipantIDs != nil {
		return mmDeleteByParticipantIDs.funcDeleteByParticipantIDs(participantIDs)
	}
	mmDeleteByParticipantIDs.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.DeleteByParticipantIDs. %v", participantIDs)
	return
}

// DeleteByParticipantIDsAfterCounter returns a count of finished ParticipantRolePrimeDBMock.DeleteByParticipantIDs invocations
func (mmDeleteByParticipantIDs *ParticipantRolePrimeDBMock) DeleteByParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.afterDeleteByParticipantIDsCounter)
}

// DeleteByParticipantIDsBeforeCounter returns a count of ParticipantRolePrimeDBMock.DeleteByParticipantIDs invocations
func (mmDeleteByParticipantIDs *ParticipantRolePrimeDBMock) DeleteByParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.beforeDeleteByParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.DeleteByParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantIDs *mParticipantRolePrimeDBMockDeleteByParticipantIDs) Calls() []*ParticipantRolePrimeDBMockDeleteByParticipantIDsParams {
	mmDeleteByParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockDeleteByParticipantIDsParams, len(mmDeleteByParticipantIDs.callArgs))
	copy(argCopy, mmDeleteByParticipantIDs.callArgs)

	mmDeleteByParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantIDsDone returns true if the count of the DeleteByParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByParticipantIDsDone() bool {
	if m.DeleteByParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantIDsMock.invocationsDone()
}

// MinimockDeleteByParticipantIDsInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByParticipantIDsInspect() {
	for _, e := range m.DeleteByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantIDsMock.defaultExpectation != nil && afterDeleteByParticipantIDsCounter < 1 {
		if m.DeleteByParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantIDs at\n%s", m.DeleteByParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantIDs at\n%s with params: %#v", m.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantIDs != nil && afterDeleteByParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByParticipantIDs at\n%s", m.funcDeleteByParticipantIDsOrigin)
	}

	if !m.DeleteByParticipantIDsMock.invocationsDone() && afterDeleteByParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.DeleteByParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantIDsMock.expectedInvocations), m.DeleteByParticipantIDsMock.expectedInvocationsOrigin, afterDeleteByParticipantIDsCounter)
	}
}

type mParticipantRolePrimeDBMockDeleteByRoleID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockDeleteByRoleIDExpectation
	expectations       []*ParticipantRolePrimeDBMockDeleteByRoleIDExpectation

	callArgs []*ParticipantRolePrimeDBMockDeleteByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockDeleteByRoleIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.DeleteByRoleID
type ParticipantRolePrimeDBMockDeleteByRoleIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockDeleteByRoleIDParams
	paramPtrs          *ParticipantRolePrimeDBMockDeleteByRoleIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockDeleteByRoleIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockDeleteByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockDeleteByRoleIDParams contains parameters of the ParticipantRolePrimeDB.DeleteByRoleID
type ParticipantRolePrimeDBMockDeleteByRoleIDParams struct {
	ctx    context.Context
	roleID int64
}

// ParticipantRolePrimeDBMockDeleteByRoleIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.DeleteByRoleID
type ParticipantRolePrimeDBMockDeleteByRoleIDParamPtrs struct {
	ctx    *context.Context
	roleID *int64
}

// ParticipantRolePrimeDBMockDeleteByRoleIDResults contains results of the ParticipantRolePrimeDB.DeleteByRoleID
type ParticipantRolePrimeDBMockDeleteByRoleIDResults struct {
	err error
}

// ParticipantRolePrimeDBMockDeleteByRoleIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.DeleteByRoleID
type ParticipantRolePrimeDBMockDeleteByRoleIDExpectationOrigins struct {
	origin       string
	originCtx    string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Optional() *mParticipantRolePrimeDBMockDeleteByRoleID {
	mmDeleteByRoleID.optional = true
	return mmDeleteByRoleID
}

// Expect sets up expected params for ParticipantRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Expect(ctx context.Context, roleID int64) *mParticipantRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleID.defaultExpectation.params = &ParticipantRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID}
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleID.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleID.defaultExpectation.params) {
			mmDeleteByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleID.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) ExpectCtxParam1(ctx context.Context) *mParticipantRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.params != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Expect")
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByRoleIDParamPtrs{}
	}
	mmDeleteByRoleID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleID
}

// ExpectRoleIDParam2 sets up expected param roleID for ParticipantRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) ExpectRoleIDParam2(roleID int64) *mParticipantRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.params != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Expect")
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByRoleIDParamPtrs{}
	}
	mmDeleteByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRoleID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Inspect(f func(ctx context.Context, roleID int64)) *mParticipantRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.inspectFuncDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.DeleteByRoleID")
	}

	mmDeleteByRoleID.mock.inspectFuncDeleteByRoleID = f

	return mmDeleteByRoleID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Return(err error) *ParticipantRolePrimeDBMock {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDExpectation{mock: mmDeleteByRoleID.mock}
	}
	mmDeleteByRoleID.defaultExpectation.results = &ParticipantRolePrimeDBMockDeleteByRoleIDResults{err}
	mmDeleteByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.DeleteByRoleID method
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Set(f func(ctx context.Context, roleID int64) (err error)) *ParticipantRolePrimeDBMock {
	if mmDeleteByRoleID.defaultExpectation != nil {
		mmDeleteByRoleID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.DeleteByRoleID method")
	}

	if len(mmDeleteByRoleID.expectations) > 0 {
		mmDeleteByRoleID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.DeleteByRoleID method")
	}

	mmDeleteByRoleID.mock.funcDeleteByRoleID = f
	mmDeleteByRoleID.mock.funcDeleteByRoleIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.DeleteByRoleID which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) When(ctx context.Context, roleID int64) *ParticipantRolePrimeDBMockDeleteByRoleIDExpectation {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockDeleteByRoleIDExpectation{
		mock:               mmDeleteByRoleID.mock,
		params:             &ParticipantRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID},
		expectationOrigins: ParticipantRolePrimeDBMockDeleteByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleID.expectations = append(mmDeleteByRoleID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.DeleteByRoleID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockDeleteByRoleIDExpectation) Then(err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockDeleteByRoleIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.DeleteByRoleID should be invoked
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Times(n uint64) *mParticipantRolePrimeDBMockDeleteByRoleID {
	if n == 0 {
		mmDeleteByRoleID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.DeleteByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleID.expectedInvocations, n)
	mmDeleteByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID
}

func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) invocationsDone() bool {
	if len(mmDeleteByRoleID.expectations) == 0 && mmDeleteByRoleID.defaultExpectation == nil && mmDeleteByRoleID.mock.funcDeleteByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleID.mock.afterDeleteByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleID implements mm_repository.ParticipantRolePrimeDB
func (mmDeleteByRoleID *ParticipantRolePrimeDBMock) DeleteByRoleID(ctx context.Context, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleID.beforeDeleteByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleID.afterDeleteByRoleIDCounter, 1)

	mmDeleteByRoleID.t.Helper()

	if mmDeleteByRoleID.inspectFuncDeleteByRoleID != nil {
		mmDeleteByRoleID.inspectFuncDeleteByRoleID(ctx, roleID)
	}

	mm_params := ParticipantRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID}

	// Record call args
	mmDeleteByRoleID.DeleteByRoleIDMock.mutex.Lock()
	mmDeleteByRoleID.DeleteByRoleIDMock.callArgs = append(mmDeleteByRoleID.DeleteByRoleIDMock.callArgs, &mm_params)
	mmDeleteByRoleID.DeleteByRoleIDMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleID.DeleteByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleID.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.DeleteByRoleID")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleID.funcDeleteByRoleID != nil {
		return mmDeleteByRoleID.funcDeleteByRoleID(ctx, roleID)
	}
	mmDeleteByRoleID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.DeleteByRoleID. %v %v", ctx, roleID)
	return
}

// DeleteByRoleIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.DeleteByRoleID invocations
func (mmDeleteByRoleID *ParticipantRolePrimeDBMock) DeleteByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleID.afterDeleteByRoleIDCounter)
}

// DeleteByRoleIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.DeleteByRoleID invocations
func (mmDeleteByRoleID *ParticipantRolePrimeDBMock) DeleteByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleID.beforeDeleteByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.DeleteByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleID *mParticipantRolePrimeDBMockDeleteByRoleID) Calls() []*ParticipantRolePrimeDBMockDeleteByRoleIDParams {
	mmDeleteByRoleID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockDeleteByRoleIDParams, len(mmDeleteByRoleID.callArgs))
	copy(argCopy, mmDeleteByRoleID.callArgs)

	mmDeleteByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDDone returns true if the count of the DeleteByRoleID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByRoleIDDone() bool {
	if m.DeleteByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDMock.invocationsDone()
}

// MinimockDeleteByRoleIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByRoleIDInspect() {
	for _, e := range m.DeleteByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDMock.defaultExpectation != nil && afterDeleteByRoleIDCounter < 1 {
		if m.DeleteByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleID at\n%s", m.DeleteByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleID at\n%s with params: %#v", m.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleID != nil && afterDeleteByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleID at\n%s", m.funcDeleteByRoleIDOrigin)
	}

	if !m.DeleteByRoleIDMock.invocationsDone() && afterDeleteByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.DeleteByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDMock.expectedInvocations), m.DeleteByRoleIDMock.expectedInvocationsOrigin, afterDeleteByRoleIDCounter)
	}
}

type mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation
	expectations       []*ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation

	callArgs []*ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation specifies expectation struct of the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams
	paramPtrs          *ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectationOrigins
	results            *ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams contains parameters of the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams struct {
	ctx            context.Context
	roleID         int64
	participantIDs []int64
}

// ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs struct {
	ctx            *context.Context
	roleID         *int64
	participantIDs *[]int64
}

// ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsResults contains results of the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsResults struct {
	err error
}

// ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsOrigins contains origins of expectations of the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectationOrigins struct {
	origin               string
	originCtx            string
	originRoleID         string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Optional() *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	mmDeleteByRoleIDAndParticipantIDs.optional = true
	return mmDeleteByRoleIDAndParticipantIDs
}

// Expect sets up expected params for ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Expect(ctx context.Context, roleID int64, participantIDs []int64) *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleIDAndParticipantIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params) {
			mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleIDAndParticipantIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) ExpectCtxParam1(ctx context.Context) *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndParticipantIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) ExpectRoleIDParam2(roleID int64) *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndParticipantIDs
}

// ExpectParticipantIDsParam3 sets up expected param participantIDs for ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) ExpectParticipantIDsParam3(participantIDs []int64) *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Inspect(f func(ctx context.Context, roleID int64, participantIDs []int64)) *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.inspectFuncDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs")
	}

	mmDeleteByRoleIDAndParticipantIDs.mock.inspectFuncDeleteByRoleIDAndParticipantIDs = f

	return mmDeleteByRoleIDAndParticipantIDs
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Return(err error) *ParticipantRolePrimeDBMock {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{mock: mmDeleteByRoleIDAndParticipantIDs.mock}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.results = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsResults{err}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs method
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Set(f func(ctx context.Context, roleID int64, participantIDs []int64) (err error)) *ParticipantRolePrimeDBMock {
	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs method")
	}

	if len(mmDeleteByRoleIDAndParticipantIDs.expectations) > 0 {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs method")
	}

	mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs = f
	mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndParticipantIDs.mock
}

// When sets expectation for the ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) When(ctx context.Context, roleID int64, participantIDs []int64) *ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{
		mock:               mmDeleteByRoleIDAndParticipantIDs.mock,
		params:             &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs},
		expectationOrigins: ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleIDAndParticipantIDs.expectations = append(mmDeleteByRoleIDAndParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation) Then(err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.DeleteByRoleIDAndParticipantIDs should be invoked
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Times(n uint64) *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if n == 0 {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleIDAndParticipantIDs.expectedInvocations, n)
	mmDeleteByRoleIDAndParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndParticipantIDs
}

func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) invocationsDone() bool {
	if len(mmDeleteByRoleIDAndParticipantIDs.expectations) == 0 && mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil && mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.mock.afterDeleteByRoleIDAndParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleIDAndParticipantIDs implements mm_repository.ParticipantRolePrimeDB
func (mmDeleteByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) DeleteByRoleIDAndParticipantIDs(ctx context.Context, roleID int64, participantIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleIDAndParticipantIDs.beforeDeleteByRoleIDAndParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleIDAndParticipantIDs.afterDeleteByRoleIDAndParticipantIDsCounter, 1)

	mmDeleteByRoleIDAndParticipantIDs.t.Helper()

	if mmDeleteByRoleIDAndParticipantIDs.inspectFuncDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.inspectFuncDeleteByRoleIDAndParticipantIDs(ctx, roleID, participantIDs)
	}

	mm_params := ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs}

	// Record call args
	mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.mutex.Lock()
	mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.callArgs = append(mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.callArgs, &mm_params)
	mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, roleID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleIDAndParticipantIDs.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleIDAndParticipantIDs.funcDeleteByRoleIDAndParticipantIDs != nil {
		return mmDeleteByRoleIDAndParticipantIDs.funcDeleteByRoleIDAndParticipantIDs(ctx, roleID, participantIDs)
	}
	mmDeleteByRoleIDAndParticipantIDs.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs. %v %v %v", ctx, roleID, participantIDs)
	return
}

// DeleteByRoleIDAndParticipantIDsAfterCounter returns a count of finished ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs invocations
func (mmDeleteByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) DeleteByRoleIDAndParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.afterDeleteByRoleIDAndParticipantIDsCounter)
}

// DeleteByRoleIDAndParticipantIDsBeforeCounter returns a count of ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs invocations
func (mmDeleteByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) DeleteByRoleIDAndParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.beforeDeleteByRoleIDAndParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDs) Calls() []*ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams {
	mmDeleteByRoleIDAndParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockDeleteByRoleIDAndParticipantIDsParams, len(mmDeleteByRoleIDAndParticipantIDs.callArgs))
	copy(argCopy, mmDeleteByRoleIDAndParticipantIDs.callArgs)

	mmDeleteByRoleIDAndParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDAndParticipantIDsDone returns true if the count of the DeleteByRoleIDAndParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByRoleIDAndParticipantIDsDone() bool {
	if m.DeleteByRoleIDAndParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDAndParticipantIDsMock.invocationsDone()
}

// MinimockDeleteByRoleIDAndParticipantIDsInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockDeleteByRoleIDAndParticipantIDsInspect() {
	for _, e := range m.DeleteByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDAndParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDAndParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation != nil && afterDeleteByRoleIDAndParticipantIDsCounter < 1 {
		if m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s", m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s with params: %#v", m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleIDAndParticipantIDs != nil && afterDeleteByRoleIDAndParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s", m.funcDeleteByRoleIDAndParticipantIDsOrigin)
	}

	if !m.DeleteByRoleIDAndParticipantIDsMock.invocationsDone() && afterDeleteByRoleIDAndParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDAndParticipantIDsMock.expectedInvocations), m.DeleteByRoleIDAndParticipantIDsMock.expectedInvocationsOrigin, afterDeleteByRoleIDAndParticipantIDsCounter)
	}
}

type mParticipantRolePrimeDBMockGetAll struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetAllExpectation
	expectations       []*ParticipantRolePrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetAllExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetAll
type ParticipantRolePrimeDBMockGetAllExpectation struct {
	mock *ParticipantRolePrimeDBMock

	results      *ParticipantRolePrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// ParticipantRolePrimeDBMockGetAllResults contains results of the ParticipantRolePrimeDB.GetAll
type ParticipantRolePrimeDBMockGetAllResults struct {
	pa1 []participantentity.ParticipantRole
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mParticipantRolePrimeDBMockGetAll) Optional() *mParticipantRolePrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetAll
func (mmGetAll *mParticipantRolePrimeDBMockGetAll) Expect() *mParticipantRolePrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ParticipantRolePrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetAll
func (mmGetAll *mParticipantRolePrimeDBMockGetAll) Inspect(f func()) *mParticipantRolePrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetAll
func (mmGetAll *mParticipantRolePrimeDBMockGetAll) Return(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ParticipantRolePrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &ParticipantRolePrimeDBMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetAll method
func (mmGetAll *mParticipantRolePrimeDBMockGetAll) Set(f func() (pa1 []participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetAll should be invoked
func (mmGetAll *mParticipantRolePrimeDBMockGetAll) Times(n uint64) *mParticipantRolePrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mParticipantRolePrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.ParticipantRolePrimeDB
func (mmGetAll *ParticipantRolePrimeDBMock) GetAll() (pa1 []participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetAll invocations
func (mmGetAll *ParticipantRolePrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetAll invocations
func (mmGetAll *ParticipantRolePrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ParticipantRolePrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mParticipantRolePrimeDBMockGetByID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetByIDExpectation
	expectations       []*ParticipantRolePrimeDBMockGetByIDExpectation

	callArgs []*ParticipantRolePrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetByIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetByID
type ParticipantRolePrimeDBMockGetByIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetByIDParams
	paramPtrs          *ParticipantRolePrimeDBMockGetByIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetByIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetByIDParams contains parameters of the ParticipantRolePrimeDB.GetByID
type ParticipantRolePrimeDBMockGetByIDParams struct {
	id int64
}

// ParticipantRolePrimeDBMockGetByIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetByID
type ParticipantRolePrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// ParticipantRolePrimeDBMockGetByIDResults contains results of the ParticipantRolePrimeDB.GetByID
type ParticipantRolePrimeDBMockGetByIDResults struct {
	p1  participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetByIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetByID
type ParticipantRolePrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Optional() *mParticipantRolePrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetByID
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Expect(id int64) *mParticipantRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ParticipantRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ParticipantRolePrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ParticipantRolePrimeDB.GetByID
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) ExpectIdParam1(id int64) *mParticipantRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ParticipantRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetByID
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Inspect(f func(id int64)) *mParticipantRolePrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetByID
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Return(p1 participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ParticipantRolePrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ParticipantRolePrimeDBMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetByID method
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Set(f func(id int64) (p1 participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) When(id int64) *ParticipantRolePrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ParticipantRolePrimeDBMockGetByIDParams{id},
		expectationOrigins: ParticipantRolePrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetByIDExpectation) Then(p1 participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetByID should be invoked
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Times(n uint64) *mParticipantRolePrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mParticipantRolePrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.ParticipantRolePrimeDB
func (mmGetByID *ParticipantRolePrimeDBMock) GetByID(id int64) (p1 participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ParticipantRolePrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ParticipantRolePrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ParticipantRolePrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetByID invocations
func (mmGetByID *ParticipantRolePrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetByID invocations
func (mmGetByID *ParticipantRolePrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mParticipantRolePrimeDBMockGetByID) Calls() []*ParticipantRolePrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mParticipantRolePrimeDBMockGetByParticipantAndRoleID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation
	expectations       []*ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation

	callArgs []*ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetByParticipantAndRoleID
type ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams
	paramPtrs          *ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetByParticipantAndRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams contains parameters of the ParticipantRolePrimeDB.GetByParticipantAndRoleID
type ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams struct {
	participantID int64
	roleID        int64
}

// ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetByParticipantAndRoleID
type ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParamPtrs struct {
	participantID *int64
	roleID        *int64
}

// ParticipantRolePrimeDBMockGetByParticipantAndRoleIDResults contains results of the ParticipantRolePrimeDB.GetByParticipantAndRoleID
type ParticipantRolePrimeDBMockGetByParticipantAndRoleIDResults struct {
	p1  participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetByParticipantAndRoleIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetByParticipantAndRoleID
type ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originRoleID        string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Optional() *mParticipantRolePrimeDBMockGetByParticipantAndRoleID {
	mmGetByParticipantAndRoleID.optional = true
	return mmGetByParticipantAndRoleID
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetByParticipantAndRoleID
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Expect(participantID int64, roleID int64) *mParticipantRolePrimeDBMockGetByParticipantAndRoleID {
	if mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Set")
	}

	if mmGetByParticipantAndRoleID.defaultExpectation == nil {
		mmGetByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation{}
	}

	if mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantAndRoleID.defaultExpectation.params = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams{participantID, roleID}
	mmGetByParticipantAndRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantAndRoleID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantAndRoleID.defaultExpectation.params) {
			mmGetByParticipantAndRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantAndRoleID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantAndRoleID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantRolePrimeDB.GetByParticipantAndRoleID
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) ExpectParticipantIDParam1(participantID int64) *mParticipantRolePrimeDBMockGetByParticipantAndRoleID {
	if mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Set")
	}

	if mmGetByParticipantAndRoleID.defaultExpectation == nil {
		mmGetByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation{}
	}

	if mmGetByParticipantAndRoleID.defaultExpectation.params != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Expect")
	}

	if mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParamPtrs{}
	}
	mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantAndRoleID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantAndRoleID
}

// ExpectRoleIDParam2 sets up expected param roleID for ParticipantRolePrimeDB.GetByParticipantAndRoleID
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) ExpectRoleIDParam2(roleID int64) *mParticipantRolePrimeDBMockGetByParticipantAndRoleID {
	if mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Set")
	}

	if mmGetByParticipantAndRoleID.defaultExpectation == nil {
		mmGetByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation{}
	}

	if mmGetByParticipantAndRoleID.defaultExpectation.params != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Expect")
	}

	if mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParamPtrs{}
	}
	mmGetByParticipantAndRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByParticipantAndRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByParticipantAndRoleID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetByParticipantAndRoleID
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Inspect(f func(participantID int64, roleID int64)) *mParticipantRolePrimeDBMockGetByParticipantAndRoleID {
	if mmGetByParticipantAndRoleID.mock.inspectFuncGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetByParticipantAndRoleID")
	}

	mmGetByParticipantAndRoleID.mock.inspectFuncGetByParticipantAndRoleID = f

	return mmGetByParticipantAndRoleID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetByParticipantAndRoleID
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Return(p1 participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Set")
	}

	if mmGetByParticipantAndRoleID.defaultExpectation == nil {
		mmGetByParticipantAndRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation{mock: mmGetByParticipantAndRoleID.mock}
	}
	mmGetByParticipantAndRoleID.defaultExpectation.results = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDResults{p1, err}
	mmGetByParticipantAndRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantAndRoleID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetByParticipantAndRoleID method
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Set(f func(participantID int64, roleID int64) (p1 participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetByParticipantAndRoleID.defaultExpectation != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetByParticipantAndRoleID method")
	}

	if len(mmGetByParticipantAndRoleID.expectations) > 0 {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetByParticipantAndRoleID method")
	}

	mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID = f
	mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantAndRoleID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetByParticipantAndRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) When(participantID int64, roleID int64) *ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation {
	if mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation{
		mock:               mmGetByParticipantAndRoleID.mock,
		params:             &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams{participantID, roleID},
		expectationOrigins: ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantAndRoleID.expectations = append(mmGetByParticipantAndRoleID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetByParticipantAndRoleID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetByParticipantAndRoleIDExpectation) Then(p1 participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetByParticipantAndRoleIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetByParticipantAndRoleID should be invoked
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Times(n uint64) *mParticipantRolePrimeDBMockGetByParticipantAndRoleID {
	if n == 0 {
		mmGetByParticipantAndRoleID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetByParticipantAndRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantAndRoleID.expectedInvocations, n)
	mmGetByParticipantAndRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantAndRoleID
}

func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) invocationsDone() bool {
	if len(mmGetByParticipantAndRoleID.expectations) == 0 && mmGetByParticipantAndRoleID.defaultExpectation == nil && mmGetByParticipantAndRoleID.mock.funcGetByParticipantAndRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantAndRoleID.mock.afterGetByParticipantAndRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantAndRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantAndRoleID implements mm_repository.ParticipantRolePrimeDB
func (mmGetByParticipantAndRoleID *ParticipantRolePrimeDBMock) GetByParticipantAndRoleID(participantID int64, roleID int64) (p1 participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantAndRoleID.beforeGetByParticipantAndRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantAndRoleID.afterGetByParticipantAndRoleIDCounter, 1)

	mmGetByParticipantAndRoleID.t.Helper()

	if mmGetByParticipantAndRoleID.inspectFuncGetByParticipantAndRoleID != nil {
		mmGetByParticipantAndRoleID.inspectFuncGetByParticipantAndRoleID(participantID, roleID)
	}

	mm_params := ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams{participantID, roleID}

	// Record call args
	mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.mutex.Lock()
	mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.callArgs = append(mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.callArgs, &mm_params)
	mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams{participantID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantAndRoleID.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantAndRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantAndRoleID.GetByParticipantAndRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantAndRoleID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetByParticipantAndRoleID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByParticipantAndRoleID.funcGetByParticipantAndRoleID != nil {
		return mmGetByParticipantAndRoleID.funcGetByParticipantAndRoleID(participantID, roleID)
	}
	mmGetByParticipantAndRoleID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID. %v %v", participantID, roleID)
	return
}

// GetByParticipantAndRoleIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetByParticipantAndRoleID invocations
func (mmGetByParticipantAndRoleID *ParticipantRolePrimeDBMock) GetByParticipantAndRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantAndRoleID.afterGetByParticipantAndRoleIDCounter)
}

// GetByParticipantAndRoleIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetByParticipantAndRoleID invocations
func (mmGetByParticipantAndRoleID *ParticipantRolePrimeDBMock) GetByParticipantAndRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantAndRoleID.beforeGetByParticipantAndRoleIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantAndRoleID *mParticipantRolePrimeDBMockGetByParticipantAndRoleID) Calls() []*ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams {
	mmGetByParticipantAndRoleID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetByParticipantAndRoleIDParams, len(mmGetByParticipantAndRoleID.callArgs))
	copy(argCopy, mmGetByParticipantAndRoleID.callArgs)

	mmGetByParticipantAndRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantAndRoleIDDone returns true if the count of the GetByParticipantAndRoleID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetByParticipantAndRoleIDDone() bool {
	if m.GetByParticipantAndRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantAndRoleIDMock.invocationsDone()
}

// MinimockGetByParticipantAndRoleIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetByParticipantAndRoleIDInspect() {
	for _, e := range m.GetByParticipantAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantAndRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantAndRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantAndRoleIDMock.defaultExpectation != nil && afterGetByParticipantAndRoleIDCounter < 1 {
		if m.GetByParticipantAndRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID at\n%s", m.GetByParticipantAndRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID at\n%s with params: %#v", m.GetByParticipantAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantAndRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantAndRoleID != nil && afterGetByParticipantAndRoleIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID at\n%s", m.funcGetByParticipantAndRoleIDOrigin)
	}

	if !m.GetByParticipantAndRoleIDMock.invocationsDone() && afterGetByParticipantAndRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetByParticipantAndRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantAndRoleIDMock.expectedInvocations), m.GetByParticipantAndRoleIDMock.expectedInvocationsOrigin, afterGetByParticipantAndRoleIDCounter)
	}
}

type mParticipantRolePrimeDBMockGetByParticipantID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetByParticipantIDExpectation
	expectations       []*ParticipantRolePrimeDBMockGetByParticipantIDExpectation

	callArgs []*ParticipantRolePrimeDBMockGetByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetByParticipantIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetByParticipantID
type ParticipantRolePrimeDBMockGetByParticipantIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetByParticipantIDParams
	paramPtrs          *ParticipantRolePrimeDBMockGetByParticipantIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetByParticipantIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetByParticipantIDParams contains parameters of the ParticipantRolePrimeDB.GetByParticipantID
type ParticipantRolePrimeDBMockGetByParticipantIDParams struct {
	participantID int64
}

// ParticipantRolePrimeDBMockGetByParticipantIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetByParticipantID
type ParticipantRolePrimeDBMockGetByParticipantIDParamPtrs struct {
	participantID *int64
}

// ParticipantRolePrimeDBMockGetByParticipantIDResults contains results of the ParticipantRolePrimeDB.GetByParticipantID
type ParticipantRolePrimeDBMockGetByParticipantIDResults struct {
	pa1 []participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetByParticipantIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetByParticipantID
type ParticipantRolePrimeDBMockGetByParticipantIDExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Optional() *mParticipantRolePrimeDBMockGetByParticipantID {
	mmGetByParticipantID.optional = true
	return mmGetByParticipantID
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Expect(participantID int64) *mParticipantRolePrimeDBMockGetByParticipantID {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantIDExpectation{}
	}

	if mmGetByParticipantID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantID.defaultExpectation.params = &ParticipantRolePrimeDBMockGetByParticipantIDParams{participantID}
	mmGetByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantID.defaultExpectation.params) {
			mmGetByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantRolePrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) ExpectParticipantIDParam1(participantID int64) *mParticipantRolePrimeDBMockGetByParticipantID {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantIDExpectation{}
	}

	if mmGetByParticipantID.defaultExpectation.params != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantID mock is already set by Expect")
	}

	if mmGetByParticipantID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByParticipantIDParamPtrs{}
	}
	mmGetByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Inspect(f func(participantID int64)) *mParticipantRolePrimeDBMockGetByParticipantID {
	if mmGetByParticipantID.mock.inspectFuncGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetByParticipantID")
	}

	mmGetByParticipantID.mock.inspectFuncGetByParticipantID = f

	return mmGetByParticipantID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Return(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantIDExpectation{mock: mmGetByParticipantID.mock}
	}
	mmGetByParticipantID.defaultExpectation.results = &ParticipantRolePrimeDBMockGetByParticipantIDResults{pa1, err}
	mmGetByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetByParticipantID method
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Set(f func(participantID int64) (pa1 []participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetByParticipantID.defaultExpectation != nil {
		mmGetByParticipantID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetByParticipantID method")
	}

	if len(mmGetByParticipantID.expectations) > 0 {
		mmGetByParticipantID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetByParticipantID method")
	}

	mmGetByParticipantID.mock.funcGetByParticipantID = f
	mmGetByParticipantID.mock.funcGetByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetByParticipantID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) When(participantID int64) *ParticipantRolePrimeDBMockGetByParticipantIDExpectation {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetByParticipantIDExpectation{
		mock:               mmGetByParticipantID.mock,
		params:             &ParticipantRolePrimeDBMockGetByParticipantIDParams{participantID},
		expectationOrigins: ParticipantRolePrimeDBMockGetByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantID.expectations = append(mmGetByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetByParticipantID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetByParticipantIDExpectation) Then(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetByParticipantIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetByParticipantID should be invoked
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Times(n uint64) *mParticipantRolePrimeDBMockGetByParticipantID {
	if n == 0 {
		mmGetByParticipantID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantID.expectedInvocations, n)
	mmGetByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID
}

func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) invocationsDone() bool {
	if len(mmGetByParticipantID.expectations) == 0 && mmGetByParticipantID.defaultExpectation == nil && mmGetByParticipantID.mock.funcGetByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantID.mock.afterGetByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantID implements mm_repository.ParticipantRolePrimeDB
func (mmGetByParticipantID *ParticipantRolePrimeDBMock) GetByParticipantID(participantID int64) (pa1 []participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantID.beforeGetByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantID.afterGetByParticipantIDCounter, 1)

	mmGetByParticipantID.t.Helper()

	if mmGetByParticipantID.inspectFuncGetByParticipantID != nil {
		mmGetByParticipantID.inspectFuncGetByParticipantID(participantID)
	}

	mm_params := ParticipantRolePrimeDBMockGetByParticipantIDParams{participantID}

	// Record call args
	mmGetByParticipantID.GetByParticipantIDMock.mutex.Lock()
	mmGetByParticipantID.GetByParticipantIDMock.callArgs = append(mmGetByParticipantID.GetByParticipantIDMock.callArgs, &mm_params)
	mmGetByParticipantID.GetByParticipantIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantID.GetByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetByParticipantIDParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantID.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantID.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetByParticipantID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByParticipantID.funcGetByParticipantID != nil {
		return mmGetByParticipantID.funcGetByParticipantID(participantID)
	}
	mmGetByParticipantID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetByParticipantID. %v", participantID)
	return
}

// GetByParticipantIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetByParticipantID invocations
func (mmGetByParticipantID *ParticipantRolePrimeDBMock) GetByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantID.afterGetByParticipantIDCounter)
}

// GetByParticipantIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetByParticipantID invocations
func (mmGetByParticipantID *ParticipantRolePrimeDBMock) GetByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantID.beforeGetByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantID *mParticipantRolePrimeDBMockGetByParticipantID) Calls() []*ParticipantRolePrimeDBMockGetByParticipantIDParams {
	mmGetByParticipantID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetByParticipantIDParams, len(mmGetByParticipantID.callArgs))
	copy(argCopy, mmGetByParticipantID.callArgs)

	mmGetByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDDone returns true if the count of the GetByParticipantID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetByParticipantIDDone() bool {
	if m.GetByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDMock.invocationsDone()
}

// MinimockGetByParticipantIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetByParticipantIDInspect() {
	for _, e := range m.GetByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDMock.defaultExpectation != nil && afterGetByParticipantIDCounter < 1 {
		if m.GetByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantID at\n%s", m.GetByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantID at\n%s with params: %#v", m.GetByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantID != nil && afterGetByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantID at\n%s", m.funcGetByParticipantIDOrigin)
	}

	if !m.GetByParticipantIDMock.invocationsDone() && afterGetByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDMock.expectedInvocations), m.GetByParticipantIDMock.expectedInvocationsOrigin, afterGetByParticipantIDCounter)
	}
}

type mParticipantRolePrimeDBMockGetByParticipantIDs struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetByParticipantIDsExpectation
	expectations       []*ParticipantRolePrimeDBMockGetByParticipantIDsExpectation

	callArgs []*ParticipantRolePrimeDBMockGetByParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetByParticipantIDsExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetByParticipantIDs
type ParticipantRolePrimeDBMockGetByParticipantIDsExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetByParticipantIDsParams
	paramPtrs          *ParticipantRolePrimeDBMockGetByParticipantIDsParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetByParticipantIDsExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetByParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetByParticipantIDsParams contains parameters of the ParticipantRolePrimeDB.GetByParticipantIDs
type ParticipantRolePrimeDBMockGetByParticipantIDsParams struct {
	participantIDs []int64
}

// ParticipantRolePrimeDBMockGetByParticipantIDsParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetByParticipantIDs
type ParticipantRolePrimeDBMockGetByParticipantIDsParamPtrs struct {
	participantIDs *[]int64
}

// ParticipantRolePrimeDBMockGetByParticipantIDsResults contains results of the ParticipantRolePrimeDB.GetByParticipantIDs
type ParticipantRolePrimeDBMockGetByParticipantIDsResults struct {
	pa1 []participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetByParticipantIDsOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetByParticipantIDs
type ParticipantRolePrimeDBMockGetByParticipantIDsExpectationOrigins struct {
	origin               string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Optional() *mParticipantRolePrimeDBMockGetByParticipantIDs {
	mmGetByParticipantIDs.optional = true
	return mmGetByParticipantIDs
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Expect(participantIDs []int64) *mParticipantRolePrimeDBMockGetByParticipantIDs {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	if mmGetByParticipantIDs.defaultExpectation == nil {
		mmGetByParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantIDsExpectation{}
	}

	if mmGetByParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantIDs mock is already set by ExpectParams functions")
	}

	mmGetByParticipantIDs.defaultExpectation.params = &ParticipantRolePrimeDBMockGetByParticipantIDsParams{participantIDs}
	mmGetByParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantIDs.expectations {
		if minimock.Equal(e.params, mmGetByParticipantIDs.defaultExpectation.params) {
			mmGetByParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantIDs.defaultExpectation.params)
		}
	}

	return mmGetByParticipantIDs
}

// ExpectParticipantIDsParam1 sets up expected param participantIDs for ParticipantRolePrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) ExpectParticipantIDsParam1(participantIDs []int64) *mParticipantRolePrimeDBMockGetByParticipantIDs {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	if mmGetByParticipantIDs.defaultExpectation == nil {
		mmGetByParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantIDsExpectation{}
	}

	if mmGetByParticipantIDs.defaultExpectation.params != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantIDs mock is already set by Expect")
	}

	if mmGetByParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByParticipantIDsParamPtrs{}
	}
	mmGetByParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmGetByParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmGetByParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Inspect(f func(participantIDs []int64)) *mParticipantRolePrimeDBMockGetByParticipantIDs {
	if mmGetByParticipantIDs.mock.inspectFuncGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetByParticipantIDs")
	}

	mmGetByParticipantIDs.mock.inspectFuncGetByParticipantIDs = f

	return mmGetByParticipantIDs
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Return(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	if mmGetByParticipantIDs.defaultExpectation == nil {
		mmGetByParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByParticipantIDsExpectation{mock: mmGetByParticipantIDs.mock}
	}
	mmGetByParticipantIDs.defaultExpectation.results = &ParticipantRolePrimeDBMockGetByParticipantIDsResults{pa1, err}
	mmGetByParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetByParticipantIDs method
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Set(f func(participantIDs []int64) (pa1 []participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetByParticipantIDs.defaultExpectation != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetByParticipantIDs method")
	}

	if len(mmGetByParticipantIDs.expectations) > 0 {
		mmGetByParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetByParticipantIDs method")
	}

	mmGetByParticipantIDs.mock.funcGetByParticipantIDs = f
	mmGetByParticipantIDs.mock.funcGetByParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDs.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetByParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) When(participantIDs []int64) *ParticipantRolePrimeDBMockGetByParticipantIDsExpectation {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetByParticipantIDsExpectation{
		mock:               mmGetByParticipantIDs.mock,
		params:             &ParticipantRolePrimeDBMockGetByParticipantIDsParams{participantIDs},
		expectationOrigins: ParticipantRolePrimeDBMockGetByParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantIDs.expectations = append(mmGetByParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetByParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetByParticipantIDsExpectation) Then(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetByParticipantIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetByParticipantIDs should be invoked
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Times(n uint64) *mParticipantRolePrimeDBMockGetByParticipantIDs {
	if n == 0 {
		mmGetByParticipantIDs.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetByParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantIDs.expectedInvocations, n)
	mmGetByParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDs
}

func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) invocationsDone() bool {
	if len(mmGetByParticipantIDs.expectations) == 0 && mmGetByParticipantIDs.defaultExpectation == nil && mmGetByParticipantIDs.mock.funcGetByParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDs.mock.afterGetByParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantIDs implements mm_repository.ParticipantRolePrimeDB
func (mmGetByParticipantIDs *ParticipantRolePrimeDBMock) GetByParticipantIDs(participantIDs []int64) (pa1 []participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantIDs.beforeGetByParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantIDs.afterGetByParticipantIDsCounter, 1)

	mmGetByParticipantIDs.t.Helper()

	if mmGetByParticipantIDs.inspectFuncGetByParticipantIDs != nil {
		mmGetByParticipantIDs.inspectFuncGetByParticipantIDs(participantIDs)
	}

	mm_params := ParticipantRolePrimeDBMockGetByParticipantIDsParams{participantIDs}

	// Record call args
	mmGetByParticipantIDs.GetByParticipantIDsMock.mutex.Lock()
	mmGetByParticipantIDs.GetByParticipantIDsMock.callArgs = append(mmGetByParticipantIDs.GetByParticipantIDsMock.callArgs, &mm_params)
	mmGetByParticipantIDs.GetByParticipantIDsMock.mutex.Unlock()

	for _, e := range mmGetByParticipantIDs.GetByParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetByParticipantIDsParams{participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmGetByParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.GetByParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantIDs.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetByParticipantIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByParticipantIDs.funcGetByParticipantIDs != nil {
		return mmGetByParticipantIDs.funcGetByParticipantIDs(participantIDs)
	}
	mmGetByParticipantIDs.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetByParticipantIDs. %v", participantIDs)
	return
}

// GetByParticipantIDsAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetByParticipantIDs invocations
func (mmGetByParticipantIDs *ParticipantRolePrimeDBMock) GetByParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDs.afterGetByParticipantIDsCounter)
}

// GetByParticipantIDsBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetByParticipantIDs invocations
func (mmGetByParticipantIDs *ParticipantRolePrimeDBMock) GetByParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDs.beforeGetByParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetByParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantIDs *mParticipantRolePrimeDBMockGetByParticipantIDs) Calls() []*ParticipantRolePrimeDBMockGetByParticipantIDsParams {
	mmGetByParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetByParticipantIDsParams, len(mmGetByParticipantIDs.callArgs))
	copy(argCopy, mmGetByParticipantIDs.callArgs)

	mmGetByParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDsDone returns true if the count of the GetByParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetByParticipantIDsDone() bool {
	if m.GetByParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDsMock.invocationsDone()
}

// MinimockGetByParticipantIDsInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetByParticipantIDsInspect() {
	for _, e := range m.GetByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDsMock.defaultExpectation != nil && afterGetByParticipantIDsCounter < 1 {
		if m.GetByParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantIDs at\n%s", m.GetByParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantIDs at\n%s with params: %#v", m.GetByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantIDs != nil && afterGetByParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByParticipantIDs at\n%s", m.funcGetByParticipantIDsOrigin)
	}

	if !m.GetByParticipantIDsMock.invocationsDone() && afterGetByParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetByParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDsMock.expectedInvocations), m.GetByParticipantIDsMock.expectedInvocationsOrigin, afterGetByParticipantIDsCounter)
	}
}

type mParticipantRolePrimeDBMockGetByRoleID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetByRoleIDExpectation
	expectations       []*ParticipantRolePrimeDBMockGetByRoleIDExpectation

	callArgs []*ParticipantRolePrimeDBMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetByRoleIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetByRoleID
type ParticipantRolePrimeDBMockGetByRoleIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetByRoleIDParams
	paramPtrs          *ParticipantRolePrimeDBMockGetByRoleIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetByRoleIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetByRoleIDParams contains parameters of the ParticipantRolePrimeDB.GetByRoleID
type ParticipantRolePrimeDBMockGetByRoleIDParams struct {
	roleID int64
}

// ParticipantRolePrimeDBMockGetByRoleIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetByRoleID
type ParticipantRolePrimeDBMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// ParticipantRolePrimeDBMockGetByRoleIDResults contains results of the ParticipantRolePrimeDB.GetByRoleID
type ParticipantRolePrimeDBMockGetByRoleIDResults struct {
	pa1 []participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetByRoleIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetByRoleID
type ParticipantRolePrimeDBMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Optional() *mParticipantRolePrimeDBMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Expect(roleID int64) *mParticipantRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &ParticipantRolePrimeDBMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for ParticipantRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mParticipantRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Inspect(f func(roleID int64)) *mParticipantRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Return(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &ParticipantRolePrimeDBMockGetByRoleIDResults{pa1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetByRoleID method
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Set(f func(roleID int64) (pa1 []participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) When(roleID int64) *ParticipantRolePrimeDBMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &ParticipantRolePrimeDBMockGetByRoleIDParams{roleID},
		expectationOrigins: ParticipantRolePrimeDBMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetByRoleIDExpectation) Then(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetByRoleIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetByRoleID should be invoked
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Times(n uint64) *mParticipantRolePrimeDBMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_repository.ParticipantRolePrimeDB
func (mmGetByRoleID *ParticipantRolePrimeDBMock) GetByRoleID(roleID int64) (pa1 []participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := ParticipantRolePrimeDBMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("ParticipantRolePrimeDBMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("ParticipantRolePrimeDBMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetByRoleID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *ParticipantRolePrimeDBMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *ParticipantRolePrimeDBMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mParticipantRolePrimeDBMockGetByRoleID) Calls() []*ParticipantRolePrimeDBMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

type mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation
	expectations       []*ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation

	callArgs []*ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams
	paramPtrs          *ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams contains parameters of the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams struct {
	roleID         int64
	participantIDs []int64
}

// ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParamPtrs struct {
	roleID         *int64
	participantIDs *[]int64
}

// ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsResults contains results of the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsResults struct {
	pa1 []participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
type ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectationOrigins struct {
	origin               string
	originRoleID         string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Optional() *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs {
	mmGetByRoleIDAndParticipantIDs.optional = true
	return mmGetByRoleIDAndParticipantIDs
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Expect(roleID int64, participantIDs []int64) *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs {
	if mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmGetByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation{}
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by ExpectParams functions")
	}

	mmGetByRoleIDAndParticipantIDs.defaultExpectation.params = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams{roleID, participantIDs}
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleIDAndParticipantIDs.expectations {
		if minimock.Equal(e.params, mmGetByRoleIDAndParticipantIDs.defaultExpectation.params) {
			mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleIDAndParticipantIDs.defaultExpectation.params)
		}
	}

	return mmGetByRoleIDAndParticipantIDs
}

// ExpectRoleIDParam1 sets up expected param roleID for ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) ExpectRoleIDParam1(roleID int64) *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs {
	if mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmGetByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation{}
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleIDAndParticipantIDs
}

// ExpectParticipantIDsParam2 sets up expected param participantIDs for ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) ExpectParticipantIDsParam2(participantIDs []int64) *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs {
	if mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmGetByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation{}
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmGetByRoleIDAndParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Inspect(f func(roleID int64, participantIDs []int64)) *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs {
	if mmGetByRoleIDAndParticipantIDs.mock.inspectFuncGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs")
	}

	mmGetByRoleIDAndParticipantIDs.mock.inspectFuncGetByRoleIDAndParticipantIDs = f

	return mmGetByRoleIDAndParticipantIDs
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Return(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmGetByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmGetByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation{mock: mmGetByRoleIDAndParticipantIDs.mock}
	}
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.results = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsResults{pa1, err}
	mmGetByRoleIDAndParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleIDAndParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs method
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Set(f func(roleID int64, participantIDs []int64) (pa1 []participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetByRoleIDAndParticipantIDs.defaultExpectation != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs method")
	}

	if len(mmGetByRoleIDAndParticipantIDs.expectations) > 0 {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs method")
	}

	mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs = f
	mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleIDAndParticipantIDs.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) When(roleID int64, participantIDs []int64) *ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation {
	if mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation{
		mock:               mmGetByRoleIDAndParticipantIDs.mock,
		params:             &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams{roleID, participantIDs},
		expectationOrigins: ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleIDAndParticipantIDs.expectations = append(mmGetByRoleIDAndParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsExpectation) Then(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetByRoleIDAndParticipantIDs should be invoked
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Times(n uint64) *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs {
	if n == 0 {
		mmGetByRoleIDAndParticipantIDs.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleIDAndParticipantIDs.expectedInvocations, n)
	mmGetByRoleIDAndParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleIDAndParticipantIDs
}

func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) invocationsDone() bool {
	if len(mmGetByRoleIDAndParticipantIDs.expectations) == 0 && mmGetByRoleIDAndParticipantIDs.defaultExpectation == nil && mmGetByRoleIDAndParticipantIDs.mock.funcGetByRoleIDAndParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleIDAndParticipantIDs.mock.afterGetByRoleIDAndParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleIDAndParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleIDAndParticipantIDs implements mm_repository.ParticipantRolePrimeDB
func (mmGetByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) GetByRoleIDAndParticipantIDs(roleID int64, participantIDs []int64) (pa1 []participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetByRoleIDAndParticipantIDs.beforeGetByRoleIDAndParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleIDAndParticipantIDs.afterGetByRoleIDAndParticipantIDsCounter, 1)

	mmGetByRoleIDAndParticipantIDs.t.Helper()

	if mmGetByRoleIDAndParticipantIDs.inspectFuncGetByRoleIDAndParticipantIDs != nil {
		mmGetByRoleIDAndParticipantIDs.inspectFuncGetByRoleIDAndParticipantIDs(roleID, participantIDs)
	}

	mm_params := ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams{roleID, participantIDs}

	// Record call args
	mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.mutex.Lock()
	mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.callArgs = append(mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.callArgs, &mm_params)
	mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.mutex.Unlock()

	for _, e := range mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams{roleID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmGetByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleIDAndParticipantIDs.t.Errorf("ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleIDAndParticipantIDs.GetByRoleIDAndParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleIDAndParticipantIDs.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByRoleIDAndParticipantIDs.funcGetByRoleIDAndParticipantIDs != nil {
		return mmGetByRoleIDAndParticipantIDs.funcGetByRoleIDAndParticipantIDs(roleID, participantIDs)
	}
	mmGetByRoleIDAndParticipantIDs.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs. %v %v", roleID, participantIDs)
	return
}

// GetByRoleIDAndParticipantIDsAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs invocations
func (mmGetByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) GetByRoleIDAndParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleIDAndParticipantIDs.afterGetByRoleIDAndParticipantIDsCounter)
}

// GetByRoleIDAndParticipantIDsBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs invocations
func (mmGetByRoleIDAndParticipantIDs *ParticipantRolePrimeDBMock) GetByRoleIDAndParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleIDAndParticipantIDs.beforeGetByRoleIDAndParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleIDAndParticipantIDs *mParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDs) Calls() []*ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams {
	mmGetByRoleIDAndParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetByRoleIDAndParticipantIDsParams, len(mmGetByRoleIDAndParticipantIDs.callArgs))
	copy(argCopy, mmGetByRoleIDAndParticipantIDs.callArgs)

	mmGetByRoleIDAndParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDAndParticipantIDsDone returns true if the count of the GetByRoleIDAndParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetByRoleIDAndParticipantIDsDone() bool {
	if m.GetByRoleIDAndParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDAndParticipantIDsMock.invocationsDone()
}

// MinimockGetByRoleIDAndParticipantIDsInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetByRoleIDAndParticipantIDsInspect() {
	for _, e := range m.GetByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDAndParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDAndParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDAndParticipantIDsMock.defaultExpectation != nil && afterGetByRoleIDAndParticipantIDsCounter < 1 {
		if m.GetByRoleIDAndParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs at\n%s", m.GetByRoleIDAndParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs at\n%s with params: %#v", m.GetByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDAndParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleIDAndParticipantIDs != nil && afterGetByRoleIDAndParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs at\n%s", m.funcGetByRoleIDAndParticipantIDsOrigin)
	}

	if !m.GetByRoleIDAndParticipantIDsMock.invocationsDone() && afterGetByRoleIDAndParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetByRoleIDAndParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDAndParticipantIDsMock.expectedInvocations), m.GetByRoleIDAndParticipantIDsMock.expectedInvocationsOrigin, afterGetByRoleIDAndParticipantIDsCounter)
	}
}

type mParticipantRolePrimeDBMockGetProductOwnersByParticipantID struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation
	expectations       []*ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation

	callArgs []*ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation specifies expectation struct of the ParticipantRolePrimeDB.GetProductOwnersByParticipantID
type ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams
	paramPtrs          *ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectationOrigins
	results            *ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams contains parameters of the ParticipantRolePrimeDB.GetProductOwnersByParticipantID
type ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams struct {
	participantID int64
}

// ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.GetProductOwnersByParticipantID
type ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParamPtrs struct {
	participantID *int64
}

// ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDResults contains results of the ParticipantRolePrimeDB.GetProductOwnersByParticipantID
type ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDResults struct {
	pa1 []participantentity.ParticipantRole
	err error
}

// ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDOrigins contains origins of expectations of the ParticipantRolePrimeDB.GetProductOwnersByParticipantID
type ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Optional() *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID {
	mmGetProductOwnersByParticipantID.optional = true
	return mmGetProductOwnersByParticipantID
}

// Expect sets up expected params for ParticipantRolePrimeDB.GetProductOwnersByParticipantID
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Expect(participantID int64) *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID {
	if mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantID != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock is already set by Set")
	}

	if mmGetProductOwnersByParticipantID.defaultExpectation == nil {
		mmGetProductOwnersByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation{}
	}

	if mmGetProductOwnersByParticipantID.defaultExpectation.paramPtrs != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock is already set by ExpectParams functions")
	}

	mmGetProductOwnersByParticipantID.defaultExpectation.params = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams{participantID}
	mmGetProductOwnersByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetProductOwnersByParticipantID.expectations {
		if minimock.Equal(e.params, mmGetProductOwnersByParticipantID.defaultExpectation.params) {
			mmGetProductOwnersByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetProductOwnersByParticipantID.defaultExpectation.params)
		}
	}

	return mmGetProductOwnersByParticipantID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantRolePrimeDB.GetProductOwnersByParticipantID
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) ExpectParticipantIDParam1(participantID int64) *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID {
	if mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantID != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock is already set by Set")
	}

	if mmGetProductOwnersByParticipantID.defaultExpectation == nil {
		mmGetProductOwnersByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation{}
	}

	if mmGetProductOwnersByParticipantID.defaultExpectation.params != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock is already set by Expect")
	}

	if mmGetProductOwnersByParticipantID.defaultExpectation.paramPtrs == nil {
		mmGetProductOwnersByParticipantID.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParamPtrs{}
	}
	mmGetProductOwnersByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetProductOwnersByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetProductOwnersByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.GetProductOwnersByParticipantID
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Inspect(f func(participantID int64)) *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID {
	if mmGetProductOwnersByParticipantID.mock.inspectFuncGetProductOwnersByParticipantID != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID")
	}

	mmGetProductOwnersByParticipantID.mock.inspectFuncGetProductOwnersByParticipantID = f

	return mmGetProductOwnersByParticipantID
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.GetProductOwnersByParticipantID
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Return(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	if mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantID != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock is already set by Set")
	}

	if mmGetProductOwnersByParticipantID.defaultExpectation == nil {
		mmGetProductOwnersByParticipantID.defaultExpectation = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation{mock: mmGetProductOwnersByParticipantID.mock}
	}
	mmGetProductOwnersByParticipantID.defaultExpectation.results = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDResults{pa1, err}
	mmGetProductOwnersByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetProductOwnersByParticipantID.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.GetProductOwnersByParticipantID method
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Set(f func(participantID int64) (pa1 []participantentity.ParticipantRole, err error)) *ParticipantRolePrimeDBMock {
	if mmGetProductOwnersByParticipantID.defaultExpectation != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.GetProductOwnersByParticipantID method")
	}

	if len(mmGetProductOwnersByParticipantID.expectations) > 0 {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.GetProductOwnersByParticipantID method")
	}

	mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantID = f
	mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmGetProductOwnersByParticipantID.mock
}

// When sets expectation for the ParticipantRolePrimeDB.GetProductOwnersByParticipantID which will trigger the result defined by the following
// Then helper
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) When(participantID int64) *ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation {
	if mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantID != nil {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation{
		mock:               mmGetProductOwnersByParticipantID.mock,
		params:             &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams{participantID},
		expectationOrigins: ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetProductOwnersByParticipantID.expectations = append(mmGetProductOwnersByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.GetProductOwnersByParticipantID return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDExpectation) Then(pa1 []participantentity.ParticipantRole, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.GetProductOwnersByParticipantID should be invoked
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Times(n uint64) *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID {
	if n == 0 {
		mmGetProductOwnersByParticipantID.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetProductOwnersByParticipantID.expectedInvocations, n)
	mmGetProductOwnersByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetProductOwnersByParticipantID
}

func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) invocationsDone() bool {
	if len(mmGetProductOwnersByParticipantID.expectations) == 0 && mmGetProductOwnersByParticipantID.defaultExpectation == nil && mmGetProductOwnersByParticipantID.mock.funcGetProductOwnersByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetProductOwnersByParticipantID.mock.afterGetProductOwnersByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetProductOwnersByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetProductOwnersByParticipantID implements mm_repository.ParticipantRolePrimeDB
func (mmGetProductOwnersByParticipantID *ParticipantRolePrimeDBMock) GetProductOwnersByParticipantID(participantID int64) (pa1 []participantentity.ParticipantRole, err error) {
	mm_atomic.AddUint64(&mmGetProductOwnersByParticipantID.beforeGetProductOwnersByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetProductOwnersByParticipantID.afterGetProductOwnersByParticipantIDCounter, 1)

	mmGetProductOwnersByParticipantID.t.Helper()

	if mmGetProductOwnersByParticipantID.inspectFuncGetProductOwnersByParticipantID != nil {
		mmGetProductOwnersByParticipantID.inspectFuncGetProductOwnersByParticipantID(participantID)
	}

	mm_params := ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams{participantID}

	// Record call args
	mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.mutex.Lock()
	mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.callArgs = append(mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.callArgs, &mm_params)
	mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.mutex.Unlock()

	for _, e := range mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetProductOwnersByParticipantID.t.Errorf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetProductOwnersByParticipantID.t.Errorf("ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetProductOwnersByParticipantID.GetProductOwnersByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetProductOwnersByParticipantID.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetProductOwnersByParticipantID.funcGetProductOwnersByParticipantID != nil {
		return mmGetProductOwnersByParticipantID.funcGetProductOwnersByParticipantID(participantID)
	}
	mmGetProductOwnersByParticipantID.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID. %v", participantID)
	return
}

// GetProductOwnersByParticipantIDAfterCounter returns a count of finished ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID invocations
func (mmGetProductOwnersByParticipantID *ParticipantRolePrimeDBMock) GetProductOwnersByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductOwnersByParticipantID.afterGetProductOwnersByParticipantIDCounter)
}

// GetProductOwnersByParticipantIDBeforeCounter returns a count of ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID invocations
func (mmGetProductOwnersByParticipantID *ParticipantRolePrimeDBMock) GetProductOwnersByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductOwnersByParticipantID.beforeGetProductOwnersByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetProductOwnersByParticipantID *mParticipantRolePrimeDBMockGetProductOwnersByParticipantID) Calls() []*ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams {
	mmGetProductOwnersByParticipantID.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockGetProductOwnersByParticipantIDParams, len(mmGetProductOwnersByParticipantID.callArgs))
	copy(argCopy, mmGetProductOwnersByParticipantID.callArgs)

	mmGetProductOwnersByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockGetProductOwnersByParticipantIDDone returns true if the count of the GetProductOwnersByParticipantID invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockGetProductOwnersByParticipantIDDone() bool {
	if m.GetProductOwnersByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetProductOwnersByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetProductOwnersByParticipantIDMock.invocationsDone()
}

// MinimockGetProductOwnersByParticipantIDInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockGetProductOwnersByParticipantIDInspect() {
	for _, e := range m.GetProductOwnersByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetProductOwnersByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterGetProductOwnersByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetProductOwnersByParticipantIDMock.defaultExpectation != nil && afterGetProductOwnersByParticipantIDCounter < 1 {
		if m.GetProductOwnersByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID at\n%s", m.GetProductOwnersByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID at\n%s with params: %#v", m.GetProductOwnersByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.GetProductOwnersByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetProductOwnersByParticipantID != nil && afterGetProductOwnersByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID at\n%s", m.funcGetProductOwnersByParticipantIDOrigin)
	}

	if !m.GetProductOwnersByParticipantIDMock.invocationsDone() && afterGetProductOwnersByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.GetProductOwnersByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetProductOwnersByParticipantIDMock.expectedInvocations), m.GetProductOwnersByParticipantIDMock.expectedInvocationsOrigin, afterGetProductOwnersByParticipantIDCounter)
	}
}

type mParticipantRolePrimeDBMockIsOwner struct {
	optional           bool
	mock               *ParticipantRolePrimeDBMock
	defaultExpectation *ParticipantRolePrimeDBMockIsOwnerExpectation
	expectations       []*ParticipantRolePrimeDBMockIsOwnerExpectation

	callArgs []*ParticipantRolePrimeDBMockIsOwnerParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantRolePrimeDBMockIsOwnerExpectation specifies expectation struct of the ParticipantRolePrimeDB.IsOwner
type ParticipantRolePrimeDBMockIsOwnerExpectation struct {
	mock               *ParticipantRolePrimeDBMock
	params             *ParticipantRolePrimeDBMockIsOwnerParams
	paramPtrs          *ParticipantRolePrimeDBMockIsOwnerParamPtrs
	expectationOrigins ParticipantRolePrimeDBMockIsOwnerExpectationOrigins
	results            *ParticipantRolePrimeDBMockIsOwnerResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantRolePrimeDBMockIsOwnerParams contains parameters of the ParticipantRolePrimeDB.IsOwner
type ParticipantRolePrimeDBMockIsOwnerParams struct {
	participantID int64
}

// ParticipantRolePrimeDBMockIsOwnerParamPtrs contains pointers to parameters of the ParticipantRolePrimeDB.IsOwner
type ParticipantRolePrimeDBMockIsOwnerParamPtrs struct {
	participantID *int64
}

// ParticipantRolePrimeDBMockIsOwnerResults contains results of the ParticipantRolePrimeDB.IsOwner
type ParticipantRolePrimeDBMockIsOwnerResults struct {
	b1  bool
	err error
}

// ParticipantRolePrimeDBMockIsOwnerOrigins contains origins of expectations of the ParticipantRolePrimeDB.IsOwner
type ParticipantRolePrimeDBMockIsOwnerExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Optional() *mParticipantRolePrimeDBMockIsOwner {
	mmIsOwner.optional = true
	return mmIsOwner
}

// Expect sets up expected params for ParticipantRolePrimeDB.IsOwner
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Expect(participantID int64) *mParticipantRolePrimeDBMockIsOwner {
	if mmIsOwner.mock.funcIsOwner != nil {
		mmIsOwner.mock.t.Fatalf("ParticipantRolePrimeDBMock.IsOwner mock is already set by Set")
	}

	if mmIsOwner.defaultExpectation == nil {
		mmIsOwner.defaultExpectation = &ParticipantRolePrimeDBMockIsOwnerExpectation{}
	}

	if mmIsOwner.defaultExpectation.paramPtrs != nil {
		mmIsOwner.mock.t.Fatalf("ParticipantRolePrimeDBMock.IsOwner mock is already set by ExpectParams functions")
	}

	mmIsOwner.defaultExpectation.params = &ParticipantRolePrimeDBMockIsOwnerParams{participantID}
	mmIsOwner.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmIsOwner.expectations {
		if minimock.Equal(e.params, mmIsOwner.defaultExpectation.params) {
			mmIsOwner.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmIsOwner.defaultExpectation.params)
		}
	}

	return mmIsOwner
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantRolePrimeDB.IsOwner
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) ExpectParticipantIDParam1(participantID int64) *mParticipantRolePrimeDBMockIsOwner {
	if mmIsOwner.mock.funcIsOwner != nil {
		mmIsOwner.mock.t.Fatalf("ParticipantRolePrimeDBMock.IsOwner mock is already set by Set")
	}

	if mmIsOwner.defaultExpectation == nil {
		mmIsOwner.defaultExpectation = &ParticipantRolePrimeDBMockIsOwnerExpectation{}
	}

	if mmIsOwner.defaultExpectation.params != nil {
		mmIsOwner.mock.t.Fatalf("ParticipantRolePrimeDBMock.IsOwner mock is already set by Expect")
	}

	if mmIsOwner.defaultExpectation.paramPtrs == nil {
		mmIsOwner.defaultExpectation.paramPtrs = &ParticipantRolePrimeDBMockIsOwnerParamPtrs{}
	}
	mmIsOwner.defaultExpectation.paramPtrs.participantID = &participantID
	mmIsOwner.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmIsOwner
}

// Inspect accepts an inspector function that has same arguments as the ParticipantRolePrimeDB.IsOwner
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Inspect(f func(participantID int64)) *mParticipantRolePrimeDBMockIsOwner {
	if mmIsOwner.mock.inspectFuncIsOwner != nil {
		mmIsOwner.mock.t.Fatalf("Inspect function is already set for ParticipantRolePrimeDBMock.IsOwner")
	}

	mmIsOwner.mock.inspectFuncIsOwner = f

	return mmIsOwner
}

// Return sets up results that will be returned by ParticipantRolePrimeDB.IsOwner
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Return(b1 bool, err error) *ParticipantRolePrimeDBMock {
	if mmIsOwner.mock.funcIsOwner != nil {
		mmIsOwner.mock.t.Fatalf("ParticipantRolePrimeDBMock.IsOwner mock is already set by Set")
	}

	if mmIsOwner.defaultExpectation == nil {
		mmIsOwner.defaultExpectation = &ParticipantRolePrimeDBMockIsOwnerExpectation{mock: mmIsOwner.mock}
	}
	mmIsOwner.defaultExpectation.results = &ParticipantRolePrimeDBMockIsOwnerResults{b1, err}
	mmIsOwner.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmIsOwner.mock
}

// Set uses given function f to mock the ParticipantRolePrimeDB.IsOwner method
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Set(f func(participantID int64) (b1 bool, err error)) *ParticipantRolePrimeDBMock {
	if mmIsOwner.defaultExpectation != nil {
		mmIsOwner.mock.t.Fatalf("Default expectation is already set for the ParticipantRolePrimeDB.IsOwner method")
	}

	if len(mmIsOwner.expectations) > 0 {
		mmIsOwner.mock.t.Fatalf("Some expectations are already set for the ParticipantRolePrimeDB.IsOwner method")
	}

	mmIsOwner.mock.funcIsOwner = f
	mmIsOwner.mock.funcIsOwnerOrigin = minimock.CallerInfo(1)
	return mmIsOwner.mock
}

// When sets expectation for the ParticipantRolePrimeDB.IsOwner which will trigger the result defined by the following
// Then helper
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) When(participantID int64) *ParticipantRolePrimeDBMockIsOwnerExpectation {
	if mmIsOwner.mock.funcIsOwner != nil {
		mmIsOwner.mock.t.Fatalf("ParticipantRolePrimeDBMock.IsOwner mock is already set by Set")
	}

	expectation := &ParticipantRolePrimeDBMockIsOwnerExpectation{
		mock:               mmIsOwner.mock,
		params:             &ParticipantRolePrimeDBMockIsOwnerParams{participantID},
		expectationOrigins: ParticipantRolePrimeDBMockIsOwnerExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmIsOwner.expectations = append(mmIsOwner.expectations, expectation)
	return expectation
}

// Then sets up ParticipantRolePrimeDB.IsOwner return parameters for the expectation previously defined by the When method
func (e *ParticipantRolePrimeDBMockIsOwnerExpectation) Then(b1 bool, err error) *ParticipantRolePrimeDBMock {
	e.results = &ParticipantRolePrimeDBMockIsOwnerResults{b1, err}
	return e.mock
}

// Times sets number of times ParticipantRolePrimeDB.IsOwner should be invoked
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Times(n uint64) *mParticipantRolePrimeDBMockIsOwner {
	if n == 0 {
		mmIsOwner.mock.t.Fatalf("Times of ParticipantRolePrimeDBMock.IsOwner mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmIsOwner.expectedInvocations, n)
	mmIsOwner.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmIsOwner
}

func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) invocationsDone() bool {
	if len(mmIsOwner.expectations) == 0 && mmIsOwner.defaultExpectation == nil && mmIsOwner.mock.funcIsOwner == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmIsOwner.mock.afterIsOwnerCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmIsOwner.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// IsOwner implements mm_repository.ParticipantRolePrimeDB
func (mmIsOwner *ParticipantRolePrimeDBMock) IsOwner(participantID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmIsOwner.beforeIsOwnerCounter, 1)
	defer mm_atomic.AddUint64(&mmIsOwner.afterIsOwnerCounter, 1)

	mmIsOwner.t.Helper()

	if mmIsOwner.inspectFuncIsOwner != nil {
		mmIsOwner.inspectFuncIsOwner(participantID)
	}

	mm_params := ParticipantRolePrimeDBMockIsOwnerParams{participantID}

	// Record call args
	mmIsOwner.IsOwnerMock.mutex.Lock()
	mmIsOwner.IsOwnerMock.callArgs = append(mmIsOwner.IsOwnerMock.callArgs, &mm_params)
	mmIsOwner.IsOwnerMock.mutex.Unlock()

	for _, e := range mmIsOwner.IsOwnerMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmIsOwner.IsOwnerMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmIsOwner.IsOwnerMock.defaultExpectation.Counter, 1)
		mm_want := mmIsOwner.IsOwnerMock.defaultExpectation.params
		mm_want_ptrs := mmIsOwner.IsOwnerMock.defaultExpectation.paramPtrs

		mm_got := ParticipantRolePrimeDBMockIsOwnerParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmIsOwner.t.Errorf("ParticipantRolePrimeDBMock.IsOwner got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmIsOwner.IsOwnerMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmIsOwner.t.Errorf("ParticipantRolePrimeDBMock.IsOwner got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmIsOwner.IsOwnerMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmIsOwner.IsOwnerMock.defaultExpectation.results
		if mm_results == nil {
			mmIsOwner.t.Fatal("No results are set for the ParticipantRolePrimeDBMock.IsOwner")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmIsOwner.funcIsOwner != nil {
		return mmIsOwner.funcIsOwner(participantID)
	}
	mmIsOwner.t.Fatalf("Unexpected call to ParticipantRolePrimeDBMock.IsOwner. %v", participantID)
	return
}

// IsOwnerAfterCounter returns a count of finished ParticipantRolePrimeDBMock.IsOwner invocations
func (mmIsOwner *ParticipantRolePrimeDBMock) IsOwnerAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmIsOwner.afterIsOwnerCounter)
}

// IsOwnerBeforeCounter returns a count of ParticipantRolePrimeDBMock.IsOwner invocations
func (mmIsOwner *ParticipantRolePrimeDBMock) IsOwnerBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmIsOwner.beforeIsOwnerCounter)
}

// Calls returns a list of arguments used in each call to ParticipantRolePrimeDBMock.IsOwner.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmIsOwner *mParticipantRolePrimeDBMockIsOwner) Calls() []*ParticipantRolePrimeDBMockIsOwnerParams {
	mmIsOwner.mutex.RLock()

	argCopy := make([]*ParticipantRolePrimeDBMockIsOwnerParams, len(mmIsOwner.callArgs))
	copy(argCopy, mmIsOwner.callArgs)

	mmIsOwner.mutex.RUnlock()

	return argCopy
}

// MinimockIsOwnerDone returns true if the count of the IsOwner invocations corresponds
// the number of defined expectations
func (m *ParticipantRolePrimeDBMock) MinimockIsOwnerDone() bool {
	if m.IsOwnerMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.IsOwnerMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.IsOwnerMock.invocationsDone()
}

// MinimockIsOwnerInspect logs each unmet expectation
func (m *ParticipantRolePrimeDBMock) MinimockIsOwnerInspect() {
	for _, e := range m.IsOwnerMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.IsOwner at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterIsOwnerCounter := mm_atomic.LoadUint64(&m.afterIsOwnerCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.IsOwnerMock.defaultExpectation != nil && afterIsOwnerCounter < 1 {
		if m.IsOwnerMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.IsOwner at\n%s", m.IsOwnerMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.IsOwner at\n%s with params: %#v", m.IsOwnerMock.defaultExpectation.expectationOrigins.origin, *m.IsOwnerMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcIsOwner != nil && afterIsOwnerCounter < 1 {
		m.t.Errorf("Expected call to ParticipantRolePrimeDBMock.IsOwner at\n%s", m.funcIsOwnerOrigin)
	}

	if !m.IsOwnerMock.invocationsDone() && afterIsOwnerCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantRolePrimeDBMock.IsOwner at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.IsOwnerMock.expectedInvocations), m.IsOwnerMock.expectedInvocationsOrigin, afterIsOwnerCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ParticipantRolePrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByRoleIDAndParticipantIDsInspect()

			m.MinimockDeleteByParticipantAndRoleIDInspect()

			m.MinimockDeleteByParticipantIDInspect()

			m.MinimockDeleteByParticipantIDsInspect()

			m.MinimockDeleteByRoleIDInspect()

			m.MinimockDeleteByRoleIDAndParticipantIDsInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByParticipantAndRoleIDInspect()

			m.MinimockGetByParticipantIDInspect()

			m.MinimockGetByParticipantIDsInspect()

			m.MinimockGetByRoleIDInspect()

			m.MinimockGetByRoleIDAndParticipantIDsInspect()

			m.MinimockGetProductOwnersByParticipantIDInspect()

			m.MinimockIsOwnerInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ParticipantRolePrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ParticipantRolePrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByRoleIDAndParticipantIDsDone() &&
		m.MinimockDeleteByParticipantAndRoleIDDone() &&
		m.MinimockDeleteByParticipantIDDone() &&
		m.MinimockDeleteByParticipantIDsDone() &&
		m.MinimockDeleteByRoleIDDone() &&
		m.MinimockDeleteByRoleIDAndParticipantIDsDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByParticipantAndRoleIDDone() &&
		m.MinimockGetByParticipantIDDone() &&
		m.MinimockGetByParticipantIDsDone() &&
		m.MinimockGetByRoleIDDone() &&
		m.MinimockGetByRoleIDAndParticipantIDsDone() &&
		m.MinimockGetProductOwnersByParticipantIDDone() &&
		m.MinimockIsOwnerDone()
}
