// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository.ParticipantGroupPrimeDB -o participant_group_prime_db_mock.go -n ParticipantGroupPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"github.com/gojuno/minimock/v3"
)

// ParticipantGroupPrimeDBMock implements mm_repository.ParticipantGroupPrimeDB
type ParticipantGroupPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, participantID int64, groupID int64) (p1 participantentity.ParticipantGroup, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, participantID int64, groupID int64)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mParticipantGroupPrimeDBMockCreate

	funcCreateByRoleIDAndParticipantIDs          func(ctx context.Context, groupID int64, participantIDs []int64) (err error)
	funcCreateByRoleIDAndParticipantIDsOrigin    string
	inspectFuncCreateByRoleIDAndParticipantIDs   func(ctx context.Context, groupID int64, participantIDs []int64)
	afterCreateByRoleIDAndParticipantIDsCounter  uint64
	beforeCreateByRoleIDAndParticipantIDsCounter uint64
	CreateByRoleIDAndParticipantIDsMock          mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs

	funcDeleteByGroupID          func(ctx context.Context, groupID int64) (err error)
	funcDeleteByGroupIDOrigin    string
	inspectFuncDeleteByGroupID   func(ctx context.Context, groupID int64)
	afterDeleteByGroupIDCounter  uint64
	beforeDeleteByGroupIDCounter uint64
	DeleteByGroupIDMock          mParticipantGroupPrimeDBMockDeleteByGroupID

	funcDeleteByParticipantAndGroupID          func(ctx context.Context, participantID int64, groupID int64) (err error)
	funcDeleteByParticipantAndGroupIDOrigin    string
	inspectFuncDeleteByParticipantAndGroupID   func(ctx context.Context, participantID int64, groupID int64)
	afterDeleteByParticipantAndGroupIDCounter  uint64
	beforeDeleteByParticipantAndGroupIDCounter uint64
	DeleteByParticipantAndGroupIDMock          mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID

	funcDeleteByParticipantID          func(ctx context.Context, participantID int64) (err error)
	funcDeleteByParticipantIDOrigin    string
	inspectFuncDeleteByParticipantID   func(ctx context.Context, participantID int64)
	afterDeleteByParticipantIDCounter  uint64
	beforeDeleteByParticipantIDCounter uint64
	DeleteByParticipantIDMock          mParticipantGroupPrimeDBMockDeleteByParticipantID

	funcDeleteByParticipantIDs          func(ctx context.Context, participantIDs []int64) (err error)
	funcDeleteByParticipantIDsOrigin    string
	inspectFuncDeleteByParticipantIDs   func(ctx context.Context, participantIDs []int64)
	afterDeleteByParticipantIDsCounter  uint64
	beforeDeleteByParticipantIDsCounter uint64
	DeleteByParticipantIDsMock          mParticipantGroupPrimeDBMockDeleteByParticipantIDs

	funcDeleteByRoleIDAndParticipantIDs          func(ctx context.Context, groupID int64, participantIDs []int64) (err error)
	funcDeleteByRoleIDAndParticipantIDsOrigin    string
	inspectFuncDeleteByRoleIDAndParticipantIDs   func(ctx context.Context, groupID int64, participantIDs []int64)
	afterDeleteByRoleIDAndParticipantIDsCounter  uint64
	beforeDeleteByRoleIDAndParticipantIDsCounter uint64
	DeleteByRoleIDAndParticipantIDsMock          mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs

	funcGetAll          func() (pa1 []participantentity.ParticipantGroup, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mParticipantGroupPrimeDBMockGetAll

	funcGetByGroupID          func(groupID int64) (pa1 []participantentity.ParticipantGroup, err error)
	funcGetByGroupIDOrigin    string
	inspectFuncGetByGroupID   func(groupID int64)
	afterGetByGroupIDCounter  uint64
	beforeGetByGroupIDCounter uint64
	GetByGroupIDMock          mParticipantGroupPrimeDBMockGetByGroupID

	funcGetByID          func(id int64) (p1 participantentity.ParticipantGroup, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mParticipantGroupPrimeDBMockGetByID

	funcGetByParticipantID          func(participantID int64) (pa1 []participantentity.ParticipantGroup, err error)
	funcGetByParticipantIDOrigin    string
	inspectFuncGetByParticipantID   func(participantID int64)
	afterGetByParticipantIDCounter  uint64
	beforeGetByParticipantIDCounter uint64
	GetByParticipantIDMock          mParticipantGroupPrimeDBMockGetByParticipantID

	funcGetByParticipantIDAndGroupID          func(participantID int64, groupID int64) (p1 participantentity.ParticipantGroup, err error)
	funcGetByParticipantIDAndGroupIDOrigin    string
	inspectFuncGetByParticipantIDAndGroupID   func(participantID int64, groupID int64)
	afterGetByParticipantIDAndGroupIDCounter  uint64
	beforeGetByParticipantIDAndGroupIDCounter uint64
	GetByParticipantIDAndGroupIDMock          mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID

	funcGetByParticipantIDs          func(participantIDs []int64) (pa1 []participantentity.ParticipantGroup, err error)
	funcGetByParticipantIDsOrigin    string
	inspectFuncGetByParticipantIDs   func(participantIDs []int64)
	afterGetByParticipantIDsCounter  uint64
	beforeGetByParticipantIDsCounter uint64
	GetByParticipantIDsMock          mParticipantGroupPrimeDBMockGetByParticipantIDs

	funcUpdate          func(ctx context.Context, id int64, participantID *int64, groupID *int64) (p1 participantentity.ParticipantGroup, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(ctx context.Context, id int64, participantID *int64, groupID *int64)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mParticipantGroupPrimeDBMockUpdate
}

// NewParticipantGroupPrimeDBMock returns a mock for mm_repository.ParticipantGroupPrimeDB
func NewParticipantGroupPrimeDBMock(t minimock.Tester) *ParticipantGroupPrimeDBMock {
	m := &ParticipantGroupPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mParticipantGroupPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*ParticipantGroupPrimeDBMockCreateParams{}

	m.CreateByRoleIDAndParticipantIDsMock = mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs{mock: m}
	m.CreateByRoleIDAndParticipantIDsMock.callArgs = []*ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams{}

	m.DeleteByGroupIDMock = mParticipantGroupPrimeDBMockDeleteByGroupID{mock: m}
	m.DeleteByGroupIDMock.callArgs = []*ParticipantGroupPrimeDBMockDeleteByGroupIDParams{}

	m.DeleteByParticipantAndGroupIDMock = mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID{mock: m}
	m.DeleteByParticipantAndGroupIDMock.callArgs = []*ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams{}

	m.DeleteByParticipantIDMock = mParticipantGroupPrimeDBMockDeleteByParticipantID{mock: m}
	m.DeleteByParticipantIDMock.callArgs = []*ParticipantGroupPrimeDBMockDeleteByParticipantIDParams{}

	m.DeleteByParticipantIDsMock = mParticipantGroupPrimeDBMockDeleteByParticipantIDs{mock: m}
	m.DeleteByParticipantIDsMock.callArgs = []*ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams{}

	m.DeleteByRoleIDAndParticipantIDsMock = mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs{mock: m}
	m.DeleteByRoleIDAndParticipantIDsMock.callArgs = []*ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams{}

	m.GetAllMock = mParticipantGroupPrimeDBMockGetAll{mock: m}

	m.GetByGroupIDMock = mParticipantGroupPrimeDBMockGetByGroupID{mock: m}
	m.GetByGroupIDMock.callArgs = []*ParticipantGroupPrimeDBMockGetByGroupIDParams{}

	m.GetByIDMock = mParticipantGroupPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ParticipantGroupPrimeDBMockGetByIDParams{}

	m.GetByParticipantIDMock = mParticipantGroupPrimeDBMockGetByParticipantID{mock: m}
	m.GetByParticipantIDMock.callArgs = []*ParticipantGroupPrimeDBMockGetByParticipantIDParams{}

	m.GetByParticipantIDAndGroupIDMock = mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID{mock: m}
	m.GetByParticipantIDAndGroupIDMock.callArgs = []*ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams{}

	m.GetByParticipantIDsMock = mParticipantGroupPrimeDBMockGetByParticipantIDs{mock: m}
	m.GetByParticipantIDsMock.callArgs = []*ParticipantGroupPrimeDBMockGetByParticipantIDsParams{}

	m.UpdateMock = mParticipantGroupPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*ParticipantGroupPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mParticipantGroupPrimeDBMockCreate struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockCreateExpectation
	expectations       []*ParticipantGroupPrimeDBMockCreateExpectation

	callArgs []*ParticipantGroupPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockCreateExpectation specifies expectation struct of the ParticipantGroupPrimeDB.Create
type ParticipantGroupPrimeDBMockCreateExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockCreateParams
	paramPtrs          *ParticipantGroupPrimeDBMockCreateParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockCreateExpectationOrigins
	results            *ParticipantGroupPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockCreateParams contains parameters of the ParticipantGroupPrimeDB.Create
type ParticipantGroupPrimeDBMockCreateParams struct {
	ctx           context.Context
	participantID int64
	groupID       int64
}

// ParticipantGroupPrimeDBMockCreateParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.Create
type ParticipantGroupPrimeDBMockCreateParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	groupID       *int64
}

// ParticipantGroupPrimeDBMockCreateResults contains results of the ParticipantGroupPrimeDB.Create
type ParticipantGroupPrimeDBMockCreateResults struct {
	p1  participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockCreateOrigins contains origins of expectations of the ParticipantGroupPrimeDB.Create
type ParticipantGroupPrimeDBMockCreateExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originGroupID       string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Optional() *mParticipantGroupPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ParticipantGroupPrimeDB.Create
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Expect(ctx context.Context, participantID int64, groupID int64) *mParticipantGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ParticipantGroupPrimeDBMockCreateParams{ctx, participantID, groupID}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.Create
func (mmCreate *mParticipantGroupPrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantGroupPrimeDB.Create
func (mmCreate *mParticipantGroupPrimeDBMockCreate) ExpectParticipantIDParam2(participantID int64) *mParticipantGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.participantID = &participantID
	mmCreate.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectGroupIDParam3 sets up expected param groupID for ParticipantGroupPrimeDB.Create
func (mmCreate *mParticipantGroupPrimeDBMockCreate) ExpectGroupIDParam3(groupID int64) *mParticipantGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.groupID = &groupID
	mmCreate.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.Create
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Inspect(f func(ctx context.Context, participantID int64, groupID int64)) *mParticipantGroupPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.Create
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Return(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantGroupPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ParticipantGroupPrimeDBMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.Create method
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Set(f func(ctx context.Context, participantID int64, groupID int64) (p1 participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mParticipantGroupPrimeDBMockCreate) When(ctx context.Context, participantID int64, groupID int64) *ParticipantGroupPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ParticipantGroupPrimeDBMockCreateParams{ctx, participantID, groupID},
		expectationOrigins: ParticipantGroupPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockCreateExpectation) Then(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.Create should be invoked
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Times(n uint64) *mParticipantGroupPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mParticipantGroupPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.ParticipantGroupPrimeDB
func (mmCreate *ParticipantGroupPrimeDBMock) Create(ctx context.Context, participantID int64, groupID int64) (p1 participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, participantID, groupID)
	}

	mm_params := ParticipantGroupPrimeDBMockCreateParams{ctx, participantID, groupID}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockCreateParams{ctx, participantID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("ParticipantGroupPrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmCreate.t.Errorf("ParticipantGroupPrimeDBMock.Create got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmCreate.t.Errorf("ParticipantGroupPrimeDBMock.Create got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ParticipantGroupPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, participantID, groupID)
	}
	mmCreate.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.Create. %v %v %v", ctx, participantID, groupID)
	return
}

// CreateAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.Create invocations
func (mmCreate *ParticipantGroupPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ParticipantGroupPrimeDBMock.Create invocations
func (mmCreate *ParticipantGroupPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mParticipantGroupPrimeDBMockCreate) Calls() []*ParticipantGroupPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation
	expectations       []*ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation

	callArgs []*ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation specifies expectation struct of the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams
	paramPtrs          *ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectationOrigins
	results            *ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams contains parameters of the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams struct {
	ctx            context.Context
	groupID        int64
	participantIDs []int64
}

// ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs struct {
	ctx            *context.Context
	groupID        *int64
	participantIDs *[]int64
}

// ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsResults contains results of the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsResults struct {
	err error
}

// ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsOrigins contains origins of expectations of the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectationOrigins struct {
	origin               string
	originCtx            string
	originGroupID        string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Optional() *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	mmCreateByRoleIDAndParticipantIDs.optional = true
	return mmCreateByRoleIDAndParticipantIDs
}

// Expect sets up expected params for ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Expect(ctx context.Context, groupID int64, participantIDs []int64) *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by ExpectParams functions")
	}

	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByRoleIDAndParticipantIDs.expectations {
		if minimock.Equal(e.params, mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params) {
			mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params)
		}
	}

	return mmCreateByRoleIDAndParticipantIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndParticipantIDs
}

// ExpectGroupIDParam2 sets up expected param groupID for ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) ExpectGroupIDParam2(groupID int64) *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.groupID = &groupID
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndParticipantIDs
}

// ExpectParticipantIDsParam3 sets up expected param participantIDs for ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) ExpectParticipantIDsParam3(participantIDs []int64) *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{}
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Inspect(f func(ctx context.Context, groupID int64, participantIDs []int64)) *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	if mmCreateByRoleIDAndParticipantIDs.mock.inspectFuncCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs")
	}

	mmCreateByRoleIDAndParticipantIDs.mock.inspectFuncCreateByRoleIDAndParticipantIDs = f

	return mmCreateByRoleIDAndParticipantIDs
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Return(err error) *ParticipantGroupPrimeDBMock {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{mock: mmCreateByRoleIDAndParticipantIDs.mock}
	}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.results = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsResults{err}
	mmCreateByRoleIDAndParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs method
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Set(f func(ctx context.Context, groupID int64, participantIDs []int64) (err error)) *ParticipantGroupPrimeDBMock {
	if mmCreateByRoleIDAndParticipantIDs.defaultExpectation != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs method")
	}

	if len(mmCreateByRoleIDAndParticipantIDs.expectations) > 0 {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs method")
	}

	mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs = f
	mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndParticipantIDs.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) When(ctx context.Context, groupID int64, participantIDs []int64) *ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation {
	if mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation{
		mock:               mmCreateByRoleIDAndParticipantIDs.mock,
		params:             &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs},
		expectationOrigins: ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByRoleIDAndParticipantIDs.expectations = append(mmCreateByRoleIDAndParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsExpectation) Then(err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.CreateByRoleIDAndParticipantIDs should be invoked
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Times(n uint64) *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs {
	if n == 0 {
		mmCreateByRoleIDAndParticipantIDs.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByRoleIDAndParticipantIDs.expectedInvocations, n)
	mmCreateByRoleIDAndParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndParticipantIDs
}

func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) invocationsDone() bool {
	if len(mmCreateByRoleIDAndParticipantIDs.expectations) == 0 && mmCreateByRoleIDAndParticipantIDs.defaultExpectation == nil && mmCreateByRoleIDAndParticipantIDs.mock.funcCreateByRoleIDAndParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.mock.afterCreateByRoleIDAndParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByRoleIDAndParticipantIDs implements mm_repository.ParticipantGroupPrimeDB
func (mmCreateByRoleIDAndParticipantIDs *ParticipantGroupPrimeDBMock) CreateByRoleIDAndParticipantIDs(ctx context.Context, groupID int64, participantIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByRoleIDAndParticipantIDs.beforeCreateByRoleIDAndParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByRoleIDAndParticipantIDs.afterCreateByRoleIDAndParticipantIDsCounter, 1)

	mmCreateByRoleIDAndParticipantIDs.t.Helper()

	if mmCreateByRoleIDAndParticipantIDs.inspectFuncCreateByRoleIDAndParticipantIDs != nil {
		mmCreateByRoleIDAndParticipantIDs.inspectFuncCreateByRoleIDAndParticipantIDs(ctx, groupID, participantIDs)
	}

	mm_params := ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs}

	// Record call args
	mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.mutex.Lock()
	mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.callArgs = append(mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.callArgs, &mm_params)
	mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.mutex.Unlock()

	for _, e := range mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByRoleIDAndParticipantIDs.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByRoleIDAndParticipantIDs.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByRoleIDAndParticipantIDs.funcCreateByRoleIDAndParticipantIDs != nil {
		return mmCreateByRoleIDAndParticipantIDs.funcCreateByRoleIDAndParticipantIDs(ctx, groupID, participantIDs)
	}
	mmCreateByRoleIDAndParticipantIDs.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs. %v %v %v", ctx, groupID, participantIDs)
	return
}

// CreateByRoleIDAndParticipantIDsAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs invocations
func (mmCreateByRoleIDAndParticipantIDs *ParticipantGroupPrimeDBMock) CreateByRoleIDAndParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.afterCreateByRoleIDAndParticipantIDsCounter)
}

// CreateByRoleIDAndParticipantIDsBeforeCounter returns a count of ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs invocations
func (mmCreateByRoleIDAndParticipantIDs *ParticipantGroupPrimeDBMock) CreateByRoleIDAndParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndParticipantIDs.beforeCreateByRoleIDAndParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDs) Calls() []*ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams {
	mmCreateByRoleIDAndParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockCreateByRoleIDAndParticipantIDsParams, len(mmCreateByRoleIDAndParticipantIDs.callArgs))
	copy(argCopy, mmCreateByRoleIDAndParticipantIDs.callArgs)

	mmCreateByRoleIDAndParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByRoleIDAndParticipantIDsDone returns true if the count of the CreateByRoleIDAndParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockCreateByRoleIDAndParticipantIDsDone() bool {
	if m.CreateByRoleIDAndParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByRoleIDAndParticipantIDsMock.invocationsDone()
}

// MinimockCreateByRoleIDAndParticipantIDsInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockCreateByRoleIDAndParticipantIDsInspect() {
	for _, e := range m.CreateByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByRoleIDAndParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByRoleIDAndParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation != nil && afterCreateByRoleIDAndParticipantIDsCounter < 1 {
		if m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s", m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s with params: %#v", m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByRoleIDAndParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByRoleIDAndParticipantIDs != nil && afterCreateByRoleIDAndParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s", m.funcCreateByRoleIDAndParticipantIDsOrigin)
	}

	if !m.CreateByRoleIDAndParticipantIDsMock.invocationsDone() && afterCreateByRoleIDAndParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.CreateByRoleIDAndParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByRoleIDAndParticipantIDsMock.expectedInvocations), m.CreateByRoleIDAndParticipantIDsMock.expectedInvocationsOrigin, afterCreateByRoleIDAndParticipantIDsCounter)
	}
}

type mParticipantGroupPrimeDBMockDeleteByGroupID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockDeleteByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.DeleteByGroupID
type ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockDeleteByGroupIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockDeleteByGroupIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockDeleteByGroupIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockDeleteByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockDeleteByGroupIDParams contains parameters of the ParticipantGroupPrimeDB.DeleteByGroupID
type ParticipantGroupPrimeDBMockDeleteByGroupIDParams struct {
	ctx     context.Context
	groupID int64
}

// ParticipantGroupPrimeDBMockDeleteByGroupIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.DeleteByGroupID
type ParticipantGroupPrimeDBMockDeleteByGroupIDParamPtrs struct {
	ctx     *context.Context
	groupID *int64
}

// ParticipantGroupPrimeDBMockDeleteByGroupIDResults contains results of the ParticipantGroupPrimeDB.DeleteByGroupID
type ParticipantGroupPrimeDBMockDeleteByGroupIDResults struct {
	err error
}

// ParticipantGroupPrimeDBMockDeleteByGroupIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.DeleteByGroupID
type ParticipantGroupPrimeDBMockDeleteByGroupIDExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Optional() *mParticipantGroupPrimeDBMockDeleteByGroupID {
	mmDeleteByGroupID.optional = true
	return mmDeleteByGroupID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Expect(ctx context.Context, groupID int64) *mParticipantGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by ExpectParams functions")
	}

	mmDeleteByGroupID.defaultExpectation.params = &ParticipantGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID}
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByGroupID.expectations {
		if minimock.Equal(e.params, mmDeleteByGroupID.defaultExpectation.params) {
			mmDeleteByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByGroupID.defaultExpectation.params)
		}
	}

	return mmDeleteByGroupID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.params != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Expect")
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByGroupIDParamPtrs{}
	}
	mmDeleteByGroupID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByGroupID
}

// ExpectGroupIDParam2 sets up expected param groupID for ParticipantGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) ExpectGroupIDParam2(groupID int64) *mParticipantGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.params != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Expect")
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByGroupIDParamPtrs{}
	}
	mmDeleteByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByGroupID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Inspect(f func(ctx context.Context, groupID int64)) *mParticipantGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.inspectFuncDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.DeleteByGroupID")
	}

	mmDeleteByGroupID.mock.inspectFuncDeleteByGroupID = f

	return mmDeleteByGroupID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Return(err error) *ParticipantGroupPrimeDBMock {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation{mock: mmDeleteByGroupID.mock}
	}
	mmDeleteByGroupID.defaultExpectation.results = &ParticipantGroupPrimeDBMockDeleteByGroupIDResults{err}
	mmDeleteByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.DeleteByGroupID method
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Set(f func(ctx context.Context, groupID int64) (err error)) *ParticipantGroupPrimeDBMock {
	if mmDeleteByGroupID.defaultExpectation != nil {
		mmDeleteByGroupID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.DeleteByGroupID method")
	}

	if len(mmDeleteByGroupID.expectations) > 0 {
		mmDeleteByGroupID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.DeleteByGroupID method")
	}

	mmDeleteByGroupID.mock.funcDeleteByGroupID = f
	mmDeleteByGroupID.mock.funcDeleteByGroupIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.DeleteByGroupID which will trigger the result defined by the following
// Then helper
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) When(ctx context.Context, groupID int64) *ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation{
		mock:               mmDeleteByGroupID.mock,
		params:             &ParticipantGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID},
		expectationOrigins: ParticipantGroupPrimeDBMockDeleteByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByGroupID.expectations = append(mmDeleteByGroupID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.DeleteByGroupID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockDeleteByGroupIDExpectation) Then(err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockDeleteByGroupIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.DeleteByGroupID should be invoked
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Times(n uint64) *mParticipantGroupPrimeDBMockDeleteByGroupID {
	if n == 0 {
		mmDeleteByGroupID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.DeleteByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByGroupID.expectedInvocations, n)
	mmDeleteByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID
}

func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) invocationsDone() bool {
	if len(mmDeleteByGroupID.expectations) == 0 && mmDeleteByGroupID.defaultExpectation == nil && mmDeleteByGroupID.mock.funcDeleteByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupID.mock.afterDeleteByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByGroupID implements mm_repository.ParticipantGroupPrimeDB
func (mmDeleteByGroupID *ParticipantGroupPrimeDBMock) DeleteByGroupID(ctx context.Context, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByGroupID.beforeDeleteByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByGroupID.afterDeleteByGroupIDCounter, 1)

	mmDeleteByGroupID.t.Helper()

	if mmDeleteByGroupID.inspectFuncDeleteByGroupID != nil {
		mmDeleteByGroupID.inspectFuncDeleteByGroupID(ctx, groupID)
	}

	mm_params := ParticipantGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID}

	// Record call args
	mmDeleteByGroupID.DeleteByGroupIDMock.mutex.Lock()
	mmDeleteByGroupID.DeleteByGroupIDMock.callArgs = append(mmDeleteByGroupID.DeleteByGroupIDMock.callArgs, &mm_params)
	mmDeleteByGroupID.DeleteByGroupIDMock.mutex.Unlock()

	for _, e := range mmDeleteByGroupID.DeleteByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByGroupID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByGroupID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.DeleteByGroupID")
		}
		return (*mm_results).err
	}
	if mmDeleteByGroupID.funcDeleteByGroupID != nil {
		return mmDeleteByGroupID.funcDeleteByGroupID(ctx, groupID)
	}
	mmDeleteByGroupID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.DeleteByGroupID. %v %v", ctx, groupID)
	return
}

// DeleteByGroupIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.DeleteByGroupID invocations
func (mmDeleteByGroupID *ParticipantGroupPrimeDBMock) DeleteByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupID.afterDeleteByGroupIDCounter)
}

// DeleteByGroupIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.DeleteByGroupID invocations
func (mmDeleteByGroupID *ParticipantGroupPrimeDBMock) DeleteByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupID.beforeDeleteByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.DeleteByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByGroupID *mParticipantGroupPrimeDBMockDeleteByGroupID) Calls() []*ParticipantGroupPrimeDBMockDeleteByGroupIDParams {
	mmDeleteByGroupID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockDeleteByGroupIDParams, len(mmDeleteByGroupID.callArgs))
	copy(argCopy, mmDeleteByGroupID.callArgs)

	mmDeleteByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByGroupIDDone returns true if the count of the DeleteByGroupID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByGroupIDDone() bool {
	if m.DeleteByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByGroupIDMock.invocationsDone()
}

// MinimockDeleteByGroupIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByGroupIDInspect() {
	for _, e := range m.DeleteByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByGroupIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByGroupIDMock.defaultExpectation != nil && afterDeleteByGroupIDCounter < 1 {
		if m.DeleteByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByGroupID at\n%s", m.DeleteByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByGroupID at\n%s with params: %#v", m.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByGroupID != nil && afterDeleteByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByGroupID at\n%s", m.funcDeleteByGroupIDOrigin)
	}

	if !m.DeleteByGroupIDMock.invocationsDone() && afterDeleteByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.DeleteByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByGroupIDMock.expectedInvocations), m.DeleteByGroupIDMock.expectedInvocationsOrigin, afterDeleteByGroupIDCounter)
	}
}

type mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
type ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams contains parameters of the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
type ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams struct {
	ctx           context.Context
	participantID int64
	groupID       int64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
type ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	groupID       *int64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDResults contains results of the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
type ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDResults struct {
	err error
}

// ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
type ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originGroupID       string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Optional() *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	mmDeleteByParticipantAndGroupID.optional = true
	return mmDeleteByParticipantAndGroupID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Expect(ctx context.Context, participantID int64, groupID int64) *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	if mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Set")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation{}
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantAndGroupID.defaultExpectation.params = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams{ctx, participantID, groupID}
	mmDeleteByParticipantAndGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantAndGroupID.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantAndGroupID.defaultExpectation.params) {
			mmDeleteByParticipantAndGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantAndGroupID.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantAndGroupID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	if mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Set")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation{}
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.params != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Expect")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParamPtrs{}
	}
	mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByParticipantAndGroupID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByParticipantAndGroupID
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) ExpectParticipantIDParam2(participantID int64) *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	if mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Set")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation{}
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.params != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Expect")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParamPtrs{}
	}
	mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs.participantID = &participantID
	mmDeleteByParticipantAndGroupID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDeleteByParticipantAndGroupID
}

// ExpectGroupIDParam3 sets up expected param groupID for ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) ExpectGroupIDParam3(groupID int64) *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	if mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Set")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation{}
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.params != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Expect")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParamPtrs{}
	}
	mmDeleteByParticipantAndGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByParticipantAndGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByParticipantAndGroupID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Inspect(f func(ctx context.Context, participantID int64, groupID int64)) *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	if mmDeleteByParticipantAndGroupID.mock.inspectFuncDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID")
	}

	mmDeleteByParticipantAndGroupID.mock.inspectFuncDeleteByParticipantAndGroupID = f

	return mmDeleteByParticipantAndGroupID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Return(err error) *ParticipantGroupPrimeDBMock {
	if mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Set")
	}

	if mmDeleteByParticipantAndGroupID.defaultExpectation == nil {
		mmDeleteByParticipantAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation{mock: mmDeleteByParticipantAndGroupID.mock}
	}
	mmDeleteByParticipantAndGroupID.defaultExpectation.results = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDResults{err}
	mmDeleteByParticipantAndGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantAndGroupID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID method
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Set(f func(ctx context.Context, participantID int64, groupID int64) (err error)) *ParticipantGroupPrimeDBMock {
	if mmDeleteByParticipantAndGroupID.defaultExpectation != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID method")
	}

	if len(mmDeleteByParticipantAndGroupID.expectations) > 0 {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID method")
	}

	mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID = f
	mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantAndGroupID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) When(ctx context.Context, participantID int64, groupID int64) *ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation {
	if mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation{
		mock:               mmDeleteByParticipantAndGroupID.mock,
		params:             &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams{ctx, participantID, groupID},
		expectationOrigins: ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantAndGroupID.expectations = append(mmDeleteByParticipantAndGroupID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDExpectation) Then(err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.DeleteByParticipantAndGroupID should be invoked
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Times(n uint64) *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID {
	if n == 0 {
		mmDeleteByParticipantAndGroupID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantAndGroupID.expectedInvocations, n)
	mmDeleteByParticipantAndGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantAndGroupID
}

func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) invocationsDone() bool {
	if len(mmDeleteByParticipantAndGroupID.expectations) == 0 && mmDeleteByParticipantAndGroupID.defaultExpectation == nil && mmDeleteByParticipantAndGroupID.mock.funcDeleteByParticipantAndGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantAndGroupID.mock.afterDeleteByParticipantAndGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantAndGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantAndGroupID implements mm_repository.ParticipantGroupPrimeDB
func (mmDeleteByParticipantAndGroupID *ParticipantGroupPrimeDBMock) DeleteByParticipantAndGroupID(ctx context.Context, participantID int64, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantAndGroupID.beforeDeleteByParticipantAndGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantAndGroupID.afterDeleteByParticipantAndGroupIDCounter, 1)

	mmDeleteByParticipantAndGroupID.t.Helper()

	if mmDeleteByParticipantAndGroupID.inspectFuncDeleteByParticipantAndGroupID != nil {
		mmDeleteByParticipantAndGroupID.inspectFuncDeleteByParticipantAndGroupID(ctx, participantID, groupID)
	}

	mm_params := ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams{ctx, participantID, groupID}

	// Record call args
	mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.mutex.Lock()
	mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.callArgs = append(mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.callArgs, &mm_params)
	mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams{ctx, participantID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByParticipantAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDeleteByParticipantAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByParticipantAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantAndGroupID.DeleteByParticipantAndGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantAndGroupID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantAndGroupID.funcDeleteByParticipantAndGroupID != nil {
		return mmDeleteByParticipantAndGroupID.funcDeleteByParticipantAndGroupID(ctx, participantID, groupID)
	}
	mmDeleteByParticipantAndGroupID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID. %v %v %v", ctx, participantID, groupID)
	return
}

// DeleteByParticipantAndGroupIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID invocations
func (mmDeleteByParticipantAndGroupID *ParticipantGroupPrimeDBMock) DeleteByParticipantAndGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantAndGroupID.afterDeleteByParticipantAndGroupIDCounter)
}

// DeleteByParticipantAndGroupIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID invocations
func (mmDeleteByParticipantAndGroupID *ParticipantGroupPrimeDBMock) DeleteByParticipantAndGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantAndGroupID.beforeDeleteByParticipantAndGroupIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantAndGroupID *mParticipantGroupPrimeDBMockDeleteByParticipantAndGroupID) Calls() []*ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams {
	mmDeleteByParticipantAndGroupID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockDeleteByParticipantAndGroupIDParams, len(mmDeleteByParticipantAndGroupID.callArgs))
	copy(argCopy, mmDeleteByParticipantAndGroupID.callArgs)

	mmDeleteByParticipantAndGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantAndGroupIDDone returns true if the count of the DeleteByParticipantAndGroupID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByParticipantAndGroupIDDone() bool {
	if m.DeleteByParticipantAndGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantAndGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantAndGroupIDMock.invocationsDone()
}

// MinimockDeleteByParticipantAndGroupIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByParticipantAndGroupIDInspect() {
	for _, e := range m.DeleteByParticipantAndGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantAndGroupIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantAndGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantAndGroupIDMock.defaultExpectation != nil && afterDeleteByParticipantAndGroupIDCounter < 1 {
		if m.DeleteByParticipantAndGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID at\n%s", m.DeleteByParticipantAndGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID at\n%s with params: %#v", m.DeleteByParticipantAndGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantAndGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantAndGroupID != nil && afterDeleteByParticipantAndGroupIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID at\n%s", m.funcDeleteByParticipantAndGroupIDOrigin)
	}

	if !m.DeleteByParticipantAndGroupIDMock.invocationsDone() && afterDeleteByParticipantAndGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.DeleteByParticipantAndGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantAndGroupIDMock.expectedInvocations), m.DeleteByParticipantAndGroupIDMock.expectedInvocationsOrigin, afterDeleteByParticipantAndGroupIDCounter)
	}
}

type mParticipantGroupPrimeDBMockDeleteByParticipantID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockDeleteByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.DeleteByParticipantID
type ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockDeleteByParticipantIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockDeleteByParticipantIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockDeleteByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDParams contains parameters of the ParticipantGroupPrimeDB.DeleteByParticipantID
type ParticipantGroupPrimeDBMockDeleteByParticipantIDParams struct {
	ctx           context.Context
	participantID int64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.DeleteByParticipantID
type ParticipantGroupPrimeDBMockDeleteByParticipantIDParamPtrs struct {
	ctx           *context.Context
	participantID *int64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDResults contains results of the ParticipantGroupPrimeDB.DeleteByParticipantID
type ParticipantGroupPrimeDBMockDeleteByParticipantIDResults struct {
	err error
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.DeleteByParticipantID
type ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Optional() *mParticipantGroupPrimeDBMockDeleteByParticipantID {
	mmDeleteByParticipantID.optional = true
	return mmDeleteByParticipantID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Expect(ctx context.Context, participantID int64) *mParticipantGroupPrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation{}
	}

	if mmDeleteByParticipantID.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantID.defaultExpectation.params = &ParticipantGroupPrimeDBMockDeleteByParticipantIDParams{ctx, participantID}
	mmDeleteByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantID.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantID.defaultExpectation.params) {
			mmDeleteByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantID.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation{}
	}

	if mmDeleteByParticipantID.defaultExpectation.params != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Expect")
	}

	if mmDeleteByParticipantID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantIDParamPtrs{}
	}
	mmDeleteByParticipantID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByParticipantID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByParticipantID
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantGroupPrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) ExpectParticipantIDParam2(participantID int64) *mParticipantGroupPrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation{}
	}

	if mmDeleteByParticipantID.defaultExpectation.params != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Expect")
	}

	if mmDeleteByParticipantID.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantIDParamPtrs{}
	}
	mmDeleteByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmDeleteByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDeleteByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Inspect(f func(ctx context.Context, participantID int64)) *mParticipantGroupPrimeDBMockDeleteByParticipantID {
	if mmDeleteByParticipantID.mock.inspectFuncDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.DeleteByParticipantID")
	}

	mmDeleteByParticipantID.mock.inspectFuncDeleteByParticipantID = f

	return mmDeleteByParticipantID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.DeleteByParticipantID
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Return(err error) *ParticipantGroupPrimeDBMock {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	if mmDeleteByParticipantID.defaultExpectation == nil {
		mmDeleteByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation{mock: mmDeleteByParticipantID.mock}
	}
	mmDeleteByParticipantID.defaultExpectation.results = &ParticipantGroupPrimeDBMockDeleteByParticipantIDResults{err}
	mmDeleteByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.DeleteByParticipantID method
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Set(f func(ctx context.Context, participantID int64) (err error)) *ParticipantGroupPrimeDBMock {
	if mmDeleteByParticipantID.defaultExpectation != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.DeleteByParticipantID method")
	}

	if len(mmDeleteByParticipantID.expectations) > 0 {
		mmDeleteByParticipantID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.DeleteByParticipantID method")
	}

	mmDeleteByParticipantID.mock.funcDeleteByParticipantID = f
	mmDeleteByParticipantID.mock.funcDeleteByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.DeleteByParticipantID which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) When(ctx context.Context, participantID int64) *ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation {
	if mmDeleteByParticipantID.mock.funcDeleteByParticipantID != nil {
		mmDeleteByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation{
		mock:               mmDeleteByParticipantID.mock,
		params:             &ParticipantGroupPrimeDBMockDeleteByParticipantIDParams{ctx, participantID},
		expectationOrigins: ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantID.expectations = append(mmDeleteByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.DeleteByParticipantID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockDeleteByParticipantIDExpectation) Then(err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockDeleteByParticipantIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.DeleteByParticipantID should be invoked
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Times(n uint64) *mParticipantGroupPrimeDBMockDeleteByParticipantID {
	if n == 0 {
		mmDeleteByParticipantID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.DeleteByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantID.expectedInvocations, n)
	mmDeleteByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantID
}

func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) invocationsDone() bool {
	if len(mmDeleteByParticipantID.expectations) == 0 && mmDeleteByParticipantID.defaultExpectation == nil && mmDeleteByParticipantID.mock.funcDeleteByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantID.mock.afterDeleteByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantID implements mm_repository.ParticipantGroupPrimeDB
func (mmDeleteByParticipantID *ParticipantGroupPrimeDBMock) DeleteByParticipantID(ctx context.Context, participantID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantID.beforeDeleteByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantID.afterDeleteByParticipantIDCounter, 1)

	mmDeleteByParticipantID.t.Helper()

	if mmDeleteByParticipantID.inspectFuncDeleteByParticipantID != nil {
		mmDeleteByParticipantID.inspectFuncDeleteByParticipantID(ctx, participantID)
	}

	mm_params := ParticipantGroupPrimeDBMockDeleteByParticipantIDParams{ctx, participantID}

	// Record call args
	mmDeleteByParticipantID.DeleteByParticipantIDMock.mutex.Lock()
	mmDeleteByParticipantID.DeleteByParticipantIDMock.callArgs = append(mmDeleteByParticipantID.DeleteByParticipantIDMock.callArgs, &mm_params)
	mmDeleteByParticipantID.DeleteByParticipantIDMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantID.DeleteByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockDeleteByParticipantIDParams{ctx, participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByParticipantID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDeleteByParticipantID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantID.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantID.DeleteByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.DeleteByParticipantID")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantID.funcDeleteByParticipantID != nil {
		return mmDeleteByParticipantID.funcDeleteByParticipantID(ctx, participantID)
	}
	mmDeleteByParticipantID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.DeleteByParticipantID. %v %v", ctx, participantID)
	return
}

// DeleteByParticipantIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.DeleteByParticipantID invocations
func (mmDeleteByParticipantID *ParticipantGroupPrimeDBMock) DeleteByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantID.afterDeleteByParticipantIDCounter)
}

// DeleteByParticipantIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.DeleteByParticipantID invocations
func (mmDeleteByParticipantID *ParticipantGroupPrimeDBMock) DeleteByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantID.beforeDeleteByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.DeleteByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantID *mParticipantGroupPrimeDBMockDeleteByParticipantID) Calls() []*ParticipantGroupPrimeDBMockDeleteByParticipantIDParams {
	mmDeleteByParticipantID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockDeleteByParticipantIDParams, len(mmDeleteByParticipantID.callArgs))
	copy(argCopy, mmDeleteByParticipantID.callArgs)

	mmDeleteByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantIDDone returns true if the count of the DeleteByParticipantID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByParticipantIDDone() bool {
	if m.DeleteByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantIDMock.invocationsDone()
}

// MinimockDeleteByParticipantIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByParticipantIDInspect() {
	for _, e := range m.DeleteByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantIDMock.defaultExpectation != nil && afterDeleteByParticipantIDCounter < 1 {
		if m.DeleteByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantID at\n%s", m.DeleteByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantID at\n%s with params: %#v", m.DeleteByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantID != nil && afterDeleteByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantID at\n%s", m.funcDeleteByParticipantIDOrigin)
	}

	if !m.DeleteByParticipantIDMock.invocationsDone() && afterDeleteByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.DeleteByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantIDMock.expectedInvocations), m.DeleteByParticipantIDMock.expectedInvocationsOrigin, afterDeleteByParticipantIDCounter)
	}
}

type mParticipantGroupPrimeDBMockDeleteByParticipantIDs struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation
	expectations       []*ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation

	callArgs []*ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation specifies expectation struct of the ParticipantGroupPrimeDB.DeleteByParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams
	paramPtrs          *ParticipantGroupPrimeDBMockDeleteByParticipantIDsParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectationOrigins
	results            *ParticipantGroupPrimeDBMockDeleteByParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams contains parameters of the ParticipantGroupPrimeDB.DeleteByParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams struct {
	ctx            context.Context
	participantIDs []int64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDsParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.DeleteByParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByParticipantIDsParamPtrs struct {
	ctx            *context.Context
	participantIDs *[]int64
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDsResults contains results of the ParticipantGroupPrimeDB.DeleteByParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByParticipantIDsResults struct {
	err error
}

// ParticipantGroupPrimeDBMockDeleteByParticipantIDsOrigins contains origins of expectations of the ParticipantGroupPrimeDB.DeleteByParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectationOrigins struct {
	origin               string
	originCtx            string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Optional() *mParticipantGroupPrimeDBMockDeleteByParticipantIDs {
	mmDeleteByParticipantIDs.optional = true
	return mmDeleteByParticipantIDs
}

// Expect sets up expected params for ParticipantGroupPrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Expect(ctx context.Context, participantIDs []int64) *mParticipantGroupPrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation{}
	}

	if mmDeleteByParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByParticipantIDs.defaultExpectation.params = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams{ctx, participantIDs}
	mmDeleteByParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByParticipantIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByParticipantIDs.defaultExpectation.params) {
			mmDeleteByParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByParticipantIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByParticipantIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation{}
	}

	if mmDeleteByParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsParamPtrs{}
	}
	mmDeleteByParticipantIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByParticipantIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByParticipantIDs
}

// ExpectParticipantIDsParam2 sets up expected param participantIDs for ParticipantGroupPrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) ExpectParticipantIDsParam2(participantIDs []int64) *mParticipantGroupPrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation{}
	}

	if mmDeleteByParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsParamPtrs{}
	}
	mmDeleteByParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmDeleteByParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmDeleteByParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Inspect(f func(ctx context.Context, participantIDs []int64)) *mParticipantGroupPrimeDBMockDeleteByParticipantIDs {
	if mmDeleteByParticipantIDs.mock.inspectFuncDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.DeleteByParticipantIDs")
	}

	mmDeleteByParticipantIDs.mock.inspectFuncDeleteByParticipantIDs = f

	return mmDeleteByParticipantIDs
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.DeleteByParticipantIDs
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Return(err error) *ParticipantGroupPrimeDBMock {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	if mmDeleteByParticipantIDs.defaultExpectation == nil {
		mmDeleteByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation{mock: mmDeleteByParticipantIDs.mock}
	}
	mmDeleteByParticipantIDs.defaultExpectation.results = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsResults{err}
	mmDeleteByParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.DeleteByParticipantIDs method
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Set(f func(ctx context.Context, participantIDs []int64) (err error)) *ParticipantGroupPrimeDBMock {
	if mmDeleteByParticipantIDs.defaultExpectation != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.DeleteByParticipantIDs method")
	}

	if len(mmDeleteByParticipantIDs.expectations) > 0 {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.DeleteByParticipantIDs method")
	}

	mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs = f
	mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDs.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.DeleteByParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) When(ctx context.Context, participantIDs []int64) *ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation {
	if mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation{
		mock:               mmDeleteByParticipantIDs.mock,
		params:             &ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams{ctx, participantIDs},
		expectationOrigins: ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByParticipantIDs.expectations = append(mmDeleteByParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.DeleteByParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockDeleteByParticipantIDsExpectation) Then(err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockDeleteByParticipantIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.DeleteByParticipantIDs should be invoked
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Times(n uint64) *mParticipantGroupPrimeDBMockDeleteByParticipantIDs {
	if n == 0 {
		mmDeleteByParticipantIDs.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.DeleteByParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByParticipantIDs.expectedInvocations, n)
	mmDeleteByParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByParticipantIDs
}

func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) invocationsDone() bool {
	if len(mmDeleteByParticipantIDs.expectations) == 0 && mmDeleteByParticipantIDs.defaultExpectation == nil && mmDeleteByParticipantIDs.mock.funcDeleteByParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.mock.afterDeleteByParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByParticipantIDs implements mm_repository.ParticipantGroupPrimeDB
func (mmDeleteByParticipantIDs *ParticipantGroupPrimeDBMock) DeleteByParticipantIDs(ctx context.Context, participantIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByParticipantIDs.beforeDeleteByParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByParticipantIDs.afterDeleteByParticipantIDsCounter, 1)

	mmDeleteByParticipantIDs.t.Helper()

	if mmDeleteByParticipantIDs.inspectFuncDeleteByParticipantIDs != nil {
		mmDeleteByParticipantIDs.inspectFuncDeleteByParticipantIDs(ctx, participantIDs)
	}

	mm_params := ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams{ctx, participantIDs}

	// Record call args
	mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.mutex.Lock()
	mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.callArgs = append(mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.callArgs, &mm_params)
	mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams{ctx, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmDeleteByParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByParticipantIDs.DeleteByParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByParticipantIDs.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.DeleteByParticipantIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByParticipantIDs.funcDeleteByParticipantIDs != nil {
		return mmDeleteByParticipantIDs.funcDeleteByParticipantIDs(ctx, participantIDs)
	}
	mmDeleteByParticipantIDs.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs. %v %v", ctx, participantIDs)
	return
}

// DeleteByParticipantIDsAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.DeleteByParticipantIDs invocations
func (mmDeleteByParticipantIDs *ParticipantGroupPrimeDBMock) DeleteByParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.afterDeleteByParticipantIDsCounter)
}

// DeleteByParticipantIDsBeforeCounter returns a count of ParticipantGroupPrimeDBMock.DeleteByParticipantIDs invocations
func (mmDeleteByParticipantIDs *ParticipantGroupPrimeDBMock) DeleteByParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByParticipantIDs.beforeDeleteByParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByParticipantIDs *mParticipantGroupPrimeDBMockDeleteByParticipantIDs) Calls() []*ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams {
	mmDeleteByParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockDeleteByParticipantIDsParams, len(mmDeleteByParticipantIDs.callArgs))
	copy(argCopy, mmDeleteByParticipantIDs.callArgs)

	mmDeleteByParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByParticipantIDsDone returns true if the count of the DeleteByParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByParticipantIDsDone() bool {
	if m.DeleteByParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByParticipantIDsMock.invocationsDone()
}

// MinimockDeleteByParticipantIDsInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByParticipantIDsInspect() {
	for _, e := range m.DeleteByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByParticipantIDsMock.defaultExpectation != nil && afterDeleteByParticipantIDsCounter < 1 {
		if m.DeleteByParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs at\n%s", m.DeleteByParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs at\n%s with params: %#v", m.DeleteByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByParticipantIDs != nil && afterDeleteByParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs at\n%s", m.funcDeleteByParticipantIDsOrigin)
	}

	if !m.DeleteByParticipantIDsMock.invocationsDone() && afterDeleteByParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.DeleteByParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByParticipantIDsMock.expectedInvocations), m.DeleteByParticipantIDsMock.expectedInvocationsOrigin, afterDeleteByParticipantIDsCounter)
	}
}

type mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation
	expectations       []*ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation

	callArgs []*ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation specifies expectation struct of the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams
	paramPtrs          *ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectationOrigins
	results            *ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams contains parameters of the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams struct {
	ctx            context.Context
	groupID        int64
	participantIDs []int64
}

// ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs struct {
	ctx            *context.Context
	groupID        *int64
	participantIDs *[]int64
}

// ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsResults contains results of the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsResults struct {
	err error
}

// ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsOrigins contains origins of expectations of the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
type ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectationOrigins struct {
	origin               string
	originCtx            string
	originGroupID        string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Optional() *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	mmDeleteByRoleIDAndParticipantIDs.optional = true
	return mmDeleteByRoleIDAndParticipantIDs
}

// Expect sets up expected params for ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Expect(ctx context.Context, groupID int64, participantIDs []int64) *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleIDAndParticipantIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params) {
			mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleIDAndParticipantIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndParticipantIDs
}

// ExpectGroupIDParam2 sets up expected param groupID for ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) ExpectGroupIDParam2(groupID int64) *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndParticipantIDs
}

// ExpectParticipantIDsParam3 sets up expected param participantIDs for ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) ExpectParticipantIDsParam3(participantIDs []int64) *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{}
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Inspect(f func(ctx context.Context, groupID int64, participantIDs []int64)) *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if mmDeleteByRoleIDAndParticipantIDs.mock.inspectFuncDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs")
	}

	mmDeleteByRoleIDAndParticipantIDs.mock.inspectFuncDeleteByRoleIDAndParticipantIDs = f

	return mmDeleteByRoleIDAndParticipantIDs
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Return(err error) *ParticipantGroupPrimeDBMock {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{mock: mmDeleteByRoleIDAndParticipantIDs.mock}
	}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.results = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsResults{err}
	mmDeleteByRoleIDAndParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs method
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Set(f func(ctx context.Context, groupID int64, participantIDs []int64) (err error)) *ParticipantGroupPrimeDBMock {
	if mmDeleteByRoleIDAndParticipantIDs.defaultExpectation != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs method")
	}

	if len(mmDeleteByRoleIDAndParticipantIDs.expectations) > 0 {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs method")
	}

	mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs = f
	mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndParticipantIDs.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) When(ctx context.Context, groupID int64, participantIDs []int64) *ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation {
	if mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation{
		mock:               mmDeleteByRoleIDAndParticipantIDs.mock,
		params:             &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs},
		expectationOrigins: ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleIDAndParticipantIDs.expectations = append(mmDeleteByRoleIDAndParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsExpectation) Then(err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.DeleteByRoleIDAndParticipantIDs should be invoked
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Times(n uint64) *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs {
	if n == 0 {
		mmDeleteByRoleIDAndParticipantIDs.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleIDAndParticipantIDs.expectedInvocations, n)
	mmDeleteByRoleIDAndParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndParticipantIDs
}

func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) invocationsDone() bool {
	if len(mmDeleteByRoleIDAndParticipantIDs.expectations) == 0 && mmDeleteByRoleIDAndParticipantIDs.defaultExpectation == nil && mmDeleteByRoleIDAndParticipantIDs.mock.funcDeleteByRoleIDAndParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.mock.afterDeleteByRoleIDAndParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleIDAndParticipantIDs implements mm_repository.ParticipantGroupPrimeDB
func (mmDeleteByRoleIDAndParticipantIDs *ParticipantGroupPrimeDBMock) DeleteByRoleIDAndParticipantIDs(ctx context.Context, groupID int64, participantIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleIDAndParticipantIDs.beforeDeleteByRoleIDAndParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleIDAndParticipantIDs.afterDeleteByRoleIDAndParticipantIDsCounter, 1)

	mmDeleteByRoleIDAndParticipantIDs.t.Helper()

	if mmDeleteByRoleIDAndParticipantIDs.inspectFuncDeleteByRoleIDAndParticipantIDs != nil {
		mmDeleteByRoleIDAndParticipantIDs.inspectFuncDeleteByRoleIDAndParticipantIDs(ctx, groupID, participantIDs)
	}

	mm_params := ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs}

	// Record call args
	mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.mutex.Lock()
	mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.callArgs = append(mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.callArgs, &mm_params)
	mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams{ctx, groupID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleIDAndParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleIDAndParticipantIDs.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleIDAndParticipantIDs.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleIDAndParticipantIDs.funcDeleteByRoleIDAndParticipantIDs != nil {
		return mmDeleteByRoleIDAndParticipantIDs.funcDeleteByRoleIDAndParticipantIDs(ctx, groupID, participantIDs)
	}
	mmDeleteByRoleIDAndParticipantIDs.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs. %v %v %v", ctx, groupID, participantIDs)
	return
}

// DeleteByRoleIDAndParticipantIDsAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs invocations
func (mmDeleteByRoleIDAndParticipantIDs *ParticipantGroupPrimeDBMock) DeleteByRoleIDAndParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.afterDeleteByRoleIDAndParticipantIDsCounter)
}

// DeleteByRoleIDAndParticipantIDsBeforeCounter returns a count of ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs invocations
func (mmDeleteByRoleIDAndParticipantIDs *ParticipantGroupPrimeDBMock) DeleteByRoleIDAndParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndParticipantIDs.beforeDeleteByRoleIDAndParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleIDAndParticipantIDs *mParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDs) Calls() []*ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams {
	mmDeleteByRoleIDAndParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockDeleteByRoleIDAndParticipantIDsParams, len(mmDeleteByRoleIDAndParticipantIDs.callArgs))
	copy(argCopy, mmDeleteByRoleIDAndParticipantIDs.callArgs)

	mmDeleteByRoleIDAndParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDAndParticipantIDsDone returns true if the count of the DeleteByRoleIDAndParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByRoleIDAndParticipantIDsDone() bool {
	if m.DeleteByRoleIDAndParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDAndParticipantIDsMock.invocationsDone()
}

// MinimockDeleteByRoleIDAndParticipantIDsInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockDeleteByRoleIDAndParticipantIDsInspect() {
	for _, e := range m.DeleteByRoleIDAndParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDAndParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDAndParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation != nil && afterDeleteByRoleIDAndParticipantIDsCounter < 1 {
		if m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s", m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s with params: %#v", m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDAndParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleIDAndParticipantIDs != nil && afterDeleteByRoleIDAndParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s", m.funcDeleteByRoleIDAndParticipantIDsOrigin)
	}

	if !m.DeleteByRoleIDAndParticipantIDsMock.invocationsDone() && afterDeleteByRoleIDAndParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.DeleteByRoleIDAndParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDAndParticipantIDsMock.expectedInvocations), m.DeleteByRoleIDAndParticipantIDsMock.expectedInvocationsOrigin, afterDeleteByRoleIDAndParticipantIDsCounter)
	}
}

type mParticipantGroupPrimeDBMockGetAll struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockGetAllExpectation
	expectations       []*ParticipantGroupPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockGetAllExpectation specifies expectation struct of the ParticipantGroupPrimeDB.GetAll
type ParticipantGroupPrimeDBMockGetAllExpectation struct {
	mock *ParticipantGroupPrimeDBMock

	results      *ParticipantGroupPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// ParticipantGroupPrimeDBMockGetAllResults contains results of the ParticipantGroupPrimeDB.GetAll
type ParticipantGroupPrimeDBMockGetAllResults struct {
	pa1 []participantentity.ParticipantGroup
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) Optional() *mParticipantGroupPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for ParticipantGroupPrimeDB.GetAll
func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) Expect() *mParticipantGroupPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ParticipantGroupPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.GetAll
func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) Inspect(f func()) *mParticipantGroupPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.GetAll
func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) Return(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ParticipantGroupPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &ParticipantGroupPrimeDBMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.GetAll method
func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) Set(f func() (pa1 []participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times ParticipantGroupPrimeDB.GetAll should be invoked
func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) Times(n uint64) *mParticipantGroupPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mParticipantGroupPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.ParticipantGroupPrimeDB
func (mmGetAll *ParticipantGroupPrimeDBMock) GetAll() (pa1 []participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.GetAll invocations
func (mmGetAll *ParticipantGroupPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of ParticipantGroupPrimeDBMock.GetAll invocations
func (mmGetAll *ParticipantGroupPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ParticipantGroupPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mParticipantGroupPrimeDBMockGetByGroupID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockGetByGroupIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockGetByGroupIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockGetByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockGetByGroupIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.GetByGroupID
type ParticipantGroupPrimeDBMockGetByGroupIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockGetByGroupIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockGetByGroupIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockGetByGroupIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockGetByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockGetByGroupIDParams contains parameters of the ParticipantGroupPrimeDB.GetByGroupID
type ParticipantGroupPrimeDBMockGetByGroupIDParams struct {
	groupID int64
}

// ParticipantGroupPrimeDBMockGetByGroupIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.GetByGroupID
type ParticipantGroupPrimeDBMockGetByGroupIDParamPtrs struct {
	groupID *int64
}

// ParticipantGroupPrimeDBMockGetByGroupIDResults contains results of the ParticipantGroupPrimeDB.GetByGroupID
type ParticipantGroupPrimeDBMockGetByGroupIDResults struct {
	pa1 []participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockGetByGroupIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.GetByGroupID
type ParticipantGroupPrimeDBMockGetByGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Optional() *mParticipantGroupPrimeDBMockGetByGroupID {
	mmGetByGroupID.optional = true
	return mmGetByGroupID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Expect(groupID int64) *mParticipantGroupPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByGroupID mock is already set by ExpectParams functions")
	}

	mmGetByGroupID.defaultExpectation.params = &ParticipantGroupPrimeDBMockGetByGroupIDParams{groupID}
	mmGetByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupID.expectations {
		if minimock.Equal(e.params, mmGetByGroupID.defaultExpectation.params) {
			mmGetByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupID.defaultExpectation.params)
		}
	}

	return mmGetByGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for ParticipantGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) ExpectGroupIDParam1(groupID int64) *mParticipantGroupPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.params != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByGroupID mock is already set by Expect")
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockGetByGroupIDParamPtrs{}
	}
	mmGetByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Inspect(f func(groupID int64)) *mParticipantGroupPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.GetByGroupID")
	}

	mmGetByGroupID.mock.inspectFuncGetByGroupID = f

	return mmGetByGroupID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Return(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByGroupIDExpectation{mock: mmGetByGroupID.mock}
	}
	mmGetByGroupID.defaultExpectation.results = &ParticipantGroupPrimeDBMockGetByGroupIDResults{pa1, err}
	mmGetByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.GetByGroupID method
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Set(f func(groupID int64) (pa1 []participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmGetByGroupID.defaultExpectation != nil {
		mmGetByGroupID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.GetByGroupID method")
	}

	if len(mmGetByGroupID.expectations) > 0 {
		mmGetByGroupID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.GetByGroupID method")
	}

	mmGetByGroupID.mock.funcGetByGroupID = f
	mmGetByGroupID.mock.funcGetByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.GetByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) When(groupID int64) *ParticipantGroupPrimeDBMockGetByGroupIDExpectation {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockGetByGroupIDExpectation{
		mock:               mmGetByGroupID.mock,
		params:             &ParticipantGroupPrimeDBMockGetByGroupIDParams{groupID},
		expectationOrigins: ParticipantGroupPrimeDBMockGetByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupID.expectations = append(mmGetByGroupID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.GetByGroupID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockGetByGroupIDExpectation) Then(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockGetByGroupIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.GetByGroupID should be invoked
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Times(n uint64) *mParticipantGroupPrimeDBMockGetByGroupID {
	if n == 0 {
		mmGetByGroupID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.GetByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupID.expectedInvocations, n)
	mmGetByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID
}

func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) invocationsDone() bool {
	if len(mmGetByGroupID.expectations) == 0 && mmGetByGroupID.defaultExpectation == nil && mmGetByGroupID.mock.funcGetByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.mock.afterGetByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupID implements mm_repository.ParticipantGroupPrimeDB
func (mmGetByGroupID *ParticipantGroupPrimeDBMock) GetByGroupID(groupID int64) (pa1 []participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmGetByGroupID.beforeGetByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupID.afterGetByGroupIDCounter, 1)

	mmGetByGroupID.t.Helper()

	if mmGetByGroupID.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.inspectFuncGetByGroupID(groupID)
	}

	mm_params := ParticipantGroupPrimeDBMockGetByGroupIDParams{groupID}

	// Record call args
	mmGetByGroupID.GetByGroupIDMock.mutex.Lock()
	mmGetByGroupID.GetByGroupIDMock.callArgs = append(mmGetByGroupID.GetByGroupIDMock.callArgs, &mm_params)
	mmGetByGroupID.GetByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupID.GetByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByGroupID.GetByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupID.GetByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockGetByGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupID.t.Errorf("ParticipantGroupPrimeDBMock.GetByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupID.t.Errorf("ParticipantGroupPrimeDBMock.GetByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.GetByGroupID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByGroupID.funcGetByGroupID != nil {
		return mmGetByGroupID.funcGetByGroupID(groupID)
	}
	mmGetByGroupID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.GetByGroupID. %v", groupID)
	return
}

// GetByGroupIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *ParticipantGroupPrimeDBMock) GetByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.afterGetByGroupIDCounter)
}

// GetByGroupIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *ParticipantGroupPrimeDBMock) GetByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.beforeGetByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.GetByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupID *mParticipantGroupPrimeDBMockGetByGroupID) Calls() []*ParticipantGroupPrimeDBMockGetByGroupIDParams {
	mmGetByGroupID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockGetByGroupIDParams, len(mmGetByGroupID.callArgs))
	copy(argCopy, mmGetByGroupID.callArgs)

	mmGetByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDDone returns true if the count of the GetByGroupID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockGetByGroupIDDone() bool {
	if m.GetByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDMock.invocationsDone()
}

// MinimockGetByGroupIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockGetByGroupIDInspect() {
	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDMock.defaultExpectation != nil && afterGetByGroupIDCounter < 1 {
		if m.GetByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByGroupID at\n%s", m.GetByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByGroupID at\n%s with params: %#v", m.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupID != nil && afterGetByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByGroupID at\n%s", m.funcGetByGroupIDOrigin)
	}

	if !m.GetByGroupIDMock.invocationsDone() && afterGetByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.GetByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDMock.expectedInvocations), m.GetByGroupIDMock.expectedInvocationsOrigin, afterGetByGroupIDCounter)
	}
}

type mParticipantGroupPrimeDBMockGetByID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockGetByIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockGetByIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockGetByIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.GetByID
type ParticipantGroupPrimeDBMockGetByIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockGetByIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockGetByIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockGetByIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockGetByIDParams contains parameters of the ParticipantGroupPrimeDB.GetByID
type ParticipantGroupPrimeDBMockGetByIDParams struct {
	id int64
}

// ParticipantGroupPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.GetByID
type ParticipantGroupPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// ParticipantGroupPrimeDBMockGetByIDResults contains results of the ParticipantGroupPrimeDB.GetByID
type ParticipantGroupPrimeDBMockGetByIDResults struct {
	p1  participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockGetByIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.GetByID
type ParticipantGroupPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Optional() *mParticipantGroupPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.GetByID
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Expect(id int64) *mParticipantGroupPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ParticipantGroupPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ParticipantGroupPrimeDB.GetByID
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) ExpectIdParam1(id int64) *mParticipantGroupPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.GetByID
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Inspect(f func(id int64)) *mParticipantGroupPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.GetByID
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Return(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ParticipantGroupPrimeDBMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.GetByID method
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Set(f func(id int64) (p1 participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) When(id int64) *ParticipantGroupPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ParticipantGroupPrimeDBMockGetByIDParams{id},
		expectationOrigins: ParticipantGroupPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockGetByIDExpectation) Then(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.GetByID should be invoked
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Times(n uint64) *mParticipantGroupPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.ParticipantGroupPrimeDB
func (mmGetByID *ParticipantGroupPrimeDBMock) GetByID(id int64) (p1 participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ParticipantGroupPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ParticipantGroupPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ParticipantGroupPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.GetByID invocations
func (mmGetByID *ParticipantGroupPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.GetByID invocations
func (mmGetByID *ParticipantGroupPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mParticipantGroupPrimeDBMockGetByID) Calls() []*ParticipantGroupPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mParticipantGroupPrimeDBMockGetByParticipantID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockGetByParticipantIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockGetByParticipantIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockGetByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockGetByParticipantIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.GetByParticipantID
type ParticipantGroupPrimeDBMockGetByParticipantIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockGetByParticipantIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockGetByParticipantIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockGetByParticipantIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockGetByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDParams contains parameters of the ParticipantGroupPrimeDB.GetByParticipantID
type ParticipantGroupPrimeDBMockGetByParticipantIDParams struct {
	participantID int64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.GetByParticipantID
type ParticipantGroupPrimeDBMockGetByParticipantIDParamPtrs struct {
	participantID *int64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDResults contains results of the ParticipantGroupPrimeDB.GetByParticipantID
type ParticipantGroupPrimeDBMockGetByParticipantIDResults struct {
	pa1 []participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockGetByParticipantIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.GetByParticipantID
type ParticipantGroupPrimeDBMockGetByParticipantIDExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Optional() *mParticipantGroupPrimeDBMockGetByParticipantID {
	mmGetByParticipantID.optional = true
	return mmGetByParticipantID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Expect(participantID int64) *mParticipantGroupPrimeDBMockGetByParticipantID {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDExpectation{}
	}

	if mmGetByParticipantID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantID.defaultExpectation.params = &ParticipantGroupPrimeDBMockGetByParticipantIDParams{participantID}
	mmGetByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantID.defaultExpectation.params) {
			mmGetByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantGroupPrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) ExpectParticipantIDParam1(participantID int64) *mParticipantGroupPrimeDBMockGetByParticipantID {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDExpectation{}
	}

	if mmGetByParticipantID.defaultExpectation.params != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantID mock is already set by Expect")
	}

	if mmGetByParticipantID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockGetByParticipantIDParamPtrs{}
	}
	mmGetByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Inspect(f func(participantID int64)) *mParticipantGroupPrimeDBMockGetByParticipantID {
	if mmGetByParticipantID.mock.inspectFuncGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.GetByParticipantID")
	}

	mmGetByParticipantID.mock.inspectFuncGetByParticipantID = f

	return mmGetByParticipantID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.GetByParticipantID
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Return(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDExpectation{mock: mmGetByParticipantID.mock}
	}
	mmGetByParticipantID.defaultExpectation.results = &ParticipantGroupPrimeDBMockGetByParticipantIDResults{pa1, err}
	mmGetByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.GetByParticipantID method
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Set(f func(participantID int64) (pa1 []participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmGetByParticipantID.defaultExpectation != nil {
		mmGetByParticipantID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.GetByParticipantID method")
	}

	if len(mmGetByParticipantID.expectations) > 0 {
		mmGetByParticipantID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.GetByParticipantID method")
	}

	mmGetByParticipantID.mock.funcGetByParticipantID = f
	mmGetByParticipantID.mock.funcGetByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.GetByParticipantID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) When(participantID int64) *ParticipantGroupPrimeDBMockGetByParticipantIDExpectation {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockGetByParticipantIDExpectation{
		mock:               mmGetByParticipantID.mock,
		params:             &ParticipantGroupPrimeDBMockGetByParticipantIDParams{participantID},
		expectationOrigins: ParticipantGroupPrimeDBMockGetByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantID.expectations = append(mmGetByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.GetByParticipantID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockGetByParticipantIDExpectation) Then(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockGetByParticipantIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.GetByParticipantID should be invoked
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Times(n uint64) *mParticipantGroupPrimeDBMockGetByParticipantID {
	if n == 0 {
		mmGetByParticipantID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.GetByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantID.expectedInvocations, n)
	mmGetByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID
}

func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) invocationsDone() bool {
	if len(mmGetByParticipantID.expectations) == 0 && mmGetByParticipantID.defaultExpectation == nil && mmGetByParticipantID.mock.funcGetByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantID.mock.afterGetByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantID implements mm_repository.ParticipantGroupPrimeDB
func (mmGetByParticipantID *ParticipantGroupPrimeDBMock) GetByParticipantID(participantID int64) (pa1 []participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantID.beforeGetByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantID.afterGetByParticipantIDCounter, 1)

	mmGetByParticipantID.t.Helper()

	if mmGetByParticipantID.inspectFuncGetByParticipantID != nil {
		mmGetByParticipantID.inspectFuncGetByParticipantID(participantID)
	}

	mm_params := ParticipantGroupPrimeDBMockGetByParticipantIDParams{participantID}

	// Record call args
	mmGetByParticipantID.GetByParticipantIDMock.mutex.Lock()
	mmGetByParticipantID.GetByParticipantIDMock.callArgs = append(mmGetByParticipantID.GetByParticipantIDMock.callArgs, &mm_params)
	mmGetByParticipantID.GetByParticipantIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantID.GetByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockGetByParticipantIDParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantID.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantID.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.GetByParticipantID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByParticipantID.funcGetByParticipantID != nil {
		return mmGetByParticipantID.funcGetByParticipantID(participantID)
	}
	mmGetByParticipantID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.GetByParticipantID. %v", participantID)
	return
}

// GetByParticipantIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.GetByParticipantID invocations
func (mmGetByParticipantID *ParticipantGroupPrimeDBMock) GetByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantID.afterGetByParticipantIDCounter)
}

// GetByParticipantIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.GetByParticipantID invocations
func (mmGetByParticipantID *ParticipantGroupPrimeDBMock) GetByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantID.beforeGetByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.GetByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantID *mParticipantGroupPrimeDBMockGetByParticipantID) Calls() []*ParticipantGroupPrimeDBMockGetByParticipantIDParams {
	mmGetByParticipantID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockGetByParticipantIDParams, len(mmGetByParticipantID.callArgs))
	copy(argCopy, mmGetByParticipantID.callArgs)

	mmGetByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDDone returns true if the count of the GetByParticipantID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockGetByParticipantIDDone() bool {
	if m.GetByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDMock.invocationsDone()
}

// MinimockGetByParticipantIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockGetByParticipantIDInspect() {
	for _, e := range m.GetByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDMock.defaultExpectation != nil && afterGetByParticipantIDCounter < 1 {
		if m.GetByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantID at\n%s", m.GetByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantID at\n%s with params: %#v", m.GetByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantID != nil && afterGetByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantID at\n%s", m.funcGetByParticipantIDOrigin)
	}

	if !m.GetByParticipantIDMock.invocationsDone() && afterGetByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.GetByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDMock.expectedInvocations), m.GetByParticipantIDMock.expectedInvocationsOrigin, afterGetByParticipantIDCounter)
	}
}

type mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation
	expectations       []*ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation

	callArgs []*ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation specifies expectation struct of the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
type ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams
	paramPtrs          *ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectationOrigins
	results            *ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams contains parameters of the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
type ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams struct {
	participantID int64
	groupID       int64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
type ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParamPtrs struct {
	participantID *int64
	groupID       *int64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDResults contains results of the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
type ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDResults struct {
	p1  participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDOrigins contains origins of expectations of the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
type ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originGroupID       string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Optional() *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID {
	mmGetByParticipantIDAndGroupID.optional = true
	return mmGetByParticipantIDAndGroupID
}

// Expect sets up expected params for ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Expect(participantID int64, groupID int64) *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID {
	if mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Set")
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation == nil {
		mmGetByParticipantIDAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation{}
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantIDAndGroupID.defaultExpectation.params = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams{participantID, groupID}
	mmGetByParticipantIDAndGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantIDAndGroupID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantIDAndGroupID.defaultExpectation.params) {
			mmGetByParticipantIDAndGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantIDAndGroupID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantIDAndGroupID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) ExpectParticipantIDParam1(participantID int64) *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID {
	if mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Set")
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation == nil {
		mmGetByParticipantIDAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation{}
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParamPtrs{}
	}
	mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantIDAndGroupID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndGroupID
}

// ExpectGroupIDParam2 sets up expected param groupID for ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) ExpectGroupIDParam2(groupID int64) *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID {
	if mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Set")
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation == nil {
		mmGetByParticipantIDAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation{}
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParamPtrs{}
	}
	mmGetByParticipantIDAndGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByParticipantIDAndGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndGroupID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Inspect(f func(participantID int64, groupID int64)) *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID {
	if mmGetByParticipantIDAndGroupID.mock.inspectFuncGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID")
	}

	mmGetByParticipantIDAndGroupID.mock.inspectFuncGetByParticipantIDAndGroupID = f

	return mmGetByParticipantIDAndGroupID
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Return(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Set")
	}

	if mmGetByParticipantIDAndGroupID.defaultExpectation == nil {
		mmGetByParticipantIDAndGroupID.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation{mock: mmGetByParticipantIDAndGroupID.mock}
	}
	mmGetByParticipantIDAndGroupID.defaultExpectation.results = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDResults{p1, err}
	mmGetByParticipantIDAndGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndGroupID.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID method
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Set(f func(participantID int64, groupID int64) (p1 participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmGetByParticipantIDAndGroupID.defaultExpectation != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID method")
	}

	if len(mmGetByParticipantIDAndGroupID.expectations) > 0 {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID method")
	}

	mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID = f
	mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndGroupID.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) When(participantID int64, groupID int64) *ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation {
	if mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation{
		mock:               mmGetByParticipantIDAndGroupID.mock,
		params:             &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams{participantID, groupID},
		expectationOrigins: ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantIDAndGroupID.expectations = append(mmGetByParticipantIDAndGroupID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDExpectation) Then(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.GetByParticipantIDAndGroupID should be invoked
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Times(n uint64) *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID {
	if n == 0 {
		mmGetByParticipantIDAndGroupID.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantIDAndGroupID.expectedInvocations, n)
	mmGetByParticipantIDAndGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndGroupID
}

func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) invocationsDone() bool {
	if len(mmGetByParticipantIDAndGroupID.expectations) == 0 && mmGetByParticipantIDAndGroupID.defaultExpectation == nil && mmGetByParticipantIDAndGroupID.mock.funcGetByParticipantIDAndGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndGroupID.mock.afterGetByParticipantIDAndGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantIDAndGroupID implements mm_repository.ParticipantGroupPrimeDB
func (mmGetByParticipantIDAndGroupID *ParticipantGroupPrimeDBMock) GetByParticipantIDAndGroupID(participantID int64, groupID int64) (p1 participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantIDAndGroupID.beforeGetByParticipantIDAndGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantIDAndGroupID.afterGetByParticipantIDAndGroupIDCounter, 1)

	mmGetByParticipantIDAndGroupID.t.Helper()

	if mmGetByParticipantIDAndGroupID.inspectFuncGetByParticipantIDAndGroupID != nil {
		mmGetByParticipantIDAndGroupID.inspectFuncGetByParticipantIDAndGroupID(participantID, groupID)
	}

	mm_params := ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams{participantID, groupID}

	// Record call args
	mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.mutex.Lock()
	mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.callArgs = append(mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.callArgs, &mm_params)
	mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams{participantID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantIDAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByParticipantIDAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantIDAndGroupID.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantIDAndGroupID.GetByParticipantIDAndGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantIDAndGroupID.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByParticipantIDAndGroupID.funcGetByParticipantIDAndGroupID != nil {
		return mmGetByParticipantIDAndGroupID.funcGetByParticipantIDAndGroupID(participantID, groupID)
	}
	mmGetByParticipantIDAndGroupID.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID. %v %v", participantID, groupID)
	return
}

// GetByParticipantIDAndGroupIDAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID invocations
func (mmGetByParticipantIDAndGroupID *ParticipantGroupPrimeDBMock) GetByParticipantIDAndGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndGroupID.afterGetByParticipantIDAndGroupIDCounter)
}

// GetByParticipantIDAndGroupIDBeforeCounter returns a count of ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID invocations
func (mmGetByParticipantIDAndGroupID *ParticipantGroupPrimeDBMock) GetByParticipantIDAndGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndGroupID.beforeGetByParticipantIDAndGroupIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantIDAndGroupID *mParticipantGroupPrimeDBMockGetByParticipantIDAndGroupID) Calls() []*ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams {
	mmGetByParticipantIDAndGroupID.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockGetByParticipantIDAndGroupIDParams, len(mmGetByParticipantIDAndGroupID.callArgs))
	copy(argCopy, mmGetByParticipantIDAndGroupID.callArgs)

	mmGetByParticipantIDAndGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDAndGroupIDDone returns true if the count of the GetByParticipantIDAndGroupID invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockGetByParticipantIDAndGroupIDDone() bool {
	if m.GetByParticipantIDAndGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDAndGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDAndGroupIDMock.invocationsDone()
}

// MinimockGetByParticipantIDAndGroupIDInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockGetByParticipantIDAndGroupIDInspect() {
	for _, e := range m.GetByParticipantIDAndGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDAndGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDAndGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDAndGroupIDMock.defaultExpectation != nil && afterGetByParticipantIDAndGroupIDCounter < 1 {
		if m.GetByParticipantIDAndGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID at\n%s", m.GetByParticipantIDAndGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID at\n%s with params: %#v", m.GetByParticipantIDAndGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDAndGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantIDAndGroupID != nil && afterGetByParticipantIDAndGroupIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID at\n%s", m.funcGetByParticipantIDAndGroupIDOrigin)
	}

	if !m.GetByParticipantIDAndGroupIDMock.invocationsDone() && afterGetByParticipantIDAndGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.GetByParticipantIDAndGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDAndGroupIDMock.expectedInvocations), m.GetByParticipantIDAndGroupIDMock.expectedInvocationsOrigin, afterGetByParticipantIDAndGroupIDCounter)
	}
}

type mParticipantGroupPrimeDBMockGetByParticipantIDs struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation
	expectations       []*ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation

	callArgs []*ParticipantGroupPrimeDBMockGetByParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation specifies expectation struct of the ParticipantGroupPrimeDB.GetByParticipantIDs
type ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockGetByParticipantIDsParams
	paramPtrs          *ParticipantGroupPrimeDBMockGetByParticipantIDsParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockGetByParticipantIDsExpectationOrigins
	results            *ParticipantGroupPrimeDBMockGetByParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDsParams contains parameters of the ParticipantGroupPrimeDB.GetByParticipantIDs
type ParticipantGroupPrimeDBMockGetByParticipantIDsParams struct {
	participantIDs []int64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDsParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.GetByParticipantIDs
type ParticipantGroupPrimeDBMockGetByParticipantIDsParamPtrs struct {
	participantIDs *[]int64
}

// ParticipantGroupPrimeDBMockGetByParticipantIDsResults contains results of the ParticipantGroupPrimeDB.GetByParticipantIDs
type ParticipantGroupPrimeDBMockGetByParticipantIDsResults struct {
	pa1 []participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockGetByParticipantIDsOrigins contains origins of expectations of the ParticipantGroupPrimeDB.GetByParticipantIDs
type ParticipantGroupPrimeDBMockGetByParticipantIDsExpectationOrigins struct {
	origin               string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Optional() *mParticipantGroupPrimeDBMockGetByParticipantIDs {
	mmGetByParticipantIDs.optional = true
	return mmGetByParticipantIDs
}

// Expect sets up expected params for ParticipantGroupPrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Expect(participantIDs []int64) *mParticipantGroupPrimeDBMockGetByParticipantIDs {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	if mmGetByParticipantIDs.defaultExpectation == nil {
		mmGetByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation{}
	}

	if mmGetByParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDs mock is already set by ExpectParams functions")
	}

	mmGetByParticipantIDs.defaultExpectation.params = &ParticipantGroupPrimeDBMockGetByParticipantIDsParams{participantIDs}
	mmGetByParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantIDs.expectations {
		if minimock.Equal(e.params, mmGetByParticipantIDs.defaultExpectation.params) {
			mmGetByParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantIDs.defaultExpectation.params)
		}
	}

	return mmGetByParticipantIDs
}

// ExpectParticipantIDsParam1 sets up expected param participantIDs for ParticipantGroupPrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) ExpectParticipantIDsParam1(participantIDs []int64) *mParticipantGroupPrimeDBMockGetByParticipantIDs {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	if mmGetByParticipantIDs.defaultExpectation == nil {
		mmGetByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation{}
	}

	if mmGetByParticipantIDs.defaultExpectation.params != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDs mock is already set by Expect")
	}

	if mmGetByParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDs.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockGetByParticipantIDsParamPtrs{}
	}
	mmGetByParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmGetByParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmGetByParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Inspect(f func(participantIDs []int64)) *mParticipantGroupPrimeDBMockGetByParticipantIDs {
	if mmGetByParticipantIDs.mock.inspectFuncGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.GetByParticipantIDs")
	}

	mmGetByParticipantIDs.mock.inspectFuncGetByParticipantIDs = f

	return mmGetByParticipantIDs
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.GetByParticipantIDs
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Return(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	if mmGetByParticipantIDs.defaultExpectation == nil {
		mmGetByParticipantIDs.defaultExpectation = &ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation{mock: mmGetByParticipantIDs.mock}
	}
	mmGetByParticipantIDs.defaultExpectation.results = &ParticipantGroupPrimeDBMockGetByParticipantIDsResults{pa1, err}
	mmGetByParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDs.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.GetByParticipantIDs method
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Set(f func(participantIDs []int64) (pa1 []participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmGetByParticipantIDs.defaultExpectation != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.GetByParticipantIDs method")
	}

	if len(mmGetByParticipantIDs.expectations) > 0 {
		mmGetByParticipantIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.GetByParticipantIDs method")
	}

	mmGetByParticipantIDs.mock.funcGetByParticipantIDs = f
	mmGetByParticipantIDs.mock.funcGetByParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDs.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.GetByParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) When(participantIDs []int64) *ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation {
	if mmGetByParticipantIDs.mock.funcGetByParticipantIDs != nil {
		mmGetByParticipantIDs.mock.t.Fatalf("ParticipantGroupPrimeDBMock.GetByParticipantIDs mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation{
		mock:               mmGetByParticipantIDs.mock,
		params:             &ParticipantGroupPrimeDBMockGetByParticipantIDsParams{participantIDs},
		expectationOrigins: ParticipantGroupPrimeDBMockGetByParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantIDs.expectations = append(mmGetByParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.GetByParticipantIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockGetByParticipantIDsExpectation) Then(pa1 []participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockGetByParticipantIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.GetByParticipantIDs should be invoked
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Times(n uint64) *mParticipantGroupPrimeDBMockGetByParticipantIDs {
	if n == 0 {
		mmGetByParticipantIDs.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.GetByParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantIDs.expectedInvocations, n)
	mmGetByParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDs
}

func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) invocationsDone() bool {
	if len(mmGetByParticipantIDs.expectations) == 0 && mmGetByParticipantIDs.defaultExpectation == nil && mmGetByParticipantIDs.mock.funcGetByParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDs.mock.afterGetByParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantIDs implements mm_repository.ParticipantGroupPrimeDB
func (mmGetByParticipantIDs *ParticipantGroupPrimeDBMock) GetByParticipantIDs(participantIDs []int64) (pa1 []participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantIDs.beforeGetByParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantIDs.afterGetByParticipantIDsCounter, 1)

	mmGetByParticipantIDs.t.Helper()

	if mmGetByParticipantIDs.inspectFuncGetByParticipantIDs != nil {
		mmGetByParticipantIDs.inspectFuncGetByParticipantIDs(participantIDs)
	}

	mm_params := ParticipantGroupPrimeDBMockGetByParticipantIDsParams{participantIDs}

	// Record call args
	mmGetByParticipantIDs.GetByParticipantIDsMock.mutex.Lock()
	mmGetByParticipantIDs.GetByParticipantIDsMock.callArgs = append(mmGetByParticipantIDs.GetByParticipantIDsMock.callArgs, &mm_params)
	mmGetByParticipantIDs.GetByParticipantIDsMock.mutex.Unlock()

	for _, e := range mmGetByParticipantIDs.GetByParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockGetByParticipantIDsParams{participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmGetByParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantIDs.t.Errorf("ParticipantGroupPrimeDBMock.GetByParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantIDs.GetByParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantIDs.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.GetByParticipantIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByParticipantIDs.funcGetByParticipantIDs != nil {
		return mmGetByParticipantIDs.funcGetByParticipantIDs(participantIDs)
	}
	mmGetByParticipantIDs.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.GetByParticipantIDs. %v", participantIDs)
	return
}

// GetByParticipantIDsAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.GetByParticipantIDs invocations
func (mmGetByParticipantIDs *ParticipantGroupPrimeDBMock) GetByParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDs.afterGetByParticipantIDsCounter)
}

// GetByParticipantIDsBeforeCounter returns a count of ParticipantGroupPrimeDBMock.GetByParticipantIDs invocations
func (mmGetByParticipantIDs *ParticipantGroupPrimeDBMock) GetByParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDs.beforeGetByParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.GetByParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantIDs *mParticipantGroupPrimeDBMockGetByParticipantIDs) Calls() []*ParticipantGroupPrimeDBMockGetByParticipantIDsParams {
	mmGetByParticipantIDs.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockGetByParticipantIDsParams, len(mmGetByParticipantIDs.callArgs))
	copy(argCopy, mmGetByParticipantIDs.callArgs)

	mmGetByParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDsDone returns true if the count of the GetByParticipantIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockGetByParticipantIDsDone() bool {
	if m.GetByParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDsMock.invocationsDone()
}

// MinimockGetByParticipantIDsInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockGetByParticipantIDsInspect() {
	for _, e := range m.GetByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDsMock.defaultExpectation != nil && afterGetByParticipantIDsCounter < 1 {
		if m.GetByParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDs at\n%s", m.GetByParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDs at\n%s with params: %#v", m.GetByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantIDs != nil && afterGetByParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.GetByParticipantIDs at\n%s", m.funcGetByParticipantIDsOrigin)
	}

	if !m.GetByParticipantIDsMock.invocationsDone() && afterGetByParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.GetByParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDsMock.expectedInvocations), m.GetByParticipantIDsMock.expectedInvocationsOrigin, afterGetByParticipantIDsCounter)
	}
}

type mParticipantGroupPrimeDBMockUpdate struct {
	optional           bool
	mock               *ParticipantGroupPrimeDBMock
	defaultExpectation *ParticipantGroupPrimeDBMockUpdateExpectation
	expectations       []*ParticipantGroupPrimeDBMockUpdateExpectation

	callArgs []*ParticipantGroupPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantGroupPrimeDBMockUpdateExpectation specifies expectation struct of the ParticipantGroupPrimeDB.Update
type ParticipantGroupPrimeDBMockUpdateExpectation struct {
	mock               *ParticipantGroupPrimeDBMock
	params             *ParticipantGroupPrimeDBMockUpdateParams
	paramPtrs          *ParticipantGroupPrimeDBMockUpdateParamPtrs
	expectationOrigins ParticipantGroupPrimeDBMockUpdateExpectationOrigins
	results            *ParticipantGroupPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantGroupPrimeDBMockUpdateParams contains parameters of the ParticipantGroupPrimeDB.Update
type ParticipantGroupPrimeDBMockUpdateParams struct {
	ctx           context.Context
	id            int64
	participantID *int64
	groupID       *int64
}

// ParticipantGroupPrimeDBMockUpdateParamPtrs contains pointers to parameters of the ParticipantGroupPrimeDB.Update
type ParticipantGroupPrimeDBMockUpdateParamPtrs struct {
	ctx           *context.Context
	id            *int64
	participantID **int64
	groupID       **int64
}

// ParticipantGroupPrimeDBMockUpdateResults contains results of the ParticipantGroupPrimeDB.Update
type ParticipantGroupPrimeDBMockUpdateResults struct {
	p1  participantentity.ParticipantGroup
	err error
}

// ParticipantGroupPrimeDBMockUpdateOrigins contains origins of expectations of the ParticipantGroupPrimeDB.Update
type ParticipantGroupPrimeDBMockUpdateExpectationOrigins struct {
	origin              string
	originCtx           string
	originId            string
	originParticipantID string
	originGroupID       string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Optional() *mParticipantGroupPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Expect(ctx context.Context, id int64, participantID *int64, groupID *int64) *mParticipantGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ParticipantGroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &ParticipantGroupPrimeDBMockUpdateParams{ctx, id, participantID, groupID}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) ExpectCtxParam1(ctx context.Context) *mParticipantGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ParticipantGroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdate
}

// ExpectIdParam2 sets up expected param id for ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) ExpectIdParam2(id int64) *mParticipantGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ParticipantGroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.id = &id
	mmUpdate.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmUpdate
}

// ExpectParticipantIDParam3 sets up expected param participantID for ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) ExpectParticipantIDParam3(participantID *int64) *mParticipantGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ParticipantGroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.participantID = &participantID
	mmUpdate.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmUpdate
}

// ExpectGroupIDParam4 sets up expected param groupID for ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) ExpectGroupIDParam4(groupID *int64) *mParticipantGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ParticipantGroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ParticipantGroupPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.groupID = &groupID
	mmUpdate.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Inspect(f func(ctx context.Context, id int64, participantID *int64, groupID *int64)) *mParticipantGroupPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for ParticipantGroupPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by ParticipantGroupPrimeDB.Update
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Return(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ParticipantGroupPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &ParticipantGroupPrimeDBMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the ParticipantGroupPrimeDB.Update method
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Set(f func(ctx context.Context, id int64, participantID *int64, groupID *int64) (p1 participantentity.ParticipantGroup, err error)) *ParticipantGroupPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the ParticipantGroupPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the ParticipantGroupPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the ParticipantGroupPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) When(ctx context.Context, id int64, participantID *int64, groupID *int64) *ParticipantGroupPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ParticipantGroupPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &ParticipantGroupPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &ParticipantGroupPrimeDBMockUpdateParams{ctx, id, participantID, groupID},
		expectationOrigins: ParticipantGroupPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up ParticipantGroupPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *ParticipantGroupPrimeDBMockUpdateExpectation) Then(p1 participantentity.ParticipantGroup, err error) *ParticipantGroupPrimeDBMock {
	e.results = &ParticipantGroupPrimeDBMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantGroupPrimeDB.Update should be invoked
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Times(n uint64) *mParticipantGroupPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of ParticipantGroupPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.ParticipantGroupPrimeDB
func (mmUpdate *ParticipantGroupPrimeDBMock) Update(ctx context.Context, id int64, participantID *int64, groupID *int64) (p1 participantentity.ParticipantGroup, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(ctx, id, participantID, groupID)
	}

	mm_params := ParticipantGroupPrimeDBMockUpdateParams{ctx, id, participantID, groupID}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := ParticipantGroupPrimeDBMockUpdateParams{ctx, id, participantID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdate.t.Errorf("ParticipantGroupPrimeDBMock.Update got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmUpdate.t.Errorf("ParticipantGroupPrimeDBMock.Update got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmUpdate.t.Errorf("ParticipantGroupPrimeDBMock.Update got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmUpdate.t.Errorf("ParticipantGroupPrimeDBMock.Update got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("ParticipantGroupPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the ParticipantGroupPrimeDBMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(ctx, id, participantID, groupID)
	}
	mmUpdate.t.Fatalf("Unexpected call to ParticipantGroupPrimeDBMock.Update. %v %v %v %v", ctx, id, participantID, groupID)
	return
}

// UpdateAfterCounter returns a count of finished ParticipantGroupPrimeDBMock.Update invocations
func (mmUpdate *ParticipantGroupPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of ParticipantGroupPrimeDBMock.Update invocations
func (mmUpdate *ParticipantGroupPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to ParticipantGroupPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mParticipantGroupPrimeDBMockUpdate) Calls() []*ParticipantGroupPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*ParticipantGroupPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *ParticipantGroupPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *ParticipantGroupPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to ParticipantGroupPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantGroupPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ParticipantGroupPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByRoleIDAndParticipantIDsInspect()

			m.MinimockDeleteByGroupIDInspect()

			m.MinimockDeleteByParticipantAndGroupIDInspect()

			m.MinimockDeleteByParticipantIDInspect()

			m.MinimockDeleteByParticipantIDsInspect()

			m.MinimockDeleteByRoleIDAndParticipantIDsInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByGroupIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByParticipantIDInspect()

			m.MinimockGetByParticipantIDAndGroupIDInspect()

			m.MinimockGetByParticipantIDsInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ParticipantGroupPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ParticipantGroupPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByRoleIDAndParticipantIDsDone() &&
		m.MinimockDeleteByGroupIDDone() &&
		m.MinimockDeleteByParticipantAndGroupIDDone() &&
		m.MinimockDeleteByParticipantIDDone() &&
		m.MinimockDeleteByParticipantIDsDone() &&
		m.MinimockDeleteByRoleIDAndParticipantIDsDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByGroupIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByParticipantIDDone() &&
		m.MinimockGetByParticipantIDAndGroupIDDone() &&
		m.MinimockGetByParticipantIDsDone() &&
		m.MinimockUpdateDone()
}
