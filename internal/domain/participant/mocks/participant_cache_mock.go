// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository.ParticipantCache -o participant_cache_mock.go -n ParticipantCacheMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	"github.com/gojuno/minimock/v3"
)

// ParticipantCacheMock implements mm_repository.ParticipantCache
type ParticipantCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcDelete          func(participantID int64, productID int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(participantID int64, productID int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mParticipantCacheMockDelete

	funcDeleteByUserIDAndProductIDs          func(userID int64, productIDs []int64) (err error)
	funcDeleteByUserIDAndProductIDsOrigin    string
	inspectFuncDeleteByUserIDAndProductIDs   func(userID int64, productIDs []int64)
	afterDeleteByUserIDAndProductIDsCounter  uint64
	beforeDeleteByUserIDAndProductIDsCounter uint64
	DeleteByUserIDAndProductIDsMock          mParticipantCacheMockDeleteByUserIDAndProductIDs

	funcExistByParticipantIDAndProductID          func(participantID int64, productID int64) (b1 bool, err error)
	funcExistByParticipantIDAndProductIDOrigin    string
	inspectFuncExistByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterExistByParticipantIDAndProductIDCounter  uint64
	beforeExistByParticipantIDAndProductIDCounter uint64
	ExistByParticipantIDAndProductIDMock          mParticipantCacheMockExistByParticipantIDAndProductID

	funcExistByUserIDAndProductID          func(userID int64, productID int64) (b1 bool, err error)
	funcExistByUserIDAndProductIDOrigin    string
	inspectFuncExistByUserIDAndProductID   func(userID int64, productID int64)
	afterExistByUserIDAndProductIDCounter  uint64
	beforeExistByUserIDAndProductIDCounter uint64
	ExistByUserIDAndProductIDMock          mParticipantCacheMockExistByUserIDAndProductID

	funcGetByParticipantIDAndProductID          func(participantID int64, productID int64) (p1 participantentity.Participant, err error)
	funcGetByParticipantIDAndProductIDOrigin    string
	inspectFuncGetByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterGetByParticipantIDAndProductIDCounter  uint64
	beforeGetByParticipantIDAndProductIDCounter uint64
	GetByParticipantIDAndProductIDMock          mParticipantCacheMockGetByParticipantIDAndProductID

	funcGetByProductID          func(productID int64) (pa1 []participantentity.Participant, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mParticipantCacheMockGetByProductID

	funcGetByUserID          func(userID int64) (pa1 []participantentity.Participant, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mParticipantCacheMockGetByUserID

	funcGetByUserIDAndProductID          func(userID int64, productID int64) (p1 participantentity.Participant, err error)
	funcGetByUserIDAndProductIDOrigin    string
	inspectFuncGetByUserIDAndProductID   func(userID int64, productID int64)
	afterGetByUserIDAndProductIDCounter  uint64
	beforeGetByUserIDAndProductIDCounter uint64
	GetByUserIDAndProductIDMock          mParticipantCacheMockGetByUserIDAndProductID

	funcGetByUserIDAndProductIDs          func(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error)
	funcGetByUserIDAndProductIDsOrigin    string
	inspectFuncGetByUserIDAndProductIDs   func(userID int64, productIDs []int64)
	afterGetByUserIDAndProductIDsCounter  uint64
	beforeGetByUserIDAndProductIDsCounter uint64
	GetByUserIDAndProductIDsMock          mParticipantCacheMockGetByUserIDAndProductIDs

	funcSet          func(participant participantentity.Participant) (err error)
	funcSetOrigin    string
	inspectFuncSet   func(participant participantentity.Participant)
	afterSetCounter  uint64
	beforeSetCounter uint64
	SetMock          mParticipantCacheMockSet
}

// NewParticipantCacheMock returns a mock for mm_repository.ParticipantCache
func NewParticipantCacheMock(t minimock.Tester) *ParticipantCacheMock {
	m := &ParticipantCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.DeleteMock = mParticipantCacheMockDelete{mock: m}
	m.DeleteMock.callArgs = []*ParticipantCacheMockDeleteParams{}

	m.DeleteByUserIDAndProductIDsMock = mParticipantCacheMockDeleteByUserIDAndProductIDs{mock: m}
	m.DeleteByUserIDAndProductIDsMock.callArgs = []*ParticipantCacheMockDeleteByUserIDAndProductIDsParams{}

	m.ExistByParticipantIDAndProductIDMock = mParticipantCacheMockExistByParticipantIDAndProductID{mock: m}
	m.ExistByParticipantIDAndProductIDMock.callArgs = []*ParticipantCacheMockExistByParticipantIDAndProductIDParams{}

	m.ExistByUserIDAndProductIDMock = mParticipantCacheMockExistByUserIDAndProductID{mock: m}
	m.ExistByUserIDAndProductIDMock.callArgs = []*ParticipantCacheMockExistByUserIDAndProductIDParams{}

	m.GetByParticipantIDAndProductIDMock = mParticipantCacheMockGetByParticipantIDAndProductID{mock: m}
	m.GetByParticipantIDAndProductIDMock.callArgs = []*ParticipantCacheMockGetByParticipantIDAndProductIDParams{}

	m.GetByProductIDMock = mParticipantCacheMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*ParticipantCacheMockGetByProductIDParams{}

	m.GetByUserIDMock = mParticipantCacheMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*ParticipantCacheMockGetByUserIDParams{}

	m.GetByUserIDAndProductIDMock = mParticipantCacheMockGetByUserIDAndProductID{mock: m}
	m.GetByUserIDAndProductIDMock.callArgs = []*ParticipantCacheMockGetByUserIDAndProductIDParams{}

	m.GetByUserIDAndProductIDsMock = mParticipantCacheMockGetByUserIDAndProductIDs{mock: m}
	m.GetByUserIDAndProductIDsMock.callArgs = []*ParticipantCacheMockGetByUserIDAndProductIDsParams{}

	m.SetMock = mParticipantCacheMockSet{mock: m}
	m.SetMock.callArgs = []*ParticipantCacheMockSetParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mParticipantCacheMockDelete struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockDeleteExpectation
	expectations       []*ParticipantCacheMockDeleteExpectation

	callArgs []*ParticipantCacheMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockDeleteExpectation specifies expectation struct of the ParticipantCache.Delete
type ParticipantCacheMockDeleteExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockDeleteParams
	paramPtrs          *ParticipantCacheMockDeleteParamPtrs
	expectationOrigins ParticipantCacheMockDeleteExpectationOrigins
	results            *ParticipantCacheMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockDeleteParams contains parameters of the ParticipantCache.Delete
type ParticipantCacheMockDeleteParams struct {
	participantID int64
	productID     int64
}

// ParticipantCacheMockDeleteParamPtrs contains pointers to parameters of the ParticipantCache.Delete
type ParticipantCacheMockDeleteParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantCacheMockDeleteResults contains results of the ParticipantCache.Delete
type ParticipantCacheMockDeleteResults struct {
	err error
}

// ParticipantCacheMockDeleteOrigins contains origins of expectations of the ParticipantCache.Delete
type ParticipantCacheMockDeleteExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mParticipantCacheMockDelete) Optional() *mParticipantCacheMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for ParticipantCache.Delete
func (mmDelete *mParticipantCacheMockDelete) Expect(participantID int64, productID int64) *mParticipantCacheMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantCacheMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &ParticipantCacheMockDeleteParams{participantID, productID}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantCache.Delete
func (mmDelete *mParticipantCacheMockDelete) ExpectParticipantIDParam1(participantID int64) *mParticipantCacheMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantCacheMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantCacheMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.participantID = &participantID
	mmDelete.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantCache.Delete
func (mmDelete *mParticipantCacheMockDelete) ExpectProductIDParam2(productID int64) *mParticipantCacheMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantCacheMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantCacheMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.productID = &productID
	mmDelete.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.Delete
func (mmDelete *mParticipantCacheMockDelete) Inspect(f func(participantID int64, productID int64)) *mParticipantCacheMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by ParticipantCache.Delete
func (mmDelete *mParticipantCacheMockDelete) Return(err error) *ParticipantCacheMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantCacheMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &ParticipantCacheMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the ParticipantCache.Delete method
func (mmDelete *mParticipantCacheMockDelete) Set(f func(participantID int64, productID int64) (err error)) *ParticipantCacheMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the ParticipantCache.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mParticipantCacheMockDelete) When(participantID int64, productID int64) *ParticipantCacheMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantCacheMock.Delete mock is already set by Set")
	}

	expectation := &ParticipantCacheMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &ParticipantCacheMockDeleteParams{participantID, productID},
		expectationOrigins: ParticipantCacheMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.Delete return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockDeleteExpectation) Then(err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockDeleteResults{err}
	return e.mock
}

// Times sets number of times ParticipantCache.Delete should be invoked
func (mmDelete *mParticipantCacheMockDelete) Times(n uint64) *mParticipantCacheMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of ParticipantCacheMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mParticipantCacheMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_repository.ParticipantCache
func (mmDelete *ParticipantCacheMock) Delete(participantID int64, productID int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(participantID, productID)
	}

	mm_params := ParticipantCacheMockDeleteParams{participantID, productID}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockDeleteParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDelete.t.Errorf("ParticipantCacheMock.Delete got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmDelete.t.Errorf("ParticipantCacheMock.Delete got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("ParticipantCacheMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the ParticipantCacheMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(participantID, productID)
	}
	mmDelete.t.Fatalf("Unexpected call to ParticipantCacheMock.Delete. %v %v", participantID, productID)
	return
}

// DeleteAfterCounter returns a count of finished ParticipantCacheMock.Delete invocations
func (mmDelete *ParticipantCacheMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of ParticipantCacheMock.Delete invocations
func (mmDelete *ParticipantCacheMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mParticipantCacheMockDelete) Calls() []*ParticipantCacheMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mParticipantCacheMockDeleteByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation
	expectations       []*ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation

	callArgs []*ParticipantCacheMockDeleteByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantCache.DeleteByUserIDAndProductIDs
type ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockDeleteByUserIDAndProductIDsParams
	paramPtrs          *ParticipantCacheMockDeleteByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantCacheMockDeleteByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantCacheMockDeleteByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockDeleteByUserIDAndProductIDsParams contains parameters of the ParticipantCache.DeleteByUserIDAndProductIDs
type ParticipantCacheMockDeleteByUserIDAndProductIDsParams struct {
	userID     int64
	productIDs []int64
}

// ParticipantCacheMockDeleteByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantCache.DeleteByUserIDAndProductIDs
type ParticipantCacheMockDeleteByUserIDAndProductIDsParamPtrs struct {
	userID     *int64
	productIDs *[]int64
}

// ParticipantCacheMockDeleteByUserIDAndProductIDsResults contains results of the ParticipantCache.DeleteByUserIDAndProductIDs
type ParticipantCacheMockDeleteByUserIDAndProductIDsResults struct {
	err error
}

// ParticipantCacheMockDeleteByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantCache.DeleteByUserIDAndProductIDs
type ParticipantCacheMockDeleteByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Optional() *mParticipantCacheMockDeleteByUserIDAndProductIDs {
	mmDeleteByUserIDAndProductIDs.optional = true
	return mmDeleteByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantCache.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Expect(userID int64, productIDs []int64) *mParticipantCacheMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndProductIDs.defaultExpectation.params = &ParticipantCacheMockDeleteByUserIDAndProductIDsParams{userID, productIDs}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndProductIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndProductIDs
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantCache.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) ExpectUserIDParam1(userID int64) *mParticipantCacheMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantCacheMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// ExpectProductIDsParam2 sets up expected param productIDs for ParticipantCache.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) ExpectProductIDsParam2(productIDs []int64) *mParticipantCacheMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantCacheMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Inspect(f func(userID int64, productIDs []int64)) *mParticipantCacheMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.inspectFuncDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.DeleteByUserIDAndProductIDs")
	}

	mmDeleteByUserIDAndProductIDs.mock.inspectFuncDeleteByUserIDAndProductIDs = f

	return mmDeleteByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantCache.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Return(err error) *ParticipantCacheMock {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation{mock: mmDeleteByUserIDAndProductIDs.mock}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.results = &ParticipantCacheMockDeleteByUserIDAndProductIDsResults{err}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantCache.DeleteByUserIDAndProductIDs method
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Set(f func(userID int64, productIDs []int64) (err error)) *ParticipantCacheMock {
	if mmDeleteByUserIDAndProductIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.DeleteByUserIDAndProductIDs method")
	}

	if len(mmDeleteByUserIDAndProductIDs.expectations) > 0 {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.DeleteByUserIDAndProductIDs method")
	}

	mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs = f
	mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantCache.DeleteByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) When(userID int64, productIDs []int64) *ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation{
		mock:               mmDeleteByUserIDAndProductIDs.mock,
		params:             &ParticipantCacheMockDeleteByUserIDAndProductIDsParams{userID, productIDs},
		expectationOrigins: ParticipantCacheMockDeleteByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndProductIDs.expectations = append(mmDeleteByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.DeleteByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockDeleteByUserIDAndProductIDsExpectation) Then(err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockDeleteByUserIDAndProductIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantCache.DeleteByUserIDAndProductIDs should be invoked
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Times(n uint64) *mParticipantCacheMockDeleteByUserIDAndProductIDs {
	if n == 0 {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantCacheMock.DeleteByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndProductIDs.expectedInvocations, n)
	mmDeleteByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs
}

func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndProductIDs.expectations) == 0 && mmDeleteByUserIDAndProductIDs.defaultExpectation == nil && mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.mock.afterDeleteByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndProductIDs implements mm_repository.ParticipantCache
func (mmDeleteByUserIDAndProductIDs *ParticipantCacheMock) DeleteByUserIDAndProductIDs(userID int64, productIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.beforeDeleteByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.afterDeleteByUserIDAndProductIDsCounter, 1)

	mmDeleteByUserIDAndProductIDs.t.Helper()

	if mmDeleteByUserIDAndProductIDs.inspectFuncDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.inspectFuncDeleteByUserIDAndProductIDs(userID, productIDs)
	}

	mm_params := ParticipantCacheMockDeleteByUserIDAndProductIDsParams{userID, productIDs}

	// Record call args
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.mutex.Lock()
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.callArgs = append(mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockDeleteByUserIDAndProductIDsParams{userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantCacheMock.DeleteByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantCacheMock.DeleteByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantCacheMock.DeleteByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantCacheMock.DeleteByUserIDAndProductIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndProductIDs.funcDeleteByUserIDAndProductIDs != nil {
		return mmDeleteByUserIDAndProductIDs.funcDeleteByUserIDAndProductIDs(userID, productIDs)
	}
	mmDeleteByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantCacheMock.DeleteByUserIDAndProductIDs. %v %v", userID, productIDs)
	return
}

// DeleteByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantCacheMock.DeleteByUserIDAndProductIDs invocations
func (mmDeleteByUserIDAndProductIDs *ParticipantCacheMock) DeleteByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.afterDeleteByUserIDAndProductIDsCounter)
}

// DeleteByUserIDAndProductIDsBeforeCounter returns a count of ParticipantCacheMock.DeleteByUserIDAndProductIDs invocations
func (mmDeleteByUserIDAndProductIDs *ParticipantCacheMock) DeleteByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.beforeDeleteByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.DeleteByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndProductIDs *mParticipantCacheMockDeleteByUserIDAndProductIDs) Calls() []*ParticipantCacheMockDeleteByUserIDAndProductIDsParams {
	mmDeleteByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockDeleteByUserIDAndProductIDsParams, len(mmDeleteByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndProductIDs.callArgs)

	mmDeleteByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndProductIDsDone returns true if the count of the DeleteByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockDeleteByUserIDAndProductIDsDone() bool {
	if m.DeleteByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockDeleteByUserIDAndProductIDsInspect() {
	for _, e := range m.DeleteByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.DeleteByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndProductIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndProductIDsCounter < 1 {
		if m.DeleteByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.DeleteByUserIDAndProductIDs at\n%s", m.DeleteByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.DeleteByUserIDAndProductIDs at\n%s with params: %#v", m.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndProductIDs != nil && afterDeleteByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.DeleteByUserIDAndProductIDs at\n%s", m.funcDeleteByUserIDAndProductIDsOrigin)
	}

	if !m.DeleteByUserIDAndProductIDsMock.invocationsDone() && afterDeleteByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.DeleteByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndProductIDsMock.expectedInvocations), m.DeleteByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndProductIDsCounter)
	}
}

type mParticipantCacheMockExistByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockExistByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantCacheMockExistByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantCacheMockExistByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockExistByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantCache.ExistByParticipantIDAndProductID
type ParticipantCacheMockExistByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockExistByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantCacheMockExistByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantCacheMockExistByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantCacheMockExistByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockExistByParticipantIDAndProductIDParams contains parameters of the ParticipantCache.ExistByParticipantIDAndProductID
type ParticipantCacheMockExistByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantCacheMockExistByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantCache.ExistByParticipantIDAndProductID
type ParticipantCacheMockExistByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantCacheMockExistByParticipantIDAndProductIDResults contains results of the ParticipantCache.ExistByParticipantIDAndProductID
type ParticipantCacheMockExistByParticipantIDAndProductIDResults struct {
	b1  bool
	err error
}

// ParticipantCacheMockExistByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantCache.ExistByParticipantIDAndProductID
type ParticipantCacheMockExistByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Optional() *mParticipantCacheMockExistByParticipantIDAndProductID {
	mmExistByParticipantIDAndProductID.optional = true
	return mmExistByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantCache.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantCacheMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmExistByParticipantIDAndProductID.defaultExpectation.params = &ParticipantCacheMockExistByParticipantIDAndProductIDParams{participantID, productID}
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmExistByParticipantIDAndProductID.defaultExpectation.params) {
			mmExistByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmExistByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantCache.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantCacheMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockExistByParticipantIDAndProductIDParamPtrs{}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmExistByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantCache.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantCacheMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockExistByParticipantIDAndProductIDParamPtrs{}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmExistByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantCacheMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.inspectFuncExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.ExistByParticipantIDAndProductID")
	}

	mmExistByParticipantIDAndProductID.mock.inspectFuncExistByParticipantIDAndProductID = f

	return mmExistByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantCache.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Return(b1 bool, err error) *ParticipantCacheMock {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByParticipantIDAndProductIDExpectation{mock: mmExistByParticipantIDAndProductID.mock}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.results = &ParticipantCacheMockExistByParticipantIDAndProductIDResults{b1, err}
	mmExistByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantCache.ExistByParticipantIDAndProductID method
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (b1 bool, err error)) *ParticipantCacheMock {
	if mmExistByParticipantIDAndProductID.defaultExpectation != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.ExistByParticipantIDAndProductID method")
	}

	if len(mmExistByParticipantIDAndProductID.expectations) > 0 {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.ExistByParticipantIDAndProductID method")
	}

	mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID = f
	mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantCache.ExistByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantCacheMockExistByParticipantIDAndProductIDExpectation {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantCacheMockExistByParticipantIDAndProductIDExpectation{
		mock:               mmExistByParticipantIDAndProductID.mock,
		params:             &ParticipantCacheMockExistByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantCacheMockExistByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByParticipantIDAndProductID.expectations = append(mmExistByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.ExistByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockExistByParticipantIDAndProductIDExpectation) Then(b1 bool, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockExistByParticipantIDAndProductIDResults{b1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.ExistByParticipantIDAndProductID should be invoked
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Times(n uint64) *mParticipantCacheMockExistByParticipantIDAndProductID {
	if n == 0 {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantCacheMock.ExistByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByParticipantIDAndProductID.expectedInvocations, n)
	mmExistByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID
}

func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmExistByParticipantIDAndProductID.expectations) == 0 && mmExistByParticipantIDAndProductID.defaultExpectation == nil && mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.mock.afterExistByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByParticipantIDAndProductID implements mm_repository.ParticipantCache
func (mmExistByParticipantIDAndProductID *ParticipantCacheMock) ExistByParticipantIDAndProductID(participantID int64, productID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.beforeExistByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.afterExistByParticipantIDAndProductIDCounter, 1)

	mmExistByParticipantIDAndProductID.t.Helper()

	if mmExistByParticipantIDAndProductID.inspectFuncExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.inspectFuncExistByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantCacheMockExistByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.mutex.Lock()
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.callArgs = append(mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockExistByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmExistByParticipantIDAndProductID.t.Errorf("ParticipantCacheMock.ExistByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmExistByParticipantIDAndProductID.t.Errorf("ParticipantCacheMock.ExistByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByParticipantIDAndProductID.t.Errorf("ParticipantCacheMock.ExistByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantCacheMock.ExistByParticipantIDAndProductID")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByParticipantIDAndProductID.funcExistByParticipantIDAndProductID != nil {
		return mmExistByParticipantIDAndProductID.funcExistByParticipantIDAndProductID(participantID, productID)
	}
	mmExistByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantCacheMock.ExistByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// ExistByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantCacheMock.ExistByParticipantIDAndProductID invocations
func (mmExistByParticipantIDAndProductID *ParticipantCacheMock) ExistByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.afterExistByParticipantIDAndProductIDCounter)
}

// ExistByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantCacheMock.ExistByParticipantIDAndProductID invocations
func (mmExistByParticipantIDAndProductID *ParticipantCacheMock) ExistByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.beforeExistByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.ExistByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByParticipantIDAndProductID *mParticipantCacheMockExistByParticipantIDAndProductID) Calls() []*ParticipantCacheMockExistByParticipantIDAndProductIDParams {
	mmExistByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockExistByParticipantIDAndProductIDParams, len(mmExistByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmExistByParticipantIDAndProductID.callArgs)

	mmExistByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockExistByParticipantIDAndProductIDDone returns true if the count of the ExistByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockExistByParticipantIDAndProductIDDone() bool {
	if m.ExistByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockExistByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockExistByParticipantIDAndProductIDInspect() {
	for _, e := range m.ExistByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.ExistByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterExistByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByParticipantIDAndProductIDMock.defaultExpectation != nil && afterExistByParticipantIDAndProductIDCounter < 1 {
		if m.ExistByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.ExistByParticipantIDAndProductID at\n%s", m.ExistByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.ExistByParticipantIDAndProductID at\n%s with params: %#v", m.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.ExistByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByParticipantIDAndProductID != nil && afterExistByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.ExistByParticipantIDAndProductID at\n%s", m.funcExistByParticipantIDAndProductIDOrigin)
	}

	if !m.ExistByParticipantIDAndProductIDMock.invocationsDone() && afterExistByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.ExistByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByParticipantIDAndProductIDMock.expectedInvocations), m.ExistByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterExistByParticipantIDAndProductIDCounter)
	}
}

type mParticipantCacheMockExistByUserIDAndProductID struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockExistByUserIDAndProductIDExpectation
	expectations       []*ParticipantCacheMockExistByUserIDAndProductIDExpectation

	callArgs []*ParticipantCacheMockExistByUserIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockExistByUserIDAndProductIDExpectation specifies expectation struct of the ParticipantCache.ExistByUserIDAndProductID
type ParticipantCacheMockExistByUserIDAndProductIDExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockExistByUserIDAndProductIDParams
	paramPtrs          *ParticipantCacheMockExistByUserIDAndProductIDParamPtrs
	expectationOrigins ParticipantCacheMockExistByUserIDAndProductIDExpectationOrigins
	results            *ParticipantCacheMockExistByUserIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockExistByUserIDAndProductIDParams contains parameters of the ParticipantCache.ExistByUserIDAndProductID
type ParticipantCacheMockExistByUserIDAndProductIDParams struct {
	userID    int64
	productID int64
}

// ParticipantCacheMockExistByUserIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantCache.ExistByUserIDAndProductID
type ParticipantCacheMockExistByUserIDAndProductIDParamPtrs struct {
	userID    *int64
	productID *int64
}

// ParticipantCacheMockExistByUserIDAndProductIDResults contains results of the ParticipantCache.ExistByUserIDAndProductID
type ParticipantCacheMockExistByUserIDAndProductIDResults struct {
	b1  bool
	err error
}

// ParticipantCacheMockExistByUserIDAndProductIDOrigins contains origins of expectations of the ParticipantCache.ExistByUserIDAndProductID
type ParticipantCacheMockExistByUserIDAndProductIDExpectationOrigins struct {
	origin          string
	originUserID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Optional() *mParticipantCacheMockExistByUserIDAndProductID {
	mmExistByUserIDAndProductID.optional = true
	return mmExistByUserIDAndProductID
}

// Expect sets up expected params for ParticipantCache.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Expect(userID int64, productID int64) *mParticipantCacheMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByUserIDAndProductIDExpectation{}
	}

	if mmExistByUserIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by ExpectParams functions")
	}

	mmExistByUserIDAndProductID.defaultExpectation.params = &ParticipantCacheMockExistByUserIDAndProductIDParams{userID, productID}
	mmExistByUserIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByUserIDAndProductID.expectations {
		if minimock.Equal(e.params, mmExistByUserIDAndProductID.defaultExpectation.params) {
			mmExistByUserIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByUserIDAndProductID.defaultExpectation.params)
		}
	}

	return mmExistByUserIDAndProductID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantCache.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) ExpectUserIDParam1(userID int64) *mParticipantCacheMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByUserIDAndProductIDExpectation{}
	}

	if mmExistByUserIDAndProductID.defaultExpectation.params != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Expect")
	}

	if mmExistByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockExistByUserIDAndProductIDParamPtrs{}
	}
	mmExistByUserIDAndProductID.defaultExpectation.paramPtrs.userID = &userID
	mmExistByUserIDAndProductID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmExistByUserIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantCache.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantCacheMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByUserIDAndProductIDExpectation{}
	}

	if mmExistByUserIDAndProductID.defaultExpectation.params != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Expect")
	}

	if mmExistByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockExistByUserIDAndProductIDParamPtrs{}
	}
	mmExistByUserIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmExistByUserIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmExistByUserIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Inspect(f func(userID int64, productID int64)) *mParticipantCacheMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.inspectFuncExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.ExistByUserIDAndProductID")
	}

	mmExistByUserIDAndProductID.mock.inspectFuncExistByUserIDAndProductID = f

	return mmExistByUserIDAndProductID
}

// Return sets up results that will be returned by ParticipantCache.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Return(b1 bool, err error) *ParticipantCacheMock {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockExistByUserIDAndProductIDExpectation{mock: mmExistByUserIDAndProductID.mock}
	}
	mmExistByUserIDAndProductID.defaultExpectation.results = &ParticipantCacheMockExistByUserIDAndProductIDResults{b1, err}
	mmExistByUserIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByUserIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantCache.ExistByUserIDAndProductID method
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Set(f func(userID int64, productID int64) (b1 bool, err error)) *ParticipantCacheMock {
	if mmExistByUserIDAndProductID.defaultExpectation != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.ExistByUserIDAndProductID method")
	}

	if len(mmExistByUserIDAndProductID.expectations) > 0 {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.ExistByUserIDAndProductID method")
	}

	mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID = f
	mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmExistByUserIDAndProductID.mock
}

// When sets expectation for the ParticipantCache.ExistByUserIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) When(userID int64, productID int64) *ParticipantCacheMockExistByUserIDAndProductIDExpectation {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantCacheMockExistByUserIDAndProductIDExpectation{
		mock:               mmExistByUserIDAndProductID.mock,
		params:             &ParticipantCacheMockExistByUserIDAndProductIDParams{userID, productID},
		expectationOrigins: ParticipantCacheMockExistByUserIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByUserIDAndProductID.expectations = append(mmExistByUserIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.ExistByUserIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockExistByUserIDAndProductIDExpectation) Then(b1 bool, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockExistByUserIDAndProductIDResults{b1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.ExistByUserIDAndProductID should be invoked
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Times(n uint64) *mParticipantCacheMockExistByUserIDAndProductID {
	if n == 0 {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Times of ParticipantCacheMock.ExistByUserIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByUserIDAndProductID.expectedInvocations, n)
	mmExistByUserIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByUserIDAndProductID
}

func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) invocationsDone() bool {
	if len(mmExistByUserIDAndProductID.expectations) == 0 && mmExistByUserIDAndProductID.defaultExpectation == nil && mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.mock.afterExistByUserIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByUserIDAndProductID implements mm_repository.ParticipantCache
func (mmExistByUserIDAndProductID *ParticipantCacheMock) ExistByUserIDAndProductID(userID int64, productID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByUserIDAndProductID.beforeExistByUserIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByUserIDAndProductID.afterExistByUserIDAndProductIDCounter, 1)

	mmExistByUserIDAndProductID.t.Helper()

	if mmExistByUserIDAndProductID.inspectFuncExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.inspectFuncExistByUserIDAndProductID(userID, productID)
	}

	mm_params := ParticipantCacheMockExistByUserIDAndProductIDParams{userID, productID}

	// Record call args
	mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.mutex.Lock()
	mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.callArgs = append(mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.callArgs, &mm_params)
	mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockExistByUserIDAndProductIDParams{userID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmExistByUserIDAndProductID.t.Errorf("ParticipantCacheMock.ExistByUserIDAndProductID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmExistByUserIDAndProductID.t.Errorf("ParticipantCacheMock.ExistByUserIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByUserIDAndProductID.t.Errorf("ParticipantCacheMock.ExistByUserIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByUserIDAndProductID.t.Fatal("No results are set for the ParticipantCacheMock.ExistByUserIDAndProductID")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByUserIDAndProductID.funcExistByUserIDAndProductID != nil {
		return mmExistByUserIDAndProductID.funcExistByUserIDAndProductID(userID, productID)
	}
	mmExistByUserIDAndProductID.t.Fatalf("Unexpected call to ParticipantCacheMock.ExistByUserIDAndProductID. %v %v", userID, productID)
	return
}

// ExistByUserIDAndProductIDAfterCounter returns a count of finished ParticipantCacheMock.ExistByUserIDAndProductID invocations
func (mmExistByUserIDAndProductID *ParticipantCacheMock) ExistByUserIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.afterExistByUserIDAndProductIDCounter)
}

// ExistByUserIDAndProductIDBeforeCounter returns a count of ParticipantCacheMock.ExistByUserIDAndProductID invocations
func (mmExistByUserIDAndProductID *ParticipantCacheMock) ExistByUserIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.beforeExistByUserIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.ExistByUserIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByUserIDAndProductID *mParticipantCacheMockExistByUserIDAndProductID) Calls() []*ParticipantCacheMockExistByUserIDAndProductIDParams {
	mmExistByUserIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockExistByUserIDAndProductIDParams, len(mmExistByUserIDAndProductID.callArgs))
	copy(argCopy, mmExistByUserIDAndProductID.callArgs)

	mmExistByUserIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockExistByUserIDAndProductIDDone returns true if the count of the ExistByUserIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockExistByUserIDAndProductIDDone() bool {
	if m.ExistByUserIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByUserIDAndProductIDMock.invocationsDone()
}

// MinimockExistByUserIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockExistByUserIDAndProductIDInspect() {
	for _, e := range m.ExistByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.ExistByUserIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByUserIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterExistByUserIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByUserIDAndProductIDMock.defaultExpectation != nil && afterExistByUserIDAndProductIDCounter < 1 {
		if m.ExistByUserIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.ExistByUserIDAndProductID at\n%s", m.ExistByUserIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.ExistByUserIDAndProductID at\n%s with params: %#v", m.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.ExistByUserIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByUserIDAndProductID != nil && afterExistByUserIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.ExistByUserIDAndProductID at\n%s", m.funcExistByUserIDAndProductIDOrigin)
	}

	if !m.ExistByUserIDAndProductIDMock.invocationsDone() && afterExistByUserIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.ExistByUserIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByUserIDAndProductIDMock.expectedInvocations), m.ExistByUserIDAndProductIDMock.expectedInvocationsOrigin, afterExistByUserIDAndProductIDCounter)
	}
}

type mParticipantCacheMockGetByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockGetByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantCacheMockGetByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantCacheMockGetByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockGetByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantCache.GetByParticipantIDAndProductID
type ParticipantCacheMockGetByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockGetByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantCacheMockGetByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantCacheMockGetByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantCacheMockGetByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockGetByParticipantIDAndProductIDParams contains parameters of the ParticipantCache.GetByParticipantIDAndProductID
type ParticipantCacheMockGetByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantCacheMockGetByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantCache.GetByParticipantIDAndProductID
type ParticipantCacheMockGetByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantCacheMockGetByParticipantIDAndProductIDResults contains results of the ParticipantCache.GetByParticipantIDAndProductID
type ParticipantCacheMockGetByParticipantIDAndProductIDResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantCacheMockGetByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantCache.GetByParticipantIDAndProductID
type ParticipantCacheMockGetByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Optional() *mParticipantCacheMockGetByParticipantIDAndProductID {
	mmGetByParticipantIDAndProductID.optional = true
	return mmGetByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantCache.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantCacheMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantIDAndProductID.defaultExpectation.params = &ParticipantCacheMockGetByParticipantIDAndProductIDParams{participantID, productID}
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantIDAndProductID.defaultExpectation.params) {
			mmGetByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantCache.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantCacheMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantCache.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantCacheMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantCacheMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.inspectFuncGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.GetByParticipantIDAndProductID")
	}

	mmGetByParticipantIDAndProductID.mock.inspectFuncGetByParticipantIDAndProductID = f

	return mmGetByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantCache.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Return(p1 participantentity.Participant, err error) *ParticipantCacheMock {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByParticipantIDAndProductIDExpectation{mock: mmGetByParticipantIDAndProductID.mock}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.results = &ParticipantCacheMockGetByParticipantIDAndProductIDResults{p1, err}
	mmGetByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantCache.GetByParticipantIDAndProductID method
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (p1 participantentity.Participant, err error)) *ParticipantCacheMock {
	if mmGetByParticipantIDAndProductID.defaultExpectation != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.GetByParticipantIDAndProductID method")
	}

	if len(mmGetByParticipantIDAndProductID.expectations) > 0 {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.GetByParticipantIDAndProductID method")
	}

	mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID = f
	mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantCache.GetByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantCacheMockGetByParticipantIDAndProductIDExpectation {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantCacheMockGetByParticipantIDAndProductIDExpectation{
		mock:               mmGetByParticipantIDAndProductID.mock,
		params:             &ParticipantCacheMockGetByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantCacheMockGetByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantIDAndProductID.expectations = append(mmGetByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.GetByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockGetByParticipantIDAndProductIDExpectation) Then(p1 participantentity.Participant, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockGetByParticipantIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.GetByParticipantIDAndProductID should be invoked
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Times(n uint64) *mParticipantCacheMockGetByParticipantIDAndProductID {
	if n == 0 {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantCacheMock.GetByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantIDAndProductID.expectedInvocations, n)
	mmGetByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID
}

func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmGetByParticipantIDAndProductID.expectations) == 0 && mmGetByParticipantIDAndProductID.defaultExpectation == nil && mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.mock.afterGetByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantIDAndProductID implements mm_repository.ParticipantCache
func (mmGetByParticipantIDAndProductID *ParticipantCacheMock) GetByParticipantIDAndProductID(participantID int64, productID int64) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.beforeGetByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.afterGetByParticipantIDAndProductIDCounter, 1)

	mmGetByParticipantIDAndProductID.t.Helper()

	if mmGetByParticipantIDAndProductID.inspectFuncGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.inspectFuncGetByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantCacheMockGetByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.mutex.Lock()
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.callArgs = append(mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockGetByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantIDAndProductID.t.Errorf("ParticipantCacheMock.GetByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByParticipantIDAndProductID.t.Errorf("ParticipantCacheMock.GetByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantIDAndProductID.t.Errorf("ParticipantCacheMock.GetByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantCacheMock.GetByParticipantIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByParticipantIDAndProductID.funcGetByParticipantIDAndProductID != nil {
		return mmGetByParticipantIDAndProductID.funcGetByParticipantIDAndProductID(participantID, productID)
	}
	mmGetByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantCacheMock.GetByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// GetByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantCacheMock.GetByParticipantIDAndProductID invocations
func (mmGetByParticipantIDAndProductID *ParticipantCacheMock) GetByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.afterGetByParticipantIDAndProductIDCounter)
}

// GetByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantCacheMock.GetByParticipantIDAndProductID invocations
func (mmGetByParticipantIDAndProductID *ParticipantCacheMock) GetByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.beforeGetByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.GetByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantIDAndProductID *mParticipantCacheMockGetByParticipantIDAndProductID) Calls() []*ParticipantCacheMockGetByParticipantIDAndProductIDParams {
	mmGetByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockGetByParticipantIDAndProductIDParams, len(mmGetByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmGetByParticipantIDAndProductID.callArgs)

	mmGetByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDAndProductIDDone returns true if the count of the GetByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockGetByParticipantIDAndProductIDDone() bool {
	if m.GetByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockGetByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockGetByParticipantIDAndProductIDInspect() {
	for _, e := range m.GetByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDAndProductIDMock.defaultExpectation != nil && afterGetByParticipantIDAndProductIDCounter < 1 {
		if m.GetByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByParticipantIDAndProductID at\n%s", m.GetByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByParticipantIDAndProductID at\n%s with params: %#v", m.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantIDAndProductID != nil && afterGetByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.GetByParticipantIDAndProductID at\n%s", m.funcGetByParticipantIDAndProductIDOrigin)
	}

	if !m.GetByParticipantIDAndProductIDMock.invocationsDone() && afterGetByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.GetByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDAndProductIDMock.expectedInvocations), m.GetByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterGetByParticipantIDAndProductIDCounter)
	}
}

type mParticipantCacheMockGetByProductID struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockGetByProductIDExpectation
	expectations       []*ParticipantCacheMockGetByProductIDExpectation

	callArgs []*ParticipantCacheMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockGetByProductIDExpectation specifies expectation struct of the ParticipantCache.GetByProductID
type ParticipantCacheMockGetByProductIDExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockGetByProductIDParams
	paramPtrs          *ParticipantCacheMockGetByProductIDParamPtrs
	expectationOrigins ParticipantCacheMockGetByProductIDExpectationOrigins
	results            *ParticipantCacheMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockGetByProductIDParams contains parameters of the ParticipantCache.GetByProductID
type ParticipantCacheMockGetByProductIDParams struct {
	productID int64
}

// ParticipantCacheMockGetByProductIDParamPtrs contains pointers to parameters of the ParticipantCache.GetByProductID
type ParticipantCacheMockGetByProductIDParamPtrs struct {
	productID *int64
}

// ParticipantCacheMockGetByProductIDResults contains results of the ParticipantCache.GetByProductID
type ParticipantCacheMockGetByProductIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantCacheMockGetByProductIDOrigins contains origins of expectations of the ParticipantCache.GetByProductID
type ParticipantCacheMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Optional() *mParticipantCacheMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for ParticipantCache.GetByProductID
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Expect(productID int64) *mParticipantCacheMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantCacheMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantCacheMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantCacheMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &ParticipantCacheMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantCache.GetByProductID
func (mmGetByProductID *mParticipantCacheMockGetByProductID) ExpectProductIDParam1(productID int64) *mParticipantCacheMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantCacheMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantCacheMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantCacheMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.GetByProductID
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Inspect(f func(productID int64)) *mParticipantCacheMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by ParticipantCache.GetByProductID
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Return(pa1 []participantentity.Participant, err error) *ParticipantCacheMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantCacheMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantCacheMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &ParticipantCacheMockGetByProductIDResults{pa1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the ParticipantCache.GetByProductID method
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Set(f func(productID int64) (pa1 []participantentity.Participant, err error)) *ParticipantCacheMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the ParticipantCache.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mParticipantCacheMockGetByProductID) When(productID int64) *ParticipantCacheMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantCacheMock.GetByProductID mock is already set by Set")
	}

	expectation := &ParticipantCacheMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &ParticipantCacheMockGetByProductIDParams{productID},
		expectationOrigins: ParticipantCacheMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.GetByProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockGetByProductIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockGetByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.GetByProductID should be invoked
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Times(n uint64) *mParticipantCacheMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of ParticipantCacheMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mParticipantCacheMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_repository.ParticipantCache
func (mmGetByProductID *ParticipantCacheMock) GetByProductID(productID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := ParticipantCacheMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("ParticipantCacheMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("ParticipantCacheMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the ParticipantCacheMock.GetByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to ParticipantCacheMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished ParticipantCacheMock.GetByProductID invocations
func (mmGetByProductID *ParticipantCacheMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of ParticipantCacheMock.GetByProductID invocations
func (mmGetByProductID *ParticipantCacheMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mParticipantCacheMockGetByProductID) Calls() []*ParticipantCacheMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mParticipantCacheMockGetByUserID struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockGetByUserIDExpectation
	expectations       []*ParticipantCacheMockGetByUserIDExpectation

	callArgs []*ParticipantCacheMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockGetByUserIDExpectation specifies expectation struct of the ParticipantCache.GetByUserID
type ParticipantCacheMockGetByUserIDExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockGetByUserIDParams
	paramPtrs          *ParticipantCacheMockGetByUserIDParamPtrs
	expectationOrigins ParticipantCacheMockGetByUserIDExpectationOrigins
	results            *ParticipantCacheMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockGetByUserIDParams contains parameters of the ParticipantCache.GetByUserID
type ParticipantCacheMockGetByUserIDParams struct {
	userID int64
}

// ParticipantCacheMockGetByUserIDParamPtrs contains pointers to parameters of the ParticipantCache.GetByUserID
type ParticipantCacheMockGetByUserIDParamPtrs struct {
	userID *int64
}

// ParticipantCacheMockGetByUserIDResults contains results of the ParticipantCache.GetByUserID
type ParticipantCacheMockGetByUserIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantCacheMockGetByUserIDOrigins contains origins of expectations of the ParticipantCache.GetByUserID
type ParticipantCacheMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Optional() *mParticipantCacheMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for ParticipantCache.GetByUserID
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Expect(userID int64) *mParticipantCacheMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantCacheMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantCacheMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantCacheMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &ParticipantCacheMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantCache.GetByUserID
func (mmGetByUserID *mParticipantCacheMockGetByUserID) ExpectUserIDParam1(userID int64) *mParticipantCacheMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantCacheMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantCacheMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantCacheMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.GetByUserID
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Inspect(f func(userID int64)) *mParticipantCacheMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by ParticipantCache.GetByUserID
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Return(pa1 []participantentity.Participant, err error) *ParticipantCacheMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantCacheMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantCacheMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &ParticipantCacheMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the ParticipantCache.GetByUserID method
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Set(f func(userID int64) (pa1 []participantentity.Participant, err error)) *ParticipantCacheMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the ParticipantCache.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mParticipantCacheMockGetByUserID) When(userID int64) *ParticipantCacheMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantCacheMock.GetByUserID mock is already set by Set")
	}

	expectation := &ParticipantCacheMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &ParticipantCacheMockGetByUserIDParams{userID},
		expectationOrigins: ParticipantCacheMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.GetByUserID return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockGetByUserIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.GetByUserID should be invoked
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Times(n uint64) *mParticipantCacheMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of ParticipantCacheMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mParticipantCacheMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.ParticipantCache
func (mmGetByUserID *ParticipantCacheMock) GetByUserID(userID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := ParticipantCacheMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("ParticipantCacheMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("ParticipantCacheMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the ParticipantCacheMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to ParticipantCacheMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished ParticipantCacheMock.GetByUserID invocations
func (mmGetByUserID *ParticipantCacheMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of ParticipantCacheMock.GetByUserID invocations
func (mmGetByUserID *ParticipantCacheMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mParticipantCacheMockGetByUserID) Calls() []*ParticipantCacheMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mParticipantCacheMockGetByUserIDAndProductID struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockGetByUserIDAndProductIDExpectation
	expectations       []*ParticipantCacheMockGetByUserIDAndProductIDExpectation

	callArgs []*ParticipantCacheMockGetByUserIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockGetByUserIDAndProductIDExpectation specifies expectation struct of the ParticipantCache.GetByUserIDAndProductID
type ParticipantCacheMockGetByUserIDAndProductIDExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockGetByUserIDAndProductIDParams
	paramPtrs          *ParticipantCacheMockGetByUserIDAndProductIDParamPtrs
	expectationOrigins ParticipantCacheMockGetByUserIDAndProductIDExpectationOrigins
	results            *ParticipantCacheMockGetByUserIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockGetByUserIDAndProductIDParams contains parameters of the ParticipantCache.GetByUserIDAndProductID
type ParticipantCacheMockGetByUserIDAndProductIDParams struct {
	userID    int64
	productID int64
}

// ParticipantCacheMockGetByUserIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantCache.GetByUserIDAndProductID
type ParticipantCacheMockGetByUserIDAndProductIDParamPtrs struct {
	userID    *int64
	productID *int64
}

// ParticipantCacheMockGetByUserIDAndProductIDResults contains results of the ParticipantCache.GetByUserIDAndProductID
type ParticipantCacheMockGetByUserIDAndProductIDResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantCacheMockGetByUserIDAndProductIDOrigins contains origins of expectations of the ParticipantCache.GetByUserIDAndProductID
type ParticipantCacheMockGetByUserIDAndProductIDExpectationOrigins struct {
	origin          string
	originUserID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Optional() *mParticipantCacheMockGetByUserIDAndProductID {
	mmGetByUserIDAndProductID.optional = true
	return mmGetByUserIDAndProductID
}

// Expect sets up expected params for ParticipantCache.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Expect(userID int64, productID int64) *mParticipantCacheMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndProductID.defaultExpectation.params = &ParticipantCacheMockGetByUserIDAndProductIDParams{userID, productID}
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndProductID.defaultExpectation.params) {
			mmGetByUserIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndProductID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantCache.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) ExpectUserIDParam1(userID int64) *mParticipantCacheMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.params != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Expect")
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByUserIDAndProductIDParamPtrs{}
	}
	mmGetByUserIDAndProductID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantCache.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantCacheMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.params != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Expect")
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByUserIDAndProductIDParamPtrs{}
	}
	mmGetByUserIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Inspect(f func(userID int64, productID int64)) *mParticipantCacheMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.inspectFuncGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.GetByUserIDAndProductID")
	}

	mmGetByUserIDAndProductID.mock.inspectFuncGetByUserIDAndProductID = f

	return mmGetByUserIDAndProductID
}

// Return sets up results that will be returned by ParticipantCache.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Return(p1 participantentity.Participant, err error) *ParticipantCacheMock {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDExpectation{mock: mmGetByUserIDAndProductID.mock}
	}
	mmGetByUserIDAndProductID.defaultExpectation.results = &ParticipantCacheMockGetByUserIDAndProductIDResults{p1, err}
	mmGetByUserIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantCache.GetByUserIDAndProductID method
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Set(f func(userID int64, productID int64) (p1 participantentity.Participant, err error)) *ParticipantCacheMock {
	if mmGetByUserIDAndProductID.defaultExpectation != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.GetByUserIDAndProductID method")
	}

	if len(mmGetByUserIDAndProductID.expectations) > 0 {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.GetByUserIDAndProductID method")
	}

	mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID = f
	mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID.mock
}

// When sets expectation for the ParticipantCache.GetByUserIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) When(userID int64, productID int64) *ParticipantCacheMockGetByUserIDAndProductIDExpectation {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantCacheMockGetByUserIDAndProductIDExpectation{
		mock:               mmGetByUserIDAndProductID.mock,
		params:             &ParticipantCacheMockGetByUserIDAndProductIDParams{userID, productID},
		expectationOrigins: ParticipantCacheMockGetByUserIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndProductID.expectations = append(mmGetByUserIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.GetByUserIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockGetByUserIDAndProductIDExpectation) Then(p1 participantentity.Participant, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockGetByUserIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.GetByUserIDAndProductID should be invoked
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Times(n uint64) *mParticipantCacheMockGetByUserIDAndProductID {
	if n == 0 {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Times of ParticipantCacheMock.GetByUserIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndProductID.expectedInvocations, n)
	mmGetByUserIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID
}

func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) invocationsDone() bool {
	if len(mmGetByUserIDAndProductID.expectations) == 0 && mmGetByUserIDAndProductID.defaultExpectation == nil && mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.mock.afterGetByUserIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndProductID implements mm_repository.ParticipantCache
func (mmGetByUserIDAndProductID *ParticipantCacheMock) GetByUserIDAndProductID(userID int64, productID int64) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndProductID.beforeGetByUserIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndProductID.afterGetByUserIDAndProductIDCounter, 1)

	mmGetByUserIDAndProductID.t.Helper()

	if mmGetByUserIDAndProductID.inspectFuncGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.inspectFuncGetByUserIDAndProductID(userID, productID)
	}

	mm_params := ParticipantCacheMockGetByUserIDAndProductIDParams{userID, productID}

	// Record call args
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.mutex.Lock()
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.callArgs = append(mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.callArgs, &mm_params)
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockGetByUserIDAndProductIDParams{userID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndProductID.t.Errorf("ParticipantCacheMock.GetByUserIDAndProductID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByUserIDAndProductID.t.Errorf("ParticipantCacheMock.GetByUserIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndProductID.t.Errorf("ParticipantCacheMock.GetByUserIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndProductID.t.Fatal("No results are set for the ParticipantCacheMock.GetByUserIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByUserIDAndProductID.funcGetByUserIDAndProductID != nil {
		return mmGetByUserIDAndProductID.funcGetByUserIDAndProductID(userID, productID)
	}
	mmGetByUserIDAndProductID.t.Fatalf("Unexpected call to ParticipantCacheMock.GetByUserIDAndProductID. %v %v", userID, productID)
	return
}

// GetByUserIDAndProductIDAfterCounter returns a count of finished ParticipantCacheMock.GetByUserIDAndProductID invocations
func (mmGetByUserIDAndProductID *ParticipantCacheMock) GetByUserIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.afterGetByUserIDAndProductIDCounter)
}

// GetByUserIDAndProductIDBeforeCounter returns a count of ParticipantCacheMock.GetByUserIDAndProductID invocations
func (mmGetByUserIDAndProductID *ParticipantCacheMock) GetByUserIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.beforeGetByUserIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.GetByUserIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndProductID *mParticipantCacheMockGetByUserIDAndProductID) Calls() []*ParticipantCacheMockGetByUserIDAndProductIDParams {
	mmGetByUserIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockGetByUserIDAndProductIDParams, len(mmGetByUserIDAndProductID.callArgs))
	copy(argCopy, mmGetByUserIDAndProductID.callArgs)

	mmGetByUserIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndProductIDDone returns true if the count of the GetByUserIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockGetByUserIDAndProductIDDone() bool {
	if m.GetByUserIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndProductIDMock.invocationsDone()
}

// MinimockGetByUserIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockGetByUserIDAndProductIDInspect() {
	for _, e := range m.GetByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndProductIDMock.defaultExpectation != nil && afterGetByUserIDAndProductIDCounter < 1 {
		if m.GetByUserIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductID at\n%s", m.GetByUserIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductID at\n%s with params: %#v", m.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndProductID != nil && afterGetByUserIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductID at\n%s", m.funcGetByUserIDAndProductIDOrigin)
	}

	if !m.GetByUserIDAndProductIDMock.invocationsDone() && afterGetByUserIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.GetByUserIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndProductIDMock.expectedInvocations), m.GetByUserIDAndProductIDMock.expectedInvocationsOrigin, afterGetByUserIDAndProductIDCounter)
	}
}

type mParticipantCacheMockGetByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockGetByUserIDAndProductIDsExpectation
	expectations       []*ParticipantCacheMockGetByUserIDAndProductIDsExpectation

	callArgs []*ParticipantCacheMockGetByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockGetByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantCache.GetByUserIDAndProductIDs
type ParticipantCacheMockGetByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockGetByUserIDAndProductIDsParams
	paramPtrs          *ParticipantCacheMockGetByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantCacheMockGetByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantCacheMockGetByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockGetByUserIDAndProductIDsParams contains parameters of the ParticipantCache.GetByUserIDAndProductIDs
type ParticipantCacheMockGetByUserIDAndProductIDsParams struct {
	userID     int64
	productIDs []int64
}

// ParticipantCacheMockGetByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantCache.GetByUserIDAndProductIDs
type ParticipantCacheMockGetByUserIDAndProductIDsParamPtrs struct {
	userID     *int64
	productIDs *[]int64
}

// ParticipantCacheMockGetByUserIDAndProductIDsResults contains results of the ParticipantCache.GetByUserIDAndProductIDs
type ParticipantCacheMockGetByUserIDAndProductIDsResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantCacheMockGetByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantCache.GetByUserIDAndProductIDs
type ParticipantCacheMockGetByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Optional() *mParticipantCacheMockGetByUserIDAndProductIDs {
	mmGetByUserIDAndProductIDs.optional = true
	return mmGetByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantCache.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Expect(userID int64, productIDs []int64) *mParticipantCacheMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndProductIDs.defaultExpectation.params = &ParticipantCacheMockGetByUserIDAndProductIDsParams{userID, productIDs}
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndProductIDs.defaultExpectation.params) {
			mmGetByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndProductIDs
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantCache.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) ExpectUserIDParam1(userID int64) *mParticipantCacheMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByUserIDAndProductIDsParamPtrs{}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductIDs
}

// ExpectProductIDsParam2 sets up expected param productIDs for ParticipantCache.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) ExpectProductIDsParam2(productIDs []int64) *mParticipantCacheMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantCacheMockGetByUserIDAndProductIDsParamPtrs{}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Inspect(f func(userID int64, productIDs []int64)) *mParticipantCacheMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.inspectFuncGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.GetByUserIDAndProductIDs")
	}

	mmGetByUserIDAndProductIDs.mock.inspectFuncGetByUserIDAndProductIDs = f

	return mmGetByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantCache.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Return(pa1 []participantentity.Participant, err error) *ParticipantCacheMock {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantCacheMockGetByUserIDAndProductIDsExpectation{mock: mmGetByUserIDAndProductIDs.mock}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.results = &ParticipantCacheMockGetByUserIDAndProductIDsResults{pa1, err}
	mmGetByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantCache.GetByUserIDAndProductIDs method
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Set(f func(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error)) *ParticipantCacheMock {
	if mmGetByUserIDAndProductIDs.defaultExpectation != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.GetByUserIDAndProductIDs method")
	}

	if len(mmGetByUserIDAndProductIDs.expectations) > 0 {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.GetByUserIDAndProductIDs method")
	}

	mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs = f
	mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantCache.GetByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) When(userID int64, productIDs []int64) *ParticipantCacheMockGetByUserIDAndProductIDsExpectation {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantCacheMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantCacheMockGetByUserIDAndProductIDsExpectation{
		mock:               mmGetByUserIDAndProductIDs.mock,
		params:             &ParticipantCacheMockGetByUserIDAndProductIDsParams{userID, productIDs},
		expectationOrigins: ParticipantCacheMockGetByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndProductIDs.expectations = append(mmGetByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.GetByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockGetByUserIDAndProductIDsExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockGetByUserIDAndProductIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantCache.GetByUserIDAndProductIDs should be invoked
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Times(n uint64) *mParticipantCacheMockGetByUserIDAndProductIDs {
	if n == 0 {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantCacheMock.GetByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndProductIDs.expectedInvocations, n)
	mmGetByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs
}

func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmGetByUserIDAndProductIDs.expectations) == 0 && mmGetByUserIDAndProductIDs.defaultExpectation == nil && mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.mock.afterGetByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndProductIDs implements mm_repository.ParticipantCache
func (mmGetByUserIDAndProductIDs *ParticipantCacheMock) GetByUserIDAndProductIDs(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.beforeGetByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.afterGetByUserIDAndProductIDsCounter, 1)

	mmGetByUserIDAndProductIDs.t.Helper()

	if mmGetByUserIDAndProductIDs.inspectFuncGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.inspectFuncGetByUserIDAndProductIDs(userID, productIDs)
	}

	mm_params := ParticipantCacheMockGetByUserIDAndProductIDsParams{userID, productIDs}

	// Record call args
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.mutex.Lock()
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.callArgs = append(mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockGetByUserIDAndProductIDsParams{userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndProductIDs.t.Errorf("ParticipantCacheMock.GetByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmGetByUserIDAndProductIDs.t.Errorf("ParticipantCacheMock.GetByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndProductIDs.t.Errorf("ParticipantCacheMock.GetByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantCacheMock.GetByUserIDAndProductIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserIDAndProductIDs.funcGetByUserIDAndProductIDs != nil {
		return mmGetByUserIDAndProductIDs.funcGetByUserIDAndProductIDs(userID, productIDs)
	}
	mmGetByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantCacheMock.GetByUserIDAndProductIDs. %v %v", userID, productIDs)
	return
}

// GetByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantCacheMock.GetByUserIDAndProductIDs invocations
func (mmGetByUserIDAndProductIDs *ParticipantCacheMock) GetByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.afterGetByUserIDAndProductIDsCounter)
}

// GetByUserIDAndProductIDsBeforeCounter returns a count of ParticipantCacheMock.GetByUserIDAndProductIDs invocations
func (mmGetByUserIDAndProductIDs *ParticipantCacheMock) GetByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.beforeGetByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.GetByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndProductIDs *mParticipantCacheMockGetByUserIDAndProductIDs) Calls() []*ParticipantCacheMockGetByUserIDAndProductIDsParams {
	mmGetByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockGetByUserIDAndProductIDsParams, len(mmGetByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmGetByUserIDAndProductIDs.callArgs)

	mmGetByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndProductIDsDone returns true if the count of the GetByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockGetByUserIDAndProductIDsDone() bool {
	if m.GetByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockGetByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockGetByUserIDAndProductIDsInspect() {
	for _, e := range m.GetByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndProductIDsMock.defaultExpectation != nil && afterGetByUserIDAndProductIDsCounter < 1 {
		if m.GetByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductIDs at\n%s", m.GetByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductIDs at\n%s with params: %#v", m.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndProductIDs != nil && afterGetByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.GetByUserIDAndProductIDs at\n%s", m.funcGetByUserIDAndProductIDsOrigin)
	}

	if !m.GetByUserIDAndProductIDsMock.invocationsDone() && afterGetByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.GetByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndProductIDsMock.expectedInvocations), m.GetByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterGetByUserIDAndProductIDsCounter)
	}
}

type mParticipantCacheMockSet struct {
	optional           bool
	mock               *ParticipantCacheMock
	defaultExpectation *ParticipantCacheMockSetExpectation
	expectations       []*ParticipantCacheMockSetExpectation

	callArgs []*ParticipantCacheMockSetParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantCacheMockSetExpectation specifies expectation struct of the ParticipantCache.Set
type ParticipantCacheMockSetExpectation struct {
	mock               *ParticipantCacheMock
	params             *ParticipantCacheMockSetParams
	paramPtrs          *ParticipantCacheMockSetParamPtrs
	expectationOrigins ParticipantCacheMockSetExpectationOrigins
	results            *ParticipantCacheMockSetResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantCacheMockSetParams contains parameters of the ParticipantCache.Set
type ParticipantCacheMockSetParams struct {
	participant participantentity.Participant
}

// ParticipantCacheMockSetParamPtrs contains pointers to parameters of the ParticipantCache.Set
type ParticipantCacheMockSetParamPtrs struct {
	participant *participantentity.Participant
}

// ParticipantCacheMockSetResults contains results of the ParticipantCache.Set
type ParticipantCacheMockSetResults struct {
	err error
}

// ParticipantCacheMockSetOrigins contains origins of expectations of the ParticipantCache.Set
type ParticipantCacheMockSetExpectationOrigins struct {
	origin            string
	originParticipant string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSet *mParticipantCacheMockSet) Optional() *mParticipantCacheMockSet {
	mmSet.optional = true
	return mmSet
}

// Expect sets up expected params for ParticipantCache.Set
func (mmSet *mParticipantCacheMockSet) Expect(participant participantentity.Participant) *mParticipantCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ParticipantCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &ParticipantCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.paramPtrs != nil {
		mmSet.mock.t.Fatalf("ParticipantCacheMock.Set mock is already set by ExpectParams functions")
	}

	mmSet.defaultExpectation.params = &ParticipantCacheMockSetParams{participant}
	mmSet.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSet.expectations {
		if minimock.Equal(e.params, mmSet.defaultExpectation.params) {
			mmSet.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSet.defaultExpectation.params)
		}
	}

	return mmSet
}

// ExpectParticipantParam1 sets up expected param participant for ParticipantCache.Set
func (mmSet *mParticipantCacheMockSet) ExpectParticipantParam1(participant participantentity.Participant) *mParticipantCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ParticipantCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &ParticipantCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.params != nil {
		mmSet.mock.t.Fatalf("ParticipantCacheMock.Set mock is already set by Expect")
	}

	if mmSet.defaultExpectation.paramPtrs == nil {
		mmSet.defaultExpectation.paramPtrs = &ParticipantCacheMockSetParamPtrs{}
	}
	mmSet.defaultExpectation.paramPtrs.participant = &participant
	mmSet.defaultExpectation.expectationOrigins.originParticipant = minimock.CallerInfo(1)

	return mmSet
}

// Inspect accepts an inspector function that has same arguments as the ParticipantCache.Set
func (mmSet *mParticipantCacheMockSet) Inspect(f func(participant participantentity.Participant)) *mParticipantCacheMockSet {
	if mmSet.mock.inspectFuncSet != nil {
		mmSet.mock.t.Fatalf("Inspect function is already set for ParticipantCacheMock.Set")
	}

	mmSet.mock.inspectFuncSet = f

	return mmSet
}

// Return sets up results that will be returned by ParticipantCache.Set
func (mmSet *mParticipantCacheMockSet) Return(err error) *ParticipantCacheMock {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ParticipantCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &ParticipantCacheMockSetExpectation{mock: mmSet.mock}
	}
	mmSet.defaultExpectation.results = &ParticipantCacheMockSetResults{err}
	mmSet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// Set uses given function f to mock the ParticipantCache.Set method
func (mmSet *mParticipantCacheMockSet) Set(f func(participant participantentity.Participant) (err error)) *ParticipantCacheMock {
	if mmSet.defaultExpectation != nil {
		mmSet.mock.t.Fatalf("Default expectation is already set for the ParticipantCache.Set method")
	}

	if len(mmSet.expectations) > 0 {
		mmSet.mock.t.Fatalf("Some expectations are already set for the ParticipantCache.Set method")
	}

	mmSet.mock.funcSet = f
	mmSet.mock.funcSetOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// When sets expectation for the ParticipantCache.Set which will trigger the result defined by the following
// Then helper
func (mmSet *mParticipantCacheMockSet) When(participant participantentity.Participant) *ParticipantCacheMockSetExpectation {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ParticipantCacheMock.Set mock is already set by Set")
	}

	expectation := &ParticipantCacheMockSetExpectation{
		mock:               mmSet.mock,
		params:             &ParticipantCacheMockSetParams{participant},
		expectationOrigins: ParticipantCacheMockSetExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSet.expectations = append(mmSet.expectations, expectation)
	return expectation
}

// Then sets up ParticipantCache.Set return parameters for the expectation previously defined by the When method
func (e *ParticipantCacheMockSetExpectation) Then(err error) *ParticipantCacheMock {
	e.results = &ParticipantCacheMockSetResults{err}
	return e.mock
}

// Times sets number of times ParticipantCache.Set should be invoked
func (mmSet *mParticipantCacheMockSet) Times(n uint64) *mParticipantCacheMockSet {
	if n == 0 {
		mmSet.mock.t.Fatalf("Times of ParticipantCacheMock.Set mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSet.expectedInvocations, n)
	mmSet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSet
}

func (mmSet *mParticipantCacheMockSet) invocationsDone() bool {
	if len(mmSet.expectations) == 0 && mmSet.defaultExpectation == nil && mmSet.mock.funcSet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSet.mock.afterSetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Set implements mm_repository.ParticipantCache
func (mmSet *ParticipantCacheMock) Set(participant participantentity.Participant) (err error) {
	mm_atomic.AddUint64(&mmSet.beforeSetCounter, 1)
	defer mm_atomic.AddUint64(&mmSet.afterSetCounter, 1)

	mmSet.t.Helper()

	if mmSet.inspectFuncSet != nil {
		mmSet.inspectFuncSet(participant)
	}

	mm_params := ParticipantCacheMockSetParams{participant}

	// Record call args
	mmSet.SetMock.mutex.Lock()
	mmSet.SetMock.callArgs = append(mmSet.SetMock.callArgs, &mm_params)
	mmSet.SetMock.mutex.Unlock()

	for _, e := range mmSet.SetMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSet.SetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSet.SetMock.defaultExpectation.Counter, 1)
		mm_want := mmSet.SetMock.defaultExpectation.params
		mm_want_ptrs := mmSet.SetMock.defaultExpectation.paramPtrs

		mm_got := ParticipantCacheMockSetParams{participant}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participant != nil && !minimock.Equal(*mm_want_ptrs.participant, mm_got.participant) {
				mmSet.t.Errorf("ParticipantCacheMock.Set got unexpected parameter participant, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSet.SetMock.defaultExpectation.expectationOrigins.originParticipant, *mm_want_ptrs.participant, mm_got.participant, minimock.Diff(*mm_want_ptrs.participant, mm_got.participant))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSet.t.Errorf("ParticipantCacheMock.Set got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSet.SetMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSet.SetMock.defaultExpectation.results
		if mm_results == nil {
			mmSet.t.Fatal("No results are set for the ParticipantCacheMock.Set")
		}
		return (*mm_results).err
	}
	if mmSet.funcSet != nil {
		return mmSet.funcSet(participant)
	}
	mmSet.t.Fatalf("Unexpected call to ParticipantCacheMock.Set. %v", participant)
	return
}

// SetAfterCounter returns a count of finished ParticipantCacheMock.Set invocations
func (mmSet *ParticipantCacheMock) SetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.afterSetCounter)
}

// SetBeforeCounter returns a count of ParticipantCacheMock.Set invocations
func (mmSet *ParticipantCacheMock) SetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.beforeSetCounter)
}

// Calls returns a list of arguments used in each call to ParticipantCacheMock.Set.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSet *mParticipantCacheMockSet) Calls() []*ParticipantCacheMockSetParams {
	mmSet.mutex.RLock()

	argCopy := make([]*ParticipantCacheMockSetParams, len(mmSet.callArgs))
	copy(argCopy, mmSet.callArgs)

	mmSet.mutex.RUnlock()

	return argCopy
}

// MinimockSetDone returns true if the count of the Set invocations corresponds
// the number of defined expectations
func (m *ParticipantCacheMock) MinimockSetDone() bool {
	if m.SetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetMock.invocationsDone()
}

// MinimockSetInspect logs each unmet expectation
func (m *ParticipantCacheMock) MinimockSetInspect() {
	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantCacheMock.Set at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCounter := mm_atomic.LoadUint64(&m.afterSetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetMock.defaultExpectation != nil && afterSetCounter < 1 {
		if m.SetMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantCacheMock.Set at\n%s", m.SetMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantCacheMock.Set at\n%s with params: %#v", m.SetMock.defaultExpectation.expectationOrigins.origin, *m.SetMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSet != nil && afterSetCounter < 1 {
		m.t.Errorf("Expected call to ParticipantCacheMock.Set at\n%s", m.funcSetOrigin)
	}

	if !m.SetMock.invocationsDone() && afterSetCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantCacheMock.Set at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetMock.expectedInvocations), m.SetMock.expectedInvocationsOrigin, afterSetCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ParticipantCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockDeleteInspect()

			m.MinimockDeleteByUserIDAndProductIDsInspect()

			m.MinimockExistByParticipantIDAndProductIDInspect()

			m.MinimockExistByUserIDAndProductIDInspect()

			m.MinimockGetByParticipantIDAndProductIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetByUserIDAndProductIDInspect()

			m.MinimockGetByUserIDAndProductIDsInspect()

			m.MinimockSetInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ParticipantCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ParticipantCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockDeleteDone() &&
		m.MinimockDeleteByUserIDAndProductIDsDone() &&
		m.MinimockExistByParticipantIDAndProductIDDone() &&
		m.MinimockExistByUserIDAndProductIDDone() &&
		m.MinimockGetByParticipantIDAndProductIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetByUserIDAndProductIDDone() &&
		m.MinimockGetByUserIDAndProductIDsDone() &&
		m.MinimockSetDone()
}
