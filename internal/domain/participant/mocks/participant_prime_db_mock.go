// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository.ParticipantPrimeDB -o participant_prime_db_mock.go -n ParticipantPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"github.com/gojuno/minimock/v3"
)

// ParticipantPrimeDBMock implements mm_repository.ParticipantPrimeDB
type ParticipantPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.Participant, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, participant participantentity.ParticipantCreateData)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mParticipantPrimeDBMockCreate

	funcCreateByUserIDAndProductIDs          func(ctx context.Context, userID int64, productIDs []int64) (err error)
	funcCreateByUserIDAndProductIDsOrigin    string
	inspectFuncCreateByUserIDAndProductIDs   func(ctx context.Context, userID int64, productIDs []int64)
	afterCreateByUserIDAndProductIDsCounter  uint64
	beforeCreateByUserIDAndProductIDsCounter uint64
	CreateByUserIDAndProductIDsMock          mParticipantPrimeDBMockCreateByUserIDAndProductIDs

	funcDelete          func(ctx context.Context, participantID int64, productID int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(ctx context.Context, participantID int64, productID int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mParticipantPrimeDBMockDelete

	funcDeleteByUserIDAndGroupIDs          func(ctx context.Context, userID int64, groupIDs []int64) (err error)
	funcDeleteByUserIDAndGroupIDsOrigin    string
	inspectFuncDeleteByUserIDAndGroupIDs   func(ctx context.Context, userID int64, groupIDs []int64)
	afterDeleteByUserIDAndGroupIDsCounter  uint64
	beforeDeleteByUserIDAndGroupIDsCounter uint64
	DeleteByUserIDAndGroupIDsMock          mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs

	funcDeleteByUserIDAndProductIDs          func(ctx context.Context, userID int64, productIDs []int64) (err error)
	funcDeleteByUserIDAndProductIDsOrigin    string
	inspectFuncDeleteByUserIDAndProductIDs   func(ctx context.Context, userID int64, productIDs []int64)
	afterDeleteByUserIDAndProductIDsCounter  uint64
	beforeDeleteByUserIDAndProductIDsCounter uint64
	DeleteByUserIDAndProductIDsMock          mParticipantPrimeDBMockDeleteByUserIDAndProductIDs

	funcDeleteByUserIDAndRoleIDs          func(ctx context.Context, userID int64, roleIDs []int64) (err error)
	funcDeleteByUserIDAndRoleIDsOrigin    string
	inspectFuncDeleteByUserIDAndRoleIDs   func(ctx context.Context, userID int64, roleIDs []int64)
	afterDeleteByUserIDAndRoleIDsCounter  uint64
	beforeDeleteByUserIDAndRoleIDsCounter uint64
	DeleteByUserIDAndRoleIDsMock          mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs

	funcDeleteByUserIDsAndProductID          func(ctx context.Context, productID int64, userID []int64) (err error)
	funcDeleteByUserIDsAndProductIDOrigin    string
	inspectFuncDeleteByUserIDsAndProductID   func(ctx context.Context, productID int64, userID []int64)
	afterDeleteByUserIDsAndProductIDCounter  uint64
	beforeDeleteByUserIDsAndProductIDCounter uint64
	DeleteByUserIDsAndProductIDMock          mParticipantPrimeDBMockDeleteByUserIDsAndProductID

	funcExistByParticipantIDAndProductID          func(participantID int64, productID int64) (b1 bool, err error)
	funcExistByParticipantIDAndProductIDOrigin    string
	inspectFuncExistByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterExistByParticipantIDAndProductIDCounter  uint64
	beforeExistByParticipantIDAndProductIDCounter uint64
	ExistByParticipantIDAndProductIDMock          mParticipantPrimeDBMockExistByParticipantIDAndProductID

	funcExistByUserIDAndProductID          func(userID int64, productID int64) (b1 bool, err error)
	funcExistByUserIDAndProductIDOrigin    string
	inspectFuncExistByUserIDAndProductID   func(userID int64, productID int64)
	afterExistByUserIDAndProductIDCounter  uint64
	beforeExistByUserIDAndProductIDCounter uint64
	ExistByUserIDAndProductIDMock          mParticipantPrimeDBMockExistByUserIDAndProductID

	funcGetByGroupID          func(groupID int64) (pa1 []participantentity.Participant, err error)
	funcGetByGroupIDOrigin    string
	inspectFuncGetByGroupID   func(groupID int64)
	afterGetByGroupIDCounter  uint64
	beforeGetByGroupIDCounter uint64
	GetByGroupIDMock          mParticipantPrimeDBMockGetByGroupID

	funcGetByParticipantIDAndProductID          func(participantID int64, productID int64) (p1 participantentity.Participant, err error)
	funcGetByParticipantIDAndProductIDOrigin    string
	inspectFuncGetByParticipantIDAndProductID   func(participantID int64, productID int64)
	afterGetByParticipantIDAndProductIDCounter  uint64
	beforeGetByParticipantIDAndProductIDCounter uint64
	GetByParticipantIDAndProductIDMock          mParticipantPrimeDBMockGetByParticipantIDAndProductID

	funcGetByProductID          func(productID int64) (pa1 []participantentity.Participant, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mParticipantPrimeDBMockGetByProductID

	funcGetByProductIDAndUserEmail          func(productID int64, email string) (p1 participantentity.Participant, err error)
	funcGetByProductIDAndUserEmailOrigin    string
	inspectFuncGetByProductIDAndUserEmail   func(productID int64, email string)
	afterGetByProductIDAndUserEmailCounter  uint64
	beforeGetByProductIDAndUserEmailCounter uint64
	GetByProductIDAndUserEmailMock          mParticipantPrimeDBMockGetByProductIDAndUserEmail

	funcGetByRoleID          func(roleID int64) (pa1 []participantentity.Participant, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mParticipantPrimeDBMockGetByRoleID

	funcGetByUserID          func(userID int64) (pa1 []participantentity.Participant, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mParticipantPrimeDBMockGetByUserID

	funcGetByUserIDAndProductID          func(userID int64, productID int64) (p1 participantentity.Participant, err error)
	funcGetByUserIDAndProductIDOrigin    string
	inspectFuncGetByUserIDAndProductID   func(userID int64, productID int64)
	afterGetByUserIDAndProductIDCounter  uint64
	beforeGetByUserIDAndProductIDCounter uint64
	GetByUserIDAndProductIDMock          mParticipantPrimeDBMockGetByUserIDAndProductID

	funcGetByUserIDAndProductIDs          func(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error)
	funcGetByUserIDAndProductIDsOrigin    string
	inspectFuncGetByUserIDAndProductIDs   func(userID int64, productIDs []int64)
	afterGetByUserIDAndProductIDsCounter  uint64
	beforeGetByUserIDAndProductIDsCounter uint64
	GetByUserIDAndProductIDsMock          mParticipantPrimeDBMockGetByUserIDAndProductIDs

	funcGetOwnersByProductID          func(productID int64) (oa1 []productentity.Owner, err error)
	funcGetOwnersByProductIDOrigin    string
	inspectFuncGetOwnersByProductID   func(productID int64)
	afterGetOwnersByProductIDCounter  uint64
	beforeGetOwnersByProductIDCounter uint64
	GetOwnersByProductIDMock          mParticipantPrimeDBMockGetOwnersByProductID
}

// NewParticipantPrimeDBMock returns a mock for mm_repository.ParticipantPrimeDB
func NewParticipantPrimeDBMock(t minimock.Tester) *ParticipantPrimeDBMock {
	m := &ParticipantPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mParticipantPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*ParticipantPrimeDBMockCreateParams{}

	m.CreateByUserIDAndProductIDsMock = mParticipantPrimeDBMockCreateByUserIDAndProductIDs{mock: m}
	m.CreateByUserIDAndProductIDsMock.callArgs = []*ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams{}

	m.DeleteMock = mParticipantPrimeDBMockDelete{mock: m}
	m.DeleteMock.callArgs = []*ParticipantPrimeDBMockDeleteParams{}

	m.DeleteByUserIDAndGroupIDsMock = mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs{mock: m}
	m.DeleteByUserIDAndGroupIDsMock.callArgs = []*ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams{}

	m.DeleteByUserIDAndProductIDsMock = mParticipantPrimeDBMockDeleteByUserIDAndProductIDs{mock: m}
	m.DeleteByUserIDAndProductIDsMock.callArgs = []*ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams{}

	m.DeleteByUserIDAndRoleIDsMock = mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs{mock: m}
	m.DeleteByUserIDAndRoleIDsMock.callArgs = []*ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams{}

	m.DeleteByUserIDsAndProductIDMock = mParticipantPrimeDBMockDeleteByUserIDsAndProductID{mock: m}
	m.DeleteByUserIDsAndProductIDMock.callArgs = []*ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams{}

	m.ExistByParticipantIDAndProductIDMock = mParticipantPrimeDBMockExistByParticipantIDAndProductID{mock: m}
	m.ExistByParticipantIDAndProductIDMock.callArgs = []*ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams{}

	m.ExistByUserIDAndProductIDMock = mParticipantPrimeDBMockExistByUserIDAndProductID{mock: m}
	m.ExistByUserIDAndProductIDMock.callArgs = []*ParticipantPrimeDBMockExistByUserIDAndProductIDParams{}

	m.GetByGroupIDMock = mParticipantPrimeDBMockGetByGroupID{mock: m}
	m.GetByGroupIDMock.callArgs = []*ParticipantPrimeDBMockGetByGroupIDParams{}

	m.GetByParticipantIDAndProductIDMock = mParticipantPrimeDBMockGetByParticipantIDAndProductID{mock: m}
	m.GetByParticipantIDAndProductIDMock.callArgs = []*ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams{}

	m.GetByProductIDMock = mParticipantPrimeDBMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*ParticipantPrimeDBMockGetByProductIDParams{}

	m.GetByProductIDAndUserEmailMock = mParticipantPrimeDBMockGetByProductIDAndUserEmail{mock: m}
	m.GetByProductIDAndUserEmailMock.callArgs = []*ParticipantPrimeDBMockGetByProductIDAndUserEmailParams{}

	m.GetByRoleIDMock = mParticipantPrimeDBMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*ParticipantPrimeDBMockGetByRoleIDParams{}

	m.GetByUserIDMock = mParticipantPrimeDBMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*ParticipantPrimeDBMockGetByUserIDParams{}

	m.GetByUserIDAndProductIDMock = mParticipantPrimeDBMockGetByUserIDAndProductID{mock: m}
	m.GetByUserIDAndProductIDMock.callArgs = []*ParticipantPrimeDBMockGetByUserIDAndProductIDParams{}

	m.GetByUserIDAndProductIDsMock = mParticipantPrimeDBMockGetByUserIDAndProductIDs{mock: m}
	m.GetByUserIDAndProductIDsMock.callArgs = []*ParticipantPrimeDBMockGetByUserIDAndProductIDsParams{}

	m.GetOwnersByProductIDMock = mParticipantPrimeDBMockGetOwnersByProductID{mock: m}
	m.GetOwnersByProductIDMock.callArgs = []*ParticipantPrimeDBMockGetOwnersByProductIDParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mParticipantPrimeDBMockCreate struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockCreateExpectation
	expectations       []*ParticipantPrimeDBMockCreateExpectation

	callArgs []*ParticipantPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockCreateExpectation specifies expectation struct of the ParticipantPrimeDB.Create
type ParticipantPrimeDBMockCreateExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockCreateParams
	paramPtrs          *ParticipantPrimeDBMockCreateParamPtrs
	expectationOrigins ParticipantPrimeDBMockCreateExpectationOrigins
	results            *ParticipantPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockCreateParams contains parameters of the ParticipantPrimeDB.Create
type ParticipantPrimeDBMockCreateParams struct {
	ctx         context.Context
	participant participantentity.ParticipantCreateData
}

// ParticipantPrimeDBMockCreateParamPtrs contains pointers to parameters of the ParticipantPrimeDB.Create
type ParticipantPrimeDBMockCreateParamPtrs struct {
	ctx         *context.Context
	participant *participantentity.ParticipantCreateData
}

// ParticipantPrimeDBMockCreateResults contains results of the ParticipantPrimeDB.Create
type ParticipantPrimeDBMockCreateResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantPrimeDBMockCreateOrigins contains origins of expectations of the ParticipantPrimeDB.Create
type ParticipantPrimeDBMockCreateExpectationOrigins struct {
	origin            string
	originCtx         string
	originParticipant string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mParticipantPrimeDBMockCreate) Optional() *mParticipantPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ParticipantPrimeDB.Create
func (mmCreate *mParticipantPrimeDBMockCreate) Expect(ctx context.Context, participant participantentity.ParticipantCreateData) *mParticipantPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ParticipantPrimeDBMockCreateParams{ctx, participant}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.Create
func (mmCreate *mParticipantPrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectParticipantParam2 sets up expected param participant for ParticipantPrimeDB.Create
func (mmCreate *mParticipantPrimeDBMockCreate) ExpectParticipantParam2(participant participantentity.ParticipantCreateData) *mParticipantPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.participant = &participant
	mmCreate.defaultExpectation.expectationOrigins.originParticipant = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.Create
func (mmCreate *mParticipantPrimeDBMockCreate) Inspect(f func(ctx context.Context, participant participantentity.ParticipantCreateData)) *mParticipantPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ParticipantPrimeDB.Create
func (mmCreate *mParticipantPrimeDBMockCreate) Return(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ParticipantPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ParticipantPrimeDBMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.Create method
func (mmCreate *mParticipantPrimeDBMockCreate) Set(f func(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ParticipantPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mParticipantPrimeDBMockCreate) When(ctx context.Context, participant participantentity.ParticipantCreateData) *ParticipantPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ParticipantPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ParticipantPrimeDBMockCreateParams{ctx, participant},
		expectationOrigins: ParticipantPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockCreateExpectation) Then(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.Create should be invoked
func (mmCreate *mParticipantPrimeDBMockCreate) Times(n uint64) *mParticipantPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ParticipantPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mParticipantPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.ParticipantPrimeDB
func (mmCreate *ParticipantPrimeDBMock) Create(ctx context.Context, participant participantentity.ParticipantCreateData) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, participant)
	}

	mm_params := ParticipantPrimeDBMockCreateParams{ctx, participant}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockCreateParams{ctx, participant}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("ParticipantPrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participant != nil && !minimock.Equal(*mm_want_ptrs.participant, mm_got.participant) {
				mmCreate.t.Errorf("ParticipantPrimeDBMock.Create got unexpected parameter participant, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originParticipant, *mm_want_ptrs.participant, mm_got.participant, minimock.Diff(*mm_want_ptrs.participant, mm_got.participant))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ParticipantPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ParticipantPrimeDBMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, participant)
	}
	mmCreate.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.Create. %v %v", ctx, participant)
	return
}

// CreateAfterCounter returns a count of finished ParticipantPrimeDBMock.Create invocations
func (mmCreate *ParticipantPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ParticipantPrimeDBMock.Create invocations
func (mmCreate *ParticipantPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mParticipantPrimeDBMockCreate) Calls() []*ParticipantPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mParticipantPrimeDBMockCreateByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation
	expectations       []*ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation

	callArgs []*ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantPrimeDB.CreateByUserIDAndProductIDs
type ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams
	paramPtrs          *ParticipantPrimeDBMockCreateByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantPrimeDBMockCreateByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams contains parameters of the ParticipantPrimeDB.CreateByUserIDAndProductIDs
type ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams struct {
	ctx        context.Context
	userID     int64
	productIDs []int64
}

// ParticipantPrimeDBMockCreateByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantPrimeDB.CreateByUserIDAndProductIDs
type ParticipantPrimeDBMockCreateByUserIDAndProductIDsParamPtrs struct {
	ctx        *context.Context
	userID     *int64
	productIDs *[]int64
}

// ParticipantPrimeDBMockCreateByUserIDAndProductIDsResults contains results of the ParticipantPrimeDB.CreateByUserIDAndProductIDs
type ParticipantPrimeDBMockCreateByUserIDAndProductIDsResults struct {
	err error
}

// ParticipantPrimeDBMockCreateByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantPrimeDB.CreateByUserIDAndProductIDs
type ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originCtx        string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Optional() *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	mmCreateByUserIDAndProductIDs.optional = true
	return mmCreateByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantPrimeDB.CreateByUserIDAndProductIDs
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Expect(ctx context.Context, userID int64, productIDs []int64) *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	if mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation{}
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmCreateByUserIDAndProductIDs.defaultExpectation.params = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams{ctx, userID, productIDs}
	mmCreateByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmCreateByUserIDAndProductIDs.defaultExpectation.params) {
			mmCreateByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmCreateByUserIDAndProductIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.CreateByUserIDAndProductIDs
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	if mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation{}
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsParamPtrs{}
	}
	mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByUserIDAndProductIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantPrimeDB.CreateByUserIDAndProductIDs
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) ExpectUserIDParam2(userID int64) *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	if mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation{}
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsParamPtrs{}
	}
	mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmCreateByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmCreateByUserIDAndProductIDs
}

// ExpectProductIDsParam3 sets up expected param productIDs for ParticipantPrimeDB.CreateByUserIDAndProductIDs
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) ExpectProductIDsParam3(productIDs []int64) *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	if mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation{}
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsParamPtrs{}
	}
	mmCreateByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmCreateByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmCreateByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.CreateByUserIDAndProductIDs
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Inspect(f func(ctx context.Context, userID int64, productIDs []int64)) *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	if mmCreateByUserIDAndProductIDs.mock.inspectFuncCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.CreateByUserIDAndProductIDs")
	}

	mmCreateByUserIDAndProductIDs.mock.inspectFuncCreateByUserIDAndProductIDs = f

	return mmCreateByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantPrimeDB.CreateByUserIDAndProductIDs
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Return(err error) *ParticipantPrimeDBMock {
	if mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndProductIDs.defaultExpectation == nil {
		mmCreateByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation{mock: mmCreateByUserIDAndProductIDs.mock}
	}
	mmCreateByUserIDAndProductIDs.defaultExpectation.results = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsResults{err}
	mmCreateByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.CreateByUserIDAndProductIDs method
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Set(f func(ctx context.Context, userID int64, productIDs []int64) (err error)) *ParticipantPrimeDBMock {
	if mmCreateByUserIDAndProductIDs.defaultExpectation != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.CreateByUserIDAndProductIDs method")
	}

	if len(mmCreateByUserIDAndProductIDs.expectations) > 0 {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.CreateByUserIDAndProductIDs method")
	}

	mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs = f
	mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantPrimeDB.CreateByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) When(ctx context.Context, userID int64, productIDs []int64) *ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation {
	if mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation{
		mock:               mmCreateByUserIDAndProductIDs.mock,
		params:             &ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams{ctx, userID, productIDs},
		expectationOrigins: ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByUserIDAndProductIDs.expectations = append(mmCreateByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.CreateByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockCreateByUserIDAndProductIDsExpectation) Then(err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockCreateByUserIDAndProductIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.CreateByUserIDAndProductIDs should be invoked
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Times(n uint64) *mParticipantPrimeDBMockCreateByUserIDAndProductIDs {
	if n == 0 {
		mmCreateByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantPrimeDBMock.CreateByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByUserIDAndProductIDs.expectedInvocations, n)
	mmCreateByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByUserIDAndProductIDs
}

func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmCreateByUserIDAndProductIDs.expectations) == 0 && mmCreateByUserIDAndProductIDs.defaultExpectation == nil && mmCreateByUserIDAndProductIDs.mock.funcCreateByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByUserIDAndProductIDs.mock.afterCreateByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByUserIDAndProductIDs implements mm_repository.ParticipantPrimeDB
func (mmCreateByUserIDAndProductIDs *ParticipantPrimeDBMock) CreateByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByUserIDAndProductIDs.beforeCreateByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByUserIDAndProductIDs.afterCreateByUserIDAndProductIDsCounter, 1)

	mmCreateByUserIDAndProductIDs.t.Helper()

	if mmCreateByUserIDAndProductIDs.inspectFuncCreateByUserIDAndProductIDs != nil {
		mmCreateByUserIDAndProductIDs.inspectFuncCreateByUserIDAndProductIDs(ctx, userID, productIDs)
	}

	mm_params := ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams{ctx, userID, productIDs}

	// Record call args
	mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.mutex.Lock()
	mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.callArgs = append(mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams{ctx, userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmCreateByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmCreateByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.CreateByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByUserIDAndProductIDs.CreateByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantPrimeDBMock.CreateByUserIDAndProductIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByUserIDAndProductIDs.funcCreateByUserIDAndProductIDs != nil {
		return mmCreateByUserIDAndProductIDs.funcCreateByUserIDAndProductIDs(ctx, userID, productIDs)
	}
	mmCreateByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs. %v %v %v", ctx, userID, productIDs)
	return
}

// CreateByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantPrimeDBMock.CreateByUserIDAndProductIDs invocations
func (mmCreateByUserIDAndProductIDs *ParticipantPrimeDBMock) CreateByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByUserIDAndProductIDs.afterCreateByUserIDAndProductIDsCounter)
}

// CreateByUserIDAndProductIDsBeforeCounter returns a count of ParticipantPrimeDBMock.CreateByUserIDAndProductIDs invocations
func (mmCreateByUserIDAndProductIDs *ParticipantPrimeDBMock) CreateByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByUserIDAndProductIDs.beforeCreateByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByUserIDAndProductIDs *mParticipantPrimeDBMockCreateByUserIDAndProductIDs) Calls() []*ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams {
	mmCreateByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockCreateByUserIDAndProductIDsParams, len(mmCreateByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmCreateByUserIDAndProductIDs.callArgs)

	mmCreateByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByUserIDAndProductIDsDone returns true if the count of the CreateByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockCreateByUserIDAndProductIDsDone() bool {
	if m.CreateByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockCreateByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockCreateByUserIDAndProductIDsInspect() {
	for _, e := range m.CreateByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByUserIDAndProductIDsMock.defaultExpectation != nil && afterCreateByUserIDAndProductIDsCounter < 1 {
		if m.CreateByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs at\n%s", m.CreateByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs at\n%s with params: %#v", m.CreateByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByUserIDAndProductIDs != nil && afterCreateByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs at\n%s", m.funcCreateByUserIDAndProductIDsOrigin)
	}

	if !m.CreateByUserIDAndProductIDsMock.invocationsDone() && afterCreateByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.CreateByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByUserIDAndProductIDsMock.expectedInvocations), m.CreateByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterCreateByUserIDAndProductIDsCounter)
	}
}

type mParticipantPrimeDBMockDelete struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockDeleteExpectation
	expectations       []*ParticipantPrimeDBMockDeleteExpectation

	callArgs []*ParticipantPrimeDBMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockDeleteExpectation specifies expectation struct of the ParticipantPrimeDB.Delete
type ParticipantPrimeDBMockDeleteExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockDeleteParams
	paramPtrs          *ParticipantPrimeDBMockDeleteParamPtrs
	expectationOrigins ParticipantPrimeDBMockDeleteExpectationOrigins
	results            *ParticipantPrimeDBMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockDeleteParams contains parameters of the ParticipantPrimeDB.Delete
type ParticipantPrimeDBMockDeleteParams struct {
	ctx           context.Context
	participantID int64
	productID     int64
}

// ParticipantPrimeDBMockDeleteParamPtrs contains pointers to parameters of the ParticipantPrimeDB.Delete
type ParticipantPrimeDBMockDeleteParamPtrs struct {
	ctx           *context.Context
	participantID *int64
	productID     *int64
}

// ParticipantPrimeDBMockDeleteResults contains results of the ParticipantPrimeDB.Delete
type ParticipantPrimeDBMockDeleteResults struct {
	err error
}

// ParticipantPrimeDBMockDeleteOrigins contains origins of expectations of the ParticipantPrimeDB.Delete
type ParticipantPrimeDBMockDeleteExpectationOrigins struct {
	origin              string
	originCtx           string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mParticipantPrimeDBMockDelete) Optional() *mParticipantPrimeDBMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for ParticipantPrimeDB.Delete
func (mmDelete *mParticipantPrimeDBMockDelete) Expect(ctx context.Context, participantID int64, productID int64) *mParticipantPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &ParticipantPrimeDBMockDeleteParams{ctx, participantID, productID}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.Delete
func (mmDelete *mParticipantPrimeDBMockDelete) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.ctx = &ctx
	mmDelete.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectParticipantIDParam2 sets up expected param participantID for ParticipantPrimeDB.Delete
func (mmDelete *mParticipantPrimeDBMockDelete) ExpectParticipantIDParam2(participantID int64) *mParticipantPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.participantID = &participantID
	mmDelete.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectProductIDParam3 sets up expected param productID for ParticipantPrimeDB.Delete
func (mmDelete *mParticipantPrimeDBMockDelete) ExpectProductIDParam3(productID int64) *mParticipantPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.productID = &productID
	mmDelete.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.Delete
func (mmDelete *mParticipantPrimeDBMockDelete) Inspect(f func(ctx context.Context, participantID int64, productID int64)) *mParticipantPrimeDBMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by ParticipantPrimeDB.Delete
func (mmDelete *mParticipantPrimeDBMockDelete) Return(err error) *ParticipantPrimeDBMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ParticipantPrimeDBMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &ParticipantPrimeDBMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.Delete method
func (mmDelete *mParticipantPrimeDBMockDelete) Set(f func(ctx context.Context, participantID int64, productID int64) (err error)) *ParticipantPrimeDBMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the ParticipantPrimeDB.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mParticipantPrimeDBMockDelete) When(ctx context.Context, participantID int64, productID int64) *ParticipantPrimeDBMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ParticipantPrimeDBMock.Delete mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &ParticipantPrimeDBMockDeleteParams{ctx, participantID, productID},
		expectationOrigins: ParticipantPrimeDBMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.Delete return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockDeleteExpectation) Then(err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockDeleteResults{err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.Delete should be invoked
func (mmDelete *mParticipantPrimeDBMockDelete) Times(n uint64) *mParticipantPrimeDBMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of ParticipantPrimeDBMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mParticipantPrimeDBMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_repository.ParticipantPrimeDB
func (mmDelete *ParticipantPrimeDBMock) Delete(ctx context.Context, participantID int64, productID int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(ctx, participantID, productID)
	}

	mm_params := ParticipantPrimeDBMockDeleteParams{ctx, participantID, productID}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockDeleteParams{ctx, participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDelete.t.Errorf("ParticipantPrimeDBMock.Delete got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmDelete.t.Errorf("ParticipantPrimeDBMock.Delete got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmDelete.t.Errorf("ParticipantPrimeDBMock.Delete got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("ParticipantPrimeDBMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the ParticipantPrimeDBMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(ctx, participantID, productID)
	}
	mmDelete.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.Delete. %v %v %v", ctx, participantID, productID)
	return
}

// DeleteAfterCounter returns a count of finished ParticipantPrimeDBMock.Delete invocations
func (mmDelete *ParticipantPrimeDBMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of ParticipantPrimeDBMock.Delete invocations
func (mmDelete *ParticipantPrimeDBMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mParticipantPrimeDBMockDelete) Calls() []*ParticipantPrimeDBMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation
	expectations       []*ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation

	callArgs []*ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation specifies expectation struct of the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
type ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams
	paramPtrs          *ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs
	expectationOrigins ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectationOrigins
	results            *ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams contains parameters of the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
type ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams struct {
	ctx      context.Context
	userID   int64
	groupIDs []int64
}

// ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs contains pointers to parameters of the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
type ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs struct {
	ctx      *context.Context
	userID   *int64
	groupIDs *[]int64
}

// ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsResults contains results of the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
type ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsResults struct {
	err error
}

// ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsOrigins contains origins of expectations of the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
type ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectationOrigins struct {
	origin         string
	originCtx      string
	originUserID   string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Optional() *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	mmDeleteByUserIDAndGroupIDs.optional = true
	return mmDeleteByUserIDAndGroupIDs
}

// Expect sets up expected params for ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Expect(ctx context.Context, userID int64, groupIDs []int64) *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndGroupIDs.defaultExpectation.params = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndGroupIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndGroupIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndGroupIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) ExpectUserIDParam2(userID int64) *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) ExpectGroupIDsParam3(groupIDs []int64) *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Inspect(f func(ctx context.Context, userID int64, groupIDs []int64)) *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs")
	}

	mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs = f

	return mmDeleteByUserIDAndGroupIDs
}

// Return sets up results that will be returned by ParticipantPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Return(err error) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{mock: mmDeleteByUserIDAndGroupIDs.mock}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.results = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsResults{err}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs method
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Set(f func(ctx context.Context, userID int64, groupIDs []int64) (err error)) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDAndGroupIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs method")
	}

	if len(mmDeleteByUserIDAndGroupIDs.expectations) > 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs method")
	}

	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs = f
	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// When sets expectation for the ParticipantPrimeDB.DeleteByUserIDAndGroupIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) When(ctx context.Context, userID int64, groupIDs []int64) *ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{
		mock:               mmDeleteByUserIDAndGroupIDs.mock,
		params:             &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs},
		expectationOrigins: ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndGroupIDs.expectations = append(mmDeleteByUserIDAndGroupIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.DeleteByUserIDAndGroupIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsExpectation) Then(err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.DeleteByUserIDAndGroupIDs should be invoked
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Times(n uint64) *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs {
	if n == 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Times of ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations, n)
	mmDeleteByUserIDAndGroupIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs
}

func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndGroupIDs.expectations) == 0 && mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil && mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.mock.afterDeleteByUserIDAndGroupIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndGroupIDs implements mm_repository.ParticipantPrimeDB
func (mmDeleteByUserIDAndGroupIDs *ParticipantPrimeDBMock) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter, 1)

	mmDeleteByUserIDAndGroupIDs.t.Helper()

	if mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}

	mm_params := ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

	// Record call args
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Lock()
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs = append(mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndGroupIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndGroupIDs.t.Fatal("No results are set for the ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs != nil {
		return mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}
	mmDeleteByUserIDAndGroupIDs.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs. %v %v %v", ctx, userID, groupIDs)
	return
}

// DeleteByUserIDAndGroupIDsAfterCounter returns a count of finished ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *ParticipantPrimeDBMock) DeleteByUserIDAndGroupIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter)
}

// DeleteByUserIDAndGroupIDsBeforeCounter returns a count of ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *ParticipantPrimeDBMock) DeleteByUserIDAndGroupIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndGroupIDs *mParticipantPrimeDBMockDeleteByUserIDAndGroupIDs) Calls() []*ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams {
	mmDeleteByUserIDAndGroupIDs.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockDeleteByUserIDAndGroupIDsParams, len(mmDeleteByUserIDAndGroupIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndGroupIDs.callArgs)

	mmDeleteByUserIDAndGroupIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndGroupIDsDone returns true if the count of the DeleteByUserIDAndGroupIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDAndGroupIDsDone() bool {
	if m.DeleteByUserIDAndGroupIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndGroupIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndGroupIDsInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDAndGroupIDsInspect() {
	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndGroupIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndGroupIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndGroupIDs != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s", m.funcDeleteByUserIDAndGroupIDsOrigin)
	}

	if !m.DeleteByUserIDAndGroupIDsMock.invocationsDone() && afterDeleteByUserIDAndGroupIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndGroupIDsMock.expectedInvocations), m.DeleteByUserIDAndGroupIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndGroupIDsCounter)
	}
}

type mParticipantPrimeDBMockDeleteByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation
	expectations       []*ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation

	callArgs []*ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantPrimeDB.DeleteByUserIDAndProductIDs
type ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams
	paramPtrs          *ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantPrimeDBMockDeleteByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams contains parameters of the ParticipantPrimeDB.DeleteByUserIDAndProductIDs
type ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams struct {
	ctx        context.Context
	userID     int64
	productIDs []int64
}

// ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantPrimeDB.DeleteByUserIDAndProductIDs
type ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParamPtrs struct {
	ctx        *context.Context
	userID     *int64
	productIDs *[]int64
}

// ParticipantPrimeDBMockDeleteByUserIDAndProductIDsResults contains results of the ParticipantPrimeDB.DeleteByUserIDAndProductIDs
type ParticipantPrimeDBMockDeleteByUserIDAndProductIDsResults struct {
	err error
}

// ParticipantPrimeDBMockDeleteByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantPrimeDB.DeleteByUserIDAndProductIDs
type ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originCtx        string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Optional() *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	mmDeleteByUserIDAndProductIDs.optional = true
	return mmDeleteByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantPrimeDB.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Expect(ctx context.Context, userID int64, productIDs []int64) *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndProductIDs.defaultExpectation.params = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndProductIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndProductIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantPrimeDB.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) ExpectUserIDParam2(userID int64) *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// ExpectProductIDsParam3 sets up expected param productIDs for ParticipantPrimeDB.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) ExpectProductIDsParam3(productIDs []int64) *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation{}
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParamPtrs{}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmDeleteByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Inspect(f func(ctx context.Context, userID int64, productIDs []int64)) *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	if mmDeleteByUserIDAndProductIDs.mock.inspectFuncDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs")
	}

	mmDeleteByUserIDAndProductIDs.mock.inspectFuncDeleteByUserIDAndProductIDs = f

	return mmDeleteByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantPrimeDB.DeleteByUserIDAndProductIDs
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Return(err error) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndProductIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation{mock: mmDeleteByUserIDAndProductIDs.mock}
	}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.results = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsResults{err}
	mmDeleteByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.DeleteByUserIDAndProductIDs method
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Set(f func(ctx context.Context, userID int64, productIDs []int64) (err error)) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDAndProductIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.DeleteByUserIDAndProductIDs method")
	}

	if len(mmDeleteByUserIDAndProductIDs.expectations) > 0 {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.DeleteByUserIDAndProductIDs method")
	}

	mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs = f
	mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantPrimeDB.DeleteByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) When(ctx context.Context, userID int64, productIDs []int64) *ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation {
	if mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation{
		mock:               mmDeleteByUserIDAndProductIDs.mock,
		params:             &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs},
		expectationOrigins: ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndProductIDs.expectations = append(mmDeleteByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.DeleteByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockDeleteByUserIDAndProductIDsExpectation) Then(err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockDeleteByUserIDAndProductIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.DeleteByUserIDAndProductIDs should be invoked
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Times(n uint64) *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs {
	if n == 0 {
		mmDeleteByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndProductIDs.expectedInvocations, n)
	mmDeleteByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndProductIDs
}

func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndProductIDs.expectations) == 0 && mmDeleteByUserIDAndProductIDs.defaultExpectation == nil && mmDeleteByUserIDAndProductIDs.mock.funcDeleteByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.mock.afterDeleteByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndProductIDs implements mm_repository.ParticipantPrimeDB
func (mmDeleteByUserIDAndProductIDs *ParticipantPrimeDBMock) DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.beforeDeleteByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.afterDeleteByUserIDAndProductIDsCounter, 1)

	mmDeleteByUserIDAndProductIDs.t.Helper()

	if mmDeleteByUserIDAndProductIDs.inspectFuncDeleteByUserIDAndProductIDs != nil {
		mmDeleteByUserIDAndProductIDs.inspectFuncDeleteByUserIDAndProductIDs(ctx, userID, productIDs)
	}

	mm_params := ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs}

	// Record call args
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.mutex.Lock()
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.callArgs = append(mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams{ctx, userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndProductIDs.DeleteByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndProductIDs.funcDeleteByUserIDAndProductIDs != nil {
		return mmDeleteByUserIDAndProductIDs.funcDeleteByUserIDAndProductIDs(ctx, userID, productIDs)
	}
	mmDeleteByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs. %v %v %v", ctx, userID, productIDs)
	return
}

// DeleteByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs invocations
func (mmDeleteByUserIDAndProductIDs *ParticipantPrimeDBMock) DeleteByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.afterDeleteByUserIDAndProductIDsCounter)
}

// DeleteByUserIDAndProductIDsBeforeCounter returns a count of ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs invocations
func (mmDeleteByUserIDAndProductIDs *ParticipantPrimeDBMock) DeleteByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndProductIDs.beforeDeleteByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndProductIDs *mParticipantPrimeDBMockDeleteByUserIDAndProductIDs) Calls() []*ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams {
	mmDeleteByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockDeleteByUserIDAndProductIDsParams, len(mmDeleteByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndProductIDs.callArgs)

	mmDeleteByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndProductIDsDone returns true if the count of the DeleteByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDAndProductIDsDone() bool {
	if m.DeleteByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDAndProductIDsInspect() {
	for _, e := range m.DeleteByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndProductIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndProductIDsCounter < 1 {
		if m.DeleteByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs at\n%s", m.DeleteByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs at\n%s with params: %#v", m.DeleteByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndProductIDs != nil && afterDeleteByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs at\n%s", m.funcDeleteByUserIDAndProductIDsOrigin)
	}

	if !m.DeleteByUserIDAndProductIDsMock.invocationsDone() && afterDeleteByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.DeleteByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndProductIDsMock.expectedInvocations), m.DeleteByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndProductIDsCounter)
	}
}

type mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation
	expectations       []*ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation

	callArgs []*ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation specifies expectation struct of the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
type ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams
	paramPtrs          *ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs
	expectationOrigins ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectationOrigins
	results            *ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams contains parameters of the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
type ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams struct {
	ctx     context.Context
	userID  int64
	roleIDs []int64
}

// ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs contains pointers to parameters of the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
type ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs struct {
	ctx     *context.Context
	userID  *int64
	roleIDs *[]int64
}

// ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsResults contains results of the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
type ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsResults struct {
	err error
}

// ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsOrigins contains origins of expectations of the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
type ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originUserID  string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Optional() *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	mmDeleteByUserIDAndRoleIDs.optional = true
	return mmDeleteByUserIDAndRoleIDs
}

// Expect sets up expected params for ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Expect(ctx context.Context, userID int64, roleIDs []int64) *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndRoleIDs.defaultExpectation.params = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndRoleIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndRoleIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndRoleIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectUserIDParam2 sets up expected param userID for ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) ExpectUserIDParam2(userID int64) *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) ExpectRoleIDsParam3(roleIDs []int64) *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Inspect(f func(ctx context.Context, userID int64, roleIDs []int64)) *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs")
	}

	mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs = f

	return mmDeleteByUserIDAndRoleIDs
}

// Return sets up results that will be returned by ParticipantPrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Return(err error) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation{mock: mmDeleteByUserIDAndRoleIDs.mock}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.results = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsResults{err}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs method
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Set(f func(ctx context.Context, userID int64, roleIDs []int64) (err error)) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDAndRoleIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs method")
	}

	if len(mmDeleteByUserIDAndRoleIDs.expectations) > 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs method")
	}

	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs = f
	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// When sets expectation for the ParticipantPrimeDB.DeleteByUserIDAndRoleIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) When(ctx context.Context, userID int64, roleIDs []int64) *ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation{
		mock:               mmDeleteByUserIDAndRoleIDs.mock,
		params:             &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs},
		expectationOrigins: ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndRoleIDs.expectations = append(mmDeleteByUserIDAndRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.DeleteByUserIDAndRoleIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsExpectation) Then(err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsResults{err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.DeleteByUserIDAndRoleIDs should be invoked
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Times(n uint64) *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs {
	if n == 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Times of ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations, n)
	mmDeleteByUserIDAndRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs
}

func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndRoleIDs.expectations) == 0 && mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil && mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.mock.afterDeleteByUserIDAndRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndRoleIDs implements mm_repository.ParticipantPrimeDB
func (mmDeleteByUserIDAndRoleIDs *ParticipantPrimeDBMock) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter, 1)

	mmDeleteByUserIDAndRoleIDs.t.Helper()

	if mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}

	mm_params := ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

	// Record call args
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Lock()
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs = append(mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndRoleIDs.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndRoleIDs.t.Fatal("No results are set for the ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs != nil {
		return mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}
	mmDeleteByUserIDAndRoleIDs.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs. %v %v %v", ctx, userID, roleIDs)
	return
}

// DeleteByUserIDAndRoleIDsAfterCounter returns a count of finished ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *ParticipantPrimeDBMock) DeleteByUserIDAndRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter)
}

// DeleteByUserIDAndRoleIDsBeforeCounter returns a count of ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *ParticipantPrimeDBMock) DeleteByUserIDAndRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndRoleIDs *mParticipantPrimeDBMockDeleteByUserIDAndRoleIDs) Calls() []*ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams {
	mmDeleteByUserIDAndRoleIDs.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockDeleteByUserIDAndRoleIDsParams, len(mmDeleteByUserIDAndRoleIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndRoleIDs.callArgs)

	mmDeleteByUserIDAndRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndRoleIDsDone returns true if the count of the DeleteByUserIDAndRoleIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDAndRoleIDsDone() bool {
	if m.DeleteByUserIDAndRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndRoleIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndRoleIDsInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDAndRoleIDsInspect() {
	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndRoleIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndRoleIDs != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s", m.funcDeleteByUserIDAndRoleIDsOrigin)
	}

	if !m.DeleteByUserIDAndRoleIDsMock.invocationsDone() && afterDeleteByUserIDAndRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndRoleIDsMock.expectedInvocations), m.DeleteByUserIDAndRoleIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndRoleIDsCounter)
	}
}

type mParticipantPrimeDBMockDeleteByUserIDsAndProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation
	expectations       []*ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation

	callArgs []*ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.DeleteByUserIDsAndProductID
type ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams
	paramPtrs          *ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockDeleteByUserIDsAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams contains parameters of the ParticipantPrimeDB.DeleteByUserIDsAndProductID
type ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams struct {
	ctx       context.Context
	productID int64
	userID    []int64
}

// ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.DeleteByUserIDsAndProductID
type ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParamPtrs struct {
	ctx       *context.Context
	productID *int64
	userID    *[]int64
}

// ParticipantPrimeDBMockDeleteByUserIDsAndProductIDResults contains results of the ParticipantPrimeDB.DeleteByUserIDsAndProductID
type ParticipantPrimeDBMockDeleteByUserIDsAndProductIDResults struct {
	err error
}

// ParticipantPrimeDBMockDeleteByUserIDsAndProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.DeleteByUserIDsAndProductID
type ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectationOrigins struct {
	origin          string
	originCtx       string
	originProductID string
	originUserID    string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Optional() *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	mmDeleteByUserIDsAndProductID.optional = true
	return mmDeleteByUserIDsAndProductID
}

// Expect sets up expected params for ParticipantPrimeDB.DeleteByUserIDsAndProductID
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Expect(ctx context.Context, productID int64, userID []int64) *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	if mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Set")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation{}
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDsAndProductID.defaultExpectation.params = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams{ctx, productID, userID}
	mmDeleteByUserIDsAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDsAndProductID.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDsAndProductID.defaultExpectation.params) {
			mmDeleteByUserIDsAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDsAndProductID.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDsAndProductID
}

// ExpectCtxParam1 sets up expected param ctx for ParticipantPrimeDB.DeleteByUserIDsAndProductID
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) ExpectCtxParam1(ctx context.Context) *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	if mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Set")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation{}
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.params != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Expect")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParamPtrs{}
	}
	mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDsAndProductID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDsAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantPrimeDB.DeleteByUserIDsAndProductID
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) ExpectProductIDParam2(productID int64) *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	if mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Set")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation{}
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.params != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Expect")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParamPtrs{}
	}
	mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmDeleteByUserIDsAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmDeleteByUserIDsAndProductID
}

// ExpectUserIDParam3 sets up expected param userID for ParticipantPrimeDB.DeleteByUserIDsAndProductID
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) ExpectUserIDParam3(userID []int64) *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	if mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Set")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation{}
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.params != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Expect")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParamPtrs{}
	}
	mmDeleteByUserIDsAndProductID.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDsAndProductID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDsAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.DeleteByUserIDsAndProductID
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Inspect(f func(ctx context.Context, productID int64, userID []int64)) *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	if mmDeleteByUserIDsAndProductID.mock.inspectFuncDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.DeleteByUserIDsAndProductID")
	}

	mmDeleteByUserIDsAndProductID.mock.inspectFuncDeleteByUserIDsAndProductID = f

	return mmDeleteByUserIDsAndProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.DeleteByUserIDsAndProductID
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Return(err error) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Set")
	}

	if mmDeleteByUserIDsAndProductID.defaultExpectation == nil {
		mmDeleteByUserIDsAndProductID.defaultExpectation = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation{mock: mmDeleteByUserIDsAndProductID.mock}
	}
	mmDeleteByUserIDsAndProductID.defaultExpectation.results = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDResults{err}
	mmDeleteByUserIDsAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDsAndProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.DeleteByUserIDsAndProductID method
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Set(f func(ctx context.Context, productID int64, userID []int64) (err error)) *ParticipantPrimeDBMock {
	if mmDeleteByUserIDsAndProductID.defaultExpectation != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.DeleteByUserIDsAndProductID method")
	}

	if len(mmDeleteByUserIDsAndProductID.expectations) > 0 {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.DeleteByUserIDsAndProductID method")
	}

	mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID = f
	mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDsAndProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.DeleteByUserIDsAndProductID which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) When(ctx context.Context, productID int64, userID []int64) *ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation {
	if mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation{
		mock:               mmDeleteByUserIDsAndProductID.mock,
		params:             &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams{ctx, productID, userID},
		expectationOrigins: ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDsAndProductID.expectations = append(mmDeleteByUserIDsAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.DeleteByUserIDsAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockDeleteByUserIDsAndProductIDExpectation) Then(err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockDeleteByUserIDsAndProductIDResults{err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.DeleteByUserIDsAndProductID should be invoked
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Times(n uint64) *mParticipantPrimeDBMockDeleteByUserIDsAndProductID {
	if n == 0 {
		mmDeleteByUserIDsAndProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.DeleteByUserIDsAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDsAndProductID.expectedInvocations, n)
	mmDeleteByUserIDsAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDsAndProductID
}

func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) invocationsDone() bool {
	if len(mmDeleteByUserIDsAndProductID.expectations) == 0 && mmDeleteByUserIDsAndProductID.defaultExpectation == nil && mmDeleteByUserIDsAndProductID.mock.funcDeleteByUserIDsAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDsAndProductID.mock.afterDeleteByUserIDsAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDsAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDsAndProductID implements mm_repository.ParticipantPrimeDB
func (mmDeleteByUserIDsAndProductID *ParticipantPrimeDBMock) DeleteByUserIDsAndProductID(ctx context.Context, productID int64, userID []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDsAndProductID.beforeDeleteByUserIDsAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDsAndProductID.afterDeleteByUserIDsAndProductIDCounter, 1)

	mmDeleteByUserIDsAndProductID.t.Helper()

	if mmDeleteByUserIDsAndProductID.inspectFuncDeleteByUserIDsAndProductID != nil {
		mmDeleteByUserIDsAndProductID.inspectFuncDeleteByUserIDsAndProductID(ctx, productID, userID)
	}

	mm_params := ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams{ctx, productID, userID}

	// Record call args
	mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.mutex.Lock()
	mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.callArgs = append(mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.callArgs, &mm_params)
	mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams{ctx, productID, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDsAndProductID.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmDeleteByUserIDsAndProductID.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDsAndProductID.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDsAndProductID.t.Errorf("ParticipantPrimeDBMock.DeleteByUserIDsAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDsAndProductID.DeleteByUserIDsAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDsAndProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.DeleteByUserIDsAndProductID")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDsAndProductID.funcDeleteByUserIDsAndProductID != nil {
		return mmDeleteByUserIDsAndProductID.funcDeleteByUserIDsAndProductID(ctx, productID, userID)
	}
	mmDeleteByUserIDsAndProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID. %v %v %v", ctx, productID, userID)
	return
}

// DeleteByUserIDsAndProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.DeleteByUserIDsAndProductID invocations
func (mmDeleteByUserIDsAndProductID *ParticipantPrimeDBMock) DeleteByUserIDsAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDsAndProductID.afterDeleteByUserIDsAndProductIDCounter)
}

// DeleteByUserIDsAndProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.DeleteByUserIDsAndProductID invocations
func (mmDeleteByUserIDsAndProductID *ParticipantPrimeDBMock) DeleteByUserIDsAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDsAndProductID.beforeDeleteByUserIDsAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDsAndProductID *mParticipantPrimeDBMockDeleteByUserIDsAndProductID) Calls() []*ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams {
	mmDeleteByUserIDsAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockDeleteByUserIDsAndProductIDParams, len(mmDeleteByUserIDsAndProductID.callArgs))
	copy(argCopy, mmDeleteByUserIDsAndProductID.callArgs)

	mmDeleteByUserIDsAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDsAndProductIDDone returns true if the count of the DeleteByUserIDsAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDsAndProductIDDone() bool {
	if m.DeleteByUserIDsAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDsAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDsAndProductIDMock.invocationsDone()
}

// MinimockDeleteByUserIDsAndProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockDeleteByUserIDsAndProductIDInspect() {
	for _, e := range m.DeleteByUserIDsAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDsAndProductIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDsAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDsAndProductIDMock.defaultExpectation != nil && afterDeleteByUserIDsAndProductIDCounter < 1 {
		if m.DeleteByUserIDsAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID at\n%s", m.DeleteByUserIDsAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID at\n%s with params: %#v", m.DeleteByUserIDsAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDsAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDsAndProductID != nil && afterDeleteByUserIDsAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID at\n%s", m.funcDeleteByUserIDsAndProductIDOrigin)
	}

	if !m.DeleteByUserIDsAndProductIDMock.invocationsDone() && afterDeleteByUserIDsAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.DeleteByUserIDsAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDsAndProductIDMock.expectedInvocations), m.DeleteByUserIDsAndProductIDMock.expectedInvocationsOrigin, afterDeleteByUserIDsAndProductIDCounter)
	}
}

type mParticipantPrimeDBMockExistByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.ExistByParticipantIDAndProductID
type ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantPrimeDBMockExistByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockExistByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams contains parameters of the ParticipantPrimeDB.ExistByParticipantIDAndProductID
type ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantPrimeDBMockExistByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.ExistByParticipantIDAndProductID
type ParticipantPrimeDBMockExistByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantPrimeDBMockExistByParticipantIDAndProductIDResults contains results of the ParticipantPrimeDB.ExistByParticipantIDAndProductID
type ParticipantPrimeDBMockExistByParticipantIDAndProductIDResults struct {
	b1  bool
	err error
}

// ParticipantPrimeDBMockExistByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.ExistByParticipantIDAndProductID
type ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Optional() *mParticipantPrimeDBMockExistByParticipantIDAndProductID {
	mmExistByParticipantIDAndProductID.optional = true
	return mmExistByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantPrimeDB.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantPrimeDBMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmExistByParticipantIDAndProductID.defaultExpectation.params = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams{participantID, productID}
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmExistByParticipantIDAndProductID.defaultExpectation.params) {
			mmExistByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmExistByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantPrimeDB.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantPrimeDBMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDParamPtrs{}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmExistByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantPrimeDB.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantPrimeDBMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation{}
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDParamPtrs{}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmExistByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmExistByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantPrimeDBMockExistByParticipantIDAndProductID {
	if mmExistByParticipantIDAndProductID.mock.inspectFuncExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.ExistByParticipantIDAndProductID")
	}

	mmExistByParticipantIDAndProductID.mock.inspectFuncExistByParticipantIDAndProductID = f

	return mmExistByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.ExistByParticipantIDAndProductID
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Return(b1 bool, err error) *ParticipantPrimeDBMock {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	if mmExistByParticipantIDAndProductID.defaultExpectation == nil {
		mmExistByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation{mock: mmExistByParticipantIDAndProductID.mock}
	}
	mmExistByParticipantIDAndProductID.defaultExpectation.results = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDResults{b1, err}
	mmExistByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.ExistByParticipantIDAndProductID method
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (b1 bool, err error)) *ParticipantPrimeDBMock {
	if mmExistByParticipantIDAndProductID.defaultExpectation != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.ExistByParticipantIDAndProductID method")
	}

	if len(mmExistByParticipantIDAndProductID.expectations) > 0 {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.ExistByParticipantIDAndProductID method")
	}

	mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID = f
	mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.ExistByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation {
	if mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation{
		mock:               mmExistByParticipantIDAndProductID.mock,
		params:             &ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByParticipantIDAndProductID.expectations = append(mmExistByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.ExistByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockExistByParticipantIDAndProductIDExpectation) Then(b1 bool, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockExistByParticipantIDAndProductIDResults{b1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.ExistByParticipantIDAndProductID should be invoked
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Times(n uint64) *mParticipantPrimeDBMockExistByParticipantIDAndProductID {
	if n == 0 {
		mmExistByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.ExistByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByParticipantIDAndProductID.expectedInvocations, n)
	mmExistByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByParticipantIDAndProductID
}

func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmExistByParticipantIDAndProductID.expectations) == 0 && mmExistByParticipantIDAndProductID.defaultExpectation == nil && mmExistByParticipantIDAndProductID.mock.funcExistByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.mock.afterExistByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByParticipantIDAndProductID implements mm_repository.ParticipantPrimeDB
func (mmExistByParticipantIDAndProductID *ParticipantPrimeDBMock) ExistByParticipantIDAndProductID(participantID int64, productID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.beforeExistByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.afterExistByParticipantIDAndProductIDCounter, 1)

	mmExistByParticipantIDAndProductID.t.Helper()

	if mmExistByParticipantIDAndProductID.inspectFuncExistByParticipantIDAndProductID != nil {
		mmExistByParticipantIDAndProductID.inspectFuncExistByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.mutex.Lock()
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.callArgs = append(mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmExistByParticipantIDAndProductID.t.Errorf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmExistByParticipantIDAndProductID.t.Errorf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByParticipantIDAndProductID.t.Errorf("ParticipantPrimeDBMock.ExistByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByParticipantIDAndProductID.ExistByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.ExistByParticipantIDAndProductID")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByParticipantIDAndProductID.funcExistByParticipantIDAndProductID != nil {
		return mmExistByParticipantIDAndProductID.funcExistByParticipantIDAndProductID(participantID, productID)
	}
	mmExistByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// ExistByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.ExistByParticipantIDAndProductID invocations
func (mmExistByParticipantIDAndProductID *ParticipantPrimeDBMock) ExistByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.afterExistByParticipantIDAndProductIDCounter)
}

// ExistByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.ExistByParticipantIDAndProductID invocations
func (mmExistByParticipantIDAndProductID *ParticipantPrimeDBMock) ExistByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByParticipantIDAndProductID.beforeExistByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByParticipantIDAndProductID *mParticipantPrimeDBMockExistByParticipantIDAndProductID) Calls() []*ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams {
	mmExistByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockExistByParticipantIDAndProductIDParams, len(mmExistByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmExistByParticipantIDAndProductID.callArgs)

	mmExistByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockExistByParticipantIDAndProductIDDone returns true if the count of the ExistByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockExistByParticipantIDAndProductIDDone() bool {
	if m.ExistByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockExistByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockExistByParticipantIDAndProductIDInspect() {
	for _, e := range m.ExistByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterExistByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByParticipantIDAndProductIDMock.defaultExpectation != nil && afterExistByParticipantIDAndProductIDCounter < 1 {
		if m.ExistByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID at\n%s", m.ExistByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID at\n%s with params: %#v", m.ExistByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.ExistByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByParticipantIDAndProductID != nil && afterExistByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID at\n%s", m.funcExistByParticipantIDAndProductIDOrigin)
	}

	if !m.ExistByParticipantIDAndProductIDMock.invocationsDone() && afterExistByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.ExistByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByParticipantIDAndProductIDMock.expectedInvocations), m.ExistByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterExistByParticipantIDAndProductIDCounter)
	}
}

type mParticipantPrimeDBMockExistByUserIDAndProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation
	expectations       []*ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation

	callArgs []*ParticipantPrimeDBMockExistByUserIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.ExistByUserIDAndProductID
type ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockExistByUserIDAndProductIDParams
	paramPtrs          *ParticipantPrimeDBMockExistByUserIDAndProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockExistByUserIDAndProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockExistByUserIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockExistByUserIDAndProductIDParams contains parameters of the ParticipantPrimeDB.ExistByUserIDAndProductID
type ParticipantPrimeDBMockExistByUserIDAndProductIDParams struct {
	userID    int64
	productID int64
}

// ParticipantPrimeDBMockExistByUserIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.ExistByUserIDAndProductID
type ParticipantPrimeDBMockExistByUserIDAndProductIDParamPtrs struct {
	userID    *int64
	productID *int64
}

// ParticipantPrimeDBMockExistByUserIDAndProductIDResults contains results of the ParticipantPrimeDB.ExistByUserIDAndProductID
type ParticipantPrimeDBMockExistByUserIDAndProductIDResults struct {
	b1  bool
	err error
}

// ParticipantPrimeDBMockExistByUserIDAndProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.ExistByUserIDAndProductID
type ParticipantPrimeDBMockExistByUserIDAndProductIDExpectationOrigins struct {
	origin          string
	originUserID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Optional() *mParticipantPrimeDBMockExistByUserIDAndProductID {
	mmExistByUserIDAndProductID.optional = true
	return mmExistByUserIDAndProductID
}

// Expect sets up expected params for ParticipantPrimeDB.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Expect(userID int64, productID int64) *mParticipantPrimeDBMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation{}
	}

	if mmExistByUserIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by ExpectParams functions")
	}

	mmExistByUserIDAndProductID.defaultExpectation.params = &ParticipantPrimeDBMockExistByUserIDAndProductIDParams{userID, productID}
	mmExistByUserIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByUserIDAndProductID.expectations {
		if minimock.Equal(e.params, mmExistByUserIDAndProductID.defaultExpectation.params) {
			mmExistByUserIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByUserIDAndProductID.defaultExpectation.params)
		}
	}

	return mmExistByUserIDAndProductID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantPrimeDB.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) ExpectUserIDParam1(userID int64) *mParticipantPrimeDBMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation{}
	}

	if mmExistByUserIDAndProductID.defaultExpectation.params != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Expect")
	}

	if mmExistByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockExistByUserIDAndProductIDParamPtrs{}
	}
	mmExistByUserIDAndProductID.defaultExpectation.paramPtrs.userID = &userID
	mmExistByUserIDAndProductID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmExistByUserIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantPrimeDB.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantPrimeDBMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation{}
	}

	if mmExistByUserIDAndProductID.defaultExpectation.params != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Expect")
	}

	if mmExistByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmExistByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockExistByUserIDAndProductIDParamPtrs{}
	}
	mmExistByUserIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmExistByUserIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmExistByUserIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Inspect(f func(userID int64, productID int64)) *mParticipantPrimeDBMockExistByUserIDAndProductID {
	if mmExistByUserIDAndProductID.mock.inspectFuncExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.ExistByUserIDAndProductID")
	}

	mmExistByUserIDAndProductID.mock.inspectFuncExistByUserIDAndProductID = f

	return mmExistByUserIDAndProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.ExistByUserIDAndProductID
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Return(b1 bool, err error) *ParticipantPrimeDBMock {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	if mmExistByUserIDAndProductID.defaultExpectation == nil {
		mmExistByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation{mock: mmExistByUserIDAndProductID.mock}
	}
	mmExistByUserIDAndProductID.defaultExpectation.results = &ParticipantPrimeDBMockExistByUserIDAndProductIDResults{b1, err}
	mmExistByUserIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByUserIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.ExistByUserIDAndProductID method
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Set(f func(userID int64, productID int64) (b1 bool, err error)) *ParticipantPrimeDBMock {
	if mmExistByUserIDAndProductID.defaultExpectation != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.ExistByUserIDAndProductID method")
	}

	if len(mmExistByUserIDAndProductID.expectations) > 0 {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.ExistByUserIDAndProductID method")
	}

	mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID = f
	mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmExistByUserIDAndProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.ExistByUserIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) When(userID int64, productID int64) *ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation {
	if mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.ExistByUserIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation{
		mock:               mmExistByUserIDAndProductID.mock,
		params:             &ParticipantPrimeDBMockExistByUserIDAndProductIDParams{userID, productID},
		expectationOrigins: ParticipantPrimeDBMockExistByUserIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByUserIDAndProductID.expectations = append(mmExistByUserIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.ExistByUserIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockExistByUserIDAndProductIDExpectation) Then(b1 bool, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockExistByUserIDAndProductIDResults{b1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.ExistByUserIDAndProductID should be invoked
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Times(n uint64) *mParticipantPrimeDBMockExistByUserIDAndProductID {
	if n == 0 {
		mmExistByUserIDAndProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.ExistByUserIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByUserIDAndProductID.expectedInvocations, n)
	mmExistByUserIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByUserIDAndProductID
}

func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) invocationsDone() bool {
	if len(mmExistByUserIDAndProductID.expectations) == 0 && mmExistByUserIDAndProductID.defaultExpectation == nil && mmExistByUserIDAndProductID.mock.funcExistByUserIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.mock.afterExistByUserIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByUserIDAndProductID implements mm_repository.ParticipantPrimeDB
func (mmExistByUserIDAndProductID *ParticipantPrimeDBMock) ExistByUserIDAndProductID(userID int64, productID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByUserIDAndProductID.beforeExistByUserIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByUserIDAndProductID.afterExistByUserIDAndProductIDCounter, 1)

	mmExistByUserIDAndProductID.t.Helper()

	if mmExistByUserIDAndProductID.inspectFuncExistByUserIDAndProductID != nil {
		mmExistByUserIDAndProductID.inspectFuncExistByUserIDAndProductID(userID, productID)
	}

	mm_params := ParticipantPrimeDBMockExistByUserIDAndProductIDParams{userID, productID}

	// Record call args
	mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.mutex.Lock()
	mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.callArgs = append(mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.callArgs, &mm_params)
	mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockExistByUserIDAndProductIDParams{userID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmExistByUserIDAndProductID.t.Errorf("ParticipantPrimeDBMock.ExistByUserIDAndProductID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmExistByUserIDAndProductID.t.Errorf("ParticipantPrimeDBMock.ExistByUserIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByUserIDAndProductID.t.Errorf("ParticipantPrimeDBMock.ExistByUserIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByUserIDAndProductID.ExistByUserIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByUserIDAndProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.ExistByUserIDAndProductID")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByUserIDAndProductID.funcExistByUserIDAndProductID != nil {
		return mmExistByUserIDAndProductID.funcExistByUserIDAndProductID(userID, productID)
	}
	mmExistByUserIDAndProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.ExistByUserIDAndProductID. %v %v", userID, productID)
	return
}

// ExistByUserIDAndProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.ExistByUserIDAndProductID invocations
func (mmExistByUserIDAndProductID *ParticipantPrimeDBMock) ExistByUserIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.afterExistByUserIDAndProductIDCounter)
}

// ExistByUserIDAndProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.ExistByUserIDAndProductID invocations
func (mmExistByUserIDAndProductID *ParticipantPrimeDBMock) ExistByUserIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByUserIDAndProductID.beforeExistByUserIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.ExistByUserIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByUserIDAndProductID *mParticipantPrimeDBMockExistByUserIDAndProductID) Calls() []*ParticipantPrimeDBMockExistByUserIDAndProductIDParams {
	mmExistByUserIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockExistByUserIDAndProductIDParams, len(mmExistByUserIDAndProductID.callArgs))
	copy(argCopy, mmExistByUserIDAndProductID.callArgs)

	mmExistByUserIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockExistByUserIDAndProductIDDone returns true if the count of the ExistByUserIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockExistByUserIDAndProductIDDone() bool {
	if m.ExistByUserIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByUserIDAndProductIDMock.invocationsDone()
}

// MinimockExistByUserIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockExistByUserIDAndProductIDInspect() {
	for _, e := range m.ExistByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByUserIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByUserIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterExistByUserIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByUserIDAndProductIDMock.defaultExpectation != nil && afterExistByUserIDAndProductIDCounter < 1 {
		if m.ExistByUserIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByUserIDAndProductID at\n%s", m.ExistByUserIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByUserIDAndProductID at\n%s with params: %#v", m.ExistByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.ExistByUserIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByUserIDAndProductID != nil && afterExistByUserIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.ExistByUserIDAndProductID at\n%s", m.funcExistByUserIDAndProductIDOrigin)
	}

	if !m.ExistByUserIDAndProductIDMock.invocationsDone() && afterExistByUserIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.ExistByUserIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByUserIDAndProductIDMock.expectedInvocations), m.ExistByUserIDAndProductIDMock.expectedInvocationsOrigin, afterExistByUserIDAndProductIDCounter)
	}
}

type mParticipantPrimeDBMockGetByGroupID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByGroupIDExpectation
	expectations       []*ParticipantPrimeDBMockGetByGroupIDExpectation

	callArgs []*ParticipantPrimeDBMockGetByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByGroupIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetByGroupID
type ParticipantPrimeDBMockGetByGroupIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByGroupIDParams
	paramPtrs          *ParticipantPrimeDBMockGetByGroupIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByGroupIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByGroupIDParams contains parameters of the ParticipantPrimeDB.GetByGroupID
type ParticipantPrimeDBMockGetByGroupIDParams struct {
	groupID int64
}

// ParticipantPrimeDBMockGetByGroupIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByGroupID
type ParticipantPrimeDBMockGetByGroupIDParamPtrs struct {
	groupID *int64
}

// ParticipantPrimeDBMockGetByGroupIDResults contains results of the ParticipantPrimeDB.GetByGroupID
type ParticipantPrimeDBMockGetByGroupIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByGroupIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetByGroupID
type ParticipantPrimeDBMockGetByGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Optional() *mParticipantPrimeDBMockGetByGroupID {
	mmGetByGroupID.optional = true
	return mmGetByGroupID
}

// Expect sets up expected params for ParticipantPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Expect(groupID int64) *mParticipantPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &ParticipantPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByGroupID mock is already set by ExpectParams functions")
	}

	mmGetByGroupID.defaultExpectation.params = &ParticipantPrimeDBMockGetByGroupIDParams{groupID}
	mmGetByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupID.expectations {
		if minimock.Equal(e.params, mmGetByGroupID.defaultExpectation.params) {
			mmGetByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupID.defaultExpectation.params)
		}
	}

	return mmGetByGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for ParticipantPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) ExpectGroupIDParam1(groupID int64) *mParticipantPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &ParticipantPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.params != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByGroupID mock is already set by Expect")
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByGroupIDParamPtrs{}
	}
	mmGetByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Inspect(f func(groupID int64)) *mParticipantPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByGroupID")
	}

	mmGetByGroupID.mock.inspectFuncGetByGroupID = f

	return mmGetByGroupID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByGroupID
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Return(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &ParticipantPrimeDBMockGetByGroupIDExpectation{mock: mmGetByGroupID.mock}
	}
	mmGetByGroupID.defaultExpectation.results = &ParticipantPrimeDBMockGetByGroupIDResults{pa1, err}
	mmGetByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByGroupID method
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Set(f func(groupID int64) (pa1 []participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByGroupID.defaultExpectation != nil {
		mmGetByGroupID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByGroupID method")
	}

	if len(mmGetByGroupID.expectations) > 0 {
		mmGetByGroupID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByGroupID method")
	}

	mmGetByGroupID.mock.funcGetByGroupID = f
	mmGetByGroupID.mock.funcGetByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) When(groupID int64) *ParticipantPrimeDBMockGetByGroupIDExpectation {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByGroupIDExpectation{
		mock:               mmGetByGroupID.mock,
		params:             &ParticipantPrimeDBMockGetByGroupIDParams{groupID},
		expectationOrigins: ParticipantPrimeDBMockGetByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupID.expectations = append(mmGetByGroupID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByGroupID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByGroupIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByGroupIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByGroupID should be invoked
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Times(n uint64) *mParticipantPrimeDBMockGetByGroupID {
	if n == 0 {
		mmGetByGroupID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupID.expectedInvocations, n)
	mmGetByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID
}

func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) invocationsDone() bool {
	if len(mmGetByGroupID.expectations) == 0 && mmGetByGroupID.defaultExpectation == nil && mmGetByGroupID.mock.funcGetByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.mock.afterGetByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupID implements mm_repository.ParticipantPrimeDB
func (mmGetByGroupID *ParticipantPrimeDBMock) GetByGroupID(groupID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByGroupID.beforeGetByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupID.afterGetByGroupIDCounter, 1)

	mmGetByGroupID.t.Helper()

	if mmGetByGroupID.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.inspectFuncGetByGroupID(groupID)
	}

	mm_params := ParticipantPrimeDBMockGetByGroupIDParams{groupID}

	// Record call args
	mmGetByGroupID.GetByGroupIDMock.mutex.Lock()
	mmGetByGroupID.GetByGroupIDMock.callArgs = append(mmGetByGroupID.GetByGroupIDMock.callArgs, &mm_params)
	mmGetByGroupID.GetByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupID.GetByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByGroupID.GetByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupID.GetByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupID.t.Errorf("ParticipantPrimeDBMock.GetByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupID.t.Errorf("ParticipantPrimeDBMock.GetByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByGroupID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByGroupID.funcGetByGroupID != nil {
		return mmGetByGroupID.funcGetByGroupID(groupID)
	}
	mmGetByGroupID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByGroupID. %v", groupID)
	return
}

// GetByGroupIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *ParticipantPrimeDBMock) GetByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.afterGetByGroupIDCounter)
}

// GetByGroupIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *ParticipantPrimeDBMock) GetByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.beforeGetByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupID *mParticipantPrimeDBMockGetByGroupID) Calls() []*ParticipantPrimeDBMockGetByGroupIDParams {
	mmGetByGroupID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByGroupIDParams, len(mmGetByGroupID.callArgs))
	copy(argCopy, mmGetByGroupID.callArgs)

	mmGetByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDDone returns true if the count of the GetByGroupID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByGroupIDDone() bool {
	if m.GetByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDMock.invocationsDone()
}

// MinimockGetByGroupIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByGroupIDInspect() {
	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDMock.defaultExpectation != nil && afterGetByGroupIDCounter < 1 {
		if m.GetByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByGroupID at\n%s", m.GetByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByGroupID at\n%s with params: %#v", m.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupID != nil && afterGetByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByGroupID at\n%s", m.funcGetByGroupIDOrigin)
	}

	if !m.GetByGroupIDMock.invocationsDone() && afterGetByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDMock.expectedInvocations), m.GetByGroupIDMock.expectedInvocationsOrigin, afterGetByGroupIDCounter)
	}
}

type mParticipantPrimeDBMockGetByParticipantIDAndProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation
	expectations       []*ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation

	callArgs []*ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetByParticipantIDAndProductID
type ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams
	paramPtrs          *ParticipantPrimeDBMockGetByParticipantIDAndProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetByParticipantIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams contains parameters of the ParticipantPrimeDB.GetByParticipantIDAndProductID
type ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams struct {
	participantID int64
	productID     int64
}

// ParticipantPrimeDBMockGetByParticipantIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByParticipantIDAndProductID
type ParticipantPrimeDBMockGetByParticipantIDAndProductIDParamPtrs struct {
	participantID *int64
	productID     *int64
}

// ParticipantPrimeDBMockGetByParticipantIDAndProductIDResults contains results of the ParticipantPrimeDB.GetByParticipantIDAndProductID
type ParticipantPrimeDBMockGetByParticipantIDAndProductIDResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByParticipantIDAndProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetByParticipantIDAndProductID
type ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectationOrigins struct {
	origin              string
	originParticipantID string
	originProductID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Optional() *mParticipantPrimeDBMockGetByParticipantIDAndProductID {
	mmGetByParticipantIDAndProductID.optional = true
	return mmGetByParticipantIDAndProductID
}

// Expect sets up expected params for ParticipantPrimeDB.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Expect(participantID int64, productID int64) *mParticipantPrimeDBMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantIDAndProductID.defaultExpectation.params = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams{participantID, productID}
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantIDAndProductID.defaultExpectation.params) {
			mmGetByParticipantIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantIDAndProductID
}

// ExpectParticipantIDParam1 sets up expected param participantID for ParticipantPrimeDB.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) ExpectParticipantIDParam1(participantID int64) *mParticipantPrimeDBMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantPrimeDB.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantPrimeDBMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation{}
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.params != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Expect")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDParamPtrs{}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByParticipantIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByParticipantIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Inspect(f func(participantID int64, productID int64)) *mParticipantPrimeDBMockGetByParticipantIDAndProductID {
	if mmGetByParticipantIDAndProductID.mock.inspectFuncGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByParticipantIDAndProductID")
	}

	mmGetByParticipantIDAndProductID.mock.inspectFuncGetByParticipantIDAndProductID = f

	return mmGetByParticipantIDAndProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByParticipantIDAndProductID
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Return(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	if mmGetByParticipantIDAndProductID.defaultExpectation == nil {
		mmGetByParticipantIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation{mock: mmGetByParticipantIDAndProductID.mock}
	}
	mmGetByParticipantIDAndProductID.defaultExpectation.results = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDResults{p1, err}
	mmGetByParticipantIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByParticipantIDAndProductID method
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Set(f func(participantID int64, productID int64) (p1 participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByParticipantIDAndProductID.defaultExpectation != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByParticipantIDAndProductID method")
	}

	if len(mmGetByParticipantIDAndProductID.expectations) > 0 {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByParticipantIDAndProductID method")
	}

	mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID = f
	mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByParticipantIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) When(participantID int64, productID int64) *ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation {
	if mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation{
		mock:               mmGetByParticipantIDAndProductID.mock,
		params:             &ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams{participantID, productID},
		expectationOrigins: ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantIDAndProductID.expectations = append(mmGetByParticipantIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByParticipantIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByParticipantIDAndProductIDExpectation) Then(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByParticipantIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByParticipantIDAndProductID should be invoked
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Times(n uint64) *mParticipantPrimeDBMockGetByParticipantIDAndProductID {
	if n == 0 {
		mmGetByParticipantIDAndProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByParticipantIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantIDAndProductID.expectedInvocations, n)
	mmGetByParticipantIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantIDAndProductID
}

func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) invocationsDone() bool {
	if len(mmGetByParticipantIDAndProductID.expectations) == 0 && mmGetByParticipantIDAndProductID.defaultExpectation == nil && mmGetByParticipantIDAndProductID.mock.funcGetByParticipantIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.mock.afterGetByParticipantIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantIDAndProductID implements mm_repository.ParticipantPrimeDB
func (mmGetByParticipantIDAndProductID *ParticipantPrimeDBMock) GetByParticipantIDAndProductID(participantID int64, productID int64) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.beforeGetByParticipantIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.afterGetByParticipantIDAndProductIDCounter, 1)

	mmGetByParticipantIDAndProductID.t.Helper()

	if mmGetByParticipantIDAndProductID.inspectFuncGetByParticipantIDAndProductID != nil {
		mmGetByParticipantIDAndProductID.inspectFuncGetByParticipantIDAndProductID(participantID, productID)
	}

	mm_params := ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams{participantID, productID}

	// Record call args
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.mutex.Lock()
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.callArgs = append(mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.callArgs, &mm_params)
	mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams{participantID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantIDAndProductID.t.Errorf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByParticipantIDAndProductID.t.Errorf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantIDAndProductID.t.Errorf("ParticipantPrimeDBMock.GetByParticipantIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantIDAndProductID.GetByParticipantIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantIDAndProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByParticipantIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByParticipantIDAndProductID.funcGetByParticipantIDAndProductID != nil {
		return mmGetByParticipantIDAndProductID.funcGetByParticipantIDAndProductID(participantID, productID)
	}
	mmGetByParticipantIDAndProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByParticipantIDAndProductID. %v %v", participantID, productID)
	return
}

// GetByParticipantIDAndProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByParticipantIDAndProductID invocations
func (mmGetByParticipantIDAndProductID *ParticipantPrimeDBMock) GetByParticipantIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.afterGetByParticipantIDAndProductIDCounter)
}

// GetByParticipantIDAndProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetByParticipantIDAndProductID invocations
func (mmGetByParticipantIDAndProductID *ParticipantPrimeDBMock) GetByParticipantIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantIDAndProductID.beforeGetByParticipantIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByParticipantIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantIDAndProductID *mParticipantPrimeDBMockGetByParticipantIDAndProductID) Calls() []*ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams {
	mmGetByParticipantIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByParticipantIDAndProductIDParams, len(mmGetByParticipantIDAndProductID.callArgs))
	copy(argCopy, mmGetByParticipantIDAndProductID.callArgs)

	mmGetByParticipantIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDAndProductIDDone returns true if the count of the GetByParticipantIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByParticipantIDAndProductIDDone() bool {
	if m.GetByParticipantIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDAndProductIDMock.invocationsDone()
}

// MinimockGetByParticipantIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByParticipantIDAndProductIDInspect() {
	for _, e := range m.GetByParticipantIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByParticipantIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDAndProductIDMock.defaultExpectation != nil && afterGetByParticipantIDAndProductIDCounter < 1 {
		if m.GetByParticipantIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByParticipantIDAndProductID at\n%s", m.GetByParticipantIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByParticipantIDAndProductID at\n%s with params: %#v", m.GetByParticipantIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantIDAndProductID != nil && afterGetByParticipantIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByParticipantIDAndProductID at\n%s", m.funcGetByParticipantIDAndProductIDOrigin)
	}

	if !m.GetByParticipantIDAndProductIDMock.invocationsDone() && afterGetByParticipantIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByParticipantIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDAndProductIDMock.expectedInvocations), m.GetByParticipantIDAndProductIDMock.expectedInvocationsOrigin, afterGetByParticipantIDAndProductIDCounter)
	}
}

type mParticipantPrimeDBMockGetByProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByProductIDExpectation
	expectations       []*ParticipantPrimeDBMockGetByProductIDExpectation

	callArgs []*ParticipantPrimeDBMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetByProductID
type ParticipantPrimeDBMockGetByProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByProductIDParams
	paramPtrs          *ParticipantPrimeDBMockGetByProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByProductIDParams contains parameters of the ParticipantPrimeDB.GetByProductID
type ParticipantPrimeDBMockGetByProductIDParams struct {
	productID int64
}

// ParticipantPrimeDBMockGetByProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByProductID
type ParticipantPrimeDBMockGetByProductIDParamPtrs struct {
	productID *int64
}

// ParticipantPrimeDBMockGetByProductIDResults contains results of the ParticipantPrimeDB.GetByProductID
type ParticipantPrimeDBMockGetByProductIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetByProductID
type ParticipantPrimeDBMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Optional() *mParticipantPrimeDBMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for ParticipantPrimeDB.GetByProductID
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Expect(productID int64) *mParticipantPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &ParticipantPrimeDBMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantPrimeDB.GetByProductID
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) ExpectProductIDParam1(productID int64) *mParticipantPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByProductID
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Inspect(f func(productID int64)) *mParticipantPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByProductID
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Return(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &ParticipantPrimeDBMockGetByProductIDResults{pa1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByProductID method
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Set(f func(productID int64) (pa1 []participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) When(productID int64) *ParticipantPrimeDBMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &ParticipantPrimeDBMockGetByProductIDParams{productID},
		expectationOrigins: ParticipantPrimeDBMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByProductIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByProductID should be invoked
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Times(n uint64) *mParticipantPrimeDBMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_repository.ParticipantPrimeDB
func (mmGetByProductID *ParticipantPrimeDBMock) GetByProductID(productID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := ParticipantPrimeDBMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("ParticipantPrimeDBMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("ParticipantPrimeDBMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByProductID invocations
func (mmGetByProductID *ParticipantPrimeDBMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetByProductID invocations
func (mmGetByProductID *ParticipantPrimeDBMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mParticipantPrimeDBMockGetByProductID) Calls() []*ParticipantPrimeDBMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mParticipantPrimeDBMockGetByProductIDAndUserEmail struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation
	expectations       []*ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation

	callArgs []*ParticipantPrimeDBMockGetByProductIDAndUserEmailParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation specifies expectation struct of the ParticipantPrimeDB.GetByProductIDAndUserEmail
type ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByProductIDAndUserEmailParams
	paramPtrs          *ParticipantPrimeDBMockGetByProductIDAndUserEmailParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectationOrigins
	results            *ParticipantPrimeDBMockGetByProductIDAndUserEmailResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByProductIDAndUserEmailParams contains parameters of the ParticipantPrimeDB.GetByProductIDAndUserEmail
type ParticipantPrimeDBMockGetByProductIDAndUserEmailParams struct {
	productID int64
	email     string
}

// ParticipantPrimeDBMockGetByProductIDAndUserEmailParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByProductIDAndUserEmail
type ParticipantPrimeDBMockGetByProductIDAndUserEmailParamPtrs struct {
	productID *int64
	email     *string
}

// ParticipantPrimeDBMockGetByProductIDAndUserEmailResults contains results of the ParticipantPrimeDB.GetByProductIDAndUserEmail
type ParticipantPrimeDBMockGetByProductIDAndUserEmailResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByProductIDAndUserEmailOrigins contains origins of expectations of the ParticipantPrimeDB.GetByProductIDAndUserEmail
type ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectationOrigins struct {
	origin          string
	originProductID string
	originEmail     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Optional() *mParticipantPrimeDBMockGetByProductIDAndUserEmail {
	mmGetByProductIDAndUserEmail.optional = true
	return mmGetByProductIDAndUserEmail
}

// Expect sets up expected params for ParticipantPrimeDB.GetByProductIDAndUserEmail
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Expect(productID int64, email string) *mParticipantPrimeDBMockGetByProductIDAndUserEmail {
	if mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Set")
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation == nil {
		mmGetByProductIDAndUserEmail.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation{}
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by ExpectParams functions")
	}

	mmGetByProductIDAndUserEmail.defaultExpectation.params = &ParticipantPrimeDBMockGetByProductIDAndUserEmailParams{productID, email}
	mmGetByProductIDAndUserEmail.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductIDAndUserEmail.expectations {
		if minimock.Equal(e.params, mmGetByProductIDAndUserEmail.defaultExpectation.params) {
			mmGetByProductIDAndUserEmail.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductIDAndUserEmail.defaultExpectation.params)
		}
	}

	return mmGetByProductIDAndUserEmail
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantPrimeDB.GetByProductIDAndUserEmail
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) ExpectProductIDParam1(productID int64) *mParticipantPrimeDBMockGetByProductIDAndUserEmail {
	if mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Set")
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation == nil {
		mmGetByProductIDAndUserEmail.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation{}
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation.params != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Expect")
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByProductIDAndUserEmailParamPtrs{}
	}
	mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductIDAndUserEmail.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductIDAndUserEmail
}

// ExpectEmailParam2 sets up expected param email for ParticipantPrimeDB.GetByProductIDAndUserEmail
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) ExpectEmailParam2(email string) *mParticipantPrimeDBMockGetByProductIDAndUserEmail {
	if mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Set")
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation == nil {
		mmGetByProductIDAndUserEmail.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation{}
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation.params != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Expect")
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByProductIDAndUserEmailParamPtrs{}
	}
	mmGetByProductIDAndUserEmail.defaultExpectation.paramPtrs.email = &email
	mmGetByProductIDAndUserEmail.defaultExpectation.expectationOrigins.originEmail = minimock.CallerInfo(1)

	return mmGetByProductIDAndUserEmail
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByProductIDAndUserEmail
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Inspect(f func(productID int64, email string)) *mParticipantPrimeDBMockGetByProductIDAndUserEmail {
	if mmGetByProductIDAndUserEmail.mock.inspectFuncGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByProductIDAndUserEmail")
	}

	mmGetByProductIDAndUserEmail.mock.inspectFuncGetByProductIDAndUserEmail = f

	return mmGetByProductIDAndUserEmail
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByProductIDAndUserEmail
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Return(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Set")
	}

	if mmGetByProductIDAndUserEmail.defaultExpectation == nil {
		mmGetByProductIDAndUserEmail.defaultExpectation = &ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation{mock: mmGetByProductIDAndUserEmail.mock}
	}
	mmGetByProductIDAndUserEmail.defaultExpectation.results = &ParticipantPrimeDBMockGetByProductIDAndUserEmailResults{p1, err}
	mmGetByProductIDAndUserEmail.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndUserEmail.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByProductIDAndUserEmail method
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Set(f func(productID int64, email string) (p1 participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByProductIDAndUserEmail.defaultExpectation != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByProductIDAndUserEmail method")
	}

	if len(mmGetByProductIDAndUserEmail.expectations) > 0 {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByProductIDAndUserEmail method")
	}

	mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail = f
	mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmailOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndUserEmail.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByProductIDAndUserEmail which will trigger the result defined by the following
// Then helper
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) When(productID int64, email string) *ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation {
	if mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation{
		mock:               mmGetByProductIDAndUserEmail.mock,
		params:             &ParticipantPrimeDBMockGetByProductIDAndUserEmailParams{productID, email},
		expectationOrigins: ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductIDAndUserEmail.expectations = append(mmGetByProductIDAndUserEmail.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByProductIDAndUserEmail return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByProductIDAndUserEmailExpectation) Then(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByProductIDAndUserEmailResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByProductIDAndUserEmail should be invoked
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Times(n uint64) *mParticipantPrimeDBMockGetByProductIDAndUserEmail {
	if n == 0 {
		mmGetByProductIDAndUserEmail.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByProductIDAndUserEmail mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductIDAndUserEmail.expectedInvocations, n)
	mmGetByProductIDAndUserEmail.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndUserEmail
}

func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) invocationsDone() bool {
	if len(mmGetByProductIDAndUserEmail.expectations) == 0 && mmGetByProductIDAndUserEmail.defaultExpectation == nil && mmGetByProductIDAndUserEmail.mock.funcGetByProductIDAndUserEmail == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndUserEmail.mock.afterGetByProductIDAndUserEmailCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndUserEmail.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductIDAndUserEmail implements mm_repository.ParticipantPrimeDB
func (mmGetByProductIDAndUserEmail *ParticipantPrimeDBMock) GetByProductIDAndUserEmail(productID int64, email string) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByProductIDAndUserEmail.beforeGetByProductIDAndUserEmailCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductIDAndUserEmail.afterGetByProductIDAndUserEmailCounter, 1)

	mmGetByProductIDAndUserEmail.t.Helper()

	if mmGetByProductIDAndUserEmail.inspectFuncGetByProductIDAndUserEmail != nil {
		mmGetByProductIDAndUserEmail.inspectFuncGetByProductIDAndUserEmail(productID, email)
	}

	mm_params := ParticipantPrimeDBMockGetByProductIDAndUserEmailParams{productID, email}

	// Record call args
	mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.mutex.Lock()
	mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.callArgs = append(mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.callArgs, &mm_params)
	mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.mutex.Unlock()

	for _, e := range mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByProductIDAndUserEmailParams{productID, email}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductIDAndUserEmail.t.Errorf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.email != nil && !minimock.Equal(*mm_want_ptrs.email, mm_got.email) {
				mmGetByProductIDAndUserEmail.t.Errorf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail got unexpected parameter email, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.expectationOrigins.originEmail, *mm_want_ptrs.email, mm_got.email, minimock.Diff(*mm_want_ptrs.email, mm_got.email))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductIDAndUserEmail.t.Errorf("ParticipantPrimeDBMock.GetByProductIDAndUserEmail got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductIDAndUserEmail.GetByProductIDAndUserEmailMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductIDAndUserEmail.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByProductIDAndUserEmail")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByProductIDAndUserEmail.funcGetByProductIDAndUserEmail != nil {
		return mmGetByProductIDAndUserEmail.funcGetByProductIDAndUserEmail(productID, email)
	}
	mmGetByProductIDAndUserEmail.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByProductIDAndUserEmail. %v %v", productID, email)
	return
}

// GetByProductIDAndUserEmailAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByProductIDAndUserEmail invocations
func (mmGetByProductIDAndUserEmail *ParticipantPrimeDBMock) GetByProductIDAndUserEmailAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndUserEmail.afterGetByProductIDAndUserEmailCounter)
}

// GetByProductIDAndUserEmailBeforeCounter returns a count of ParticipantPrimeDBMock.GetByProductIDAndUserEmail invocations
func (mmGetByProductIDAndUserEmail *ParticipantPrimeDBMock) GetByProductIDAndUserEmailBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndUserEmail.beforeGetByProductIDAndUserEmailCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByProductIDAndUserEmail.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductIDAndUserEmail *mParticipantPrimeDBMockGetByProductIDAndUserEmail) Calls() []*ParticipantPrimeDBMockGetByProductIDAndUserEmailParams {
	mmGetByProductIDAndUserEmail.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByProductIDAndUserEmailParams, len(mmGetByProductIDAndUserEmail.callArgs))
	copy(argCopy, mmGetByProductIDAndUserEmail.callArgs)

	mmGetByProductIDAndUserEmail.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDAndUserEmailDone returns true if the count of the GetByProductIDAndUserEmail invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByProductIDAndUserEmailDone() bool {
	if m.GetByProductIDAndUserEmailMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDAndUserEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDAndUserEmailMock.invocationsDone()
}

// MinimockGetByProductIDAndUserEmailInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByProductIDAndUserEmailInspect() {
	for _, e := range m.GetByProductIDAndUserEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductIDAndUserEmail at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDAndUserEmailCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDAndUserEmailCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDAndUserEmailMock.defaultExpectation != nil && afterGetByProductIDAndUserEmailCounter < 1 {
		if m.GetByProductIDAndUserEmailMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductIDAndUserEmail at\n%s", m.GetByProductIDAndUserEmailMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductIDAndUserEmail at\n%s with params: %#v", m.GetByProductIDAndUserEmailMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDAndUserEmailMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductIDAndUserEmail != nil && afterGetByProductIDAndUserEmailCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByProductIDAndUserEmail at\n%s", m.funcGetByProductIDAndUserEmailOrigin)
	}

	if !m.GetByProductIDAndUserEmailMock.invocationsDone() && afterGetByProductIDAndUserEmailCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByProductIDAndUserEmail at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDAndUserEmailMock.expectedInvocations), m.GetByProductIDAndUserEmailMock.expectedInvocationsOrigin, afterGetByProductIDAndUserEmailCounter)
	}
}

type mParticipantPrimeDBMockGetByRoleID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByRoleIDExpectation
	expectations       []*ParticipantPrimeDBMockGetByRoleIDExpectation

	callArgs []*ParticipantPrimeDBMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByRoleIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetByRoleID
type ParticipantPrimeDBMockGetByRoleIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByRoleIDParams
	paramPtrs          *ParticipantPrimeDBMockGetByRoleIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByRoleIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByRoleIDParams contains parameters of the ParticipantPrimeDB.GetByRoleID
type ParticipantPrimeDBMockGetByRoleIDParams struct {
	roleID int64
}

// ParticipantPrimeDBMockGetByRoleIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByRoleID
type ParticipantPrimeDBMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// ParticipantPrimeDBMockGetByRoleIDResults contains results of the ParticipantPrimeDB.GetByRoleID
type ParticipantPrimeDBMockGetByRoleIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByRoleIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetByRoleID
type ParticipantPrimeDBMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Optional() *mParticipantPrimeDBMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for ParticipantPrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Expect(roleID int64) *mParticipantPrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &ParticipantPrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &ParticipantPrimeDBMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for ParticipantPrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mParticipantPrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &ParticipantPrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Inspect(f func(roleID int64)) *mParticipantPrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByRoleID
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Return(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &ParticipantPrimeDBMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &ParticipantPrimeDBMockGetByRoleIDResults{pa1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByRoleID method
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Set(f func(roleID int64) (pa1 []participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) When(roleID int64) *ParticipantPrimeDBMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &ParticipantPrimeDBMockGetByRoleIDParams{roleID},
		expectationOrigins: ParticipantPrimeDBMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByRoleIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByRoleIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByRoleID should be invoked
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Times(n uint64) *mParticipantPrimeDBMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_repository.ParticipantPrimeDB
func (mmGetByRoleID *ParticipantPrimeDBMock) GetByRoleID(roleID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := ParticipantPrimeDBMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("ParticipantPrimeDBMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("ParticipantPrimeDBMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByRoleID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *ParticipantPrimeDBMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *ParticipantPrimeDBMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mParticipantPrimeDBMockGetByRoleID) Calls() []*ParticipantPrimeDBMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

type mParticipantPrimeDBMockGetByUserID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByUserIDExpectation
	expectations       []*ParticipantPrimeDBMockGetByUserIDExpectation

	callArgs []*ParticipantPrimeDBMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByUserIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetByUserID
type ParticipantPrimeDBMockGetByUserIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByUserIDParams
	paramPtrs          *ParticipantPrimeDBMockGetByUserIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByUserIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByUserIDParams contains parameters of the ParticipantPrimeDB.GetByUserID
type ParticipantPrimeDBMockGetByUserIDParams struct {
	userID int64
}

// ParticipantPrimeDBMockGetByUserIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByUserID
type ParticipantPrimeDBMockGetByUserIDParamPtrs struct {
	userID *int64
}

// ParticipantPrimeDBMockGetByUserIDResults contains results of the ParticipantPrimeDB.GetByUserID
type ParticipantPrimeDBMockGetByUserIDResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByUserIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetByUserID
type ParticipantPrimeDBMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Optional() *mParticipantPrimeDBMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for ParticipantPrimeDB.GetByUserID
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Expect(userID int64) *mParticipantPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &ParticipantPrimeDBMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantPrimeDB.GetByUserID
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) ExpectUserIDParam1(userID int64) *mParticipantPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByUserID
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Inspect(f func(userID int64)) *mParticipantPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByUserID
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Return(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &ParticipantPrimeDBMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByUserID method
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Set(f func(userID int64) (pa1 []participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) When(userID int64) *ParticipantPrimeDBMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &ParticipantPrimeDBMockGetByUserIDParams{userID},
		expectationOrigins: ParticipantPrimeDBMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByUserID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByUserIDExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByUserID should be invoked
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Times(n uint64) *mParticipantPrimeDBMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.ParticipantPrimeDB
func (mmGetByUserID *ParticipantPrimeDBMock) GetByUserID(userID int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := ParticipantPrimeDBMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("ParticipantPrimeDBMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("ParticipantPrimeDBMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *ParticipantPrimeDBMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *ParticipantPrimeDBMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mParticipantPrimeDBMockGetByUserID) Calls() []*ParticipantPrimeDBMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mParticipantPrimeDBMockGetByUserIDAndProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation
	expectations       []*ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation

	callArgs []*ParticipantPrimeDBMockGetByUserIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetByUserIDAndProductID
type ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByUserIDAndProductIDParams
	paramPtrs          *ParticipantPrimeDBMockGetByUserIDAndProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByUserIDAndProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetByUserIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDParams contains parameters of the ParticipantPrimeDB.GetByUserIDAndProductID
type ParticipantPrimeDBMockGetByUserIDAndProductIDParams struct {
	userID    int64
	productID int64
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByUserIDAndProductID
type ParticipantPrimeDBMockGetByUserIDAndProductIDParamPtrs struct {
	userID    *int64
	productID *int64
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDResults contains results of the ParticipantPrimeDB.GetByUserIDAndProductID
type ParticipantPrimeDBMockGetByUserIDAndProductIDResults struct {
	p1  participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetByUserIDAndProductID
type ParticipantPrimeDBMockGetByUserIDAndProductIDExpectationOrigins struct {
	origin          string
	originUserID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Optional() *mParticipantPrimeDBMockGetByUserIDAndProductID {
	mmGetByUserIDAndProductID.optional = true
	return mmGetByUserIDAndProductID
}

// Expect sets up expected params for ParticipantPrimeDB.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Expect(userID int64, productID int64) *mParticipantPrimeDBMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndProductID.defaultExpectation.params = &ParticipantPrimeDBMockGetByUserIDAndProductIDParams{userID, productID}
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndProductID.defaultExpectation.params) {
			mmGetByUserIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndProductID
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantPrimeDB.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) ExpectUserIDParam1(userID int64) *mParticipantPrimeDBMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.params != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Expect")
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByUserIDAndProductIDParamPtrs{}
	}
	mmGetByUserIDAndProductID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for ParticipantPrimeDB.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) ExpectProductIDParam2(productID int64) *mParticipantPrimeDBMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation{}
	}

	if mmGetByUserIDAndProductID.defaultExpectation.params != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Expect")
	}

	if mmGetByUserIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByUserIDAndProductIDParamPtrs{}
	}
	mmGetByUserIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByUserIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Inspect(f func(userID int64, productID int64)) *mParticipantPrimeDBMockGetByUserIDAndProductID {
	if mmGetByUserIDAndProductID.mock.inspectFuncGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByUserIDAndProductID")
	}

	mmGetByUserIDAndProductID.mock.inspectFuncGetByUserIDAndProductID = f

	return mmGetByUserIDAndProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByUserIDAndProductID
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Return(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Set")
	}

	if mmGetByUserIDAndProductID.defaultExpectation == nil {
		mmGetByUserIDAndProductID.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation{mock: mmGetByUserIDAndProductID.mock}
	}
	mmGetByUserIDAndProductID.defaultExpectation.results = &ParticipantPrimeDBMockGetByUserIDAndProductIDResults{p1, err}
	mmGetByUserIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByUserIDAndProductID method
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Set(f func(userID int64, productID int64) (p1 participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByUserIDAndProductID.defaultExpectation != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByUserIDAndProductID method")
	}

	if len(mmGetByUserIDAndProductID.expectations) > 0 {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByUserIDAndProductID method")
	}

	mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID = f
	mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByUserIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) When(userID int64, productID int64) *ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation {
	if mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation{
		mock:               mmGetByUserIDAndProductID.mock,
		params:             &ParticipantPrimeDBMockGetByUserIDAndProductIDParams{userID, productID},
		expectationOrigins: ParticipantPrimeDBMockGetByUserIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndProductID.expectations = append(mmGetByUserIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByUserIDAndProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByUserIDAndProductIDExpectation) Then(p1 participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByUserIDAndProductIDResults{p1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByUserIDAndProductID should be invoked
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Times(n uint64) *mParticipantPrimeDBMockGetByUserIDAndProductID {
	if n == 0 {
		mmGetByUserIDAndProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByUserIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndProductID.expectedInvocations, n)
	mmGetByUserIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductID
}

func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) invocationsDone() bool {
	if len(mmGetByUserIDAndProductID.expectations) == 0 && mmGetByUserIDAndProductID.defaultExpectation == nil && mmGetByUserIDAndProductID.mock.funcGetByUserIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.mock.afterGetByUserIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndProductID implements mm_repository.ParticipantPrimeDB
func (mmGetByUserIDAndProductID *ParticipantPrimeDBMock) GetByUserIDAndProductID(userID int64, productID int64) (p1 participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndProductID.beforeGetByUserIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndProductID.afterGetByUserIDAndProductIDCounter, 1)

	mmGetByUserIDAndProductID.t.Helper()

	if mmGetByUserIDAndProductID.inspectFuncGetByUserIDAndProductID != nil {
		mmGetByUserIDAndProductID.inspectFuncGetByUserIDAndProductID(userID, productID)
	}

	mm_params := ParticipantPrimeDBMockGetByUserIDAndProductIDParams{userID, productID}

	// Record call args
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.mutex.Lock()
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.callArgs = append(mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.callArgs, &mm_params)
	mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByUserIDAndProductIDParams{userID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndProductID.t.Errorf("ParticipantPrimeDBMock.GetByUserIDAndProductID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByUserIDAndProductID.t.Errorf("ParticipantPrimeDBMock.GetByUserIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndProductID.t.Errorf("ParticipantPrimeDBMock.GetByUserIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndProductID.GetByUserIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByUserIDAndProductID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByUserIDAndProductID.funcGetByUserIDAndProductID != nil {
		return mmGetByUserIDAndProductID.funcGetByUserIDAndProductID(userID, productID)
	}
	mmGetByUserIDAndProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByUserIDAndProductID. %v %v", userID, productID)
	return
}

// GetByUserIDAndProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByUserIDAndProductID invocations
func (mmGetByUserIDAndProductID *ParticipantPrimeDBMock) GetByUserIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.afterGetByUserIDAndProductIDCounter)
}

// GetByUserIDAndProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetByUserIDAndProductID invocations
func (mmGetByUserIDAndProductID *ParticipantPrimeDBMock) GetByUserIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductID.beforeGetByUserIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByUserIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndProductID *mParticipantPrimeDBMockGetByUserIDAndProductID) Calls() []*ParticipantPrimeDBMockGetByUserIDAndProductIDParams {
	mmGetByUserIDAndProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByUserIDAndProductIDParams, len(mmGetByUserIDAndProductID.callArgs))
	copy(argCopy, mmGetByUserIDAndProductID.callArgs)

	mmGetByUserIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndProductIDDone returns true if the count of the GetByUserIDAndProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByUserIDAndProductIDDone() bool {
	if m.GetByUserIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndProductIDMock.invocationsDone()
}

// MinimockGetByUserIDAndProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByUserIDAndProductIDInspect() {
	for _, e := range m.GetByUserIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndProductIDMock.defaultExpectation != nil && afterGetByUserIDAndProductIDCounter < 1 {
		if m.GetByUserIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductID at\n%s", m.GetByUserIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductID at\n%s with params: %#v", m.GetByUserIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndProductID != nil && afterGetByUserIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductID at\n%s", m.funcGetByUserIDAndProductIDOrigin)
	}

	if !m.GetByUserIDAndProductIDMock.invocationsDone() && afterGetByUserIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByUserIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndProductIDMock.expectedInvocations), m.GetByUserIDAndProductIDMock.expectedInvocationsOrigin, afterGetByUserIDAndProductIDCounter)
	}
}

type mParticipantPrimeDBMockGetByUserIDAndProductIDs struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation
	expectations       []*ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation

	callArgs []*ParticipantPrimeDBMockGetByUserIDAndProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation specifies expectation struct of the ParticipantPrimeDB.GetByUserIDAndProductIDs
type ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetByUserIDAndProductIDsParams
	paramPtrs          *ParticipantPrimeDBMockGetByUserIDAndProductIDsParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectationOrigins
	results            *ParticipantPrimeDBMockGetByUserIDAndProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDsParams contains parameters of the ParticipantPrimeDB.GetByUserIDAndProductIDs
type ParticipantPrimeDBMockGetByUserIDAndProductIDsParams struct {
	userID     int64
	productIDs []int64
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDsParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetByUserIDAndProductIDs
type ParticipantPrimeDBMockGetByUserIDAndProductIDsParamPtrs struct {
	userID     *int64
	productIDs *[]int64
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDsResults contains results of the ParticipantPrimeDB.GetByUserIDAndProductIDs
type ParticipantPrimeDBMockGetByUserIDAndProductIDsResults struct {
	pa1 []participantentity.Participant
	err error
}

// ParticipantPrimeDBMockGetByUserIDAndProductIDsOrigins contains origins of expectations of the ParticipantPrimeDB.GetByUserIDAndProductIDs
type ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectationOrigins struct {
	origin           string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Optional() *mParticipantPrimeDBMockGetByUserIDAndProductIDs {
	mmGetByUserIDAndProductIDs.optional = true
	return mmGetByUserIDAndProductIDs
}

// Expect sets up expected params for ParticipantPrimeDB.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Expect(userID int64, productIDs []int64) *mParticipantPrimeDBMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndProductIDs.defaultExpectation.params = &ParticipantPrimeDBMockGetByUserIDAndProductIDsParams{userID, productIDs}
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndProductIDs.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndProductIDs.defaultExpectation.params) {
			mmGetByUserIDAndProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndProductIDs.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndProductIDs
}

// ExpectUserIDParam1 sets up expected param userID for ParticipantPrimeDB.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) ExpectUserIDParam1(userID int64) *mParticipantPrimeDBMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByUserIDAndProductIDsParamPtrs{}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductIDs
}

// ExpectProductIDsParam2 sets up expected param productIDs for ParticipantPrimeDB.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) ExpectProductIDsParam2(productIDs []int64) *mParticipantPrimeDBMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation{}
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.params != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Expect")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetByUserIDAndProductIDsParamPtrs{}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmGetByUserIDAndProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmGetByUserIDAndProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Inspect(f func(userID int64, productIDs []int64)) *mParticipantPrimeDBMockGetByUserIDAndProductIDs {
	if mmGetByUserIDAndProductIDs.mock.inspectFuncGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetByUserIDAndProductIDs")
	}

	mmGetByUserIDAndProductIDs.mock.inspectFuncGetByUserIDAndProductIDs = f

	return mmGetByUserIDAndProductIDs
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetByUserIDAndProductIDs
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Return(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	if mmGetByUserIDAndProductIDs.defaultExpectation == nil {
		mmGetByUserIDAndProductIDs.defaultExpectation = &ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation{mock: mmGetByUserIDAndProductIDs.mock}
	}
	mmGetByUserIDAndProductIDs.defaultExpectation.results = &ParticipantPrimeDBMockGetByUserIDAndProductIDsResults{pa1, err}
	mmGetByUserIDAndProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetByUserIDAndProductIDs method
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Set(f func(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error)) *ParticipantPrimeDBMock {
	if mmGetByUserIDAndProductIDs.defaultExpectation != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetByUserIDAndProductIDs method")
	}

	if len(mmGetByUserIDAndProductIDs.expectations) > 0 {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetByUserIDAndProductIDs method")
	}

	mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs = f
	mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs.mock
}

// When sets expectation for the ParticipantPrimeDB.GetByUserIDAndProductIDs which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) When(userID int64, productIDs []int64) *ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation {
	if mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation{
		mock:               mmGetByUserIDAndProductIDs.mock,
		params:             &ParticipantPrimeDBMockGetByUserIDAndProductIDsParams{userID, productIDs},
		expectationOrigins: ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndProductIDs.expectations = append(mmGetByUserIDAndProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetByUserIDAndProductIDs return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetByUserIDAndProductIDsExpectation) Then(pa1 []participantentity.Participant, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetByUserIDAndProductIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetByUserIDAndProductIDs should be invoked
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Times(n uint64) *mParticipantPrimeDBMockGetByUserIDAndProductIDs {
	if n == 0 {
		mmGetByUserIDAndProductIDs.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetByUserIDAndProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndProductIDs.expectedInvocations, n)
	mmGetByUserIDAndProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndProductIDs
}

func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) invocationsDone() bool {
	if len(mmGetByUserIDAndProductIDs.expectations) == 0 && mmGetByUserIDAndProductIDs.defaultExpectation == nil && mmGetByUserIDAndProductIDs.mock.funcGetByUserIDAndProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.mock.afterGetByUserIDAndProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndProductIDs implements mm_repository.ParticipantPrimeDB
func (mmGetByUserIDAndProductIDs *ParticipantPrimeDBMock) GetByUserIDAndProductIDs(userID int64, productIDs []int64) (pa1 []participantentity.Participant, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.beforeGetByUserIDAndProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.afterGetByUserIDAndProductIDsCounter, 1)

	mmGetByUserIDAndProductIDs.t.Helper()

	if mmGetByUserIDAndProductIDs.inspectFuncGetByUserIDAndProductIDs != nil {
		mmGetByUserIDAndProductIDs.inspectFuncGetByUserIDAndProductIDs(userID, productIDs)
	}

	mm_params := ParticipantPrimeDBMockGetByUserIDAndProductIDsParams{userID, productIDs}

	// Record call args
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.mutex.Lock()
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.callArgs = append(mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.callArgs, &mm_params)
	mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetByUserIDAndProductIDsParams{userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmGetByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndProductIDs.t.Errorf("ParticipantPrimeDBMock.GetByUserIDAndProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndProductIDs.GetByUserIDAndProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndProductIDs.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetByUserIDAndProductIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserIDAndProductIDs.funcGetByUserIDAndProductIDs != nil {
		return mmGetByUserIDAndProductIDs.funcGetByUserIDAndProductIDs(userID, productIDs)
	}
	mmGetByUserIDAndProductIDs.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetByUserIDAndProductIDs. %v %v", userID, productIDs)
	return
}

// GetByUserIDAndProductIDsAfterCounter returns a count of finished ParticipantPrimeDBMock.GetByUserIDAndProductIDs invocations
func (mmGetByUserIDAndProductIDs *ParticipantPrimeDBMock) GetByUserIDAndProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.afterGetByUserIDAndProductIDsCounter)
}

// GetByUserIDAndProductIDsBeforeCounter returns a count of ParticipantPrimeDBMock.GetByUserIDAndProductIDs invocations
func (mmGetByUserIDAndProductIDs *ParticipantPrimeDBMock) GetByUserIDAndProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndProductIDs.beforeGetByUserIDAndProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetByUserIDAndProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndProductIDs *mParticipantPrimeDBMockGetByUserIDAndProductIDs) Calls() []*ParticipantPrimeDBMockGetByUserIDAndProductIDsParams {
	mmGetByUserIDAndProductIDs.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetByUserIDAndProductIDsParams, len(mmGetByUserIDAndProductIDs.callArgs))
	copy(argCopy, mmGetByUserIDAndProductIDs.callArgs)

	mmGetByUserIDAndProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndProductIDsDone returns true if the count of the GetByUserIDAndProductIDs invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetByUserIDAndProductIDsDone() bool {
	if m.GetByUserIDAndProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndProductIDsMock.invocationsDone()
}

// MinimockGetByUserIDAndProductIDsInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetByUserIDAndProductIDsInspect() {
	for _, e := range m.GetByUserIDAndProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndProductIDsCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndProductIDsMock.defaultExpectation != nil && afterGetByUserIDAndProductIDsCounter < 1 {
		if m.GetByUserIDAndProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductIDs at\n%s", m.GetByUserIDAndProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductIDs at\n%s with params: %#v", m.GetByUserIDAndProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndProductIDs != nil && afterGetByUserIDAndProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetByUserIDAndProductIDs at\n%s", m.funcGetByUserIDAndProductIDsOrigin)
	}

	if !m.GetByUserIDAndProductIDsMock.invocationsDone() && afterGetByUserIDAndProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetByUserIDAndProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndProductIDsMock.expectedInvocations), m.GetByUserIDAndProductIDsMock.expectedInvocationsOrigin, afterGetByUserIDAndProductIDsCounter)
	}
}

type mParticipantPrimeDBMockGetOwnersByProductID struct {
	optional           bool
	mock               *ParticipantPrimeDBMock
	defaultExpectation *ParticipantPrimeDBMockGetOwnersByProductIDExpectation
	expectations       []*ParticipantPrimeDBMockGetOwnersByProductIDExpectation

	callArgs []*ParticipantPrimeDBMockGetOwnersByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ParticipantPrimeDBMockGetOwnersByProductIDExpectation specifies expectation struct of the ParticipantPrimeDB.GetOwnersByProductID
type ParticipantPrimeDBMockGetOwnersByProductIDExpectation struct {
	mock               *ParticipantPrimeDBMock
	params             *ParticipantPrimeDBMockGetOwnersByProductIDParams
	paramPtrs          *ParticipantPrimeDBMockGetOwnersByProductIDParamPtrs
	expectationOrigins ParticipantPrimeDBMockGetOwnersByProductIDExpectationOrigins
	results            *ParticipantPrimeDBMockGetOwnersByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ParticipantPrimeDBMockGetOwnersByProductIDParams contains parameters of the ParticipantPrimeDB.GetOwnersByProductID
type ParticipantPrimeDBMockGetOwnersByProductIDParams struct {
	productID int64
}

// ParticipantPrimeDBMockGetOwnersByProductIDParamPtrs contains pointers to parameters of the ParticipantPrimeDB.GetOwnersByProductID
type ParticipantPrimeDBMockGetOwnersByProductIDParamPtrs struct {
	productID *int64
}

// ParticipantPrimeDBMockGetOwnersByProductIDResults contains results of the ParticipantPrimeDB.GetOwnersByProductID
type ParticipantPrimeDBMockGetOwnersByProductIDResults struct {
	oa1 []productentity.Owner
	err error
}

// ParticipantPrimeDBMockGetOwnersByProductIDOrigins contains origins of expectations of the ParticipantPrimeDB.GetOwnersByProductID
type ParticipantPrimeDBMockGetOwnersByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Optional() *mParticipantPrimeDBMockGetOwnersByProductID {
	mmGetOwnersByProductID.optional = true
	return mmGetOwnersByProductID
}

// Expect sets up expected params for ParticipantPrimeDB.GetOwnersByProductID
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Expect(productID int64) *mParticipantPrimeDBMockGetOwnersByProductID {
	if mmGetOwnersByProductID.mock.funcGetOwnersByProductID != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetOwnersByProductID mock is already set by Set")
	}

	if mmGetOwnersByProductID.defaultExpectation == nil {
		mmGetOwnersByProductID.defaultExpectation = &ParticipantPrimeDBMockGetOwnersByProductIDExpectation{}
	}

	if mmGetOwnersByProductID.defaultExpectation.paramPtrs != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetOwnersByProductID mock is already set by ExpectParams functions")
	}

	mmGetOwnersByProductID.defaultExpectation.params = &ParticipantPrimeDBMockGetOwnersByProductIDParams{productID}
	mmGetOwnersByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetOwnersByProductID.expectations {
		if minimock.Equal(e.params, mmGetOwnersByProductID.defaultExpectation.params) {
			mmGetOwnersByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetOwnersByProductID.defaultExpectation.params)
		}
	}

	return mmGetOwnersByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ParticipantPrimeDB.GetOwnersByProductID
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) ExpectProductIDParam1(productID int64) *mParticipantPrimeDBMockGetOwnersByProductID {
	if mmGetOwnersByProductID.mock.funcGetOwnersByProductID != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetOwnersByProductID mock is already set by Set")
	}

	if mmGetOwnersByProductID.defaultExpectation == nil {
		mmGetOwnersByProductID.defaultExpectation = &ParticipantPrimeDBMockGetOwnersByProductIDExpectation{}
	}

	if mmGetOwnersByProductID.defaultExpectation.params != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetOwnersByProductID mock is already set by Expect")
	}

	if mmGetOwnersByProductID.defaultExpectation.paramPtrs == nil {
		mmGetOwnersByProductID.defaultExpectation.paramPtrs = &ParticipantPrimeDBMockGetOwnersByProductIDParamPtrs{}
	}
	mmGetOwnersByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetOwnersByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetOwnersByProductID
}

// Inspect accepts an inspector function that has same arguments as the ParticipantPrimeDB.GetOwnersByProductID
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Inspect(f func(productID int64)) *mParticipantPrimeDBMockGetOwnersByProductID {
	if mmGetOwnersByProductID.mock.inspectFuncGetOwnersByProductID != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("Inspect function is already set for ParticipantPrimeDBMock.GetOwnersByProductID")
	}

	mmGetOwnersByProductID.mock.inspectFuncGetOwnersByProductID = f

	return mmGetOwnersByProductID
}

// Return sets up results that will be returned by ParticipantPrimeDB.GetOwnersByProductID
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Return(oa1 []productentity.Owner, err error) *ParticipantPrimeDBMock {
	if mmGetOwnersByProductID.mock.funcGetOwnersByProductID != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetOwnersByProductID mock is already set by Set")
	}

	if mmGetOwnersByProductID.defaultExpectation == nil {
		mmGetOwnersByProductID.defaultExpectation = &ParticipantPrimeDBMockGetOwnersByProductIDExpectation{mock: mmGetOwnersByProductID.mock}
	}
	mmGetOwnersByProductID.defaultExpectation.results = &ParticipantPrimeDBMockGetOwnersByProductIDResults{oa1, err}
	mmGetOwnersByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductID.mock
}

// Set uses given function f to mock the ParticipantPrimeDB.GetOwnersByProductID method
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Set(f func(productID int64) (oa1 []productentity.Owner, err error)) *ParticipantPrimeDBMock {
	if mmGetOwnersByProductID.defaultExpectation != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("Default expectation is already set for the ParticipantPrimeDB.GetOwnersByProductID method")
	}

	if len(mmGetOwnersByProductID.expectations) > 0 {
		mmGetOwnersByProductID.mock.t.Fatalf("Some expectations are already set for the ParticipantPrimeDB.GetOwnersByProductID method")
	}

	mmGetOwnersByProductID.mock.funcGetOwnersByProductID = f
	mmGetOwnersByProductID.mock.funcGetOwnersByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductID.mock
}

// When sets expectation for the ParticipantPrimeDB.GetOwnersByProductID which will trigger the result defined by the following
// Then helper
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) When(productID int64) *ParticipantPrimeDBMockGetOwnersByProductIDExpectation {
	if mmGetOwnersByProductID.mock.funcGetOwnersByProductID != nil {
		mmGetOwnersByProductID.mock.t.Fatalf("ParticipantPrimeDBMock.GetOwnersByProductID mock is already set by Set")
	}

	expectation := &ParticipantPrimeDBMockGetOwnersByProductIDExpectation{
		mock:               mmGetOwnersByProductID.mock,
		params:             &ParticipantPrimeDBMockGetOwnersByProductIDParams{productID},
		expectationOrigins: ParticipantPrimeDBMockGetOwnersByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetOwnersByProductID.expectations = append(mmGetOwnersByProductID.expectations, expectation)
	return expectation
}

// Then sets up ParticipantPrimeDB.GetOwnersByProductID return parameters for the expectation previously defined by the When method
func (e *ParticipantPrimeDBMockGetOwnersByProductIDExpectation) Then(oa1 []productentity.Owner, err error) *ParticipantPrimeDBMock {
	e.results = &ParticipantPrimeDBMockGetOwnersByProductIDResults{oa1, err}
	return e.mock
}

// Times sets number of times ParticipantPrimeDB.GetOwnersByProductID should be invoked
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Times(n uint64) *mParticipantPrimeDBMockGetOwnersByProductID {
	if n == 0 {
		mmGetOwnersByProductID.mock.t.Fatalf("Times of ParticipantPrimeDBMock.GetOwnersByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetOwnersByProductID.expectedInvocations, n)
	mmGetOwnersByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductID
}

func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) invocationsDone() bool {
	if len(mmGetOwnersByProductID.expectations) == 0 && mmGetOwnersByProductID.defaultExpectation == nil && mmGetOwnersByProductID.mock.funcGetOwnersByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetOwnersByProductID.mock.afterGetOwnersByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetOwnersByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetOwnersByProductID implements mm_repository.ParticipantPrimeDB
func (mmGetOwnersByProductID *ParticipantPrimeDBMock) GetOwnersByProductID(productID int64) (oa1 []productentity.Owner, err error) {
	mm_atomic.AddUint64(&mmGetOwnersByProductID.beforeGetOwnersByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetOwnersByProductID.afterGetOwnersByProductIDCounter, 1)

	mmGetOwnersByProductID.t.Helper()

	if mmGetOwnersByProductID.inspectFuncGetOwnersByProductID != nil {
		mmGetOwnersByProductID.inspectFuncGetOwnersByProductID(productID)
	}

	mm_params := ParticipantPrimeDBMockGetOwnersByProductIDParams{productID}

	// Record call args
	mmGetOwnersByProductID.GetOwnersByProductIDMock.mutex.Lock()
	mmGetOwnersByProductID.GetOwnersByProductIDMock.callArgs = append(mmGetOwnersByProductID.GetOwnersByProductIDMock.callArgs, &mm_params)
	mmGetOwnersByProductID.GetOwnersByProductIDMock.mutex.Unlock()

	for _, e := range mmGetOwnersByProductID.GetOwnersByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.oa1, e.results.err
		}
	}

	if mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ParticipantPrimeDBMockGetOwnersByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetOwnersByProductID.t.Errorf("ParticipantPrimeDBMock.GetOwnersByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetOwnersByProductID.t.Errorf("ParticipantPrimeDBMock.GetOwnersByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetOwnersByProductID.GetOwnersByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetOwnersByProductID.t.Fatal("No results are set for the ParticipantPrimeDBMock.GetOwnersByProductID")
		}
		return (*mm_results).oa1, (*mm_results).err
	}
	if mmGetOwnersByProductID.funcGetOwnersByProductID != nil {
		return mmGetOwnersByProductID.funcGetOwnersByProductID(productID)
	}
	mmGetOwnersByProductID.t.Fatalf("Unexpected call to ParticipantPrimeDBMock.GetOwnersByProductID. %v", productID)
	return
}

// GetOwnersByProductIDAfterCounter returns a count of finished ParticipantPrimeDBMock.GetOwnersByProductID invocations
func (mmGetOwnersByProductID *ParticipantPrimeDBMock) GetOwnersByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnersByProductID.afterGetOwnersByProductIDCounter)
}

// GetOwnersByProductIDBeforeCounter returns a count of ParticipantPrimeDBMock.GetOwnersByProductID invocations
func (mmGetOwnersByProductID *ParticipantPrimeDBMock) GetOwnersByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnersByProductID.beforeGetOwnersByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ParticipantPrimeDBMock.GetOwnersByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetOwnersByProductID *mParticipantPrimeDBMockGetOwnersByProductID) Calls() []*ParticipantPrimeDBMockGetOwnersByProductIDParams {
	mmGetOwnersByProductID.mutex.RLock()

	argCopy := make([]*ParticipantPrimeDBMockGetOwnersByProductIDParams, len(mmGetOwnersByProductID.callArgs))
	copy(argCopy, mmGetOwnersByProductID.callArgs)

	mmGetOwnersByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetOwnersByProductIDDone returns true if the count of the GetOwnersByProductID invocations corresponds
// the number of defined expectations
func (m *ParticipantPrimeDBMock) MinimockGetOwnersByProductIDDone() bool {
	if m.GetOwnersByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetOwnersByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetOwnersByProductIDMock.invocationsDone()
}

// MinimockGetOwnersByProductIDInspect logs each unmet expectation
func (m *ParticipantPrimeDBMock) MinimockGetOwnersByProductIDInspect() {
	for _, e := range m.GetOwnersByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetOwnersByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetOwnersByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetOwnersByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetOwnersByProductIDMock.defaultExpectation != nil && afterGetOwnersByProductIDCounter < 1 {
		if m.GetOwnersByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetOwnersByProductID at\n%s", m.GetOwnersByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetOwnersByProductID at\n%s with params: %#v", m.GetOwnersByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetOwnersByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetOwnersByProductID != nil && afterGetOwnersByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ParticipantPrimeDBMock.GetOwnersByProductID at\n%s", m.funcGetOwnersByProductIDOrigin)
	}

	if !m.GetOwnersByProductIDMock.invocationsDone() && afterGetOwnersByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ParticipantPrimeDBMock.GetOwnersByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetOwnersByProductIDMock.expectedInvocations), m.GetOwnersByProductIDMock.expectedInvocationsOrigin, afterGetOwnersByProductIDCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ParticipantPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByUserIDAndProductIDsInspect()

			m.MinimockDeleteInspect()

			m.MinimockDeleteByUserIDAndGroupIDsInspect()

			m.MinimockDeleteByUserIDAndProductIDsInspect()

			m.MinimockDeleteByUserIDAndRoleIDsInspect()

			m.MinimockDeleteByUserIDsAndProductIDInspect()

			m.MinimockExistByParticipantIDAndProductIDInspect()

			m.MinimockExistByUserIDAndProductIDInspect()

			m.MinimockGetByGroupIDInspect()

			m.MinimockGetByParticipantIDAndProductIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByProductIDAndUserEmailInspect()

			m.MinimockGetByRoleIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetByUserIDAndProductIDInspect()

			m.MinimockGetByUserIDAndProductIDsInspect()

			m.MinimockGetOwnersByProductIDInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ParticipantPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ParticipantPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByUserIDAndProductIDsDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockDeleteByUserIDAndGroupIDsDone() &&
		m.MinimockDeleteByUserIDAndProductIDsDone() &&
		m.MinimockDeleteByUserIDAndRoleIDsDone() &&
		m.MinimockDeleteByUserIDsAndProductIDDone() &&
		m.MinimockExistByParticipantIDAndProductIDDone() &&
		m.MinimockExistByUserIDAndProductIDDone() &&
		m.MinimockGetByGroupIDDone() &&
		m.MinimockGetByParticipantIDAndProductIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByProductIDAndUserEmailDone() &&
		m.MinimockGetByRoleIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetByUserIDAndProductIDDone() &&
		m.MinimockGetByUserIDAndProductIDsDone() &&
		m.MinimockGetOwnersByProductIDDone()
}
