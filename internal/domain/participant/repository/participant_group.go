package repository

import (
	"context"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
)

//go:generate minimock -i ParticipantGroupPrimeDB -o ../mocks/participant_group_prime_db_mock.go -s _mock.go
type ParticipantGroupPrimeDB interface {
	Create(ctx context.Context, participantID, groupID int64) (participantentity.ParticipantGroup, error)
	CreateByRoleIDAndParticipantIDs(ctx context.Context, groupID int64, participantIDs []int64) error
	GetAll() ([]participantentity.ParticipantGroup, error)
	GetByGroupID(groupID int64) ([]participantentity.ParticipantGroup, error)
	GetByID(id int64) (participantentity.ParticipantGroup, error)
	GetByParticipantID(participantID int64) ([]participantentity.ParticipantGroup, error)
	GetByParticipantIDAndGroupID(participantID, groupID int64) (participantentity.ParticipantGroup, error)
	GetByParticipantIDs(participantIDs []int64) ([]participantentity.ParticipantGroup, error)
	Update(ctx context.Context, id int64, participantID, groupID *int64) (participantentity.ParticipantGroup, error)
	DeleteByGroupID(ctx context.Context, groupID int64) error
	DeleteByParticipantAndGroupID(ctx context.Context, participantID, groupID int64) error
	DeleteByParticipantID(ctx context.Context, participantID int64) error
	DeleteByParticipantIDs(ctx context.Context, participantIDs []int64) error
	DeleteByRoleIDAndParticipantIDs(ctx context.Context, groupID int64, participantIDs []int64) error
}
