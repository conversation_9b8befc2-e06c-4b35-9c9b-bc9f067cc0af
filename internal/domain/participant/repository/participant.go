package repository

import (
	"context"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

//go:generate minimock -i ParticipantPrimeDB -o ../mocks/participant_prime_db_mock.go -s _mock.go
type ParticipantPrimeDB interface {
	Create(ctx context.Context, participant participantentity.ParticipantCreateData) (participantentity.Participant, error)
	CreateByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error
	GetByGroupID(groupID int64) ([]participantentity.Participant, error)
	GetByParticipantIDAndProductID(participantID, productID int64) (participantentity.Participant, error)
	GetByProductID(productID int64) ([]participantentity.Participant, error)
	GetByProductIDAndUserEmail(productID int64, email string) (participantentity.Participant, error)
	GetByRoleID(roleID int64) ([]participantentity.Participant, error)
	GetByUserID(userID int64) ([]participantentity.Participant, error)
	GetByUserIDAndProductID(userID, productID int64) (participantentity.Participant, error)
	GetByUserIDAndProductIDs(userID int64, productIDs []int64) ([]participantentity.Participant, error)
	GetOwnersByProductID(productID int64) ([]productentity.Owner, error)
	ExistByParticipantIDAndProductID(participantID, productID int64) (bool, error)
	ExistByUserIDAndProductID(userID, productID int64) (bool, error)
	Delete(ctx context.Context, participantID, productID int64) error
	DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error
	DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error
	DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error
	DeleteByUserIDsAndProductID(ctx context.Context, productID int64, userID []int64) error
}

//go:generate minimock -i ParticipantCache -o ../mocks/participant_cache_mock.go -s _mock.go
type ParticipantCache interface {
	GetByParticipantIDAndProductID(participantID, productID int64) (participantentity.Participant, error)
	GetByProductID(productID int64) ([]participantentity.Participant, error)
	GetByUserID(userID int64) ([]participantentity.Participant, error)
	GetByUserIDAndProductID(userID, productID int64) (participantentity.Participant, error)
	GetByUserIDAndProductIDs(userID int64, productIDs []int64) ([]participantentity.Participant, error)
	ExistByParticipantIDAndProductID(participantID, productID int64) (bool, error)
	ExistByUserIDAndProductID(userID, productID int64) (bool, error)
	Set(participant participantentity.Participant) error
	Delete(participantID, productID int64) error
	DeleteByUserIDAndProductIDs(userID int64, productIDs []int64) error
}
