package repository

import (
	"context"

	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
)

//go:generate minimock -i ParticipantRolePrimeDB -o ../mocks/participant_role_prime_db_mock.go -s _mock.go
type ParticipantRolePrimeDB interface {
	Create(ctx context.Context, participantID, roleID int64) (participantentity.ParticipantRole, error)
	CreateByRoleIDAndParticipantIDs(ctx context.Context, roleID int64, participantIDs []int64) error
	GetAll() ([]participantentity.ParticipantRole, error)
	GetByID(id int64) (participantentity.ParticipantRole, error)
	GetByParticipantAndRoleID(participantID, roleID int64) (participantentity.ParticipantRole, error)
	GetByParticipantID(participantID int64) ([]participantentity.ParticipantRole, error)
	GetByParticipantIDs(participantIDs []int64) ([]participantentity.ParticipantRole, error)
	GetByRoleID(roleID int64) ([]participantentity.ParticipantRole, error)
	GetByRoleIDAndParticipantIDs(roleID int64, participantIDs []int64) ([]participantentity.ParticipantRole, error)
	GetProductOwnersByParticipantID(participantID int64) ([]participantentity.ParticipantRole, error)
	IsOwner(participantID int64) (bool, error)
	DeleteByParticipantAndRoleID(ctx context.Context, participantID, roleID int64) error
	DeleteByParticipantID(participantID int64) error
	DeleteByParticipantIDs(participantIDs []int64) error
	DeleteByRoleID(ctx context.Context, roleID int64) error
	DeleteByRoleIDAndParticipantIDs(ctx context.Context, roleID int64, participantIDs []int64) error
}
