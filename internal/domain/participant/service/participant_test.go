package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	identityprovidermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/mocks"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

type mockRepositories struct {
	participantGroupPrimeDB *participantmocks.ParticipantGroupPrimeDBMock
	participantPrimeDB      *participantmocks.ParticipantPrimeDBMock
	participantRolePrimeDB  *participantmocks.ParticipantRolePrimeDBMock
	productPrimeDB          *productmocks.ProductPrimeDBMock
	rolePrimeDB             *rolemocks.RolePrimeDBMock
	userPrimeDB             *usermocks.UserPrimeDBMock
	identityRepository      *identityprovidermocks.IdentityProviderMock
	groupPrimeDB            *groupmocks.GroupPrimeDBMock
}

func createMocks(t *testing.T) *mockRepositories {
	return &mockRepositories{
		participantGroupPrimeDB: participantmocks.NewParticipantGroupPrimeDBMock(t),
		participantPrimeDB:      participantmocks.NewParticipantPrimeDBMock(t),
		participantRolePrimeDB:  participantmocks.NewParticipantRolePrimeDBMock(t),
		productPrimeDB:          productmocks.NewProductPrimeDBMock(t),
		rolePrimeDB:             rolemocks.NewRolePrimeDBMock(t),
		userPrimeDB:             usermocks.NewUserPrimeDBMock(t),
		identityRepository:      identityprovidermocks.NewIdentityProviderMock(t),
		groupPrimeDB:            groupmocks.NewGroupPrimeDBMock(t),
	}
}

func createService(mocks *mockRepositories) ParticipantDomainService {
	return NewParticipantDomainService(
		mocks.participantGroupPrimeDB,
		mocks.participantPrimeDB,
		mocks.participantRolePrimeDB,
		mocks.productPrimeDB,
		mocks.rolePrimeDB,
		mocks.userPrimeDB,
		mocks.identityRepository,
		mocks.groupPrimeDB,
	)
}

func createTestParticipant() participantentity.Participant {
	now := time.Now()
	return participantentity.Participant{
		ID:        1,
		ProductID: 1,
		UserID:    1,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

func createTestUser() userentity.User {
	lastLoginAt := time.Now().Add(-24 * time.Hour)
	return userentity.User{
		ID:          1,
		CategoryID:  1,
		Email:       "<EMAIL>",
		FullName:    "Test User",
		Position:    "Developer",
		IsAdmin:     false,
		ActiveFlg:   true,
		LastLoginAt: &lastLoginAt,
		CreatedAt:   time.Now().Add(-48 * time.Hour),
	}
}

func createTestProduct() productentity.Product {
	return productentity.Product{
		ID:        1,
		IID:       "test-product",
		TechName:  "test_product",
		Name:      "Test Product",
		ActiveFlg: true,
		CreatedAt: time.Now().Add(-48 * time.Hour),
		UpdatedAt: time.Now().Add(-24 * time.Hour),
	}
}

func createTestOwnerRole() roleentity.Role {
	return roleentity.Role{
		ID:   1,
		Name: "Owner",
		// Type: "owner", // Field is missing in role
	}
}

// Tests for Create method
func TestParticipantDomainService_Create_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	createData := participantentity.ParticipantCreateData{
		ProductID: 1,
		UserID:    1,
	}
	expectedParticipant := createTestParticipant()

	mocks.participantPrimeDB.CreateMock.Expect(ctx, createData).Return(expectedParticipant, nil)

	// Act
	result, err := svc.Create(ctx, createData)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipant, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.CreateAfterCounter())
}

func TestParticipantDomainService_Create_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	createData := participantentity.ParticipantCreateData{
		ProductID: 1,
		UserID:    1,
	}
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.CreateMock.Expect(ctx, createData).Return(participantentity.Participant{}, expectedError)

	// Act
	result, err := svc.Create(ctx, createData)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, participantentity.Participant{}, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.CreateAfterCounter())
}

// Tests for GetByUserID method
func TestParticipantDomainService_GetByUserID_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	expectedParticipants := []participantentity.Participant{
		createTestParticipant(),
		{ID: 2, ProductID: 2, UserID: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	mocks.participantPrimeDB.GetByUserIDMock.Expect(userID).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByUserID(userID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAfterCounter())
}

func TestParticipantDomainService_GetByUserID_EmptyResult(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	var expectedParticipants []participantentity.Participant

	mocks.participantPrimeDB.GetByUserIDMock.Expect(userID).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByUserID(userID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Len(t, result, 0)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAfterCounter())
}

func TestParticipantDomainService_GetByUserID_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.GetByUserIDMock.Expect(userID).Return(nil, expectedError)

	// Act
	result, err := svc.GetByUserID(userID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAfterCounter())
}

// Tests for GetByUserIDAndProductID method
func TestParticipantDomainService_GetByUserIDAndProductID_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	productID := int64(1)
	expectedParticipant := createTestParticipant()

	mocks.participantPrimeDB.GetByUserIDAndProductIDMock.Expect(userID, productID).Return(expectedParticipant, nil)

	// Act
	result, err := svc.GetByUserIDAndProductID(userID, productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipant, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAndProductIDAfterCounter())
}

func TestParticipantDomainService_GetByUserIDAndProductID_NotFound(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	productID := int64(1)
	expectedError := errors.New("not found")

	mocks.participantPrimeDB.GetByUserIDAndProductIDMock.Expect(userID, productID).Return(participantentity.Participant{}, expectedError)

	// Act
	result, err := svc.GetByUserIDAndProductID(userID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, participantentity.Participant{}, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAndProductIDAfterCounter())
}

// Tests for GetByUserIDAndProductIDs method
func TestParticipantDomainService_GetByUserIDAndProductIDs_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	productIDs := []int64{1, 2}
	expectedParticipants := []participantentity.Participant{
		createTestParticipant(),
		{ID: 2, ProductID: 2, UserID: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	mocks.participantPrimeDB.GetByUserIDAndProductIDsMock.Expect(userID, productIDs).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByUserIDAndProductIDs(userID, productIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAndProductIDsAfterCounter())
}

func TestParticipantDomainService_GetByUserIDAndProductIDs_EmptyProductIDs(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	var productIDs []int64
	var expectedParticipants []participantentity.Participant

	mocks.participantPrimeDB.GetByUserIDAndProductIDsMock.Expect(userID, productIDs).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByUserIDAndProductIDs(userID, productIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Len(t, result, 0)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAndProductIDsAfterCounter())
}

// Tests for GetOwnersByProductIDs method
func TestParticipantDomainService_GetOwnersByProductIDs_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productIDs := []int64{1, 2}
	owners1 := []productentity.Owner{
		{ProductID: 1, ParticipantID: 1, UserID: 1, Email: "<EMAIL>", FullName: "Owner 1"},
	}
	owners2 := []productentity.Owner{
		{ProductID: 2, ParticipantID: 2, UserID: 2, Email: "<EMAIL>", FullName: "Owner 2"},
	}

	mocks.participantPrimeDB.GetOwnersByProductIDMock.When(int64(1)).Then(owners1, nil)
	mocks.participantPrimeDB.GetOwnersByProductIDMock.When(int64(2)).Then(owners2, nil)

	// Act
	result, err := svc.GetOwnersByProductIDs(productIDs)

	// Assert
	require.NoError(t, err)
	require.Len(t, result, 2)
	require.Equal(t, owners1, result[1])
	require.Equal(t, owners2, result[2])
	require.Equal(t, uint64(2), mocks.participantPrimeDB.GetOwnersByProductIDAfterCounter())
}

func TestParticipantDomainService_GetOwnersByProductIDs_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productIDs := []int64{1}
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.GetOwnersByProductIDMock.Expect(int64(1)).Return(nil, expectedError)

	// Act
	result, err := svc.GetOwnersByProductIDs(productIDs)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetOwnersByProductIDAfterCounter())
}

// Tests for Delete method
func TestParticipantDomainService_Delete_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	participantID := int64(1)
	productID := int64(1)

	mocks.participantPrimeDB.DeleteMock.Expect(ctx, participantID, productID).Return(nil)

	// Act
	err := svc.Delete(ctx, participantID, productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteAfterCounter())
}

func TestParticipantDomainService_Delete_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	participantID := int64(1)
	productID := int64(1)
	expectedError := errors.New("delete error")

	mocks.participantPrimeDB.DeleteMock.Expect(ctx, participantID, productID).Return(expectedError)

	// Act
	err := svc.Delete(ctx, participantID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteAfterCounter())
}

// Tests for GetByParticipantIDAndProductID method
func TestParticipantDomainService_GetByParticipantIDAndProductID_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	participantID := int64(1)
	productID := int64(1)

	product := createTestProduct()
	participant := createTestParticipant()
	user := createTestUser()
	groups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 1, GroupID: 2, CreatedAt: time.Now()},
	}
	roles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 1, RoleID: 2, CreatedAt: time.Now()},
	}
	ownerRole := createTestOwnerRole()

	mocks.productPrimeDB.GetByIDMock.Expect(productID).Return(product, nil)
	mocks.participantPrimeDB.GetByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(participant, nil)
	mocks.userPrimeDB.GetByIDMock.Expect(participant.UserID).Return(user, nil)
	mocks.participantGroupPrimeDB.GetByParticipantIDMock.Expect(participant.ID).Return(groups, nil)
	mocks.participantRolePrimeDB.GetByParticipantIDMock.Expect(participant.ID).Return(roles, nil)
	mocks.rolePrimeDB.GetOwnerRoleMock.Expect().Return(ownerRole, nil)

	// Act
	result, err := svc.GetByParticipantIDAndProductID(participantID, productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, participant.ID, result.ID)
	require.Equal(t, participant.ProductID, result.ProductID)
	require.Equal(t, participant.UserID, result.UserID)
	require.Equal(t, user.Email, result.Email)
	require.Equal(t, user.FullName, result.FullName)
	require.Equal(t, user.Position, result.Position)
	require.True(t, result.IsOwner) // Role 1 is owner role
	require.Equal(t, []int64{1, 2}, result.GroupIDs)
	require.Equal(t, []int64{1, 2}, result.RoleIDs)
	require.Equal(t, participant.CreatedAt, result.CreatedAt)
	require.Equal(t, participant.UpdatedAt, result.UpdatedAt)
}

func TestParticipantDomainService_GetByParticipantIDAndProductID_ProductNotActive(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	participantID := int64(1)
	productID := int64(1)

	product := createTestProduct()
	product.ActiveFlg = false

	mocks.productPrimeDB.GetByIDMock.Expect(productID).Return(product, nil)

	// Act
	result, err := svc.GetByParticipantIDAndProductID(participantID, productID)

	// Assert
	require.Error(t, err)
	require.Contains(t, err.Error(), "not found")
	require.Equal(t, participantentity.ParticipantFull{}, result)
}

func TestParticipantDomainService_GetByParticipantIDAndProductID_ProductError(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	participantID := int64(1)
	productID := int64(1)
	expectedError := errors.New("product error")

	mocks.productPrimeDB.GetByIDMock.Expect(productID).Return(productentity.Product{}, expectedError)

	// Act
	result, err := svc.GetByParticipantIDAndProductID(participantID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, participantentity.ParticipantFull{}, result)
}

// Tests for ExistByParticipantIDAndProductID method
func TestParticipantDomainService_ExistByParticipantIDAndProductID_Exists(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	participantID := int64(1)
	productID := int64(1)

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(true, nil)

	// Act
	result, err := svc.ExistByParticipantIDAndProductID(participantID, productID)

	// Assert
	require.NoError(t, err)
	require.True(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.ExistByParticipantIDAndProductIDAfterCounter())
}

func TestParticipantDomainService_ExistByParticipantIDAndProductID_NotExists(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	participantID := int64(1)
	productID := int64(1)

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(false, nil)

	// Act
	result, err := svc.ExistByParticipantIDAndProductID(participantID, productID)

	// Assert
	require.NoError(t, err)
	require.False(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.ExistByParticipantIDAndProductIDAfterCounter())
}

func TestParticipantDomainService_ExistByParticipantIDAndProductID_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	participantID := int64(1)
	productID := int64(1)
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(false, expectedError)

	// Act
	result, err := svc.ExistByParticipantIDAndProductID(participantID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.False(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.ExistByParticipantIDAndProductIDAfterCounter())
}

// Tests for UpdateParticipantByUserUpdateData method
func TestParticipantDomainService_UpdateParticipantByUserUpdateData_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	productIDs := []int64{1, 2, 3}
	updData := userentity.UserUpdateData{
		ID:         userID,
		ProductIDs: &productIDs,
	}

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 1}, // existing, remains
		{ID: 2, UserID: userID, ProductID: 4}, // existing, will be deleted
	}

	newParticipant := participantentity.Participant{ID: 3, UserID: userID, ProductID: 2}

	mocks.participantPrimeDB.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)
	// Create new participants for products 2 and 3
	mocks.participantPrimeDB.CreateMock.When(context.Background(), participantentity.ParticipantCreateData{UserID: userID, ProductID: 2}).Then(newParticipant, nil)
	mocks.participantPrimeDB.CreateMock.When(context.Background(), participantentity.ParticipantCreateData{UserID: userID, ProductID: 3}).Then(participantentity.Participant{ID: 4, UserID: userID, ProductID: 3}, nil)
	// Delete participant for product 4
	mocks.participantPrimeDB.DeleteMock.Expect(context.Background(), int64(2), int64(4)).Return(nil)

	// Act
	err := svc.UpdateParticipantByUserUpdateData(userID, updData)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAfterCounter())
	require.Equal(t, uint64(2), mocks.participantPrimeDB.CreateAfterCounter())
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteAfterCounter())
}

func TestParticipantDomainService_UpdateParticipantByUserUpdateData_NilProductIDs(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	updData := userentity.UserUpdateData{
		ID:         userID,
		ProductIDs: nil, // No changes in products
	}

	// Act
	err := svc.UpdateParticipantByUserUpdateData(userID, updData)

	// Assert
	require.NoError(t, err)
	// No database calls should be made
	require.Equal(t, uint64(0), mocks.participantPrimeDB.GetByUserIDAfterCounter())
}

func TestParticipantDomainService_UpdateParticipantByUserUpdateData_GetError(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	userID := int64(1)
	productIDs := []int64{1}
	updData := userentity.UserUpdateData{
		ID:         userID,
		ProductIDs: &productIDs,
	}
	expectedError := errors.New("get error")

	mocks.participantPrimeDB.GetByUserIDMock.Expect(userID).Return(nil, expectedError)

	// Act
	err := svc.UpdateParticipantByUserUpdateData(userID, updData)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByUserIDAfterCounter())
}

// Tests for UpdateGroups method
func TestParticipantDomainService_UpdateGroups_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	participantID := int64(1)
	groupIDs := []int64{1, 2}

	mocks.participantGroupPrimeDB.DeleteByParticipantIDMock.Expect(ctx, participantID).Return(nil)
	mocks.participantGroupPrimeDB.CreateMock.When(ctx, participantID, int64(1)).Then(participantentity.ParticipantGroup{GroupID: 1}, nil)
	mocks.participantGroupPrimeDB.CreateMock.When(ctx, participantID, int64(2)).Then(participantentity.ParticipantGroup{GroupID: 2}, nil)

	// Act
	result, err := svc.UpdateGroups(ctx, participantID, groupIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, []int64{1, 2}, result)
	require.Equal(t, uint64(1), mocks.participantGroupPrimeDB.DeleteByParticipantIDAfterCounter())
	require.Equal(t, uint64(2), mocks.participantGroupPrimeDB.CreateAfterCounter())
}

func TestParticipantDomainService_UpdateGroups_EmptyGroupIDs(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	participantID := int64(1)
	var groupIDs []int64

	mocks.participantGroupPrimeDB.DeleteByParticipantIDMock.Expect(ctx, participantID).Return(nil)

	// Act
	result, err := svc.UpdateGroups(ctx, participantID, groupIDs)

	// Assert
	require.NoError(t, err)
	require.Empty(t, result)
	require.Equal(t, uint64(1), mocks.participantGroupPrimeDB.DeleteByParticipantIDAfterCounter())
	require.Equal(t, uint64(0), mocks.participantGroupPrimeDB.CreateAfterCounter())
}

func TestParticipantDomainService_UpdateGroups_DeleteError(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	participantID := int64(1)
	groupIDs := []int64{1}
	expectedError := errors.New("delete error")

	mocks.participantGroupPrimeDB.DeleteByParticipantIDMock.Expect(ctx, participantID).Return(expectedError)

	// Act
	result, err := svc.UpdateGroups(ctx, participantID, groupIDs)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), mocks.participantGroupPrimeDB.DeleteByParticipantIDAfterCounter())
}

// Tests for DeleteByParticipantIDAndProductID method
func TestParticipantDomainService_DeleteByParticipantIDAndProductID_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.WithValue(context.Background(), constants.UserIDKey, int64(2))
	participantID := int64(1)
	productID := int64(1)

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(true, nil)
	mocks.productPrimeDB.GetByIDMock.Expect(productID).Return(createTestProduct(), nil)
	mocks.participantPrimeDB.GetByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(createTestParticipant(), nil)
	mocks.userPrimeDB.GetByIDMock.Expect(int64(1)).Return(createTestUser(), nil)
	mocks.participantGroupPrimeDB.GetByParticipantIDMock.Expect(participantID).Return([]participantentity.ParticipantGroup{}, nil)
	mocks.participantRolePrimeDB.GetByParticipantIDMock.Expect(participantID).Return([]participantentity.ParticipantRole{}, nil)
	mocks.rolePrimeDB.GetOwnerRoleMock.Expect().Return(createTestOwnerRole(), nil)
	mocks.participantPrimeDB.DeleteMock.Expect(ctx, participantID, productID).Return(nil)

	// Act
	err := svc.DeleteByParticipantIDAndProductID(ctx, participantID, productID)

	// Assert
	require.NoError(t, err)
}

func TestParticipantDomainService_DeleteByParticipantIDAndProductID_ParticipantNotExists(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	participantID := int64(1)
	productID := int64(1)

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(false, nil)

	// Act
	err := svc.DeleteByParticipantIDAndProductID(ctx, participantID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, "participant not found in product", err.Error())
}

func TestParticipantDomainService_DeleteByParticipantIDAndProductID_SelfDelete(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.WithValue(context.Background(), constants.UserIDKey, int64(1))
	participantID := int64(1)
	productID := int64(1)

	participant := createTestParticipant()
	participant.UserID = 1 // Same user who tries to delete himself

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(true, nil)
	mocks.productPrimeDB.GetByIDMock.Expect(productID).Return(createTestProduct(), nil)
	mocks.participantPrimeDB.GetByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(participant, nil)
	mocks.userPrimeDB.GetByIDMock.Expect(int64(1)).Return(createTestUser(), nil)
	mocks.participantGroupPrimeDB.GetByParticipantIDMock.Expect(participantID).Return([]participantentity.ParticipantGroup{}, nil)
	mocks.participantRolePrimeDB.GetByParticipantIDMock.Expect(participantID).Return([]participantentity.ParticipantRole{}, nil)
	mocks.rolePrimeDB.GetOwnerRoleMock.Expect().Return(createTestOwnerRole(), nil)

	// Act
	err := svc.DeleteByParticipantIDAndProductID(ctx, participantID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, "participant cannot delete himself from product", err.Error())
}

func TestParticipantDomainService_DeleteByParticipantIDAndProductID_MissingUserID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background() // UserIDKey is missing in context
	participantID := int64(1)
	productID := int64(1)

	participant := createTestParticipant()

	mocks.participantPrimeDB.ExistByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(true, nil)
	mocks.productPrimeDB.GetByIDMock.Expect(productID).Return(createTestProduct(), nil)
	mocks.participantPrimeDB.GetByParticipantIDAndProductIDMock.Expect(participantID, productID).Return(participant, nil)
	mocks.userPrimeDB.GetByIDMock.Expect(int64(1)).Return(createTestUser(), nil)
	mocks.participantGroupPrimeDB.GetByParticipantIDMock.Expect(participantID).Return([]participantentity.ParticipantGroup{}, nil)
	mocks.participantRolePrimeDB.GetByParticipantIDMock.Expect(participantID).Return([]participantentity.ParticipantRole{}, nil)
	mocks.rolePrimeDB.GetOwnerRoleMock.Expect().Return(createTestOwnerRole(), nil)

	// Act
	err := svc.DeleteByParticipantIDAndProductID(ctx, participantID, productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, "user ID is missing in context", err.Error())
}

// Tests for DeleteByUserIDAndProductIDs method
func TestParticipantDomainService_DeleteByUserIDAndProductIDs_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	productIDs := []int64{1, 2, 3}

	mocks.participantPrimeDB.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, productIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndProductIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndProductIDs_EmptyProductIDs(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	var productIDs []int64

	mocks.participantPrimeDB.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, productIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndProductIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndProductIDs_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	productIDs := []int64{1, 2}
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, productIDs).Return(expectedError)

	// Act
	err := svc.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndProductIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndProductIDs_SingleProductID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	productIDs := []int64{1}

	mocks.participantPrimeDB.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, productIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndProductIDsAfterCounter())
}

// Tests for DeleteByUserIDAndGroupIDs method
func TestParticipantDomainService_DeleteByUserIDAndGroupIDs_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	groupIDs := []int64{1, 2, 3}

	mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, groupIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndGroupIDs_EmptyGroupIDs(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	var groupIDs []int64

	mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, groupIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndGroupIDs_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	groupIDs := []int64{1, 2}
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, groupIDs).Return(expectedError)

	// Act
	err := svc.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndGroupIDs_SingleGroupID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	groupIDs := []int64{1}

	mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, groupIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsAfterCounter())
}

// Tests for DeleteByUserIDAndRoleIDs method
func TestParticipantDomainService_DeleteByUserIDAndRoleIDs_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	roleIDs := []int64{1, 2, 3}

	mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, roleIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndRoleIDs_EmptyRoleIDs(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	var roleIDs []int64

	mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, roleIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndRoleIDs_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	roleIDs := []int64{1, 2}
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, roleIDs).Return(expectedError)

	// Act
	err := svc.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndRoleIDs_SingleRoleID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(1)
	roleIDs := []int64{1}

	mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, roleIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsAfterCounter())
}

// Tests for GetByProductID method
func TestParticipantDomainService_GetByProductID_Success(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productID := int64(1)
	expectedParticipants := []participantentity.Participant{
		createTestParticipant(),
		{ID: 2, ProductID: 1, UserID: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 3, ProductID: 1, UserID: 3, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	mocks.participantPrimeDB.GetByProductIDMock.Expect(productID).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByProductID(productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Len(t, result, 3)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByProductIDAfterCounter())
}

func TestParticipantDomainService_GetByProductID_EmptyResult(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productID := int64(1)
	var expectedParticipants []participantentity.Participant

	mocks.participantPrimeDB.GetByProductIDMock.Expect(productID).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByProductID(productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Len(t, result, 0)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByProductIDAfterCounter())
}

func TestParticipantDomainService_GetByProductID_Error(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productID := int64(1)
	expectedError := errors.New("database error")

	mocks.participantPrimeDB.GetByProductIDMock.Expect(productID).Return(nil, expectedError)

	// Act
	result, err := svc.GetByProductID(productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByProductIDAfterCounter())
}

func TestParticipantDomainService_GetByProductID_SingleParticipant(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productID := int64(1)
	expectedParticipants := []participantentity.Participant{
		createTestParticipant(),
	}

	mocks.participantPrimeDB.GetByProductIDMock.Expect(productID).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByProductID(productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Len(t, result, 1)
	require.Equal(t, expectedParticipants[0].ID, result[0].ID)
	require.Equal(t, expectedParticipants[0].ProductID, result[0].ProductID)
	require.Equal(t, expectedParticipants[0].UserID, result[0].UserID)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByProductIDAfterCounter())
}

func TestParticipantDomainService_GetByProductID_NonExistentProduct(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productID := int64(999) // Non-existent product
	expectedError := errors.New("product not found")

	mocks.participantPrimeDB.GetByProductIDMock.Expect(productID).Return(nil, expectedError)

	// Act
	result, err := svc.GetByProductID(productID)

	// Assert
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByProductIDAfterCounter())
}

// Tests with zero values
func TestParticipantDomainService_DeleteByUserIDAndProductIDs_ZeroUserID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(0)
	productIDs := []int64{1}

	mocks.participantPrimeDB.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, productIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndProductIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndGroupIDs_ZeroUserID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(0)
	groupIDs := []int64{1}

	mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, groupIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndGroupIDsAfterCounter())
}

func TestParticipantDomainService_DeleteByUserIDAndRoleIDs_ZeroUserID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	ctx := context.Background()
	userID := int64(0)
	roleIDs := []int64{1}

	mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, roleIDs).Return(nil)

	// Act
	err := svc.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)

	// Assert
	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.DeleteByUserIDAndRoleIDsAfterCounter())
}

func TestParticipantDomainService_GetByProductID_ZeroProductID(t *testing.T) {
	// Arrange
	mocks := createMocks(t)
	svc := createService(mocks)

	productID := int64(0)
	var expectedParticipants []participantentity.Participant

	mocks.participantPrimeDB.GetByProductIDMock.Expect(productID).Return(expectedParticipants, nil)

	// Act
	result, err := svc.GetByProductID(productID)

	// Assert
	require.NoError(t, err)
	require.Equal(t, expectedParticipants, result)
	require.Len(t, result, 0)
	require.Equal(t, uint64(1), mocks.participantPrimeDB.GetByProductIDAfterCounter())
}
