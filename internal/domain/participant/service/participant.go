package service

import (
	"context"
	"errors"
	"slices"
	"strconv"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	IdentityProvider "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	rolerepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

//go:generate minimock -i ParticipantDomainService -o ../mocks/participant_domain_service_mock.go -s _mock.go
type ParticipantDomainService interface {
	Create(ctx context.Context, participant participantentity.ParticipantCreateData) (participantentity.Participant, error)
	CreateParticipant(ctx context.Context, participant participantentity.ParticipantCreateData) (participantentity.ParticipantFull, error)
	GetByParticipantIDAndProductID(participantID, productID int64) (participantentity.ParticipantFull, error)
	GetByProductID(productID int64) ([]participantentity.Participant, error)
	GetByUserID(userID int64) ([]participantentity.Participant, error)
	GetByUserIDAndProductID(userID int64, productID int64) (participantentity.Participant, error)
	GetByUserIDAndProductIDs(userID int64, productIDs []int64) ([]participantentity.Participant, error)
	GetOwnersByProductIDs(productIDs []int64) (map[int64][]productentity.Owner, error)
	GetParticipantFullByParticipantIDAndProductID(participantID, productID int64) (participantentity.ParticipantFull, error)
	GetParticipantFullByProductID(productID int64) ([]participantentity.ParticipantFull, error)
	ExistByParticipantIDAndProductID(participantID, productID int64) (bool, error)
	UpdateGroups(ctx context.Context, participantID int64, groupIDs []int64) ([]int64, error)
	UpdateOwners(productID int64, ownerEmails []string) error
	UpdateParticipantByUserUpdateData(userID int64, updData userentity.UserUpdateData) error
	Delete(ctx context.Context, participantID, productID int64) error
	DeleteByParticipantIDAndProductID(ctx context.Context, participantID int64, productID int64) error
	DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error
	DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error
	DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error
}

type participantDomainService struct {
	participantGroupPrimeDB participantrepository.ParticipantGroupPrimeDB
	participantPrimeDB      participantrepository.ParticipantPrimeDB
	participantRolePrimeDB  participantrepository.ParticipantRolePrimeDB
	productPrimeDB          productrepository.ProductPrimeDB
	rolePrimeDB             rolerepository.RolePrimeDB
	userPrimeDB             userrepository.UserPrimeDB
	identityrepository      IdentityProvider.IdentityProvider
	groupPrimeDB            grouprepository.GroupPrimeDB
}

func NewParticipantDomainService(
	participantGroupPrimeDB participantrepository.ParticipantGroupPrimeDB,
	participantPrimeDB participantrepository.ParticipantPrimeDB,
	participantRolePrimeDB participantrepository.ParticipantRolePrimeDB,
	productPrimeDB productrepository.ProductPrimeDB,
	rolePrimeDB rolerepository.RolePrimeDB,
	userPrimeDB userrepository.UserPrimeDB,
	identityrepository IdentityProvider.IdentityProvider,
	groupPrimeDB grouprepository.GroupPrimeDB,
) ParticipantDomainService {
	return &participantDomainService{
		participantGroupPrimeDB: participantGroupPrimeDB,
		participantPrimeDB:      participantPrimeDB,
		participantRolePrimeDB:  participantRolePrimeDB,
		productPrimeDB:          productPrimeDB,
		rolePrimeDB:             rolePrimeDB,
		userPrimeDB:             userPrimeDB,
		identityrepository:      identityrepository,
		groupPrimeDB:            groupPrimeDB,
	}
}

func (s *participantDomainService) Create(ctx context.Context, participant participantentity.ParticipantCreateData) (participantentity.Participant, error) {
	return s.participantPrimeDB.Create(ctx, participant)
}

func (s *participantDomainService) CreateParticipant(ctx context.Context, participant participantentity.ParticipantCreateData) (participantentity.ParticipantFull, error) {
	user, err := s.userPrimeDB.GetByEmail(participant.Email)
	if err != nil {
		if errkit.IsNotFoundError(err) {
			keycloakUsers, err := s.identityrepository.GetUsersByEmail(participant.Email)
			if err != nil {
				return participantentity.ParticipantFull{}, err
			}
			if len(keycloakUsers) == 0 {
				return participantentity.ParticipantFull{}, errkit.NewInternalError("identityprovider.GetUsersByDomain", participant.Email, "user not found in identityprovider")
			}
			kcUser := keycloakUsers[0]
			var photo []byte
			if kcUser.Photo != "" {
				photo = []byte(kcUser.Photo)
			}
			user, err = s.userPrimeDB.Create(ctx, userentity.User{
				CategoryID: 1,
				Email:      participant.Email,
				FullName:   kcUser.FullName,
				Position:   kcUser.Position,
				Photo:      &photo,
			})
			if err != nil {
				return participantentity.ParticipantFull{}, err
			}
		} else {
			return participantentity.ParticipantFull{}, err
		}
	}

	pp, err := s.participantPrimeDB.Create(ctx,
		participantentity.ParticipantCreateData{
			ProductID: participant.ProductID,
			UserID:    user.ID,
		},
	)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	groups, err := s.participantGroupPrimeDB.GetByParticipantID(pp.ID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}
	var groupIDs []int64
	for _, g := range groups {
		groupIDs = append(groupIDs, g.GroupID)
	}

	roles, err := s.participantRolePrimeDB.GetByParticipantID(pp.ID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}
	var roleIDs []int64
	for _, r := range roles {
		roleIDs = append(roleIDs, r.RoleID)
	}

	return participantentity.ParticipantFull{
		ID:          pp.ID,
		ProductID:   pp.ProductID,
		UserID:      pp.UserID,
		Email:       user.Email,
		FullName:    user.FullName,
		Position:    user.Position,
		GroupIDs:    groupIDs,
		RoleIDs:     roleIDs,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   pp.CreatedAt,
		UpdatedAt:   pp.UpdatedAt,
	}, nil
}

func (s *participantDomainService) GetByParticipantIDAndProductID(participantID, productID int64) (participantentity.ParticipantFull, error) {
	product, err := s.productPrimeDB.GetByID(productID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}
	if !product.ActiveFlg {
		return participantentity.ParticipantFull{}, errkit.NewObjectNotFound("product", strconv.FormatInt(productID, 10), "product not found")
	}

	participant, err := s.participantPrimeDB.GetByParticipantIDAndProductID(participantID, productID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	user, err := s.userPrimeDB.GetByID(participant.UserID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	groups, err := s.participantGroupPrimeDB.GetByParticipantID(participant.ID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}
	var groupIDs []int64
	for _, g := range groups {
		groupIDs = append(groupIDs, g.GroupID)
	}

	roles, err := s.participantRolePrimeDB.GetByParticipantID(participant.ID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	var isOwner bool
	ownerRole, err := s.rolePrimeDB.GetOwnerRole()
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	var roleIDs []int64
	for _, r := range roles {
		if r.RoleID == ownerRole.ID {
			isOwner = true
		}
		roleIDs = append(roleIDs, r.RoleID)
	}

	return participantentity.ParticipantFull{
		ID:          participant.ID,
		ProductID:   participant.ProductID,
		UserID:      participant.UserID,
		Email:       user.Email,
		FullName:    user.FullName,
		Position:    user.Position,
		IsOwner:     isOwner,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   participant.CreatedAt,
		UpdatedAt:   participant.UpdatedAt,
		GroupIDs:    groupIDs,
		RoleIDs:     roleIDs,
	}, nil
}

func (s *participantDomainService) GetByProductID(productID int64) ([]participantentity.Participant, error) {
	return s.participantPrimeDB.GetByProductID(productID)
}

func (s *participantDomainService) GetByUserID(userID int64) ([]participantentity.Participant, error) {
	return s.participantPrimeDB.GetByUserID(userID)
}

func (s *participantDomainService) GetByUserIDAndProductID(userID int64, productID int64) (participantentity.Participant, error) {
	return s.participantPrimeDB.GetByUserIDAndProductID(userID, productID)
}

func (s *participantDomainService) GetByUserIDAndProductIDs(userID int64, productIDs []int64) ([]participantentity.Participant, error) {
	return s.participantPrimeDB.GetByUserIDAndProductIDs(userID, productIDs)
}

func (s *participantDomainService) GetOwnersByProductIDs(productIDs []int64) (map[int64][]productentity.Owner, error) {
	allOwners := make(map[int64][]productentity.Owner)
	for _, productID := range productIDs {
		productOwners, err := s.participantPrimeDB.GetOwnersByProductID(productID)
		if err != nil {
			return nil, err
		}
		allOwners[productID] = append(allOwners[productID], productOwners...)
	}
	return allOwners, nil
}

func (s *participantDomainService) GetParticipantFullByParticipantIDAndProductID(participantID, productID int64) (participantentity.ParticipantFull, error) {
	// TODO: check if it's needed
	// product, err := s.productPrimeDB.GetByID(productID)
	// if err != nil {
	// 	return participantentity.ParticipantFull{}, err
	// }
	// if !product.ActiveFlg {
	// 	return participantentity.ParticipantFull{}, errkit.NewObjectNotFound("product", strconv.FormatInt(productID, 10), "product not found")
	// }

	participant, err := s.participantPrimeDB.GetByParticipantIDAndProductID(participantID, productID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	user, err := s.userPrimeDB.GetByID(participant.UserID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	groups, err := s.participantGroupPrimeDB.GetByParticipantID(participant.ID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}
	var groupIDs []int64
	for _, g := range groups {
		groupIDs = append(groupIDs, g.GroupID)
	}

	roles, err := s.participantRolePrimeDB.GetByParticipantID(participant.ID)
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	var isOwner bool
	ownerRole, err := s.rolePrimeDB.GetOwnerRole()
	if err != nil {
		return participantentity.ParticipantFull{}, err
	}

	var roleIDs []int64
	for _, r := range roles {
		if r.RoleID == ownerRole.ID {
			isOwner = true
		}
		roleIDs = append(roleIDs, r.RoleID)
	}

	return participantentity.ParticipantFull{
		ID:          participant.ID,
		ProductID:   participant.ProductID,
		UserID:      participant.UserID,
		Email:       user.Email,
		FullName:    user.FullName,
		Position:    user.Position,
		IsOwner:     isOwner,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   participant.CreatedAt,
		UpdatedAt:   participant.UpdatedAt,
		GroupIDs:    groupIDs,
		RoleIDs:     roleIDs,
	}, nil
}

func (s *participantDomainService) GetParticipantFullByProductID(productID int64) ([]participantentity.ParticipantFull, error) {
	// TODO: check if it's needed
	// product, err := s.productPrimeDB.GetByID(productID)
	// if err != nil {
	// 	return nil, err
	// }
	// if !product.ActiveFlg {
	// 	return nil, errkit.NewObjectNotFound("product", strconv.FormatInt(productID, 10), "product not found")
	// }

	participants, err := s.participantPrimeDB.GetByProductID(productID)
	if err != nil {
		return nil, err
	}

	var participantsService []participantentity.ParticipantFull
	for _, p := range participants {

		user, err := s.userPrimeDB.GetByID(p.UserID)
		if err != nil {
			return nil, err
		}

		groups, err := s.participantGroupPrimeDB.GetByParticipantID(p.ID)
		if err != nil {
			return nil, err
		}
		var groupIDs []int64
		for _, g := range groups {
			groupIDs = append(groupIDs, g.GroupID)
		}

		var isOwner bool
		ownerRole, err := s.rolePrimeDB.GetOwnerRole()
		if err != nil {
			return nil, err
		}

		roles, err := s.participantRolePrimeDB.GetByParticipantID(p.ID)
		if err != nil {
			return nil, err
		}
		var roleIDs []int64
		for _, r := range roles {
			if r.RoleID == ownerRole.ID {
				isOwner = true
			}
			roleIDs = append(roleIDs, r.RoleID)
		}

		participant := participantentity.ParticipantFull{
			ID:          p.ID,
			ProductID:   p.ProductID,
			UserID:      p.UserID,
			Email:       user.Email,
			FullName:    user.FullName,
			Position:    user.Position,
			IsOwner:     isOwner,
			GroupIDs:    groupIDs,
			RoleIDs:     roleIDs,
			LastLoginAt: user.LastLoginAt,
			CreatedAt:   p.CreatedAt,
			UpdatedAt:   p.UpdatedAt,
		}
		participantsService = append(participantsService, participant)
	}

	return participantsService, nil
}

func (s *participantDomainService) ExistByParticipantIDAndProductID(participantID, productID int64) (bool, error) {
	return s.participantPrimeDB.ExistByParticipantIDAndProductID(participantID, productID)
}

func (s *participantDomainService) UpdateGroups(ctx context.Context, participantID int64, groupIDs []int64) ([]int64, error) {
	err := s.participantGroupPrimeDB.DeleteByParticipantID(ctx, participantID)
	if err != nil {
		return nil, err
	}

	groupIDsNew := make([]int64, 0, len(groupIDs))
	for _, groupID := range groupIDs {
		participantGroupNew, err := s.participantGroupPrimeDB.Create(ctx, participantID, groupID)
		if err != nil {
			return nil, err
		}
		groupIDsNew = append(groupIDsNew, participantGroupNew.GroupID)
	}

	return groupIDsNew, nil
}

func (s *participantDomainService) UpdateOwners(productID int64, ownerEmails []string) error {

	// get owner role
	ownerRole, err := s.rolePrimeDB.GetOwnerRole()
	if err != nil {
		return err
	}

	// get actual owners
	actualOwners, err := s.participantPrimeDB.GetOwnersByProductID(productID)
	if err != nil {
		return err
	}

	// actualOwnersEmails
	actualOwnersEmails := make(map[string]productentity.Owner)
	for _, owner := range actualOwners {
		actualOwnersEmails[owner.Email] = owner
	}

	// delete if not in ownerEmails
	for _, actualOwner := range actualOwners {
		if !slices.Contains(ownerEmails, actualOwner.Email) {
			err := s.participantRolePrimeDB.DeleteByParticipantAndRoleID(context.Background(), actualOwner.ParticipantID, ownerRole.ID)
			if err != nil {
				return err
			}
		}
	}

	// create if in ownerEmails
	for _, email := range ownerEmails {
		if _, ok := actualOwnersEmails[email]; !ok {
			var participant participantentity.Participant
			participant, err = s.participantPrimeDB.GetByProductIDAndUserEmail(productID, email)
			if err != nil {
				if err.Error() == "no rows in result set" {
					user, err := s.userPrimeDB.GetByEmail(email)
					if err != nil {
						return err
					}
					newParticipant, err := s.participantPrimeDB.Create(context.Background(), participantentity.ParticipantCreateData{
						UserID:    user.ID,
						ProductID: productID,
					})
					if err != nil {
						return err
					}
					participant = newParticipant
				} else {
					return err
				}
			}
			_, err = s.participantRolePrimeDB.Create(context.Background(), participant.ID, ownerRole.ID)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *participantDomainService) UpdateParticipantByUserUpdateData(userID int64, updData userentity.UserUpdateData) error {

	if updData.ProductIDs == nil {
		return nil
	}

	allPp, err := s.GetByUserID(userID)
	if err != nil {
		return err
	}

	existingProductIDs := make(map[int64]struct{})
	for _, p := range allPp {
		existingProductIDs[p.ProductID] = struct{}{}
	}

	for _, productID := range *updData.ProductIDs {
		_, exists := existingProductIDs[productID]
		if exists {
			continue
		}

		_, err := s.participantPrimeDB.Create(context.Background(), participantentity.ParticipantCreateData{
			UserID:    userID,
			ProductID: productID,
		})
		if err != nil {
			return err
		}
	}

	for _, p := range allPp {
		if !slices.Contains(*updData.ProductIDs, p.ProductID) && p.ProductID != 0 {
			err := s.participantPrimeDB.Delete(context.Background(), p.ID, p.ProductID)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *participantDomainService) Delete(ctx context.Context, participantID, productID int64) error {
	return s.participantPrimeDB.Delete(ctx, participantID, productID)
}

func (s *participantDomainService) DeleteByParticipantIDAndProductID(ctx context.Context, participantID int64, productID int64) error {
	exist, err := s.ExistByParticipantIDAndProductID(participantID, productID)
	if err != nil {
		return err
	}
	if !exist {
		return errors.New("participant not found in product")

	}
	participantData, err := s.GetByParticipantIDAndProductID(participantID, productID)
	if err != nil {
		return err
	}
	userID, ok := ctx.Value(constants.UserIDKey).(int64)
	if !ok {
		return errors.New("user ID is missing in context")
	}

	if participantData.UserID == userID {
		return errors.New("participant cannot delete himself from product")

	}
	return s.Delete(ctx, participantID, productID)
}

func (s *participantDomainService) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error {
	return s.participantPrimeDB.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
}

func (s *participantDomainService) DeleteByUserIDAndProductIDs(ctx context.Context, userID int64, productIDs []int64) error {
	return s.participantPrimeDB.DeleteByUserIDAndProductIDs(ctx, userID, productIDs)
}

func (s *participantDomainService) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error {
	return s.participantPrimeDB.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
}
