package repository

import (
	"context"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
)

//go:generate minimock -i GroupRolePrimeDB -o ../mocks/group_role_prime_db_mock.go -s _mock.go
type GroupRolePrimeDB interface {
	Create(data groupentity.GroupRole) (groupentity.GroupRole, error)
	CreateByGroupIDAndRoleIDs(ctx context.Context, groupID int64, roleIDs []int64) error
	CreateByRoleIDAndGroupIDs(ctx context.Context, roleID int64, groupIDs []int64) error
	GetAll() ([]groupentity.GroupRole, error)
	GetByGroupID(groupID int64) ([]groupentity.GroupRole, error)
	GetByGroupRoleID(groupID, roleID int64) (groupentity.GroupRole, error)
	GetByID(id int64) (groupentity.GroupRole, error)
	GetByRoleID(roleID int64) ([]groupentity.GroupRole, error)
	DeleteByGroupID(ctx context.Context, groupID int64) error
	DeleteByGroupIDsAndRoleID(ctx context.Context, groupIDs []int64, roleID int64) error
	DeleteByRoleIDs(ctx context.Context, roleIDs []int64) error
	DeleteByRoleIDsAndGroupID(ctx context.Context, roleIDs []int64, groupID int64) error
}
