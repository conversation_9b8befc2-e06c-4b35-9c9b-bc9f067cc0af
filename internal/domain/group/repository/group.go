package repository

import (
	"context"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
)

//go:generate minimock -i GroupPrimeDB -o ../mocks/group_prime_db_mock.go -s _mock.go
type GroupPrimeDB interface {
	Create(group groupentity.Group) (groupentity.Group, error)
	GetAll() ([]groupentity.Group, error)
	GetAllByUserID(userID int64) ([]groupentity.Group, error)
	GetAllWithProductIDByUserID(userID int64) ([]groupentity.GroupWithProductID, error)
	GetByFiltersAndPagination(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams) ([]groupentity.Group, int64, error)
	GetByGroupIDAndProductID(groupID, productID int64) (groupentity.Group, error)
	GetByID(id int64) (groupentity.Group, error)
	GetByProductID(productID int64) ([]groupentity.Group, error)
	GetByProductIDAndIsActive(productID int64, isActive bool) ([]groupentity.AdminGroup, error)
	GetGroupCategoryStates() ([]groupentity.GroupCategoryLink, error)
	GetSystemGroups() ([]groupentity.Group, error)
	GetSystemGroupsWithStats() ([]groupentity.GroupWithStats, error)
	ExistByName(name string) (bool, error)
	Update(group groupentity.GroupUpdateData) (groupentity.Group, error)
	DeleteGroup(groupID int64) error
	DeactivateByIDs(groupIDs []int64) error
}

//go:generate minimock -i GroupCache -o ../mocks/group_cache_mock.go -s _mock.go
type GroupCache interface {
	GetGroup(ctx context.Context, id int64) (groupentity.Group, error)
	GetGroups(ctx context.Context) ([]groupentity.Group, error)
	GetProductGroups(ctx context.Context, productID int64) ([]groupentity.Group, error)
	SetGroup(ctx context.Context, group groupentity.Group) error
	SetGroups(ctx context.Context, groups []groupentity.Group) error
	SetProductGroups(ctx context.Context, productID int64, groups []groupentity.Group) error
	DeleteGroup(ctx context.Context, id int64) error
	DeleteGroups(ctx context.Context, ids []int64) error
}
