package entity

import (
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

type AdminGroupsFilter struct {
	Search   string
	Products []int64
	Type     string
	Status   string
}

type SortParams struct {
	Field string
	Order string
}

type PaginationParams struct {
	Limit  int64
	Offset int64
}

type AdminGroup struct {
	ID               int64
	Name             string
	IsActive         bool
	Type             string
	Product          *productentity.Product
	ParticipantCount int64
	UserCount        int64
	RoleCount        int64
}
