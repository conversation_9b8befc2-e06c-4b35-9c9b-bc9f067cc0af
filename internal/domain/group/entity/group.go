package entity

import (
	"time"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

type Group struct {
	ID        int64
	Name      string
	ProductID *int64
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
}

func (g Group) TypeBoolToString() string {
	if g.IsSystem {
		return constants.SystemType
	}
	return constants.CustomType
}

type GroupWithProduct struct {
	ID        int64
	Name      string
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
	Product   *productentity.Product
}

type GroupFull struct {
	ID             int64
	Name           string
	IsSystem       bool
	ParticipantIDs *[]int64
	UserIDs        *[]int64
	RoleIDs        *[]int64
}

type GroupUpdateData struct {
	ID        int64
	Name      *string
	ProductID *int64
	IsSystem  *bool
	ActiveFlg *bool
	DeletedAt *time.Time
}

type GroupRole struct {
	ID        int64
	GroupID   int64
	RoleID    int64
	CreatedAt time.Time
}

type GroupWithCategoryStats struct {
	GroupID          int64
	GroupName        string
	IsSystem         bool
	RoleCount        int64
	ParticipantCount int64
	UserCount        int64
	CategoryStates   []GroupCategoryStates
}

type GroupCategoryStates struct {
	CategoryID   int64
	CategoryName string
	IsActive     bool
}

type GroupWithStats struct {
	GroupID          int64
	GroupName        string
	IsSystem         bool
	RoleCount        int64
	ParticipantCount int64
	UserCount        int64
}

type GroupCategoryLink struct {
	GroupID      int64
	CategoryID   int64
	CategoryName string
	IsActive     bool
}

type GroupWithProductCollection struct {
	GroupID   int64
	GroupName string
	IsSystem  bool
	Product   *userentity.UserProduct
}

type GroupShort struct {
	ID       int64
	Name     string
	IsSystem bool
}

type GroupProductLink struct {
	GroupID   int64
	ProductID *int64
}

type GroupID struct {
	ID int64
}

type GroupWithProductID struct {
	ID        int64
	Name      string
	IsSystem  bool
	ActiveFlg bool
	ProductID *int64
}
