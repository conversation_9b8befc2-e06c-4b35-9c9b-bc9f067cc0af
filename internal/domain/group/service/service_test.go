package service

import (
	"testing"
	"time"

	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

type MockRepositories struct {
	CategoryGroupRepo *categorymocks.CategoryGroupPrimeDBMock
	CategoryRepo      *categorymocks.CategoryPrimeDBMock
	CategoryRoleRepo  *categorymocks.CategoryRolePrimeDBMock
	GroupCache        *groupmocks.GroupCacheMock
	GroupRepo         *groupmocks.GroupPrimeDBMock
	GroupRoleRepo     *groupmocks.GroupRolePrimeDBMock
	ParticipantGroup  *participantmocks.ParticipantGroupPrimeDBMock
	ParticipantRepo   *participantmocks.ParticipantPrimeDBMock
	ProductRepo       *productmocks.ProductPrimeDBMock
	RoleRepo          *rolemocks.RolePrimeDBMock
	UserGroupRepo     *usermocks.UserGroupPrimeDBMock
	UserRepo          *usermocks.UserPrimeDBMock
}

func createMocks(t *testing.T) *MockRepositories {
	return &MockRepositories{
		CategoryGroupRepo: categorymocks.NewCategoryGroupPrimeDBMock(t),
		CategoryRepo:      categorymocks.NewCategoryPrimeDBMock(t),
		CategoryRoleRepo:  categorymocks.NewCategoryRolePrimeDBMock(t),
		GroupCache:        groupmocks.NewGroupCacheMock(t),
		GroupRepo:         groupmocks.NewGroupPrimeDBMock(t),
		GroupRoleRepo:     groupmocks.NewGroupRolePrimeDBMock(t),
		ParticipantGroup:  participantmocks.NewParticipantGroupPrimeDBMock(t),
		ParticipantRepo:   participantmocks.NewParticipantPrimeDBMock(t),
		ProductRepo:       productmocks.NewProductPrimeDBMock(t),
		RoleRepo:          rolemocks.NewRolePrimeDBMock(t),
		UserGroupRepo:     usermocks.NewUserGroupPrimeDBMock(t),
		UserRepo:          usermocks.NewUserPrimeDBMock(t),
	}
}

func createService(mocks *MockRepositories) GroupDomainService {
	return NewGroupDomainService(
		mocks.CategoryGroupRepo,
		mocks.CategoryRepo,
		mocks.CategoryRoleRepo,
		mocks.GroupCache,
		mocks.GroupRepo,
		mocks.GroupRoleRepo,
		mocks.ParticipantGroup,
		mocks.ParticipantRepo,
		mocks.ProductRepo,
		mocks.RoleRepo,
		mocks.UserGroupRepo,
		mocks.UserRepo,
	)
}

func createTestGroup() groupentity.Group {
	now := time.Now()
	return groupentity.Group{
		ID:        1,
		Name:      "Test Group",
		ProductID: int64Ptr(1),
		IsSystem:  false,
		ActiveFlg: true,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

func createTestSystemGroup() groupentity.Group {
	now := time.Now()
	return groupentity.Group{
		ID:        2,
		Name:      "System Group",
		ProductID: nil,
		IsSystem:  true,
		ActiveFlg: true,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

func createTestProduct() productentity.Product {
	return productentity.Product{
		ID:       1,
		IID:      "test-product",
		TechName: "test_product",
		Name:     "Test Product",
	}
}

func int64Ptr(i int64) *int64 {
	return &i
}
