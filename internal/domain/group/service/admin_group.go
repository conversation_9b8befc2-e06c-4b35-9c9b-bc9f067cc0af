package service

import (
	"context"
	"errors"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

func (s *groupDomainService) CreateAsAdmin(ctx context.Context, data groupentity.Group) (groupentity.Group, error) {
	isExist, err := s.groupRepo.ExistByName(data.Name)
	if err != nil {
		return groupentity.Group{}, err
	}
	if isExist {
		return groupentity.Group{}, errors.New("group with these parameters already exists")
	}

	return s.groupRepo.Create(data)
}

func (s *groupDomainService) GetAllAsAdmin() ([]groupentity.AdminGroup, error) {
	groups, err := s.groupRepo.GetAll()
	if err != nil {
		return nil, err
	}

	adminGroups := make([]groupentity.AdminGroup, 0, len(groups))
	for _, group := range groups {
		adminGroup, err := s.buildAdminGroup(group)
		if err != nil {
			return nil, err
		}
		adminGroups = append(adminGroups, adminGroup)
	}

	return adminGroups, nil
}

func (s *groupDomainService) DeleteAsAdmin(groupID int64) error {
	return s.groupRepo.DeleteGroup(groupID)
}

func (s *groupDomainService) buildAdminGroup(group groupentity.Group) (groupentity.AdminGroup, error) {
	adminGroup := groupentity.AdminGroup{
		ID:       group.ID,
		Name:     group.Name,
		IsActive: group.ActiveFlg,
		Type:     constants.IsSystemToConstant(group.IsSystem),
	}

	var err error
	adminGroup.UserCount, err = s.calculateUserCount(group)
	if err != nil {
		return groupentity.AdminGroup{}, err
	}

	adminGroup.RoleCount, err = s.calculateRoleCount(group.ID)
	if err != nil {
		return groupentity.AdminGroup{}, err
	}

	if group.ProductID != nil {
		product, err := s.getProductForGroup(*group.ProductID)
		if err != nil {
			return groupentity.AdminGroup{}, err
		}
		adminGroup.Product = product
	}

	return adminGroup, nil
}

func (s *groupDomainService) calculateRoleCount(groupID int64) (int64, error) {
	groupRoles, err := s.groupRoleRepo.GetByGroupID(groupID)
	if err != nil {
		return 0, err
	}
	return int64(len(groupRoles)), nil
}

func (s *groupDomainService) calculateUserCount(group groupentity.Group) (int64, error) {
	if group.ProductID != nil {
		return s.calculateUserCountForProductGroup(*group.ProductID, group.ID)
	}
	return s.calculateUserCountForSystemGroup(group.ID)
}

func (s *groupDomainService) calculateUserCountForProductGroup(productID, groupID int64) (int64, error) {
	participants, err := s.participantRepo.GetByProductID(productID)
	if err != nil {
		return 0, err
	}

	userCount := 0
	for _, participant := range participants {
		isInGroup, err := s.isParticipantInGroup(participant.ID, groupID)
		if err != nil {
			return 0, err
		}
		if isInGroup {
			userCount++
		}
	}

	return int64(userCount), nil
}

func (s *groupDomainService) calculateUserCountForSystemGroup(groupID int64) (int64, error) {
	userGroups, err := s.userGroupRepo.GetByGroupID(groupID)
	if err != nil {
		return 0, err
	}
	return int64(len(userGroups)), nil
}

func (s *groupDomainService) getProductForGroup(productID int64) (*productentity.Product, error) {
	product, err := s.productRepo.GetByID(productID)
	if err != nil {
		return nil, err
	}

	return &productentity.Product{
		ID:       product.ID,
		IID:      product.IID,
		TechName: product.TechName,
		Name:     product.Name,
	}, nil
}

func (s *groupDomainService) isParticipantInGroup(participantID, groupID int64) (bool, error) {
	participantGroups, err := s.participantGroup.GetByParticipantID(participantID)
	if err != nil {
		return false, err
	}

	for _, pg := range participantGroups {
		if pg.GroupID == groupID {
			return true, nil
		}
	}
	return false, nil
}
