package service

import (
	"context"
	"slices"
	"sort"
	"strconv"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	groupaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/aggregate"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

func (s *groupDomainService) Create(group groupentity.Group) (groupentity.GroupWithStats, error) {

	groupCreated, err := s.groupRepo.Create(group)
	if err != nil {
		return groupentity.GroupWithStats{}, err
	}

	err = s.assignToSystemCategory(groupCreated.ID, group.IsSystem)
	if err != nil {
		return groupentity.GroupWithStats{}, err
	}

	return groupentity.GroupWithStats{
		GroupID:   groupCreated.ID,
		GroupName: groupCreated.Name,
		IsSystem:  groupCreated.IsSystem,
	}, nil
}

func (s *groupDomainService) AssignToRoles(ctx context.Context, groupID int64, rolesIDs []int64) error {
	err := s.groupRoleRepo.CreateByGroupIDAndRoleIDs(ctx, groupID, rolesIDs)
	if err != nil {
		return err
	}

	return nil
}

func (s *groupDomainService) GetAll() ([]groupentity.Group, error) {
	return s.groupRepo.GetAll()
}

func (s *groupDomainService) GetAvailableSystemGroups(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) ([]groupentity.GroupWithStats, error) {
	var availableUserGroups []groupentity.GroupWithStats
	for _, g := range groups {
		if slices.Contains(userGroups, g.GroupID) && g.IsSystem {
			availableUserGroups = append(availableUserGroups, g)
		}
	}

	for _, cg := range *category.Groups {
		found := false
		for _, g := range availableUserGroups {
			if g.GroupID == cg.ID {
				found = true
				break
			}
		}
		if !found && cg.IsSystem {
			for _, g := range groups {
				if g.GroupID == cg.ID {
					availableUserGroups = append(availableUserGroups, g)
					break
				}
			}
		}
	}

	return availableUserGroups, nil
}

func (s *groupDomainService) GetByCategoryID(categoryID int64) ([]groupentity.GroupWithStats, error) {
	catGroups, err := s.categoryGroupRepo.GetByCategoryID(categoryID)
	if err != nil {
		return nil, err
	}

	var groupShorts []groupentity.GroupWithStats
	for _, catGroup := range catGroups {
		group, err := s.groupRepo.GetByID(catGroup.GroupID)
		if err != nil {
			return nil, err
		}
		groupShorts = append(groupShorts, groupentity.GroupWithStats{
			GroupID:   group.ID,
			GroupName: group.Name,
		})
	}
	return groupShorts, nil
}

func (s *groupDomainService) GetByGroupIDAndProductID(groupID, productID int64) (groupentity.GroupFull, error) {
	var (
		group           groupentity.Group
		participantsIDs []int64
		userIDs         []int64
		roleIDs         []int64
		err             error
	)

	if productID != 0 {
		group, err = s.groupRepo.GetByGroupIDAndProductID(groupID, productID)
		if err != nil {
			return groupentity.GroupFull{}, err
		}
		if !group.ActiveFlg {
			return groupentity.GroupFull{}, errkit.NewObjectNotFound(errkit.ObjectTypeGroup, strconv.FormatInt(groupID, 10), errkit.StateNotFound)
		}

		participantsIDs, err = s.fetchParticipantIDs(groupID)
		if err != nil {
			return groupentity.GroupFull{}, err
		}
	} else {
		group, err = s.groupRepo.GetByID(groupID)
		if err != nil {
			return groupentity.GroupFull{}, err
		}

		userIDs, err = s.fetchUserIDs(groupID)
		if err != nil {
			return groupentity.GroupFull{}, err
		}
	}

	roleIDs, err = s.fetchRoleIDs(groupID)
	if err != nil {
		return groupentity.GroupFull{}, err
	}

	return groupentity.GroupFull{
		ID:             group.ID,
		Name:           group.Name,
		IsSystem:       group.IsSystem,
		ParticipantIDs: sharedentity.SlicePtrOrNil(participantsIDs),
		RoleIDs:        sharedentity.SlicePtrOrNil(roleIDs),
		UserIDs:        sharedentity.SlicePtrOrNil(userIDs),
	}, nil
}

func (s *groupDomainService) GetByID(id int64) (groupentity.Group, error) {
	return s.groupRepo.GetByID(id)
}

func (s *groupDomainService) GetByParticipantID(participantID int64) ([]groupentity.GroupWithStats, error) {
	participantGroups, err := s.participantGroup.GetByParticipantID(participantID)
	if err != nil {
		return nil, err
	}

	var groupShorts []groupentity.GroupWithStats
	for _, participantGroup := range participantGroups {
		group, err := s.groupRepo.GetByID(participantGroup.GroupID)
		if err != nil {
			return nil, err
		}
		groupShorts = append(groupShorts, groupentity.GroupWithStats{
			GroupID:   group.ID,
			GroupName: group.Name,
		})
	}

	return groupShorts, nil
}

func (s *groupDomainService) GetByProductID(productID int64) ([]groupentity.Group, error) {
	return s.groupRepo.GetByProductID(productID)
}

func (s *groupDomainService) GetByRoleID(roleID int64) ([]groupentity.Group, error) {
	groupRoles, err := s.groupRoleRepo.GetByRoleID(roleID)
	if err != nil {
		return nil, err
	}

	groups := make([]groupentity.Group, 0, len(groupRoles))
	for _, groupRole := range groupRoles {
		group, err := s.groupRepo.GetByID(groupRole.GroupID)
		if err != nil {
			return nil, err
		}
		groups = append(groups, group)
	}

	return groups, nil
}

func (s *groupDomainService) GetByUserID(userID int64) ([]groupentity.Group, error) {
	userGroups, err := s.userGroupRepo.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	var groups []groupentity.Group
	for _, userGroup := range userGroups {
		group, err := s.groupRepo.GetByID(userGroup.GroupID)
		if err != nil {
			return nil, err
		}
		groups = append(groups, group)
	}

	return groups, nil
}

func (s *groupDomainService) GetGroupsWithCategoryStats() ([]groupentity.GroupWithCategoryStats, error) {
	groups, err := s.groupRepo.GetSystemGroupsWithStats()
	if err != nil {
		return nil, err
	}

	groupCategoryLink, err := s.groupRepo.GetGroupCategoryStates()
	if err != nil {
		return nil, err
	}

	return aggregateGroupsWithCategoryStats(groups, groupCategoryLink), nil
}

func (s *groupDomainService) GetParticipantGroupsWithProductByUserID(userID int64) ([]groupentity.Group, error) {
	return s.userGroupRepo.GetParticipantGroupsByUserID(userID)
}

func (s *groupDomainService) GetSystemGroupsWithStats() ([]groupentity.GroupWithStats, error) {
	return s.groupRepo.GetSystemGroupsWithStats()
}

func (s *groupDomainService) GetWithCountsByProductID(productID int64) ([]groupentity.AdminGroup, error) {
	return s.groupRepo.GetByProductIDAndIsActive(productID, true)
}

func (s *groupDomainService) GetWithProductByUserID(userID int64) ([]groupentity.GroupWithProduct, error) {
	groups, err := s.groupRepo.GetAllWithProductIDByUserID(userID)
	if err != nil {
		return nil, err
	}

	groupsWithProduct := make([]groupentity.GroupWithProduct, 0, len(groups))
	for _, group := range groups {
		if group.ProductID == nil {
			groupsWithProduct = append(groupsWithProduct, groupentity.GroupWithProduct{
				ID:        group.ID,
				Name:      group.Name,
				IsSystem:  group.IsSystem,
				ActiveFlg: group.ActiveFlg,
			})
			continue
		}

		product, err := s.productRepo.GetByID(*group.ProductID)
		if err != nil {
			return nil, err
		}

		groupsWithProduct = append(groupsWithProduct, groupentity.GroupWithProduct{
			ID:        group.ID,
			Name:      group.Name,
			Product:   product.ProductOrNil(),
			IsSystem:  group.IsSystem,
			ActiveFlg: group.ActiveFlg,
		})
	}

	return groupsWithProduct, nil
}

func (s *groupDomainService) GetWithProductsByRoleID(roleID int64) ([]groupentity.GroupWithProduct, error) {
	groups, err := s.groupRoleRepo.GetByRoleID(roleID)
	if err != nil {
		return nil, err
	}

	groupsWithProduct := make([]groupentity.GroupWithProduct, 0, len(groups))
	for _, group := range groups {
		group, err := s.groupRepo.GetByID(group.GroupID)
		if err != nil {
			return nil, err
		}
		if group.ProductID == nil {
			groupsWithProduct = append(groupsWithProduct, groupentity.GroupWithProduct{
				ID:        group.ID,
				Name:      group.Name,
				IsSystem:  group.IsSystem,
				ActiveFlg: group.ActiveFlg,
				CreatedAt: group.CreatedAt,
				UpdatedAt: group.UpdatedAt,
				DeletedAt: group.DeletedAt,
			})
			continue
		}
		product, err := s.productRepo.GetByID(*group.ProductID)
		if err != nil {
			return nil, err
		}
		groupsWithProduct = append(groupsWithProduct, groupentity.GroupWithProduct{
			ID:        group.ID,
			Name:      group.Name,
			Product:   product.ProductOrNil(),
			IsSystem:  group.IsSystem,
			ActiveFlg: group.ActiveFlg,
			CreatedAt: group.CreatedAt,
			UpdatedAt: group.UpdatedAt,
			DeletedAt: group.DeletedAt,
		})
	}

	return groupsWithProduct, nil
}

func (s *groupDomainService) GetWithStatsByProductID(productID int64) ([]groupentity.GroupWithStats, error) {
	groups, err := s.groupRepo.GetByProductID(productID)
	if err != nil {
		return nil, err
	}

	sort.Slice(groups, func(i, j int) bool {
		return groups[i].UpdatedAt.After(groups[j].UpdatedAt)
	})

	return s.getFilteredGroupShorts(groups, func(group groupentity.Group) bool {
		return group.ActiveFlg
	})
}

func (s *groupDomainService) Update(group groupentity.GroupUpdateData) (groupentity.Group, error) {
	return s.groupRepo.Update(group)
}

func (s *groupDomainService) UpdateByGroupFull(ctx context.Context, group groupentity.GroupFull, productID int64) (groupentity.GroupFull, error) {
	groupUpdated, err := s.groupRepo.Update(groupentity.GroupUpdateData{
		ID:        group.ID,
		Name:      sharedentity.StringPtrOrNil(group.Name),
		ProductID: &productID,
		IsSystem:  &group.IsSystem,
	})
	if err != nil {
		return groupentity.GroupFull{}, err
	}

	var participantsIDs []int64
	if group.ParticipantIDs != nil {
		participantsIDs, err = s.UpdateParticipantGroups(ctx, group.ID, *group.ParticipantIDs)
		if err != nil {
			return groupentity.GroupFull{}, err
		}
	}

	var userIDsNew []int64
	if group.UserIDs != nil {
		userIDsNew, err = s.updateUserGroupsInternal(ctx, group.ID, *group.UserIDs)
		if err != nil {
			return groupentity.GroupFull{}, err
		}
	}

	var roleIDsNew []int64
	if group.RoleIDs != nil {
		roleIDsNew, err = s.updateGroupRoles(ctx, group.ID, *group.RoleIDs)
		if err != nil {
			return groupentity.GroupFull{}, err
		}
	}

	return groupentity.GroupFull{
		ID:             groupUpdated.ID,
		Name:           groupUpdated.Name,
		IsSystem:       groupUpdated.IsSystem,
		ParticipantIDs: sharedentity.SlicePtrOrNil(participantsIDs),
		RoleIDs:        sharedentity.SlicePtrOrNil(roleIDsNew),
		UserIDs:        sharedentity.SlicePtrOrNil(userIDsNew),
	}, nil
}

func (s *groupDomainService) UpdateCategoryStates(groupsWithCategories []groupentity.GroupWithCategoryStats) error {
	existingCategoryGroupLinks, err := s.getExistingCategoryGroupLinks()
	if err != nil {
		return err
	}

	groupAggregate := groupaggregate.NewGroupAggregate()
	groupAggregate.CacheCategoryGroupLinks(existingCategoryGroupLinks)

	for _, groupWithCategory := range groupsWithCategories {
		for _, category := range groupWithCategory.CategoryStates {
			hasRoleLink := groupAggregate.HasCategoryGroupLink(category.CategoryID, groupWithCategory.GroupID)
			if category.IsActive && !hasRoleLink {
				if _, err := s.categoryGroupRepo.Create(categoryentity.CategoryGroupLink{
					CategoryID: category.CategoryID,
					GroupID:    groupWithCategory.GroupID,
				}); err != nil {
					return err
				}
			} else if !category.IsActive && hasRoleLink {
				if err := s.categoryGroupRepo.Delete(category.CategoryID, groupWithCategory.GroupID); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (s *groupDomainService) UpdateLinksWithRoles(ctx context.Context, groupID int64, roles []roleentity.RoleID) error {

	roleGroupsOld, err := s.groupRoleRepo.GetByGroupID(groupID)
	if err != nil {
		return err
	}

	var rolesNew []roleentity.RoleID
	for _, r := range roles {
		rolesNew = append(rolesNew, roleentity.RoleID{ID: r.ID})
	}

	toDelete, toAdd := diffRoleIDsForGroup(roleGroupsOld, rolesNew)

	if len(toDelete) > 0 {
		err = s.UnassignFromRoles(ctx, groupID, toDelete)
		if err != nil {
			return err
		}
	}

	if len(toAdd) > 0 {
		err = s.AssignToRoles(ctx, groupID, toAdd)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *groupDomainService) UpdateLinksWithUsers(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) error {

	/*
		WITHOUT PRODUCTS. AS USERS
	*/

	existingUsersGroups, err := s.userGroupRepo.GetByGroupID(groupID)
	if err != nil {
		return err
	}

	toDeleteForUsers, toAddForUsers := diffUserIDsForGroup(existingUsersGroups, userLinks)

	if len(toDeleteForUsers) > 0 {
		err = s.userGroupRepo.DeleteByGroupIDAndUserIDs(ctx, groupID, toDeleteForUsers)
		if err != nil {
			return err
		}
	}

	if len(toAddForUsers) > 0 {
		err = s.userGroupRepo.CreateByGroupIDAndUserIDs(ctx, groupID, toAddForUsers)
		if err != nil {
			return err
		}
	}

	/*
		WITH PRODUCTS. AS PARTICIPANTS
	*/

	usersProducts := make(map[int64][]int64)
	for _, link := range userLinks {
		if link.ProductID == nil {
			continue
		}
		usersProducts[link.UserID] = append(usersProducts[link.UserID], *link.ProductID)
	}

	var existingParticipantIDs []int64
	for userID, productIDs := range usersProducts {
		userParticipants, err := s.participantRepo.GetByUserIDAndProductIDs(userID, productIDs)
		if err != nil {
			return err
		}
		for _, participant := range userParticipants {
			existingParticipantIDs = append(existingParticipantIDs, participant.ID)
		}
	}

	existingParticipantsGroups, err := s.participantGroup.GetByGroupID(groupID)
	if err != nil {
		return err
	}

	toDeleteForParticipants, toAddForParticipants := diffParticipantIDsForGroup(existingParticipantsGroups, existingParticipantIDs)

	if len(toDeleteForParticipants) > 0 {
		err = s.participantGroup.DeleteByRoleIDAndParticipantIDs(ctx, groupID, toDeleteForParticipants)
		if err != nil {
			return err
		}
	}

	if len(toAddForParticipants) > 0 {
		err = s.participantGroup.CreateByRoleIDAndParticipantIDs(ctx, groupID, toAddForParticipants)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *groupDomainService) UpdateParticipantGroups(ctx context.Context, groupID int64, participantIDs []int64) ([]int64, error) {
	var participantsIDs []int64
	if len(participantIDs) > 0 {
		if err := s.participantGroup.DeleteByGroupID(ctx, groupID); err != nil {
			return nil, err
		}

		for _, participantID := range participantIDs {
			_, err := s.participantGroup.Create(ctx, participantID, groupID)
			if err != nil {
				return nil, err
			}
			participantsIDs = append(participantsIDs, participantID)
		}
	} else {
		existingParticipantGroups, err := s.participantGroup.GetByGroupID(groupID)
		if err != nil {
			return nil, err
		}
		for _, pg := range existingParticipantGroups {
			participantsIDs = append(participantsIDs, pg.ParticipantID)
		}
	}

	return participantsIDs, nil
}

func (s *groupDomainService) UpdateUserGroups(ctx context.Context, userID int64, user userentity.UserUpdateData) error {

	if user.GroupIDs == nil {
		return nil
	}

	userGroups, err := s.userGroupRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	groupIDMap := make(map[int64]struct{}, len(*user.GroupIDs))
	for _, id := range *user.GroupIDs {
		groupIDMap[id] = struct{}{}
	}

	existingGroupIDs := make(map[int64]struct{})
	for _, userGroup := range userGroups {
		existingGroupIDs[userGroup.GroupID] = struct{}{}
	}

	for _, groupID := range *user.GroupIDs {
		_, exists := existingGroupIDs[groupID]
		if exists {
			continue
		}

		_, err = s.userGroupRepo.Create(ctx, userentity.UserGroupCreateData{
			UserID:  userID,
			GroupID: groupID,
		})
		if err != nil {
			return err
		}
	}

	var toDeleteUserGroups []int64
	for _, userGroup := range userGroups {
		if _, exists := groupIDMap[userGroup.GroupID]; !exists {
			toDeleteUserGroups = append(toDeleteUserGroups, userGroup.GroupID)
		}
	}

	if len(toDeleteUserGroups) > 0 {
		if err := s.userGroupRepo.DeleteByUserIDAndGroupIDs(ctx, userID, toDeleteUserGroups); err != nil {
			return err
		}
	}

	return nil
}

func (s *groupDomainService) Delete(productID, groupID int64) error {

	if productID != 0 {
		_, err := s.groupRepo.GetByGroupIDAndProductID(groupID, productID)
		if err != nil {
			return err
		}
	}

	flagFalse := false
	_, err := s.groupRepo.Update(groupentity.GroupUpdateData{
		ID:        groupID,
		ActiveFlg: &flagFalse,
	})

	return err
}

func (s *groupDomainService) UnassignFromRoles(ctx context.Context, groupID int64, roleIDs []int64) error {
	return s.groupRoleRepo.DeleteByRoleIDsAndGroupID(ctx, roleIDs, groupID)
}

func (s *groupDomainService) assignToSystemCategory(groupID int64, isSystem bool) error {
	if isSystem {
		_, err := s.categoryGroupRepo.Create(categoryentity.CategoryGroupLink{
			CategoryID: constants.CategorySystemID,
			GroupID:    groupID,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *groupDomainService) fetchParticipantIDs(groupID int64) ([]int64, error) {
	participantGroups, err := s.participantGroup.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	var participantIDs []int64
	for _, pg := range participantGroups {
		participantIDs = append(participantIDs, pg.ParticipantID)
	}

	return participantIDs, nil
}

func (s *groupDomainService) fetchRoleIDs(groupID int64) ([]int64, error) {
	groupRoles, err := s.groupRoleRepo.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	var roleIDs []int64
	for _, gr := range groupRoles {
		role, err := s.roleRepo.GetByID(gr.RoleID)
		if err != nil {
			return nil, err
		}
		roleIDs = append(roleIDs, role.ID)
	}

	if len(roleIDs) > 0 {
		return roleIDs, nil
	}

	return nil, nil
}

func (s *groupDomainService) fetchUserIDs(groupID int64) ([]int64, error) {
	userGroups, err := s.userGroupRepo.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	var userIDs []int64
	for _, ug := range userGroups {
		userIDs = append(userIDs, ug.UserID)
	}

	if len(userIDs) > 0 {
		return userIDs, nil
	}

	return nil, nil
}

func (s *groupDomainService) getExistingCategoryGroupLinks() (map[int64]map[int64]struct{}, error) {
	allRelations, err := s.categoryGroupRepo.GetAll()
	if err != nil {
		return nil, err
	}

	result := make(map[int64]map[int64]struct{})
	for _, rel := range allRelations {
		if result[rel.CategoryID] == nil {
			result[rel.CategoryID] = make(map[int64]struct{})
		}
		result[rel.CategoryID][rel.GroupID] = struct{}{}
	}

	return result, nil
}

func (s *groupDomainService) getFilteredGroupShorts(groupsRepo []groupentity.Group, filter func(groupentity.Group) bool) ([]groupentity.GroupWithStats, error) {
	var groups []groupentity.GroupWithStats
	for _, group := range groupsRepo {
		if !filter(group) {
			continue
		}

		roleGroups, err := s.groupRoleRepo.GetByGroupID(group.ID)
		if err != nil {
			return nil, err
		}

		participantGroups, err := s.participantGroup.GetByGroupID(group.ID)
		if err != nil {
			return nil, err
		}

		userGroups, err := s.userGroupRepo.GetByGroupID(group.ID)
		if err != nil {
			return nil, err
		}

		groups = append(groups, groupentity.GroupWithStats{
			GroupID:          group.ID,
			GroupName:        group.Name,
			IsSystem:         group.IsSystem,
			RoleCount:        sharedentity.SliceLen(roleGroups),
			ParticipantCount: sharedentity.SliceLen(participantGroups),
			UserCount:        sharedentity.SliceLen(userGroups),
		})
	}

	return groups, nil
}

func (s *groupDomainService) updateGroupRoles(ctx context.Context, groupID int64, roleIDs []int64) ([]int64, error) {
	var roleIDsNew []int64
	if len(roleIDs) > 0 {
		if err := s.groupRoleRepo.DeleteByGroupID(ctx, groupID); err != nil {
			return nil, err
		}

		for _, roleID := range roleIDs {
			groupRoleDB, err := s.groupRoleRepo.Create(groupentity.GroupRole{
				GroupID: groupID,
				RoleID:  roleID,
			})
			if err != nil {
				return nil, err
			}
			roleIDsNew = append(roleIDsNew, groupRoleDB.RoleID)
		}
	} else {
		existingGroupRoles, err := s.groupRoleRepo.GetByGroupID(groupID)
		if err != nil {
			return nil, err
		}
		for _, gr := range existingGroupRoles {
			roleIDsNew = append(roleIDsNew, gr.RoleID)
		}
	}

	return roleIDsNew, nil
}

func (s *groupDomainService) updateUserGroupsInternal(ctx context.Context, groupID int64, userIDs []int64) ([]int64, error) {
	var userIDsNew []int64
	if len(userIDs) > 0 {
		if err := s.userGroupRepo.DeleteByGroupID(ctx, groupID); err != nil {
			return nil, err
		}

		for _, userID := range userIDs {
			_, err := s.userGroupRepo.Create(ctx, userentity.UserGroupCreateData{
				UserID:  userID,
				GroupID: groupID,
			})
			if err != nil {
				return nil, err
			}
			userIDsNew = append(userIDsNew, userID)
		}
	} else {
		existingUserGroups, err := s.userGroupRepo.GetByGroupID(groupID)
		if err != nil {
			return nil, err
		}
		for _, ug := range existingUserGroups {
			userIDsNew = append(userIDsNew, ug.UserID)
		}
	}

	return userIDsNew, nil
}

func aggregateGroupsWithCategoryStats(groups []groupentity.GroupWithStats, groupCategoryLink []groupentity.GroupCategoryLink) []groupentity.GroupWithCategoryStats {
	catMap := make(map[int64][]groupentity.GroupCategoryStates, len(groups))
	for _, link := range groupCategoryLink {
		catMap[link.GroupID] = append(catMap[link.GroupID], groupentity.GroupCategoryStates{
			CategoryID:   link.CategoryID,
			CategoryName: link.CategoryName,
			IsActive:     link.IsActive,
		})
	}

	result := make([]groupentity.GroupWithCategoryStats, 0, len(groups))
	for _, group := range groups {
		result = append(result, groupentity.GroupWithCategoryStats{
			GroupID:          group.GroupID,
			GroupName:        group.GroupName,
			IsSystem:         group.IsSystem,
			RoleCount:        group.RoleCount,
			ParticipantCount: group.ParticipantCount,
			UserCount:        group.UserCount,
			CategoryStates:   catMap[group.GroupID],
		})
	}

	return result
}

func diffParticipantIDsForGroup(participantsOld []participantentity.ParticipantGroup, participantIDsNew []int64) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		participantsOld,
		participantIDsNew,
		func(p participantentity.ParticipantGroup) int64 { return p.ParticipantID },
		func(id int64) int64 { return id },
	)
}

func diffRoleIDsForGroup(roleGroupsOld []groupentity.GroupRole, groupsNew []roleentity.RoleID) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		roleGroupsOld,
		groupsNew,
		func(gr groupentity.GroupRole) int64 { return gr.RoleID },
		func(r roleentity.RoleID) int64 { return r.ID },
	)
}

func diffUserIDsForGroup(usersOld []userentity.UserGroup, usersNew []userentity.UserProductLink) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		usersOld,
		usersNew,
		func(ug userentity.UserGroup) int64 { return ug.UserID },
		func(u userentity.UserProductLink) int64 {
			if u.ProductID == nil {
				return u.UserID
			}
			return 0 // Skip users with ProductID set
		},
	)
}
