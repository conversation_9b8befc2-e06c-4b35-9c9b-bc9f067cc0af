package service

import (
	"context"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categoryrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	participantrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	productrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolerepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

//go:generate minimock -i GroupDomainService -o ../mocks/group_domain_service_mock.go -s _mock.go
type GroupDomainService interface {
	Create(group groupentity.Group) (groupentity.GroupWithStats, error)
	CreateAsAdmin(ctx context.Context, data groupentity.Group) (groupentity.Group, error)
	AssignToRoles(ctx context.Context, groupID int64, rolesIDs []int64) error
	GetAll() ([]groupentity.Group, error)
	GetAllAsAdmin() ([]groupentity.AdminGroup, error)
	GetAvailableSystemGroups(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) ([]groupentity.GroupWithStats, error)
	GetByCategoryID(categoryID int64) ([]groupentity.GroupWithStats, error)
	GetByGroupIDAndProductID(groupID, productID int64) (groupentity.GroupFull, error)
	GetByID(id int64) (groupentity.Group, error)
	GetByParticipantID(participantID int64) ([]groupentity.GroupWithStats, error)
	GetByProductID(productID int64) ([]groupentity.Group, error)
	GetByRoleID(roleID int64) ([]groupentity.Group, error)
	GetByUserID(userID int64) ([]groupentity.Group, error)
	GetGroupsWithCategoryStats() ([]groupentity.GroupWithCategoryStats, error)
	GetParticipantGroupsWithProductByUserID(userID int64) ([]groupentity.Group, error)
	GetSystemGroupsWithStats() ([]groupentity.GroupWithStats, error)
	GetWithCountsByProductID(productID int64) ([]groupentity.AdminGroup, error)
	GetWithProductByUserID(userID int64) ([]groupentity.GroupWithProduct, error)
	GetWithProductsByRoleID(roleID int64) ([]groupentity.GroupWithProduct, error)
	GetWithStatsByProductID(productID int64) ([]groupentity.GroupWithStats, error)
	Update(group groupentity.GroupUpdateData) (groupentity.Group, error)
	UpdateByGroupFull(ctx context.Context, group groupentity.GroupFull, productID int64) (groupentity.GroupFull, error)
	UpdateCategoryStates(groupsWithCategories []groupentity.GroupWithCategoryStats) error
	UpdateLinksWithRoles(ctx context.Context, groupID int64, roles []roleentity.RoleID) error
	UpdateLinksWithUsers(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) error
	UpdateParticipantGroups(ctx context.Context, groupID int64, participantIDs []int64) ([]int64, error)
	UpdateUserGroups(ctx context.Context, userID int64, user userentity.UserUpdateData) error
	Delete(productID, groupID int64) error
	DeleteAsAdmin(groupID int64) error
	UnassignFromRoles(ctx context.Context, groupID int64, roleIDs []int64) error
}

type groupDomainService struct {
	categoryGroupRepo categoryrepository.CategoryGroupPrimeDB
	categoryRepo      categoryrepository.CategoryPrimeDB
	categoryRoleRepo  categoryrepository.CategoryRolePrimeDB
	groupCache        grouprepository.GroupCache
	groupRepo         grouprepository.GroupPrimeDB
	groupRoleRepo     grouprepository.GroupRolePrimeDB
	participantGroup  participantrepository.ParticipantGroupPrimeDB
	participantRepo   participantrepository.ParticipantPrimeDB
	productRepo       productrepository.ProductPrimeDB
	roleRepo          rolerepository.RolePrimeDB
	userGroupRepo     userrepository.UserGroupPrimeDB
	userRepo          userrepository.UserPrimeDB
}

func NewGroupDomainService(
	categoryGroupRepo categoryrepository.CategoryGroupPrimeDB,
	categoryRepo categoryrepository.CategoryPrimeDB,
	categoryRoleRepo categoryrepository.CategoryRolePrimeDB,
	groupCache grouprepository.GroupCache,
	groupRepo grouprepository.GroupPrimeDB,
	groupRoleRepo grouprepository.GroupRolePrimeDB,
	participantGroup participantrepository.ParticipantGroupPrimeDB,
	participantRepo participantrepository.ParticipantPrimeDB,
	productRepo productrepository.ProductPrimeDB,
	roleRepo rolerepository.RolePrimeDB,
	userGroupRepo userrepository.UserGroupPrimeDB,
	userRepo userrepository.UserPrimeDB,
) GroupDomainService {
	return &groupDomainService{
		categoryGroupRepo: categoryGroupRepo,
		categoryRepo:      categoryRepo,
		categoryRoleRepo:  categoryRoleRepo,
		groupCache:        groupCache,
		groupRepo:         groupRepo,
		groupRoleRepo:     groupRoleRepo,
		participantGroup:  participantGroup,
		participantRepo:   participantRepo,
		productRepo:       productRepo,
		roleRepo:          roleRepo,
		userGroupRepo:     userGroupRepo,
		userRepo:          userRepo,
	}
}
