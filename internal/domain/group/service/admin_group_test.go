package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

func TestGroupDomainService_CreateAsAdmin_Success(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	inputGroup := groupentity.Group{
		Name:      "Test Group",
		ProductID: int64Ptr(1),
	}

	expectedGroup := createTestGroup()
	expectedGroup.Name = "Test Group"

	mocks.GroupRepo.ExistByNameMock.Expect("Test Group").Return(false, nil)
	mocks.GroupRepo.CreateMock.Expect(inputGroup).Return(expectedGroup, nil)

	result, err := svc.CreateAsAdmin(context.Background(), inputGroup)

	require.NoError(t, err)
	require.Equal(t, expectedGroup, result)
	require.Equal(t, uint64(1), mocks.GroupRepo.ExistByNameAfterCounter())
	require.Equal(t, uint64(1), mocks.GroupRepo.CreateAfterCounter())
}

func TestGroupDomainService_CreateAsAdmin_GroupExistsError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	inputGroup := groupentity.Group{
		Name:      "Test Group",
		ProductID: int64Ptr(1),
	}

	mocks.GroupRepo.ExistByNameMock.Expect("Test Group").Return(true, nil)

	_, err := svc.CreateAsAdmin(context.Background(), inputGroup)

	require.Error(t, err)
	require.Equal(t, "group with these parameters already exists", err.Error())
}

func TestGroupDomainService_CreateAsAdmin_ExistByNameError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	inputGroup := groupentity.Group{
		Name:      "Test Group",
		ProductID: int64Ptr(1),
	}

	expectedError := errors.New("exist check error")

	mocks.GroupRepo.ExistByNameMock.Expect("Test Group").Return(false, expectedError)

	_, err := svc.CreateAsAdmin(context.Background(), inputGroup)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_CreateAsAdmin_CreateError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	inputGroup := groupentity.Group{
		Name:      "Test Group",
		ProductID: int64Ptr(1),
	}

	expectedError := errors.New("create error")

	mocks.GroupRepo.ExistByNameMock.Expect("Test Group").Return(false, nil)
	mocks.GroupRepo.CreateMock.Expect(inputGroup).Return(groupentity.Group{}, expectedError)

	_, err := svc.CreateAsAdmin(context.Background(), inputGroup)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_GetAllAsAdmin_Success_WithProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	group := createTestGroup()
	groups := []groupentity.Group{group}

	product := createTestProduct()
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: *group.ProductID},
		{ID: 2, UserID: 2, ProductID: *group.ProductID},
	}

	participantGroups1 := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: group.ID},
	}
	var participantGroups2 []participantentity.ParticipantGroup

	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: group.ID, RoleID: 1},
		{ID: 2, GroupID: group.ID, RoleID: 2},
	}

	expectedResult := []groupentity.AdminGroup{
		{
			ID:        group.ID,
			Name:      group.Name,
			IsActive:  group.ActiveFlg,
			Type:      "custom",
			Product:   &product,
			UserCount: 1,
			RoleCount: 2,
		},
	}

	mocks.GroupRepo.GetAllMock.Expect().Return(groups, nil)
	mocks.ParticipantRepo.GetByProductIDMock.Expect(*group.ProductID).Return(participants, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.When(int64(1)).Then(participantGroups1, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.When(int64(2)).Then(participantGroups2, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(groupRoles, nil)
	mocks.ProductRepo.GetByIDMock.Expect(*group.ProductID).Return(product, nil)

	result, err := svc.GetAllAsAdmin()

	require.NoError(t, err)
	require.Len(t, result, 1)
	require.Equal(t, expectedResult[0].ID, result[0].ID)
	require.Equal(t, expectedResult[0].UserCount, result[0].UserCount)
	require.Equal(t, expectedResult[0].RoleCount, result[0].RoleCount)
	require.NotNil(t, result[0].Product)
}

func TestGroupDomainService_GetAllAsAdmin_Success_WithoutProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	group := createTestSystemGroup()
	groups := []groupentity.Group{group}

	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: group.ID},
		{ID: 2, UserID: 2, GroupID: group.ID},
	}

	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: group.ID, RoleID: 1},
	}

	expectedResult := []groupentity.AdminGroup{
		{
			ID:        group.ID,
			Name:      group.Name,
			IsActive:  group.ActiveFlg,
			Type:      "system",
			UserCount: 2,
			RoleCount: 1,
		},
	}

	mocks.GroupRepo.GetAllMock.Expect().Return(groups, nil)
	mocks.UserGroupRepo.GetByGroupIDMock.Expect(group.ID).Return(userGroups, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(groupRoles, nil)

	result, err := svc.GetAllAsAdmin()

	require.NoError(t, err)
	require.Len(t, result, 1)
	require.Equal(t, expectedResult[0].ID, result[0].ID)
	require.Equal(t, expectedResult[0].UserCount, result[0].UserCount)
	require.Equal(t, expectedResult[0].RoleCount, result[0].RoleCount)
	require.Nil(t, result[0].Product)
}

func TestGroupDomainService_GetAllAsAdmin_GetAllError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	expectedError := errors.New("get all error")

	mocks.GroupRepo.GetAllMock.Expect().Return(nil, expectedError)

	_, err := svc.GetAllAsAdmin()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_buildAdminGroup_Success_WithProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestGroup()
	product := createTestProduct()
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: *group.ProductID},
	}
	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: group.ID},
	}
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: group.ID, RoleID: 1},
	}

	mocks.ParticipantRepo.GetByProductIDMock.Expect(*group.ProductID).Return(participants, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(int64(1)).Return(participantGroups, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(groupRoles, nil)
	mocks.ProductRepo.GetByIDMock.Expect(*group.ProductID).Return(product, nil)

	result, err := svc.buildAdminGroup(group)

	require.NoError(t, err)
	require.Equal(t, group.ID, result.ID)
	require.Equal(t, group.Name, result.Name)
	require.Equal(t, group.ActiveFlg, result.IsActive)
	require.Equal(t, "custom", result.Type)
	require.NotNil(t, result.Product)
	require.Equal(t, int64(1), result.UserCount)
	require.Equal(t, int64(1), result.RoleCount)
}

func TestGroupDomainService_buildAdminGroup_Success_WithoutProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestSystemGroup()
	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: group.ID},
		{ID: 2, UserID: 2, GroupID: group.ID},
	}
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: group.ID, RoleID: 1},
	}

	mocks.UserGroupRepo.GetByGroupIDMock.Expect(group.ID).Return(userGroups, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(groupRoles, nil)

	result, err := svc.buildAdminGroup(group)

	require.NoError(t, err)
	require.Equal(t, group.ID, result.ID)
	require.Equal(t, group.Name, result.Name)
	require.Equal(t, group.ActiveFlg, result.IsActive)
	require.Equal(t, "system", result.Type)
	require.Nil(t, result.Product)
	require.Equal(t, int64(2), result.UserCount)
	require.Equal(t, int64(1), result.RoleCount)
}

func TestGroupDomainService_buildAdminGroup_UserCountError_WithProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestGroup()
	expectedError := errors.New("participant error")

	mocks.ParticipantRepo.GetByProductIDMock.Expect(*group.ProductID).Return(nil, expectedError)

	_, err := svc.buildAdminGroup(group)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_buildAdminGroup_UserCountError_WithoutProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestSystemGroup()
	expectedError := errors.New("user group error")

	mocks.UserGroupRepo.GetByGroupIDMock.Expect(group.ID).Return(nil, expectedError)

	_, err := svc.buildAdminGroup(group)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_buildAdminGroup_RoleCountError_WithProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestGroup()
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: *group.ProductID},
	}
	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: group.ID},
	}
	expectedError := errors.New("group role error")

	mocks.ParticipantRepo.GetByProductIDMock.Expect(*group.ProductID).Return(participants, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(int64(1)).Return(participantGroups, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(nil, expectedError)

	_, err := svc.buildAdminGroup(group)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_buildAdminGroup_RoleCountError_WithoutProduct(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestSystemGroup()
	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: group.ID},
	}
	expectedError := errors.New("group role error")

	mocks.UserGroupRepo.GetByGroupIDMock.Expect(group.ID).Return(userGroups, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(nil, expectedError)

	_, err := svc.buildAdminGroup(group)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_buildAdminGroup_ProductError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	group := createTestGroup()
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: *group.ProductID},
	}
	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: group.ID},
	}
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: group.ID, RoleID: 1},
	}
	expectedError := errors.New("product error")

	mocks.ParticipantRepo.GetByProductIDMock.Expect(*group.ProductID).Return(participants, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(int64(1)).Return(participantGroups, nil)
	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(group.ID).Return(groupRoles, nil)
	mocks.ProductRepo.GetByIDMock.Expect(*group.ProductID).Return(productentity.Product{}, expectedError)

	_, err := svc.buildAdminGroup(group)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_GetAllAsAdmin_BuildAdminGroupError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	group := createTestGroup()
	groups := []groupentity.Group{group}
	expectedError := errors.New("participant error")

	mocks.GroupRepo.GetAllMock.Expect().Return(groups, nil)
	mocks.ParticipantRepo.GetByProductIDMock.Expect(*group.ProductID).Return(nil, expectedError)

	_, err := svc.GetAllAsAdmin()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_calculateUserCountForProductGroup_Success(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	productID := int64(1)
	groupID := int64(1)
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID},
		{ID: 2, UserID: 2, ProductID: productID},
	}
	participantGroups1 := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: groupID},
	}
	var participantGroups2 []participantentity.ParticipantGroup

	mocks.ParticipantRepo.GetByProductIDMock.Expect(productID).Return(participants, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.When(int64(1)).Then(participantGroups1, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.When(int64(2)).Then(participantGroups2, nil)

	result, err := svc.calculateUserCountForProductGroup(productID, groupID)

	require.NoError(t, err)
	require.Equal(t, int64(1), result)
}

func TestGroupDomainService_calculateUserCountForProductGroup_ParticipantError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	productID := int64(1)
	groupID := int64(1)
	expectedError := errors.New("participant error")

	mocks.ParticipantRepo.GetByProductIDMock.Expect(productID).Return(nil, expectedError)

	_, err := svc.calculateUserCountForProductGroup(productID, groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_calculateUserCountForProductGroup_ParticipantGroupError(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	productID := int64(1)
	groupID := int64(1)
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID},
	}
	expectedError := errors.New("participant group error")

	mocks.ParticipantRepo.GetByProductIDMock.Expect(productID).Return(participants, nil)
	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(int64(1)).Return(nil, expectedError)

	_, err := svc.calculateUserCountForProductGroup(productID, groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_calculateUserCountForProductGroup_EmptyParticipants(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	productID := int64(1)
	groupID := int64(1)
	var participants []participantentity.Participant

	mocks.ParticipantRepo.GetByProductIDMock.Expect(productID).Return(participants, nil)

	result, err := svc.calculateUserCountForProductGroup(productID, groupID)

	require.NoError(t, err)
	require.Equal(t, int64(0), result)
}

func TestGroupDomainService_calculateUserCountForSystemGroup_Success(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	groupID := int64(1)
	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: groupID},
		{ID: 2, UserID: 2, GroupID: groupID},
	}

	mocks.UserGroupRepo.GetByGroupIDMock.Expect(groupID).Return(userGroups, nil)

	result, err := svc.calculateUserCountForSystemGroup(groupID)

	require.NoError(t, err)
	require.Equal(t, int64(2), result)
}

func TestGroupDomainService_calculateUserCountForSystemGroup_Error(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	groupID := int64(1)
	expectedError := errors.New("user group error")

	mocks.UserGroupRepo.GetByGroupIDMock.Expect(groupID).Return(nil, expectedError)

	_, err := svc.calculateUserCountForSystemGroup(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_calculateRoleCount_Success(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	groupID := int64(1)
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: groupID, RoleID: 1},
		{ID: 2, GroupID: groupID, RoleID: 2},
	}

	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(groupID).Return(groupRoles, nil)

	result, err := svc.calculateRoleCount(groupID)

	require.NoError(t, err)
	require.Equal(t, int64(2), result)
}

func TestGroupDomainService_calculateRoleCount_Error(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	groupID := int64(1)
	expectedError := errors.New("group role error")

	mocks.GroupRoleRepo.GetByGroupIDMock.Expect(groupID).Return(nil, expectedError)

	_, err := svc.calculateRoleCount(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_isParticipantInGroup_True(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	participantID := int64(1)
	groupID := int64(1)
	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: participantID, GroupID: groupID},
		{ID: 2, ParticipantID: participantID, GroupID: 2},
	}

	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(participantID).Return(participantGroups, nil)

	result, err := svc.isParticipantInGroup(participantID, groupID)

	require.NoError(t, err)
	require.True(t, result)
}

func TestGroupDomainService_isParticipantInGroup_False(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	participantID := int64(1)
	groupID := int64(1)
	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: participantID, GroupID: 2},
		{ID: 2, ParticipantID: participantID, GroupID: 3},
	}

	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(participantID).Return(participantGroups, nil)

	result, err := svc.isParticipantInGroup(participantID, groupID)

	require.NoError(t, err)
	require.False(t, result)
}

func TestGroupDomainService_isParticipantInGroup_Error(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	participantID := int64(1)
	groupID := int64(1)
	expectedError := errors.New("participant group error")

	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(participantID).Return(nil, expectedError)

	_, err := svc.isParticipantInGroup(participantID, groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_isParticipantInGroup_EmptyGroups(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	participantID := int64(1)
	groupID := int64(1)
	var participantGroups []participantentity.ParticipantGroup

	mocks.ParticipantGroup.GetByParticipantIDMock.Expect(participantID).Return(participantGroups, nil)

	result, err := svc.isParticipantInGroup(participantID, groupID)

	require.NoError(t, err)
	require.False(t, result)
}

func TestGroupDomainService_getProductForGroup_Success(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	productID := int64(1)
	product := createTestProduct()

	mocks.ProductRepo.GetByIDMock.Expect(productID).Return(product, nil)

	result, err := svc.getProductForGroup(productID)

	require.NoError(t, err)
	require.NotNil(t, result)
	require.Equal(t, product.ID, result.ID)
	require.Equal(t, product.IID, result.IID)
	require.Equal(t, product.TechName, result.TechName)
	require.Equal(t, product.Name, result.Name)
}

func TestGroupDomainService_getProductForGroup_Error(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks).(*groupDomainService)

	productID := int64(1)
	expectedError := errors.New("product not found")

	mocks.ProductRepo.GetByIDMock.Expect(productID).Return(productentity.Product{}, expectedError)

	_, err := svc.getProductForGroup(productID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestGroupDomainService_DeleteAsAdmin_Success(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	groupID := int64(1)

	mocks.GroupRepo.DeleteGroupMock.Expect(groupID).Return(nil)

	err := svc.DeleteAsAdmin(groupID)

	require.NoError(t, err)
	require.Equal(t, uint64(1), mocks.GroupRepo.DeleteGroupAfterCounter())
}

func TestGroupDomainService_DeleteAsAdmin_Error(t *testing.T) {
	mocks := createMocks(t)
	svc := createService(mocks)

	groupID := int64(1)
	expectedError := errors.New("delete error")

	mocks.GroupRepo.DeleteGroupMock.Expect(groupID).Return(expectedError)

	err := svc.DeleteAsAdmin(groupID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}
