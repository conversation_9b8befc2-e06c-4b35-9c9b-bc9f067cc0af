package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

func createGroupMocks(t *testing.T) (
	*categorymocks.CategoryGroupPrimeDBMock,
	*categorymocks.CategoryPrimeDBMock,
	*categorymocks.CategoryRolePrimeDBMock,
	*groupmocks.GroupCacheMock,
	*groupmocks.GroupPrimeDBMock,
	*groupmocks.GroupRolePrimeDBMock,
	*participantmocks.ParticipantGroupPrimeDBMock,
	*participantmocks.ParticipantPrimeDBMock,
	*productmocks.ProductPrimeDBMock,
	*rolemocks.RolePrimeDBMock,
	*usermocks.UserGroupPrimeDBMock,
	*usermocks.UserPrimeDBMock,
) {
	return categorymocks.NewCategoryGroupPrimeDBMock(t),
		categorymocks.NewCategoryPrimeDBMock(t),
		categorymocks.NewCategoryRolePrimeDBMock(t),
		groupmocks.NewGroupCacheMock(t),
		groupmocks.NewGroupPrimeDBMock(t),
		groupmocks.NewGroupRolePrimeDBMock(t),
		participantmocks.NewParticipantGroupPrimeDBMock(t),
		participantmocks.NewParticipantPrimeDBMock(t),
		productmocks.NewProductPrimeDBMock(t),
		rolemocks.NewRolePrimeDBMock(t),
		usermocks.NewUserGroupPrimeDBMock(t),
		usermocks.NewUserPrimeDBMock(t)
}

func TestGroupDomainService_Create_Success_SystemGroup(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	inputGroup := groupentity.Group{
		Name:     "System Group",
		IsSystem: true,
	}

	createdGroup := groupentity.Group{
		ID:       123,
		Name:     "System Group",
		IsSystem: true,
	}

	expectedResult := groupentity.GroupWithStats{
		GroupID:   123,
		GroupName: "System Group",
		IsSystem:  true,
	}

	groupRepo.CreateMock.Expect(inputGroup).Return(createdGroup, nil)
	categoryGroupRepo.CreateMock.Expect(categoryentity.CategoryGroupLink{
		CategoryID: constants.CategorySystemID,
		GroupID:    123,
	}).Return(categoryentity.CategoryGroupLink{}, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.Create(inputGroup)

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), groupRepo.CreateAfterCounter())
	assert.Equal(t, uint64(1), categoryGroupRepo.CreateAfterCounter())
}

func TestGroupDomainService_Create_Success_CustomGroup(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	inputGroup := groupentity.Group{
		Name:     "Custom Group",
		IsSystem: false,
	}

	createdGroup := groupentity.Group{
		ID:       456,
		Name:     "Custom Group",
		IsSystem: false,
	}

	expectedResult := groupentity.GroupWithStats{
		GroupID:   456,
		GroupName: "Custom Group",
		IsSystem:  false,
	}

	groupRepo.CreateMock.Expect(inputGroup).Return(createdGroup, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.Create(inputGroup)

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), groupRepo.CreateAfterCounter())
	assert.Equal(t, uint64(0), categoryGroupRepo.CreateAfterCounter())
}

func TestGroupDomainService_Create_CreateError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	inputGroup := groupentity.Group{
		Name:     "Test Group",
		IsSystem: false,
	}

	expectedError := errors.New("create error")

	groupRepo.CreateMock.Expect(inputGroup).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.Create(inputGroup)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, groupentity.GroupWithStats{}, result)
}

func TestGroupDomainService_Create_AssignToSystemCategoryError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	inputGroup := groupentity.Group{
		Name:     "System Group",
		IsSystem: true,
	}

	createdGroup := groupentity.Group{
		ID:       123,
		Name:     "System Group",
		IsSystem: true,
	}

	expectedError := errors.New("category assign error")

	groupRepo.CreateMock.Expect(inputGroup).Return(createdGroup, nil)
	categoryGroupRepo.CreateMock.Expect(categoryentity.CategoryGroupLink{
		CategoryID: constants.CategorySystemID,
		GroupID:    123,
	}).Return(categoryentity.CategoryGroupLink{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.Create(inputGroup)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, groupentity.GroupWithStats{}, result)
}

func TestGroupDomainService_AssignToRoles_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	ctx := context.Background()
	groupID := int64(123)
	roleIDs := []int64{1, 2, 3}

	groupRoleRepo.CreateByGroupIDAndRoleIDsMock.Expect(ctx, groupID, roleIDs).Return(nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	err := service.AssignToRoles(ctx, groupID, roleIDs)

	require.NoError(t, err)
	assert.Equal(t, uint64(1), groupRoleRepo.CreateByGroupIDAndRoleIDsAfterCounter())
}

func TestGroupDomainService_AssignToRoles_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	ctx := context.Background()
	groupID := int64(123)
	roleIDs := []int64{1, 2, 3}
	expectedError := errors.New("assign error")

	groupRoleRepo.CreateByGroupIDAndRoleIDsMock.Expect(ctx, groupID, roleIDs).Return(expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	err := service.AssignToRoles(ctx, groupID, roleIDs)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
}

func TestGroupDomainService_GetAll_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	expectedGroups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
		{ID: 2, Name: "Group 2", IsSystem: false},
	}

	groupRepo.GetAllMock.Expect().Return(expectedGroups, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetAll()

	require.NoError(t, err)
	assert.Equal(t, expectedGroups, result)
	assert.Equal(t, uint64(1), groupRepo.GetAllAfterCounter())
}

func TestGroupDomainService_GetAll_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	expectedError := errors.New("get all error")

	groupRepo.GetAllMock.Expect().Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetAll()

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetAvailableSystemGroups_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	category := categoryentity.CategoryFull{
		Groups: &[]groupentity.GroupShort{
			{ID: 1, Name: "Category Group 1", IsSystem: true},
			{ID: 3, Name: "Category Group 3", IsSystem: true},
		},
	}

	groups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "Group 1", IsSystem: true},
		{GroupID: 2, GroupName: "Group 2", IsSystem: false},
		{GroupID: 3, GroupName: "Group 3", IsSystem: true},
	}

	userGroups := []int64{1}

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetAvailableSystemGroups(category, groups, userGroups)

	require.NoError(t, err)
	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].GroupID)
	assert.Equal(t, int64(3), result[1].GroupID)
	assert.True(t, result[0].IsSystem)
	assert.True(t, result[1].IsSystem)
}

func TestGroupDomainService_GetByCategoryID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	categoryID := int64(100)
	catGroups := []categoryentity.CategoryGroupLink{
		{ID: 1, CategoryID: categoryID, GroupID: 1},
		{ID: 2, CategoryID: categoryID, GroupID: 2},
	}

	groups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
		{ID: 2, Name: "Group 2", IsSystem: false},
	}

	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(catGroups, nil)
	groupRepo.GetByIDMock.When(int64(1)).Then(groups[0], nil)
	groupRepo.GetByIDMock.When(int64(2)).Then(groups[1], nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByCategoryID(categoryID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].GroupID)
	assert.Equal(t, "Group 1", result[0].GroupName)
	assert.Equal(t, int64(2), result[1].GroupID)
	assert.Equal(t, "Group 2", result[1].GroupName)
}

func TestGroupDomainService_GetByCategoryID_CategoryError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	categoryID := int64(100)
	expectedError := errors.New("category error")

	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByCategoryID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetByCategoryID_GroupError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	categoryID := int64(100)
	catGroups := []categoryentity.CategoryGroupLink{
		{ID: 1, CategoryID: categoryID, GroupID: 1},
	}
	expectedError := errors.New("group error")

	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(catGroups, nil)
	groupRepo.GetByIDMock.Expect(int64(1)).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByCategoryID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetByGroupIDAndProductID_WithProduct_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupID := int64(123)
	productID := int64(456)

	group := groupentity.Group{
		ID:        groupID,
		Name:      "Test Group",
		IsSystem:  false,
		ActiveFlg: true,
	}

	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: groupID},
		{ID: 2, ParticipantID: 2, GroupID: groupID},
	}

	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: groupID, RoleID: 1},
		{ID: 2, GroupID: groupID, RoleID: 2},
	}

	groupRepo.GetByGroupIDAndProductIDMock.Expect(groupID, productID).Return(group, nil)
	participantGroup.GetByGroupIDMock.Expect(groupID).Return(participantGroups, nil)
	groupRoleRepo.GetByGroupIDMock.Expect(groupID).Return(groupRoles, nil)
	roleRepo.GetByIDMock.When(int64(1)).Then(roleentity.Role{ID: 1, Name: "Role 1"}, nil)
	roleRepo.GetByIDMock.When(int64(2)).Then(roleentity.Role{ID: 2, Name: "Role 2"}, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByGroupIDAndProductID(groupID, productID)

	require.NoError(t, err)
	assert.Equal(t, groupID, result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.False(t, result.IsSystem)

	require.NotNil(t, result.ParticipantIDs)
	require.Len(t, *result.ParticipantIDs, 2)
	assert.Equal(t, int64(1), (*result.ParticipantIDs)[0])
	assert.Equal(t, int64(2), (*result.ParticipantIDs)[1])

	require.NotNil(t, result.RoleIDs)
	require.Len(t, *result.RoleIDs, 2)
	assert.Equal(t, int64(1), (*result.RoleIDs)[0])
	assert.Equal(t, int64(2), (*result.RoleIDs)[1])

	assert.Nil(t, result.UserIDs)
}

func TestGroupDomainService_GetByGroupIDAndProductID_WithoutProduct_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupID := int64(123)
	productID := int64(0)

	group := groupentity.Group{
		ID:       groupID,
		Name:     "Test Group",
		IsSystem: true,
	}

	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: groupID},
		{ID: 2, UserID: 2, GroupID: groupID},
	}

	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: groupID, RoleID: 1},
	}

	groupRepo.GetByIDMock.Expect(groupID).Return(group, nil)
	userGroupRepo.GetByGroupIDMock.Expect(groupID).Return(userGroups, nil)
	groupRoleRepo.GetByGroupIDMock.Expect(groupID).Return(groupRoles, nil)
	roleRepo.GetByIDMock.When(int64(1)).Then(roleentity.Role{ID: 1, Name: "Role 1"}, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByGroupIDAndProductID(groupID, productID)

	require.NoError(t, err)
	assert.Equal(t, groupID, result.ID)
	assert.Equal(t, "Test Group", result.Name)
	assert.True(t, result.IsSystem)

	assert.Nil(t, result.ParticipantIDs)

	require.NotNil(t, result.RoleIDs)
	require.Len(t, *result.RoleIDs, 1)
	assert.Equal(t, int64(1), (*result.RoleIDs)[0])

	require.NotNil(t, result.UserIDs)
	require.Len(t, *result.UserIDs, 2)
	assert.Equal(t, int64(1), (*result.UserIDs)[0])
	assert.Equal(t, int64(2), (*result.UserIDs)[1])
}

func TestGroupDomainService_GetByGroupIDAndProductID_InactiveGroup(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupID := int64(123)
	productID := int64(456)

	group := groupentity.Group{
		ID:        groupID,
		Name:      "Inactive Group",
		IsSystem:  false,
		ActiveFlg: false,
	}

	groupRepo.GetByGroupIDAndProductIDMock.Expect(groupID, productID).Return(group, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByGroupIDAndProductID(groupID, productID)

	require.Error(t, err)
	assert.True(t, errkit.IsNotFoundError(err))
	assert.Equal(t, groupentity.GroupFull{}, result)
}

func TestGroupDomainService_GetByID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupID := int64(123)
	expectedGroup := groupentity.Group{
		ID:       groupID,
		Name:     "Test Group",
		IsSystem: true,
	}

	groupRepo.GetByIDMock.Expect(groupID).Return(expectedGroup, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByID(groupID)

	require.NoError(t, err)
	assert.Equal(t, expectedGroup, result)
	assert.Equal(t, uint64(1), groupRepo.GetByIDAfterCounter())
}

func TestGroupDomainService_GetByID_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupID := int64(123)
	expectedError := errors.New("not found")

	groupRepo.GetByIDMock.Expect(groupID).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByID(groupID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, groupentity.Group{}, result)
}

func TestGroupDomainService_GetByParticipantID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	participantID := int64(123)

	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: participantID, GroupID: 1},
		{ID: 2, ParticipantID: participantID, GroupID: 2},
	}

	groups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
		{ID: 2, Name: "Group 2", IsSystem: false},
	}

	participantGroup.GetByParticipantIDMock.Expect(participantID).Return(participantGroups, nil)
	groupRepo.GetByIDMock.When(int64(1)).Then(groups[0], nil)
	groupRepo.GetByIDMock.When(int64(2)).Then(groups[1], nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByParticipantID(participantID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].GroupID)
	assert.Equal(t, "Group 1", result[0].GroupName)
	assert.Equal(t, int64(2), result[1].GroupID)
	assert.Equal(t, "Group 2", result[1].GroupName)
}

func TestGroupDomainService_GetByProductID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	productID := int64(456)
	expectedGroups := []groupentity.Group{
		{ID: 1, Name: "Product Group 1", IsSystem: false},
		{ID: 2, Name: "Product Group 2", IsSystem: false},
	}

	groupRepo.GetByProductIDMock.Expect(productID).Return(expectedGroups, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByProductID(productID)

	require.NoError(t, err)
	assert.Equal(t, expectedGroups, result)
	assert.Equal(t, uint64(1), groupRepo.GetByProductIDAfterCounter())
}

func TestGroupDomainService_GetByRoleID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	roleID := int64(789)

	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: 1, RoleID: roleID},
		{ID: 2, GroupID: 2, RoleID: roleID},
	}

	groups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
		{ID: 2, Name: "Group 2", IsSystem: false},
	}

	groupRoleRepo.GetByRoleIDMock.Expect(roleID).Return(groupRoles, nil)
	groupRepo.GetByIDMock.When(int64(1)).Then(groups[0], nil)
	groupRepo.GetByIDMock.When(int64(2)).Then(groups[1], nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByRoleID(roleID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Group 2", result[1].Name)
}

func TestGroupDomainService_GetByUserID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)

	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: userID, GroupID: 1},
		{ID: 2, UserID: userID, GroupID: 2},
	}

	groups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
		{ID: 2, Name: "Group 2", IsSystem: false},
	}

	userGroupRepo.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	groupRepo.GetByIDMock.When(int64(1)).Then(groups[0], nil)
	groupRepo.GetByIDMock.When(int64(2)).Then(groups[1], nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByUserID(userID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, "Group 1", result[0].Name)
	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, "Group 2", result[1].Name)
}

func TestGroupDomainService_GetByUserID_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)
	expectedError := errors.New("user groups error")

	userGroupRepo.GetByUserIDMock.Expect(userID).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByUserID(userID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetSystemGroupsWithStats_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	expectedGroups := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "System Group 1", IsSystem: true, RoleCount: 1, ParticipantCount: 1, UserCount: 1},
		{GroupID: 3, GroupName: "System Group 2", IsSystem: true, RoleCount: 0, ParticipantCount: 0, UserCount: 0},
	}

	groupRepo.GetSystemGroupsWithStatsMock.Expect().Return(expectedGroups, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetSystemGroupsWithStats()

	require.NoError(t, err)
	assert.Equal(t, expectedGroups, result)
	assert.Equal(t, uint64(1), groupRepo.GetSystemGroupsWithStatsAfterCounter())
}

func TestGroupDomainService_Update_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	name := "Updated Group"
	updateData := groupentity.GroupUpdateData{
		ID:   123,
		Name: &name,
	}

	updatedGroup := groupentity.Group{
		ID:   123,
		Name: "Updated Group",
	}

	groupRepo.UpdateMock.Expect(updateData).Return(updatedGroup, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.Update(updateData)

	require.NoError(t, err)
	assert.Equal(t, updatedGroup, result)
	assert.Equal(t, uint64(1), groupRepo.UpdateAfterCounter())
}

func TestGroupDomainService_Update_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	name := "Updated Group"
	updateData := groupentity.GroupUpdateData{
		ID:   123,
		Name: &name,
	}

	expectedError := errors.New("update error")

	groupRepo.UpdateMock.Expect(updateData).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.Update(updateData)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, groupentity.Group{}, result)
}

func TestGroupDomainService_GetGroupsWithCategoryStats_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupsWithStats := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "System Group 1", IsSystem: true, RoleCount: 2, ParticipantCount: 1, UserCount: 3},
		{GroupID: 2, GroupName: "System Group 2", IsSystem: true, RoleCount: 1, ParticipantCount: 0, UserCount: 2},
	}

	groupCategoryLinks := []groupentity.GroupCategoryLink{
		{GroupID: 1, CategoryID: 100, CategoryName: "Category 1", IsActive: true},
		{GroupID: 1, CategoryID: 101, CategoryName: "Category 2", IsActive: false},
		{GroupID: 2, CategoryID: 100, CategoryName: "Category 1", IsActive: true},
	}

	expectedResult := []groupentity.GroupWithCategoryStats{
		{
			GroupID:          1,
			GroupName:        "System Group 1",
			IsSystem:         true,
			RoleCount:        2,
			ParticipantCount: 1,
			UserCount:        3,
			CategoryStates: []groupentity.GroupCategoryStates{
				{CategoryID: 100, CategoryName: "Category 1", IsActive: true},
				{CategoryID: 101, CategoryName: "Category 2", IsActive: false},
			},
		},
		{
			GroupID:          2,
			GroupName:        "System Group 2",
			IsSystem:         true,
			RoleCount:        1,
			ParticipantCount: 0,
			UserCount:        2,
			CategoryStates: []groupentity.GroupCategoryStates{
				{CategoryID: 100, CategoryName: "Category 1", IsActive: true},
			},
		},
	}

	groupRepo.GetSystemGroupsWithStatsMock.Expect().Return(groupsWithStats, nil)
	groupRepo.GetGroupCategoryStatesMock.Expect().Return(groupCategoryLinks, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetGroupsWithCategoryStats()

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), groupRepo.GetSystemGroupsWithStatsAfterCounter())
	assert.Equal(t, uint64(1), groupRepo.GetGroupCategoryStatesAfterCounter())
}

func TestGroupDomainService_GetGroupsWithCategoryStats_GetSystemGroupsError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	expectedError := errors.New("system groups error")

	groupRepo.GetSystemGroupsWithStatsMock.Expect().Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetGroupsWithCategoryStats()

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetGroupsWithCategoryStats_GetCategoryStatesError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	groupsWithStats := []groupentity.GroupWithStats{
		{GroupID: 1, GroupName: "System Group 1", IsSystem: true},
	}

	expectedError := errors.New("category states error")

	groupRepo.GetSystemGroupsWithStatsMock.Expect().Return(groupsWithStats, nil)
	groupRepo.GetGroupCategoryStatesMock.Expect().Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetGroupsWithCategoryStats()

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetParticipantGroupsWithProductByUserID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)
	expectedGroups := []groupentity.Group{
		{ID: 1, Name: "Participant Group 1", IsSystem: false},
		{ID: 2, Name: "Participant Group 2", IsSystem: true},
	}

	userGroupRepo.GetParticipantGroupsByUserIDMock.Expect(userID).Return(expectedGroups, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetParticipantGroupsWithProductByUserID(userID)

	require.NoError(t, err)
	assert.Equal(t, expectedGroups, result)
	assert.Equal(t, uint64(1), userGroupRepo.GetParticipantGroupsByUserIDAfterCounter())
}

func TestGroupDomainService_GetParticipantGroupsWithProductByUserID_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)
	expectedError := errors.New("participant groups error")

	userGroupRepo.GetParticipantGroupsByUserIDMock.Expect(userID).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetParticipantGroupsWithProductByUserID(userID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithCountsByProductID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	productID := int64(456)
	expectedGroups := []groupentity.AdminGroup{
		{ID: 1, Name: "Product Group 1", Type: "custom", UserCount: 5, RoleCount: 2},
		{ID: 2, Name: "Product Group 2", Type: "system", UserCount: 3, RoleCount: 1},
	}

	groupRepo.GetByProductIDAndIsActiveMock.Expect(productID, true).Return(expectedGroups, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithCountsByProductID(productID)

	require.NoError(t, err)
	assert.Equal(t, expectedGroups, result)
	assert.Equal(t, uint64(1), groupRepo.GetByProductIDAndIsActiveAfterCounter())
}

func TestGroupDomainService_GetWithCountsByProductID_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	productID := int64(456)
	expectedError := errors.New("product groups error")

	groupRepo.GetByProductIDAndIsActiveMock.Expect(productID, true).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithCountsByProductID(productID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithProductByUserID_Success_WithoutProduct(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)
	groupsWithProductID := []groupentity.GroupWithProductID{
		{ID: 1, Name: "Group Without Product", IsSystem: true, ActiveFlg: true, ProductID: nil},
		{ID: 2, Name: "Group With Product", IsSystem: false, ActiveFlg: true, ProductID: &[]int64{456}[0]},
	}

	product := productentity.Product{
		ID:   456,
		Name: "Test Product",
	}

	expectedResult := []groupentity.GroupWithProduct{
		{ID: 1, Name: "Group Without Product", IsSystem: true, ActiveFlg: true, Product: nil},
		{ID: 2, Name: "Group With Product", IsSystem: false, ActiveFlg: true, Product: product.ProductOrNil()},
	}

	groupRepo.GetAllWithProductIDByUserIDMock.Expect(userID).Return(groupsWithProductID, nil)
	productRepo.GetByIDMock.When(int64(456)).Then(product, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductByUserID(userID)

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), groupRepo.GetAllWithProductIDByUserIDAfterCounter())
	assert.Equal(t, uint64(1), productRepo.GetByIDAfterCounter())
}

func TestGroupDomainService_GetWithProductByUserID_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)
	expectedError := errors.New("groups error")

	groupRepo.GetAllWithProductIDByUserIDMock.Expect(userID).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductByUserID(userID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithProductByUserID_ProductError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(123)
	groupsWithProductID := []groupentity.GroupWithProductID{
		{ID: 2, Name: "Group With Product", IsSystem: false, ActiveFlg: true, ProductID: &[]int64{456}[0]},
	}

	expectedError := errors.New("product error")

	groupRepo.GetAllWithProductIDByUserIDMock.Expect(userID).Return(groupsWithProductID, nil)
	productRepo.GetByIDMock.Expect(int64(456)).Return(productentity.Product{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductByUserID(userID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithProductsByRoleID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	roleID := int64(789)
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: 10, RoleID: roleID},
		{ID: 2, GroupID: 20, RoleID: roleID},
	}

	groupWithoutProduct := groupentity.Group{
		ID: 10, Name: "Group Without Product", IsSystem: true, ActiveFlg: true, ProductID: nil,
	}
	groupWithProduct := groupentity.Group{
		ID: 20, Name: "Group With Product", IsSystem: false, ActiveFlg: true, ProductID: &[]int64{456}[0],
	}

	product := productentity.Product{
		ID:   456,
		Name: "Test Product",
	}

	groupRoleRepo.GetByRoleIDMock.Expect(roleID).Return(groupRoles, nil)
	groupRepo.GetByIDMock.When(int64(10)).Then(groupWithoutProduct, nil)
	groupRepo.GetByIDMock.When(int64(20)).Then(groupWithProduct, nil)
	productRepo.GetByIDMock.When(int64(456)).Then(product, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductsByRoleID(roleID)

	require.NoError(t, err)
	require.Len(t, result, 2)

	assert.Equal(t, int64(10), result[0].ID)
	assert.Equal(t, "Group Without Product", result[0].Name)
	assert.True(t, result[0].IsSystem)
	assert.Nil(t, result[0].Product)

	assert.Equal(t, int64(20), result[1].ID)
	assert.Equal(t, "Group With Product", result[1].Name)
	assert.False(t, result[1].IsSystem)
	assert.NotNil(t, result[1].Product)
}

func TestGroupDomainService_GetWithProductsByRoleID_GroupRoleError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	roleID := int64(789)
	expectedError := errors.New("group roles error")

	groupRoleRepo.GetByRoleIDMock.Expect(roleID).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductsByRoleID(roleID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithStatsByProductID_Success(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	productID := int64(456)
	groups := []groupentity.Group{
		{ID: 1, Name: "Active Group 1", ActiveFlg: true},
		{ID: 2, Name: "Inactive Group", ActiveFlg: false},
		{ID: 3, Name: "Active Group 2", ActiveFlg: true},
	}

	groupRoles1 := []groupentity.GroupRole{
		{ID: 1, GroupID: 1, RoleID: 1},
	}
	participantGroups1 := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 1},
	}
	userGroups1 := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: 1},
	}

	groupRoles3 := []groupentity.GroupRole{
		{ID: 3, GroupID: 3, RoleID: 2},
		{ID: 4, GroupID: 3, RoleID: 3},
	}
	var participantGroups3 []participantentity.ParticipantGroup
	var userGroups3 []userentity.UserGroup

	groupRepo.GetByProductIDMock.Expect(productID).Return(groups, nil)
	groupRoleRepo.GetByGroupIDMock.When(int64(1)).Then(groupRoles1, nil)
	participantGroup.GetByGroupIDMock.When(int64(1)).Then(participantGroups1, nil)
	userGroupRepo.GetByGroupIDMock.When(int64(1)).Then(userGroups1, nil)
	groupRoleRepo.GetByGroupIDMock.When(int64(3)).Then(groupRoles3, nil)
	participantGroup.GetByGroupIDMock.When(int64(3)).Then(participantGroups3, nil)
	userGroupRepo.GetByGroupIDMock.When(int64(3)).Then(userGroups3, nil)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithStatsByProductID(productID)

	require.NoError(t, err)
	require.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].GroupID)
	assert.Equal(t, "Active Group 1", result[0].GroupName)
	assert.Equal(t, int64(1), result[0].RoleCount)
	assert.Equal(t, int64(1), result[0].ParticipantCount)
	assert.Equal(t, int64(1), result[0].UserCount)

	assert.Equal(t, int64(3), result[1].GroupID)
	assert.Equal(t, "Active Group 2", result[1].GroupName)
	assert.Equal(t, int64(2), result[1].RoleCount)
	assert.Equal(t, int64(0), result[1].ParticipantCount)
	assert.Equal(t, int64(0), result[1].UserCount)
}

func TestGroupDomainService_GetWithStatsByProductID_Error(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	productID := int64(456)
	expectedError := errors.New("groups error")

	groupRepo.GetByProductIDMock.Expect(productID).Return(nil, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithStatsByProductID(productID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetByParticipantID_GetGroupError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	participantID := int64(123)
	participantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: participantID, GroupID: 10},
	}

	expectedError := errors.New("group error")

	participantGroup.GetByParticipantIDMock.Expect(participantID).Return(participantGroups, nil)
	groupRepo.GetByIDMock.Expect(int64(10)).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByParticipantID(participantID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetByRoleID_GetGroupError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	roleID := int64(789)
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: 20, RoleID: roleID},
	}

	expectedError := errors.New("group error")

	groupRoleRepo.GetByRoleIDMock.Expect(roleID).Return(groupRoles, nil)
	groupRepo.GetByIDMock.Expect(int64(20)).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByRoleID(roleID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetByUserID_GetGroupError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	userID := int64(456)
	userGroups := []userentity.UserGroup{
		{ID: 1, UserID: userID, GroupID: 30},
	}

	expectedError := errors.New("group error")

	userGroupRepo.GetByUserIDMock.Expect(userID).Return(userGroups, nil)
	groupRepo.GetByIDMock.Expect(int64(30)).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetByUserID(userID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithProductsByRoleID_GetGroupError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	roleID := int64(789)
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: 10, RoleID: roleID},
	}

	expectedError := errors.New("group error")

	groupRoleRepo.GetByRoleIDMock.Expect(roleID).Return(groupRoles, nil)
	groupRepo.GetByIDMock.Expect(int64(10)).Return(groupentity.Group{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductsByRoleID(roleID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestGroupDomainService_GetWithProductsByRoleID_ProductError(t *testing.T) {
	categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo := createGroupMocks(t)

	roleID := int64(789)
	groupRoles := []groupentity.GroupRole{
		{ID: 1, GroupID: 20, RoleID: roleID},
	}

	groupWithProduct := groupentity.Group{
		ID: 20, Name: "Group With Product", IsSystem: false, ActiveFlg: true, ProductID: &[]int64{456}[0],
	}

	expectedError := errors.New("product error")

	groupRoleRepo.GetByRoleIDMock.Expect(roleID).Return(groupRoles, nil)
	groupRepo.GetByIDMock.When(int64(20)).Then(groupWithProduct, nil)
	productRepo.GetByIDMock.Expect(int64(456)).Return(productentity.Product{}, expectedError)

	service := NewGroupDomainService(
		categoryGroupRepo, categoryRepo, categoryRoleRepo, groupCache, groupRepo, groupRoleRepo,
		participantGroup, participantRepo, productRepo, roleRepo, userGroupRepo, userRepo,
	)

	result, err := service.GetWithProductsByRoleID(roleID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}
