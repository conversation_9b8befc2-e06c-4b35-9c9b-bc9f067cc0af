// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository.GroupRolePrimeDB -o group_role_prime_db_mock.go -n GroupRolePrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	"github.com/gojuno/minimock/v3"
)

// GroupRolePrimeDBMock implements mm_repository.GroupRolePrimeDB
type GroupRolePrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(data groupentity.GroupRole) (g1 groupentity.GroupRole, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(data groupentity.GroupRole)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mGroupRolePrimeDBMockCreate

	funcCreateByGroupIDAndRoleIDs          func(ctx context.Context, groupID int64, roleIDs []int64) (err error)
	funcCreateByGroupIDAndRoleIDsOrigin    string
	inspectFuncCreateByGroupIDAndRoleIDs   func(ctx context.Context, groupID int64, roleIDs []int64)
	afterCreateByGroupIDAndRoleIDsCounter  uint64
	beforeCreateByGroupIDAndRoleIDsCounter uint64
	CreateByGroupIDAndRoleIDsMock          mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs

	funcCreateByRoleIDAndGroupIDs          func(ctx context.Context, roleID int64, groupIDs []int64) (err error)
	funcCreateByRoleIDAndGroupIDsOrigin    string
	inspectFuncCreateByRoleIDAndGroupIDs   func(ctx context.Context, roleID int64, groupIDs []int64)
	afterCreateByRoleIDAndGroupIDsCounter  uint64
	beforeCreateByRoleIDAndGroupIDsCounter uint64
	CreateByRoleIDAndGroupIDsMock          mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs

	funcDeleteByGroupID          func(ctx context.Context, groupID int64) (err error)
	funcDeleteByGroupIDOrigin    string
	inspectFuncDeleteByGroupID   func(ctx context.Context, groupID int64)
	afterDeleteByGroupIDCounter  uint64
	beforeDeleteByGroupIDCounter uint64
	DeleteByGroupIDMock          mGroupRolePrimeDBMockDeleteByGroupID

	funcDeleteByGroupIDsAndRoleID          func(ctx context.Context, groupIDs []int64, roleID int64) (err error)
	funcDeleteByGroupIDsAndRoleIDOrigin    string
	inspectFuncDeleteByGroupIDsAndRoleID   func(ctx context.Context, groupIDs []int64, roleID int64)
	afterDeleteByGroupIDsAndRoleIDCounter  uint64
	beforeDeleteByGroupIDsAndRoleIDCounter uint64
	DeleteByGroupIDsAndRoleIDMock          mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID

	funcDeleteByRoleIDs          func(ctx context.Context, roleIDs []int64) (err error)
	funcDeleteByRoleIDsOrigin    string
	inspectFuncDeleteByRoleIDs   func(ctx context.Context, roleIDs []int64)
	afterDeleteByRoleIDsCounter  uint64
	beforeDeleteByRoleIDsCounter uint64
	DeleteByRoleIDsMock          mGroupRolePrimeDBMockDeleteByRoleIDs

	funcDeleteByRoleIDsAndGroupID          func(ctx context.Context, roleIDs []int64, groupID int64) (err error)
	funcDeleteByRoleIDsAndGroupIDOrigin    string
	inspectFuncDeleteByRoleIDsAndGroupID   func(ctx context.Context, roleIDs []int64, groupID int64)
	afterDeleteByRoleIDsAndGroupIDCounter  uint64
	beforeDeleteByRoleIDsAndGroupIDCounter uint64
	DeleteByRoleIDsAndGroupIDMock          mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID

	funcGetAll          func() (ga1 []groupentity.GroupRole, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mGroupRolePrimeDBMockGetAll

	funcGetByGroupID          func(groupID int64) (ga1 []groupentity.GroupRole, err error)
	funcGetByGroupIDOrigin    string
	inspectFuncGetByGroupID   func(groupID int64)
	afterGetByGroupIDCounter  uint64
	beforeGetByGroupIDCounter uint64
	GetByGroupIDMock          mGroupRolePrimeDBMockGetByGroupID

	funcGetByGroupRoleID          func(groupID int64, roleID int64) (g1 groupentity.GroupRole, err error)
	funcGetByGroupRoleIDOrigin    string
	inspectFuncGetByGroupRoleID   func(groupID int64, roleID int64)
	afterGetByGroupRoleIDCounter  uint64
	beforeGetByGroupRoleIDCounter uint64
	GetByGroupRoleIDMock          mGroupRolePrimeDBMockGetByGroupRoleID

	funcGetByID          func(id int64) (g1 groupentity.GroupRole, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mGroupRolePrimeDBMockGetByID

	funcGetByRoleID          func(roleID int64) (ga1 []groupentity.GroupRole, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mGroupRolePrimeDBMockGetByRoleID
}

// NewGroupRolePrimeDBMock returns a mock for mm_repository.GroupRolePrimeDB
func NewGroupRolePrimeDBMock(t minimock.Tester) *GroupRolePrimeDBMock {
	m := &GroupRolePrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mGroupRolePrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*GroupRolePrimeDBMockCreateParams{}

	m.CreateByGroupIDAndRoleIDsMock = mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs{mock: m}
	m.CreateByGroupIDAndRoleIDsMock.callArgs = []*GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams{}

	m.CreateByRoleIDAndGroupIDsMock = mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs{mock: m}
	m.CreateByRoleIDAndGroupIDsMock.callArgs = []*GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams{}

	m.DeleteByGroupIDMock = mGroupRolePrimeDBMockDeleteByGroupID{mock: m}
	m.DeleteByGroupIDMock.callArgs = []*GroupRolePrimeDBMockDeleteByGroupIDParams{}

	m.DeleteByGroupIDsAndRoleIDMock = mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID{mock: m}
	m.DeleteByGroupIDsAndRoleIDMock.callArgs = []*GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams{}

	m.DeleteByRoleIDsMock = mGroupRolePrimeDBMockDeleteByRoleIDs{mock: m}
	m.DeleteByRoleIDsMock.callArgs = []*GroupRolePrimeDBMockDeleteByRoleIDsParams{}

	m.DeleteByRoleIDsAndGroupIDMock = mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID{mock: m}
	m.DeleteByRoleIDsAndGroupIDMock.callArgs = []*GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams{}

	m.GetAllMock = mGroupRolePrimeDBMockGetAll{mock: m}

	m.GetByGroupIDMock = mGroupRolePrimeDBMockGetByGroupID{mock: m}
	m.GetByGroupIDMock.callArgs = []*GroupRolePrimeDBMockGetByGroupIDParams{}

	m.GetByGroupRoleIDMock = mGroupRolePrimeDBMockGetByGroupRoleID{mock: m}
	m.GetByGroupRoleIDMock.callArgs = []*GroupRolePrimeDBMockGetByGroupRoleIDParams{}

	m.GetByIDMock = mGroupRolePrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*GroupRolePrimeDBMockGetByIDParams{}

	m.GetByRoleIDMock = mGroupRolePrimeDBMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*GroupRolePrimeDBMockGetByRoleIDParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mGroupRolePrimeDBMockCreate struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockCreateExpectation
	expectations       []*GroupRolePrimeDBMockCreateExpectation

	callArgs []*GroupRolePrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockCreateExpectation specifies expectation struct of the GroupRolePrimeDB.Create
type GroupRolePrimeDBMockCreateExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockCreateParams
	paramPtrs          *GroupRolePrimeDBMockCreateParamPtrs
	expectationOrigins GroupRolePrimeDBMockCreateExpectationOrigins
	results            *GroupRolePrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockCreateParams contains parameters of the GroupRolePrimeDB.Create
type GroupRolePrimeDBMockCreateParams struct {
	data groupentity.GroupRole
}

// GroupRolePrimeDBMockCreateParamPtrs contains pointers to parameters of the GroupRolePrimeDB.Create
type GroupRolePrimeDBMockCreateParamPtrs struct {
	data *groupentity.GroupRole
}

// GroupRolePrimeDBMockCreateResults contains results of the GroupRolePrimeDB.Create
type GroupRolePrimeDBMockCreateResults struct {
	g1  groupentity.GroupRole
	err error
}

// GroupRolePrimeDBMockCreateOrigins contains origins of expectations of the GroupRolePrimeDB.Create
type GroupRolePrimeDBMockCreateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mGroupRolePrimeDBMockCreate) Optional() *mGroupRolePrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for GroupRolePrimeDB.Create
func (mmCreate *mGroupRolePrimeDBMockCreate) Expect(data groupentity.GroupRole) *mGroupRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("GroupRolePrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &GroupRolePrimeDBMockCreateParams{data}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectDataParam1 sets up expected param data for GroupRolePrimeDB.Create
func (mmCreate *mGroupRolePrimeDBMockCreate) ExpectDataParam1(data groupentity.GroupRole) *mGroupRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("GroupRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.data = &data
	mmCreate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.Create
func (mmCreate *mGroupRolePrimeDBMockCreate) Inspect(f func(data groupentity.GroupRole)) *mGroupRolePrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by GroupRolePrimeDB.Create
func (mmCreate *mGroupRolePrimeDBMockCreate) Return(g1 groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupRolePrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &GroupRolePrimeDBMockCreateResults{g1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.Create method
func (mmCreate *mGroupRolePrimeDBMockCreate) Set(f func(data groupentity.GroupRole) (g1 groupentity.GroupRole, err error)) *GroupRolePrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the GroupRolePrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mGroupRolePrimeDBMockCreate) When(data groupentity.GroupRole) *GroupRolePrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupRolePrimeDBMock.Create mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &GroupRolePrimeDBMockCreateParams{data},
		expectationOrigins: GroupRolePrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockCreateExpectation) Then(g1 groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockCreateResults{g1, err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.Create should be invoked
func (mmCreate *mGroupRolePrimeDBMockCreate) Times(n uint64) *mGroupRolePrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of GroupRolePrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mGroupRolePrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.GroupRolePrimeDB
func (mmCreate *GroupRolePrimeDBMock) Create(data groupentity.GroupRole) (g1 groupentity.GroupRole, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(data)
	}

	mm_params := GroupRolePrimeDBMockCreateParams{data}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockCreateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreate.t.Errorf("GroupRolePrimeDBMock.Create got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("GroupRolePrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the GroupRolePrimeDBMock.Create")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(data)
	}
	mmCreate.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.Create. %v", data)
	return
}

// CreateAfterCounter returns a count of finished GroupRolePrimeDBMock.Create invocations
func (mmCreate *GroupRolePrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of GroupRolePrimeDBMock.Create invocations
func (mmCreate *GroupRolePrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mGroupRolePrimeDBMockCreate) Calls() []*GroupRolePrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation
	expectations       []*GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation

	callArgs []*GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation specifies expectation struct of the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
type GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams
	paramPtrs          *GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParamPtrs
	expectationOrigins GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectationOrigins
	results            *GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams contains parameters of the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
type GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams struct {
	ctx     context.Context
	groupID int64
	roleIDs []int64
}

// GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParamPtrs contains pointers to parameters of the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
type GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParamPtrs struct {
	ctx     *context.Context
	groupID *int64
	roleIDs *[]int64
}

// GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsResults contains results of the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
type GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsResults struct {
	err error
}

// GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsOrigins contains origins of expectations of the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
type GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Optional() *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	mmCreateByGroupIDAndRoleIDs.optional = true
	return mmCreateByGroupIDAndRoleIDs
}

// Expect sets up expected params for GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Expect(ctx context.Context, groupID int64, roleIDs []int64) *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	if mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation{}
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by ExpectParams functions")
	}

	mmCreateByGroupIDAndRoleIDs.defaultExpectation.params = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams{ctx, groupID, roleIDs}
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByGroupIDAndRoleIDs.expectations {
		if minimock.Equal(e.params, mmCreateByGroupIDAndRoleIDs.defaultExpectation.params) {
			mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByGroupIDAndRoleIDs.defaultExpectation.params)
		}
	}

	return mmCreateByGroupIDAndRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) ExpectCtxParam1(ctx context.Context) *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	if mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation{}
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.params != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Expect")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParamPtrs{}
	}
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByGroupIDAndRoleIDs
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) ExpectGroupIDParam2(groupID int64) *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	if mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation{}
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.params != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Expect")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParamPtrs{}
	}
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs.groupID = &groupID
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmCreateByGroupIDAndRoleIDs
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) ExpectRoleIDsParam3(roleIDs []int64) *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	if mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation{}
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.params != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Expect")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParamPtrs{}
	}
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmCreateByGroupIDAndRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Inspect(f func(ctx context.Context, groupID int64, roleIDs []int64)) *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	if mmCreateByGroupIDAndRoleIDs.mock.inspectFuncCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs")
	}

	mmCreateByGroupIDAndRoleIDs.mock.inspectFuncCreateByGroupIDAndRoleIDs = f

	return mmCreateByGroupIDAndRoleIDs
}

// Return sets up results that will be returned by GroupRolePrimeDB.CreateByGroupIDAndRoleIDs
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Return(err error) *GroupRolePrimeDBMock {
	if mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndRoleIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation{mock: mmCreateByGroupIDAndRoleIDs.mock}
	}
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.results = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsResults{err}
	mmCreateByGroupIDAndRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByGroupIDAndRoleIDs.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs method
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Set(f func(ctx context.Context, groupID int64, roleIDs []int64) (err error)) *GroupRolePrimeDBMock {
	if mmCreateByGroupIDAndRoleIDs.defaultExpectation != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs method")
	}

	if len(mmCreateByGroupIDAndRoleIDs.expectations) > 0 {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs method")
	}

	mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs = f
	mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByGroupIDAndRoleIDs.mock
}

// When sets expectation for the GroupRolePrimeDB.CreateByGroupIDAndRoleIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) When(ctx context.Context, groupID int64, roleIDs []int64) *GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation {
	if mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation{
		mock:               mmCreateByGroupIDAndRoleIDs.mock,
		params:             &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams{ctx, groupID, roleIDs},
		expectationOrigins: GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByGroupIDAndRoleIDs.expectations = append(mmCreateByGroupIDAndRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.CreateByGroupIDAndRoleIDs return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsExpectation) Then(err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsResults{err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.CreateByGroupIDAndRoleIDs should be invoked
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Times(n uint64) *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs {
	if n == 0 {
		mmCreateByGroupIDAndRoleIDs.mock.t.Fatalf("Times of GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByGroupIDAndRoleIDs.expectedInvocations, n)
	mmCreateByGroupIDAndRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByGroupIDAndRoleIDs
}

func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) invocationsDone() bool {
	if len(mmCreateByGroupIDAndRoleIDs.expectations) == 0 && mmCreateByGroupIDAndRoleIDs.defaultExpectation == nil && mmCreateByGroupIDAndRoleIDs.mock.funcCreateByGroupIDAndRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByGroupIDAndRoleIDs.mock.afterCreateByGroupIDAndRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByGroupIDAndRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByGroupIDAndRoleIDs implements mm_repository.GroupRolePrimeDB
func (mmCreateByGroupIDAndRoleIDs *GroupRolePrimeDBMock) CreateByGroupIDAndRoleIDs(ctx context.Context, groupID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByGroupIDAndRoleIDs.beforeCreateByGroupIDAndRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByGroupIDAndRoleIDs.afterCreateByGroupIDAndRoleIDsCounter, 1)

	mmCreateByGroupIDAndRoleIDs.t.Helper()

	if mmCreateByGroupIDAndRoleIDs.inspectFuncCreateByGroupIDAndRoleIDs != nil {
		mmCreateByGroupIDAndRoleIDs.inspectFuncCreateByGroupIDAndRoleIDs(ctx, groupID, roleIDs)
	}

	mm_params := GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams{ctx, groupID, roleIDs}

	// Record call args
	mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.mutex.Lock()
	mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.callArgs = append(mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.callArgs, &mm_params)
	mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.mutex.Unlock()

	for _, e := range mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams{ctx, groupID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByGroupIDAndRoleIDs.t.Errorf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmCreateByGroupIDAndRoleIDs.t.Errorf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmCreateByGroupIDAndRoleIDs.t.Errorf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByGroupIDAndRoleIDs.t.Errorf("GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByGroupIDAndRoleIDs.CreateByGroupIDAndRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByGroupIDAndRoleIDs.t.Fatal("No results are set for the GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByGroupIDAndRoleIDs.funcCreateByGroupIDAndRoleIDs != nil {
		return mmCreateByGroupIDAndRoleIDs.funcCreateByGroupIDAndRoleIDs(ctx, groupID, roleIDs)
	}
	mmCreateByGroupIDAndRoleIDs.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs. %v %v %v", ctx, groupID, roleIDs)
	return
}

// CreateByGroupIDAndRoleIDsAfterCounter returns a count of finished GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs invocations
func (mmCreateByGroupIDAndRoleIDs *GroupRolePrimeDBMock) CreateByGroupIDAndRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByGroupIDAndRoleIDs.afterCreateByGroupIDAndRoleIDsCounter)
}

// CreateByGroupIDAndRoleIDsBeforeCounter returns a count of GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs invocations
func (mmCreateByGroupIDAndRoleIDs *GroupRolePrimeDBMock) CreateByGroupIDAndRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByGroupIDAndRoleIDs.beforeCreateByGroupIDAndRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByGroupIDAndRoleIDs *mGroupRolePrimeDBMockCreateByGroupIDAndRoleIDs) Calls() []*GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams {
	mmCreateByGroupIDAndRoleIDs.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockCreateByGroupIDAndRoleIDsParams, len(mmCreateByGroupIDAndRoleIDs.callArgs))
	copy(argCopy, mmCreateByGroupIDAndRoleIDs.callArgs)

	mmCreateByGroupIDAndRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByGroupIDAndRoleIDsDone returns true if the count of the CreateByGroupIDAndRoleIDs invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockCreateByGroupIDAndRoleIDsDone() bool {
	if m.CreateByGroupIDAndRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByGroupIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByGroupIDAndRoleIDsMock.invocationsDone()
}

// MinimockCreateByGroupIDAndRoleIDsInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockCreateByGroupIDAndRoleIDsInspect() {
	for _, e := range m.CreateByGroupIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByGroupIDAndRoleIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByGroupIDAndRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByGroupIDAndRoleIDsMock.defaultExpectation != nil && afterCreateByGroupIDAndRoleIDsCounter < 1 {
		if m.CreateByGroupIDAndRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs at\n%s", m.CreateByGroupIDAndRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs at\n%s with params: %#v", m.CreateByGroupIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByGroupIDAndRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByGroupIDAndRoleIDs != nil && afterCreateByGroupIDAndRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs at\n%s", m.funcCreateByGroupIDAndRoleIDsOrigin)
	}

	if !m.CreateByGroupIDAndRoleIDsMock.invocationsDone() && afterCreateByGroupIDAndRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.CreateByGroupIDAndRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByGroupIDAndRoleIDsMock.expectedInvocations), m.CreateByGroupIDAndRoleIDsMock.expectedInvocationsOrigin, afterCreateByGroupIDAndRoleIDsCounter)
	}
}

type mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation
	expectations       []*GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation

	callArgs []*GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation specifies expectation struct of the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
type GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams
	paramPtrs          *GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParamPtrs
	expectationOrigins GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectationOrigins
	results            *GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams contains parameters of the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
type GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams struct {
	ctx      context.Context
	roleID   int64
	groupIDs []int64
}

// GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParamPtrs contains pointers to parameters of the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
type GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParamPtrs struct {
	ctx      *context.Context
	roleID   *int64
	groupIDs *[]int64
}

// GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsResults contains results of the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
type GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsResults struct {
	err error
}

// GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsOrigins contains origins of expectations of the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
type GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectationOrigins struct {
	origin         string
	originCtx      string
	originRoleID   string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Optional() *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	mmCreateByRoleIDAndGroupIDs.optional = true
	return mmCreateByRoleIDAndGroupIDs
}

// Expect sets up expected params for GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Expect(ctx context.Context, roleID int64, groupIDs []int64) *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	if mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation{}
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by ExpectParams functions")
	}

	mmCreateByRoleIDAndGroupIDs.defaultExpectation.params = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams{ctx, roleID, groupIDs}
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByRoleIDAndGroupIDs.expectations {
		if minimock.Equal(e.params, mmCreateByRoleIDAndGroupIDs.defaultExpectation.params) {
			mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByRoleIDAndGroupIDs.defaultExpectation.params)
		}
	}

	return mmCreateByRoleIDAndGroupIDs
}

// ExpectCtxParam1 sets up expected param ctx for GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) ExpectCtxParam1(ctx context.Context) *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	if mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation{}
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParamPtrs{}
	}
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndGroupIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) ExpectRoleIDParam2(roleID int64) *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	if mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation{}
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParamPtrs{}
	}
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndGroupIDs
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) ExpectGroupIDsParam3(groupIDs []int64) *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	if mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation{}
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParamPtrs{}
	}
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndGroupIDs
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Inspect(f func(ctx context.Context, roleID int64, groupIDs []int64)) *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	if mmCreateByRoleIDAndGroupIDs.mock.inspectFuncCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs")
	}

	mmCreateByRoleIDAndGroupIDs.mock.inspectFuncCreateByRoleIDAndGroupIDs = f

	return mmCreateByRoleIDAndGroupIDs
}

// Return sets up results that will be returned by GroupRolePrimeDB.CreateByRoleIDAndGroupIDs
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Return(err error) *GroupRolePrimeDBMock {
	if mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndGroupIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndGroupIDs.defaultExpectation = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation{mock: mmCreateByRoleIDAndGroupIDs.mock}
	}
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.results = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsResults{err}
	mmCreateByRoleIDAndGroupIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndGroupIDs.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs method
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Set(f func(ctx context.Context, roleID int64, groupIDs []int64) (err error)) *GroupRolePrimeDBMock {
	if mmCreateByRoleIDAndGroupIDs.defaultExpectation != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs method")
	}

	if len(mmCreateByRoleIDAndGroupIDs.expectations) > 0 {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs method")
	}

	mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs = f
	mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndGroupIDs.mock
}

// When sets expectation for the GroupRolePrimeDB.CreateByRoleIDAndGroupIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) When(ctx context.Context, roleID int64, groupIDs []int64) *GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation {
	if mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation{
		mock:               mmCreateByRoleIDAndGroupIDs.mock,
		params:             &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams{ctx, roleID, groupIDs},
		expectationOrigins: GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByRoleIDAndGroupIDs.expectations = append(mmCreateByRoleIDAndGroupIDs.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.CreateByRoleIDAndGroupIDs return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsExpectation) Then(err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsResults{err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.CreateByRoleIDAndGroupIDs should be invoked
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Times(n uint64) *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs {
	if n == 0 {
		mmCreateByRoleIDAndGroupIDs.mock.t.Fatalf("Times of GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByRoleIDAndGroupIDs.expectedInvocations, n)
	mmCreateByRoleIDAndGroupIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndGroupIDs
}

func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) invocationsDone() bool {
	if len(mmCreateByRoleIDAndGroupIDs.expectations) == 0 && mmCreateByRoleIDAndGroupIDs.defaultExpectation == nil && mmCreateByRoleIDAndGroupIDs.mock.funcCreateByRoleIDAndGroupIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndGroupIDs.mock.afterCreateByRoleIDAndGroupIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndGroupIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByRoleIDAndGroupIDs implements mm_repository.GroupRolePrimeDB
func (mmCreateByRoleIDAndGroupIDs *GroupRolePrimeDBMock) CreateByRoleIDAndGroupIDs(ctx context.Context, roleID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByRoleIDAndGroupIDs.beforeCreateByRoleIDAndGroupIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByRoleIDAndGroupIDs.afterCreateByRoleIDAndGroupIDsCounter, 1)

	mmCreateByRoleIDAndGroupIDs.t.Helper()

	if mmCreateByRoleIDAndGroupIDs.inspectFuncCreateByRoleIDAndGroupIDs != nil {
		mmCreateByRoleIDAndGroupIDs.inspectFuncCreateByRoleIDAndGroupIDs(ctx, roleID, groupIDs)
	}

	mm_params := GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams{ctx, roleID, groupIDs}

	// Record call args
	mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.mutex.Lock()
	mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.callArgs = append(mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.callArgs, &mm_params)
	mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.mutex.Unlock()

	for _, e := range mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams{ctx, roleID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByRoleIDAndGroupIDs.t.Errorf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmCreateByRoleIDAndGroupIDs.t.Errorf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmCreateByRoleIDAndGroupIDs.t.Errorf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByRoleIDAndGroupIDs.t.Errorf("GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByRoleIDAndGroupIDs.CreateByRoleIDAndGroupIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByRoleIDAndGroupIDs.t.Fatal("No results are set for the GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByRoleIDAndGroupIDs.funcCreateByRoleIDAndGroupIDs != nil {
		return mmCreateByRoleIDAndGroupIDs.funcCreateByRoleIDAndGroupIDs(ctx, roleID, groupIDs)
	}
	mmCreateByRoleIDAndGroupIDs.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs. %v %v %v", ctx, roleID, groupIDs)
	return
}

// CreateByRoleIDAndGroupIDsAfterCounter returns a count of finished GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs invocations
func (mmCreateByRoleIDAndGroupIDs *GroupRolePrimeDBMock) CreateByRoleIDAndGroupIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndGroupIDs.afterCreateByRoleIDAndGroupIDsCounter)
}

// CreateByRoleIDAndGroupIDsBeforeCounter returns a count of GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs invocations
func (mmCreateByRoleIDAndGroupIDs *GroupRolePrimeDBMock) CreateByRoleIDAndGroupIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndGroupIDs.beforeCreateByRoleIDAndGroupIDsCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByRoleIDAndGroupIDs *mGroupRolePrimeDBMockCreateByRoleIDAndGroupIDs) Calls() []*GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams {
	mmCreateByRoleIDAndGroupIDs.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockCreateByRoleIDAndGroupIDsParams, len(mmCreateByRoleIDAndGroupIDs.callArgs))
	copy(argCopy, mmCreateByRoleIDAndGroupIDs.callArgs)

	mmCreateByRoleIDAndGroupIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByRoleIDAndGroupIDsDone returns true if the count of the CreateByRoleIDAndGroupIDs invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockCreateByRoleIDAndGroupIDsDone() bool {
	if m.CreateByRoleIDAndGroupIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByRoleIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByRoleIDAndGroupIDsMock.invocationsDone()
}

// MinimockCreateByRoleIDAndGroupIDsInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockCreateByRoleIDAndGroupIDsInspect() {
	for _, e := range m.CreateByRoleIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByRoleIDAndGroupIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByRoleIDAndGroupIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByRoleIDAndGroupIDsMock.defaultExpectation != nil && afterCreateByRoleIDAndGroupIDsCounter < 1 {
		if m.CreateByRoleIDAndGroupIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs at\n%s", m.CreateByRoleIDAndGroupIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs at\n%s with params: %#v", m.CreateByRoleIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByRoleIDAndGroupIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByRoleIDAndGroupIDs != nil && afterCreateByRoleIDAndGroupIDsCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs at\n%s", m.funcCreateByRoleIDAndGroupIDsOrigin)
	}

	if !m.CreateByRoleIDAndGroupIDsMock.invocationsDone() && afterCreateByRoleIDAndGroupIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.CreateByRoleIDAndGroupIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByRoleIDAndGroupIDsMock.expectedInvocations), m.CreateByRoleIDAndGroupIDsMock.expectedInvocationsOrigin, afterCreateByRoleIDAndGroupIDsCounter)
	}
}

type mGroupRolePrimeDBMockDeleteByGroupID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockDeleteByGroupIDExpectation
	expectations       []*GroupRolePrimeDBMockDeleteByGroupIDExpectation

	callArgs []*GroupRolePrimeDBMockDeleteByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockDeleteByGroupIDExpectation specifies expectation struct of the GroupRolePrimeDB.DeleteByGroupID
type GroupRolePrimeDBMockDeleteByGroupIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockDeleteByGroupIDParams
	paramPtrs          *GroupRolePrimeDBMockDeleteByGroupIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockDeleteByGroupIDExpectationOrigins
	results            *GroupRolePrimeDBMockDeleteByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockDeleteByGroupIDParams contains parameters of the GroupRolePrimeDB.DeleteByGroupID
type GroupRolePrimeDBMockDeleteByGroupIDParams struct {
	ctx     context.Context
	groupID int64
}

// GroupRolePrimeDBMockDeleteByGroupIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.DeleteByGroupID
type GroupRolePrimeDBMockDeleteByGroupIDParamPtrs struct {
	ctx     *context.Context
	groupID *int64
}

// GroupRolePrimeDBMockDeleteByGroupIDResults contains results of the GroupRolePrimeDB.DeleteByGroupID
type GroupRolePrimeDBMockDeleteByGroupIDResults struct {
	err error
}

// GroupRolePrimeDBMockDeleteByGroupIDOrigins contains origins of expectations of the GroupRolePrimeDB.DeleteByGroupID
type GroupRolePrimeDBMockDeleteByGroupIDExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Optional() *mGroupRolePrimeDBMockDeleteByGroupID {
	mmDeleteByGroupID.optional = true
	return mmDeleteByGroupID
}

// Expect sets up expected params for GroupRolePrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Expect(ctx context.Context, groupID int64) *mGroupRolePrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by ExpectParams functions")
	}

	mmDeleteByGroupID.defaultExpectation.params = &GroupRolePrimeDBMockDeleteByGroupIDParams{ctx, groupID}
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByGroupID.expectations {
		if minimock.Equal(e.params, mmDeleteByGroupID.defaultExpectation.params) {
			mmDeleteByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByGroupID.defaultExpectation.params)
		}
	}

	return mmDeleteByGroupID
}

// ExpectCtxParam1 sets up expected param ctx for GroupRolePrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) ExpectCtxParam1(ctx context.Context) *mGroupRolePrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.params != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Expect")
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByGroupIDParamPtrs{}
	}
	mmDeleteByGroupID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByGroupID
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupRolePrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) ExpectGroupIDParam2(groupID int64) *mGroupRolePrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.params != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Expect")
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByGroupIDParamPtrs{}
	}
	mmDeleteByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByGroupID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Inspect(f func(ctx context.Context, groupID int64)) *mGroupRolePrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.inspectFuncDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.DeleteByGroupID")
	}

	mmDeleteByGroupID.mock.inspectFuncDeleteByGroupID = f

	return mmDeleteByGroupID
}

// Return sets up results that will be returned by GroupRolePrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Return(err error) *GroupRolePrimeDBMock {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDExpectation{mock: mmDeleteByGroupID.mock}
	}
	mmDeleteByGroupID.defaultExpectation.results = &GroupRolePrimeDBMockDeleteByGroupIDResults{err}
	mmDeleteByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.DeleteByGroupID method
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Set(f func(ctx context.Context, groupID int64) (err error)) *GroupRolePrimeDBMock {
	if mmDeleteByGroupID.defaultExpectation != nil {
		mmDeleteByGroupID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.DeleteByGroupID method")
	}

	if len(mmDeleteByGroupID.expectations) > 0 {
		mmDeleteByGroupID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.DeleteByGroupID method")
	}

	mmDeleteByGroupID.mock.funcDeleteByGroupID = f
	mmDeleteByGroupID.mock.funcDeleteByGroupIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID.mock
}

// When sets expectation for the GroupRolePrimeDB.DeleteByGroupID which will trigger the result defined by the following
// Then helper
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) When(ctx context.Context, groupID int64) *GroupRolePrimeDBMockDeleteByGroupIDExpectation {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockDeleteByGroupIDExpectation{
		mock:               mmDeleteByGroupID.mock,
		params:             &GroupRolePrimeDBMockDeleteByGroupIDParams{ctx, groupID},
		expectationOrigins: GroupRolePrimeDBMockDeleteByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByGroupID.expectations = append(mmDeleteByGroupID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.DeleteByGroupID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockDeleteByGroupIDExpectation) Then(err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockDeleteByGroupIDResults{err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.DeleteByGroupID should be invoked
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Times(n uint64) *mGroupRolePrimeDBMockDeleteByGroupID {
	if n == 0 {
		mmDeleteByGroupID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.DeleteByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByGroupID.expectedInvocations, n)
	mmDeleteByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID
}

func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) invocationsDone() bool {
	if len(mmDeleteByGroupID.expectations) == 0 && mmDeleteByGroupID.defaultExpectation == nil && mmDeleteByGroupID.mock.funcDeleteByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupID.mock.afterDeleteByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByGroupID implements mm_repository.GroupRolePrimeDB
func (mmDeleteByGroupID *GroupRolePrimeDBMock) DeleteByGroupID(ctx context.Context, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByGroupID.beforeDeleteByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByGroupID.afterDeleteByGroupIDCounter, 1)

	mmDeleteByGroupID.t.Helper()

	if mmDeleteByGroupID.inspectFuncDeleteByGroupID != nil {
		mmDeleteByGroupID.inspectFuncDeleteByGroupID(ctx, groupID)
	}

	mm_params := GroupRolePrimeDBMockDeleteByGroupIDParams{ctx, groupID}

	// Record call args
	mmDeleteByGroupID.DeleteByGroupIDMock.mutex.Lock()
	mmDeleteByGroupID.DeleteByGroupIDMock.callArgs = append(mmDeleteByGroupID.DeleteByGroupIDMock.callArgs, &mm_params)
	mmDeleteByGroupID.DeleteByGroupIDMock.mutex.Unlock()

	for _, e := range mmDeleteByGroupID.DeleteByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockDeleteByGroupIDParams{ctx, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByGroupID.t.Fatal("No results are set for the GroupRolePrimeDBMock.DeleteByGroupID")
		}
		return (*mm_results).err
	}
	if mmDeleteByGroupID.funcDeleteByGroupID != nil {
		return mmDeleteByGroupID.funcDeleteByGroupID(ctx, groupID)
	}
	mmDeleteByGroupID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.DeleteByGroupID. %v %v", ctx, groupID)
	return
}

// DeleteByGroupIDAfterCounter returns a count of finished GroupRolePrimeDBMock.DeleteByGroupID invocations
func (mmDeleteByGroupID *GroupRolePrimeDBMock) DeleteByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupID.afterDeleteByGroupIDCounter)
}

// DeleteByGroupIDBeforeCounter returns a count of GroupRolePrimeDBMock.DeleteByGroupID invocations
func (mmDeleteByGroupID *GroupRolePrimeDBMock) DeleteByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupID.beforeDeleteByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.DeleteByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByGroupID *mGroupRolePrimeDBMockDeleteByGroupID) Calls() []*GroupRolePrimeDBMockDeleteByGroupIDParams {
	mmDeleteByGroupID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockDeleteByGroupIDParams, len(mmDeleteByGroupID.callArgs))
	copy(argCopy, mmDeleteByGroupID.callArgs)

	mmDeleteByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByGroupIDDone returns true if the count of the DeleteByGroupID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockDeleteByGroupIDDone() bool {
	if m.DeleteByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByGroupIDMock.invocationsDone()
}

// MinimockDeleteByGroupIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockDeleteByGroupIDInspect() {
	for _, e := range m.DeleteByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByGroupIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByGroupIDMock.defaultExpectation != nil && afterDeleteByGroupIDCounter < 1 {
		if m.DeleteByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupID at\n%s", m.DeleteByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupID at\n%s with params: %#v", m.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByGroupID != nil && afterDeleteByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupID at\n%s", m.funcDeleteByGroupIDOrigin)
	}

	if !m.DeleteByGroupIDMock.invocationsDone() && afterDeleteByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.DeleteByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByGroupIDMock.expectedInvocations), m.DeleteByGroupIDMock.expectedInvocationsOrigin, afterDeleteByGroupIDCounter)
	}
}

type mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation
	expectations       []*GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation

	callArgs []*GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation specifies expectation struct of the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
type GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams
	paramPtrs          *GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectationOrigins
	results            *GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams contains parameters of the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
type GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams struct {
	ctx      context.Context
	groupIDs []int64
	roleID   int64
}

// GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
type GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParamPtrs struct {
	ctx      *context.Context
	groupIDs *[]int64
	roleID   *int64
}

// GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDResults contains results of the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
type GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDResults struct {
	err error
}

// GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDOrigins contains origins of expectations of the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
type GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectationOrigins struct {
	origin         string
	originCtx      string
	originGroupIDs string
	originRoleID   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Optional() *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	mmDeleteByGroupIDsAndRoleID.optional = true
	return mmDeleteByGroupIDsAndRoleID
}

// Expect sets up expected params for GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Expect(ctx context.Context, groupIDs []int64, roleID int64) *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	if mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Set")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation{}
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by ExpectParams functions")
	}

	mmDeleteByGroupIDsAndRoleID.defaultExpectation.params = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams{ctx, groupIDs, roleID}
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByGroupIDsAndRoleID.expectations {
		if minimock.Equal(e.params, mmDeleteByGroupIDsAndRoleID.defaultExpectation.params) {
			mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByGroupIDsAndRoleID.defaultExpectation.params)
		}
	}

	return mmDeleteByGroupIDsAndRoleID
}

// ExpectCtxParam1 sets up expected param ctx for GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) ExpectCtxParam1(ctx context.Context) *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	if mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Set")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation{}
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.params != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Expect")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParamPtrs{}
	}
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByGroupIDsAndRoleID
}

// ExpectGroupIDsParam2 sets up expected param groupIDs for GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) ExpectGroupIDsParam2(groupIDs []int64) *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	if mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Set")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation{}
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.params != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Expect")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParamPtrs{}
	}
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmDeleteByGroupIDsAndRoleID
}

// ExpectRoleIDParam3 sets up expected param roleID for GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) ExpectRoleIDParam3(roleID int64) *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	if mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Set")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation{}
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.params != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Expect")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParamPtrs{}
	}
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByGroupIDsAndRoleID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Inspect(f func(ctx context.Context, groupIDs []int64, roleID int64)) *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	if mmDeleteByGroupIDsAndRoleID.mock.inspectFuncDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID")
	}

	mmDeleteByGroupIDsAndRoleID.mock.inspectFuncDeleteByGroupIDsAndRoleID = f

	return mmDeleteByGroupIDsAndRoleID
}

// Return sets up results that will be returned by GroupRolePrimeDB.DeleteByGroupIDsAndRoleID
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Return(err error) *GroupRolePrimeDBMock {
	if mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Set")
	}

	if mmDeleteByGroupIDsAndRoleID.defaultExpectation == nil {
		mmDeleteByGroupIDsAndRoleID.defaultExpectation = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation{mock: mmDeleteByGroupIDsAndRoleID.mock}
	}
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.results = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDResults{err}
	mmDeleteByGroupIDsAndRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupIDsAndRoleID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID method
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Set(f func(ctx context.Context, groupIDs []int64, roleID int64) (err error)) *GroupRolePrimeDBMock {
	if mmDeleteByGroupIDsAndRoleID.defaultExpectation != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID method")
	}

	if len(mmDeleteByGroupIDsAndRoleID.expectations) > 0 {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID method")
	}

	mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID = f
	mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupIDsAndRoleID.mock
}

// When sets expectation for the GroupRolePrimeDB.DeleteByGroupIDsAndRoleID which will trigger the result defined by the following
// Then helper
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) When(ctx context.Context, groupIDs []int64, roleID int64) *GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation {
	if mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation{
		mock:               mmDeleteByGroupIDsAndRoleID.mock,
		params:             &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams{ctx, groupIDs, roleID},
		expectationOrigins: GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByGroupIDsAndRoleID.expectations = append(mmDeleteByGroupIDsAndRoleID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.DeleteByGroupIDsAndRoleID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDExpectation) Then(err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDResults{err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.DeleteByGroupIDsAndRoleID should be invoked
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Times(n uint64) *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID {
	if n == 0 {
		mmDeleteByGroupIDsAndRoleID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByGroupIDsAndRoleID.expectedInvocations, n)
	mmDeleteByGroupIDsAndRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupIDsAndRoleID
}

func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) invocationsDone() bool {
	if len(mmDeleteByGroupIDsAndRoleID.expectations) == 0 && mmDeleteByGroupIDsAndRoleID.defaultExpectation == nil && mmDeleteByGroupIDsAndRoleID.mock.funcDeleteByGroupIDsAndRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupIDsAndRoleID.mock.afterDeleteByGroupIDsAndRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupIDsAndRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByGroupIDsAndRoleID implements mm_repository.GroupRolePrimeDB
func (mmDeleteByGroupIDsAndRoleID *GroupRolePrimeDBMock) DeleteByGroupIDsAndRoleID(ctx context.Context, groupIDs []int64, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByGroupIDsAndRoleID.beforeDeleteByGroupIDsAndRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByGroupIDsAndRoleID.afterDeleteByGroupIDsAndRoleIDCounter, 1)

	mmDeleteByGroupIDsAndRoleID.t.Helper()

	if mmDeleteByGroupIDsAndRoleID.inspectFuncDeleteByGroupIDsAndRoleID != nil {
		mmDeleteByGroupIDsAndRoleID.inspectFuncDeleteByGroupIDsAndRoleID(ctx, groupIDs, roleID)
	}

	mm_params := GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams{ctx, groupIDs, roleID}

	// Record call args
	mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.mutex.Lock()
	mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.callArgs = append(mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.callArgs, &mm_params)
	mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.mutex.Unlock()

	for _, e := range mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams{ctx, groupIDs, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByGroupIDsAndRoleID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmDeleteByGroupIDsAndRoleID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByGroupIDsAndRoleID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByGroupIDsAndRoleID.t.Errorf("GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByGroupIDsAndRoleID.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByGroupIDsAndRoleID.t.Fatal("No results are set for the GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID")
		}
		return (*mm_results).err
	}
	if mmDeleteByGroupIDsAndRoleID.funcDeleteByGroupIDsAndRoleID != nil {
		return mmDeleteByGroupIDsAndRoleID.funcDeleteByGroupIDsAndRoleID(ctx, groupIDs, roleID)
	}
	mmDeleteByGroupIDsAndRoleID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID. %v %v %v", ctx, groupIDs, roleID)
	return
}

// DeleteByGroupIDsAndRoleIDAfterCounter returns a count of finished GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID invocations
func (mmDeleteByGroupIDsAndRoleID *GroupRolePrimeDBMock) DeleteByGroupIDsAndRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupIDsAndRoleID.afterDeleteByGroupIDsAndRoleIDCounter)
}

// DeleteByGroupIDsAndRoleIDBeforeCounter returns a count of GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID invocations
func (mmDeleteByGroupIDsAndRoleID *GroupRolePrimeDBMock) DeleteByGroupIDsAndRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupIDsAndRoleID.beforeDeleteByGroupIDsAndRoleIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByGroupIDsAndRoleID *mGroupRolePrimeDBMockDeleteByGroupIDsAndRoleID) Calls() []*GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams {
	mmDeleteByGroupIDsAndRoleID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockDeleteByGroupIDsAndRoleIDParams, len(mmDeleteByGroupIDsAndRoleID.callArgs))
	copy(argCopy, mmDeleteByGroupIDsAndRoleID.callArgs)

	mmDeleteByGroupIDsAndRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByGroupIDsAndRoleIDDone returns true if the count of the DeleteByGroupIDsAndRoleID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockDeleteByGroupIDsAndRoleIDDone() bool {
	if m.DeleteByGroupIDsAndRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByGroupIDsAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByGroupIDsAndRoleIDMock.invocationsDone()
}

// MinimockDeleteByGroupIDsAndRoleIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockDeleteByGroupIDsAndRoleIDInspect() {
	for _, e := range m.DeleteByGroupIDsAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByGroupIDsAndRoleIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByGroupIDsAndRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByGroupIDsAndRoleIDMock.defaultExpectation != nil && afterDeleteByGroupIDsAndRoleIDCounter < 1 {
		if m.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID at\n%s", m.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID at\n%s with params: %#v", m.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByGroupIDsAndRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByGroupIDsAndRoleID != nil && afterDeleteByGroupIDsAndRoleIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID at\n%s", m.funcDeleteByGroupIDsAndRoleIDOrigin)
	}

	if !m.DeleteByGroupIDsAndRoleIDMock.invocationsDone() && afterDeleteByGroupIDsAndRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.DeleteByGroupIDsAndRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByGroupIDsAndRoleIDMock.expectedInvocations), m.DeleteByGroupIDsAndRoleIDMock.expectedInvocationsOrigin, afterDeleteByGroupIDsAndRoleIDCounter)
	}
}

type mGroupRolePrimeDBMockDeleteByRoleIDs struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockDeleteByRoleIDsExpectation
	expectations       []*GroupRolePrimeDBMockDeleteByRoleIDsExpectation

	callArgs []*GroupRolePrimeDBMockDeleteByRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockDeleteByRoleIDsExpectation specifies expectation struct of the GroupRolePrimeDB.DeleteByRoleIDs
type GroupRolePrimeDBMockDeleteByRoleIDsExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockDeleteByRoleIDsParams
	paramPtrs          *GroupRolePrimeDBMockDeleteByRoleIDsParamPtrs
	expectationOrigins GroupRolePrimeDBMockDeleteByRoleIDsExpectationOrigins
	results            *GroupRolePrimeDBMockDeleteByRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockDeleteByRoleIDsParams contains parameters of the GroupRolePrimeDB.DeleteByRoleIDs
type GroupRolePrimeDBMockDeleteByRoleIDsParams struct {
	ctx     context.Context
	roleIDs []int64
}

// GroupRolePrimeDBMockDeleteByRoleIDsParamPtrs contains pointers to parameters of the GroupRolePrimeDB.DeleteByRoleIDs
type GroupRolePrimeDBMockDeleteByRoleIDsParamPtrs struct {
	ctx     *context.Context
	roleIDs *[]int64
}

// GroupRolePrimeDBMockDeleteByRoleIDsResults contains results of the GroupRolePrimeDB.DeleteByRoleIDs
type GroupRolePrimeDBMockDeleteByRoleIDsResults struct {
	err error
}

// GroupRolePrimeDBMockDeleteByRoleIDsOrigins contains origins of expectations of the GroupRolePrimeDB.DeleteByRoleIDs
type GroupRolePrimeDBMockDeleteByRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Optional() *mGroupRolePrimeDBMockDeleteByRoleIDs {
	mmDeleteByRoleIDs.optional = true
	return mmDeleteByRoleIDs
}

// Expect sets up expected params for GroupRolePrimeDB.DeleteByRoleIDs
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Expect(ctx context.Context, roleIDs []int64) *mGroupRolePrimeDBMockDeleteByRoleIDs {
	if mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDs.defaultExpectation == nil {
		mmDeleteByRoleIDs.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsExpectation{}
	}

	if mmDeleteByRoleIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleIDs.defaultExpectation.params = &GroupRolePrimeDBMockDeleteByRoleIDsParams{ctx, roleIDs}
	mmDeleteByRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleIDs.defaultExpectation.params) {
			mmDeleteByRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for GroupRolePrimeDB.DeleteByRoleIDs
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) ExpectCtxParam1(ctx context.Context) *mGroupRolePrimeDBMockDeleteByRoleIDs {
	if mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDs.defaultExpectation == nil {
		mmDeleteByRoleIDs.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsExpectation{}
	}

	if mmDeleteByRoleIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByRoleIDsParamPtrs{}
	}
	mmDeleteByRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleIDs
}

// ExpectRoleIDsParam2 sets up expected param roleIDs for GroupRolePrimeDB.DeleteByRoleIDs
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) ExpectRoleIDsParam2(roleIDs []int64) *mGroupRolePrimeDBMockDeleteByRoleIDs {
	if mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDs.defaultExpectation == nil {
		mmDeleteByRoleIDs.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsExpectation{}
	}

	if mmDeleteByRoleIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDs.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByRoleIDsParamPtrs{}
	}
	mmDeleteByRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeleteByRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeleteByRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.DeleteByRoleIDs
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Inspect(f func(ctx context.Context, roleIDs []int64)) *mGroupRolePrimeDBMockDeleteByRoleIDs {
	if mmDeleteByRoleIDs.mock.inspectFuncDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.DeleteByRoleIDs")
	}

	mmDeleteByRoleIDs.mock.inspectFuncDeleteByRoleIDs = f

	return mmDeleteByRoleIDs
}

// Return sets up results that will be returned by GroupRolePrimeDB.DeleteByRoleIDs
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Return(err error) *GroupRolePrimeDBMock {
	if mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDs.defaultExpectation == nil {
		mmDeleteByRoleIDs.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsExpectation{mock: mmDeleteByRoleIDs.mock}
	}
	mmDeleteByRoleIDs.defaultExpectation.results = &GroupRolePrimeDBMockDeleteByRoleIDsResults{err}
	mmDeleteByRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDs.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.DeleteByRoleIDs method
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Set(f func(ctx context.Context, roleIDs []int64) (err error)) *GroupRolePrimeDBMock {
	if mmDeleteByRoleIDs.defaultExpectation != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.DeleteByRoleIDs method")
	}

	if len(mmDeleteByRoleIDs.expectations) > 0 {
		mmDeleteByRoleIDs.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.DeleteByRoleIDs method")
	}

	mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs = f
	mmDeleteByRoleIDs.mock.funcDeleteByRoleIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDs.mock
}

// When sets expectation for the GroupRolePrimeDB.DeleteByRoleIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) When(ctx context.Context, roleIDs []int64) *GroupRolePrimeDBMockDeleteByRoleIDsExpectation {
	if mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDs mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockDeleteByRoleIDsExpectation{
		mock:               mmDeleteByRoleIDs.mock,
		params:             &GroupRolePrimeDBMockDeleteByRoleIDsParams{ctx, roleIDs},
		expectationOrigins: GroupRolePrimeDBMockDeleteByRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleIDs.expectations = append(mmDeleteByRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.DeleteByRoleIDs return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockDeleteByRoleIDsExpectation) Then(err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockDeleteByRoleIDsResults{err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.DeleteByRoleIDs should be invoked
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Times(n uint64) *mGroupRolePrimeDBMockDeleteByRoleIDs {
	if n == 0 {
		mmDeleteByRoleIDs.mock.t.Fatalf("Times of GroupRolePrimeDBMock.DeleteByRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleIDs.expectedInvocations, n)
	mmDeleteByRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDs
}

func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) invocationsDone() bool {
	if len(mmDeleteByRoleIDs.expectations) == 0 && mmDeleteByRoleIDs.defaultExpectation == nil && mmDeleteByRoleIDs.mock.funcDeleteByRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDs.mock.afterDeleteByRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleIDs implements mm_repository.GroupRolePrimeDB
func (mmDeleteByRoleIDs *GroupRolePrimeDBMock) DeleteByRoleIDs(ctx context.Context, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleIDs.beforeDeleteByRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleIDs.afterDeleteByRoleIDsCounter, 1)

	mmDeleteByRoleIDs.t.Helper()

	if mmDeleteByRoleIDs.inspectFuncDeleteByRoleIDs != nil {
		mmDeleteByRoleIDs.inspectFuncDeleteByRoleIDs(ctx, roleIDs)
	}

	mm_params := GroupRolePrimeDBMockDeleteByRoleIDsParams{ctx, roleIDs}

	// Record call args
	mmDeleteByRoleIDs.DeleteByRoleIDsMock.mutex.Lock()
	mmDeleteByRoleIDs.DeleteByRoleIDsMock.callArgs = append(mmDeleteByRoleIDs.DeleteByRoleIDsMock.callArgs, &mm_params)
	mmDeleteByRoleIDs.DeleteByRoleIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleIDs.DeleteByRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockDeleteByRoleIDsParams{ctx, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleIDs.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeleteByRoleIDs.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleIDs.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleIDs.DeleteByRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleIDs.t.Fatal("No results are set for the GroupRolePrimeDBMock.DeleteByRoleIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleIDs.funcDeleteByRoleIDs != nil {
		return mmDeleteByRoleIDs.funcDeleteByRoleIDs(ctx, roleIDs)
	}
	mmDeleteByRoleIDs.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.DeleteByRoleIDs. %v %v", ctx, roleIDs)
	return
}

// DeleteByRoleIDsAfterCounter returns a count of finished GroupRolePrimeDBMock.DeleteByRoleIDs invocations
func (mmDeleteByRoleIDs *GroupRolePrimeDBMock) DeleteByRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDs.afterDeleteByRoleIDsCounter)
}

// DeleteByRoleIDsBeforeCounter returns a count of GroupRolePrimeDBMock.DeleteByRoleIDs invocations
func (mmDeleteByRoleIDs *GroupRolePrimeDBMock) DeleteByRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDs.beforeDeleteByRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.DeleteByRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleIDs *mGroupRolePrimeDBMockDeleteByRoleIDs) Calls() []*GroupRolePrimeDBMockDeleteByRoleIDsParams {
	mmDeleteByRoleIDs.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockDeleteByRoleIDsParams, len(mmDeleteByRoleIDs.callArgs))
	copy(argCopy, mmDeleteByRoleIDs.callArgs)

	mmDeleteByRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDsDone returns true if the count of the DeleteByRoleIDs invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockDeleteByRoleIDsDone() bool {
	if m.DeleteByRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDsMock.invocationsDone()
}

// MinimockDeleteByRoleIDsInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockDeleteByRoleIDsInspect() {
	for _, e := range m.DeleteByRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDsMock.defaultExpectation != nil && afterDeleteByRoleIDsCounter < 1 {
		if m.DeleteByRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDs at\n%s", m.DeleteByRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDs at\n%s with params: %#v", m.DeleteByRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleIDs != nil && afterDeleteByRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDs at\n%s", m.funcDeleteByRoleIDsOrigin)
	}

	if !m.DeleteByRoleIDsMock.invocationsDone() && afterDeleteByRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.DeleteByRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDsMock.expectedInvocations), m.DeleteByRoleIDsMock.expectedInvocationsOrigin, afterDeleteByRoleIDsCounter)
	}
}

type mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation
	expectations       []*GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation

	callArgs []*GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation specifies expectation struct of the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
type GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams
	paramPtrs          *GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectationOrigins
	results            *GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams contains parameters of the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
type GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams struct {
	ctx     context.Context
	roleIDs []int64
	groupID int64
}

// GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
type GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParamPtrs struct {
	ctx     *context.Context
	roleIDs *[]int64
	groupID *int64
}

// GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDResults contains results of the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
type GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDResults struct {
	err error
}

// GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDOrigins contains origins of expectations of the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
type GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectationOrigins struct {
	origin        string
	originCtx     string
	originRoleIDs string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Optional() *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	mmDeleteByRoleIDsAndGroupID.optional = true
	return mmDeleteByRoleIDsAndGroupID
}

// Expect sets up expected params for GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Expect(ctx context.Context, roleIDs []int64, groupID int64) *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	if mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Set")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation{}
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleIDsAndGroupID.defaultExpectation.params = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams{ctx, roleIDs, groupID}
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleIDsAndGroupID.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleIDsAndGroupID.defaultExpectation.params) {
			mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleIDsAndGroupID.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleIDsAndGroupID
}

// ExpectCtxParam1 sets up expected param ctx for GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) ExpectCtxParam1(ctx context.Context) *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	if mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Set")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation{}
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.params != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Expect")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParamPtrs{}
	}
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleIDsAndGroupID
}

// ExpectRoleIDsParam2 sets up expected param roleIDs for GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) ExpectRoleIDsParam2(roleIDs []int64) *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	if mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Set")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation{}
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.params != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Expect")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParamPtrs{}
	}
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeleteByRoleIDsAndGroupID
}

// ExpectGroupIDParam3 sets up expected param groupID for GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) ExpectGroupIDParam3(groupID int64) *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	if mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Set")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation{}
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.params != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Expect")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParamPtrs{}
	}
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByRoleIDsAndGroupID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Inspect(f func(ctx context.Context, roleIDs []int64, groupID int64)) *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	if mmDeleteByRoleIDsAndGroupID.mock.inspectFuncDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID")
	}

	mmDeleteByRoleIDsAndGroupID.mock.inspectFuncDeleteByRoleIDsAndGroupID = f

	return mmDeleteByRoleIDsAndGroupID
}

// Return sets up results that will be returned by GroupRolePrimeDB.DeleteByRoleIDsAndGroupID
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Return(err error) *GroupRolePrimeDBMock {
	if mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Set")
	}

	if mmDeleteByRoleIDsAndGroupID.defaultExpectation == nil {
		mmDeleteByRoleIDsAndGroupID.defaultExpectation = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation{mock: mmDeleteByRoleIDsAndGroupID.mock}
	}
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.results = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDResults{err}
	mmDeleteByRoleIDsAndGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDsAndGroupID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID method
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Set(f func(ctx context.Context, roleIDs []int64, groupID int64) (err error)) *GroupRolePrimeDBMock {
	if mmDeleteByRoleIDsAndGroupID.defaultExpectation != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID method")
	}

	if len(mmDeleteByRoleIDsAndGroupID.expectations) > 0 {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID method")
	}

	mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID = f
	mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDsAndGroupID.mock
}

// When sets expectation for the GroupRolePrimeDB.DeleteByRoleIDsAndGroupID which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) When(ctx context.Context, roleIDs []int64, groupID int64) *GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation {
	if mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation{
		mock:               mmDeleteByRoleIDsAndGroupID.mock,
		params:             &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams{ctx, roleIDs, groupID},
		expectationOrigins: GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleIDsAndGroupID.expectations = append(mmDeleteByRoleIDsAndGroupID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.DeleteByRoleIDsAndGroupID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDExpectation) Then(err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDResults{err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.DeleteByRoleIDsAndGroupID should be invoked
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Times(n uint64) *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID {
	if n == 0 {
		mmDeleteByRoleIDsAndGroupID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleIDsAndGroupID.expectedInvocations, n)
	mmDeleteByRoleIDsAndGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDsAndGroupID
}

func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) invocationsDone() bool {
	if len(mmDeleteByRoleIDsAndGroupID.expectations) == 0 && mmDeleteByRoleIDsAndGroupID.defaultExpectation == nil && mmDeleteByRoleIDsAndGroupID.mock.funcDeleteByRoleIDsAndGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDsAndGroupID.mock.afterDeleteByRoleIDsAndGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDsAndGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleIDsAndGroupID implements mm_repository.GroupRolePrimeDB
func (mmDeleteByRoleIDsAndGroupID *GroupRolePrimeDBMock) DeleteByRoleIDsAndGroupID(ctx context.Context, roleIDs []int64, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleIDsAndGroupID.beforeDeleteByRoleIDsAndGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleIDsAndGroupID.afterDeleteByRoleIDsAndGroupIDCounter, 1)

	mmDeleteByRoleIDsAndGroupID.t.Helper()

	if mmDeleteByRoleIDsAndGroupID.inspectFuncDeleteByRoleIDsAndGroupID != nil {
		mmDeleteByRoleIDsAndGroupID.inspectFuncDeleteByRoleIDsAndGroupID(ctx, roleIDs, groupID)
	}

	mm_params := GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams{ctx, roleIDs, groupID}

	// Record call args
	mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.mutex.Lock()
	mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.callArgs = append(mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.callArgs, &mm_params)
	mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams{ctx, roleIDs, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleIDsAndGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeleteByRoleIDsAndGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByRoleIDsAndGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleIDsAndGroupID.t.Errorf("GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleIDsAndGroupID.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleIDsAndGroupID.t.Fatal("No results are set for the GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleIDsAndGroupID.funcDeleteByRoleIDsAndGroupID != nil {
		return mmDeleteByRoleIDsAndGroupID.funcDeleteByRoleIDsAndGroupID(ctx, roleIDs, groupID)
	}
	mmDeleteByRoleIDsAndGroupID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID. %v %v %v", ctx, roleIDs, groupID)
	return
}

// DeleteByRoleIDsAndGroupIDAfterCounter returns a count of finished GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID invocations
func (mmDeleteByRoleIDsAndGroupID *GroupRolePrimeDBMock) DeleteByRoleIDsAndGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDsAndGroupID.afterDeleteByRoleIDsAndGroupIDCounter)
}

// DeleteByRoleIDsAndGroupIDBeforeCounter returns a count of GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID invocations
func (mmDeleteByRoleIDsAndGroupID *GroupRolePrimeDBMock) DeleteByRoleIDsAndGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDsAndGroupID.beforeDeleteByRoleIDsAndGroupIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleIDsAndGroupID *mGroupRolePrimeDBMockDeleteByRoleIDsAndGroupID) Calls() []*GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams {
	mmDeleteByRoleIDsAndGroupID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockDeleteByRoleIDsAndGroupIDParams, len(mmDeleteByRoleIDsAndGroupID.callArgs))
	copy(argCopy, mmDeleteByRoleIDsAndGroupID.callArgs)

	mmDeleteByRoleIDsAndGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDsAndGroupIDDone returns true if the count of the DeleteByRoleIDsAndGroupID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockDeleteByRoleIDsAndGroupIDDone() bool {
	if m.DeleteByRoleIDsAndGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDsAndGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDsAndGroupIDMock.invocationsDone()
}

// MinimockDeleteByRoleIDsAndGroupIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockDeleteByRoleIDsAndGroupIDInspect() {
	for _, e := range m.DeleteByRoleIDsAndGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDsAndGroupIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDsAndGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDsAndGroupIDMock.defaultExpectation != nil && afterDeleteByRoleIDsAndGroupIDCounter < 1 {
		if m.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID at\n%s", m.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID at\n%s with params: %#v", m.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDsAndGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleIDsAndGroupID != nil && afterDeleteByRoleIDsAndGroupIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID at\n%s", m.funcDeleteByRoleIDsAndGroupIDOrigin)
	}

	if !m.DeleteByRoleIDsAndGroupIDMock.invocationsDone() && afterDeleteByRoleIDsAndGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.DeleteByRoleIDsAndGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDsAndGroupIDMock.expectedInvocations), m.DeleteByRoleIDsAndGroupIDMock.expectedInvocationsOrigin, afterDeleteByRoleIDsAndGroupIDCounter)
	}
}

type mGroupRolePrimeDBMockGetAll struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockGetAllExpectation
	expectations       []*GroupRolePrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockGetAllExpectation specifies expectation struct of the GroupRolePrimeDB.GetAll
type GroupRolePrimeDBMockGetAllExpectation struct {
	mock *GroupRolePrimeDBMock

	results      *GroupRolePrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// GroupRolePrimeDBMockGetAllResults contains results of the GroupRolePrimeDB.GetAll
type GroupRolePrimeDBMockGetAllResults struct {
	ga1 []groupentity.GroupRole
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mGroupRolePrimeDBMockGetAll) Optional() *mGroupRolePrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for GroupRolePrimeDB.GetAll
func (mmGetAll *mGroupRolePrimeDBMockGetAll) Expect() *mGroupRolePrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("GroupRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &GroupRolePrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.GetAll
func (mmGetAll *mGroupRolePrimeDBMockGetAll) Inspect(f func()) *mGroupRolePrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by GroupRolePrimeDB.GetAll
func (mmGetAll *mGroupRolePrimeDBMockGetAll) Return(ga1 []groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("GroupRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &GroupRolePrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &GroupRolePrimeDBMockGetAllResults{ga1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.GetAll method
func (mmGetAll *mGroupRolePrimeDBMockGetAll) Set(f func() (ga1 []groupentity.GroupRole, err error)) *GroupRolePrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times GroupRolePrimeDB.GetAll should be invoked
func (mmGetAll *mGroupRolePrimeDBMockGetAll) Times(n uint64) *mGroupRolePrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of GroupRolePrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mGroupRolePrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.GroupRolePrimeDB
func (mmGetAll *GroupRolePrimeDBMock) GetAll() (ga1 []groupentity.GroupRole, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the GroupRolePrimeDBMock.GetAll")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished GroupRolePrimeDBMock.GetAll invocations
func (mmGetAll *GroupRolePrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of GroupRolePrimeDBMock.GetAll invocations
func (mmGetAll *GroupRolePrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupRolePrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mGroupRolePrimeDBMockGetByGroupID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockGetByGroupIDExpectation
	expectations       []*GroupRolePrimeDBMockGetByGroupIDExpectation

	callArgs []*GroupRolePrimeDBMockGetByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockGetByGroupIDExpectation specifies expectation struct of the GroupRolePrimeDB.GetByGroupID
type GroupRolePrimeDBMockGetByGroupIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockGetByGroupIDParams
	paramPtrs          *GroupRolePrimeDBMockGetByGroupIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockGetByGroupIDExpectationOrigins
	results            *GroupRolePrimeDBMockGetByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockGetByGroupIDParams contains parameters of the GroupRolePrimeDB.GetByGroupID
type GroupRolePrimeDBMockGetByGroupIDParams struct {
	groupID int64
}

// GroupRolePrimeDBMockGetByGroupIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.GetByGroupID
type GroupRolePrimeDBMockGetByGroupIDParamPtrs struct {
	groupID *int64
}

// GroupRolePrimeDBMockGetByGroupIDResults contains results of the GroupRolePrimeDB.GetByGroupID
type GroupRolePrimeDBMockGetByGroupIDResults struct {
	ga1 []groupentity.GroupRole
	err error
}

// GroupRolePrimeDBMockGetByGroupIDOrigins contains origins of expectations of the GroupRolePrimeDB.GetByGroupID
type GroupRolePrimeDBMockGetByGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Optional() *mGroupRolePrimeDBMockGetByGroupID {
	mmGetByGroupID.optional = true
	return mmGetByGroupID
}

// Expect sets up expected params for GroupRolePrimeDB.GetByGroupID
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Expect(groupID int64) *mGroupRolePrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupID mock is already set by ExpectParams functions")
	}

	mmGetByGroupID.defaultExpectation.params = &GroupRolePrimeDBMockGetByGroupIDParams{groupID}
	mmGetByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupID.expectations {
		if minimock.Equal(e.params, mmGetByGroupID.defaultExpectation.params) {
			mmGetByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupID.defaultExpectation.params)
		}
	}

	return mmGetByGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for GroupRolePrimeDB.GetByGroupID
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) ExpectGroupIDParam1(groupID int64) *mGroupRolePrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.params != nil {
		mmGetByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupID mock is already set by Expect")
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockGetByGroupIDParamPtrs{}
	}
	mmGetByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.GetByGroupID
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Inspect(f func(groupID int64)) *mGroupRolePrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.GetByGroupID")
	}

	mmGetByGroupID.mock.inspectFuncGetByGroupID = f

	return mmGetByGroupID
}

// Return sets up results that will be returned by GroupRolePrimeDB.GetByGroupID
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Return(ga1 []groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupIDExpectation{mock: mmGetByGroupID.mock}
	}
	mmGetByGroupID.defaultExpectation.results = &GroupRolePrimeDBMockGetByGroupIDResults{ga1, err}
	mmGetByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.GetByGroupID method
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Set(f func(groupID int64) (ga1 []groupentity.GroupRole, err error)) *GroupRolePrimeDBMock {
	if mmGetByGroupID.defaultExpectation != nil {
		mmGetByGroupID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.GetByGroupID method")
	}

	if len(mmGetByGroupID.expectations) > 0 {
		mmGetByGroupID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.GetByGroupID method")
	}

	mmGetByGroupID.mock.funcGetByGroupID = f
	mmGetByGroupID.mock.funcGetByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// When sets expectation for the GroupRolePrimeDB.GetByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) When(groupID int64) *GroupRolePrimeDBMockGetByGroupIDExpectation {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockGetByGroupIDExpectation{
		mock:               mmGetByGroupID.mock,
		params:             &GroupRolePrimeDBMockGetByGroupIDParams{groupID},
		expectationOrigins: GroupRolePrimeDBMockGetByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupID.expectations = append(mmGetByGroupID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.GetByGroupID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockGetByGroupIDExpectation) Then(ga1 []groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockGetByGroupIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.GetByGroupID should be invoked
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Times(n uint64) *mGroupRolePrimeDBMockGetByGroupID {
	if n == 0 {
		mmGetByGroupID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.GetByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupID.expectedInvocations, n)
	mmGetByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID
}

func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) invocationsDone() bool {
	if len(mmGetByGroupID.expectations) == 0 && mmGetByGroupID.defaultExpectation == nil && mmGetByGroupID.mock.funcGetByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.mock.afterGetByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupID implements mm_repository.GroupRolePrimeDB
func (mmGetByGroupID *GroupRolePrimeDBMock) GetByGroupID(groupID int64) (ga1 []groupentity.GroupRole, err error) {
	mm_atomic.AddUint64(&mmGetByGroupID.beforeGetByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupID.afterGetByGroupIDCounter, 1)

	mmGetByGroupID.t.Helper()

	if mmGetByGroupID.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.inspectFuncGetByGroupID(groupID)
	}

	mm_params := GroupRolePrimeDBMockGetByGroupIDParams{groupID}

	// Record call args
	mmGetByGroupID.GetByGroupIDMock.mutex.Lock()
	mmGetByGroupID.GetByGroupIDMock.callArgs = append(mmGetByGroupID.GetByGroupIDMock.callArgs, &mm_params)
	mmGetByGroupID.GetByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupID.GetByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByGroupID.GetByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupID.GetByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockGetByGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupID.t.Errorf("GroupRolePrimeDBMock.GetByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupID.t.Errorf("GroupRolePrimeDBMock.GetByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupID.t.Fatal("No results are set for the GroupRolePrimeDBMock.GetByGroupID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByGroupID.funcGetByGroupID != nil {
		return mmGetByGroupID.funcGetByGroupID(groupID)
	}
	mmGetByGroupID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.GetByGroupID. %v", groupID)
	return
}

// GetByGroupIDAfterCounter returns a count of finished GroupRolePrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *GroupRolePrimeDBMock) GetByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.afterGetByGroupIDCounter)
}

// GetByGroupIDBeforeCounter returns a count of GroupRolePrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *GroupRolePrimeDBMock) GetByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.beforeGetByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.GetByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupID *mGroupRolePrimeDBMockGetByGroupID) Calls() []*GroupRolePrimeDBMockGetByGroupIDParams {
	mmGetByGroupID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockGetByGroupIDParams, len(mmGetByGroupID.callArgs))
	copy(argCopy, mmGetByGroupID.callArgs)

	mmGetByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDDone returns true if the count of the GetByGroupID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockGetByGroupIDDone() bool {
	if m.GetByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDMock.invocationsDone()
}

// MinimockGetByGroupIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockGetByGroupIDInspect() {
	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDMock.defaultExpectation != nil && afterGetByGroupIDCounter < 1 {
		if m.GetByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupID at\n%s", m.GetByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupID at\n%s with params: %#v", m.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupID != nil && afterGetByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupID at\n%s", m.funcGetByGroupIDOrigin)
	}

	if !m.GetByGroupIDMock.invocationsDone() && afterGetByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.GetByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDMock.expectedInvocations), m.GetByGroupIDMock.expectedInvocationsOrigin, afterGetByGroupIDCounter)
	}
}

type mGroupRolePrimeDBMockGetByGroupRoleID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockGetByGroupRoleIDExpectation
	expectations       []*GroupRolePrimeDBMockGetByGroupRoleIDExpectation

	callArgs []*GroupRolePrimeDBMockGetByGroupRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockGetByGroupRoleIDExpectation specifies expectation struct of the GroupRolePrimeDB.GetByGroupRoleID
type GroupRolePrimeDBMockGetByGroupRoleIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockGetByGroupRoleIDParams
	paramPtrs          *GroupRolePrimeDBMockGetByGroupRoleIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockGetByGroupRoleIDExpectationOrigins
	results            *GroupRolePrimeDBMockGetByGroupRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockGetByGroupRoleIDParams contains parameters of the GroupRolePrimeDB.GetByGroupRoleID
type GroupRolePrimeDBMockGetByGroupRoleIDParams struct {
	groupID int64
	roleID  int64
}

// GroupRolePrimeDBMockGetByGroupRoleIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.GetByGroupRoleID
type GroupRolePrimeDBMockGetByGroupRoleIDParamPtrs struct {
	groupID *int64
	roleID  *int64
}

// GroupRolePrimeDBMockGetByGroupRoleIDResults contains results of the GroupRolePrimeDB.GetByGroupRoleID
type GroupRolePrimeDBMockGetByGroupRoleIDResults struct {
	g1  groupentity.GroupRole
	err error
}

// GroupRolePrimeDBMockGetByGroupRoleIDOrigins contains origins of expectations of the GroupRolePrimeDB.GetByGroupRoleID
type GroupRolePrimeDBMockGetByGroupRoleIDExpectationOrigins struct {
	origin        string
	originGroupID string
	originRoleID  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Optional() *mGroupRolePrimeDBMockGetByGroupRoleID {
	mmGetByGroupRoleID.optional = true
	return mmGetByGroupRoleID
}

// Expect sets up expected params for GroupRolePrimeDB.GetByGroupRoleID
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Expect(groupID int64, roleID int64) *mGroupRolePrimeDBMockGetByGroupRoleID {
	if mmGetByGroupRoleID.mock.funcGetByGroupRoleID != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Set")
	}

	if mmGetByGroupRoleID.defaultExpectation == nil {
		mmGetByGroupRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupRoleIDExpectation{}
	}

	if mmGetByGroupRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by ExpectParams functions")
	}

	mmGetByGroupRoleID.defaultExpectation.params = &GroupRolePrimeDBMockGetByGroupRoleIDParams{groupID, roleID}
	mmGetByGroupRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupRoleID.expectations {
		if minimock.Equal(e.params, mmGetByGroupRoleID.defaultExpectation.params) {
			mmGetByGroupRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupRoleID.defaultExpectation.params)
		}
	}

	return mmGetByGroupRoleID
}

// ExpectGroupIDParam1 sets up expected param groupID for GroupRolePrimeDB.GetByGroupRoleID
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) ExpectGroupIDParam1(groupID int64) *mGroupRolePrimeDBMockGetByGroupRoleID {
	if mmGetByGroupRoleID.mock.funcGetByGroupRoleID != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Set")
	}

	if mmGetByGroupRoleID.defaultExpectation == nil {
		mmGetByGroupRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupRoleIDExpectation{}
	}

	if mmGetByGroupRoleID.defaultExpectation.params != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Expect")
	}

	if mmGetByGroupRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupRoleID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockGetByGroupRoleIDParamPtrs{}
	}
	mmGetByGroupRoleID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupRoleID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupRoleID
}

// ExpectRoleIDParam2 sets up expected param roleID for GroupRolePrimeDB.GetByGroupRoleID
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) ExpectRoleIDParam2(roleID int64) *mGroupRolePrimeDBMockGetByGroupRoleID {
	if mmGetByGroupRoleID.mock.funcGetByGroupRoleID != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Set")
	}

	if mmGetByGroupRoleID.defaultExpectation == nil {
		mmGetByGroupRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupRoleIDExpectation{}
	}

	if mmGetByGroupRoleID.defaultExpectation.params != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Expect")
	}

	if mmGetByGroupRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupRoleID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockGetByGroupRoleIDParamPtrs{}
	}
	mmGetByGroupRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByGroupRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByGroupRoleID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.GetByGroupRoleID
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Inspect(f func(groupID int64, roleID int64)) *mGroupRolePrimeDBMockGetByGroupRoleID {
	if mmGetByGroupRoleID.mock.inspectFuncGetByGroupRoleID != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.GetByGroupRoleID")
	}

	mmGetByGroupRoleID.mock.inspectFuncGetByGroupRoleID = f

	return mmGetByGroupRoleID
}

// Return sets up results that will be returned by GroupRolePrimeDB.GetByGroupRoleID
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Return(g1 groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	if mmGetByGroupRoleID.mock.funcGetByGroupRoleID != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Set")
	}

	if mmGetByGroupRoleID.defaultExpectation == nil {
		mmGetByGroupRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByGroupRoleIDExpectation{mock: mmGetByGroupRoleID.mock}
	}
	mmGetByGroupRoleID.defaultExpectation.results = &GroupRolePrimeDBMockGetByGroupRoleIDResults{g1, err}
	mmGetByGroupRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupRoleID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.GetByGroupRoleID method
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Set(f func(groupID int64, roleID int64) (g1 groupentity.GroupRole, err error)) *GroupRolePrimeDBMock {
	if mmGetByGroupRoleID.defaultExpectation != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.GetByGroupRoleID method")
	}

	if len(mmGetByGroupRoleID.expectations) > 0 {
		mmGetByGroupRoleID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.GetByGroupRoleID method")
	}

	mmGetByGroupRoleID.mock.funcGetByGroupRoleID = f
	mmGetByGroupRoleID.mock.funcGetByGroupRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupRoleID.mock
}

// When sets expectation for the GroupRolePrimeDB.GetByGroupRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) When(groupID int64, roleID int64) *GroupRolePrimeDBMockGetByGroupRoleIDExpectation {
	if mmGetByGroupRoleID.mock.funcGetByGroupRoleID != nil {
		mmGetByGroupRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByGroupRoleID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockGetByGroupRoleIDExpectation{
		mock:               mmGetByGroupRoleID.mock,
		params:             &GroupRolePrimeDBMockGetByGroupRoleIDParams{groupID, roleID},
		expectationOrigins: GroupRolePrimeDBMockGetByGroupRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupRoleID.expectations = append(mmGetByGroupRoleID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.GetByGroupRoleID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockGetByGroupRoleIDExpectation) Then(g1 groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockGetByGroupRoleIDResults{g1, err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.GetByGroupRoleID should be invoked
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Times(n uint64) *mGroupRolePrimeDBMockGetByGroupRoleID {
	if n == 0 {
		mmGetByGroupRoleID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.GetByGroupRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupRoleID.expectedInvocations, n)
	mmGetByGroupRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupRoleID
}

func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) invocationsDone() bool {
	if len(mmGetByGroupRoleID.expectations) == 0 && mmGetByGroupRoleID.defaultExpectation == nil && mmGetByGroupRoleID.mock.funcGetByGroupRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupRoleID.mock.afterGetByGroupRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupRoleID implements mm_repository.GroupRolePrimeDB
func (mmGetByGroupRoleID *GroupRolePrimeDBMock) GetByGroupRoleID(groupID int64, roleID int64) (g1 groupentity.GroupRole, err error) {
	mm_atomic.AddUint64(&mmGetByGroupRoleID.beforeGetByGroupRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupRoleID.afterGetByGroupRoleIDCounter, 1)

	mmGetByGroupRoleID.t.Helper()

	if mmGetByGroupRoleID.inspectFuncGetByGroupRoleID != nil {
		mmGetByGroupRoleID.inspectFuncGetByGroupRoleID(groupID, roleID)
	}

	mm_params := GroupRolePrimeDBMockGetByGroupRoleIDParams{groupID, roleID}

	// Record call args
	mmGetByGroupRoleID.GetByGroupRoleIDMock.mutex.Lock()
	mmGetByGroupRoleID.GetByGroupRoleIDMock.callArgs = append(mmGetByGroupRoleID.GetByGroupRoleIDMock.callArgs, &mm_params)
	mmGetByGroupRoleID.GetByGroupRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupRoleID.GetByGroupRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockGetByGroupRoleIDParams{groupID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupRoleID.t.Errorf("GroupRolePrimeDBMock.GetByGroupRoleID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByGroupRoleID.t.Errorf("GroupRolePrimeDBMock.GetByGroupRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupRoleID.t.Errorf("GroupRolePrimeDBMock.GetByGroupRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupRoleID.GetByGroupRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupRoleID.t.Fatal("No results are set for the GroupRolePrimeDBMock.GetByGroupRoleID")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetByGroupRoleID.funcGetByGroupRoleID != nil {
		return mmGetByGroupRoleID.funcGetByGroupRoleID(groupID, roleID)
	}
	mmGetByGroupRoleID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.GetByGroupRoleID. %v %v", groupID, roleID)
	return
}

// GetByGroupRoleIDAfterCounter returns a count of finished GroupRolePrimeDBMock.GetByGroupRoleID invocations
func (mmGetByGroupRoleID *GroupRolePrimeDBMock) GetByGroupRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupRoleID.afterGetByGroupRoleIDCounter)
}

// GetByGroupRoleIDBeforeCounter returns a count of GroupRolePrimeDBMock.GetByGroupRoleID invocations
func (mmGetByGroupRoleID *GroupRolePrimeDBMock) GetByGroupRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupRoleID.beforeGetByGroupRoleIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.GetByGroupRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupRoleID *mGroupRolePrimeDBMockGetByGroupRoleID) Calls() []*GroupRolePrimeDBMockGetByGroupRoleIDParams {
	mmGetByGroupRoleID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockGetByGroupRoleIDParams, len(mmGetByGroupRoleID.callArgs))
	copy(argCopy, mmGetByGroupRoleID.callArgs)

	mmGetByGroupRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupRoleIDDone returns true if the count of the GetByGroupRoleID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockGetByGroupRoleIDDone() bool {
	if m.GetByGroupRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupRoleIDMock.invocationsDone()
}

// MinimockGetByGroupRoleIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockGetByGroupRoleIDInspect() {
	for _, e := range m.GetByGroupRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupRoleIDMock.defaultExpectation != nil && afterGetByGroupRoleIDCounter < 1 {
		if m.GetByGroupRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupRoleID at\n%s", m.GetByGroupRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupRoleID at\n%s with params: %#v", m.GetByGroupRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupRoleID != nil && afterGetByGroupRoleIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByGroupRoleID at\n%s", m.funcGetByGroupRoleIDOrigin)
	}

	if !m.GetByGroupRoleIDMock.invocationsDone() && afterGetByGroupRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.GetByGroupRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupRoleIDMock.expectedInvocations), m.GetByGroupRoleIDMock.expectedInvocationsOrigin, afterGetByGroupRoleIDCounter)
	}
}

type mGroupRolePrimeDBMockGetByID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockGetByIDExpectation
	expectations       []*GroupRolePrimeDBMockGetByIDExpectation

	callArgs []*GroupRolePrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockGetByIDExpectation specifies expectation struct of the GroupRolePrimeDB.GetByID
type GroupRolePrimeDBMockGetByIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockGetByIDParams
	paramPtrs          *GroupRolePrimeDBMockGetByIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockGetByIDExpectationOrigins
	results            *GroupRolePrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockGetByIDParams contains parameters of the GroupRolePrimeDB.GetByID
type GroupRolePrimeDBMockGetByIDParams struct {
	id int64
}

// GroupRolePrimeDBMockGetByIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.GetByID
type GroupRolePrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// GroupRolePrimeDBMockGetByIDResults contains results of the GroupRolePrimeDB.GetByID
type GroupRolePrimeDBMockGetByIDResults struct {
	g1  groupentity.GroupRole
	err error
}

// GroupRolePrimeDBMockGetByIDOrigins contains origins of expectations of the GroupRolePrimeDB.GetByID
type GroupRolePrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Optional() *mGroupRolePrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for GroupRolePrimeDB.GetByID
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Expect(id int64) *mGroupRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &GroupRolePrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for GroupRolePrimeDB.GetByID
func (mmGetByID *mGroupRolePrimeDBMockGetByID) ExpectIdParam1(id int64) *mGroupRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.GetByID
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Inspect(f func(id int64)) *mGroupRolePrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by GroupRolePrimeDB.GetByID
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Return(g1 groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupRolePrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &GroupRolePrimeDBMockGetByIDResults{g1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.GetByID method
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Set(f func(id int64) (g1 groupentity.GroupRole, err error)) *GroupRolePrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the GroupRolePrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mGroupRolePrimeDBMockGetByID) When(id int64) *GroupRolePrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &GroupRolePrimeDBMockGetByIDParams{id},
		expectationOrigins: GroupRolePrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockGetByIDExpectation) Then(g1 groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockGetByIDResults{g1, err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.GetByID should be invoked
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Times(n uint64) *mGroupRolePrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mGroupRolePrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.GroupRolePrimeDB
func (mmGetByID *GroupRolePrimeDBMock) GetByID(id int64) (g1 groupentity.GroupRole, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := GroupRolePrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("GroupRolePrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("GroupRolePrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the GroupRolePrimeDBMock.GetByID")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished GroupRolePrimeDBMock.GetByID invocations
func (mmGetByID *GroupRolePrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of GroupRolePrimeDBMock.GetByID invocations
func (mmGetByID *GroupRolePrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mGroupRolePrimeDBMockGetByID) Calls() []*GroupRolePrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mGroupRolePrimeDBMockGetByRoleID struct {
	optional           bool
	mock               *GroupRolePrimeDBMock
	defaultExpectation *GroupRolePrimeDBMockGetByRoleIDExpectation
	expectations       []*GroupRolePrimeDBMockGetByRoleIDExpectation

	callArgs []*GroupRolePrimeDBMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupRolePrimeDBMockGetByRoleIDExpectation specifies expectation struct of the GroupRolePrimeDB.GetByRoleID
type GroupRolePrimeDBMockGetByRoleIDExpectation struct {
	mock               *GroupRolePrimeDBMock
	params             *GroupRolePrimeDBMockGetByRoleIDParams
	paramPtrs          *GroupRolePrimeDBMockGetByRoleIDParamPtrs
	expectationOrigins GroupRolePrimeDBMockGetByRoleIDExpectationOrigins
	results            *GroupRolePrimeDBMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupRolePrimeDBMockGetByRoleIDParams contains parameters of the GroupRolePrimeDB.GetByRoleID
type GroupRolePrimeDBMockGetByRoleIDParams struct {
	roleID int64
}

// GroupRolePrimeDBMockGetByRoleIDParamPtrs contains pointers to parameters of the GroupRolePrimeDB.GetByRoleID
type GroupRolePrimeDBMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// GroupRolePrimeDBMockGetByRoleIDResults contains results of the GroupRolePrimeDB.GetByRoleID
type GroupRolePrimeDBMockGetByRoleIDResults struct {
	ga1 []groupentity.GroupRole
	err error
}

// GroupRolePrimeDBMockGetByRoleIDOrigins contains origins of expectations of the GroupRolePrimeDB.GetByRoleID
type GroupRolePrimeDBMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Optional() *mGroupRolePrimeDBMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for GroupRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Expect(roleID int64) *mGroupRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &GroupRolePrimeDBMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for GroupRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mGroupRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &GroupRolePrimeDBMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the GroupRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Inspect(f func(roleID int64)) *mGroupRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for GroupRolePrimeDBMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by GroupRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Return(ga1 []groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &GroupRolePrimeDBMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &GroupRolePrimeDBMockGetByRoleIDResults{ga1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the GroupRolePrimeDB.GetByRoleID method
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Set(f func(roleID int64) (ga1 []groupentity.GroupRole, err error)) *GroupRolePrimeDBMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the GroupRolePrimeDB.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the GroupRolePrimeDB.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the GroupRolePrimeDB.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) When(roleID int64) *GroupRolePrimeDBMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	expectation := &GroupRolePrimeDBMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &GroupRolePrimeDBMockGetByRoleIDParams{roleID},
		expectationOrigins: GroupRolePrimeDBMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up GroupRolePrimeDB.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *GroupRolePrimeDBMockGetByRoleIDExpectation) Then(ga1 []groupentity.GroupRole, err error) *GroupRolePrimeDBMock {
	e.results = &GroupRolePrimeDBMockGetByRoleIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupRolePrimeDB.GetByRoleID should be invoked
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Times(n uint64) *mGroupRolePrimeDBMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of GroupRolePrimeDBMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_repository.GroupRolePrimeDB
func (mmGetByRoleID *GroupRolePrimeDBMock) GetByRoleID(roleID int64) (ga1 []groupentity.GroupRole, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := GroupRolePrimeDBMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := GroupRolePrimeDBMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("GroupRolePrimeDBMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("GroupRolePrimeDBMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the GroupRolePrimeDBMock.GetByRoleID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to GroupRolePrimeDBMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished GroupRolePrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *GroupRolePrimeDBMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of GroupRolePrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *GroupRolePrimeDBMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to GroupRolePrimeDBMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mGroupRolePrimeDBMockGetByRoleID) Calls() []*GroupRolePrimeDBMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*GroupRolePrimeDBMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *GroupRolePrimeDBMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *GroupRolePrimeDBMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to GroupRolePrimeDBMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupRolePrimeDBMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *GroupRolePrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByGroupIDAndRoleIDsInspect()

			m.MinimockCreateByRoleIDAndGroupIDsInspect()

			m.MinimockDeleteByGroupIDInspect()

			m.MinimockDeleteByGroupIDsAndRoleIDInspect()

			m.MinimockDeleteByRoleIDsInspect()

			m.MinimockDeleteByRoleIDsAndGroupIDInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByGroupIDInspect()

			m.MinimockGetByGroupRoleIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByRoleIDInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *GroupRolePrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *GroupRolePrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByGroupIDAndRoleIDsDone() &&
		m.MinimockCreateByRoleIDAndGroupIDsDone() &&
		m.MinimockDeleteByGroupIDDone() &&
		m.MinimockDeleteByGroupIDsAndRoleIDDone() &&
		m.MinimockDeleteByRoleIDsDone() &&
		m.MinimockDeleteByRoleIDsAndGroupIDDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByGroupIDDone() &&
		m.MinimockGetByGroupRoleIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByRoleIDDone()
}
