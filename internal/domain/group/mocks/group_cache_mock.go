// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository.GroupCache -o group_cache_mock.go -n GroupCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	"github.com/gojuno/minimock/v3"
)

// GroupCacheMock implements mm_repository.GroupCache
type GroupCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcDeleteGroup          func(ctx context.Context, id int64) (err error)
	funcDeleteGroupOrigin    string
	inspectFuncDeleteGroup   func(ctx context.Context, id int64)
	afterDeleteGroupCounter  uint64
	beforeDeleteGroupCounter uint64
	DeleteGroupMock          mGroupCacheMockDeleteGroup

	funcDeleteGroups          func(ctx context.Context, ids []int64) (err error)
	funcDeleteGroupsOrigin    string
	inspectFuncDeleteGroups   func(ctx context.Context, ids []int64)
	afterDeleteGroupsCounter  uint64
	beforeDeleteGroupsCounter uint64
	DeleteGroupsMock          mGroupCacheMockDeleteGroups

	funcGetGroup          func(ctx context.Context, id int64) (g1 groupentity.Group, err error)
	funcGetGroupOrigin    string
	inspectFuncGetGroup   func(ctx context.Context, id int64)
	afterGetGroupCounter  uint64
	beforeGetGroupCounter uint64
	GetGroupMock          mGroupCacheMockGetGroup

	funcGetGroups          func(ctx context.Context) (ga1 []groupentity.Group, err error)
	funcGetGroupsOrigin    string
	inspectFuncGetGroups   func(ctx context.Context)
	afterGetGroupsCounter  uint64
	beforeGetGroupsCounter uint64
	GetGroupsMock          mGroupCacheMockGetGroups

	funcGetProductGroups          func(ctx context.Context, productID int64) (ga1 []groupentity.Group, err error)
	funcGetProductGroupsOrigin    string
	inspectFuncGetProductGroups   func(ctx context.Context, productID int64)
	afterGetProductGroupsCounter  uint64
	beforeGetProductGroupsCounter uint64
	GetProductGroupsMock          mGroupCacheMockGetProductGroups

	funcSetGroup          func(ctx context.Context, group groupentity.Group) (err error)
	funcSetGroupOrigin    string
	inspectFuncSetGroup   func(ctx context.Context, group groupentity.Group)
	afterSetGroupCounter  uint64
	beforeSetGroupCounter uint64
	SetGroupMock          mGroupCacheMockSetGroup

	funcSetGroups          func(ctx context.Context, groups []groupentity.Group) (err error)
	funcSetGroupsOrigin    string
	inspectFuncSetGroups   func(ctx context.Context, groups []groupentity.Group)
	afterSetGroupsCounter  uint64
	beforeSetGroupsCounter uint64
	SetGroupsMock          mGroupCacheMockSetGroups

	funcSetProductGroups          func(ctx context.Context, productID int64, groups []groupentity.Group) (err error)
	funcSetProductGroupsOrigin    string
	inspectFuncSetProductGroups   func(ctx context.Context, productID int64, groups []groupentity.Group)
	afterSetProductGroupsCounter  uint64
	beforeSetProductGroupsCounter uint64
	SetProductGroupsMock          mGroupCacheMockSetProductGroups
}

// NewGroupCacheMock returns a mock for mm_repository.GroupCache
func NewGroupCacheMock(t minimock.Tester) *GroupCacheMock {
	m := &GroupCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.DeleteGroupMock = mGroupCacheMockDeleteGroup{mock: m}
	m.DeleteGroupMock.callArgs = []*GroupCacheMockDeleteGroupParams{}

	m.DeleteGroupsMock = mGroupCacheMockDeleteGroups{mock: m}
	m.DeleteGroupsMock.callArgs = []*GroupCacheMockDeleteGroupsParams{}

	m.GetGroupMock = mGroupCacheMockGetGroup{mock: m}
	m.GetGroupMock.callArgs = []*GroupCacheMockGetGroupParams{}

	m.GetGroupsMock = mGroupCacheMockGetGroups{mock: m}
	m.GetGroupsMock.callArgs = []*GroupCacheMockGetGroupsParams{}

	m.GetProductGroupsMock = mGroupCacheMockGetProductGroups{mock: m}
	m.GetProductGroupsMock.callArgs = []*GroupCacheMockGetProductGroupsParams{}

	m.SetGroupMock = mGroupCacheMockSetGroup{mock: m}
	m.SetGroupMock.callArgs = []*GroupCacheMockSetGroupParams{}

	m.SetGroupsMock = mGroupCacheMockSetGroups{mock: m}
	m.SetGroupsMock.callArgs = []*GroupCacheMockSetGroupsParams{}

	m.SetProductGroupsMock = mGroupCacheMockSetProductGroups{mock: m}
	m.SetProductGroupsMock.callArgs = []*GroupCacheMockSetProductGroupsParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mGroupCacheMockDeleteGroup struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockDeleteGroupExpectation
	expectations       []*GroupCacheMockDeleteGroupExpectation

	callArgs []*GroupCacheMockDeleteGroupParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockDeleteGroupExpectation specifies expectation struct of the GroupCache.DeleteGroup
type GroupCacheMockDeleteGroupExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockDeleteGroupParams
	paramPtrs          *GroupCacheMockDeleteGroupParamPtrs
	expectationOrigins GroupCacheMockDeleteGroupExpectationOrigins
	results            *GroupCacheMockDeleteGroupResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockDeleteGroupParams contains parameters of the GroupCache.DeleteGroup
type GroupCacheMockDeleteGroupParams struct {
	ctx context.Context
	id  int64
}

// GroupCacheMockDeleteGroupParamPtrs contains pointers to parameters of the GroupCache.DeleteGroup
type GroupCacheMockDeleteGroupParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// GroupCacheMockDeleteGroupResults contains results of the GroupCache.DeleteGroup
type GroupCacheMockDeleteGroupResults struct {
	err error
}

// GroupCacheMockDeleteGroupOrigins contains origins of expectations of the GroupCache.DeleteGroup
type GroupCacheMockDeleteGroupExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Optional() *mGroupCacheMockDeleteGroup {
	mmDeleteGroup.optional = true
	return mmDeleteGroup
}

// Expect sets up expected params for GroupCache.DeleteGroup
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Expect(ctx context.Context, id int64) *mGroupCacheMockDeleteGroup {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupCacheMockDeleteGroupExpectation{}
	}

	if mmDeleteGroup.defaultExpectation.paramPtrs != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by ExpectParams functions")
	}

	mmDeleteGroup.defaultExpectation.params = &GroupCacheMockDeleteGroupParams{ctx, id}
	mmDeleteGroup.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteGroup.expectations {
		if minimock.Equal(e.params, mmDeleteGroup.defaultExpectation.params) {
			mmDeleteGroup.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteGroup.defaultExpectation.params)
		}
	}

	return mmDeleteGroup
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.DeleteGroup
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockDeleteGroup {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupCacheMockDeleteGroupExpectation{}
	}

	if mmDeleteGroup.defaultExpectation.params != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Expect")
	}

	if mmDeleteGroup.defaultExpectation.paramPtrs == nil {
		mmDeleteGroup.defaultExpectation.paramPtrs = &GroupCacheMockDeleteGroupParamPtrs{}
	}
	mmDeleteGroup.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteGroup.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteGroup
}

// ExpectIdParam2 sets up expected param id for GroupCache.DeleteGroup
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) ExpectIdParam2(id int64) *mGroupCacheMockDeleteGroup {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupCacheMockDeleteGroupExpectation{}
	}

	if mmDeleteGroup.defaultExpectation.params != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Expect")
	}

	if mmDeleteGroup.defaultExpectation.paramPtrs == nil {
		mmDeleteGroup.defaultExpectation.paramPtrs = &GroupCacheMockDeleteGroupParamPtrs{}
	}
	mmDeleteGroup.defaultExpectation.paramPtrs.id = &id
	mmDeleteGroup.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDeleteGroup
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.DeleteGroup
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Inspect(f func(ctx context.Context, id int64)) *mGroupCacheMockDeleteGroup {
	if mmDeleteGroup.mock.inspectFuncDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.DeleteGroup")
	}

	mmDeleteGroup.mock.inspectFuncDeleteGroup = f

	return mmDeleteGroup
}

// Return sets up results that will be returned by GroupCache.DeleteGroup
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Return(err error) *GroupCacheMock {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupCacheMockDeleteGroupExpectation{mock: mmDeleteGroup.mock}
	}
	mmDeleteGroup.defaultExpectation.results = &GroupCacheMockDeleteGroupResults{err}
	mmDeleteGroup.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteGroup.mock
}

// Set uses given function f to mock the GroupCache.DeleteGroup method
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Set(f func(ctx context.Context, id int64) (err error)) *GroupCacheMock {
	if mmDeleteGroup.defaultExpectation != nil {
		mmDeleteGroup.mock.t.Fatalf("Default expectation is already set for the GroupCache.DeleteGroup method")
	}

	if len(mmDeleteGroup.expectations) > 0 {
		mmDeleteGroup.mock.t.Fatalf("Some expectations are already set for the GroupCache.DeleteGroup method")
	}

	mmDeleteGroup.mock.funcDeleteGroup = f
	mmDeleteGroup.mock.funcDeleteGroupOrigin = minimock.CallerInfo(1)
	return mmDeleteGroup.mock
}

// When sets expectation for the GroupCache.DeleteGroup which will trigger the result defined by the following
// Then helper
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) When(ctx context.Context, id int64) *GroupCacheMockDeleteGroupExpectation {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupCacheMock.DeleteGroup mock is already set by Set")
	}

	expectation := &GroupCacheMockDeleteGroupExpectation{
		mock:               mmDeleteGroup.mock,
		params:             &GroupCacheMockDeleteGroupParams{ctx, id},
		expectationOrigins: GroupCacheMockDeleteGroupExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteGroup.expectations = append(mmDeleteGroup.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.DeleteGroup return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockDeleteGroupExpectation) Then(err error) *GroupCacheMock {
	e.results = &GroupCacheMockDeleteGroupResults{err}
	return e.mock
}

// Times sets number of times GroupCache.DeleteGroup should be invoked
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Times(n uint64) *mGroupCacheMockDeleteGroup {
	if n == 0 {
		mmDeleteGroup.mock.t.Fatalf("Times of GroupCacheMock.DeleteGroup mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteGroup.expectedInvocations, n)
	mmDeleteGroup.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteGroup
}

func (mmDeleteGroup *mGroupCacheMockDeleteGroup) invocationsDone() bool {
	if len(mmDeleteGroup.expectations) == 0 && mmDeleteGroup.defaultExpectation == nil && mmDeleteGroup.mock.funcDeleteGroup == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteGroup.mock.afterDeleteGroupCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteGroup.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteGroup implements mm_repository.GroupCache
func (mmDeleteGroup *GroupCacheMock) DeleteGroup(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteGroup.beforeDeleteGroupCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteGroup.afterDeleteGroupCounter, 1)

	mmDeleteGroup.t.Helper()

	if mmDeleteGroup.inspectFuncDeleteGroup != nil {
		mmDeleteGroup.inspectFuncDeleteGroup(ctx, id)
	}

	mm_params := GroupCacheMockDeleteGroupParams{ctx, id}

	// Record call args
	mmDeleteGroup.DeleteGroupMock.mutex.Lock()
	mmDeleteGroup.DeleteGroupMock.callArgs = append(mmDeleteGroup.DeleteGroupMock.callArgs, &mm_params)
	mmDeleteGroup.DeleteGroupMock.mutex.Unlock()

	for _, e := range mmDeleteGroup.DeleteGroupMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteGroup.DeleteGroupMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteGroup.DeleteGroupMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteGroup.DeleteGroupMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteGroup.DeleteGroupMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockDeleteGroupParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteGroup.t.Errorf("GroupCacheMock.DeleteGroup got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteGroup.DeleteGroupMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDeleteGroup.t.Errorf("GroupCacheMock.DeleteGroup got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteGroup.DeleteGroupMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteGroup.t.Errorf("GroupCacheMock.DeleteGroup got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteGroup.DeleteGroupMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteGroup.DeleteGroupMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteGroup.t.Fatal("No results are set for the GroupCacheMock.DeleteGroup")
		}
		return (*mm_results).err
	}
	if mmDeleteGroup.funcDeleteGroup != nil {
		return mmDeleteGroup.funcDeleteGroup(ctx, id)
	}
	mmDeleteGroup.t.Fatalf("Unexpected call to GroupCacheMock.DeleteGroup. %v %v", ctx, id)
	return
}

// DeleteGroupAfterCounter returns a count of finished GroupCacheMock.DeleteGroup invocations
func (mmDeleteGroup *GroupCacheMock) DeleteGroupAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteGroup.afterDeleteGroupCounter)
}

// DeleteGroupBeforeCounter returns a count of GroupCacheMock.DeleteGroup invocations
func (mmDeleteGroup *GroupCacheMock) DeleteGroupBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteGroup.beforeDeleteGroupCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.DeleteGroup.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteGroup *mGroupCacheMockDeleteGroup) Calls() []*GroupCacheMockDeleteGroupParams {
	mmDeleteGroup.mutex.RLock()

	argCopy := make([]*GroupCacheMockDeleteGroupParams, len(mmDeleteGroup.callArgs))
	copy(argCopy, mmDeleteGroup.callArgs)

	mmDeleteGroup.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteGroupDone returns true if the count of the DeleteGroup invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockDeleteGroupDone() bool {
	if m.DeleteGroupMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteGroupMock.invocationsDone()
}

// MinimockDeleteGroupInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockDeleteGroupInspect() {
	for _, e := range m.DeleteGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.DeleteGroup at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteGroupCounter := mm_atomic.LoadUint64(&m.afterDeleteGroupCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteGroupMock.defaultExpectation != nil && afterDeleteGroupCounter < 1 {
		if m.DeleteGroupMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.DeleteGroup at\n%s", m.DeleteGroupMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.DeleteGroup at\n%s with params: %#v", m.DeleteGroupMock.defaultExpectation.expectationOrigins.origin, *m.DeleteGroupMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteGroup != nil && afterDeleteGroupCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.DeleteGroup at\n%s", m.funcDeleteGroupOrigin)
	}

	if !m.DeleteGroupMock.invocationsDone() && afterDeleteGroupCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.DeleteGroup at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteGroupMock.expectedInvocations), m.DeleteGroupMock.expectedInvocationsOrigin, afterDeleteGroupCounter)
	}
}

type mGroupCacheMockDeleteGroups struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockDeleteGroupsExpectation
	expectations       []*GroupCacheMockDeleteGroupsExpectation

	callArgs []*GroupCacheMockDeleteGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockDeleteGroupsExpectation specifies expectation struct of the GroupCache.DeleteGroups
type GroupCacheMockDeleteGroupsExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockDeleteGroupsParams
	paramPtrs          *GroupCacheMockDeleteGroupsParamPtrs
	expectationOrigins GroupCacheMockDeleteGroupsExpectationOrigins
	results            *GroupCacheMockDeleteGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockDeleteGroupsParams contains parameters of the GroupCache.DeleteGroups
type GroupCacheMockDeleteGroupsParams struct {
	ctx context.Context
	ids []int64
}

// GroupCacheMockDeleteGroupsParamPtrs contains pointers to parameters of the GroupCache.DeleteGroups
type GroupCacheMockDeleteGroupsParamPtrs struct {
	ctx *context.Context
	ids *[]int64
}

// GroupCacheMockDeleteGroupsResults contains results of the GroupCache.DeleteGroups
type GroupCacheMockDeleteGroupsResults struct {
	err error
}

// GroupCacheMockDeleteGroupsOrigins contains origins of expectations of the GroupCache.DeleteGroups
type GroupCacheMockDeleteGroupsExpectationOrigins struct {
	origin    string
	originCtx string
	originIds string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Optional() *mGroupCacheMockDeleteGroups {
	mmDeleteGroups.optional = true
	return mmDeleteGroups
}

// Expect sets up expected params for GroupCache.DeleteGroups
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Expect(ctx context.Context, ids []int64) *mGroupCacheMockDeleteGroups {
	if mmDeleteGroups.mock.funcDeleteGroups != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Set")
	}

	if mmDeleteGroups.defaultExpectation == nil {
		mmDeleteGroups.defaultExpectation = &GroupCacheMockDeleteGroupsExpectation{}
	}

	if mmDeleteGroups.defaultExpectation.paramPtrs != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by ExpectParams functions")
	}

	mmDeleteGroups.defaultExpectation.params = &GroupCacheMockDeleteGroupsParams{ctx, ids}
	mmDeleteGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteGroups.expectations {
		if minimock.Equal(e.params, mmDeleteGroups.defaultExpectation.params) {
			mmDeleteGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteGroups.defaultExpectation.params)
		}
	}

	return mmDeleteGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.DeleteGroups
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockDeleteGroups {
	if mmDeleteGroups.mock.funcDeleteGroups != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Set")
	}

	if mmDeleteGroups.defaultExpectation == nil {
		mmDeleteGroups.defaultExpectation = &GroupCacheMockDeleteGroupsExpectation{}
	}

	if mmDeleteGroups.defaultExpectation.params != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Expect")
	}

	if mmDeleteGroups.defaultExpectation.paramPtrs == nil {
		mmDeleteGroups.defaultExpectation.paramPtrs = &GroupCacheMockDeleteGroupsParamPtrs{}
	}
	mmDeleteGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteGroups
}

// ExpectIdsParam2 sets up expected param ids for GroupCache.DeleteGroups
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) ExpectIdsParam2(ids []int64) *mGroupCacheMockDeleteGroups {
	if mmDeleteGroups.mock.funcDeleteGroups != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Set")
	}

	if mmDeleteGroups.defaultExpectation == nil {
		mmDeleteGroups.defaultExpectation = &GroupCacheMockDeleteGroupsExpectation{}
	}

	if mmDeleteGroups.defaultExpectation.params != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Expect")
	}

	if mmDeleteGroups.defaultExpectation.paramPtrs == nil {
		mmDeleteGroups.defaultExpectation.paramPtrs = &GroupCacheMockDeleteGroupsParamPtrs{}
	}
	mmDeleteGroups.defaultExpectation.paramPtrs.ids = &ids
	mmDeleteGroups.defaultExpectation.expectationOrigins.originIds = minimock.CallerInfo(1)

	return mmDeleteGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.DeleteGroups
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Inspect(f func(ctx context.Context, ids []int64)) *mGroupCacheMockDeleteGroups {
	if mmDeleteGroups.mock.inspectFuncDeleteGroups != nil {
		mmDeleteGroups.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.DeleteGroups")
	}

	mmDeleteGroups.mock.inspectFuncDeleteGroups = f

	return mmDeleteGroups
}

// Return sets up results that will be returned by GroupCache.DeleteGroups
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Return(err error) *GroupCacheMock {
	if mmDeleteGroups.mock.funcDeleteGroups != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Set")
	}

	if mmDeleteGroups.defaultExpectation == nil {
		mmDeleteGroups.defaultExpectation = &GroupCacheMockDeleteGroupsExpectation{mock: mmDeleteGroups.mock}
	}
	mmDeleteGroups.defaultExpectation.results = &GroupCacheMockDeleteGroupsResults{err}
	mmDeleteGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteGroups.mock
}

// Set uses given function f to mock the GroupCache.DeleteGroups method
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Set(f func(ctx context.Context, ids []int64) (err error)) *GroupCacheMock {
	if mmDeleteGroups.defaultExpectation != nil {
		mmDeleteGroups.mock.t.Fatalf("Default expectation is already set for the GroupCache.DeleteGroups method")
	}

	if len(mmDeleteGroups.expectations) > 0 {
		mmDeleteGroups.mock.t.Fatalf("Some expectations are already set for the GroupCache.DeleteGroups method")
	}

	mmDeleteGroups.mock.funcDeleteGroups = f
	mmDeleteGroups.mock.funcDeleteGroupsOrigin = minimock.CallerInfo(1)
	return mmDeleteGroups.mock
}

// When sets expectation for the GroupCache.DeleteGroups which will trigger the result defined by the following
// Then helper
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) When(ctx context.Context, ids []int64) *GroupCacheMockDeleteGroupsExpectation {
	if mmDeleteGroups.mock.funcDeleteGroups != nil {
		mmDeleteGroups.mock.t.Fatalf("GroupCacheMock.DeleteGroups mock is already set by Set")
	}

	expectation := &GroupCacheMockDeleteGroupsExpectation{
		mock:               mmDeleteGroups.mock,
		params:             &GroupCacheMockDeleteGroupsParams{ctx, ids},
		expectationOrigins: GroupCacheMockDeleteGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteGroups.expectations = append(mmDeleteGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.DeleteGroups return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockDeleteGroupsExpectation) Then(err error) *GroupCacheMock {
	e.results = &GroupCacheMockDeleteGroupsResults{err}
	return e.mock
}

// Times sets number of times GroupCache.DeleteGroups should be invoked
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Times(n uint64) *mGroupCacheMockDeleteGroups {
	if n == 0 {
		mmDeleteGroups.mock.t.Fatalf("Times of GroupCacheMock.DeleteGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteGroups.expectedInvocations, n)
	mmDeleteGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteGroups
}

func (mmDeleteGroups *mGroupCacheMockDeleteGroups) invocationsDone() bool {
	if len(mmDeleteGroups.expectations) == 0 && mmDeleteGroups.defaultExpectation == nil && mmDeleteGroups.mock.funcDeleteGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteGroups.mock.afterDeleteGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteGroups implements mm_repository.GroupCache
func (mmDeleteGroups *GroupCacheMock) DeleteGroups(ctx context.Context, ids []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteGroups.beforeDeleteGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteGroups.afterDeleteGroupsCounter, 1)

	mmDeleteGroups.t.Helper()

	if mmDeleteGroups.inspectFuncDeleteGroups != nil {
		mmDeleteGroups.inspectFuncDeleteGroups(ctx, ids)
	}

	mm_params := GroupCacheMockDeleteGroupsParams{ctx, ids}

	// Record call args
	mmDeleteGroups.DeleteGroupsMock.mutex.Lock()
	mmDeleteGroups.DeleteGroupsMock.callArgs = append(mmDeleteGroups.DeleteGroupsMock.callArgs, &mm_params)
	mmDeleteGroups.DeleteGroupsMock.mutex.Unlock()

	for _, e := range mmDeleteGroups.DeleteGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteGroups.DeleteGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteGroups.DeleteGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteGroups.DeleteGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteGroups.DeleteGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockDeleteGroupsParams{ctx, ids}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteGroups.t.Errorf("GroupCacheMock.DeleteGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteGroups.DeleteGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.ids != nil && !minimock.Equal(*mm_want_ptrs.ids, mm_got.ids) {
				mmDeleteGroups.t.Errorf("GroupCacheMock.DeleteGroups got unexpected parameter ids, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteGroups.DeleteGroupsMock.defaultExpectation.expectationOrigins.originIds, *mm_want_ptrs.ids, mm_got.ids, minimock.Diff(*mm_want_ptrs.ids, mm_got.ids))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteGroups.t.Errorf("GroupCacheMock.DeleteGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteGroups.DeleteGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteGroups.DeleteGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteGroups.t.Fatal("No results are set for the GroupCacheMock.DeleteGroups")
		}
		return (*mm_results).err
	}
	if mmDeleteGroups.funcDeleteGroups != nil {
		return mmDeleteGroups.funcDeleteGroups(ctx, ids)
	}
	mmDeleteGroups.t.Fatalf("Unexpected call to GroupCacheMock.DeleteGroups. %v %v", ctx, ids)
	return
}

// DeleteGroupsAfterCounter returns a count of finished GroupCacheMock.DeleteGroups invocations
func (mmDeleteGroups *GroupCacheMock) DeleteGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteGroups.afterDeleteGroupsCounter)
}

// DeleteGroupsBeforeCounter returns a count of GroupCacheMock.DeleteGroups invocations
func (mmDeleteGroups *GroupCacheMock) DeleteGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteGroups.beforeDeleteGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.DeleteGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteGroups *mGroupCacheMockDeleteGroups) Calls() []*GroupCacheMockDeleteGroupsParams {
	mmDeleteGroups.mutex.RLock()

	argCopy := make([]*GroupCacheMockDeleteGroupsParams, len(mmDeleteGroups.callArgs))
	copy(argCopy, mmDeleteGroups.callArgs)

	mmDeleteGroups.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteGroupsDone returns true if the count of the DeleteGroups invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockDeleteGroupsDone() bool {
	if m.DeleteGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteGroupsMock.invocationsDone()
}

// MinimockDeleteGroupsInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockDeleteGroupsInspect() {
	for _, e := range m.DeleteGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.DeleteGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteGroupsCounter := mm_atomic.LoadUint64(&m.afterDeleteGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteGroupsMock.defaultExpectation != nil && afterDeleteGroupsCounter < 1 {
		if m.DeleteGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.DeleteGroups at\n%s", m.DeleteGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.DeleteGroups at\n%s with params: %#v", m.DeleteGroupsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteGroups != nil && afterDeleteGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.DeleteGroups at\n%s", m.funcDeleteGroupsOrigin)
	}

	if !m.DeleteGroupsMock.invocationsDone() && afterDeleteGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.DeleteGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteGroupsMock.expectedInvocations), m.DeleteGroupsMock.expectedInvocationsOrigin, afterDeleteGroupsCounter)
	}
}

type mGroupCacheMockGetGroup struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockGetGroupExpectation
	expectations       []*GroupCacheMockGetGroupExpectation

	callArgs []*GroupCacheMockGetGroupParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockGetGroupExpectation specifies expectation struct of the GroupCache.GetGroup
type GroupCacheMockGetGroupExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockGetGroupParams
	paramPtrs          *GroupCacheMockGetGroupParamPtrs
	expectationOrigins GroupCacheMockGetGroupExpectationOrigins
	results            *GroupCacheMockGetGroupResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockGetGroupParams contains parameters of the GroupCache.GetGroup
type GroupCacheMockGetGroupParams struct {
	ctx context.Context
	id  int64
}

// GroupCacheMockGetGroupParamPtrs contains pointers to parameters of the GroupCache.GetGroup
type GroupCacheMockGetGroupParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// GroupCacheMockGetGroupResults contains results of the GroupCache.GetGroup
type GroupCacheMockGetGroupResults struct {
	g1  groupentity.Group
	err error
}

// GroupCacheMockGetGroupOrigins contains origins of expectations of the GroupCache.GetGroup
type GroupCacheMockGetGroupExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetGroup *mGroupCacheMockGetGroup) Optional() *mGroupCacheMockGetGroup {
	mmGetGroup.optional = true
	return mmGetGroup
}

// Expect sets up expected params for GroupCache.GetGroup
func (mmGetGroup *mGroupCacheMockGetGroup) Expect(ctx context.Context, id int64) *mGroupCacheMockGetGroup {
	if mmGetGroup.mock.funcGetGroup != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Set")
	}

	if mmGetGroup.defaultExpectation == nil {
		mmGetGroup.defaultExpectation = &GroupCacheMockGetGroupExpectation{}
	}

	if mmGetGroup.defaultExpectation.paramPtrs != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by ExpectParams functions")
	}

	mmGetGroup.defaultExpectation.params = &GroupCacheMockGetGroupParams{ctx, id}
	mmGetGroup.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetGroup.expectations {
		if minimock.Equal(e.params, mmGetGroup.defaultExpectation.params) {
			mmGetGroup.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetGroup.defaultExpectation.params)
		}
	}

	return mmGetGroup
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.GetGroup
func (mmGetGroup *mGroupCacheMockGetGroup) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockGetGroup {
	if mmGetGroup.mock.funcGetGroup != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Set")
	}

	if mmGetGroup.defaultExpectation == nil {
		mmGetGroup.defaultExpectation = &GroupCacheMockGetGroupExpectation{}
	}

	if mmGetGroup.defaultExpectation.params != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Expect")
	}

	if mmGetGroup.defaultExpectation.paramPtrs == nil {
		mmGetGroup.defaultExpectation.paramPtrs = &GroupCacheMockGetGroupParamPtrs{}
	}
	mmGetGroup.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetGroup.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetGroup
}

// ExpectIdParam2 sets up expected param id for GroupCache.GetGroup
func (mmGetGroup *mGroupCacheMockGetGroup) ExpectIdParam2(id int64) *mGroupCacheMockGetGroup {
	if mmGetGroup.mock.funcGetGroup != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Set")
	}

	if mmGetGroup.defaultExpectation == nil {
		mmGetGroup.defaultExpectation = &GroupCacheMockGetGroupExpectation{}
	}

	if mmGetGroup.defaultExpectation.params != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Expect")
	}

	if mmGetGroup.defaultExpectation.paramPtrs == nil {
		mmGetGroup.defaultExpectation.paramPtrs = &GroupCacheMockGetGroupParamPtrs{}
	}
	mmGetGroup.defaultExpectation.paramPtrs.id = &id
	mmGetGroup.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetGroup
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.GetGroup
func (mmGetGroup *mGroupCacheMockGetGroup) Inspect(f func(ctx context.Context, id int64)) *mGroupCacheMockGetGroup {
	if mmGetGroup.mock.inspectFuncGetGroup != nil {
		mmGetGroup.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.GetGroup")
	}

	mmGetGroup.mock.inspectFuncGetGroup = f

	return mmGetGroup
}

// Return sets up results that will be returned by GroupCache.GetGroup
func (mmGetGroup *mGroupCacheMockGetGroup) Return(g1 groupentity.Group, err error) *GroupCacheMock {
	if mmGetGroup.mock.funcGetGroup != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Set")
	}

	if mmGetGroup.defaultExpectation == nil {
		mmGetGroup.defaultExpectation = &GroupCacheMockGetGroupExpectation{mock: mmGetGroup.mock}
	}
	mmGetGroup.defaultExpectation.results = &GroupCacheMockGetGroupResults{g1, err}
	mmGetGroup.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetGroup.mock
}

// Set uses given function f to mock the GroupCache.GetGroup method
func (mmGetGroup *mGroupCacheMockGetGroup) Set(f func(ctx context.Context, id int64) (g1 groupentity.Group, err error)) *GroupCacheMock {
	if mmGetGroup.defaultExpectation != nil {
		mmGetGroup.mock.t.Fatalf("Default expectation is already set for the GroupCache.GetGroup method")
	}

	if len(mmGetGroup.expectations) > 0 {
		mmGetGroup.mock.t.Fatalf("Some expectations are already set for the GroupCache.GetGroup method")
	}

	mmGetGroup.mock.funcGetGroup = f
	mmGetGroup.mock.funcGetGroupOrigin = minimock.CallerInfo(1)
	return mmGetGroup.mock
}

// When sets expectation for the GroupCache.GetGroup which will trigger the result defined by the following
// Then helper
func (mmGetGroup *mGroupCacheMockGetGroup) When(ctx context.Context, id int64) *GroupCacheMockGetGroupExpectation {
	if mmGetGroup.mock.funcGetGroup != nil {
		mmGetGroup.mock.t.Fatalf("GroupCacheMock.GetGroup mock is already set by Set")
	}

	expectation := &GroupCacheMockGetGroupExpectation{
		mock:               mmGetGroup.mock,
		params:             &GroupCacheMockGetGroupParams{ctx, id},
		expectationOrigins: GroupCacheMockGetGroupExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetGroup.expectations = append(mmGetGroup.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.GetGroup return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockGetGroupExpectation) Then(g1 groupentity.Group, err error) *GroupCacheMock {
	e.results = &GroupCacheMockGetGroupResults{g1, err}
	return e.mock
}

// Times sets number of times GroupCache.GetGroup should be invoked
func (mmGetGroup *mGroupCacheMockGetGroup) Times(n uint64) *mGroupCacheMockGetGroup {
	if n == 0 {
		mmGetGroup.mock.t.Fatalf("Times of GroupCacheMock.GetGroup mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetGroup.expectedInvocations, n)
	mmGetGroup.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetGroup
}

func (mmGetGroup *mGroupCacheMockGetGroup) invocationsDone() bool {
	if len(mmGetGroup.expectations) == 0 && mmGetGroup.defaultExpectation == nil && mmGetGroup.mock.funcGetGroup == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetGroup.mock.afterGetGroupCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetGroup.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetGroup implements mm_repository.GroupCache
func (mmGetGroup *GroupCacheMock) GetGroup(ctx context.Context, id int64) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetGroup.beforeGetGroupCounter, 1)
	defer mm_atomic.AddUint64(&mmGetGroup.afterGetGroupCounter, 1)

	mmGetGroup.t.Helper()

	if mmGetGroup.inspectFuncGetGroup != nil {
		mmGetGroup.inspectFuncGetGroup(ctx, id)
	}

	mm_params := GroupCacheMockGetGroupParams{ctx, id}

	// Record call args
	mmGetGroup.GetGroupMock.mutex.Lock()
	mmGetGroup.GetGroupMock.callArgs = append(mmGetGroup.GetGroupMock.callArgs, &mm_params)
	mmGetGroup.GetGroupMock.mutex.Unlock()

	for _, e := range mmGetGroup.GetGroupMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetGroup.GetGroupMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetGroup.GetGroupMock.defaultExpectation.Counter, 1)
		mm_want := mmGetGroup.GetGroupMock.defaultExpectation.params
		mm_want_ptrs := mmGetGroup.GetGroupMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockGetGroupParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetGroup.t.Errorf("GroupCacheMock.GetGroup got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetGroup.GetGroupMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetGroup.t.Errorf("GroupCacheMock.GetGroup got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetGroup.GetGroupMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetGroup.t.Errorf("GroupCacheMock.GetGroup got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetGroup.GetGroupMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetGroup.GetGroupMock.defaultExpectation.results
		if mm_results == nil {
			mmGetGroup.t.Fatal("No results are set for the GroupCacheMock.GetGroup")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetGroup.funcGetGroup != nil {
		return mmGetGroup.funcGetGroup(ctx, id)
	}
	mmGetGroup.t.Fatalf("Unexpected call to GroupCacheMock.GetGroup. %v %v", ctx, id)
	return
}

// GetGroupAfterCounter returns a count of finished GroupCacheMock.GetGroup invocations
func (mmGetGroup *GroupCacheMock) GetGroupAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroup.afterGetGroupCounter)
}

// GetGroupBeforeCounter returns a count of GroupCacheMock.GetGroup invocations
func (mmGetGroup *GroupCacheMock) GetGroupBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroup.beforeGetGroupCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.GetGroup.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetGroup *mGroupCacheMockGetGroup) Calls() []*GroupCacheMockGetGroupParams {
	mmGetGroup.mutex.RLock()

	argCopy := make([]*GroupCacheMockGetGroupParams, len(mmGetGroup.callArgs))
	copy(argCopy, mmGetGroup.callArgs)

	mmGetGroup.mutex.RUnlock()

	return argCopy
}

// MinimockGetGroupDone returns true if the count of the GetGroup invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockGetGroupDone() bool {
	if m.GetGroupMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetGroupMock.invocationsDone()
}

// MinimockGetGroupInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockGetGroupInspect() {
	for _, e := range m.GetGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.GetGroup at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetGroupCounter := mm_atomic.LoadUint64(&m.afterGetGroupCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetGroupMock.defaultExpectation != nil && afterGetGroupCounter < 1 {
		if m.GetGroupMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.GetGroup at\n%s", m.GetGroupMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.GetGroup at\n%s with params: %#v", m.GetGroupMock.defaultExpectation.expectationOrigins.origin, *m.GetGroupMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetGroup != nil && afterGetGroupCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.GetGroup at\n%s", m.funcGetGroupOrigin)
	}

	if !m.GetGroupMock.invocationsDone() && afterGetGroupCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.GetGroup at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetGroupMock.expectedInvocations), m.GetGroupMock.expectedInvocationsOrigin, afterGetGroupCounter)
	}
}

type mGroupCacheMockGetGroups struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockGetGroupsExpectation
	expectations       []*GroupCacheMockGetGroupsExpectation

	callArgs []*GroupCacheMockGetGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockGetGroupsExpectation specifies expectation struct of the GroupCache.GetGroups
type GroupCacheMockGetGroupsExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockGetGroupsParams
	paramPtrs          *GroupCacheMockGetGroupsParamPtrs
	expectationOrigins GroupCacheMockGetGroupsExpectationOrigins
	results            *GroupCacheMockGetGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockGetGroupsParams contains parameters of the GroupCache.GetGroups
type GroupCacheMockGetGroupsParams struct {
	ctx context.Context
}

// GroupCacheMockGetGroupsParamPtrs contains pointers to parameters of the GroupCache.GetGroups
type GroupCacheMockGetGroupsParamPtrs struct {
	ctx *context.Context
}

// GroupCacheMockGetGroupsResults contains results of the GroupCache.GetGroups
type GroupCacheMockGetGroupsResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupCacheMockGetGroupsOrigins contains origins of expectations of the GroupCache.GetGroups
type GroupCacheMockGetGroupsExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetGroups *mGroupCacheMockGetGroups) Optional() *mGroupCacheMockGetGroups {
	mmGetGroups.optional = true
	return mmGetGroups
}

// Expect sets up expected params for GroupCache.GetGroups
func (mmGetGroups *mGroupCacheMockGetGroups) Expect(ctx context.Context) *mGroupCacheMockGetGroups {
	if mmGetGroups.mock.funcGetGroups != nil {
		mmGetGroups.mock.t.Fatalf("GroupCacheMock.GetGroups mock is already set by Set")
	}

	if mmGetGroups.defaultExpectation == nil {
		mmGetGroups.defaultExpectation = &GroupCacheMockGetGroupsExpectation{}
	}

	if mmGetGroups.defaultExpectation.paramPtrs != nil {
		mmGetGroups.mock.t.Fatalf("GroupCacheMock.GetGroups mock is already set by ExpectParams functions")
	}

	mmGetGroups.defaultExpectation.params = &GroupCacheMockGetGroupsParams{ctx}
	mmGetGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetGroups.expectations {
		if minimock.Equal(e.params, mmGetGroups.defaultExpectation.params) {
			mmGetGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetGroups.defaultExpectation.params)
		}
	}

	return mmGetGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.GetGroups
func (mmGetGroups *mGroupCacheMockGetGroups) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockGetGroups {
	if mmGetGroups.mock.funcGetGroups != nil {
		mmGetGroups.mock.t.Fatalf("GroupCacheMock.GetGroups mock is already set by Set")
	}

	if mmGetGroups.defaultExpectation == nil {
		mmGetGroups.defaultExpectation = &GroupCacheMockGetGroupsExpectation{}
	}

	if mmGetGroups.defaultExpectation.params != nil {
		mmGetGroups.mock.t.Fatalf("GroupCacheMock.GetGroups mock is already set by Expect")
	}

	if mmGetGroups.defaultExpectation.paramPtrs == nil {
		mmGetGroups.defaultExpectation.paramPtrs = &GroupCacheMockGetGroupsParamPtrs{}
	}
	mmGetGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.GetGroups
func (mmGetGroups *mGroupCacheMockGetGroups) Inspect(f func(ctx context.Context)) *mGroupCacheMockGetGroups {
	if mmGetGroups.mock.inspectFuncGetGroups != nil {
		mmGetGroups.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.GetGroups")
	}

	mmGetGroups.mock.inspectFuncGetGroups = f

	return mmGetGroups
}

// Return sets up results that will be returned by GroupCache.GetGroups
func (mmGetGroups *mGroupCacheMockGetGroups) Return(ga1 []groupentity.Group, err error) *GroupCacheMock {
	if mmGetGroups.mock.funcGetGroups != nil {
		mmGetGroups.mock.t.Fatalf("GroupCacheMock.GetGroups mock is already set by Set")
	}

	if mmGetGroups.defaultExpectation == nil {
		mmGetGroups.defaultExpectation = &GroupCacheMockGetGroupsExpectation{mock: mmGetGroups.mock}
	}
	mmGetGroups.defaultExpectation.results = &GroupCacheMockGetGroupsResults{ga1, err}
	mmGetGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetGroups.mock
}

// Set uses given function f to mock the GroupCache.GetGroups method
func (mmGetGroups *mGroupCacheMockGetGroups) Set(f func(ctx context.Context) (ga1 []groupentity.Group, err error)) *GroupCacheMock {
	if mmGetGroups.defaultExpectation != nil {
		mmGetGroups.mock.t.Fatalf("Default expectation is already set for the GroupCache.GetGroups method")
	}

	if len(mmGetGroups.expectations) > 0 {
		mmGetGroups.mock.t.Fatalf("Some expectations are already set for the GroupCache.GetGroups method")
	}

	mmGetGroups.mock.funcGetGroups = f
	mmGetGroups.mock.funcGetGroupsOrigin = minimock.CallerInfo(1)
	return mmGetGroups.mock
}

// When sets expectation for the GroupCache.GetGroups which will trigger the result defined by the following
// Then helper
func (mmGetGroups *mGroupCacheMockGetGroups) When(ctx context.Context) *GroupCacheMockGetGroupsExpectation {
	if mmGetGroups.mock.funcGetGroups != nil {
		mmGetGroups.mock.t.Fatalf("GroupCacheMock.GetGroups mock is already set by Set")
	}

	expectation := &GroupCacheMockGetGroupsExpectation{
		mock:               mmGetGroups.mock,
		params:             &GroupCacheMockGetGroupsParams{ctx},
		expectationOrigins: GroupCacheMockGetGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetGroups.expectations = append(mmGetGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.GetGroups return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockGetGroupsExpectation) Then(ga1 []groupentity.Group, err error) *GroupCacheMock {
	e.results = &GroupCacheMockGetGroupsResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupCache.GetGroups should be invoked
func (mmGetGroups *mGroupCacheMockGetGroups) Times(n uint64) *mGroupCacheMockGetGroups {
	if n == 0 {
		mmGetGroups.mock.t.Fatalf("Times of GroupCacheMock.GetGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetGroups.expectedInvocations, n)
	mmGetGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetGroups
}

func (mmGetGroups *mGroupCacheMockGetGroups) invocationsDone() bool {
	if len(mmGetGroups.expectations) == 0 && mmGetGroups.defaultExpectation == nil && mmGetGroups.mock.funcGetGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetGroups.mock.afterGetGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetGroups implements mm_repository.GroupCache
func (mmGetGroups *GroupCacheMock) GetGroups(ctx context.Context) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetGroups.beforeGetGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetGroups.afterGetGroupsCounter, 1)

	mmGetGroups.t.Helper()

	if mmGetGroups.inspectFuncGetGroups != nil {
		mmGetGroups.inspectFuncGetGroups(ctx)
	}

	mm_params := GroupCacheMockGetGroupsParams{ctx}

	// Record call args
	mmGetGroups.GetGroupsMock.mutex.Lock()
	mmGetGroups.GetGroupsMock.callArgs = append(mmGetGroups.GetGroupsMock.callArgs, &mm_params)
	mmGetGroups.GetGroupsMock.mutex.Unlock()

	for _, e := range mmGetGroups.GetGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetGroups.GetGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetGroups.GetGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetGroups.GetGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmGetGroups.GetGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockGetGroupsParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetGroups.t.Errorf("GroupCacheMock.GetGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetGroups.GetGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetGroups.t.Errorf("GroupCacheMock.GetGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetGroups.GetGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetGroups.GetGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetGroups.t.Fatal("No results are set for the GroupCacheMock.GetGroups")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetGroups.funcGetGroups != nil {
		return mmGetGroups.funcGetGroups(ctx)
	}
	mmGetGroups.t.Fatalf("Unexpected call to GroupCacheMock.GetGroups. %v", ctx)
	return
}

// GetGroupsAfterCounter returns a count of finished GroupCacheMock.GetGroups invocations
func (mmGetGroups *GroupCacheMock) GetGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroups.afterGetGroupsCounter)
}

// GetGroupsBeforeCounter returns a count of GroupCacheMock.GetGroups invocations
func (mmGetGroups *GroupCacheMock) GetGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroups.beforeGetGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.GetGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetGroups *mGroupCacheMockGetGroups) Calls() []*GroupCacheMockGetGroupsParams {
	mmGetGroups.mutex.RLock()

	argCopy := make([]*GroupCacheMockGetGroupsParams, len(mmGetGroups.callArgs))
	copy(argCopy, mmGetGroups.callArgs)

	mmGetGroups.mutex.RUnlock()

	return argCopy
}

// MinimockGetGroupsDone returns true if the count of the GetGroups invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockGetGroupsDone() bool {
	if m.GetGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetGroupsMock.invocationsDone()
}

// MinimockGetGroupsInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockGetGroupsInspect() {
	for _, e := range m.GetGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.GetGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetGroupsCounter := mm_atomic.LoadUint64(&m.afterGetGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetGroupsMock.defaultExpectation != nil && afterGetGroupsCounter < 1 {
		if m.GetGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.GetGroups at\n%s", m.GetGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.GetGroups at\n%s with params: %#v", m.GetGroupsMock.defaultExpectation.expectationOrigins.origin, *m.GetGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetGroups != nil && afterGetGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.GetGroups at\n%s", m.funcGetGroupsOrigin)
	}

	if !m.GetGroupsMock.invocationsDone() && afterGetGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.GetGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetGroupsMock.expectedInvocations), m.GetGroupsMock.expectedInvocationsOrigin, afterGetGroupsCounter)
	}
}

type mGroupCacheMockGetProductGroups struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockGetProductGroupsExpectation
	expectations       []*GroupCacheMockGetProductGroupsExpectation

	callArgs []*GroupCacheMockGetProductGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockGetProductGroupsExpectation specifies expectation struct of the GroupCache.GetProductGroups
type GroupCacheMockGetProductGroupsExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockGetProductGroupsParams
	paramPtrs          *GroupCacheMockGetProductGroupsParamPtrs
	expectationOrigins GroupCacheMockGetProductGroupsExpectationOrigins
	results            *GroupCacheMockGetProductGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockGetProductGroupsParams contains parameters of the GroupCache.GetProductGroups
type GroupCacheMockGetProductGroupsParams struct {
	ctx       context.Context
	productID int64
}

// GroupCacheMockGetProductGroupsParamPtrs contains pointers to parameters of the GroupCache.GetProductGroups
type GroupCacheMockGetProductGroupsParamPtrs struct {
	ctx       *context.Context
	productID *int64
}

// GroupCacheMockGetProductGroupsResults contains results of the GroupCache.GetProductGroups
type GroupCacheMockGetProductGroupsResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupCacheMockGetProductGroupsOrigins contains origins of expectations of the GroupCache.GetProductGroups
type GroupCacheMockGetProductGroupsExpectationOrigins struct {
	origin          string
	originCtx       string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Optional() *mGroupCacheMockGetProductGroups {
	mmGetProductGroups.optional = true
	return mmGetProductGroups
}

// Expect sets up expected params for GroupCache.GetProductGroups
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Expect(ctx context.Context, productID int64) *mGroupCacheMockGetProductGroups {
	if mmGetProductGroups.mock.funcGetProductGroups != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Set")
	}

	if mmGetProductGroups.defaultExpectation == nil {
		mmGetProductGroups.defaultExpectation = &GroupCacheMockGetProductGroupsExpectation{}
	}

	if mmGetProductGroups.defaultExpectation.paramPtrs != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by ExpectParams functions")
	}

	mmGetProductGroups.defaultExpectation.params = &GroupCacheMockGetProductGroupsParams{ctx, productID}
	mmGetProductGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetProductGroups.expectations {
		if minimock.Equal(e.params, mmGetProductGroups.defaultExpectation.params) {
			mmGetProductGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetProductGroups.defaultExpectation.params)
		}
	}

	return mmGetProductGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.GetProductGroups
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockGetProductGroups {
	if mmGetProductGroups.mock.funcGetProductGroups != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Set")
	}

	if mmGetProductGroups.defaultExpectation == nil {
		mmGetProductGroups.defaultExpectation = &GroupCacheMockGetProductGroupsExpectation{}
	}

	if mmGetProductGroups.defaultExpectation.params != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Expect")
	}

	if mmGetProductGroups.defaultExpectation.paramPtrs == nil {
		mmGetProductGroups.defaultExpectation.paramPtrs = &GroupCacheMockGetProductGroupsParamPtrs{}
	}
	mmGetProductGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetProductGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetProductGroups
}

// ExpectProductIDParam2 sets up expected param productID for GroupCache.GetProductGroups
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) ExpectProductIDParam2(productID int64) *mGroupCacheMockGetProductGroups {
	if mmGetProductGroups.mock.funcGetProductGroups != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Set")
	}

	if mmGetProductGroups.defaultExpectation == nil {
		mmGetProductGroups.defaultExpectation = &GroupCacheMockGetProductGroupsExpectation{}
	}

	if mmGetProductGroups.defaultExpectation.params != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Expect")
	}

	if mmGetProductGroups.defaultExpectation.paramPtrs == nil {
		mmGetProductGroups.defaultExpectation.paramPtrs = &GroupCacheMockGetProductGroupsParamPtrs{}
	}
	mmGetProductGroups.defaultExpectation.paramPtrs.productID = &productID
	mmGetProductGroups.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetProductGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.GetProductGroups
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Inspect(f func(ctx context.Context, productID int64)) *mGroupCacheMockGetProductGroups {
	if mmGetProductGroups.mock.inspectFuncGetProductGroups != nil {
		mmGetProductGroups.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.GetProductGroups")
	}

	mmGetProductGroups.mock.inspectFuncGetProductGroups = f

	return mmGetProductGroups
}

// Return sets up results that will be returned by GroupCache.GetProductGroups
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Return(ga1 []groupentity.Group, err error) *GroupCacheMock {
	if mmGetProductGroups.mock.funcGetProductGroups != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Set")
	}

	if mmGetProductGroups.defaultExpectation == nil {
		mmGetProductGroups.defaultExpectation = &GroupCacheMockGetProductGroupsExpectation{mock: mmGetProductGroups.mock}
	}
	mmGetProductGroups.defaultExpectation.results = &GroupCacheMockGetProductGroupsResults{ga1, err}
	mmGetProductGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetProductGroups.mock
}

// Set uses given function f to mock the GroupCache.GetProductGroups method
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Set(f func(ctx context.Context, productID int64) (ga1 []groupentity.Group, err error)) *GroupCacheMock {
	if mmGetProductGroups.defaultExpectation != nil {
		mmGetProductGroups.mock.t.Fatalf("Default expectation is already set for the GroupCache.GetProductGroups method")
	}

	if len(mmGetProductGroups.expectations) > 0 {
		mmGetProductGroups.mock.t.Fatalf("Some expectations are already set for the GroupCache.GetProductGroups method")
	}

	mmGetProductGroups.mock.funcGetProductGroups = f
	mmGetProductGroups.mock.funcGetProductGroupsOrigin = minimock.CallerInfo(1)
	return mmGetProductGroups.mock
}

// When sets expectation for the GroupCache.GetProductGroups which will trigger the result defined by the following
// Then helper
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) When(ctx context.Context, productID int64) *GroupCacheMockGetProductGroupsExpectation {
	if mmGetProductGroups.mock.funcGetProductGroups != nil {
		mmGetProductGroups.mock.t.Fatalf("GroupCacheMock.GetProductGroups mock is already set by Set")
	}

	expectation := &GroupCacheMockGetProductGroupsExpectation{
		mock:               mmGetProductGroups.mock,
		params:             &GroupCacheMockGetProductGroupsParams{ctx, productID},
		expectationOrigins: GroupCacheMockGetProductGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetProductGroups.expectations = append(mmGetProductGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.GetProductGroups return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockGetProductGroupsExpectation) Then(ga1 []groupentity.Group, err error) *GroupCacheMock {
	e.results = &GroupCacheMockGetProductGroupsResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupCache.GetProductGroups should be invoked
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Times(n uint64) *mGroupCacheMockGetProductGroups {
	if n == 0 {
		mmGetProductGroups.mock.t.Fatalf("Times of GroupCacheMock.GetProductGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetProductGroups.expectedInvocations, n)
	mmGetProductGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetProductGroups
}

func (mmGetProductGroups *mGroupCacheMockGetProductGroups) invocationsDone() bool {
	if len(mmGetProductGroups.expectations) == 0 && mmGetProductGroups.defaultExpectation == nil && mmGetProductGroups.mock.funcGetProductGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetProductGroups.mock.afterGetProductGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetProductGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetProductGroups implements mm_repository.GroupCache
func (mmGetProductGroups *GroupCacheMock) GetProductGroups(ctx context.Context, productID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetProductGroups.beforeGetProductGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetProductGroups.afterGetProductGroupsCounter, 1)

	mmGetProductGroups.t.Helper()

	if mmGetProductGroups.inspectFuncGetProductGroups != nil {
		mmGetProductGroups.inspectFuncGetProductGroups(ctx, productID)
	}

	mm_params := GroupCacheMockGetProductGroupsParams{ctx, productID}

	// Record call args
	mmGetProductGroups.GetProductGroupsMock.mutex.Lock()
	mmGetProductGroups.GetProductGroupsMock.callArgs = append(mmGetProductGroups.GetProductGroupsMock.callArgs, &mm_params)
	mmGetProductGroups.GetProductGroupsMock.mutex.Unlock()

	for _, e := range mmGetProductGroups.GetProductGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetProductGroups.GetProductGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetProductGroups.GetProductGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetProductGroups.GetProductGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmGetProductGroups.GetProductGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockGetProductGroupsParams{ctx, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetProductGroups.t.Errorf("GroupCacheMock.GetProductGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProductGroups.GetProductGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetProductGroups.t.Errorf("GroupCacheMock.GetProductGroups got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProductGroups.GetProductGroupsMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetProductGroups.t.Errorf("GroupCacheMock.GetProductGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetProductGroups.GetProductGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetProductGroups.GetProductGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetProductGroups.t.Fatal("No results are set for the GroupCacheMock.GetProductGroups")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetProductGroups.funcGetProductGroups != nil {
		return mmGetProductGroups.funcGetProductGroups(ctx, productID)
	}
	mmGetProductGroups.t.Fatalf("Unexpected call to GroupCacheMock.GetProductGroups. %v %v", ctx, productID)
	return
}

// GetProductGroupsAfterCounter returns a count of finished GroupCacheMock.GetProductGroups invocations
func (mmGetProductGroups *GroupCacheMock) GetProductGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductGroups.afterGetProductGroupsCounter)
}

// GetProductGroupsBeforeCounter returns a count of GroupCacheMock.GetProductGroups invocations
func (mmGetProductGroups *GroupCacheMock) GetProductGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductGroups.beforeGetProductGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.GetProductGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetProductGroups *mGroupCacheMockGetProductGroups) Calls() []*GroupCacheMockGetProductGroupsParams {
	mmGetProductGroups.mutex.RLock()

	argCopy := make([]*GroupCacheMockGetProductGroupsParams, len(mmGetProductGroups.callArgs))
	copy(argCopy, mmGetProductGroups.callArgs)

	mmGetProductGroups.mutex.RUnlock()

	return argCopy
}

// MinimockGetProductGroupsDone returns true if the count of the GetProductGroups invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockGetProductGroupsDone() bool {
	if m.GetProductGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetProductGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetProductGroupsMock.invocationsDone()
}

// MinimockGetProductGroupsInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockGetProductGroupsInspect() {
	for _, e := range m.GetProductGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.GetProductGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetProductGroupsCounter := mm_atomic.LoadUint64(&m.afterGetProductGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetProductGroupsMock.defaultExpectation != nil && afterGetProductGroupsCounter < 1 {
		if m.GetProductGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.GetProductGroups at\n%s", m.GetProductGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.GetProductGroups at\n%s with params: %#v", m.GetProductGroupsMock.defaultExpectation.expectationOrigins.origin, *m.GetProductGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetProductGroups != nil && afterGetProductGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.GetProductGroups at\n%s", m.funcGetProductGroupsOrigin)
	}

	if !m.GetProductGroupsMock.invocationsDone() && afterGetProductGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.GetProductGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetProductGroupsMock.expectedInvocations), m.GetProductGroupsMock.expectedInvocationsOrigin, afterGetProductGroupsCounter)
	}
}

type mGroupCacheMockSetGroup struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockSetGroupExpectation
	expectations       []*GroupCacheMockSetGroupExpectation

	callArgs []*GroupCacheMockSetGroupParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockSetGroupExpectation specifies expectation struct of the GroupCache.SetGroup
type GroupCacheMockSetGroupExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockSetGroupParams
	paramPtrs          *GroupCacheMockSetGroupParamPtrs
	expectationOrigins GroupCacheMockSetGroupExpectationOrigins
	results            *GroupCacheMockSetGroupResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockSetGroupParams contains parameters of the GroupCache.SetGroup
type GroupCacheMockSetGroupParams struct {
	ctx   context.Context
	group groupentity.Group
}

// GroupCacheMockSetGroupParamPtrs contains pointers to parameters of the GroupCache.SetGroup
type GroupCacheMockSetGroupParamPtrs struct {
	ctx   *context.Context
	group *groupentity.Group
}

// GroupCacheMockSetGroupResults contains results of the GroupCache.SetGroup
type GroupCacheMockSetGroupResults struct {
	err error
}

// GroupCacheMockSetGroupOrigins contains origins of expectations of the GroupCache.SetGroup
type GroupCacheMockSetGroupExpectationOrigins struct {
	origin      string
	originCtx   string
	originGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetGroup *mGroupCacheMockSetGroup) Optional() *mGroupCacheMockSetGroup {
	mmSetGroup.optional = true
	return mmSetGroup
}

// Expect sets up expected params for GroupCache.SetGroup
func (mmSetGroup *mGroupCacheMockSetGroup) Expect(ctx context.Context, group groupentity.Group) *mGroupCacheMockSetGroup {
	if mmSetGroup.mock.funcSetGroup != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Set")
	}

	if mmSetGroup.defaultExpectation == nil {
		mmSetGroup.defaultExpectation = &GroupCacheMockSetGroupExpectation{}
	}

	if mmSetGroup.defaultExpectation.paramPtrs != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by ExpectParams functions")
	}

	mmSetGroup.defaultExpectation.params = &GroupCacheMockSetGroupParams{ctx, group}
	mmSetGroup.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetGroup.expectations {
		if minimock.Equal(e.params, mmSetGroup.defaultExpectation.params) {
			mmSetGroup.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetGroup.defaultExpectation.params)
		}
	}

	return mmSetGroup
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.SetGroup
func (mmSetGroup *mGroupCacheMockSetGroup) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockSetGroup {
	if mmSetGroup.mock.funcSetGroup != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Set")
	}

	if mmSetGroup.defaultExpectation == nil {
		mmSetGroup.defaultExpectation = &GroupCacheMockSetGroupExpectation{}
	}

	if mmSetGroup.defaultExpectation.params != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Expect")
	}

	if mmSetGroup.defaultExpectation.paramPtrs == nil {
		mmSetGroup.defaultExpectation.paramPtrs = &GroupCacheMockSetGroupParamPtrs{}
	}
	mmSetGroup.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetGroup.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetGroup
}

// ExpectGroupParam2 sets up expected param group for GroupCache.SetGroup
func (mmSetGroup *mGroupCacheMockSetGroup) ExpectGroupParam2(group groupentity.Group) *mGroupCacheMockSetGroup {
	if mmSetGroup.mock.funcSetGroup != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Set")
	}

	if mmSetGroup.defaultExpectation == nil {
		mmSetGroup.defaultExpectation = &GroupCacheMockSetGroupExpectation{}
	}

	if mmSetGroup.defaultExpectation.params != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Expect")
	}

	if mmSetGroup.defaultExpectation.paramPtrs == nil {
		mmSetGroup.defaultExpectation.paramPtrs = &GroupCacheMockSetGroupParamPtrs{}
	}
	mmSetGroup.defaultExpectation.paramPtrs.group = &group
	mmSetGroup.defaultExpectation.expectationOrigins.originGroup = minimock.CallerInfo(1)

	return mmSetGroup
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.SetGroup
func (mmSetGroup *mGroupCacheMockSetGroup) Inspect(f func(ctx context.Context, group groupentity.Group)) *mGroupCacheMockSetGroup {
	if mmSetGroup.mock.inspectFuncSetGroup != nil {
		mmSetGroup.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.SetGroup")
	}

	mmSetGroup.mock.inspectFuncSetGroup = f

	return mmSetGroup
}

// Return sets up results that will be returned by GroupCache.SetGroup
func (mmSetGroup *mGroupCacheMockSetGroup) Return(err error) *GroupCacheMock {
	if mmSetGroup.mock.funcSetGroup != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Set")
	}

	if mmSetGroup.defaultExpectation == nil {
		mmSetGroup.defaultExpectation = &GroupCacheMockSetGroupExpectation{mock: mmSetGroup.mock}
	}
	mmSetGroup.defaultExpectation.results = &GroupCacheMockSetGroupResults{err}
	mmSetGroup.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetGroup.mock
}

// Set uses given function f to mock the GroupCache.SetGroup method
func (mmSetGroup *mGroupCacheMockSetGroup) Set(f func(ctx context.Context, group groupentity.Group) (err error)) *GroupCacheMock {
	if mmSetGroup.defaultExpectation != nil {
		mmSetGroup.mock.t.Fatalf("Default expectation is already set for the GroupCache.SetGroup method")
	}

	if len(mmSetGroup.expectations) > 0 {
		mmSetGroup.mock.t.Fatalf("Some expectations are already set for the GroupCache.SetGroup method")
	}

	mmSetGroup.mock.funcSetGroup = f
	mmSetGroup.mock.funcSetGroupOrigin = minimock.CallerInfo(1)
	return mmSetGroup.mock
}

// When sets expectation for the GroupCache.SetGroup which will trigger the result defined by the following
// Then helper
func (mmSetGroup *mGroupCacheMockSetGroup) When(ctx context.Context, group groupentity.Group) *GroupCacheMockSetGroupExpectation {
	if mmSetGroup.mock.funcSetGroup != nil {
		mmSetGroup.mock.t.Fatalf("GroupCacheMock.SetGroup mock is already set by Set")
	}

	expectation := &GroupCacheMockSetGroupExpectation{
		mock:               mmSetGroup.mock,
		params:             &GroupCacheMockSetGroupParams{ctx, group},
		expectationOrigins: GroupCacheMockSetGroupExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetGroup.expectations = append(mmSetGroup.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.SetGroup return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockSetGroupExpectation) Then(err error) *GroupCacheMock {
	e.results = &GroupCacheMockSetGroupResults{err}
	return e.mock
}

// Times sets number of times GroupCache.SetGroup should be invoked
func (mmSetGroup *mGroupCacheMockSetGroup) Times(n uint64) *mGroupCacheMockSetGroup {
	if n == 0 {
		mmSetGroup.mock.t.Fatalf("Times of GroupCacheMock.SetGroup mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetGroup.expectedInvocations, n)
	mmSetGroup.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetGroup
}

func (mmSetGroup *mGroupCacheMockSetGroup) invocationsDone() bool {
	if len(mmSetGroup.expectations) == 0 && mmSetGroup.defaultExpectation == nil && mmSetGroup.mock.funcSetGroup == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetGroup.mock.afterSetGroupCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetGroup.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetGroup implements mm_repository.GroupCache
func (mmSetGroup *GroupCacheMock) SetGroup(ctx context.Context, group groupentity.Group) (err error) {
	mm_atomic.AddUint64(&mmSetGroup.beforeSetGroupCounter, 1)
	defer mm_atomic.AddUint64(&mmSetGroup.afterSetGroupCounter, 1)

	mmSetGroup.t.Helper()

	if mmSetGroup.inspectFuncSetGroup != nil {
		mmSetGroup.inspectFuncSetGroup(ctx, group)
	}

	mm_params := GroupCacheMockSetGroupParams{ctx, group}

	// Record call args
	mmSetGroup.SetGroupMock.mutex.Lock()
	mmSetGroup.SetGroupMock.callArgs = append(mmSetGroup.SetGroupMock.callArgs, &mm_params)
	mmSetGroup.SetGroupMock.mutex.Unlock()

	for _, e := range mmSetGroup.SetGroupMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetGroup.SetGroupMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetGroup.SetGroupMock.defaultExpectation.Counter, 1)
		mm_want := mmSetGroup.SetGroupMock.defaultExpectation.params
		mm_want_ptrs := mmSetGroup.SetGroupMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockSetGroupParams{ctx, group}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetGroup.t.Errorf("GroupCacheMock.SetGroup got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetGroup.SetGroupMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.group != nil && !minimock.Equal(*mm_want_ptrs.group, mm_got.group) {
				mmSetGroup.t.Errorf("GroupCacheMock.SetGroup got unexpected parameter group, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetGroup.SetGroupMock.defaultExpectation.expectationOrigins.originGroup, *mm_want_ptrs.group, mm_got.group, minimock.Diff(*mm_want_ptrs.group, mm_got.group))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetGroup.t.Errorf("GroupCacheMock.SetGroup got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetGroup.SetGroupMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetGroup.SetGroupMock.defaultExpectation.results
		if mm_results == nil {
			mmSetGroup.t.Fatal("No results are set for the GroupCacheMock.SetGroup")
		}
		return (*mm_results).err
	}
	if mmSetGroup.funcSetGroup != nil {
		return mmSetGroup.funcSetGroup(ctx, group)
	}
	mmSetGroup.t.Fatalf("Unexpected call to GroupCacheMock.SetGroup. %v %v", ctx, group)
	return
}

// SetGroupAfterCounter returns a count of finished GroupCacheMock.SetGroup invocations
func (mmSetGroup *GroupCacheMock) SetGroupAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetGroup.afterSetGroupCounter)
}

// SetGroupBeforeCounter returns a count of GroupCacheMock.SetGroup invocations
func (mmSetGroup *GroupCacheMock) SetGroupBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetGroup.beforeSetGroupCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.SetGroup.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetGroup *mGroupCacheMockSetGroup) Calls() []*GroupCacheMockSetGroupParams {
	mmSetGroup.mutex.RLock()

	argCopy := make([]*GroupCacheMockSetGroupParams, len(mmSetGroup.callArgs))
	copy(argCopy, mmSetGroup.callArgs)

	mmSetGroup.mutex.RUnlock()

	return argCopy
}

// MinimockSetGroupDone returns true if the count of the SetGroup invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockSetGroupDone() bool {
	if m.SetGroupMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetGroupMock.invocationsDone()
}

// MinimockSetGroupInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockSetGroupInspect() {
	for _, e := range m.SetGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.SetGroup at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetGroupCounter := mm_atomic.LoadUint64(&m.afterSetGroupCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetGroupMock.defaultExpectation != nil && afterSetGroupCounter < 1 {
		if m.SetGroupMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.SetGroup at\n%s", m.SetGroupMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.SetGroup at\n%s with params: %#v", m.SetGroupMock.defaultExpectation.expectationOrigins.origin, *m.SetGroupMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetGroup != nil && afterSetGroupCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.SetGroup at\n%s", m.funcSetGroupOrigin)
	}

	if !m.SetGroupMock.invocationsDone() && afterSetGroupCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.SetGroup at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetGroupMock.expectedInvocations), m.SetGroupMock.expectedInvocationsOrigin, afterSetGroupCounter)
	}
}

type mGroupCacheMockSetGroups struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockSetGroupsExpectation
	expectations       []*GroupCacheMockSetGroupsExpectation

	callArgs []*GroupCacheMockSetGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockSetGroupsExpectation specifies expectation struct of the GroupCache.SetGroups
type GroupCacheMockSetGroupsExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockSetGroupsParams
	paramPtrs          *GroupCacheMockSetGroupsParamPtrs
	expectationOrigins GroupCacheMockSetGroupsExpectationOrigins
	results            *GroupCacheMockSetGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockSetGroupsParams contains parameters of the GroupCache.SetGroups
type GroupCacheMockSetGroupsParams struct {
	ctx    context.Context
	groups []groupentity.Group
}

// GroupCacheMockSetGroupsParamPtrs contains pointers to parameters of the GroupCache.SetGroups
type GroupCacheMockSetGroupsParamPtrs struct {
	ctx    *context.Context
	groups *[]groupentity.Group
}

// GroupCacheMockSetGroupsResults contains results of the GroupCache.SetGroups
type GroupCacheMockSetGroupsResults struct {
	err error
}

// GroupCacheMockSetGroupsOrigins contains origins of expectations of the GroupCache.SetGroups
type GroupCacheMockSetGroupsExpectationOrigins struct {
	origin       string
	originCtx    string
	originGroups string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetGroups *mGroupCacheMockSetGroups) Optional() *mGroupCacheMockSetGroups {
	mmSetGroups.optional = true
	return mmSetGroups
}

// Expect sets up expected params for GroupCache.SetGroups
func (mmSetGroups *mGroupCacheMockSetGroups) Expect(ctx context.Context, groups []groupentity.Group) *mGroupCacheMockSetGroups {
	if mmSetGroups.mock.funcSetGroups != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Set")
	}

	if mmSetGroups.defaultExpectation == nil {
		mmSetGroups.defaultExpectation = &GroupCacheMockSetGroupsExpectation{}
	}

	if mmSetGroups.defaultExpectation.paramPtrs != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by ExpectParams functions")
	}

	mmSetGroups.defaultExpectation.params = &GroupCacheMockSetGroupsParams{ctx, groups}
	mmSetGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetGroups.expectations {
		if minimock.Equal(e.params, mmSetGroups.defaultExpectation.params) {
			mmSetGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetGroups.defaultExpectation.params)
		}
	}

	return mmSetGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.SetGroups
func (mmSetGroups *mGroupCacheMockSetGroups) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockSetGroups {
	if mmSetGroups.mock.funcSetGroups != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Set")
	}

	if mmSetGroups.defaultExpectation == nil {
		mmSetGroups.defaultExpectation = &GroupCacheMockSetGroupsExpectation{}
	}

	if mmSetGroups.defaultExpectation.params != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Expect")
	}

	if mmSetGroups.defaultExpectation.paramPtrs == nil {
		mmSetGroups.defaultExpectation.paramPtrs = &GroupCacheMockSetGroupsParamPtrs{}
	}
	mmSetGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetGroups
}

// ExpectGroupsParam2 sets up expected param groups for GroupCache.SetGroups
func (mmSetGroups *mGroupCacheMockSetGroups) ExpectGroupsParam2(groups []groupentity.Group) *mGroupCacheMockSetGroups {
	if mmSetGroups.mock.funcSetGroups != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Set")
	}

	if mmSetGroups.defaultExpectation == nil {
		mmSetGroups.defaultExpectation = &GroupCacheMockSetGroupsExpectation{}
	}

	if mmSetGroups.defaultExpectation.params != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Expect")
	}

	if mmSetGroups.defaultExpectation.paramPtrs == nil {
		mmSetGroups.defaultExpectation.paramPtrs = &GroupCacheMockSetGroupsParamPtrs{}
	}
	mmSetGroups.defaultExpectation.paramPtrs.groups = &groups
	mmSetGroups.defaultExpectation.expectationOrigins.originGroups = minimock.CallerInfo(1)

	return mmSetGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.SetGroups
func (mmSetGroups *mGroupCacheMockSetGroups) Inspect(f func(ctx context.Context, groups []groupentity.Group)) *mGroupCacheMockSetGroups {
	if mmSetGroups.mock.inspectFuncSetGroups != nil {
		mmSetGroups.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.SetGroups")
	}

	mmSetGroups.mock.inspectFuncSetGroups = f

	return mmSetGroups
}

// Return sets up results that will be returned by GroupCache.SetGroups
func (mmSetGroups *mGroupCacheMockSetGroups) Return(err error) *GroupCacheMock {
	if mmSetGroups.mock.funcSetGroups != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Set")
	}

	if mmSetGroups.defaultExpectation == nil {
		mmSetGroups.defaultExpectation = &GroupCacheMockSetGroupsExpectation{mock: mmSetGroups.mock}
	}
	mmSetGroups.defaultExpectation.results = &GroupCacheMockSetGroupsResults{err}
	mmSetGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetGroups.mock
}

// Set uses given function f to mock the GroupCache.SetGroups method
func (mmSetGroups *mGroupCacheMockSetGroups) Set(f func(ctx context.Context, groups []groupentity.Group) (err error)) *GroupCacheMock {
	if mmSetGroups.defaultExpectation != nil {
		mmSetGroups.mock.t.Fatalf("Default expectation is already set for the GroupCache.SetGroups method")
	}

	if len(mmSetGroups.expectations) > 0 {
		mmSetGroups.mock.t.Fatalf("Some expectations are already set for the GroupCache.SetGroups method")
	}

	mmSetGroups.mock.funcSetGroups = f
	mmSetGroups.mock.funcSetGroupsOrigin = minimock.CallerInfo(1)
	return mmSetGroups.mock
}

// When sets expectation for the GroupCache.SetGroups which will trigger the result defined by the following
// Then helper
func (mmSetGroups *mGroupCacheMockSetGroups) When(ctx context.Context, groups []groupentity.Group) *GroupCacheMockSetGroupsExpectation {
	if mmSetGroups.mock.funcSetGroups != nil {
		mmSetGroups.mock.t.Fatalf("GroupCacheMock.SetGroups mock is already set by Set")
	}

	expectation := &GroupCacheMockSetGroupsExpectation{
		mock:               mmSetGroups.mock,
		params:             &GroupCacheMockSetGroupsParams{ctx, groups},
		expectationOrigins: GroupCacheMockSetGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetGroups.expectations = append(mmSetGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.SetGroups return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockSetGroupsExpectation) Then(err error) *GroupCacheMock {
	e.results = &GroupCacheMockSetGroupsResults{err}
	return e.mock
}

// Times sets number of times GroupCache.SetGroups should be invoked
func (mmSetGroups *mGroupCacheMockSetGroups) Times(n uint64) *mGroupCacheMockSetGroups {
	if n == 0 {
		mmSetGroups.mock.t.Fatalf("Times of GroupCacheMock.SetGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetGroups.expectedInvocations, n)
	mmSetGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetGroups
}

func (mmSetGroups *mGroupCacheMockSetGroups) invocationsDone() bool {
	if len(mmSetGroups.expectations) == 0 && mmSetGroups.defaultExpectation == nil && mmSetGroups.mock.funcSetGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetGroups.mock.afterSetGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetGroups implements mm_repository.GroupCache
func (mmSetGroups *GroupCacheMock) SetGroups(ctx context.Context, groups []groupentity.Group) (err error) {
	mm_atomic.AddUint64(&mmSetGroups.beforeSetGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmSetGroups.afterSetGroupsCounter, 1)

	mmSetGroups.t.Helper()

	if mmSetGroups.inspectFuncSetGroups != nil {
		mmSetGroups.inspectFuncSetGroups(ctx, groups)
	}

	mm_params := GroupCacheMockSetGroupsParams{ctx, groups}

	// Record call args
	mmSetGroups.SetGroupsMock.mutex.Lock()
	mmSetGroups.SetGroupsMock.callArgs = append(mmSetGroups.SetGroupsMock.callArgs, &mm_params)
	mmSetGroups.SetGroupsMock.mutex.Unlock()

	for _, e := range mmSetGroups.SetGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetGroups.SetGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetGroups.SetGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmSetGroups.SetGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmSetGroups.SetGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockSetGroupsParams{ctx, groups}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetGroups.t.Errorf("GroupCacheMock.SetGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetGroups.SetGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groups != nil && !minimock.Equal(*mm_want_ptrs.groups, mm_got.groups) {
				mmSetGroups.t.Errorf("GroupCacheMock.SetGroups got unexpected parameter groups, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetGroups.SetGroupsMock.defaultExpectation.expectationOrigins.originGroups, *mm_want_ptrs.groups, mm_got.groups, minimock.Diff(*mm_want_ptrs.groups, mm_got.groups))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetGroups.t.Errorf("GroupCacheMock.SetGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetGroups.SetGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetGroups.SetGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmSetGroups.t.Fatal("No results are set for the GroupCacheMock.SetGroups")
		}
		return (*mm_results).err
	}
	if mmSetGroups.funcSetGroups != nil {
		return mmSetGroups.funcSetGroups(ctx, groups)
	}
	mmSetGroups.t.Fatalf("Unexpected call to GroupCacheMock.SetGroups. %v %v", ctx, groups)
	return
}

// SetGroupsAfterCounter returns a count of finished GroupCacheMock.SetGroups invocations
func (mmSetGroups *GroupCacheMock) SetGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetGroups.afterSetGroupsCounter)
}

// SetGroupsBeforeCounter returns a count of GroupCacheMock.SetGroups invocations
func (mmSetGroups *GroupCacheMock) SetGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetGroups.beforeSetGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.SetGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetGroups *mGroupCacheMockSetGroups) Calls() []*GroupCacheMockSetGroupsParams {
	mmSetGroups.mutex.RLock()

	argCopy := make([]*GroupCacheMockSetGroupsParams, len(mmSetGroups.callArgs))
	copy(argCopy, mmSetGroups.callArgs)

	mmSetGroups.mutex.RUnlock()

	return argCopy
}

// MinimockSetGroupsDone returns true if the count of the SetGroups invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockSetGroupsDone() bool {
	if m.SetGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetGroupsMock.invocationsDone()
}

// MinimockSetGroupsInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockSetGroupsInspect() {
	for _, e := range m.SetGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.SetGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetGroupsCounter := mm_atomic.LoadUint64(&m.afterSetGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetGroupsMock.defaultExpectation != nil && afterSetGroupsCounter < 1 {
		if m.SetGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.SetGroups at\n%s", m.SetGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.SetGroups at\n%s with params: %#v", m.SetGroupsMock.defaultExpectation.expectationOrigins.origin, *m.SetGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetGroups != nil && afterSetGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.SetGroups at\n%s", m.funcSetGroupsOrigin)
	}

	if !m.SetGroupsMock.invocationsDone() && afterSetGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.SetGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetGroupsMock.expectedInvocations), m.SetGroupsMock.expectedInvocationsOrigin, afterSetGroupsCounter)
	}
}

type mGroupCacheMockSetProductGroups struct {
	optional           bool
	mock               *GroupCacheMock
	defaultExpectation *GroupCacheMockSetProductGroupsExpectation
	expectations       []*GroupCacheMockSetProductGroupsExpectation

	callArgs []*GroupCacheMockSetProductGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupCacheMockSetProductGroupsExpectation specifies expectation struct of the GroupCache.SetProductGroups
type GroupCacheMockSetProductGroupsExpectation struct {
	mock               *GroupCacheMock
	params             *GroupCacheMockSetProductGroupsParams
	paramPtrs          *GroupCacheMockSetProductGroupsParamPtrs
	expectationOrigins GroupCacheMockSetProductGroupsExpectationOrigins
	results            *GroupCacheMockSetProductGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupCacheMockSetProductGroupsParams contains parameters of the GroupCache.SetProductGroups
type GroupCacheMockSetProductGroupsParams struct {
	ctx       context.Context
	productID int64
	groups    []groupentity.Group
}

// GroupCacheMockSetProductGroupsParamPtrs contains pointers to parameters of the GroupCache.SetProductGroups
type GroupCacheMockSetProductGroupsParamPtrs struct {
	ctx       *context.Context
	productID *int64
	groups    *[]groupentity.Group
}

// GroupCacheMockSetProductGroupsResults contains results of the GroupCache.SetProductGroups
type GroupCacheMockSetProductGroupsResults struct {
	err error
}

// GroupCacheMockSetProductGroupsOrigins contains origins of expectations of the GroupCache.SetProductGroups
type GroupCacheMockSetProductGroupsExpectationOrigins struct {
	origin          string
	originCtx       string
	originProductID string
	originGroups    string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Optional() *mGroupCacheMockSetProductGroups {
	mmSetProductGroups.optional = true
	return mmSetProductGroups
}

// Expect sets up expected params for GroupCache.SetProductGroups
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Expect(ctx context.Context, productID int64, groups []groupentity.Group) *mGroupCacheMockSetProductGroups {
	if mmSetProductGroups.mock.funcSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Set")
	}

	if mmSetProductGroups.defaultExpectation == nil {
		mmSetProductGroups.defaultExpectation = &GroupCacheMockSetProductGroupsExpectation{}
	}

	if mmSetProductGroups.defaultExpectation.paramPtrs != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by ExpectParams functions")
	}

	mmSetProductGroups.defaultExpectation.params = &GroupCacheMockSetProductGroupsParams{ctx, productID, groups}
	mmSetProductGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetProductGroups.expectations {
		if minimock.Equal(e.params, mmSetProductGroups.defaultExpectation.params) {
			mmSetProductGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetProductGroups.defaultExpectation.params)
		}
	}

	return mmSetProductGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupCache.SetProductGroups
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) ExpectCtxParam1(ctx context.Context) *mGroupCacheMockSetProductGroups {
	if mmSetProductGroups.mock.funcSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Set")
	}

	if mmSetProductGroups.defaultExpectation == nil {
		mmSetProductGroups.defaultExpectation = &GroupCacheMockSetProductGroupsExpectation{}
	}

	if mmSetProductGroups.defaultExpectation.params != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Expect")
	}

	if mmSetProductGroups.defaultExpectation.paramPtrs == nil {
		mmSetProductGroups.defaultExpectation.paramPtrs = &GroupCacheMockSetProductGroupsParamPtrs{}
	}
	mmSetProductGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetProductGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetProductGroups
}

// ExpectProductIDParam2 sets up expected param productID for GroupCache.SetProductGroups
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) ExpectProductIDParam2(productID int64) *mGroupCacheMockSetProductGroups {
	if mmSetProductGroups.mock.funcSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Set")
	}

	if mmSetProductGroups.defaultExpectation == nil {
		mmSetProductGroups.defaultExpectation = &GroupCacheMockSetProductGroupsExpectation{}
	}

	if mmSetProductGroups.defaultExpectation.params != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Expect")
	}

	if mmSetProductGroups.defaultExpectation.paramPtrs == nil {
		mmSetProductGroups.defaultExpectation.paramPtrs = &GroupCacheMockSetProductGroupsParamPtrs{}
	}
	mmSetProductGroups.defaultExpectation.paramPtrs.productID = &productID
	mmSetProductGroups.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmSetProductGroups
}

// ExpectGroupsParam3 sets up expected param groups for GroupCache.SetProductGroups
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) ExpectGroupsParam3(groups []groupentity.Group) *mGroupCacheMockSetProductGroups {
	if mmSetProductGroups.mock.funcSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Set")
	}

	if mmSetProductGroups.defaultExpectation == nil {
		mmSetProductGroups.defaultExpectation = &GroupCacheMockSetProductGroupsExpectation{}
	}

	if mmSetProductGroups.defaultExpectation.params != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Expect")
	}

	if mmSetProductGroups.defaultExpectation.paramPtrs == nil {
		mmSetProductGroups.defaultExpectation.paramPtrs = &GroupCacheMockSetProductGroupsParamPtrs{}
	}
	mmSetProductGroups.defaultExpectation.paramPtrs.groups = &groups
	mmSetProductGroups.defaultExpectation.expectationOrigins.originGroups = minimock.CallerInfo(1)

	return mmSetProductGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupCache.SetProductGroups
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Inspect(f func(ctx context.Context, productID int64, groups []groupentity.Group)) *mGroupCacheMockSetProductGroups {
	if mmSetProductGroups.mock.inspectFuncSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("Inspect function is already set for GroupCacheMock.SetProductGroups")
	}

	mmSetProductGroups.mock.inspectFuncSetProductGroups = f

	return mmSetProductGroups
}

// Return sets up results that will be returned by GroupCache.SetProductGroups
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Return(err error) *GroupCacheMock {
	if mmSetProductGroups.mock.funcSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Set")
	}

	if mmSetProductGroups.defaultExpectation == nil {
		mmSetProductGroups.defaultExpectation = &GroupCacheMockSetProductGroupsExpectation{mock: mmSetProductGroups.mock}
	}
	mmSetProductGroups.defaultExpectation.results = &GroupCacheMockSetProductGroupsResults{err}
	mmSetProductGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetProductGroups.mock
}

// Set uses given function f to mock the GroupCache.SetProductGroups method
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Set(f func(ctx context.Context, productID int64, groups []groupentity.Group) (err error)) *GroupCacheMock {
	if mmSetProductGroups.defaultExpectation != nil {
		mmSetProductGroups.mock.t.Fatalf("Default expectation is already set for the GroupCache.SetProductGroups method")
	}

	if len(mmSetProductGroups.expectations) > 0 {
		mmSetProductGroups.mock.t.Fatalf("Some expectations are already set for the GroupCache.SetProductGroups method")
	}

	mmSetProductGroups.mock.funcSetProductGroups = f
	mmSetProductGroups.mock.funcSetProductGroupsOrigin = minimock.CallerInfo(1)
	return mmSetProductGroups.mock
}

// When sets expectation for the GroupCache.SetProductGroups which will trigger the result defined by the following
// Then helper
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) When(ctx context.Context, productID int64, groups []groupentity.Group) *GroupCacheMockSetProductGroupsExpectation {
	if mmSetProductGroups.mock.funcSetProductGroups != nil {
		mmSetProductGroups.mock.t.Fatalf("GroupCacheMock.SetProductGroups mock is already set by Set")
	}

	expectation := &GroupCacheMockSetProductGroupsExpectation{
		mock:               mmSetProductGroups.mock,
		params:             &GroupCacheMockSetProductGroupsParams{ctx, productID, groups},
		expectationOrigins: GroupCacheMockSetProductGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetProductGroups.expectations = append(mmSetProductGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupCache.SetProductGroups return parameters for the expectation previously defined by the When method
func (e *GroupCacheMockSetProductGroupsExpectation) Then(err error) *GroupCacheMock {
	e.results = &GroupCacheMockSetProductGroupsResults{err}
	return e.mock
}

// Times sets number of times GroupCache.SetProductGroups should be invoked
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Times(n uint64) *mGroupCacheMockSetProductGroups {
	if n == 0 {
		mmSetProductGroups.mock.t.Fatalf("Times of GroupCacheMock.SetProductGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetProductGroups.expectedInvocations, n)
	mmSetProductGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetProductGroups
}

func (mmSetProductGroups *mGroupCacheMockSetProductGroups) invocationsDone() bool {
	if len(mmSetProductGroups.expectations) == 0 && mmSetProductGroups.defaultExpectation == nil && mmSetProductGroups.mock.funcSetProductGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetProductGroups.mock.afterSetProductGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetProductGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetProductGroups implements mm_repository.GroupCache
func (mmSetProductGroups *GroupCacheMock) SetProductGroups(ctx context.Context, productID int64, groups []groupentity.Group) (err error) {
	mm_atomic.AddUint64(&mmSetProductGroups.beforeSetProductGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmSetProductGroups.afterSetProductGroupsCounter, 1)

	mmSetProductGroups.t.Helper()

	if mmSetProductGroups.inspectFuncSetProductGroups != nil {
		mmSetProductGroups.inspectFuncSetProductGroups(ctx, productID, groups)
	}

	mm_params := GroupCacheMockSetProductGroupsParams{ctx, productID, groups}

	// Record call args
	mmSetProductGroups.SetProductGroupsMock.mutex.Lock()
	mmSetProductGroups.SetProductGroupsMock.callArgs = append(mmSetProductGroups.SetProductGroupsMock.callArgs, &mm_params)
	mmSetProductGroups.SetProductGroupsMock.mutex.Unlock()

	for _, e := range mmSetProductGroups.SetProductGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetProductGroups.SetProductGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetProductGroups.SetProductGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmSetProductGroups.SetProductGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmSetProductGroups.SetProductGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupCacheMockSetProductGroupsParams{ctx, productID, groups}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetProductGroups.t.Errorf("GroupCacheMock.SetProductGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProductGroups.SetProductGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmSetProductGroups.t.Errorf("GroupCacheMock.SetProductGroups got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProductGroups.SetProductGroupsMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.groups != nil && !minimock.Equal(*mm_want_ptrs.groups, mm_got.groups) {
				mmSetProductGroups.t.Errorf("GroupCacheMock.SetProductGroups got unexpected parameter groups, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProductGroups.SetProductGroupsMock.defaultExpectation.expectationOrigins.originGroups, *mm_want_ptrs.groups, mm_got.groups, minimock.Diff(*mm_want_ptrs.groups, mm_got.groups))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetProductGroups.t.Errorf("GroupCacheMock.SetProductGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetProductGroups.SetProductGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetProductGroups.SetProductGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmSetProductGroups.t.Fatal("No results are set for the GroupCacheMock.SetProductGroups")
		}
		return (*mm_results).err
	}
	if mmSetProductGroups.funcSetProductGroups != nil {
		return mmSetProductGroups.funcSetProductGroups(ctx, productID, groups)
	}
	mmSetProductGroups.t.Fatalf("Unexpected call to GroupCacheMock.SetProductGroups. %v %v %v", ctx, productID, groups)
	return
}

// SetProductGroupsAfterCounter returns a count of finished GroupCacheMock.SetProductGroups invocations
func (mmSetProductGroups *GroupCacheMock) SetProductGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetProductGroups.afterSetProductGroupsCounter)
}

// SetProductGroupsBeforeCounter returns a count of GroupCacheMock.SetProductGroups invocations
func (mmSetProductGroups *GroupCacheMock) SetProductGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetProductGroups.beforeSetProductGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupCacheMock.SetProductGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetProductGroups *mGroupCacheMockSetProductGroups) Calls() []*GroupCacheMockSetProductGroupsParams {
	mmSetProductGroups.mutex.RLock()

	argCopy := make([]*GroupCacheMockSetProductGroupsParams, len(mmSetProductGroups.callArgs))
	copy(argCopy, mmSetProductGroups.callArgs)

	mmSetProductGroups.mutex.RUnlock()

	return argCopy
}

// MinimockSetProductGroupsDone returns true if the count of the SetProductGroups invocations corresponds
// the number of defined expectations
func (m *GroupCacheMock) MinimockSetProductGroupsDone() bool {
	if m.SetProductGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetProductGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetProductGroupsMock.invocationsDone()
}

// MinimockSetProductGroupsInspect logs each unmet expectation
func (m *GroupCacheMock) MinimockSetProductGroupsInspect() {
	for _, e := range m.SetProductGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupCacheMock.SetProductGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetProductGroupsCounter := mm_atomic.LoadUint64(&m.afterSetProductGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetProductGroupsMock.defaultExpectation != nil && afterSetProductGroupsCounter < 1 {
		if m.SetProductGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupCacheMock.SetProductGroups at\n%s", m.SetProductGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupCacheMock.SetProductGroups at\n%s with params: %#v", m.SetProductGroupsMock.defaultExpectation.expectationOrigins.origin, *m.SetProductGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetProductGroups != nil && afterSetProductGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupCacheMock.SetProductGroups at\n%s", m.funcSetProductGroupsOrigin)
	}

	if !m.SetProductGroupsMock.invocationsDone() && afterSetProductGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupCacheMock.SetProductGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetProductGroupsMock.expectedInvocations), m.SetProductGroupsMock.expectedInvocationsOrigin, afterSetProductGroupsCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *GroupCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockDeleteGroupInspect()

			m.MinimockDeleteGroupsInspect()

			m.MinimockGetGroupInspect()

			m.MinimockGetGroupsInspect()

			m.MinimockGetProductGroupsInspect()

			m.MinimockSetGroupInspect()

			m.MinimockSetGroupsInspect()

			m.MinimockSetProductGroupsInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *GroupCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *GroupCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockDeleteGroupDone() &&
		m.MinimockDeleteGroupsDone() &&
		m.MinimockGetGroupDone() &&
		m.MinimockGetGroupsDone() &&
		m.MinimockGetProductGroupsDone() &&
		m.MinimockSetGroupDone() &&
		m.MinimockSetGroupsDone() &&
		m.MinimockSetProductGroupsDone()
}
