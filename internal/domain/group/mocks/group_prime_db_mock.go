// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository.GroupPrimeDB -o group_prime_db_mock.go -n GroupPrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	"github.com/gojuno/minimock/v3"
)

// GroupPrimeDBMock implements mm_repository.GroupPrimeDB
type GroupPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(group groupentity.Group) (g1 groupentity.Group, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(group groupentity.Group)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mGroupPrimeDBMockCreate

	funcDeactivateByIDs          func(groupIDs []int64) (err error)
	funcDeactivateByIDsOrigin    string
	inspectFuncDeactivateByIDs   func(groupIDs []int64)
	afterDeactivateByIDsCounter  uint64
	beforeDeactivateByIDsCounter uint64
	DeactivateByIDsMock          mGroupPrimeDBMockDeactivateByIDs

	funcDeleteGroup          func(groupID int64) (err error)
	funcDeleteGroupOrigin    string
	inspectFuncDeleteGroup   func(groupID int64)
	afterDeleteGroupCounter  uint64
	beforeDeleteGroupCounter uint64
	DeleteGroupMock          mGroupPrimeDBMockDeleteGroup

	funcExistByName          func(name string) (b1 bool, err error)
	funcExistByNameOrigin    string
	inspectFuncExistByName   func(name string)
	afterExistByNameCounter  uint64
	beforeExistByNameCounter uint64
	ExistByNameMock          mGroupPrimeDBMockExistByName

	funcGetAll          func() (ga1 []groupentity.Group, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mGroupPrimeDBMockGetAll

	funcGetAllByUserID          func(userID int64) (ga1 []groupentity.Group, err error)
	funcGetAllByUserIDOrigin    string
	inspectFuncGetAllByUserID   func(userID int64)
	afterGetAllByUserIDCounter  uint64
	beforeGetAllByUserIDCounter uint64
	GetAllByUserIDMock          mGroupPrimeDBMockGetAllByUserID

	funcGetAllWithProductIDByUserID          func(userID int64) (ga1 []groupentity.GroupWithProductID, err error)
	funcGetAllWithProductIDByUserIDOrigin    string
	inspectFuncGetAllWithProductIDByUserID   func(userID int64)
	afterGetAllWithProductIDByUserIDCounter  uint64
	beforeGetAllWithProductIDByUserIDCounter uint64
	GetAllWithProductIDByUserIDMock          mGroupPrimeDBMockGetAllWithProductIDByUserID

	funcGetByFiltersAndPagination          func(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams) (ga1 []groupentity.Group, i1 int64, err error)
	funcGetByFiltersAndPaginationOrigin    string
	inspectFuncGetByFiltersAndPagination   func(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams)
	afterGetByFiltersAndPaginationCounter  uint64
	beforeGetByFiltersAndPaginationCounter uint64
	GetByFiltersAndPaginationMock          mGroupPrimeDBMockGetByFiltersAndPagination

	funcGetByGroupIDAndProductID          func(groupID int64, productID int64) (g1 groupentity.Group, err error)
	funcGetByGroupIDAndProductIDOrigin    string
	inspectFuncGetByGroupIDAndProductID   func(groupID int64, productID int64)
	afterGetByGroupIDAndProductIDCounter  uint64
	beforeGetByGroupIDAndProductIDCounter uint64
	GetByGroupIDAndProductIDMock          mGroupPrimeDBMockGetByGroupIDAndProductID

	funcGetByID          func(id int64) (g1 groupentity.Group, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mGroupPrimeDBMockGetByID

	funcGetByProductID          func(productID int64) (ga1 []groupentity.Group, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mGroupPrimeDBMockGetByProductID

	funcGetByProductIDAndIsActive          func(productID int64, isActive bool) (aa1 []groupentity.AdminGroup, err error)
	funcGetByProductIDAndIsActiveOrigin    string
	inspectFuncGetByProductIDAndIsActive   func(productID int64, isActive bool)
	afterGetByProductIDAndIsActiveCounter  uint64
	beforeGetByProductIDAndIsActiveCounter uint64
	GetByProductIDAndIsActiveMock          mGroupPrimeDBMockGetByProductIDAndIsActive

	funcGetGroupCategoryStates          func() (ga1 []groupentity.GroupCategoryLink, err error)
	funcGetGroupCategoryStatesOrigin    string
	inspectFuncGetGroupCategoryStates   func()
	afterGetGroupCategoryStatesCounter  uint64
	beforeGetGroupCategoryStatesCounter uint64
	GetGroupCategoryStatesMock          mGroupPrimeDBMockGetGroupCategoryStates

	funcGetSystemGroups          func() (ga1 []groupentity.Group, err error)
	funcGetSystemGroupsOrigin    string
	inspectFuncGetSystemGroups   func()
	afterGetSystemGroupsCounter  uint64
	beforeGetSystemGroupsCounter uint64
	GetSystemGroupsMock          mGroupPrimeDBMockGetSystemGroups

	funcGetSystemGroupsWithStats          func() (ga1 []groupentity.GroupWithStats, err error)
	funcGetSystemGroupsWithStatsOrigin    string
	inspectFuncGetSystemGroupsWithStats   func()
	afterGetSystemGroupsWithStatsCounter  uint64
	beforeGetSystemGroupsWithStatsCounter uint64
	GetSystemGroupsWithStatsMock          mGroupPrimeDBMockGetSystemGroupsWithStats

	funcUpdate          func(group groupentity.GroupUpdateData) (g1 groupentity.Group, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(group groupentity.GroupUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mGroupPrimeDBMockUpdate
}

// NewGroupPrimeDBMock returns a mock for mm_repository.GroupPrimeDB
func NewGroupPrimeDBMock(t minimock.Tester) *GroupPrimeDBMock {
	m := &GroupPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mGroupPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*GroupPrimeDBMockCreateParams{}

	m.DeactivateByIDsMock = mGroupPrimeDBMockDeactivateByIDs{mock: m}
	m.DeactivateByIDsMock.callArgs = []*GroupPrimeDBMockDeactivateByIDsParams{}

	m.DeleteGroupMock = mGroupPrimeDBMockDeleteGroup{mock: m}
	m.DeleteGroupMock.callArgs = []*GroupPrimeDBMockDeleteGroupParams{}

	m.ExistByNameMock = mGroupPrimeDBMockExistByName{mock: m}
	m.ExistByNameMock.callArgs = []*GroupPrimeDBMockExistByNameParams{}

	m.GetAllMock = mGroupPrimeDBMockGetAll{mock: m}

	m.GetAllByUserIDMock = mGroupPrimeDBMockGetAllByUserID{mock: m}
	m.GetAllByUserIDMock.callArgs = []*GroupPrimeDBMockGetAllByUserIDParams{}

	m.GetAllWithProductIDByUserIDMock = mGroupPrimeDBMockGetAllWithProductIDByUserID{mock: m}
	m.GetAllWithProductIDByUserIDMock.callArgs = []*GroupPrimeDBMockGetAllWithProductIDByUserIDParams{}

	m.GetByFiltersAndPaginationMock = mGroupPrimeDBMockGetByFiltersAndPagination{mock: m}
	m.GetByFiltersAndPaginationMock.callArgs = []*GroupPrimeDBMockGetByFiltersAndPaginationParams{}

	m.GetByGroupIDAndProductIDMock = mGroupPrimeDBMockGetByGroupIDAndProductID{mock: m}
	m.GetByGroupIDAndProductIDMock.callArgs = []*GroupPrimeDBMockGetByGroupIDAndProductIDParams{}

	m.GetByIDMock = mGroupPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*GroupPrimeDBMockGetByIDParams{}

	m.GetByProductIDMock = mGroupPrimeDBMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*GroupPrimeDBMockGetByProductIDParams{}

	m.GetByProductIDAndIsActiveMock = mGroupPrimeDBMockGetByProductIDAndIsActive{mock: m}
	m.GetByProductIDAndIsActiveMock.callArgs = []*GroupPrimeDBMockGetByProductIDAndIsActiveParams{}

	m.GetGroupCategoryStatesMock = mGroupPrimeDBMockGetGroupCategoryStates{mock: m}

	m.GetSystemGroupsMock = mGroupPrimeDBMockGetSystemGroups{mock: m}

	m.GetSystemGroupsWithStatsMock = mGroupPrimeDBMockGetSystemGroupsWithStats{mock: m}

	m.UpdateMock = mGroupPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*GroupPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mGroupPrimeDBMockCreate struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockCreateExpectation
	expectations       []*GroupPrimeDBMockCreateExpectation

	callArgs []*GroupPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockCreateExpectation specifies expectation struct of the GroupPrimeDB.Create
type GroupPrimeDBMockCreateExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockCreateParams
	paramPtrs          *GroupPrimeDBMockCreateParamPtrs
	expectationOrigins GroupPrimeDBMockCreateExpectationOrigins
	results            *GroupPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockCreateParams contains parameters of the GroupPrimeDB.Create
type GroupPrimeDBMockCreateParams struct {
	group groupentity.Group
}

// GroupPrimeDBMockCreateParamPtrs contains pointers to parameters of the GroupPrimeDB.Create
type GroupPrimeDBMockCreateParamPtrs struct {
	group *groupentity.Group
}

// GroupPrimeDBMockCreateResults contains results of the GroupPrimeDB.Create
type GroupPrimeDBMockCreateResults struct {
	g1  groupentity.Group
	err error
}

// GroupPrimeDBMockCreateOrigins contains origins of expectations of the GroupPrimeDB.Create
type GroupPrimeDBMockCreateExpectationOrigins struct {
	origin      string
	originGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mGroupPrimeDBMockCreate) Optional() *mGroupPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for GroupPrimeDB.Create
func (mmCreate *mGroupPrimeDBMockCreate) Expect(group groupentity.Group) *mGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("GroupPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &GroupPrimeDBMockCreateParams{group}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectGroupParam1 sets up expected param group for GroupPrimeDB.Create
func (mmCreate *mGroupPrimeDBMockCreate) ExpectGroupParam1(group groupentity.Group) *mGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("GroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &GroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.group = &group
	mmCreate.defaultExpectation.expectationOrigins.originGroup = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.Create
func (mmCreate *mGroupPrimeDBMockCreate) Inspect(f func(group groupentity.Group)) *mGroupPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by GroupPrimeDB.Create
func (mmCreate *mGroupPrimeDBMockCreate) Return(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &GroupPrimeDBMockCreateResults{g1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the GroupPrimeDB.Create method
func (mmCreate *mGroupPrimeDBMockCreate) Set(f func(group groupentity.Group) (g1 groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the GroupPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mGroupPrimeDBMockCreate) When(group groupentity.Group) *GroupPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &GroupPrimeDBMockCreateParams{group},
		expectationOrigins: GroupPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockCreateExpectation) Then(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockCreateResults{g1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.Create should be invoked
func (mmCreate *mGroupPrimeDBMockCreate) Times(n uint64) *mGroupPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of GroupPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mGroupPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.GroupPrimeDB
func (mmCreate *GroupPrimeDBMock) Create(group groupentity.Group) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(group)
	}

	mm_params := GroupPrimeDBMockCreateParams{group}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockCreateParams{group}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.group != nil && !minimock.Equal(*mm_want_ptrs.group, mm_got.group) {
				mmCreate.t.Errorf("GroupPrimeDBMock.Create got unexpected parameter group, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originGroup, *mm_want_ptrs.group, mm_got.group, minimock.Diff(*mm_want_ptrs.group, mm_got.group))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("GroupPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the GroupPrimeDBMock.Create")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(group)
	}
	mmCreate.t.Fatalf("Unexpected call to GroupPrimeDBMock.Create. %v", group)
	return
}

// CreateAfterCounter returns a count of finished GroupPrimeDBMock.Create invocations
func (mmCreate *GroupPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of GroupPrimeDBMock.Create invocations
func (mmCreate *GroupPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mGroupPrimeDBMockCreate) Calls() []*GroupPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mGroupPrimeDBMockDeactivateByIDs struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockDeactivateByIDsExpectation
	expectations       []*GroupPrimeDBMockDeactivateByIDsExpectation

	callArgs []*GroupPrimeDBMockDeactivateByIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockDeactivateByIDsExpectation specifies expectation struct of the GroupPrimeDB.DeactivateByIDs
type GroupPrimeDBMockDeactivateByIDsExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockDeactivateByIDsParams
	paramPtrs          *GroupPrimeDBMockDeactivateByIDsParamPtrs
	expectationOrigins GroupPrimeDBMockDeactivateByIDsExpectationOrigins
	results            *GroupPrimeDBMockDeactivateByIDsResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockDeactivateByIDsParams contains parameters of the GroupPrimeDB.DeactivateByIDs
type GroupPrimeDBMockDeactivateByIDsParams struct {
	groupIDs []int64
}

// GroupPrimeDBMockDeactivateByIDsParamPtrs contains pointers to parameters of the GroupPrimeDB.DeactivateByIDs
type GroupPrimeDBMockDeactivateByIDsParamPtrs struct {
	groupIDs *[]int64
}

// GroupPrimeDBMockDeactivateByIDsResults contains results of the GroupPrimeDB.DeactivateByIDs
type GroupPrimeDBMockDeactivateByIDsResults struct {
	err error
}

// GroupPrimeDBMockDeactivateByIDsOrigins contains origins of expectations of the GroupPrimeDB.DeactivateByIDs
type GroupPrimeDBMockDeactivateByIDsExpectationOrigins struct {
	origin         string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Optional() *mGroupPrimeDBMockDeactivateByIDs {
	mmDeactivateByIDs.optional = true
	return mmDeactivateByIDs
}

// Expect sets up expected params for GroupPrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Expect(groupIDs []int64) *mGroupPrimeDBMockDeactivateByIDs {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("GroupPrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	if mmDeactivateByIDs.defaultExpectation == nil {
		mmDeactivateByIDs.defaultExpectation = &GroupPrimeDBMockDeactivateByIDsExpectation{}
	}

	if mmDeactivateByIDs.defaultExpectation.paramPtrs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("GroupPrimeDBMock.DeactivateByIDs mock is already set by ExpectParams functions")
	}

	mmDeactivateByIDs.defaultExpectation.params = &GroupPrimeDBMockDeactivateByIDsParams{groupIDs}
	mmDeactivateByIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeactivateByIDs.expectations {
		if minimock.Equal(e.params, mmDeactivateByIDs.defaultExpectation.params) {
			mmDeactivateByIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeactivateByIDs.defaultExpectation.params)
		}
	}

	return mmDeactivateByIDs
}

// ExpectGroupIDsParam1 sets up expected param groupIDs for GroupPrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) ExpectGroupIDsParam1(groupIDs []int64) *mGroupPrimeDBMockDeactivateByIDs {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("GroupPrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	if mmDeactivateByIDs.defaultExpectation == nil {
		mmDeactivateByIDs.defaultExpectation = &GroupPrimeDBMockDeactivateByIDsExpectation{}
	}

	if mmDeactivateByIDs.defaultExpectation.params != nil {
		mmDeactivateByIDs.mock.t.Fatalf("GroupPrimeDBMock.DeactivateByIDs mock is already set by Expect")
	}

	if mmDeactivateByIDs.defaultExpectation.paramPtrs == nil {
		mmDeactivateByIDs.defaultExpectation.paramPtrs = &GroupPrimeDBMockDeactivateByIDsParamPtrs{}
	}
	mmDeactivateByIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmDeactivateByIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmDeactivateByIDs
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Inspect(f func(groupIDs []int64)) *mGroupPrimeDBMockDeactivateByIDs {
	if mmDeactivateByIDs.mock.inspectFuncDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.DeactivateByIDs")
	}

	mmDeactivateByIDs.mock.inspectFuncDeactivateByIDs = f

	return mmDeactivateByIDs
}

// Return sets up results that will be returned by GroupPrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Return(err error) *GroupPrimeDBMock {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("GroupPrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	if mmDeactivateByIDs.defaultExpectation == nil {
		mmDeactivateByIDs.defaultExpectation = &GroupPrimeDBMockDeactivateByIDsExpectation{mock: mmDeactivateByIDs.mock}
	}
	mmDeactivateByIDs.defaultExpectation.results = &GroupPrimeDBMockDeactivateByIDsResults{err}
	mmDeactivateByIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeactivateByIDs.mock
}

// Set uses given function f to mock the GroupPrimeDB.DeactivateByIDs method
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Set(f func(groupIDs []int64) (err error)) *GroupPrimeDBMock {
	if mmDeactivateByIDs.defaultExpectation != nil {
		mmDeactivateByIDs.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.DeactivateByIDs method")
	}

	if len(mmDeactivateByIDs.expectations) > 0 {
		mmDeactivateByIDs.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.DeactivateByIDs method")
	}

	mmDeactivateByIDs.mock.funcDeactivateByIDs = f
	mmDeactivateByIDs.mock.funcDeactivateByIDsOrigin = minimock.CallerInfo(1)
	return mmDeactivateByIDs.mock
}

// When sets expectation for the GroupPrimeDB.DeactivateByIDs which will trigger the result defined by the following
// Then helper
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) When(groupIDs []int64) *GroupPrimeDBMockDeactivateByIDsExpectation {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("GroupPrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockDeactivateByIDsExpectation{
		mock:               mmDeactivateByIDs.mock,
		params:             &GroupPrimeDBMockDeactivateByIDsParams{groupIDs},
		expectationOrigins: GroupPrimeDBMockDeactivateByIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeactivateByIDs.expectations = append(mmDeactivateByIDs.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.DeactivateByIDs return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockDeactivateByIDsExpectation) Then(err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockDeactivateByIDsResults{err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.DeactivateByIDs should be invoked
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Times(n uint64) *mGroupPrimeDBMockDeactivateByIDs {
	if n == 0 {
		mmDeactivateByIDs.mock.t.Fatalf("Times of GroupPrimeDBMock.DeactivateByIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeactivateByIDs.expectedInvocations, n)
	mmDeactivateByIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeactivateByIDs
}

func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) invocationsDone() bool {
	if len(mmDeactivateByIDs.expectations) == 0 && mmDeactivateByIDs.defaultExpectation == nil && mmDeactivateByIDs.mock.funcDeactivateByIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeactivateByIDs.mock.afterDeactivateByIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeactivateByIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeactivateByIDs implements mm_repository.GroupPrimeDB
func (mmDeactivateByIDs *GroupPrimeDBMock) DeactivateByIDs(groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeactivateByIDs.beforeDeactivateByIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeactivateByIDs.afterDeactivateByIDsCounter, 1)

	mmDeactivateByIDs.t.Helper()

	if mmDeactivateByIDs.inspectFuncDeactivateByIDs != nil {
		mmDeactivateByIDs.inspectFuncDeactivateByIDs(groupIDs)
	}

	mm_params := GroupPrimeDBMockDeactivateByIDsParams{groupIDs}

	// Record call args
	mmDeactivateByIDs.DeactivateByIDsMock.mutex.Lock()
	mmDeactivateByIDs.DeactivateByIDsMock.callArgs = append(mmDeactivateByIDs.DeactivateByIDsMock.callArgs, &mm_params)
	mmDeactivateByIDs.DeactivateByIDsMock.mutex.Unlock()

	for _, e := range mmDeactivateByIDs.DeactivateByIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockDeactivateByIDsParams{groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmDeactivateByIDs.t.Errorf("GroupPrimeDBMock.DeactivateByIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeactivateByIDs.t.Errorf("GroupPrimeDBMock.DeactivateByIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeactivateByIDs.t.Fatal("No results are set for the GroupPrimeDBMock.DeactivateByIDs")
		}
		return (*mm_results).err
	}
	if mmDeactivateByIDs.funcDeactivateByIDs != nil {
		return mmDeactivateByIDs.funcDeactivateByIDs(groupIDs)
	}
	mmDeactivateByIDs.t.Fatalf("Unexpected call to GroupPrimeDBMock.DeactivateByIDs. %v", groupIDs)
	return
}

// DeactivateByIDsAfterCounter returns a count of finished GroupPrimeDBMock.DeactivateByIDs invocations
func (mmDeactivateByIDs *GroupPrimeDBMock) DeactivateByIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeactivateByIDs.afterDeactivateByIDsCounter)
}

// DeactivateByIDsBeforeCounter returns a count of GroupPrimeDBMock.DeactivateByIDs invocations
func (mmDeactivateByIDs *GroupPrimeDBMock) DeactivateByIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeactivateByIDs.beforeDeactivateByIDsCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.DeactivateByIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeactivateByIDs *mGroupPrimeDBMockDeactivateByIDs) Calls() []*GroupPrimeDBMockDeactivateByIDsParams {
	mmDeactivateByIDs.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockDeactivateByIDsParams, len(mmDeactivateByIDs.callArgs))
	copy(argCopy, mmDeactivateByIDs.callArgs)

	mmDeactivateByIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeactivateByIDsDone returns true if the count of the DeactivateByIDs invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockDeactivateByIDsDone() bool {
	if m.DeactivateByIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeactivateByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeactivateByIDsMock.invocationsDone()
}

// MinimockDeactivateByIDsInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockDeactivateByIDsInspect() {
	for _, e := range m.DeactivateByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.DeactivateByIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeactivateByIDsCounter := mm_atomic.LoadUint64(&m.afterDeactivateByIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeactivateByIDsMock.defaultExpectation != nil && afterDeactivateByIDsCounter < 1 {
		if m.DeactivateByIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.DeactivateByIDs at\n%s", m.DeactivateByIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.DeactivateByIDs at\n%s with params: %#v", m.DeactivateByIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeactivateByIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeactivateByIDs != nil && afterDeactivateByIDsCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.DeactivateByIDs at\n%s", m.funcDeactivateByIDsOrigin)
	}

	if !m.DeactivateByIDsMock.invocationsDone() && afterDeactivateByIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.DeactivateByIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeactivateByIDsMock.expectedInvocations), m.DeactivateByIDsMock.expectedInvocationsOrigin, afterDeactivateByIDsCounter)
	}
}

type mGroupPrimeDBMockDeleteGroup struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockDeleteGroupExpectation
	expectations       []*GroupPrimeDBMockDeleteGroupExpectation

	callArgs []*GroupPrimeDBMockDeleteGroupParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockDeleteGroupExpectation specifies expectation struct of the GroupPrimeDB.DeleteGroup
type GroupPrimeDBMockDeleteGroupExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockDeleteGroupParams
	paramPtrs          *GroupPrimeDBMockDeleteGroupParamPtrs
	expectationOrigins GroupPrimeDBMockDeleteGroupExpectationOrigins
	results            *GroupPrimeDBMockDeleteGroupResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockDeleteGroupParams contains parameters of the GroupPrimeDB.DeleteGroup
type GroupPrimeDBMockDeleteGroupParams struct {
	groupID int64
}

// GroupPrimeDBMockDeleteGroupParamPtrs contains pointers to parameters of the GroupPrimeDB.DeleteGroup
type GroupPrimeDBMockDeleteGroupParamPtrs struct {
	groupID *int64
}

// GroupPrimeDBMockDeleteGroupResults contains results of the GroupPrimeDB.DeleteGroup
type GroupPrimeDBMockDeleteGroupResults struct {
	err error
}

// GroupPrimeDBMockDeleteGroupOrigins contains origins of expectations of the GroupPrimeDB.DeleteGroup
type GroupPrimeDBMockDeleteGroupExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Optional() *mGroupPrimeDBMockDeleteGroup {
	mmDeleteGroup.optional = true
	return mmDeleteGroup
}

// Expect sets up expected params for GroupPrimeDB.DeleteGroup
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Expect(groupID int64) *mGroupPrimeDBMockDeleteGroup {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupPrimeDBMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupPrimeDBMockDeleteGroupExpectation{}
	}

	if mmDeleteGroup.defaultExpectation.paramPtrs != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupPrimeDBMock.DeleteGroup mock is already set by ExpectParams functions")
	}

	mmDeleteGroup.defaultExpectation.params = &GroupPrimeDBMockDeleteGroupParams{groupID}
	mmDeleteGroup.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteGroup.expectations {
		if minimock.Equal(e.params, mmDeleteGroup.defaultExpectation.params) {
			mmDeleteGroup.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteGroup.defaultExpectation.params)
		}
	}

	return mmDeleteGroup
}

// ExpectGroupIDParam1 sets up expected param groupID for GroupPrimeDB.DeleteGroup
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) ExpectGroupIDParam1(groupID int64) *mGroupPrimeDBMockDeleteGroup {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupPrimeDBMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupPrimeDBMockDeleteGroupExpectation{}
	}

	if mmDeleteGroup.defaultExpectation.params != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupPrimeDBMock.DeleteGroup mock is already set by Expect")
	}

	if mmDeleteGroup.defaultExpectation.paramPtrs == nil {
		mmDeleteGroup.defaultExpectation.paramPtrs = &GroupPrimeDBMockDeleteGroupParamPtrs{}
	}
	mmDeleteGroup.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteGroup.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteGroup
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.DeleteGroup
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Inspect(f func(groupID int64)) *mGroupPrimeDBMockDeleteGroup {
	if mmDeleteGroup.mock.inspectFuncDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.DeleteGroup")
	}

	mmDeleteGroup.mock.inspectFuncDeleteGroup = f

	return mmDeleteGroup
}

// Return sets up results that will be returned by GroupPrimeDB.DeleteGroup
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Return(err error) *GroupPrimeDBMock {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupPrimeDBMock.DeleteGroup mock is already set by Set")
	}

	if mmDeleteGroup.defaultExpectation == nil {
		mmDeleteGroup.defaultExpectation = &GroupPrimeDBMockDeleteGroupExpectation{mock: mmDeleteGroup.mock}
	}
	mmDeleteGroup.defaultExpectation.results = &GroupPrimeDBMockDeleteGroupResults{err}
	mmDeleteGroup.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteGroup.mock
}

// Set uses given function f to mock the GroupPrimeDB.DeleteGroup method
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Set(f func(groupID int64) (err error)) *GroupPrimeDBMock {
	if mmDeleteGroup.defaultExpectation != nil {
		mmDeleteGroup.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.DeleteGroup method")
	}

	if len(mmDeleteGroup.expectations) > 0 {
		mmDeleteGroup.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.DeleteGroup method")
	}

	mmDeleteGroup.mock.funcDeleteGroup = f
	mmDeleteGroup.mock.funcDeleteGroupOrigin = minimock.CallerInfo(1)
	return mmDeleteGroup.mock
}

// When sets expectation for the GroupPrimeDB.DeleteGroup which will trigger the result defined by the following
// Then helper
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) When(groupID int64) *GroupPrimeDBMockDeleteGroupExpectation {
	if mmDeleteGroup.mock.funcDeleteGroup != nil {
		mmDeleteGroup.mock.t.Fatalf("GroupPrimeDBMock.DeleteGroup mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockDeleteGroupExpectation{
		mock:               mmDeleteGroup.mock,
		params:             &GroupPrimeDBMockDeleteGroupParams{groupID},
		expectationOrigins: GroupPrimeDBMockDeleteGroupExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteGroup.expectations = append(mmDeleteGroup.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.DeleteGroup return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockDeleteGroupExpectation) Then(err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockDeleteGroupResults{err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.DeleteGroup should be invoked
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Times(n uint64) *mGroupPrimeDBMockDeleteGroup {
	if n == 0 {
		mmDeleteGroup.mock.t.Fatalf("Times of GroupPrimeDBMock.DeleteGroup mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteGroup.expectedInvocations, n)
	mmDeleteGroup.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteGroup
}

func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) invocationsDone() bool {
	if len(mmDeleteGroup.expectations) == 0 && mmDeleteGroup.defaultExpectation == nil && mmDeleteGroup.mock.funcDeleteGroup == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteGroup.mock.afterDeleteGroupCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteGroup.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteGroup implements mm_repository.GroupPrimeDB
func (mmDeleteGroup *GroupPrimeDBMock) DeleteGroup(groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteGroup.beforeDeleteGroupCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteGroup.afterDeleteGroupCounter, 1)

	mmDeleteGroup.t.Helper()

	if mmDeleteGroup.inspectFuncDeleteGroup != nil {
		mmDeleteGroup.inspectFuncDeleteGroup(groupID)
	}

	mm_params := GroupPrimeDBMockDeleteGroupParams{groupID}

	// Record call args
	mmDeleteGroup.DeleteGroupMock.mutex.Lock()
	mmDeleteGroup.DeleteGroupMock.callArgs = append(mmDeleteGroup.DeleteGroupMock.callArgs, &mm_params)
	mmDeleteGroup.DeleteGroupMock.mutex.Unlock()

	for _, e := range mmDeleteGroup.DeleteGroupMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteGroup.DeleteGroupMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteGroup.DeleteGroupMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteGroup.DeleteGroupMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteGroup.DeleteGroupMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockDeleteGroupParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteGroup.t.Errorf("GroupPrimeDBMock.DeleteGroup got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteGroup.DeleteGroupMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteGroup.t.Errorf("GroupPrimeDBMock.DeleteGroup got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteGroup.DeleteGroupMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteGroup.DeleteGroupMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteGroup.t.Fatal("No results are set for the GroupPrimeDBMock.DeleteGroup")
		}
		return (*mm_results).err
	}
	if mmDeleteGroup.funcDeleteGroup != nil {
		return mmDeleteGroup.funcDeleteGroup(groupID)
	}
	mmDeleteGroup.t.Fatalf("Unexpected call to GroupPrimeDBMock.DeleteGroup. %v", groupID)
	return
}

// DeleteGroupAfterCounter returns a count of finished GroupPrimeDBMock.DeleteGroup invocations
func (mmDeleteGroup *GroupPrimeDBMock) DeleteGroupAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteGroup.afterDeleteGroupCounter)
}

// DeleteGroupBeforeCounter returns a count of GroupPrimeDBMock.DeleteGroup invocations
func (mmDeleteGroup *GroupPrimeDBMock) DeleteGroupBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteGroup.beforeDeleteGroupCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.DeleteGroup.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteGroup *mGroupPrimeDBMockDeleteGroup) Calls() []*GroupPrimeDBMockDeleteGroupParams {
	mmDeleteGroup.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockDeleteGroupParams, len(mmDeleteGroup.callArgs))
	copy(argCopy, mmDeleteGroup.callArgs)

	mmDeleteGroup.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteGroupDone returns true if the count of the DeleteGroup invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockDeleteGroupDone() bool {
	if m.DeleteGroupMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteGroupMock.invocationsDone()
}

// MinimockDeleteGroupInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockDeleteGroupInspect() {
	for _, e := range m.DeleteGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.DeleteGroup at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteGroupCounter := mm_atomic.LoadUint64(&m.afterDeleteGroupCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteGroupMock.defaultExpectation != nil && afterDeleteGroupCounter < 1 {
		if m.DeleteGroupMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.DeleteGroup at\n%s", m.DeleteGroupMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.DeleteGroup at\n%s with params: %#v", m.DeleteGroupMock.defaultExpectation.expectationOrigins.origin, *m.DeleteGroupMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteGroup != nil && afterDeleteGroupCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.DeleteGroup at\n%s", m.funcDeleteGroupOrigin)
	}

	if !m.DeleteGroupMock.invocationsDone() && afterDeleteGroupCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.DeleteGroup at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteGroupMock.expectedInvocations), m.DeleteGroupMock.expectedInvocationsOrigin, afterDeleteGroupCounter)
	}
}

type mGroupPrimeDBMockExistByName struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockExistByNameExpectation
	expectations       []*GroupPrimeDBMockExistByNameExpectation

	callArgs []*GroupPrimeDBMockExistByNameParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockExistByNameExpectation specifies expectation struct of the GroupPrimeDB.ExistByName
type GroupPrimeDBMockExistByNameExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockExistByNameParams
	paramPtrs          *GroupPrimeDBMockExistByNameParamPtrs
	expectationOrigins GroupPrimeDBMockExistByNameExpectationOrigins
	results            *GroupPrimeDBMockExistByNameResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockExistByNameParams contains parameters of the GroupPrimeDB.ExistByName
type GroupPrimeDBMockExistByNameParams struct {
	name string
}

// GroupPrimeDBMockExistByNameParamPtrs contains pointers to parameters of the GroupPrimeDB.ExistByName
type GroupPrimeDBMockExistByNameParamPtrs struct {
	name *string
}

// GroupPrimeDBMockExistByNameResults contains results of the GroupPrimeDB.ExistByName
type GroupPrimeDBMockExistByNameResults struct {
	b1  bool
	err error
}

// GroupPrimeDBMockExistByNameOrigins contains origins of expectations of the GroupPrimeDB.ExistByName
type GroupPrimeDBMockExistByNameExpectationOrigins struct {
	origin     string
	originName string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByName *mGroupPrimeDBMockExistByName) Optional() *mGroupPrimeDBMockExistByName {
	mmExistByName.optional = true
	return mmExistByName
}

// Expect sets up expected params for GroupPrimeDB.ExistByName
func (mmExistByName *mGroupPrimeDBMockExistByName) Expect(name string) *mGroupPrimeDBMockExistByName {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("GroupPrimeDBMock.ExistByName mock is already set by Set")
	}

	if mmExistByName.defaultExpectation == nil {
		mmExistByName.defaultExpectation = &GroupPrimeDBMockExistByNameExpectation{}
	}

	if mmExistByName.defaultExpectation.paramPtrs != nil {
		mmExistByName.mock.t.Fatalf("GroupPrimeDBMock.ExistByName mock is already set by ExpectParams functions")
	}

	mmExistByName.defaultExpectation.params = &GroupPrimeDBMockExistByNameParams{name}
	mmExistByName.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByName.expectations {
		if minimock.Equal(e.params, mmExistByName.defaultExpectation.params) {
			mmExistByName.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByName.defaultExpectation.params)
		}
	}

	return mmExistByName
}

// ExpectNameParam1 sets up expected param name for GroupPrimeDB.ExistByName
func (mmExistByName *mGroupPrimeDBMockExistByName) ExpectNameParam1(name string) *mGroupPrimeDBMockExistByName {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("GroupPrimeDBMock.ExistByName mock is already set by Set")
	}

	if mmExistByName.defaultExpectation == nil {
		mmExistByName.defaultExpectation = &GroupPrimeDBMockExistByNameExpectation{}
	}

	if mmExistByName.defaultExpectation.params != nil {
		mmExistByName.mock.t.Fatalf("GroupPrimeDBMock.ExistByName mock is already set by Expect")
	}

	if mmExistByName.defaultExpectation.paramPtrs == nil {
		mmExistByName.defaultExpectation.paramPtrs = &GroupPrimeDBMockExistByNameParamPtrs{}
	}
	mmExistByName.defaultExpectation.paramPtrs.name = &name
	mmExistByName.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmExistByName
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.ExistByName
func (mmExistByName *mGroupPrimeDBMockExistByName) Inspect(f func(name string)) *mGroupPrimeDBMockExistByName {
	if mmExistByName.mock.inspectFuncExistByName != nil {
		mmExistByName.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.ExistByName")
	}

	mmExistByName.mock.inspectFuncExistByName = f

	return mmExistByName
}

// Return sets up results that will be returned by GroupPrimeDB.ExistByName
func (mmExistByName *mGroupPrimeDBMockExistByName) Return(b1 bool, err error) *GroupPrimeDBMock {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("GroupPrimeDBMock.ExistByName mock is already set by Set")
	}

	if mmExistByName.defaultExpectation == nil {
		mmExistByName.defaultExpectation = &GroupPrimeDBMockExistByNameExpectation{mock: mmExistByName.mock}
	}
	mmExistByName.defaultExpectation.results = &GroupPrimeDBMockExistByNameResults{b1, err}
	mmExistByName.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByName.mock
}

// Set uses given function f to mock the GroupPrimeDB.ExistByName method
func (mmExistByName *mGroupPrimeDBMockExistByName) Set(f func(name string) (b1 bool, err error)) *GroupPrimeDBMock {
	if mmExistByName.defaultExpectation != nil {
		mmExistByName.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.ExistByName method")
	}

	if len(mmExistByName.expectations) > 0 {
		mmExistByName.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.ExistByName method")
	}

	mmExistByName.mock.funcExistByName = f
	mmExistByName.mock.funcExistByNameOrigin = minimock.CallerInfo(1)
	return mmExistByName.mock
}

// When sets expectation for the GroupPrimeDB.ExistByName which will trigger the result defined by the following
// Then helper
func (mmExistByName *mGroupPrimeDBMockExistByName) When(name string) *GroupPrimeDBMockExistByNameExpectation {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("GroupPrimeDBMock.ExistByName mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockExistByNameExpectation{
		mock:               mmExistByName.mock,
		params:             &GroupPrimeDBMockExistByNameParams{name},
		expectationOrigins: GroupPrimeDBMockExistByNameExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByName.expectations = append(mmExistByName.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.ExistByName return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockExistByNameExpectation) Then(b1 bool, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockExistByNameResults{b1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.ExistByName should be invoked
func (mmExistByName *mGroupPrimeDBMockExistByName) Times(n uint64) *mGroupPrimeDBMockExistByName {
	if n == 0 {
		mmExistByName.mock.t.Fatalf("Times of GroupPrimeDBMock.ExistByName mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByName.expectedInvocations, n)
	mmExistByName.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByName
}

func (mmExistByName *mGroupPrimeDBMockExistByName) invocationsDone() bool {
	if len(mmExistByName.expectations) == 0 && mmExistByName.defaultExpectation == nil && mmExistByName.mock.funcExistByName == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByName.mock.afterExistByNameCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByName.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByName implements mm_repository.GroupPrimeDB
func (mmExistByName *GroupPrimeDBMock) ExistByName(name string) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByName.beforeExistByNameCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByName.afterExistByNameCounter, 1)

	mmExistByName.t.Helper()

	if mmExistByName.inspectFuncExistByName != nil {
		mmExistByName.inspectFuncExistByName(name)
	}

	mm_params := GroupPrimeDBMockExistByNameParams{name}

	// Record call args
	mmExistByName.ExistByNameMock.mutex.Lock()
	mmExistByName.ExistByNameMock.callArgs = append(mmExistByName.ExistByNameMock.callArgs, &mm_params)
	mmExistByName.ExistByNameMock.mutex.Unlock()

	for _, e := range mmExistByName.ExistByNameMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByName.ExistByNameMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByName.ExistByNameMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByName.ExistByNameMock.defaultExpectation.params
		mm_want_ptrs := mmExistByName.ExistByNameMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockExistByNameParams{name}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmExistByName.t.Errorf("GroupPrimeDBMock.ExistByName got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByName.ExistByNameMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByName.t.Errorf("GroupPrimeDBMock.ExistByName got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByName.ExistByNameMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByName.ExistByNameMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByName.t.Fatal("No results are set for the GroupPrimeDBMock.ExistByName")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByName.funcExistByName != nil {
		return mmExistByName.funcExistByName(name)
	}
	mmExistByName.t.Fatalf("Unexpected call to GroupPrimeDBMock.ExistByName. %v", name)
	return
}

// ExistByNameAfterCounter returns a count of finished GroupPrimeDBMock.ExistByName invocations
func (mmExistByName *GroupPrimeDBMock) ExistByNameAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByName.afterExistByNameCounter)
}

// ExistByNameBeforeCounter returns a count of GroupPrimeDBMock.ExistByName invocations
func (mmExistByName *GroupPrimeDBMock) ExistByNameBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByName.beforeExistByNameCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.ExistByName.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByName *mGroupPrimeDBMockExistByName) Calls() []*GroupPrimeDBMockExistByNameParams {
	mmExistByName.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockExistByNameParams, len(mmExistByName.callArgs))
	copy(argCopy, mmExistByName.callArgs)

	mmExistByName.mutex.RUnlock()

	return argCopy
}

// MinimockExistByNameDone returns true if the count of the ExistByName invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockExistByNameDone() bool {
	if m.ExistByNameMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByNameMock.invocationsDone()
}

// MinimockExistByNameInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockExistByNameInspect() {
	for _, e := range m.ExistByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.ExistByName at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByNameCounter := mm_atomic.LoadUint64(&m.afterExistByNameCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByNameMock.defaultExpectation != nil && afterExistByNameCounter < 1 {
		if m.ExistByNameMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.ExistByName at\n%s", m.ExistByNameMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.ExistByName at\n%s with params: %#v", m.ExistByNameMock.defaultExpectation.expectationOrigins.origin, *m.ExistByNameMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByName != nil && afterExistByNameCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.ExistByName at\n%s", m.funcExistByNameOrigin)
	}

	if !m.ExistByNameMock.invocationsDone() && afterExistByNameCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.ExistByName at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByNameMock.expectedInvocations), m.ExistByNameMock.expectedInvocationsOrigin, afterExistByNameCounter)
	}
}

type mGroupPrimeDBMockGetAll struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetAllExpectation
	expectations       []*GroupPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetAllExpectation specifies expectation struct of the GroupPrimeDB.GetAll
type GroupPrimeDBMockGetAllExpectation struct {
	mock *GroupPrimeDBMock

	results      *GroupPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// GroupPrimeDBMockGetAllResults contains results of the GroupPrimeDB.GetAll
type GroupPrimeDBMockGetAllResults struct {
	ga1 []groupentity.Group
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mGroupPrimeDBMockGetAll) Optional() *mGroupPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for GroupPrimeDB.GetAll
func (mmGetAll *mGroupPrimeDBMockGetAll) Expect() *mGroupPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("GroupPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &GroupPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetAll
func (mmGetAll *mGroupPrimeDBMockGetAll) Inspect(f func()) *mGroupPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by GroupPrimeDB.GetAll
func (mmGetAll *mGroupPrimeDBMockGetAll) Return(ga1 []groupentity.Group, err error) *GroupPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("GroupPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &GroupPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &GroupPrimeDBMockGetAllResults{ga1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetAll method
func (mmGetAll *mGroupPrimeDBMockGetAll) Set(f func() (ga1 []groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times GroupPrimeDB.GetAll should be invoked
func (mmGetAll *mGroupPrimeDBMockGetAll) Times(n uint64) *mGroupPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of GroupPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mGroupPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.GroupPrimeDB
func (mmGetAll *GroupPrimeDBMock) GetAll() (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the GroupPrimeDBMock.GetAll")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished GroupPrimeDBMock.GetAll invocations
func (mmGetAll *GroupPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of GroupPrimeDBMock.GetAll invocations
func (mmGetAll *GroupPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mGroupPrimeDBMockGetAllByUserID struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetAllByUserIDExpectation
	expectations       []*GroupPrimeDBMockGetAllByUserIDExpectation

	callArgs []*GroupPrimeDBMockGetAllByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetAllByUserIDExpectation specifies expectation struct of the GroupPrimeDB.GetAllByUserID
type GroupPrimeDBMockGetAllByUserIDExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetAllByUserIDParams
	paramPtrs          *GroupPrimeDBMockGetAllByUserIDParamPtrs
	expectationOrigins GroupPrimeDBMockGetAllByUserIDExpectationOrigins
	results            *GroupPrimeDBMockGetAllByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetAllByUserIDParams contains parameters of the GroupPrimeDB.GetAllByUserID
type GroupPrimeDBMockGetAllByUserIDParams struct {
	userID int64
}

// GroupPrimeDBMockGetAllByUserIDParamPtrs contains pointers to parameters of the GroupPrimeDB.GetAllByUserID
type GroupPrimeDBMockGetAllByUserIDParamPtrs struct {
	userID *int64
}

// GroupPrimeDBMockGetAllByUserIDResults contains results of the GroupPrimeDB.GetAllByUserID
type GroupPrimeDBMockGetAllByUserIDResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupPrimeDBMockGetAllByUserIDOrigins contains origins of expectations of the GroupPrimeDB.GetAllByUserID
type GroupPrimeDBMockGetAllByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Optional() *mGroupPrimeDBMockGetAllByUserID {
	mmGetAllByUserID.optional = true
	return mmGetAllByUserID
}

// Expect sets up expected params for GroupPrimeDB.GetAllByUserID
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Expect(userID int64) *mGroupPrimeDBMockGetAllByUserID {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	if mmGetAllByUserID.defaultExpectation == nil {
		mmGetAllByUserID.defaultExpectation = &GroupPrimeDBMockGetAllByUserIDExpectation{}
	}

	if mmGetAllByUserID.defaultExpectation.paramPtrs != nil {
		mmGetAllByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllByUserID mock is already set by ExpectParams functions")
	}

	mmGetAllByUserID.defaultExpectation.params = &GroupPrimeDBMockGetAllByUserIDParams{userID}
	mmGetAllByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAllByUserID.expectations {
		if minimock.Equal(e.params, mmGetAllByUserID.defaultExpectation.params) {
			mmGetAllByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAllByUserID.defaultExpectation.params)
		}
	}

	return mmGetAllByUserID
}

// ExpectUserIDParam1 sets up expected param userID for GroupPrimeDB.GetAllByUserID
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) ExpectUserIDParam1(userID int64) *mGroupPrimeDBMockGetAllByUserID {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	if mmGetAllByUserID.defaultExpectation == nil {
		mmGetAllByUserID.defaultExpectation = &GroupPrimeDBMockGetAllByUserIDExpectation{}
	}

	if mmGetAllByUserID.defaultExpectation.params != nil {
		mmGetAllByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllByUserID mock is already set by Expect")
	}

	if mmGetAllByUserID.defaultExpectation.paramPtrs == nil {
		mmGetAllByUserID.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetAllByUserIDParamPtrs{}
	}
	mmGetAllByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetAllByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetAllByUserID
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetAllByUserID
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Inspect(f func(userID int64)) *mGroupPrimeDBMockGetAllByUserID {
	if mmGetAllByUserID.mock.inspectFuncGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetAllByUserID")
	}

	mmGetAllByUserID.mock.inspectFuncGetAllByUserID = f

	return mmGetAllByUserID
}

// Return sets up results that will be returned by GroupPrimeDB.GetAllByUserID
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Return(ga1 []groupentity.Group, err error) *GroupPrimeDBMock {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	if mmGetAllByUserID.defaultExpectation == nil {
		mmGetAllByUserID.defaultExpectation = &GroupPrimeDBMockGetAllByUserIDExpectation{mock: mmGetAllByUserID.mock}
	}
	mmGetAllByUserID.defaultExpectation.results = &GroupPrimeDBMockGetAllByUserIDResults{ga1, err}
	mmGetAllByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllByUserID.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetAllByUserID method
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Set(f func(userID int64) (ga1 []groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmGetAllByUserID.defaultExpectation != nil {
		mmGetAllByUserID.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetAllByUserID method")
	}

	if len(mmGetAllByUserID.expectations) > 0 {
		mmGetAllByUserID.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetAllByUserID method")
	}

	mmGetAllByUserID.mock.funcGetAllByUserID = f
	mmGetAllByUserID.mock.funcGetAllByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetAllByUserID.mock
}

// When sets expectation for the GroupPrimeDB.GetAllByUserID which will trigger the result defined by the following
// Then helper
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) When(userID int64) *GroupPrimeDBMockGetAllByUserIDExpectation {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetAllByUserIDExpectation{
		mock:               mmGetAllByUserID.mock,
		params:             &GroupPrimeDBMockGetAllByUserIDParams{userID},
		expectationOrigins: GroupPrimeDBMockGetAllByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAllByUserID.expectations = append(mmGetAllByUserID.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetAllByUserID return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetAllByUserIDExpectation) Then(ga1 []groupentity.Group, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetAllByUserIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetAllByUserID should be invoked
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Times(n uint64) *mGroupPrimeDBMockGetAllByUserID {
	if n == 0 {
		mmGetAllByUserID.mock.t.Fatalf("Times of GroupPrimeDBMock.GetAllByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllByUserID.expectedInvocations, n)
	mmGetAllByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllByUserID
}

func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) invocationsDone() bool {
	if len(mmGetAllByUserID.expectations) == 0 && mmGetAllByUserID.defaultExpectation == nil && mmGetAllByUserID.mock.funcGetAllByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllByUserID.mock.afterGetAllByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllByUserID implements mm_repository.GroupPrimeDB
func (mmGetAllByUserID *GroupPrimeDBMock) GetAllByUserID(userID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetAllByUserID.beforeGetAllByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllByUserID.afterGetAllByUserIDCounter, 1)

	mmGetAllByUserID.t.Helper()

	if mmGetAllByUserID.inspectFuncGetAllByUserID != nil {
		mmGetAllByUserID.inspectFuncGetAllByUserID(userID)
	}

	mm_params := GroupPrimeDBMockGetAllByUserIDParams{userID}

	// Record call args
	mmGetAllByUserID.GetAllByUserIDMock.mutex.Lock()
	mmGetAllByUserID.GetAllByUserIDMock.callArgs = append(mmGetAllByUserID.GetAllByUserIDMock.callArgs, &mm_params)
	mmGetAllByUserID.GetAllByUserIDMock.mutex.Unlock()

	for _, e := range mmGetAllByUserID.GetAllByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetAllByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetAllByUserID.t.Errorf("GroupPrimeDBMock.GetAllByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAllByUserID.t.Errorf("GroupPrimeDBMock.GetAllByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllByUserID.t.Fatal("No results are set for the GroupPrimeDBMock.GetAllByUserID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetAllByUserID.funcGetAllByUserID != nil {
		return mmGetAllByUserID.funcGetAllByUserID(userID)
	}
	mmGetAllByUserID.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetAllByUserID. %v", userID)
	return
}

// GetAllByUserIDAfterCounter returns a count of finished GroupPrimeDBMock.GetAllByUserID invocations
func (mmGetAllByUserID *GroupPrimeDBMock) GetAllByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllByUserID.afterGetAllByUserIDCounter)
}

// GetAllByUserIDBeforeCounter returns a count of GroupPrimeDBMock.GetAllByUserID invocations
func (mmGetAllByUserID *GroupPrimeDBMock) GetAllByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllByUserID.beforeGetAllByUserIDCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetAllByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAllByUserID *mGroupPrimeDBMockGetAllByUserID) Calls() []*GroupPrimeDBMockGetAllByUserIDParams {
	mmGetAllByUserID.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetAllByUserIDParams, len(mmGetAllByUserID.callArgs))
	copy(argCopy, mmGetAllByUserID.callArgs)

	mmGetAllByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetAllByUserIDDone returns true if the count of the GetAllByUserID invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetAllByUserIDDone() bool {
	if m.GetAllByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllByUserIDMock.invocationsDone()
}

// MinimockGetAllByUserIDInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetAllByUserIDInspect() {
	for _, e := range m.GetAllByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAllByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetAllByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllByUserIDMock.defaultExpectation != nil && afterGetAllByUserIDCounter < 1 {
		if m.GetAllByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllByUserID at\n%s", m.GetAllByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllByUserID at\n%s with params: %#v", m.GetAllByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetAllByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllByUserID != nil && afterGetAllByUserIDCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllByUserID at\n%s", m.funcGetAllByUserIDOrigin)
	}

	if !m.GetAllByUserIDMock.invocationsDone() && afterGetAllByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetAllByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllByUserIDMock.expectedInvocations), m.GetAllByUserIDMock.expectedInvocationsOrigin, afterGetAllByUserIDCounter)
	}
}

type mGroupPrimeDBMockGetAllWithProductIDByUserID struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation
	expectations       []*GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation

	callArgs []*GroupPrimeDBMockGetAllWithProductIDByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation specifies expectation struct of the GroupPrimeDB.GetAllWithProductIDByUserID
type GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetAllWithProductIDByUserIDParams
	paramPtrs          *GroupPrimeDBMockGetAllWithProductIDByUserIDParamPtrs
	expectationOrigins GroupPrimeDBMockGetAllWithProductIDByUserIDExpectationOrigins
	results            *GroupPrimeDBMockGetAllWithProductIDByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetAllWithProductIDByUserIDParams contains parameters of the GroupPrimeDB.GetAllWithProductIDByUserID
type GroupPrimeDBMockGetAllWithProductIDByUserIDParams struct {
	userID int64
}

// GroupPrimeDBMockGetAllWithProductIDByUserIDParamPtrs contains pointers to parameters of the GroupPrimeDB.GetAllWithProductIDByUserID
type GroupPrimeDBMockGetAllWithProductIDByUserIDParamPtrs struct {
	userID *int64
}

// GroupPrimeDBMockGetAllWithProductIDByUserIDResults contains results of the GroupPrimeDB.GetAllWithProductIDByUserID
type GroupPrimeDBMockGetAllWithProductIDByUserIDResults struct {
	ga1 []groupentity.GroupWithProductID
	err error
}

// GroupPrimeDBMockGetAllWithProductIDByUserIDOrigins contains origins of expectations of the GroupPrimeDB.GetAllWithProductIDByUserID
type GroupPrimeDBMockGetAllWithProductIDByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Optional() *mGroupPrimeDBMockGetAllWithProductIDByUserID {
	mmGetAllWithProductIDByUserID.optional = true
	return mmGetAllWithProductIDByUserID
}

// Expect sets up expected params for GroupPrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Expect(userID int64) *mGroupPrimeDBMockGetAllWithProductIDByUserID {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation = &GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation{}
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllWithProductIDByUserID mock is already set by ExpectParams functions")
	}

	mmGetAllWithProductIDByUserID.defaultExpectation.params = &GroupPrimeDBMockGetAllWithProductIDByUserIDParams{userID}
	mmGetAllWithProductIDByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAllWithProductIDByUserID.expectations {
		if minimock.Equal(e.params, mmGetAllWithProductIDByUserID.defaultExpectation.params) {
			mmGetAllWithProductIDByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAllWithProductIDByUserID.defaultExpectation.params)
		}
	}

	return mmGetAllWithProductIDByUserID
}

// ExpectUserIDParam1 sets up expected param userID for GroupPrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) ExpectUserIDParam1(userID int64) *mGroupPrimeDBMockGetAllWithProductIDByUserID {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation = &GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation{}
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation.params != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllWithProductIDByUserID mock is already set by Expect")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetAllWithProductIDByUserIDParamPtrs{}
	}
	mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetAllWithProductIDByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetAllWithProductIDByUserID
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Inspect(f func(userID int64)) *mGroupPrimeDBMockGetAllWithProductIDByUserID {
	if mmGetAllWithProductIDByUserID.mock.inspectFuncGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetAllWithProductIDByUserID")
	}

	mmGetAllWithProductIDByUserID.mock.inspectFuncGetAllWithProductIDByUserID = f

	return mmGetAllWithProductIDByUserID
}

// Return sets up results that will be returned by GroupPrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Return(ga1 []groupentity.GroupWithProductID, err error) *GroupPrimeDBMock {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation = &GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation{mock: mmGetAllWithProductIDByUserID.mock}
	}
	mmGetAllWithProductIDByUserID.defaultExpectation.results = &GroupPrimeDBMockGetAllWithProductIDByUserIDResults{ga1, err}
	mmGetAllWithProductIDByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllWithProductIDByUserID.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetAllWithProductIDByUserID method
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Set(f func(userID int64) (ga1 []groupentity.GroupWithProductID, err error)) *GroupPrimeDBMock {
	if mmGetAllWithProductIDByUserID.defaultExpectation != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetAllWithProductIDByUserID method")
	}

	if len(mmGetAllWithProductIDByUserID.expectations) > 0 {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetAllWithProductIDByUserID method")
	}

	mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID = f
	mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetAllWithProductIDByUserID.mock
}

// When sets expectation for the GroupPrimeDB.GetAllWithProductIDByUserID which will trigger the result defined by the following
// Then helper
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) When(userID int64) *GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("GroupPrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation{
		mock:               mmGetAllWithProductIDByUserID.mock,
		params:             &GroupPrimeDBMockGetAllWithProductIDByUserIDParams{userID},
		expectationOrigins: GroupPrimeDBMockGetAllWithProductIDByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAllWithProductIDByUserID.expectations = append(mmGetAllWithProductIDByUserID.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetAllWithProductIDByUserID return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetAllWithProductIDByUserIDExpectation) Then(ga1 []groupentity.GroupWithProductID, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetAllWithProductIDByUserIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetAllWithProductIDByUserID should be invoked
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Times(n uint64) *mGroupPrimeDBMockGetAllWithProductIDByUserID {
	if n == 0 {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Times of GroupPrimeDBMock.GetAllWithProductIDByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllWithProductIDByUserID.expectedInvocations, n)
	mmGetAllWithProductIDByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllWithProductIDByUserID
}

func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) invocationsDone() bool {
	if len(mmGetAllWithProductIDByUserID.expectations) == 0 && mmGetAllWithProductIDByUserID.defaultExpectation == nil && mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.mock.afterGetAllWithProductIDByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllWithProductIDByUserID implements mm_repository.GroupPrimeDB
func (mmGetAllWithProductIDByUserID *GroupPrimeDBMock) GetAllWithProductIDByUserID(userID int64) (ga1 []groupentity.GroupWithProductID, err error) {
	mm_atomic.AddUint64(&mmGetAllWithProductIDByUserID.beforeGetAllWithProductIDByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllWithProductIDByUserID.afterGetAllWithProductIDByUserIDCounter, 1)

	mmGetAllWithProductIDByUserID.t.Helper()

	if mmGetAllWithProductIDByUserID.inspectFuncGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.inspectFuncGetAllWithProductIDByUserID(userID)
	}

	mm_params := GroupPrimeDBMockGetAllWithProductIDByUserIDParams{userID}

	// Record call args
	mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.mutex.Lock()
	mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.callArgs = append(mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.callArgs, &mm_params)
	mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.mutex.Unlock()

	for _, e := range mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetAllWithProductIDByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetAllWithProductIDByUserID.t.Errorf("GroupPrimeDBMock.GetAllWithProductIDByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAllWithProductIDByUserID.t.Errorf("GroupPrimeDBMock.GetAllWithProductIDByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllWithProductIDByUserID.t.Fatal("No results are set for the GroupPrimeDBMock.GetAllWithProductIDByUserID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetAllWithProductIDByUserID.funcGetAllWithProductIDByUserID != nil {
		return mmGetAllWithProductIDByUserID.funcGetAllWithProductIDByUserID(userID)
	}
	mmGetAllWithProductIDByUserID.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetAllWithProductIDByUserID. %v", userID)
	return
}

// GetAllWithProductIDByUserIDAfterCounter returns a count of finished GroupPrimeDBMock.GetAllWithProductIDByUserID invocations
func (mmGetAllWithProductIDByUserID *GroupPrimeDBMock) GetAllWithProductIDByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.afterGetAllWithProductIDByUserIDCounter)
}

// GetAllWithProductIDByUserIDBeforeCounter returns a count of GroupPrimeDBMock.GetAllWithProductIDByUserID invocations
func (mmGetAllWithProductIDByUserID *GroupPrimeDBMock) GetAllWithProductIDByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.beforeGetAllWithProductIDByUserIDCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetAllWithProductIDByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAllWithProductIDByUserID *mGroupPrimeDBMockGetAllWithProductIDByUserID) Calls() []*GroupPrimeDBMockGetAllWithProductIDByUserIDParams {
	mmGetAllWithProductIDByUserID.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetAllWithProductIDByUserIDParams, len(mmGetAllWithProductIDByUserID.callArgs))
	copy(argCopy, mmGetAllWithProductIDByUserID.callArgs)

	mmGetAllWithProductIDByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetAllWithProductIDByUserIDDone returns true if the count of the GetAllWithProductIDByUserID invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetAllWithProductIDByUserIDDone() bool {
	if m.GetAllWithProductIDByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllWithProductIDByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllWithProductIDByUserIDMock.invocationsDone()
}

// MinimockGetAllWithProductIDByUserIDInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetAllWithProductIDByUserIDInspect() {
	for _, e := range m.GetAllWithProductIDByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllWithProductIDByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAllWithProductIDByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetAllWithProductIDByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllWithProductIDByUserIDMock.defaultExpectation != nil && afterGetAllWithProductIDByUserIDCounter < 1 {
		if m.GetAllWithProductIDByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllWithProductIDByUserID at\n%s", m.GetAllWithProductIDByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllWithProductIDByUserID at\n%s with params: %#v", m.GetAllWithProductIDByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetAllWithProductIDByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllWithProductIDByUserID != nil && afterGetAllWithProductIDByUserIDCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetAllWithProductIDByUserID at\n%s", m.funcGetAllWithProductIDByUserIDOrigin)
	}

	if !m.GetAllWithProductIDByUserIDMock.invocationsDone() && afterGetAllWithProductIDByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetAllWithProductIDByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllWithProductIDByUserIDMock.expectedInvocations), m.GetAllWithProductIDByUserIDMock.expectedInvocationsOrigin, afterGetAllWithProductIDByUserIDCounter)
	}
}

type mGroupPrimeDBMockGetByFiltersAndPagination struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetByFiltersAndPaginationExpectation
	expectations       []*GroupPrimeDBMockGetByFiltersAndPaginationExpectation

	callArgs []*GroupPrimeDBMockGetByFiltersAndPaginationParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetByFiltersAndPaginationExpectation specifies expectation struct of the GroupPrimeDB.GetByFiltersAndPagination
type GroupPrimeDBMockGetByFiltersAndPaginationExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetByFiltersAndPaginationParams
	paramPtrs          *GroupPrimeDBMockGetByFiltersAndPaginationParamPtrs
	expectationOrigins GroupPrimeDBMockGetByFiltersAndPaginationExpectationOrigins
	results            *GroupPrimeDBMockGetByFiltersAndPaginationResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetByFiltersAndPaginationParams contains parameters of the GroupPrimeDB.GetByFiltersAndPagination
type GroupPrimeDBMockGetByFiltersAndPaginationParams struct {
	filters          groupentity.AdminGroupsFilter
	paginationParams groupentity.PaginationParams
}

// GroupPrimeDBMockGetByFiltersAndPaginationParamPtrs contains pointers to parameters of the GroupPrimeDB.GetByFiltersAndPagination
type GroupPrimeDBMockGetByFiltersAndPaginationParamPtrs struct {
	filters          *groupentity.AdminGroupsFilter
	paginationParams *groupentity.PaginationParams
}

// GroupPrimeDBMockGetByFiltersAndPaginationResults contains results of the GroupPrimeDB.GetByFiltersAndPagination
type GroupPrimeDBMockGetByFiltersAndPaginationResults struct {
	ga1 []groupentity.Group
	i1  int64
	err error
}

// GroupPrimeDBMockGetByFiltersAndPaginationOrigins contains origins of expectations of the GroupPrimeDB.GetByFiltersAndPagination
type GroupPrimeDBMockGetByFiltersAndPaginationExpectationOrigins struct {
	origin                 string
	originFilters          string
	originPaginationParams string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Optional() *mGroupPrimeDBMockGetByFiltersAndPagination {
	mmGetByFiltersAndPagination.optional = true
	return mmGetByFiltersAndPagination
}

// Expect sets up expected params for GroupPrimeDB.GetByFiltersAndPagination
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Expect(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams) *mGroupPrimeDBMockGetByFiltersAndPagination {
	if mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Set")
	}

	if mmGetByFiltersAndPagination.defaultExpectation == nil {
		mmGetByFiltersAndPagination.defaultExpectation = &GroupPrimeDBMockGetByFiltersAndPaginationExpectation{}
	}

	if mmGetByFiltersAndPagination.defaultExpectation.paramPtrs != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by ExpectParams functions")
	}

	mmGetByFiltersAndPagination.defaultExpectation.params = &GroupPrimeDBMockGetByFiltersAndPaginationParams{filters, paginationParams}
	mmGetByFiltersAndPagination.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByFiltersAndPagination.expectations {
		if minimock.Equal(e.params, mmGetByFiltersAndPagination.defaultExpectation.params) {
			mmGetByFiltersAndPagination.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByFiltersAndPagination.defaultExpectation.params)
		}
	}

	return mmGetByFiltersAndPagination
}

// ExpectFiltersParam1 sets up expected param filters for GroupPrimeDB.GetByFiltersAndPagination
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) ExpectFiltersParam1(filters groupentity.AdminGroupsFilter) *mGroupPrimeDBMockGetByFiltersAndPagination {
	if mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Set")
	}

	if mmGetByFiltersAndPagination.defaultExpectation == nil {
		mmGetByFiltersAndPagination.defaultExpectation = &GroupPrimeDBMockGetByFiltersAndPaginationExpectation{}
	}

	if mmGetByFiltersAndPagination.defaultExpectation.params != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Expect")
	}

	if mmGetByFiltersAndPagination.defaultExpectation.paramPtrs == nil {
		mmGetByFiltersAndPagination.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByFiltersAndPaginationParamPtrs{}
	}
	mmGetByFiltersAndPagination.defaultExpectation.paramPtrs.filters = &filters
	mmGetByFiltersAndPagination.defaultExpectation.expectationOrigins.originFilters = minimock.CallerInfo(1)

	return mmGetByFiltersAndPagination
}

// ExpectPaginationParamsParam2 sets up expected param paginationParams for GroupPrimeDB.GetByFiltersAndPagination
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) ExpectPaginationParamsParam2(paginationParams groupentity.PaginationParams) *mGroupPrimeDBMockGetByFiltersAndPagination {
	if mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Set")
	}

	if mmGetByFiltersAndPagination.defaultExpectation == nil {
		mmGetByFiltersAndPagination.defaultExpectation = &GroupPrimeDBMockGetByFiltersAndPaginationExpectation{}
	}

	if mmGetByFiltersAndPagination.defaultExpectation.params != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Expect")
	}

	if mmGetByFiltersAndPagination.defaultExpectation.paramPtrs == nil {
		mmGetByFiltersAndPagination.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByFiltersAndPaginationParamPtrs{}
	}
	mmGetByFiltersAndPagination.defaultExpectation.paramPtrs.paginationParams = &paginationParams
	mmGetByFiltersAndPagination.defaultExpectation.expectationOrigins.originPaginationParams = minimock.CallerInfo(1)

	return mmGetByFiltersAndPagination
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetByFiltersAndPagination
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Inspect(f func(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams)) *mGroupPrimeDBMockGetByFiltersAndPagination {
	if mmGetByFiltersAndPagination.mock.inspectFuncGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetByFiltersAndPagination")
	}

	mmGetByFiltersAndPagination.mock.inspectFuncGetByFiltersAndPagination = f

	return mmGetByFiltersAndPagination
}

// Return sets up results that will be returned by GroupPrimeDB.GetByFiltersAndPagination
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Return(ga1 []groupentity.Group, i1 int64, err error) *GroupPrimeDBMock {
	if mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Set")
	}

	if mmGetByFiltersAndPagination.defaultExpectation == nil {
		mmGetByFiltersAndPagination.defaultExpectation = &GroupPrimeDBMockGetByFiltersAndPaginationExpectation{mock: mmGetByFiltersAndPagination.mock}
	}
	mmGetByFiltersAndPagination.defaultExpectation.results = &GroupPrimeDBMockGetByFiltersAndPaginationResults{ga1, i1, err}
	mmGetByFiltersAndPagination.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersAndPagination.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetByFiltersAndPagination method
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Set(f func(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams) (ga1 []groupentity.Group, i1 int64, err error)) *GroupPrimeDBMock {
	if mmGetByFiltersAndPagination.defaultExpectation != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetByFiltersAndPagination method")
	}

	if len(mmGetByFiltersAndPagination.expectations) > 0 {
		mmGetByFiltersAndPagination.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetByFiltersAndPagination method")
	}

	mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination = f
	mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPaginationOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersAndPagination.mock
}

// When sets expectation for the GroupPrimeDB.GetByFiltersAndPagination which will trigger the result defined by the following
// Then helper
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) When(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams) *GroupPrimeDBMockGetByFiltersAndPaginationExpectation {
	if mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.mock.t.Fatalf("GroupPrimeDBMock.GetByFiltersAndPagination mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetByFiltersAndPaginationExpectation{
		mock:               mmGetByFiltersAndPagination.mock,
		params:             &GroupPrimeDBMockGetByFiltersAndPaginationParams{filters, paginationParams},
		expectationOrigins: GroupPrimeDBMockGetByFiltersAndPaginationExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByFiltersAndPagination.expectations = append(mmGetByFiltersAndPagination.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetByFiltersAndPagination return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetByFiltersAndPaginationExpectation) Then(ga1 []groupentity.Group, i1 int64, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetByFiltersAndPaginationResults{ga1, i1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetByFiltersAndPagination should be invoked
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Times(n uint64) *mGroupPrimeDBMockGetByFiltersAndPagination {
	if n == 0 {
		mmGetByFiltersAndPagination.mock.t.Fatalf("Times of GroupPrimeDBMock.GetByFiltersAndPagination mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByFiltersAndPagination.expectedInvocations, n)
	mmGetByFiltersAndPagination.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersAndPagination
}

func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) invocationsDone() bool {
	if len(mmGetByFiltersAndPagination.expectations) == 0 && mmGetByFiltersAndPagination.defaultExpectation == nil && mmGetByFiltersAndPagination.mock.funcGetByFiltersAndPagination == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByFiltersAndPagination.mock.afterGetByFiltersAndPaginationCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByFiltersAndPagination.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByFiltersAndPagination implements mm_repository.GroupPrimeDB
func (mmGetByFiltersAndPagination *GroupPrimeDBMock) GetByFiltersAndPagination(filters groupentity.AdminGroupsFilter, paginationParams groupentity.PaginationParams) (ga1 []groupentity.Group, i1 int64, err error) {
	mm_atomic.AddUint64(&mmGetByFiltersAndPagination.beforeGetByFiltersAndPaginationCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByFiltersAndPagination.afterGetByFiltersAndPaginationCounter, 1)

	mmGetByFiltersAndPagination.t.Helper()

	if mmGetByFiltersAndPagination.inspectFuncGetByFiltersAndPagination != nil {
		mmGetByFiltersAndPagination.inspectFuncGetByFiltersAndPagination(filters, paginationParams)
	}

	mm_params := GroupPrimeDBMockGetByFiltersAndPaginationParams{filters, paginationParams}

	// Record call args
	mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.mutex.Lock()
	mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.callArgs = append(mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.callArgs, &mm_params)
	mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.mutex.Unlock()

	for _, e := range mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.i1, e.results.err
		}
	}

	if mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.params
		mm_want_ptrs := mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetByFiltersAndPaginationParams{filters, paginationParams}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.filters != nil && !minimock.Equal(*mm_want_ptrs.filters, mm_got.filters) {
				mmGetByFiltersAndPagination.t.Errorf("GroupPrimeDBMock.GetByFiltersAndPagination got unexpected parameter filters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.expectationOrigins.originFilters, *mm_want_ptrs.filters, mm_got.filters, minimock.Diff(*mm_want_ptrs.filters, mm_got.filters))
			}

			if mm_want_ptrs.paginationParams != nil && !minimock.Equal(*mm_want_ptrs.paginationParams, mm_got.paginationParams) {
				mmGetByFiltersAndPagination.t.Errorf("GroupPrimeDBMock.GetByFiltersAndPagination got unexpected parameter paginationParams, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.expectationOrigins.originPaginationParams, *mm_want_ptrs.paginationParams, mm_got.paginationParams, minimock.Diff(*mm_want_ptrs.paginationParams, mm_got.paginationParams))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByFiltersAndPagination.t.Errorf("GroupPrimeDBMock.GetByFiltersAndPagination got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByFiltersAndPagination.GetByFiltersAndPaginationMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByFiltersAndPagination.t.Fatal("No results are set for the GroupPrimeDBMock.GetByFiltersAndPagination")
		}
		return (*mm_results).ga1, (*mm_results).i1, (*mm_results).err
	}
	if mmGetByFiltersAndPagination.funcGetByFiltersAndPagination != nil {
		return mmGetByFiltersAndPagination.funcGetByFiltersAndPagination(filters, paginationParams)
	}
	mmGetByFiltersAndPagination.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetByFiltersAndPagination. %v %v", filters, paginationParams)
	return
}

// GetByFiltersAndPaginationAfterCounter returns a count of finished GroupPrimeDBMock.GetByFiltersAndPagination invocations
func (mmGetByFiltersAndPagination *GroupPrimeDBMock) GetByFiltersAndPaginationAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFiltersAndPagination.afterGetByFiltersAndPaginationCounter)
}

// GetByFiltersAndPaginationBeforeCounter returns a count of GroupPrimeDBMock.GetByFiltersAndPagination invocations
func (mmGetByFiltersAndPagination *GroupPrimeDBMock) GetByFiltersAndPaginationBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFiltersAndPagination.beforeGetByFiltersAndPaginationCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetByFiltersAndPagination.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByFiltersAndPagination *mGroupPrimeDBMockGetByFiltersAndPagination) Calls() []*GroupPrimeDBMockGetByFiltersAndPaginationParams {
	mmGetByFiltersAndPagination.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetByFiltersAndPaginationParams, len(mmGetByFiltersAndPagination.callArgs))
	copy(argCopy, mmGetByFiltersAndPagination.callArgs)

	mmGetByFiltersAndPagination.mutex.RUnlock()

	return argCopy
}

// MinimockGetByFiltersAndPaginationDone returns true if the count of the GetByFiltersAndPagination invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetByFiltersAndPaginationDone() bool {
	if m.GetByFiltersAndPaginationMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByFiltersAndPaginationMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByFiltersAndPaginationMock.invocationsDone()
}

// MinimockGetByFiltersAndPaginationInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetByFiltersAndPaginationInspect() {
	for _, e := range m.GetByFiltersAndPaginationMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByFiltersAndPagination at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByFiltersAndPaginationCounter := mm_atomic.LoadUint64(&m.afterGetByFiltersAndPaginationCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByFiltersAndPaginationMock.defaultExpectation != nil && afterGetByFiltersAndPaginationCounter < 1 {
		if m.GetByFiltersAndPaginationMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByFiltersAndPagination at\n%s", m.GetByFiltersAndPaginationMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByFiltersAndPagination at\n%s with params: %#v", m.GetByFiltersAndPaginationMock.defaultExpectation.expectationOrigins.origin, *m.GetByFiltersAndPaginationMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByFiltersAndPagination != nil && afterGetByFiltersAndPaginationCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetByFiltersAndPagination at\n%s", m.funcGetByFiltersAndPaginationOrigin)
	}

	if !m.GetByFiltersAndPaginationMock.invocationsDone() && afterGetByFiltersAndPaginationCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetByFiltersAndPagination at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByFiltersAndPaginationMock.expectedInvocations), m.GetByFiltersAndPaginationMock.expectedInvocationsOrigin, afterGetByFiltersAndPaginationCounter)
	}
}

type mGroupPrimeDBMockGetByGroupIDAndProductID struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetByGroupIDAndProductIDExpectation
	expectations       []*GroupPrimeDBMockGetByGroupIDAndProductIDExpectation

	callArgs []*GroupPrimeDBMockGetByGroupIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetByGroupIDAndProductIDExpectation specifies expectation struct of the GroupPrimeDB.GetByGroupIDAndProductID
type GroupPrimeDBMockGetByGroupIDAndProductIDExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetByGroupIDAndProductIDParams
	paramPtrs          *GroupPrimeDBMockGetByGroupIDAndProductIDParamPtrs
	expectationOrigins GroupPrimeDBMockGetByGroupIDAndProductIDExpectationOrigins
	results            *GroupPrimeDBMockGetByGroupIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetByGroupIDAndProductIDParams contains parameters of the GroupPrimeDB.GetByGroupIDAndProductID
type GroupPrimeDBMockGetByGroupIDAndProductIDParams struct {
	groupID   int64
	productID int64
}

// GroupPrimeDBMockGetByGroupIDAndProductIDParamPtrs contains pointers to parameters of the GroupPrimeDB.GetByGroupIDAndProductID
type GroupPrimeDBMockGetByGroupIDAndProductIDParamPtrs struct {
	groupID   *int64
	productID *int64
}

// GroupPrimeDBMockGetByGroupIDAndProductIDResults contains results of the GroupPrimeDB.GetByGroupIDAndProductID
type GroupPrimeDBMockGetByGroupIDAndProductIDResults struct {
	g1  groupentity.Group
	err error
}

// GroupPrimeDBMockGetByGroupIDAndProductIDOrigins contains origins of expectations of the GroupPrimeDB.GetByGroupIDAndProductID
type GroupPrimeDBMockGetByGroupIDAndProductIDExpectationOrigins struct {
	origin          string
	originGroupID   string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Optional() *mGroupPrimeDBMockGetByGroupIDAndProductID {
	mmGetByGroupIDAndProductID.optional = true
	return mmGetByGroupIDAndProductID
}

// Expect sets up expected params for GroupPrimeDB.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Expect(groupID int64, productID int64) *mGroupPrimeDBMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupPrimeDBMockGetByGroupIDAndProductIDExpectation{}
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByGroupIDAndProductID.defaultExpectation.params = &GroupPrimeDBMockGetByGroupIDAndProductIDParams{groupID, productID}
	mmGetByGroupIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByGroupIDAndProductID.defaultExpectation.params) {
			mmGetByGroupIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByGroupIDAndProductID
}

// ExpectGroupIDParam1 sets up expected param groupID for GroupPrimeDB.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) ExpectGroupIDParam1(groupID int64) *mGroupPrimeDBMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupPrimeDBMockGetByGroupIDAndProductIDExpectation{}
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.params != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Expect")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByGroupIDAndProductIDParamPtrs{}
	}
	mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupIDAndProductID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for GroupPrimeDB.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) ExpectProductIDParam2(productID int64) *mGroupPrimeDBMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupPrimeDBMockGetByGroupIDAndProductIDExpectation{}
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.params != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Expect")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByGroupIDAndProductIDParamPtrs{}
	}
	mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByGroupIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByGroupIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Inspect(f func(groupID int64, productID int64)) *mGroupPrimeDBMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.inspectFuncGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetByGroupIDAndProductID")
	}

	mmGetByGroupIDAndProductID.mock.inspectFuncGetByGroupIDAndProductID = f

	return mmGetByGroupIDAndProductID
}

// Return sets up results that will be returned by GroupPrimeDB.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Return(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupPrimeDBMockGetByGroupIDAndProductIDExpectation{mock: mmGetByGroupIDAndProductID.mock}
	}
	mmGetByGroupIDAndProductID.defaultExpectation.results = &GroupPrimeDBMockGetByGroupIDAndProductIDResults{g1, err}
	mmGetByGroupIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupIDAndProductID.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetByGroupIDAndProductID method
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Set(f func(groupID int64, productID int64) (g1 groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmGetByGroupIDAndProductID.defaultExpectation != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetByGroupIDAndProductID method")
	}

	if len(mmGetByGroupIDAndProductID.expectations) > 0 {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetByGroupIDAndProductID method")
	}

	mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID = f
	mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupIDAndProductID.mock
}

// When sets expectation for the GroupPrimeDB.GetByGroupIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) When(groupID int64, productID int64) *GroupPrimeDBMockGetByGroupIDAndProductIDExpectation {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetByGroupIDAndProductIDExpectation{
		mock:               mmGetByGroupIDAndProductID.mock,
		params:             &GroupPrimeDBMockGetByGroupIDAndProductIDParams{groupID, productID},
		expectationOrigins: GroupPrimeDBMockGetByGroupIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupIDAndProductID.expectations = append(mmGetByGroupIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetByGroupIDAndProductID return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetByGroupIDAndProductIDExpectation) Then(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetByGroupIDAndProductIDResults{g1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetByGroupIDAndProductID should be invoked
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Times(n uint64) *mGroupPrimeDBMockGetByGroupIDAndProductID {
	if n == 0 {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Times of GroupPrimeDBMock.GetByGroupIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupIDAndProductID.expectedInvocations, n)
	mmGetByGroupIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupIDAndProductID
}

func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) invocationsDone() bool {
	if len(mmGetByGroupIDAndProductID.expectations) == 0 && mmGetByGroupIDAndProductID.defaultExpectation == nil && mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.mock.afterGetByGroupIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupIDAndProductID implements mm_repository.GroupPrimeDB
func (mmGetByGroupIDAndProductID *GroupPrimeDBMock) GetByGroupIDAndProductID(groupID int64, productID int64) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByGroupIDAndProductID.beforeGetByGroupIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupIDAndProductID.afterGetByGroupIDAndProductIDCounter, 1)

	mmGetByGroupIDAndProductID.t.Helper()

	if mmGetByGroupIDAndProductID.inspectFuncGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.inspectFuncGetByGroupIDAndProductID(groupID, productID)
	}

	mm_params := GroupPrimeDBMockGetByGroupIDAndProductIDParams{groupID, productID}

	// Record call args
	mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.mutex.Lock()
	mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.callArgs = append(mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.callArgs, &mm_params)
	mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetByGroupIDAndProductIDParams{groupID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupIDAndProductID.t.Errorf("GroupPrimeDBMock.GetByGroupIDAndProductID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByGroupIDAndProductID.t.Errorf("GroupPrimeDBMock.GetByGroupIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupIDAndProductID.t.Errorf("GroupPrimeDBMock.GetByGroupIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupIDAndProductID.t.Fatal("No results are set for the GroupPrimeDBMock.GetByGroupIDAndProductID")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetByGroupIDAndProductID.funcGetByGroupIDAndProductID != nil {
		return mmGetByGroupIDAndProductID.funcGetByGroupIDAndProductID(groupID, productID)
	}
	mmGetByGroupIDAndProductID.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetByGroupIDAndProductID. %v %v", groupID, productID)
	return
}

// GetByGroupIDAndProductIDAfterCounter returns a count of finished GroupPrimeDBMock.GetByGroupIDAndProductID invocations
func (mmGetByGroupIDAndProductID *GroupPrimeDBMock) GetByGroupIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.afterGetByGroupIDAndProductIDCounter)
}

// GetByGroupIDAndProductIDBeforeCounter returns a count of GroupPrimeDBMock.GetByGroupIDAndProductID invocations
func (mmGetByGroupIDAndProductID *GroupPrimeDBMock) GetByGroupIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.beforeGetByGroupIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetByGroupIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupIDAndProductID *mGroupPrimeDBMockGetByGroupIDAndProductID) Calls() []*GroupPrimeDBMockGetByGroupIDAndProductIDParams {
	mmGetByGroupIDAndProductID.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetByGroupIDAndProductIDParams, len(mmGetByGroupIDAndProductID.callArgs))
	copy(argCopy, mmGetByGroupIDAndProductID.callArgs)

	mmGetByGroupIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDAndProductIDDone returns true if the count of the GetByGroupIDAndProductID invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetByGroupIDAndProductIDDone() bool {
	if m.GetByGroupIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDAndProductIDMock.invocationsDone()
}

// MinimockGetByGroupIDAndProductIDInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetByGroupIDAndProductIDInspect() {
	for _, e := range m.GetByGroupIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByGroupIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDAndProductIDMock.defaultExpectation != nil && afterGetByGroupIDAndProductIDCounter < 1 {
		if m.GetByGroupIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByGroupIDAndProductID at\n%s", m.GetByGroupIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByGroupIDAndProductID at\n%s with params: %#v", m.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupIDAndProductID != nil && afterGetByGroupIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetByGroupIDAndProductID at\n%s", m.funcGetByGroupIDAndProductIDOrigin)
	}

	if !m.GetByGroupIDAndProductIDMock.invocationsDone() && afterGetByGroupIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetByGroupIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDAndProductIDMock.expectedInvocations), m.GetByGroupIDAndProductIDMock.expectedInvocationsOrigin, afterGetByGroupIDAndProductIDCounter)
	}
}

type mGroupPrimeDBMockGetByID struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetByIDExpectation
	expectations       []*GroupPrimeDBMockGetByIDExpectation

	callArgs []*GroupPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetByIDExpectation specifies expectation struct of the GroupPrimeDB.GetByID
type GroupPrimeDBMockGetByIDExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetByIDParams
	paramPtrs          *GroupPrimeDBMockGetByIDParamPtrs
	expectationOrigins GroupPrimeDBMockGetByIDExpectationOrigins
	results            *GroupPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetByIDParams contains parameters of the GroupPrimeDB.GetByID
type GroupPrimeDBMockGetByIDParams struct {
	id int64
}

// GroupPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the GroupPrimeDB.GetByID
type GroupPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// GroupPrimeDBMockGetByIDResults contains results of the GroupPrimeDB.GetByID
type GroupPrimeDBMockGetByIDResults struct {
	g1  groupentity.Group
	err error
}

// GroupPrimeDBMockGetByIDOrigins contains origins of expectations of the GroupPrimeDB.GetByID
type GroupPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mGroupPrimeDBMockGetByID) Optional() *mGroupPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for GroupPrimeDB.GetByID
func (mmGetByID *mGroupPrimeDBMockGetByID) Expect(id int64) *mGroupPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("GroupPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &GroupPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for GroupPrimeDB.GetByID
func (mmGetByID *mGroupPrimeDBMockGetByID) ExpectIdParam1(id int64) *mGroupPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("GroupPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetByID
func (mmGetByID *mGroupPrimeDBMockGetByID) Inspect(f func(id int64)) *mGroupPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by GroupPrimeDB.GetByID
func (mmGetByID *mGroupPrimeDBMockGetByID) Return(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &GroupPrimeDBMockGetByIDResults{g1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetByID method
func (mmGetByID *mGroupPrimeDBMockGetByID) Set(f func(id int64) (g1 groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the GroupPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mGroupPrimeDBMockGetByID) When(id int64) *GroupPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &GroupPrimeDBMockGetByIDParams{id},
		expectationOrigins: GroupPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetByIDExpectation) Then(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetByIDResults{g1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetByID should be invoked
func (mmGetByID *mGroupPrimeDBMockGetByID) Times(n uint64) *mGroupPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of GroupPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mGroupPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.GroupPrimeDB
func (mmGetByID *GroupPrimeDBMock) GetByID(id int64) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := GroupPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("GroupPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("GroupPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the GroupPrimeDBMock.GetByID")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished GroupPrimeDBMock.GetByID invocations
func (mmGetByID *GroupPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of GroupPrimeDBMock.GetByID invocations
func (mmGetByID *GroupPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mGroupPrimeDBMockGetByID) Calls() []*GroupPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mGroupPrimeDBMockGetByProductID struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetByProductIDExpectation
	expectations       []*GroupPrimeDBMockGetByProductIDExpectation

	callArgs []*GroupPrimeDBMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetByProductIDExpectation specifies expectation struct of the GroupPrimeDB.GetByProductID
type GroupPrimeDBMockGetByProductIDExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetByProductIDParams
	paramPtrs          *GroupPrimeDBMockGetByProductIDParamPtrs
	expectationOrigins GroupPrimeDBMockGetByProductIDExpectationOrigins
	results            *GroupPrimeDBMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetByProductIDParams contains parameters of the GroupPrimeDB.GetByProductID
type GroupPrimeDBMockGetByProductIDParams struct {
	productID int64
}

// GroupPrimeDBMockGetByProductIDParamPtrs contains pointers to parameters of the GroupPrimeDB.GetByProductID
type GroupPrimeDBMockGetByProductIDParamPtrs struct {
	productID *int64
}

// GroupPrimeDBMockGetByProductIDResults contains results of the GroupPrimeDB.GetByProductID
type GroupPrimeDBMockGetByProductIDResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupPrimeDBMockGetByProductIDOrigins contains origins of expectations of the GroupPrimeDB.GetByProductID
type GroupPrimeDBMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Optional() *mGroupPrimeDBMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for GroupPrimeDB.GetByProductID
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Expect(productID int64) *mGroupPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &GroupPrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &GroupPrimeDBMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for GroupPrimeDB.GetByProductID
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) ExpectProductIDParam1(productID int64) *mGroupPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &GroupPrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetByProductID
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Inspect(f func(productID int64)) *mGroupPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by GroupPrimeDB.GetByProductID
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Return(ga1 []groupentity.Group, err error) *GroupPrimeDBMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &GroupPrimeDBMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &GroupPrimeDBMockGetByProductIDResults{ga1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetByProductID method
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Set(f func(productID int64) (ga1 []groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the GroupPrimeDB.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) When(productID int64) *GroupPrimeDBMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupPrimeDBMock.GetByProductID mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &GroupPrimeDBMockGetByProductIDParams{productID},
		expectationOrigins: GroupPrimeDBMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetByProductID return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetByProductIDExpectation) Then(ga1 []groupentity.Group, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetByProductIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetByProductID should be invoked
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Times(n uint64) *mGroupPrimeDBMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of GroupPrimeDBMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_repository.GroupPrimeDB
func (mmGetByProductID *GroupPrimeDBMock) GetByProductID(productID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := GroupPrimeDBMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("GroupPrimeDBMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("GroupPrimeDBMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the GroupPrimeDBMock.GetByProductID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished GroupPrimeDBMock.GetByProductID invocations
func (mmGetByProductID *GroupPrimeDBMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of GroupPrimeDBMock.GetByProductID invocations
func (mmGetByProductID *GroupPrimeDBMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mGroupPrimeDBMockGetByProductID) Calls() []*GroupPrimeDBMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mGroupPrimeDBMockGetByProductIDAndIsActive struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetByProductIDAndIsActiveExpectation
	expectations       []*GroupPrimeDBMockGetByProductIDAndIsActiveExpectation

	callArgs []*GroupPrimeDBMockGetByProductIDAndIsActiveParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetByProductIDAndIsActiveExpectation specifies expectation struct of the GroupPrimeDB.GetByProductIDAndIsActive
type GroupPrimeDBMockGetByProductIDAndIsActiveExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockGetByProductIDAndIsActiveParams
	paramPtrs          *GroupPrimeDBMockGetByProductIDAndIsActiveParamPtrs
	expectationOrigins GroupPrimeDBMockGetByProductIDAndIsActiveExpectationOrigins
	results            *GroupPrimeDBMockGetByProductIDAndIsActiveResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockGetByProductIDAndIsActiveParams contains parameters of the GroupPrimeDB.GetByProductIDAndIsActive
type GroupPrimeDBMockGetByProductIDAndIsActiveParams struct {
	productID int64
	isActive  bool
}

// GroupPrimeDBMockGetByProductIDAndIsActiveParamPtrs contains pointers to parameters of the GroupPrimeDB.GetByProductIDAndIsActive
type GroupPrimeDBMockGetByProductIDAndIsActiveParamPtrs struct {
	productID *int64
	isActive  *bool
}

// GroupPrimeDBMockGetByProductIDAndIsActiveResults contains results of the GroupPrimeDB.GetByProductIDAndIsActive
type GroupPrimeDBMockGetByProductIDAndIsActiveResults struct {
	aa1 []groupentity.AdminGroup
	err error
}

// GroupPrimeDBMockGetByProductIDAndIsActiveOrigins contains origins of expectations of the GroupPrimeDB.GetByProductIDAndIsActive
type GroupPrimeDBMockGetByProductIDAndIsActiveExpectationOrigins struct {
	origin          string
	originProductID string
	originIsActive  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Optional() *mGroupPrimeDBMockGetByProductIDAndIsActive {
	mmGetByProductIDAndIsActive.optional = true
	return mmGetByProductIDAndIsActive
}

// Expect sets up expected params for GroupPrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Expect(productID int64, isActive bool) *mGroupPrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &GroupPrimeDBMockGetByProductIDAndIsActiveExpectation{}
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by ExpectParams functions")
	}

	mmGetByProductIDAndIsActive.defaultExpectation.params = &GroupPrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive}
	mmGetByProductIDAndIsActive.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductIDAndIsActive.expectations {
		if minimock.Equal(e.params, mmGetByProductIDAndIsActive.defaultExpectation.params) {
			mmGetByProductIDAndIsActive.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductIDAndIsActive.defaultExpectation.params)
		}
	}

	return mmGetByProductIDAndIsActive
}

// ExpectProductIDParam1 sets up expected param productID for GroupPrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) ExpectProductIDParam1(productID int64) *mGroupPrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &GroupPrimeDBMockGetByProductIDAndIsActiveExpectation{}
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.params != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Expect")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByProductIDAndIsActiveParamPtrs{}
	}
	mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductIDAndIsActive.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductIDAndIsActive
}

// ExpectIsActiveParam2 sets up expected param isActive for GroupPrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) ExpectIsActiveParam2(isActive bool) *mGroupPrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &GroupPrimeDBMockGetByProductIDAndIsActiveExpectation{}
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.params != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Expect")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs = &GroupPrimeDBMockGetByProductIDAndIsActiveParamPtrs{}
	}
	mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs.isActive = &isActive
	mmGetByProductIDAndIsActive.defaultExpectation.expectationOrigins.originIsActive = minimock.CallerInfo(1)

	return mmGetByProductIDAndIsActive
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Inspect(f func(productID int64, isActive bool)) *mGroupPrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.inspectFuncGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetByProductIDAndIsActive")
	}

	mmGetByProductIDAndIsActive.mock.inspectFuncGetByProductIDAndIsActive = f

	return mmGetByProductIDAndIsActive
}

// Return sets up results that will be returned by GroupPrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Return(aa1 []groupentity.AdminGroup, err error) *GroupPrimeDBMock {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &GroupPrimeDBMockGetByProductIDAndIsActiveExpectation{mock: mmGetByProductIDAndIsActive.mock}
	}
	mmGetByProductIDAndIsActive.defaultExpectation.results = &GroupPrimeDBMockGetByProductIDAndIsActiveResults{aa1, err}
	mmGetByProductIDAndIsActive.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndIsActive.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetByProductIDAndIsActive method
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Set(f func(productID int64, isActive bool) (aa1 []groupentity.AdminGroup, err error)) *GroupPrimeDBMock {
	if mmGetByProductIDAndIsActive.defaultExpectation != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetByProductIDAndIsActive method")
	}

	if len(mmGetByProductIDAndIsActive.expectations) > 0 {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetByProductIDAndIsActive method")
	}

	mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive = f
	mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActiveOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndIsActive.mock
}

// When sets expectation for the GroupPrimeDB.GetByProductIDAndIsActive which will trigger the result defined by the following
// Then helper
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) When(productID int64, isActive bool) *GroupPrimeDBMockGetByProductIDAndIsActiveExpectation {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("GroupPrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockGetByProductIDAndIsActiveExpectation{
		mock:               mmGetByProductIDAndIsActive.mock,
		params:             &GroupPrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive},
		expectationOrigins: GroupPrimeDBMockGetByProductIDAndIsActiveExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductIDAndIsActive.expectations = append(mmGetByProductIDAndIsActive.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.GetByProductIDAndIsActive return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockGetByProductIDAndIsActiveExpectation) Then(aa1 []groupentity.AdminGroup, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockGetByProductIDAndIsActiveResults{aa1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.GetByProductIDAndIsActive should be invoked
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Times(n uint64) *mGroupPrimeDBMockGetByProductIDAndIsActive {
	if n == 0 {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Times of GroupPrimeDBMock.GetByProductIDAndIsActive mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductIDAndIsActive.expectedInvocations, n)
	mmGetByProductIDAndIsActive.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndIsActive
}

func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) invocationsDone() bool {
	if len(mmGetByProductIDAndIsActive.expectations) == 0 && mmGetByProductIDAndIsActive.defaultExpectation == nil && mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.mock.afterGetByProductIDAndIsActiveCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductIDAndIsActive implements mm_repository.GroupPrimeDB
func (mmGetByProductIDAndIsActive *GroupPrimeDBMock) GetByProductIDAndIsActive(productID int64, isActive bool) (aa1 []groupentity.AdminGroup, err error) {
	mm_atomic.AddUint64(&mmGetByProductIDAndIsActive.beforeGetByProductIDAndIsActiveCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductIDAndIsActive.afterGetByProductIDAndIsActiveCounter, 1)

	mmGetByProductIDAndIsActive.t.Helper()

	if mmGetByProductIDAndIsActive.inspectFuncGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.inspectFuncGetByProductIDAndIsActive(productID, isActive)
	}

	mm_params := GroupPrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive}

	// Record call args
	mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.mutex.Lock()
	mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.callArgs = append(mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.callArgs, &mm_params)
	mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.mutex.Unlock()

	for _, e := range mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.aa1, e.results.err
		}
	}

	if mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductIDAndIsActive.t.Errorf("GroupPrimeDBMock.GetByProductIDAndIsActive got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.isActive != nil && !minimock.Equal(*mm_want_ptrs.isActive, mm_got.isActive) {
				mmGetByProductIDAndIsActive.t.Errorf("GroupPrimeDBMock.GetByProductIDAndIsActive got unexpected parameter isActive, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.originIsActive, *mm_want_ptrs.isActive, mm_got.isActive, minimock.Diff(*mm_want_ptrs.isActive, mm_got.isActive))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductIDAndIsActive.t.Errorf("GroupPrimeDBMock.GetByProductIDAndIsActive got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductIDAndIsActive.t.Fatal("No results are set for the GroupPrimeDBMock.GetByProductIDAndIsActive")
		}
		return (*mm_results).aa1, (*mm_results).err
	}
	if mmGetByProductIDAndIsActive.funcGetByProductIDAndIsActive != nil {
		return mmGetByProductIDAndIsActive.funcGetByProductIDAndIsActive(productID, isActive)
	}
	mmGetByProductIDAndIsActive.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetByProductIDAndIsActive. %v %v", productID, isActive)
	return
}

// GetByProductIDAndIsActiveAfterCounter returns a count of finished GroupPrimeDBMock.GetByProductIDAndIsActive invocations
func (mmGetByProductIDAndIsActive *GroupPrimeDBMock) GetByProductIDAndIsActiveAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.afterGetByProductIDAndIsActiveCounter)
}

// GetByProductIDAndIsActiveBeforeCounter returns a count of GroupPrimeDBMock.GetByProductIDAndIsActive invocations
func (mmGetByProductIDAndIsActive *GroupPrimeDBMock) GetByProductIDAndIsActiveBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.beforeGetByProductIDAndIsActiveCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.GetByProductIDAndIsActive.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductIDAndIsActive *mGroupPrimeDBMockGetByProductIDAndIsActive) Calls() []*GroupPrimeDBMockGetByProductIDAndIsActiveParams {
	mmGetByProductIDAndIsActive.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockGetByProductIDAndIsActiveParams, len(mmGetByProductIDAndIsActive.callArgs))
	copy(argCopy, mmGetByProductIDAndIsActive.callArgs)

	mmGetByProductIDAndIsActive.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDAndIsActiveDone returns true if the count of the GetByProductIDAndIsActive invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetByProductIDAndIsActiveDone() bool {
	if m.GetByProductIDAndIsActiveMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDAndIsActiveMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDAndIsActiveMock.invocationsDone()
}

// MinimockGetByProductIDAndIsActiveInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetByProductIDAndIsActiveInspect() {
	for _, e := range m.GetByProductIDAndIsActiveMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductIDAndIsActive at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDAndIsActiveCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDAndIsActiveCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDAndIsActiveMock.defaultExpectation != nil && afterGetByProductIDAndIsActiveCounter < 1 {
		if m.GetByProductIDAndIsActiveMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductIDAndIsActive at\n%s", m.GetByProductIDAndIsActiveMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductIDAndIsActive at\n%s with params: %#v", m.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDAndIsActiveMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductIDAndIsActive != nil && afterGetByProductIDAndIsActiveCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetByProductIDAndIsActive at\n%s", m.funcGetByProductIDAndIsActiveOrigin)
	}

	if !m.GetByProductIDAndIsActiveMock.invocationsDone() && afterGetByProductIDAndIsActiveCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetByProductIDAndIsActive at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDAndIsActiveMock.expectedInvocations), m.GetByProductIDAndIsActiveMock.expectedInvocationsOrigin, afterGetByProductIDAndIsActiveCounter)
	}
}

type mGroupPrimeDBMockGetGroupCategoryStates struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetGroupCategoryStatesExpectation
	expectations       []*GroupPrimeDBMockGetGroupCategoryStatesExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetGroupCategoryStatesExpectation specifies expectation struct of the GroupPrimeDB.GetGroupCategoryStates
type GroupPrimeDBMockGetGroupCategoryStatesExpectation struct {
	mock *GroupPrimeDBMock

	results      *GroupPrimeDBMockGetGroupCategoryStatesResults
	returnOrigin string
	Counter      uint64
}

// GroupPrimeDBMockGetGroupCategoryStatesResults contains results of the GroupPrimeDB.GetGroupCategoryStates
type GroupPrimeDBMockGetGroupCategoryStatesResults struct {
	ga1 []groupentity.GroupCategoryLink
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) Optional() *mGroupPrimeDBMockGetGroupCategoryStates {
	mmGetGroupCategoryStates.optional = true
	return mmGetGroupCategoryStates
}

// Expect sets up expected params for GroupPrimeDB.GetGroupCategoryStates
func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) Expect() *mGroupPrimeDBMockGetGroupCategoryStates {
	if mmGetGroupCategoryStates.mock.funcGetGroupCategoryStates != nil {
		mmGetGroupCategoryStates.mock.t.Fatalf("GroupPrimeDBMock.GetGroupCategoryStates mock is already set by Set")
	}

	if mmGetGroupCategoryStates.defaultExpectation == nil {
		mmGetGroupCategoryStates.defaultExpectation = &GroupPrimeDBMockGetGroupCategoryStatesExpectation{}
	}

	return mmGetGroupCategoryStates
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetGroupCategoryStates
func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) Inspect(f func()) *mGroupPrimeDBMockGetGroupCategoryStates {
	if mmGetGroupCategoryStates.mock.inspectFuncGetGroupCategoryStates != nil {
		mmGetGroupCategoryStates.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetGroupCategoryStates")
	}

	mmGetGroupCategoryStates.mock.inspectFuncGetGroupCategoryStates = f

	return mmGetGroupCategoryStates
}

// Return sets up results that will be returned by GroupPrimeDB.GetGroupCategoryStates
func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) Return(ga1 []groupentity.GroupCategoryLink, err error) *GroupPrimeDBMock {
	if mmGetGroupCategoryStates.mock.funcGetGroupCategoryStates != nil {
		mmGetGroupCategoryStates.mock.t.Fatalf("GroupPrimeDBMock.GetGroupCategoryStates mock is already set by Set")
	}

	if mmGetGroupCategoryStates.defaultExpectation == nil {
		mmGetGroupCategoryStates.defaultExpectation = &GroupPrimeDBMockGetGroupCategoryStatesExpectation{mock: mmGetGroupCategoryStates.mock}
	}
	mmGetGroupCategoryStates.defaultExpectation.results = &GroupPrimeDBMockGetGroupCategoryStatesResults{ga1, err}
	mmGetGroupCategoryStates.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetGroupCategoryStates.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetGroupCategoryStates method
func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) Set(f func() (ga1 []groupentity.GroupCategoryLink, err error)) *GroupPrimeDBMock {
	if mmGetGroupCategoryStates.defaultExpectation != nil {
		mmGetGroupCategoryStates.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetGroupCategoryStates method")
	}

	if len(mmGetGroupCategoryStates.expectations) > 0 {
		mmGetGroupCategoryStates.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetGroupCategoryStates method")
	}

	mmGetGroupCategoryStates.mock.funcGetGroupCategoryStates = f
	mmGetGroupCategoryStates.mock.funcGetGroupCategoryStatesOrigin = minimock.CallerInfo(1)
	return mmGetGroupCategoryStates.mock
}

// Times sets number of times GroupPrimeDB.GetGroupCategoryStates should be invoked
func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) Times(n uint64) *mGroupPrimeDBMockGetGroupCategoryStates {
	if n == 0 {
		mmGetGroupCategoryStates.mock.t.Fatalf("Times of GroupPrimeDBMock.GetGroupCategoryStates mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetGroupCategoryStates.expectedInvocations, n)
	mmGetGroupCategoryStates.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetGroupCategoryStates
}

func (mmGetGroupCategoryStates *mGroupPrimeDBMockGetGroupCategoryStates) invocationsDone() bool {
	if len(mmGetGroupCategoryStates.expectations) == 0 && mmGetGroupCategoryStates.defaultExpectation == nil && mmGetGroupCategoryStates.mock.funcGetGroupCategoryStates == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetGroupCategoryStates.mock.afterGetGroupCategoryStatesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetGroupCategoryStates.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetGroupCategoryStates implements mm_repository.GroupPrimeDB
func (mmGetGroupCategoryStates *GroupPrimeDBMock) GetGroupCategoryStates() (ga1 []groupentity.GroupCategoryLink, err error) {
	mm_atomic.AddUint64(&mmGetGroupCategoryStates.beforeGetGroupCategoryStatesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetGroupCategoryStates.afterGetGroupCategoryStatesCounter, 1)

	mmGetGroupCategoryStates.t.Helper()

	if mmGetGroupCategoryStates.inspectFuncGetGroupCategoryStates != nil {
		mmGetGroupCategoryStates.inspectFuncGetGroupCategoryStates()
	}

	if mmGetGroupCategoryStates.GetGroupCategoryStatesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetGroupCategoryStates.GetGroupCategoryStatesMock.defaultExpectation.Counter, 1)

		mm_results := mmGetGroupCategoryStates.GetGroupCategoryStatesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetGroupCategoryStates.t.Fatal("No results are set for the GroupPrimeDBMock.GetGroupCategoryStates")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetGroupCategoryStates.funcGetGroupCategoryStates != nil {
		return mmGetGroupCategoryStates.funcGetGroupCategoryStates()
	}
	mmGetGroupCategoryStates.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetGroupCategoryStates.")
	return
}

// GetGroupCategoryStatesAfterCounter returns a count of finished GroupPrimeDBMock.GetGroupCategoryStates invocations
func (mmGetGroupCategoryStates *GroupPrimeDBMock) GetGroupCategoryStatesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroupCategoryStates.afterGetGroupCategoryStatesCounter)
}

// GetGroupCategoryStatesBeforeCounter returns a count of GroupPrimeDBMock.GetGroupCategoryStates invocations
func (mmGetGroupCategoryStates *GroupPrimeDBMock) GetGroupCategoryStatesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroupCategoryStates.beforeGetGroupCategoryStatesCounter)
}

// MinimockGetGroupCategoryStatesDone returns true if the count of the GetGroupCategoryStates invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetGroupCategoryStatesDone() bool {
	if m.GetGroupCategoryStatesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetGroupCategoryStatesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetGroupCategoryStatesMock.invocationsDone()
}

// MinimockGetGroupCategoryStatesInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetGroupCategoryStatesInspect() {
	for _, e := range m.GetGroupCategoryStatesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupPrimeDBMock.GetGroupCategoryStates")
		}
	}

	afterGetGroupCategoryStatesCounter := mm_atomic.LoadUint64(&m.afterGetGroupCategoryStatesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetGroupCategoryStatesMock.defaultExpectation != nil && afterGetGroupCategoryStatesCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetGroupCategoryStates at\n%s", m.GetGroupCategoryStatesMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetGroupCategoryStates != nil && afterGetGroupCategoryStatesCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetGroupCategoryStates at\n%s", m.funcGetGroupCategoryStatesOrigin)
	}

	if !m.GetGroupCategoryStatesMock.invocationsDone() && afterGetGroupCategoryStatesCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetGroupCategoryStates at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetGroupCategoryStatesMock.expectedInvocations), m.GetGroupCategoryStatesMock.expectedInvocationsOrigin, afterGetGroupCategoryStatesCounter)
	}
}

type mGroupPrimeDBMockGetSystemGroups struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetSystemGroupsExpectation
	expectations       []*GroupPrimeDBMockGetSystemGroupsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetSystemGroupsExpectation specifies expectation struct of the GroupPrimeDB.GetSystemGroups
type GroupPrimeDBMockGetSystemGroupsExpectation struct {
	mock *GroupPrimeDBMock

	results      *GroupPrimeDBMockGetSystemGroupsResults
	returnOrigin string
	Counter      uint64
}

// GroupPrimeDBMockGetSystemGroupsResults contains results of the GroupPrimeDB.GetSystemGroups
type GroupPrimeDBMockGetSystemGroupsResults struct {
	ga1 []groupentity.Group
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) Optional() *mGroupPrimeDBMockGetSystemGroups {
	mmGetSystemGroups.optional = true
	return mmGetSystemGroups
}

// Expect sets up expected params for GroupPrimeDB.GetSystemGroups
func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) Expect() *mGroupPrimeDBMockGetSystemGroups {
	if mmGetSystemGroups.mock.funcGetSystemGroups != nil {
		mmGetSystemGroups.mock.t.Fatalf("GroupPrimeDBMock.GetSystemGroups mock is already set by Set")
	}

	if mmGetSystemGroups.defaultExpectation == nil {
		mmGetSystemGroups.defaultExpectation = &GroupPrimeDBMockGetSystemGroupsExpectation{}
	}

	return mmGetSystemGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetSystemGroups
func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) Inspect(f func()) *mGroupPrimeDBMockGetSystemGroups {
	if mmGetSystemGroups.mock.inspectFuncGetSystemGroups != nil {
		mmGetSystemGroups.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetSystemGroups")
	}

	mmGetSystemGroups.mock.inspectFuncGetSystemGroups = f

	return mmGetSystemGroups
}

// Return sets up results that will be returned by GroupPrimeDB.GetSystemGroups
func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) Return(ga1 []groupentity.Group, err error) *GroupPrimeDBMock {
	if mmGetSystemGroups.mock.funcGetSystemGroups != nil {
		mmGetSystemGroups.mock.t.Fatalf("GroupPrimeDBMock.GetSystemGroups mock is already set by Set")
	}

	if mmGetSystemGroups.defaultExpectation == nil {
		mmGetSystemGroups.defaultExpectation = &GroupPrimeDBMockGetSystemGroupsExpectation{mock: mmGetSystemGroups.mock}
	}
	mmGetSystemGroups.defaultExpectation.results = &GroupPrimeDBMockGetSystemGroupsResults{ga1, err}
	mmGetSystemGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroups.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetSystemGroups method
func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) Set(f func() (ga1 []groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmGetSystemGroups.defaultExpectation != nil {
		mmGetSystemGroups.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetSystemGroups method")
	}

	if len(mmGetSystemGroups.expectations) > 0 {
		mmGetSystemGroups.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetSystemGroups method")
	}

	mmGetSystemGroups.mock.funcGetSystemGroups = f
	mmGetSystemGroups.mock.funcGetSystemGroupsOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroups.mock
}

// Times sets number of times GroupPrimeDB.GetSystemGroups should be invoked
func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) Times(n uint64) *mGroupPrimeDBMockGetSystemGroups {
	if n == 0 {
		mmGetSystemGroups.mock.t.Fatalf("Times of GroupPrimeDBMock.GetSystemGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetSystemGroups.expectedInvocations, n)
	mmGetSystemGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroups
}

func (mmGetSystemGroups *mGroupPrimeDBMockGetSystemGroups) invocationsDone() bool {
	if len(mmGetSystemGroups.expectations) == 0 && mmGetSystemGroups.defaultExpectation == nil && mmGetSystemGroups.mock.funcGetSystemGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetSystemGroups.mock.afterGetSystemGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetSystemGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetSystemGroups implements mm_repository.GroupPrimeDB
func (mmGetSystemGroups *GroupPrimeDBMock) GetSystemGroups() (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetSystemGroups.beforeGetSystemGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetSystemGroups.afterGetSystemGroupsCounter, 1)

	mmGetSystemGroups.t.Helper()

	if mmGetSystemGroups.inspectFuncGetSystemGroups != nil {
		mmGetSystemGroups.inspectFuncGetSystemGroups()
	}

	if mmGetSystemGroups.GetSystemGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetSystemGroups.GetSystemGroupsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetSystemGroups.GetSystemGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetSystemGroups.t.Fatal("No results are set for the GroupPrimeDBMock.GetSystemGroups")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetSystemGroups.funcGetSystemGroups != nil {
		return mmGetSystemGroups.funcGetSystemGroups()
	}
	mmGetSystemGroups.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetSystemGroups.")
	return
}

// GetSystemGroupsAfterCounter returns a count of finished GroupPrimeDBMock.GetSystemGroups invocations
func (mmGetSystemGroups *GroupPrimeDBMock) GetSystemGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemGroups.afterGetSystemGroupsCounter)
}

// GetSystemGroupsBeforeCounter returns a count of GroupPrimeDBMock.GetSystemGroups invocations
func (mmGetSystemGroups *GroupPrimeDBMock) GetSystemGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemGroups.beforeGetSystemGroupsCounter)
}

// MinimockGetSystemGroupsDone returns true if the count of the GetSystemGroups invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetSystemGroupsDone() bool {
	if m.GetSystemGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetSystemGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetSystemGroupsMock.invocationsDone()
}

// MinimockGetSystemGroupsInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetSystemGroupsInspect() {
	for _, e := range m.GetSystemGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupPrimeDBMock.GetSystemGroups")
		}
	}

	afterGetSystemGroupsCounter := mm_atomic.LoadUint64(&m.afterGetSystemGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetSystemGroupsMock.defaultExpectation != nil && afterGetSystemGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetSystemGroups at\n%s", m.GetSystemGroupsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetSystemGroups != nil && afterGetSystemGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetSystemGroups at\n%s", m.funcGetSystemGroupsOrigin)
	}

	if !m.GetSystemGroupsMock.invocationsDone() && afterGetSystemGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetSystemGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetSystemGroupsMock.expectedInvocations), m.GetSystemGroupsMock.expectedInvocationsOrigin, afterGetSystemGroupsCounter)
	}
}

type mGroupPrimeDBMockGetSystemGroupsWithStats struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockGetSystemGroupsWithStatsExpectation
	expectations       []*GroupPrimeDBMockGetSystemGroupsWithStatsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockGetSystemGroupsWithStatsExpectation specifies expectation struct of the GroupPrimeDB.GetSystemGroupsWithStats
type GroupPrimeDBMockGetSystemGroupsWithStatsExpectation struct {
	mock *GroupPrimeDBMock

	results      *GroupPrimeDBMockGetSystemGroupsWithStatsResults
	returnOrigin string
	Counter      uint64
}

// GroupPrimeDBMockGetSystemGroupsWithStatsResults contains results of the GroupPrimeDB.GetSystemGroupsWithStats
type GroupPrimeDBMockGetSystemGroupsWithStatsResults struct {
	ga1 []groupentity.GroupWithStats
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) Optional() *mGroupPrimeDBMockGetSystemGroupsWithStats {
	mmGetSystemGroupsWithStats.optional = true
	return mmGetSystemGroupsWithStats
}

// Expect sets up expected params for GroupPrimeDB.GetSystemGroupsWithStats
func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) Expect() *mGroupPrimeDBMockGetSystemGroupsWithStats {
	if mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("GroupPrimeDBMock.GetSystemGroupsWithStats mock is already set by Set")
	}

	if mmGetSystemGroupsWithStats.defaultExpectation == nil {
		mmGetSystemGroupsWithStats.defaultExpectation = &GroupPrimeDBMockGetSystemGroupsWithStatsExpectation{}
	}

	return mmGetSystemGroupsWithStats
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.GetSystemGroupsWithStats
func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) Inspect(f func()) *mGroupPrimeDBMockGetSystemGroupsWithStats {
	if mmGetSystemGroupsWithStats.mock.inspectFuncGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.GetSystemGroupsWithStats")
	}

	mmGetSystemGroupsWithStats.mock.inspectFuncGetSystemGroupsWithStats = f

	return mmGetSystemGroupsWithStats
}

// Return sets up results that will be returned by GroupPrimeDB.GetSystemGroupsWithStats
func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) Return(ga1 []groupentity.GroupWithStats, err error) *GroupPrimeDBMock {
	if mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("GroupPrimeDBMock.GetSystemGroupsWithStats mock is already set by Set")
	}

	if mmGetSystemGroupsWithStats.defaultExpectation == nil {
		mmGetSystemGroupsWithStats.defaultExpectation = &GroupPrimeDBMockGetSystemGroupsWithStatsExpectation{mock: mmGetSystemGroupsWithStats.mock}
	}
	mmGetSystemGroupsWithStats.defaultExpectation.results = &GroupPrimeDBMockGetSystemGroupsWithStatsResults{ga1, err}
	mmGetSystemGroupsWithStats.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroupsWithStats.mock
}

// Set uses given function f to mock the GroupPrimeDB.GetSystemGroupsWithStats method
func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) Set(f func() (ga1 []groupentity.GroupWithStats, err error)) *GroupPrimeDBMock {
	if mmGetSystemGroupsWithStats.defaultExpectation != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.GetSystemGroupsWithStats method")
	}

	if len(mmGetSystemGroupsWithStats.expectations) > 0 {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.GetSystemGroupsWithStats method")
	}

	mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats = f
	mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStatsOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroupsWithStats.mock
}

// Times sets number of times GroupPrimeDB.GetSystemGroupsWithStats should be invoked
func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) Times(n uint64) *mGroupPrimeDBMockGetSystemGroupsWithStats {
	if n == 0 {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Times of GroupPrimeDBMock.GetSystemGroupsWithStats mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetSystemGroupsWithStats.expectedInvocations, n)
	mmGetSystemGroupsWithStats.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroupsWithStats
}

func (mmGetSystemGroupsWithStats *mGroupPrimeDBMockGetSystemGroupsWithStats) invocationsDone() bool {
	if len(mmGetSystemGroupsWithStats.expectations) == 0 && mmGetSystemGroupsWithStats.defaultExpectation == nil && mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.mock.afterGetSystemGroupsWithStatsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetSystemGroupsWithStats implements mm_repository.GroupPrimeDB
func (mmGetSystemGroupsWithStats *GroupPrimeDBMock) GetSystemGroupsWithStats() (ga1 []groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmGetSystemGroupsWithStats.beforeGetSystemGroupsWithStatsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetSystemGroupsWithStats.afterGetSystemGroupsWithStatsCounter, 1)

	mmGetSystemGroupsWithStats.t.Helper()

	if mmGetSystemGroupsWithStats.inspectFuncGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.inspectFuncGetSystemGroupsWithStats()
	}

	if mmGetSystemGroupsWithStats.GetSystemGroupsWithStatsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetSystemGroupsWithStats.GetSystemGroupsWithStatsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetSystemGroupsWithStats.GetSystemGroupsWithStatsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetSystemGroupsWithStats.t.Fatal("No results are set for the GroupPrimeDBMock.GetSystemGroupsWithStats")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetSystemGroupsWithStats.funcGetSystemGroupsWithStats != nil {
		return mmGetSystemGroupsWithStats.funcGetSystemGroupsWithStats()
	}
	mmGetSystemGroupsWithStats.t.Fatalf("Unexpected call to GroupPrimeDBMock.GetSystemGroupsWithStats.")
	return
}

// GetSystemGroupsWithStatsAfterCounter returns a count of finished GroupPrimeDBMock.GetSystemGroupsWithStats invocations
func (mmGetSystemGroupsWithStats *GroupPrimeDBMock) GetSystemGroupsWithStatsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.afterGetSystemGroupsWithStatsCounter)
}

// GetSystemGroupsWithStatsBeforeCounter returns a count of GroupPrimeDBMock.GetSystemGroupsWithStats invocations
func (mmGetSystemGroupsWithStats *GroupPrimeDBMock) GetSystemGroupsWithStatsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.beforeGetSystemGroupsWithStatsCounter)
}

// MinimockGetSystemGroupsWithStatsDone returns true if the count of the GetSystemGroupsWithStats invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockGetSystemGroupsWithStatsDone() bool {
	if m.GetSystemGroupsWithStatsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetSystemGroupsWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetSystemGroupsWithStatsMock.invocationsDone()
}

// MinimockGetSystemGroupsWithStatsInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockGetSystemGroupsWithStatsInspect() {
	for _, e := range m.GetSystemGroupsWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupPrimeDBMock.GetSystemGroupsWithStats")
		}
	}

	afterGetSystemGroupsWithStatsCounter := mm_atomic.LoadUint64(&m.afterGetSystemGroupsWithStatsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetSystemGroupsWithStatsMock.defaultExpectation != nil && afterGetSystemGroupsWithStatsCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetSystemGroupsWithStats at\n%s", m.GetSystemGroupsWithStatsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetSystemGroupsWithStats != nil && afterGetSystemGroupsWithStatsCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.GetSystemGroupsWithStats at\n%s", m.funcGetSystemGroupsWithStatsOrigin)
	}

	if !m.GetSystemGroupsWithStatsMock.invocationsDone() && afterGetSystemGroupsWithStatsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.GetSystemGroupsWithStats at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetSystemGroupsWithStatsMock.expectedInvocations), m.GetSystemGroupsWithStatsMock.expectedInvocationsOrigin, afterGetSystemGroupsWithStatsCounter)
	}
}

type mGroupPrimeDBMockUpdate struct {
	optional           bool
	mock               *GroupPrimeDBMock
	defaultExpectation *GroupPrimeDBMockUpdateExpectation
	expectations       []*GroupPrimeDBMockUpdateExpectation

	callArgs []*GroupPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupPrimeDBMockUpdateExpectation specifies expectation struct of the GroupPrimeDB.Update
type GroupPrimeDBMockUpdateExpectation struct {
	mock               *GroupPrimeDBMock
	params             *GroupPrimeDBMockUpdateParams
	paramPtrs          *GroupPrimeDBMockUpdateParamPtrs
	expectationOrigins GroupPrimeDBMockUpdateExpectationOrigins
	results            *GroupPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// GroupPrimeDBMockUpdateParams contains parameters of the GroupPrimeDB.Update
type GroupPrimeDBMockUpdateParams struct {
	group groupentity.GroupUpdateData
}

// GroupPrimeDBMockUpdateParamPtrs contains pointers to parameters of the GroupPrimeDB.Update
type GroupPrimeDBMockUpdateParamPtrs struct {
	group *groupentity.GroupUpdateData
}

// GroupPrimeDBMockUpdateResults contains results of the GroupPrimeDB.Update
type GroupPrimeDBMockUpdateResults struct {
	g1  groupentity.Group
	err error
}

// GroupPrimeDBMockUpdateOrigins contains origins of expectations of the GroupPrimeDB.Update
type GroupPrimeDBMockUpdateExpectationOrigins struct {
	origin      string
	originGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mGroupPrimeDBMockUpdate) Optional() *mGroupPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for GroupPrimeDB.Update
func (mmUpdate *mGroupPrimeDBMockUpdate) Expect(group groupentity.GroupUpdateData) *mGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &GroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("GroupPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &GroupPrimeDBMockUpdateParams{group}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectGroupParam1 sets up expected param group for GroupPrimeDB.Update
func (mmUpdate *mGroupPrimeDBMockUpdate) ExpectGroupParam1(group groupentity.GroupUpdateData) *mGroupPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &GroupPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("GroupPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &GroupPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.group = &group
	mmUpdate.defaultExpectation.expectationOrigins.originGroup = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the GroupPrimeDB.Update
func (mmUpdate *mGroupPrimeDBMockUpdate) Inspect(f func(group groupentity.GroupUpdateData)) *mGroupPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for GroupPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by GroupPrimeDB.Update
func (mmUpdate *mGroupPrimeDBMockUpdate) Return(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &GroupPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &GroupPrimeDBMockUpdateResults{g1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the GroupPrimeDB.Update method
func (mmUpdate *mGroupPrimeDBMockUpdate) Set(f func(group groupentity.GroupUpdateData) (g1 groupentity.Group, err error)) *GroupPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the GroupPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the GroupPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the GroupPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mGroupPrimeDBMockUpdate) When(group groupentity.GroupUpdateData) *GroupPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &GroupPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &GroupPrimeDBMockUpdateParams{group},
		expectationOrigins: GroupPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up GroupPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *GroupPrimeDBMockUpdateExpectation) Then(g1 groupentity.Group, err error) *GroupPrimeDBMock {
	e.results = &GroupPrimeDBMockUpdateResults{g1, err}
	return e.mock
}

// Times sets number of times GroupPrimeDB.Update should be invoked
func (mmUpdate *mGroupPrimeDBMockUpdate) Times(n uint64) *mGroupPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of GroupPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mGroupPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.GroupPrimeDB
func (mmUpdate *GroupPrimeDBMock) Update(group groupentity.GroupUpdateData) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(group)
	}

	mm_params := GroupPrimeDBMockUpdateParams{group}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := GroupPrimeDBMockUpdateParams{group}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.group != nil && !minimock.Equal(*mm_want_ptrs.group, mm_got.group) {
				mmUpdate.t.Errorf("GroupPrimeDBMock.Update got unexpected parameter group, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originGroup, *mm_want_ptrs.group, mm_got.group, minimock.Diff(*mm_want_ptrs.group, mm_got.group))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("GroupPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the GroupPrimeDBMock.Update")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(group)
	}
	mmUpdate.t.Fatalf("Unexpected call to GroupPrimeDBMock.Update. %v", group)
	return
}

// UpdateAfterCounter returns a count of finished GroupPrimeDBMock.Update invocations
func (mmUpdate *GroupPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of GroupPrimeDBMock.Update invocations
func (mmUpdate *GroupPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to GroupPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mGroupPrimeDBMockUpdate) Calls() []*GroupPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*GroupPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *GroupPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *GroupPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to GroupPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *GroupPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeactivateByIDsInspect()

			m.MinimockDeleteGroupInspect()

			m.MinimockExistByNameInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllByUserIDInspect()

			m.MinimockGetAllWithProductIDByUserIDInspect()

			m.MinimockGetByFiltersAndPaginationInspect()

			m.MinimockGetByGroupIDAndProductIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByProductIDAndIsActiveInspect()

			m.MinimockGetGroupCategoryStatesInspect()

			m.MinimockGetSystemGroupsInspect()

			m.MinimockGetSystemGroupsWithStatsInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *GroupPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *GroupPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeactivateByIDsDone() &&
		m.MinimockDeleteGroupDone() &&
		m.MinimockExistByNameDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllByUserIDDone() &&
		m.MinimockGetAllWithProductIDByUserIDDone() &&
		m.MinimockGetByFiltersAndPaginationDone() &&
		m.MinimockGetByGroupIDAndProductIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByProductIDAndIsActiveDone() &&
		m.MinimockGetGroupCategoryStatesDone() &&
		m.MinimockGetSystemGroupsDone() &&
		m.MinimockGetSystemGroupsWithStatsDone() &&
		m.MinimockUpdateDone()
}
