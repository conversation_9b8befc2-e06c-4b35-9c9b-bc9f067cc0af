// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/service.GroupDomainService -o group_domain_service_mock.go -n GroupDomainServiceMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// GroupDomainServiceMock implements mm_service.GroupDomainService
type GroupDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcAssignToRoles          func(ctx context.Context, groupID int64, rolesIDs []int64) (err error)
	funcAssignToRolesOrigin    string
	inspectFuncAssignToRoles   func(ctx context.Context, groupID int64, rolesIDs []int64)
	afterAssignToRolesCounter  uint64
	beforeAssignToRolesCounter uint64
	AssignToRolesMock          mGroupDomainServiceMockAssignToRoles

	funcCreate          func(group groupentity.Group) (g1 groupentity.GroupWithStats, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(group groupentity.Group)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mGroupDomainServiceMockCreate

	funcCreateAsAdmin          func(ctx context.Context, data groupentity.Group) (g1 groupentity.Group, err error)
	funcCreateAsAdminOrigin    string
	inspectFuncCreateAsAdmin   func(ctx context.Context, data groupentity.Group)
	afterCreateAsAdminCounter  uint64
	beforeCreateAsAdminCounter uint64
	CreateAsAdminMock          mGroupDomainServiceMockCreateAsAdmin

	funcDelete          func(productID int64, groupID int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(productID int64, groupID int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mGroupDomainServiceMockDelete

	funcDeleteAsAdmin          func(groupID int64) (err error)
	funcDeleteAsAdminOrigin    string
	inspectFuncDeleteAsAdmin   func(groupID int64)
	afterDeleteAsAdminCounter  uint64
	beforeDeleteAsAdminCounter uint64
	DeleteAsAdminMock          mGroupDomainServiceMockDeleteAsAdmin

	funcGetAll          func() (ga1 []groupentity.Group, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mGroupDomainServiceMockGetAll

	funcGetAllAsAdmin          func() (aa1 []groupentity.AdminGroup, err error)
	funcGetAllAsAdminOrigin    string
	inspectFuncGetAllAsAdmin   func()
	afterGetAllAsAdminCounter  uint64
	beforeGetAllAsAdminCounter uint64
	GetAllAsAdminMock          mGroupDomainServiceMockGetAllAsAdmin

	funcGetAvailableSystemGroups          func(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) (ga1 []groupentity.GroupWithStats, err error)
	funcGetAvailableSystemGroupsOrigin    string
	inspectFuncGetAvailableSystemGroups   func(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64)
	afterGetAvailableSystemGroupsCounter  uint64
	beforeGetAvailableSystemGroupsCounter uint64
	GetAvailableSystemGroupsMock          mGroupDomainServiceMockGetAvailableSystemGroups

	funcGetByCategoryID          func(categoryID int64) (ga1 []groupentity.GroupWithStats, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mGroupDomainServiceMockGetByCategoryID

	funcGetByGroupIDAndProductID          func(groupID int64, productID int64) (g1 groupentity.GroupFull, err error)
	funcGetByGroupIDAndProductIDOrigin    string
	inspectFuncGetByGroupIDAndProductID   func(groupID int64, productID int64)
	afterGetByGroupIDAndProductIDCounter  uint64
	beforeGetByGroupIDAndProductIDCounter uint64
	GetByGroupIDAndProductIDMock          mGroupDomainServiceMockGetByGroupIDAndProductID

	funcGetByID          func(id int64) (g1 groupentity.Group, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mGroupDomainServiceMockGetByID

	funcGetByParticipantID          func(participantID int64) (ga1 []groupentity.GroupWithStats, err error)
	funcGetByParticipantIDOrigin    string
	inspectFuncGetByParticipantID   func(participantID int64)
	afterGetByParticipantIDCounter  uint64
	beforeGetByParticipantIDCounter uint64
	GetByParticipantIDMock          mGroupDomainServiceMockGetByParticipantID

	funcGetByProductID          func(productID int64) (ga1 []groupentity.Group, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mGroupDomainServiceMockGetByProductID

	funcGetByRoleID          func(roleID int64) (ga1 []groupentity.Group, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mGroupDomainServiceMockGetByRoleID

	funcGetByUserID          func(userID int64) (ga1 []groupentity.Group, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mGroupDomainServiceMockGetByUserID

	funcGetGroupsWithCategoryStats          func() (ga1 []groupentity.GroupWithCategoryStats, err error)
	funcGetGroupsWithCategoryStatsOrigin    string
	inspectFuncGetGroupsWithCategoryStats   func()
	afterGetGroupsWithCategoryStatsCounter  uint64
	beforeGetGroupsWithCategoryStatsCounter uint64
	GetGroupsWithCategoryStatsMock          mGroupDomainServiceMockGetGroupsWithCategoryStats

	funcGetParticipantGroupsWithProductByUserID          func(userID int64) (ga1 []groupentity.Group, err error)
	funcGetParticipantGroupsWithProductByUserIDOrigin    string
	inspectFuncGetParticipantGroupsWithProductByUserID   func(userID int64)
	afterGetParticipantGroupsWithProductByUserIDCounter  uint64
	beforeGetParticipantGroupsWithProductByUserIDCounter uint64
	GetParticipantGroupsWithProductByUserIDMock          mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID

	funcGetSystemGroupsWithStats          func() (ga1 []groupentity.GroupWithStats, err error)
	funcGetSystemGroupsWithStatsOrigin    string
	inspectFuncGetSystemGroupsWithStats   func()
	afterGetSystemGroupsWithStatsCounter  uint64
	beforeGetSystemGroupsWithStatsCounter uint64
	GetSystemGroupsWithStatsMock          mGroupDomainServiceMockGetSystemGroupsWithStats

	funcGetWithCountsByProductID          func(productID int64) (aa1 []groupentity.AdminGroup, err error)
	funcGetWithCountsByProductIDOrigin    string
	inspectFuncGetWithCountsByProductID   func(productID int64)
	afterGetWithCountsByProductIDCounter  uint64
	beforeGetWithCountsByProductIDCounter uint64
	GetWithCountsByProductIDMock          mGroupDomainServiceMockGetWithCountsByProductID

	funcGetWithProductByUserID          func(userID int64) (ga1 []groupentity.GroupWithProduct, err error)
	funcGetWithProductByUserIDOrigin    string
	inspectFuncGetWithProductByUserID   func(userID int64)
	afterGetWithProductByUserIDCounter  uint64
	beforeGetWithProductByUserIDCounter uint64
	GetWithProductByUserIDMock          mGroupDomainServiceMockGetWithProductByUserID

	funcGetWithProductsByRoleID          func(roleID int64) (ga1 []groupentity.GroupWithProduct, err error)
	funcGetWithProductsByRoleIDOrigin    string
	inspectFuncGetWithProductsByRoleID   func(roleID int64)
	afterGetWithProductsByRoleIDCounter  uint64
	beforeGetWithProductsByRoleIDCounter uint64
	GetWithProductsByRoleIDMock          mGroupDomainServiceMockGetWithProductsByRoleID

	funcGetWithStatsByProductID          func(productID int64) (ga1 []groupentity.GroupWithStats, err error)
	funcGetWithStatsByProductIDOrigin    string
	inspectFuncGetWithStatsByProductID   func(productID int64)
	afterGetWithStatsByProductIDCounter  uint64
	beforeGetWithStatsByProductIDCounter uint64
	GetWithStatsByProductIDMock          mGroupDomainServiceMockGetWithStatsByProductID

	funcUnassignFromRoles          func(ctx context.Context, groupID int64, roleIDs []int64) (err error)
	funcUnassignFromRolesOrigin    string
	inspectFuncUnassignFromRoles   func(ctx context.Context, groupID int64, roleIDs []int64)
	afterUnassignFromRolesCounter  uint64
	beforeUnassignFromRolesCounter uint64
	UnassignFromRolesMock          mGroupDomainServiceMockUnassignFromRoles

	funcUpdate          func(group groupentity.GroupUpdateData) (g1 groupentity.Group, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(group groupentity.GroupUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mGroupDomainServiceMockUpdate

	funcUpdateByGroupFull          func(ctx context.Context, group groupentity.GroupFull, productID int64) (g1 groupentity.GroupFull, err error)
	funcUpdateByGroupFullOrigin    string
	inspectFuncUpdateByGroupFull   func(ctx context.Context, group groupentity.GroupFull, productID int64)
	afterUpdateByGroupFullCounter  uint64
	beforeUpdateByGroupFullCounter uint64
	UpdateByGroupFullMock          mGroupDomainServiceMockUpdateByGroupFull

	funcUpdateCategoryStates          func(groupsWithCategories []groupentity.GroupWithCategoryStats) (err error)
	funcUpdateCategoryStatesOrigin    string
	inspectFuncUpdateCategoryStates   func(groupsWithCategories []groupentity.GroupWithCategoryStats)
	afterUpdateCategoryStatesCounter  uint64
	beforeUpdateCategoryStatesCounter uint64
	UpdateCategoryStatesMock          mGroupDomainServiceMockUpdateCategoryStates

	funcUpdateLinksWithRoles          func(ctx context.Context, groupID int64, roles []roleentity.RoleID) (err error)
	funcUpdateLinksWithRolesOrigin    string
	inspectFuncUpdateLinksWithRoles   func(ctx context.Context, groupID int64, roles []roleentity.RoleID)
	afterUpdateLinksWithRolesCounter  uint64
	beforeUpdateLinksWithRolesCounter uint64
	UpdateLinksWithRolesMock          mGroupDomainServiceMockUpdateLinksWithRoles

	funcUpdateLinksWithUsers          func(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) (err error)
	funcUpdateLinksWithUsersOrigin    string
	inspectFuncUpdateLinksWithUsers   func(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink)
	afterUpdateLinksWithUsersCounter  uint64
	beforeUpdateLinksWithUsersCounter uint64
	UpdateLinksWithUsersMock          mGroupDomainServiceMockUpdateLinksWithUsers

	funcUpdateParticipantGroups          func(ctx context.Context, groupID int64, participantIDs []int64) (ia1 []int64, err error)
	funcUpdateParticipantGroupsOrigin    string
	inspectFuncUpdateParticipantGroups   func(ctx context.Context, groupID int64, participantIDs []int64)
	afterUpdateParticipantGroupsCounter  uint64
	beforeUpdateParticipantGroupsCounter uint64
	UpdateParticipantGroupsMock          mGroupDomainServiceMockUpdateParticipantGroups

	funcUpdateUserGroups          func(ctx context.Context, userID int64, user userentity.UserUpdateData) (err error)
	funcUpdateUserGroupsOrigin    string
	inspectFuncUpdateUserGroups   func(ctx context.Context, userID int64, user userentity.UserUpdateData)
	afterUpdateUserGroupsCounter  uint64
	beforeUpdateUserGroupsCounter uint64
	UpdateUserGroupsMock          mGroupDomainServiceMockUpdateUserGroups
}

// NewGroupDomainServiceMock returns a mock for mm_service.GroupDomainService
func NewGroupDomainServiceMock(t minimock.Tester) *GroupDomainServiceMock {
	m := &GroupDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.AssignToRolesMock = mGroupDomainServiceMockAssignToRoles{mock: m}
	m.AssignToRolesMock.callArgs = []*GroupDomainServiceMockAssignToRolesParams{}

	m.CreateMock = mGroupDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*GroupDomainServiceMockCreateParams{}

	m.CreateAsAdminMock = mGroupDomainServiceMockCreateAsAdmin{mock: m}
	m.CreateAsAdminMock.callArgs = []*GroupDomainServiceMockCreateAsAdminParams{}

	m.DeleteMock = mGroupDomainServiceMockDelete{mock: m}
	m.DeleteMock.callArgs = []*GroupDomainServiceMockDeleteParams{}

	m.DeleteAsAdminMock = mGroupDomainServiceMockDeleteAsAdmin{mock: m}
	m.DeleteAsAdminMock.callArgs = []*GroupDomainServiceMockDeleteAsAdminParams{}

	m.GetAllMock = mGroupDomainServiceMockGetAll{mock: m}

	m.GetAllAsAdminMock = mGroupDomainServiceMockGetAllAsAdmin{mock: m}

	m.GetAvailableSystemGroupsMock = mGroupDomainServiceMockGetAvailableSystemGroups{mock: m}
	m.GetAvailableSystemGroupsMock.callArgs = []*GroupDomainServiceMockGetAvailableSystemGroupsParams{}

	m.GetByCategoryIDMock = mGroupDomainServiceMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*GroupDomainServiceMockGetByCategoryIDParams{}

	m.GetByGroupIDAndProductIDMock = mGroupDomainServiceMockGetByGroupIDAndProductID{mock: m}
	m.GetByGroupIDAndProductIDMock.callArgs = []*GroupDomainServiceMockGetByGroupIDAndProductIDParams{}

	m.GetByIDMock = mGroupDomainServiceMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*GroupDomainServiceMockGetByIDParams{}

	m.GetByParticipantIDMock = mGroupDomainServiceMockGetByParticipantID{mock: m}
	m.GetByParticipantIDMock.callArgs = []*GroupDomainServiceMockGetByParticipantIDParams{}

	m.GetByProductIDMock = mGroupDomainServiceMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*GroupDomainServiceMockGetByProductIDParams{}

	m.GetByRoleIDMock = mGroupDomainServiceMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*GroupDomainServiceMockGetByRoleIDParams{}

	m.GetByUserIDMock = mGroupDomainServiceMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*GroupDomainServiceMockGetByUserIDParams{}

	m.GetGroupsWithCategoryStatsMock = mGroupDomainServiceMockGetGroupsWithCategoryStats{mock: m}

	m.GetParticipantGroupsWithProductByUserIDMock = mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID{mock: m}
	m.GetParticipantGroupsWithProductByUserIDMock.callArgs = []*GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams{}

	m.GetSystemGroupsWithStatsMock = mGroupDomainServiceMockGetSystemGroupsWithStats{mock: m}

	m.GetWithCountsByProductIDMock = mGroupDomainServiceMockGetWithCountsByProductID{mock: m}
	m.GetWithCountsByProductIDMock.callArgs = []*GroupDomainServiceMockGetWithCountsByProductIDParams{}

	m.GetWithProductByUserIDMock = mGroupDomainServiceMockGetWithProductByUserID{mock: m}
	m.GetWithProductByUserIDMock.callArgs = []*GroupDomainServiceMockGetWithProductByUserIDParams{}

	m.GetWithProductsByRoleIDMock = mGroupDomainServiceMockGetWithProductsByRoleID{mock: m}
	m.GetWithProductsByRoleIDMock.callArgs = []*GroupDomainServiceMockGetWithProductsByRoleIDParams{}

	m.GetWithStatsByProductIDMock = mGroupDomainServiceMockGetWithStatsByProductID{mock: m}
	m.GetWithStatsByProductIDMock.callArgs = []*GroupDomainServiceMockGetWithStatsByProductIDParams{}

	m.UnassignFromRolesMock = mGroupDomainServiceMockUnassignFromRoles{mock: m}
	m.UnassignFromRolesMock.callArgs = []*GroupDomainServiceMockUnassignFromRolesParams{}

	m.UpdateMock = mGroupDomainServiceMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*GroupDomainServiceMockUpdateParams{}

	m.UpdateByGroupFullMock = mGroupDomainServiceMockUpdateByGroupFull{mock: m}
	m.UpdateByGroupFullMock.callArgs = []*GroupDomainServiceMockUpdateByGroupFullParams{}

	m.UpdateCategoryStatesMock = mGroupDomainServiceMockUpdateCategoryStates{mock: m}
	m.UpdateCategoryStatesMock.callArgs = []*GroupDomainServiceMockUpdateCategoryStatesParams{}

	m.UpdateLinksWithRolesMock = mGroupDomainServiceMockUpdateLinksWithRoles{mock: m}
	m.UpdateLinksWithRolesMock.callArgs = []*GroupDomainServiceMockUpdateLinksWithRolesParams{}

	m.UpdateLinksWithUsersMock = mGroupDomainServiceMockUpdateLinksWithUsers{mock: m}
	m.UpdateLinksWithUsersMock.callArgs = []*GroupDomainServiceMockUpdateLinksWithUsersParams{}

	m.UpdateParticipantGroupsMock = mGroupDomainServiceMockUpdateParticipantGroups{mock: m}
	m.UpdateParticipantGroupsMock.callArgs = []*GroupDomainServiceMockUpdateParticipantGroupsParams{}

	m.UpdateUserGroupsMock = mGroupDomainServiceMockUpdateUserGroups{mock: m}
	m.UpdateUserGroupsMock.callArgs = []*GroupDomainServiceMockUpdateUserGroupsParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mGroupDomainServiceMockAssignToRoles struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockAssignToRolesExpectation
	expectations       []*GroupDomainServiceMockAssignToRolesExpectation

	callArgs []*GroupDomainServiceMockAssignToRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockAssignToRolesExpectation specifies expectation struct of the GroupDomainService.AssignToRoles
type GroupDomainServiceMockAssignToRolesExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockAssignToRolesParams
	paramPtrs          *GroupDomainServiceMockAssignToRolesParamPtrs
	expectationOrigins GroupDomainServiceMockAssignToRolesExpectationOrigins
	results            *GroupDomainServiceMockAssignToRolesResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockAssignToRolesParams contains parameters of the GroupDomainService.AssignToRoles
type GroupDomainServiceMockAssignToRolesParams struct {
	ctx      context.Context
	groupID  int64
	rolesIDs []int64
}

// GroupDomainServiceMockAssignToRolesParamPtrs contains pointers to parameters of the GroupDomainService.AssignToRoles
type GroupDomainServiceMockAssignToRolesParamPtrs struct {
	ctx      *context.Context
	groupID  *int64
	rolesIDs *[]int64
}

// GroupDomainServiceMockAssignToRolesResults contains results of the GroupDomainService.AssignToRoles
type GroupDomainServiceMockAssignToRolesResults struct {
	err error
}

// GroupDomainServiceMockAssignToRolesOrigins contains origins of expectations of the GroupDomainService.AssignToRoles
type GroupDomainServiceMockAssignToRolesExpectationOrigins struct {
	origin         string
	originCtx      string
	originGroupID  string
	originRolesIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Optional() *mGroupDomainServiceMockAssignToRoles {
	mmAssignToRoles.optional = true
	return mmAssignToRoles
}

// Expect sets up expected params for GroupDomainService.AssignToRoles
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Expect(ctx context.Context, groupID int64, rolesIDs []int64) *mGroupDomainServiceMockAssignToRoles {
	if mmAssignToRoles.mock.funcAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Set")
	}

	if mmAssignToRoles.defaultExpectation == nil {
		mmAssignToRoles.defaultExpectation = &GroupDomainServiceMockAssignToRolesExpectation{}
	}

	if mmAssignToRoles.defaultExpectation.paramPtrs != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by ExpectParams functions")
	}

	mmAssignToRoles.defaultExpectation.params = &GroupDomainServiceMockAssignToRolesParams{ctx, groupID, rolesIDs}
	mmAssignToRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmAssignToRoles.expectations {
		if minimock.Equal(e.params, mmAssignToRoles.defaultExpectation.params) {
			mmAssignToRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmAssignToRoles.defaultExpectation.params)
		}
	}

	return mmAssignToRoles
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.AssignToRoles
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockAssignToRoles {
	if mmAssignToRoles.mock.funcAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Set")
	}

	if mmAssignToRoles.defaultExpectation == nil {
		mmAssignToRoles.defaultExpectation = &GroupDomainServiceMockAssignToRolesExpectation{}
	}

	if mmAssignToRoles.defaultExpectation.params != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Expect")
	}

	if mmAssignToRoles.defaultExpectation.paramPtrs == nil {
		mmAssignToRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockAssignToRolesParamPtrs{}
	}
	mmAssignToRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmAssignToRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmAssignToRoles
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupDomainService.AssignToRoles
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) ExpectGroupIDParam2(groupID int64) *mGroupDomainServiceMockAssignToRoles {
	if mmAssignToRoles.mock.funcAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Set")
	}

	if mmAssignToRoles.defaultExpectation == nil {
		mmAssignToRoles.defaultExpectation = &GroupDomainServiceMockAssignToRolesExpectation{}
	}

	if mmAssignToRoles.defaultExpectation.params != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Expect")
	}

	if mmAssignToRoles.defaultExpectation.paramPtrs == nil {
		mmAssignToRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockAssignToRolesParamPtrs{}
	}
	mmAssignToRoles.defaultExpectation.paramPtrs.groupID = &groupID
	mmAssignToRoles.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmAssignToRoles
}

// ExpectRolesIDsParam3 sets up expected param rolesIDs for GroupDomainService.AssignToRoles
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) ExpectRolesIDsParam3(rolesIDs []int64) *mGroupDomainServiceMockAssignToRoles {
	if mmAssignToRoles.mock.funcAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Set")
	}

	if mmAssignToRoles.defaultExpectation == nil {
		mmAssignToRoles.defaultExpectation = &GroupDomainServiceMockAssignToRolesExpectation{}
	}

	if mmAssignToRoles.defaultExpectation.params != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Expect")
	}

	if mmAssignToRoles.defaultExpectation.paramPtrs == nil {
		mmAssignToRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockAssignToRolesParamPtrs{}
	}
	mmAssignToRoles.defaultExpectation.paramPtrs.rolesIDs = &rolesIDs
	mmAssignToRoles.defaultExpectation.expectationOrigins.originRolesIDs = minimock.CallerInfo(1)

	return mmAssignToRoles
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.AssignToRoles
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Inspect(f func(ctx context.Context, groupID int64, rolesIDs []int64)) *mGroupDomainServiceMockAssignToRoles {
	if mmAssignToRoles.mock.inspectFuncAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.AssignToRoles")
	}

	mmAssignToRoles.mock.inspectFuncAssignToRoles = f

	return mmAssignToRoles
}

// Return sets up results that will be returned by GroupDomainService.AssignToRoles
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Return(err error) *GroupDomainServiceMock {
	if mmAssignToRoles.mock.funcAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Set")
	}

	if mmAssignToRoles.defaultExpectation == nil {
		mmAssignToRoles.defaultExpectation = &GroupDomainServiceMockAssignToRolesExpectation{mock: mmAssignToRoles.mock}
	}
	mmAssignToRoles.defaultExpectation.results = &GroupDomainServiceMockAssignToRolesResults{err}
	mmAssignToRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmAssignToRoles.mock
}

// Set uses given function f to mock the GroupDomainService.AssignToRoles method
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Set(f func(ctx context.Context, groupID int64, rolesIDs []int64) (err error)) *GroupDomainServiceMock {
	if mmAssignToRoles.defaultExpectation != nil {
		mmAssignToRoles.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.AssignToRoles method")
	}

	if len(mmAssignToRoles.expectations) > 0 {
		mmAssignToRoles.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.AssignToRoles method")
	}

	mmAssignToRoles.mock.funcAssignToRoles = f
	mmAssignToRoles.mock.funcAssignToRolesOrigin = minimock.CallerInfo(1)
	return mmAssignToRoles.mock
}

// When sets expectation for the GroupDomainService.AssignToRoles which will trigger the result defined by the following
// Then helper
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) When(ctx context.Context, groupID int64, rolesIDs []int64) *GroupDomainServiceMockAssignToRolesExpectation {
	if mmAssignToRoles.mock.funcAssignToRoles != nil {
		mmAssignToRoles.mock.t.Fatalf("GroupDomainServiceMock.AssignToRoles mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockAssignToRolesExpectation{
		mock:               mmAssignToRoles.mock,
		params:             &GroupDomainServiceMockAssignToRolesParams{ctx, groupID, rolesIDs},
		expectationOrigins: GroupDomainServiceMockAssignToRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmAssignToRoles.expectations = append(mmAssignToRoles.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.AssignToRoles return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockAssignToRolesExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockAssignToRolesResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.AssignToRoles should be invoked
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Times(n uint64) *mGroupDomainServiceMockAssignToRoles {
	if n == 0 {
		mmAssignToRoles.mock.t.Fatalf("Times of GroupDomainServiceMock.AssignToRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmAssignToRoles.expectedInvocations, n)
	mmAssignToRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmAssignToRoles
}

func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) invocationsDone() bool {
	if len(mmAssignToRoles.expectations) == 0 && mmAssignToRoles.defaultExpectation == nil && mmAssignToRoles.mock.funcAssignToRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmAssignToRoles.mock.afterAssignToRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmAssignToRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// AssignToRoles implements mm_service.GroupDomainService
func (mmAssignToRoles *GroupDomainServiceMock) AssignToRoles(ctx context.Context, groupID int64, rolesIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmAssignToRoles.beforeAssignToRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmAssignToRoles.afterAssignToRolesCounter, 1)

	mmAssignToRoles.t.Helper()

	if mmAssignToRoles.inspectFuncAssignToRoles != nil {
		mmAssignToRoles.inspectFuncAssignToRoles(ctx, groupID, rolesIDs)
	}

	mm_params := GroupDomainServiceMockAssignToRolesParams{ctx, groupID, rolesIDs}

	// Record call args
	mmAssignToRoles.AssignToRolesMock.mutex.Lock()
	mmAssignToRoles.AssignToRolesMock.callArgs = append(mmAssignToRoles.AssignToRolesMock.callArgs, &mm_params)
	mmAssignToRoles.AssignToRolesMock.mutex.Unlock()

	for _, e := range mmAssignToRoles.AssignToRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmAssignToRoles.AssignToRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmAssignToRoles.AssignToRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmAssignToRoles.AssignToRolesMock.defaultExpectation.params
		mm_want_ptrs := mmAssignToRoles.AssignToRolesMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockAssignToRolesParams{ctx, groupID, rolesIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmAssignToRoles.t.Errorf("GroupDomainServiceMock.AssignToRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignToRoles.AssignToRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmAssignToRoles.t.Errorf("GroupDomainServiceMock.AssignToRoles got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignToRoles.AssignToRolesMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.rolesIDs != nil && !minimock.Equal(*mm_want_ptrs.rolesIDs, mm_got.rolesIDs) {
				mmAssignToRoles.t.Errorf("GroupDomainServiceMock.AssignToRoles got unexpected parameter rolesIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignToRoles.AssignToRolesMock.defaultExpectation.expectationOrigins.originRolesIDs, *mm_want_ptrs.rolesIDs, mm_got.rolesIDs, minimock.Diff(*mm_want_ptrs.rolesIDs, mm_got.rolesIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmAssignToRoles.t.Errorf("GroupDomainServiceMock.AssignToRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmAssignToRoles.AssignToRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmAssignToRoles.AssignToRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmAssignToRoles.t.Fatal("No results are set for the GroupDomainServiceMock.AssignToRoles")
		}
		return (*mm_results).err
	}
	if mmAssignToRoles.funcAssignToRoles != nil {
		return mmAssignToRoles.funcAssignToRoles(ctx, groupID, rolesIDs)
	}
	mmAssignToRoles.t.Fatalf("Unexpected call to GroupDomainServiceMock.AssignToRoles. %v %v %v", ctx, groupID, rolesIDs)
	return
}

// AssignToRolesAfterCounter returns a count of finished GroupDomainServiceMock.AssignToRoles invocations
func (mmAssignToRoles *GroupDomainServiceMock) AssignToRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignToRoles.afterAssignToRolesCounter)
}

// AssignToRolesBeforeCounter returns a count of GroupDomainServiceMock.AssignToRoles invocations
func (mmAssignToRoles *GroupDomainServiceMock) AssignToRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignToRoles.beforeAssignToRolesCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.AssignToRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmAssignToRoles *mGroupDomainServiceMockAssignToRoles) Calls() []*GroupDomainServiceMockAssignToRolesParams {
	mmAssignToRoles.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockAssignToRolesParams, len(mmAssignToRoles.callArgs))
	copy(argCopy, mmAssignToRoles.callArgs)

	mmAssignToRoles.mutex.RUnlock()

	return argCopy
}

// MinimockAssignToRolesDone returns true if the count of the AssignToRoles invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockAssignToRolesDone() bool {
	if m.AssignToRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.AssignToRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.AssignToRolesMock.invocationsDone()
}

// MinimockAssignToRolesInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockAssignToRolesInspect() {
	for _, e := range m.AssignToRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.AssignToRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterAssignToRolesCounter := mm_atomic.LoadUint64(&m.afterAssignToRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.AssignToRolesMock.defaultExpectation != nil && afterAssignToRolesCounter < 1 {
		if m.AssignToRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.AssignToRoles at\n%s", m.AssignToRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.AssignToRoles at\n%s with params: %#v", m.AssignToRolesMock.defaultExpectation.expectationOrigins.origin, *m.AssignToRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcAssignToRoles != nil && afterAssignToRolesCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.AssignToRoles at\n%s", m.funcAssignToRolesOrigin)
	}

	if !m.AssignToRolesMock.invocationsDone() && afterAssignToRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.AssignToRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.AssignToRolesMock.expectedInvocations), m.AssignToRolesMock.expectedInvocationsOrigin, afterAssignToRolesCounter)
	}
}

type mGroupDomainServiceMockCreate struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockCreateExpectation
	expectations       []*GroupDomainServiceMockCreateExpectation

	callArgs []*GroupDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockCreateExpectation specifies expectation struct of the GroupDomainService.Create
type GroupDomainServiceMockCreateExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockCreateParams
	paramPtrs          *GroupDomainServiceMockCreateParamPtrs
	expectationOrigins GroupDomainServiceMockCreateExpectationOrigins
	results            *GroupDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockCreateParams contains parameters of the GroupDomainService.Create
type GroupDomainServiceMockCreateParams struct {
	group groupentity.Group
}

// GroupDomainServiceMockCreateParamPtrs contains pointers to parameters of the GroupDomainService.Create
type GroupDomainServiceMockCreateParamPtrs struct {
	group *groupentity.Group
}

// GroupDomainServiceMockCreateResults contains results of the GroupDomainService.Create
type GroupDomainServiceMockCreateResults struct {
	g1  groupentity.GroupWithStats
	err error
}

// GroupDomainServiceMockCreateOrigins contains origins of expectations of the GroupDomainService.Create
type GroupDomainServiceMockCreateExpectationOrigins struct {
	origin      string
	originGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mGroupDomainServiceMockCreate) Optional() *mGroupDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for GroupDomainService.Create
func (mmCreate *mGroupDomainServiceMockCreate) Expect(group groupentity.Group) *mGroupDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("GroupDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &GroupDomainServiceMockCreateParams{group}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectGroupParam1 sets up expected param group for GroupDomainService.Create
func (mmCreate *mGroupDomainServiceMockCreate) ExpectGroupParam1(group groupentity.Group) *mGroupDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("GroupDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &GroupDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.group = &group
	mmCreate.defaultExpectation.expectationOrigins.originGroup = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.Create
func (mmCreate *mGroupDomainServiceMockCreate) Inspect(f func(group groupentity.Group)) *mGroupDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by GroupDomainService.Create
func (mmCreate *mGroupDomainServiceMockCreate) Return(g1 groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &GroupDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &GroupDomainServiceMockCreateResults{g1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the GroupDomainService.Create method
func (mmCreate *mGroupDomainServiceMockCreate) Set(f func(group groupentity.Group) (g1 groupentity.GroupWithStats, err error)) *GroupDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the GroupDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mGroupDomainServiceMockCreate) When(group groupentity.Group) *GroupDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("GroupDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &GroupDomainServiceMockCreateParams{group},
		expectationOrigins: GroupDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.Create return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockCreateExpectation) Then(g1 groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockCreateResults{g1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.Create should be invoked
func (mmCreate *mGroupDomainServiceMockCreate) Times(n uint64) *mGroupDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of GroupDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mGroupDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.GroupDomainService
func (mmCreate *GroupDomainServiceMock) Create(group groupentity.Group) (g1 groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(group)
	}

	mm_params := GroupDomainServiceMockCreateParams{group}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockCreateParams{group}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.group != nil && !minimock.Equal(*mm_want_ptrs.group, mm_got.group) {
				mmCreate.t.Errorf("GroupDomainServiceMock.Create got unexpected parameter group, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originGroup, *mm_want_ptrs.group, mm_got.group, minimock.Diff(*mm_want_ptrs.group, mm_got.group))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("GroupDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the GroupDomainServiceMock.Create")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(group)
	}
	mmCreate.t.Fatalf("Unexpected call to GroupDomainServiceMock.Create. %v", group)
	return
}

// CreateAfterCounter returns a count of finished GroupDomainServiceMock.Create invocations
func (mmCreate *GroupDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of GroupDomainServiceMock.Create invocations
func (mmCreate *GroupDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mGroupDomainServiceMockCreate) Calls() []*GroupDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mGroupDomainServiceMockCreateAsAdmin struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockCreateAsAdminExpectation
	expectations       []*GroupDomainServiceMockCreateAsAdminExpectation

	callArgs []*GroupDomainServiceMockCreateAsAdminParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockCreateAsAdminExpectation specifies expectation struct of the GroupDomainService.CreateAsAdmin
type GroupDomainServiceMockCreateAsAdminExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockCreateAsAdminParams
	paramPtrs          *GroupDomainServiceMockCreateAsAdminParamPtrs
	expectationOrigins GroupDomainServiceMockCreateAsAdminExpectationOrigins
	results            *GroupDomainServiceMockCreateAsAdminResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockCreateAsAdminParams contains parameters of the GroupDomainService.CreateAsAdmin
type GroupDomainServiceMockCreateAsAdminParams struct {
	ctx  context.Context
	data groupentity.Group
}

// GroupDomainServiceMockCreateAsAdminParamPtrs contains pointers to parameters of the GroupDomainService.CreateAsAdmin
type GroupDomainServiceMockCreateAsAdminParamPtrs struct {
	ctx  *context.Context
	data *groupentity.Group
}

// GroupDomainServiceMockCreateAsAdminResults contains results of the GroupDomainService.CreateAsAdmin
type GroupDomainServiceMockCreateAsAdminResults struct {
	g1  groupentity.Group
	err error
}

// GroupDomainServiceMockCreateAsAdminOrigins contains origins of expectations of the GroupDomainService.CreateAsAdmin
type GroupDomainServiceMockCreateAsAdminExpectationOrigins struct {
	origin     string
	originCtx  string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Optional() *mGroupDomainServiceMockCreateAsAdmin {
	mmCreateAsAdmin.optional = true
	return mmCreateAsAdmin
}

// Expect sets up expected params for GroupDomainService.CreateAsAdmin
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Expect(ctx context.Context, data groupentity.Group) *mGroupDomainServiceMockCreateAsAdmin {
	if mmCreateAsAdmin.mock.funcCreateAsAdmin != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Set")
	}

	if mmCreateAsAdmin.defaultExpectation == nil {
		mmCreateAsAdmin.defaultExpectation = &GroupDomainServiceMockCreateAsAdminExpectation{}
	}

	if mmCreateAsAdmin.defaultExpectation.paramPtrs != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by ExpectParams functions")
	}

	mmCreateAsAdmin.defaultExpectation.params = &GroupDomainServiceMockCreateAsAdminParams{ctx, data}
	mmCreateAsAdmin.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateAsAdmin.expectations {
		if minimock.Equal(e.params, mmCreateAsAdmin.defaultExpectation.params) {
			mmCreateAsAdmin.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateAsAdmin.defaultExpectation.params)
		}
	}

	return mmCreateAsAdmin
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.CreateAsAdmin
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockCreateAsAdmin {
	if mmCreateAsAdmin.mock.funcCreateAsAdmin != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Set")
	}

	if mmCreateAsAdmin.defaultExpectation == nil {
		mmCreateAsAdmin.defaultExpectation = &GroupDomainServiceMockCreateAsAdminExpectation{}
	}

	if mmCreateAsAdmin.defaultExpectation.params != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Expect")
	}

	if mmCreateAsAdmin.defaultExpectation.paramPtrs == nil {
		mmCreateAsAdmin.defaultExpectation.paramPtrs = &GroupDomainServiceMockCreateAsAdminParamPtrs{}
	}
	mmCreateAsAdmin.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateAsAdmin.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateAsAdmin
}

// ExpectDataParam2 sets up expected param data for GroupDomainService.CreateAsAdmin
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) ExpectDataParam2(data groupentity.Group) *mGroupDomainServiceMockCreateAsAdmin {
	if mmCreateAsAdmin.mock.funcCreateAsAdmin != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Set")
	}

	if mmCreateAsAdmin.defaultExpectation == nil {
		mmCreateAsAdmin.defaultExpectation = &GroupDomainServiceMockCreateAsAdminExpectation{}
	}

	if mmCreateAsAdmin.defaultExpectation.params != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Expect")
	}

	if mmCreateAsAdmin.defaultExpectation.paramPtrs == nil {
		mmCreateAsAdmin.defaultExpectation.paramPtrs = &GroupDomainServiceMockCreateAsAdminParamPtrs{}
	}
	mmCreateAsAdmin.defaultExpectation.paramPtrs.data = &data
	mmCreateAsAdmin.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreateAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.CreateAsAdmin
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Inspect(f func(ctx context.Context, data groupentity.Group)) *mGroupDomainServiceMockCreateAsAdmin {
	if mmCreateAsAdmin.mock.inspectFuncCreateAsAdmin != nil {
		mmCreateAsAdmin.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.CreateAsAdmin")
	}

	mmCreateAsAdmin.mock.inspectFuncCreateAsAdmin = f

	return mmCreateAsAdmin
}

// Return sets up results that will be returned by GroupDomainService.CreateAsAdmin
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Return(g1 groupentity.Group, err error) *GroupDomainServiceMock {
	if mmCreateAsAdmin.mock.funcCreateAsAdmin != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Set")
	}

	if mmCreateAsAdmin.defaultExpectation == nil {
		mmCreateAsAdmin.defaultExpectation = &GroupDomainServiceMockCreateAsAdminExpectation{mock: mmCreateAsAdmin.mock}
	}
	mmCreateAsAdmin.defaultExpectation.results = &GroupDomainServiceMockCreateAsAdminResults{g1, err}
	mmCreateAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateAsAdmin.mock
}

// Set uses given function f to mock the GroupDomainService.CreateAsAdmin method
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Set(f func(ctx context.Context, data groupentity.Group) (g1 groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmCreateAsAdmin.defaultExpectation != nil {
		mmCreateAsAdmin.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.CreateAsAdmin method")
	}

	if len(mmCreateAsAdmin.expectations) > 0 {
		mmCreateAsAdmin.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.CreateAsAdmin method")
	}

	mmCreateAsAdmin.mock.funcCreateAsAdmin = f
	mmCreateAsAdmin.mock.funcCreateAsAdminOrigin = minimock.CallerInfo(1)
	return mmCreateAsAdmin.mock
}

// When sets expectation for the GroupDomainService.CreateAsAdmin which will trigger the result defined by the following
// Then helper
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) When(ctx context.Context, data groupentity.Group) *GroupDomainServiceMockCreateAsAdminExpectation {
	if mmCreateAsAdmin.mock.funcCreateAsAdmin != nil {
		mmCreateAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.CreateAsAdmin mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockCreateAsAdminExpectation{
		mock:               mmCreateAsAdmin.mock,
		params:             &GroupDomainServiceMockCreateAsAdminParams{ctx, data},
		expectationOrigins: GroupDomainServiceMockCreateAsAdminExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateAsAdmin.expectations = append(mmCreateAsAdmin.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.CreateAsAdmin return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockCreateAsAdminExpectation) Then(g1 groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockCreateAsAdminResults{g1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.CreateAsAdmin should be invoked
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Times(n uint64) *mGroupDomainServiceMockCreateAsAdmin {
	if n == 0 {
		mmCreateAsAdmin.mock.t.Fatalf("Times of GroupDomainServiceMock.CreateAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateAsAdmin.expectedInvocations, n)
	mmCreateAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateAsAdmin
}

func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) invocationsDone() bool {
	if len(mmCreateAsAdmin.expectations) == 0 && mmCreateAsAdmin.defaultExpectation == nil && mmCreateAsAdmin.mock.funcCreateAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateAsAdmin.mock.afterCreateAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateAsAdmin implements mm_service.GroupDomainService
func (mmCreateAsAdmin *GroupDomainServiceMock) CreateAsAdmin(ctx context.Context, data groupentity.Group) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmCreateAsAdmin.beforeCreateAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateAsAdmin.afterCreateAsAdminCounter, 1)

	mmCreateAsAdmin.t.Helper()

	if mmCreateAsAdmin.inspectFuncCreateAsAdmin != nil {
		mmCreateAsAdmin.inspectFuncCreateAsAdmin(ctx, data)
	}

	mm_params := GroupDomainServiceMockCreateAsAdminParams{ctx, data}

	// Record call args
	mmCreateAsAdmin.CreateAsAdminMock.mutex.Lock()
	mmCreateAsAdmin.CreateAsAdminMock.callArgs = append(mmCreateAsAdmin.CreateAsAdminMock.callArgs, &mm_params)
	mmCreateAsAdmin.CreateAsAdminMock.mutex.Unlock()

	for _, e := range mmCreateAsAdmin.CreateAsAdminMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.params
		mm_want_ptrs := mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockCreateAsAdminParams{ctx, data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateAsAdmin.t.Errorf("GroupDomainServiceMock.CreateAsAdmin got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreateAsAdmin.t.Errorf("GroupDomainServiceMock.CreateAsAdmin got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateAsAdmin.t.Errorf("GroupDomainServiceMock.CreateAsAdmin got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateAsAdmin.CreateAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateAsAdmin.t.Fatal("No results are set for the GroupDomainServiceMock.CreateAsAdmin")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmCreateAsAdmin.funcCreateAsAdmin != nil {
		return mmCreateAsAdmin.funcCreateAsAdmin(ctx, data)
	}
	mmCreateAsAdmin.t.Fatalf("Unexpected call to GroupDomainServiceMock.CreateAsAdmin. %v %v", ctx, data)
	return
}

// CreateAsAdminAfterCounter returns a count of finished GroupDomainServiceMock.CreateAsAdmin invocations
func (mmCreateAsAdmin *GroupDomainServiceMock) CreateAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateAsAdmin.afterCreateAsAdminCounter)
}

// CreateAsAdminBeforeCounter returns a count of GroupDomainServiceMock.CreateAsAdmin invocations
func (mmCreateAsAdmin *GroupDomainServiceMock) CreateAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateAsAdmin.beforeCreateAsAdminCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.CreateAsAdmin.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateAsAdmin *mGroupDomainServiceMockCreateAsAdmin) Calls() []*GroupDomainServiceMockCreateAsAdminParams {
	mmCreateAsAdmin.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockCreateAsAdminParams, len(mmCreateAsAdmin.callArgs))
	copy(argCopy, mmCreateAsAdmin.callArgs)

	mmCreateAsAdmin.mutex.RUnlock()

	return argCopy
}

// MinimockCreateAsAdminDone returns true if the count of the CreateAsAdmin invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockCreateAsAdminDone() bool {
	if m.CreateAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateAsAdminMock.invocationsDone()
}

// MinimockCreateAsAdminInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockCreateAsAdminInspect() {
	for _, e := range m.CreateAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.CreateAsAdmin at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateAsAdminCounter := mm_atomic.LoadUint64(&m.afterCreateAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateAsAdminMock.defaultExpectation != nil && afterCreateAsAdminCounter < 1 {
		if m.CreateAsAdminMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.CreateAsAdmin at\n%s", m.CreateAsAdminMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.CreateAsAdmin at\n%s with params: %#v", m.CreateAsAdminMock.defaultExpectation.expectationOrigins.origin, *m.CreateAsAdminMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateAsAdmin != nil && afterCreateAsAdminCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.CreateAsAdmin at\n%s", m.funcCreateAsAdminOrigin)
	}

	if !m.CreateAsAdminMock.invocationsDone() && afterCreateAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.CreateAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateAsAdminMock.expectedInvocations), m.CreateAsAdminMock.expectedInvocationsOrigin, afterCreateAsAdminCounter)
	}
}

type mGroupDomainServiceMockDelete struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockDeleteExpectation
	expectations       []*GroupDomainServiceMockDeleteExpectation

	callArgs []*GroupDomainServiceMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockDeleteExpectation specifies expectation struct of the GroupDomainService.Delete
type GroupDomainServiceMockDeleteExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockDeleteParams
	paramPtrs          *GroupDomainServiceMockDeleteParamPtrs
	expectationOrigins GroupDomainServiceMockDeleteExpectationOrigins
	results            *GroupDomainServiceMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockDeleteParams contains parameters of the GroupDomainService.Delete
type GroupDomainServiceMockDeleteParams struct {
	productID int64
	groupID   int64
}

// GroupDomainServiceMockDeleteParamPtrs contains pointers to parameters of the GroupDomainService.Delete
type GroupDomainServiceMockDeleteParamPtrs struct {
	productID *int64
	groupID   *int64
}

// GroupDomainServiceMockDeleteResults contains results of the GroupDomainService.Delete
type GroupDomainServiceMockDeleteResults struct {
	err error
}

// GroupDomainServiceMockDeleteOrigins contains origins of expectations of the GroupDomainService.Delete
type GroupDomainServiceMockDeleteExpectationOrigins struct {
	origin          string
	originProductID string
	originGroupID   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mGroupDomainServiceMockDelete) Optional() *mGroupDomainServiceMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for GroupDomainService.Delete
func (mmDelete *mGroupDomainServiceMockDelete) Expect(productID int64, groupID int64) *mGroupDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &GroupDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &GroupDomainServiceMockDeleteParams{productID, groupID}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectProductIDParam1 sets up expected param productID for GroupDomainService.Delete
func (mmDelete *mGroupDomainServiceMockDelete) ExpectProductIDParam1(productID int64) *mGroupDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &GroupDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &GroupDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.productID = &productID
	mmDelete.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupDomainService.Delete
func (mmDelete *mGroupDomainServiceMockDelete) ExpectGroupIDParam2(groupID int64) *mGroupDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &GroupDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &GroupDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.groupID = &groupID
	mmDelete.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.Delete
func (mmDelete *mGroupDomainServiceMockDelete) Inspect(f func(productID int64, groupID int64)) *mGroupDomainServiceMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by GroupDomainService.Delete
func (mmDelete *mGroupDomainServiceMockDelete) Return(err error) *GroupDomainServiceMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &GroupDomainServiceMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &GroupDomainServiceMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the GroupDomainService.Delete method
func (mmDelete *mGroupDomainServiceMockDelete) Set(f func(productID int64, groupID int64) (err error)) *GroupDomainServiceMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the GroupDomainService.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mGroupDomainServiceMockDelete) When(productID int64, groupID int64) *GroupDomainServiceMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("GroupDomainServiceMock.Delete mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &GroupDomainServiceMockDeleteParams{productID, groupID},
		expectationOrigins: GroupDomainServiceMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.Delete return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockDeleteExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockDeleteResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.Delete should be invoked
func (mmDelete *mGroupDomainServiceMockDelete) Times(n uint64) *mGroupDomainServiceMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of GroupDomainServiceMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mGroupDomainServiceMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_service.GroupDomainService
func (mmDelete *GroupDomainServiceMock) Delete(productID int64, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(productID, groupID)
	}

	mm_params := GroupDomainServiceMockDeleteParams{productID, groupID}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockDeleteParams{productID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmDelete.t.Errorf("GroupDomainServiceMock.Delete got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDelete.t.Errorf("GroupDomainServiceMock.Delete got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("GroupDomainServiceMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the GroupDomainServiceMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(productID, groupID)
	}
	mmDelete.t.Fatalf("Unexpected call to GroupDomainServiceMock.Delete. %v %v", productID, groupID)
	return
}

// DeleteAfterCounter returns a count of finished GroupDomainServiceMock.Delete invocations
func (mmDelete *GroupDomainServiceMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of GroupDomainServiceMock.Delete invocations
func (mmDelete *GroupDomainServiceMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mGroupDomainServiceMockDelete) Calls() []*GroupDomainServiceMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mGroupDomainServiceMockDeleteAsAdmin struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockDeleteAsAdminExpectation
	expectations       []*GroupDomainServiceMockDeleteAsAdminExpectation

	callArgs []*GroupDomainServiceMockDeleteAsAdminParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockDeleteAsAdminExpectation specifies expectation struct of the GroupDomainService.DeleteAsAdmin
type GroupDomainServiceMockDeleteAsAdminExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockDeleteAsAdminParams
	paramPtrs          *GroupDomainServiceMockDeleteAsAdminParamPtrs
	expectationOrigins GroupDomainServiceMockDeleteAsAdminExpectationOrigins
	results            *GroupDomainServiceMockDeleteAsAdminResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockDeleteAsAdminParams contains parameters of the GroupDomainService.DeleteAsAdmin
type GroupDomainServiceMockDeleteAsAdminParams struct {
	groupID int64
}

// GroupDomainServiceMockDeleteAsAdminParamPtrs contains pointers to parameters of the GroupDomainService.DeleteAsAdmin
type GroupDomainServiceMockDeleteAsAdminParamPtrs struct {
	groupID *int64
}

// GroupDomainServiceMockDeleteAsAdminResults contains results of the GroupDomainService.DeleteAsAdmin
type GroupDomainServiceMockDeleteAsAdminResults struct {
	err error
}

// GroupDomainServiceMockDeleteAsAdminOrigins contains origins of expectations of the GroupDomainService.DeleteAsAdmin
type GroupDomainServiceMockDeleteAsAdminExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Optional() *mGroupDomainServiceMockDeleteAsAdmin {
	mmDeleteAsAdmin.optional = true
	return mmDeleteAsAdmin
}

// Expect sets up expected params for GroupDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Expect(groupID int64) *mGroupDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &GroupDomainServiceMockDeleteAsAdminExpectation{}
	}

	if mmDeleteAsAdmin.defaultExpectation.paramPtrs != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.DeleteAsAdmin mock is already set by ExpectParams functions")
	}

	mmDeleteAsAdmin.defaultExpectation.params = &GroupDomainServiceMockDeleteAsAdminParams{groupID}
	mmDeleteAsAdmin.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteAsAdmin.expectations {
		if minimock.Equal(e.params, mmDeleteAsAdmin.defaultExpectation.params) {
			mmDeleteAsAdmin.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteAsAdmin.defaultExpectation.params)
		}
	}

	return mmDeleteAsAdmin
}

// ExpectGroupIDParam1 sets up expected param groupID for GroupDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) ExpectGroupIDParam1(groupID int64) *mGroupDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &GroupDomainServiceMockDeleteAsAdminExpectation{}
	}

	if mmDeleteAsAdmin.defaultExpectation.params != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.DeleteAsAdmin mock is already set by Expect")
	}

	if mmDeleteAsAdmin.defaultExpectation.paramPtrs == nil {
		mmDeleteAsAdmin.defaultExpectation.paramPtrs = &GroupDomainServiceMockDeleteAsAdminParamPtrs{}
	}
	mmDeleteAsAdmin.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteAsAdmin.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Inspect(f func(groupID int64)) *mGroupDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.inspectFuncDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.DeleteAsAdmin")
	}

	mmDeleteAsAdmin.mock.inspectFuncDeleteAsAdmin = f

	return mmDeleteAsAdmin
}

// Return sets up results that will be returned by GroupDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Return(err error) *GroupDomainServiceMock {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &GroupDomainServiceMockDeleteAsAdminExpectation{mock: mmDeleteAsAdmin.mock}
	}
	mmDeleteAsAdmin.defaultExpectation.results = &GroupDomainServiceMockDeleteAsAdminResults{err}
	mmDeleteAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteAsAdmin.mock
}

// Set uses given function f to mock the GroupDomainService.DeleteAsAdmin method
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Set(f func(groupID int64) (err error)) *GroupDomainServiceMock {
	if mmDeleteAsAdmin.defaultExpectation != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.DeleteAsAdmin method")
	}

	if len(mmDeleteAsAdmin.expectations) > 0 {
		mmDeleteAsAdmin.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.DeleteAsAdmin method")
	}

	mmDeleteAsAdmin.mock.funcDeleteAsAdmin = f
	mmDeleteAsAdmin.mock.funcDeleteAsAdminOrigin = minimock.CallerInfo(1)
	return mmDeleteAsAdmin.mock
}

// When sets expectation for the GroupDomainService.DeleteAsAdmin which will trigger the result defined by the following
// Then helper
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) When(groupID int64) *GroupDomainServiceMockDeleteAsAdminExpectation {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockDeleteAsAdminExpectation{
		mock:               mmDeleteAsAdmin.mock,
		params:             &GroupDomainServiceMockDeleteAsAdminParams{groupID},
		expectationOrigins: GroupDomainServiceMockDeleteAsAdminExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteAsAdmin.expectations = append(mmDeleteAsAdmin.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.DeleteAsAdmin return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockDeleteAsAdminExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockDeleteAsAdminResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.DeleteAsAdmin should be invoked
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Times(n uint64) *mGroupDomainServiceMockDeleteAsAdmin {
	if n == 0 {
		mmDeleteAsAdmin.mock.t.Fatalf("Times of GroupDomainServiceMock.DeleteAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteAsAdmin.expectedInvocations, n)
	mmDeleteAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteAsAdmin
}

func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) invocationsDone() bool {
	if len(mmDeleteAsAdmin.expectations) == 0 && mmDeleteAsAdmin.defaultExpectation == nil && mmDeleteAsAdmin.mock.funcDeleteAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteAsAdmin.mock.afterDeleteAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteAsAdmin implements mm_service.GroupDomainService
func (mmDeleteAsAdmin *GroupDomainServiceMock) DeleteAsAdmin(groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteAsAdmin.beforeDeleteAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteAsAdmin.afterDeleteAsAdminCounter, 1)

	mmDeleteAsAdmin.t.Helper()

	if mmDeleteAsAdmin.inspectFuncDeleteAsAdmin != nil {
		mmDeleteAsAdmin.inspectFuncDeleteAsAdmin(groupID)
	}

	mm_params := GroupDomainServiceMockDeleteAsAdminParams{groupID}

	// Record call args
	mmDeleteAsAdmin.DeleteAsAdminMock.mutex.Lock()
	mmDeleteAsAdmin.DeleteAsAdminMock.callArgs = append(mmDeleteAsAdmin.DeleteAsAdminMock.callArgs, &mm_params)
	mmDeleteAsAdmin.DeleteAsAdminMock.mutex.Unlock()

	for _, e := range mmDeleteAsAdmin.DeleteAsAdminMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockDeleteAsAdminParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteAsAdmin.t.Errorf("GroupDomainServiceMock.DeleteAsAdmin got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteAsAdmin.t.Errorf("GroupDomainServiceMock.DeleteAsAdmin got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteAsAdmin.t.Fatal("No results are set for the GroupDomainServiceMock.DeleteAsAdmin")
		}
		return (*mm_results).err
	}
	if mmDeleteAsAdmin.funcDeleteAsAdmin != nil {
		return mmDeleteAsAdmin.funcDeleteAsAdmin(groupID)
	}
	mmDeleteAsAdmin.t.Fatalf("Unexpected call to GroupDomainServiceMock.DeleteAsAdmin. %v", groupID)
	return
}

// DeleteAsAdminAfterCounter returns a count of finished GroupDomainServiceMock.DeleteAsAdmin invocations
func (mmDeleteAsAdmin *GroupDomainServiceMock) DeleteAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteAsAdmin.afterDeleteAsAdminCounter)
}

// DeleteAsAdminBeforeCounter returns a count of GroupDomainServiceMock.DeleteAsAdmin invocations
func (mmDeleteAsAdmin *GroupDomainServiceMock) DeleteAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteAsAdmin.beforeDeleteAsAdminCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.DeleteAsAdmin.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteAsAdmin *mGroupDomainServiceMockDeleteAsAdmin) Calls() []*GroupDomainServiceMockDeleteAsAdminParams {
	mmDeleteAsAdmin.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockDeleteAsAdminParams, len(mmDeleteAsAdmin.callArgs))
	copy(argCopy, mmDeleteAsAdmin.callArgs)

	mmDeleteAsAdmin.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteAsAdminDone returns true if the count of the DeleteAsAdmin invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockDeleteAsAdminDone() bool {
	if m.DeleteAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteAsAdminMock.invocationsDone()
}

// MinimockDeleteAsAdminInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockDeleteAsAdminInspect() {
	for _, e := range m.DeleteAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.DeleteAsAdmin at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteAsAdminCounter := mm_atomic.LoadUint64(&m.afterDeleteAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteAsAdminMock.defaultExpectation != nil && afterDeleteAsAdminCounter < 1 {
		if m.DeleteAsAdminMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.DeleteAsAdmin at\n%s", m.DeleteAsAdminMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.DeleteAsAdmin at\n%s with params: %#v", m.DeleteAsAdminMock.defaultExpectation.expectationOrigins.origin, *m.DeleteAsAdminMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteAsAdmin != nil && afterDeleteAsAdminCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.DeleteAsAdmin at\n%s", m.funcDeleteAsAdminOrigin)
	}

	if !m.DeleteAsAdminMock.invocationsDone() && afterDeleteAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.DeleteAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteAsAdminMock.expectedInvocations), m.DeleteAsAdminMock.expectedInvocationsOrigin, afterDeleteAsAdminCounter)
	}
}

type mGroupDomainServiceMockGetAll struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetAllExpectation
	expectations       []*GroupDomainServiceMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetAllExpectation specifies expectation struct of the GroupDomainService.GetAll
type GroupDomainServiceMockGetAllExpectation struct {
	mock *GroupDomainServiceMock

	results      *GroupDomainServiceMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// GroupDomainServiceMockGetAllResults contains results of the GroupDomainService.GetAll
type GroupDomainServiceMockGetAllResults struct {
	ga1 []groupentity.Group
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mGroupDomainServiceMockGetAll) Optional() *mGroupDomainServiceMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for GroupDomainService.GetAll
func (mmGetAll *mGroupDomainServiceMockGetAll) Expect() *mGroupDomainServiceMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("GroupDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &GroupDomainServiceMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetAll
func (mmGetAll *mGroupDomainServiceMockGetAll) Inspect(f func()) *mGroupDomainServiceMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by GroupDomainService.GetAll
func (mmGetAll *mGroupDomainServiceMockGetAll) Return(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("GroupDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &GroupDomainServiceMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &GroupDomainServiceMockGetAllResults{ga1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the GroupDomainService.GetAll method
func (mmGetAll *mGroupDomainServiceMockGetAll) Set(f func() (ga1 []groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times GroupDomainService.GetAll should be invoked
func (mmGetAll *mGroupDomainServiceMockGetAll) Times(n uint64) *mGroupDomainServiceMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of GroupDomainServiceMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mGroupDomainServiceMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_service.GroupDomainService
func (mmGetAll *GroupDomainServiceMock) GetAll() (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the GroupDomainServiceMock.GetAll")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished GroupDomainServiceMock.GetAll invocations
func (mmGetAll *GroupDomainServiceMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of GroupDomainServiceMock.GetAll invocations
func (mmGetAll *GroupDomainServiceMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupDomainServiceMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mGroupDomainServiceMockGetAllAsAdmin struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetAllAsAdminExpectation
	expectations       []*GroupDomainServiceMockGetAllAsAdminExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetAllAsAdminExpectation specifies expectation struct of the GroupDomainService.GetAllAsAdmin
type GroupDomainServiceMockGetAllAsAdminExpectation struct {
	mock *GroupDomainServiceMock

	results      *GroupDomainServiceMockGetAllAsAdminResults
	returnOrigin string
	Counter      uint64
}

// GroupDomainServiceMockGetAllAsAdminResults contains results of the GroupDomainService.GetAllAsAdmin
type GroupDomainServiceMockGetAllAsAdminResults struct {
	aa1 []groupentity.AdminGroup
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) Optional() *mGroupDomainServiceMockGetAllAsAdmin {
	mmGetAllAsAdmin.optional = true
	return mmGetAllAsAdmin
}

// Expect sets up expected params for GroupDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) Expect() *mGroupDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &GroupDomainServiceMockGetAllAsAdminExpectation{}
	}

	return mmGetAllAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) Inspect(f func()) *mGroupDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetAllAsAdmin")
	}

	mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin = f

	return mmGetAllAsAdmin
}

// Return sets up results that will be returned by GroupDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) Return(aa1 []groupentity.AdminGroup, err error) *GroupDomainServiceMock {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("GroupDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &GroupDomainServiceMockGetAllAsAdminExpectation{mock: mmGetAllAsAdmin.mock}
	}
	mmGetAllAsAdmin.defaultExpectation.results = &GroupDomainServiceMockGetAllAsAdminResults{aa1, err}
	mmGetAllAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Set uses given function f to mock the GroupDomainService.GetAllAsAdmin method
func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) Set(f func() (aa1 []groupentity.AdminGroup, err error)) *GroupDomainServiceMock {
	if mmGetAllAsAdmin.defaultExpectation != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetAllAsAdmin method")
	}

	if len(mmGetAllAsAdmin.expectations) > 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetAllAsAdmin method")
	}

	mmGetAllAsAdmin.mock.funcGetAllAsAdmin = f
	mmGetAllAsAdmin.mock.funcGetAllAsAdminOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Times sets number of times GroupDomainService.GetAllAsAdmin should be invoked
func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) Times(n uint64) *mGroupDomainServiceMockGetAllAsAdmin {
	if n == 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Times of GroupDomainServiceMock.GetAllAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllAsAdmin.expectedInvocations, n)
	mmGetAllAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin
}

func (mmGetAllAsAdmin *mGroupDomainServiceMockGetAllAsAdmin) invocationsDone() bool {
	if len(mmGetAllAsAdmin.expectations) == 0 && mmGetAllAsAdmin.defaultExpectation == nil && mmGetAllAsAdmin.mock.funcGetAllAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.mock.afterGetAllAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllAsAdmin implements mm_service.GroupDomainService
func (mmGetAllAsAdmin *GroupDomainServiceMock) GetAllAsAdmin() (aa1 []groupentity.AdminGroup, err error) {
	mm_atomic.AddUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter, 1)

	mmGetAllAsAdmin.t.Helper()

	if mmGetAllAsAdmin.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.inspectFuncGetAllAsAdmin()
	}

	if mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllAsAdmin.t.Fatal("No results are set for the GroupDomainServiceMock.GetAllAsAdmin")
		}
		return (*mm_results).aa1, (*mm_results).err
	}
	if mmGetAllAsAdmin.funcGetAllAsAdmin != nil {
		return mmGetAllAsAdmin.funcGetAllAsAdmin()
	}
	mmGetAllAsAdmin.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetAllAsAdmin.")
	return
}

// GetAllAsAdminAfterCounter returns a count of finished GroupDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *GroupDomainServiceMock) GetAllAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter)
}

// GetAllAsAdminBeforeCounter returns a count of GroupDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *GroupDomainServiceMock) GetAllAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter)
}

// MinimockGetAllAsAdminDone returns true if the count of the GetAllAsAdmin invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetAllAsAdminDone() bool {
	if m.GetAllAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllAsAdminMock.invocationsDone()
}

// MinimockGetAllAsAdminInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetAllAsAdminInspect() {
	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupDomainServiceMock.GetAllAsAdmin")
		}
	}

	afterGetAllAsAdminCounter := mm_atomic.LoadUint64(&m.afterGetAllAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllAsAdminMock.defaultExpectation != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetAllAsAdmin at\n%s", m.GetAllAsAdminMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllAsAdmin != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetAllAsAdmin at\n%s", m.funcGetAllAsAdminOrigin)
	}

	if !m.GetAllAsAdminMock.invocationsDone() && afterGetAllAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetAllAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllAsAdminMock.expectedInvocations), m.GetAllAsAdminMock.expectedInvocationsOrigin, afterGetAllAsAdminCounter)
	}
}

type mGroupDomainServiceMockGetAvailableSystemGroups struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetAvailableSystemGroupsExpectation
	expectations       []*GroupDomainServiceMockGetAvailableSystemGroupsExpectation

	callArgs []*GroupDomainServiceMockGetAvailableSystemGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetAvailableSystemGroupsExpectation specifies expectation struct of the GroupDomainService.GetAvailableSystemGroups
type GroupDomainServiceMockGetAvailableSystemGroupsExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetAvailableSystemGroupsParams
	paramPtrs          *GroupDomainServiceMockGetAvailableSystemGroupsParamPtrs
	expectationOrigins GroupDomainServiceMockGetAvailableSystemGroupsExpectationOrigins
	results            *GroupDomainServiceMockGetAvailableSystemGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetAvailableSystemGroupsParams contains parameters of the GroupDomainService.GetAvailableSystemGroups
type GroupDomainServiceMockGetAvailableSystemGroupsParams struct {
	category   categoryentity.CategoryFull
	groups     []groupentity.GroupWithStats
	userGroups []int64
}

// GroupDomainServiceMockGetAvailableSystemGroupsParamPtrs contains pointers to parameters of the GroupDomainService.GetAvailableSystemGroups
type GroupDomainServiceMockGetAvailableSystemGroupsParamPtrs struct {
	category   *categoryentity.CategoryFull
	groups     *[]groupentity.GroupWithStats
	userGroups *[]int64
}

// GroupDomainServiceMockGetAvailableSystemGroupsResults contains results of the GroupDomainService.GetAvailableSystemGroups
type GroupDomainServiceMockGetAvailableSystemGroupsResults struct {
	ga1 []groupentity.GroupWithStats
	err error
}

// GroupDomainServiceMockGetAvailableSystemGroupsOrigins contains origins of expectations of the GroupDomainService.GetAvailableSystemGroups
type GroupDomainServiceMockGetAvailableSystemGroupsExpectationOrigins struct {
	origin           string
	originCategory   string
	originGroups     string
	originUserGroups string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Optional() *mGroupDomainServiceMockGetAvailableSystemGroups {
	mmGetAvailableSystemGroups.optional = true
	return mmGetAvailableSystemGroups
}

// Expect sets up expected params for GroupDomainService.GetAvailableSystemGroups
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Expect(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) *mGroupDomainServiceMockGetAvailableSystemGroups {
	if mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Set")
	}

	if mmGetAvailableSystemGroups.defaultExpectation == nil {
		mmGetAvailableSystemGroups.defaultExpectation = &GroupDomainServiceMockGetAvailableSystemGroupsExpectation{}
	}

	if mmGetAvailableSystemGroups.defaultExpectation.paramPtrs != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by ExpectParams functions")
	}

	mmGetAvailableSystemGroups.defaultExpectation.params = &GroupDomainServiceMockGetAvailableSystemGroupsParams{category, groups, userGroups}
	mmGetAvailableSystemGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAvailableSystemGroups.expectations {
		if minimock.Equal(e.params, mmGetAvailableSystemGroups.defaultExpectation.params) {
			mmGetAvailableSystemGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAvailableSystemGroups.defaultExpectation.params)
		}
	}

	return mmGetAvailableSystemGroups
}

// ExpectCategoryParam1 sets up expected param category for GroupDomainService.GetAvailableSystemGroups
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) ExpectCategoryParam1(category categoryentity.CategoryFull) *mGroupDomainServiceMockGetAvailableSystemGroups {
	if mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Set")
	}

	if mmGetAvailableSystemGroups.defaultExpectation == nil {
		mmGetAvailableSystemGroups.defaultExpectation = &GroupDomainServiceMockGetAvailableSystemGroupsExpectation{}
	}

	if mmGetAvailableSystemGroups.defaultExpectation.params != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Expect")
	}

	if mmGetAvailableSystemGroups.defaultExpectation.paramPtrs == nil {
		mmGetAvailableSystemGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetAvailableSystemGroupsParamPtrs{}
	}
	mmGetAvailableSystemGroups.defaultExpectation.paramPtrs.category = &category
	mmGetAvailableSystemGroups.defaultExpectation.expectationOrigins.originCategory = minimock.CallerInfo(1)

	return mmGetAvailableSystemGroups
}

// ExpectGroupsParam2 sets up expected param groups for GroupDomainService.GetAvailableSystemGroups
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) ExpectGroupsParam2(groups []groupentity.GroupWithStats) *mGroupDomainServiceMockGetAvailableSystemGroups {
	if mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Set")
	}

	if mmGetAvailableSystemGroups.defaultExpectation == nil {
		mmGetAvailableSystemGroups.defaultExpectation = &GroupDomainServiceMockGetAvailableSystemGroupsExpectation{}
	}

	if mmGetAvailableSystemGroups.defaultExpectation.params != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Expect")
	}

	if mmGetAvailableSystemGroups.defaultExpectation.paramPtrs == nil {
		mmGetAvailableSystemGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetAvailableSystemGroupsParamPtrs{}
	}
	mmGetAvailableSystemGroups.defaultExpectation.paramPtrs.groups = &groups
	mmGetAvailableSystemGroups.defaultExpectation.expectationOrigins.originGroups = minimock.CallerInfo(1)

	return mmGetAvailableSystemGroups
}

// ExpectUserGroupsParam3 sets up expected param userGroups for GroupDomainService.GetAvailableSystemGroups
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) ExpectUserGroupsParam3(userGroups []int64) *mGroupDomainServiceMockGetAvailableSystemGroups {
	if mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Set")
	}

	if mmGetAvailableSystemGroups.defaultExpectation == nil {
		mmGetAvailableSystemGroups.defaultExpectation = &GroupDomainServiceMockGetAvailableSystemGroupsExpectation{}
	}

	if mmGetAvailableSystemGroups.defaultExpectation.params != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Expect")
	}

	if mmGetAvailableSystemGroups.defaultExpectation.paramPtrs == nil {
		mmGetAvailableSystemGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetAvailableSystemGroupsParamPtrs{}
	}
	mmGetAvailableSystemGroups.defaultExpectation.paramPtrs.userGroups = &userGroups
	mmGetAvailableSystemGroups.defaultExpectation.expectationOrigins.originUserGroups = minimock.CallerInfo(1)

	return mmGetAvailableSystemGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetAvailableSystemGroups
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Inspect(f func(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64)) *mGroupDomainServiceMockGetAvailableSystemGroups {
	if mmGetAvailableSystemGroups.mock.inspectFuncGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetAvailableSystemGroups")
	}

	mmGetAvailableSystemGroups.mock.inspectFuncGetAvailableSystemGroups = f

	return mmGetAvailableSystemGroups
}

// Return sets up results that will be returned by GroupDomainService.GetAvailableSystemGroups
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Return(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	if mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Set")
	}

	if mmGetAvailableSystemGroups.defaultExpectation == nil {
		mmGetAvailableSystemGroups.defaultExpectation = &GroupDomainServiceMockGetAvailableSystemGroupsExpectation{mock: mmGetAvailableSystemGroups.mock}
	}
	mmGetAvailableSystemGroups.defaultExpectation.results = &GroupDomainServiceMockGetAvailableSystemGroupsResults{ga1, err}
	mmGetAvailableSystemGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAvailableSystemGroups.mock
}

// Set uses given function f to mock the GroupDomainService.GetAvailableSystemGroups method
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Set(f func(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) (ga1 []groupentity.GroupWithStats, err error)) *GroupDomainServiceMock {
	if mmGetAvailableSystemGroups.defaultExpectation != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetAvailableSystemGroups method")
	}

	if len(mmGetAvailableSystemGroups.expectations) > 0 {
		mmGetAvailableSystemGroups.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetAvailableSystemGroups method")
	}

	mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups = f
	mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroupsOrigin = minimock.CallerInfo(1)
	return mmGetAvailableSystemGroups.mock
}

// When sets expectation for the GroupDomainService.GetAvailableSystemGroups which will trigger the result defined by the following
// Then helper
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) When(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) *GroupDomainServiceMockGetAvailableSystemGroupsExpectation {
	if mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.mock.t.Fatalf("GroupDomainServiceMock.GetAvailableSystemGroups mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetAvailableSystemGroupsExpectation{
		mock:               mmGetAvailableSystemGroups.mock,
		params:             &GroupDomainServiceMockGetAvailableSystemGroupsParams{category, groups, userGroups},
		expectationOrigins: GroupDomainServiceMockGetAvailableSystemGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAvailableSystemGroups.expectations = append(mmGetAvailableSystemGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetAvailableSystemGroups return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetAvailableSystemGroupsExpectation) Then(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetAvailableSystemGroupsResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetAvailableSystemGroups should be invoked
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Times(n uint64) *mGroupDomainServiceMockGetAvailableSystemGroups {
	if n == 0 {
		mmGetAvailableSystemGroups.mock.t.Fatalf("Times of GroupDomainServiceMock.GetAvailableSystemGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAvailableSystemGroups.expectedInvocations, n)
	mmGetAvailableSystemGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAvailableSystemGroups
}

func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) invocationsDone() bool {
	if len(mmGetAvailableSystemGroups.expectations) == 0 && mmGetAvailableSystemGroups.defaultExpectation == nil && mmGetAvailableSystemGroups.mock.funcGetAvailableSystemGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAvailableSystemGroups.mock.afterGetAvailableSystemGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAvailableSystemGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAvailableSystemGroups implements mm_service.GroupDomainService
func (mmGetAvailableSystemGroups *GroupDomainServiceMock) GetAvailableSystemGroups(category categoryentity.CategoryFull, groups []groupentity.GroupWithStats, userGroups []int64) (ga1 []groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmGetAvailableSystemGroups.beforeGetAvailableSystemGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAvailableSystemGroups.afterGetAvailableSystemGroupsCounter, 1)

	mmGetAvailableSystemGroups.t.Helper()

	if mmGetAvailableSystemGroups.inspectFuncGetAvailableSystemGroups != nil {
		mmGetAvailableSystemGroups.inspectFuncGetAvailableSystemGroups(category, groups, userGroups)
	}

	mm_params := GroupDomainServiceMockGetAvailableSystemGroupsParams{category, groups, userGroups}

	// Record call args
	mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.mutex.Lock()
	mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.callArgs = append(mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.callArgs, &mm_params)
	mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.mutex.Unlock()

	for _, e := range mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetAvailableSystemGroupsParams{category, groups, userGroups}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.category != nil && !minimock.Equal(*mm_want_ptrs.category, mm_got.category) {
				mmGetAvailableSystemGroups.t.Errorf("GroupDomainServiceMock.GetAvailableSystemGroups got unexpected parameter category, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.expectationOrigins.originCategory, *mm_want_ptrs.category, mm_got.category, minimock.Diff(*mm_want_ptrs.category, mm_got.category))
			}

			if mm_want_ptrs.groups != nil && !minimock.Equal(*mm_want_ptrs.groups, mm_got.groups) {
				mmGetAvailableSystemGroups.t.Errorf("GroupDomainServiceMock.GetAvailableSystemGroups got unexpected parameter groups, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.expectationOrigins.originGroups, *mm_want_ptrs.groups, mm_got.groups, minimock.Diff(*mm_want_ptrs.groups, mm_got.groups))
			}

			if mm_want_ptrs.userGroups != nil && !minimock.Equal(*mm_want_ptrs.userGroups, mm_got.userGroups) {
				mmGetAvailableSystemGroups.t.Errorf("GroupDomainServiceMock.GetAvailableSystemGroups got unexpected parameter userGroups, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.expectationOrigins.originUserGroups, *mm_want_ptrs.userGroups, mm_got.userGroups, minimock.Diff(*mm_want_ptrs.userGroups, mm_got.userGroups))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAvailableSystemGroups.t.Errorf("GroupDomainServiceMock.GetAvailableSystemGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAvailableSystemGroups.GetAvailableSystemGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAvailableSystemGroups.t.Fatal("No results are set for the GroupDomainServiceMock.GetAvailableSystemGroups")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetAvailableSystemGroups.funcGetAvailableSystemGroups != nil {
		return mmGetAvailableSystemGroups.funcGetAvailableSystemGroups(category, groups, userGroups)
	}
	mmGetAvailableSystemGroups.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetAvailableSystemGroups. %v %v %v", category, groups, userGroups)
	return
}

// GetAvailableSystemGroupsAfterCounter returns a count of finished GroupDomainServiceMock.GetAvailableSystemGroups invocations
func (mmGetAvailableSystemGroups *GroupDomainServiceMock) GetAvailableSystemGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAvailableSystemGroups.afterGetAvailableSystemGroupsCounter)
}

// GetAvailableSystemGroupsBeforeCounter returns a count of GroupDomainServiceMock.GetAvailableSystemGroups invocations
func (mmGetAvailableSystemGroups *GroupDomainServiceMock) GetAvailableSystemGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAvailableSystemGroups.beforeGetAvailableSystemGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetAvailableSystemGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAvailableSystemGroups *mGroupDomainServiceMockGetAvailableSystemGroups) Calls() []*GroupDomainServiceMockGetAvailableSystemGroupsParams {
	mmGetAvailableSystemGroups.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetAvailableSystemGroupsParams, len(mmGetAvailableSystemGroups.callArgs))
	copy(argCopy, mmGetAvailableSystemGroups.callArgs)

	mmGetAvailableSystemGroups.mutex.RUnlock()

	return argCopy
}

// MinimockGetAvailableSystemGroupsDone returns true if the count of the GetAvailableSystemGroups invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetAvailableSystemGroupsDone() bool {
	if m.GetAvailableSystemGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAvailableSystemGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAvailableSystemGroupsMock.invocationsDone()
}

// MinimockGetAvailableSystemGroupsInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetAvailableSystemGroupsInspect() {
	for _, e := range m.GetAvailableSystemGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetAvailableSystemGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAvailableSystemGroupsCounter := mm_atomic.LoadUint64(&m.afterGetAvailableSystemGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAvailableSystemGroupsMock.defaultExpectation != nil && afterGetAvailableSystemGroupsCounter < 1 {
		if m.GetAvailableSystemGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetAvailableSystemGroups at\n%s", m.GetAvailableSystemGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetAvailableSystemGroups at\n%s with params: %#v", m.GetAvailableSystemGroupsMock.defaultExpectation.expectationOrigins.origin, *m.GetAvailableSystemGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAvailableSystemGroups != nil && afterGetAvailableSystemGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetAvailableSystemGroups at\n%s", m.funcGetAvailableSystemGroupsOrigin)
	}

	if !m.GetAvailableSystemGroupsMock.invocationsDone() && afterGetAvailableSystemGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetAvailableSystemGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAvailableSystemGroupsMock.expectedInvocations), m.GetAvailableSystemGroupsMock.expectedInvocationsOrigin, afterGetAvailableSystemGroupsCounter)
	}
}

type mGroupDomainServiceMockGetByCategoryID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByCategoryIDExpectation
	expectations       []*GroupDomainServiceMockGetByCategoryIDExpectation

	callArgs []*GroupDomainServiceMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByCategoryIDExpectation specifies expectation struct of the GroupDomainService.GetByCategoryID
type GroupDomainServiceMockGetByCategoryIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByCategoryIDParams
	paramPtrs          *GroupDomainServiceMockGetByCategoryIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByCategoryIDExpectationOrigins
	results            *GroupDomainServiceMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByCategoryIDParams contains parameters of the GroupDomainService.GetByCategoryID
type GroupDomainServiceMockGetByCategoryIDParams struct {
	categoryID int64
}

// GroupDomainServiceMockGetByCategoryIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByCategoryID
type GroupDomainServiceMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// GroupDomainServiceMockGetByCategoryIDResults contains results of the GroupDomainService.GetByCategoryID
type GroupDomainServiceMockGetByCategoryIDResults struct {
	ga1 []groupentity.GroupWithStats
	err error
}

// GroupDomainServiceMockGetByCategoryIDOrigins contains origins of expectations of the GroupDomainService.GetByCategoryID
type GroupDomainServiceMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Optional() *mGroupDomainServiceMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for GroupDomainService.GetByCategoryID
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Expect(categoryID int64) *mGroupDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("GroupDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &GroupDomainServiceMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("GroupDomainServiceMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &GroupDomainServiceMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for GroupDomainService.GetByCategoryID
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mGroupDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("GroupDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &GroupDomainServiceMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("GroupDomainServiceMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByCategoryID
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Inspect(f func(categoryID int64)) *mGroupDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by GroupDomainService.GetByCategoryID
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Return(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("GroupDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &GroupDomainServiceMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &GroupDomainServiceMockGetByCategoryIDResults{ga1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByCategoryID method
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Set(f func(categoryID int64) (ga1 []groupentity.GroupWithStats, err error)) *GroupDomainServiceMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the GroupDomainService.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) When(categoryID int64) *GroupDomainServiceMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("GroupDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &GroupDomainServiceMockGetByCategoryIDParams{categoryID},
		expectationOrigins: GroupDomainServiceMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByCategoryIDExpectation) Then(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByCategoryIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByCategoryID should be invoked
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Times(n uint64) *mGroupDomainServiceMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_service.GroupDomainService
func (mmGetByCategoryID *GroupDomainServiceMock) GetByCategoryID(categoryID int64) (ga1 []groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := GroupDomainServiceMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("GroupDomainServiceMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("GroupDomainServiceMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByCategoryID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByCategoryID invocations
func (mmGetByCategoryID *GroupDomainServiceMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of GroupDomainServiceMock.GetByCategoryID invocations
func (mmGetByCategoryID *GroupDomainServiceMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mGroupDomainServiceMockGetByCategoryID) Calls() []*GroupDomainServiceMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mGroupDomainServiceMockGetByGroupIDAndProductID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByGroupIDAndProductIDExpectation
	expectations       []*GroupDomainServiceMockGetByGroupIDAndProductIDExpectation

	callArgs []*GroupDomainServiceMockGetByGroupIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByGroupIDAndProductIDExpectation specifies expectation struct of the GroupDomainService.GetByGroupIDAndProductID
type GroupDomainServiceMockGetByGroupIDAndProductIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByGroupIDAndProductIDParams
	paramPtrs          *GroupDomainServiceMockGetByGroupIDAndProductIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByGroupIDAndProductIDExpectationOrigins
	results            *GroupDomainServiceMockGetByGroupIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByGroupIDAndProductIDParams contains parameters of the GroupDomainService.GetByGroupIDAndProductID
type GroupDomainServiceMockGetByGroupIDAndProductIDParams struct {
	groupID   int64
	productID int64
}

// GroupDomainServiceMockGetByGroupIDAndProductIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByGroupIDAndProductID
type GroupDomainServiceMockGetByGroupIDAndProductIDParamPtrs struct {
	groupID   *int64
	productID *int64
}

// GroupDomainServiceMockGetByGroupIDAndProductIDResults contains results of the GroupDomainService.GetByGroupIDAndProductID
type GroupDomainServiceMockGetByGroupIDAndProductIDResults struct {
	g1  groupentity.GroupFull
	err error
}

// GroupDomainServiceMockGetByGroupIDAndProductIDOrigins contains origins of expectations of the GroupDomainService.GetByGroupIDAndProductID
type GroupDomainServiceMockGetByGroupIDAndProductIDExpectationOrigins struct {
	origin          string
	originGroupID   string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Optional() *mGroupDomainServiceMockGetByGroupIDAndProductID {
	mmGetByGroupIDAndProductID.optional = true
	return mmGetByGroupIDAndProductID
}

// Expect sets up expected params for GroupDomainService.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Expect(groupID int64, productID int64) *mGroupDomainServiceMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupDomainServiceMockGetByGroupIDAndProductIDExpectation{}
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByGroupIDAndProductID.defaultExpectation.params = &GroupDomainServiceMockGetByGroupIDAndProductIDParams{groupID, productID}
	mmGetByGroupIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByGroupIDAndProductID.defaultExpectation.params) {
			mmGetByGroupIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByGroupIDAndProductID
}

// ExpectGroupIDParam1 sets up expected param groupID for GroupDomainService.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) ExpectGroupIDParam1(groupID int64) *mGroupDomainServiceMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupDomainServiceMockGetByGroupIDAndProductIDExpectation{}
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.params != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Expect")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByGroupIDAndProductIDParamPtrs{}
	}
	mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupIDAndProductID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for GroupDomainService.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) ExpectProductIDParam2(productID int64) *mGroupDomainServiceMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupDomainServiceMockGetByGroupIDAndProductIDExpectation{}
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.params != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Expect")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByGroupIDAndProductIDParamPtrs{}
	}
	mmGetByGroupIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByGroupIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByGroupIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Inspect(f func(groupID int64, productID int64)) *mGroupDomainServiceMockGetByGroupIDAndProductID {
	if mmGetByGroupIDAndProductID.mock.inspectFuncGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByGroupIDAndProductID")
	}

	mmGetByGroupIDAndProductID.mock.inspectFuncGetByGroupIDAndProductID = f

	return mmGetByGroupIDAndProductID
}

// Return sets up results that will be returned by GroupDomainService.GetByGroupIDAndProductID
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Return(g1 groupentity.GroupFull, err error) *GroupDomainServiceMock {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	if mmGetByGroupIDAndProductID.defaultExpectation == nil {
		mmGetByGroupIDAndProductID.defaultExpectation = &GroupDomainServiceMockGetByGroupIDAndProductIDExpectation{mock: mmGetByGroupIDAndProductID.mock}
	}
	mmGetByGroupIDAndProductID.defaultExpectation.results = &GroupDomainServiceMockGetByGroupIDAndProductIDResults{g1, err}
	mmGetByGroupIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupIDAndProductID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByGroupIDAndProductID method
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Set(f func(groupID int64, productID int64) (g1 groupentity.GroupFull, err error)) *GroupDomainServiceMock {
	if mmGetByGroupIDAndProductID.defaultExpectation != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByGroupIDAndProductID method")
	}

	if len(mmGetByGroupIDAndProductID.expectations) > 0 {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByGroupIDAndProductID method")
	}

	mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID = f
	mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupIDAndProductID.mock
}

// When sets expectation for the GroupDomainService.GetByGroupIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) When(groupID int64, productID int64) *GroupDomainServiceMockGetByGroupIDAndProductIDExpectation {
	if mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByGroupIDAndProductID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByGroupIDAndProductIDExpectation{
		mock:               mmGetByGroupIDAndProductID.mock,
		params:             &GroupDomainServiceMockGetByGroupIDAndProductIDParams{groupID, productID},
		expectationOrigins: GroupDomainServiceMockGetByGroupIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupIDAndProductID.expectations = append(mmGetByGroupIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByGroupIDAndProductID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByGroupIDAndProductIDExpectation) Then(g1 groupentity.GroupFull, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByGroupIDAndProductIDResults{g1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByGroupIDAndProductID should be invoked
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Times(n uint64) *mGroupDomainServiceMockGetByGroupIDAndProductID {
	if n == 0 {
		mmGetByGroupIDAndProductID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByGroupIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupIDAndProductID.expectedInvocations, n)
	mmGetByGroupIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupIDAndProductID
}

func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) invocationsDone() bool {
	if len(mmGetByGroupIDAndProductID.expectations) == 0 && mmGetByGroupIDAndProductID.defaultExpectation == nil && mmGetByGroupIDAndProductID.mock.funcGetByGroupIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.mock.afterGetByGroupIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupIDAndProductID implements mm_service.GroupDomainService
func (mmGetByGroupIDAndProductID *GroupDomainServiceMock) GetByGroupIDAndProductID(groupID int64, productID int64) (g1 groupentity.GroupFull, err error) {
	mm_atomic.AddUint64(&mmGetByGroupIDAndProductID.beforeGetByGroupIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupIDAndProductID.afterGetByGroupIDAndProductIDCounter, 1)

	mmGetByGroupIDAndProductID.t.Helper()

	if mmGetByGroupIDAndProductID.inspectFuncGetByGroupIDAndProductID != nil {
		mmGetByGroupIDAndProductID.inspectFuncGetByGroupIDAndProductID(groupID, productID)
	}

	mm_params := GroupDomainServiceMockGetByGroupIDAndProductIDParams{groupID, productID}

	// Record call args
	mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.mutex.Lock()
	mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.callArgs = append(mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.callArgs, &mm_params)
	mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByGroupIDAndProductIDParams{groupID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupIDAndProductID.t.Errorf("GroupDomainServiceMock.GetByGroupIDAndProductID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByGroupIDAndProductID.t.Errorf("GroupDomainServiceMock.GetByGroupIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupIDAndProductID.t.Errorf("GroupDomainServiceMock.GetByGroupIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupIDAndProductID.GetByGroupIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupIDAndProductID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByGroupIDAndProductID")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetByGroupIDAndProductID.funcGetByGroupIDAndProductID != nil {
		return mmGetByGroupIDAndProductID.funcGetByGroupIDAndProductID(groupID, productID)
	}
	mmGetByGroupIDAndProductID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByGroupIDAndProductID. %v %v", groupID, productID)
	return
}

// GetByGroupIDAndProductIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByGroupIDAndProductID invocations
func (mmGetByGroupIDAndProductID *GroupDomainServiceMock) GetByGroupIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.afterGetByGroupIDAndProductIDCounter)
}

// GetByGroupIDAndProductIDBeforeCounter returns a count of GroupDomainServiceMock.GetByGroupIDAndProductID invocations
func (mmGetByGroupIDAndProductID *GroupDomainServiceMock) GetByGroupIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupIDAndProductID.beforeGetByGroupIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByGroupIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupIDAndProductID *mGroupDomainServiceMockGetByGroupIDAndProductID) Calls() []*GroupDomainServiceMockGetByGroupIDAndProductIDParams {
	mmGetByGroupIDAndProductID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByGroupIDAndProductIDParams, len(mmGetByGroupIDAndProductID.callArgs))
	copy(argCopy, mmGetByGroupIDAndProductID.callArgs)

	mmGetByGroupIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDAndProductIDDone returns true if the count of the GetByGroupIDAndProductID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByGroupIDAndProductIDDone() bool {
	if m.GetByGroupIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDAndProductIDMock.invocationsDone()
}

// MinimockGetByGroupIDAndProductIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByGroupIDAndProductIDInspect() {
	for _, e := range m.GetByGroupIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByGroupIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDAndProductIDMock.defaultExpectation != nil && afterGetByGroupIDAndProductIDCounter < 1 {
		if m.GetByGroupIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByGroupIDAndProductID at\n%s", m.GetByGroupIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByGroupIDAndProductID at\n%s with params: %#v", m.GetByGroupIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupIDAndProductID != nil && afterGetByGroupIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByGroupIDAndProductID at\n%s", m.funcGetByGroupIDAndProductIDOrigin)
	}

	if !m.GetByGroupIDAndProductIDMock.invocationsDone() && afterGetByGroupIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByGroupIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDAndProductIDMock.expectedInvocations), m.GetByGroupIDAndProductIDMock.expectedInvocationsOrigin, afterGetByGroupIDAndProductIDCounter)
	}
}

type mGroupDomainServiceMockGetByID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByIDExpectation
	expectations       []*GroupDomainServiceMockGetByIDExpectation

	callArgs []*GroupDomainServiceMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByIDExpectation specifies expectation struct of the GroupDomainService.GetByID
type GroupDomainServiceMockGetByIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByIDParams
	paramPtrs          *GroupDomainServiceMockGetByIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByIDExpectationOrigins
	results            *GroupDomainServiceMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByIDParams contains parameters of the GroupDomainService.GetByID
type GroupDomainServiceMockGetByIDParams struct {
	id int64
}

// GroupDomainServiceMockGetByIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByID
type GroupDomainServiceMockGetByIDParamPtrs struct {
	id *int64
}

// GroupDomainServiceMockGetByIDResults contains results of the GroupDomainService.GetByID
type GroupDomainServiceMockGetByIDResults struct {
	g1  groupentity.Group
	err error
}

// GroupDomainServiceMockGetByIDOrigins contains origins of expectations of the GroupDomainService.GetByID
type GroupDomainServiceMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mGroupDomainServiceMockGetByID) Optional() *mGroupDomainServiceMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for GroupDomainService.GetByID
func (mmGetByID *mGroupDomainServiceMockGetByID) Expect(id int64) *mGroupDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("GroupDomainServiceMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &GroupDomainServiceMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for GroupDomainService.GetByID
func (mmGetByID *mGroupDomainServiceMockGetByID) ExpectIdParam1(id int64) *mGroupDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("GroupDomainServiceMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByID
func (mmGetByID *mGroupDomainServiceMockGetByID) Inspect(f func(id int64)) *mGroupDomainServiceMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by GroupDomainService.GetByID
func (mmGetByID *mGroupDomainServiceMockGetByID) Return(g1 groupentity.Group, err error) *GroupDomainServiceMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &GroupDomainServiceMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &GroupDomainServiceMockGetByIDResults{g1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByID method
func (mmGetByID *mGroupDomainServiceMockGetByID) Set(f func(id int64) (g1 groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the GroupDomainService.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mGroupDomainServiceMockGetByID) When(id int64) *GroupDomainServiceMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("GroupDomainServiceMock.GetByID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &GroupDomainServiceMockGetByIDParams{id},
		expectationOrigins: GroupDomainServiceMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByIDExpectation) Then(g1 groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByIDResults{g1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByID should be invoked
func (mmGetByID *mGroupDomainServiceMockGetByID) Times(n uint64) *mGroupDomainServiceMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mGroupDomainServiceMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_service.GroupDomainService
func (mmGetByID *GroupDomainServiceMock) GetByID(id int64) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := GroupDomainServiceMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("GroupDomainServiceMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("GroupDomainServiceMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByID")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByID invocations
func (mmGetByID *GroupDomainServiceMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of GroupDomainServiceMock.GetByID invocations
func (mmGetByID *GroupDomainServiceMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mGroupDomainServiceMockGetByID) Calls() []*GroupDomainServiceMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mGroupDomainServiceMockGetByParticipantID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByParticipantIDExpectation
	expectations       []*GroupDomainServiceMockGetByParticipantIDExpectation

	callArgs []*GroupDomainServiceMockGetByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByParticipantIDExpectation specifies expectation struct of the GroupDomainService.GetByParticipantID
type GroupDomainServiceMockGetByParticipantIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByParticipantIDParams
	paramPtrs          *GroupDomainServiceMockGetByParticipantIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByParticipantIDExpectationOrigins
	results            *GroupDomainServiceMockGetByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByParticipantIDParams contains parameters of the GroupDomainService.GetByParticipantID
type GroupDomainServiceMockGetByParticipantIDParams struct {
	participantID int64
}

// GroupDomainServiceMockGetByParticipantIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByParticipantID
type GroupDomainServiceMockGetByParticipantIDParamPtrs struct {
	participantID *int64
}

// GroupDomainServiceMockGetByParticipantIDResults contains results of the GroupDomainService.GetByParticipantID
type GroupDomainServiceMockGetByParticipantIDResults struct {
	ga1 []groupentity.GroupWithStats
	err error
}

// GroupDomainServiceMockGetByParticipantIDOrigins contains origins of expectations of the GroupDomainService.GetByParticipantID
type GroupDomainServiceMockGetByParticipantIDExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Optional() *mGroupDomainServiceMockGetByParticipantID {
	mmGetByParticipantID.optional = true
	return mmGetByParticipantID
}

// Expect sets up expected params for GroupDomainService.GetByParticipantID
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Expect(participantID int64) *mGroupDomainServiceMockGetByParticipantID {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("GroupDomainServiceMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &GroupDomainServiceMockGetByParticipantIDExpectation{}
	}

	if mmGetByParticipantID.defaultExpectation.paramPtrs != nil {
		mmGetByParticipantID.mock.t.Fatalf("GroupDomainServiceMock.GetByParticipantID mock is already set by ExpectParams functions")
	}

	mmGetByParticipantID.defaultExpectation.params = &GroupDomainServiceMockGetByParticipantIDParams{participantID}
	mmGetByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByParticipantID.expectations {
		if minimock.Equal(e.params, mmGetByParticipantID.defaultExpectation.params) {
			mmGetByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByParticipantID.defaultExpectation.params)
		}
	}

	return mmGetByParticipantID
}

// ExpectParticipantIDParam1 sets up expected param participantID for GroupDomainService.GetByParticipantID
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) ExpectParticipantIDParam1(participantID int64) *mGroupDomainServiceMockGetByParticipantID {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("GroupDomainServiceMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &GroupDomainServiceMockGetByParticipantIDExpectation{}
	}

	if mmGetByParticipantID.defaultExpectation.params != nil {
		mmGetByParticipantID.mock.t.Fatalf("GroupDomainServiceMock.GetByParticipantID mock is already set by Expect")
	}

	if mmGetByParticipantID.defaultExpectation.paramPtrs == nil {
		mmGetByParticipantID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByParticipantIDParamPtrs{}
	}
	mmGetByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByParticipantID
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Inspect(f func(participantID int64)) *mGroupDomainServiceMockGetByParticipantID {
	if mmGetByParticipantID.mock.inspectFuncGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByParticipantID")
	}

	mmGetByParticipantID.mock.inspectFuncGetByParticipantID = f

	return mmGetByParticipantID
}

// Return sets up results that will be returned by GroupDomainService.GetByParticipantID
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Return(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("GroupDomainServiceMock.GetByParticipantID mock is already set by Set")
	}

	if mmGetByParticipantID.defaultExpectation == nil {
		mmGetByParticipantID.defaultExpectation = &GroupDomainServiceMockGetByParticipantIDExpectation{mock: mmGetByParticipantID.mock}
	}
	mmGetByParticipantID.defaultExpectation.results = &GroupDomainServiceMockGetByParticipantIDResults{ga1, err}
	mmGetByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByParticipantID method
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Set(f func(participantID int64) (ga1 []groupentity.GroupWithStats, err error)) *GroupDomainServiceMock {
	if mmGetByParticipantID.defaultExpectation != nil {
		mmGetByParticipantID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByParticipantID method")
	}

	if len(mmGetByParticipantID.expectations) > 0 {
		mmGetByParticipantID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByParticipantID method")
	}

	mmGetByParticipantID.mock.funcGetByParticipantID = f
	mmGetByParticipantID.mock.funcGetByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID.mock
}

// When sets expectation for the GroupDomainService.GetByParticipantID which will trigger the result defined by the following
// Then helper
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) When(participantID int64) *GroupDomainServiceMockGetByParticipantIDExpectation {
	if mmGetByParticipantID.mock.funcGetByParticipantID != nil {
		mmGetByParticipantID.mock.t.Fatalf("GroupDomainServiceMock.GetByParticipantID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByParticipantIDExpectation{
		mock:               mmGetByParticipantID.mock,
		params:             &GroupDomainServiceMockGetByParticipantIDParams{participantID},
		expectationOrigins: GroupDomainServiceMockGetByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByParticipantID.expectations = append(mmGetByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByParticipantID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByParticipantIDExpectation) Then(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByParticipantIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByParticipantID should be invoked
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Times(n uint64) *mGroupDomainServiceMockGetByParticipantID {
	if n == 0 {
		mmGetByParticipantID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByParticipantID.expectedInvocations, n)
	mmGetByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByParticipantID
}

func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) invocationsDone() bool {
	if len(mmGetByParticipantID.expectations) == 0 && mmGetByParticipantID.defaultExpectation == nil && mmGetByParticipantID.mock.funcGetByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByParticipantID.mock.afterGetByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByParticipantID implements mm_service.GroupDomainService
func (mmGetByParticipantID *GroupDomainServiceMock) GetByParticipantID(participantID int64) (ga1 []groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmGetByParticipantID.beforeGetByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByParticipantID.afterGetByParticipantIDCounter, 1)

	mmGetByParticipantID.t.Helper()

	if mmGetByParticipantID.inspectFuncGetByParticipantID != nil {
		mmGetByParticipantID.inspectFuncGetByParticipantID(participantID)
	}

	mm_params := GroupDomainServiceMockGetByParticipantIDParams{participantID}

	// Record call args
	mmGetByParticipantID.GetByParticipantIDMock.mutex.Lock()
	mmGetByParticipantID.GetByParticipantIDMock.callArgs = append(mmGetByParticipantID.GetByParticipantIDMock.callArgs, &mm_params)
	mmGetByParticipantID.GetByParticipantIDMock.mutex.Unlock()

	for _, e := range mmGetByParticipantID.GetByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByParticipantIDParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetByParticipantID.t.Errorf("GroupDomainServiceMock.GetByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByParticipantID.t.Errorf("GroupDomainServiceMock.GetByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByParticipantID.GetByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByParticipantID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByParticipantID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByParticipantID.funcGetByParticipantID != nil {
		return mmGetByParticipantID.funcGetByParticipantID(participantID)
	}
	mmGetByParticipantID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByParticipantID. %v", participantID)
	return
}

// GetByParticipantIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByParticipantID invocations
func (mmGetByParticipantID *GroupDomainServiceMock) GetByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantID.afterGetByParticipantIDCounter)
}

// GetByParticipantIDBeforeCounter returns a count of GroupDomainServiceMock.GetByParticipantID invocations
func (mmGetByParticipantID *GroupDomainServiceMock) GetByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByParticipantID.beforeGetByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByParticipantID *mGroupDomainServiceMockGetByParticipantID) Calls() []*GroupDomainServiceMockGetByParticipantIDParams {
	mmGetByParticipantID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByParticipantIDParams, len(mmGetByParticipantID.callArgs))
	copy(argCopy, mmGetByParticipantID.callArgs)

	mmGetByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByParticipantIDDone returns true if the count of the GetByParticipantID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByParticipantIDDone() bool {
	if m.GetByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByParticipantIDMock.invocationsDone()
}

// MinimockGetByParticipantIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByParticipantIDInspect() {
	for _, e := range m.GetByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterGetByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByParticipantIDMock.defaultExpectation != nil && afterGetByParticipantIDCounter < 1 {
		if m.GetByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByParticipantID at\n%s", m.GetByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByParticipantID at\n%s with params: %#v", m.GetByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByParticipantID != nil && afterGetByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByParticipantID at\n%s", m.funcGetByParticipantIDOrigin)
	}

	if !m.GetByParticipantIDMock.invocationsDone() && afterGetByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByParticipantIDMock.expectedInvocations), m.GetByParticipantIDMock.expectedInvocationsOrigin, afterGetByParticipantIDCounter)
	}
}

type mGroupDomainServiceMockGetByProductID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByProductIDExpectation
	expectations       []*GroupDomainServiceMockGetByProductIDExpectation

	callArgs []*GroupDomainServiceMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByProductIDExpectation specifies expectation struct of the GroupDomainService.GetByProductID
type GroupDomainServiceMockGetByProductIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByProductIDParams
	paramPtrs          *GroupDomainServiceMockGetByProductIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByProductIDExpectationOrigins
	results            *GroupDomainServiceMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByProductIDParams contains parameters of the GroupDomainService.GetByProductID
type GroupDomainServiceMockGetByProductIDParams struct {
	productID int64
}

// GroupDomainServiceMockGetByProductIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByProductID
type GroupDomainServiceMockGetByProductIDParamPtrs struct {
	productID *int64
}

// GroupDomainServiceMockGetByProductIDResults contains results of the GroupDomainService.GetByProductID
type GroupDomainServiceMockGetByProductIDResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupDomainServiceMockGetByProductIDOrigins contains origins of expectations of the GroupDomainService.GetByProductID
type GroupDomainServiceMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Optional() *mGroupDomainServiceMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for GroupDomainService.GetByProductID
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Expect(productID int64) *mGroupDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &GroupDomainServiceMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &GroupDomainServiceMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for GroupDomainService.GetByProductID
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) ExpectProductIDParam1(productID int64) *mGroupDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &GroupDomainServiceMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByProductID
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Inspect(f func(productID int64)) *mGroupDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by GroupDomainService.GetByProductID
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Return(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &GroupDomainServiceMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &GroupDomainServiceMockGetByProductIDResults{ga1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByProductID method
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Set(f func(productID int64) (ga1 []groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the GroupDomainService.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) When(productID int64) *GroupDomainServiceMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetByProductID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &GroupDomainServiceMockGetByProductIDParams{productID},
		expectationOrigins: GroupDomainServiceMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByProductID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByProductIDExpectation) Then(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByProductIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByProductID should be invoked
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Times(n uint64) *mGroupDomainServiceMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_service.GroupDomainService
func (mmGetByProductID *GroupDomainServiceMock) GetByProductID(productID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := GroupDomainServiceMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("GroupDomainServiceMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("GroupDomainServiceMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByProductID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByProductID invocations
func (mmGetByProductID *GroupDomainServiceMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of GroupDomainServiceMock.GetByProductID invocations
func (mmGetByProductID *GroupDomainServiceMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mGroupDomainServiceMockGetByProductID) Calls() []*GroupDomainServiceMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mGroupDomainServiceMockGetByRoleID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByRoleIDExpectation
	expectations       []*GroupDomainServiceMockGetByRoleIDExpectation

	callArgs []*GroupDomainServiceMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByRoleIDExpectation specifies expectation struct of the GroupDomainService.GetByRoleID
type GroupDomainServiceMockGetByRoleIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByRoleIDParams
	paramPtrs          *GroupDomainServiceMockGetByRoleIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByRoleIDExpectationOrigins
	results            *GroupDomainServiceMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByRoleIDParams contains parameters of the GroupDomainService.GetByRoleID
type GroupDomainServiceMockGetByRoleIDParams struct {
	roleID int64
}

// GroupDomainServiceMockGetByRoleIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByRoleID
type GroupDomainServiceMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// GroupDomainServiceMockGetByRoleIDResults contains results of the GroupDomainService.GetByRoleID
type GroupDomainServiceMockGetByRoleIDResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupDomainServiceMockGetByRoleIDOrigins contains origins of expectations of the GroupDomainService.GetByRoleID
type GroupDomainServiceMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Optional() *mGroupDomainServiceMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for GroupDomainService.GetByRoleID
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Expect(roleID int64) *mGroupDomainServiceMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &GroupDomainServiceMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &GroupDomainServiceMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for GroupDomainService.GetByRoleID
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mGroupDomainServiceMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &GroupDomainServiceMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByRoleID
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Inspect(f func(roleID int64)) *mGroupDomainServiceMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by GroupDomainService.GetByRoleID
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Return(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &GroupDomainServiceMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &GroupDomainServiceMockGetByRoleIDResults{ga1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByRoleID method
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Set(f func(roleID int64) (ga1 []groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the GroupDomainService.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) When(roleID int64) *GroupDomainServiceMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &GroupDomainServiceMockGetByRoleIDParams{roleID},
		expectationOrigins: GroupDomainServiceMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByRoleIDExpectation) Then(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByRoleIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByRoleID should be invoked
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Times(n uint64) *mGroupDomainServiceMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_service.GroupDomainService
func (mmGetByRoleID *GroupDomainServiceMock) GetByRoleID(roleID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := GroupDomainServiceMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("GroupDomainServiceMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("GroupDomainServiceMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByRoleID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByRoleID invocations
func (mmGetByRoleID *GroupDomainServiceMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of GroupDomainServiceMock.GetByRoleID invocations
func (mmGetByRoleID *GroupDomainServiceMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mGroupDomainServiceMockGetByRoleID) Calls() []*GroupDomainServiceMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

type mGroupDomainServiceMockGetByUserID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetByUserIDExpectation
	expectations       []*GroupDomainServiceMockGetByUserIDExpectation

	callArgs []*GroupDomainServiceMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetByUserIDExpectation specifies expectation struct of the GroupDomainService.GetByUserID
type GroupDomainServiceMockGetByUserIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetByUserIDParams
	paramPtrs          *GroupDomainServiceMockGetByUserIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetByUserIDExpectationOrigins
	results            *GroupDomainServiceMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetByUserIDParams contains parameters of the GroupDomainService.GetByUserID
type GroupDomainServiceMockGetByUserIDParams struct {
	userID int64
}

// GroupDomainServiceMockGetByUserIDParamPtrs contains pointers to parameters of the GroupDomainService.GetByUserID
type GroupDomainServiceMockGetByUserIDParamPtrs struct {
	userID *int64
}

// GroupDomainServiceMockGetByUserIDResults contains results of the GroupDomainService.GetByUserID
type GroupDomainServiceMockGetByUserIDResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupDomainServiceMockGetByUserIDOrigins contains origins of expectations of the GroupDomainService.GetByUserID
type GroupDomainServiceMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Optional() *mGroupDomainServiceMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for GroupDomainService.GetByUserID
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Expect(userID int64) *mGroupDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &GroupDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &GroupDomainServiceMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for GroupDomainService.GetByUserID
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) ExpectUserIDParam1(userID int64) *mGroupDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &GroupDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetByUserID
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Inspect(f func(userID int64)) *mGroupDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by GroupDomainService.GetByUserID
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Return(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &GroupDomainServiceMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &GroupDomainServiceMockGetByUserIDResults{ga1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the GroupDomainService.GetByUserID method
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Set(f func(userID int64) (ga1 []groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the GroupDomainService.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) When(userID int64) *GroupDomainServiceMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetByUserID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &GroupDomainServiceMockGetByUserIDParams{userID},
		expectationOrigins: GroupDomainServiceMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetByUserID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetByUserIDExpectation) Then(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetByUserIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetByUserID should be invoked
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Times(n uint64) *mGroupDomainServiceMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_service.GroupDomainService
func (mmGetByUserID *GroupDomainServiceMock) GetByUserID(userID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := GroupDomainServiceMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("GroupDomainServiceMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("GroupDomainServiceMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the GroupDomainServiceMock.GetByUserID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished GroupDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *GroupDomainServiceMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of GroupDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *GroupDomainServiceMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mGroupDomainServiceMockGetByUserID) Calls() []*GroupDomainServiceMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mGroupDomainServiceMockGetGroupsWithCategoryStats struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetGroupsWithCategoryStatsExpectation
	expectations       []*GroupDomainServiceMockGetGroupsWithCategoryStatsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetGroupsWithCategoryStatsExpectation specifies expectation struct of the GroupDomainService.GetGroupsWithCategoryStats
type GroupDomainServiceMockGetGroupsWithCategoryStatsExpectation struct {
	mock *GroupDomainServiceMock

	results      *GroupDomainServiceMockGetGroupsWithCategoryStatsResults
	returnOrigin string
	Counter      uint64
}

// GroupDomainServiceMockGetGroupsWithCategoryStatsResults contains results of the GroupDomainService.GetGroupsWithCategoryStats
type GroupDomainServiceMockGetGroupsWithCategoryStatsResults struct {
	ga1 []groupentity.GroupWithCategoryStats
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) Optional() *mGroupDomainServiceMockGetGroupsWithCategoryStats {
	mmGetGroupsWithCategoryStats.optional = true
	return mmGetGroupsWithCategoryStats
}

// Expect sets up expected params for GroupDomainService.GetGroupsWithCategoryStats
func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) Expect() *mGroupDomainServiceMockGetGroupsWithCategoryStats {
	if mmGetGroupsWithCategoryStats.mock.funcGetGroupsWithCategoryStats != nil {
		mmGetGroupsWithCategoryStats.mock.t.Fatalf("GroupDomainServiceMock.GetGroupsWithCategoryStats mock is already set by Set")
	}

	if mmGetGroupsWithCategoryStats.defaultExpectation == nil {
		mmGetGroupsWithCategoryStats.defaultExpectation = &GroupDomainServiceMockGetGroupsWithCategoryStatsExpectation{}
	}

	return mmGetGroupsWithCategoryStats
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetGroupsWithCategoryStats
func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) Inspect(f func()) *mGroupDomainServiceMockGetGroupsWithCategoryStats {
	if mmGetGroupsWithCategoryStats.mock.inspectFuncGetGroupsWithCategoryStats != nil {
		mmGetGroupsWithCategoryStats.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetGroupsWithCategoryStats")
	}

	mmGetGroupsWithCategoryStats.mock.inspectFuncGetGroupsWithCategoryStats = f

	return mmGetGroupsWithCategoryStats
}

// Return sets up results that will be returned by GroupDomainService.GetGroupsWithCategoryStats
func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) Return(ga1 []groupentity.GroupWithCategoryStats, err error) *GroupDomainServiceMock {
	if mmGetGroupsWithCategoryStats.mock.funcGetGroupsWithCategoryStats != nil {
		mmGetGroupsWithCategoryStats.mock.t.Fatalf("GroupDomainServiceMock.GetGroupsWithCategoryStats mock is already set by Set")
	}

	if mmGetGroupsWithCategoryStats.defaultExpectation == nil {
		mmGetGroupsWithCategoryStats.defaultExpectation = &GroupDomainServiceMockGetGroupsWithCategoryStatsExpectation{mock: mmGetGroupsWithCategoryStats.mock}
	}
	mmGetGroupsWithCategoryStats.defaultExpectation.results = &GroupDomainServiceMockGetGroupsWithCategoryStatsResults{ga1, err}
	mmGetGroupsWithCategoryStats.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetGroupsWithCategoryStats.mock
}

// Set uses given function f to mock the GroupDomainService.GetGroupsWithCategoryStats method
func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) Set(f func() (ga1 []groupentity.GroupWithCategoryStats, err error)) *GroupDomainServiceMock {
	if mmGetGroupsWithCategoryStats.defaultExpectation != nil {
		mmGetGroupsWithCategoryStats.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetGroupsWithCategoryStats method")
	}

	if len(mmGetGroupsWithCategoryStats.expectations) > 0 {
		mmGetGroupsWithCategoryStats.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetGroupsWithCategoryStats method")
	}

	mmGetGroupsWithCategoryStats.mock.funcGetGroupsWithCategoryStats = f
	mmGetGroupsWithCategoryStats.mock.funcGetGroupsWithCategoryStatsOrigin = minimock.CallerInfo(1)
	return mmGetGroupsWithCategoryStats.mock
}

// Times sets number of times GroupDomainService.GetGroupsWithCategoryStats should be invoked
func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) Times(n uint64) *mGroupDomainServiceMockGetGroupsWithCategoryStats {
	if n == 0 {
		mmGetGroupsWithCategoryStats.mock.t.Fatalf("Times of GroupDomainServiceMock.GetGroupsWithCategoryStats mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetGroupsWithCategoryStats.expectedInvocations, n)
	mmGetGroupsWithCategoryStats.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetGroupsWithCategoryStats
}

func (mmGetGroupsWithCategoryStats *mGroupDomainServiceMockGetGroupsWithCategoryStats) invocationsDone() bool {
	if len(mmGetGroupsWithCategoryStats.expectations) == 0 && mmGetGroupsWithCategoryStats.defaultExpectation == nil && mmGetGroupsWithCategoryStats.mock.funcGetGroupsWithCategoryStats == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetGroupsWithCategoryStats.mock.afterGetGroupsWithCategoryStatsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetGroupsWithCategoryStats.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetGroupsWithCategoryStats implements mm_service.GroupDomainService
func (mmGetGroupsWithCategoryStats *GroupDomainServiceMock) GetGroupsWithCategoryStats() (ga1 []groupentity.GroupWithCategoryStats, err error) {
	mm_atomic.AddUint64(&mmGetGroupsWithCategoryStats.beforeGetGroupsWithCategoryStatsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetGroupsWithCategoryStats.afterGetGroupsWithCategoryStatsCounter, 1)

	mmGetGroupsWithCategoryStats.t.Helper()

	if mmGetGroupsWithCategoryStats.inspectFuncGetGroupsWithCategoryStats != nil {
		mmGetGroupsWithCategoryStats.inspectFuncGetGroupsWithCategoryStats()
	}

	if mmGetGroupsWithCategoryStats.GetGroupsWithCategoryStatsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetGroupsWithCategoryStats.GetGroupsWithCategoryStatsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetGroupsWithCategoryStats.GetGroupsWithCategoryStatsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetGroupsWithCategoryStats.t.Fatal("No results are set for the GroupDomainServiceMock.GetGroupsWithCategoryStats")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetGroupsWithCategoryStats.funcGetGroupsWithCategoryStats != nil {
		return mmGetGroupsWithCategoryStats.funcGetGroupsWithCategoryStats()
	}
	mmGetGroupsWithCategoryStats.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetGroupsWithCategoryStats.")
	return
}

// GetGroupsWithCategoryStatsAfterCounter returns a count of finished GroupDomainServiceMock.GetGroupsWithCategoryStats invocations
func (mmGetGroupsWithCategoryStats *GroupDomainServiceMock) GetGroupsWithCategoryStatsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroupsWithCategoryStats.afterGetGroupsWithCategoryStatsCounter)
}

// GetGroupsWithCategoryStatsBeforeCounter returns a count of GroupDomainServiceMock.GetGroupsWithCategoryStats invocations
func (mmGetGroupsWithCategoryStats *GroupDomainServiceMock) GetGroupsWithCategoryStatsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroupsWithCategoryStats.beforeGetGroupsWithCategoryStatsCounter)
}

// MinimockGetGroupsWithCategoryStatsDone returns true if the count of the GetGroupsWithCategoryStats invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetGroupsWithCategoryStatsDone() bool {
	if m.GetGroupsWithCategoryStatsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetGroupsWithCategoryStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetGroupsWithCategoryStatsMock.invocationsDone()
}

// MinimockGetGroupsWithCategoryStatsInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetGroupsWithCategoryStatsInspect() {
	for _, e := range m.GetGroupsWithCategoryStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupDomainServiceMock.GetGroupsWithCategoryStats")
		}
	}

	afterGetGroupsWithCategoryStatsCounter := mm_atomic.LoadUint64(&m.afterGetGroupsWithCategoryStatsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetGroupsWithCategoryStatsMock.defaultExpectation != nil && afterGetGroupsWithCategoryStatsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetGroupsWithCategoryStats at\n%s", m.GetGroupsWithCategoryStatsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetGroupsWithCategoryStats != nil && afterGetGroupsWithCategoryStatsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetGroupsWithCategoryStats at\n%s", m.funcGetGroupsWithCategoryStatsOrigin)
	}

	if !m.GetGroupsWithCategoryStatsMock.invocationsDone() && afterGetGroupsWithCategoryStatsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetGroupsWithCategoryStats at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetGroupsWithCategoryStatsMock.expectedInvocations), m.GetGroupsWithCategoryStatsMock.expectedInvocationsOrigin, afterGetGroupsWithCategoryStatsCounter)
	}
}

type mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation
	expectations       []*GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation

	callArgs []*GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation specifies expectation struct of the GroupDomainService.GetParticipantGroupsWithProductByUserID
type GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams
	paramPtrs          *GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectationOrigins
	results            *GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams contains parameters of the GroupDomainService.GetParticipantGroupsWithProductByUserID
type GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams struct {
	userID int64
}

// GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParamPtrs contains pointers to parameters of the GroupDomainService.GetParticipantGroupsWithProductByUserID
type GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParamPtrs struct {
	userID *int64
}

// GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDResults contains results of the GroupDomainService.GetParticipantGroupsWithProductByUserID
type GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDResults struct {
	ga1 []groupentity.Group
	err error
}

// GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDOrigins contains origins of expectations of the GroupDomainService.GetParticipantGroupsWithProductByUserID
type GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Optional() *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID {
	mmGetParticipantGroupsWithProductByUserID.optional = true
	return mmGetParticipantGroupsWithProductByUserID
}

// Expect sets up expected params for GroupDomainService.GetParticipantGroupsWithProductByUserID
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Expect(userID int64) *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID {
	if mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserID != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock is already set by Set")
	}

	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation == nil {
		mmGetParticipantGroupsWithProductByUserID.defaultExpectation = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation{}
	}

	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation.paramPtrs != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock is already set by ExpectParams functions")
	}

	mmGetParticipantGroupsWithProductByUserID.defaultExpectation.params = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams{userID}
	mmGetParticipantGroupsWithProductByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetParticipantGroupsWithProductByUserID.expectations {
		if minimock.Equal(e.params, mmGetParticipantGroupsWithProductByUserID.defaultExpectation.params) {
			mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetParticipantGroupsWithProductByUserID.defaultExpectation.params)
		}
	}

	return mmGetParticipantGroupsWithProductByUserID
}

// ExpectUserIDParam1 sets up expected param userID for GroupDomainService.GetParticipantGroupsWithProductByUserID
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) ExpectUserIDParam1(userID int64) *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID {
	if mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserID != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock is already set by Set")
	}

	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation == nil {
		mmGetParticipantGroupsWithProductByUserID.defaultExpectation = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation{}
	}

	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation.params != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock is already set by Expect")
	}

	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation.paramPtrs == nil {
		mmGetParticipantGroupsWithProductByUserID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParamPtrs{}
	}
	mmGetParticipantGroupsWithProductByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetParticipantGroupsWithProductByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetParticipantGroupsWithProductByUserID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetParticipantGroupsWithProductByUserID
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Inspect(f func(userID int64)) *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID {
	if mmGetParticipantGroupsWithProductByUserID.mock.inspectFuncGetParticipantGroupsWithProductByUserID != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID")
	}

	mmGetParticipantGroupsWithProductByUserID.mock.inspectFuncGetParticipantGroupsWithProductByUserID = f

	return mmGetParticipantGroupsWithProductByUserID
}

// Return sets up results that will be returned by GroupDomainService.GetParticipantGroupsWithProductByUserID
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Return(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	if mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserID != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock is already set by Set")
	}

	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation == nil {
		mmGetParticipantGroupsWithProductByUserID.defaultExpectation = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation{mock: mmGetParticipantGroupsWithProductByUserID.mock}
	}
	mmGetParticipantGroupsWithProductByUserID.defaultExpectation.results = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDResults{ga1, err}
	mmGetParticipantGroupsWithProductByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetParticipantGroupsWithProductByUserID.mock
}

// Set uses given function f to mock the GroupDomainService.GetParticipantGroupsWithProductByUserID method
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Set(f func(userID int64) (ga1 []groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmGetParticipantGroupsWithProductByUserID.defaultExpectation != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetParticipantGroupsWithProductByUserID method")
	}

	if len(mmGetParticipantGroupsWithProductByUserID.expectations) > 0 {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetParticipantGroupsWithProductByUserID method")
	}

	mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserID = f
	mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetParticipantGroupsWithProductByUserID.mock
}

// When sets expectation for the GroupDomainService.GetParticipantGroupsWithProductByUserID which will trigger the result defined by the following
// Then helper
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) When(userID int64) *GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation {
	if mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserID != nil {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation{
		mock:               mmGetParticipantGroupsWithProductByUserID.mock,
		params:             &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams{userID},
		expectationOrigins: GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetParticipantGroupsWithProductByUserID.expectations = append(mmGetParticipantGroupsWithProductByUserID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetParticipantGroupsWithProductByUserID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDExpectation) Then(ga1 []groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetParticipantGroupsWithProductByUserID should be invoked
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Times(n uint64) *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID {
	if n == 0 {
		mmGetParticipantGroupsWithProductByUserID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetParticipantGroupsWithProductByUserID.expectedInvocations, n)
	mmGetParticipantGroupsWithProductByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetParticipantGroupsWithProductByUserID
}

func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) invocationsDone() bool {
	if len(mmGetParticipantGroupsWithProductByUserID.expectations) == 0 && mmGetParticipantGroupsWithProductByUserID.defaultExpectation == nil && mmGetParticipantGroupsWithProductByUserID.mock.funcGetParticipantGroupsWithProductByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetParticipantGroupsWithProductByUserID.mock.afterGetParticipantGroupsWithProductByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetParticipantGroupsWithProductByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetParticipantGroupsWithProductByUserID implements mm_service.GroupDomainService
func (mmGetParticipantGroupsWithProductByUserID *GroupDomainServiceMock) GetParticipantGroupsWithProductByUserID(userID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetParticipantGroupsWithProductByUserID.beforeGetParticipantGroupsWithProductByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetParticipantGroupsWithProductByUserID.afterGetParticipantGroupsWithProductByUserIDCounter, 1)

	mmGetParticipantGroupsWithProductByUserID.t.Helper()

	if mmGetParticipantGroupsWithProductByUserID.inspectFuncGetParticipantGroupsWithProductByUserID != nil {
		mmGetParticipantGroupsWithProductByUserID.inspectFuncGetParticipantGroupsWithProductByUserID(userID)
	}

	mm_params := GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams{userID}

	// Record call args
	mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.mutex.Lock()
	mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.callArgs = append(mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.callArgs, &mm_params)
	mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.mutex.Unlock()

	for _, e := range mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetParticipantGroupsWithProductByUserID.t.Errorf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetParticipantGroupsWithProductByUserID.t.Errorf("GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetParticipantGroupsWithProductByUserID.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetParticipantGroupsWithProductByUserID.t.Fatal("No results are set for the GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetParticipantGroupsWithProductByUserID.funcGetParticipantGroupsWithProductByUserID != nil {
		return mmGetParticipantGroupsWithProductByUserID.funcGetParticipantGroupsWithProductByUserID(userID)
	}
	mmGetParticipantGroupsWithProductByUserID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID. %v", userID)
	return
}

// GetParticipantGroupsWithProductByUserIDAfterCounter returns a count of finished GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID invocations
func (mmGetParticipantGroupsWithProductByUserID *GroupDomainServiceMock) GetParticipantGroupsWithProductByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantGroupsWithProductByUserID.afterGetParticipantGroupsWithProductByUserIDCounter)
}

// GetParticipantGroupsWithProductByUserIDBeforeCounter returns a count of GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID invocations
func (mmGetParticipantGroupsWithProductByUserID *GroupDomainServiceMock) GetParticipantGroupsWithProductByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantGroupsWithProductByUserID.beforeGetParticipantGroupsWithProductByUserIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetParticipantGroupsWithProductByUserID *mGroupDomainServiceMockGetParticipantGroupsWithProductByUserID) Calls() []*GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams {
	mmGetParticipantGroupsWithProductByUserID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetParticipantGroupsWithProductByUserIDParams, len(mmGetParticipantGroupsWithProductByUserID.callArgs))
	copy(argCopy, mmGetParticipantGroupsWithProductByUserID.callArgs)

	mmGetParticipantGroupsWithProductByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetParticipantGroupsWithProductByUserIDDone returns true if the count of the GetParticipantGroupsWithProductByUserID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetParticipantGroupsWithProductByUserIDDone() bool {
	if m.GetParticipantGroupsWithProductByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetParticipantGroupsWithProductByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetParticipantGroupsWithProductByUserIDMock.invocationsDone()
}

// MinimockGetParticipantGroupsWithProductByUserIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetParticipantGroupsWithProductByUserIDInspect() {
	for _, e := range m.GetParticipantGroupsWithProductByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetParticipantGroupsWithProductByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetParticipantGroupsWithProductByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation != nil && afterGetParticipantGroupsWithProductByUserIDCounter < 1 {
		if m.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID at\n%s", m.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID at\n%s with params: %#v", m.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetParticipantGroupsWithProductByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetParticipantGroupsWithProductByUserID != nil && afterGetParticipantGroupsWithProductByUserIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID at\n%s", m.funcGetParticipantGroupsWithProductByUserIDOrigin)
	}

	if !m.GetParticipantGroupsWithProductByUserIDMock.invocationsDone() && afterGetParticipantGroupsWithProductByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetParticipantGroupsWithProductByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetParticipantGroupsWithProductByUserIDMock.expectedInvocations), m.GetParticipantGroupsWithProductByUserIDMock.expectedInvocationsOrigin, afterGetParticipantGroupsWithProductByUserIDCounter)
	}
}

type mGroupDomainServiceMockGetSystemGroupsWithStats struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetSystemGroupsWithStatsExpectation
	expectations       []*GroupDomainServiceMockGetSystemGroupsWithStatsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetSystemGroupsWithStatsExpectation specifies expectation struct of the GroupDomainService.GetSystemGroupsWithStats
type GroupDomainServiceMockGetSystemGroupsWithStatsExpectation struct {
	mock *GroupDomainServiceMock

	results      *GroupDomainServiceMockGetSystemGroupsWithStatsResults
	returnOrigin string
	Counter      uint64
}

// GroupDomainServiceMockGetSystemGroupsWithStatsResults contains results of the GroupDomainService.GetSystemGroupsWithStats
type GroupDomainServiceMockGetSystemGroupsWithStatsResults struct {
	ga1 []groupentity.GroupWithStats
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) Optional() *mGroupDomainServiceMockGetSystemGroupsWithStats {
	mmGetSystemGroupsWithStats.optional = true
	return mmGetSystemGroupsWithStats
}

// Expect sets up expected params for GroupDomainService.GetSystemGroupsWithStats
func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) Expect() *mGroupDomainServiceMockGetSystemGroupsWithStats {
	if mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("GroupDomainServiceMock.GetSystemGroupsWithStats mock is already set by Set")
	}

	if mmGetSystemGroupsWithStats.defaultExpectation == nil {
		mmGetSystemGroupsWithStats.defaultExpectation = &GroupDomainServiceMockGetSystemGroupsWithStatsExpectation{}
	}

	return mmGetSystemGroupsWithStats
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetSystemGroupsWithStats
func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) Inspect(f func()) *mGroupDomainServiceMockGetSystemGroupsWithStats {
	if mmGetSystemGroupsWithStats.mock.inspectFuncGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetSystemGroupsWithStats")
	}

	mmGetSystemGroupsWithStats.mock.inspectFuncGetSystemGroupsWithStats = f

	return mmGetSystemGroupsWithStats
}

// Return sets up results that will be returned by GroupDomainService.GetSystemGroupsWithStats
func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) Return(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	if mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("GroupDomainServiceMock.GetSystemGroupsWithStats mock is already set by Set")
	}

	if mmGetSystemGroupsWithStats.defaultExpectation == nil {
		mmGetSystemGroupsWithStats.defaultExpectation = &GroupDomainServiceMockGetSystemGroupsWithStatsExpectation{mock: mmGetSystemGroupsWithStats.mock}
	}
	mmGetSystemGroupsWithStats.defaultExpectation.results = &GroupDomainServiceMockGetSystemGroupsWithStatsResults{ga1, err}
	mmGetSystemGroupsWithStats.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroupsWithStats.mock
}

// Set uses given function f to mock the GroupDomainService.GetSystemGroupsWithStats method
func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) Set(f func() (ga1 []groupentity.GroupWithStats, err error)) *GroupDomainServiceMock {
	if mmGetSystemGroupsWithStats.defaultExpectation != nil {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetSystemGroupsWithStats method")
	}

	if len(mmGetSystemGroupsWithStats.expectations) > 0 {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetSystemGroupsWithStats method")
	}

	mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats = f
	mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStatsOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroupsWithStats.mock
}

// Times sets number of times GroupDomainService.GetSystemGroupsWithStats should be invoked
func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) Times(n uint64) *mGroupDomainServiceMockGetSystemGroupsWithStats {
	if n == 0 {
		mmGetSystemGroupsWithStats.mock.t.Fatalf("Times of GroupDomainServiceMock.GetSystemGroupsWithStats mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetSystemGroupsWithStats.expectedInvocations, n)
	mmGetSystemGroupsWithStats.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetSystemGroupsWithStats
}

func (mmGetSystemGroupsWithStats *mGroupDomainServiceMockGetSystemGroupsWithStats) invocationsDone() bool {
	if len(mmGetSystemGroupsWithStats.expectations) == 0 && mmGetSystemGroupsWithStats.defaultExpectation == nil && mmGetSystemGroupsWithStats.mock.funcGetSystemGroupsWithStats == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.mock.afterGetSystemGroupsWithStatsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetSystemGroupsWithStats implements mm_service.GroupDomainService
func (mmGetSystemGroupsWithStats *GroupDomainServiceMock) GetSystemGroupsWithStats() (ga1 []groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmGetSystemGroupsWithStats.beforeGetSystemGroupsWithStatsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetSystemGroupsWithStats.afterGetSystemGroupsWithStatsCounter, 1)

	mmGetSystemGroupsWithStats.t.Helper()

	if mmGetSystemGroupsWithStats.inspectFuncGetSystemGroupsWithStats != nil {
		mmGetSystemGroupsWithStats.inspectFuncGetSystemGroupsWithStats()
	}

	if mmGetSystemGroupsWithStats.GetSystemGroupsWithStatsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetSystemGroupsWithStats.GetSystemGroupsWithStatsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetSystemGroupsWithStats.GetSystemGroupsWithStatsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetSystemGroupsWithStats.t.Fatal("No results are set for the GroupDomainServiceMock.GetSystemGroupsWithStats")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetSystemGroupsWithStats.funcGetSystemGroupsWithStats != nil {
		return mmGetSystemGroupsWithStats.funcGetSystemGroupsWithStats()
	}
	mmGetSystemGroupsWithStats.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetSystemGroupsWithStats.")
	return
}

// GetSystemGroupsWithStatsAfterCounter returns a count of finished GroupDomainServiceMock.GetSystemGroupsWithStats invocations
func (mmGetSystemGroupsWithStats *GroupDomainServiceMock) GetSystemGroupsWithStatsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.afterGetSystemGroupsWithStatsCounter)
}

// GetSystemGroupsWithStatsBeforeCounter returns a count of GroupDomainServiceMock.GetSystemGroupsWithStats invocations
func (mmGetSystemGroupsWithStats *GroupDomainServiceMock) GetSystemGroupsWithStatsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemGroupsWithStats.beforeGetSystemGroupsWithStatsCounter)
}

// MinimockGetSystemGroupsWithStatsDone returns true if the count of the GetSystemGroupsWithStats invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetSystemGroupsWithStatsDone() bool {
	if m.GetSystemGroupsWithStatsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetSystemGroupsWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetSystemGroupsWithStatsMock.invocationsDone()
}

// MinimockGetSystemGroupsWithStatsInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetSystemGroupsWithStatsInspect() {
	for _, e := range m.GetSystemGroupsWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to GroupDomainServiceMock.GetSystemGroupsWithStats")
		}
	}

	afterGetSystemGroupsWithStatsCounter := mm_atomic.LoadUint64(&m.afterGetSystemGroupsWithStatsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetSystemGroupsWithStatsMock.defaultExpectation != nil && afterGetSystemGroupsWithStatsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetSystemGroupsWithStats at\n%s", m.GetSystemGroupsWithStatsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetSystemGroupsWithStats != nil && afterGetSystemGroupsWithStatsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetSystemGroupsWithStats at\n%s", m.funcGetSystemGroupsWithStatsOrigin)
	}

	if !m.GetSystemGroupsWithStatsMock.invocationsDone() && afterGetSystemGroupsWithStatsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetSystemGroupsWithStats at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetSystemGroupsWithStatsMock.expectedInvocations), m.GetSystemGroupsWithStatsMock.expectedInvocationsOrigin, afterGetSystemGroupsWithStatsCounter)
	}
}

type mGroupDomainServiceMockGetWithCountsByProductID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetWithCountsByProductIDExpectation
	expectations       []*GroupDomainServiceMockGetWithCountsByProductIDExpectation

	callArgs []*GroupDomainServiceMockGetWithCountsByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetWithCountsByProductIDExpectation specifies expectation struct of the GroupDomainService.GetWithCountsByProductID
type GroupDomainServiceMockGetWithCountsByProductIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetWithCountsByProductIDParams
	paramPtrs          *GroupDomainServiceMockGetWithCountsByProductIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetWithCountsByProductIDExpectationOrigins
	results            *GroupDomainServiceMockGetWithCountsByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetWithCountsByProductIDParams contains parameters of the GroupDomainService.GetWithCountsByProductID
type GroupDomainServiceMockGetWithCountsByProductIDParams struct {
	productID int64
}

// GroupDomainServiceMockGetWithCountsByProductIDParamPtrs contains pointers to parameters of the GroupDomainService.GetWithCountsByProductID
type GroupDomainServiceMockGetWithCountsByProductIDParamPtrs struct {
	productID *int64
}

// GroupDomainServiceMockGetWithCountsByProductIDResults contains results of the GroupDomainService.GetWithCountsByProductID
type GroupDomainServiceMockGetWithCountsByProductIDResults struct {
	aa1 []groupentity.AdminGroup
	err error
}

// GroupDomainServiceMockGetWithCountsByProductIDOrigins contains origins of expectations of the GroupDomainService.GetWithCountsByProductID
type GroupDomainServiceMockGetWithCountsByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Optional() *mGroupDomainServiceMockGetWithCountsByProductID {
	mmGetWithCountsByProductID.optional = true
	return mmGetWithCountsByProductID
}

// Expect sets up expected params for GroupDomainService.GetWithCountsByProductID
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Expect(productID int64) *mGroupDomainServiceMockGetWithCountsByProductID {
	if mmGetWithCountsByProductID.mock.funcGetWithCountsByProductID != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithCountsByProductID mock is already set by Set")
	}

	if mmGetWithCountsByProductID.defaultExpectation == nil {
		mmGetWithCountsByProductID.defaultExpectation = &GroupDomainServiceMockGetWithCountsByProductIDExpectation{}
	}

	if mmGetWithCountsByProductID.defaultExpectation.paramPtrs != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithCountsByProductID mock is already set by ExpectParams functions")
	}

	mmGetWithCountsByProductID.defaultExpectation.params = &GroupDomainServiceMockGetWithCountsByProductIDParams{productID}
	mmGetWithCountsByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetWithCountsByProductID.expectations {
		if minimock.Equal(e.params, mmGetWithCountsByProductID.defaultExpectation.params) {
			mmGetWithCountsByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetWithCountsByProductID.defaultExpectation.params)
		}
	}

	return mmGetWithCountsByProductID
}

// ExpectProductIDParam1 sets up expected param productID for GroupDomainService.GetWithCountsByProductID
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) ExpectProductIDParam1(productID int64) *mGroupDomainServiceMockGetWithCountsByProductID {
	if mmGetWithCountsByProductID.mock.funcGetWithCountsByProductID != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithCountsByProductID mock is already set by Set")
	}

	if mmGetWithCountsByProductID.defaultExpectation == nil {
		mmGetWithCountsByProductID.defaultExpectation = &GroupDomainServiceMockGetWithCountsByProductIDExpectation{}
	}

	if mmGetWithCountsByProductID.defaultExpectation.params != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithCountsByProductID mock is already set by Expect")
	}

	if mmGetWithCountsByProductID.defaultExpectation.paramPtrs == nil {
		mmGetWithCountsByProductID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetWithCountsByProductIDParamPtrs{}
	}
	mmGetWithCountsByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetWithCountsByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetWithCountsByProductID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetWithCountsByProductID
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Inspect(f func(productID int64)) *mGroupDomainServiceMockGetWithCountsByProductID {
	if mmGetWithCountsByProductID.mock.inspectFuncGetWithCountsByProductID != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetWithCountsByProductID")
	}

	mmGetWithCountsByProductID.mock.inspectFuncGetWithCountsByProductID = f

	return mmGetWithCountsByProductID
}

// Return sets up results that will be returned by GroupDomainService.GetWithCountsByProductID
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Return(aa1 []groupentity.AdminGroup, err error) *GroupDomainServiceMock {
	if mmGetWithCountsByProductID.mock.funcGetWithCountsByProductID != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithCountsByProductID mock is already set by Set")
	}

	if mmGetWithCountsByProductID.defaultExpectation == nil {
		mmGetWithCountsByProductID.defaultExpectation = &GroupDomainServiceMockGetWithCountsByProductIDExpectation{mock: mmGetWithCountsByProductID.mock}
	}
	mmGetWithCountsByProductID.defaultExpectation.results = &GroupDomainServiceMockGetWithCountsByProductIDResults{aa1, err}
	mmGetWithCountsByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetWithCountsByProductID.mock
}

// Set uses given function f to mock the GroupDomainService.GetWithCountsByProductID method
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Set(f func(productID int64) (aa1 []groupentity.AdminGroup, err error)) *GroupDomainServiceMock {
	if mmGetWithCountsByProductID.defaultExpectation != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetWithCountsByProductID method")
	}

	if len(mmGetWithCountsByProductID.expectations) > 0 {
		mmGetWithCountsByProductID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetWithCountsByProductID method")
	}

	mmGetWithCountsByProductID.mock.funcGetWithCountsByProductID = f
	mmGetWithCountsByProductID.mock.funcGetWithCountsByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetWithCountsByProductID.mock
}

// When sets expectation for the GroupDomainService.GetWithCountsByProductID which will trigger the result defined by the following
// Then helper
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) When(productID int64) *GroupDomainServiceMockGetWithCountsByProductIDExpectation {
	if mmGetWithCountsByProductID.mock.funcGetWithCountsByProductID != nil {
		mmGetWithCountsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithCountsByProductID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetWithCountsByProductIDExpectation{
		mock:               mmGetWithCountsByProductID.mock,
		params:             &GroupDomainServiceMockGetWithCountsByProductIDParams{productID},
		expectationOrigins: GroupDomainServiceMockGetWithCountsByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetWithCountsByProductID.expectations = append(mmGetWithCountsByProductID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetWithCountsByProductID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetWithCountsByProductIDExpectation) Then(aa1 []groupentity.AdminGroup, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetWithCountsByProductIDResults{aa1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetWithCountsByProductID should be invoked
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Times(n uint64) *mGroupDomainServiceMockGetWithCountsByProductID {
	if n == 0 {
		mmGetWithCountsByProductID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetWithCountsByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetWithCountsByProductID.expectedInvocations, n)
	mmGetWithCountsByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetWithCountsByProductID
}

func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) invocationsDone() bool {
	if len(mmGetWithCountsByProductID.expectations) == 0 && mmGetWithCountsByProductID.defaultExpectation == nil && mmGetWithCountsByProductID.mock.funcGetWithCountsByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetWithCountsByProductID.mock.afterGetWithCountsByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetWithCountsByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetWithCountsByProductID implements mm_service.GroupDomainService
func (mmGetWithCountsByProductID *GroupDomainServiceMock) GetWithCountsByProductID(productID int64) (aa1 []groupentity.AdminGroup, err error) {
	mm_atomic.AddUint64(&mmGetWithCountsByProductID.beforeGetWithCountsByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetWithCountsByProductID.afterGetWithCountsByProductIDCounter, 1)

	mmGetWithCountsByProductID.t.Helper()

	if mmGetWithCountsByProductID.inspectFuncGetWithCountsByProductID != nil {
		mmGetWithCountsByProductID.inspectFuncGetWithCountsByProductID(productID)
	}

	mm_params := GroupDomainServiceMockGetWithCountsByProductIDParams{productID}

	// Record call args
	mmGetWithCountsByProductID.GetWithCountsByProductIDMock.mutex.Lock()
	mmGetWithCountsByProductID.GetWithCountsByProductIDMock.callArgs = append(mmGetWithCountsByProductID.GetWithCountsByProductIDMock.callArgs, &mm_params)
	mmGetWithCountsByProductID.GetWithCountsByProductIDMock.mutex.Unlock()

	for _, e := range mmGetWithCountsByProductID.GetWithCountsByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.aa1, e.results.err
		}
	}

	if mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetWithCountsByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetWithCountsByProductID.t.Errorf("GroupDomainServiceMock.GetWithCountsByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetWithCountsByProductID.t.Errorf("GroupDomainServiceMock.GetWithCountsByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetWithCountsByProductID.GetWithCountsByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetWithCountsByProductID.t.Fatal("No results are set for the GroupDomainServiceMock.GetWithCountsByProductID")
		}
		return (*mm_results).aa1, (*mm_results).err
	}
	if mmGetWithCountsByProductID.funcGetWithCountsByProductID != nil {
		return mmGetWithCountsByProductID.funcGetWithCountsByProductID(productID)
	}
	mmGetWithCountsByProductID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetWithCountsByProductID. %v", productID)
	return
}

// GetWithCountsByProductIDAfterCounter returns a count of finished GroupDomainServiceMock.GetWithCountsByProductID invocations
func (mmGetWithCountsByProductID *GroupDomainServiceMock) GetWithCountsByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithCountsByProductID.afterGetWithCountsByProductIDCounter)
}

// GetWithCountsByProductIDBeforeCounter returns a count of GroupDomainServiceMock.GetWithCountsByProductID invocations
func (mmGetWithCountsByProductID *GroupDomainServiceMock) GetWithCountsByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithCountsByProductID.beforeGetWithCountsByProductIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetWithCountsByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetWithCountsByProductID *mGroupDomainServiceMockGetWithCountsByProductID) Calls() []*GroupDomainServiceMockGetWithCountsByProductIDParams {
	mmGetWithCountsByProductID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetWithCountsByProductIDParams, len(mmGetWithCountsByProductID.callArgs))
	copy(argCopy, mmGetWithCountsByProductID.callArgs)

	mmGetWithCountsByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetWithCountsByProductIDDone returns true if the count of the GetWithCountsByProductID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetWithCountsByProductIDDone() bool {
	if m.GetWithCountsByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetWithCountsByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetWithCountsByProductIDMock.invocationsDone()
}

// MinimockGetWithCountsByProductIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetWithCountsByProductIDInspect() {
	for _, e := range m.GetWithCountsByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithCountsByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetWithCountsByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetWithCountsByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetWithCountsByProductIDMock.defaultExpectation != nil && afterGetWithCountsByProductIDCounter < 1 {
		if m.GetWithCountsByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithCountsByProductID at\n%s", m.GetWithCountsByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithCountsByProductID at\n%s with params: %#v", m.GetWithCountsByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetWithCountsByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetWithCountsByProductID != nil && afterGetWithCountsByProductIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithCountsByProductID at\n%s", m.funcGetWithCountsByProductIDOrigin)
	}

	if !m.GetWithCountsByProductIDMock.invocationsDone() && afterGetWithCountsByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetWithCountsByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetWithCountsByProductIDMock.expectedInvocations), m.GetWithCountsByProductIDMock.expectedInvocationsOrigin, afterGetWithCountsByProductIDCounter)
	}
}

type mGroupDomainServiceMockGetWithProductByUserID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetWithProductByUserIDExpectation
	expectations       []*GroupDomainServiceMockGetWithProductByUserIDExpectation

	callArgs []*GroupDomainServiceMockGetWithProductByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetWithProductByUserIDExpectation specifies expectation struct of the GroupDomainService.GetWithProductByUserID
type GroupDomainServiceMockGetWithProductByUserIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetWithProductByUserIDParams
	paramPtrs          *GroupDomainServiceMockGetWithProductByUserIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetWithProductByUserIDExpectationOrigins
	results            *GroupDomainServiceMockGetWithProductByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetWithProductByUserIDParams contains parameters of the GroupDomainService.GetWithProductByUserID
type GroupDomainServiceMockGetWithProductByUserIDParams struct {
	userID int64
}

// GroupDomainServiceMockGetWithProductByUserIDParamPtrs contains pointers to parameters of the GroupDomainService.GetWithProductByUserID
type GroupDomainServiceMockGetWithProductByUserIDParamPtrs struct {
	userID *int64
}

// GroupDomainServiceMockGetWithProductByUserIDResults contains results of the GroupDomainService.GetWithProductByUserID
type GroupDomainServiceMockGetWithProductByUserIDResults struct {
	ga1 []groupentity.GroupWithProduct
	err error
}

// GroupDomainServiceMockGetWithProductByUserIDOrigins contains origins of expectations of the GroupDomainService.GetWithProductByUserID
type GroupDomainServiceMockGetWithProductByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Optional() *mGroupDomainServiceMockGetWithProductByUserID {
	mmGetWithProductByUserID.optional = true
	return mmGetWithProductByUserID
}

// Expect sets up expected params for GroupDomainService.GetWithProductByUserID
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Expect(userID int64) *mGroupDomainServiceMockGetWithProductByUserID {
	if mmGetWithProductByUserID.mock.funcGetWithProductByUserID != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductByUserID mock is already set by Set")
	}

	if mmGetWithProductByUserID.defaultExpectation == nil {
		mmGetWithProductByUserID.defaultExpectation = &GroupDomainServiceMockGetWithProductByUserIDExpectation{}
	}

	if mmGetWithProductByUserID.defaultExpectation.paramPtrs != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductByUserID mock is already set by ExpectParams functions")
	}

	mmGetWithProductByUserID.defaultExpectation.params = &GroupDomainServiceMockGetWithProductByUserIDParams{userID}
	mmGetWithProductByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetWithProductByUserID.expectations {
		if minimock.Equal(e.params, mmGetWithProductByUserID.defaultExpectation.params) {
			mmGetWithProductByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetWithProductByUserID.defaultExpectation.params)
		}
	}

	return mmGetWithProductByUserID
}

// ExpectUserIDParam1 sets up expected param userID for GroupDomainService.GetWithProductByUserID
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) ExpectUserIDParam1(userID int64) *mGroupDomainServiceMockGetWithProductByUserID {
	if mmGetWithProductByUserID.mock.funcGetWithProductByUserID != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductByUserID mock is already set by Set")
	}

	if mmGetWithProductByUserID.defaultExpectation == nil {
		mmGetWithProductByUserID.defaultExpectation = &GroupDomainServiceMockGetWithProductByUserIDExpectation{}
	}

	if mmGetWithProductByUserID.defaultExpectation.params != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductByUserID mock is already set by Expect")
	}

	if mmGetWithProductByUserID.defaultExpectation.paramPtrs == nil {
		mmGetWithProductByUserID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetWithProductByUserIDParamPtrs{}
	}
	mmGetWithProductByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetWithProductByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetWithProductByUserID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetWithProductByUserID
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Inspect(f func(userID int64)) *mGroupDomainServiceMockGetWithProductByUserID {
	if mmGetWithProductByUserID.mock.inspectFuncGetWithProductByUserID != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetWithProductByUserID")
	}

	mmGetWithProductByUserID.mock.inspectFuncGetWithProductByUserID = f

	return mmGetWithProductByUserID
}

// Return sets up results that will be returned by GroupDomainService.GetWithProductByUserID
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Return(ga1 []groupentity.GroupWithProduct, err error) *GroupDomainServiceMock {
	if mmGetWithProductByUserID.mock.funcGetWithProductByUserID != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductByUserID mock is already set by Set")
	}

	if mmGetWithProductByUserID.defaultExpectation == nil {
		mmGetWithProductByUserID.defaultExpectation = &GroupDomainServiceMockGetWithProductByUserIDExpectation{mock: mmGetWithProductByUserID.mock}
	}
	mmGetWithProductByUserID.defaultExpectation.results = &GroupDomainServiceMockGetWithProductByUserIDResults{ga1, err}
	mmGetWithProductByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetWithProductByUserID.mock
}

// Set uses given function f to mock the GroupDomainService.GetWithProductByUserID method
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Set(f func(userID int64) (ga1 []groupentity.GroupWithProduct, err error)) *GroupDomainServiceMock {
	if mmGetWithProductByUserID.defaultExpectation != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetWithProductByUserID method")
	}

	if len(mmGetWithProductByUserID.expectations) > 0 {
		mmGetWithProductByUserID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetWithProductByUserID method")
	}

	mmGetWithProductByUserID.mock.funcGetWithProductByUserID = f
	mmGetWithProductByUserID.mock.funcGetWithProductByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetWithProductByUserID.mock
}

// When sets expectation for the GroupDomainService.GetWithProductByUserID which will trigger the result defined by the following
// Then helper
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) When(userID int64) *GroupDomainServiceMockGetWithProductByUserIDExpectation {
	if mmGetWithProductByUserID.mock.funcGetWithProductByUserID != nil {
		mmGetWithProductByUserID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductByUserID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetWithProductByUserIDExpectation{
		mock:               mmGetWithProductByUserID.mock,
		params:             &GroupDomainServiceMockGetWithProductByUserIDParams{userID},
		expectationOrigins: GroupDomainServiceMockGetWithProductByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetWithProductByUserID.expectations = append(mmGetWithProductByUserID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetWithProductByUserID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetWithProductByUserIDExpectation) Then(ga1 []groupentity.GroupWithProduct, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetWithProductByUserIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetWithProductByUserID should be invoked
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Times(n uint64) *mGroupDomainServiceMockGetWithProductByUserID {
	if n == 0 {
		mmGetWithProductByUserID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetWithProductByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetWithProductByUserID.expectedInvocations, n)
	mmGetWithProductByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetWithProductByUserID
}

func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) invocationsDone() bool {
	if len(mmGetWithProductByUserID.expectations) == 0 && mmGetWithProductByUserID.defaultExpectation == nil && mmGetWithProductByUserID.mock.funcGetWithProductByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetWithProductByUserID.mock.afterGetWithProductByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetWithProductByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetWithProductByUserID implements mm_service.GroupDomainService
func (mmGetWithProductByUserID *GroupDomainServiceMock) GetWithProductByUserID(userID int64) (ga1 []groupentity.GroupWithProduct, err error) {
	mm_atomic.AddUint64(&mmGetWithProductByUserID.beforeGetWithProductByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetWithProductByUserID.afterGetWithProductByUserIDCounter, 1)

	mmGetWithProductByUserID.t.Helper()

	if mmGetWithProductByUserID.inspectFuncGetWithProductByUserID != nil {
		mmGetWithProductByUserID.inspectFuncGetWithProductByUserID(userID)
	}

	mm_params := GroupDomainServiceMockGetWithProductByUserIDParams{userID}

	// Record call args
	mmGetWithProductByUserID.GetWithProductByUserIDMock.mutex.Lock()
	mmGetWithProductByUserID.GetWithProductByUserIDMock.callArgs = append(mmGetWithProductByUserID.GetWithProductByUserIDMock.callArgs, &mm_params)
	mmGetWithProductByUserID.GetWithProductByUserIDMock.mutex.Unlock()

	for _, e := range mmGetWithProductByUserID.GetWithProductByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetWithProductByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetWithProductByUserID.t.Errorf("GroupDomainServiceMock.GetWithProductByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetWithProductByUserID.t.Errorf("GroupDomainServiceMock.GetWithProductByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetWithProductByUserID.GetWithProductByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetWithProductByUserID.t.Fatal("No results are set for the GroupDomainServiceMock.GetWithProductByUserID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetWithProductByUserID.funcGetWithProductByUserID != nil {
		return mmGetWithProductByUserID.funcGetWithProductByUserID(userID)
	}
	mmGetWithProductByUserID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetWithProductByUserID. %v", userID)
	return
}

// GetWithProductByUserIDAfterCounter returns a count of finished GroupDomainServiceMock.GetWithProductByUserID invocations
func (mmGetWithProductByUserID *GroupDomainServiceMock) GetWithProductByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithProductByUserID.afterGetWithProductByUserIDCounter)
}

// GetWithProductByUserIDBeforeCounter returns a count of GroupDomainServiceMock.GetWithProductByUserID invocations
func (mmGetWithProductByUserID *GroupDomainServiceMock) GetWithProductByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithProductByUserID.beforeGetWithProductByUserIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetWithProductByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetWithProductByUserID *mGroupDomainServiceMockGetWithProductByUserID) Calls() []*GroupDomainServiceMockGetWithProductByUserIDParams {
	mmGetWithProductByUserID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetWithProductByUserIDParams, len(mmGetWithProductByUserID.callArgs))
	copy(argCopy, mmGetWithProductByUserID.callArgs)

	mmGetWithProductByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetWithProductByUserIDDone returns true if the count of the GetWithProductByUserID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetWithProductByUserIDDone() bool {
	if m.GetWithProductByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetWithProductByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetWithProductByUserIDMock.invocationsDone()
}

// MinimockGetWithProductByUserIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetWithProductByUserIDInspect() {
	for _, e := range m.GetWithProductByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetWithProductByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetWithProductByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetWithProductByUserIDMock.defaultExpectation != nil && afterGetWithProductByUserIDCounter < 1 {
		if m.GetWithProductByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductByUserID at\n%s", m.GetWithProductByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductByUserID at\n%s with params: %#v", m.GetWithProductByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetWithProductByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetWithProductByUserID != nil && afterGetWithProductByUserIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductByUserID at\n%s", m.funcGetWithProductByUserIDOrigin)
	}

	if !m.GetWithProductByUserIDMock.invocationsDone() && afterGetWithProductByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetWithProductByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetWithProductByUserIDMock.expectedInvocations), m.GetWithProductByUserIDMock.expectedInvocationsOrigin, afterGetWithProductByUserIDCounter)
	}
}

type mGroupDomainServiceMockGetWithProductsByRoleID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetWithProductsByRoleIDExpectation
	expectations       []*GroupDomainServiceMockGetWithProductsByRoleIDExpectation

	callArgs []*GroupDomainServiceMockGetWithProductsByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetWithProductsByRoleIDExpectation specifies expectation struct of the GroupDomainService.GetWithProductsByRoleID
type GroupDomainServiceMockGetWithProductsByRoleIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetWithProductsByRoleIDParams
	paramPtrs          *GroupDomainServiceMockGetWithProductsByRoleIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetWithProductsByRoleIDExpectationOrigins
	results            *GroupDomainServiceMockGetWithProductsByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetWithProductsByRoleIDParams contains parameters of the GroupDomainService.GetWithProductsByRoleID
type GroupDomainServiceMockGetWithProductsByRoleIDParams struct {
	roleID int64
}

// GroupDomainServiceMockGetWithProductsByRoleIDParamPtrs contains pointers to parameters of the GroupDomainService.GetWithProductsByRoleID
type GroupDomainServiceMockGetWithProductsByRoleIDParamPtrs struct {
	roleID *int64
}

// GroupDomainServiceMockGetWithProductsByRoleIDResults contains results of the GroupDomainService.GetWithProductsByRoleID
type GroupDomainServiceMockGetWithProductsByRoleIDResults struct {
	ga1 []groupentity.GroupWithProduct
	err error
}

// GroupDomainServiceMockGetWithProductsByRoleIDOrigins contains origins of expectations of the GroupDomainService.GetWithProductsByRoleID
type GroupDomainServiceMockGetWithProductsByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Optional() *mGroupDomainServiceMockGetWithProductsByRoleID {
	mmGetWithProductsByRoleID.optional = true
	return mmGetWithProductsByRoleID
}

// Expect sets up expected params for GroupDomainService.GetWithProductsByRoleID
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Expect(roleID int64) *mGroupDomainServiceMockGetWithProductsByRoleID {
	if mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleID != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductsByRoleID mock is already set by Set")
	}

	if mmGetWithProductsByRoleID.defaultExpectation == nil {
		mmGetWithProductsByRoleID.defaultExpectation = &GroupDomainServiceMockGetWithProductsByRoleIDExpectation{}
	}

	if mmGetWithProductsByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductsByRoleID mock is already set by ExpectParams functions")
	}

	mmGetWithProductsByRoleID.defaultExpectation.params = &GroupDomainServiceMockGetWithProductsByRoleIDParams{roleID}
	mmGetWithProductsByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetWithProductsByRoleID.expectations {
		if minimock.Equal(e.params, mmGetWithProductsByRoleID.defaultExpectation.params) {
			mmGetWithProductsByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetWithProductsByRoleID.defaultExpectation.params)
		}
	}

	return mmGetWithProductsByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for GroupDomainService.GetWithProductsByRoleID
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) ExpectRoleIDParam1(roleID int64) *mGroupDomainServiceMockGetWithProductsByRoleID {
	if mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleID != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductsByRoleID mock is already set by Set")
	}

	if mmGetWithProductsByRoleID.defaultExpectation == nil {
		mmGetWithProductsByRoleID.defaultExpectation = &GroupDomainServiceMockGetWithProductsByRoleIDExpectation{}
	}

	if mmGetWithProductsByRoleID.defaultExpectation.params != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductsByRoleID mock is already set by Expect")
	}

	if mmGetWithProductsByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetWithProductsByRoleID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetWithProductsByRoleIDParamPtrs{}
	}
	mmGetWithProductsByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetWithProductsByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetWithProductsByRoleID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetWithProductsByRoleID
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Inspect(f func(roleID int64)) *mGroupDomainServiceMockGetWithProductsByRoleID {
	if mmGetWithProductsByRoleID.mock.inspectFuncGetWithProductsByRoleID != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetWithProductsByRoleID")
	}

	mmGetWithProductsByRoleID.mock.inspectFuncGetWithProductsByRoleID = f

	return mmGetWithProductsByRoleID
}

// Return sets up results that will be returned by GroupDomainService.GetWithProductsByRoleID
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Return(ga1 []groupentity.GroupWithProduct, err error) *GroupDomainServiceMock {
	if mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleID != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductsByRoleID mock is already set by Set")
	}

	if mmGetWithProductsByRoleID.defaultExpectation == nil {
		mmGetWithProductsByRoleID.defaultExpectation = &GroupDomainServiceMockGetWithProductsByRoleIDExpectation{mock: mmGetWithProductsByRoleID.mock}
	}
	mmGetWithProductsByRoleID.defaultExpectation.results = &GroupDomainServiceMockGetWithProductsByRoleIDResults{ga1, err}
	mmGetWithProductsByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetWithProductsByRoleID.mock
}

// Set uses given function f to mock the GroupDomainService.GetWithProductsByRoleID method
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Set(f func(roleID int64) (ga1 []groupentity.GroupWithProduct, err error)) *GroupDomainServiceMock {
	if mmGetWithProductsByRoleID.defaultExpectation != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetWithProductsByRoleID method")
	}

	if len(mmGetWithProductsByRoleID.expectations) > 0 {
		mmGetWithProductsByRoleID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetWithProductsByRoleID method")
	}

	mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleID = f
	mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetWithProductsByRoleID.mock
}

// When sets expectation for the GroupDomainService.GetWithProductsByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) When(roleID int64) *GroupDomainServiceMockGetWithProductsByRoleIDExpectation {
	if mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleID != nil {
		mmGetWithProductsByRoleID.mock.t.Fatalf("GroupDomainServiceMock.GetWithProductsByRoleID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetWithProductsByRoleIDExpectation{
		mock:               mmGetWithProductsByRoleID.mock,
		params:             &GroupDomainServiceMockGetWithProductsByRoleIDParams{roleID},
		expectationOrigins: GroupDomainServiceMockGetWithProductsByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetWithProductsByRoleID.expectations = append(mmGetWithProductsByRoleID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetWithProductsByRoleID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetWithProductsByRoleIDExpectation) Then(ga1 []groupentity.GroupWithProduct, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetWithProductsByRoleIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetWithProductsByRoleID should be invoked
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Times(n uint64) *mGroupDomainServiceMockGetWithProductsByRoleID {
	if n == 0 {
		mmGetWithProductsByRoleID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetWithProductsByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetWithProductsByRoleID.expectedInvocations, n)
	mmGetWithProductsByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetWithProductsByRoleID
}

func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) invocationsDone() bool {
	if len(mmGetWithProductsByRoleID.expectations) == 0 && mmGetWithProductsByRoleID.defaultExpectation == nil && mmGetWithProductsByRoleID.mock.funcGetWithProductsByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetWithProductsByRoleID.mock.afterGetWithProductsByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetWithProductsByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetWithProductsByRoleID implements mm_service.GroupDomainService
func (mmGetWithProductsByRoleID *GroupDomainServiceMock) GetWithProductsByRoleID(roleID int64) (ga1 []groupentity.GroupWithProduct, err error) {
	mm_atomic.AddUint64(&mmGetWithProductsByRoleID.beforeGetWithProductsByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetWithProductsByRoleID.afterGetWithProductsByRoleIDCounter, 1)

	mmGetWithProductsByRoleID.t.Helper()

	if mmGetWithProductsByRoleID.inspectFuncGetWithProductsByRoleID != nil {
		mmGetWithProductsByRoleID.inspectFuncGetWithProductsByRoleID(roleID)
	}

	mm_params := GroupDomainServiceMockGetWithProductsByRoleIDParams{roleID}

	// Record call args
	mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.mutex.Lock()
	mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.callArgs = append(mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.callArgs, &mm_params)
	mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetWithProductsByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetWithProductsByRoleID.t.Errorf("GroupDomainServiceMock.GetWithProductsByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetWithProductsByRoleID.t.Errorf("GroupDomainServiceMock.GetWithProductsByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetWithProductsByRoleID.GetWithProductsByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetWithProductsByRoleID.t.Fatal("No results are set for the GroupDomainServiceMock.GetWithProductsByRoleID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetWithProductsByRoleID.funcGetWithProductsByRoleID != nil {
		return mmGetWithProductsByRoleID.funcGetWithProductsByRoleID(roleID)
	}
	mmGetWithProductsByRoleID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetWithProductsByRoleID. %v", roleID)
	return
}

// GetWithProductsByRoleIDAfterCounter returns a count of finished GroupDomainServiceMock.GetWithProductsByRoleID invocations
func (mmGetWithProductsByRoleID *GroupDomainServiceMock) GetWithProductsByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithProductsByRoleID.afterGetWithProductsByRoleIDCounter)
}

// GetWithProductsByRoleIDBeforeCounter returns a count of GroupDomainServiceMock.GetWithProductsByRoleID invocations
func (mmGetWithProductsByRoleID *GroupDomainServiceMock) GetWithProductsByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithProductsByRoleID.beforeGetWithProductsByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetWithProductsByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetWithProductsByRoleID *mGroupDomainServiceMockGetWithProductsByRoleID) Calls() []*GroupDomainServiceMockGetWithProductsByRoleIDParams {
	mmGetWithProductsByRoleID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetWithProductsByRoleIDParams, len(mmGetWithProductsByRoleID.callArgs))
	copy(argCopy, mmGetWithProductsByRoleID.callArgs)

	mmGetWithProductsByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetWithProductsByRoleIDDone returns true if the count of the GetWithProductsByRoleID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetWithProductsByRoleIDDone() bool {
	if m.GetWithProductsByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetWithProductsByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetWithProductsByRoleIDMock.invocationsDone()
}

// MinimockGetWithProductsByRoleIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetWithProductsByRoleIDInspect() {
	for _, e := range m.GetWithProductsByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductsByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetWithProductsByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetWithProductsByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetWithProductsByRoleIDMock.defaultExpectation != nil && afterGetWithProductsByRoleIDCounter < 1 {
		if m.GetWithProductsByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductsByRoleID at\n%s", m.GetWithProductsByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductsByRoleID at\n%s with params: %#v", m.GetWithProductsByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetWithProductsByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetWithProductsByRoleID != nil && afterGetWithProductsByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithProductsByRoleID at\n%s", m.funcGetWithProductsByRoleIDOrigin)
	}

	if !m.GetWithProductsByRoleIDMock.invocationsDone() && afterGetWithProductsByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetWithProductsByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetWithProductsByRoleIDMock.expectedInvocations), m.GetWithProductsByRoleIDMock.expectedInvocationsOrigin, afterGetWithProductsByRoleIDCounter)
	}
}

type mGroupDomainServiceMockGetWithStatsByProductID struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockGetWithStatsByProductIDExpectation
	expectations       []*GroupDomainServiceMockGetWithStatsByProductIDExpectation

	callArgs []*GroupDomainServiceMockGetWithStatsByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockGetWithStatsByProductIDExpectation specifies expectation struct of the GroupDomainService.GetWithStatsByProductID
type GroupDomainServiceMockGetWithStatsByProductIDExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockGetWithStatsByProductIDParams
	paramPtrs          *GroupDomainServiceMockGetWithStatsByProductIDParamPtrs
	expectationOrigins GroupDomainServiceMockGetWithStatsByProductIDExpectationOrigins
	results            *GroupDomainServiceMockGetWithStatsByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockGetWithStatsByProductIDParams contains parameters of the GroupDomainService.GetWithStatsByProductID
type GroupDomainServiceMockGetWithStatsByProductIDParams struct {
	productID int64
}

// GroupDomainServiceMockGetWithStatsByProductIDParamPtrs contains pointers to parameters of the GroupDomainService.GetWithStatsByProductID
type GroupDomainServiceMockGetWithStatsByProductIDParamPtrs struct {
	productID *int64
}

// GroupDomainServiceMockGetWithStatsByProductIDResults contains results of the GroupDomainService.GetWithStatsByProductID
type GroupDomainServiceMockGetWithStatsByProductIDResults struct {
	ga1 []groupentity.GroupWithStats
	err error
}

// GroupDomainServiceMockGetWithStatsByProductIDOrigins contains origins of expectations of the GroupDomainService.GetWithStatsByProductID
type GroupDomainServiceMockGetWithStatsByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Optional() *mGroupDomainServiceMockGetWithStatsByProductID {
	mmGetWithStatsByProductID.optional = true
	return mmGetWithStatsByProductID
}

// Expect sets up expected params for GroupDomainService.GetWithStatsByProductID
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Expect(productID int64) *mGroupDomainServiceMockGetWithStatsByProductID {
	if mmGetWithStatsByProductID.mock.funcGetWithStatsByProductID != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithStatsByProductID mock is already set by Set")
	}

	if mmGetWithStatsByProductID.defaultExpectation == nil {
		mmGetWithStatsByProductID.defaultExpectation = &GroupDomainServiceMockGetWithStatsByProductIDExpectation{}
	}

	if mmGetWithStatsByProductID.defaultExpectation.paramPtrs != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithStatsByProductID mock is already set by ExpectParams functions")
	}

	mmGetWithStatsByProductID.defaultExpectation.params = &GroupDomainServiceMockGetWithStatsByProductIDParams{productID}
	mmGetWithStatsByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetWithStatsByProductID.expectations {
		if minimock.Equal(e.params, mmGetWithStatsByProductID.defaultExpectation.params) {
			mmGetWithStatsByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetWithStatsByProductID.defaultExpectation.params)
		}
	}

	return mmGetWithStatsByProductID
}

// ExpectProductIDParam1 sets up expected param productID for GroupDomainService.GetWithStatsByProductID
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) ExpectProductIDParam1(productID int64) *mGroupDomainServiceMockGetWithStatsByProductID {
	if mmGetWithStatsByProductID.mock.funcGetWithStatsByProductID != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithStatsByProductID mock is already set by Set")
	}

	if mmGetWithStatsByProductID.defaultExpectation == nil {
		mmGetWithStatsByProductID.defaultExpectation = &GroupDomainServiceMockGetWithStatsByProductIDExpectation{}
	}

	if mmGetWithStatsByProductID.defaultExpectation.params != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithStatsByProductID mock is already set by Expect")
	}

	if mmGetWithStatsByProductID.defaultExpectation.paramPtrs == nil {
		mmGetWithStatsByProductID.defaultExpectation.paramPtrs = &GroupDomainServiceMockGetWithStatsByProductIDParamPtrs{}
	}
	mmGetWithStatsByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetWithStatsByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetWithStatsByProductID
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.GetWithStatsByProductID
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Inspect(f func(productID int64)) *mGroupDomainServiceMockGetWithStatsByProductID {
	if mmGetWithStatsByProductID.mock.inspectFuncGetWithStatsByProductID != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.GetWithStatsByProductID")
	}

	mmGetWithStatsByProductID.mock.inspectFuncGetWithStatsByProductID = f

	return mmGetWithStatsByProductID
}

// Return sets up results that will be returned by GroupDomainService.GetWithStatsByProductID
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Return(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	if mmGetWithStatsByProductID.mock.funcGetWithStatsByProductID != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithStatsByProductID mock is already set by Set")
	}

	if mmGetWithStatsByProductID.defaultExpectation == nil {
		mmGetWithStatsByProductID.defaultExpectation = &GroupDomainServiceMockGetWithStatsByProductIDExpectation{mock: mmGetWithStatsByProductID.mock}
	}
	mmGetWithStatsByProductID.defaultExpectation.results = &GroupDomainServiceMockGetWithStatsByProductIDResults{ga1, err}
	mmGetWithStatsByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetWithStatsByProductID.mock
}

// Set uses given function f to mock the GroupDomainService.GetWithStatsByProductID method
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Set(f func(productID int64) (ga1 []groupentity.GroupWithStats, err error)) *GroupDomainServiceMock {
	if mmGetWithStatsByProductID.defaultExpectation != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.GetWithStatsByProductID method")
	}

	if len(mmGetWithStatsByProductID.expectations) > 0 {
		mmGetWithStatsByProductID.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.GetWithStatsByProductID method")
	}

	mmGetWithStatsByProductID.mock.funcGetWithStatsByProductID = f
	mmGetWithStatsByProductID.mock.funcGetWithStatsByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetWithStatsByProductID.mock
}

// When sets expectation for the GroupDomainService.GetWithStatsByProductID which will trigger the result defined by the following
// Then helper
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) When(productID int64) *GroupDomainServiceMockGetWithStatsByProductIDExpectation {
	if mmGetWithStatsByProductID.mock.funcGetWithStatsByProductID != nil {
		mmGetWithStatsByProductID.mock.t.Fatalf("GroupDomainServiceMock.GetWithStatsByProductID mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockGetWithStatsByProductIDExpectation{
		mock:               mmGetWithStatsByProductID.mock,
		params:             &GroupDomainServiceMockGetWithStatsByProductIDParams{productID},
		expectationOrigins: GroupDomainServiceMockGetWithStatsByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetWithStatsByProductID.expectations = append(mmGetWithStatsByProductID.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.GetWithStatsByProductID return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockGetWithStatsByProductIDExpectation) Then(ga1 []groupentity.GroupWithStats, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockGetWithStatsByProductIDResults{ga1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.GetWithStatsByProductID should be invoked
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Times(n uint64) *mGroupDomainServiceMockGetWithStatsByProductID {
	if n == 0 {
		mmGetWithStatsByProductID.mock.t.Fatalf("Times of GroupDomainServiceMock.GetWithStatsByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetWithStatsByProductID.expectedInvocations, n)
	mmGetWithStatsByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetWithStatsByProductID
}

func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) invocationsDone() bool {
	if len(mmGetWithStatsByProductID.expectations) == 0 && mmGetWithStatsByProductID.defaultExpectation == nil && mmGetWithStatsByProductID.mock.funcGetWithStatsByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetWithStatsByProductID.mock.afterGetWithStatsByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetWithStatsByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetWithStatsByProductID implements mm_service.GroupDomainService
func (mmGetWithStatsByProductID *GroupDomainServiceMock) GetWithStatsByProductID(productID int64) (ga1 []groupentity.GroupWithStats, err error) {
	mm_atomic.AddUint64(&mmGetWithStatsByProductID.beforeGetWithStatsByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetWithStatsByProductID.afterGetWithStatsByProductIDCounter, 1)

	mmGetWithStatsByProductID.t.Helper()

	if mmGetWithStatsByProductID.inspectFuncGetWithStatsByProductID != nil {
		mmGetWithStatsByProductID.inspectFuncGetWithStatsByProductID(productID)
	}

	mm_params := GroupDomainServiceMockGetWithStatsByProductIDParams{productID}

	// Record call args
	mmGetWithStatsByProductID.GetWithStatsByProductIDMock.mutex.Lock()
	mmGetWithStatsByProductID.GetWithStatsByProductIDMock.callArgs = append(mmGetWithStatsByProductID.GetWithStatsByProductIDMock.callArgs, &mm_params)
	mmGetWithStatsByProductID.GetWithStatsByProductIDMock.mutex.Unlock()

	for _, e := range mmGetWithStatsByProductID.GetWithStatsByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockGetWithStatsByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetWithStatsByProductID.t.Errorf("GroupDomainServiceMock.GetWithStatsByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetWithStatsByProductID.t.Errorf("GroupDomainServiceMock.GetWithStatsByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetWithStatsByProductID.GetWithStatsByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetWithStatsByProductID.t.Fatal("No results are set for the GroupDomainServiceMock.GetWithStatsByProductID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetWithStatsByProductID.funcGetWithStatsByProductID != nil {
		return mmGetWithStatsByProductID.funcGetWithStatsByProductID(productID)
	}
	mmGetWithStatsByProductID.t.Fatalf("Unexpected call to GroupDomainServiceMock.GetWithStatsByProductID. %v", productID)
	return
}

// GetWithStatsByProductIDAfterCounter returns a count of finished GroupDomainServiceMock.GetWithStatsByProductID invocations
func (mmGetWithStatsByProductID *GroupDomainServiceMock) GetWithStatsByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithStatsByProductID.afterGetWithStatsByProductIDCounter)
}

// GetWithStatsByProductIDBeforeCounter returns a count of GroupDomainServiceMock.GetWithStatsByProductID invocations
func (mmGetWithStatsByProductID *GroupDomainServiceMock) GetWithStatsByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetWithStatsByProductID.beforeGetWithStatsByProductIDCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.GetWithStatsByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetWithStatsByProductID *mGroupDomainServiceMockGetWithStatsByProductID) Calls() []*GroupDomainServiceMockGetWithStatsByProductIDParams {
	mmGetWithStatsByProductID.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockGetWithStatsByProductIDParams, len(mmGetWithStatsByProductID.callArgs))
	copy(argCopy, mmGetWithStatsByProductID.callArgs)

	mmGetWithStatsByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetWithStatsByProductIDDone returns true if the count of the GetWithStatsByProductID invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockGetWithStatsByProductIDDone() bool {
	if m.GetWithStatsByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetWithStatsByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetWithStatsByProductIDMock.invocationsDone()
}

// MinimockGetWithStatsByProductIDInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockGetWithStatsByProductIDInspect() {
	for _, e := range m.GetWithStatsByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithStatsByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetWithStatsByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetWithStatsByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetWithStatsByProductIDMock.defaultExpectation != nil && afterGetWithStatsByProductIDCounter < 1 {
		if m.GetWithStatsByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithStatsByProductID at\n%s", m.GetWithStatsByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithStatsByProductID at\n%s with params: %#v", m.GetWithStatsByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetWithStatsByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetWithStatsByProductID != nil && afterGetWithStatsByProductIDCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.GetWithStatsByProductID at\n%s", m.funcGetWithStatsByProductIDOrigin)
	}

	if !m.GetWithStatsByProductIDMock.invocationsDone() && afterGetWithStatsByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.GetWithStatsByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetWithStatsByProductIDMock.expectedInvocations), m.GetWithStatsByProductIDMock.expectedInvocationsOrigin, afterGetWithStatsByProductIDCounter)
	}
}

type mGroupDomainServiceMockUnassignFromRoles struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUnassignFromRolesExpectation
	expectations       []*GroupDomainServiceMockUnassignFromRolesExpectation

	callArgs []*GroupDomainServiceMockUnassignFromRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUnassignFromRolesExpectation specifies expectation struct of the GroupDomainService.UnassignFromRoles
type GroupDomainServiceMockUnassignFromRolesExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUnassignFromRolesParams
	paramPtrs          *GroupDomainServiceMockUnassignFromRolesParamPtrs
	expectationOrigins GroupDomainServiceMockUnassignFromRolesExpectationOrigins
	results            *GroupDomainServiceMockUnassignFromRolesResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUnassignFromRolesParams contains parameters of the GroupDomainService.UnassignFromRoles
type GroupDomainServiceMockUnassignFromRolesParams struct {
	ctx     context.Context
	groupID int64
	roleIDs []int64
}

// GroupDomainServiceMockUnassignFromRolesParamPtrs contains pointers to parameters of the GroupDomainService.UnassignFromRoles
type GroupDomainServiceMockUnassignFromRolesParamPtrs struct {
	ctx     *context.Context
	groupID *int64
	roleIDs *[]int64
}

// GroupDomainServiceMockUnassignFromRolesResults contains results of the GroupDomainService.UnassignFromRoles
type GroupDomainServiceMockUnassignFromRolesResults struct {
	err error
}

// GroupDomainServiceMockUnassignFromRolesOrigins contains origins of expectations of the GroupDomainService.UnassignFromRoles
type GroupDomainServiceMockUnassignFromRolesExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Optional() *mGroupDomainServiceMockUnassignFromRoles {
	mmUnassignFromRoles.optional = true
	return mmUnassignFromRoles
}

// Expect sets up expected params for GroupDomainService.UnassignFromRoles
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Expect(ctx context.Context, groupID int64, roleIDs []int64) *mGroupDomainServiceMockUnassignFromRoles {
	if mmUnassignFromRoles.mock.funcUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Set")
	}

	if mmUnassignFromRoles.defaultExpectation == nil {
		mmUnassignFromRoles.defaultExpectation = &GroupDomainServiceMockUnassignFromRolesExpectation{}
	}

	if mmUnassignFromRoles.defaultExpectation.paramPtrs != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by ExpectParams functions")
	}

	mmUnassignFromRoles.defaultExpectation.params = &GroupDomainServiceMockUnassignFromRolesParams{ctx, groupID, roleIDs}
	mmUnassignFromRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUnassignFromRoles.expectations {
		if minimock.Equal(e.params, mmUnassignFromRoles.defaultExpectation.params) {
			mmUnassignFromRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUnassignFromRoles.defaultExpectation.params)
		}
	}

	return mmUnassignFromRoles
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.UnassignFromRoles
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockUnassignFromRoles {
	if mmUnassignFromRoles.mock.funcUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Set")
	}

	if mmUnassignFromRoles.defaultExpectation == nil {
		mmUnassignFromRoles.defaultExpectation = &GroupDomainServiceMockUnassignFromRolesExpectation{}
	}

	if mmUnassignFromRoles.defaultExpectation.params != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Expect")
	}

	if mmUnassignFromRoles.defaultExpectation.paramPtrs == nil {
		mmUnassignFromRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockUnassignFromRolesParamPtrs{}
	}
	mmUnassignFromRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmUnassignFromRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUnassignFromRoles
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupDomainService.UnassignFromRoles
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) ExpectGroupIDParam2(groupID int64) *mGroupDomainServiceMockUnassignFromRoles {
	if mmUnassignFromRoles.mock.funcUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Set")
	}

	if mmUnassignFromRoles.defaultExpectation == nil {
		mmUnassignFromRoles.defaultExpectation = &GroupDomainServiceMockUnassignFromRolesExpectation{}
	}

	if mmUnassignFromRoles.defaultExpectation.params != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Expect")
	}

	if mmUnassignFromRoles.defaultExpectation.paramPtrs == nil {
		mmUnassignFromRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockUnassignFromRolesParamPtrs{}
	}
	mmUnassignFromRoles.defaultExpectation.paramPtrs.groupID = &groupID
	mmUnassignFromRoles.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmUnassignFromRoles
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for GroupDomainService.UnassignFromRoles
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) ExpectRoleIDsParam3(roleIDs []int64) *mGroupDomainServiceMockUnassignFromRoles {
	if mmUnassignFromRoles.mock.funcUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Set")
	}

	if mmUnassignFromRoles.defaultExpectation == nil {
		mmUnassignFromRoles.defaultExpectation = &GroupDomainServiceMockUnassignFromRolesExpectation{}
	}

	if mmUnassignFromRoles.defaultExpectation.params != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Expect")
	}

	if mmUnassignFromRoles.defaultExpectation.paramPtrs == nil {
		mmUnassignFromRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockUnassignFromRolesParamPtrs{}
	}
	mmUnassignFromRoles.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmUnassignFromRoles.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmUnassignFromRoles
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UnassignFromRoles
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Inspect(f func(ctx context.Context, groupID int64, roleIDs []int64)) *mGroupDomainServiceMockUnassignFromRoles {
	if mmUnassignFromRoles.mock.inspectFuncUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UnassignFromRoles")
	}

	mmUnassignFromRoles.mock.inspectFuncUnassignFromRoles = f

	return mmUnassignFromRoles
}

// Return sets up results that will be returned by GroupDomainService.UnassignFromRoles
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Return(err error) *GroupDomainServiceMock {
	if mmUnassignFromRoles.mock.funcUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Set")
	}

	if mmUnassignFromRoles.defaultExpectation == nil {
		mmUnassignFromRoles.defaultExpectation = &GroupDomainServiceMockUnassignFromRolesExpectation{mock: mmUnassignFromRoles.mock}
	}
	mmUnassignFromRoles.defaultExpectation.results = &GroupDomainServiceMockUnassignFromRolesResults{err}
	mmUnassignFromRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUnassignFromRoles.mock
}

// Set uses given function f to mock the GroupDomainService.UnassignFromRoles method
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Set(f func(ctx context.Context, groupID int64, roleIDs []int64) (err error)) *GroupDomainServiceMock {
	if mmUnassignFromRoles.defaultExpectation != nil {
		mmUnassignFromRoles.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UnassignFromRoles method")
	}

	if len(mmUnassignFromRoles.expectations) > 0 {
		mmUnassignFromRoles.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UnassignFromRoles method")
	}

	mmUnassignFromRoles.mock.funcUnassignFromRoles = f
	mmUnassignFromRoles.mock.funcUnassignFromRolesOrigin = minimock.CallerInfo(1)
	return mmUnassignFromRoles.mock
}

// When sets expectation for the GroupDomainService.UnassignFromRoles which will trigger the result defined by the following
// Then helper
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) When(ctx context.Context, groupID int64, roleIDs []int64) *GroupDomainServiceMockUnassignFromRolesExpectation {
	if mmUnassignFromRoles.mock.funcUnassignFromRoles != nil {
		mmUnassignFromRoles.mock.t.Fatalf("GroupDomainServiceMock.UnassignFromRoles mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUnassignFromRolesExpectation{
		mock:               mmUnassignFromRoles.mock,
		params:             &GroupDomainServiceMockUnassignFromRolesParams{ctx, groupID, roleIDs},
		expectationOrigins: GroupDomainServiceMockUnassignFromRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUnassignFromRoles.expectations = append(mmUnassignFromRoles.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UnassignFromRoles return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUnassignFromRolesExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUnassignFromRolesResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.UnassignFromRoles should be invoked
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Times(n uint64) *mGroupDomainServiceMockUnassignFromRoles {
	if n == 0 {
		mmUnassignFromRoles.mock.t.Fatalf("Times of GroupDomainServiceMock.UnassignFromRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUnassignFromRoles.expectedInvocations, n)
	mmUnassignFromRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUnassignFromRoles
}

func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) invocationsDone() bool {
	if len(mmUnassignFromRoles.expectations) == 0 && mmUnassignFromRoles.defaultExpectation == nil && mmUnassignFromRoles.mock.funcUnassignFromRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUnassignFromRoles.mock.afterUnassignFromRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUnassignFromRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UnassignFromRoles implements mm_service.GroupDomainService
func (mmUnassignFromRoles *GroupDomainServiceMock) UnassignFromRoles(ctx context.Context, groupID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmUnassignFromRoles.beforeUnassignFromRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmUnassignFromRoles.afterUnassignFromRolesCounter, 1)

	mmUnassignFromRoles.t.Helper()

	if mmUnassignFromRoles.inspectFuncUnassignFromRoles != nil {
		mmUnassignFromRoles.inspectFuncUnassignFromRoles(ctx, groupID, roleIDs)
	}

	mm_params := GroupDomainServiceMockUnassignFromRolesParams{ctx, groupID, roleIDs}

	// Record call args
	mmUnassignFromRoles.UnassignFromRolesMock.mutex.Lock()
	mmUnassignFromRoles.UnassignFromRolesMock.callArgs = append(mmUnassignFromRoles.UnassignFromRolesMock.callArgs, &mm_params)
	mmUnassignFromRoles.UnassignFromRolesMock.mutex.Unlock()

	for _, e := range mmUnassignFromRoles.UnassignFromRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.params
		mm_want_ptrs := mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUnassignFromRolesParams{ctx, groupID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUnassignFromRoles.t.Errorf("GroupDomainServiceMock.UnassignFromRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmUnassignFromRoles.t.Errorf("GroupDomainServiceMock.UnassignFromRoles got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmUnassignFromRoles.t.Errorf("GroupDomainServiceMock.UnassignFromRoles got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUnassignFromRoles.t.Errorf("GroupDomainServiceMock.UnassignFromRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUnassignFromRoles.UnassignFromRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmUnassignFromRoles.t.Fatal("No results are set for the GroupDomainServiceMock.UnassignFromRoles")
		}
		return (*mm_results).err
	}
	if mmUnassignFromRoles.funcUnassignFromRoles != nil {
		return mmUnassignFromRoles.funcUnassignFromRoles(ctx, groupID, roleIDs)
	}
	mmUnassignFromRoles.t.Fatalf("Unexpected call to GroupDomainServiceMock.UnassignFromRoles. %v %v %v", ctx, groupID, roleIDs)
	return
}

// UnassignFromRolesAfterCounter returns a count of finished GroupDomainServiceMock.UnassignFromRoles invocations
func (mmUnassignFromRoles *GroupDomainServiceMock) UnassignFromRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUnassignFromRoles.afterUnassignFromRolesCounter)
}

// UnassignFromRolesBeforeCounter returns a count of GroupDomainServiceMock.UnassignFromRoles invocations
func (mmUnassignFromRoles *GroupDomainServiceMock) UnassignFromRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUnassignFromRoles.beforeUnassignFromRolesCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UnassignFromRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUnassignFromRoles *mGroupDomainServiceMockUnassignFromRoles) Calls() []*GroupDomainServiceMockUnassignFromRolesParams {
	mmUnassignFromRoles.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUnassignFromRolesParams, len(mmUnassignFromRoles.callArgs))
	copy(argCopy, mmUnassignFromRoles.callArgs)

	mmUnassignFromRoles.mutex.RUnlock()

	return argCopy
}

// MinimockUnassignFromRolesDone returns true if the count of the UnassignFromRoles invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUnassignFromRolesDone() bool {
	if m.UnassignFromRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UnassignFromRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UnassignFromRolesMock.invocationsDone()
}

// MinimockUnassignFromRolesInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUnassignFromRolesInspect() {
	for _, e := range m.UnassignFromRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UnassignFromRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUnassignFromRolesCounter := mm_atomic.LoadUint64(&m.afterUnassignFromRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UnassignFromRolesMock.defaultExpectation != nil && afterUnassignFromRolesCounter < 1 {
		if m.UnassignFromRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UnassignFromRoles at\n%s", m.UnassignFromRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UnassignFromRoles at\n%s with params: %#v", m.UnassignFromRolesMock.defaultExpectation.expectationOrigins.origin, *m.UnassignFromRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUnassignFromRoles != nil && afterUnassignFromRolesCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UnassignFromRoles at\n%s", m.funcUnassignFromRolesOrigin)
	}

	if !m.UnassignFromRolesMock.invocationsDone() && afterUnassignFromRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UnassignFromRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UnassignFromRolesMock.expectedInvocations), m.UnassignFromRolesMock.expectedInvocationsOrigin, afterUnassignFromRolesCounter)
	}
}

type mGroupDomainServiceMockUpdate struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateExpectation
	expectations       []*GroupDomainServiceMockUpdateExpectation

	callArgs []*GroupDomainServiceMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateExpectation specifies expectation struct of the GroupDomainService.Update
type GroupDomainServiceMockUpdateExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateParams
	paramPtrs          *GroupDomainServiceMockUpdateParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateExpectationOrigins
	results            *GroupDomainServiceMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateParams contains parameters of the GroupDomainService.Update
type GroupDomainServiceMockUpdateParams struct {
	group groupentity.GroupUpdateData
}

// GroupDomainServiceMockUpdateParamPtrs contains pointers to parameters of the GroupDomainService.Update
type GroupDomainServiceMockUpdateParamPtrs struct {
	group *groupentity.GroupUpdateData
}

// GroupDomainServiceMockUpdateResults contains results of the GroupDomainService.Update
type GroupDomainServiceMockUpdateResults struct {
	g1  groupentity.Group
	err error
}

// GroupDomainServiceMockUpdateOrigins contains origins of expectations of the GroupDomainService.Update
type GroupDomainServiceMockUpdateExpectationOrigins struct {
	origin      string
	originGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mGroupDomainServiceMockUpdate) Optional() *mGroupDomainServiceMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for GroupDomainService.Update
func (mmUpdate *mGroupDomainServiceMockUpdate) Expect(group groupentity.GroupUpdateData) *mGroupDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &GroupDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("GroupDomainServiceMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &GroupDomainServiceMockUpdateParams{group}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectGroupParam1 sets up expected param group for GroupDomainService.Update
func (mmUpdate *mGroupDomainServiceMockUpdate) ExpectGroupParam1(group groupentity.GroupUpdateData) *mGroupDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &GroupDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("GroupDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.group = &group
	mmUpdate.defaultExpectation.expectationOrigins.originGroup = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.Update
func (mmUpdate *mGroupDomainServiceMockUpdate) Inspect(f func(group groupentity.GroupUpdateData)) *mGroupDomainServiceMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by GroupDomainService.Update
func (mmUpdate *mGroupDomainServiceMockUpdate) Return(g1 groupentity.Group, err error) *GroupDomainServiceMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &GroupDomainServiceMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &GroupDomainServiceMockUpdateResults{g1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the GroupDomainService.Update method
func (mmUpdate *mGroupDomainServiceMockUpdate) Set(f func(group groupentity.GroupUpdateData) (g1 groupentity.Group, err error)) *GroupDomainServiceMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the GroupDomainService.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mGroupDomainServiceMockUpdate) When(group groupentity.GroupUpdateData) *GroupDomainServiceMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("GroupDomainServiceMock.Update mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &GroupDomainServiceMockUpdateParams{group},
		expectationOrigins: GroupDomainServiceMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.Update return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateExpectation) Then(g1 groupentity.Group, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateResults{g1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.Update should be invoked
func (mmUpdate *mGroupDomainServiceMockUpdate) Times(n uint64) *mGroupDomainServiceMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of GroupDomainServiceMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mGroupDomainServiceMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_service.GroupDomainService
func (mmUpdate *GroupDomainServiceMock) Update(group groupentity.GroupUpdateData) (g1 groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(group)
	}

	mm_params := GroupDomainServiceMockUpdateParams{group}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateParams{group}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.group != nil && !minimock.Equal(*mm_want_ptrs.group, mm_got.group) {
				mmUpdate.t.Errorf("GroupDomainServiceMock.Update got unexpected parameter group, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originGroup, *mm_want_ptrs.group, mm_got.group, minimock.Diff(*mm_want_ptrs.group, mm_got.group))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("GroupDomainServiceMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the GroupDomainServiceMock.Update")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(group)
	}
	mmUpdate.t.Fatalf("Unexpected call to GroupDomainServiceMock.Update. %v", group)
	return
}

// UpdateAfterCounter returns a count of finished GroupDomainServiceMock.Update invocations
func (mmUpdate *GroupDomainServiceMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of GroupDomainServiceMock.Update invocations
func (mmUpdate *GroupDomainServiceMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mGroupDomainServiceMockUpdate) Calls() []*GroupDomainServiceMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

type mGroupDomainServiceMockUpdateByGroupFull struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateByGroupFullExpectation
	expectations       []*GroupDomainServiceMockUpdateByGroupFullExpectation

	callArgs []*GroupDomainServiceMockUpdateByGroupFullParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateByGroupFullExpectation specifies expectation struct of the GroupDomainService.UpdateByGroupFull
type GroupDomainServiceMockUpdateByGroupFullExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateByGroupFullParams
	paramPtrs          *GroupDomainServiceMockUpdateByGroupFullParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateByGroupFullExpectationOrigins
	results            *GroupDomainServiceMockUpdateByGroupFullResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateByGroupFullParams contains parameters of the GroupDomainService.UpdateByGroupFull
type GroupDomainServiceMockUpdateByGroupFullParams struct {
	ctx       context.Context
	group     groupentity.GroupFull
	productID int64
}

// GroupDomainServiceMockUpdateByGroupFullParamPtrs contains pointers to parameters of the GroupDomainService.UpdateByGroupFull
type GroupDomainServiceMockUpdateByGroupFullParamPtrs struct {
	ctx       *context.Context
	group     *groupentity.GroupFull
	productID *int64
}

// GroupDomainServiceMockUpdateByGroupFullResults contains results of the GroupDomainService.UpdateByGroupFull
type GroupDomainServiceMockUpdateByGroupFullResults struct {
	g1  groupentity.GroupFull
	err error
}

// GroupDomainServiceMockUpdateByGroupFullOrigins contains origins of expectations of the GroupDomainService.UpdateByGroupFull
type GroupDomainServiceMockUpdateByGroupFullExpectationOrigins struct {
	origin          string
	originCtx       string
	originGroup     string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Optional() *mGroupDomainServiceMockUpdateByGroupFull {
	mmUpdateByGroupFull.optional = true
	return mmUpdateByGroupFull
}

// Expect sets up expected params for GroupDomainService.UpdateByGroupFull
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Expect(ctx context.Context, group groupentity.GroupFull, productID int64) *mGroupDomainServiceMockUpdateByGroupFull {
	if mmUpdateByGroupFull.mock.funcUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Set")
	}

	if mmUpdateByGroupFull.defaultExpectation == nil {
		mmUpdateByGroupFull.defaultExpectation = &GroupDomainServiceMockUpdateByGroupFullExpectation{}
	}

	if mmUpdateByGroupFull.defaultExpectation.paramPtrs != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by ExpectParams functions")
	}

	mmUpdateByGroupFull.defaultExpectation.params = &GroupDomainServiceMockUpdateByGroupFullParams{ctx, group, productID}
	mmUpdateByGroupFull.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateByGroupFull.expectations {
		if minimock.Equal(e.params, mmUpdateByGroupFull.defaultExpectation.params) {
			mmUpdateByGroupFull.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateByGroupFull.defaultExpectation.params)
		}
	}

	return mmUpdateByGroupFull
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.UpdateByGroupFull
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockUpdateByGroupFull {
	if mmUpdateByGroupFull.mock.funcUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Set")
	}

	if mmUpdateByGroupFull.defaultExpectation == nil {
		mmUpdateByGroupFull.defaultExpectation = &GroupDomainServiceMockUpdateByGroupFullExpectation{}
	}

	if mmUpdateByGroupFull.defaultExpectation.params != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Expect")
	}

	if mmUpdateByGroupFull.defaultExpectation.paramPtrs == nil {
		mmUpdateByGroupFull.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateByGroupFullParamPtrs{}
	}
	mmUpdateByGroupFull.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateByGroupFull.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateByGroupFull
}

// ExpectGroupParam2 sets up expected param group for GroupDomainService.UpdateByGroupFull
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) ExpectGroupParam2(group groupentity.GroupFull) *mGroupDomainServiceMockUpdateByGroupFull {
	if mmUpdateByGroupFull.mock.funcUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Set")
	}

	if mmUpdateByGroupFull.defaultExpectation == nil {
		mmUpdateByGroupFull.defaultExpectation = &GroupDomainServiceMockUpdateByGroupFullExpectation{}
	}

	if mmUpdateByGroupFull.defaultExpectation.params != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Expect")
	}

	if mmUpdateByGroupFull.defaultExpectation.paramPtrs == nil {
		mmUpdateByGroupFull.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateByGroupFullParamPtrs{}
	}
	mmUpdateByGroupFull.defaultExpectation.paramPtrs.group = &group
	mmUpdateByGroupFull.defaultExpectation.expectationOrigins.originGroup = minimock.CallerInfo(1)

	return mmUpdateByGroupFull
}

// ExpectProductIDParam3 sets up expected param productID for GroupDomainService.UpdateByGroupFull
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) ExpectProductIDParam3(productID int64) *mGroupDomainServiceMockUpdateByGroupFull {
	if mmUpdateByGroupFull.mock.funcUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Set")
	}

	if mmUpdateByGroupFull.defaultExpectation == nil {
		mmUpdateByGroupFull.defaultExpectation = &GroupDomainServiceMockUpdateByGroupFullExpectation{}
	}

	if mmUpdateByGroupFull.defaultExpectation.params != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Expect")
	}

	if mmUpdateByGroupFull.defaultExpectation.paramPtrs == nil {
		mmUpdateByGroupFull.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateByGroupFullParamPtrs{}
	}
	mmUpdateByGroupFull.defaultExpectation.paramPtrs.productID = &productID
	mmUpdateByGroupFull.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmUpdateByGroupFull
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UpdateByGroupFull
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Inspect(f func(ctx context.Context, group groupentity.GroupFull, productID int64)) *mGroupDomainServiceMockUpdateByGroupFull {
	if mmUpdateByGroupFull.mock.inspectFuncUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UpdateByGroupFull")
	}

	mmUpdateByGroupFull.mock.inspectFuncUpdateByGroupFull = f

	return mmUpdateByGroupFull
}

// Return sets up results that will be returned by GroupDomainService.UpdateByGroupFull
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Return(g1 groupentity.GroupFull, err error) *GroupDomainServiceMock {
	if mmUpdateByGroupFull.mock.funcUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Set")
	}

	if mmUpdateByGroupFull.defaultExpectation == nil {
		mmUpdateByGroupFull.defaultExpectation = &GroupDomainServiceMockUpdateByGroupFullExpectation{mock: mmUpdateByGroupFull.mock}
	}
	mmUpdateByGroupFull.defaultExpectation.results = &GroupDomainServiceMockUpdateByGroupFullResults{g1, err}
	mmUpdateByGroupFull.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateByGroupFull.mock
}

// Set uses given function f to mock the GroupDomainService.UpdateByGroupFull method
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Set(f func(ctx context.Context, group groupentity.GroupFull, productID int64) (g1 groupentity.GroupFull, err error)) *GroupDomainServiceMock {
	if mmUpdateByGroupFull.defaultExpectation != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UpdateByGroupFull method")
	}

	if len(mmUpdateByGroupFull.expectations) > 0 {
		mmUpdateByGroupFull.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UpdateByGroupFull method")
	}

	mmUpdateByGroupFull.mock.funcUpdateByGroupFull = f
	mmUpdateByGroupFull.mock.funcUpdateByGroupFullOrigin = minimock.CallerInfo(1)
	return mmUpdateByGroupFull.mock
}

// When sets expectation for the GroupDomainService.UpdateByGroupFull which will trigger the result defined by the following
// Then helper
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) When(ctx context.Context, group groupentity.GroupFull, productID int64) *GroupDomainServiceMockUpdateByGroupFullExpectation {
	if mmUpdateByGroupFull.mock.funcUpdateByGroupFull != nil {
		mmUpdateByGroupFull.mock.t.Fatalf("GroupDomainServiceMock.UpdateByGroupFull mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateByGroupFullExpectation{
		mock:               mmUpdateByGroupFull.mock,
		params:             &GroupDomainServiceMockUpdateByGroupFullParams{ctx, group, productID},
		expectationOrigins: GroupDomainServiceMockUpdateByGroupFullExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateByGroupFull.expectations = append(mmUpdateByGroupFull.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UpdateByGroupFull return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateByGroupFullExpectation) Then(g1 groupentity.GroupFull, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateByGroupFullResults{g1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.UpdateByGroupFull should be invoked
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Times(n uint64) *mGroupDomainServiceMockUpdateByGroupFull {
	if n == 0 {
		mmUpdateByGroupFull.mock.t.Fatalf("Times of GroupDomainServiceMock.UpdateByGroupFull mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateByGroupFull.expectedInvocations, n)
	mmUpdateByGroupFull.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateByGroupFull
}

func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) invocationsDone() bool {
	if len(mmUpdateByGroupFull.expectations) == 0 && mmUpdateByGroupFull.defaultExpectation == nil && mmUpdateByGroupFull.mock.funcUpdateByGroupFull == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateByGroupFull.mock.afterUpdateByGroupFullCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateByGroupFull.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateByGroupFull implements mm_service.GroupDomainService
func (mmUpdateByGroupFull *GroupDomainServiceMock) UpdateByGroupFull(ctx context.Context, group groupentity.GroupFull, productID int64) (g1 groupentity.GroupFull, err error) {
	mm_atomic.AddUint64(&mmUpdateByGroupFull.beforeUpdateByGroupFullCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateByGroupFull.afterUpdateByGroupFullCounter, 1)

	mmUpdateByGroupFull.t.Helper()

	if mmUpdateByGroupFull.inspectFuncUpdateByGroupFull != nil {
		mmUpdateByGroupFull.inspectFuncUpdateByGroupFull(ctx, group, productID)
	}

	mm_params := GroupDomainServiceMockUpdateByGroupFullParams{ctx, group, productID}

	// Record call args
	mmUpdateByGroupFull.UpdateByGroupFullMock.mutex.Lock()
	mmUpdateByGroupFull.UpdateByGroupFullMock.callArgs = append(mmUpdateByGroupFull.UpdateByGroupFullMock.callArgs, &mm_params)
	mmUpdateByGroupFull.UpdateByGroupFullMock.mutex.Unlock()

	for _, e := range mmUpdateByGroupFull.UpdateByGroupFullMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.g1, e.results.err
		}
	}

	if mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateByGroupFullParams{ctx, group, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateByGroupFull.t.Errorf("GroupDomainServiceMock.UpdateByGroupFull got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.group != nil && !minimock.Equal(*mm_want_ptrs.group, mm_got.group) {
				mmUpdateByGroupFull.t.Errorf("GroupDomainServiceMock.UpdateByGroupFull got unexpected parameter group, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.expectationOrigins.originGroup, *mm_want_ptrs.group, mm_got.group, minimock.Diff(*mm_want_ptrs.group, mm_got.group))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmUpdateByGroupFull.t.Errorf("GroupDomainServiceMock.UpdateByGroupFull got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateByGroupFull.t.Errorf("GroupDomainServiceMock.UpdateByGroupFull got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateByGroupFull.UpdateByGroupFullMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateByGroupFull.t.Fatal("No results are set for the GroupDomainServiceMock.UpdateByGroupFull")
		}
		return (*mm_results).g1, (*mm_results).err
	}
	if mmUpdateByGroupFull.funcUpdateByGroupFull != nil {
		return mmUpdateByGroupFull.funcUpdateByGroupFull(ctx, group, productID)
	}
	mmUpdateByGroupFull.t.Fatalf("Unexpected call to GroupDomainServiceMock.UpdateByGroupFull. %v %v %v", ctx, group, productID)
	return
}

// UpdateByGroupFullAfterCounter returns a count of finished GroupDomainServiceMock.UpdateByGroupFull invocations
func (mmUpdateByGroupFull *GroupDomainServiceMock) UpdateByGroupFullAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateByGroupFull.afterUpdateByGroupFullCounter)
}

// UpdateByGroupFullBeforeCounter returns a count of GroupDomainServiceMock.UpdateByGroupFull invocations
func (mmUpdateByGroupFull *GroupDomainServiceMock) UpdateByGroupFullBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateByGroupFull.beforeUpdateByGroupFullCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UpdateByGroupFull.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateByGroupFull *mGroupDomainServiceMockUpdateByGroupFull) Calls() []*GroupDomainServiceMockUpdateByGroupFullParams {
	mmUpdateByGroupFull.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateByGroupFullParams, len(mmUpdateByGroupFull.callArgs))
	copy(argCopy, mmUpdateByGroupFull.callArgs)

	mmUpdateByGroupFull.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateByGroupFullDone returns true if the count of the UpdateByGroupFull invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateByGroupFullDone() bool {
	if m.UpdateByGroupFullMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateByGroupFullMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateByGroupFullMock.invocationsDone()
}

// MinimockUpdateByGroupFullInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateByGroupFullInspect() {
	for _, e := range m.UpdateByGroupFullMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateByGroupFull at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateByGroupFullCounter := mm_atomic.LoadUint64(&m.afterUpdateByGroupFullCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateByGroupFullMock.defaultExpectation != nil && afterUpdateByGroupFullCounter < 1 {
		if m.UpdateByGroupFullMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateByGroupFull at\n%s", m.UpdateByGroupFullMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateByGroupFull at\n%s with params: %#v", m.UpdateByGroupFullMock.defaultExpectation.expectationOrigins.origin, *m.UpdateByGroupFullMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateByGroupFull != nil && afterUpdateByGroupFullCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateByGroupFull at\n%s", m.funcUpdateByGroupFullOrigin)
	}

	if !m.UpdateByGroupFullMock.invocationsDone() && afterUpdateByGroupFullCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UpdateByGroupFull at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateByGroupFullMock.expectedInvocations), m.UpdateByGroupFullMock.expectedInvocationsOrigin, afterUpdateByGroupFullCounter)
	}
}

type mGroupDomainServiceMockUpdateCategoryStates struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateCategoryStatesExpectation
	expectations       []*GroupDomainServiceMockUpdateCategoryStatesExpectation

	callArgs []*GroupDomainServiceMockUpdateCategoryStatesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateCategoryStatesExpectation specifies expectation struct of the GroupDomainService.UpdateCategoryStates
type GroupDomainServiceMockUpdateCategoryStatesExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateCategoryStatesParams
	paramPtrs          *GroupDomainServiceMockUpdateCategoryStatesParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateCategoryStatesExpectationOrigins
	results            *GroupDomainServiceMockUpdateCategoryStatesResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateCategoryStatesParams contains parameters of the GroupDomainService.UpdateCategoryStates
type GroupDomainServiceMockUpdateCategoryStatesParams struct {
	groupsWithCategories []groupentity.GroupWithCategoryStats
}

// GroupDomainServiceMockUpdateCategoryStatesParamPtrs contains pointers to parameters of the GroupDomainService.UpdateCategoryStates
type GroupDomainServiceMockUpdateCategoryStatesParamPtrs struct {
	groupsWithCategories *[]groupentity.GroupWithCategoryStats
}

// GroupDomainServiceMockUpdateCategoryStatesResults contains results of the GroupDomainService.UpdateCategoryStates
type GroupDomainServiceMockUpdateCategoryStatesResults struct {
	err error
}

// GroupDomainServiceMockUpdateCategoryStatesOrigins contains origins of expectations of the GroupDomainService.UpdateCategoryStates
type GroupDomainServiceMockUpdateCategoryStatesExpectationOrigins struct {
	origin                     string
	originGroupsWithCategories string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Optional() *mGroupDomainServiceMockUpdateCategoryStates {
	mmUpdateCategoryStates.optional = true
	return mmUpdateCategoryStates
}

// Expect sets up expected params for GroupDomainService.UpdateCategoryStates
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Expect(groupsWithCategories []groupentity.GroupWithCategoryStats) *mGroupDomainServiceMockUpdateCategoryStates {
	if mmUpdateCategoryStates.mock.funcUpdateCategoryStates != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("GroupDomainServiceMock.UpdateCategoryStates mock is already set by Set")
	}

	if mmUpdateCategoryStates.defaultExpectation == nil {
		mmUpdateCategoryStates.defaultExpectation = &GroupDomainServiceMockUpdateCategoryStatesExpectation{}
	}

	if mmUpdateCategoryStates.defaultExpectation.paramPtrs != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("GroupDomainServiceMock.UpdateCategoryStates mock is already set by ExpectParams functions")
	}

	mmUpdateCategoryStates.defaultExpectation.params = &GroupDomainServiceMockUpdateCategoryStatesParams{groupsWithCategories}
	mmUpdateCategoryStates.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateCategoryStates.expectations {
		if minimock.Equal(e.params, mmUpdateCategoryStates.defaultExpectation.params) {
			mmUpdateCategoryStates.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateCategoryStates.defaultExpectation.params)
		}
	}

	return mmUpdateCategoryStates
}

// ExpectGroupsWithCategoriesParam1 sets up expected param groupsWithCategories for GroupDomainService.UpdateCategoryStates
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) ExpectGroupsWithCategoriesParam1(groupsWithCategories []groupentity.GroupWithCategoryStats) *mGroupDomainServiceMockUpdateCategoryStates {
	if mmUpdateCategoryStates.mock.funcUpdateCategoryStates != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("GroupDomainServiceMock.UpdateCategoryStates mock is already set by Set")
	}

	if mmUpdateCategoryStates.defaultExpectation == nil {
		mmUpdateCategoryStates.defaultExpectation = &GroupDomainServiceMockUpdateCategoryStatesExpectation{}
	}

	if mmUpdateCategoryStates.defaultExpectation.params != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("GroupDomainServiceMock.UpdateCategoryStates mock is already set by Expect")
	}

	if mmUpdateCategoryStates.defaultExpectation.paramPtrs == nil {
		mmUpdateCategoryStates.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateCategoryStatesParamPtrs{}
	}
	mmUpdateCategoryStates.defaultExpectation.paramPtrs.groupsWithCategories = &groupsWithCategories
	mmUpdateCategoryStates.defaultExpectation.expectationOrigins.originGroupsWithCategories = minimock.CallerInfo(1)

	return mmUpdateCategoryStates
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UpdateCategoryStates
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Inspect(f func(groupsWithCategories []groupentity.GroupWithCategoryStats)) *mGroupDomainServiceMockUpdateCategoryStates {
	if mmUpdateCategoryStates.mock.inspectFuncUpdateCategoryStates != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UpdateCategoryStates")
	}

	mmUpdateCategoryStates.mock.inspectFuncUpdateCategoryStates = f

	return mmUpdateCategoryStates
}

// Return sets up results that will be returned by GroupDomainService.UpdateCategoryStates
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Return(err error) *GroupDomainServiceMock {
	if mmUpdateCategoryStates.mock.funcUpdateCategoryStates != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("GroupDomainServiceMock.UpdateCategoryStates mock is already set by Set")
	}

	if mmUpdateCategoryStates.defaultExpectation == nil {
		mmUpdateCategoryStates.defaultExpectation = &GroupDomainServiceMockUpdateCategoryStatesExpectation{mock: mmUpdateCategoryStates.mock}
	}
	mmUpdateCategoryStates.defaultExpectation.results = &GroupDomainServiceMockUpdateCategoryStatesResults{err}
	mmUpdateCategoryStates.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateCategoryStates.mock
}

// Set uses given function f to mock the GroupDomainService.UpdateCategoryStates method
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Set(f func(groupsWithCategories []groupentity.GroupWithCategoryStats) (err error)) *GroupDomainServiceMock {
	if mmUpdateCategoryStates.defaultExpectation != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UpdateCategoryStates method")
	}

	if len(mmUpdateCategoryStates.expectations) > 0 {
		mmUpdateCategoryStates.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UpdateCategoryStates method")
	}

	mmUpdateCategoryStates.mock.funcUpdateCategoryStates = f
	mmUpdateCategoryStates.mock.funcUpdateCategoryStatesOrigin = minimock.CallerInfo(1)
	return mmUpdateCategoryStates.mock
}

// When sets expectation for the GroupDomainService.UpdateCategoryStates which will trigger the result defined by the following
// Then helper
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) When(groupsWithCategories []groupentity.GroupWithCategoryStats) *GroupDomainServiceMockUpdateCategoryStatesExpectation {
	if mmUpdateCategoryStates.mock.funcUpdateCategoryStates != nil {
		mmUpdateCategoryStates.mock.t.Fatalf("GroupDomainServiceMock.UpdateCategoryStates mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateCategoryStatesExpectation{
		mock:               mmUpdateCategoryStates.mock,
		params:             &GroupDomainServiceMockUpdateCategoryStatesParams{groupsWithCategories},
		expectationOrigins: GroupDomainServiceMockUpdateCategoryStatesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateCategoryStates.expectations = append(mmUpdateCategoryStates.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UpdateCategoryStates return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateCategoryStatesExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateCategoryStatesResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.UpdateCategoryStates should be invoked
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Times(n uint64) *mGroupDomainServiceMockUpdateCategoryStates {
	if n == 0 {
		mmUpdateCategoryStates.mock.t.Fatalf("Times of GroupDomainServiceMock.UpdateCategoryStates mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateCategoryStates.expectedInvocations, n)
	mmUpdateCategoryStates.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateCategoryStates
}

func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) invocationsDone() bool {
	if len(mmUpdateCategoryStates.expectations) == 0 && mmUpdateCategoryStates.defaultExpectation == nil && mmUpdateCategoryStates.mock.funcUpdateCategoryStates == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateCategoryStates.mock.afterUpdateCategoryStatesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateCategoryStates.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateCategoryStates implements mm_service.GroupDomainService
func (mmUpdateCategoryStates *GroupDomainServiceMock) UpdateCategoryStates(groupsWithCategories []groupentity.GroupWithCategoryStats) (err error) {
	mm_atomic.AddUint64(&mmUpdateCategoryStates.beforeUpdateCategoryStatesCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateCategoryStates.afterUpdateCategoryStatesCounter, 1)

	mmUpdateCategoryStates.t.Helper()

	if mmUpdateCategoryStates.inspectFuncUpdateCategoryStates != nil {
		mmUpdateCategoryStates.inspectFuncUpdateCategoryStates(groupsWithCategories)
	}

	mm_params := GroupDomainServiceMockUpdateCategoryStatesParams{groupsWithCategories}

	// Record call args
	mmUpdateCategoryStates.UpdateCategoryStatesMock.mutex.Lock()
	mmUpdateCategoryStates.UpdateCategoryStatesMock.callArgs = append(mmUpdateCategoryStates.UpdateCategoryStatesMock.callArgs, &mm_params)
	mmUpdateCategoryStates.UpdateCategoryStatesMock.mutex.Unlock()

	for _, e := range mmUpdateCategoryStates.UpdateCategoryStatesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateCategoryStatesParams{groupsWithCategories}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupsWithCategories != nil && !minimock.Equal(*mm_want_ptrs.groupsWithCategories, mm_got.groupsWithCategories) {
				mmUpdateCategoryStates.t.Errorf("GroupDomainServiceMock.UpdateCategoryStates got unexpected parameter groupsWithCategories, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation.expectationOrigins.originGroupsWithCategories, *mm_want_ptrs.groupsWithCategories, mm_got.groupsWithCategories, minimock.Diff(*mm_want_ptrs.groupsWithCategories, mm_got.groupsWithCategories))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateCategoryStates.t.Errorf("GroupDomainServiceMock.UpdateCategoryStates got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateCategoryStates.UpdateCategoryStatesMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateCategoryStates.t.Fatal("No results are set for the GroupDomainServiceMock.UpdateCategoryStates")
		}
		return (*mm_results).err
	}
	if mmUpdateCategoryStates.funcUpdateCategoryStates != nil {
		return mmUpdateCategoryStates.funcUpdateCategoryStates(groupsWithCategories)
	}
	mmUpdateCategoryStates.t.Fatalf("Unexpected call to GroupDomainServiceMock.UpdateCategoryStates. %v", groupsWithCategories)
	return
}

// UpdateCategoryStatesAfterCounter returns a count of finished GroupDomainServiceMock.UpdateCategoryStates invocations
func (mmUpdateCategoryStates *GroupDomainServiceMock) UpdateCategoryStatesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateCategoryStates.afterUpdateCategoryStatesCounter)
}

// UpdateCategoryStatesBeforeCounter returns a count of GroupDomainServiceMock.UpdateCategoryStates invocations
func (mmUpdateCategoryStates *GroupDomainServiceMock) UpdateCategoryStatesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateCategoryStates.beforeUpdateCategoryStatesCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UpdateCategoryStates.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateCategoryStates *mGroupDomainServiceMockUpdateCategoryStates) Calls() []*GroupDomainServiceMockUpdateCategoryStatesParams {
	mmUpdateCategoryStates.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateCategoryStatesParams, len(mmUpdateCategoryStates.callArgs))
	copy(argCopy, mmUpdateCategoryStates.callArgs)

	mmUpdateCategoryStates.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateCategoryStatesDone returns true if the count of the UpdateCategoryStates invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateCategoryStatesDone() bool {
	if m.UpdateCategoryStatesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateCategoryStatesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateCategoryStatesMock.invocationsDone()
}

// MinimockUpdateCategoryStatesInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateCategoryStatesInspect() {
	for _, e := range m.UpdateCategoryStatesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateCategoryStates at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCategoryStatesCounter := mm_atomic.LoadUint64(&m.afterUpdateCategoryStatesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateCategoryStatesMock.defaultExpectation != nil && afterUpdateCategoryStatesCounter < 1 {
		if m.UpdateCategoryStatesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateCategoryStates at\n%s", m.UpdateCategoryStatesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateCategoryStates at\n%s with params: %#v", m.UpdateCategoryStatesMock.defaultExpectation.expectationOrigins.origin, *m.UpdateCategoryStatesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateCategoryStates != nil && afterUpdateCategoryStatesCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateCategoryStates at\n%s", m.funcUpdateCategoryStatesOrigin)
	}

	if !m.UpdateCategoryStatesMock.invocationsDone() && afterUpdateCategoryStatesCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UpdateCategoryStates at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateCategoryStatesMock.expectedInvocations), m.UpdateCategoryStatesMock.expectedInvocationsOrigin, afterUpdateCategoryStatesCounter)
	}
}

type mGroupDomainServiceMockUpdateLinksWithRoles struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateLinksWithRolesExpectation
	expectations       []*GroupDomainServiceMockUpdateLinksWithRolesExpectation

	callArgs []*GroupDomainServiceMockUpdateLinksWithRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateLinksWithRolesExpectation specifies expectation struct of the GroupDomainService.UpdateLinksWithRoles
type GroupDomainServiceMockUpdateLinksWithRolesExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateLinksWithRolesParams
	paramPtrs          *GroupDomainServiceMockUpdateLinksWithRolesParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateLinksWithRolesExpectationOrigins
	results            *GroupDomainServiceMockUpdateLinksWithRolesResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateLinksWithRolesParams contains parameters of the GroupDomainService.UpdateLinksWithRoles
type GroupDomainServiceMockUpdateLinksWithRolesParams struct {
	ctx     context.Context
	groupID int64
	roles   []roleentity.RoleID
}

// GroupDomainServiceMockUpdateLinksWithRolesParamPtrs contains pointers to parameters of the GroupDomainService.UpdateLinksWithRoles
type GroupDomainServiceMockUpdateLinksWithRolesParamPtrs struct {
	ctx     *context.Context
	groupID *int64
	roles   *[]roleentity.RoleID
}

// GroupDomainServiceMockUpdateLinksWithRolesResults contains results of the GroupDomainService.UpdateLinksWithRoles
type GroupDomainServiceMockUpdateLinksWithRolesResults struct {
	err error
}

// GroupDomainServiceMockUpdateLinksWithRolesOrigins contains origins of expectations of the GroupDomainService.UpdateLinksWithRoles
type GroupDomainServiceMockUpdateLinksWithRolesExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
	originRoles   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Optional() *mGroupDomainServiceMockUpdateLinksWithRoles {
	mmUpdateLinksWithRoles.optional = true
	return mmUpdateLinksWithRoles
}

// Expect sets up expected params for GroupDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Expect(ctx context.Context, groupID int64, roles []roleentity.RoleID) *mGroupDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by ExpectParams functions")
	}

	mmUpdateLinksWithRoles.defaultExpectation.params = &GroupDomainServiceMockUpdateLinksWithRolesParams{ctx, groupID, roles}
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLinksWithRoles.expectations {
		if minimock.Equal(e.params, mmUpdateLinksWithRoles.defaultExpectation.params) {
			mmUpdateLinksWithRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLinksWithRoles.defaultExpectation.params)
		}
	}

	return mmUpdateLinksWithRoles
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.params != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Expect")
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateLinksWithRolesParamPtrs{}
	}
	mmUpdateLinksWithRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLinksWithRoles
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) ExpectGroupIDParam2(groupID int64) *mGroupDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.params != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Expect")
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateLinksWithRolesParamPtrs{}
	}
	mmUpdateLinksWithRoles.defaultExpectation.paramPtrs.groupID = &groupID
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmUpdateLinksWithRoles
}

// ExpectRolesParam3 sets up expected param roles for GroupDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) ExpectRolesParam3(roles []roleentity.RoleID) *mGroupDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.params != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Expect")
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithRoles.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateLinksWithRolesParamPtrs{}
	}
	mmUpdateLinksWithRoles.defaultExpectation.paramPtrs.roles = &roles
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.originRoles = minimock.CallerInfo(1)

	return mmUpdateLinksWithRoles
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Inspect(f func(ctx context.Context, groupID int64, roles []roleentity.RoleID)) *mGroupDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.inspectFuncUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UpdateLinksWithRoles")
	}

	mmUpdateLinksWithRoles.mock.inspectFuncUpdateLinksWithRoles = f

	return mmUpdateLinksWithRoles
}

// Return sets up results that will be returned by GroupDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Return(err error) *GroupDomainServiceMock {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithRolesExpectation{mock: mmUpdateLinksWithRoles.mock}
	}
	mmUpdateLinksWithRoles.defaultExpectation.results = &GroupDomainServiceMockUpdateLinksWithRolesResults{err}
	mmUpdateLinksWithRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithRoles.mock
}

// Set uses given function f to mock the GroupDomainService.UpdateLinksWithRoles method
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Set(f func(ctx context.Context, groupID int64, roles []roleentity.RoleID) (err error)) *GroupDomainServiceMock {
	if mmUpdateLinksWithRoles.defaultExpectation != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UpdateLinksWithRoles method")
	}

	if len(mmUpdateLinksWithRoles.expectations) > 0 {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UpdateLinksWithRoles method")
	}

	mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles = f
	mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRolesOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithRoles.mock
}

// When sets expectation for the GroupDomainService.UpdateLinksWithRoles which will trigger the result defined by the following
// Then helper
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) When(ctx context.Context, groupID int64, roles []roleentity.RoleID) *GroupDomainServiceMockUpdateLinksWithRolesExpectation {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateLinksWithRolesExpectation{
		mock:               mmUpdateLinksWithRoles.mock,
		params:             &GroupDomainServiceMockUpdateLinksWithRolesParams{ctx, groupID, roles},
		expectationOrigins: GroupDomainServiceMockUpdateLinksWithRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLinksWithRoles.expectations = append(mmUpdateLinksWithRoles.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UpdateLinksWithRoles return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateLinksWithRolesExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateLinksWithRolesResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.UpdateLinksWithRoles should be invoked
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Times(n uint64) *mGroupDomainServiceMockUpdateLinksWithRoles {
	if n == 0 {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Times of GroupDomainServiceMock.UpdateLinksWithRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLinksWithRoles.expectedInvocations, n)
	mmUpdateLinksWithRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithRoles
}

func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) invocationsDone() bool {
	if len(mmUpdateLinksWithRoles.expectations) == 0 && mmUpdateLinksWithRoles.defaultExpectation == nil && mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.mock.afterUpdateLinksWithRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLinksWithRoles implements mm_service.GroupDomainService
func (mmUpdateLinksWithRoles *GroupDomainServiceMock) UpdateLinksWithRoles(ctx context.Context, groupID int64, roles []roleentity.RoleID) (err error) {
	mm_atomic.AddUint64(&mmUpdateLinksWithRoles.beforeUpdateLinksWithRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLinksWithRoles.afterUpdateLinksWithRolesCounter, 1)

	mmUpdateLinksWithRoles.t.Helper()

	if mmUpdateLinksWithRoles.inspectFuncUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.inspectFuncUpdateLinksWithRoles(ctx, groupID, roles)
	}

	mm_params := GroupDomainServiceMockUpdateLinksWithRolesParams{ctx, groupID, roles}

	// Record call args
	mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.mutex.Lock()
	mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.callArgs = append(mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.callArgs, &mm_params)
	mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.mutex.Unlock()

	for _, e := range mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateLinksWithRolesParams{ctx, groupID, roles}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLinksWithRoles.t.Errorf("GroupDomainServiceMock.UpdateLinksWithRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmUpdateLinksWithRoles.t.Errorf("GroupDomainServiceMock.UpdateLinksWithRoles got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.roles != nil && !minimock.Equal(*mm_want_ptrs.roles, mm_got.roles) {
				mmUpdateLinksWithRoles.t.Errorf("GroupDomainServiceMock.UpdateLinksWithRoles got unexpected parameter roles, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.originRoles, *mm_want_ptrs.roles, mm_got.roles, minimock.Diff(*mm_want_ptrs.roles, mm_got.roles))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLinksWithRoles.t.Errorf("GroupDomainServiceMock.UpdateLinksWithRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLinksWithRoles.t.Fatal("No results are set for the GroupDomainServiceMock.UpdateLinksWithRoles")
		}
		return (*mm_results).err
	}
	if mmUpdateLinksWithRoles.funcUpdateLinksWithRoles != nil {
		return mmUpdateLinksWithRoles.funcUpdateLinksWithRoles(ctx, groupID, roles)
	}
	mmUpdateLinksWithRoles.t.Fatalf("Unexpected call to GroupDomainServiceMock.UpdateLinksWithRoles. %v %v %v", ctx, groupID, roles)
	return
}

// UpdateLinksWithRolesAfterCounter returns a count of finished GroupDomainServiceMock.UpdateLinksWithRoles invocations
func (mmUpdateLinksWithRoles *GroupDomainServiceMock) UpdateLinksWithRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.afterUpdateLinksWithRolesCounter)
}

// UpdateLinksWithRolesBeforeCounter returns a count of GroupDomainServiceMock.UpdateLinksWithRoles invocations
func (mmUpdateLinksWithRoles *GroupDomainServiceMock) UpdateLinksWithRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.beforeUpdateLinksWithRolesCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UpdateLinksWithRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLinksWithRoles *mGroupDomainServiceMockUpdateLinksWithRoles) Calls() []*GroupDomainServiceMockUpdateLinksWithRolesParams {
	mmUpdateLinksWithRoles.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateLinksWithRolesParams, len(mmUpdateLinksWithRoles.callArgs))
	copy(argCopy, mmUpdateLinksWithRoles.callArgs)

	mmUpdateLinksWithRoles.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLinksWithRolesDone returns true if the count of the UpdateLinksWithRoles invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateLinksWithRolesDone() bool {
	if m.UpdateLinksWithRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLinksWithRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLinksWithRolesMock.invocationsDone()
}

// MinimockUpdateLinksWithRolesInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateLinksWithRolesInspect() {
	for _, e := range m.UpdateLinksWithRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLinksWithRolesCounter := mm_atomic.LoadUint64(&m.afterUpdateLinksWithRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLinksWithRolesMock.defaultExpectation != nil && afterUpdateLinksWithRolesCounter < 1 {
		if m.UpdateLinksWithRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithRoles at\n%s", m.UpdateLinksWithRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithRoles at\n%s with params: %#v", m.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLinksWithRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLinksWithRoles != nil && afterUpdateLinksWithRolesCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithRoles at\n%s", m.funcUpdateLinksWithRolesOrigin)
	}

	if !m.UpdateLinksWithRolesMock.invocationsDone() && afterUpdateLinksWithRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UpdateLinksWithRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLinksWithRolesMock.expectedInvocations), m.UpdateLinksWithRolesMock.expectedInvocationsOrigin, afterUpdateLinksWithRolesCounter)
	}
}

type mGroupDomainServiceMockUpdateLinksWithUsers struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateLinksWithUsersExpectation
	expectations       []*GroupDomainServiceMockUpdateLinksWithUsersExpectation

	callArgs []*GroupDomainServiceMockUpdateLinksWithUsersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateLinksWithUsersExpectation specifies expectation struct of the GroupDomainService.UpdateLinksWithUsers
type GroupDomainServiceMockUpdateLinksWithUsersExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateLinksWithUsersParams
	paramPtrs          *GroupDomainServiceMockUpdateLinksWithUsersParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateLinksWithUsersExpectationOrigins
	results            *GroupDomainServiceMockUpdateLinksWithUsersResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateLinksWithUsersParams contains parameters of the GroupDomainService.UpdateLinksWithUsers
type GroupDomainServiceMockUpdateLinksWithUsersParams struct {
	ctx       context.Context
	groupID   int64
	userLinks []userentity.UserProductLink
}

// GroupDomainServiceMockUpdateLinksWithUsersParamPtrs contains pointers to parameters of the GroupDomainService.UpdateLinksWithUsers
type GroupDomainServiceMockUpdateLinksWithUsersParamPtrs struct {
	ctx       *context.Context
	groupID   *int64
	userLinks *[]userentity.UserProductLink
}

// GroupDomainServiceMockUpdateLinksWithUsersResults contains results of the GroupDomainService.UpdateLinksWithUsers
type GroupDomainServiceMockUpdateLinksWithUsersResults struct {
	err error
}

// GroupDomainServiceMockUpdateLinksWithUsersOrigins contains origins of expectations of the GroupDomainService.UpdateLinksWithUsers
type GroupDomainServiceMockUpdateLinksWithUsersExpectationOrigins struct {
	origin          string
	originCtx       string
	originGroupID   string
	originUserLinks string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Optional() *mGroupDomainServiceMockUpdateLinksWithUsers {
	mmUpdateLinksWithUsers.optional = true
	return mmUpdateLinksWithUsers
}

// Expect sets up expected params for GroupDomainService.UpdateLinksWithUsers
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Expect(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) *mGroupDomainServiceMockUpdateLinksWithUsers {
	if mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Set")
	}

	if mmUpdateLinksWithUsers.defaultExpectation == nil {
		mmUpdateLinksWithUsers.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithUsersExpectation{}
	}

	if mmUpdateLinksWithUsers.defaultExpectation.paramPtrs != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by ExpectParams functions")
	}

	mmUpdateLinksWithUsers.defaultExpectation.params = &GroupDomainServiceMockUpdateLinksWithUsersParams{ctx, groupID, userLinks}
	mmUpdateLinksWithUsers.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLinksWithUsers.expectations {
		if minimock.Equal(e.params, mmUpdateLinksWithUsers.defaultExpectation.params) {
			mmUpdateLinksWithUsers.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLinksWithUsers.defaultExpectation.params)
		}
	}

	return mmUpdateLinksWithUsers
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.UpdateLinksWithUsers
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockUpdateLinksWithUsers {
	if mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Set")
	}

	if mmUpdateLinksWithUsers.defaultExpectation == nil {
		mmUpdateLinksWithUsers.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithUsersExpectation{}
	}

	if mmUpdateLinksWithUsers.defaultExpectation.params != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Expect")
	}

	if mmUpdateLinksWithUsers.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithUsers.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateLinksWithUsersParamPtrs{}
	}
	mmUpdateLinksWithUsers.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLinksWithUsers.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLinksWithUsers
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupDomainService.UpdateLinksWithUsers
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) ExpectGroupIDParam2(groupID int64) *mGroupDomainServiceMockUpdateLinksWithUsers {
	if mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Set")
	}

	if mmUpdateLinksWithUsers.defaultExpectation == nil {
		mmUpdateLinksWithUsers.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithUsersExpectation{}
	}

	if mmUpdateLinksWithUsers.defaultExpectation.params != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Expect")
	}

	if mmUpdateLinksWithUsers.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithUsers.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateLinksWithUsersParamPtrs{}
	}
	mmUpdateLinksWithUsers.defaultExpectation.paramPtrs.groupID = &groupID
	mmUpdateLinksWithUsers.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmUpdateLinksWithUsers
}

// ExpectUserLinksParam3 sets up expected param userLinks for GroupDomainService.UpdateLinksWithUsers
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) ExpectUserLinksParam3(userLinks []userentity.UserProductLink) *mGroupDomainServiceMockUpdateLinksWithUsers {
	if mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Set")
	}

	if mmUpdateLinksWithUsers.defaultExpectation == nil {
		mmUpdateLinksWithUsers.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithUsersExpectation{}
	}

	if mmUpdateLinksWithUsers.defaultExpectation.params != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Expect")
	}

	if mmUpdateLinksWithUsers.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithUsers.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateLinksWithUsersParamPtrs{}
	}
	mmUpdateLinksWithUsers.defaultExpectation.paramPtrs.userLinks = &userLinks
	mmUpdateLinksWithUsers.defaultExpectation.expectationOrigins.originUserLinks = minimock.CallerInfo(1)

	return mmUpdateLinksWithUsers
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UpdateLinksWithUsers
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Inspect(f func(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink)) *mGroupDomainServiceMockUpdateLinksWithUsers {
	if mmUpdateLinksWithUsers.mock.inspectFuncUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UpdateLinksWithUsers")
	}

	mmUpdateLinksWithUsers.mock.inspectFuncUpdateLinksWithUsers = f

	return mmUpdateLinksWithUsers
}

// Return sets up results that will be returned by GroupDomainService.UpdateLinksWithUsers
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Return(err error) *GroupDomainServiceMock {
	if mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Set")
	}

	if mmUpdateLinksWithUsers.defaultExpectation == nil {
		mmUpdateLinksWithUsers.defaultExpectation = &GroupDomainServiceMockUpdateLinksWithUsersExpectation{mock: mmUpdateLinksWithUsers.mock}
	}
	mmUpdateLinksWithUsers.defaultExpectation.results = &GroupDomainServiceMockUpdateLinksWithUsersResults{err}
	mmUpdateLinksWithUsers.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithUsers.mock
}

// Set uses given function f to mock the GroupDomainService.UpdateLinksWithUsers method
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Set(f func(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) (err error)) *GroupDomainServiceMock {
	if mmUpdateLinksWithUsers.defaultExpectation != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UpdateLinksWithUsers method")
	}

	if len(mmUpdateLinksWithUsers.expectations) > 0 {
		mmUpdateLinksWithUsers.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UpdateLinksWithUsers method")
	}

	mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers = f
	mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsersOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithUsers.mock
}

// When sets expectation for the GroupDomainService.UpdateLinksWithUsers which will trigger the result defined by the following
// Then helper
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) When(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) *GroupDomainServiceMockUpdateLinksWithUsersExpectation {
	if mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.mock.t.Fatalf("GroupDomainServiceMock.UpdateLinksWithUsers mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateLinksWithUsersExpectation{
		mock:               mmUpdateLinksWithUsers.mock,
		params:             &GroupDomainServiceMockUpdateLinksWithUsersParams{ctx, groupID, userLinks},
		expectationOrigins: GroupDomainServiceMockUpdateLinksWithUsersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLinksWithUsers.expectations = append(mmUpdateLinksWithUsers.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UpdateLinksWithUsers return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateLinksWithUsersExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateLinksWithUsersResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.UpdateLinksWithUsers should be invoked
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Times(n uint64) *mGroupDomainServiceMockUpdateLinksWithUsers {
	if n == 0 {
		mmUpdateLinksWithUsers.mock.t.Fatalf("Times of GroupDomainServiceMock.UpdateLinksWithUsers mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLinksWithUsers.expectedInvocations, n)
	mmUpdateLinksWithUsers.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithUsers
}

func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) invocationsDone() bool {
	if len(mmUpdateLinksWithUsers.expectations) == 0 && mmUpdateLinksWithUsers.defaultExpectation == nil && mmUpdateLinksWithUsers.mock.funcUpdateLinksWithUsers == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithUsers.mock.afterUpdateLinksWithUsersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithUsers.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLinksWithUsers implements mm_service.GroupDomainService
func (mmUpdateLinksWithUsers *GroupDomainServiceMock) UpdateLinksWithUsers(ctx context.Context, groupID int64, userLinks []userentity.UserProductLink) (err error) {
	mm_atomic.AddUint64(&mmUpdateLinksWithUsers.beforeUpdateLinksWithUsersCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLinksWithUsers.afterUpdateLinksWithUsersCounter, 1)

	mmUpdateLinksWithUsers.t.Helper()

	if mmUpdateLinksWithUsers.inspectFuncUpdateLinksWithUsers != nil {
		mmUpdateLinksWithUsers.inspectFuncUpdateLinksWithUsers(ctx, groupID, userLinks)
	}

	mm_params := GroupDomainServiceMockUpdateLinksWithUsersParams{ctx, groupID, userLinks}

	// Record call args
	mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.mutex.Lock()
	mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.callArgs = append(mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.callArgs, &mm_params)
	mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.mutex.Unlock()

	for _, e := range mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateLinksWithUsersParams{ctx, groupID, userLinks}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLinksWithUsers.t.Errorf("GroupDomainServiceMock.UpdateLinksWithUsers got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmUpdateLinksWithUsers.t.Errorf("GroupDomainServiceMock.UpdateLinksWithUsers got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.userLinks != nil && !minimock.Equal(*mm_want_ptrs.userLinks, mm_got.userLinks) {
				mmUpdateLinksWithUsers.t.Errorf("GroupDomainServiceMock.UpdateLinksWithUsers got unexpected parameter userLinks, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.expectationOrigins.originUserLinks, *mm_want_ptrs.userLinks, mm_got.userLinks, minimock.Diff(*mm_want_ptrs.userLinks, mm_got.userLinks))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLinksWithUsers.t.Errorf("GroupDomainServiceMock.UpdateLinksWithUsers got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLinksWithUsers.UpdateLinksWithUsersMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLinksWithUsers.t.Fatal("No results are set for the GroupDomainServiceMock.UpdateLinksWithUsers")
		}
		return (*mm_results).err
	}
	if mmUpdateLinksWithUsers.funcUpdateLinksWithUsers != nil {
		return mmUpdateLinksWithUsers.funcUpdateLinksWithUsers(ctx, groupID, userLinks)
	}
	mmUpdateLinksWithUsers.t.Fatalf("Unexpected call to GroupDomainServiceMock.UpdateLinksWithUsers. %v %v %v", ctx, groupID, userLinks)
	return
}

// UpdateLinksWithUsersAfterCounter returns a count of finished GroupDomainServiceMock.UpdateLinksWithUsers invocations
func (mmUpdateLinksWithUsers *GroupDomainServiceMock) UpdateLinksWithUsersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithUsers.afterUpdateLinksWithUsersCounter)
}

// UpdateLinksWithUsersBeforeCounter returns a count of GroupDomainServiceMock.UpdateLinksWithUsers invocations
func (mmUpdateLinksWithUsers *GroupDomainServiceMock) UpdateLinksWithUsersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithUsers.beforeUpdateLinksWithUsersCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UpdateLinksWithUsers.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLinksWithUsers *mGroupDomainServiceMockUpdateLinksWithUsers) Calls() []*GroupDomainServiceMockUpdateLinksWithUsersParams {
	mmUpdateLinksWithUsers.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateLinksWithUsersParams, len(mmUpdateLinksWithUsers.callArgs))
	copy(argCopy, mmUpdateLinksWithUsers.callArgs)

	mmUpdateLinksWithUsers.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLinksWithUsersDone returns true if the count of the UpdateLinksWithUsers invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateLinksWithUsersDone() bool {
	if m.UpdateLinksWithUsersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLinksWithUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLinksWithUsersMock.invocationsDone()
}

// MinimockUpdateLinksWithUsersInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateLinksWithUsersInspect() {
	for _, e := range m.UpdateLinksWithUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithUsers at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLinksWithUsersCounter := mm_atomic.LoadUint64(&m.afterUpdateLinksWithUsersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLinksWithUsersMock.defaultExpectation != nil && afterUpdateLinksWithUsersCounter < 1 {
		if m.UpdateLinksWithUsersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithUsers at\n%s", m.UpdateLinksWithUsersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithUsers at\n%s with params: %#v", m.UpdateLinksWithUsersMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLinksWithUsersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLinksWithUsers != nil && afterUpdateLinksWithUsersCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateLinksWithUsers at\n%s", m.funcUpdateLinksWithUsersOrigin)
	}

	if !m.UpdateLinksWithUsersMock.invocationsDone() && afterUpdateLinksWithUsersCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UpdateLinksWithUsers at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLinksWithUsersMock.expectedInvocations), m.UpdateLinksWithUsersMock.expectedInvocationsOrigin, afterUpdateLinksWithUsersCounter)
	}
}

type mGroupDomainServiceMockUpdateParticipantGroups struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateParticipantGroupsExpectation
	expectations       []*GroupDomainServiceMockUpdateParticipantGroupsExpectation

	callArgs []*GroupDomainServiceMockUpdateParticipantGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateParticipantGroupsExpectation specifies expectation struct of the GroupDomainService.UpdateParticipantGroups
type GroupDomainServiceMockUpdateParticipantGroupsExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateParticipantGroupsParams
	paramPtrs          *GroupDomainServiceMockUpdateParticipantGroupsParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateParticipantGroupsExpectationOrigins
	results            *GroupDomainServiceMockUpdateParticipantGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateParticipantGroupsParams contains parameters of the GroupDomainService.UpdateParticipantGroups
type GroupDomainServiceMockUpdateParticipantGroupsParams struct {
	ctx            context.Context
	groupID        int64
	participantIDs []int64
}

// GroupDomainServiceMockUpdateParticipantGroupsParamPtrs contains pointers to parameters of the GroupDomainService.UpdateParticipantGroups
type GroupDomainServiceMockUpdateParticipantGroupsParamPtrs struct {
	ctx            *context.Context
	groupID        *int64
	participantIDs *[]int64
}

// GroupDomainServiceMockUpdateParticipantGroupsResults contains results of the GroupDomainService.UpdateParticipantGroups
type GroupDomainServiceMockUpdateParticipantGroupsResults struct {
	ia1 []int64
	err error
}

// GroupDomainServiceMockUpdateParticipantGroupsOrigins contains origins of expectations of the GroupDomainService.UpdateParticipantGroups
type GroupDomainServiceMockUpdateParticipantGroupsExpectationOrigins struct {
	origin               string
	originCtx            string
	originGroupID        string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Optional() *mGroupDomainServiceMockUpdateParticipantGroups {
	mmUpdateParticipantGroups.optional = true
	return mmUpdateParticipantGroups
}

// Expect sets up expected params for GroupDomainService.UpdateParticipantGroups
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Expect(ctx context.Context, groupID int64, participantIDs []int64) *mGroupDomainServiceMockUpdateParticipantGroups {
	if mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Set")
	}

	if mmUpdateParticipantGroups.defaultExpectation == nil {
		mmUpdateParticipantGroups.defaultExpectation = &GroupDomainServiceMockUpdateParticipantGroupsExpectation{}
	}

	if mmUpdateParticipantGroups.defaultExpectation.paramPtrs != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by ExpectParams functions")
	}

	mmUpdateParticipantGroups.defaultExpectation.params = &GroupDomainServiceMockUpdateParticipantGroupsParams{ctx, groupID, participantIDs}
	mmUpdateParticipantGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateParticipantGroups.expectations {
		if minimock.Equal(e.params, mmUpdateParticipantGroups.defaultExpectation.params) {
			mmUpdateParticipantGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateParticipantGroups.defaultExpectation.params)
		}
	}

	return mmUpdateParticipantGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.UpdateParticipantGroups
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockUpdateParticipantGroups {
	if mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Set")
	}

	if mmUpdateParticipantGroups.defaultExpectation == nil {
		mmUpdateParticipantGroups.defaultExpectation = &GroupDomainServiceMockUpdateParticipantGroupsExpectation{}
	}

	if mmUpdateParticipantGroups.defaultExpectation.params != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Expect")
	}

	if mmUpdateParticipantGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateParticipantGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateParticipantGroupsParamPtrs{}
	}
	mmUpdateParticipantGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateParticipantGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateParticipantGroups
}

// ExpectGroupIDParam2 sets up expected param groupID for GroupDomainService.UpdateParticipantGroups
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) ExpectGroupIDParam2(groupID int64) *mGroupDomainServiceMockUpdateParticipantGroups {
	if mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Set")
	}

	if mmUpdateParticipantGroups.defaultExpectation == nil {
		mmUpdateParticipantGroups.defaultExpectation = &GroupDomainServiceMockUpdateParticipantGroupsExpectation{}
	}

	if mmUpdateParticipantGroups.defaultExpectation.params != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Expect")
	}

	if mmUpdateParticipantGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateParticipantGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateParticipantGroupsParamPtrs{}
	}
	mmUpdateParticipantGroups.defaultExpectation.paramPtrs.groupID = &groupID
	mmUpdateParticipantGroups.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmUpdateParticipantGroups
}

// ExpectParticipantIDsParam3 sets up expected param participantIDs for GroupDomainService.UpdateParticipantGroups
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) ExpectParticipantIDsParam3(participantIDs []int64) *mGroupDomainServiceMockUpdateParticipantGroups {
	if mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Set")
	}

	if mmUpdateParticipantGroups.defaultExpectation == nil {
		mmUpdateParticipantGroups.defaultExpectation = &GroupDomainServiceMockUpdateParticipantGroupsExpectation{}
	}

	if mmUpdateParticipantGroups.defaultExpectation.params != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Expect")
	}

	if mmUpdateParticipantGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateParticipantGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateParticipantGroupsParamPtrs{}
	}
	mmUpdateParticipantGroups.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmUpdateParticipantGroups.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmUpdateParticipantGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UpdateParticipantGroups
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Inspect(f func(ctx context.Context, groupID int64, participantIDs []int64)) *mGroupDomainServiceMockUpdateParticipantGroups {
	if mmUpdateParticipantGroups.mock.inspectFuncUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UpdateParticipantGroups")
	}

	mmUpdateParticipantGroups.mock.inspectFuncUpdateParticipantGroups = f

	return mmUpdateParticipantGroups
}

// Return sets up results that will be returned by GroupDomainService.UpdateParticipantGroups
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Return(ia1 []int64, err error) *GroupDomainServiceMock {
	if mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Set")
	}

	if mmUpdateParticipantGroups.defaultExpectation == nil {
		mmUpdateParticipantGroups.defaultExpectation = &GroupDomainServiceMockUpdateParticipantGroupsExpectation{mock: mmUpdateParticipantGroups.mock}
	}
	mmUpdateParticipantGroups.defaultExpectation.results = &GroupDomainServiceMockUpdateParticipantGroupsResults{ia1, err}
	mmUpdateParticipantGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateParticipantGroups.mock
}

// Set uses given function f to mock the GroupDomainService.UpdateParticipantGroups method
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Set(f func(ctx context.Context, groupID int64, participantIDs []int64) (ia1 []int64, err error)) *GroupDomainServiceMock {
	if mmUpdateParticipantGroups.defaultExpectation != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UpdateParticipantGroups method")
	}

	if len(mmUpdateParticipantGroups.expectations) > 0 {
		mmUpdateParticipantGroups.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UpdateParticipantGroups method")
	}

	mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups = f
	mmUpdateParticipantGroups.mock.funcUpdateParticipantGroupsOrigin = minimock.CallerInfo(1)
	return mmUpdateParticipantGroups.mock
}

// When sets expectation for the GroupDomainService.UpdateParticipantGroups which will trigger the result defined by the following
// Then helper
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) When(ctx context.Context, groupID int64, participantIDs []int64) *GroupDomainServiceMockUpdateParticipantGroupsExpectation {
	if mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateParticipantGroups mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateParticipantGroupsExpectation{
		mock:               mmUpdateParticipantGroups.mock,
		params:             &GroupDomainServiceMockUpdateParticipantGroupsParams{ctx, groupID, participantIDs},
		expectationOrigins: GroupDomainServiceMockUpdateParticipantGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateParticipantGroups.expectations = append(mmUpdateParticipantGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UpdateParticipantGroups return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateParticipantGroupsExpectation) Then(ia1 []int64, err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateParticipantGroupsResults{ia1, err}
	return e.mock
}

// Times sets number of times GroupDomainService.UpdateParticipantGroups should be invoked
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Times(n uint64) *mGroupDomainServiceMockUpdateParticipantGroups {
	if n == 0 {
		mmUpdateParticipantGroups.mock.t.Fatalf("Times of GroupDomainServiceMock.UpdateParticipantGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateParticipantGroups.expectedInvocations, n)
	mmUpdateParticipantGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateParticipantGroups
}

func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) invocationsDone() bool {
	if len(mmUpdateParticipantGroups.expectations) == 0 && mmUpdateParticipantGroups.defaultExpectation == nil && mmUpdateParticipantGroups.mock.funcUpdateParticipantGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateParticipantGroups.mock.afterUpdateParticipantGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateParticipantGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateParticipantGroups implements mm_service.GroupDomainService
func (mmUpdateParticipantGroups *GroupDomainServiceMock) UpdateParticipantGroups(ctx context.Context, groupID int64, participantIDs []int64) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmUpdateParticipantGroups.beforeUpdateParticipantGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateParticipantGroups.afterUpdateParticipantGroupsCounter, 1)

	mmUpdateParticipantGroups.t.Helper()

	if mmUpdateParticipantGroups.inspectFuncUpdateParticipantGroups != nil {
		mmUpdateParticipantGroups.inspectFuncUpdateParticipantGroups(ctx, groupID, participantIDs)
	}

	mm_params := GroupDomainServiceMockUpdateParticipantGroupsParams{ctx, groupID, participantIDs}

	// Record call args
	mmUpdateParticipantGroups.UpdateParticipantGroupsMock.mutex.Lock()
	mmUpdateParticipantGroups.UpdateParticipantGroupsMock.callArgs = append(mmUpdateParticipantGroups.UpdateParticipantGroupsMock.callArgs, &mm_params)
	mmUpdateParticipantGroups.UpdateParticipantGroupsMock.mutex.Unlock()

	for _, e := range mmUpdateParticipantGroups.UpdateParticipantGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateParticipantGroupsParams{ctx, groupID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateParticipantGroups.t.Errorf("GroupDomainServiceMock.UpdateParticipantGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmUpdateParticipantGroups.t.Errorf("GroupDomainServiceMock.UpdateParticipantGroups got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmUpdateParticipantGroups.t.Errorf("GroupDomainServiceMock.UpdateParticipantGroups got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateParticipantGroups.t.Errorf("GroupDomainServiceMock.UpdateParticipantGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateParticipantGroups.UpdateParticipantGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateParticipantGroups.t.Fatal("No results are set for the GroupDomainServiceMock.UpdateParticipantGroups")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmUpdateParticipantGroups.funcUpdateParticipantGroups != nil {
		return mmUpdateParticipantGroups.funcUpdateParticipantGroups(ctx, groupID, participantIDs)
	}
	mmUpdateParticipantGroups.t.Fatalf("Unexpected call to GroupDomainServiceMock.UpdateParticipantGroups. %v %v %v", ctx, groupID, participantIDs)
	return
}

// UpdateParticipantGroupsAfterCounter returns a count of finished GroupDomainServiceMock.UpdateParticipantGroups invocations
func (mmUpdateParticipantGroups *GroupDomainServiceMock) UpdateParticipantGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateParticipantGroups.afterUpdateParticipantGroupsCounter)
}

// UpdateParticipantGroupsBeforeCounter returns a count of GroupDomainServiceMock.UpdateParticipantGroups invocations
func (mmUpdateParticipantGroups *GroupDomainServiceMock) UpdateParticipantGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateParticipantGroups.beforeUpdateParticipantGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UpdateParticipantGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateParticipantGroups *mGroupDomainServiceMockUpdateParticipantGroups) Calls() []*GroupDomainServiceMockUpdateParticipantGroupsParams {
	mmUpdateParticipantGroups.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateParticipantGroupsParams, len(mmUpdateParticipantGroups.callArgs))
	copy(argCopy, mmUpdateParticipantGroups.callArgs)

	mmUpdateParticipantGroups.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateParticipantGroupsDone returns true if the count of the UpdateParticipantGroups invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateParticipantGroupsDone() bool {
	if m.UpdateParticipantGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateParticipantGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateParticipantGroupsMock.invocationsDone()
}

// MinimockUpdateParticipantGroupsInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateParticipantGroupsInspect() {
	for _, e := range m.UpdateParticipantGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateParticipantGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateParticipantGroupsCounter := mm_atomic.LoadUint64(&m.afterUpdateParticipantGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateParticipantGroupsMock.defaultExpectation != nil && afterUpdateParticipantGroupsCounter < 1 {
		if m.UpdateParticipantGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateParticipantGroups at\n%s", m.UpdateParticipantGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateParticipantGroups at\n%s with params: %#v", m.UpdateParticipantGroupsMock.defaultExpectation.expectationOrigins.origin, *m.UpdateParticipantGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateParticipantGroups != nil && afterUpdateParticipantGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateParticipantGroups at\n%s", m.funcUpdateParticipantGroupsOrigin)
	}

	if !m.UpdateParticipantGroupsMock.invocationsDone() && afterUpdateParticipantGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UpdateParticipantGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateParticipantGroupsMock.expectedInvocations), m.UpdateParticipantGroupsMock.expectedInvocationsOrigin, afterUpdateParticipantGroupsCounter)
	}
}

type mGroupDomainServiceMockUpdateUserGroups struct {
	optional           bool
	mock               *GroupDomainServiceMock
	defaultExpectation *GroupDomainServiceMockUpdateUserGroupsExpectation
	expectations       []*GroupDomainServiceMockUpdateUserGroupsExpectation

	callArgs []*GroupDomainServiceMockUpdateUserGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// GroupDomainServiceMockUpdateUserGroupsExpectation specifies expectation struct of the GroupDomainService.UpdateUserGroups
type GroupDomainServiceMockUpdateUserGroupsExpectation struct {
	mock               *GroupDomainServiceMock
	params             *GroupDomainServiceMockUpdateUserGroupsParams
	paramPtrs          *GroupDomainServiceMockUpdateUserGroupsParamPtrs
	expectationOrigins GroupDomainServiceMockUpdateUserGroupsExpectationOrigins
	results            *GroupDomainServiceMockUpdateUserGroupsResults
	returnOrigin       string
	Counter            uint64
}

// GroupDomainServiceMockUpdateUserGroupsParams contains parameters of the GroupDomainService.UpdateUserGroups
type GroupDomainServiceMockUpdateUserGroupsParams struct {
	ctx    context.Context
	userID int64
	user   userentity.UserUpdateData
}

// GroupDomainServiceMockUpdateUserGroupsParamPtrs contains pointers to parameters of the GroupDomainService.UpdateUserGroups
type GroupDomainServiceMockUpdateUserGroupsParamPtrs struct {
	ctx    *context.Context
	userID *int64
	user   *userentity.UserUpdateData
}

// GroupDomainServiceMockUpdateUserGroupsResults contains results of the GroupDomainService.UpdateUserGroups
type GroupDomainServiceMockUpdateUserGroupsResults struct {
	err error
}

// GroupDomainServiceMockUpdateUserGroupsOrigins contains origins of expectations of the GroupDomainService.UpdateUserGroups
type GroupDomainServiceMockUpdateUserGroupsExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
	originUser   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Optional() *mGroupDomainServiceMockUpdateUserGroups {
	mmUpdateUserGroups.optional = true
	return mmUpdateUserGroups
}

// Expect sets up expected params for GroupDomainService.UpdateUserGroups
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Expect(ctx context.Context, userID int64, user userentity.UserUpdateData) *mGroupDomainServiceMockUpdateUserGroups {
	if mmUpdateUserGroups.mock.funcUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Set")
	}

	if mmUpdateUserGroups.defaultExpectation == nil {
		mmUpdateUserGroups.defaultExpectation = &GroupDomainServiceMockUpdateUserGroupsExpectation{}
	}

	if mmUpdateUserGroups.defaultExpectation.paramPtrs != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by ExpectParams functions")
	}

	mmUpdateUserGroups.defaultExpectation.params = &GroupDomainServiceMockUpdateUserGroupsParams{ctx, userID, user}
	mmUpdateUserGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateUserGroups.expectations {
		if minimock.Equal(e.params, mmUpdateUserGroups.defaultExpectation.params) {
			mmUpdateUserGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateUserGroups.defaultExpectation.params)
		}
	}

	return mmUpdateUserGroups
}

// ExpectCtxParam1 sets up expected param ctx for GroupDomainService.UpdateUserGroups
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) ExpectCtxParam1(ctx context.Context) *mGroupDomainServiceMockUpdateUserGroups {
	if mmUpdateUserGroups.mock.funcUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Set")
	}

	if mmUpdateUserGroups.defaultExpectation == nil {
		mmUpdateUserGroups.defaultExpectation = &GroupDomainServiceMockUpdateUserGroupsExpectation{}
	}

	if mmUpdateUserGroups.defaultExpectation.params != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Expect")
	}

	if mmUpdateUserGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateUserGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateUserGroupsParamPtrs{}
	}
	mmUpdateUserGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateUserGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateUserGroups
}

// ExpectUserIDParam2 sets up expected param userID for GroupDomainService.UpdateUserGroups
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) ExpectUserIDParam2(userID int64) *mGroupDomainServiceMockUpdateUserGroups {
	if mmUpdateUserGroups.mock.funcUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Set")
	}

	if mmUpdateUserGroups.defaultExpectation == nil {
		mmUpdateUserGroups.defaultExpectation = &GroupDomainServiceMockUpdateUserGroupsExpectation{}
	}

	if mmUpdateUserGroups.defaultExpectation.params != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Expect")
	}

	if mmUpdateUserGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateUserGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateUserGroupsParamPtrs{}
	}
	mmUpdateUserGroups.defaultExpectation.paramPtrs.userID = &userID
	mmUpdateUserGroups.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmUpdateUserGroups
}

// ExpectUserParam3 sets up expected param user for GroupDomainService.UpdateUserGroups
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) ExpectUserParam3(user userentity.UserUpdateData) *mGroupDomainServiceMockUpdateUserGroups {
	if mmUpdateUserGroups.mock.funcUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Set")
	}

	if mmUpdateUserGroups.defaultExpectation == nil {
		mmUpdateUserGroups.defaultExpectation = &GroupDomainServiceMockUpdateUserGroupsExpectation{}
	}

	if mmUpdateUserGroups.defaultExpectation.params != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Expect")
	}

	if mmUpdateUserGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateUserGroups.defaultExpectation.paramPtrs = &GroupDomainServiceMockUpdateUserGroupsParamPtrs{}
	}
	mmUpdateUserGroups.defaultExpectation.paramPtrs.user = &user
	mmUpdateUserGroups.defaultExpectation.expectationOrigins.originUser = minimock.CallerInfo(1)

	return mmUpdateUserGroups
}

// Inspect accepts an inspector function that has same arguments as the GroupDomainService.UpdateUserGroups
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Inspect(f func(ctx context.Context, userID int64, user userentity.UserUpdateData)) *mGroupDomainServiceMockUpdateUserGroups {
	if mmUpdateUserGroups.mock.inspectFuncUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("Inspect function is already set for GroupDomainServiceMock.UpdateUserGroups")
	}

	mmUpdateUserGroups.mock.inspectFuncUpdateUserGroups = f

	return mmUpdateUserGroups
}

// Return sets up results that will be returned by GroupDomainService.UpdateUserGroups
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Return(err error) *GroupDomainServiceMock {
	if mmUpdateUserGroups.mock.funcUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Set")
	}

	if mmUpdateUserGroups.defaultExpectation == nil {
		mmUpdateUserGroups.defaultExpectation = &GroupDomainServiceMockUpdateUserGroupsExpectation{mock: mmUpdateUserGroups.mock}
	}
	mmUpdateUserGroups.defaultExpectation.results = &GroupDomainServiceMockUpdateUserGroupsResults{err}
	mmUpdateUserGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateUserGroups.mock
}

// Set uses given function f to mock the GroupDomainService.UpdateUserGroups method
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Set(f func(ctx context.Context, userID int64, user userentity.UserUpdateData) (err error)) *GroupDomainServiceMock {
	if mmUpdateUserGroups.defaultExpectation != nil {
		mmUpdateUserGroups.mock.t.Fatalf("Default expectation is already set for the GroupDomainService.UpdateUserGroups method")
	}

	if len(mmUpdateUserGroups.expectations) > 0 {
		mmUpdateUserGroups.mock.t.Fatalf("Some expectations are already set for the GroupDomainService.UpdateUserGroups method")
	}

	mmUpdateUserGroups.mock.funcUpdateUserGroups = f
	mmUpdateUserGroups.mock.funcUpdateUserGroupsOrigin = minimock.CallerInfo(1)
	return mmUpdateUserGroups.mock
}

// When sets expectation for the GroupDomainService.UpdateUserGroups which will trigger the result defined by the following
// Then helper
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) When(ctx context.Context, userID int64, user userentity.UserUpdateData) *GroupDomainServiceMockUpdateUserGroupsExpectation {
	if mmUpdateUserGroups.mock.funcUpdateUserGroups != nil {
		mmUpdateUserGroups.mock.t.Fatalf("GroupDomainServiceMock.UpdateUserGroups mock is already set by Set")
	}

	expectation := &GroupDomainServiceMockUpdateUserGroupsExpectation{
		mock:               mmUpdateUserGroups.mock,
		params:             &GroupDomainServiceMockUpdateUserGroupsParams{ctx, userID, user},
		expectationOrigins: GroupDomainServiceMockUpdateUserGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateUserGroups.expectations = append(mmUpdateUserGroups.expectations, expectation)
	return expectation
}

// Then sets up GroupDomainService.UpdateUserGroups return parameters for the expectation previously defined by the When method
func (e *GroupDomainServiceMockUpdateUserGroupsExpectation) Then(err error) *GroupDomainServiceMock {
	e.results = &GroupDomainServiceMockUpdateUserGroupsResults{err}
	return e.mock
}

// Times sets number of times GroupDomainService.UpdateUserGroups should be invoked
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Times(n uint64) *mGroupDomainServiceMockUpdateUserGroups {
	if n == 0 {
		mmUpdateUserGroups.mock.t.Fatalf("Times of GroupDomainServiceMock.UpdateUserGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateUserGroups.expectedInvocations, n)
	mmUpdateUserGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateUserGroups
}

func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) invocationsDone() bool {
	if len(mmUpdateUserGroups.expectations) == 0 && mmUpdateUserGroups.defaultExpectation == nil && mmUpdateUserGroups.mock.funcUpdateUserGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateUserGroups.mock.afterUpdateUserGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateUserGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateUserGroups implements mm_service.GroupDomainService
func (mmUpdateUserGroups *GroupDomainServiceMock) UpdateUserGroups(ctx context.Context, userID int64, user userentity.UserUpdateData) (err error) {
	mm_atomic.AddUint64(&mmUpdateUserGroups.beforeUpdateUserGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateUserGroups.afterUpdateUserGroupsCounter, 1)

	mmUpdateUserGroups.t.Helper()

	if mmUpdateUserGroups.inspectFuncUpdateUserGroups != nil {
		mmUpdateUserGroups.inspectFuncUpdateUserGroups(ctx, userID, user)
	}

	mm_params := GroupDomainServiceMockUpdateUserGroupsParams{ctx, userID, user}

	// Record call args
	mmUpdateUserGroups.UpdateUserGroupsMock.mutex.Lock()
	mmUpdateUserGroups.UpdateUserGroupsMock.callArgs = append(mmUpdateUserGroups.UpdateUserGroupsMock.callArgs, &mm_params)
	mmUpdateUserGroups.UpdateUserGroupsMock.mutex.Unlock()

	for _, e := range mmUpdateUserGroups.UpdateUserGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.paramPtrs

		mm_got := GroupDomainServiceMockUpdateUserGroupsParams{ctx, userID, user}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateUserGroups.t.Errorf("GroupDomainServiceMock.UpdateUserGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmUpdateUserGroups.t.Errorf("GroupDomainServiceMock.UpdateUserGroups got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.user != nil && !minimock.Equal(*mm_want_ptrs.user, mm_got.user) {
				mmUpdateUserGroups.t.Errorf("GroupDomainServiceMock.UpdateUserGroups got unexpected parameter user, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.expectationOrigins.originUser, *mm_want_ptrs.user, mm_got.user, minimock.Diff(*mm_want_ptrs.user, mm_got.user))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateUserGroups.t.Errorf("GroupDomainServiceMock.UpdateUserGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateUserGroups.UpdateUserGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateUserGroups.t.Fatal("No results are set for the GroupDomainServiceMock.UpdateUserGroups")
		}
		return (*mm_results).err
	}
	if mmUpdateUserGroups.funcUpdateUserGroups != nil {
		return mmUpdateUserGroups.funcUpdateUserGroups(ctx, userID, user)
	}
	mmUpdateUserGroups.t.Fatalf("Unexpected call to GroupDomainServiceMock.UpdateUserGroups. %v %v %v", ctx, userID, user)
	return
}

// UpdateUserGroupsAfterCounter returns a count of finished GroupDomainServiceMock.UpdateUserGroups invocations
func (mmUpdateUserGroups *GroupDomainServiceMock) UpdateUserGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateUserGroups.afterUpdateUserGroupsCounter)
}

// UpdateUserGroupsBeforeCounter returns a count of GroupDomainServiceMock.UpdateUserGroups invocations
func (mmUpdateUserGroups *GroupDomainServiceMock) UpdateUserGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateUserGroups.beforeUpdateUserGroupsCounter)
}

// Calls returns a list of arguments used in each call to GroupDomainServiceMock.UpdateUserGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateUserGroups *mGroupDomainServiceMockUpdateUserGroups) Calls() []*GroupDomainServiceMockUpdateUserGroupsParams {
	mmUpdateUserGroups.mutex.RLock()

	argCopy := make([]*GroupDomainServiceMockUpdateUserGroupsParams, len(mmUpdateUserGroups.callArgs))
	copy(argCopy, mmUpdateUserGroups.callArgs)

	mmUpdateUserGroups.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateUserGroupsDone returns true if the count of the UpdateUserGroups invocations corresponds
// the number of defined expectations
func (m *GroupDomainServiceMock) MinimockUpdateUserGroupsDone() bool {
	if m.UpdateUserGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateUserGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateUserGroupsMock.invocationsDone()
}

// MinimockUpdateUserGroupsInspect logs each unmet expectation
func (m *GroupDomainServiceMock) MinimockUpdateUserGroupsInspect() {
	for _, e := range m.UpdateUserGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateUserGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateUserGroupsCounter := mm_atomic.LoadUint64(&m.afterUpdateUserGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateUserGroupsMock.defaultExpectation != nil && afterUpdateUserGroupsCounter < 1 {
		if m.UpdateUserGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateUserGroups at\n%s", m.UpdateUserGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateUserGroups at\n%s with params: %#v", m.UpdateUserGroupsMock.defaultExpectation.expectationOrigins.origin, *m.UpdateUserGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateUserGroups != nil && afterUpdateUserGroupsCounter < 1 {
		m.t.Errorf("Expected call to GroupDomainServiceMock.UpdateUserGroups at\n%s", m.funcUpdateUserGroupsOrigin)
	}

	if !m.UpdateUserGroupsMock.invocationsDone() && afterUpdateUserGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to GroupDomainServiceMock.UpdateUserGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateUserGroupsMock.expectedInvocations), m.UpdateUserGroupsMock.expectedInvocationsOrigin, afterUpdateUserGroupsCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *GroupDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockAssignToRolesInspect()

			m.MinimockCreateInspect()

			m.MinimockCreateAsAdminInspect()

			m.MinimockDeleteInspect()

			m.MinimockDeleteAsAdminInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllAsAdminInspect()

			m.MinimockGetAvailableSystemGroupsInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByGroupIDAndProductIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByParticipantIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByRoleIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetGroupsWithCategoryStatsInspect()

			m.MinimockGetParticipantGroupsWithProductByUserIDInspect()

			m.MinimockGetSystemGroupsWithStatsInspect()

			m.MinimockGetWithCountsByProductIDInspect()

			m.MinimockGetWithProductByUserIDInspect()

			m.MinimockGetWithProductsByRoleIDInspect()

			m.MinimockGetWithStatsByProductIDInspect()

			m.MinimockUnassignFromRolesInspect()

			m.MinimockUpdateInspect()

			m.MinimockUpdateByGroupFullInspect()

			m.MinimockUpdateCategoryStatesInspect()

			m.MinimockUpdateLinksWithRolesInspect()

			m.MinimockUpdateLinksWithUsersInspect()

			m.MinimockUpdateParticipantGroupsInspect()

			m.MinimockUpdateUserGroupsInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *GroupDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *GroupDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockAssignToRolesDone() &&
		m.MinimockCreateDone() &&
		m.MinimockCreateAsAdminDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockDeleteAsAdminDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllAsAdminDone() &&
		m.MinimockGetAvailableSystemGroupsDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByGroupIDAndProductIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByParticipantIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByRoleIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetGroupsWithCategoryStatsDone() &&
		m.MinimockGetParticipantGroupsWithProductByUserIDDone() &&
		m.MinimockGetSystemGroupsWithStatsDone() &&
		m.MinimockGetWithCountsByProductIDDone() &&
		m.MinimockGetWithProductByUserIDDone() &&
		m.MinimockGetWithProductsByRoleIDDone() &&
		m.MinimockGetWithStatsByProductIDDone() &&
		m.MinimockUnassignFromRolesDone() &&
		m.MinimockUpdateDone() &&
		m.MinimockUpdateByGroupFullDone() &&
		m.MinimockUpdateCategoryStatesDone() &&
		m.MinimockUpdateLinksWithRolesDone() &&
		m.MinimockUpdateLinksWithUsersDone() &&
		m.MinimockUpdateParticipantGroupsDone() &&
		m.MinimockUpdateUserGroupsDone()
}
