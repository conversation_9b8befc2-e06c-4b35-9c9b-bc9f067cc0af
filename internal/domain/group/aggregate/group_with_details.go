package aggregate

import (
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

type GroupWithDetails struct {
	groupentity.Group
	Product *productentity.Product
	Users   []userentity.UserWithProduct
	Roles   []roleentity.RoleWithProduct
}
