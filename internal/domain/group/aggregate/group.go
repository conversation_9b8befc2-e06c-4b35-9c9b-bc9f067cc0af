package aggregate

type GroupAggregate struct {
	categoryGroupLinks map[int64]map[int64]struct{}
}

func NewGroupAggregate() *GroupAggregate {
	return &GroupAggregate{
		categoryGroupLinks: make(map[int64]map[int64]struct{}),
	}
}

func (a *GroupAggregate) CacheCategoryGroupLinks(categoryGroupLinks map[int64]map[int64]struct{}) {
	a.categoryGroupLinks = categoryGroupLinks
}

func (a *GroupAggregate) HasCategoryGroupLink(categoryID int64, groupID int64) bool {
	_, ok := a.categoryGroupLinks[categoryID][groupID]
	return ok
}
