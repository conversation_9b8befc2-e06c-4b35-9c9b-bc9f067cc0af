package entity

import (
	"time"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

type Product struct {
	ID          int64
	IID         string
	TechName    string
	Name        string
	Description string
	CreatorID   int64
	ActiveFlg   bool
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time
}

func (p Product) Status() constants.ProductStatus {
	if p.ActiveFlg {
		return constants.ProductStatusActive
	}
	return constants.ProductStatusArchive
}

func (p Product) ProductOrNil() *Product {
	if p.ID != 0 {
		return &p
	}
	return nil
}

func (p Product) ProductBasic() ProductBasic {
	return ProductBasic{
		ID:       p.ID,
		IID:      entity.StringPtrOrNil(p.IID),
		TechName: p.TechName,
		Name:     p.Name,
	}
}

type Products []Product

func (p Products) IDs() []int64 {
	ids := make([]int64, len(p))
	for i, product := range p {
		ids[i] = product.ID
	}
	return ids
}

type ProductCreateData struct {
	IID       string
	TechName  string
	Name      string
	CreatorID int64
}

type ProductUpdateData struct {
	ID          int64
	IID         *string
	TechName    *string
	Name        *string
	Description *string
	ActiveFlg   *bool
	DeletedAt   *time.Time
}

type ProductFiltersData struct {
	IDs  *[]int64
	Type *string
}

type AdminProduct struct {
	Product
	Status           constants.ProductStatus
	ParticipantCount int64
	Owners           []Owner
}

type Owner struct {
	ProductID     int64
	ParticipantID int64
	UserID        int64
	FullName      string
	Email         string
}

type AdminProductUpdateData struct {
	ProductUpdateData
	Status         *constants.ProductStatus
	OwnerEmails    *[]string
	RoleIDs        *[]int64
	GroupIDs       *[]int64
	ParticipantIDs *[]string
}

type ProductBasic struct {
	ID       int64
	IID      *string
	TechName string
	Name     string
}

func (p ProductBasic) ProductOrNil() *ProductBasic {
	if p.ID != 0 {
		return &p
	}
	return nil
}
