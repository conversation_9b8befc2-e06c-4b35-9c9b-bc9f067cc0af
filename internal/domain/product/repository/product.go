package repository

import (
	"context"

	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

//go:generate minimock -i ProductPrimeDB -o ../mocks/product_prime_db_mock.go -s _mock.go
type ProductPrimeDB interface {
	Create(product productentity.ProductCreateData, ownerIDs []int64) (productentity.Product, error)
	GetAll() ([]productentity.Product, error)
	GetByID(id int64) (productentity.Product, error)
	GetByIID(iid string) (productentity.Product, error)
	GetByUserID(userID int64) ([]productentity.Product, error)
	Update(data productentity.ProductUpdateData) (productentity.Product, error)
}

//go:generate minimock -i ProductCache -o ../mocks/product_cache_mock.go -s _mock.go
type ProductCache interface {
	GetProduct(ctx context.Context, id int64) (productentity.Product, error)
	GetProducts(ctx context.Context) ([]productentity.Product, error)
	SetProduct(ctx context.Context, product productentity.Product) error
	SetProducts(ctx context.Context, products []productentity.Product) error
	DeleteProduct(ctx context.Context, id int64) error
	DeleteProducts(ctx context.Context, ids []int64) error
}
