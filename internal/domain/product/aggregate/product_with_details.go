package aggregate

import (
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
)

type ProductWithDetails struct {
	productentity.Product
	Status           constants.ProductStatus
	ParticipantCount int64
	Owners           []productentity.Owner
	Participants     []participantentity.ParticipantShort
	Roles            []roleentity.AdminRole
	Groups           []groupentity.AdminGroup
}
