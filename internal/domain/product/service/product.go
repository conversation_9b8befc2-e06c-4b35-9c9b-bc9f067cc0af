package service

import (
	"context"
	"errors"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	identityproviderrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	rolerepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

//go:generate minimock -i ProductDomainService -o ../mocks/product_domain_service_mock.go -s _mock.go
type ProductDomainService interface {
	Create(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string) (productentity.Product, error)
	GetAll() ([]productentity.Product, error)
	GetAllAsAdmin() ([]productentity.AdminProduct, error)
	GetBasicByUserID(userID int64) ([]productentity.ProductBasic, error)
	GetByID(id int64) (productentity.Product, error)
	GetByIID(iid string) (productentity.Product, error)
	GetByUserID(userID int64) ([]productentity.Product, error)
	GetOwnersByProductIDs(productIDs []int64) (map[int64][]productentity.Owner, error)
	Update(data productentity.ProductUpdateData) (productentity.Product, error)
	UpdateLinksWithParticipants(ctx context.Context, productID int64, participantIDs []string) error
	RemoveLinksWithGroups(ctx context.Context, productID int64, groupIDs []int64) error
	RemoveLinksWithRoles(ctx context.Context, productID int64, roleIDs []int64) error
}

type productDomainService struct {
	identityProvider identityproviderrepository.IdentityProvider
	participant      participantrepository.ParticipantPrimeDB
	product          productrepository.ProductPrimeDB
	user             userrepository.UserPrimeDB
	role             rolerepository.RolePrimeDB
	group            grouprepository.GroupPrimeDB
}

func NewProductDomainService(
	identityProvider identityproviderrepository.IdentityProvider,
	participant participantrepository.ParticipantPrimeDB,
	product productrepository.ProductPrimeDB,
	user userrepository.UserPrimeDB,
	role rolerepository.RolePrimeDB,
	group grouprepository.GroupPrimeDB,
) ProductDomainService {
	return &productDomainService{
		identityProvider: identityProvider,
		participant:      participant,
		product:          product,
		user:             user,
		role:             role,
		group:            group,
	}
}

func (s *productDomainService) Create(ctx context.Context, product productentity.ProductCreateData, ownerEmails []string) (productentity.Product, error) {

	var (
		ownerIDs []int64
		err      error
	)

	for _, ownerEmail := range ownerEmails {
		user, err := s.user.GetByEmail(ownerEmail)
		if err != nil {

			// Check if the error is due to the user not being found
			if !errkit.IsNotFoundError(err) {
				return productentity.Product{}, err
			}

			// Fetch user details from Keycloak
			userKC, errKC := s.identityProvider.GetUsersBySearch(ownerEmail)
			if errKC != nil {
				return productentity.Product{}, errKC
			}

			if len(userKC) == 0 {
				return productentity.Product{}, errors.New("no users found in Keycloak with the provided email")
			}

			var photo []byte
			if userKC[0].Photo != "" {
				photo = []byte(userKC[0].Photo)
			}

			// Create new user based on Keycloak data
			userNew, errNew := s.user.Create(ctx, userentity.User{
				CategoryID: constants.CategoryDefaultID,
				Email:      userKC[0].Email,
				FullName:   userKC[0].FullName,
				Position:   userKC[0].Position,
				Photo:      &photo,
			})
			if errNew != nil {
				return productentity.Product{}, errNew
			}

			user = userNew
		}

		ownerIDs = append(ownerIDs, user.ID)
	}

	if len(ownerIDs) == 0 {
		ownerIDs = append(ownerIDs, product.CreatorID)
	}

	newProd, err := s.product.Create(
		productentity.ProductCreateData{
			IID:       product.IID,
			TechName:  product.TechName,
			Name:      product.Name,
			CreatorID: product.CreatorID,
		},
		ownerIDs,
	)
	if err != nil {
		return productentity.Product{}, err
	}

	return newProd, nil
}

func (s *productDomainService) GetAll() ([]productentity.Product, error) {
	return s.product.GetAll()
}

func (s *productDomainService) GetAllAsAdmin() ([]productentity.AdminProduct, error) {

	products, err := s.product.GetAll()
	if err != nil {
		return nil, err
	}

	owners, err := s.GetOwnersByProductIDs(productentity.Products(products).IDs())
	if err != nil {
		return nil, err
	}

	adminProducts := make([]productentity.AdminProduct, 0, len(products))
	for _, product := range products {
		participants, err := s.participant.GetByProductID(product.ID)
		if err != nil {
			return nil, err
		}

		adminProducts = append(adminProducts, productentity.AdminProduct{
			Product:          product,
			Status:           product.Status(),
			ParticipantCount: int64(len(participants)),
			Owners:           owners[product.ID],
		})
	}

	return adminProducts, nil
}

func (s *productDomainService) GetBasicByUserID(userID int64) ([]productentity.ProductBasic, error) {
	products, err := s.product.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	productsBasic := make([]productentity.ProductBasic, 0, len(products))
	for _, product := range products {
		productsBasic = append(productsBasic, product.ProductBasic())
	}

	return productsBasic, nil
}

func (s *productDomainService) GetByID(id int64) (productentity.Product, error) {
	return s.product.GetByID(id)
}

func (s *productDomainService) GetByIID(iid string) (productentity.Product, error) {
	return s.product.GetByIID(iid)
}

func (s *productDomainService) GetByUserID(userID int64) ([]productentity.Product, error) {
	return s.product.GetByUserID(userID)
}

func (s *productDomainService) GetOwnersByProductIDs(productIDs []int64) (map[int64][]productentity.Owner, error) {
	allOwners := make(map[int64][]productentity.Owner)
	for _, productID := range productIDs {
		productOwners, err := s.participant.GetOwnersByProductID(productID)
		if err != nil {
			return nil, err
		}
		allOwners[productID] = append(allOwners[productID], productOwners...)
	}
	return allOwners, nil
}

func (s *productDomainService) Update(data productentity.ProductUpdateData) (productentity.Product, error) {
	return s.product.Update(data)
}

func (s *productDomainService) UpdateLinksWithParticipants(ctx context.Context, productID int64, participantIDs []string) error {
	_, err := s.GetByID(productID)
	if err != nil {
		return err
	}

	newParticipants := make([]userentity.User, 0)

	for _, email := range participantIDs {
		user, err := s.user.GetByEmail(email)
		if err != nil {
			return err
		}
		newParticipants = append(newParticipants, user)
	}

	oldParticipantsMap := make(map[int64]struct{}, 0)

	currentParticipants, err := s.participant.GetByProductID(productID)
	if err != nil {
		return err
	}

	for _, o := range currentParticipants {
		oldParticipantsMap[o.UserID] = struct{}{}
	}

	participantsToAdd := make([]userentity.User, 0)

	for _, p := range newParticipants {
		if _, exist := oldParticipantsMap[p.ID]; !exist {
			participantsToAdd = append(participantsToAdd, p)
		}
	}

	newIDs := make(map[int64]struct{})
	for _, p := range newParticipants {
		newIDs[p.ID] = struct{}{}
	}

	toDelete := make([]int64, 0)
	for _, o := range currentParticipants {
		if _, exists := newIDs[o.UserID]; !exists {
			toDelete = append(toDelete, o.UserID)
		}
	}
	toDelete, err = s.CheckOnlyOneOwner(productID, toDelete)
	if err != nil {
		return err
	}

	err = s.participant.DeleteByUserIDsAndProductID(ctx, productID, toDelete)
	if err != nil {
		return err
	}

	for _, p := range participantsToAdd {
		_, err = s.participant.Create(ctx, participantentity.ParticipantCreateData{
			ProductID: productID,
			UserID:    p.ID,
			Email:     p.Email,
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *productDomainService) CheckOnlyOneOwner(productID int64, toDelete []int64) ([]int64, error) {
	owners, err := s.participant.GetOwnersByProductID(productID)
	if err != nil {
		return nil, err
	}

	ownerSet := make(map[int64]struct{}, len(owners))
	for _, o := range owners {
		ownerSet[o.UserID] = struct{}{}
	}

	deleteCount := 0
	for _, id := range toDelete {
		if _, exists := ownerSet[id]; exists {
			deleteCount++
		}
	}

	if deleteCount == len(owners) {
		var oldest *participantentity.Participant
		participants, err := s.participant.GetByProductID(productID)
		if err != nil {
			return nil, err
		}

		for _, p := range participants {
			if _, isOwner := ownerSet[p.UserID]; !isOwner {
				continue
			}

			if oldest == nil || p.CreatedAt.UTC().Before(oldest.CreatedAt.UTC()) {
				oldest = &p
			}
		}

		var filtered []int64
		oldestID := oldest.UserID
		for _, id := range toDelete {
			if id != oldestID {
				filtered = append(filtered, id)
			}
		}

		return filtered, nil
	}

	return toDelete, nil
}

func (s *productDomainService) RemoveLinksWithGroups(ctx context.Context, productID int64, groupIDs []int64) error {
	groupProduct, err := s.group.GetByProductID(productID)
	if err != nil {
		return err
	}

	GroupIDs := make(map[int64]struct{}, len(groupIDs))

	for _, group := range groupIDs {
		GroupIDs[group] = struct{}{}
	}

	groupToDelete := make([]int64, 0)

	for _, gP := range groupProduct {
		if _, exist := GroupIDs[gP.ID]; !exist {
			groupToDelete = append(groupToDelete, gP.ID)
		}
	}

	if err = s.group.DeactivateByIDs(groupToDelete); err != nil {
		return err
	}

	return nil
}

func (s *productDomainService) RemoveLinksWithRoles(ctx context.Context, productID int64, roleIDs []int64) error {
	rolesProduct, err := s.role.GetByProductID(productID)
	if err != nil {
		return err
	}

	RoleIDs := make(map[int64]struct{}, len(roleIDs))
	for _, role := range roleIDs {
		RoleIDs[role] = struct{}{}
	}

	roleToDelete := make([]int64, 0)
	for _, rP := range rolesProduct {
		if _, exist := RoleIDs[rP.ID]; !exist {
			roleToDelete = append(roleToDelete, rP.ID)
		}
	}

	if err = s.role.DeactivateByIDs(roleToDelete); err != nil {
		return err
	}

	return nil
}
