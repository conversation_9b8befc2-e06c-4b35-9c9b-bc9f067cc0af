// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository.ProductCache -o product_cache_mock.go -n ProductCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"github.com/gojuno/minimock/v3"
)

// ProductCacheMock implements mm_repository.ProductCache
type ProductCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcDeleteProduct          func(ctx context.Context, id int64) (err error)
	funcDeleteProductOrigin    string
	inspectFuncDeleteProduct   func(ctx context.Context, id int64)
	afterDeleteProductCounter  uint64
	beforeDeleteProductCounter uint64
	DeleteProductMock          mProductCacheMockDeleteProduct

	funcDeleteProducts          func(ctx context.Context, ids []int64) (err error)
	funcDeleteProductsOrigin    string
	inspectFuncDeleteProducts   func(ctx context.Context, ids []int64)
	afterDeleteProductsCounter  uint64
	beforeDeleteProductsCounter uint64
	DeleteProductsMock          mProductCacheMockDeleteProducts

	funcGetProduct          func(ctx context.Context, id int64) (p1 productentity.Product, err error)
	funcGetProductOrigin    string
	inspectFuncGetProduct   func(ctx context.Context, id int64)
	afterGetProductCounter  uint64
	beforeGetProductCounter uint64
	GetProductMock          mProductCacheMockGetProduct

	funcGetProducts          func(ctx context.Context) (pa1 []productentity.Product, err error)
	funcGetProductsOrigin    string
	inspectFuncGetProducts   func(ctx context.Context)
	afterGetProductsCounter  uint64
	beforeGetProductsCounter uint64
	GetProductsMock          mProductCacheMockGetProducts

	funcSetProduct          func(ctx context.Context, product productentity.Product) (err error)
	funcSetProductOrigin    string
	inspectFuncSetProduct   func(ctx context.Context, product productentity.Product)
	afterSetProductCounter  uint64
	beforeSetProductCounter uint64
	SetProductMock          mProductCacheMockSetProduct

	funcSetProducts          func(ctx context.Context, products []productentity.Product) (err error)
	funcSetProductsOrigin    string
	inspectFuncSetProducts   func(ctx context.Context, products []productentity.Product)
	afterSetProductsCounter  uint64
	beforeSetProductsCounter uint64
	SetProductsMock          mProductCacheMockSetProducts
}

// NewProductCacheMock returns a mock for mm_repository.ProductCache
func NewProductCacheMock(t minimock.Tester) *ProductCacheMock {
	m := &ProductCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.DeleteProductMock = mProductCacheMockDeleteProduct{mock: m}
	m.DeleteProductMock.callArgs = []*ProductCacheMockDeleteProductParams{}

	m.DeleteProductsMock = mProductCacheMockDeleteProducts{mock: m}
	m.DeleteProductsMock.callArgs = []*ProductCacheMockDeleteProductsParams{}

	m.GetProductMock = mProductCacheMockGetProduct{mock: m}
	m.GetProductMock.callArgs = []*ProductCacheMockGetProductParams{}

	m.GetProductsMock = mProductCacheMockGetProducts{mock: m}
	m.GetProductsMock.callArgs = []*ProductCacheMockGetProductsParams{}

	m.SetProductMock = mProductCacheMockSetProduct{mock: m}
	m.SetProductMock.callArgs = []*ProductCacheMockSetProductParams{}

	m.SetProductsMock = mProductCacheMockSetProducts{mock: m}
	m.SetProductsMock.callArgs = []*ProductCacheMockSetProductsParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProductCacheMockDeleteProduct struct {
	optional           bool
	mock               *ProductCacheMock
	defaultExpectation *ProductCacheMockDeleteProductExpectation
	expectations       []*ProductCacheMockDeleteProductExpectation

	callArgs []*ProductCacheMockDeleteProductParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductCacheMockDeleteProductExpectation specifies expectation struct of the ProductCache.DeleteProduct
type ProductCacheMockDeleteProductExpectation struct {
	mock               *ProductCacheMock
	params             *ProductCacheMockDeleteProductParams
	paramPtrs          *ProductCacheMockDeleteProductParamPtrs
	expectationOrigins ProductCacheMockDeleteProductExpectationOrigins
	results            *ProductCacheMockDeleteProductResults
	returnOrigin       string
	Counter            uint64
}

// ProductCacheMockDeleteProductParams contains parameters of the ProductCache.DeleteProduct
type ProductCacheMockDeleteProductParams struct {
	ctx context.Context
	id  int64
}

// ProductCacheMockDeleteProductParamPtrs contains pointers to parameters of the ProductCache.DeleteProduct
type ProductCacheMockDeleteProductParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// ProductCacheMockDeleteProductResults contains results of the ProductCache.DeleteProduct
type ProductCacheMockDeleteProductResults struct {
	err error
}

// ProductCacheMockDeleteProductOrigins contains origins of expectations of the ProductCache.DeleteProduct
type ProductCacheMockDeleteProductExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Optional() *mProductCacheMockDeleteProduct {
	mmDeleteProduct.optional = true
	return mmDeleteProduct
}

// Expect sets up expected params for ProductCache.DeleteProduct
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Expect(ctx context.Context, id int64) *mProductCacheMockDeleteProduct {
	if mmDeleteProduct.mock.funcDeleteProduct != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Set")
	}

	if mmDeleteProduct.defaultExpectation == nil {
		mmDeleteProduct.defaultExpectation = &ProductCacheMockDeleteProductExpectation{}
	}

	if mmDeleteProduct.defaultExpectation.paramPtrs != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by ExpectParams functions")
	}

	mmDeleteProduct.defaultExpectation.params = &ProductCacheMockDeleteProductParams{ctx, id}
	mmDeleteProduct.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteProduct.expectations {
		if minimock.Equal(e.params, mmDeleteProduct.defaultExpectation.params) {
			mmDeleteProduct.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteProduct.defaultExpectation.params)
		}
	}

	return mmDeleteProduct
}

// ExpectCtxParam1 sets up expected param ctx for ProductCache.DeleteProduct
func (mmDeleteProduct *mProductCacheMockDeleteProduct) ExpectCtxParam1(ctx context.Context) *mProductCacheMockDeleteProduct {
	if mmDeleteProduct.mock.funcDeleteProduct != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Set")
	}

	if mmDeleteProduct.defaultExpectation == nil {
		mmDeleteProduct.defaultExpectation = &ProductCacheMockDeleteProductExpectation{}
	}

	if mmDeleteProduct.defaultExpectation.params != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Expect")
	}

	if mmDeleteProduct.defaultExpectation.paramPtrs == nil {
		mmDeleteProduct.defaultExpectation.paramPtrs = &ProductCacheMockDeleteProductParamPtrs{}
	}
	mmDeleteProduct.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteProduct.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteProduct
}

// ExpectIdParam2 sets up expected param id for ProductCache.DeleteProduct
func (mmDeleteProduct *mProductCacheMockDeleteProduct) ExpectIdParam2(id int64) *mProductCacheMockDeleteProduct {
	if mmDeleteProduct.mock.funcDeleteProduct != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Set")
	}

	if mmDeleteProduct.defaultExpectation == nil {
		mmDeleteProduct.defaultExpectation = &ProductCacheMockDeleteProductExpectation{}
	}

	if mmDeleteProduct.defaultExpectation.params != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Expect")
	}

	if mmDeleteProduct.defaultExpectation.paramPtrs == nil {
		mmDeleteProduct.defaultExpectation.paramPtrs = &ProductCacheMockDeleteProductParamPtrs{}
	}
	mmDeleteProduct.defaultExpectation.paramPtrs.id = &id
	mmDeleteProduct.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDeleteProduct
}

// Inspect accepts an inspector function that has same arguments as the ProductCache.DeleteProduct
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Inspect(f func(ctx context.Context, id int64)) *mProductCacheMockDeleteProduct {
	if mmDeleteProduct.mock.inspectFuncDeleteProduct != nil {
		mmDeleteProduct.mock.t.Fatalf("Inspect function is already set for ProductCacheMock.DeleteProduct")
	}

	mmDeleteProduct.mock.inspectFuncDeleteProduct = f

	return mmDeleteProduct
}

// Return sets up results that will be returned by ProductCache.DeleteProduct
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Return(err error) *ProductCacheMock {
	if mmDeleteProduct.mock.funcDeleteProduct != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Set")
	}

	if mmDeleteProduct.defaultExpectation == nil {
		mmDeleteProduct.defaultExpectation = &ProductCacheMockDeleteProductExpectation{mock: mmDeleteProduct.mock}
	}
	mmDeleteProduct.defaultExpectation.results = &ProductCacheMockDeleteProductResults{err}
	mmDeleteProduct.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteProduct.mock
}

// Set uses given function f to mock the ProductCache.DeleteProduct method
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Set(f func(ctx context.Context, id int64) (err error)) *ProductCacheMock {
	if mmDeleteProduct.defaultExpectation != nil {
		mmDeleteProduct.mock.t.Fatalf("Default expectation is already set for the ProductCache.DeleteProduct method")
	}

	if len(mmDeleteProduct.expectations) > 0 {
		mmDeleteProduct.mock.t.Fatalf("Some expectations are already set for the ProductCache.DeleteProduct method")
	}

	mmDeleteProduct.mock.funcDeleteProduct = f
	mmDeleteProduct.mock.funcDeleteProductOrigin = minimock.CallerInfo(1)
	return mmDeleteProduct.mock
}

// When sets expectation for the ProductCache.DeleteProduct which will trigger the result defined by the following
// Then helper
func (mmDeleteProduct *mProductCacheMockDeleteProduct) When(ctx context.Context, id int64) *ProductCacheMockDeleteProductExpectation {
	if mmDeleteProduct.mock.funcDeleteProduct != nil {
		mmDeleteProduct.mock.t.Fatalf("ProductCacheMock.DeleteProduct mock is already set by Set")
	}

	expectation := &ProductCacheMockDeleteProductExpectation{
		mock:               mmDeleteProduct.mock,
		params:             &ProductCacheMockDeleteProductParams{ctx, id},
		expectationOrigins: ProductCacheMockDeleteProductExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteProduct.expectations = append(mmDeleteProduct.expectations, expectation)
	return expectation
}

// Then sets up ProductCache.DeleteProduct return parameters for the expectation previously defined by the When method
func (e *ProductCacheMockDeleteProductExpectation) Then(err error) *ProductCacheMock {
	e.results = &ProductCacheMockDeleteProductResults{err}
	return e.mock
}

// Times sets number of times ProductCache.DeleteProduct should be invoked
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Times(n uint64) *mProductCacheMockDeleteProduct {
	if n == 0 {
		mmDeleteProduct.mock.t.Fatalf("Times of ProductCacheMock.DeleteProduct mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteProduct.expectedInvocations, n)
	mmDeleteProduct.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteProduct
}

func (mmDeleteProduct *mProductCacheMockDeleteProduct) invocationsDone() bool {
	if len(mmDeleteProduct.expectations) == 0 && mmDeleteProduct.defaultExpectation == nil && mmDeleteProduct.mock.funcDeleteProduct == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteProduct.mock.afterDeleteProductCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteProduct.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteProduct implements mm_repository.ProductCache
func (mmDeleteProduct *ProductCacheMock) DeleteProduct(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteProduct.beforeDeleteProductCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteProduct.afterDeleteProductCounter, 1)

	mmDeleteProduct.t.Helper()

	if mmDeleteProduct.inspectFuncDeleteProduct != nil {
		mmDeleteProduct.inspectFuncDeleteProduct(ctx, id)
	}

	mm_params := ProductCacheMockDeleteProductParams{ctx, id}

	// Record call args
	mmDeleteProduct.DeleteProductMock.mutex.Lock()
	mmDeleteProduct.DeleteProductMock.callArgs = append(mmDeleteProduct.DeleteProductMock.callArgs, &mm_params)
	mmDeleteProduct.DeleteProductMock.mutex.Unlock()

	for _, e := range mmDeleteProduct.DeleteProductMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteProduct.DeleteProductMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteProduct.DeleteProductMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteProduct.DeleteProductMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteProduct.DeleteProductMock.defaultExpectation.paramPtrs

		mm_got := ProductCacheMockDeleteProductParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteProduct.t.Errorf("ProductCacheMock.DeleteProduct got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteProduct.DeleteProductMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDeleteProduct.t.Errorf("ProductCacheMock.DeleteProduct got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteProduct.DeleteProductMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteProduct.t.Errorf("ProductCacheMock.DeleteProduct got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteProduct.DeleteProductMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteProduct.DeleteProductMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteProduct.t.Fatal("No results are set for the ProductCacheMock.DeleteProduct")
		}
		return (*mm_results).err
	}
	if mmDeleteProduct.funcDeleteProduct != nil {
		return mmDeleteProduct.funcDeleteProduct(ctx, id)
	}
	mmDeleteProduct.t.Fatalf("Unexpected call to ProductCacheMock.DeleteProduct. %v %v", ctx, id)
	return
}

// DeleteProductAfterCounter returns a count of finished ProductCacheMock.DeleteProduct invocations
func (mmDeleteProduct *ProductCacheMock) DeleteProductAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteProduct.afterDeleteProductCounter)
}

// DeleteProductBeforeCounter returns a count of ProductCacheMock.DeleteProduct invocations
func (mmDeleteProduct *ProductCacheMock) DeleteProductBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteProduct.beforeDeleteProductCounter)
}

// Calls returns a list of arguments used in each call to ProductCacheMock.DeleteProduct.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteProduct *mProductCacheMockDeleteProduct) Calls() []*ProductCacheMockDeleteProductParams {
	mmDeleteProduct.mutex.RLock()

	argCopy := make([]*ProductCacheMockDeleteProductParams, len(mmDeleteProduct.callArgs))
	copy(argCopy, mmDeleteProduct.callArgs)

	mmDeleteProduct.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteProductDone returns true if the count of the DeleteProduct invocations corresponds
// the number of defined expectations
func (m *ProductCacheMock) MinimockDeleteProductDone() bool {
	if m.DeleteProductMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteProductMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteProductMock.invocationsDone()
}

// MinimockDeleteProductInspect logs each unmet expectation
func (m *ProductCacheMock) MinimockDeleteProductInspect() {
	for _, e := range m.DeleteProductMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductCacheMock.DeleteProduct at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteProductCounter := mm_atomic.LoadUint64(&m.afterDeleteProductCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteProductMock.defaultExpectation != nil && afterDeleteProductCounter < 1 {
		if m.DeleteProductMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductCacheMock.DeleteProduct at\n%s", m.DeleteProductMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductCacheMock.DeleteProduct at\n%s with params: %#v", m.DeleteProductMock.defaultExpectation.expectationOrigins.origin, *m.DeleteProductMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteProduct != nil && afterDeleteProductCounter < 1 {
		m.t.Errorf("Expected call to ProductCacheMock.DeleteProduct at\n%s", m.funcDeleteProductOrigin)
	}

	if !m.DeleteProductMock.invocationsDone() && afterDeleteProductCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductCacheMock.DeleteProduct at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteProductMock.expectedInvocations), m.DeleteProductMock.expectedInvocationsOrigin, afterDeleteProductCounter)
	}
}

type mProductCacheMockDeleteProducts struct {
	optional           bool
	mock               *ProductCacheMock
	defaultExpectation *ProductCacheMockDeleteProductsExpectation
	expectations       []*ProductCacheMockDeleteProductsExpectation

	callArgs []*ProductCacheMockDeleteProductsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductCacheMockDeleteProductsExpectation specifies expectation struct of the ProductCache.DeleteProducts
type ProductCacheMockDeleteProductsExpectation struct {
	mock               *ProductCacheMock
	params             *ProductCacheMockDeleteProductsParams
	paramPtrs          *ProductCacheMockDeleteProductsParamPtrs
	expectationOrigins ProductCacheMockDeleteProductsExpectationOrigins
	results            *ProductCacheMockDeleteProductsResults
	returnOrigin       string
	Counter            uint64
}

// ProductCacheMockDeleteProductsParams contains parameters of the ProductCache.DeleteProducts
type ProductCacheMockDeleteProductsParams struct {
	ctx context.Context
	ids []int64
}

// ProductCacheMockDeleteProductsParamPtrs contains pointers to parameters of the ProductCache.DeleteProducts
type ProductCacheMockDeleteProductsParamPtrs struct {
	ctx *context.Context
	ids *[]int64
}

// ProductCacheMockDeleteProductsResults contains results of the ProductCache.DeleteProducts
type ProductCacheMockDeleteProductsResults struct {
	err error
}

// ProductCacheMockDeleteProductsOrigins contains origins of expectations of the ProductCache.DeleteProducts
type ProductCacheMockDeleteProductsExpectationOrigins struct {
	origin    string
	originCtx string
	originIds string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Optional() *mProductCacheMockDeleteProducts {
	mmDeleteProducts.optional = true
	return mmDeleteProducts
}

// Expect sets up expected params for ProductCache.DeleteProducts
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Expect(ctx context.Context, ids []int64) *mProductCacheMockDeleteProducts {
	if mmDeleteProducts.mock.funcDeleteProducts != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Set")
	}

	if mmDeleteProducts.defaultExpectation == nil {
		mmDeleteProducts.defaultExpectation = &ProductCacheMockDeleteProductsExpectation{}
	}

	if mmDeleteProducts.defaultExpectation.paramPtrs != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by ExpectParams functions")
	}

	mmDeleteProducts.defaultExpectation.params = &ProductCacheMockDeleteProductsParams{ctx, ids}
	mmDeleteProducts.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteProducts.expectations {
		if minimock.Equal(e.params, mmDeleteProducts.defaultExpectation.params) {
			mmDeleteProducts.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteProducts.defaultExpectation.params)
		}
	}

	return mmDeleteProducts
}

// ExpectCtxParam1 sets up expected param ctx for ProductCache.DeleteProducts
func (mmDeleteProducts *mProductCacheMockDeleteProducts) ExpectCtxParam1(ctx context.Context) *mProductCacheMockDeleteProducts {
	if mmDeleteProducts.mock.funcDeleteProducts != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Set")
	}

	if mmDeleteProducts.defaultExpectation == nil {
		mmDeleteProducts.defaultExpectation = &ProductCacheMockDeleteProductsExpectation{}
	}

	if mmDeleteProducts.defaultExpectation.params != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Expect")
	}

	if mmDeleteProducts.defaultExpectation.paramPtrs == nil {
		mmDeleteProducts.defaultExpectation.paramPtrs = &ProductCacheMockDeleteProductsParamPtrs{}
	}
	mmDeleteProducts.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteProducts.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteProducts
}

// ExpectIdsParam2 sets up expected param ids for ProductCache.DeleteProducts
func (mmDeleteProducts *mProductCacheMockDeleteProducts) ExpectIdsParam2(ids []int64) *mProductCacheMockDeleteProducts {
	if mmDeleteProducts.mock.funcDeleteProducts != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Set")
	}

	if mmDeleteProducts.defaultExpectation == nil {
		mmDeleteProducts.defaultExpectation = &ProductCacheMockDeleteProductsExpectation{}
	}

	if mmDeleteProducts.defaultExpectation.params != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Expect")
	}

	if mmDeleteProducts.defaultExpectation.paramPtrs == nil {
		mmDeleteProducts.defaultExpectation.paramPtrs = &ProductCacheMockDeleteProductsParamPtrs{}
	}
	mmDeleteProducts.defaultExpectation.paramPtrs.ids = &ids
	mmDeleteProducts.defaultExpectation.expectationOrigins.originIds = minimock.CallerInfo(1)

	return mmDeleteProducts
}

// Inspect accepts an inspector function that has same arguments as the ProductCache.DeleteProducts
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Inspect(f func(ctx context.Context, ids []int64)) *mProductCacheMockDeleteProducts {
	if mmDeleteProducts.mock.inspectFuncDeleteProducts != nil {
		mmDeleteProducts.mock.t.Fatalf("Inspect function is already set for ProductCacheMock.DeleteProducts")
	}

	mmDeleteProducts.mock.inspectFuncDeleteProducts = f

	return mmDeleteProducts
}

// Return sets up results that will be returned by ProductCache.DeleteProducts
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Return(err error) *ProductCacheMock {
	if mmDeleteProducts.mock.funcDeleteProducts != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Set")
	}

	if mmDeleteProducts.defaultExpectation == nil {
		mmDeleteProducts.defaultExpectation = &ProductCacheMockDeleteProductsExpectation{mock: mmDeleteProducts.mock}
	}
	mmDeleteProducts.defaultExpectation.results = &ProductCacheMockDeleteProductsResults{err}
	mmDeleteProducts.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteProducts.mock
}

// Set uses given function f to mock the ProductCache.DeleteProducts method
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Set(f func(ctx context.Context, ids []int64) (err error)) *ProductCacheMock {
	if mmDeleteProducts.defaultExpectation != nil {
		mmDeleteProducts.mock.t.Fatalf("Default expectation is already set for the ProductCache.DeleteProducts method")
	}

	if len(mmDeleteProducts.expectations) > 0 {
		mmDeleteProducts.mock.t.Fatalf("Some expectations are already set for the ProductCache.DeleteProducts method")
	}

	mmDeleteProducts.mock.funcDeleteProducts = f
	mmDeleteProducts.mock.funcDeleteProductsOrigin = minimock.CallerInfo(1)
	return mmDeleteProducts.mock
}

// When sets expectation for the ProductCache.DeleteProducts which will trigger the result defined by the following
// Then helper
func (mmDeleteProducts *mProductCacheMockDeleteProducts) When(ctx context.Context, ids []int64) *ProductCacheMockDeleteProductsExpectation {
	if mmDeleteProducts.mock.funcDeleteProducts != nil {
		mmDeleteProducts.mock.t.Fatalf("ProductCacheMock.DeleteProducts mock is already set by Set")
	}

	expectation := &ProductCacheMockDeleteProductsExpectation{
		mock:               mmDeleteProducts.mock,
		params:             &ProductCacheMockDeleteProductsParams{ctx, ids},
		expectationOrigins: ProductCacheMockDeleteProductsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteProducts.expectations = append(mmDeleteProducts.expectations, expectation)
	return expectation
}

// Then sets up ProductCache.DeleteProducts return parameters for the expectation previously defined by the When method
func (e *ProductCacheMockDeleteProductsExpectation) Then(err error) *ProductCacheMock {
	e.results = &ProductCacheMockDeleteProductsResults{err}
	return e.mock
}

// Times sets number of times ProductCache.DeleteProducts should be invoked
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Times(n uint64) *mProductCacheMockDeleteProducts {
	if n == 0 {
		mmDeleteProducts.mock.t.Fatalf("Times of ProductCacheMock.DeleteProducts mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteProducts.expectedInvocations, n)
	mmDeleteProducts.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteProducts
}

func (mmDeleteProducts *mProductCacheMockDeleteProducts) invocationsDone() bool {
	if len(mmDeleteProducts.expectations) == 0 && mmDeleteProducts.defaultExpectation == nil && mmDeleteProducts.mock.funcDeleteProducts == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteProducts.mock.afterDeleteProductsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteProducts.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteProducts implements mm_repository.ProductCache
func (mmDeleteProducts *ProductCacheMock) DeleteProducts(ctx context.Context, ids []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteProducts.beforeDeleteProductsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteProducts.afterDeleteProductsCounter, 1)

	mmDeleteProducts.t.Helper()

	if mmDeleteProducts.inspectFuncDeleteProducts != nil {
		mmDeleteProducts.inspectFuncDeleteProducts(ctx, ids)
	}

	mm_params := ProductCacheMockDeleteProductsParams{ctx, ids}

	// Record call args
	mmDeleteProducts.DeleteProductsMock.mutex.Lock()
	mmDeleteProducts.DeleteProductsMock.callArgs = append(mmDeleteProducts.DeleteProductsMock.callArgs, &mm_params)
	mmDeleteProducts.DeleteProductsMock.mutex.Unlock()

	for _, e := range mmDeleteProducts.DeleteProductsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteProducts.DeleteProductsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteProducts.DeleteProductsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteProducts.DeleteProductsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteProducts.DeleteProductsMock.defaultExpectation.paramPtrs

		mm_got := ProductCacheMockDeleteProductsParams{ctx, ids}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteProducts.t.Errorf("ProductCacheMock.DeleteProducts got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteProducts.DeleteProductsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.ids != nil && !minimock.Equal(*mm_want_ptrs.ids, mm_got.ids) {
				mmDeleteProducts.t.Errorf("ProductCacheMock.DeleteProducts got unexpected parameter ids, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteProducts.DeleteProductsMock.defaultExpectation.expectationOrigins.originIds, *mm_want_ptrs.ids, mm_got.ids, minimock.Diff(*mm_want_ptrs.ids, mm_got.ids))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteProducts.t.Errorf("ProductCacheMock.DeleteProducts got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteProducts.DeleteProductsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteProducts.DeleteProductsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteProducts.t.Fatal("No results are set for the ProductCacheMock.DeleteProducts")
		}
		return (*mm_results).err
	}
	if mmDeleteProducts.funcDeleteProducts != nil {
		return mmDeleteProducts.funcDeleteProducts(ctx, ids)
	}
	mmDeleteProducts.t.Fatalf("Unexpected call to ProductCacheMock.DeleteProducts. %v %v", ctx, ids)
	return
}

// DeleteProductsAfterCounter returns a count of finished ProductCacheMock.DeleteProducts invocations
func (mmDeleteProducts *ProductCacheMock) DeleteProductsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteProducts.afterDeleteProductsCounter)
}

// DeleteProductsBeforeCounter returns a count of ProductCacheMock.DeleteProducts invocations
func (mmDeleteProducts *ProductCacheMock) DeleteProductsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteProducts.beforeDeleteProductsCounter)
}

// Calls returns a list of arguments used in each call to ProductCacheMock.DeleteProducts.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteProducts *mProductCacheMockDeleteProducts) Calls() []*ProductCacheMockDeleteProductsParams {
	mmDeleteProducts.mutex.RLock()

	argCopy := make([]*ProductCacheMockDeleteProductsParams, len(mmDeleteProducts.callArgs))
	copy(argCopy, mmDeleteProducts.callArgs)

	mmDeleteProducts.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteProductsDone returns true if the count of the DeleteProducts invocations corresponds
// the number of defined expectations
func (m *ProductCacheMock) MinimockDeleteProductsDone() bool {
	if m.DeleteProductsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteProductsMock.invocationsDone()
}

// MinimockDeleteProductsInspect logs each unmet expectation
func (m *ProductCacheMock) MinimockDeleteProductsInspect() {
	for _, e := range m.DeleteProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductCacheMock.DeleteProducts at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteProductsCounter := mm_atomic.LoadUint64(&m.afterDeleteProductsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteProductsMock.defaultExpectation != nil && afterDeleteProductsCounter < 1 {
		if m.DeleteProductsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductCacheMock.DeleteProducts at\n%s", m.DeleteProductsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductCacheMock.DeleteProducts at\n%s with params: %#v", m.DeleteProductsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteProductsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteProducts != nil && afterDeleteProductsCounter < 1 {
		m.t.Errorf("Expected call to ProductCacheMock.DeleteProducts at\n%s", m.funcDeleteProductsOrigin)
	}

	if !m.DeleteProductsMock.invocationsDone() && afterDeleteProductsCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductCacheMock.DeleteProducts at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteProductsMock.expectedInvocations), m.DeleteProductsMock.expectedInvocationsOrigin, afterDeleteProductsCounter)
	}
}

type mProductCacheMockGetProduct struct {
	optional           bool
	mock               *ProductCacheMock
	defaultExpectation *ProductCacheMockGetProductExpectation
	expectations       []*ProductCacheMockGetProductExpectation

	callArgs []*ProductCacheMockGetProductParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductCacheMockGetProductExpectation specifies expectation struct of the ProductCache.GetProduct
type ProductCacheMockGetProductExpectation struct {
	mock               *ProductCacheMock
	params             *ProductCacheMockGetProductParams
	paramPtrs          *ProductCacheMockGetProductParamPtrs
	expectationOrigins ProductCacheMockGetProductExpectationOrigins
	results            *ProductCacheMockGetProductResults
	returnOrigin       string
	Counter            uint64
}

// ProductCacheMockGetProductParams contains parameters of the ProductCache.GetProduct
type ProductCacheMockGetProductParams struct {
	ctx context.Context
	id  int64
}

// ProductCacheMockGetProductParamPtrs contains pointers to parameters of the ProductCache.GetProduct
type ProductCacheMockGetProductParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// ProductCacheMockGetProductResults contains results of the ProductCache.GetProduct
type ProductCacheMockGetProductResults struct {
	p1  productentity.Product
	err error
}

// ProductCacheMockGetProductOrigins contains origins of expectations of the ProductCache.GetProduct
type ProductCacheMockGetProductExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetProduct *mProductCacheMockGetProduct) Optional() *mProductCacheMockGetProduct {
	mmGetProduct.optional = true
	return mmGetProduct
}

// Expect sets up expected params for ProductCache.GetProduct
func (mmGetProduct *mProductCacheMockGetProduct) Expect(ctx context.Context, id int64) *mProductCacheMockGetProduct {
	if mmGetProduct.mock.funcGetProduct != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Set")
	}

	if mmGetProduct.defaultExpectation == nil {
		mmGetProduct.defaultExpectation = &ProductCacheMockGetProductExpectation{}
	}

	if mmGetProduct.defaultExpectation.paramPtrs != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by ExpectParams functions")
	}

	mmGetProduct.defaultExpectation.params = &ProductCacheMockGetProductParams{ctx, id}
	mmGetProduct.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetProduct.expectations {
		if minimock.Equal(e.params, mmGetProduct.defaultExpectation.params) {
			mmGetProduct.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetProduct.defaultExpectation.params)
		}
	}

	return mmGetProduct
}

// ExpectCtxParam1 sets up expected param ctx for ProductCache.GetProduct
func (mmGetProduct *mProductCacheMockGetProduct) ExpectCtxParam1(ctx context.Context) *mProductCacheMockGetProduct {
	if mmGetProduct.mock.funcGetProduct != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Set")
	}

	if mmGetProduct.defaultExpectation == nil {
		mmGetProduct.defaultExpectation = &ProductCacheMockGetProductExpectation{}
	}

	if mmGetProduct.defaultExpectation.params != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Expect")
	}

	if mmGetProduct.defaultExpectation.paramPtrs == nil {
		mmGetProduct.defaultExpectation.paramPtrs = &ProductCacheMockGetProductParamPtrs{}
	}
	mmGetProduct.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetProduct.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetProduct
}

// ExpectIdParam2 sets up expected param id for ProductCache.GetProduct
func (mmGetProduct *mProductCacheMockGetProduct) ExpectIdParam2(id int64) *mProductCacheMockGetProduct {
	if mmGetProduct.mock.funcGetProduct != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Set")
	}

	if mmGetProduct.defaultExpectation == nil {
		mmGetProduct.defaultExpectation = &ProductCacheMockGetProductExpectation{}
	}

	if mmGetProduct.defaultExpectation.params != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Expect")
	}

	if mmGetProduct.defaultExpectation.paramPtrs == nil {
		mmGetProduct.defaultExpectation.paramPtrs = &ProductCacheMockGetProductParamPtrs{}
	}
	mmGetProduct.defaultExpectation.paramPtrs.id = &id
	mmGetProduct.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetProduct
}

// Inspect accepts an inspector function that has same arguments as the ProductCache.GetProduct
func (mmGetProduct *mProductCacheMockGetProduct) Inspect(f func(ctx context.Context, id int64)) *mProductCacheMockGetProduct {
	if mmGetProduct.mock.inspectFuncGetProduct != nil {
		mmGetProduct.mock.t.Fatalf("Inspect function is already set for ProductCacheMock.GetProduct")
	}

	mmGetProduct.mock.inspectFuncGetProduct = f

	return mmGetProduct
}

// Return sets up results that will be returned by ProductCache.GetProduct
func (mmGetProduct *mProductCacheMockGetProduct) Return(p1 productentity.Product, err error) *ProductCacheMock {
	if mmGetProduct.mock.funcGetProduct != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Set")
	}

	if mmGetProduct.defaultExpectation == nil {
		mmGetProduct.defaultExpectation = &ProductCacheMockGetProductExpectation{mock: mmGetProduct.mock}
	}
	mmGetProduct.defaultExpectation.results = &ProductCacheMockGetProductResults{p1, err}
	mmGetProduct.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetProduct.mock
}

// Set uses given function f to mock the ProductCache.GetProduct method
func (mmGetProduct *mProductCacheMockGetProduct) Set(f func(ctx context.Context, id int64) (p1 productentity.Product, err error)) *ProductCacheMock {
	if mmGetProduct.defaultExpectation != nil {
		mmGetProduct.mock.t.Fatalf("Default expectation is already set for the ProductCache.GetProduct method")
	}

	if len(mmGetProduct.expectations) > 0 {
		mmGetProduct.mock.t.Fatalf("Some expectations are already set for the ProductCache.GetProduct method")
	}

	mmGetProduct.mock.funcGetProduct = f
	mmGetProduct.mock.funcGetProductOrigin = minimock.CallerInfo(1)
	return mmGetProduct.mock
}

// When sets expectation for the ProductCache.GetProduct which will trigger the result defined by the following
// Then helper
func (mmGetProduct *mProductCacheMockGetProduct) When(ctx context.Context, id int64) *ProductCacheMockGetProductExpectation {
	if mmGetProduct.mock.funcGetProduct != nil {
		mmGetProduct.mock.t.Fatalf("ProductCacheMock.GetProduct mock is already set by Set")
	}

	expectation := &ProductCacheMockGetProductExpectation{
		mock:               mmGetProduct.mock,
		params:             &ProductCacheMockGetProductParams{ctx, id},
		expectationOrigins: ProductCacheMockGetProductExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetProduct.expectations = append(mmGetProduct.expectations, expectation)
	return expectation
}

// Then sets up ProductCache.GetProduct return parameters for the expectation previously defined by the When method
func (e *ProductCacheMockGetProductExpectation) Then(p1 productentity.Product, err error) *ProductCacheMock {
	e.results = &ProductCacheMockGetProductResults{p1, err}
	return e.mock
}

// Times sets number of times ProductCache.GetProduct should be invoked
func (mmGetProduct *mProductCacheMockGetProduct) Times(n uint64) *mProductCacheMockGetProduct {
	if n == 0 {
		mmGetProduct.mock.t.Fatalf("Times of ProductCacheMock.GetProduct mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetProduct.expectedInvocations, n)
	mmGetProduct.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetProduct
}

func (mmGetProduct *mProductCacheMockGetProduct) invocationsDone() bool {
	if len(mmGetProduct.expectations) == 0 && mmGetProduct.defaultExpectation == nil && mmGetProduct.mock.funcGetProduct == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetProduct.mock.afterGetProductCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetProduct.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetProduct implements mm_repository.ProductCache
func (mmGetProduct *ProductCacheMock) GetProduct(ctx context.Context, id int64) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetProduct.beforeGetProductCounter, 1)
	defer mm_atomic.AddUint64(&mmGetProduct.afterGetProductCounter, 1)

	mmGetProduct.t.Helper()

	if mmGetProduct.inspectFuncGetProduct != nil {
		mmGetProduct.inspectFuncGetProduct(ctx, id)
	}

	mm_params := ProductCacheMockGetProductParams{ctx, id}

	// Record call args
	mmGetProduct.GetProductMock.mutex.Lock()
	mmGetProduct.GetProductMock.callArgs = append(mmGetProduct.GetProductMock.callArgs, &mm_params)
	mmGetProduct.GetProductMock.mutex.Unlock()

	for _, e := range mmGetProduct.GetProductMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetProduct.GetProductMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetProduct.GetProductMock.defaultExpectation.Counter, 1)
		mm_want := mmGetProduct.GetProductMock.defaultExpectation.params
		mm_want_ptrs := mmGetProduct.GetProductMock.defaultExpectation.paramPtrs

		mm_got := ProductCacheMockGetProductParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetProduct.t.Errorf("ProductCacheMock.GetProduct got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProduct.GetProductMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetProduct.t.Errorf("ProductCacheMock.GetProduct got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProduct.GetProductMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetProduct.t.Errorf("ProductCacheMock.GetProduct got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetProduct.GetProductMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetProduct.GetProductMock.defaultExpectation.results
		if mm_results == nil {
			mmGetProduct.t.Fatal("No results are set for the ProductCacheMock.GetProduct")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetProduct.funcGetProduct != nil {
		return mmGetProduct.funcGetProduct(ctx, id)
	}
	mmGetProduct.t.Fatalf("Unexpected call to ProductCacheMock.GetProduct. %v %v", ctx, id)
	return
}

// GetProductAfterCounter returns a count of finished ProductCacheMock.GetProduct invocations
func (mmGetProduct *ProductCacheMock) GetProductAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProduct.afterGetProductCounter)
}

// GetProductBeforeCounter returns a count of ProductCacheMock.GetProduct invocations
func (mmGetProduct *ProductCacheMock) GetProductBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProduct.beforeGetProductCounter)
}

// Calls returns a list of arguments used in each call to ProductCacheMock.GetProduct.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetProduct *mProductCacheMockGetProduct) Calls() []*ProductCacheMockGetProductParams {
	mmGetProduct.mutex.RLock()

	argCopy := make([]*ProductCacheMockGetProductParams, len(mmGetProduct.callArgs))
	copy(argCopy, mmGetProduct.callArgs)

	mmGetProduct.mutex.RUnlock()

	return argCopy
}

// MinimockGetProductDone returns true if the count of the GetProduct invocations corresponds
// the number of defined expectations
func (m *ProductCacheMock) MinimockGetProductDone() bool {
	if m.GetProductMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetProductMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetProductMock.invocationsDone()
}

// MinimockGetProductInspect logs each unmet expectation
func (m *ProductCacheMock) MinimockGetProductInspect() {
	for _, e := range m.GetProductMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductCacheMock.GetProduct at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetProductCounter := mm_atomic.LoadUint64(&m.afterGetProductCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetProductMock.defaultExpectation != nil && afterGetProductCounter < 1 {
		if m.GetProductMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductCacheMock.GetProduct at\n%s", m.GetProductMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductCacheMock.GetProduct at\n%s with params: %#v", m.GetProductMock.defaultExpectation.expectationOrigins.origin, *m.GetProductMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetProduct != nil && afterGetProductCounter < 1 {
		m.t.Errorf("Expected call to ProductCacheMock.GetProduct at\n%s", m.funcGetProductOrigin)
	}

	if !m.GetProductMock.invocationsDone() && afterGetProductCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductCacheMock.GetProduct at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetProductMock.expectedInvocations), m.GetProductMock.expectedInvocationsOrigin, afterGetProductCounter)
	}
}

type mProductCacheMockGetProducts struct {
	optional           bool
	mock               *ProductCacheMock
	defaultExpectation *ProductCacheMockGetProductsExpectation
	expectations       []*ProductCacheMockGetProductsExpectation

	callArgs []*ProductCacheMockGetProductsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductCacheMockGetProductsExpectation specifies expectation struct of the ProductCache.GetProducts
type ProductCacheMockGetProductsExpectation struct {
	mock               *ProductCacheMock
	params             *ProductCacheMockGetProductsParams
	paramPtrs          *ProductCacheMockGetProductsParamPtrs
	expectationOrigins ProductCacheMockGetProductsExpectationOrigins
	results            *ProductCacheMockGetProductsResults
	returnOrigin       string
	Counter            uint64
}

// ProductCacheMockGetProductsParams contains parameters of the ProductCache.GetProducts
type ProductCacheMockGetProductsParams struct {
	ctx context.Context
}

// ProductCacheMockGetProductsParamPtrs contains pointers to parameters of the ProductCache.GetProducts
type ProductCacheMockGetProductsParamPtrs struct {
	ctx *context.Context
}

// ProductCacheMockGetProductsResults contains results of the ProductCache.GetProducts
type ProductCacheMockGetProductsResults struct {
	pa1 []productentity.Product
	err error
}

// ProductCacheMockGetProductsOrigins contains origins of expectations of the ProductCache.GetProducts
type ProductCacheMockGetProductsExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetProducts *mProductCacheMockGetProducts) Optional() *mProductCacheMockGetProducts {
	mmGetProducts.optional = true
	return mmGetProducts
}

// Expect sets up expected params for ProductCache.GetProducts
func (mmGetProducts *mProductCacheMockGetProducts) Expect(ctx context.Context) *mProductCacheMockGetProducts {
	if mmGetProducts.mock.funcGetProducts != nil {
		mmGetProducts.mock.t.Fatalf("ProductCacheMock.GetProducts mock is already set by Set")
	}

	if mmGetProducts.defaultExpectation == nil {
		mmGetProducts.defaultExpectation = &ProductCacheMockGetProductsExpectation{}
	}

	if mmGetProducts.defaultExpectation.paramPtrs != nil {
		mmGetProducts.mock.t.Fatalf("ProductCacheMock.GetProducts mock is already set by ExpectParams functions")
	}

	mmGetProducts.defaultExpectation.params = &ProductCacheMockGetProductsParams{ctx}
	mmGetProducts.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetProducts.expectations {
		if minimock.Equal(e.params, mmGetProducts.defaultExpectation.params) {
			mmGetProducts.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetProducts.defaultExpectation.params)
		}
	}

	return mmGetProducts
}

// ExpectCtxParam1 sets up expected param ctx for ProductCache.GetProducts
func (mmGetProducts *mProductCacheMockGetProducts) ExpectCtxParam1(ctx context.Context) *mProductCacheMockGetProducts {
	if mmGetProducts.mock.funcGetProducts != nil {
		mmGetProducts.mock.t.Fatalf("ProductCacheMock.GetProducts mock is already set by Set")
	}

	if mmGetProducts.defaultExpectation == nil {
		mmGetProducts.defaultExpectation = &ProductCacheMockGetProductsExpectation{}
	}

	if mmGetProducts.defaultExpectation.params != nil {
		mmGetProducts.mock.t.Fatalf("ProductCacheMock.GetProducts mock is already set by Expect")
	}

	if mmGetProducts.defaultExpectation.paramPtrs == nil {
		mmGetProducts.defaultExpectation.paramPtrs = &ProductCacheMockGetProductsParamPtrs{}
	}
	mmGetProducts.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetProducts.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetProducts
}

// Inspect accepts an inspector function that has same arguments as the ProductCache.GetProducts
func (mmGetProducts *mProductCacheMockGetProducts) Inspect(f func(ctx context.Context)) *mProductCacheMockGetProducts {
	if mmGetProducts.mock.inspectFuncGetProducts != nil {
		mmGetProducts.mock.t.Fatalf("Inspect function is already set for ProductCacheMock.GetProducts")
	}

	mmGetProducts.mock.inspectFuncGetProducts = f

	return mmGetProducts
}

// Return sets up results that will be returned by ProductCache.GetProducts
func (mmGetProducts *mProductCacheMockGetProducts) Return(pa1 []productentity.Product, err error) *ProductCacheMock {
	if mmGetProducts.mock.funcGetProducts != nil {
		mmGetProducts.mock.t.Fatalf("ProductCacheMock.GetProducts mock is already set by Set")
	}

	if mmGetProducts.defaultExpectation == nil {
		mmGetProducts.defaultExpectation = &ProductCacheMockGetProductsExpectation{mock: mmGetProducts.mock}
	}
	mmGetProducts.defaultExpectation.results = &ProductCacheMockGetProductsResults{pa1, err}
	mmGetProducts.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetProducts.mock
}

// Set uses given function f to mock the ProductCache.GetProducts method
func (mmGetProducts *mProductCacheMockGetProducts) Set(f func(ctx context.Context) (pa1 []productentity.Product, err error)) *ProductCacheMock {
	if mmGetProducts.defaultExpectation != nil {
		mmGetProducts.mock.t.Fatalf("Default expectation is already set for the ProductCache.GetProducts method")
	}

	if len(mmGetProducts.expectations) > 0 {
		mmGetProducts.mock.t.Fatalf("Some expectations are already set for the ProductCache.GetProducts method")
	}

	mmGetProducts.mock.funcGetProducts = f
	mmGetProducts.mock.funcGetProductsOrigin = minimock.CallerInfo(1)
	return mmGetProducts.mock
}

// When sets expectation for the ProductCache.GetProducts which will trigger the result defined by the following
// Then helper
func (mmGetProducts *mProductCacheMockGetProducts) When(ctx context.Context) *ProductCacheMockGetProductsExpectation {
	if mmGetProducts.mock.funcGetProducts != nil {
		mmGetProducts.mock.t.Fatalf("ProductCacheMock.GetProducts mock is already set by Set")
	}

	expectation := &ProductCacheMockGetProductsExpectation{
		mock:               mmGetProducts.mock,
		params:             &ProductCacheMockGetProductsParams{ctx},
		expectationOrigins: ProductCacheMockGetProductsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetProducts.expectations = append(mmGetProducts.expectations, expectation)
	return expectation
}

// Then sets up ProductCache.GetProducts return parameters for the expectation previously defined by the When method
func (e *ProductCacheMockGetProductsExpectation) Then(pa1 []productentity.Product, err error) *ProductCacheMock {
	e.results = &ProductCacheMockGetProductsResults{pa1, err}
	return e.mock
}

// Times sets number of times ProductCache.GetProducts should be invoked
func (mmGetProducts *mProductCacheMockGetProducts) Times(n uint64) *mProductCacheMockGetProducts {
	if n == 0 {
		mmGetProducts.mock.t.Fatalf("Times of ProductCacheMock.GetProducts mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetProducts.expectedInvocations, n)
	mmGetProducts.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetProducts
}

func (mmGetProducts *mProductCacheMockGetProducts) invocationsDone() bool {
	if len(mmGetProducts.expectations) == 0 && mmGetProducts.defaultExpectation == nil && mmGetProducts.mock.funcGetProducts == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetProducts.mock.afterGetProductsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetProducts.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetProducts implements mm_repository.ProductCache
func (mmGetProducts *ProductCacheMock) GetProducts(ctx context.Context) (pa1 []productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetProducts.beforeGetProductsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetProducts.afterGetProductsCounter, 1)

	mmGetProducts.t.Helper()

	if mmGetProducts.inspectFuncGetProducts != nil {
		mmGetProducts.inspectFuncGetProducts(ctx)
	}

	mm_params := ProductCacheMockGetProductsParams{ctx}

	// Record call args
	mmGetProducts.GetProductsMock.mutex.Lock()
	mmGetProducts.GetProductsMock.callArgs = append(mmGetProducts.GetProductsMock.callArgs, &mm_params)
	mmGetProducts.GetProductsMock.mutex.Unlock()

	for _, e := range mmGetProducts.GetProductsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetProducts.GetProductsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetProducts.GetProductsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetProducts.GetProductsMock.defaultExpectation.params
		mm_want_ptrs := mmGetProducts.GetProductsMock.defaultExpectation.paramPtrs

		mm_got := ProductCacheMockGetProductsParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetProducts.t.Errorf("ProductCacheMock.GetProducts got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProducts.GetProductsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetProducts.t.Errorf("ProductCacheMock.GetProducts got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetProducts.GetProductsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetProducts.GetProductsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetProducts.t.Fatal("No results are set for the ProductCacheMock.GetProducts")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetProducts.funcGetProducts != nil {
		return mmGetProducts.funcGetProducts(ctx)
	}
	mmGetProducts.t.Fatalf("Unexpected call to ProductCacheMock.GetProducts. %v", ctx)
	return
}

// GetProductsAfterCounter returns a count of finished ProductCacheMock.GetProducts invocations
func (mmGetProducts *ProductCacheMock) GetProductsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProducts.afterGetProductsCounter)
}

// GetProductsBeforeCounter returns a count of ProductCacheMock.GetProducts invocations
func (mmGetProducts *ProductCacheMock) GetProductsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProducts.beforeGetProductsCounter)
}

// Calls returns a list of arguments used in each call to ProductCacheMock.GetProducts.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetProducts *mProductCacheMockGetProducts) Calls() []*ProductCacheMockGetProductsParams {
	mmGetProducts.mutex.RLock()

	argCopy := make([]*ProductCacheMockGetProductsParams, len(mmGetProducts.callArgs))
	copy(argCopy, mmGetProducts.callArgs)

	mmGetProducts.mutex.RUnlock()

	return argCopy
}

// MinimockGetProductsDone returns true if the count of the GetProducts invocations corresponds
// the number of defined expectations
func (m *ProductCacheMock) MinimockGetProductsDone() bool {
	if m.GetProductsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetProductsMock.invocationsDone()
}

// MinimockGetProductsInspect logs each unmet expectation
func (m *ProductCacheMock) MinimockGetProductsInspect() {
	for _, e := range m.GetProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductCacheMock.GetProducts at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetProductsCounter := mm_atomic.LoadUint64(&m.afterGetProductsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetProductsMock.defaultExpectation != nil && afterGetProductsCounter < 1 {
		if m.GetProductsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductCacheMock.GetProducts at\n%s", m.GetProductsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductCacheMock.GetProducts at\n%s with params: %#v", m.GetProductsMock.defaultExpectation.expectationOrigins.origin, *m.GetProductsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetProducts != nil && afterGetProductsCounter < 1 {
		m.t.Errorf("Expected call to ProductCacheMock.GetProducts at\n%s", m.funcGetProductsOrigin)
	}

	if !m.GetProductsMock.invocationsDone() && afterGetProductsCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductCacheMock.GetProducts at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetProductsMock.expectedInvocations), m.GetProductsMock.expectedInvocationsOrigin, afterGetProductsCounter)
	}
}

type mProductCacheMockSetProduct struct {
	optional           bool
	mock               *ProductCacheMock
	defaultExpectation *ProductCacheMockSetProductExpectation
	expectations       []*ProductCacheMockSetProductExpectation

	callArgs []*ProductCacheMockSetProductParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductCacheMockSetProductExpectation specifies expectation struct of the ProductCache.SetProduct
type ProductCacheMockSetProductExpectation struct {
	mock               *ProductCacheMock
	params             *ProductCacheMockSetProductParams
	paramPtrs          *ProductCacheMockSetProductParamPtrs
	expectationOrigins ProductCacheMockSetProductExpectationOrigins
	results            *ProductCacheMockSetProductResults
	returnOrigin       string
	Counter            uint64
}

// ProductCacheMockSetProductParams contains parameters of the ProductCache.SetProduct
type ProductCacheMockSetProductParams struct {
	ctx     context.Context
	product productentity.Product
}

// ProductCacheMockSetProductParamPtrs contains pointers to parameters of the ProductCache.SetProduct
type ProductCacheMockSetProductParamPtrs struct {
	ctx     *context.Context
	product *productentity.Product
}

// ProductCacheMockSetProductResults contains results of the ProductCache.SetProduct
type ProductCacheMockSetProductResults struct {
	err error
}

// ProductCacheMockSetProductOrigins contains origins of expectations of the ProductCache.SetProduct
type ProductCacheMockSetProductExpectationOrigins struct {
	origin        string
	originCtx     string
	originProduct string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetProduct *mProductCacheMockSetProduct) Optional() *mProductCacheMockSetProduct {
	mmSetProduct.optional = true
	return mmSetProduct
}

// Expect sets up expected params for ProductCache.SetProduct
func (mmSetProduct *mProductCacheMockSetProduct) Expect(ctx context.Context, product productentity.Product) *mProductCacheMockSetProduct {
	if mmSetProduct.mock.funcSetProduct != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Set")
	}

	if mmSetProduct.defaultExpectation == nil {
		mmSetProduct.defaultExpectation = &ProductCacheMockSetProductExpectation{}
	}

	if mmSetProduct.defaultExpectation.paramPtrs != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by ExpectParams functions")
	}

	mmSetProduct.defaultExpectation.params = &ProductCacheMockSetProductParams{ctx, product}
	mmSetProduct.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetProduct.expectations {
		if minimock.Equal(e.params, mmSetProduct.defaultExpectation.params) {
			mmSetProduct.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetProduct.defaultExpectation.params)
		}
	}

	return mmSetProduct
}

// ExpectCtxParam1 sets up expected param ctx for ProductCache.SetProduct
func (mmSetProduct *mProductCacheMockSetProduct) ExpectCtxParam1(ctx context.Context) *mProductCacheMockSetProduct {
	if mmSetProduct.mock.funcSetProduct != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Set")
	}

	if mmSetProduct.defaultExpectation == nil {
		mmSetProduct.defaultExpectation = &ProductCacheMockSetProductExpectation{}
	}

	if mmSetProduct.defaultExpectation.params != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Expect")
	}

	if mmSetProduct.defaultExpectation.paramPtrs == nil {
		mmSetProduct.defaultExpectation.paramPtrs = &ProductCacheMockSetProductParamPtrs{}
	}
	mmSetProduct.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetProduct.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetProduct
}

// ExpectProductParam2 sets up expected param product for ProductCache.SetProduct
func (mmSetProduct *mProductCacheMockSetProduct) ExpectProductParam2(product productentity.Product) *mProductCacheMockSetProduct {
	if mmSetProduct.mock.funcSetProduct != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Set")
	}

	if mmSetProduct.defaultExpectation == nil {
		mmSetProduct.defaultExpectation = &ProductCacheMockSetProductExpectation{}
	}

	if mmSetProduct.defaultExpectation.params != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Expect")
	}

	if mmSetProduct.defaultExpectation.paramPtrs == nil {
		mmSetProduct.defaultExpectation.paramPtrs = &ProductCacheMockSetProductParamPtrs{}
	}
	mmSetProduct.defaultExpectation.paramPtrs.product = &product
	mmSetProduct.defaultExpectation.expectationOrigins.originProduct = minimock.CallerInfo(1)

	return mmSetProduct
}

// Inspect accepts an inspector function that has same arguments as the ProductCache.SetProduct
func (mmSetProduct *mProductCacheMockSetProduct) Inspect(f func(ctx context.Context, product productentity.Product)) *mProductCacheMockSetProduct {
	if mmSetProduct.mock.inspectFuncSetProduct != nil {
		mmSetProduct.mock.t.Fatalf("Inspect function is already set for ProductCacheMock.SetProduct")
	}

	mmSetProduct.mock.inspectFuncSetProduct = f

	return mmSetProduct
}

// Return sets up results that will be returned by ProductCache.SetProduct
func (mmSetProduct *mProductCacheMockSetProduct) Return(err error) *ProductCacheMock {
	if mmSetProduct.mock.funcSetProduct != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Set")
	}

	if mmSetProduct.defaultExpectation == nil {
		mmSetProduct.defaultExpectation = &ProductCacheMockSetProductExpectation{mock: mmSetProduct.mock}
	}
	mmSetProduct.defaultExpectation.results = &ProductCacheMockSetProductResults{err}
	mmSetProduct.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetProduct.mock
}

// Set uses given function f to mock the ProductCache.SetProduct method
func (mmSetProduct *mProductCacheMockSetProduct) Set(f func(ctx context.Context, product productentity.Product) (err error)) *ProductCacheMock {
	if mmSetProduct.defaultExpectation != nil {
		mmSetProduct.mock.t.Fatalf("Default expectation is already set for the ProductCache.SetProduct method")
	}

	if len(mmSetProduct.expectations) > 0 {
		mmSetProduct.mock.t.Fatalf("Some expectations are already set for the ProductCache.SetProduct method")
	}

	mmSetProduct.mock.funcSetProduct = f
	mmSetProduct.mock.funcSetProductOrigin = minimock.CallerInfo(1)
	return mmSetProduct.mock
}

// When sets expectation for the ProductCache.SetProduct which will trigger the result defined by the following
// Then helper
func (mmSetProduct *mProductCacheMockSetProduct) When(ctx context.Context, product productentity.Product) *ProductCacheMockSetProductExpectation {
	if mmSetProduct.mock.funcSetProduct != nil {
		mmSetProduct.mock.t.Fatalf("ProductCacheMock.SetProduct mock is already set by Set")
	}

	expectation := &ProductCacheMockSetProductExpectation{
		mock:               mmSetProduct.mock,
		params:             &ProductCacheMockSetProductParams{ctx, product},
		expectationOrigins: ProductCacheMockSetProductExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetProduct.expectations = append(mmSetProduct.expectations, expectation)
	return expectation
}

// Then sets up ProductCache.SetProduct return parameters for the expectation previously defined by the When method
func (e *ProductCacheMockSetProductExpectation) Then(err error) *ProductCacheMock {
	e.results = &ProductCacheMockSetProductResults{err}
	return e.mock
}

// Times sets number of times ProductCache.SetProduct should be invoked
func (mmSetProduct *mProductCacheMockSetProduct) Times(n uint64) *mProductCacheMockSetProduct {
	if n == 0 {
		mmSetProduct.mock.t.Fatalf("Times of ProductCacheMock.SetProduct mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetProduct.expectedInvocations, n)
	mmSetProduct.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetProduct
}

func (mmSetProduct *mProductCacheMockSetProduct) invocationsDone() bool {
	if len(mmSetProduct.expectations) == 0 && mmSetProduct.defaultExpectation == nil && mmSetProduct.mock.funcSetProduct == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetProduct.mock.afterSetProductCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetProduct.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetProduct implements mm_repository.ProductCache
func (mmSetProduct *ProductCacheMock) SetProduct(ctx context.Context, product productentity.Product) (err error) {
	mm_atomic.AddUint64(&mmSetProduct.beforeSetProductCounter, 1)
	defer mm_atomic.AddUint64(&mmSetProduct.afterSetProductCounter, 1)

	mmSetProduct.t.Helper()

	if mmSetProduct.inspectFuncSetProduct != nil {
		mmSetProduct.inspectFuncSetProduct(ctx, product)
	}

	mm_params := ProductCacheMockSetProductParams{ctx, product}

	// Record call args
	mmSetProduct.SetProductMock.mutex.Lock()
	mmSetProduct.SetProductMock.callArgs = append(mmSetProduct.SetProductMock.callArgs, &mm_params)
	mmSetProduct.SetProductMock.mutex.Unlock()

	for _, e := range mmSetProduct.SetProductMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetProduct.SetProductMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetProduct.SetProductMock.defaultExpectation.Counter, 1)
		mm_want := mmSetProduct.SetProductMock.defaultExpectation.params
		mm_want_ptrs := mmSetProduct.SetProductMock.defaultExpectation.paramPtrs

		mm_got := ProductCacheMockSetProductParams{ctx, product}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetProduct.t.Errorf("ProductCacheMock.SetProduct got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProduct.SetProductMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.product != nil && !minimock.Equal(*mm_want_ptrs.product, mm_got.product) {
				mmSetProduct.t.Errorf("ProductCacheMock.SetProduct got unexpected parameter product, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProduct.SetProductMock.defaultExpectation.expectationOrigins.originProduct, *mm_want_ptrs.product, mm_got.product, minimock.Diff(*mm_want_ptrs.product, mm_got.product))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetProduct.t.Errorf("ProductCacheMock.SetProduct got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetProduct.SetProductMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetProduct.SetProductMock.defaultExpectation.results
		if mm_results == nil {
			mmSetProduct.t.Fatal("No results are set for the ProductCacheMock.SetProduct")
		}
		return (*mm_results).err
	}
	if mmSetProduct.funcSetProduct != nil {
		return mmSetProduct.funcSetProduct(ctx, product)
	}
	mmSetProduct.t.Fatalf("Unexpected call to ProductCacheMock.SetProduct. %v %v", ctx, product)
	return
}

// SetProductAfterCounter returns a count of finished ProductCacheMock.SetProduct invocations
func (mmSetProduct *ProductCacheMock) SetProductAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetProduct.afterSetProductCounter)
}

// SetProductBeforeCounter returns a count of ProductCacheMock.SetProduct invocations
func (mmSetProduct *ProductCacheMock) SetProductBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetProduct.beforeSetProductCounter)
}

// Calls returns a list of arguments used in each call to ProductCacheMock.SetProduct.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetProduct *mProductCacheMockSetProduct) Calls() []*ProductCacheMockSetProductParams {
	mmSetProduct.mutex.RLock()

	argCopy := make([]*ProductCacheMockSetProductParams, len(mmSetProduct.callArgs))
	copy(argCopy, mmSetProduct.callArgs)

	mmSetProduct.mutex.RUnlock()

	return argCopy
}

// MinimockSetProductDone returns true if the count of the SetProduct invocations corresponds
// the number of defined expectations
func (m *ProductCacheMock) MinimockSetProductDone() bool {
	if m.SetProductMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetProductMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetProductMock.invocationsDone()
}

// MinimockSetProductInspect logs each unmet expectation
func (m *ProductCacheMock) MinimockSetProductInspect() {
	for _, e := range m.SetProductMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductCacheMock.SetProduct at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetProductCounter := mm_atomic.LoadUint64(&m.afterSetProductCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetProductMock.defaultExpectation != nil && afterSetProductCounter < 1 {
		if m.SetProductMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductCacheMock.SetProduct at\n%s", m.SetProductMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductCacheMock.SetProduct at\n%s with params: %#v", m.SetProductMock.defaultExpectation.expectationOrigins.origin, *m.SetProductMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetProduct != nil && afterSetProductCounter < 1 {
		m.t.Errorf("Expected call to ProductCacheMock.SetProduct at\n%s", m.funcSetProductOrigin)
	}

	if !m.SetProductMock.invocationsDone() && afterSetProductCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductCacheMock.SetProduct at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetProductMock.expectedInvocations), m.SetProductMock.expectedInvocationsOrigin, afterSetProductCounter)
	}
}

type mProductCacheMockSetProducts struct {
	optional           bool
	mock               *ProductCacheMock
	defaultExpectation *ProductCacheMockSetProductsExpectation
	expectations       []*ProductCacheMockSetProductsExpectation

	callArgs []*ProductCacheMockSetProductsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductCacheMockSetProductsExpectation specifies expectation struct of the ProductCache.SetProducts
type ProductCacheMockSetProductsExpectation struct {
	mock               *ProductCacheMock
	params             *ProductCacheMockSetProductsParams
	paramPtrs          *ProductCacheMockSetProductsParamPtrs
	expectationOrigins ProductCacheMockSetProductsExpectationOrigins
	results            *ProductCacheMockSetProductsResults
	returnOrigin       string
	Counter            uint64
}

// ProductCacheMockSetProductsParams contains parameters of the ProductCache.SetProducts
type ProductCacheMockSetProductsParams struct {
	ctx      context.Context
	products []productentity.Product
}

// ProductCacheMockSetProductsParamPtrs contains pointers to parameters of the ProductCache.SetProducts
type ProductCacheMockSetProductsParamPtrs struct {
	ctx      *context.Context
	products *[]productentity.Product
}

// ProductCacheMockSetProductsResults contains results of the ProductCache.SetProducts
type ProductCacheMockSetProductsResults struct {
	err error
}

// ProductCacheMockSetProductsOrigins contains origins of expectations of the ProductCache.SetProducts
type ProductCacheMockSetProductsExpectationOrigins struct {
	origin         string
	originCtx      string
	originProducts string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetProducts *mProductCacheMockSetProducts) Optional() *mProductCacheMockSetProducts {
	mmSetProducts.optional = true
	return mmSetProducts
}

// Expect sets up expected params for ProductCache.SetProducts
func (mmSetProducts *mProductCacheMockSetProducts) Expect(ctx context.Context, products []productentity.Product) *mProductCacheMockSetProducts {
	if mmSetProducts.mock.funcSetProducts != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Set")
	}

	if mmSetProducts.defaultExpectation == nil {
		mmSetProducts.defaultExpectation = &ProductCacheMockSetProductsExpectation{}
	}

	if mmSetProducts.defaultExpectation.paramPtrs != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by ExpectParams functions")
	}

	mmSetProducts.defaultExpectation.params = &ProductCacheMockSetProductsParams{ctx, products}
	mmSetProducts.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetProducts.expectations {
		if minimock.Equal(e.params, mmSetProducts.defaultExpectation.params) {
			mmSetProducts.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetProducts.defaultExpectation.params)
		}
	}

	return mmSetProducts
}

// ExpectCtxParam1 sets up expected param ctx for ProductCache.SetProducts
func (mmSetProducts *mProductCacheMockSetProducts) ExpectCtxParam1(ctx context.Context) *mProductCacheMockSetProducts {
	if mmSetProducts.mock.funcSetProducts != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Set")
	}

	if mmSetProducts.defaultExpectation == nil {
		mmSetProducts.defaultExpectation = &ProductCacheMockSetProductsExpectation{}
	}

	if mmSetProducts.defaultExpectation.params != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Expect")
	}

	if mmSetProducts.defaultExpectation.paramPtrs == nil {
		mmSetProducts.defaultExpectation.paramPtrs = &ProductCacheMockSetProductsParamPtrs{}
	}
	mmSetProducts.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetProducts.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetProducts
}

// ExpectProductsParam2 sets up expected param products for ProductCache.SetProducts
func (mmSetProducts *mProductCacheMockSetProducts) ExpectProductsParam2(products []productentity.Product) *mProductCacheMockSetProducts {
	if mmSetProducts.mock.funcSetProducts != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Set")
	}

	if mmSetProducts.defaultExpectation == nil {
		mmSetProducts.defaultExpectation = &ProductCacheMockSetProductsExpectation{}
	}

	if mmSetProducts.defaultExpectation.params != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Expect")
	}

	if mmSetProducts.defaultExpectation.paramPtrs == nil {
		mmSetProducts.defaultExpectation.paramPtrs = &ProductCacheMockSetProductsParamPtrs{}
	}
	mmSetProducts.defaultExpectation.paramPtrs.products = &products
	mmSetProducts.defaultExpectation.expectationOrigins.originProducts = minimock.CallerInfo(1)

	return mmSetProducts
}

// Inspect accepts an inspector function that has same arguments as the ProductCache.SetProducts
func (mmSetProducts *mProductCacheMockSetProducts) Inspect(f func(ctx context.Context, products []productentity.Product)) *mProductCacheMockSetProducts {
	if mmSetProducts.mock.inspectFuncSetProducts != nil {
		mmSetProducts.mock.t.Fatalf("Inspect function is already set for ProductCacheMock.SetProducts")
	}

	mmSetProducts.mock.inspectFuncSetProducts = f

	return mmSetProducts
}

// Return sets up results that will be returned by ProductCache.SetProducts
func (mmSetProducts *mProductCacheMockSetProducts) Return(err error) *ProductCacheMock {
	if mmSetProducts.mock.funcSetProducts != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Set")
	}

	if mmSetProducts.defaultExpectation == nil {
		mmSetProducts.defaultExpectation = &ProductCacheMockSetProductsExpectation{mock: mmSetProducts.mock}
	}
	mmSetProducts.defaultExpectation.results = &ProductCacheMockSetProductsResults{err}
	mmSetProducts.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetProducts.mock
}

// Set uses given function f to mock the ProductCache.SetProducts method
func (mmSetProducts *mProductCacheMockSetProducts) Set(f func(ctx context.Context, products []productentity.Product) (err error)) *ProductCacheMock {
	if mmSetProducts.defaultExpectation != nil {
		mmSetProducts.mock.t.Fatalf("Default expectation is already set for the ProductCache.SetProducts method")
	}

	if len(mmSetProducts.expectations) > 0 {
		mmSetProducts.mock.t.Fatalf("Some expectations are already set for the ProductCache.SetProducts method")
	}

	mmSetProducts.mock.funcSetProducts = f
	mmSetProducts.mock.funcSetProductsOrigin = minimock.CallerInfo(1)
	return mmSetProducts.mock
}

// When sets expectation for the ProductCache.SetProducts which will trigger the result defined by the following
// Then helper
func (mmSetProducts *mProductCacheMockSetProducts) When(ctx context.Context, products []productentity.Product) *ProductCacheMockSetProductsExpectation {
	if mmSetProducts.mock.funcSetProducts != nil {
		mmSetProducts.mock.t.Fatalf("ProductCacheMock.SetProducts mock is already set by Set")
	}

	expectation := &ProductCacheMockSetProductsExpectation{
		mock:               mmSetProducts.mock,
		params:             &ProductCacheMockSetProductsParams{ctx, products},
		expectationOrigins: ProductCacheMockSetProductsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetProducts.expectations = append(mmSetProducts.expectations, expectation)
	return expectation
}

// Then sets up ProductCache.SetProducts return parameters for the expectation previously defined by the When method
func (e *ProductCacheMockSetProductsExpectation) Then(err error) *ProductCacheMock {
	e.results = &ProductCacheMockSetProductsResults{err}
	return e.mock
}

// Times sets number of times ProductCache.SetProducts should be invoked
func (mmSetProducts *mProductCacheMockSetProducts) Times(n uint64) *mProductCacheMockSetProducts {
	if n == 0 {
		mmSetProducts.mock.t.Fatalf("Times of ProductCacheMock.SetProducts mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetProducts.expectedInvocations, n)
	mmSetProducts.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetProducts
}

func (mmSetProducts *mProductCacheMockSetProducts) invocationsDone() bool {
	if len(mmSetProducts.expectations) == 0 && mmSetProducts.defaultExpectation == nil && mmSetProducts.mock.funcSetProducts == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetProducts.mock.afterSetProductsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetProducts.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetProducts implements mm_repository.ProductCache
func (mmSetProducts *ProductCacheMock) SetProducts(ctx context.Context, products []productentity.Product) (err error) {
	mm_atomic.AddUint64(&mmSetProducts.beforeSetProductsCounter, 1)
	defer mm_atomic.AddUint64(&mmSetProducts.afterSetProductsCounter, 1)

	mmSetProducts.t.Helper()

	if mmSetProducts.inspectFuncSetProducts != nil {
		mmSetProducts.inspectFuncSetProducts(ctx, products)
	}

	mm_params := ProductCacheMockSetProductsParams{ctx, products}

	// Record call args
	mmSetProducts.SetProductsMock.mutex.Lock()
	mmSetProducts.SetProductsMock.callArgs = append(mmSetProducts.SetProductsMock.callArgs, &mm_params)
	mmSetProducts.SetProductsMock.mutex.Unlock()

	for _, e := range mmSetProducts.SetProductsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetProducts.SetProductsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetProducts.SetProductsMock.defaultExpectation.Counter, 1)
		mm_want := mmSetProducts.SetProductsMock.defaultExpectation.params
		mm_want_ptrs := mmSetProducts.SetProductsMock.defaultExpectation.paramPtrs

		mm_got := ProductCacheMockSetProductsParams{ctx, products}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetProducts.t.Errorf("ProductCacheMock.SetProducts got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProducts.SetProductsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.products != nil && !minimock.Equal(*mm_want_ptrs.products, mm_got.products) {
				mmSetProducts.t.Errorf("ProductCacheMock.SetProducts got unexpected parameter products, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetProducts.SetProductsMock.defaultExpectation.expectationOrigins.originProducts, *mm_want_ptrs.products, mm_got.products, minimock.Diff(*mm_want_ptrs.products, mm_got.products))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetProducts.t.Errorf("ProductCacheMock.SetProducts got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetProducts.SetProductsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetProducts.SetProductsMock.defaultExpectation.results
		if mm_results == nil {
			mmSetProducts.t.Fatal("No results are set for the ProductCacheMock.SetProducts")
		}
		return (*mm_results).err
	}
	if mmSetProducts.funcSetProducts != nil {
		return mmSetProducts.funcSetProducts(ctx, products)
	}
	mmSetProducts.t.Fatalf("Unexpected call to ProductCacheMock.SetProducts. %v %v", ctx, products)
	return
}

// SetProductsAfterCounter returns a count of finished ProductCacheMock.SetProducts invocations
func (mmSetProducts *ProductCacheMock) SetProductsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetProducts.afterSetProductsCounter)
}

// SetProductsBeforeCounter returns a count of ProductCacheMock.SetProducts invocations
func (mmSetProducts *ProductCacheMock) SetProductsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetProducts.beforeSetProductsCounter)
}

// Calls returns a list of arguments used in each call to ProductCacheMock.SetProducts.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetProducts *mProductCacheMockSetProducts) Calls() []*ProductCacheMockSetProductsParams {
	mmSetProducts.mutex.RLock()

	argCopy := make([]*ProductCacheMockSetProductsParams, len(mmSetProducts.callArgs))
	copy(argCopy, mmSetProducts.callArgs)

	mmSetProducts.mutex.RUnlock()

	return argCopy
}

// MinimockSetProductsDone returns true if the count of the SetProducts invocations corresponds
// the number of defined expectations
func (m *ProductCacheMock) MinimockSetProductsDone() bool {
	if m.SetProductsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetProductsMock.invocationsDone()
}

// MinimockSetProductsInspect logs each unmet expectation
func (m *ProductCacheMock) MinimockSetProductsInspect() {
	for _, e := range m.SetProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductCacheMock.SetProducts at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetProductsCounter := mm_atomic.LoadUint64(&m.afterSetProductsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetProductsMock.defaultExpectation != nil && afterSetProductsCounter < 1 {
		if m.SetProductsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductCacheMock.SetProducts at\n%s", m.SetProductsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductCacheMock.SetProducts at\n%s with params: %#v", m.SetProductsMock.defaultExpectation.expectationOrigins.origin, *m.SetProductsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetProducts != nil && afterSetProductsCounter < 1 {
		m.t.Errorf("Expected call to ProductCacheMock.SetProducts at\n%s", m.funcSetProductsOrigin)
	}

	if !m.SetProductsMock.invocationsDone() && afterSetProductsCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductCacheMock.SetProducts at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetProductsMock.expectedInvocations), m.SetProductsMock.expectedInvocationsOrigin, afterSetProductsCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProductCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockDeleteProductInspect()

			m.MinimockDeleteProductsInspect()

			m.MinimockGetProductInspect()

			m.MinimockGetProductsInspect()

			m.MinimockSetProductInspect()

			m.MinimockSetProductsInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProductCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProductCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockDeleteProductDone() &&
		m.MinimockDeleteProductsDone() &&
		m.MinimockGetProductDone() &&
		m.MinimockGetProductsDone() &&
		m.MinimockSetProductDone() &&
		m.MinimockSetProductsDone()
}
