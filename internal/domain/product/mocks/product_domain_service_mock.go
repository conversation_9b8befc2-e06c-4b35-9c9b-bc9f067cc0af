// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/service.ProductDomainService -o product_domain_service_mock.go -n ProductDomainServiceMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"github.com/gojuno/minimock/v3"
)

// ProductDomainServiceMock implements mm_service.ProductDomainService
type ProductDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string) (p1 productentity.Product, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mProductDomainServiceMockCreate

	funcGetAll          func() (pa1 []productentity.Product, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mProductDomainServiceMockGetAll

	funcGetAllAsAdmin          func() (aa1 []productentity.AdminProduct, err error)
	funcGetAllAsAdminOrigin    string
	inspectFuncGetAllAsAdmin   func()
	afterGetAllAsAdminCounter  uint64
	beforeGetAllAsAdminCounter uint64
	GetAllAsAdminMock          mProductDomainServiceMockGetAllAsAdmin

	funcGetBasicByUserID          func(userID int64) (pa1 []productentity.ProductBasic, err error)
	funcGetBasicByUserIDOrigin    string
	inspectFuncGetBasicByUserID   func(userID int64)
	afterGetBasicByUserIDCounter  uint64
	beforeGetBasicByUserIDCounter uint64
	GetBasicByUserIDMock          mProductDomainServiceMockGetBasicByUserID

	funcGetByID          func(id int64) (p1 productentity.Product, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mProductDomainServiceMockGetByID

	funcGetByIID          func(iid string) (p1 productentity.Product, err error)
	funcGetByIIDOrigin    string
	inspectFuncGetByIID   func(iid string)
	afterGetByIIDCounter  uint64
	beforeGetByIIDCounter uint64
	GetByIIDMock          mProductDomainServiceMockGetByIID

	funcGetByUserID          func(userID int64) (pa1 []productentity.Product, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mProductDomainServiceMockGetByUserID

	funcGetOwnersByProductIDs          func(productIDs []int64) (m1 map[int64][]productentity.Owner, err error)
	funcGetOwnersByProductIDsOrigin    string
	inspectFuncGetOwnersByProductIDs   func(productIDs []int64)
	afterGetOwnersByProductIDsCounter  uint64
	beforeGetOwnersByProductIDsCounter uint64
	GetOwnersByProductIDsMock          mProductDomainServiceMockGetOwnersByProductIDs

	funcRemoveLinksWithGroups          func(ctx context.Context, productID int64, groupIDs []int64) (err error)
	funcRemoveLinksWithGroupsOrigin    string
	inspectFuncRemoveLinksWithGroups   func(ctx context.Context, productID int64, groupIDs []int64)
	afterRemoveLinksWithGroupsCounter  uint64
	beforeRemoveLinksWithGroupsCounter uint64
	RemoveLinksWithGroupsMock          mProductDomainServiceMockRemoveLinksWithGroups

	funcRemoveLinksWithRoles          func(ctx context.Context, productID int64, roleIDs []int64) (err error)
	funcRemoveLinksWithRolesOrigin    string
	inspectFuncRemoveLinksWithRoles   func(ctx context.Context, productID int64, roleIDs []int64)
	afterRemoveLinksWithRolesCounter  uint64
	beforeRemoveLinksWithRolesCounter uint64
	RemoveLinksWithRolesMock          mProductDomainServiceMockRemoveLinksWithRoles

	funcUpdate          func(data productentity.ProductUpdateData) (p1 productentity.Product, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(data productentity.ProductUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mProductDomainServiceMockUpdate

	funcUpdateLinksWithParticipants          func(ctx context.Context, productID int64, participantIDs []string) (err error)
	funcUpdateLinksWithParticipantsOrigin    string
	inspectFuncUpdateLinksWithParticipants   func(ctx context.Context, productID int64, participantIDs []string)
	afterUpdateLinksWithParticipantsCounter  uint64
	beforeUpdateLinksWithParticipantsCounter uint64
	UpdateLinksWithParticipantsMock          mProductDomainServiceMockUpdateLinksWithParticipants
}

// NewProductDomainServiceMock returns a mock for mm_service.ProductDomainService
func NewProductDomainServiceMock(t minimock.Tester) *ProductDomainServiceMock {
	m := &ProductDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mProductDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*ProductDomainServiceMockCreateParams{}

	m.GetAllMock = mProductDomainServiceMockGetAll{mock: m}

	m.GetAllAsAdminMock = mProductDomainServiceMockGetAllAsAdmin{mock: m}

	m.GetBasicByUserIDMock = mProductDomainServiceMockGetBasicByUserID{mock: m}
	m.GetBasicByUserIDMock.callArgs = []*ProductDomainServiceMockGetBasicByUserIDParams{}

	m.GetByIDMock = mProductDomainServiceMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ProductDomainServiceMockGetByIDParams{}

	m.GetByIIDMock = mProductDomainServiceMockGetByIID{mock: m}
	m.GetByIIDMock.callArgs = []*ProductDomainServiceMockGetByIIDParams{}

	m.GetByUserIDMock = mProductDomainServiceMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*ProductDomainServiceMockGetByUserIDParams{}

	m.GetOwnersByProductIDsMock = mProductDomainServiceMockGetOwnersByProductIDs{mock: m}
	m.GetOwnersByProductIDsMock.callArgs = []*ProductDomainServiceMockGetOwnersByProductIDsParams{}

	m.RemoveLinksWithGroupsMock = mProductDomainServiceMockRemoveLinksWithGroups{mock: m}
	m.RemoveLinksWithGroupsMock.callArgs = []*ProductDomainServiceMockRemoveLinksWithGroupsParams{}

	m.RemoveLinksWithRolesMock = mProductDomainServiceMockRemoveLinksWithRoles{mock: m}
	m.RemoveLinksWithRolesMock.callArgs = []*ProductDomainServiceMockRemoveLinksWithRolesParams{}

	m.UpdateMock = mProductDomainServiceMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*ProductDomainServiceMockUpdateParams{}

	m.UpdateLinksWithParticipantsMock = mProductDomainServiceMockUpdateLinksWithParticipants{mock: m}
	m.UpdateLinksWithParticipantsMock.callArgs = []*ProductDomainServiceMockUpdateLinksWithParticipantsParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProductDomainServiceMockCreate struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockCreateExpectation
	expectations       []*ProductDomainServiceMockCreateExpectation

	callArgs []*ProductDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockCreateExpectation specifies expectation struct of the ProductDomainService.Create
type ProductDomainServiceMockCreateExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockCreateParams
	paramPtrs          *ProductDomainServiceMockCreateParamPtrs
	expectationOrigins ProductDomainServiceMockCreateExpectationOrigins
	results            *ProductDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockCreateParams contains parameters of the ProductDomainService.Create
type ProductDomainServiceMockCreateParams struct {
	ctx         context.Context
	data        productentity.ProductCreateData
	ownerEmails []string
}

// ProductDomainServiceMockCreateParamPtrs contains pointers to parameters of the ProductDomainService.Create
type ProductDomainServiceMockCreateParamPtrs struct {
	ctx         *context.Context
	data        *productentity.ProductCreateData
	ownerEmails *[]string
}

// ProductDomainServiceMockCreateResults contains results of the ProductDomainService.Create
type ProductDomainServiceMockCreateResults struct {
	p1  productentity.Product
	err error
}

// ProductDomainServiceMockCreateOrigins contains origins of expectations of the ProductDomainService.Create
type ProductDomainServiceMockCreateExpectationOrigins struct {
	origin            string
	originCtx         string
	originData        string
	originOwnerEmails string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mProductDomainServiceMockCreate) Optional() *mProductDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ProductDomainService.Create
func (mmCreate *mProductDomainServiceMockCreate) Expect(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string) *mProductDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ProductDomainServiceMockCreateParams{ctx, data, ownerEmails}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for ProductDomainService.Create
func (mmCreate *mProductDomainServiceMockCreate) ExpectCtxParam1(ctx context.Context) *mProductDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProductDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectDataParam2 sets up expected param data for ProductDomainService.Create
func (mmCreate *mProductDomainServiceMockCreate) ExpectDataParam2(data productentity.ProductCreateData) *mProductDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProductDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.data = &data
	mmCreate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectOwnerEmailsParam3 sets up expected param ownerEmails for ProductDomainService.Create
func (mmCreate *mProductDomainServiceMockCreate) ExpectOwnerEmailsParam3(ownerEmails []string) *mProductDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProductDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ownerEmails = &ownerEmails
	mmCreate.defaultExpectation.expectationOrigins.originOwnerEmails = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.Create
func (mmCreate *mProductDomainServiceMockCreate) Inspect(f func(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string)) *mProductDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ProductDomainService.Create
func (mmCreate *mProductDomainServiceMockCreate) Return(p1 productentity.Product, err error) *ProductDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ProductDomainServiceMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ProductDomainService.Create method
func (mmCreate *mProductDomainServiceMockCreate) Set(f func(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string) (p1 productentity.Product, err error)) *ProductDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ProductDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mProductDomainServiceMockCreate) When(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string) *ProductDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ProductDomainServiceMockCreateParams{ctx, data, ownerEmails},
		expectationOrigins: ProductDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.Create return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockCreateExpectation) Then(p1 productentity.Product, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.Create should be invoked
func (mmCreate *mProductDomainServiceMockCreate) Times(n uint64) *mProductDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ProductDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mProductDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.ProductDomainService
func (mmCreate *ProductDomainServiceMock) Create(ctx context.Context, data productentity.ProductCreateData, ownerEmails []string) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, data, ownerEmails)
	}

	mm_params := ProductDomainServiceMockCreateParams{ctx, data, ownerEmails}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockCreateParams{ctx, data, ownerEmails}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("ProductDomainServiceMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreate.t.Errorf("ProductDomainServiceMock.Create got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

			if mm_want_ptrs.ownerEmails != nil && !minimock.Equal(*mm_want_ptrs.ownerEmails, mm_got.ownerEmails) {
				mmCreate.t.Errorf("ProductDomainServiceMock.Create got unexpected parameter ownerEmails, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originOwnerEmails, *mm_want_ptrs.ownerEmails, mm_got.ownerEmails, minimock.Diff(*mm_want_ptrs.ownerEmails, mm_got.ownerEmails))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ProductDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ProductDomainServiceMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, data, ownerEmails)
	}
	mmCreate.t.Fatalf("Unexpected call to ProductDomainServiceMock.Create. %v %v %v", ctx, data, ownerEmails)
	return
}

// CreateAfterCounter returns a count of finished ProductDomainServiceMock.Create invocations
func (mmCreate *ProductDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ProductDomainServiceMock.Create invocations
func (mmCreate *ProductDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mProductDomainServiceMockCreate) Calls() []*ProductDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mProductDomainServiceMockGetAll struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetAllExpectation
	expectations       []*ProductDomainServiceMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetAllExpectation specifies expectation struct of the ProductDomainService.GetAll
type ProductDomainServiceMockGetAllExpectation struct {
	mock *ProductDomainServiceMock

	results      *ProductDomainServiceMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// ProductDomainServiceMockGetAllResults contains results of the ProductDomainService.GetAll
type ProductDomainServiceMockGetAllResults struct {
	pa1 []productentity.Product
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mProductDomainServiceMockGetAll) Optional() *mProductDomainServiceMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for ProductDomainService.GetAll
func (mmGetAll *mProductDomainServiceMockGetAll) Expect() *mProductDomainServiceMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ProductDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ProductDomainServiceMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetAll
func (mmGetAll *mProductDomainServiceMockGetAll) Inspect(f func()) *mProductDomainServiceMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by ProductDomainService.GetAll
func (mmGetAll *mProductDomainServiceMockGetAll) Return(pa1 []productentity.Product, err error) *ProductDomainServiceMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ProductDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ProductDomainServiceMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &ProductDomainServiceMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the ProductDomainService.GetAll method
func (mmGetAll *mProductDomainServiceMockGetAll) Set(f func() (pa1 []productentity.Product, err error)) *ProductDomainServiceMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times ProductDomainService.GetAll should be invoked
func (mmGetAll *mProductDomainServiceMockGetAll) Times(n uint64) *mProductDomainServiceMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of ProductDomainServiceMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mProductDomainServiceMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_service.ProductDomainService
func (mmGetAll *ProductDomainServiceMock) GetAll() (pa1 []productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the ProductDomainServiceMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished ProductDomainServiceMock.GetAll invocations
func (mmGetAll *ProductDomainServiceMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of ProductDomainServiceMock.GetAll invocations
func (mmGetAll *ProductDomainServiceMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ProductDomainServiceMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mProductDomainServiceMockGetAllAsAdmin struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetAllAsAdminExpectation
	expectations       []*ProductDomainServiceMockGetAllAsAdminExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetAllAsAdminExpectation specifies expectation struct of the ProductDomainService.GetAllAsAdmin
type ProductDomainServiceMockGetAllAsAdminExpectation struct {
	mock *ProductDomainServiceMock

	results      *ProductDomainServiceMockGetAllAsAdminResults
	returnOrigin string
	Counter      uint64
}

// ProductDomainServiceMockGetAllAsAdminResults contains results of the ProductDomainService.GetAllAsAdmin
type ProductDomainServiceMockGetAllAsAdminResults struct {
	aa1 []productentity.AdminProduct
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) Optional() *mProductDomainServiceMockGetAllAsAdmin {
	mmGetAllAsAdmin.optional = true
	return mmGetAllAsAdmin
}

// Expect sets up expected params for ProductDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) Expect() *mProductDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("ProductDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &ProductDomainServiceMockGetAllAsAdminExpectation{}
	}

	return mmGetAllAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) Inspect(f func()) *mProductDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetAllAsAdmin")
	}

	mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin = f

	return mmGetAllAsAdmin
}

// Return sets up results that will be returned by ProductDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) Return(aa1 []productentity.AdminProduct, err error) *ProductDomainServiceMock {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("ProductDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &ProductDomainServiceMockGetAllAsAdminExpectation{mock: mmGetAllAsAdmin.mock}
	}
	mmGetAllAsAdmin.defaultExpectation.results = &ProductDomainServiceMockGetAllAsAdminResults{aa1, err}
	mmGetAllAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Set uses given function f to mock the ProductDomainService.GetAllAsAdmin method
func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) Set(f func() (aa1 []productentity.AdminProduct, err error)) *ProductDomainServiceMock {
	if mmGetAllAsAdmin.defaultExpectation != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetAllAsAdmin method")
	}

	if len(mmGetAllAsAdmin.expectations) > 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetAllAsAdmin method")
	}

	mmGetAllAsAdmin.mock.funcGetAllAsAdmin = f
	mmGetAllAsAdmin.mock.funcGetAllAsAdminOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Times sets number of times ProductDomainService.GetAllAsAdmin should be invoked
func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) Times(n uint64) *mProductDomainServiceMockGetAllAsAdmin {
	if n == 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Times of ProductDomainServiceMock.GetAllAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllAsAdmin.expectedInvocations, n)
	mmGetAllAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin
}

func (mmGetAllAsAdmin *mProductDomainServiceMockGetAllAsAdmin) invocationsDone() bool {
	if len(mmGetAllAsAdmin.expectations) == 0 && mmGetAllAsAdmin.defaultExpectation == nil && mmGetAllAsAdmin.mock.funcGetAllAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.mock.afterGetAllAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllAsAdmin implements mm_service.ProductDomainService
func (mmGetAllAsAdmin *ProductDomainServiceMock) GetAllAsAdmin() (aa1 []productentity.AdminProduct, err error) {
	mm_atomic.AddUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter, 1)

	mmGetAllAsAdmin.t.Helper()

	if mmGetAllAsAdmin.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.inspectFuncGetAllAsAdmin()
	}

	if mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllAsAdmin.t.Fatal("No results are set for the ProductDomainServiceMock.GetAllAsAdmin")
		}
		return (*mm_results).aa1, (*mm_results).err
	}
	if mmGetAllAsAdmin.funcGetAllAsAdmin != nil {
		return mmGetAllAsAdmin.funcGetAllAsAdmin()
	}
	mmGetAllAsAdmin.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetAllAsAdmin.")
	return
}

// GetAllAsAdminAfterCounter returns a count of finished ProductDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *ProductDomainServiceMock) GetAllAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter)
}

// GetAllAsAdminBeforeCounter returns a count of ProductDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *ProductDomainServiceMock) GetAllAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter)
}

// MinimockGetAllAsAdminDone returns true if the count of the GetAllAsAdmin invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetAllAsAdminDone() bool {
	if m.GetAllAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllAsAdminMock.invocationsDone()
}

// MinimockGetAllAsAdminInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetAllAsAdminInspect() {
	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ProductDomainServiceMock.GetAllAsAdmin")
		}
	}

	afterGetAllAsAdminCounter := mm_atomic.LoadUint64(&m.afterGetAllAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllAsAdminMock.defaultExpectation != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetAllAsAdmin at\n%s", m.GetAllAsAdminMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllAsAdmin != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetAllAsAdmin at\n%s", m.funcGetAllAsAdminOrigin)
	}

	if !m.GetAllAsAdminMock.invocationsDone() && afterGetAllAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetAllAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllAsAdminMock.expectedInvocations), m.GetAllAsAdminMock.expectedInvocationsOrigin, afterGetAllAsAdminCounter)
	}
}

type mProductDomainServiceMockGetBasicByUserID struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetBasicByUserIDExpectation
	expectations       []*ProductDomainServiceMockGetBasicByUserIDExpectation

	callArgs []*ProductDomainServiceMockGetBasicByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetBasicByUserIDExpectation specifies expectation struct of the ProductDomainService.GetBasicByUserID
type ProductDomainServiceMockGetBasicByUserIDExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockGetBasicByUserIDParams
	paramPtrs          *ProductDomainServiceMockGetBasicByUserIDParamPtrs
	expectationOrigins ProductDomainServiceMockGetBasicByUserIDExpectationOrigins
	results            *ProductDomainServiceMockGetBasicByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockGetBasicByUserIDParams contains parameters of the ProductDomainService.GetBasicByUserID
type ProductDomainServiceMockGetBasicByUserIDParams struct {
	userID int64
}

// ProductDomainServiceMockGetBasicByUserIDParamPtrs contains pointers to parameters of the ProductDomainService.GetBasicByUserID
type ProductDomainServiceMockGetBasicByUserIDParamPtrs struct {
	userID *int64
}

// ProductDomainServiceMockGetBasicByUserIDResults contains results of the ProductDomainService.GetBasicByUserID
type ProductDomainServiceMockGetBasicByUserIDResults struct {
	pa1 []productentity.ProductBasic
	err error
}

// ProductDomainServiceMockGetBasicByUserIDOrigins contains origins of expectations of the ProductDomainService.GetBasicByUserID
type ProductDomainServiceMockGetBasicByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Optional() *mProductDomainServiceMockGetBasicByUserID {
	mmGetBasicByUserID.optional = true
	return mmGetBasicByUserID
}

// Expect sets up expected params for ProductDomainService.GetBasicByUserID
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Expect(userID int64) *mProductDomainServiceMockGetBasicByUserID {
	if mmGetBasicByUserID.mock.funcGetBasicByUserID != nil {
		mmGetBasicByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetBasicByUserID mock is already set by Set")
	}

	if mmGetBasicByUserID.defaultExpectation == nil {
		mmGetBasicByUserID.defaultExpectation = &ProductDomainServiceMockGetBasicByUserIDExpectation{}
	}

	if mmGetBasicByUserID.defaultExpectation.paramPtrs != nil {
		mmGetBasicByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetBasicByUserID mock is already set by ExpectParams functions")
	}

	mmGetBasicByUserID.defaultExpectation.params = &ProductDomainServiceMockGetBasicByUserIDParams{userID}
	mmGetBasicByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetBasicByUserID.expectations {
		if minimock.Equal(e.params, mmGetBasicByUserID.defaultExpectation.params) {
			mmGetBasicByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetBasicByUserID.defaultExpectation.params)
		}
	}

	return mmGetBasicByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ProductDomainService.GetBasicByUserID
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) ExpectUserIDParam1(userID int64) *mProductDomainServiceMockGetBasicByUserID {
	if mmGetBasicByUserID.mock.funcGetBasicByUserID != nil {
		mmGetBasicByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetBasicByUserID mock is already set by Set")
	}

	if mmGetBasicByUserID.defaultExpectation == nil {
		mmGetBasicByUserID.defaultExpectation = &ProductDomainServiceMockGetBasicByUserIDExpectation{}
	}

	if mmGetBasicByUserID.defaultExpectation.params != nil {
		mmGetBasicByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetBasicByUserID mock is already set by Expect")
	}

	if mmGetBasicByUserID.defaultExpectation.paramPtrs == nil {
		mmGetBasicByUserID.defaultExpectation.paramPtrs = &ProductDomainServiceMockGetBasicByUserIDParamPtrs{}
	}
	mmGetBasicByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetBasicByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetBasicByUserID
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetBasicByUserID
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Inspect(f func(userID int64)) *mProductDomainServiceMockGetBasicByUserID {
	if mmGetBasicByUserID.mock.inspectFuncGetBasicByUserID != nil {
		mmGetBasicByUserID.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetBasicByUserID")
	}

	mmGetBasicByUserID.mock.inspectFuncGetBasicByUserID = f

	return mmGetBasicByUserID
}

// Return sets up results that will be returned by ProductDomainService.GetBasicByUserID
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Return(pa1 []productentity.ProductBasic, err error) *ProductDomainServiceMock {
	if mmGetBasicByUserID.mock.funcGetBasicByUserID != nil {
		mmGetBasicByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetBasicByUserID mock is already set by Set")
	}

	if mmGetBasicByUserID.defaultExpectation == nil {
		mmGetBasicByUserID.defaultExpectation = &ProductDomainServiceMockGetBasicByUserIDExpectation{mock: mmGetBasicByUserID.mock}
	}
	mmGetBasicByUserID.defaultExpectation.results = &ProductDomainServiceMockGetBasicByUserIDResults{pa1, err}
	mmGetBasicByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetBasicByUserID.mock
}

// Set uses given function f to mock the ProductDomainService.GetBasicByUserID method
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Set(f func(userID int64) (pa1 []productentity.ProductBasic, err error)) *ProductDomainServiceMock {
	if mmGetBasicByUserID.defaultExpectation != nil {
		mmGetBasicByUserID.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetBasicByUserID method")
	}

	if len(mmGetBasicByUserID.expectations) > 0 {
		mmGetBasicByUserID.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetBasicByUserID method")
	}

	mmGetBasicByUserID.mock.funcGetBasicByUserID = f
	mmGetBasicByUserID.mock.funcGetBasicByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetBasicByUserID.mock
}

// When sets expectation for the ProductDomainService.GetBasicByUserID which will trigger the result defined by the following
// Then helper
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) When(userID int64) *ProductDomainServiceMockGetBasicByUserIDExpectation {
	if mmGetBasicByUserID.mock.funcGetBasicByUserID != nil {
		mmGetBasicByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetBasicByUserID mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockGetBasicByUserIDExpectation{
		mock:               mmGetBasicByUserID.mock,
		params:             &ProductDomainServiceMockGetBasicByUserIDParams{userID},
		expectationOrigins: ProductDomainServiceMockGetBasicByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetBasicByUserID.expectations = append(mmGetBasicByUserID.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.GetBasicByUserID return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockGetBasicByUserIDExpectation) Then(pa1 []productentity.ProductBasic, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockGetBasicByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.GetBasicByUserID should be invoked
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Times(n uint64) *mProductDomainServiceMockGetBasicByUserID {
	if n == 0 {
		mmGetBasicByUserID.mock.t.Fatalf("Times of ProductDomainServiceMock.GetBasicByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetBasicByUserID.expectedInvocations, n)
	mmGetBasicByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetBasicByUserID
}

func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) invocationsDone() bool {
	if len(mmGetBasicByUserID.expectations) == 0 && mmGetBasicByUserID.defaultExpectation == nil && mmGetBasicByUserID.mock.funcGetBasicByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetBasicByUserID.mock.afterGetBasicByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetBasicByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetBasicByUserID implements mm_service.ProductDomainService
func (mmGetBasicByUserID *ProductDomainServiceMock) GetBasicByUserID(userID int64) (pa1 []productentity.ProductBasic, err error) {
	mm_atomic.AddUint64(&mmGetBasicByUserID.beforeGetBasicByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetBasicByUserID.afterGetBasicByUserIDCounter, 1)

	mmGetBasicByUserID.t.Helper()

	if mmGetBasicByUserID.inspectFuncGetBasicByUserID != nil {
		mmGetBasicByUserID.inspectFuncGetBasicByUserID(userID)
	}

	mm_params := ProductDomainServiceMockGetBasicByUserIDParams{userID}

	// Record call args
	mmGetBasicByUserID.GetBasicByUserIDMock.mutex.Lock()
	mmGetBasicByUserID.GetBasicByUserIDMock.callArgs = append(mmGetBasicByUserID.GetBasicByUserIDMock.callArgs, &mm_params)
	mmGetBasicByUserID.GetBasicByUserIDMock.mutex.Unlock()

	for _, e := range mmGetBasicByUserID.GetBasicByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockGetBasicByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetBasicByUserID.t.Errorf("ProductDomainServiceMock.GetBasicByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetBasicByUserID.t.Errorf("ProductDomainServiceMock.GetBasicByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetBasicByUserID.GetBasicByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetBasicByUserID.t.Fatal("No results are set for the ProductDomainServiceMock.GetBasicByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetBasicByUserID.funcGetBasicByUserID != nil {
		return mmGetBasicByUserID.funcGetBasicByUserID(userID)
	}
	mmGetBasicByUserID.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetBasicByUserID. %v", userID)
	return
}

// GetBasicByUserIDAfterCounter returns a count of finished ProductDomainServiceMock.GetBasicByUserID invocations
func (mmGetBasicByUserID *ProductDomainServiceMock) GetBasicByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetBasicByUserID.afterGetBasicByUserIDCounter)
}

// GetBasicByUserIDBeforeCounter returns a count of ProductDomainServiceMock.GetBasicByUserID invocations
func (mmGetBasicByUserID *ProductDomainServiceMock) GetBasicByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetBasicByUserID.beforeGetBasicByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.GetBasicByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetBasicByUserID *mProductDomainServiceMockGetBasicByUserID) Calls() []*ProductDomainServiceMockGetBasicByUserIDParams {
	mmGetBasicByUserID.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockGetBasicByUserIDParams, len(mmGetBasicByUserID.callArgs))
	copy(argCopy, mmGetBasicByUserID.callArgs)

	mmGetBasicByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetBasicByUserIDDone returns true if the count of the GetBasicByUserID invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetBasicByUserIDDone() bool {
	if m.GetBasicByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetBasicByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetBasicByUserIDMock.invocationsDone()
}

// MinimockGetBasicByUserIDInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetBasicByUserIDInspect() {
	for _, e := range m.GetBasicByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetBasicByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetBasicByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetBasicByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetBasicByUserIDMock.defaultExpectation != nil && afterGetBasicByUserIDCounter < 1 {
		if m.GetBasicByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetBasicByUserID at\n%s", m.GetBasicByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetBasicByUserID at\n%s with params: %#v", m.GetBasicByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetBasicByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetBasicByUserID != nil && afterGetBasicByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetBasicByUserID at\n%s", m.funcGetBasicByUserIDOrigin)
	}

	if !m.GetBasicByUserIDMock.invocationsDone() && afterGetBasicByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetBasicByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetBasicByUserIDMock.expectedInvocations), m.GetBasicByUserIDMock.expectedInvocationsOrigin, afterGetBasicByUserIDCounter)
	}
}

type mProductDomainServiceMockGetByID struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetByIDExpectation
	expectations       []*ProductDomainServiceMockGetByIDExpectation

	callArgs []*ProductDomainServiceMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetByIDExpectation specifies expectation struct of the ProductDomainService.GetByID
type ProductDomainServiceMockGetByIDExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockGetByIDParams
	paramPtrs          *ProductDomainServiceMockGetByIDParamPtrs
	expectationOrigins ProductDomainServiceMockGetByIDExpectationOrigins
	results            *ProductDomainServiceMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockGetByIDParams contains parameters of the ProductDomainService.GetByID
type ProductDomainServiceMockGetByIDParams struct {
	id int64
}

// ProductDomainServiceMockGetByIDParamPtrs contains pointers to parameters of the ProductDomainService.GetByID
type ProductDomainServiceMockGetByIDParamPtrs struct {
	id *int64
}

// ProductDomainServiceMockGetByIDResults contains results of the ProductDomainService.GetByID
type ProductDomainServiceMockGetByIDResults struct {
	p1  productentity.Product
	err error
}

// ProductDomainServiceMockGetByIDOrigins contains origins of expectations of the ProductDomainService.GetByID
type ProductDomainServiceMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mProductDomainServiceMockGetByID) Optional() *mProductDomainServiceMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ProductDomainService.GetByID
func (mmGetByID *mProductDomainServiceMockGetByID) Expect(id int64) *mProductDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProductDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ProductDomainServiceMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ProductDomainServiceMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ProductDomainService.GetByID
func (mmGetByID *mProductDomainServiceMockGetByID) ExpectIdParam1(id int64) *mProductDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProductDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ProductDomainServiceMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ProductDomainServiceMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetByID
func (mmGetByID *mProductDomainServiceMockGetByID) Inspect(f func(id int64)) *mProductDomainServiceMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ProductDomainService.GetByID
func (mmGetByID *mProductDomainServiceMockGetByID) Return(p1 productentity.Product, err error) *ProductDomainServiceMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProductDomainServiceMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ProductDomainServiceMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ProductDomainService.GetByID method
func (mmGetByID *mProductDomainServiceMockGetByID) Set(f func(id int64) (p1 productentity.Product, err error)) *ProductDomainServiceMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ProductDomainService.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mProductDomainServiceMockGetByID) When(id int64) *ProductDomainServiceMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductDomainServiceMock.GetByID mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ProductDomainServiceMockGetByIDParams{id},
		expectationOrigins: ProductDomainServiceMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.GetByID return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockGetByIDExpectation) Then(p1 productentity.Product, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.GetByID should be invoked
func (mmGetByID *mProductDomainServiceMockGetByID) Times(n uint64) *mProductDomainServiceMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ProductDomainServiceMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mProductDomainServiceMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_service.ProductDomainService
func (mmGetByID *ProductDomainServiceMock) GetByID(id int64) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ProductDomainServiceMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ProductDomainServiceMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ProductDomainServiceMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ProductDomainServiceMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ProductDomainServiceMock.GetByID invocations
func (mmGetByID *ProductDomainServiceMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ProductDomainServiceMock.GetByID invocations
func (mmGetByID *ProductDomainServiceMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mProductDomainServiceMockGetByID) Calls() []*ProductDomainServiceMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mProductDomainServiceMockGetByIID struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetByIIDExpectation
	expectations       []*ProductDomainServiceMockGetByIIDExpectation

	callArgs []*ProductDomainServiceMockGetByIIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetByIIDExpectation specifies expectation struct of the ProductDomainService.GetByIID
type ProductDomainServiceMockGetByIIDExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockGetByIIDParams
	paramPtrs          *ProductDomainServiceMockGetByIIDParamPtrs
	expectationOrigins ProductDomainServiceMockGetByIIDExpectationOrigins
	results            *ProductDomainServiceMockGetByIIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockGetByIIDParams contains parameters of the ProductDomainService.GetByIID
type ProductDomainServiceMockGetByIIDParams struct {
	iid string
}

// ProductDomainServiceMockGetByIIDParamPtrs contains pointers to parameters of the ProductDomainService.GetByIID
type ProductDomainServiceMockGetByIIDParamPtrs struct {
	iid *string
}

// ProductDomainServiceMockGetByIIDResults contains results of the ProductDomainService.GetByIID
type ProductDomainServiceMockGetByIIDResults struct {
	p1  productentity.Product
	err error
}

// ProductDomainServiceMockGetByIIDOrigins contains origins of expectations of the ProductDomainService.GetByIID
type ProductDomainServiceMockGetByIIDExpectationOrigins struct {
	origin    string
	originIid string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByIID *mProductDomainServiceMockGetByIID) Optional() *mProductDomainServiceMockGetByIID {
	mmGetByIID.optional = true
	return mmGetByIID
}

// Expect sets up expected params for ProductDomainService.GetByIID
func (mmGetByIID *mProductDomainServiceMockGetByIID) Expect(iid string) *mProductDomainServiceMockGetByIID {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductDomainServiceMock.GetByIID mock is already set by Set")
	}

	if mmGetByIID.defaultExpectation == nil {
		mmGetByIID.defaultExpectation = &ProductDomainServiceMockGetByIIDExpectation{}
	}

	if mmGetByIID.defaultExpectation.paramPtrs != nil {
		mmGetByIID.mock.t.Fatalf("ProductDomainServiceMock.GetByIID mock is already set by ExpectParams functions")
	}

	mmGetByIID.defaultExpectation.params = &ProductDomainServiceMockGetByIIDParams{iid}
	mmGetByIID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByIID.expectations {
		if minimock.Equal(e.params, mmGetByIID.defaultExpectation.params) {
			mmGetByIID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByIID.defaultExpectation.params)
		}
	}

	return mmGetByIID
}

// ExpectIidParam1 sets up expected param iid for ProductDomainService.GetByIID
func (mmGetByIID *mProductDomainServiceMockGetByIID) ExpectIidParam1(iid string) *mProductDomainServiceMockGetByIID {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductDomainServiceMock.GetByIID mock is already set by Set")
	}

	if mmGetByIID.defaultExpectation == nil {
		mmGetByIID.defaultExpectation = &ProductDomainServiceMockGetByIIDExpectation{}
	}

	if mmGetByIID.defaultExpectation.params != nil {
		mmGetByIID.mock.t.Fatalf("ProductDomainServiceMock.GetByIID mock is already set by Expect")
	}

	if mmGetByIID.defaultExpectation.paramPtrs == nil {
		mmGetByIID.defaultExpectation.paramPtrs = &ProductDomainServiceMockGetByIIDParamPtrs{}
	}
	mmGetByIID.defaultExpectation.paramPtrs.iid = &iid
	mmGetByIID.defaultExpectation.expectationOrigins.originIid = minimock.CallerInfo(1)

	return mmGetByIID
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetByIID
func (mmGetByIID *mProductDomainServiceMockGetByIID) Inspect(f func(iid string)) *mProductDomainServiceMockGetByIID {
	if mmGetByIID.mock.inspectFuncGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetByIID")
	}

	mmGetByIID.mock.inspectFuncGetByIID = f

	return mmGetByIID
}

// Return sets up results that will be returned by ProductDomainService.GetByIID
func (mmGetByIID *mProductDomainServiceMockGetByIID) Return(p1 productentity.Product, err error) *ProductDomainServiceMock {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductDomainServiceMock.GetByIID mock is already set by Set")
	}

	if mmGetByIID.defaultExpectation == nil {
		mmGetByIID.defaultExpectation = &ProductDomainServiceMockGetByIIDExpectation{mock: mmGetByIID.mock}
	}
	mmGetByIID.defaultExpectation.results = &ProductDomainServiceMockGetByIIDResults{p1, err}
	mmGetByIID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByIID.mock
}

// Set uses given function f to mock the ProductDomainService.GetByIID method
func (mmGetByIID *mProductDomainServiceMockGetByIID) Set(f func(iid string) (p1 productentity.Product, err error)) *ProductDomainServiceMock {
	if mmGetByIID.defaultExpectation != nil {
		mmGetByIID.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetByIID method")
	}

	if len(mmGetByIID.expectations) > 0 {
		mmGetByIID.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetByIID method")
	}

	mmGetByIID.mock.funcGetByIID = f
	mmGetByIID.mock.funcGetByIIDOrigin = minimock.CallerInfo(1)
	return mmGetByIID.mock
}

// When sets expectation for the ProductDomainService.GetByIID which will trigger the result defined by the following
// Then helper
func (mmGetByIID *mProductDomainServiceMockGetByIID) When(iid string) *ProductDomainServiceMockGetByIIDExpectation {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductDomainServiceMock.GetByIID mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockGetByIIDExpectation{
		mock:               mmGetByIID.mock,
		params:             &ProductDomainServiceMockGetByIIDParams{iid},
		expectationOrigins: ProductDomainServiceMockGetByIIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByIID.expectations = append(mmGetByIID.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.GetByIID return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockGetByIIDExpectation) Then(p1 productentity.Product, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockGetByIIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.GetByIID should be invoked
func (mmGetByIID *mProductDomainServiceMockGetByIID) Times(n uint64) *mProductDomainServiceMockGetByIID {
	if n == 0 {
		mmGetByIID.mock.t.Fatalf("Times of ProductDomainServiceMock.GetByIID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByIID.expectedInvocations, n)
	mmGetByIID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByIID
}

func (mmGetByIID *mProductDomainServiceMockGetByIID) invocationsDone() bool {
	if len(mmGetByIID.expectations) == 0 && mmGetByIID.defaultExpectation == nil && mmGetByIID.mock.funcGetByIID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByIID.mock.afterGetByIIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByIID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByIID implements mm_service.ProductDomainService
func (mmGetByIID *ProductDomainServiceMock) GetByIID(iid string) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetByIID.beforeGetByIIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByIID.afterGetByIIDCounter, 1)

	mmGetByIID.t.Helper()

	if mmGetByIID.inspectFuncGetByIID != nil {
		mmGetByIID.inspectFuncGetByIID(iid)
	}

	mm_params := ProductDomainServiceMockGetByIIDParams{iid}

	// Record call args
	mmGetByIID.GetByIIDMock.mutex.Lock()
	mmGetByIID.GetByIIDMock.callArgs = append(mmGetByIID.GetByIIDMock.callArgs, &mm_params)
	mmGetByIID.GetByIIDMock.mutex.Unlock()

	for _, e := range mmGetByIID.GetByIIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByIID.GetByIIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByIID.GetByIIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByIID.GetByIIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByIID.GetByIIDMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockGetByIIDParams{iid}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.iid != nil && !minimock.Equal(*mm_want_ptrs.iid, mm_got.iid) {
				mmGetByIID.t.Errorf("ProductDomainServiceMock.GetByIID got unexpected parameter iid, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByIID.GetByIIDMock.defaultExpectation.expectationOrigins.originIid, *mm_want_ptrs.iid, mm_got.iid, minimock.Diff(*mm_want_ptrs.iid, mm_got.iid))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByIID.t.Errorf("ProductDomainServiceMock.GetByIID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByIID.GetByIIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByIID.GetByIIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByIID.t.Fatal("No results are set for the ProductDomainServiceMock.GetByIID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByIID.funcGetByIID != nil {
		return mmGetByIID.funcGetByIID(iid)
	}
	mmGetByIID.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetByIID. %v", iid)
	return
}

// GetByIIDAfterCounter returns a count of finished ProductDomainServiceMock.GetByIID invocations
func (mmGetByIID *ProductDomainServiceMock) GetByIIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByIID.afterGetByIIDCounter)
}

// GetByIIDBeforeCounter returns a count of ProductDomainServiceMock.GetByIID invocations
func (mmGetByIID *ProductDomainServiceMock) GetByIIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByIID.beforeGetByIIDCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.GetByIID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByIID *mProductDomainServiceMockGetByIID) Calls() []*ProductDomainServiceMockGetByIIDParams {
	mmGetByIID.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockGetByIIDParams, len(mmGetByIID.callArgs))
	copy(argCopy, mmGetByIID.callArgs)

	mmGetByIID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIIDDone returns true if the count of the GetByIID invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetByIIDDone() bool {
	if m.GetByIIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIIDMock.invocationsDone()
}

// MinimockGetByIIDInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetByIIDInspect() {
	for _, e := range m.GetByIIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByIID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIIDCounter := mm_atomic.LoadUint64(&m.afterGetByIIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIIDMock.defaultExpectation != nil && afterGetByIIDCounter < 1 {
		if m.GetByIIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByIID at\n%s", m.GetByIIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByIID at\n%s with params: %#v", m.GetByIIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByIID != nil && afterGetByIIDCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetByIID at\n%s", m.funcGetByIIDOrigin)
	}

	if !m.GetByIIDMock.invocationsDone() && afterGetByIIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetByIID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIIDMock.expectedInvocations), m.GetByIIDMock.expectedInvocationsOrigin, afterGetByIIDCounter)
	}
}

type mProductDomainServiceMockGetByUserID struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetByUserIDExpectation
	expectations       []*ProductDomainServiceMockGetByUserIDExpectation

	callArgs []*ProductDomainServiceMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetByUserIDExpectation specifies expectation struct of the ProductDomainService.GetByUserID
type ProductDomainServiceMockGetByUserIDExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockGetByUserIDParams
	paramPtrs          *ProductDomainServiceMockGetByUserIDParamPtrs
	expectationOrigins ProductDomainServiceMockGetByUserIDExpectationOrigins
	results            *ProductDomainServiceMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockGetByUserIDParams contains parameters of the ProductDomainService.GetByUserID
type ProductDomainServiceMockGetByUserIDParams struct {
	userID int64
}

// ProductDomainServiceMockGetByUserIDParamPtrs contains pointers to parameters of the ProductDomainService.GetByUserID
type ProductDomainServiceMockGetByUserIDParamPtrs struct {
	userID *int64
}

// ProductDomainServiceMockGetByUserIDResults contains results of the ProductDomainService.GetByUserID
type ProductDomainServiceMockGetByUserIDResults struct {
	pa1 []productentity.Product
	err error
}

// ProductDomainServiceMockGetByUserIDOrigins contains origins of expectations of the ProductDomainService.GetByUserID
type ProductDomainServiceMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Optional() *mProductDomainServiceMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for ProductDomainService.GetByUserID
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Expect(userID int64) *mProductDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProductDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &ProductDomainServiceMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ProductDomainService.GetByUserID
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) ExpectUserIDParam1(userID int64) *mProductDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProductDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &ProductDomainServiceMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetByUserID
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Inspect(f func(userID int64)) *mProductDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by ProductDomainService.GetByUserID
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Return(pa1 []productentity.Product, err error) *ProductDomainServiceMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProductDomainServiceMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &ProductDomainServiceMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the ProductDomainService.GetByUserID method
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Set(f func(userID int64) (pa1 []productentity.Product, err error)) *ProductDomainServiceMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the ProductDomainService.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) When(userID int64) *ProductDomainServiceMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductDomainServiceMock.GetByUserID mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &ProductDomainServiceMockGetByUserIDParams{userID},
		expectationOrigins: ProductDomainServiceMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.GetByUserID return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockGetByUserIDExpectation) Then(pa1 []productentity.Product, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.GetByUserID should be invoked
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Times(n uint64) *mProductDomainServiceMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of ProductDomainServiceMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mProductDomainServiceMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_service.ProductDomainService
func (mmGetByUserID *ProductDomainServiceMock) GetByUserID(userID int64) (pa1 []productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := ProductDomainServiceMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("ProductDomainServiceMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("ProductDomainServiceMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the ProductDomainServiceMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished ProductDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *ProductDomainServiceMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of ProductDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *ProductDomainServiceMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mProductDomainServiceMockGetByUserID) Calls() []*ProductDomainServiceMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mProductDomainServiceMockGetOwnersByProductIDs struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockGetOwnersByProductIDsExpectation
	expectations       []*ProductDomainServiceMockGetOwnersByProductIDsExpectation

	callArgs []*ProductDomainServiceMockGetOwnersByProductIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockGetOwnersByProductIDsExpectation specifies expectation struct of the ProductDomainService.GetOwnersByProductIDs
type ProductDomainServiceMockGetOwnersByProductIDsExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockGetOwnersByProductIDsParams
	paramPtrs          *ProductDomainServiceMockGetOwnersByProductIDsParamPtrs
	expectationOrigins ProductDomainServiceMockGetOwnersByProductIDsExpectationOrigins
	results            *ProductDomainServiceMockGetOwnersByProductIDsResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockGetOwnersByProductIDsParams contains parameters of the ProductDomainService.GetOwnersByProductIDs
type ProductDomainServiceMockGetOwnersByProductIDsParams struct {
	productIDs []int64
}

// ProductDomainServiceMockGetOwnersByProductIDsParamPtrs contains pointers to parameters of the ProductDomainService.GetOwnersByProductIDs
type ProductDomainServiceMockGetOwnersByProductIDsParamPtrs struct {
	productIDs *[]int64
}

// ProductDomainServiceMockGetOwnersByProductIDsResults contains results of the ProductDomainService.GetOwnersByProductIDs
type ProductDomainServiceMockGetOwnersByProductIDsResults struct {
	m1  map[int64][]productentity.Owner
	err error
}

// ProductDomainServiceMockGetOwnersByProductIDsOrigins contains origins of expectations of the ProductDomainService.GetOwnersByProductIDs
type ProductDomainServiceMockGetOwnersByProductIDsExpectationOrigins struct {
	origin           string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Optional() *mProductDomainServiceMockGetOwnersByProductIDs {
	mmGetOwnersByProductIDs.optional = true
	return mmGetOwnersByProductIDs
}

// Expect sets up expected params for ProductDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Expect(productIDs []int64) *mProductDomainServiceMockGetOwnersByProductIDs {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ProductDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	if mmGetOwnersByProductIDs.defaultExpectation == nil {
		mmGetOwnersByProductIDs.defaultExpectation = &ProductDomainServiceMockGetOwnersByProductIDsExpectation{}
	}

	if mmGetOwnersByProductIDs.defaultExpectation.paramPtrs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ProductDomainServiceMock.GetOwnersByProductIDs mock is already set by ExpectParams functions")
	}

	mmGetOwnersByProductIDs.defaultExpectation.params = &ProductDomainServiceMockGetOwnersByProductIDsParams{productIDs}
	mmGetOwnersByProductIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetOwnersByProductIDs.expectations {
		if minimock.Equal(e.params, mmGetOwnersByProductIDs.defaultExpectation.params) {
			mmGetOwnersByProductIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetOwnersByProductIDs.defaultExpectation.params)
		}
	}

	return mmGetOwnersByProductIDs
}

// ExpectProductIDsParam1 sets up expected param productIDs for ProductDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) ExpectProductIDsParam1(productIDs []int64) *mProductDomainServiceMockGetOwnersByProductIDs {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ProductDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	if mmGetOwnersByProductIDs.defaultExpectation == nil {
		mmGetOwnersByProductIDs.defaultExpectation = &ProductDomainServiceMockGetOwnersByProductIDsExpectation{}
	}

	if mmGetOwnersByProductIDs.defaultExpectation.params != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ProductDomainServiceMock.GetOwnersByProductIDs mock is already set by Expect")
	}

	if mmGetOwnersByProductIDs.defaultExpectation.paramPtrs == nil {
		mmGetOwnersByProductIDs.defaultExpectation.paramPtrs = &ProductDomainServiceMockGetOwnersByProductIDsParamPtrs{}
	}
	mmGetOwnersByProductIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmGetOwnersByProductIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmGetOwnersByProductIDs
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Inspect(f func(productIDs []int64)) *mProductDomainServiceMockGetOwnersByProductIDs {
	if mmGetOwnersByProductIDs.mock.inspectFuncGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.GetOwnersByProductIDs")
	}

	mmGetOwnersByProductIDs.mock.inspectFuncGetOwnersByProductIDs = f

	return mmGetOwnersByProductIDs
}

// Return sets up results that will be returned by ProductDomainService.GetOwnersByProductIDs
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Return(m1 map[int64][]productentity.Owner, err error) *ProductDomainServiceMock {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ProductDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	if mmGetOwnersByProductIDs.defaultExpectation == nil {
		mmGetOwnersByProductIDs.defaultExpectation = &ProductDomainServiceMockGetOwnersByProductIDsExpectation{mock: mmGetOwnersByProductIDs.mock}
	}
	mmGetOwnersByProductIDs.defaultExpectation.results = &ProductDomainServiceMockGetOwnersByProductIDsResults{m1, err}
	mmGetOwnersByProductIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductIDs.mock
}

// Set uses given function f to mock the ProductDomainService.GetOwnersByProductIDs method
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Set(f func(productIDs []int64) (m1 map[int64][]productentity.Owner, err error)) *ProductDomainServiceMock {
	if mmGetOwnersByProductIDs.defaultExpectation != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.GetOwnersByProductIDs method")
	}

	if len(mmGetOwnersByProductIDs.expectations) > 0 {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.GetOwnersByProductIDs method")
	}

	mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs = f
	mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDsOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductIDs.mock
}

// When sets expectation for the ProductDomainService.GetOwnersByProductIDs which will trigger the result defined by the following
// Then helper
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) When(productIDs []int64) *ProductDomainServiceMockGetOwnersByProductIDsExpectation {
	if mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.mock.t.Fatalf("ProductDomainServiceMock.GetOwnersByProductIDs mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockGetOwnersByProductIDsExpectation{
		mock:               mmGetOwnersByProductIDs.mock,
		params:             &ProductDomainServiceMockGetOwnersByProductIDsParams{productIDs},
		expectationOrigins: ProductDomainServiceMockGetOwnersByProductIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetOwnersByProductIDs.expectations = append(mmGetOwnersByProductIDs.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.GetOwnersByProductIDs return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockGetOwnersByProductIDsExpectation) Then(m1 map[int64][]productentity.Owner, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockGetOwnersByProductIDsResults{m1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.GetOwnersByProductIDs should be invoked
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Times(n uint64) *mProductDomainServiceMockGetOwnersByProductIDs {
	if n == 0 {
		mmGetOwnersByProductIDs.mock.t.Fatalf("Times of ProductDomainServiceMock.GetOwnersByProductIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetOwnersByProductIDs.expectedInvocations, n)
	mmGetOwnersByProductIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetOwnersByProductIDs
}

func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) invocationsDone() bool {
	if len(mmGetOwnersByProductIDs.expectations) == 0 && mmGetOwnersByProductIDs.defaultExpectation == nil && mmGetOwnersByProductIDs.mock.funcGetOwnersByProductIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.mock.afterGetOwnersByProductIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetOwnersByProductIDs implements mm_service.ProductDomainService
func (mmGetOwnersByProductIDs *ProductDomainServiceMock) GetOwnersByProductIDs(productIDs []int64) (m1 map[int64][]productentity.Owner, err error) {
	mm_atomic.AddUint64(&mmGetOwnersByProductIDs.beforeGetOwnersByProductIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetOwnersByProductIDs.afterGetOwnersByProductIDsCounter, 1)

	mmGetOwnersByProductIDs.t.Helper()

	if mmGetOwnersByProductIDs.inspectFuncGetOwnersByProductIDs != nil {
		mmGetOwnersByProductIDs.inspectFuncGetOwnersByProductIDs(productIDs)
	}

	mm_params := ProductDomainServiceMockGetOwnersByProductIDsParams{productIDs}

	// Record call args
	mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.mutex.Lock()
	mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.callArgs = append(mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.callArgs, &mm_params)
	mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.mutex.Unlock()

	for _, e := range mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.m1, e.results.err
		}
	}

	if mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockGetOwnersByProductIDsParams{productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmGetOwnersByProductIDs.t.Errorf("ProductDomainServiceMock.GetOwnersByProductIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetOwnersByProductIDs.t.Errorf("ProductDomainServiceMock.GetOwnersByProductIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetOwnersByProductIDs.GetOwnersByProductIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetOwnersByProductIDs.t.Fatal("No results are set for the ProductDomainServiceMock.GetOwnersByProductIDs")
		}
		return (*mm_results).m1, (*mm_results).err
	}
	if mmGetOwnersByProductIDs.funcGetOwnersByProductIDs != nil {
		return mmGetOwnersByProductIDs.funcGetOwnersByProductIDs(productIDs)
	}
	mmGetOwnersByProductIDs.t.Fatalf("Unexpected call to ProductDomainServiceMock.GetOwnersByProductIDs. %v", productIDs)
	return
}

// GetOwnersByProductIDsAfterCounter returns a count of finished ProductDomainServiceMock.GetOwnersByProductIDs invocations
func (mmGetOwnersByProductIDs *ProductDomainServiceMock) GetOwnersByProductIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.afterGetOwnersByProductIDsCounter)
}

// GetOwnersByProductIDsBeforeCounter returns a count of ProductDomainServiceMock.GetOwnersByProductIDs invocations
func (mmGetOwnersByProductIDs *ProductDomainServiceMock) GetOwnersByProductIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnersByProductIDs.beforeGetOwnersByProductIDsCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.GetOwnersByProductIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetOwnersByProductIDs *mProductDomainServiceMockGetOwnersByProductIDs) Calls() []*ProductDomainServiceMockGetOwnersByProductIDsParams {
	mmGetOwnersByProductIDs.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockGetOwnersByProductIDsParams, len(mmGetOwnersByProductIDs.callArgs))
	copy(argCopy, mmGetOwnersByProductIDs.callArgs)

	mmGetOwnersByProductIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetOwnersByProductIDsDone returns true if the count of the GetOwnersByProductIDs invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockGetOwnersByProductIDsDone() bool {
	if m.GetOwnersByProductIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetOwnersByProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetOwnersByProductIDsMock.invocationsDone()
}

// MinimockGetOwnersByProductIDsInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockGetOwnersByProductIDsInspect() {
	for _, e := range m.GetOwnersByProductIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetOwnersByProductIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetOwnersByProductIDsCounter := mm_atomic.LoadUint64(&m.afterGetOwnersByProductIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetOwnersByProductIDsMock.defaultExpectation != nil && afterGetOwnersByProductIDsCounter < 1 {
		if m.GetOwnersByProductIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetOwnersByProductIDs at\n%s", m.GetOwnersByProductIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.GetOwnersByProductIDs at\n%s with params: %#v", m.GetOwnersByProductIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetOwnersByProductIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetOwnersByProductIDs != nil && afterGetOwnersByProductIDsCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.GetOwnersByProductIDs at\n%s", m.funcGetOwnersByProductIDsOrigin)
	}

	if !m.GetOwnersByProductIDsMock.invocationsDone() && afterGetOwnersByProductIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.GetOwnersByProductIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetOwnersByProductIDsMock.expectedInvocations), m.GetOwnersByProductIDsMock.expectedInvocationsOrigin, afterGetOwnersByProductIDsCounter)
	}
}

type mProductDomainServiceMockRemoveLinksWithGroups struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockRemoveLinksWithGroupsExpectation
	expectations       []*ProductDomainServiceMockRemoveLinksWithGroupsExpectation

	callArgs []*ProductDomainServiceMockRemoveLinksWithGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockRemoveLinksWithGroupsExpectation specifies expectation struct of the ProductDomainService.RemoveLinksWithGroups
type ProductDomainServiceMockRemoveLinksWithGroupsExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockRemoveLinksWithGroupsParams
	paramPtrs          *ProductDomainServiceMockRemoveLinksWithGroupsParamPtrs
	expectationOrigins ProductDomainServiceMockRemoveLinksWithGroupsExpectationOrigins
	results            *ProductDomainServiceMockRemoveLinksWithGroupsResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockRemoveLinksWithGroupsParams contains parameters of the ProductDomainService.RemoveLinksWithGroups
type ProductDomainServiceMockRemoveLinksWithGroupsParams struct {
	ctx       context.Context
	productID int64
	groupIDs  []int64
}

// ProductDomainServiceMockRemoveLinksWithGroupsParamPtrs contains pointers to parameters of the ProductDomainService.RemoveLinksWithGroups
type ProductDomainServiceMockRemoveLinksWithGroupsParamPtrs struct {
	ctx       *context.Context
	productID *int64
	groupIDs  *[]int64
}

// ProductDomainServiceMockRemoveLinksWithGroupsResults contains results of the ProductDomainService.RemoveLinksWithGroups
type ProductDomainServiceMockRemoveLinksWithGroupsResults struct {
	err error
}

// ProductDomainServiceMockRemoveLinksWithGroupsOrigins contains origins of expectations of the ProductDomainService.RemoveLinksWithGroups
type ProductDomainServiceMockRemoveLinksWithGroupsExpectationOrigins struct {
	origin          string
	originCtx       string
	originProductID string
	originGroupIDs  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Optional() *mProductDomainServiceMockRemoveLinksWithGroups {
	mmRemoveLinksWithGroups.optional = true
	return mmRemoveLinksWithGroups
}

// Expect sets up expected params for ProductDomainService.RemoveLinksWithGroups
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Expect(ctx context.Context, productID int64, groupIDs []int64) *mProductDomainServiceMockRemoveLinksWithGroups {
	if mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Set")
	}

	if mmRemoveLinksWithGroups.defaultExpectation == nil {
		mmRemoveLinksWithGroups.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithGroupsExpectation{}
	}

	if mmRemoveLinksWithGroups.defaultExpectation.paramPtrs != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by ExpectParams functions")
	}

	mmRemoveLinksWithGroups.defaultExpectation.params = &ProductDomainServiceMockRemoveLinksWithGroupsParams{ctx, productID, groupIDs}
	mmRemoveLinksWithGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmRemoveLinksWithGroups.expectations {
		if minimock.Equal(e.params, mmRemoveLinksWithGroups.defaultExpectation.params) {
			mmRemoveLinksWithGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmRemoveLinksWithGroups.defaultExpectation.params)
		}
	}

	return mmRemoveLinksWithGroups
}

// ExpectCtxParam1 sets up expected param ctx for ProductDomainService.RemoveLinksWithGroups
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) ExpectCtxParam1(ctx context.Context) *mProductDomainServiceMockRemoveLinksWithGroups {
	if mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Set")
	}

	if mmRemoveLinksWithGroups.defaultExpectation == nil {
		mmRemoveLinksWithGroups.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithGroupsExpectation{}
	}

	if mmRemoveLinksWithGroups.defaultExpectation.params != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Expect")
	}

	if mmRemoveLinksWithGroups.defaultExpectation.paramPtrs == nil {
		mmRemoveLinksWithGroups.defaultExpectation.paramPtrs = &ProductDomainServiceMockRemoveLinksWithGroupsParamPtrs{}
	}
	mmRemoveLinksWithGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmRemoveLinksWithGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmRemoveLinksWithGroups
}

// ExpectProductIDParam2 sets up expected param productID for ProductDomainService.RemoveLinksWithGroups
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) ExpectProductIDParam2(productID int64) *mProductDomainServiceMockRemoveLinksWithGroups {
	if mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Set")
	}

	if mmRemoveLinksWithGroups.defaultExpectation == nil {
		mmRemoveLinksWithGroups.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithGroupsExpectation{}
	}

	if mmRemoveLinksWithGroups.defaultExpectation.params != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Expect")
	}

	if mmRemoveLinksWithGroups.defaultExpectation.paramPtrs == nil {
		mmRemoveLinksWithGroups.defaultExpectation.paramPtrs = &ProductDomainServiceMockRemoveLinksWithGroupsParamPtrs{}
	}
	mmRemoveLinksWithGroups.defaultExpectation.paramPtrs.productID = &productID
	mmRemoveLinksWithGroups.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmRemoveLinksWithGroups
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for ProductDomainService.RemoveLinksWithGroups
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) ExpectGroupIDsParam3(groupIDs []int64) *mProductDomainServiceMockRemoveLinksWithGroups {
	if mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Set")
	}

	if mmRemoveLinksWithGroups.defaultExpectation == nil {
		mmRemoveLinksWithGroups.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithGroupsExpectation{}
	}

	if mmRemoveLinksWithGroups.defaultExpectation.params != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Expect")
	}

	if mmRemoveLinksWithGroups.defaultExpectation.paramPtrs == nil {
		mmRemoveLinksWithGroups.defaultExpectation.paramPtrs = &ProductDomainServiceMockRemoveLinksWithGroupsParamPtrs{}
	}
	mmRemoveLinksWithGroups.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmRemoveLinksWithGroups.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmRemoveLinksWithGroups
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.RemoveLinksWithGroups
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Inspect(f func(ctx context.Context, productID int64, groupIDs []int64)) *mProductDomainServiceMockRemoveLinksWithGroups {
	if mmRemoveLinksWithGroups.mock.inspectFuncRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.RemoveLinksWithGroups")
	}

	mmRemoveLinksWithGroups.mock.inspectFuncRemoveLinksWithGroups = f

	return mmRemoveLinksWithGroups
}

// Return sets up results that will be returned by ProductDomainService.RemoveLinksWithGroups
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Return(err error) *ProductDomainServiceMock {
	if mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Set")
	}

	if mmRemoveLinksWithGroups.defaultExpectation == nil {
		mmRemoveLinksWithGroups.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithGroupsExpectation{mock: mmRemoveLinksWithGroups.mock}
	}
	mmRemoveLinksWithGroups.defaultExpectation.results = &ProductDomainServiceMockRemoveLinksWithGroupsResults{err}
	mmRemoveLinksWithGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmRemoveLinksWithGroups.mock
}

// Set uses given function f to mock the ProductDomainService.RemoveLinksWithGroups method
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Set(f func(ctx context.Context, productID int64, groupIDs []int64) (err error)) *ProductDomainServiceMock {
	if mmRemoveLinksWithGroups.defaultExpectation != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.RemoveLinksWithGroups method")
	}

	if len(mmRemoveLinksWithGroups.expectations) > 0 {
		mmRemoveLinksWithGroups.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.RemoveLinksWithGroups method")
	}

	mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups = f
	mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroupsOrigin = minimock.CallerInfo(1)
	return mmRemoveLinksWithGroups.mock
}

// When sets expectation for the ProductDomainService.RemoveLinksWithGroups which will trigger the result defined by the following
// Then helper
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) When(ctx context.Context, productID int64, groupIDs []int64) *ProductDomainServiceMockRemoveLinksWithGroupsExpectation {
	if mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithGroups mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockRemoveLinksWithGroupsExpectation{
		mock:               mmRemoveLinksWithGroups.mock,
		params:             &ProductDomainServiceMockRemoveLinksWithGroupsParams{ctx, productID, groupIDs},
		expectationOrigins: ProductDomainServiceMockRemoveLinksWithGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmRemoveLinksWithGroups.expectations = append(mmRemoveLinksWithGroups.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.RemoveLinksWithGroups return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockRemoveLinksWithGroupsExpectation) Then(err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockRemoveLinksWithGroupsResults{err}
	return e.mock
}

// Times sets number of times ProductDomainService.RemoveLinksWithGroups should be invoked
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Times(n uint64) *mProductDomainServiceMockRemoveLinksWithGroups {
	if n == 0 {
		mmRemoveLinksWithGroups.mock.t.Fatalf("Times of ProductDomainServiceMock.RemoveLinksWithGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmRemoveLinksWithGroups.expectedInvocations, n)
	mmRemoveLinksWithGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmRemoveLinksWithGroups
}

func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) invocationsDone() bool {
	if len(mmRemoveLinksWithGroups.expectations) == 0 && mmRemoveLinksWithGroups.defaultExpectation == nil && mmRemoveLinksWithGroups.mock.funcRemoveLinksWithGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmRemoveLinksWithGroups.mock.afterRemoveLinksWithGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmRemoveLinksWithGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// RemoveLinksWithGroups implements mm_service.ProductDomainService
func (mmRemoveLinksWithGroups *ProductDomainServiceMock) RemoveLinksWithGroups(ctx context.Context, productID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmRemoveLinksWithGroups.beforeRemoveLinksWithGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmRemoveLinksWithGroups.afterRemoveLinksWithGroupsCounter, 1)

	mmRemoveLinksWithGroups.t.Helper()

	if mmRemoveLinksWithGroups.inspectFuncRemoveLinksWithGroups != nil {
		mmRemoveLinksWithGroups.inspectFuncRemoveLinksWithGroups(ctx, productID, groupIDs)
	}

	mm_params := ProductDomainServiceMockRemoveLinksWithGroupsParams{ctx, productID, groupIDs}

	// Record call args
	mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.mutex.Lock()
	mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.callArgs = append(mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.callArgs, &mm_params)
	mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.mutex.Unlock()

	for _, e := range mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockRemoveLinksWithGroupsParams{ctx, productID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmRemoveLinksWithGroups.t.Errorf("ProductDomainServiceMock.RemoveLinksWithGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmRemoveLinksWithGroups.t.Errorf("ProductDomainServiceMock.RemoveLinksWithGroups got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmRemoveLinksWithGroups.t.Errorf("ProductDomainServiceMock.RemoveLinksWithGroups got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmRemoveLinksWithGroups.t.Errorf("ProductDomainServiceMock.RemoveLinksWithGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmRemoveLinksWithGroups.RemoveLinksWithGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmRemoveLinksWithGroups.t.Fatal("No results are set for the ProductDomainServiceMock.RemoveLinksWithGroups")
		}
		return (*mm_results).err
	}
	if mmRemoveLinksWithGroups.funcRemoveLinksWithGroups != nil {
		return mmRemoveLinksWithGroups.funcRemoveLinksWithGroups(ctx, productID, groupIDs)
	}
	mmRemoveLinksWithGroups.t.Fatalf("Unexpected call to ProductDomainServiceMock.RemoveLinksWithGroups. %v %v %v", ctx, productID, groupIDs)
	return
}

// RemoveLinksWithGroupsAfterCounter returns a count of finished ProductDomainServiceMock.RemoveLinksWithGroups invocations
func (mmRemoveLinksWithGroups *ProductDomainServiceMock) RemoveLinksWithGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveLinksWithGroups.afterRemoveLinksWithGroupsCounter)
}

// RemoveLinksWithGroupsBeforeCounter returns a count of ProductDomainServiceMock.RemoveLinksWithGroups invocations
func (mmRemoveLinksWithGroups *ProductDomainServiceMock) RemoveLinksWithGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveLinksWithGroups.beforeRemoveLinksWithGroupsCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.RemoveLinksWithGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmRemoveLinksWithGroups *mProductDomainServiceMockRemoveLinksWithGroups) Calls() []*ProductDomainServiceMockRemoveLinksWithGroupsParams {
	mmRemoveLinksWithGroups.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockRemoveLinksWithGroupsParams, len(mmRemoveLinksWithGroups.callArgs))
	copy(argCopy, mmRemoveLinksWithGroups.callArgs)

	mmRemoveLinksWithGroups.mutex.RUnlock()

	return argCopy
}

// MinimockRemoveLinksWithGroupsDone returns true if the count of the RemoveLinksWithGroups invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockRemoveLinksWithGroupsDone() bool {
	if m.RemoveLinksWithGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.RemoveLinksWithGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.RemoveLinksWithGroupsMock.invocationsDone()
}

// MinimockRemoveLinksWithGroupsInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockRemoveLinksWithGroupsInspect() {
	for _, e := range m.RemoveLinksWithGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterRemoveLinksWithGroupsCounter := mm_atomic.LoadUint64(&m.afterRemoveLinksWithGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.RemoveLinksWithGroupsMock.defaultExpectation != nil && afterRemoveLinksWithGroupsCounter < 1 {
		if m.RemoveLinksWithGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithGroups at\n%s", m.RemoveLinksWithGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithGroups at\n%s with params: %#v", m.RemoveLinksWithGroupsMock.defaultExpectation.expectationOrigins.origin, *m.RemoveLinksWithGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcRemoveLinksWithGroups != nil && afterRemoveLinksWithGroupsCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithGroups at\n%s", m.funcRemoveLinksWithGroupsOrigin)
	}

	if !m.RemoveLinksWithGroupsMock.invocationsDone() && afterRemoveLinksWithGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.RemoveLinksWithGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.RemoveLinksWithGroupsMock.expectedInvocations), m.RemoveLinksWithGroupsMock.expectedInvocationsOrigin, afterRemoveLinksWithGroupsCounter)
	}
}

type mProductDomainServiceMockRemoveLinksWithRoles struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockRemoveLinksWithRolesExpectation
	expectations       []*ProductDomainServiceMockRemoveLinksWithRolesExpectation

	callArgs []*ProductDomainServiceMockRemoveLinksWithRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockRemoveLinksWithRolesExpectation specifies expectation struct of the ProductDomainService.RemoveLinksWithRoles
type ProductDomainServiceMockRemoveLinksWithRolesExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockRemoveLinksWithRolesParams
	paramPtrs          *ProductDomainServiceMockRemoveLinksWithRolesParamPtrs
	expectationOrigins ProductDomainServiceMockRemoveLinksWithRolesExpectationOrigins
	results            *ProductDomainServiceMockRemoveLinksWithRolesResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockRemoveLinksWithRolesParams contains parameters of the ProductDomainService.RemoveLinksWithRoles
type ProductDomainServiceMockRemoveLinksWithRolesParams struct {
	ctx       context.Context
	productID int64
	roleIDs   []int64
}

// ProductDomainServiceMockRemoveLinksWithRolesParamPtrs contains pointers to parameters of the ProductDomainService.RemoveLinksWithRoles
type ProductDomainServiceMockRemoveLinksWithRolesParamPtrs struct {
	ctx       *context.Context
	productID *int64
	roleIDs   *[]int64
}

// ProductDomainServiceMockRemoveLinksWithRolesResults contains results of the ProductDomainService.RemoveLinksWithRoles
type ProductDomainServiceMockRemoveLinksWithRolesResults struct {
	err error
}

// ProductDomainServiceMockRemoveLinksWithRolesOrigins contains origins of expectations of the ProductDomainService.RemoveLinksWithRoles
type ProductDomainServiceMockRemoveLinksWithRolesExpectationOrigins struct {
	origin          string
	originCtx       string
	originProductID string
	originRoleIDs   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Optional() *mProductDomainServiceMockRemoveLinksWithRoles {
	mmRemoveLinksWithRoles.optional = true
	return mmRemoveLinksWithRoles
}

// Expect sets up expected params for ProductDomainService.RemoveLinksWithRoles
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Expect(ctx context.Context, productID int64, roleIDs []int64) *mProductDomainServiceMockRemoveLinksWithRoles {
	if mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Set")
	}

	if mmRemoveLinksWithRoles.defaultExpectation == nil {
		mmRemoveLinksWithRoles.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithRolesExpectation{}
	}

	if mmRemoveLinksWithRoles.defaultExpectation.paramPtrs != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by ExpectParams functions")
	}

	mmRemoveLinksWithRoles.defaultExpectation.params = &ProductDomainServiceMockRemoveLinksWithRolesParams{ctx, productID, roleIDs}
	mmRemoveLinksWithRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmRemoveLinksWithRoles.expectations {
		if minimock.Equal(e.params, mmRemoveLinksWithRoles.defaultExpectation.params) {
			mmRemoveLinksWithRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmRemoveLinksWithRoles.defaultExpectation.params)
		}
	}

	return mmRemoveLinksWithRoles
}

// ExpectCtxParam1 sets up expected param ctx for ProductDomainService.RemoveLinksWithRoles
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) ExpectCtxParam1(ctx context.Context) *mProductDomainServiceMockRemoveLinksWithRoles {
	if mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Set")
	}

	if mmRemoveLinksWithRoles.defaultExpectation == nil {
		mmRemoveLinksWithRoles.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithRolesExpectation{}
	}

	if mmRemoveLinksWithRoles.defaultExpectation.params != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Expect")
	}

	if mmRemoveLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmRemoveLinksWithRoles.defaultExpectation.paramPtrs = &ProductDomainServiceMockRemoveLinksWithRolesParamPtrs{}
	}
	mmRemoveLinksWithRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmRemoveLinksWithRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmRemoveLinksWithRoles
}

// ExpectProductIDParam2 sets up expected param productID for ProductDomainService.RemoveLinksWithRoles
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) ExpectProductIDParam2(productID int64) *mProductDomainServiceMockRemoveLinksWithRoles {
	if mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Set")
	}

	if mmRemoveLinksWithRoles.defaultExpectation == nil {
		mmRemoveLinksWithRoles.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithRolesExpectation{}
	}

	if mmRemoveLinksWithRoles.defaultExpectation.params != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Expect")
	}

	if mmRemoveLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmRemoveLinksWithRoles.defaultExpectation.paramPtrs = &ProductDomainServiceMockRemoveLinksWithRolesParamPtrs{}
	}
	mmRemoveLinksWithRoles.defaultExpectation.paramPtrs.productID = &productID
	mmRemoveLinksWithRoles.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmRemoveLinksWithRoles
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for ProductDomainService.RemoveLinksWithRoles
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) ExpectRoleIDsParam3(roleIDs []int64) *mProductDomainServiceMockRemoveLinksWithRoles {
	if mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Set")
	}

	if mmRemoveLinksWithRoles.defaultExpectation == nil {
		mmRemoveLinksWithRoles.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithRolesExpectation{}
	}

	if mmRemoveLinksWithRoles.defaultExpectation.params != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Expect")
	}

	if mmRemoveLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmRemoveLinksWithRoles.defaultExpectation.paramPtrs = &ProductDomainServiceMockRemoveLinksWithRolesParamPtrs{}
	}
	mmRemoveLinksWithRoles.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmRemoveLinksWithRoles.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmRemoveLinksWithRoles
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.RemoveLinksWithRoles
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Inspect(f func(ctx context.Context, productID int64, roleIDs []int64)) *mProductDomainServiceMockRemoveLinksWithRoles {
	if mmRemoveLinksWithRoles.mock.inspectFuncRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.RemoveLinksWithRoles")
	}

	mmRemoveLinksWithRoles.mock.inspectFuncRemoveLinksWithRoles = f

	return mmRemoveLinksWithRoles
}

// Return sets up results that will be returned by ProductDomainService.RemoveLinksWithRoles
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Return(err error) *ProductDomainServiceMock {
	if mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Set")
	}

	if mmRemoveLinksWithRoles.defaultExpectation == nil {
		mmRemoveLinksWithRoles.defaultExpectation = &ProductDomainServiceMockRemoveLinksWithRolesExpectation{mock: mmRemoveLinksWithRoles.mock}
	}
	mmRemoveLinksWithRoles.defaultExpectation.results = &ProductDomainServiceMockRemoveLinksWithRolesResults{err}
	mmRemoveLinksWithRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmRemoveLinksWithRoles.mock
}

// Set uses given function f to mock the ProductDomainService.RemoveLinksWithRoles method
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Set(f func(ctx context.Context, productID int64, roleIDs []int64) (err error)) *ProductDomainServiceMock {
	if mmRemoveLinksWithRoles.defaultExpectation != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.RemoveLinksWithRoles method")
	}

	if len(mmRemoveLinksWithRoles.expectations) > 0 {
		mmRemoveLinksWithRoles.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.RemoveLinksWithRoles method")
	}

	mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles = f
	mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRolesOrigin = minimock.CallerInfo(1)
	return mmRemoveLinksWithRoles.mock
}

// When sets expectation for the ProductDomainService.RemoveLinksWithRoles which will trigger the result defined by the following
// Then helper
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) When(ctx context.Context, productID int64, roleIDs []int64) *ProductDomainServiceMockRemoveLinksWithRolesExpectation {
	if mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.mock.t.Fatalf("ProductDomainServiceMock.RemoveLinksWithRoles mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockRemoveLinksWithRolesExpectation{
		mock:               mmRemoveLinksWithRoles.mock,
		params:             &ProductDomainServiceMockRemoveLinksWithRolesParams{ctx, productID, roleIDs},
		expectationOrigins: ProductDomainServiceMockRemoveLinksWithRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmRemoveLinksWithRoles.expectations = append(mmRemoveLinksWithRoles.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.RemoveLinksWithRoles return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockRemoveLinksWithRolesExpectation) Then(err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockRemoveLinksWithRolesResults{err}
	return e.mock
}

// Times sets number of times ProductDomainService.RemoveLinksWithRoles should be invoked
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Times(n uint64) *mProductDomainServiceMockRemoveLinksWithRoles {
	if n == 0 {
		mmRemoveLinksWithRoles.mock.t.Fatalf("Times of ProductDomainServiceMock.RemoveLinksWithRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmRemoveLinksWithRoles.expectedInvocations, n)
	mmRemoveLinksWithRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmRemoveLinksWithRoles
}

func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) invocationsDone() bool {
	if len(mmRemoveLinksWithRoles.expectations) == 0 && mmRemoveLinksWithRoles.defaultExpectation == nil && mmRemoveLinksWithRoles.mock.funcRemoveLinksWithRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmRemoveLinksWithRoles.mock.afterRemoveLinksWithRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmRemoveLinksWithRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// RemoveLinksWithRoles implements mm_service.ProductDomainService
func (mmRemoveLinksWithRoles *ProductDomainServiceMock) RemoveLinksWithRoles(ctx context.Context, productID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmRemoveLinksWithRoles.beforeRemoveLinksWithRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmRemoveLinksWithRoles.afterRemoveLinksWithRolesCounter, 1)

	mmRemoveLinksWithRoles.t.Helper()

	if mmRemoveLinksWithRoles.inspectFuncRemoveLinksWithRoles != nil {
		mmRemoveLinksWithRoles.inspectFuncRemoveLinksWithRoles(ctx, productID, roleIDs)
	}

	mm_params := ProductDomainServiceMockRemoveLinksWithRolesParams{ctx, productID, roleIDs}

	// Record call args
	mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.mutex.Lock()
	mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.callArgs = append(mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.callArgs, &mm_params)
	mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.mutex.Unlock()

	for _, e := range mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.params
		mm_want_ptrs := mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockRemoveLinksWithRolesParams{ctx, productID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmRemoveLinksWithRoles.t.Errorf("ProductDomainServiceMock.RemoveLinksWithRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmRemoveLinksWithRoles.t.Errorf("ProductDomainServiceMock.RemoveLinksWithRoles got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmRemoveLinksWithRoles.t.Errorf("ProductDomainServiceMock.RemoveLinksWithRoles got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmRemoveLinksWithRoles.t.Errorf("ProductDomainServiceMock.RemoveLinksWithRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmRemoveLinksWithRoles.RemoveLinksWithRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmRemoveLinksWithRoles.t.Fatal("No results are set for the ProductDomainServiceMock.RemoveLinksWithRoles")
		}
		return (*mm_results).err
	}
	if mmRemoveLinksWithRoles.funcRemoveLinksWithRoles != nil {
		return mmRemoveLinksWithRoles.funcRemoveLinksWithRoles(ctx, productID, roleIDs)
	}
	mmRemoveLinksWithRoles.t.Fatalf("Unexpected call to ProductDomainServiceMock.RemoveLinksWithRoles. %v %v %v", ctx, productID, roleIDs)
	return
}

// RemoveLinksWithRolesAfterCounter returns a count of finished ProductDomainServiceMock.RemoveLinksWithRoles invocations
func (mmRemoveLinksWithRoles *ProductDomainServiceMock) RemoveLinksWithRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveLinksWithRoles.afterRemoveLinksWithRolesCounter)
}

// RemoveLinksWithRolesBeforeCounter returns a count of ProductDomainServiceMock.RemoveLinksWithRoles invocations
func (mmRemoveLinksWithRoles *ProductDomainServiceMock) RemoveLinksWithRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveLinksWithRoles.beforeRemoveLinksWithRolesCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.RemoveLinksWithRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmRemoveLinksWithRoles *mProductDomainServiceMockRemoveLinksWithRoles) Calls() []*ProductDomainServiceMockRemoveLinksWithRolesParams {
	mmRemoveLinksWithRoles.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockRemoveLinksWithRolesParams, len(mmRemoveLinksWithRoles.callArgs))
	copy(argCopy, mmRemoveLinksWithRoles.callArgs)

	mmRemoveLinksWithRoles.mutex.RUnlock()

	return argCopy
}

// MinimockRemoveLinksWithRolesDone returns true if the count of the RemoveLinksWithRoles invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockRemoveLinksWithRolesDone() bool {
	if m.RemoveLinksWithRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.RemoveLinksWithRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.RemoveLinksWithRolesMock.invocationsDone()
}

// MinimockRemoveLinksWithRolesInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockRemoveLinksWithRolesInspect() {
	for _, e := range m.RemoveLinksWithRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterRemoveLinksWithRolesCounter := mm_atomic.LoadUint64(&m.afterRemoveLinksWithRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.RemoveLinksWithRolesMock.defaultExpectation != nil && afterRemoveLinksWithRolesCounter < 1 {
		if m.RemoveLinksWithRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithRoles at\n%s", m.RemoveLinksWithRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithRoles at\n%s with params: %#v", m.RemoveLinksWithRolesMock.defaultExpectation.expectationOrigins.origin, *m.RemoveLinksWithRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcRemoveLinksWithRoles != nil && afterRemoveLinksWithRolesCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.RemoveLinksWithRoles at\n%s", m.funcRemoveLinksWithRolesOrigin)
	}

	if !m.RemoveLinksWithRolesMock.invocationsDone() && afterRemoveLinksWithRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.RemoveLinksWithRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.RemoveLinksWithRolesMock.expectedInvocations), m.RemoveLinksWithRolesMock.expectedInvocationsOrigin, afterRemoveLinksWithRolesCounter)
	}
}

type mProductDomainServiceMockUpdate struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockUpdateExpectation
	expectations       []*ProductDomainServiceMockUpdateExpectation

	callArgs []*ProductDomainServiceMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockUpdateExpectation specifies expectation struct of the ProductDomainService.Update
type ProductDomainServiceMockUpdateExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockUpdateParams
	paramPtrs          *ProductDomainServiceMockUpdateParamPtrs
	expectationOrigins ProductDomainServiceMockUpdateExpectationOrigins
	results            *ProductDomainServiceMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockUpdateParams contains parameters of the ProductDomainService.Update
type ProductDomainServiceMockUpdateParams struct {
	data productentity.ProductUpdateData
}

// ProductDomainServiceMockUpdateParamPtrs contains pointers to parameters of the ProductDomainService.Update
type ProductDomainServiceMockUpdateParamPtrs struct {
	data *productentity.ProductUpdateData
}

// ProductDomainServiceMockUpdateResults contains results of the ProductDomainService.Update
type ProductDomainServiceMockUpdateResults struct {
	p1  productentity.Product
	err error
}

// ProductDomainServiceMockUpdateOrigins contains origins of expectations of the ProductDomainService.Update
type ProductDomainServiceMockUpdateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mProductDomainServiceMockUpdate) Optional() *mProductDomainServiceMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for ProductDomainService.Update
func (mmUpdate *mProductDomainServiceMockUpdate) Expect(data productentity.ProductUpdateData) *mProductDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProductDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("ProductDomainServiceMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &ProductDomainServiceMockUpdateParams{data}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectDataParam1 sets up expected param data for ProductDomainService.Update
func (mmUpdate *mProductDomainServiceMockUpdate) ExpectDataParam1(data productentity.ProductUpdateData) *mProductDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProductDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ProductDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ProductDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.data = &data
	mmUpdate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.Update
func (mmUpdate *mProductDomainServiceMockUpdate) Inspect(f func(data productentity.ProductUpdateData)) *mProductDomainServiceMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by ProductDomainService.Update
func (mmUpdate *mProductDomainServiceMockUpdate) Return(p1 productentity.Product, err error) *ProductDomainServiceMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProductDomainServiceMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &ProductDomainServiceMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the ProductDomainService.Update method
func (mmUpdate *mProductDomainServiceMockUpdate) Set(f func(data productentity.ProductUpdateData) (p1 productentity.Product, err error)) *ProductDomainServiceMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the ProductDomainService.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mProductDomainServiceMockUpdate) When(data productentity.ProductUpdateData) *ProductDomainServiceMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductDomainServiceMock.Update mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &ProductDomainServiceMockUpdateParams{data},
		expectationOrigins: ProductDomainServiceMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.Update return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockUpdateExpectation) Then(p1 productentity.Product, err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times ProductDomainService.Update should be invoked
func (mmUpdate *mProductDomainServiceMockUpdate) Times(n uint64) *mProductDomainServiceMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of ProductDomainServiceMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mProductDomainServiceMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_service.ProductDomainService
func (mmUpdate *ProductDomainServiceMock) Update(data productentity.ProductUpdateData) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(data)
	}

	mm_params := ProductDomainServiceMockUpdateParams{data}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockUpdateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdate.t.Errorf("ProductDomainServiceMock.Update got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("ProductDomainServiceMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the ProductDomainServiceMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(data)
	}
	mmUpdate.t.Fatalf("Unexpected call to ProductDomainServiceMock.Update. %v", data)
	return
}

// UpdateAfterCounter returns a count of finished ProductDomainServiceMock.Update invocations
func (mmUpdate *ProductDomainServiceMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of ProductDomainServiceMock.Update invocations
func (mmUpdate *ProductDomainServiceMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mProductDomainServiceMockUpdate) Calls() []*ProductDomainServiceMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

type mProductDomainServiceMockUpdateLinksWithParticipants struct {
	optional           bool
	mock               *ProductDomainServiceMock
	defaultExpectation *ProductDomainServiceMockUpdateLinksWithParticipantsExpectation
	expectations       []*ProductDomainServiceMockUpdateLinksWithParticipantsExpectation

	callArgs []*ProductDomainServiceMockUpdateLinksWithParticipantsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductDomainServiceMockUpdateLinksWithParticipantsExpectation specifies expectation struct of the ProductDomainService.UpdateLinksWithParticipants
type ProductDomainServiceMockUpdateLinksWithParticipantsExpectation struct {
	mock               *ProductDomainServiceMock
	params             *ProductDomainServiceMockUpdateLinksWithParticipantsParams
	paramPtrs          *ProductDomainServiceMockUpdateLinksWithParticipantsParamPtrs
	expectationOrigins ProductDomainServiceMockUpdateLinksWithParticipantsExpectationOrigins
	results            *ProductDomainServiceMockUpdateLinksWithParticipantsResults
	returnOrigin       string
	Counter            uint64
}

// ProductDomainServiceMockUpdateLinksWithParticipantsParams contains parameters of the ProductDomainService.UpdateLinksWithParticipants
type ProductDomainServiceMockUpdateLinksWithParticipantsParams struct {
	ctx            context.Context
	productID      int64
	participantIDs []string
}

// ProductDomainServiceMockUpdateLinksWithParticipantsParamPtrs contains pointers to parameters of the ProductDomainService.UpdateLinksWithParticipants
type ProductDomainServiceMockUpdateLinksWithParticipantsParamPtrs struct {
	ctx            *context.Context
	productID      *int64
	participantIDs *[]string
}

// ProductDomainServiceMockUpdateLinksWithParticipantsResults contains results of the ProductDomainService.UpdateLinksWithParticipants
type ProductDomainServiceMockUpdateLinksWithParticipantsResults struct {
	err error
}

// ProductDomainServiceMockUpdateLinksWithParticipantsOrigins contains origins of expectations of the ProductDomainService.UpdateLinksWithParticipants
type ProductDomainServiceMockUpdateLinksWithParticipantsExpectationOrigins struct {
	origin               string
	originCtx            string
	originProductID      string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Optional() *mProductDomainServiceMockUpdateLinksWithParticipants {
	mmUpdateLinksWithParticipants.optional = true
	return mmUpdateLinksWithParticipants
}

// Expect sets up expected params for ProductDomainService.UpdateLinksWithParticipants
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Expect(ctx context.Context, productID int64, participantIDs []string) *mProductDomainServiceMockUpdateLinksWithParticipants {
	if mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Set")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation == nil {
		mmUpdateLinksWithParticipants.defaultExpectation = &ProductDomainServiceMockUpdateLinksWithParticipantsExpectation{}
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by ExpectParams functions")
	}

	mmUpdateLinksWithParticipants.defaultExpectation.params = &ProductDomainServiceMockUpdateLinksWithParticipantsParams{ctx, productID, participantIDs}
	mmUpdateLinksWithParticipants.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLinksWithParticipants.expectations {
		if minimock.Equal(e.params, mmUpdateLinksWithParticipants.defaultExpectation.params) {
			mmUpdateLinksWithParticipants.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLinksWithParticipants.defaultExpectation.params)
		}
	}

	return mmUpdateLinksWithParticipants
}

// ExpectCtxParam1 sets up expected param ctx for ProductDomainService.UpdateLinksWithParticipants
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) ExpectCtxParam1(ctx context.Context) *mProductDomainServiceMockUpdateLinksWithParticipants {
	if mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Set")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation == nil {
		mmUpdateLinksWithParticipants.defaultExpectation = &ProductDomainServiceMockUpdateLinksWithParticipantsExpectation{}
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.params != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Expect")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs = &ProductDomainServiceMockUpdateLinksWithParticipantsParamPtrs{}
	}
	mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLinksWithParticipants.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLinksWithParticipants
}

// ExpectProductIDParam2 sets up expected param productID for ProductDomainService.UpdateLinksWithParticipants
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) ExpectProductIDParam2(productID int64) *mProductDomainServiceMockUpdateLinksWithParticipants {
	if mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Set")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation == nil {
		mmUpdateLinksWithParticipants.defaultExpectation = &ProductDomainServiceMockUpdateLinksWithParticipantsExpectation{}
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.params != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Expect")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs = &ProductDomainServiceMockUpdateLinksWithParticipantsParamPtrs{}
	}
	mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs.productID = &productID
	mmUpdateLinksWithParticipants.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmUpdateLinksWithParticipants
}

// ExpectParticipantIDsParam3 sets up expected param participantIDs for ProductDomainService.UpdateLinksWithParticipants
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) ExpectParticipantIDsParam3(participantIDs []string) *mProductDomainServiceMockUpdateLinksWithParticipants {
	if mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Set")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation == nil {
		mmUpdateLinksWithParticipants.defaultExpectation = &ProductDomainServiceMockUpdateLinksWithParticipantsExpectation{}
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.params != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Expect")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs = &ProductDomainServiceMockUpdateLinksWithParticipantsParamPtrs{}
	}
	mmUpdateLinksWithParticipants.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmUpdateLinksWithParticipants.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmUpdateLinksWithParticipants
}

// Inspect accepts an inspector function that has same arguments as the ProductDomainService.UpdateLinksWithParticipants
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Inspect(f func(ctx context.Context, productID int64, participantIDs []string)) *mProductDomainServiceMockUpdateLinksWithParticipants {
	if mmUpdateLinksWithParticipants.mock.inspectFuncUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("Inspect function is already set for ProductDomainServiceMock.UpdateLinksWithParticipants")
	}

	mmUpdateLinksWithParticipants.mock.inspectFuncUpdateLinksWithParticipants = f

	return mmUpdateLinksWithParticipants
}

// Return sets up results that will be returned by ProductDomainService.UpdateLinksWithParticipants
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Return(err error) *ProductDomainServiceMock {
	if mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Set")
	}

	if mmUpdateLinksWithParticipants.defaultExpectation == nil {
		mmUpdateLinksWithParticipants.defaultExpectation = &ProductDomainServiceMockUpdateLinksWithParticipantsExpectation{mock: mmUpdateLinksWithParticipants.mock}
	}
	mmUpdateLinksWithParticipants.defaultExpectation.results = &ProductDomainServiceMockUpdateLinksWithParticipantsResults{err}
	mmUpdateLinksWithParticipants.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithParticipants.mock
}

// Set uses given function f to mock the ProductDomainService.UpdateLinksWithParticipants method
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Set(f func(ctx context.Context, productID int64, participantIDs []string) (err error)) *ProductDomainServiceMock {
	if mmUpdateLinksWithParticipants.defaultExpectation != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("Default expectation is already set for the ProductDomainService.UpdateLinksWithParticipants method")
	}

	if len(mmUpdateLinksWithParticipants.expectations) > 0 {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("Some expectations are already set for the ProductDomainService.UpdateLinksWithParticipants method")
	}

	mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants = f
	mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipantsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithParticipants.mock
}

// When sets expectation for the ProductDomainService.UpdateLinksWithParticipants which will trigger the result defined by the following
// Then helper
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) When(ctx context.Context, productID int64, participantIDs []string) *ProductDomainServiceMockUpdateLinksWithParticipantsExpectation {
	if mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("ProductDomainServiceMock.UpdateLinksWithParticipants mock is already set by Set")
	}

	expectation := &ProductDomainServiceMockUpdateLinksWithParticipantsExpectation{
		mock:               mmUpdateLinksWithParticipants.mock,
		params:             &ProductDomainServiceMockUpdateLinksWithParticipantsParams{ctx, productID, participantIDs},
		expectationOrigins: ProductDomainServiceMockUpdateLinksWithParticipantsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLinksWithParticipants.expectations = append(mmUpdateLinksWithParticipants.expectations, expectation)
	return expectation
}

// Then sets up ProductDomainService.UpdateLinksWithParticipants return parameters for the expectation previously defined by the When method
func (e *ProductDomainServiceMockUpdateLinksWithParticipantsExpectation) Then(err error) *ProductDomainServiceMock {
	e.results = &ProductDomainServiceMockUpdateLinksWithParticipantsResults{err}
	return e.mock
}

// Times sets number of times ProductDomainService.UpdateLinksWithParticipants should be invoked
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Times(n uint64) *mProductDomainServiceMockUpdateLinksWithParticipants {
	if n == 0 {
		mmUpdateLinksWithParticipants.mock.t.Fatalf("Times of ProductDomainServiceMock.UpdateLinksWithParticipants mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLinksWithParticipants.expectedInvocations, n)
	mmUpdateLinksWithParticipants.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithParticipants
}

func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) invocationsDone() bool {
	if len(mmUpdateLinksWithParticipants.expectations) == 0 && mmUpdateLinksWithParticipants.defaultExpectation == nil && mmUpdateLinksWithParticipants.mock.funcUpdateLinksWithParticipants == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithParticipants.mock.afterUpdateLinksWithParticipantsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithParticipants.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLinksWithParticipants implements mm_service.ProductDomainService
func (mmUpdateLinksWithParticipants *ProductDomainServiceMock) UpdateLinksWithParticipants(ctx context.Context, productID int64, participantIDs []string) (err error) {
	mm_atomic.AddUint64(&mmUpdateLinksWithParticipants.beforeUpdateLinksWithParticipantsCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLinksWithParticipants.afterUpdateLinksWithParticipantsCounter, 1)

	mmUpdateLinksWithParticipants.t.Helper()

	if mmUpdateLinksWithParticipants.inspectFuncUpdateLinksWithParticipants != nil {
		mmUpdateLinksWithParticipants.inspectFuncUpdateLinksWithParticipants(ctx, productID, participantIDs)
	}

	mm_params := ProductDomainServiceMockUpdateLinksWithParticipantsParams{ctx, productID, participantIDs}

	// Record call args
	mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.mutex.Lock()
	mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.callArgs = append(mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.callArgs, &mm_params)
	mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.mutex.Unlock()

	for _, e := range mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.paramPtrs

		mm_got := ProductDomainServiceMockUpdateLinksWithParticipantsParams{ctx, productID, participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLinksWithParticipants.t.Errorf("ProductDomainServiceMock.UpdateLinksWithParticipants got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmUpdateLinksWithParticipants.t.Errorf("ProductDomainServiceMock.UpdateLinksWithParticipants got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmUpdateLinksWithParticipants.t.Errorf("ProductDomainServiceMock.UpdateLinksWithParticipants got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLinksWithParticipants.t.Errorf("ProductDomainServiceMock.UpdateLinksWithParticipants got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLinksWithParticipants.UpdateLinksWithParticipantsMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLinksWithParticipants.t.Fatal("No results are set for the ProductDomainServiceMock.UpdateLinksWithParticipants")
		}
		return (*mm_results).err
	}
	if mmUpdateLinksWithParticipants.funcUpdateLinksWithParticipants != nil {
		return mmUpdateLinksWithParticipants.funcUpdateLinksWithParticipants(ctx, productID, participantIDs)
	}
	mmUpdateLinksWithParticipants.t.Fatalf("Unexpected call to ProductDomainServiceMock.UpdateLinksWithParticipants. %v %v %v", ctx, productID, participantIDs)
	return
}

// UpdateLinksWithParticipantsAfterCounter returns a count of finished ProductDomainServiceMock.UpdateLinksWithParticipants invocations
func (mmUpdateLinksWithParticipants *ProductDomainServiceMock) UpdateLinksWithParticipantsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithParticipants.afterUpdateLinksWithParticipantsCounter)
}

// UpdateLinksWithParticipantsBeforeCounter returns a count of ProductDomainServiceMock.UpdateLinksWithParticipants invocations
func (mmUpdateLinksWithParticipants *ProductDomainServiceMock) UpdateLinksWithParticipantsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithParticipants.beforeUpdateLinksWithParticipantsCounter)
}

// Calls returns a list of arguments used in each call to ProductDomainServiceMock.UpdateLinksWithParticipants.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLinksWithParticipants *mProductDomainServiceMockUpdateLinksWithParticipants) Calls() []*ProductDomainServiceMockUpdateLinksWithParticipantsParams {
	mmUpdateLinksWithParticipants.mutex.RLock()

	argCopy := make([]*ProductDomainServiceMockUpdateLinksWithParticipantsParams, len(mmUpdateLinksWithParticipants.callArgs))
	copy(argCopy, mmUpdateLinksWithParticipants.callArgs)

	mmUpdateLinksWithParticipants.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLinksWithParticipantsDone returns true if the count of the UpdateLinksWithParticipants invocations corresponds
// the number of defined expectations
func (m *ProductDomainServiceMock) MinimockUpdateLinksWithParticipantsDone() bool {
	if m.UpdateLinksWithParticipantsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLinksWithParticipantsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLinksWithParticipantsMock.invocationsDone()
}

// MinimockUpdateLinksWithParticipantsInspect logs each unmet expectation
func (m *ProductDomainServiceMock) MinimockUpdateLinksWithParticipantsInspect() {
	for _, e := range m.UpdateLinksWithParticipantsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductDomainServiceMock.UpdateLinksWithParticipants at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLinksWithParticipantsCounter := mm_atomic.LoadUint64(&m.afterUpdateLinksWithParticipantsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLinksWithParticipantsMock.defaultExpectation != nil && afterUpdateLinksWithParticipantsCounter < 1 {
		if m.UpdateLinksWithParticipantsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductDomainServiceMock.UpdateLinksWithParticipants at\n%s", m.UpdateLinksWithParticipantsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductDomainServiceMock.UpdateLinksWithParticipants at\n%s with params: %#v", m.UpdateLinksWithParticipantsMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLinksWithParticipantsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLinksWithParticipants != nil && afterUpdateLinksWithParticipantsCounter < 1 {
		m.t.Errorf("Expected call to ProductDomainServiceMock.UpdateLinksWithParticipants at\n%s", m.funcUpdateLinksWithParticipantsOrigin)
	}

	if !m.UpdateLinksWithParticipantsMock.invocationsDone() && afterUpdateLinksWithParticipantsCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductDomainServiceMock.UpdateLinksWithParticipants at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLinksWithParticipantsMock.expectedInvocations), m.UpdateLinksWithParticipantsMock.expectedInvocationsOrigin, afterUpdateLinksWithParticipantsCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProductDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllAsAdminInspect()

			m.MinimockGetBasicByUserIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByIIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetOwnersByProductIDsInspect()

			m.MinimockRemoveLinksWithGroupsInspect()

			m.MinimockRemoveLinksWithRolesInspect()

			m.MinimockUpdateInspect()

			m.MinimockUpdateLinksWithParticipantsInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProductDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProductDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllAsAdminDone() &&
		m.MinimockGetBasicByUserIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByIIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetOwnersByProductIDsDone() &&
		m.MinimockRemoveLinksWithGroupsDone() &&
		m.MinimockRemoveLinksWithRolesDone() &&
		m.MinimockUpdateDone() &&
		m.MinimockUpdateLinksWithParticipantsDone()
}
