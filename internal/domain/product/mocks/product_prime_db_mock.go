// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository.ProductPrimeDB -o product_prime_db_mock.go -n ProductPrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	"github.com/gojuno/minimock/v3"
)

// ProductPrimeDBMock implements mm_repository.ProductPrimeDB
type ProductPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(product productentity.ProductCreateData, ownerIDs []int64) (p1 productentity.Product, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(product productentity.ProductCreateData, ownerIDs []int64)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mProductPrimeDBMockCreate

	funcGetAll          func() (pa1 []productentity.Product, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mProductPrimeDBMockGetAll

	funcGetByID          func(id int64) (p1 productentity.Product, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mProductPrimeDBMockGetByID

	funcGetByIID          func(iid string) (p1 productentity.Product, err error)
	funcGetByIIDOrigin    string
	inspectFuncGetByIID   func(iid string)
	afterGetByIIDCounter  uint64
	beforeGetByIIDCounter uint64
	GetByIIDMock          mProductPrimeDBMockGetByIID

	funcGetByUserID          func(userID int64) (pa1 []productentity.Product, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mProductPrimeDBMockGetByUserID

	funcUpdate          func(data productentity.ProductUpdateData) (p1 productentity.Product, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(data productentity.ProductUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mProductPrimeDBMockUpdate
}

// NewProductPrimeDBMock returns a mock for mm_repository.ProductPrimeDB
func NewProductPrimeDBMock(t minimock.Tester) *ProductPrimeDBMock {
	m := &ProductPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mProductPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*ProductPrimeDBMockCreateParams{}

	m.GetAllMock = mProductPrimeDBMockGetAll{mock: m}

	m.GetByIDMock = mProductPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ProductPrimeDBMockGetByIDParams{}

	m.GetByIIDMock = mProductPrimeDBMockGetByIID{mock: m}
	m.GetByIIDMock.callArgs = []*ProductPrimeDBMockGetByIIDParams{}

	m.GetByUserIDMock = mProductPrimeDBMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*ProductPrimeDBMockGetByUserIDParams{}

	m.UpdateMock = mProductPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*ProductPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProductPrimeDBMockCreate struct {
	optional           bool
	mock               *ProductPrimeDBMock
	defaultExpectation *ProductPrimeDBMockCreateExpectation
	expectations       []*ProductPrimeDBMockCreateExpectation

	callArgs []*ProductPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductPrimeDBMockCreateExpectation specifies expectation struct of the ProductPrimeDB.Create
type ProductPrimeDBMockCreateExpectation struct {
	mock               *ProductPrimeDBMock
	params             *ProductPrimeDBMockCreateParams
	paramPtrs          *ProductPrimeDBMockCreateParamPtrs
	expectationOrigins ProductPrimeDBMockCreateExpectationOrigins
	results            *ProductPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ProductPrimeDBMockCreateParams contains parameters of the ProductPrimeDB.Create
type ProductPrimeDBMockCreateParams struct {
	product  productentity.ProductCreateData
	ownerIDs []int64
}

// ProductPrimeDBMockCreateParamPtrs contains pointers to parameters of the ProductPrimeDB.Create
type ProductPrimeDBMockCreateParamPtrs struct {
	product  *productentity.ProductCreateData
	ownerIDs *[]int64
}

// ProductPrimeDBMockCreateResults contains results of the ProductPrimeDB.Create
type ProductPrimeDBMockCreateResults struct {
	p1  productentity.Product
	err error
}

// ProductPrimeDBMockCreateOrigins contains origins of expectations of the ProductPrimeDB.Create
type ProductPrimeDBMockCreateExpectationOrigins struct {
	origin         string
	originProduct  string
	originOwnerIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mProductPrimeDBMockCreate) Optional() *mProductPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ProductPrimeDB.Create
func (mmCreate *mProductPrimeDBMockCreate) Expect(product productentity.ProductCreateData, ownerIDs []int64) *mProductPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ProductPrimeDBMockCreateParams{product, ownerIDs}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectProductParam1 sets up expected param product for ProductPrimeDB.Create
func (mmCreate *mProductPrimeDBMockCreate) ExpectProductParam1(product productentity.ProductCreateData) *mProductPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProductPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.product = &product
	mmCreate.defaultExpectation.expectationOrigins.originProduct = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectOwnerIDsParam2 sets up expected param ownerIDs for ProductPrimeDB.Create
func (mmCreate *mProductPrimeDBMockCreate) ExpectOwnerIDsParam2(ownerIDs []int64) *mProductPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProductPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ownerIDs = &ownerIDs
	mmCreate.defaultExpectation.expectationOrigins.originOwnerIDs = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ProductPrimeDB.Create
func (mmCreate *mProductPrimeDBMockCreate) Inspect(f func(product productentity.ProductCreateData, ownerIDs []int64)) *mProductPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ProductPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ProductPrimeDB.Create
func (mmCreate *mProductPrimeDBMockCreate) Return(p1 productentity.Product, err error) *ProductPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProductPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ProductPrimeDBMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ProductPrimeDB.Create method
func (mmCreate *mProductPrimeDBMockCreate) Set(f func(product productentity.ProductCreateData, ownerIDs []int64) (p1 productentity.Product, err error)) *ProductPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ProductPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ProductPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ProductPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mProductPrimeDBMockCreate) When(product productentity.ProductCreateData, ownerIDs []int64) *ProductPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProductPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &ProductPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ProductPrimeDBMockCreateParams{product, ownerIDs},
		expectationOrigins: ProductPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ProductPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *ProductPrimeDBMockCreateExpectation) Then(p1 productentity.Product, err error) *ProductPrimeDBMock {
	e.results = &ProductPrimeDBMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ProductPrimeDB.Create should be invoked
func (mmCreate *mProductPrimeDBMockCreate) Times(n uint64) *mProductPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ProductPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mProductPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.ProductPrimeDB
func (mmCreate *ProductPrimeDBMock) Create(product productentity.ProductCreateData, ownerIDs []int64) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(product, ownerIDs)
	}

	mm_params := ProductPrimeDBMockCreateParams{product, ownerIDs}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ProductPrimeDBMockCreateParams{product, ownerIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.product != nil && !minimock.Equal(*mm_want_ptrs.product, mm_got.product) {
				mmCreate.t.Errorf("ProductPrimeDBMock.Create got unexpected parameter product, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originProduct, *mm_want_ptrs.product, mm_got.product, minimock.Diff(*mm_want_ptrs.product, mm_got.product))
			}

			if mm_want_ptrs.ownerIDs != nil && !minimock.Equal(*mm_want_ptrs.ownerIDs, mm_got.ownerIDs) {
				mmCreate.t.Errorf("ProductPrimeDBMock.Create got unexpected parameter ownerIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originOwnerIDs, *mm_want_ptrs.ownerIDs, mm_got.ownerIDs, minimock.Diff(*mm_want_ptrs.ownerIDs, mm_got.ownerIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ProductPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ProductPrimeDBMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(product, ownerIDs)
	}
	mmCreate.t.Fatalf("Unexpected call to ProductPrimeDBMock.Create. %v %v", product, ownerIDs)
	return
}

// CreateAfterCounter returns a count of finished ProductPrimeDBMock.Create invocations
func (mmCreate *ProductPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ProductPrimeDBMock.Create invocations
func (mmCreate *ProductPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ProductPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mProductPrimeDBMockCreate) Calls() []*ProductPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ProductPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ProductPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ProductPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mProductPrimeDBMockGetAll struct {
	optional           bool
	mock               *ProductPrimeDBMock
	defaultExpectation *ProductPrimeDBMockGetAllExpectation
	expectations       []*ProductPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductPrimeDBMockGetAllExpectation specifies expectation struct of the ProductPrimeDB.GetAll
type ProductPrimeDBMockGetAllExpectation struct {
	mock *ProductPrimeDBMock

	results      *ProductPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// ProductPrimeDBMockGetAllResults contains results of the ProductPrimeDB.GetAll
type ProductPrimeDBMockGetAllResults struct {
	pa1 []productentity.Product
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mProductPrimeDBMockGetAll) Optional() *mProductPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for ProductPrimeDB.GetAll
func (mmGetAll *mProductPrimeDBMockGetAll) Expect() *mProductPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ProductPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ProductPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the ProductPrimeDB.GetAll
func (mmGetAll *mProductPrimeDBMockGetAll) Inspect(f func()) *mProductPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for ProductPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by ProductPrimeDB.GetAll
func (mmGetAll *mProductPrimeDBMockGetAll) Return(pa1 []productentity.Product, err error) *ProductPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ProductPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ProductPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &ProductPrimeDBMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the ProductPrimeDB.GetAll method
func (mmGetAll *mProductPrimeDBMockGetAll) Set(f func() (pa1 []productentity.Product, err error)) *ProductPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the ProductPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the ProductPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times ProductPrimeDB.GetAll should be invoked
func (mmGetAll *mProductPrimeDBMockGetAll) Times(n uint64) *mProductPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of ProductPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mProductPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.ProductPrimeDB
func (mmGetAll *ProductPrimeDBMock) GetAll() (pa1 []productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the ProductPrimeDBMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to ProductPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished ProductPrimeDBMock.GetAll invocations
func (mmGetAll *ProductPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of ProductPrimeDBMock.GetAll invocations
func (mmGetAll *ProductPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *ProductPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *ProductPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ProductPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mProductPrimeDBMockGetByID struct {
	optional           bool
	mock               *ProductPrimeDBMock
	defaultExpectation *ProductPrimeDBMockGetByIDExpectation
	expectations       []*ProductPrimeDBMockGetByIDExpectation

	callArgs []*ProductPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductPrimeDBMockGetByIDExpectation specifies expectation struct of the ProductPrimeDB.GetByID
type ProductPrimeDBMockGetByIDExpectation struct {
	mock               *ProductPrimeDBMock
	params             *ProductPrimeDBMockGetByIDParams
	paramPtrs          *ProductPrimeDBMockGetByIDParamPtrs
	expectationOrigins ProductPrimeDBMockGetByIDExpectationOrigins
	results            *ProductPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductPrimeDBMockGetByIDParams contains parameters of the ProductPrimeDB.GetByID
type ProductPrimeDBMockGetByIDParams struct {
	id int64
}

// ProductPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the ProductPrimeDB.GetByID
type ProductPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// ProductPrimeDBMockGetByIDResults contains results of the ProductPrimeDB.GetByID
type ProductPrimeDBMockGetByIDResults struct {
	p1  productentity.Product
	err error
}

// ProductPrimeDBMockGetByIDOrigins contains origins of expectations of the ProductPrimeDB.GetByID
type ProductPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mProductPrimeDBMockGetByID) Optional() *mProductPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ProductPrimeDB.GetByID
func (mmGetByID *mProductPrimeDBMockGetByID) Expect(id int64) *mProductPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProductPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ProductPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ProductPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ProductPrimeDB.GetByID
func (mmGetByID *mProductPrimeDBMockGetByID) ExpectIdParam1(id int64) *mProductPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProductPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ProductPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ProductPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ProductPrimeDB.GetByID
func (mmGetByID *mProductPrimeDBMockGetByID) Inspect(f func(id int64)) *mProductPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ProductPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ProductPrimeDB.GetByID
func (mmGetByID *mProductPrimeDBMockGetByID) Return(p1 productentity.Product, err error) *ProductPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProductPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ProductPrimeDBMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ProductPrimeDB.GetByID method
func (mmGetByID *mProductPrimeDBMockGetByID) Set(f func(id int64) (p1 productentity.Product, err error)) *ProductPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ProductPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ProductPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ProductPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mProductPrimeDBMockGetByID) When(id int64) *ProductPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProductPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &ProductPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ProductPrimeDBMockGetByIDParams{id},
		expectationOrigins: ProductPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ProductPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *ProductPrimeDBMockGetByIDExpectation) Then(p1 productentity.Product, err error) *ProductPrimeDBMock {
	e.results = &ProductPrimeDBMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProductPrimeDB.GetByID should be invoked
func (mmGetByID *mProductPrimeDBMockGetByID) Times(n uint64) *mProductPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ProductPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mProductPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.ProductPrimeDB
func (mmGetByID *ProductPrimeDBMock) GetByID(id int64) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ProductPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ProductPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ProductPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ProductPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ProductPrimeDBMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ProductPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ProductPrimeDBMock.GetByID invocations
func (mmGetByID *ProductPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ProductPrimeDBMock.GetByID invocations
func (mmGetByID *ProductPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ProductPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mProductPrimeDBMockGetByID) Calls() []*ProductPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ProductPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ProductPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ProductPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mProductPrimeDBMockGetByIID struct {
	optional           bool
	mock               *ProductPrimeDBMock
	defaultExpectation *ProductPrimeDBMockGetByIIDExpectation
	expectations       []*ProductPrimeDBMockGetByIIDExpectation

	callArgs []*ProductPrimeDBMockGetByIIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductPrimeDBMockGetByIIDExpectation specifies expectation struct of the ProductPrimeDB.GetByIID
type ProductPrimeDBMockGetByIIDExpectation struct {
	mock               *ProductPrimeDBMock
	params             *ProductPrimeDBMockGetByIIDParams
	paramPtrs          *ProductPrimeDBMockGetByIIDParamPtrs
	expectationOrigins ProductPrimeDBMockGetByIIDExpectationOrigins
	results            *ProductPrimeDBMockGetByIIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductPrimeDBMockGetByIIDParams contains parameters of the ProductPrimeDB.GetByIID
type ProductPrimeDBMockGetByIIDParams struct {
	iid string
}

// ProductPrimeDBMockGetByIIDParamPtrs contains pointers to parameters of the ProductPrimeDB.GetByIID
type ProductPrimeDBMockGetByIIDParamPtrs struct {
	iid *string
}

// ProductPrimeDBMockGetByIIDResults contains results of the ProductPrimeDB.GetByIID
type ProductPrimeDBMockGetByIIDResults struct {
	p1  productentity.Product
	err error
}

// ProductPrimeDBMockGetByIIDOrigins contains origins of expectations of the ProductPrimeDB.GetByIID
type ProductPrimeDBMockGetByIIDExpectationOrigins struct {
	origin    string
	originIid string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByIID *mProductPrimeDBMockGetByIID) Optional() *mProductPrimeDBMockGetByIID {
	mmGetByIID.optional = true
	return mmGetByIID
}

// Expect sets up expected params for ProductPrimeDB.GetByIID
func (mmGetByIID *mProductPrimeDBMockGetByIID) Expect(iid string) *mProductPrimeDBMockGetByIID {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductPrimeDBMock.GetByIID mock is already set by Set")
	}

	if mmGetByIID.defaultExpectation == nil {
		mmGetByIID.defaultExpectation = &ProductPrimeDBMockGetByIIDExpectation{}
	}

	if mmGetByIID.defaultExpectation.paramPtrs != nil {
		mmGetByIID.mock.t.Fatalf("ProductPrimeDBMock.GetByIID mock is already set by ExpectParams functions")
	}

	mmGetByIID.defaultExpectation.params = &ProductPrimeDBMockGetByIIDParams{iid}
	mmGetByIID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByIID.expectations {
		if minimock.Equal(e.params, mmGetByIID.defaultExpectation.params) {
			mmGetByIID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByIID.defaultExpectation.params)
		}
	}

	return mmGetByIID
}

// ExpectIidParam1 sets up expected param iid for ProductPrimeDB.GetByIID
func (mmGetByIID *mProductPrimeDBMockGetByIID) ExpectIidParam1(iid string) *mProductPrimeDBMockGetByIID {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductPrimeDBMock.GetByIID mock is already set by Set")
	}

	if mmGetByIID.defaultExpectation == nil {
		mmGetByIID.defaultExpectation = &ProductPrimeDBMockGetByIIDExpectation{}
	}

	if mmGetByIID.defaultExpectation.params != nil {
		mmGetByIID.mock.t.Fatalf("ProductPrimeDBMock.GetByIID mock is already set by Expect")
	}

	if mmGetByIID.defaultExpectation.paramPtrs == nil {
		mmGetByIID.defaultExpectation.paramPtrs = &ProductPrimeDBMockGetByIIDParamPtrs{}
	}
	mmGetByIID.defaultExpectation.paramPtrs.iid = &iid
	mmGetByIID.defaultExpectation.expectationOrigins.originIid = minimock.CallerInfo(1)

	return mmGetByIID
}

// Inspect accepts an inspector function that has same arguments as the ProductPrimeDB.GetByIID
func (mmGetByIID *mProductPrimeDBMockGetByIID) Inspect(f func(iid string)) *mProductPrimeDBMockGetByIID {
	if mmGetByIID.mock.inspectFuncGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("Inspect function is already set for ProductPrimeDBMock.GetByIID")
	}

	mmGetByIID.mock.inspectFuncGetByIID = f

	return mmGetByIID
}

// Return sets up results that will be returned by ProductPrimeDB.GetByIID
func (mmGetByIID *mProductPrimeDBMockGetByIID) Return(p1 productentity.Product, err error) *ProductPrimeDBMock {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductPrimeDBMock.GetByIID mock is already set by Set")
	}

	if mmGetByIID.defaultExpectation == nil {
		mmGetByIID.defaultExpectation = &ProductPrimeDBMockGetByIIDExpectation{mock: mmGetByIID.mock}
	}
	mmGetByIID.defaultExpectation.results = &ProductPrimeDBMockGetByIIDResults{p1, err}
	mmGetByIID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByIID.mock
}

// Set uses given function f to mock the ProductPrimeDB.GetByIID method
func (mmGetByIID *mProductPrimeDBMockGetByIID) Set(f func(iid string) (p1 productentity.Product, err error)) *ProductPrimeDBMock {
	if mmGetByIID.defaultExpectation != nil {
		mmGetByIID.mock.t.Fatalf("Default expectation is already set for the ProductPrimeDB.GetByIID method")
	}

	if len(mmGetByIID.expectations) > 0 {
		mmGetByIID.mock.t.Fatalf("Some expectations are already set for the ProductPrimeDB.GetByIID method")
	}

	mmGetByIID.mock.funcGetByIID = f
	mmGetByIID.mock.funcGetByIIDOrigin = minimock.CallerInfo(1)
	return mmGetByIID.mock
}

// When sets expectation for the ProductPrimeDB.GetByIID which will trigger the result defined by the following
// Then helper
func (mmGetByIID *mProductPrimeDBMockGetByIID) When(iid string) *ProductPrimeDBMockGetByIIDExpectation {
	if mmGetByIID.mock.funcGetByIID != nil {
		mmGetByIID.mock.t.Fatalf("ProductPrimeDBMock.GetByIID mock is already set by Set")
	}

	expectation := &ProductPrimeDBMockGetByIIDExpectation{
		mock:               mmGetByIID.mock,
		params:             &ProductPrimeDBMockGetByIIDParams{iid},
		expectationOrigins: ProductPrimeDBMockGetByIIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByIID.expectations = append(mmGetByIID.expectations, expectation)
	return expectation
}

// Then sets up ProductPrimeDB.GetByIID return parameters for the expectation previously defined by the When method
func (e *ProductPrimeDBMockGetByIIDExpectation) Then(p1 productentity.Product, err error) *ProductPrimeDBMock {
	e.results = &ProductPrimeDBMockGetByIIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProductPrimeDB.GetByIID should be invoked
func (mmGetByIID *mProductPrimeDBMockGetByIID) Times(n uint64) *mProductPrimeDBMockGetByIID {
	if n == 0 {
		mmGetByIID.mock.t.Fatalf("Times of ProductPrimeDBMock.GetByIID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByIID.expectedInvocations, n)
	mmGetByIID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByIID
}

func (mmGetByIID *mProductPrimeDBMockGetByIID) invocationsDone() bool {
	if len(mmGetByIID.expectations) == 0 && mmGetByIID.defaultExpectation == nil && mmGetByIID.mock.funcGetByIID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByIID.mock.afterGetByIIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByIID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByIID implements mm_repository.ProductPrimeDB
func (mmGetByIID *ProductPrimeDBMock) GetByIID(iid string) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetByIID.beforeGetByIIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByIID.afterGetByIIDCounter, 1)

	mmGetByIID.t.Helper()

	if mmGetByIID.inspectFuncGetByIID != nil {
		mmGetByIID.inspectFuncGetByIID(iid)
	}

	mm_params := ProductPrimeDBMockGetByIIDParams{iid}

	// Record call args
	mmGetByIID.GetByIIDMock.mutex.Lock()
	mmGetByIID.GetByIIDMock.callArgs = append(mmGetByIID.GetByIIDMock.callArgs, &mm_params)
	mmGetByIID.GetByIIDMock.mutex.Unlock()

	for _, e := range mmGetByIID.GetByIIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByIID.GetByIIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByIID.GetByIIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByIID.GetByIIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByIID.GetByIIDMock.defaultExpectation.paramPtrs

		mm_got := ProductPrimeDBMockGetByIIDParams{iid}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.iid != nil && !minimock.Equal(*mm_want_ptrs.iid, mm_got.iid) {
				mmGetByIID.t.Errorf("ProductPrimeDBMock.GetByIID got unexpected parameter iid, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByIID.GetByIIDMock.defaultExpectation.expectationOrigins.originIid, *mm_want_ptrs.iid, mm_got.iid, minimock.Diff(*mm_want_ptrs.iid, mm_got.iid))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByIID.t.Errorf("ProductPrimeDBMock.GetByIID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByIID.GetByIIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByIID.GetByIIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByIID.t.Fatal("No results are set for the ProductPrimeDBMock.GetByIID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByIID.funcGetByIID != nil {
		return mmGetByIID.funcGetByIID(iid)
	}
	mmGetByIID.t.Fatalf("Unexpected call to ProductPrimeDBMock.GetByIID. %v", iid)
	return
}

// GetByIIDAfterCounter returns a count of finished ProductPrimeDBMock.GetByIID invocations
func (mmGetByIID *ProductPrimeDBMock) GetByIIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByIID.afterGetByIIDCounter)
}

// GetByIIDBeforeCounter returns a count of ProductPrimeDBMock.GetByIID invocations
func (mmGetByIID *ProductPrimeDBMock) GetByIIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByIID.beforeGetByIIDCounter)
}

// Calls returns a list of arguments used in each call to ProductPrimeDBMock.GetByIID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByIID *mProductPrimeDBMockGetByIID) Calls() []*ProductPrimeDBMockGetByIIDParams {
	mmGetByIID.mutex.RLock()

	argCopy := make([]*ProductPrimeDBMockGetByIIDParams, len(mmGetByIID.callArgs))
	copy(argCopy, mmGetByIID.callArgs)

	mmGetByIID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIIDDone returns true if the count of the GetByIID invocations corresponds
// the number of defined expectations
func (m *ProductPrimeDBMock) MinimockGetByIIDDone() bool {
	if m.GetByIIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIIDMock.invocationsDone()
}

// MinimockGetByIIDInspect logs each unmet expectation
func (m *ProductPrimeDBMock) MinimockGetByIIDInspect() {
	for _, e := range m.GetByIIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByIID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIIDCounter := mm_atomic.LoadUint64(&m.afterGetByIIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIIDMock.defaultExpectation != nil && afterGetByIIDCounter < 1 {
		if m.GetByIIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByIID at\n%s", m.GetByIIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByIID at\n%s with params: %#v", m.GetByIIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByIID != nil && afterGetByIIDCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.GetByIID at\n%s", m.funcGetByIIDOrigin)
	}

	if !m.GetByIIDMock.invocationsDone() && afterGetByIIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductPrimeDBMock.GetByIID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIIDMock.expectedInvocations), m.GetByIIDMock.expectedInvocationsOrigin, afterGetByIIDCounter)
	}
}

type mProductPrimeDBMockGetByUserID struct {
	optional           bool
	mock               *ProductPrimeDBMock
	defaultExpectation *ProductPrimeDBMockGetByUserIDExpectation
	expectations       []*ProductPrimeDBMockGetByUserIDExpectation

	callArgs []*ProductPrimeDBMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductPrimeDBMockGetByUserIDExpectation specifies expectation struct of the ProductPrimeDB.GetByUserID
type ProductPrimeDBMockGetByUserIDExpectation struct {
	mock               *ProductPrimeDBMock
	params             *ProductPrimeDBMockGetByUserIDParams
	paramPtrs          *ProductPrimeDBMockGetByUserIDParamPtrs
	expectationOrigins ProductPrimeDBMockGetByUserIDExpectationOrigins
	results            *ProductPrimeDBMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ProductPrimeDBMockGetByUserIDParams contains parameters of the ProductPrimeDB.GetByUserID
type ProductPrimeDBMockGetByUserIDParams struct {
	userID int64
}

// ProductPrimeDBMockGetByUserIDParamPtrs contains pointers to parameters of the ProductPrimeDB.GetByUserID
type ProductPrimeDBMockGetByUserIDParamPtrs struct {
	userID *int64
}

// ProductPrimeDBMockGetByUserIDResults contains results of the ProductPrimeDB.GetByUserID
type ProductPrimeDBMockGetByUserIDResults struct {
	pa1 []productentity.Product
	err error
}

// ProductPrimeDBMockGetByUserIDOrigins contains origins of expectations of the ProductPrimeDB.GetByUserID
type ProductPrimeDBMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Optional() *mProductPrimeDBMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for ProductPrimeDB.GetByUserID
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Expect(userID int64) *mProductPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProductPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("ProductPrimeDBMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &ProductPrimeDBMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ProductPrimeDB.GetByUserID
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) ExpectUserIDParam1(userID int64) *mProductPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProductPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("ProductPrimeDBMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &ProductPrimeDBMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the ProductPrimeDB.GetByUserID
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Inspect(f func(userID int64)) *mProductPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for ProductPrimeDBMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by ProductPrimeDB.GetByUserID
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Return(pa1 []productentity.Product, err error) *ProductPrimeDBMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProductPrimeDBMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &ProductPrimeDBMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the ProductPrimeDB.GetByUserID method
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Set(f func(userID int64) (pa1 []productentity.Product, err error)) *ProductPrimeDBMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the ProductPrimeDB.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the ProductPrimeDB.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the ProductPrimeDB.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) When(userID int64) *ProductPrimeDBMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProductPrimeDBMock.GetByUserID mock is already set by Set")
	}

	expectation := &ProductPrimeDBMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &ProductPrimeDBMockGetByUserIDParams{userID},
		expectationOrigins: ProductPrimeDBMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up ProductPrimeDB.GetByUserID return parameters for the expectation previously defined by the When method
func (e *ProductPrimeDBMockGetByUserIDExpectation) Then(pa1 []productentity.Product, err error) *ProductPrimeDBMock {
	e.results = &ProductPrimeDBMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ProductPrimeDB.GetByUserID should be invoked
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Times(n uint64) *mProductPrimeDBMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of ProductPrimeDBMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mProductPrimeDBMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.ProductPrimeDB
func (mmGetByUserID *ProductPrimeDBMock) GetByUserID(userID int64) (pa1 []productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := ProductPrimeDBMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ProductPrimeDBMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("ProductPrimeDBMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("ProductPrimeDBMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the ProductPrimeDBMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to ProductPrimeDBMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished ProductPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *ProductPrimeDBMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of ProductPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *ProductPrimeDBMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ProductPrimeDBMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mProductPrimeDBMockGetByUserID) Calls() []*ProductPrimeDBMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*ProductPrimeDBMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *ProductPrimeDBMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *ProductPrimeDBMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductPrimeDBMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductPrimeDBMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mProductPrimeDBMockUpdate struct {
	optional           bool
	mock               *ProductPrimeDBMock
	defaultExpectation *ProductPrimeDBMockUpdateExpectation
	expectations       []*ProductPrimeDBMockUpdateExpectation

	callArgs []*ProductPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProductPrimeDBMockUpdateExpectation specifies expectation struct of the ProductPrimeDB.Update
type ProductPrimeDBMockUpdateExpectation struct {
	mock               *ProductPrimeDBMock
	params             *ProductPrimeDBMockUpdateParams
	paramPtrs          *ProductPrimeDBMockUpdateParamPtrs
	expectationOrigins ProductPrimeDBMockUpdateExpectationOrigins
	results            *ProductPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// ProductPrimeDBMockUpdateParams contains parameters of the ProductPrimeDB.Update
type ProductPrimeDBMockUpdateParams struct {
	data productentity.ProductUpdateData
}

// ProductPrimeDBMockUpdateParamPtrs contains pointers to parameters of the ProductPrimeDB.Update
type ProductPrimeDBMockUpdateParamPtrs struct {
	data *productentity.ProductUpdateData
}

// ProductPrimeDBMockUpdateResults contains results of the ProductPrimeDB.Update
type ProductPrimeDBMockUpdateResults struct {
	p1  productentity.Product
	err error
}

// ProductPrimeDBMockUpdateOrigins contains origins of expectations of the ProductPrimeDB.Update
type ProductPrimeDBMockUpdateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mProductPrimeDBMockUpdate) Optional() *mProductPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for ProductPrimeDB.Update
func (mmUpdate *mProductPrimeDBMockUpdate) Expect(data productentity.ProductUpdateData) *mProductPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProductPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("ProductPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &ProductPrimeDBMockUpdateParams{data}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectDataParam1 sets up expected param data for ProductPrimeDB.Update
func (mmUpdate *mProductPrimeDBMockUpdate) ExpectDataParam1(data productentity.ProductUpdateData) *mProductPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProductPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ProductPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ProductPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.data = &data
	mmUpdate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the ProductPrimeDB.Update
func (mmUpdate *mProductPrimeDBMockUpdate) Inspect(f func(data productentity.ProductUpdateData)) *mProductPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for ProductPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by ProductPrimeDB.Update
func (mmUpdate *mProductPrimeDBMockUpdate) Return(p1 productentity.Product, err error) *ProductPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProductPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &ProductPrimeDBMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the ProductPrimeDB.Update method
func (mmUpdate *mProductPrimeDBMockUpdate) Set(f func(data productentity.ProductUpdateData) (p1 productentity.Product, err error)) *ProductPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the ProductPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the ProductPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the ProductPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mProductPrimeDBMockUpdate) When(data productentity.ProductUpdateData) *ProductPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProductPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &ProductPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &ProductPrimeDBMockUpdateParams{data},
		expectationOrigins: ProductPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up ProductPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *ProductPrimeDBMockUpdateExpectation) Then(p1 productentity.Product, err error) *ProductPrimeDBMock {
	e.results = &ProductPrimeDBMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times ProductPrimeDB.Update should be invoked
func (mmUpdate *mProductPrimeDBMockUpdate) Times(n uint64) *mProductPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of ProductPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mProductPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.ProductPrimeDB
func (mmUpdate *ProductPrimeDBMock) Update(data productentity.ProductUpdateData) (p1 productentity.Product, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(data)
	}

	mm_params := ProductPrimeDBMockUpdateParams{data}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := ProductPrimeDBMockUpdateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdate.t.Errorf("ProductPrimeDBMock.Update got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("ProductPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the ProductPrimeDBMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(data)
	}
	mmUpdate.t.Fatalf("Unexpected call to ProductPrimeDBMock.Update. %v", data)
	return
}

// UpdateAfterCounter returns a count of finished ProductPrimeDBMock.Update invocations
func (mmUpdate *ProductPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of ProductPrimeDBMock.Update invocations
func (mmUpdate *ProductPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to ProductPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mProductPrimeDBMockUpdate) Calls() []*ProductPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*ProductPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *ProductPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *ProductPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProductPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProductPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProductPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to ProductPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProductPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProductPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByIIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProductPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProductPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByIIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockUpdateDone()
}
