package service

import (
	"context"
	"fmt"

	identityproviderrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository"
	sharedrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/repository"
	statusentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/entity"
	statusrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/repository"
)

//go:generate minimock -i StatusDomainService -o ../mocks/status_domain_service_mock.go -s _mock.go
type StatusDomainService interface {
	Get() statusentity.Status
}

type statusDomainService struct {
	version          string
	identityProvider identityproviderrepository.IdentityProvider
	serviceCache     statusrepository.StatusCache
	primeDB          sharedrepository.DBVersionPrimeDB
}

func NewStatusDomainService(
	cache statusrepository.StatusCache,
	identityProvider identityproviderrepository.IdentityProvider,
	primeDB sharedrepository.DBVersionPrimeDB,
	version string,
) StatusDomainService {
	return &statusDomainService{
		identityProvider: identityProvider,
		primeDB:          primeDB,
		serviceCache:     cache,
		version:          version,
	}
}

func (s *statusDomainService) Get() statusentity.Status {

	var services []statusentity.Service

	vPrimeDB, err := s.primeDB.GetVersion(context.Background())
	if err != nil {
		services = append(services, statusentity.Service{
			Name:    "PostgreSQL",
			Status:  fmt.Sprintf("Error: %s", err.Error()),
			Version: "unknown",
			Type:    "PrimeDB",
		})
	} else {
		services = append(services, statusentity.Service{
			Name:    "PostgreSQL",
			Status:  "Connected",
			Version: vPrimeDB,
			Type:    "PrimeDB",
		})
	}

	vKeycloak, err := s.identityProvider.GetVersion()
	if err != nil {
		services = append(services, statusentity.Service{
			Name:    "Keycloak",
			Status:  fmt.Sprintf("Error: %s", err.Error()),
			Version: "unknown",
			Type:    "Identity provider",
		})
	} else {
		services = append(services, statusentity.Service{
			Name:    "Keycloak",
			Status:  "Connected",
			Version: vKeycloak,
			Type:    "Identity provider",
		})
	}

	version, err := s.serviceCache.GetVersion(context.Background())
	if err != nil {
		services = append(services, statusentity.Service{
			Name:    "Redis",
			Status:  fmt.Sprintf("Error: %s", err.Error()),
			Version: "unknown",
			Type:    "Cache",
		})
	} else {
		services = append(services, statusentity.Service{
			Name:    "Redis",
			Status:  "Connected",
			Version: version,
			Type:    "Cache",
		})
	}

	return statusentity.Status{
		PMS: statusentity.PMS{
			Status:  "Running",
			Version: s.version,
		},
		Services: services,
	}
}
