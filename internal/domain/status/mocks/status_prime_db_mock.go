// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/repository.StatusPrimeDB -o status_prime_db_mock.go -n StatusPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	"github.com/gojuno/minimock/v3"
)

// StatusPrimeDBMock implements mm_repository.StatusPrimeDB
type StatusPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetVersion          func(ctx context.Context) (s1 string, err error)
	funcGetVersionOrigin    string
	inspectFuncGetVersion   func(ctx context.Context)
	afterGetVersionCounter  uint64
	beforeGetVersionCounter uint64
	GetVersionMock          mStatusPrimeDBMockGetVersion
}

// NewStatusPrimeDBMock returns a mock for mm_repository.StatusPrimeDB
func NewStatusPrimeDBMock(t minimock.Tester) *StatusPrimeDBMock {
	m := &StatusPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetVersionMock = mStatusPrimeDBMockGetVersion{mock: m}
	m.GetVersionMock.callArgs = []*StatusPrimeDBMockGetVersionParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mStatusPrimeDBMockGetVersion struct {
	optional           bool
	mock               *StatusPrimeDBMock
	defaultExpectation *StatusPrimeDBMockGetVersionExpectation
	expectations       []*StatusPrimeDBMockGetVersionExpectation

	callArgs []*StatusPrimeDBMockGetVersionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// StatusPrimeDBMockGetVersionExpectation specifies expectation struct of the StatusPrimeDB.GetVersion
type StatusPrimeDBMockGetVersionExpectation struct {
	mock               *StatusPrimeDBMock
	params             *StatusPrimeDBMockGetVersionParams
	paramPtrs          *StatusPrimeDBMockGetVersionParamPtrs
	expectationOrigins StatusPrimeDBMockGetVersionExpectationOrigins
	results            *StatusPrimeDBMockGetVersionResults
	returnOrigin       string
	Counter            uint64
}

// StatusPrimeDBMockGetVersionParams contains parameters of the StatusPrimeDB.GetVersion
type StatusPrimeDBMockGetVersionParams struct {
	ctx context.Context
}

// StatusPrimeDBMockGetVersionParamPtrs contains pointers to parameters of the StatusPrimeDB.GetVersion
type StatusPrimeDBMockGetVersionParamPtrs struct {
	ctx *context.Context
}

// StatusPrimeDBMockGetVersionResults contains results of the StatusPrimeDB.GetVersion
type StatusPrimeDBMockGetVersionResults struct {
	s1  string
	err error
}

// StatusPrimeDBMockGetVersionOrigins contains origins of expectations of the StatusPrimeDB.GetVersion
type StatusPrimeDBMockGetVersionExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Optional() *mStatusPrimeDBMockGetVersion {
	mmGetVersion.optional = true
	return mmGetVersion
}

// Expect sets up expected params for StatusPrimeDB.GetVersion
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Expect(ctx context.Context) *mStatusPrimeDBMockGetVersion {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusPrimeDBMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &StatusPrimeDBMockGetVersionExpectation{}
	}

	if mmGetVersion.defaultExpectation.paramPtrs != nil {
		mmGetVersion.mock.t.Fatalf("StatusPrimeDBMock.GetVersion mock is already set by ExpectParams functions")
	}

	mmGetVersion.defaultExpectation.params = &StatusPrimeDBMockGetVersionParams{ctx}
	mmGetVersion.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetVersion.expectations {
		if minimock.Equal(e.params, mmGetVersion.defaultExpectation.params) {
			mmGetVersion.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetVersion.defaultExpectation.params)
		}
	}

	return mmGetVersion
}

// ExpectCtxParam1 sets up expected param ctx for StatusPrimeDB.GetVersion
func (mmGetVersion *mStatusPrimeDBMockGetVersion) ExpectCtxParam1(ctx context.Context) *mStatusPrimeDBMockGetVersion {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusPrimeDBMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &StatusPrimeDBMockGetVersionExpectation{}
	}

	if mmGetVersion.defaultExpectation.params != nil {
		mmGetVersion.mock.t.Fatalf("StatusPrimeDBMock.GetVersion mock is already set by Expect")
	}

	if mmGetVersion.defaultExpectation.paramPtrs == nil {
		mmGetVersion.defaultExpectation.paramPtrs = &StatusPrimeDBMockGetVersionParamPtrs{}
	}
	mmGetVersion.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetVersion.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetVersion
}

// Inspect accepts an inspector function that has same arguments as the StatusPrimeDB.GetVersion
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Inspect(f func(ctx context.Context)) *mStatusPrimeDBMockGetVersion {
	if mmGetVersion.mock.inspectFuncGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("Inspect function is already set for StatusPrimeDBMock.GetVersion")
	}

	mmGetVersion.mock.inspectFuncGetVersion = f

	return mmGetVersion
}

// Return sets up results that will be returned by StatusPrimeDB.GetVersion
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Return(s1 string, err error) *StatusPrimeDBMock {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusPrimeDBMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &StatusPrimeDBMockGetVersionExpectation{mock: mmGetVersion.mock}
	}
	mmGetVersion.defaultExpectation.results = &StatusPrimeDBMockGetVersionResults{s1, err}
	mmGetVersion.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// Set uses given function f to mock the StatusPrimeDB.GetVersion method
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Set(f func(ctx context.Context) (s1 string, err error)) *StatusPrimeDBMock {
	if mmGetVersion.defaultExpectation != nil {
		mmGetVersion.mock.t.Fatalf("Default expectation is already set for the StatusPrimeDB.GetVersion method")
	}

	if len(mmGetVersion.expectations) > 0 {
		mmGetVersion.mock.t.Fatalf("Some expectations are already set for the StatusPrimeDB.GetVersion method")
	}

	mmGetVersion.mock.funcGetVersion = f
	mmGetVersion.mock.funcGetVersionOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// When sets expectation for the StatusPrimeDB.GetVersion which will trigger the result defined by the following
// Then helper
func (mmGetVersion *mStatusPrimeDBMockGetVersion) When(ctx context.Context) *StatusPrimeDBMockGetVersionExpectation {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusPrimeDBMock.GetVersion mock is already set by Set")
	}

	expectation := &StatusPrimeDBMockGetVersionExpectation{
		mock:               mmGetVersion.mock,
		params:             &StatusPrimeDBMockGetVersionParams{ctx},
		expectationOrigins: StatusPrimeDBMockGetVersionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetVersion.expectations = append(mmGetVersion.expectations, expectation)
	return expectation
}

// Then sets up StatusPrimeDB.GetVersion return parameters for the expectation previously defined by the When method
func (e *StatusPrimeDBMockGetVersionExpectation) Then(s1 string, err error) *StatusPrimeDBMock {
	e.results = &StatusPrimeDBMockGetVersionResults{s1, err}
	return e.mock
}

// Times sets number of times StatusPrimeDB.GetVersion should be invoked
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Times(n uint64) *mStatusPrimeDBMockGetVersion {
	if n == 0 {
		mmGetVersion.mock.t.Fatalf("Times of StatusPrimeDBMock.GetVersion mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetVersion.expectedInvocations, n)
	mmGetVersion.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetVersion
}

func (mmGetVersion *mStatusPrimeDBMockGetVersion) invocationsDone() bool {
	if len(mmGetVersion.expectations) == 0 && mmGetVersion.defaultExpectation == nil && mmGetVersion.mock.funcGetVersion == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetVersion.mock.afterGetVersionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetVersion.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetVersion implements mm_repository.StatusPrimeDB
func (mmGetVersion *StatusPrimeDBMock) GetVersion(ctx context.Context) (s1 string, err error) {
	mm_atomic.AddUint64(&mmGetVersion.beforeGetVersionCounter, 1)
	defer mm_atomic.AddUint64(&mmGetVersion.afterGetVersionCounter, 1)

	mmGetVersion.t.Helper()

	if mmGetVersion.inspectFuncGetVersion != nil {
		mmGetVersion.inspectFuncGetVersion(ctx)
	}

	mm_params := StatusPrimeDBMockGetVersionParams{ctx}

	// Record call args
	mmGetVersion.GetVersionMock.mutex.Lock()
	mmGetVersion.GetVersionMock.callArgs = append(mmGetVersion.GetVersionMock.callArgs, &mm_params)
	mmGetVersion.GetVersionMock.mutex.Unlock()

	for _, e := range mmGetVersion.GetVersionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.s1, e.results.err
		}
	}

	if mmGetVersion.GetVersionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetVersion.GetVersionMock.defaultExpectation.Counter, 1)
		mm_want := mmGetVersion.GetVersionMock.defaultExpectation.params
		mm_want_ptrs := mmGetVersion.GetVersionMock.defaultExpectation.paramPtrs

		mm_got := StatusPrimeDBMockGetVersionParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetVersion.t.Errorf("StatusPrimeDBMock.GetVersion got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetVersion.GetVersionMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetVersion.t.Errorf("StatusPrimeDBMock.GetVersion got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetVersion.GetVersionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetVersion.GetVersionMock.defaultExpectation.results
		if mm_results == nil {
			mmGetVersion.t.Fatal("No results are set for the StatusPrimeDBMock.GetVersion")
		}
		return (*mm_results).s1, (*mm_results).err
	}
	if mmGetVersion.funcGetVersion != nil {
		return mmGetVersion.funcGetVersion(ctx)
	}
	mmGetVersion.t.Fatalf("Unexpected call to StatusPrimeDBMock.GetVersion. %v", ctx)
	return
}

// GetVersionAfterCounter returns a count of finished StatusPrimeDBMock.GetVersion invocations
func (mmGetVersion *StatusPrimeDBMock) GetVersionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.afterGetVersionCounter)
}

// GetVersionBeforeCounter returns a count of StatusPrimeDBMock.GetVersion invocations
func (mmGetVersion *StatusPrimeDBMock) GetVersionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.beforeGetVersionCounter)
}

// Calls returns a list of arguments used in each call to StatusPrimeDBMock.GetVersion.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetVersion *mStatusPrimeDBMockGetVersion) Calls() []*StatusPrimeDBMockGetVersionParams {
	mmGetVersion.mutex.RLock()

	argCopy := make([]*StatusPrimeDBMockGetVersionParams, len(mmGetVersion.callArgs))
	copy(argCopy, mmGetVersion.callArgs)

	mmGetVersion.mutex.RUnlock()

	return argCopy
}

// MinimockGetVersionDone returns true if the count of the GetVersion invocations corresponds
// the number of defined expectations
func (m *StatusPrimeDBMock) MinimockGetVersionDone() bool {
	if m.GetVersionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetVersionMock.invocationsDone()
}

// MinimockGetVersionInspect logs each unmet expectation
func (m *StatusPrimeDBMock) MinimockGetVersionInspect() {
	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to StatusPrimeDBMock.GetVersion at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetVersionCounter := mm_atomic.LoadUint64(&m.afterGetVersionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetVersionMock.defaultExpectation != nil && afterGetVersionCounter < 1 {
		if m.GetVersionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to StatusPrimeDBMock.GetVersion at\n%s", m.GetVersionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to StatusPrimeDBMock.GetVersion at\n%s with params: %#v", m.GetVersionMock.defaultExpectation.expectationOrigins.origin, *m.GetVersionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetVersion != nil && afterGetVersionCounter < 1 {
		m.t.Errorf("Expected call to StatusPrimeDBMock.GetVersion at\n%s", m.funcGetVersionOrigin)
	}

	if !m.GetVersionMock.invocationsDone() && afterGetVersionCounter > 0 {
		m.t.Errorf("Expected %d calls to StatusPrimeDBMock.GetVersion at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetVersionMock.expectedInvocations), m.GetVersionMock.expectedInvocationsOrigin, afterGetVersionCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *StatusPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetVersionInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *StatusPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *StatusPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetVersionDone()
}
