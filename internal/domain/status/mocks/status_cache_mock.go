// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/repository.StatusCache -o status_cache_mock.go -n StatusCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	"github.com/gojuno/minimock/v3"
)

// StatusCacheMock implements mm_repository.StatusCache
type StatusCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetVersion          func(ctx context.Context) (s1 string, err error)
	funcGetVersionOrigin    string
	inspectFuncGetVersion   func(ctx context.Context)
	afterGetVersionCounter  uint64
	beforeGetVersionCounter uint64
	GetVersionMock          mStatusCacheMockGetVersion
}

// NewStatusCacheMock returns a mock for mm_repository.StatusCache
func NewStatusCacheMock(t minimock.Tester) *StatusCacheMock {
	m := &StatusCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetVersionMock = mStatusCacheMockGetVersion{mock: m}
	m.GetVersionMock.callArgs = []*StatusCacheMockGetVersionParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mStatusCacheMockGetVersion struct {
	optional           bool
	mock               *StatusCacheMock
	defaultExpectation *StatusCacheMockGetVersionExpectation
	expectations       []*StatusCacheMockGetVersionExpectation

	callArgs []*StatusCacheMockGetVersionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// StatusCacheMockGetVersionExpectation specifies expectation struct of the StatusCache.GetVersion
type StatusCacheMockGetVersionExpectation struct {
	mock               *StatusCacheMock
	params             *StatusCacheMockGetVersionParams
	paramPtrs          *StatusCacheMockGetVersionParamPtrs
	expectationOrigins StatusCacheMockGetVersionExpectationOrigins
	results            *StatusCacheMockGetVersionResults
	returnOrigin       string
	Counter            uint64
}

// StatusCacheMockGetVersionParams contains parameters of the StatusCache.GetVersion
type StatusCacheMockGetVersionParams struct {
	ctx context.Context
}

// StatusCacheMockGetVersionParamPtrs contains pointers to parameters of the StatusCache.GetVersion
type StatusCacheMockGetVersionParamPtrs struct {
	ctx *context.Context
}

// StatusCacheMockGetVersionResults contains results of the StatusCache.GetVersion
type StatusCacheMockGetVersionResults struct {
	s1  string
	err error
}

// StatusCacheMockGetVersionOrigins contains origins of expectations of the StatusCache.GetVersion
type StatusCacheMockGetVersionExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetVersion *mStatusCacheMockGetVersion) Optional() *mStatusCacheMockGetVersion {
	mmGetVersion.optional = true
	return mmGetVersion
}

// Expect sets up expected params for StatusCache.GetVersion
func (mmGetVersion *mStatusCacheMockGetVersion) Expect(ctx context.Context) *mStatusCacheMockGetVersion {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusCacheMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &StatusCacheMockGetVersionExpectation{}
	}

	if mmGetVersion.defaultExpectation.paramPtrs != nil {
		mmGetVersion.mock.t.Fatalf("StatusCacheMock.GetVersion mock is already set by ExpectParams functions")
	}

	mmGetVersion.defaultExpectation.params = &StatusCacheMockGetVersionParams{ctx}
	mmGetVersion.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetVersion.expectations {
		if minimock.Equal(e.params, mmGetVersion.defaultExpectation.params) {
			mmGetVersion.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetVersion.defaultExpectation.params)
		}
	}

	return mmGetVersion
}

// ExpectCtxParam1 sets up expected param ctx for StatusCache.GetVersion
func (mmGetVersion *mStatusCacheMockGetVersion) ExpectCtxParam1(ctx context.Context) *mStatusCacheMockGetVersion {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusCacheMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &StatusCacheMockGetVersionExpectation{}
	}

	if mmGetVersion.defaultExpectation.params != nil {
		mmGetVersion.mock.t.Fatalf("StatusCacheMock.GetVersion mock is already set by Expect")
	}

	if mmGetVersion.defaultExpectation.paramPtrs == nil {
		mmGetVersion.defaultExpectation.paramPtrs = &StatusCacheMockGetVersionParamPtrs{}
	}
	mmGetVersion.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetVersion.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetVersion
}

// Inspect accepts an inspector function that has same arguments as the StatusCache.GetVersion
func (mmGetVersion *mStatusCacheMockGetVersion) Inspect(f func(ctx context.Context)) *mStatusCacheMockGetVersion {
	if mmGetVersion.mock.inspectFuncGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("Inspect function is already set for StatusCacheMock.GetVersion")
	}

	mmGetVersion.mock.inspectFuncGetVersion = f

	return mmGetVersion
}

// Return sets up results that will be returned by StatusCache.GetVersion
func (mmGetVersion *mStatusCacheMockGetVersion) Return(s1 string, err error) *StatusCacheMock {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusCacheMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &StatusCacheMockGetVersionExpectation{mock: mmGetVersion.mock}
	}
	mmGetVersion.defaultExpectation.results = &StatusCacheMockGetVersionResults{s1, err}
	mmGetVersion.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// Set uses given function f to mock the StatusCache.GetVersion method
func (mmGetVersion *mStatusCacheMockGetVersion) Set(f func(ctx context.Context) (s1 string, err error)) *StatusCacheMock {
	if mmGetVersion.defaultExpectation != nil {
		mmGetVersion.mock.t.Fatalf("Default expectation is already set for the StatusCache.GetVersion method")
	}

	if len(mmGetVersion.expectations) > 0 {
		mmGetVersion.mock.t.Fatalf("Some expectations are already set for the StatusCache.GetVersion method")
	}

	mmGetVersion.mock.funcGetVersion = f
	mmGetVersion.mock.funcGetVersionOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// When sets expectation for the StatusCache.GetVersion which will trigger the result defined by the following
// Then helper
func (mmGetVersion *mStatusCacheMockGetVersion) When(ctx context.Context) *StatusCacheMockGetVersionExpectation {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("StatusCacheMock.GetVersion mock is already set by Set")
	}

	expectation := &StatusCacheMockGetVersionExpectation{
		mock:               mmGetVersion.mock,
		params:             &StatusCacheMockGetVersionParams{ctx},
		expectationOrigins: StatusCacheMockGetVersionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetVersion.expectations = append(mmGetVersion.expectations, expectation)
	return expectation
}

// Then sets up StatusCache.GetVersion return parameters for the expectation previously defined by the When method
func (e *StatusCacheMockGetVersionExpectation) Then(s1 string, err error) *StatusCacheMock {
	e.results = &StatusCacheMockGetVersionResults{s1, err}
	return e.mock
}

// Times sets number of times StatusCache.GetVersion should be invoked
func (mmGetVersion *mStatusCacheMockGetVersion) Times(n uint64) *mStatusCacheMockGetVersion {
	if n == 0 {
		mmGetVersion.mock.t.Fatalf("Times of StatusCacheMock.GetVersion mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetVersion.expectedInvocations, n)
	mmGetVersion.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetVersion
}

func (mmGetVersion *mStatusCacheMockGetVersion) invocationsDone() bool {
	if len(mmGetVersion.expectations) == 0 && mmGetVersion.defaultExpectation == nil && mmGetVersion.mock.funcGetVersion == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetVersion.mock.afterGetVersionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetVersion.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetVersion implements mm_repository.StatusCache
func (mmGetVersion *StatusCacheMock) GetVersion(ctx context.Context) (s1 string, err error) {
	mm_atomic.AddUint64(&mmGetVersion.beforeGetVersionCounter, 1)
	defer mm_atomic.AddUint64(&mmGetVersion.afterGetVersionCounter, 1)

	mmGetVersion.t.Helper()

	if mmGetVersion.inspectFuncGetVersion != nil {
		mmGetVersion.inspectFuncGetVersion(ctx)
	}

	mm_params := StatusCacheMockGetVersionParams{ctx}

	// Record call args
	mmGetVersion.GetVersionMock.mutex.Lock()
	mmGetVersion.GetVersionMock.callArgs = append(mmGetVersion.GetVersionMock.callArgs, &mm_params)
	mmGetVersion.GetVersionMock.mutex.Unlock()

	for _, e := range mmGetVersion.GetVersionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.s1, e.results.err
		}
	}

	if mmGetVersion.GetVersionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetVersion.GetVersionMock.defaultExpectation.Counter, 1)
		mm_want := mmGetVersion.GetVersionMock.defaultExpectation.params
		mm_want_ptrs := mmGetVersion.GetVersionMock.defaultExpectation.paramPtrs

		mm_got := StatusCacheMockGetVersionParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetVersion.t.Errorf("StatusCacheMock.GetVersion got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetVersion.GetVersionMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetVersion.t.Errorf("StatusCacheMock.GetVersion got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetVersion.GetVersionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetVersion.GetVersionMock.defaultExpectation.results
		if mm_results == nil {
			mmGetVersion.t.Fatal("No results are set for the StatusCacheMock.GetVersion")
		}
		return (*mm_results).s1, (*mm_results).err
	}
	if mmGetVersion.funcGetVersion != nil {
		return mmGetVersion.funcGetVersion(ctx)
	}
	mmGetVersion.t.Fatalf("Unexpected call to StatusCacheMock.GetVersion. %v", ctx)
	return
}

// GetVersionAfterCounter returns a count of finished StatusCacheMock.GetVersion invocations
func (mmGetVersion *StatusCacheMock) GetVersionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.afterGetVersionCounter)
}

// GetVersionBeforeCounter returns a count of StatusCacheMock.GetVersion invocations
func (mmGetVersion *StatusCacheMock) GetVersionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.beforeGetVersionCounter)
}

// Calls returns a list of arguments used in each call to StatusCacheMock.GetVersion.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetVersion *mStatusCacheMockGetVersion) Calls() []*StatusCacheMockGetVersionParams {
	mmGetVersion.mutex.RLock()

	argCopy := make([]*StatusCacheMockGetVersionParams, len(mmGetVersion.callArgs))
	copy(argCopy, mmGetVersion.callArgs)

	mmGetVersion.mutex.RUnlock()

	return argCopy
}

// MinimockGetVersionDone returns true if the count of the GetVersion invocations corresponds
// the number of defined expectations
func (m *StatusCacheMock) MinimockGetVersionDone() bool {
	if m.GetVersionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetVersionMock.invocationsDone()
}

// MinimockGetVersionInspect logs each unmet expectation
func (m *StatusCacheMock) MinimockGetVersionInspect() {
	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to StatusCacheMock.GetVersion at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetVersionCounter := mm_atomic.LoadUint64(&m.afterGetVersionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetVersionMock.defaultExpectation != nil && afterGetVersionCounter < 1 {
		if m.GetVersionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to StatusCacheMock.GetVersion at\n%s", m.GetVersionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to StatusCacheMock.GetVersion at\n%s with params: %#v", m.GetVersionMock.defaultExpectation.expectationOrigins.origin, *m.GetVersionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetVersion != nil && afterGetVersionCounter < 1 {
		m.t.Errorf("Expected call to StatusCacheMock.GetVersion at\n%s", m.funcGetVersionOrigin)
	}

	if !m.GetVersionMock.invocationsDone() && afterGetVersionCounter > 0 {
		m.t.Errorf("Expected %d calls to StatusCacheMock.GetVersion at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetVersionMock.expectedInvocations), m.GetVersionMock.expectedInvocationsOrigin, afterGetVersionCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *StatusCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetVersionInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *StatusCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *StatusCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetVersionDone()
}
