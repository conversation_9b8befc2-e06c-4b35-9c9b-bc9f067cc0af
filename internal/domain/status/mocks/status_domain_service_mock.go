// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/service.StatusDomainService -o status_domain_service_mock.go -n StatusDomainServiceMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	statusentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/status/entity"
	"github.com/gojuno/minimock/v3"
)

// StatusDomainServiceMock implements mm_service.StatusDomainService
type StatusDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGet          func() (s1 statusentity.Status)
	funcGetOrigin    string
	inspectFuncGet   func()
	afterGetCounter  uint64
	beforeGetCounter uint64
	GetMock          mStatusDomainServiceMockGet
}

// NewStatusDomainServiceMock returns a mock for mm_service.StatusDomainService
func NewStatusDomainServiceMock(t minimock.Tester) *StatusDomainServiceMock {
	m := &StatusDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetMock = mStatusDomainServiceMockGet{mock: m}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mStatusDomainServiceMockGet struct {
	optional           bool
	mock               *StatusDomainServiceMock
	defaultExpectation *StatusDomainServiceMockGetExpectation
	expectations       []*StatusDomainServiceMockGetExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// StatusDomainServiceMockGetExpectation specifies expectation struct of the StatusDomainService.Get
type StatusDomainServiceMockGetExpectation struct {
	mock *StatusDomainServiceMock

	results      *StatusDomainServiceMockGetResults
	returnOrigin string
	Counter      uint64
}

// StatusDomainServiceMockGetResults contains results of the StatusDomainService.Get
type StatusDomainServiceMockGetResults struct {
	s1 statusentity.Status
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGet *mStatusDomainServiceMockGet) Optional() *mStatusDomainServiceMockGet {
	mmGet.optional = true
	return mmGet
}

// Expect sets up expected params for StatusDomainService.Get
func (mmGet *mStatusDomainServiceMockGet) Expect() *mStatusDomainServiceMockGet {
	if mmGet.mock.funcGet != nil {
		mmGet.mock.t.Fatalf("StatusDomainServiceMock.Get mock is already set by Set")
	}

	if mmGet.defaultExpectation == nil {
		mmGet.defaultExpectation = &StatusDomainServiceMockGetExpectation{}
	}

	return mmGet
}

// Inspect accepts an inspector function that has same arguments as the StatusDomainService.Get
func (mmGet *mStatusDomainServiceMockGet) Inspect(f func()) *mStatusDomainServiceMockGet {
	if mmGet.mock.inspectFuncGet != nil {
		mmGet.mock.t.Fatalf("Inspect function is already set for StatusDomainServiceMock.Get")
	}

	mmGet.mock.inspectFuncGet = f

	return mmGet
}

// Return sets up results that will be returned by StatusDomainService.Get
func (mmGet *mStatusDomainServiceMockGet) Return(s1 statusentity.Status) *StatusDomainServiceMock {
	if mmGet.mock.funcGet != nil {
		mmGet.mock.t.Fatalf("StatusDomainServiceMock.Get mock is already set by Set")
	}

	if mmGet.defaultExpectation == nil {
		mmGet.defaultExpectation = &StatusDomainServiceMockGetExpectation{mock: mmGet.mock}
	}
	mmGet.defaultExpectation.results = &StatusDomainServiceMockGetResults{s1}
	mmGet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGet.mock
}

// Set uses given function f to mock the StatusDomainService.Get method
func (mmGet *mStatusDomainServiceMockGet) Set(f func() (s1 statusentity.Status)) *StatusDomainServiceMock {
	if mmGet.defaultExpectation != nil {
		mmGet.mock.t.Fatalf("Default expectation is already set for the StatusDomainService.Get method")
	}

	if len(mmGet.expectations) > 0 {
		mmGet.mock.t.Fatalf("Some expectations are already set for the StatusDomainService.Get method")
	}

	mmGet.mock.funcGet = f
	mmGet.mock.funcGetOrigin = minimock.CallerInfo(1)
	return mmGet.mock
}

// Times sets number of times StatusDomainService.Get should be invoked
func (mmGet *mStatusDomainServiceMockGet) Times(n uint64) *mStatusDomainServiceMockGet {
	if n == 0 {
		mmGet.mock.t.Fatalf("Times of StatusDomainServiceMock.Get mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGet.expectedInvocations, n)
	mmGet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGet
}

func (mmGet *mStatusDomainServiceMockGet) invocationsDone() bool {
	if len(mmGet.expectations) == 0 && mmGet.defaultExpectation == nil && mmGet.mock.funcGet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGet.mock.afterGetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Get implements mm_service.StatusDomainService
func (mmGet *StatusDomainServiceMock) Get() (s1 statusentity.Status) {
	mm_atomic.AddUint64(&mmGet.beforeGetCounter, 1)
	defer mm_atomic.AddUint64(&mmGet.afterGetCounter, 1)

	mmGet.t.Helper()

	if mmGet.inspectFuncGet != nil {
		mmGet.inspectFuncGet()
	}

	if mmGet.GetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGet.GetMock.defaultExpectation.Counter, 1)

		mm_results := mmGet.GetMock.defaultExpectation.results
		if mm_results == nil {
			mmGet.t.Fatal("No results are set for the StatusDomainServiceMock.Get")
		}
		return (*mm_results).s1
	}
	if mmGet.funcGet != nil {
		return mmGet.funcGet()
	}
	mmGet.t.Fatalf("Unexpected call to StatusDomainServiceMock.Get.")
	return
}

// GetAfterCounter returns a count of finished StatusDomainServiceMock.Get invocations
func (mmGet *StatusDomainServiceMock) GetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGet.afterGetCounter)
}

// GetBeforeCounter returns a count of StatusDomainServiceMock.Get invocations
func (mmGet *StatusDomainServiceMock) GetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGet.beforeGetCounter)
}

// MinimockGetDone returns true if the count of the Get invocations corresponds
// the number of defined expectations
func (m *StatusDomainServiceMock) MinimockGetDone() bool {
	if m.GetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetMock.invocationsDone()
}

// MinimockGetInspect logs each unmet expectation
func (m *StatusDomainServiceMock) MinimockGetInspect() {
	for _, e := range m.GetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to StatusDomainServiceMock.Get")
		}
	}

	afterGetCounter := mm_atomic.LoadUint64(&m.afterGetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetMock.defaultExpectation != nil && afterGetCounter < 1 {
		m.t.Errorf("Expected call to StatusDomainServiceMock.Get at\n%s", m.GetMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGet != nil && afterGetCounter < 1 {
		m.t.Errorf("Expected call to StatusDomainServiceMock.Get at\n%s", m.funcGetOrigin)
	}

	if !m.GetMock.invocationsDone() && afterGetCounter > 0 {
		m.t.Errorf("Expected %d calls to StatusDomainServiceMock.Get at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetMock.expectedInvocations), m.GetMock.expectedInvocationsOrigin, afterGetCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *StatusDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *StatusDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *StatusDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetDone()
}
