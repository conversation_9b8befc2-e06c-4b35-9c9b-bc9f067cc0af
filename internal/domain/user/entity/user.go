package entity

import (
	"time"

	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

type User struct {
	ID                  int64
	CategoryID          int64
	Email               string
	FullName            string
	Position            string
	IsAdmin             bool
	ActiveFlg           bool
	LastActiveProductID int64
	LastLoginAt         *time.Time
	CreatedAt           time.Time
	DeletedAt           *time.Time
	Photo               *[]byte
	Products            []productentity.Product
}

type UserDetails struct {
	ID          int64
	CategoryID  int64
	Email       string
	FullName    string
	Position    string
	IsAdmin     bool
	LastLoginAt *time.Time
	Photo       *[]byte
	CreatedAt   time.Time
	Products    []UserProduct
	Roles       []UserRole
	Groups      []UserGroup
}

type UserFiltersData struct {
	ID          *int64
	Email       *string
	FullName    *string
	CategoryIDs *[]int64
	IsAdmin     *bool
	ProductIDs  *[]int64
}

type UserCreateData struct {
	CategoryID int64
	Email      string
	FullName   string
	Position   string
	Photo      *[]byte
}

type UserUpdateData struct {
	ID                  int64
	CategoryID          *int64
	Email               *string
	FullName            *string
	Position            *string
	IsAdmin             *bool
	ActiveFlg           *bool
	LastActiveProductID *int64
	LastLoginAt         *time.Time
	DeletedAt           *time.Time
	RoleIDs             *[]int64
	GroupIDs            *[]int64
	ProductIDs          *[]int64
	Photo               *[]byte
	Roles               *[]UserRoleUpdateData
	Groups              *[]UserGroupUpdateData
}

type UserGroup struct {
	ID        int64
	UserID    int64
	GroupID   int64
	CreatedAt time.Time
	Name      string
	IsActive  bool
	Type      string
	ProductID *int64
}

type UserGroupCreateData struct {
	UserID  int64
	GroupID int64
}

type UserGroupUpdateData struct {
	ID        int64
	UserID    *int64
	GroupID   *int64
	ProductID *int64
}

type UserRole struct {
	ID        int64
	UserID    int64
	RoleID    int64
	CreatedAt time.Time
	Name      string
	IsActive  bool
	Type      string
	ProductID *int64
}

type UserRoleCreateData struct {
	UserID int64
	RoleID int64
}

type UserRoleUpdateData struct {
	ID        int64
	UserID    *int64
	RoleID    *int64
	ProductID *int64
}

type UserProduct struct {
	ID       int64
	IID      string
	TechName string
	Name     string
}

type UserWithProduct struct {
	ID       int64
	Email    string
	FullName string
	Product  *productentity.Product
}

type UserProductLink struct {
	UserID    int64
	ProductID *int64
}
