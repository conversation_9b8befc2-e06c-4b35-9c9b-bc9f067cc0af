package aggregate

import (
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

type UserWithDetails struct {
	entity.User
	Groups   []groupentity.GroupWithProduct
	Roles    []roleentity.RoleWithProduct
	Products []productentity.ProductBasic
}

type AdminUserUpdateData struct {
	ID         int64
	IsAdmin    *bool
	CategoryID *int64
	RoleIDs    *[]roleentity.RoleProductLink
	GroupIDs   *[]groupentity.GroupProductLink
	ProductIDs *[]int64
}
