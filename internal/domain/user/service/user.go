package service

import (
	"context"
	"errors"
	"fmt"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	identityproviderrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

//go:generate minimock -i UserDomainService -o ../mocks/user_domain_service_mock.go -s _mock.go
type UserDomainService interface {
	Create(ctx context.Context, data userentity.User) (userentity.User, error)
	CreateByEmail(ctx context.Context, email string) (userentity.User, error)
	GetAdmins() ([]userentity.User, error)
	GetAll() ([]userentity.User, error)
	GetAllAsAdmin() ([]userentity.AdminUser, error)
	GetAllPaginated(pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error)
	GetByCategoryID(categoryID int64) ([]userentity.User, error)
	GetByEmail(email string) (userentity.User, error)
	GetByFilters(userFilters userentity.UserFiltersData) ([]userentity.User, error)
	GetByFiltersPaginated(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error)
	GetByID(id int64) (userentity.User, error)
	GetUserGroups(userID int64) ([]int64, error)
	GetUserGroupsByGroupID(groupID int64) ([]userentity.UserWithProduct, error)
	GetUserGroupsByUserID(id int64) ([]userentity.UserGroup, error)
	GetUserRolesByRoleID(roleID int64) ([]userentity.User, error)
	GetUserRolesByUserID(id int64) ([]userentity.UserRole, error)
	GetUsersWithProductsByGroupID(groupID int64) ([]userentity.UserWithProduct, error)
	GetUsersWithProductsByRoleID(roleID int64) ([]userentity.UserWithProduct, error)
	Update(data userentity.UserUpdateData) (userentity.User, error)
	UpdateLinksWithGroups(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) error
	UpdateLinksWithProducts(ctx context.Context, userID int64, productIDs []int64) error
	UpdateLinksWithRoles(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) error
	DeleteAsAdmin(ctx context.Context, id int64) error
	DeleteByUserID(ctx context.Context, userID int64) error
	DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error
	DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error
}

type userDomainService struct {
	identityProvider     identityproviderrepository.IdentityProvider
	productRepo          productrepository.ProductPrimeDB
	userGroupRepo        userrepository.UserGroupPrimeDB
	userRepo             userrepository.UserPrimeDB
	userRoleRepo         userrepository.UserRolePrimeDB
	groupRoleRepo        grouprepository.GroupRolePrimeDB
	participantRepo      participantrepository.ParticipantPrimeDB
	participantRoleRepo  participantrepository.ParticipantRolePrimeDB
	participantGroupRepo participantrepository.ParticipantGroupPrimeDB
}

func NewUserDomainService(
	identityProvider identityproviderrepository.IdentityProvider,
	productRepo productrepository.ProductPrimeDB,
	userGroupRepo userrepository.UserGroupPrimeDB,
	userRepo userrepository.UserPrimeDB,
	userRoleRepo userrepository.UserRolePrimeDB,
	groupRoleRepo grouprepository.GroupRolePrimeDB,
	participantRepo participantrepository.ParticipantPrimeDB,
	participantRoleRepo participantrepository.ParticipantRolePrimeDB,
	participantGroupRepo participantrepository.ParticipantGroupPrimeDB,

) UserDomainService {
	return &userDomainService{
		identityProvider:     identityProvider,
		productRepo:          productRepo,
		userGroupRepo:        userGroupRepo,
		userRepo:             userRepo,
		userRoleRepo:         userRoleRepo,
		groupRoleRepo:        groupRoleRepo,
		participantRepo:      participantRepo,
		participantRoleRepo:  participantRoleRepo,
		participantGroupRepo: participantGroupRepo,
	}
}

func (s *userDomainService) Create(ctx context.Context, user userentity.User) (userentity.User, error) {
	return s.userRepo.Create(ctx, user)
}

func (s *userDomainService) CreateByEmail(ctx context.Context, email string) (userentity.User, error) {

	user, err := s.userRepo.GetByEmail(email)
	if err == nil {
		return user, nil
	}

	if !errkit.IsNotFoundError(err) {
		return userentity.User{}, err
	}

	userKC, errKC := s.identityProvider.GetUsersBySearch(email)
	if errKC != nil {
		return userentity.User{}, errKC
	}

	if len(userKC) == 0 {
		return userentity.User{}, errors.New("no users found in Keycloak with the provided email")
	}

	var photo []byte
	if userKC[0].Photo != "" {
		photo = []byte(userKC[0].Photo)
	}

	userNew, errNew := s.userRepo.Create(ctx, userentity.User{
		CategoryID: constants.CategoryDefaultID,
		Email:      userKC[0].Email,
		FullName:   userKC[0].FullName,
		Position:   userKC[0].Position,
		Photo:      &photo,
	})
	if errNew != nil {
		return userentity.User{}, errNew
	}

	return userNew, nil
}

func (s *userDomainService) GetAll() ([]userentity.User, error) {
	users, err := s.userRepo.GetAll()
	if err != nil {
		return nil, err
	}
	for i := range users {
		products, err := s.productRepo.GetByUserID(users[i].ID)
		if err != nil {
			return nil, err
		}
		users[i].Products = products
	}
	return users, nil
}

func (s *userDomainService) GetAllAsAdmin() ([]userentity.AdminUser, error) {

	users, err := s.userRepo.GetAll()
	if err != nil {
		return nil, err
	}

	adminUsers := make([]userentity.AdminUser, 0, len(users))
	for _, user := range users {

		var productsBasic []productentity.ProductBasic
		products, err := s.productRepo.GetByUserID(user.ID)
		if err != nil {
			return nil, err
		}
		for _, product := range products {
			productsBasic = append(productsBasic, product.ProductBasic())
		}

		adminUsers = append(adminUsers, userentity.AdminUser{
			ID:          user.ID,
			Email:       user.Email,
			FullName:    user.FullName,
			CategoryID:  user.CategoryID,
			Position:    user.Position,
			IsAdmin:     user.IsAdmin,
			CreatedAt:   user.CreatedAt,
			LastLoginAt: user.LastLoginAt,
			Products:    productsBasic,
		})
	}

	return adminUsers, nil
}

func (s *userDomainService) GetAllPaginated(pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error) {
	users, err := s.userRepo.GetAllPaginated(pagination)
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	for i := range users.Items {
		products, err := s.productRepo.GetByUserID(users.Items[i].ID)
		if err != nil {
			return sharedentity.PaginatedResult[userentity.User]{}, err
		}
		users.Items[i].Products = products
	}

	return users, nil
}

func (s *userDomainService) GetByCategoryID(categoryID int64) ([]userentity.User, error) {
	return s.userRepo.GetByCategoryID(categoryID)
}

func (s *userDomainService) GetByEmail(email string) (userentity.User, error) {
	return s.userRepo.GetByEmail(email)
}

func (s *userDomainService) GetByFilters(userFilters userentity.UserFiltersData) ([]userentity.User, error) {
	users, err := s.userRepo.GetByFilters(userFilters)
	if err != nil {
		return nil, err
	}

	for i := range users {
		products, err := s.productRepo.GetByUserID(users[i].ID)
		if err != nil {
			return nil, err
		}
		users[i].Products = products
	}

	return users, nil
}

func (s *userDomainService) GetByFiltersPaginated(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error) {
	users, err := s.userRepo.GetByFiltersPaginated(userFilters, pagination)
	if err != nil {
		return sharedentity.PaginatedResult[userentity.User]{}, err
	}

	for i := range users.Items {
		products, err := s.productRepo.GetByUserID(users.Items[i].ID)
		if err != nil {
			return sharedentity.PaginatedResult[userentity.User]{}, err
		}
		users.Items[i].Products = products
	}

	return users, nil
}

func (s *userDomainService) GetByID(id int64) (userentity.User, error) {

	user, err := s.userRepo.GetByID(id)
	if err != nil {
		return userentity.User{}, err
	}

	user.Products, err = s.productRepo.GetByUserID(id)
	if err != nil {
		return userentity.User{}, err
	}

	return user, nil
}

func (s *userDomainService) GetUserGroups(userID int64) ([]int64, error) {
	return s.userGroupRepo.GetUserGroups(userID)
}

func (s *userDomainService) GetUserGroupsByGroupID(groupID int64) ([]userentity.UserWithProduct, error) {

	userGroups, err := s.userGroupRepo.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	users := make([]userentity.UserWithProduct, 0, len(userGroups))
	for _, userRole := range userGroups {
		user, err := s.userRepo.GetByID(userRole.UserID)
		if err != nil {
			return nil, err
		}
		users = append(users, userentity.UserWithProduct{
			ID:       user.ID,
			Email:    user.Email,
			FullName: user.FullName,
		})
	}

	return users, nil
}

func (s *userDomainService) GetUserGroupsByUserID(id int64) ([]userentity.UserGroup, error) {
	return s.userGroupRepo.GetByUserID(id)
}

func (s *userDomainService) GetUserRolesByRoleID(roleID int64) ([]userentity.User, error) {
	userRoles, err := s.userRoleRepo.GetUserRolesByRoleID(roleID)
	if err != nil {
		return nil, err
	}

	users := make([]userentity.User, 0, len(userRoles))
	for _, userRole := range userRoles {
		user, err := s.userRepo.GetByID(userRole.UserID)
		if err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	return users, nil
}

func (s *userDomainService) GetUserRolesByUserID(id int64) ([]userentity.UserRole, error) {
	return s.userRoleRepo.GetByUserID(id)
}

func (s *userDomainService) GetUsersWithProductsByGroupID(groupID int64) ([]userentity.UserWithProduct, error) {

	participants, err := s.participantRepo.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	users := make([]userentity.UserWithProduct, 0, len(participants))
	for _, participant := range participants {
		user, err := s.userRepo.GetByID(participant.UserID)
		if err != nil {
			return nil, err
		}
		product, err := s.productRepo.GetByID(participant.ProductID)
		if err != nil {
			return nil, err
		}

		users = append(users, userentity.UserWithProduct{
			ID:       user.ID,
			Email:    user.Email,
			FullName: user.FullName,
			Product:  product.ProductOrNil(),
		})
	}

	return users, nil
}

func (s *userDomainService) GetUsersWithProductsByRoleID(roleID int64) ([]userentity.UserWithProduct, error) {

	participants, err := s.participantRepo.GetByRoleID(roleID)
	if err != nil {
		return nil, err
	}

	users := make([]userentity.UserWithProduct, 0, len(participants))
	for _, participant := range participants {
		user, err := s.userRepo.GetByID(participant.UserID)
		if err != nil {
			return nil, err
		}
		product, err := s.productRepo.GetByID(participant.ProductID)
		if err != nil {
			return nil, err
		}

		users = append(users, userentity.UserWithProduct{
			ID:       user.ID,
			Email:    user.Email,
			FullName: user.FullName,
			Product:  product.ProductOrNil(),
		})
	}

	return users, nil
}

func (s *userDomainService) Update(data userentity.UserUpdateData) (userentity.User, error) {
	return s.userRepo.Update(data)
}

func (s *userDomainService) UpdateLinksWithGroups(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) error {
	groupsWithoutProduct, groupsWithProduct := separateGroupsByProduct(groups)

	existingUserGroups, err := s.userGroupRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	toDeleteUserGroups, toAddUserGroups := diffUserGroupIDsForUser(existingUserGroups, groupsWithoutProduct)

	if len(toDeleteUserGroups) > 0 {
		err = s.userGroupRepo.DeleteByUserIDAndGroupIDs(ctx, userID, toDeleteUserGroups)
		if err != nil {
			return err
		}
	}

	if len(toAddUserGroups) > 0 {
		err = s.userGroupRepo.AssignUserToGroups(userID, toAddUserGroups)
		if err != nil {
			return err
		}
	}

	participants, err := s.participantRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	participantIDs := make([]int64, 0, len(participants))
	for _, participant := range participants {
		participantIDs = append(participantIDs, participant.ID)
	}

	existingParticipantGroups, err := s.participantGroupRepo.GetByParticipantIDs(participantIDs)
	if err != nil {
		return err
	}

	expectedParticipantGroups := buildExpectedParticipantGroups(participants, groupsWithProduct)

	toDeleteParticipantGroups, toAddParticipantGroups := diffParticipantGroupsForUser(existingParticipantGroups, expectedParticipantGroups)

	for _, participantGroup := range toDeleteParticipantGroups {
		err = s.participantGroupRepo.DeleteByParticipantAndGroupID(ctx, participantGroup.ParticipantID, participantGroup.GroupID)
		if err != nil {
			return err
		}
	}

	for _, participantGroup := range toAddParticipantGroups {
		_, err = s.participantGroupRepo.Create(ctx, participantGroup.ParticipantID, participantGroup.GroupID)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *userDomainService) UpdateLinksWithProducts(ctx context.Context, userID int64, productIDs []int64) error {
	participants, err := s.participantRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	if len(toDelete) > 0 {
		err := s.participantRepo.DeleteByUserIDAndProductIDs(ctx, userID, toDelete)
		if err != nil {
			return err
		}
	}

	if len(toAdd) > 0 {
		err := s.participantRepo.CreateByUserIDAndProductIDs(ctx, userID, toAdd)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *userDomainService) UpdateLinksWithRoles(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) error {
	rolesWithoutProduct, rolesWithProduct := separateRolesByProduct(roles)

	existingUserRoles, err := s.userRoleRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	toDeleteUserRoles, toAddUserRoles := diffUserRoleIDsForUser(existingUserRoles, rolesWithoutProduct)

	if len(toDeleteUserRoles) > 0 {
		err = s.userRoleRepo.DeleteByUserIDAndRoleIDs(ctx, userID, toDeleteUserRoles)
		if err != nil {
			return err
		}
	}

	if len(toAddUserRoles) > 0 {
		err = s.userRoleRepo.CreateByUserIDAndRoleIDs(ctx, userID, toAddUserRoles)
		if err != nil {
			return err
		}
	}

	participants, err := s.participantRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	participantIDs := make([]int64, 0, len(participants))
	for _, participant := range participants {
		participantIDs = append(participantIDs, participant.ID)
	}

	existingParticipantRoles, err := s.participantRoleRepo.GetByParticipantIDs(participantIDs)
	if err != nil {
		return err
	}

	expectedParticipantRoles := buildExpectedParticipantRoles(participants, rolesWithProduct)

	toDeleteParticipantRoles, toAddParticipantRoles := diffParticipantRolesForUser(existingParticipantRoles, expectedParticipantRoles)

	for _, participantRole := range toDeleteParticipantRoles {
		err = s.participantRoleRepo.DeleteByParticipantAndRoleID(ctx, participantRole.ParticipantID, participantRole.RoleID)
		if err != nil {
			return err
		}
	}

	for _, participantRole := range toAddParticipantRoles {
		_, err = s.participantRoleRepo.Create(ctx, participantRole.ParticipantID, participantRole.RoleID)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *userDomainService) DeleteAsAdmin(ctx context.Context, userID int64) error {
	flagFalse := false
	_, err := s.userRepo.Update(userentity.UserUpdateData{
		ID:        userID,
		ActiveFlg: &flagFalse,
	})
	return err
}

func (s *userDomainService) DeleteByUserID(ctx context.Context, userID int64) error {
	flagFalse := false
	_, err := s.userRepo.Update(userentity.UserUpdateData{
		ID:        userID,
		ActiveFlg: &flagFalse,
	})
	return err
}

func (s *userDomainService) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error {
	return s.userGroupRepo.DeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
}

func (s *userDomainService) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error {
	return s.userRoleRepo.DeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
}

func (s *userDomainService) GetAdmins() ([]userentity.User, error) {
	return s.userRepo.GetAdmins()
}

func buildExpectedParticipantGroups(participants []participantentity.Participant, groupsWithProduct []groupentity.GroupProductLink) []participantentity.ParticipantGroup {
	expectedParticipantGroups := make([]participantentity.ParticipantGroup, 0)

	for _, participant := range participants {
		for _, group := range groupsWithProduct {
			if group.ProductID != nil && participant.ProductID == *group.ProductID {
				expectedParticipantGroups = append(expectedParticipantGroups, participantentity.ParticipantGroup{
					ParticipantID: participant.ID,
					GroupID:       group.GroupID,
				})
			}
		}
	}

	return expectedParticipantGroups
}

func buildExpectedParticipantRoles(participants []participantentity.Participant, rolesWithProduct []roleentity.RoleProductLink) []participantentity.ParticipantRole {
	expectedParticipantRoles := make([]participantentity.ParticipantRole, 0)

	for _, participant := range participants {
		for _, role := range rolesWithProduct {
			if role.ProductID != nil && participant.ProductID == *role.ProductID {
				expectedParticipantRoles = append(expectedParticipantRoles, participantentity.ParticipantRole{
					ParticipantID: participant.ID,
					RoleID:        role.RoleID,
				})
			}
		}
	}

	return expectedParticipantRoles
}

func diffParticipantGroupsForUser(existingParticipantGroups []participantentity.ParticipantGroup, expectedParticipantGroups []participantentity.ParticipantGroup) ([]participantentity.ParticipantGroup, []participantentity.ParticipantGroup) {
	existingGroupMap := make(map[string]bool)
	for _, group := range existingParticipantGroups {
		key := fmt.Sprintf("%d_%d", group.ParticipantID, group.GroupID)
		existingGroupMap[key] = true
	}

	expectedGroupMap := make(map[string]bool)
	for _, group := range expectedParticipantGroups {
		key := fmt.Sprintf("%d_%d", group.ParticipantID, group.GroupID)
		expectedGroupMap[key] = true
	}

	toDeleteParticipantGroups := make([]participantentity.ParticipantGroup, 0)
	toAddParticipantGroups := make([]participantentity.ParticipantGroup, 0)

	for _, group := range existingParticipantGroups {
		key := fmt.Sprintf("%d_%d", group.ParticipantID, group.GroupID)
		if !expectedGroupMap[key] {
			toDeleteParticipantGroups = append(toDeleteParticipantGroups, group)
		}
	}

	for _, group := range expectedParticipantGroups {
		key := fmt.Sprintf("%d_%d", group.ParticipantID, group.GroupID)
		if !existingGroupMap[key] {
			toAddParticipantGroups = append(toAddParticipantGroups, group)
		}
	}

	return toDeleteParticipantGroups, toAddParticipantGroups
}

func diffParticipantRolesForUser(existingParticipantRoles []participantentity.ParticipantRole, expectedParticipantRoles []participantentity.ParticipantRole) ([]participantentity.ParticipantRole, []participantentity.ParticipantRole) {
	existingRoleMap := make(map[string]bool)
	for _, role := range existingParticipantRoles {
		key := fmt.Sprintf("%d_%d", role.ParticipantID, role.RoleID)
		existingRoleMap[key] = true
	}

	expectedRoleMap := make(map[string]bool)
	for _, role := range expectedParticipantRoles {
		key := fmt.Sprintf("%d_%d", role.ParticipantID, role.RoleID)
		expectedRoleMap[key] = true
	}

	toDeleteParticipantRoles := make([]participantentity.ParticipantRole, 0)
	toAddParticipantRoles := make([]participantentity.ParticipantRole, 0)

	for _, role := range existingParticipantRoles {
		key := fmt.Sprintf("%d_%d", role.ParticipantID, role.RoleID)
		if !expectedRoleMap[key] {
			toDeleteParticipantRoles = append(toDeleteParticipantRoles, role)
		}
	}

	for _, role := range expectedParticipantRoles {
		key := fmt.Sprintf("%d_%d", role.ParticipantID, role.RoleID)
		if !existingRoleMap[key] {
			toAddParticipantRoles = append(toAddParticipantRoles, role)
		}
	}

	return toDeleteParticipantRoles, toAddParticipantRoles
}

func diffUserGroupIDsForUser(existingUserGroups []userentity.UserGroup, groupsWithoutProduct []groupentity.GroupProductLink) ([]int64, []int64) {
	existingGroupIDs := make(map[int64]bool)
	for _, userGroup := range existingUserGroups {
		existingGroupIDs[userGroup.GroupID] = true
	}

	newGroupIDs := make(map[int64]bool)
	for _, group := range groupsWithoutProduct {
		newGroupIDs[group.GroupID] = true
	}

	toDeleteUserGroups := make([]int64, 0)
	toAddUserGroups := make([]int64, 0)

	for groupID := range existingGroupIDs {
		if !newGroupIDs[groupID] {
			toDeleteUserGroups = append(toDeleteUserGroups, groupID)
		}
	}

	for _, group := range groupsWithoutProduct {
		if !existingGroupIDs[group.GroupID] {
			toAddUserGroups = append(toAddUserGroups, group.GroupID)
		}
	}

	return toDeleteUserGroups, toAddUserGroups
}

func diffUserIDsForProducts(participants []participantentity.Participant, productIDs []int64) (toDelete []int64, toAdd []int64) {
	toDelete = make([]int64, 0)
	toAdd = make([]int64, 0)

	if len(participants) == 0 && len(productIDs) == 0 {
		return toDelete, toAdd
	}

	existingProductIDs := make(map[int64]bool)
	for _, participant := range participants {
		existingProductIDs[participant.ProductID] = true
	}

	newProductIDs := make(map[int64]bool)
	for _, productID := range productIDs {
		newProductIDs[productID] = true
	}

	for productID := range existingProductIDs {
		if !newProductIDs[productID] {
			toDelete = append(toDelete, productID)
		}
	}

	for _, productID := range productIDs {
		if !existingProductIDs[productID] {
			toAdd = append(toAdd, productID)
		}
	}

	return toDelete, toAdd
}

func diffUserRoleIDsForUser(existingUserRoles []userentity.UserRole, rolesWithoutProduct []roleentity.RoleProductLink) ([]int64, []int64) {
	existingRoleIDs := make(map[int64]bool)
	for _, userRole := range existingUserRoles {
		existingRoleIDs[userRole.RoleID] = true
	}

	newRoleIDs := make(map[int64]bool)
	for _, role := range rolesWithoutProduct {
		newRoleIDs[role.RoleID] = true
	}

	toDeleteUserRoles := make([]int64, 0)
	toAddUserRoles := make([]int64, 0)

	for roleID := range existingRoleIDs {
		if !newRoleIDs[roleID] {
			toDeleteUserRoles = append(toDeleteUserRoles, roleID)
		}
	}

	for _, role := range rolesWithoutProduct {
		if !existingRoleIDs[role.RoleID] {
			toAddUserRoles = append(toAddUserRoles, role.RoleID)
		}
	}

	return toDeleteUserRoles, toAddUserRoles
}

func separateGroupsByProduct(groups []groupentity.GroupProductLink) ([]groupentity.GroupProductLink, []groupentity.GroupProductLink) {
	groupsWithoutProduct := make([]groupentity.GroupProductLink, 0)
	groupsWithProduct := make([]groupentity.GroupProductLink, 0)

	for _, group := range groups {
		if group.ProductID == nil {
			groupsWithoutProduct = append(groupsWithoutProduct, group)
		} else {
			groupsWithProduct = append(groupsWithProduct, group)
		}
	}

	return groupsWithoutProduct, groupsWithProduct
}

func separateRolesByProduct(roles []roleentity.RoleProductLink) ([]roleentity.RoleProductLink, []roleentity.RoleProductLink) {
	rolesWithoutProduct := make([]roleentity.RoleProductLink, 0)
	rolesWithProduct := make([]roleentity.RoleProductLink, 0)

	for _, role := range roles {
		if role.ProductID == nil {
			rolesWithoutProduct = append(rolesWithoutProduct, role)
		} else {
			rolesWithProduct = append(rolesWithProduct, role)
		}
	}

	return rolesWithoutProduct, rolesWithProduct
}
