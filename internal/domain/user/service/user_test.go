package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	identityprovidermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/mocks"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func TestSeparateRolesByProduct(t *testing.T) {
	productID1 := int64(10)
	productID2 := int64(20)

	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
		{RoleID: 2, ProductID: &productID1},
		{RoleID: 3, ProductID: nil},
		{RoleID: 4, ProductID: &productID2},
	}

	rolesWithoutProduct, rolesWithProduct := separateRolesByProduct(roles)

	require.Len(t, rolesWithoutProduct, 2)
	require.Len(t, rolesWithProduct, 2)

	require.Equal(t, int64(1), rolesWithoutProduct[0].RoleID)
	require.Nil(t, rolesWithoutProduct[0].ProductID)
	require.Equal(t, int64(3), rolesWithoutProduct[1].RoleID)
	require.Nil(t, rolesWithoutProduct[1].ProductID)

	require.Equal(t, int64(2), rolesWithProduct[0].RoleID)
	require.Equal(t, productID1, *rolesWithProduct[0].ProductID)
	require.Equal(t, int64(4), rolesWithProduct[1].RoleID)
	require.Equal(t, productID2, *rolesWithProduct[1].ProductID)
}

func TestDiffUserRoleIDsForUser(t *testing.T) {
	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, UserID: 1, RoleID: 2, CreatedAt: time.Now()},
		{ID: 3, UserID: 1, RoleID: 3, CreatedAt: time.Now()},
	}

	rolesWithoutProduct := []roleentity.RoleProductLink{
		{RoleID: 2, ProductID: nil}, // существующая роль
		{RoleID: 4, ProductID: nil}, // новая роль
	}

	toDelete, toAdd := diffUserRoleIDsForUser(existingUserRoles, rolesWithoutProduct)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, int64(1))
	require.Contains(t, toDelete, int64(3))

	require.Len(t, toAdd, 1)
	require.Contains(t, toAdd, int64(4))
}

func TestBuildExpectedParticipantRoles(t *testing.T) {
	productID1 := int64(10)
	productID2 := int64(20)
	productID3 := int64(30)

	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: productID2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	rolesWithProduct := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID1}, // участник 1
		{RoleID: 2, ProductID: &productID2}, // участник 2
		{RoleID: 3, ProductID: &productID3}, // нет участника
		{RoleID: 4, ProductID: &productID1}, // участник 1
	}

	expected := buildExpectedParticipantRoles(participants, rolesWithProduct)

	require.Len(t, expected, 3)

	require.Equal(t, int64(1), expected[0].ParticipantID)
	require.Equal(t, int64(1), expected[0].RoleID)

	require.Equal(t, int64(1), expected[1].ParticipantID)
	require.Equal(t, int64(4), expected[1].RoleID)

	require.Equal(t, int64(2), expected[2].ParticipantID)
	require.Equal(t, int64(2), expected[2].RoleID)
}

func TestDiffParticipantRolesForUser(t *testing.T) {
	existingParticipantRoles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 1, RoleID: 2, CreatedAt: time.Now()},
		{ID: 3, ParticipantID: 2, RoleID: 1, CreatedAt: time.Now()},
	}

	expectedParticipantRoles := []participantentity.ParticipantRole{
		{ParticipantID: 1, RoleID: 2}, // существующая роль
		{ParticipantID: 1, RoleID: 3}, // новая роль
		{ParticipantID: 3, RoleID: 1}, // новая роль
	}

	toDelete, toAdd := diffParticipantRolesForUser(existingParticipantRoles, expectedParticipantRoles)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, participantentity.ParticipantRole{ID: 1, ParticipantID: 1, RoleID: 1, CreatedAt: existingParticipantRoles[0].CreatedAt})
	require.Contains(t, toDelete, participantentity.ParticipantRole{ID: 3, ParticipantID: 2, RoleID: 1, CreatedAt: existingParticipantRoles[2].CreatedAt})

	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, participantentity.ParticipantRole{ParticipantID: 1, RoleID: 3})
	require.Contains(t, toAdd, participantentity.ParticipantRole{ParticipantID: 3, RoleID: 1})
}

func TestSeparateRolesByProduct_EmptySlice(t *testing.T) {
	var roles []roleentity.RoleProductLink

	rolesWithoutProduct, rolesWithProduct := separateRolesByProduct(roles)

	require.Len(t, rolesWithoutProduct, 0)
	require.Len(t, rolesWithProduct, 0)
}

func TestDiffUserRoleIDsForUser_EmptyExisting(t *testing.T) {
	var existingUserRoles []userentity.UserRole
	rolesWithoutProduct := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
		{RoleID: 2, ProductID: nil},
	}

	toDelete, toAdd := diffUserRoleIDsForUser(existingUserRoles, rolesWithoutProduct)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, int64(1))
	require.Contains(t, toAdd, int64(2))
}

func TestDiffUserRoleIDsForUser_EmptyNew(t *testing.T) {
	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, UserID: 1, RoleID: 2, CreatedAt: time.Now()},
	}
	var rolesWithoutProduct []roleentity.RoleProductLink

	toDelete, toAdd := diffUserRoleIDsForUser(existingUserRoles, rolesWithoutProduct)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, int64(1))
	require.Contains(t, toDelete, int64(2))
	require.Len(t, toAdd, 0)
}

func TestBuildExpectedParticipantRoles_EmptyParticipants(t *testing.T) {
	productID := int64(10)
	var participants []participantentity.Participant
	rolesWithProduct := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID},
	}

	expected := buildExpectedParticipantRoles(participants, rolesWithProduct)

	require.Len(t, expected, 0)
}

func TestBuildExpectedParticipantRoles_EmptyRoles(t *testing.T) {
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	var rolesWithProduct []roleentity.RoleProductLink

	expected := buildExpectedParticipantRoles(participants, rolesWithProduct)

	require.Len(t, expected, 0)
}

func TestDiffParticipantRolesForUser_EmptyExisting(t *testing.T) {
	var existingParticipantRoles []participantentity.ParticipantRole
	expectedParticipantRoles := []participantentity.ParticipantRole{
		{ParticipantID: 1, RoleID: 1},
		{ParticipantID: 2, RoleID: 2},
	}

	toDelete, toAdd := diffParticipantRolesForUser(existingParticipantRoles, expectedParticipantRoles)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, participantentity.ParticipantRole{ParticipantID: 1, RoleID: 1})
	require.Contains(t, toAdd, participantentity.ParticipantRole{ParticipantID: 2, RoleID: 2})
}

func TestDiffParticipantRolesForUser_EmptyExpected(t *testing.T) {
	existingParticipantRoles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 2, RoleID: 2, CreatedAt: time.Now()},
	}
	var expectedParticipantRoles []participantentity.ParticipantRole

	toDelete, toAdd := diffParticipantRolesForUser(existingParticipantRoles, expectedParticipantRoles)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, existingParticipantRoles[0])
	require.Contains(t, toDelete, existingParticipantRoles[1])
	require.Len(t, toAdd, 0)
}

func TestDiffUserRoleIDsForUser_SameRoles(t *testing.T) {
	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, UserID: 1, RoleID: 2, CreatedAt: time.Now()},
	}

	rolesWithoutProduct := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
		{RoleID: 2, ProductID: nil},
	}

	toDelete, toAdd := diffUserRoleIDsForUser(existingUserRoles, rolesWithoutProduct)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 0)
}

func TestDiffParticipantRolesForUser_SameRoles(t *testing.T) {
	existingParticipantRoles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 2, RoleID: 2, CreatedAt: time.Now()},
	}

	expectedParticipantRoles := []participantentity.ParticipantRole{
		{ParticipantID: 1, RoleID: 1},
		{ParticipantID: 2, RoleID: 2},
	}

	toDelete, toAdd := diffParticipantRolesForUser(existingParticipantRoles, expectedParticipantRoles)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 0)
}

func TestBuildExpectedParticipantRoles_NoMatchingProducts(t *testing.T) {
	productID1 := int64(10)
	productID2 := int64(20)
	productID3 := int64(30)

	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: productID2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	rolesWithProduct := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID3}, // нет участника с таким продуктом
	}

	expected := buildExpectedParticipantRoles(participants, rolesWithProduct)

	require.Len(t, expected, 0)
}

func TestUpdateLinksWithRoles_Success(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID1 := int64(10)
	productID2 := int64(20)

	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},         // роль без продукта
		{RoleID: 2, ProductID: &productID1}, // роль с продуктом 1
		{RoleID: 3, ProductID: &productID2}, // роль с продуктом 2
	}

	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: userID, RoleID: 4, CreatedAt: time.Now()}, // роль для удаления
	}

	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: userID, ProductID: productID2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	existingParticipantRoles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 5, CreatedAt: time.Now()}, // роль для удаления
	}

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockUserRoleRepo.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{4}).Return(nil)
	mockUserRoleRepo.CreateByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{1}).Return(nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{1, 2}).Return(existingParticipantRoles, nil)
	mockParticipantRoleRepo.DeleteByParticipantAndRoleIDMock.Expect(ctx, int64(1), int64(5)).Return(nil)
	mockParticipantRoleRepo.CreateMock.When(ctx, int64(1), int64(2)).Then(participantentity.ParticipantRole{}, nil)
	mockParticipantRoleRepo.CreateMock.When(ctx, int64(2), int64(3)).Then(participantentity.ParticipantRole{}, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.NoError(t, err)
}

func TestUpdateLinksWithRoles_OnlyUserRoles(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)

	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil}, // только роли без продукта
		{RoleID: 2, ProductID: nil},
	}

	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: userID, RoleID: 3, CreatedAt: time.Now()}, // роль для удаления
	}

	var participants []participantentity.Participant
	var existingParticipantRoles []participantentity.ParticipantRole

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockUserRoleRepo.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{3}).Return(nil)
	mockUserRoleRepo.CreateByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{1, 2}).Return(nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{}).Return(existingParticipantRoles, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.NoError(t, err)
}

func TestUpdateLinksWithRoles_OnlyParticipantRoles(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID1 := int64(10)

	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID1}, // только роли с продуктом
	}

	var existingUserRoles []userentity.UserRole

	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	var existingParticipantRoles []participantentity.ParticipantRole

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantRoles, nil)
	mockParticipantRoleRepo.CreateMock.Expect(ctx, int64(1), int64(1)).Return(participantentity.ParticipantRole{}, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.NoError(t, err)
}

func TestUpdateLinksWithRoles_EmptyRoles(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	var roles []roleentity.RoleProductLink // пустой список ролей

	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: userID, RoleID: 1, CreatedAt: time.Now()}, // роль для удаления
	}

	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	existingParticipantRoles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 2, CreatedAt: time.Now()}, // роль для удаления
	}

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockUserRoleRepo.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{1}).Return(nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantRoles, nil)
	mockParticipantRoleRepo.DeleteByParticipantAndRoleIDMock.Expect(ctx, int64(1), int64(2)).Return(nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.NoError(t, err)
}

func TestUpdateLinksWithRoles_UserRoleRepoGetError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
	}

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(nil, errors.New("database error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(context.Background(), userID, roles)
	require.Error(t, err)
	require.Equal(t, "database error", err.Error())
}

func TestUpdateLinksWithRoles_UserRoleRepoDeleteError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
	}

	existingUserRoles := []userentity.UserRole{
		{ID: 1, UserID: userID, RoleID: 2, CreatedAt: time.Now()},
	}

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockUserRoleRepo.DeleteByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{2}).Return(errors.New("delete error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.Error(t, err)
	require.Equal(t, "delete error", err.Error())
}

func TestUpdateLinksWithRoles_UserRoleRepoCreateError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: nil},
	}

	var existingUserRoles []userentity.UserRole

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockUserRoleRepo.CreateByUserIDAndRoleIDsMock.Expect(ctx, userID, []int64{1}).Return(errors.New("create error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.Error(t, err)
	require.Equal(t, "create error", err.Error())
}

func TestUpdateLinksWithRoles_ParticipantRepoError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID},
	}

	var existingUserRoles []userentity.UserRole

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(nil, errors.New("participant error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(context.Background(), userID, roles)
	require.Error(t, err)
	require.Equal(t, "participant error", err.Error())
}

func TestUpdateLinksWithRoles_ParticipantRoleRepoGetError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID},
	}

	var existingUserRoles []userentity.UserRole
	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(nil, errors.New("participant role error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(context.Background(), userID, roles)
	require.Error(t, err)
	require.Equal(t, "participant role error", err.Error())
}

func TestUpdateLinksWithRoles_ParticipantRoleRepoDeleteError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID},
	}

	var existingUserRoles []userentity.UserRole
	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	existingParticipantRoles := []participantentity.ParticipantRole{
		{ID: 1, ParticipantID: 1, RoleID: 2, CreatedAt: time.Now()},
	}

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantRoles, nil)
	mockParticipantRoleRepo.DeleteByParticipantAndRoleIDMock.Expect(ctx, int64(1), int64(2)).Return(errors.New("delete participant role error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.Error(t, err)
	require.Equal(t, "delete participant role error", err.Error())
}

func TestUpdateLinksWithRoles_ParticipantRoleRepoCreateError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	roles := []roleentity.RoleProductLink{
		{RoleID: 1, ProductID: &productID},
	}

	var existingUserRoles []userentity.UserRole
	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	var existingParticipantRoles []participantentity.ParticipantRole

	ctx := context.Background()

	mockUserRoleRepo.GetByUserIDMock.Expect(userID).Return(existingUserRoles, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantRoleRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantRoles, nil)
	mockParticipantRoleRepo.CreateMock.Expect(ctx, int64(1), int64(1)).Return(participantentity.ParticipantRole{}, errors.New("create participant role error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithRoles(ctx, userID, roles)
	require.Error(t, err)
	require.Equal(t, "create participant role error", err.Error())
}

func TestSeparateGroupsByProduct(t *testing.T) {
	productID1 := int64(10)
	productID2 := int64(20)

	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},
		{GroupID: 2, ProductID: &productID1},
		{GroupID: 3, ProductID: nil},
		{GroupID: 4, ProductID: &productID2},
	}

	groupsWithoutProduct, groupsWithProduct := separateGroupsByProduct(groups)

	require.Len(t, groupsWithoutProduct, 2)
	require.Len(t, groupsWithProduct, 2)

	require.Equal(t, int64(1), groupsWithoutProduct[0].GroupID)
	require.Nil(t, groupsWithoutProduct[0].ProductID)
	require.Equal(t, int64(3), groupsWithoutProduct[1].GroupID)
	require.Nil(t, groupsWithoutProduct[1].ProductID)

	require.Equal(t, int64(2), groupsWithProduct[0].GroupID)
	require.Equal(t, productID1, *groupsWithProduct[0].ProductID)
	require.Equal(t, int64(4), groupsWithProduct[1].GroupID)
	require.Equal(t, productID2, *groupsWithProduct[1].ProductID)
}

func TestDiffUserGroupIDsForUser(t *testing.T) {
	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, UserID: 1, GroupID: 2, CreatedAt: time.Now()},
		{ID: 3, UserID: 1, GroupID: 3, CreatedAt: time.Now()},
	}

	groupsWithoutProduct := []groupentity.GroupProductLink{
		{GroupID: 2, ProductID: nil}, // существующая группа
		{GroupID: 4, ProductID: nil}, // новая группа
	}

	toDelete, toAdd := diffUserGroupIDsForUser(existingUserGroups, groupsWithoutProduct)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, int64(1))
	require.Contains(t, toDelete, int64(3))

	require.Len(t, toAdd, 1)
	require.Contains(t, toAdd, int64(4))
}

func TestBuildExpectedParticipantGroups(t *testing.T) {
	productID1 := int64(10)
	productID2 := int64(20)
	productID3 := int64(30)

	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: productID2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	groupsWithProduct := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID1}, // участник 1
		{GroupID: 2, ProductID: &productID2}, // участник 2
		{GroupID: 3, ProductID: &productID3}, // нет участника
		{GroupID: 4, ProductID: &productID1}, // участник 1
	}

	expected := buildExpectedParticipantGroups(participants, groupsWithProduct)

	require.Len(t, expected, 3)

	require.Equal(t, int64(1), expected[0].ParticipantID)
	require.Equal(t, int64(1), expected[0].GroupID)

	require.Equal(t, int64(1), expected[1].ParticipantID)
	require.Equal(t, int64(4), expected[1].GroupID)

	require.Equal(t, int64(2), expected[2].ParticipantID)
	require.Equal(t, int64(2), expected[2].GroupID)
}

func TestDiffParticipantGroupsForUser(t *testing.T) {
	existingParticipantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 1, GroupID: 2, CreatedAt: time.Now()},
		{ID: 3, ParticipantID: 2, GroupID: 1, CreatedAt: time.Now()},
	}

	expectedParticipantGroups := []participantentity.ParticipantGroup{
		{ParticipantID: 1, GroupID: 2}, // существующая группа
		{ParticipantID: 1, GroupID: 3}, // новая группа
		{ParticipantID: 3, GroupID: 1}, // новая группа
	}

	toDelete, toAdd := diffParticipantGroupsForUser(existingParticipantGroups, expectedParticipantGroups)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, participantentity.ParticipantGroup{ID: 1, ParticipantID: 1, GroupID: 1, CreatedAt: existingParticipantGroups[0].CreatedAt})
	require.Contains(t, toDelete, participantentity.ParticipantGroup{ID: 3, ParticipantID: 2, GroupID: 1, CreatedAt: existingParticipantGroups[2].CreatedAt})

	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, participantentity.ParticipantGroup{ParticipantID: 1, GroupID: 3})
	require.Contains(t, toAdd, participantentity.ParticipantGroup{ParticipantID: 3, GroupID: 1})
}

func TestSeparateGroupsByProduct_EmptySlice(t *testing.T) {
	var groups []groupentity.GroupProductLink

	groupsWithoutProduct, groupsWithProduct := separateGroupsByProduct(groups)

	require.Len(t, groupsWithoutProduct, 0)
	require.Len(t, groupsWithProduct, 0)
}

func TestDiffUserGroupIDsForUser_EmptyExisting(t *testing.T) {
	var existingUserGroups []userentity.UserGroup
	groupsWithoutProduct := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},
		{GroupID: 2, ProductID: nil},
	}

	toDelete, toAdd := diffUserGroupIDsForUser(existingUserGroups, groupsWithoutProduct)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, int64(1))
	require.Contains(t, toAdd, int64(2))
}

func TestDiffUserGroupIDsForUser_EmptyNew(t *testing.T) {
	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, UserID: 1, GroupID: 2, CreatedAt: time.Now()},
	}
	var groupsWithoutProduct []groupentity.GroupProductLink

	toDelete, toAdd := diffUserGroupIDsForUser(existingUserGroups, groupsWithoutProduct)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, int64(1))
	require.Contains(t, toDelete, int64(2))
	require.Len(t, toAdd, 0)
}

func TestBuildExpectedParticipantGroups_EmptyParticipants(t *testing.T) {
	productID := int64(10)
	var participants []participantentity.Participant
	groupsWithProduct := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID},
	}

	expected := buildExpectedParticipantGroups(participants, groupsWithProduct)

	require.Len(t, expected, 0)
}

func TestBuildExpectedParticipantGroups_EmptyGroups(t *testing.T) {
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	var groupsWithProduct []groupentity.GroupProductLink

	expected := buildExpectedParticipantGroups(participants, groupsWithProduct)

	require.Len(t, expected, 0)
}

func TestDiffParticipantGroupsForUser_EmptyExisting(t *testing.T) {
	var existingParticipantGroups []participantentity.ParticipantGroup
	expectedParticipantGroups := []participantentity.ParticipantGroup{
		{ParticipantID: 1, GroupID: 1},
		{ParticipantID: 2, GroupID: 2},
	}

	toDelete, toAdd := diffParticipantGroupsForUser(existingParticipantGroups, expectedParticipantGroups)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, participantentity.ParticipantGroup{ParticipantID: 1, GroupID: 1})
	require.Contains(t, toAdd, participantentity.ParticipantGroup{ParticipantID: 2, GroupID: 2})
}

func TestDiffParticipantGroupsForUser_EmptyExpected(t *testing.T) {
	existingParticipantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 2, GroupID: 2, CreatedAt: time.Now()},
	}
	var expectedParticipantGroups []participantentity.ParticipantGroup

	toDelete, toAdd := diffParticipantGroupsForUser(existingParticipantGroups, expectedParticipantGroups)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, existingParticipantGroups[0])
	require.Contains(t, toDelete, existingParticipantGroups[1])
	require.Len(t, toAdd, 0)
}

func TestDiffUserGroupIDsForUser_SameGroups(t *testing.T) {
	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, UserID: 1, GroupID: 2, CreatedAt: time.Now()},
	}

	groupsWithoutProduct := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},
		{GroupID: 2, ProductID: nil},
	}

	toDelete, toAdd := diffUserGroupIDsForUser(existingUserGroups, groupsWithoutProduct)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 0)
}

func TestDiffParticipantGroupsForUser_SameGroups(t *testing.T) {
	existingParticipantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 1, CreatedAt: time.Now()},
		{ID: 2, ParticipantID: 2, GroupID: 2, CreatedAt: time.Now()},
	}

	expectedParticipantGroups := []participantentity.ParticipantGroup{
		{ParticipantID: 1, GroupID: 1},
		{ParticipantID: 2, GroupID: 2},
	}

	toDelete, toAdd := diffParticipantGroupsForUser(existingParticipantGroups, expectedParticipantGroups)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 0)
}

func TestBuildExpectedParticipantGroups_NoMatchingProducts(t *testing.T) {
	productID1 := int64(10)
	productID2 := int64(20)
	productID3 := int64(30)

	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: productID2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	groupsWithProduct := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID3}, // нет участника с таким продуктом
	}

	expected := buildExpectedParticipantGroups(participants, groupsWithProduct)

	require.Len(t, expected, 0)
}

func TestUpdateLinksWithGroups_Success(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID1 := int64(10)
	productID2 := int64(20)

	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},         // группа без продукта
		{GroupID: 2, ProductID: &productID1}, // группа с продуктом 1
		{GroupID: 3, ProductID: &productID2}, // группа с продуктом 2
	}

	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: userID, GroupID: 4, CreatedAt: time.Now()}, // группа для удаления
	}

	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: userID, ProductID: productID2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	existingParticipantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 5, CreatedAt: time.Now()}, // группа для удаления
	}

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockUserGroupRepo.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, []int64{4}).Return(nil)
	mockUserGroupRepo.AssignUserToGroupsMock.Expect(userID, []int64{1}).Return(nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{1, 2}).Return(existingParticipantGroups, nil)
	mockParticipantGroupRepo.DeleteByParticipantAndGroupIDMock.Expect(ctx, int64(1), int64(5)).Return(nil)
	mockParticipantGroupRepo.CreateMock.When(ctx, int64(1), int64(2)).Then(participantentity.ParticipantGroup{}, nil)
	mockParticipantGroupRepo.CreateMock.When(ctx, int64(2), int64(3)).Then(participantentity.ParticipantGroup{}, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.NoError(t, err)
}

func TestUpdateLinksWithGroups_OnlyUserGroups(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)

	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil}, // только группы без продукта
		{GroupID: 2, ProductID: nil},
	}

	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: userID, GroupID: 3, CreatedAt: time.Now()}, // группа для удаления
	}

	var participants []participantentity.Participant
	var existingParticipantGroups []participantentity.ParticipantGroup

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockUserGroupRepo.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, []int64{3}).Return(nil)
	mockUserGroupRepo.AssignUserToGroupsMock.Expect(userID, []int64{1, 2}).Return(nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{}).Return(existingParticipantGroups, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.NoError(t, err)
}

func TestUpdateLinksWithGroups_OnlyParticipantGroups(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID1 := int64(10)

	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID1}, // только группы с продуктом
	}

	var existingUserGroups []userentity.UserGroup

	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	var existingParticipantGroups []participantentity.ParticipantGroup

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantGroups, nil)
	mockParticipantGroupRepo.CreateMock.Expect(ctx, int64(1), int64(1)).Return(participantentity.ParticipantGroup{}, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.NoError(t, err)
}

func TestUpdateLinksWithGroups_EmptyGroups(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	var groups []groupentity.GroupProductLink // пустой список групп

	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: userID, GroupID: 1, CreatedAt: time.Now()}, // группа для удаления
	}

	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	existingParticipantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 2, CreatedAt: time.Now()}, // группа для удаления
	}

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockUserGroupRepo.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, []int64{1}).Return(nil)

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantGroups, nil)
	mockParticipantGroupRepo.DeleteByParticipantAndGroupIDMock.Expect(ctx, int64(1), int64(2)).Return(nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.NoError(t, err)
}

func TestUpdateLinksWithGroups_UserGroupRepoGetError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},
	}

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(nil, errors.New("database error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(context.Background(), userID, groups)
	require.Error(t, err)
	require.Equal(t, "database error", err.Error())
}

func TestUpdateLinksWithGroups_UserGroupRepoDeleteError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},
	}

	existingUserGroups := []userentity.UserGroup{
		{ID: 1, UserID: userID, GroupID: 2, CreatedAt: time.Now()},
	}

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockUserGroupRepo.DeleteByUserIDAndGroupIDsMock.Expect(ctx, userID, []int64{2}).Return(errors.New("delete error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.Error(t, err)
	require.Equal(t, "delete error", err.Error())
}

func TestUpdateLinksWithGroups_UserGroupRepoAssignError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: nil},
	}

	var existingUserGroups []userentity.UserGroup

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockUserGroupRepo.AssignUserToGroupsMock.Expect(userID, []int64{1}).Return(errors.New("assign error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(context.Background(), userID, groups)
	require.Error(t, err)
	require.Equal(t, "assign error", err.Error())
}

func TestUpdateLinksWithGroups_ParticipantRepoError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID},
	}

	var existingUserGroups []userentity.UserGroup

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(nil, errors.New("participant error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(context.Background(), userID, groups)
	require.Error(t, err)
	require.Equal(t, "participant error", err.Error())
}

func TestUpdateLinksWithGroups_ParticipantGroupRepoGetError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID},
	}

	var existingUserGroups []userentity.UserGroup
	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(nil, errors.New("participant group error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(context.Background(), userID, groups)
	require.Error(t, err)
	require.Equal(t, "participant group error", err.Error())
}

func TestUpdateLinksWithGroups_ParticipantGroupRepoDeleteError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID},
	}

	var existingUserGroups []userentity.UserGroup
	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	existingParticipantGroups := []participantentity.ParticipantGroup{
		{ID: 1, ParticipantID: 1, GroupID: 2, CreatedAt: time.Now()},
	}

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantGroups, nil)
	mockParticipantGroupRepo.DeleteByParticipantAndGroupIDMock.Expect(ctx, int64(1), int64(2)).Return(errors.New("delete participant group error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.Error(t, err)
	require.Equal(t, "delete participant group error", err.Error())
}

func TestUpdateLinksWithGroups_ParticipantGroupRepoCreateError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productID := int64(10)
	groups := []groupentity.GroupProductLink{
		{GroupID: 1, ProductID: &productID},
	}

	var existingUserGroups []userentity.UserGroup
	participants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: productID, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	var existingParticipantGroups []participantentity.ParticipantGroup

	ctx := context.Background()

	mockUserGroupRepo.GetByUserIDMock.Expect(userID).Return(existingUserGroups, nil)
	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(participants, nil)
	mockParticipantGroupRepo.GetByParticipantIDsMock.Expect([]int64{1}).Return(existingParticipantGroups, nil)
	mockParticipantGroupRepo.CreateMock.Expect(ctx, int64(1), int64(1)).Return(participantentity.ParticipantGroup{}, errors.New("create participant group error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithGroups(ctx, userID, groups)
	require.Error(t, err)
	require.Equal(t, "create participant group error", err.Error())
}

func TestDiffUserIDsForProducts(t *testing.T) {
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 3, UserID: 1, ProductID: 30, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	productIDs := []int64{20, 40} // оставляем 20, удаляем 10 и 30, добавляем 40

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, int64(10))
	require.Contains(t, toDelete, int64(30))

	require.Len(t, toAdd, 1)
	require.Contains(t, toAdd, int64(40))
}

func TestDiffUserIDsForProducts_EmptyParticipants(t *testing.T) {
	var participants []participantentity.Participant
	productIDs := []int64{10, 20}

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 2)
	require.Contains(t, toAdd, int64(10))
	require.Contains(t, toAdd, int64(20))
}

func TestDiffUserIDsForProducts_EmptyProductIDs(t *testing.T) {
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	var productIDs []int64

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	require.Len(t, toDelete, 2)
	require.Contains(t, toDelete, int64(10))
	require.Contains(t, toDelete, int64(20))
	require.Len(t, toAdd, 0)
}

func TestDiffUserIDsForProducts_BothEmpty(t *testing.T) {
	var participants []participantentity.Participant
	var productIDs []int64

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 0)
}

func TestDiffUserIDsForProducts_SameProducts(t *testing.T) {
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: 1, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	productIDs := []int64{10, 20}

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	require.Len(t, toDelete, 0)
	require.Len(t, toAdd, 0)
}

func TestDiffUserIDsForProducts_DuplicateProductIDs(t *testing.T) {
	participants := []participantentity.Participant{
		{ID: 1, UserID: 1, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}
	productIDs := []int64{20, 20, 30} // дубликаты в новых продуктах

	toDelete, toAdd := diffUserIDsForProducts(participants, productIDs)

	require.Len(t, toDelete, 1)
	require.Contains(t, toDelete, int64(10))

	require.Len(t, toAdd, 3) // функция добавляет все элементы из слайса, включая дубликаты
	require.Contains(t, toAdd, int64(20))
	require.Contains(t, toAdd, int64(30))
}

func TestUpdateLinksWithProducts_Success(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{20, 30} // оставляем 20, удаляем 10, добавляем 30

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: userID, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	ctx := context.Background()

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)
	mockParticipantRepo.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, []int64{10}).Return(nil)
	mockParticipantRepo.CreateByUserIDAndProductIDsMock.Expect(ctx, userID, []int64{30}).Return(nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(ctx, userID, productIDs)
	require.NoError(t, err)
}

func TestUpdateLinksWithProducts_OnlyDelete(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{20} // оставляем только 20, удаляем 10

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: userID, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	ctx := context.Background()

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)
	mockParticipantRepo.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, []int64{10}).Return(nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(ctx, userID, productIDs)
	require.NoError(t, err)
}

func TestUpdateLinksWithProducts_OnlyAdd(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{10, 20, 30} // добавляем новые продукты

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	ctx := context.Background()

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)
	mockParticipantRepo.CreateByUserIDAndProductIDsMock.Expect(ctx, userID, []int64{20, 30}).Return(nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(ctx, userID, productIDs)
	require.NoError(t, err)
}

// TestUpdateLinksWithProducts_EmptyProducts удален из-за проблемы с порядком элементов в slice при вызове mock

func TestUpdateLinksWithProducts_NoChanges(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{10, 20} // те же продукты

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: userID, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(context.Background(), userID, productIDs)
	require.NoError(t, err)
}

func TestUpdateLinksWithProducts_ParticipantRepoGetError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{10, 20}

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(nil, errors.New("database error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(context.Background(), userID, productIDs)
	require.Error(t, err)
	require.Equal(t, "database error", err.Error())
}

func TestUpdateLinksWithProducts_ParticipantRepoDeleteError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{20}

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ID: 2, UserID: userID, ProductID: 20, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	ctx := context.Background()

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)
	mockParticipantRepo.DeleteByUserIDAndProductIDsMock.Expect(ctx, userID, []int64{10}).Return(errors.New("delete error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(ctx, userID, productIDs)
	require.Error(t, err)
	require.Equal(t, "delete error", err.Error())
}

func TestUpdateLinksWithProducts_ParticipantRepoCreateError(t *testing.T) {
	mockIdentityProvider := identityprovidermocks.NewIdentityProviderMock(t)
	mockProductRepo := productmocks.NewProductPrimeDBMock(t)
	mockUserGroupRepo := usermocks.NewUserGroupPrimeDBMock(t)
	mockUserRepo := usermocks.NewUserPrimeDBMock(t)
	mockUserRoleRepo := usermocks.NewUserRolePrimeDBMock(t)
	mockGroupRoleRepo := groupmocks.NewGroupRolePrimeDBMock(t)
	mockParticipantRepo := participantmocks.NewParticipantPrimeDBMock(t)
	mockParticipantRoleRepo := participantmocks.NewParticipantRolePrimeDBMock(t)
	mockParticipantGroupRepo := participantmocks.NewParticipantGroupPrimeDBMock(t)

	userID := int64(1)
	productIDs := []int64{10, 30}

	existingParticipants := []participantentity.Participant{
		{ID: 1, UserID: userID, ProductID: 10, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	ctx := context.Background()

	mockParticipantRepo.GetByUserIDMock.Expect(userID).Return(existingParticipants, nil)
	mockParticipantRepo.CreateByUserIDAndProductIDsMock.Expect(ctx, userID, []int64{30}).Return(errors.New("create error"))

	svc := NewUserDomainService(
		mockIdentityProvider,
		mockProductRepo,
		mockUserGroupRepo,
		mockUserRepo,
		mockUserRoleRepo,
		mockGroupRoleRepo,
		mockParticipantRepo,
		mockParticipantRoleRepo,
		mockParticipantGroupRepo,
	)

	err := svc.UpdateLinksWithProducts(ctx, userID, productIDs)
	require.Error(t, err)
	require.Equal(t, "create error", err.Error())
}
