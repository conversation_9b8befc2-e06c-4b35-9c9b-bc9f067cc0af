// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository.UserPrimeDB -o user_prime_db_mock.go -n UserPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// UserPrimeDBMock implements mm_repository.UserPrimeDB
type UserPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, user userentity.User) (u1 userentity.User, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, user userentity.User)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mUserPrimeDBMockCreate

	funcGetAdmins          func() (ua1 []userentity.User, err error)
	funcGetAdminsOrigin    string
	inspectFuncGetAdmins   func()
	afterGetAdminsCounter  uint64
	beforeGetAdminsCounter uint64
	GetAdminsMock          mUserPrimeDBMockGetAdmins

	funcGetAll          func() (ua1 []userentity.User, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mUserPrimeDBMockGetAll

	funcGetAllPaginated          func(pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)
	funcGetAllPaginatedOrigin    string
	inspectFuncGetAllPaginated   func(pagination sharedentity.PaginationParams)
	afterGetAllPaginatedCounter  uint64
	beforeGetAllPaginatedCounter uint64
	GetAllPaginatedMock          mUserPrimeDBMockGetAllPaginated

	funcGetByCategoryID          func(categoryID int64) (ua1 []userentity.User, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mUserPrimeDBMockGetByCategoryID

	funcGetByEmail          func(email string) (u1 userentity.User, err error)
	funcGetByEmailOrigin    string
	inspectFuncGetByEmail   func(email string)
	afterGetByEmailCounter  uint64
	beforeGetByEmailCounter uint64
	GetByEmailMock          mUserPrimeDBMockGetByEmail

	funcGetByFilters          func(userFilters userentity.UserFiltersData) (ua1 []userentity.User, err error)
	funcGetByFiltersOrigin    string
	inspectFuncGetByFilters   func(userFilters userentity.UserFiltersData)
	afterGetByFiltersCounter  uint64
	beforeGetByFiltersCounter uint64
	GetByFiltersMock          mUserPrimeDBMockGetByFilters

	funcGetByFiltersPaginated          func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)
	funcGetByFiltersPaginatedOrigin    string
	inspectFuncGetByFiltersPaginated   func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams)
	afterGetByFiltersPaginatedCounter  uint64
	beforeGetByFiltersPaginatedCounter uint64
	GetByFiltersPaginatedMock          mUserPrimeDBMockGetByFiltersPaginated

	funcGetByGroupID          func(id int64) (ua1 []userentity.User, err error)
	funcGetByGroupIDOrigin    string
	inspectFuncGetByGroupID   func(id int64)
	afterGetByGroupIDCounter  uint64
	beforeGetByGroupIDCounter uint64
	GetByGroupIDMock          mUserPrimeDBMockGetByGroupID

	funcGetByID          func(id int64) (u1 userentity.User, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mUserPrimeDBMockGetByID

	funcGetUsersByParticipantGroupID          func(groupID int64) (ua1 []userentity.User, err error)
	funcGetUsersByParticipantGroupIDOrigin    string
	inspectFuncGetUsersByParticipantGroupID   func(groupID int64)
	afterGetUsersByParticipantGroupIDCounter  uint64
	beforeGetUsersByParticipantGroupIDCounter uint64
	GetUsersByParticipantGroupIDMock          mUserPrimeDBMockGetUsersByParticipantGroupID

	funcGetUsersByParticipantRoleID          func(roleID int64) (ua1 []userentity.User, err error)
	funcGetUsersByParticipantRoleIDOrigin    string
	inspectFuncGetUsersByParticipantRoleID   func(roleID int64)
	afterGetUsersByParticipantRoleIDCounter  uint64
	beforeGetUsersByParticipantRoleIDCounter uint64
	GetUsersByParticipantRoleIDMock          mUserPrimeDBMockGetUsersByParticipantRoleID

	funcUpdate          func(user userentity.UserUpdateData) (u1 userentity.User, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(user userentity.UserUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mUserPrimeDBMockUpdate
}

// NewUserPrimeDBMock returns a mock for mm_repository.UserPrimeDB
func NewUserPrimeDBMock(t minimock.Tester) *UserPrimeDBMock {
	m := &UserPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mUserPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*UserPrimeDBMockCreateParams{}

	m.GetAdminsMock = mUserPrimeDBMockGetAdmins{mock: m}

	m.GetAllMock = mUserPrimeDBMockGetAll{mock: m}

	m.GetAllPaginatedMock = mUserPrimeDBMockGetAllPaginated{mock: m}
	m.GetAllPaginatedMock.callArgs = []*UserPrimeDBMockGetAllPaginatedParams{}

	m.GetByCategoryIDMock = mUserPrimeDBMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*UserPrimeDBMockGetByCategoryIDParams{}

	m.GetByEmailMock = mUserPrimeDBMockGetByEmail{mock: m}
	m.GetByEmailMock.callArgs = []*UserPrimeDBMockGetByEmailParams{}

	m.GetByFiltersMock = mUserPrimeDBMockGetByFilters{mock: m}
	m.GetByFiltersMock.callArgs = []*UserPrimeDBMockGetByFiltersParams{}

	m.GetByFiltersPaginatedMock = mUserPrimeDBMockGetByFiltersPaginated{mock: m}
	m.GetByFiltersPaginatedMock.callArgs = []*UserPrimeDBMockGetByFiltersPaginatedParams{}

	m.GetByGroupIDMock = mUserPrimeDBMockGetByGroupID{mock: m}
	m.GetByGroupIDMock.callArgs = []*UserPrimeDBMockGetByGroupIDParams{}

	m.GetByIDMock = mUserPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*UserPrimeDBMockGetByIDParams{}

	m.GetUsersByParticipantGroupIDMock = mUserPrimeDBMockGetUsersByParticipantGroupID{mock: m}
	m.GetUsersByParticipantGroupIDMock.callArgs = []*UserPrimeDBMockGetUsersByParticipantGroupIDParams{}

	m.GetUsersByParticipantRoleIDMock = mUserPrimeDBMockGetUsersByParticipantRoleID{mock: m}
	m.GetUsersByParticipantRoleIDMock.callArgs = []*UserPrimeDBMockGetUsersByParticipantRoleIDParams{}

	m.UpdateMock = mUserPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*UserPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mUserPrimeDBMockCreate struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockCreateExpectation
	expectations       []*UserPrimeDBMockCreateExpectation

	callArgs []*UserPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockCreateExpectation specifies expectation struct of the UserPrimeDB.Create
type UserPrimeDBMockCreateExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockCreateParams
	paramPtrs          *UserPrimeDBMockCreateParamPtrs
	expectationOrigins UserPrimeDBMockCreateExpectationOrigins
	results            *UserPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockCreateParams contains parameters of the UserPrimeDB.Create
type UserPrimeDBMockCreateParams struct {
	ctx  context.Context
	user userentity.User
}

// UserPrimeDBMockCreateParamPtrs contains pointers to parameters of the UserPrimeDB.Create
type UserPrimeDBMockCreateParamPtrs struct {
	ctx  *context.Context
	user *userentity.User
}

// UserPrimeDBMockCreateResults contains results of the UserPrimeDB.Create
type UserPrimeDBMockCreateResults struct {
	u1  userentity.User
	err error
}

// UserPrimeDBMockCreateOrigins contains origins of expectations of the UserPrimeDB.Create
type UserPrimeDBMockCreateExpectationOrigins struct {
	origin     string
	originCtx  string
	originUser string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mUserPrimeDBMockCreate) Optional() *mUserPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for UserPrimeDB.Create
func (mmCreate *mUserPrimeDBMockCreate) Expect(ctx context.Context, user userentity.User) *mUserPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &UserPrimeDBMockCreateParams{ctx, user}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for UserPrimeDB.Create
func (mmCreate *mUserPrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mUserPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectUserParam2 sets up expected param user for UserPrimeDB.Create
func (mmCreate *mUserPrimeDBMockCreate) ExpectUserParam2(user userentity.User) *mUserPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.user = &user
	mmCreate.defaultExpectation.expectationOrigins.originUser = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.Create
func (mmCreate *mUserPrimeDBMockCreate) Inspect(f func(ctx context.Context, user userentity.User)) *mUserPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by UserPrimeDB.Create
func (mmCreate *mUserPrimeDBMockCreate) Return(u1 userentity.User, err error) *UserPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &UserPrimeDBMockCreateResults{u1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the UserPrimeDB.Create method
func (mmCreate *mUserPrimeDBMockCreate) Set(f func(ctx context.Context, user userentity.User) (u1 userentity.User, err error)) *UserPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the UserPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mUserPrimeDBMockCreate) When(ctx context.Context, user userentity.User) *UserPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &UserPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &UserPrimeDBMockCreateParams{ctx, user},
		expectationOrigins: UserPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockCreateExpectation) Then(u1 userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockCreateResults{u1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.Create should be invoked
func (mmCreate *mUserPrimeDBMockCreate) Times(n uint64) *mUserPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of UserPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mUserPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.UserPrimeDB
func (mmCreate *UserPrimeDBMock) Create(ctx context.Context, user userentity.User) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, user)
	}

	mm_params := UserPrimeDBMockCreateParams{ctx, user}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockCreateParams{ctx, user}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("UserPrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.user != nil && !minimock.Equal(*mm_want_ptrs.user, mm_got.user) {
				mmCreate.t.Errorf("UserPrimeDBMock.Create got unexpected parameter user, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originUser, *mm_want_ptrs.user, mm_got.user, minimock.Diff(*mm_want_ptrs.user, mm_got.user))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("UserPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the UserPrimeDBMock.Create")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, user)
	}
	mmCreate.t.Fatalf("Unexpected call to UserPrimeDBMock.Create. %v %v", ctx, user)
	return
}

// CreateAfterCounter returns a count of finished UserPrimeDBMock.Create invocations
func (mmCreate *UserPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of UserPrimeDBMock.Create invocations
func (mmCreate *UserPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mUserPrimeDBMockCreate) Calls() []*UserPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mUserPrimeDBMockGetAdmins struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetAdminsExpectation
	expectations       []*UserPrimeDBMockGetAdminsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetAdminsExpectation specifies expectation struct of the UserPrimeDB.GetAdmins
type UserPrimeDBMockGetAdminsExpectation struct {
	mock *UserPrimeDBMock

	results      *UserPrimeDBMockGetAdminsResults
	returnOrigin string
	Counter      uint64
}

// UserPrimeDBMockGetAdminsResults contains results of the UserPrimeDB.GetAdmins
type UserPrimeDBMockGetAdminsResults struct {
	ua1 []userentity.User
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAdmins *mUserPrimeDBMockGetAdmins) Optional() *mUserPrimeDBMockGetAdmins {
	mmGetAdmins.optional = true
	return mmGetAdmins
}

// Expect sets up expected params for UserPrimeDB.GetAdmins
func (mmGetAdmins *mUserPrimeDBMockGetAdmins) Expect() *mUserPrimeDBMockGetAdmins {
	if mmGetAdmins.mock.funcGetAdmins != nil {
		mmGetAdmins.mock.t.Fatalf("UserPrimeDBMock.GetAdmins mock is already set by Set")
	}

	if mmGetAdmins.defaultExpectation == nil {
		mmGetAdmins.defaultExpectation = &UserPrimeDBMockGetAdminsExpectation{}
	}

	return mmGetAdmins
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetAdmins
func (mmGetAdmins *mUserPrimeDBMockGetAdmins) Inspect(f func()) *mUserPrimeDBMockGetAdmins {
	if mmGetAdmins.mock.inspectFuncGetAdmins != nil {
		mmGetAdmins.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetAdmins")
	}

	mmGetAdmins.mock.inspectFuncGetAdmins = f

	return mmGetAdmins
}

// Return sets up results that will be returned by UserPrimeDB.GetAdmins
func (mmGetAdmins *mUserPrimeDBMockGetAdmins) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetAdmins.mock.funcGetAdmins != nil {
		mmGetAdmins.mock.t.Fatalf("UserPrimeDBMock.GetAdmins mock is already set by Set")
	}

	if mmGetAdmins.defaultExpectation == nil {
		mmGetAdmins.defaultExpectation = &UserPrimeDBMockGetAdminsExpectation{mock: mmGetAdmins.mock}
	}
	mmGetAdmins.defaultExpectation.results = &UserPrimeDBMockGetAdminsResults{ua1, err}
	mmGetAdmins.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAdmins.mock
}

// Set uses given function f to mock the UserPrimeDB.GetAdmins method
func (mmGetAdmins *mUserPrimeDBMockGetAdmins) Set(f func() (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetAdmins.defaultExpectation != nil {
		mmGetAdmins.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetAdmins method")
	}

	if len(mmGetAdmins.expectations) > 0 {
		mmGetAdmins.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetAdmins method")
	}

	mmGetAdmins.mock.funcGetAdmins = f
	mmGetAdmins.mock.funcGetAdminsOrigin = minimock.CallerInfo(1)
	return mmGetAdmins.mock
}

// Times sets number of times UserPrimeDB.GetAdmins should be invoked
func (mmGetAdmins *mUserPrimeDBMockGetAdmins) Times(n uint64) *mUserPrimeDBMockGetAdmins {
	if n == 0 {
		mmGetAdmins.mock.t.Fatalf("Times of UserPrimeDBMock.GetAdmins mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAdmins.expectedInvocations, n)
	mmGetAdmins.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAdmins
}

func (mmGetAdmins *mUserPrimeDBMockGetAdmins) invocationsDone() bool {
	if len(mmGetAdmins.expectations) == 0 && mmGetAdmins.defaultExpectation == nil && mmGetAdmins.mock.funcGetAdmins == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAdmins.mock.afterGetAdminsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAdmins.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAdmins implements mm_repository.UserPrimeDB
func (mmGetAdmins *UserPrimeDBMock) GetAdmins() (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetAdmins.beforeGetAdminsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAdmins.afterGetAdminsCounter, 1)

	mmGetAdmins.t.Helper()

	if mmGetAdmins.inspectFuncGetAdmins != nil {
		mmGetAdmins.inspectFuncGetAdmins()
	}

	if mmGetAdmins.GetAdminsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAdmins.GetAdminsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAdmins.GetAdminsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAdmins.t.Fatal("No results are set for the UserPrimeDBMock.GetAdmins")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetAdmins.funcGetAdmins != nil {
		return mmGetAdmins.funcGetAdmins()
	}
	mmGetAdmins.t.Fatalf("Unexpected call to UserPrimeDBMock.GetAdmins.")
	return
}

// GetAdminsAfterCounter returns a count of finished UserPrimeDBMock.GetAdmins invocations
func (mmGetAdmins *UserPrimeDBMock) GetAdminsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAdmins.afterGetAdminsCounter)
}

// GetAdminsBeforeCounter returns a count of UserPrimeDBMock.GetAdmins invocations
func (mmGetAdmins *UserPrimeDBMock) GetAdminsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAdmins.beforeGetAdminsCounter)
}

// MinimockGetAdminsDone returns true if the count of the GetAdmins invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetAdminsDone() bool {
	if m.GetAdminsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAdminsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAdminsMock.invocationsDone()
}

// MinimockGetAdminsInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetAdminsInspect() {
	for _, e := range m.GetAdminsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to UserPrimeDBMock.GetAdmins")
		}
	}

	afterGetAdminsCounter := mm_atomic.LoadUint64(&m.afterGetAdminsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAdminsMock.defaultExpectation != nil && afterGetAdminsCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetAdmins at\n%s", m.GetAdminsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAdmins != nil && afterGetAdminsCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetAdmins at\n%s", m.funcGetAdminsOrigin)
	}

	if !m.GetAdminsMock.invocationsDone() && afterGetAdminsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetAdmins at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAdminsMock.expectedInvocations), m.GetAdminsMock.expectedInvocationsOrigin, afterGetAdminsCounter)
	}
}

type mUserPrimeDBMockGetAll struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetAllExpectation
	expectations       []*UserPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetAllExpectation specifies expectation struct of the UserPrimeDB.GetAll
type UserPrimeDBMockGetAllExpectation struct {
	mock *UserPrimeDBMock

	results      *UserPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// UserPrimeDBMockGetAllResults contains results of the UserPrimeDB.GetAll
type UserPrimeDBMockGetAllResults struct {
	ua1 []userentity.User
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mUserPrimeDBMockGetAll) Optional() *mUserPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for UserPrimeDB.GetAll
func (mmGetAll *mUserPrimeDBMockGetAll) Expect() *mUserPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("UserPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &UserPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetAll
func (mmGetAll *mUserPrimeDBMockGetAll) Inspect(f func()) *mUserPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by UserPrimeDB.GetAll
func (mmGetAll *mUserPrimeDBMockGetAll) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("UserPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &UserPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &UserPrimeDBMockGetAllResults{ua1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the UserPrimeDB.GetAll method
func (mmGetAll *mUserPrimeDBMockGetAll) Set(f func() (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times UserPrimeDB.GetAll should be invoked
func (mmGetAll *mUserPrimeDBMockGetAll) Times(n uint64) *mUserPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of UserPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mUserPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.UserPrimeDB
func (mmGetAll *UserPrimeDBMock) GetAll() (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the UserPrimeDBMock.GetAll")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to UserPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished UserPrimeDBMock.GetAll invocations
func (mmGetAll *UserPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of UserPrimeDBMock.GetAll invocations
func (mmGetAll *UserPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to UserPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mUserPrimeDBMockGetAllPaginated struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetAllPaginatedExpectation
	expectations       []*UserPrimeDBMockGetAllPaginatedExpectation

	callArgs []*UserPrimeDBMockGetAllPaginatedParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetAllPaginatedExpectation specifies expectation struct of the UserPrimeDB.GetAllPaginated
type UserPrimeDBMockGetAllPaginatedExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetAllPaginatedParams
	paramPtrs          *UserPrimeDBMockGetAllPaginatedParamPtrs
	expectationOrigins UserPrimeDBMockGetAllPaginatedExpectationOrigins
	results            *UserPrimeDBMockGetAllPaginatedResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetAllPaginatedParams contains parameters of the UserPrimeDB.GetAllPaginated
type UserPrimeDBMockGetAllPaginatedParams struct {
	pagination sharedentity.PaginationParams
}

// UserPrimeDBMockGetAllPaginatedParamPtrs contains pointers to parameters of the UserPrimeDB.GetAllPaginated
type UserPrimeDBMockGetAllPaginatedParamPtrs struct {
	pagination *sharedentity.PaginationParams
}

// UserPrimeDBMockGetAllPaginatedResults contains results of the UserPrimeDB.GetAllPaginated
type UserPrimeDBMockGetAllPaginatedResults struct {
	p1  sharedentity.PaginatedResult[userentity.User]
	err error
}

// UserPrimeDBMockGetAllPaginatedOrigins contains origins of expectations of the UserPrimeDB.GetAllPaginated
type UserPrimeDBMockGetAllPaginatedExpectationOrigins struct {
	origin           string
	originPagination string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Optional() *mUserPrimeDBMockGetAllPaginated {
	mmGetAllPaginated.optional = true
	return mmGetAllPaginated
}

// Expect sets up expected params for UserPrimeDB.GetAllPaginated
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Expect(pagination sharedentity.PaginationParams) *mUserPrimeDBMockGetAllPaginated {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserPrimeDBMock.GetAllPaginated mock is already set by Set")
	}

	if mmGetAllPaginated.defaultExpectation == nil {
		mmGetAllPaginated.defaultExpectation = &UserPrimeDBMockGetAllPaginatedExpectation{}
	}

	if mmGetAllPaginated.defaultExpectation.paramPtrs != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserPrimeDBMock.GetAllPaginated mock is already set by ExpectParams functions")
	}

	mmGetAllPaginated.defaultExpectation.params = &UserPrimeDBMockGetAllPaginatedParams{pagination}
	mmGetAllPaginated.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAllPaginated.expectations {
		if minimock.Equal(e.params, mmGetAllPaginated.defaultExpectation.params) {
			mmGetAllPaginated.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAllPaginated.defaultExpectation.params)
		}
	}

	return mmGetAllPaginated
}

// ExpectPaginationParam1 sets up expected param pagination for UserPrimeDB.GetAllPaginated
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) ExpectPaginationParam1(pagination sharedentity.PaginationParams) *mUserPrimeDBMockGetAllPaginated {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserPrimeDBMock.GetAllPaginated mock is already set by Set")
	}

	if mmGetAllPaginated.defaultExpectation == nil {
		mmGetAllPaginated.defaultExpectation = &UserPrimeDBMockGetAllPaginatedExpectation{}
	}

	if mmGetAllPaginated.defaultExpectation.params != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserPrimeDBMock.GetAllPaginated mock is already set by Expect")
	}

	if mmGetAllPaginated.defaultExpectation.paramPtrs == nil {
		mmGetAllPaginated.defaultExpectation.paramPtrs = &UserPrimeDBMockGetAllPaginatedParamPtrs{}
	}
	mmGetAllPaginated.defaultExpectation.paramPtrs.pagination = &pagination
	mmGetAllPaginated.defaultExpectation.expectationOrigins.originPagination = minimock.CallerInfo(1)

	return mmGetAllPaginated
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetAllPaginated
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Inspect(f func(pagination sharedentity.PaginationParams)) *mUserPrimeDBMockGetAllPaginated {
	if mmGetAllPaginated.mock.inspectFuncGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetAllPaginated")
	}

	mmGetAllPaginated.mock.inspectFuncGetAllPaginated = f

	return mmGetAllPaginated
}

// Return sets up results that will be returned by UserPrimeDB.GetAllPaginated
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Return(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserPrimeDBMock {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserPrimeDBMock.GetAllPaginated mock is already set by Set")
	}

	if mmGetAllPaginated.defaultExpectation == nil {
		mmGetAllPaginated.defaultExpectation = &UserPrimeDBMockGetAllPaginatedExpectation{mock: mmGetAllPaginated.mock}
	}
	mmGetAllPaginated.defaultExpectation.results = &UserPrimeDBMockGetAllPaginatedResults{p1, err}
	mmGetAllPaginated.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllPaginated.mock
}

// Set uses given function f to mock the UserPrimeDB.GetAllPaginated method
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Set(f func(pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)) *UserPrimeDBMock {
	if mmGetAllPaginated.defaultExpectation != nil {
		mmGetAllPaginated.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetAllPaginated method")
	}

	if len(mmGetAllPaginated.expectations) > 0 {
		mmGetAllPaginated.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetAllPaginated method")
	}

	mmGetAllPaginated.mock.funcGetAllPaginated = f
	mmGetAllPaginated.mock.funcGetAllPaginatedOrigin = minimock.CallerInfo(1)
	return mmGetAllPaginated.mock
}

// When sets expectation for the UserPrimeDB.GetAllPaginated which will trigger the result defined by the following
// Then helper
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) When(pagination sharedentity.PaginationParams) *UserPrimeDBMockGetAllPaginatedExpectation {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserPrimeDBMock.GetAllPaginated mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetAllPaginatedExpectation{
		mock:               mmGetAllPaginated.mock,
		params:             &UserPrimeDBMockGetAllPaginatedParams{pagination},
		expectationOrigins: UserPrimeDBMockGetAllPaginatedExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAllPaginated.expectations = append(mmGetAllPaginated.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetAllPaginated return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetAllPaginatedExpectation) Then(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetAllPaginatedResults{p1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetAllPaginated should be invoked
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Times(n uint64) *mUserPrimeDBMockGetAllPaginated {
	if n == 0 {
		mmGetAllPaginated.mock.t.Fatalf("Times of UserPrimeDBMock.GetAllPaginated mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllPaginated.expectedInvocations, n)
	mmGetAllPaginated.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllPaginated
}

func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) invocationsDone() bool {
	if len(mmGetAllPaginated.expectations) == 0 && mmGetAllPaginated.defaultExpectation == nil && mmGetAllPaginated.mock.funcGetAllPaginated == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllPaginated.mock.afterGetAllPaginatedCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllPaginated.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllPaginated implements mm_repository.UserPrimeDB
func (mmGetAllPaginated *UserPrimeDBMock) GetAllPaginated(pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error) {
	mm_atomic.AddUint64(&mmGetAllPaginated.beforeGetAllPaginatedCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllPaginated.afterGetAllPaginatedCounter, 1)

	mmGetAllPaginated.t.Helper()

	if mmGetAllPaginated.inspectFuncGetAllPaginated != nil {
		mmGetAllPaginated.inspectFuncGetAllPaginated(pagination)
	}

	mm_params := UserPrimeDBMockGetAllPaginatedParams{pagination}

	// Record call args
	mmGetAllPaginated.GetAllPaginatedMock.mutex.Lock()
	mmGetAllPaginated.GetAllPaginatedMock.callArgs = append(mmGetAllPaginated.GetAllPaginatedMock.callArgs, &mm_params)
	mmGetAllPaginated.GetAllPaginatedMock.mutex.Unlock()

	for _, e := range mmGetAllPaginated.GetAllPaginatedMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.params
		mm_want_ptrs := mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetAllPaginatedParams{pagination}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.pagination != nil && !minimock.Equal(*mm_want_ptrs.pagination, mm_got.pagination) {
				mmGetAllPaginated.t.Errorf("UserPrimeDBMock.GetAllPaginated got unexpected parameter pagination, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.expectationOrigins.originPagination, *mm_want_ptrs.pagination, mm_got.pagination, minimock.Diff(*mm_want_ptrs.pagination, mm_got.pagination))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAllPaginated.t.Errorf("UserPrimeDBMock.GetAllPaginated got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllPaginated.t.Fatal("No results are set for the UserPrimeDBMock.GetAllPaginated")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetAllPaginated.funcGetAllPaginated != nil {
		return mmGetAllPaginated.funcGetAllPaginated(pagination)
	}
	mmGetAllPaginated.t.Fatalf("Unexpected call to UserPrimeDBMock.GetAllPaginated. %v", pagination)
	return
}

// GetAllPaginatedAfterCounter returns a count of finished UserPrimeDBMock.GetAllPaginated invocations
func (mmGetAllPaginated *UserPrimeDBMock) GetAllPaginatedAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllPaginated.afterGetAllPaginatedCounter)
}

// GetAllPaginatedBeforeCounter returns a count of UserPrimeDBMock.GetAllPaginated invocations
func (mmGetAllPaginated *UserPrimeDBMock) GetAllPaginatedBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllPaginated.beforeGetAllPaginatedCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetAllPaginated.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAllPaginated *mUserPrimeDBMockGetAllPaginated) Calls() []*UserPrimeDBMockGetAllPaginatedParams {
	mmGetAllPaginated.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetAllPaginatedParams, len(mmGetAllPaginated.callArgs))
	copy(argCopy, mmGetAllPaginated.callArgs)

	mmGetAllPaginated.mutex.RUnlock()

	return argCopy
}

// MinimockGetAllPaginatedDone returns true if the count of the GetAllPaginated invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetAllPaginatedDone() bool {
	if m.GetAllPaginatedMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllPaginatedMock.invocationsDone()
}

// MinimockGetAllPaginatedInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetAllPaginatedInspect() {
	for _, e := range m.GetAllPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetAllPaginated at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAllPaginatedCounter := mm_atomic.LoadUint64(&m.afterGetAllPaginatedCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllPaginatedMock.defaultExpectation != nil && afterGetAllPaginatedCounter < 1 {
		if m.GetAllPaginatedMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetAllPaginated at\n%s", m.GetAllPaginatedMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetAllPaginated at\n%s with params: %#v", m.GetAllPaginatedMock.defaultExpectation.expectationOrigins.origin, *m.GetAllPaginatedMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllPaginated != nil && afterGetAllPaginatedCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetAllPaginated at\n%s", m.funcGetAllPaginatedOrigin)
	}

	if !m.GetAllPaginatedMock.invocationsDone() && afterGetAllPaginatedCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetAllPaginated at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllPaginatedMock.expectedInvocations), m.GetAllPaginatedMock.expectedInvocationsOrigin, afterGetAllPaginatedCounter)
	}
}

type mUserPrimeDBMockGetByCategoryID struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetByCategoryIDExpectation
	expectations       []*UserPrimeDBMockGetByCategoryIDExpectation

	callArgs []*UserPrimeDBMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetByCategoryIDExpectation specifies expectation struct of the UserPrimeDB.GetByCategoryID
type UserPrimeDBMockGetByCategoryIDExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetByCategoryIDParams
	paramPtrs          *UserPrimeDBMockGetByCategoryIDParamPtrs
	expectationOrigins UserPrimeDBMockGetByCategoryIDExpectationOrigins
	results            *UserPrimeDBMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetByCategoryIDParams contains parameters of the UserPrimeDB.GetByCategoryID
type UserPrimeDBMockGetByCategoryIDParams struct {
	categoryID int64
}

// UserPrimeDBMockGetByCategoryIDParamPtrs contains pointers to parameters of the UserPrimeDB.GetByCategoryID
type UserPrimeDBMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// UserPrimeDBMockGetByCategoryIDResults contains results of the UserPrimeDB.GetByCategoryID
type UserPrimeDBMockGetByCategoryIDResults struct {
	ua1 []userentity.User
	err error
}

// UserPrimeDBMockGetByCategoryIDOrigins contains origins of expectations of the UserPrimeDB.GetByCategoryID
type UserPrimeDBMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Optional() *mUserPrimeDBMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for UserPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Expect(categoryID int64) *mUserPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &UserPrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserPrimeDBMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &UserPrimeDBMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for UserPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mUserPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &UserPrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserPrimeDBMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Inspect(f func(categoryID int64)) *mUserPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by UserPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &UserPrimeDBMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &UserPrimeDBMockGetByCategoryIDResults{ua1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the UserPrimeDB.GetByCategoryID method
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Set(f func(categoryID int64) (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the UserPrimeDB.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) When(categoryID int64) *UserPrimeDBMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &UserPrimeDBMockGetByCategoryIDParams{categoryID},
		expectationOrigins: UserPrimeDBMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetByCategoryIDExpectation) Then(ua1 []userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetByCategoryIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetByCategoryID should be invoked
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Times(n uint64) *mUserPrimeDBMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of UserPrimeDBMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.UserPrimeDB
func (mmGetByCategoryID *UserPrimeDBMock) GetByCategoryID(categoryID int64) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := UserPrimeDBMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("UserPrimeDBMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("UserPrimeDBMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the UserPrimeDBMock.GetByCategoryID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to UserPrimeDBMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished UserPrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *UserPrimeDBMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of UserPrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *UserPrimeDBMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mUserPrimeDBMockGetByCategoryID) Calls() []*UserPrimeDBMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mUserPrimeDBMockGetByEmail struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetByEmailExpectation
	expectations       []*UserPrimeDBMockGetByEmailExpectation

	callArgs []*UserPrimeDBMockGetByEmailParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetByEmailExpectation specifies expectation struct of the UserPrimeDB.GetByEmail
type UserPrimeDBMockGetByEmailExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetByEmailParams
	paramPtrs          *UserPrimeDBMockGetByEmailParamPtrs
	expectationOrigins UserPrimeDBMockGetByEmailExpectationOrigins
	results            *UserPrimeDBMockGetByEmailResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetByEmailParams contains parameters of the UserPrimeDB.GetByEmail
type UserPrimeDBMockGetByEmailParams struct {
	email string
}

// UserPrimeDBMockGetByEmailParamPtrs contains pointers to parameters of the UserPrimeDB.GetByEmail
type UserPrimeDBMockGetByEmailParamPtrs struct {
	email *string
}

// UserPrimeDBMockGetByEmailResults contains results of the UserPrimeDB.GetByEmail
type UserPrimeDBMockGetByEmailResults struct {
	u1  userentity.User
	err error
}

// UserPrimeDBMockGetByEmailOrigins contains origins of expectations of the UserPrimeDB.GetByEmail
type UserPrimeDBMockGetByEmailExpectationOrigins struct {
	origin      string
	originEmail string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Optional() *mUserPrimeDBMockGetByEmail {
	mmGetByEmail.optional = true
	return mmGetByEmail
}

// Expect sets up expected params for UserPrimeDB.GetByEmail
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Expect(email string) *mUserPrimeDBMockGetByEmail {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserPrimeDBMock.GetByEmail mock is already set by Set")
	}

	if mmGetByEmail.defaultExpectation == nil {
		mmGetByEmail.defaultExpectation = &UserPrimeDBMockGetByEmailExpectation{}
	}

	if mmGetByEmail.defaultExpectation.paramPtrs != nil {
		mmGetByEmail.mock.t.Fatalf("UserPrimeDBMock.GetByEmail mock is already set by ExpectParams functions")
	}

	mmGetByEmail.defaultExpectation.params = &UserPrimeDBMockGetByEmailParams{email}
	mmGetByEmail.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByEmail.expectations {
		if minimock.Equal(e.params, mmGetByEmail.defaultExpectation.params) {
			mmGetByEmail.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByEmail.defaultExpectation.params)
		}
	}

	return mmGetByEmail
}

// ExpectEmailParam1 sets up expected param email for UserPrimeDB.GetByEmail
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) ExpectEmailParam1(email string) *mUserPrimeDBMockGetByEmail {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserPrimeDBMock.GetByEmail mock is already set by Set")
	}

	if mmGetByEmail.defaultExpectation == nil {
		mmGetByEmail.defaultExpectation = &UserPrimeDBMockGetByEmailExpectation{}
	}

	if mmGetByEmail.defaultExpectation.params != nil {
		mmGetByEmail.mock.t.Fatalf("UserPrimeDBMock.GetByEmail mock is already set by Expect")
	}

	if mmGetByEmail.defaultExpectation.paramPtrs == nil {
		mmGetByEmail.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByEmailParamPtrs{}
	}
	mmGetByEmail.defaultExpectation.paramPtrs.email = &email
	mmGetByEmail.defaultExpectation.expectationOrigins.originEmail = minimock.CallerInfo(1)

	return mmGetByEmail
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetByEmail
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Inspect(f func(email string)) *mUserPrimeDBMockGetByEmail {
	if mmGetByEmail.mock.inspectFuncGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetByEmail")
	}

	mmGetByEmail.mock.inspectFuncGetByEmail = f

	return mmGetByEmail
}

// Return sets up results that will be returned by UserPrimeDB.GetByEmail
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Return(u1 userentity.User, err error) *UserPrimeDBMock {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserPrimeDBMock.GetByEmail mock is already set by Set")
	}

	if mmGetByEmail.defaultExpectation == nil {
		mmGetByEmail.defaultExpectation = &UserPrimeDBMockGetByEmailExpectation{mock: mmGetByEmail.mock}
	}
	mmGetByEmail.defaultExpectation.results = &UserPrimeDBMockGetByEmailResults{u1, err}
	mmGetByEmail.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByEmail.mock
}

// Set uses given function f to mock the UserPrimeDB.GetByEmail method
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Set(f func(email string) (u1 userentity.User, err error)) *UserPrimeDBMock {
	if mmGetByEmail.defaultExpectation != nil {
		mmGetByEmail.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetByEmail method")
	}

	if len(mmGetByEmail.expectations) > 0 {
		mmGetByEmail.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetByEmail method")
	}

	mmGetByEmail.mock.funcGetByEmail = f
	mmGetByEmail.mock.funcGetByEmailOrigin = minimock.CallerInfo(1)
	return mmGetByEmail.mock
}

// When sets expectation for the UserPrimeDB.GetByEmail which will trigger the result defined by the following
// Then helper
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) When(email string) *UserPrimeDBMockGetByEmailExpectation {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserPrimeDBMock.GetByEmail mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetByEmailExpectation{
		mock:               mmGetByEmail.mock,
		params:             &UserPrimeDBMockGetByEmailParams{email},
		expectationOrigins: UserPrimeDBMockGetByEmailExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByEmail.expectations = append(mmGetByEmail.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetByEmail return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetByEmailExpectation) Then(u1 userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetByEmailResults{u1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetByEmail should be invoked
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Times(n uint64) *mUserPrimeDBMockGetByEmail {
	if n == 0 {
		mmGetByEmail.mock.t.Fatalf("Times of UserPrimeDBMock.GetByEmail mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByEmail.expectedInvocations, n)
	mmGetByEmail.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByEmail
}

func (mmGetByEmail *mUserPrimeDBMockGetByEmail) invocationsDone() bool {
	if len(mmGetByEmail.expectations) == 0 && mmGetByEmail.defaultExpectation == nil && mmGetByEmail.mock.funcGetByEmail == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByEmail.mock.afterGetByEmailCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByEmail.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByEmail implements mm_repository.UserPrimeDB
func (mmGetByEmail *UserPrimeDBMock) GetByEmail(email string) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByEmail.beforeGetByEmailCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByEmail.afterGetByEmailCounter, 1)

	mmGetByEmail.t.Helper()

	if mmGetByEmail.inspectFuncGetByEmail != nil {
		mmGetByEmail.inspectFuncGetByEmail(email)
	}

	mm_params := UserPrimeDBMockGetByEmailParams{email}

	// Record call args
	mmGetByEmail.GetByEmailMock.mutex.Lock()
	mmGetByEmail.GetByEmailMock.callArgs = append(mmGetByEmail.GetByEmailMock.callArgs, &mm_params)
	mmGetByEmail.GetByEmailMock.mutex.Unlock()

	for _, e := range mmGetByEmail.GetByEmailMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByEmail.GetByEmailMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByEmail.GetByEmailMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByEmail.GetByEmailMock.defaultExpectation.params
		mm_want_ptrs := mmGetByEmail.GetByEmailMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetByEmailParams{email}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.email != nil && !minimock.Equal(*mm_want_ptrs.email, mm_got.email) {
				mmGetByEmail.t.Errorf("UserPrimeDBMock.GetByEmail got unexpected parameter email, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByEmail.GetByEmailMock.defaultExpectation.expectationOrigins.originEmail, *mm_want_ptrs.email, mm_got.email, minimock.Diff(*mm_want_ptrs.email, mm_got.email))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByEmail.t.Errorf("UserPrimeDBMock.GetByEmail got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByEmail.GetByEmailMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByEmail.GetByEmailMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByEmail.t.Fatal("No results are set for the UserPrimeDBMock.GetByEmail")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByEmail.funcGetByEmail != nil {
		return mmGetByEmail.funcGetByEmail(email)
	}
	mmGetByEmail.t.Fatalf("Unexpected call to UserPrimeDBMock.GetByEmail. %v", email)
	return
}

// GetByEmailAfterCounter returns a count of finished UserPrimeDBMock.GetByEmail invocations
func (mmGetByEmail *UserPrimeDBMock) GetByEmailAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByEmail.afterGetByEmailCounter)
}

// GetByEmailBeforeCounter returns a count of UserPrimeDBMock.GetByEmail invocations
func (mmGetByEmail *UserPrimeDBMock) GetByEmailBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByEmail.beforeGetByEmailCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetByEmail.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByEmail *mUserPrimeDBMockGetByEmail) Calls() []*UserPrimeDBMockGetByEmailParams {
	mmGetByEmail.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetByEmailParams, len(mmGetByEmail.callArgs))
	copy(argCopy, mmGetByEmail.callArgs)

	mmGetByEmail.mutex.RUnlock()

	return argCopy
}

// MinimockGetByEmailDone returns true if the count of the GetByEmail invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetByEmailDone() bool {
	if m.GetByEmailMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByEmailMock.invocationsDone()
}

// MinimockGetByEmailInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetByEmailInspect() {
	for _, e := range m.GetByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByEmail at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByEmailCounter := mm_atomic.LoadUint64(&m.afterGetByEmailCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByEmailMock.defaultExpectation != nil && afterGetByEmailCounter < 1 {
		if m.GetByEmailMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByEmail at\n%s", m.GetByEmailMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByEmail at\n%s with params: %#v", m.GetByEmailMock.defaultExpectation.expectationOrigins.origin, *m.GetByEmailMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByEmail != nil && afterGetByEmailCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetByEmail at\n%s", m.funcGetByEmailOrigin)
	}

	if !m.GetByEmailMock.invocationsDone() && afterGetByEmailCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetByEmail at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByEmailMock.expectedInvocations), m.GetByEmailMock.expectedInvocationsOrigin, afterGetByEmailCounter)
	}
}

type mUserPrimeDBMockGetByFilters struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetByFiltersExpectation
	expectations       []*UserPrimeDBMockGetByFiltersExpectation

	callArgs []*UserPrimeDBMockGetByFiltersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetByFiltersExpectation specifies expectation struct of the UserPrimeDB.GetByFilters
type UserPrimeDBMockGetByFiltersExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetByFiltersParams
	paramPtrs          *UserPrimeDBMockGetByFiltersParamPtrs
	expectationOrigins UserPrimeDBMockGetByFiltersExpectationOrigins
	results            *UserPrimeDBMockGetByFiltersResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetByFiltersParams contains parameters of the UserPrimeDB.GetByFilters
type UserPrimeDBMockGetByFiltersParams struct {
	userFilters userentity.UserFiltersData
}

// UserPrimeDBMockGetByFiltersParamPtrs contains pointers to parameters of the UserPrimeDB.GetByFilters
type UserPrimeDBMockGetByFiltersParamPtrs struct {
	userFilters *userentity.UserFiltersData
}

// UserPrimeDBMockGetByFiltersResults contains results of the UserPrimeDB.GetByFilters
type UserPrimeDBMockGetByFiltersResults struct {
	ua1 []userentity.User
	err error
}

// UserPrimeDBMockGetByFiltersOrigins contains origins of expectations of the UserPrimeDB.GetByFilters
type UserPrimeDBMockGetByFiltersExpectationOrigins struct {
	origin            string
	originUserFilters string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Optional() *mUserPrimeDBMockGetByFilters {
	mmGetByFilters.optional = true
	return mmGetByFilters
}

// Expect sets up expected params for UserPrimeDB.GetByFilters
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Expect(userFilters userentity.UserFiltersData) *mUserPrimeDBMockGetByFilters {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserPrimeDBMock.GetByFilters mock is already set by Set")
	}

	if mmGetByFilters.defaultExpectation == nil {
		mmGetByFilters.defaultExpectation = &UserPrimeDBMockGetByFiltersExpectation{}
	}

	if mmGetByFilters.defaultExpectation.paramPtrs != nil {
		mmGetByFilters.mock.t.Fatalf("UserPrimeDBMock.GetByFilters mock is already set by ExpectParams functions")
	}

	mmGetByFilters.defaultExpectation.params = &UserPrimeDBMockGetByFiltersParams{userFilters}
	mmGetByFilters.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByFilters.expectations {
		if minimock.Equal(e.params, mmGetByFilters.defaultExpectation.params) {
			mmGetByFilters.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByFilters.defaultExpectation.params)
		}
	}

	return mmGetByFilters
}

// ExpectUserFiltersParam1 sets up expected param userFilters for UserPrimeDB.GetByFilters
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) ExpectUserFiltersParam1(userFilters userentity.UserFiltersData) *mUserPrimeDBMockGetByFilters {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserPrimeDBMock.GetByFilters mock is already set by Set")
	}

	if mmGetByFilters.defaultExpectation == nil {
		mmGetByFilters.defaultExpectation = &UserPrimeDBMockGetByFiltersExpectation{}
	}

	if mmGetByFilters.defaultExpectation.params != nil {
		mmGetByFilters.mock.t.Fatalf("UserPrimeDBMock.GetByFilters mock is already set by Expect")
	}

	if mmGetByFilters.defaultExpectation.paramPtrs == nil {
		mmGetByFilters.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByFiltersParamPtrs{}
	}
	mmGetByFilters.defaultExpectation.paramPtrs.userFilters = &userFilters
	mmGetByFilters.defaultExpectation.expectationOrigins.originUserFilters = minimock.CallerInfo(1)

	return mmGetByFilters
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetByFilters
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Inspect(f func(userFilters userentity.UserFiltersData)) *mUserPrimeDBMockGetByFilters {
	if mmGetByFilters.mock.inspectFuncGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetByFilters")
	}

	mmGetByFilters.mock.inspectFuncGetByFilters = f

	return mmGetByFilters
}

// Return sets up results that will be returned by UserPrimeDB.GetByFilters
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserPrimeDBMock.GetByFilters mock is already set by Set")
	}

	if mmGetByFilters.defaultExpectation == nil {
		mmGetByFilters.defaultExpectation = &UserPrimeDBMockGetByFiltersExpectation{mock: mmGetByFilters.mock}
	}
	mmGetByFilters.defaultExpectation.results = &UserPrimeDBMockGetByFiltersResults{ua1, err}
	mmGetByFilters.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByFilters.mock
}

// Set uses given function f to mock the UserPrimeDB.GetByFilters method
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Set(f func(userFilters userentity.UserFiltersData) (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetByFilters.defaultExpectation != nil {
		mmGetByFilters.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetByFilters method")
	}

	if len(mmGetByFilters.expectations) > 0 {
		mmGetByFilters.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetByFilters method")
	}

	mmGetByFilters.mock.funcGetByFilters = f
	mmGetByFilters.mock.funcGetByFiltersOrigin = minimock.CallerInfo(1)
	return mmGetByFilters.mock
}

// When sets expectation for the UserPrimeDB.GetByFilters which will trigger the result defined by the following
// Then helper
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) When(userFilters userentity.UserFiltersData) *UserPrimeDBMockGetByFiltersExpectation {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserPrimeDBMock.GetByFilters mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetByFiltersExpectation{
		mock:               mmGetByFilters.mock,
		params:             &UserPrimeDBMockGetByFiltersParams{userFilters},
		expectationOrigins: UserPrimeDBMockGetByFiltersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByFilters.expectations = append(mmGetByFilters.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetByFilters return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetByFiltersExpectation) Then(ua1 []userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetByFiltersResults{ua1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetByFilters should be invoked
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Times(n uint64) *mUserPrimeDBMockGetByFilters {
	if n == 0 {
		mmGetByFilters.mock.t.Fatalf("Times of UserPrimeDBMock.GetByFilters mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByFilters.expectedInvocations, n)
	mmGetByFilters.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByFilters
}

func (mmGetByFilters *mUserPrimeDBMockGetByFilters) invocationsDone() bool {
	if len(mmGetByFilters.expectations) == 0 && mmGetByFilters.defaultExpectation == nil && mmGetByFilters.mock.funcGetByFilters == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByFilters.mock.afterGetByFiltersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByFilters.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByFilters implements mm_repository.UserPrimeDB
func (mmGetByFilters *UserPrimeDBMock) GetByFilters(userFilters userentity.UserFiltersData) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByFilters.beforeGetByFiltersCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByFilters.afterGetByFiltersCounter, 1)

	mmGetByFilters.t.Helper()

	if mmGetByFilters.inspectFuncGetByFilters != nil {
		mmGetByFilters.inspectFuncGetByFilters(userFilters)
	}

	mm_params := UserPrimeDBMockGetByFiltersParams{userFilters}

	// Record call args
	mmGetByFilters.GetByFiltersMock.mutex.Lock()
	mmGetByFilters.GetByFiltersMock.callArgs = append(mmGetByFilters.GetByFiltersMock.callArgs, &mm_params)
	mmGetByFilters.GetByFiltersMock.mutex.Unlock()

	for _, e := range mmGetByFilters.GetByFiltersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByFilters.GetByFiltersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByFilters.GetByFiltersMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByFilters.GetByFiltersMock.defaultExpectation.params
		mm_want_ptrs := mmGetByFilters.GetByFiltersMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetByFiltersParams{userFilters}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userFilters != nil && !minimock.Equal(*mm_want_ptrs.userFilters, mm_got.userFilters) {
				mmGetByFilters.t.Errorf("UserPrimeDBMock.GetByFilters got unexpected parameter userFilters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFilters.GetByFiltersMock.defaultExpectation.expectationOrigins.originUserFilters, *mm_want_ptrs.userFilters, mm_got.userFilters, minimock.Diff(*mm_want_ptrs.userFilters, mm_got.userFilters))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByFilters.t.Errorf("UserPrimeDBMock.GetByFilters got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByFilters.GetByFiltersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByFilters.GetByFiltersMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByFilters.t.Fatal("No results are set for the UserPrimeDBMock.GetByFilters")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByFilters.funcGetByFilters != nil {
		return mmGetByFilters.funcGetByFilters(userFilters)
	}
	mmGetByFilters.t.Fatalf("Unexpected call to UserPrimeDBMock.GetByFilters. %v", userFilters)
	return
}

// GetByFiltersAfterCounter returns a count of finished UserPrimeDBMock.GetByFilters invocations
func (mmGetByFilters *UserPrimeDBMock) GetByFiltersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFilters.afterGetByFiltersCounter)
}

// GetByFiltersBeforeCounter returns a count of UserPrimeDBMock.GetByFilters invocations
func (mmGetByFilters *UserPrimeDBMock) GetByFiltersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFilters.beforeGetByFiltersCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetByFilters.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByFilters *mUserPrimeDBMockGetByFilters) Calls() []*UserPrimeDBMockGetByFiltersParams {
	mmGetByFilters.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetByFiltersParams, len(mmGetByFilters.callArgs))
	copy(argCopy, mmGetByFilters.callArgs)

	mmGetByFilters.mutex.RUnlock()

	return argCopy
}

// MinimockGetByFiltersDone returns true if the count of the GetByFilters invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetByFiltersDone() bool {
	if m.GetByFiltersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByFiltersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByFiltersMock.invocationsDone()
}

// MinimockGetByFiltersInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetByFiltersInspect() {
	for _, e := range m.GetByFiltersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByFilters at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByFiltersCounter := mm_atomic.LoadUint64(&m.afterGetByFiltersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByFiltersMock.defaultExpectation != nil && afterGetByFiltersCounter < 1 {
		if m.GetByFiltersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByFilters at\n%s", m.GetByFiltersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByFilters at\n%s with params: %#v", m.GetByFiltersMock.defaultExpectation.expectationOrigins.origin, *m.GetByFiltersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByFilters != nil && afterGetByFiltersCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetByFilters at\n%s", m.funcGetByFiltersOrigin)
	}

	if !m.GetByFiltersMock.invocationsDone() && afterGetByFiltersCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetByFilters at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByFiltersMock.expectedInvocations), m.GetByFiltersMock.expectedInvocationsOrigin, afterGetByFiltersCounter)
	}
}

type mUserPrimeDBMockGetByFiltersPaginated struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetByFiltersPaginatedExpectation
	expectations       []*UserPrimeDBMockGetByFiltersPaginatedExpectation

	callArgs []*UserPrimeDBMockGetByFiltersPaginatedParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetByFiltersPaginatedExpectation specifies expectation struct of the UserPrimeDB.GetByFiltersPaginated
type UserPrimeDBMockGetByFiltersPaginatedExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetByFiltersPaginatedParams
	paramPtrs          *UserPrimeDBMockGetByFiltersPaginatedParamPtrs
	expectationOrigins UserPrimeDBMockGetByFiltersPaginatedExpectationOrigins
	results            *UserPrimeDBMockGetByFiltersPaginatedResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetByFiltersPaginatedParams contains parameters of the UserPrimeDB.GetByFiltersPaginated
type UserPrimeDBMockGetByFiltersPaginatedParams struct {
	userFilters userentity.UserFiltersData
	pagination  sharedentity.PaginationParams
}

// UserPrimeDBMockGetByFiltersPaginatedParamPtrs contains pointers to parameters of the UserPrimeDB.GetByFiltersPaginated
type UserPrimeDBMockGetByFiltersPaginatedParamPtrs struct {
	userFilters *userentity.UserFiltersData
	pagination  *sharedentity.PaginationParams
}

// UserPrimeDBMockGetByFiltersPaginatedResults contains results of the UserPrimeDB.GetByFiltersPaginated
type UserPrimeDBMockGetByFiltersPaginatedResults struct {
	p1  sharedentity.PaginatedResult[userentity.User]
	err error
}

// UserPrimeDBMockGetByFiltersPaginatedOrigins contains origins of expectations of the UserPrimeDB.GetByFiltersPaginated
type UserPrimeDBMockGetByFiltersPaginatedExpectationOrigins struct {
	origin            string
	originUserFilters string
	originPagination  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Optional() *mUserPrimeDBMockGetByFiltersPaginated {
	mmGetByFiltersPaginated.optional = true
	return mmGetByFiltersPaginated
}

// Expect sets up expected params for UserPrimeDB.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Expect(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) *mUserPrimeDBMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserPrimeDBMockGetByFiltersPaginatedExpectation{}
	}

	if mmGetByFiltersPaginated.defaultExpectation.paramPtrs != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by ExpectParams functions")
	}

	mmGetByFiltersPaginated.defaultExpectation.params = &UserPrimeDBMockGetByFiltersPaginatedParams{userFilters, pagination}
	mmGetByFiltersPaginated.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByFiltersPaginated.expectations {
		if minimock.Equal(e.params, mmGetByFiltersPaginated.defaultExpectation.params) {
			mmGetByFiltersPaginated.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByFiltersPaginated.defaultExpectation.params)
		}
	}

	return mmGetByFiltersPaginated
}

// ExpectUserFiltersParam1 sets up expected param userFilters for UserPrimeDB.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) ExpectUserFiltersParam1(userFilters userentity.UserFiltersData) *mUserPrimeDBMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserPrimeDBMockGetByFiltersPaginatedExpectation{}
	}

	if mmGetByFiltersPaginated.defaultExpectation.params != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Expect")
	}

	if mmGetByFiltersPaginated.defaultExpectation.paramPtrs == nil {
		mmGetByFiltersPaginated.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByFiltersPaginatedParamPtrs{}
	}
	mmGetByFiltersPaginated.defaultExpectation.paramPtrs.userFilters = &userFilters
	mmGetByFiltersPaginated.defaultExpectation.expectationOrigins.originUserFilters = minimock.CallerInfo(1)

	return mmGetByFiltersPaginated
}

// ExpectPaginationParam2 sets up expected param pagination for UserPrimeDB.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) ExpectPaginationParam2(pagination sharedentity.PaginationParams) *mUserPrimeDBMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserPrimeDBMockGetByFiltersPaginatedExpectation{}
	}

	if mmGetByFiltersPaginated.defaultExpectation.params != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Expect")
	}

	if mmGetByFiltersPaginated.defaultExpectation.paramPtrs == nil {
		mmGetByFiltersPaginated.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByFiltersPaginatedParamPtrs{}
	}
	mmGetByFiltersPaginated.defaultExpectation.paramPtrs.pagination = &pagination
	mmGetByFiltersPaginated.defaultExpectation.expectationOrigins.originPagination = minimock.CallerInfo(1)

	return mmGetByFiltersPaginated
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Inspect(f func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams)) *mUserPrimeDBMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.inspectFuncGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetByFiltersPaginated")
	}

	mmGetByFiltersPaginated.mock.inspectFuncGetByFiltersPaginated = f

	return mmGetByFiltersPaginated
}

// Return sets up results that will be returned by UserPrimeDB.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Return(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserPrimeDBMock {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserPrimeDBMockGetByFiltersPaginatedExpectation{mock: mmGetByFiltersPaginated.mock}
	}
	mmGetByFiltersPaginated.defaultExpectation.results = &UserPrimeDBMockGetByFiltersPaginatedResults{p1, err}
	mmGetByFiltersPaginated.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersPaginated.mock
}

// Set uses given function f to mock the UserPrimeDB.GetByFiltersPaginated method
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Set(f func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)) *UserPrimeDBMock {
	if mmGetByFiltersPaginated.defaultExpectation != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetByFiltersPaginated method")
	}

	if len(mmGetByFiltersPaginated.expectations) > 0 {
		mmGetByFiltersPaginated.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetByFiltersPaginated method")
	}

	mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated = f
	mmGetByFiltersPaginated.mock.funcGetByFiltersPaginatedOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersPaginated.mock
}

// When sets expectation for the UserPrimeDB.GetByFiltersPaginated which will trigger the result defined by the following
// Then helper
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) When(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) *UserPrimeDBMockGetByFiltersPaginatedExpectation {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserPrimeDBMock.GetByFiltersPaginated mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetByFiltersPaginatedExpectation{
		mock:               mmGetByFiltersPaginated.mock,
		params:             &UserPrimeDBMockGetByFiltersPaginatedParams{userFilters, pagination},
		expectationOrigins: UserPrimeDBMockGetByFiltersPaginatedExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByFiltersPaginated.expectations = append(mmGetByFiltersPaginated.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetByFiltersPaginated return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetByFiltersPaginatedExpectation) Then(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetByFiltersPaginatedResults{p1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetByFiltersPaginated should be invoked
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Times(n uint64) *mUserPrimeDBMockGetByFiltersPaginated {
	if n == 0 {
		mmGetByFiltersPaginated.mock.t.Fatalf("Times of UserPrimeDBMock.GetByFiltersPaginated mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByFiltersPaginated.expectedInvocations, n)
	mmGetByFiltersPaginated.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersPaginated
}

func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) invocationsDone() bool {
	if len(mmGetByFiltersPaginated.expectations) == 0 && mmGetByFiltersPaginated.defaultExpectation == nil && mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByFiltersPaginated.mock.afterGetByFiltersPaginatedCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByFiltersPaginated.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByFiltersPaginated implements mm_repository.UserPrimeDB
func (mmGetByFiltersPaginated *UserPrimeDBMock) GetByFiltersPaginated(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error) {
	mm_atomic.AddUint64(&mmGetByFiltersPaginated.beforeGetByFiltersPaginatedCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByFiltersPaginated.afterGetByFiltersPaginatedCounter, 1)

	mmGetByFiltersPaginated.t.Helper()

	if mmGetByFiltersPaginated.inspectFuncGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.inspectFuncGetByFiltersPaginated(userFilters, pagination)
	}

	mm_params := UserPrimeDBMockGetByFiltersPaginatedParams{userFilters, pagination}

	// Record call args
	mmGetByFiltersPaginated.GetByFiltersPaginatedMock.mutex.Lock()
	mmGetByFiltersPaginated.GetByFiltersPaginatedMock.callArgs = append(mmGetByFiltersPaginated.GetByFiltersPaginatedMock.callArgs, &mm_params)
	mmGetByFiltersPaginated.GetByFiltersPaginatedMock.mutex.Unlock()

	for _, e := range mmGetByFiltersPaginated.GetByFiltersPaginatedMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.params
		mm_want_ptrs := mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetByFiltersPaginatedParams{userFilters, pagination}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userFilters != nil && !minimock.Equal(*mm_want_ptrs.userFilters, mm_got.userFilters) {
				mmGetByFiltersPaginated.t.Errorf("UserPrimeDBMock.GetByFiltersPaginated got unexpected parameter userFilters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.originUserFilters, *mm_want_ptrs.userFilters, mm_got.userFilters, minimock.Diff(*mm_want_ptrs.userFilters, mm_got.userFilters))
			}

			if mm_want_ptrs.pagination != nil && !minimock.Equal(*mm_want_ptrs.pagination, mm_got.pagination) {
				mmGetByFiltersPaginated.t.Errorf("UserPrimeDBMock.GetByFiltersPaginated got unexpected parameter pagination, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.originPagination, *mm_want_ptrs.pagination, mm_got.pagination, minimock.Diff(*mm_want_ptrs.pagination, mm_got.pagination))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByFiltersPaginated.t.Errorf("UserPrimeDBMock.GetByFiltersPaginated got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByFiltersPaginated.t.Fatal("No results are set for the UserPrimeDBMock.GetByFiltersPaginated")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByFiltersPaginated.funcGetByFiltersPaginated != nil {
		return mmGetByFiltersPaginated.funcGetByFiltersPaginated(userFilters, pagination)
	}
	mmGetByFiltersPaginated.t.Fatalf("Unexpected call to UserPrimeDBMock.GetByFiltersPaginated. %v %v", userFilters, pagination)
	return
}

// GetByFiltersPaginatedAfterCounter returns a count of finished UserPrimeDBMock.GetByFiltersPaginated invocations
func (mmGetByFiltersPaginated *UserPrimeDBMock) GetByFiltersPaginatedAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFiltersPaginated.afterGetByFiltersPaginatedCounter)
}

// GetByFiltersPaginatedBeforeCounter returns a count of UserPrimeDBMock.GetByFiltersPaginated invocations
func (mmGetByFiltersPaginated *UserPrimeDBMock) GetByFiltersPaginatedBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFiltersPaginated.beforeGetByFiltersPaginatedCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetByFiltersPaginated.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByFiltersPaginated *mUserPrimeDBMockGetByFiltersPaginated) Calls() []*UserPrimeDBMockGetByFiltersPaginatedParams {
	mmGetByFiltersPaginated.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetByFiltersPaginatedParams, len(mmGetByFiltersPaginated.callArgs))
	copy(argCopy, mmGetByFiltersPaginated.callArgs)

	mmGetByFiltersPaginated.mutex.RUnlock()

	return argCopy
}

// MinimockGetByFiltersPaginatedDone returns true if the count of the GetByFiltersPaginated invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetByFiltersPaginatedDone() bool {
	if m.GetByFiltersPaginatedMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByFiltersPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByFiltersPaginatedMock.invocationsDone()
}

// MinimockGetByFiltersPaginatedInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetByFiltersPaginatedInspect() {
	for _, e := range m.GetByFiltersPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByFiltersPaginated at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByFiltersPaginatedCounter := mm_atomic.LoadUint64(&m.afterGetByFiltersPaginatedCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByFiltersPaginatedMock.defaultExpectation != nil && afterGetByFiltersPaginatedCounter < 1 {
		if m.GetByFiltersPaginatedMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByFiltersPaginated at\n%s", m.GetByFiltersPaginatedMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByFiltersPaginated at\n%s with params: %#v", m.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.origin, *m.GetByFiltersPaginatedMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByFiltersPaginated != nil && afterGetByFiltersPaginatedCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetByFiltersPaginated at\n%s", m.funcGetByFiltersPaginatedOrigin)
	}

	if !m.GetByFiltersPaginatedMock.invocationsDone() && afterGetByFiltersPaginatedCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetByFiltersPaginated at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByFiltersPaginatedMock.expectedInvocations), m.GetByFiltersPaginatedMock.expectedInvocationsOrigin, afterGetByFiltersPaginatedCounter)
	}
}

type mUserPrimeDBMockGetByGroupID struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetByGroupIDExpectation
	expectations       []*UserPrimeDBMockGetByGroupIDExpectation

	callArgs []*UserPrimeDBMockGetByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetByGroupIDExpectation specifies expectation struct of the UserPrimeDB.GetByGroupID
type UserPrimeDBMockGetByGroupIDExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetByGroupIDParams
	paramPtrs          *UserPrimeDBMockGetByGroupIDParamPtrs
	expectationOrigins UserPrimeDBMockGetByGroupIDExpectationOrigins
	results            *UserPrimeDBMockGetByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetByGroupIDParams contains parameters of the UserPrimeDB.GetByGroupID
type UserPrimeDBMockGetByGroupIDParams struct {
	id int64
}

// UserPrimeDBMockGetByGroupIDParamPtrs contains pointers to parameters of the UserPrimeDB.GetByGroupID
type UserPrimeDBMockGetByGroupIDParamPtrs struct {
	id *int64
}

// UserPrimeDBMockGetByGroupIDResults contains results of the UserPrimeDB.GetByGroupID
type UserPrimeDBMockGetByGroupIDResults struct {
	ua1 []userentity.User
	err error
}

// UserPrimeDBMockGetByGroupIDOrigins contains origins of expectations of the UserPrimeDB.GetByGroupID
type UserPrimeDBMockGetByGroupIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Optional() *mUserPrimeDBMockGetByGroupID {
	mmGetByGroupID.optional = true
	return mmGetByGroupID
}

// Expect sets up expected params for UserPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Expect(id int64) *mUserPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &UserPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupID.mock.t.Fatalf("UserPrimeDBMock.GetByGroupID mock is already set by ExpectParams functions")
	}

	mmGetByGroupID.defaultExpectation.params = &UserPrimeDBMockGetByGroupIDParams{id}
	mmGetByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupID.expectations {
		if minimock.Equal(e.params, mmGetByGroupID.defaultExpectation.params) {
			mmGetByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupID.defaultExpectation.params)
		}
	}

	return mmGetByGroupID
}

// ExpectIdParam1 sets up expected param id for UserPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) ExpectIdParam1(id int64) *mUserPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &UserPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.params != nil {
		mmGetByGroupID.mock.t.Fatalf("UserPrimeDBMock.GetByGroupID mock is already set by Expect")
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupID.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByGroupIDParamPtrs{}
	}
	mmGetByGroupID.defaultExpectation.paramPtrs.id = &id
	mmGetByGroupID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByGroupID
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Inspect(f func(id int64)) *mUserPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetByGroupID")
	}

	mmGetByGroupID.mock.inspectFuncGetByGroupID = f

	return mmGetByGroupID
}

// Return sets up results that will be returned by UserPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &UserPrimeDBMockGetByGroupIDExpectation{mock: mmGetByGroupID.mock}
	}
	mmGetByGroupID.defaultExpectation.results = &UserPrimeDBMockGetByGroupIDResults{ua1, err}
	mmGetByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// Set uses given function f to mock the UserPrimeDB.GetByGroupID method
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Set(f func(id int64) (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetByGroupID.defaultExpectation != nil {
		mmGetByGroupID.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetByGroupID method")
	}

	if len(mmGetByGroupID.expectations) > 0 {
		mmGetByGroupID.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetByGroupID method")
	}

	mmGetByGroupID.mock.funcGetByGroupID = f
	mmGetByGroupID.mock.funcGetByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// When sets expectation for the UserPrimeDB.GetByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) When(id int64) *UserPrimeDBMockGetByGroupIDExpectation {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetByGroupIDExpectation{
		mock:               mmGetByGroupID.mock,
		params:             &UserPrimeDBMockGetByGroupIDParams{id},
		expectationOrigins: UserPrimeDBMockGetByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupID.expectations = append(mmGetByGroupID.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetByGroupID return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetByGroupIDExpectation) Then(ua1 []userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetByGroupIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetByGroupID should be invoked
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Times(n uint64) *mUserPrimeDBMockGetByGroupID {
	if n == 0 {
		mmGetByGroupID.mock.t.Fatalf("Times of UserPrimeDBMock.GetByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupID.expectedInvocations, n)
	mmGetByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID
}

func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) invocationsDone() bool {
	if len(mmGetByGroupID.expectations) == 0 && mmGetByGroupID.defaultExpectation == nil && mmGetByGroupID.mock.funcGetByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.mock.afterGetByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupID implements mm_repository.UserPrimeDB
func (mmGetByGroupID *UserPrimeDBMock) GetByGroupID(id int64) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByGroupID.beforeGetByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupID.afterGetByGroupIDCounter, 1)

	mmGetByGroupID.t.Helper()

	if mmGetByGroupID.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.inspectFuncGetByGroupID(id)
	}

	mm_params := UserPrimeDBMockGetByGroupIDParams{id}

	// Record call args
	mmGetByGroupID.GetByGroupIDMock.mutex.Lock()
	mmGetByGroupID.GetByGroupIDMock.callArgs = append(mmGetByGroupID.GetByGroupIDMock.callArgs, &mm_params)
	mmGetByGroupID.GetByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupID.GetByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByGroupID.GetByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupID.GetByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetByGroupIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByGroupID.t.Errorf("UserPrimeDBMock.GetByGroupID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupID.t.Errorf("UserPrimeDBMock.GetByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupID.t.Fatal("No results are set for the UserPrimeDBMock.GetByGroupID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByGroupID.funcGetByGroupID != nil {
		return mmGetByGroupID.funcGetByGroupID(id)
	}
	mmGetByGroupID.t.Fatalf("Unexpected call to UserPrimeDBMock.GetByGroupID. %v", id)
	return
}

// GetByGroupIDAfterCounter returns a count of finished UserPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *UserPrimeDBMock) GetByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.afterGetByGroupIDCounter)
}

// GetByGroupIDBeforeCounter returns a count of UserPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *UserPrimeDBMock) GetByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.beforeGetByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupID *mUserPrimeDBMockGetByGroupID) Calls() []*UserPrimeDBMockGetByGroupIDParams {
	mmGetByGroupID.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetByGroupIDParams, len(mmGetByGroupID.callArgs))
	copy(argCopy, mmGetByGroupID.callArgs)

	mmGetByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDDone returns true if the count of the GetByGroupID invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetByGroupIDDone() bool {
	if m.GetByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDMock.invocationsDone()
}

// MinimockGetByGroupIDInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetByGroupIDInspect() {
	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDMock.defaultExpectation != nil && afterGetByGroupIDCounter < 1 {
		if m.GetByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByGroupID at\n%s", m.GetByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByGroupID at\n%s with params: %#v", m.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupID != nil && afterGetByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetByGroupID at\n%s", m.funcGetByGroupIDOrigin)
	}

	if !m.GetByGroupIDMock.invocationsDone() && afterGetByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDMock.expectedInvocations), m.GetByGroupIDMock.expectedInvocationsOrigin, afterGetByGroupIDCounter)
	}
}

type mUserPrimeDBMockGetByID struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetByIDExpectation
	expectations       []*UserPrimeDBMockGetByIDExpectation

	callArgs []*UserPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetByIDExpectation specifies expectation struct of the UserPrimeDB.GetByID
type UserPrimeDBMockGetByIDExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetByIDParams
	paramPtrs          *UserPrimeDBMockGetByIDParamPtrs
	expectationOrigins UserPrimeDBMockGetByIDExpectationOrigins
	results            *UserPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetByIDParams contains parameters of the UserPrimeDB.GetByID
type UserPrimeDBMockGetByIDParams struct {
	id int64
}

// UserPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the UserPrimeDB.GetByID
type UserPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// UserPrimeDBMockGetByIDResults contains results of the UserPrimeDB.GetByID
type UserPrimeDBMockGetByIDResults struct {
	u1  userentity.User
	err error
}

// UserPrimeDBMockGetByIDOrigins contains origins of expectations of the UserPrimeDB.GetByID
type UserPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mUserPrimeDBMockGetByID) Optional() *mUserPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for UserPrimeDB.GetByID
func (mmGetByID *mUserPrimeDBMockGetByID) Expect(id int64) *mUserPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("UserPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &UserPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for UserPrimeDB.GetByID
func (mmGetByID *mUserPrimeDBMockGetByID) ExpectIdParam1(id int64) *mUserPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("UserPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &UserPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetByID
func (mmGetByID *mUserPrimeDBMockGetByID) Inspect(f func(id int64)) *mUserPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by UserPrimeDB.GetByID
func (mmGetByID *mUserPrimeDBMockGetByID) Return(u1 userentity.User, err error) *UserPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &UserPrimeDBMockGetByIDResults{u1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the UserPrimeDB.GetByID method
func (mmGetByID *mUserPrimeDBMockGetByID) Set(f func(id int64) (u1 userentity.User, err error)) *UserPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the UserPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mUserPrimeDBMockGetByID) When(id int64) *UserPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &UserPrimeDBMockGetByIDParams{id},
		expectationOrigins: UserPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetByIDExpectation) Then(u1 userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetByIDResults{u1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetByID should be invoked
func (mmGetByID *mUserPrimeDBMockGetByID) Times(n uint64) *mUserPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of UserPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mUserPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.UserPrimeDB
func (mmGetByID *UserPrimeDBMock) GetByID(id int64) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := UserPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("UserPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("UserPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the UserPrimeDBMock.GetByID")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to UserPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished UserPrimeDBMock.GetByID invocations
func (mmGetByID *UserPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of UserPrimeDBMock.GetByID invocations
func (mmGetByID *UserPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mUserPrimeDBMockGetByID) Calls() []*UserPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mUserPrimeDBMockGetUsersByParticipantGroupID struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetUsersByParticipantGroupIDExpectation
	expectations       []*UserPrimeDBMockGetUsersByParticipantGroupIDExpectation

	callArgs []*UserPrimeDBMockGetUsersByParticipantGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetUsersByParticipantGroupIDExpectation specifies expectation struct of the UserPrimeDB.GetUsersByParticipantGroupID
type UserPrimeDBMockGetUsersByParticipantGroupIDExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetUsersByParticipantGroupIDParams
	paramPtrs          *UserPrimeDBMockGetUsersByParticipantGroupIDParamPtrs
	expectationOrigins UserPrimeDBMockGetUsersByParticipantGroupIDExpectationOrigins
	results            *UserPrimeDBMockGetUsersByParticipantGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetUsersByParticipantGroupIDParams contains parameters of the UserPrimeDB.GetUsersByParticipantGroupID
type UserPrimeDBMockGetUsersByParticipantGroupIDParams struct {
	groupID int64
}

// UserPrimeDBMockGetUsersByParticipantGroupIDParamPtrs contains pointers to parameters of the UserPrimeDB.GetUsersByParticipantGroupID
type UserPrimeDBMockGetUsersByParticipantGroupIDParamPtrs struct {
	groupID *int64
}

// UserPrimeDBMockGetUsersByParticipantGroupIDResults contains results of the UserPrimeDB.GetUsersByParticipantGroupID
type UserPrimeDBMockGetUsersByParticipantGroupIDResults struct {
	ua1 []userentity.User
	err error
}

// UserPrimeDBMockGetUsersByParticipantGroupIDOrigins contains origins of expectations of the UserPrimeDB.GetUsersByParticipantGroupID
type UserPrimeDBMockGetUsersByParticipantGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Optional() *mUserPrimeDBMockGetUsersByParticipantGroupID {
	mmGetUsersByParticipantGroupID.optional = true
	return mmGetUsersByParticipantGroupID
}

// Expect sets up expected params for UserPrimeDB.GetUsersByParticipantGroupID
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Expect(groupID int64) *mUserPrimeDBMockGetUsersByParticipantGroupID {
	if mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupID != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantGroupID mock is already set by Set")
	}

	if mmGetUsersByParticipantGroupID.defaultExpectation == nil {
		mmGetUsersByParticipantGroupID.defaultExpectation = &UserPrimeDBMockGetUsersByParticipantGroupIDExpectation{}
	}

	if mmGetUsersByParticipantGroupID.defaultExpectation.paramPtrs != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantGroupID mock is already set by ExpectParams functions")
	}

	mmGetUsersByParticipantGroupID.defaultExpectation.params = &UserPrimeDBMockGetUsersByParticipantGroupIDParams{groupID}
	mmGetUsersByParticipantGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersByParticipantGroupID.expectations {
		if minimock.Equal(e.params, mmGetUsersByParticipantGroupID.defaultExpectation.params) {
			mmGetUsersByParticipantGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersByParticipantGroupID.defaultExpectation.params)
		}
	}

	return mmGetUsersByParticipantGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for UserPrimeDB.GetUsersByParticipantGroupID
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) ExpectGroupIDParam1(groupID int64) *mUserPrimeDBMockGetUsersByParticipantGroupID {
	if mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupID != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantGroupID mock is already set by Set")
	}

	if mmGetUsersByParticipantGroupID.defaultExpectation == nil {
		mmGetUsersByParticipantGroupID.defaultExpectation = &UserPrimeDBMockGetUsersByParticipantGroupIDExpectation{}
	}

	if mmGetUsersByParticipantGroupID.defaultExpectation.params != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantGroupID mock is already set by Expect")
	}

	if mmGetUsersByParticipantGroupID.defaultExpectation.paramPtrs == nil {
		mmGetUsersByParticipantGroupID.defaultExpectation.paramPtrs = &UserPrimeDBMockGetUsersByParticipantGroupIDParamPtrs{}
	}
	mmGetUsersByParticipantGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetUsersByParticipantGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetUsersByParticipantGroupID
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetUsersByParticipantGroupID
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Inspect(f func(groupID int64)) *mUserPrimeDBMockGetUsersByParticipantGroupID {
	if mmGetUsersByParticipantGroupID.mock.inspectFuncGetUsersByParticipantGroupID != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetUsersByParticipantGroupID")
	}

	mmGetUsersByParticipantGroupID.mock.inspectFuncGetUsersByParticipantGroupID = f

	return mmGetUsersByParticipantGroupID
}

// Return sets up results that will be returned by UserPrimeDB.GetUsersByParticipantGroupID
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupID != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantGroupID mock is already set by Set")
	}

	if mmGetUsersByParticipantGroupID.defaultExpectation == nil {
		mmGetUsersByParticipantGroupID.defaultExpectation = &UserPrimeDBMockGetUsersByParticipantGroupIDExpectation{mock: mmGetUsersByParticipantGroupID.mock}
	}
	mmGetUsersByParticipantGroupID.defaultExpectation.results = &UserPrimeDBMockGetUsersByParticipantGroupIDResults{ua1, err}
	mmGetUsersByParticipantGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersByParticipantGroupID.mock
}

// Set uses given function f to mock the UserPrimeDB.GetUsersByParticipantGroupID method
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Set(f func(groupID int64) (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetUsersByParticipantGroupID.defaultExpectation != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetUsersByParticipantGroupID method")
	}

	if len(mmGetUsersByParticipantGroupID.expectations) > 0 {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetUsersByParticipantGroupID method")
	}

	mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupID = f
	mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetUsersByParticipantGroupID.mock
}

// When sets expectation for the UserPrimeDB.GetUsersByParticipantGroupID which will trigger the result defined by the following
// Then helper
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) When(groupID int64) *UserPrimeDBMockGetUsersByParticipantGroupIDExpectation {
	if mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupID != nil {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantGroupID mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetUsersByParticipantGroupIDExpectation{
		mock:               mmGetUsersByParticipantGroupID.mock,
		params:             &UserPrimeDBMockGetUsersByParticipantGroupIDParams{groupID},
		expectationOrigins: UserPrimeDBMockGetUsersByParticipantGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersByParticipantGroupID.expectations = append(mmGetUsersByParticipantGroupID.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetUsersByParticipantGroupID return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetUsersByParticipantGroupIDExpectation) Then(ua1 []userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetUsersByParticipantGroupIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetUsersByParticipantGroupID should be invoked
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Times(n uint64) *mUserPrimeDBMockGetUsersByParticipantGroupID {
	if n == 0 {
		mmGetUsersByParticipantGroupID.mock.t.Fatalf("Times of UserPrimeDBMock.GetUsersByParticipantGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersByParticipantGroupID.expectedInvocations, n)
	mmGetUsersByParticipantGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersByParticipantGroupID
}

func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) invocationsDone() bool {
	if len(mmGetUsersByParticipantGroupID.expectations) == 0 && mmGetUsersByParticipantGroupID.defaultExpectation == nil && mmGetUsersByParticipantGroupID.mock.funcGetUsersByParticipantGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersByParticipantGroupID.mock.afterGetUsersByParticipantGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersByParticipantGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersByParticipantGroupID implements mm_repository.UserPrimeDB
func (mmGetUsersByParticipantGroupID *UserPrimeDBMock) GetUsersByParticipantGroupID(groupID int64) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetUsersByParticipantGroupID.beforeGetUsersByParticipantGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersByParticipantGroupID.afterGetUsersByParticipantGroupIDCounter, 1)

	mmGetUsersByParticipantGroupID.t.Helper()

	if mmGetUsersByParticipantGroupID.inspectFuncGetUsersByParticipantGroupID != nil {
		mmGetUsersByParticipantGroupID.inspectFuncGetUsersByParticipantGroupID(groupID)
	}

	mm_params := UserPrimeDBMockGetUsersByParticipantGroupIDParams{groupID}

	// Record call args
	mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.mutex.Lock()
	mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.callArgs = append(mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.callArgs, &mm_params)
	mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.mutex.Unlock()

	for _, e := range mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetUsersByParticipantGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetUsersByParticipantGroupID.t.Errorf("UserPrimeDBMock.GetUsersByParticipantGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersByParticipantGroupID.t.Errorf("UserPrimeDBMock.GetUsersByParticipantGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersByParticipantGroupID.GetUsersByParticipantGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersByParticipantGroupID.t.Fatal("No results are set for the UserPrimeDBMock.GetUsersByParticipantGroupID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUsersByParticipantGroupID.funcGetUsersByParticipantGroupID != nil {
		return mmGetUsersByParticipantGroupID.funcGetUsersByParticipantGroupID(groupID)
	}
	mmGetUsersByParticipantGroupID.t.Fatalf("Unexpected call to UserPrimeDBMock.GetUsersByParticipantGroupID. %v", groupID)
	return
}

// GetUsersByParticipantGroupIDAfterCounter returns a count of finished UserPrimeDBMock.GetUsersByParticipantGroupID invocations
func (mmGetUsersByParticipantGroupID *UserPrimeDBMock) GetUsersByParticipantGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByParticipantGroupID.afterGetUsersByParticipantGroupIDCounter)
}

// GetUsersByParticipantGroupIDBeforeCounter returns a count of UserPrimeDBMock.GetUsersByParticipantGroupID invocations
func (mmGetUsersByParticipantGroupID *UserPrimeDBMock) GetUsersByParticipantGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByParticipantGroupID.beforeGetUsersByParticipantGroupIDCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetUsersByParticipantGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersByParticipantGroupID *mUserPrimeDBMockGetUsersByParticipantGroupID) Calls() []*UserPrimeDBMockGetUsersByParticipantGroupIDParams {
	mmGetUsersByParticipantGroupID.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetUsersByParticipantGroupIDParams, len(mmGetUsersByParticipantGroupID.callArgs))
	copy(argCopy, mmGetUsersByParticipantGroupID.callArgs)

	mmGetUsersByParticipantGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersByParticipantGroupIDDone returns true if the count of the GetUsersByParticipantGroupID invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetUsersByParticipantGroupIDDone() bool {
	if m.GetUsersByParticipantGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersByParticipantGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersByParticipantGroupIDMock.invocationsDone()
}

// MinimockGetUsersByParticipantGroupIDInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetUsersByParticipantGroupIDInspect() {
	for _, e := range m.GetUsersByParticipantGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersByParticipantGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetUsersByParticipantGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersByParticipantGroupIDMock.defaultExpectation != nil && afterGetUsersByParticipantGroupIDCounter < 1 {
		if m.GetUsersByParticipantGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantGroupID at\n%s", m.GetUsersByParticipantGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantGroupID at\n%s with params: %#v", m.GetUsersByParticipantGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersByParticipantGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersByParticipantGroupID != nil && afterGetUsersByParticipantGroupIDCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantGroupID at\n%s", m.funcGetUsersByParticipantGroupIDOrigin)
	}

	if !m.GetUsersByParticipantGroupIDMock.invocationsDone() && afterGetUsersByParticipantGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetUsersByParticipantGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersByParticipantGroupIDMock.expectedInvocations), m.GetUsersByParticipantGroupIDMock.expectedInvocationsOrigin, afterGetUsersByParticipantGroupIDCounter)
	}
}

type mUserPrimeDBMockGetUsersByParticipantRoleID struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockGetUsersByParticipantRoleIDExpectation
	expectations       []*UserPrimeDBMockGetUsersByParticipantRoleIDExpectation

	callArgs []*UserPrimeDBMockGetUsersByParticipantRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockGetUsersByParticipantRoleIDExpectation specifies expectation struct of the UserPrimeDB.GetUsersByParticipantRoleID
type UserPrimeDBMockGetUsersByParticipantRoleIDExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockGetUsersByParticipantRoleIDParams
	paramPtrs          *UserPrimeDBMockGetUsersByParticipantRoleIDParamPtrs
	expectationOrigins UserPrimeDBMockGetUsersByParticipantRoleIDExpectationOrigins
	results            *UserPrimeDBMockGetUsersByParticipantRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockGetUsersByParticipantRoleIDParams contains parameters of the UserPrimeDB.GetUsersByParticipantRoleID
type UserPrimeDBMockGetUsersByParticipantRoleIDParams struct {
	roleID int64
}

// UserPrimeDBMockGetUsersByParticipantRoleIDParamPtrs contains pointers to parameters of the UserPrimeDB.GetUsersByParticipantRoleID
type UserPrimeDBMockGetUsersByParticipantRoleIDParamPtrs struct {
	roleID *int64
}

// UserPrimeDBMockGetUsersByParticipantRoleIDResults contains results of the UserPrimeDB.GetUsersByParticipantRoleID
type UserPrimeDBMockGetUsersByParticipantRoleIDResults struct {
	ua1 []userentity.User
	err error
}

// UserPrimeDBMockGetUsersByParticipantRoleIDOrigins contains origins of expectations of the UserPrimeDB.GetUsersByParticipantRoleID
type UserPrimeDBMockGetUsersByParticipantRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Optional() *mUserPrimeDBMockGetUsersByParticipantRoleID {
	mmGetUsersByParticipantRoleID.optional = true
	return mmGetUsersByParticipantRoleID
}

// Expect sets up expected params for UserPrimeDB.GetUsersByParticipantRoleID
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Expect(roleID int64) *mUserPrimeDBMockGetUsersByParticipantRoleID {
	if mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleID != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantRoleID mock is already set by Set")
	}

	if mmGetUsersByParticipantRoleID.defaultExpectation == nil {
		mmGetUsersByParticipantRoleID.defaultExpectation = &UserPrimeDBMockGetUsersByParticipantRoleIDExpectation{}
	}

	if mmGetUsersByParticipantRoleID.defaultExpectation.paramPtrs != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantRoleID mock is already set by ExpectParams functions")
	}

	mmGetUsersByParticipantRoleID.defaultExpectation.params = &UserPrimeDBMockGetUsersByParticipantRoleIDParams{roleID}
	mmGetUsersByParticipantRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersByParticipantRoleID.expectations {
		if minimock.Equal(e.params, mmGetUsersByParticipantRoleID.defaultExpectation.params) {
			mmGetUsersByParticipantRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersByParticipantRoleID.defaultExpectation.params)
		}
	}

	return mmGetUsersByParticipantRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for UserPrimeDB.GetUsersByParticipantRoleID
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) ExpectRoleIDParam1(roleID int64) *mUserPrimeDBMockGetUsersByParticipantRoleID {
	if mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleID != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantRoleID mock is already set by Set")
	}

	if mmGetUsersByParticipantRoleID.defaultExpectation == nil {
		mmGetUsersByParticipantRoleID.defaultExpectation = &UserPrimeDBMockGetUsersByParticipantRoleIDExpectation{}
	}

	if mmGetUsersByParticipantRoleID.defaultExpectation.params != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantRoleID mock is already set by Expect")
	}

	if mmGetUsersByParticipantRoleID.defaultExpectation.paramPtrs == nil {
		mmGetUsersByParticipantRoleID.defaultExpectation.paramPtrs = &UserPrimeDBMockGetUsersByParticipantRoleIDParamPtrs{}
	}
	mmGetUsersByParticipantRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetUsersByParticipantRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetUsersByParticipantRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.GetUsersByParticipantRoleID
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Inspect(f func(roleID int64)) *mUserPrimeDBMockGetUsersByParticipantRoleID {
	if mmGetUsersByParticipantRoleID.mock.inspectFuncGetUsersByParticipantRoleID != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.GetUsersByParticipantRoleID")
	}

	mmGetUsersByParticipantRoleID.mock.inspectFuncGetUsersByParticipantRoleID = f

	return mmGetUsersByParticipantRoleID
}

// Return sets up results that will be returned by UserPrimeDB.GetUsersByParticipantRoleID
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Return(ua1 []userentity.User, err error) *UserPrimeDBMock {
	if mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleID != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantRoleID mock is already set by Set")
	}

	if mmGetUsersByParticipantRoleID.defaultExpectation == nil {
		mmGetUsersByParticipantRoleID.defaultExpectation = &UserPrimeDBMockGetUsersByParticipantRoleIDExpectation{mock: mmGetUsersByParticipantRoleID.mock}
	}
	mmGetUsersByParticipantRoleID.defaultExpectation.results = &UserPrimeDBMockGetUsersByParticipantRoleIDResults{ua1, err}
	mmGetUsersByParticipantRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersByParticipantRoleID.mock
}

// Set uses given function f to mock the UserPrimeDB.GetUsersByParticipantRoleID method
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Set(f func(roleID int64) (ua1 []userentity.User, err error)) *UserPrimeDBMock {
	if mmGetUsersByParticipantRoleID.defaultExpectation != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.GetUsersByParticipantRoleID method")
	}

	if len(mmGetUsersByParticipantRoleID.expectations) > 0 {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.GetUsersByParticipantRoleID method")
	}

	mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleID = f
	mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetUsersByParticipantRoleID.mock
}

// When sets expectation for the UserPrimeDB.GetUsersByParticipantRoleID which will trigger the result defined by the following
// Then helper
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) When(roleID int64) *UserPrimeDBMockGetUsersByParticipantRoleIDExpectation {
	if mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleID != nil {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("UserPrimeDBMock.GetUsersByParticipantRoleID mock is already set by Set")
	}

	expectation := &UserPrimeDBMockGetUsersByParticipantRoleIDExpectation{
		mock:               mmGetUsersByParticipantRoleID.mock,
		params:             &UserPrimeDBMockGetUsersByParticipantRoleIDParams{roleID},
		expectationOrigins: UserPrimeDBMockGetUsersByParticipantRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersByParticipantRoleID.expectations = append(mmGetUsersByParticipantRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.GetUsersByParticipantRoleID return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockGetUsersByParticipantRoleIDExpectation) Then(ua1 []userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockGetUsersByParticipantRoleIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.GetUsersByParticipantRoleID should be invoked
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Times(n uint64) *mUserPrimeDBMockGetUsersByParticipantRoleID {
	if n == 0 {
		mmGetUsersByParticipantRoleID.mock.t.Fatalf("Times of UserPrimeDBMock.GetUsersByParticipantRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersByParticipantRoleID.expectedInvocations, n)
	mmGetUsersByParticipantRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersByParticipantRoleID
}

func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) invocationsDone() bool {
	if len(mmGetUsersByParticipantRoleID.expectations) == 0 && mmGetUsersByParticipantRoleID.defaultExpectation == nil && mmGetUsersByParticipantRoleID.mock.funcGetUsersByParticipantRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersByParticipantRoleID.mock.afterGetUsersByParticipantRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersByParticipantRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersByParticipantRoleID implements mm_repository.UserPrimeDB
func (mmGetUsersByParticipantRoleID *UserPrimeDBMock) GetUsersByParticipantRoleID(roleID int64) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetUsersByParticipantRoleID.beforeGetUsersByParticipantRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersByParticipantRoleID.afterGetUsersByParticipantRoleIDCounter, 1)

	mmGetUsersByParticipantRoleID.t.Helper()

	if mmGetUsersByParticipantRoleID.inspectFuncGetUsersByParticipantRoleID != nil {
		mmGetUsersByParticipantRoleID.inspectFuncGetUsersByParticipantRoleID(roleID)
	}

	mm_params := UserPrimeDBMockGetUsersByParticipantRoleIDParams{roleID}

	// Record call args
	mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.mutex.Lock()
	mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.callArgs = append(mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.callArgs, &mm_params)
	mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.mutex.Unlock()

	for _, e := range mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockGetUsersByParticipantRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetUsersByParticipantRoleID.t.Errorf("UserPrimeDBMock.GetUsersByParticipantRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersByParticipantRoleID.t.Errorf("UserPrimeDBMock.GetUsersByParticipantRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersByParticipantRoleID.GetUsersByParticipantRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersByParticipantRoleID.t.Fatal("No results are set for the UserPrimeDBMock.GetUsersByParticipantRoleID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUsersByParticipantRoleID.funcGetUsersByParticipantRoleID != nil {
		return mmGetUsersByParticipantRoleID.funcGetUsersByParticipantRoleID(roleID)
	}
	mmGetUsersByParticipantRoleID.t.Fatalf("Unexpected call to UserPrimeDBMock.GetUsersByParticipantRoleID. %v", roleID)
	return
}

// GetUsersByParticipantRoleIDAfterCounter returns a count of finished UserPrimeDBMock.GetUsersByParticipantRoleID invocations
func (mmGetUsersByParticipantRoleID *UserPrimeDBMock) GetUsersByParticipantRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByParticipantRoleID.afterGetUsersByParticipantRoleIDCounter)
}

// GetUsersByParticipantRoleIDBeforeCounter returns a count of UserPrimeDBMock.GetUsersByParticipantRoleID invocations
func (mmGetUsersByParticipantRoleID *UserPrimeDBMock) GetUsersByParticipantRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByParticipantRoleID.beforeGetUsersByParticipantRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.GetUsersByParticipantRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersByParticipantRoleID *mUserPrimeDBMockGetUsersByParticipantRoleID) Calls() []*UserPrimeDBMockGetUsersByParticipantRoleIDParams {
	mmGetUsersByParticipantRoleID.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockGetUsersByParticipantRoleIDParams, len(mmGetUsersByParticipantRoleID.callArgs))
	copy(argCopy, mmGetUsersByParticipantRoleID.callArgs)

	mmGetUsersByParticipantRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersByParticipantRoleIDDone returns true if the count of the GetUsersByParticipantRoleID invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockGetUsersByParticipantRoleIDDone() bool {
	if m.GetUsersByParticipantRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersByParticipantRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersByParticipantRoleIDMock.invocationsDone()
}

// MinimockGetUsersByParticipantRoleIDInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockGetUsersByParticipantRoleIDInspect() {
	for _, e := range m.GetUsersByParticipantRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersByParticipantRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetUsersByParticipantRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersByParticipantRoleIDMock.defaultExpectation != nil && afterGetUsersByParticipantRoleIDCounter < 1 {
		if m.GetUsersByParticipantRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantRoleID at\n%s", m.GetUsersByParticipantRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantRoleID at\n%s with params: %#v", m.GetUsersByParticipantRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersByParticipantRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersByParticipantRoleID != nil && afterGetUsersByParticipantRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.GetUsersByParticipantRoleID at\n%s", m.funcGetUsersByParticipantRoleIDOrigin)
	}

	if !m.GetUsersByParticipantRoleIDMock.invocationsDone() && afterGetUsersByParticipantRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.GetUsersByParticipantRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersByParticipantRoleIDMock.expectedInvocations), m.GetUsersByParticipantRoleIDMock.expectedInvocationsOrigin, afterGetUsersByParticipantRoleIDCounter)
	}
}

type mUserPrimeDBMockUpdate struct {
	optional           bool
	mock               *UserPrimeDBMock
	defaultExpectation *UserPrimeDBMockUpdateExpectation
	expectations       []*UserPrimeDBMockUpdateExpectation

	callArgs []*UserPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserPrimeDBMockUpdateExpectation specifies expectation struct of the UserPrimeDB.Update
type UserPrimeDBMockUpdateExpectation struct {
	mock               *UserPrimeDBMock
	params             *UserPrimeDBMockUpdateParams
	paramPtrs          *UserPrimeDBMockUpdateParamPtrs
	expectationOrigins UserPrimeDBMockUpdateExpectationOrigins
	results            *UserPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// UserPrimeDBMockUpdateParams contains parameters of the UserPrimeDB.Update
type UserPrimeDBMockUpdateParams struct {
	user userentity.UserUpdateData
}

// UserPrimeDBMockUpdateParamPtrs contains pointers to parameters of the UserPrimeDB.Update
type UserPrimeDBMockUpdateParamPtrs struct {
	user *userentity.UserUpdateData
}

// UserPrimeDBMockUpdateResults contains results of the UserPrimeDB.Update
type UserPrimeDBMockUpdateResults struct {
	u1  userentity.User
	err error
}

// UserPrimeDBMockUpdateOrigins contains origins of expectations of the UserPrimeDB.Update
type UserPrimeDBMockUpdateExpectationOrigins struct {
	origin     string
	originUser string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mUserPrimeDBMockUpdate) Optional() *mUserPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for UserPrimeDB.Update
func (mmUpdate *mUserPrimeDBMockUpdate) Expect(user userentity.UserUpdateData) *mUserPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("UserPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &UserPrimeDBMockUpdateParams{user}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectUserParam1 sets up expected param user for UserPrimeDB.Update
func (mmUpdate *mUserPrimeDBMockUpdate) ExpectUserParam1(user userentity.UserUpdateData) *mUserPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("UserPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &UserPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.user = &user
	mmUpdate.defaultExpectation.expectationOrigins.originUser = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the UserPrimeDB.Update
func (mmUpdate *mUserPrimeDBMockUpdate) Inspect(f func(user userentity.UserUpdateData)) *mUserPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for UserPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by UserPrimeDB.Update
func (mmUpdate *mUserPrimeDBMockUpdate) Return(u1 userentity.User, err error) *UserPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &UserPrimeDBMockUpdateResults{u1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the UserPrimeDB.Update method
func (mmUpdate *mUserPrimeDBMockUpdate) Set(f func(user userentity.UserUpdateData) (u1 userentity.User, err error)) *UserPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the UserPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the UserPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the UserPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mUserPrimeDBMockUpdate) When(user userentity.UserUpdateData) *UserPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &UserPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &UserPrimeDBMockUpdateParams{user},
		expectationOrigins: UserPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up UserPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *UserPrimeDBMockUpdateExpectation) Then(u1 userentity.User, err error) *UserPrimeDBMock {
	e.results = &UserPrimeDBMockUpdateResults{u1, err}
	return e.mock
}

// Times sets number of times UserPrimeDB.Update should be invoked
func (mmUpdate *mUserPrimeDBMockUpdate) Times(n uint64) *mUserPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of UserPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mUserPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.UserPrimeDB
func (mmUpdate *UserPrimeDBMock) Update(user userentity.UserUpdateData) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(user)
	}

	mm_params := UserPrimeDBMockUpdateParams{user}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := UserPrimeDBMockUpdateParams{user}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.user != nil && !minimock.Equal(*mm_want_ptrs.user, mm_got.user) {
				mmUpdate.t.Errorf("UserPrimeDBMock.Update got unexpected parameter user, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originUser, *mm_want_ptrs.user, mm_got.user, minimock.Diff(*mm_want_ptrs.user, mm_got.user))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("UserPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the UserPrimeDBMock.Update")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(user)
	}
	mmUpdate.t.Fatalf("Unexpected call to UserPrimeDBMock.Update. %v", user)
	return
}

// UpdateAfterCounter returns a count of finished UserPrimeDBMock.Update invocations
func (mmUpdate *UserPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of UserPrimeDBMock.Update invocations
func (mmUpdate *UserPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to UserPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mUserPrimeDBMockUpdate) Calls() []*UserPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*UserPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *UserPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *UserPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to UserPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *UserPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockGetAdminsInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllPaginatedInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByEmailInspect()

			m.MinimockGetByFiltersInspect()

			m.MinimockGetByFiltersPaginatedInspect()

			m.MinimockGetByGroupIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetUsersByParticipantGroupIDInspect()

			m.MinimockGetUsersByParticipantRoleIDInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *UserPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *UserPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockGetAdminsDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllPaginatedDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByEmailDone() &&
		m.MinimockGetByFiltersDone() &&
		m.MinimockGetByFiltersPaginatedDone() &&
		m.MinimockGetByGroupIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetUsersByParticipantGroupIDDone() &&
		m.MinimockGetUsersByParticipantRoleIDDone() &&
		m.MinimockUpdateDone()
}
