// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/service.UserDomainService -o user_domain_service_mock.go -n UserDomainServiceMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// UserDomainServiceMock implements mm_service.UserDomainService
type UserDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, data userentity.User) (u1 userentity.User, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, data userentity.User)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mUserDomainServiceMockCreate

	funcCreateByEmail          func(ctx context.Context, email string) (u1 userentity.User, err error)
	funcCreateByEmailOrigin    string
	inspectFuncCreateByEmail   func(ctx context.Context, email string)
	afterCreateByEmailCounter  uint64
	beforeCreateByEmailCounter uint64
	CreateByEmailMock          mUserDomainServiceMockCreateByEmail

	funcDeleteAsAdmin          func(ctx context.Context, id int64) (err error)
	funcDeleteAsAdminOrigin    string
	inspectFuncDeleteAsAdmin   func(ctx context.Context, id int64)
	afterDeleteAsAdminCounter  uint64
	beforeDeleteAsAdminCounter uint64
	DeleteAsAdminMock          mUserDomainServiceMockDeleteAsAdmin

	funcDeleteByUserID          func(ctx context.Context, userID int64) (err error)
	funcDeleteByUserIDOrigin    string
	inspectFuncDeleteByUserID   func(ctx context.Context, userID int64)
	afterDeleteByUserIDCounter  uint64
	beforeDeleteByUserIDCounter uint64
	DeleteByUserIDMock          mUserDomainServiceMockDeleteByUserID

	funcDeleteByUserIDAndGroupIDs          func(ctx context.Context, userID int64, groupIDs []int64) (err error)
	funcDeleteByUserIDAndGroupIDsOrigin    string
	inspectFuncDeleteByUserIDAndGroupIDs   func(ctx context.Context, userID int64, groupIDs []int64)
	afterDeleteByUserIDAndGroupIDsCounter  uint64
	beforeDeleteByUserIDAndGroupIDsCounter uint64
	DeleteByUserIDAndGroupIDsMock          mUserDomainServiceMockDeleteByUserIDAndGroupIDs

	funcDeleteByUserIDAndRoleIDs          func(ctx context.Context, userID int64, roleIDs []int64) (err error)
	funcDeleteByUserIDAndRoleIDsOrigin    string
	inspectFuncDeleteByUserIDAndRoleIDs   func(ctx context.Context, userID int64, roleIDs []int64)
	afterDeleteByUserIDAndRoleIDsCounter  uint64
	beforeDeleteByUserIDAndRoleIDsCounter uint64
	DeleteByUserIDAndRoleIDsMock          mUserDomainServiceMockDeleteByUserIDAndRoleIDs

	funcGetAdmins          func() (ua1 []userentity.User, err error)
	funcGetAdminsOrigin    string
	inspectFuncGetAdmins   func()
	afterGetAdminsCounter  uint64
	beforeGetAdminsCounter uint64
	GetAdminsMock          mUserDomainServiceMockGetAdmins

	funcGetAll          func() (ua1 []userentity.User, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mUserDomainServiceMockGetAll

	funcGetAllAsAdmin          func() (aa1 []userentity.AdminUser, err error)
	funcGetAllAsAdminOrigin    string
	inspectFuncGetAllAsAdmin   func()
	afterGetAllAsAdminCounter  uint64
	beforeGetAllAsAdminCounter uint64
	GetAllAsAdminMock          mUserDomainServiceMockGetAllAsAdmin

	funcGetAllPaginated          func(pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)
	funcGetAllPaginatedOrigin    string
	inspectFuncGetAllPaginated   func(pagination sharedentity.PaginationParams)
	afterGetAllPaginatedCounter  uint64
	beforeGetAllPaginatedCounter uint64
	GetAllPaginatedMock          mUserDomainServiceMockGetAllPaginated

	funcGetByCategoryID          func(categoryID int64) (ua1 []userentity.User, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mUserDomainServiceMockGetByCategoryID

	funcGetByEmail          func(email string) (u1 userentity.User, err error)
	funcGetByEmailOrigin    string
	inspectFuncGetByEmail   func(email string)
	afterGetByEmailCounter  uint64
	beforeGetByEmailCounter uint64
	GetByEmailMock          mUserDomainServiceMockGetByEmail

	funcGetByFilters          func(userFilters userentity.UserFiltersData) (ua1 []userentity.User, err error)
	funcGetByFiltersOrigin    string
	inspectFuncGetByFilters   func(userFilters userentity.UserFiltersData)
	afterGetByFiltersCounter  uint64
	beforeGetByFiltersCounter uint64
	GetByFiltersMock          mUserDomainServiceMockGetByFilters

	funcGetByFiltersPaginated          func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)
	funcGetByFiltersPaginatedOrigin    string
	inspectFuncGetByFiltersPaginated   func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams)
	afterGetByFiltersPaginatedCounter  uint64
	beforeGetByFiltersPaginatedCounter uint64
	GetByFiltersPaginatedMock          mUserDomainServiceMockGetByFiltersPaginated

	funcGetByID          func(id int64) (u1 userentity.User, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mUserDomainServiceMockGetByID

	funcGetUserGroups          func(userID int64) (ia1 []int64, err error)
	funcGetUserGroupsOrigin    string
	inspectFuncGetUserGroups   func(userID int64)
	afterGetUserGroupsCounter  uint64
	beforeGetUserGroupsCounter uint64
	GetUserGroupsMock          mUserDomainServiceMockGetUserGroups

	funcGetUserGroupsByGroupID          func(groupID int64) (ua1 []userentity.UserWithProduct, err error)
	funcGetUserGroupsByGroupIDOrigin    string
	inspectFuncGetUserGroupsByGroupID   func(groupID int64)
	afterGetUserGroupsByGroupIDCounter  uint64
	beforeGetUserGroupsByGroupIDCounter uint64
	GetUserGroupsByGroupIDMock          mUserDomainServiceMockGetUserGroupsByGroupID

	funcGetUserGroupsByUserID          func(id int64) (ua1 []userentity.UserGroup, err error)
	funcGetUserGroupsByUserIDOrigin    string
	inspectFuncGetUserGroupsByUserID   func(id int64)
	afterGetUserGroupsByUserIDCounter  uint64
	beforeGetUserGroupsByUserIDCounter uint64
	GetUserGroupsByUserIDMock          mUserDomainServiceMockGetUserGroupsByUserID

	funcGetUserRolesByRoleID          func(roleID int64) (ua1 []userentity.User, err error)
	funcGetUserRolesByRoleIDOrigin    string
	inspectFuncGetUserRolesByRoleID   func(roleID int64)
	afterGetUserRolesByRoleIDCounter  uint64
	beforeGetUserRolesByRoleIDCounter uint64
	GetUserRolesByRoleIDMock          mUserDomainServiceMockGetUserRolesByRoleID

	funcGetUserRolesByUserID          func(id int64) (ua1 []userentity.UserRole, err error)
	funcGetUserRolesByUserIDOrigin    string
	inspectFuncGetUserRolesByUserID   func(id int64)
	afterGetUserRolesByUserIDCounter  uint64
	beforeGetUserRolesByUserIDCounter uint64
	GetUserRolesByUserIDMock          mUserDomainServiceMockGetUserRolesByUserID

	funcGetUsersWithProductsByGroupID          func(groupID int64) (ua1 []userentity.UserWithProduct, err error)
	funcGetUsersWithProductsByGroupIDOrigin    string
	inspectFuncGetUsersWithProductsByGroupID   func(groupID int64)
	afterGetUsersWithProductsByGroupIDCounter  uint64
	beforeGetUsersWithProductsByGroupIDCounter uint64
	GetUsersWithProductsByGroupIDMock          mUserDomainServiceMockGetUsersWithProductsByGroupID

	funcGetUsersWithProductsByRoleID          func(roleID int64) (ua1 []userentity.UserWithProduct, err error)
	funcGetUsersWithProductsByRoleIDOrigin    string
	inspectFuncGetUsersWithProductsByRoleID   func(roleID int64)
	afterGetUsersWithProductsByRoleIDCounter  uint64
	beforeGetUsersWithProductsByRoleIDCounter uint64
	GetUsersWithProductsByRoleIDMock          mUserDomainServiceMockGetUsersWithProductsByRoleID

	funcUpdate          func(data userentity.UserUpdateData) (u1 userentity.User, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(data userentity.UserUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mUserDomainServiceMockUpdate

	funcUpdateLinksWithGroups          func(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) (err error)
	funcUpdateLinksWithGroupsOrigin    string
	inspectFuncUpdateLinksWithGroups   func(ctx context.Context, userID int64, groups []groupentity.GroupProductLink)
	afterUpdateLinksWithGroupsCounter  uint64
	beforeUpdateLinksWithGroupsCounter uint64
	UpdateLinksWithGroupsMock          mUserDomainServiceMockUpdateLinksWithGroups

	funcUpdateLinksWithProducts          func(ctx context.Context, userID int64, productIDs []int64) (err error)
	funcUpdateLinksWithProductsOrigin    string
	inspectFuncUpdateLinksWithProducts   func(ctx context.Context, userID int64, productIDs []int64)
	afterUpdateLinksWithProductsCounter  uint64
	beforeUpdateLinksWithProductsCounter uint64
	UpdateLinksWithProductsMock          mUserDomainServiceMockUpdateLinksWithProducts

	funcUpdateLinksWithRoles          func(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) (err error)
	funcUpdateLinksWithRolesOrigin    string
	inspectFuncUpdateLinksWithRoles   func(ctx context.Context, userID int64, roles []roleentity.RoleProductLink)
	afterUpdateLinksWithRolesCounter  uint64
	beforeUpdateLinksWithRolesCounter uint64
	UpdateLinksWithRolesMock          mUserDomainServiceMockUpdateLinksWithRoles
}

// NewUserDomainServiceMock returns a mock for mm_service.UserDomainService
func NewUserDomainServiceMock(t minimock.Tester) *UserDomainServiceMock {
	m := &UserDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mUserDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*UserDomainServiceMockCreateParams{}

	m.CreateByEmailMock = mUserDomainServiceMockCreateByEmail{mock: m}
	m.CreateByEmailMock.callArgs = []*UserDomainServiceMockCreateByEmailParams{}

	m.DeleteAsAdminMock = mUserDomainServiceMockDeleteAsAdmin{mock: m}
	m.DeleteAsAdminMock.callArgs = []*UserDomainServiceMockDeleteAsAdminParams{}

	m.DeleteByUserIDMock = mUserDomainServiceMockDeleteByUserID{mock: m}
	m.DeleteByUserIDMock.callArgs = []*UserDomainServiceMockDeleteByUserIDParams{}

	m.DeleteByUserIDAndGroupIDsMock = mUserDomainServiceMockDeleteByUserIDAndGroupIDs{mock: m}
	m.DeleteByUserIDAndGroupIDsMock.callArgs = []*UserDomainServiceMockDeleteByUserIDAndGroupIDsParams{}

	m.DeleteByUserIDAndRoleIDsMock = mUserDomainServiceMockDeleteByUserIDAndRoleIDs{mock: m}
	m.DeleteByUserIDAndRoleIDsMock.callArgs = []*UserDomainServiceMockDeleteByUserIDAndRoleIDsParams{}

	m.GetAdminsMock = mUserDomainServiceMockGetAdmins{mock: m}

	m.GetAllMock = mUserDomainServiceMockGetAll{mock: m}

	m.GetAllAsAdminMock = mUserDomainServiceMockGetAllAsAdmin{mock: m}

	m.GetAllPaginatedMock = mUserDomainServiceMockGetAllPaginated{mock: m}
	m.GetAllPaginatedMock.callArgs = []*UserDomainServiceMockGetAllPaginatedParams{}

	m.GetByCategoryIDMock = mUserDomainServiceMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*UserDomainServiceMockGetByCategoryIDParams{}

	m.GetByEmailMock = mUserDomainServiceMockGetByEmail{mock: m}
	m.GetByEmailMock.callArgs = []*UserDomainServiceMockGetByEmailParams{}

	m.GetByFiltersMock = mUserDomainServiceMockGetByFilters{mock: m}
	m.GetByFiltersMock.callArgs = []*UserDomainServiceMockGetByFiltersParams{}

	m.GetByFiltersPaginatedMock = mUserDomainServiceMockGetByFiltersPaginated{mock: m}
	m.GetByFiltersPaginatedMock.callArgs = []*UserDomainServiceMockGetByFiltersPaginatedParams{}

	m.GetByIDMock = mUserDomainServiceMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*UserDomainServiceMockGetByIDParams{}

	m.GetUserGroupsMock = mUserDomainServiceMockGetUserGroups{mock: m}
	m.GetUserGroupsMock.callArgs = []*UserDomainServiceMockGetUserGroupsParams{}

	m.GetUserGroupsByGroupIDMock = mUserDomainServiceMockGetUserGroupsByGroupID{mock: m}
	m.GetUserGroupsByGroupIDMock.callArgs = []*UserDomainServiceMockGetUserGroupsByGroupIDParams{}

	m.GetUserGroupsByUserIDMock = mUserDomainServiceMockGetUserGroupsByUserID{mock: m}
	m.GetUserGroupsByUserIDMock.callArgs = []*UserDomainServiceMockGetUserGroupsByUserIDParams{}

	m.GetUserRolesByRoleIDMock = mUserDomainServiceMockGetUserRolesByRoleID{mock: m}
	m.GetUserRolesByRoleIDMock.callArgs = []*UserDomainServiceMockGetUserRolesByRoleIDParams{}

	m.GetUserRolesByUserIDMock = mUserDomainServiceMockGetUserRolesByUserID{mock: m}
	m.GetUserRolesByUserIDMock.callArgs = []*UserDomainServiceMockGetUserRolesByUserIDParams{}

	m.GetUsersWithProductsByGroupIDMock = mUserDomainServiceMockGetUsersWithProductsByGroupID{mock: m}
	m.GetUsersWithProductsByGroupIDMock.callArgs = []*UserDomainServiceMockGetUsersWithProductsByGroupIDParams{}

	m.GetUsersWithProductsByRoleIDMock = mUserDomainServiceMockGetUsersWithProductsByRoleID{mock: m}
	m.GetUsersWithProductsByRoleIDMock.callArgs = []*UserDomainServiceMockGetUsersWithProductsByRoleIDParams{}

	m.UpdateMock = mUserDomainServiceMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*UserDomainServiceMockUpdateParams{}

	m.UpdateLinksWithGroupsMock = mUserDomainServiceMockUpdateLinksWithGroups{mock: m}
	m.UpdateLinksWithGroupsMock.callArgs = []*UserDomainServiceMockUpdateLinksWithGroupsParams{}

	m.UpdateLinksWithProductsMock = mUserDomainServiceMockUpdateLinksWithProducts{mock: m}
	m.UpdateLinksWithProductsMock.callArgs = []*UserDomainServiceMockUpdateLinksWithProductsParams{}

	m.UpdateLinksWithRolesMock = mUserDomainServiceMockUpdateLinksWithRoles{mock: m}
	m.UpdateLinksWithRolesMock.callArgs = []*UserDomainServiceMockUpdateLinksWithRolesParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mUserDomainServiceMockCreate struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockCreateExpectation
	expectations       []*UserDomainServiceMockCreateExpectation

	callArgs []*UserDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockCreateExpectation specifies expectation struct of the UserDomainService.Create
type UserDomainServiceMockCreateExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockCreateParams
	paramPtrs          *UserDomainServiceMockCreateParamPtrs
	expectationOrigins UserDomainServiceMockCreateExpectationOrigins
	results            *UserDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockCreateParams contains parameters of the UserDomainService.Create
type UserDomainServiceMockCreateParams struct {
	ctx  context.Context
	data userentity.User
}

// UserDomainServiceMockCreateParamPtrs contains pointers to parameters of the UserDomainService.Create
type UserDomainServiceMockCreateParamPtrs struct {
	ctx  *context.Context
	data *userentity.User
}

// UserDomainServiceMockCreateResults contains results of the UserDomainService.Create
type UserDomainServiceMockCreateResults struct {
	u1  userentity.User
	err error
}

// UserDomainServiceMockCreateOrigins contains origins of expectations of the UserDomainService.Create
type UserDomainServiceMockCreateExpectationOrigins struct {
	origin     string
	originCtx  string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mUserDomainServiceMockCreate) Optional() *mUserDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for UserDomainService.Create
func (mmCreate *mUserDomainServiceMockCreate) Expect(ctx context.Context, data userentity.User) *mUserDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &UserDomainServiceMockCreateParams{ctx, data}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.Create
func (mmCreate *mUserDomainServiceMockCreate) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectDataParam2 sets up expected param data for UserDomainService.Create
func (mmCreate *mUserDomainServiceMockCreate) ExpectDataParam2(data userentity.User) *mUserDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.data = &data
	mmCreate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.Create
func (mmCreate *mUserDomainServiceMockCreate) Inspect(f func(ctx context.Context, data userentity.User)) *mUserDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by UserDomainService.Create
func (mmCreate *mUserDomainServiceMockCreate) Return(u1 userentity.User, err error) *UserDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &UserDomainServiceMockCreateResults{u1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the UserDomainService.Create method
func (mmCreate *mUserDomainServiceMockCreate) Set(f func(ctx context.Context, data userentity.User) (u1 userentity.User, err error)) *UserDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the UserDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the UserDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the UserDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mUserDomainServiceMockCreate) When(ctx context.Context, data userentity.User) *UserDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &UserDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &UserDomainServiceMockCreateParams{ctx, data},
		expectationOrigins: UserDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.Create return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockCreateExpectation) Then(u1 userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockCreateResults{u1, err}
	return e.mock
}

// Times sets number of times UserDomainService.Create should be invoked
func (mmCreate *mUserDomainServiceMockCreate) Times(n uint64) *mUserDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of UserDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mUserDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.UserDomainService
func (mmCreate *UserDomainServiceMock) Create(ctx context.Context, data userentity.User) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, data)
	}

	mm_params := UserDomainServiceMockCreateParams{ctx, data}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockCreateParams{ctx, data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("UserDomainServiceMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreate.t.Errorf("UserDomainServiceMock.Create got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("UserDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the UserDomainServiceMock.Create")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, data)
	}
	mmCreate.t.Fatalf("Unexpected call to UserDomainServiceMock.Create. %v %v", ctx, data)
	return
}

// CreateAfterCounter returns a count of finished UserDomainServiceMock.Create invocations
func (mmCreate *UserDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of UserDomainServiceMock.Create invocations
func (mmCreate *UserDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mUserDomainServiceMockCreate) Calls() []*UserDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mUserDomainServiceMockCreateByEmail struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockCreateByEmailExpectation
	expectations       []*UserDomainServiceMockCreateByEmailExpectation

	callArgs []*UserDomainServiceMockCreateByEmailParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockCreateByEmailExpectation specifies expectation struct of the UserDomainService.CreateByEmail
type UserDomainServiceMockCreateByEmailExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockCreateByEmailParams
	paramPtrs          *UserDomainServiceMockCreateByEmailParamPtrs
	expectationOrigins UserDomainServiceMockCreateByEmailExpectationOrigins
	results            *UserDomainServiceMockCreateByEmailResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockCreateByEmailParams contains parameters of the UserDomainService.CreateByEmail
type UserDomainServiceMockCreateByEmailParams struct {
	ctx   context.Context
	email string
}

// UserDomainServiceMockCreateByEmailParamPtrs contains pointers to parameters of the UserDomainService.CreateByEmail
type UserDomainServiceMockCreateByEmailParamPtrs struct {
	ctx   *context.Context
	email *string
}

// UserDomainServiceMockCreateByEmailResults contains results of the UserDomainService.CreateByEmail
type UserDomainServiceMockCreateByEmailResults struct {
	u1  userentity.User
	err error
}

// UserDomainServiceMockCreateByEmailOrigins contains origins of expectations of the UserDomainService.CreateByEmail
type UserDomainServiceMockCreateByEmailExpectationOrigins struct {
	origin      string
	originCtx   string
	originEmail string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Optional() *mUserDomainServiceMockCreateByEmail {
	mmCreateByEmail.optional = true
	return mmCreateByEmail
}

// Expect sets up expected params for UserDomainService.CreateByEmail
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Expect(ctx context.Context, email string) *mUserDomainServiceMockCreateByEmail {
	if mmCreateByEmail.mock.funcCreateByEmail != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Set")
	}

	if mmCreateByEmail.defaultExpectation == nil {
		mmCreateByEmail.defaultExpectation = &UserDomainServiceMockCreateByEmailExpectation{}
	}

	if mmCreateByEmail.defaultExpectation.paramPtrs != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by ExpectParams functions")
	}

	mmCreateByEmail.defaultExpectation.params = &UserDomainServiceMockCreateByEmailParams{ctx, email}
	mmCreateByEmail.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByEmail.expectations {
		if minimock.Equal(e.params, mmCreateByEmail.defaultExpectation.params) {
			mmCreateByEmail.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByEmail.defaultExpectation.params)
		}
	}

	return mmCreateByEmail
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.CreateByEmail
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockCreateByEmail {
	if mmCreateByEmail.mock.funcCreateByEmail != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Set")
	}

	if mmCreateByEmail.defaultExpectation == nil {
		mmCreateByEmail.defaultExpectation = &UserDomainServiceMockCreateByEmailExpectation{}
	}

	if mmCreateByEmail.defaultExpectation.params != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Expect")
	}

	if mmCreateByEmail.defaultExpectation.paramPtrs == nil {
		mmCreateByEmail.defaultExpectation.paramPtrs = &UserDomainServiceMockCreateByEmailParamPtrs{}
	}
	mmCreateByEmail.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByEmail.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByEmail
}

// ExpectEmailParam2 sets up expected param email for UserDomainService.CreateByEmail
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) ExpectEmailParam2(email string) *mUserDomainServiceMockCreateByEmail {
	if mmCreateByEmail.mock.funcCreateByEmail != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Set")
	}

	if mmCreateByEmail.defaultExpectation == nil {
		mmCreateByEmail.defaultExpectation = &UserDomainServiceMockCreateByEmailExpectation{}
	}

	if mmCreateByEmail.defaultExpectation.params != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Expect")
	}

	if mmCreateByEmail.defaultExpectation.paramPtrs == nil {
		mmCreateByEmail.defaultExpectation.paramPtrs = &UserDomainServiceMockCreateByEmailParamPtrs{}
	}
	mmCreateByEmail.defaultExpectation.paramPtrs.email = &email
	mmCreateByEmail.defaultExpectation.expectationOrigins.originEmail = minimock.CallerInfo(1)

	return mmCreateByEmail
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.CreateByEmail
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Inspect(f func(ctx context.Context, email string)) *mUserDomainServiceMockCreateByEmail {
	if mmCreateByEmail.mock.inspectFuncCreateByEmail != nil {
		mmCreateByEmail.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.CreateByEmail")
	}

	mmCreateByEmail.mock.inspectFuncCreateByEmail = f

	return mmCreateByEmail
}

// Return sets up results that will be returned by UserDomainService.CreateByEmail
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Return(u1 userentity.User, err error) *UserDomainServiceMock {
	if mmCreateByEmail.mock.funcCreateByEmail != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Set")
	}

	if mmCreateByEmail.defaultExpectation == nil {
		mmCreateByEmail.defaultExpectation = &UserDomainServiceMockCreateByEmailExpectation{mock: mmCreateByEmail.mock}
	}
	mmCreateByEmail.defaultExpectation.results = &UserDomainServiceMockCreateByEmailResults{u1, err}
	mmCreateByEmail.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByEmail.mock
}

// Set uses given function f to mock the UserDomainService.CreateByEmail method
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Set(f func(ctx context.Context, email string) (u1 userentity.User, err error)) *UserDomainServiceMock {
	if mmCreateByEmail.defaultExpectation != nil {
		mmCreateByEmail.mock.t.Fatalf("Default expectation is already set for the UserDomainService.CreateByEmail method")
	}

	if len(mmCreateByEmail.expectations) > 0 {
		mmCreateByEmail.mock.t.Fatalf("Some expectations are already set for the UserDomainService.CreateByEmail method")
	}

	mmCreateByEmail.mock.funcCreateByEmail = f
	mmCreateByEmail.mock.funcCreateByEmailOrigin = minimock.CallerInfo(1)
	return mmCreateByEmail.mock
}

// When sets expectation for the UserDomainService.CreateByEmail which will trigger the result defined by the following
// Then helper
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) When(ctx context.Context, email string) *UserDomainServiceMockCreateByEmailExpectation {
	if mmCreateByEmail.mock.funcCreateByEmail != nil {
		mmCreateByEmail.mock.t.Fatalf("UserDomainServiceMock.CreateByEmail mock is already set by Set")
	}

	expectation := &UserDomainServiceMockCreateByEmailExpectation{
		mock:               mmCreateByEmail.mock,
		params:             &UserDomainServiceMockCreateByEmailParams{ctx, email},
		expectationOrigins: UserDomainServiceMockCreateByEmailExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByEmail.expectations = append(mmCreateByEmail.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.CreateByEmail return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockCreateByEmailExpectation) Then(u1 userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockCreateByEmailResults{u1, err}
	return e.mock
}

// Times sets number of times UserDomainService.CreateByEmail should be invoked
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Times(n uint64) *mUserDomainServiceMockCreateByEmail {
	if n == 0 {
		mmCreateByEmail.mock.t.Fatalf("Times of UserDomainServiceMock.CreateByEmail mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByEmail.expectedInvocations, n)
	mmCreateByEmail.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByEmail
}

func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) invocationsDone() bool {
	if len(mmCreateByEmail.expectations) == 0 && mmCreateByEmail.defaultExpectation == nil && mmCreateByEmail.mock.funcCreateByEmail == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByEmail.mock.afterCreateByEmailCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByEmail.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByEmail implements mm_service.UserDomainService
func (mmCreateByEmail *UserDomainServiceMock) CreateByEmail(ctx context.Context, email string) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmCreateByEmail.beforeCreateByEmailCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByEmail.afterCreateByEmailCounter, 1)

	mmCreateByEmail.t.Helper()

	if mmCreateByEmail.inspectFuncCreateByEmail != nil {
		mmCreateByEmail.inspectFuncCreateByEmail(ctx, email)
	}

	mm_params := UserDomainServiceMockCreateByEmailParams{ctx, email}

	// Record call args
	mmCreateByEmail.CreateByEmailMock.mutex.Lock()
	mmCreateByEmail.CreateByEmailMock.callArgs = append(mmCreateByEmail.CreateByEmailMock.callArgs, &mm_params)
	mmCreateByEmail.CreateByEmailMock.mutex.Unlock()

	for _, e := range mmCreateByEmail.CreateByEmailMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmCreateByEmail.CreateByEmailMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByEmail.CreateByEmailMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByEmail.CreateByEmailMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByEmail.CreateByEmailMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockCreateByEmailParams{ctx, email}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByEmail.t.Errorf("UserDomainServiceMock.CreateByEmail got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByEmail.CreateByEmailMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.email != nil && !minimock.Equal(*mm_want_ptrs.email, mm_got.email) {
				mmCreateByEmail.t.Errorf("UserDomainServiceMock.CreateByEmail got unexpected parameter email, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByEmail.CreateByEmailMock.defaultExpectation.expectationOrigins.originEmail, *mm_want_ptrs.email, mm_got.email, minimock.Diff(*mm_want_ptrs.email, mm_got.email))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByEmail.t.Errorf("UserDomainServiceMock.CreateByEmail got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByEmail.CreateByEmailMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByEmail.CreateByEmailMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByEmail.t.Fatal("No results are set for the UserDomainServiceMock.CreateByEmail")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmCreateByEmail.funcCreateByEmail != nil {
		return mmCreateByEmail.funcCreateByEmail(ctx, email)
	}
	mmCreateByEmail.t.Fatalf("Unexpected call to UserDomainServiceMock.CreateByEmail. %v %v", ctx, email)
	return
}

// CreateByEmailAfterCounter returns a count of finished UserDomainServiceMock.CreateByEmail invocations
func (mmCreateByEmail *UserDomainServiceMock) CreateByEmailAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByEmail.afterCreateByEmailCounter)
}

// CreateByEmailBeforeCounter returns a count of UserDomainServiceMock.CreateByEmail invocations
func (mmCreateByEmail *UserDomainServiceMock) CreateByEmailBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByEmail.beforeCreateByEmailCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.CreateByEmail.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByEmail *mUserDomainServiceMockCreateByEmail) Calls() []*UserDomainServiceMockCreateByEmailParams {
	mmCreateByEmail.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockCreateByEmailParams, len(mmCreateByEmail.callArgs))
	copy(argCopy, mmCreateByEmail.callArgs)

	mmCreateByEmail.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByEmailDone returns true if the count of the CreateByEmail invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockCreateByEmailDone() bool {
	if m.CreateByEmailMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByEmailMock.invocationsDone()
}

// MinimockCreateByEmailInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockCreateByEmailInspect() {
	for _, e := range m.CreateByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.CreateByEmail at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByEmailCounter := mm_atomic.LoadUint64(&m.afterCreateByEmailCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByEmailMock.defaultExpectation != nil && afterCreateByEmailCounter < 1 {
		if m.CreateByEmailMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.CreateByEmail at\n%s", m.CreateByEmailMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.CreateByEmail at\n%s with params: %#v", m.CreateByEmailMock.defaultExpectation.expectationOrigins.origin, *m.CreateByEmailMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByEmail != nil && afterCreateByEmailCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.CreateByEmail at\n%s", m.funcCreateByEmailOrigin)
	}

	if !m.CreateByEmailMock.invocationsDone() && afterCreateByEmailCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.CreateByEmail at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByEmailMock.expectedInvocations), m.CreateByEmailMock.expectedInvocationsOrigin, afterCreateByEmailCounter)
	}
}

type mUserDomainServiceMockDeleteAsAdmin struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockDeleteAsAdminExpectation
	expectations       []*UserDomainServiceMockDeleteAsAdminExpectation

	callArgs []*UserDomainServiceMockDeleteAsAdminParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockDeleteAsAdminExpectation specifies expectation struct of the UserDomainService.DeleteAsAdmin
type UserDomainServiceMockDeleteAsAdminExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockDeleteAsAdminParams
	paramPtrs          *UserDomainServiceMockDeleteAsAdminParamPtrs
	expectationOrigins UserDomainServiceMockDeleteAsAdminExpectationOrigins
	results            *UserDomainServiceMockDeleteAsAdminResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockDeleteAsAdminParams contains parameters of the UserDomainService.DeleteAsAdmin
type UserDomainServiceMockDeleteAsAdminParams struct {
	ctx context.Context
	id  int64
}

// UserDomainServiceMockDeleteAsAdminParamPtrs contains pointers to parameters of the UserDomainService.DeleteAsAdmin
type UserDomainServiceMockDeleteAsAdminParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// UserDomainServiceMockDeleteAsAdminResults contains results of the UserDomainService.DeleteAsAdmin
type UserDomainServiceMockDeleteAsAdminResults struct {
	err error
}

// UserDomainServiceMockDeleteAsAdminOrigins contains origins of expectations of the UserDomainService.DeleteAsAdmin
type UserDomainServiceMockDeleteAsAdminExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Optional() *mUserDomainServiceMockDeleteAsAdmin {
	mmDeleteAsAdmin.optional = true
	return mmDeleteAsAdmin
}

// Expect sets up expected params for UserDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Expect(ctx context.Context, id int64) *mUserDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &UserDomainServiceMockDeleteAsAdminExpectation{}
	}

	if mmDeleteAsAdmin.defaultExpectation.paramPtrs != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by ExpectParams functions")
	}

	mmDeleteAsAdmin.defaultExpectation.params = &UserDomainServiceMockDeleteAsAdminParams{ctx, id}
	mmDeleteAsAdmin.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteAsAdmin.expectations {
		if minimock.Equal(e.params, mmDeleteAsAdmin.defaultExpectation.params) {
			mmDeleteAsAdmin.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteAsAdmin.defaultExpectation.params)
		}
	}

	return mmDeleteAsAdmin
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &UserDomainServiceMockDeleteAsAdminExpectation{}
	}

	if mmDeleteAsAdmin.defaultExpectation.params != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Expect")
	}

	if mmDeleteAsAdmin.defaultExpectation.paramPtrs == nil {
		mmDeleteAsAdmin.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteAsAdminParamPtrs{}
	}
	mmDeleteAsAdmin.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteAsAdmin.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteAsAdmin
}

// ExpectIdParam2 sets up expected param id for UserDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) ExpectIdParam2(id int64) *mUserDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &UserDomainServiceMockDeleteAsAdminExpectation{}
	}

	if mmDeleteAsAdmin.defaultExpectation.params != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Expect")
	}

	if mmDeleteAsAdmin.defaultExpectation.paramPtrs == nil {
		mmDeleteAsAdmin.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteAsAdminParamPtrs{}
	}
	mmDeleteAsAdmin.defaultExpectation.paramPtrs.id = &id
	mmDeleteAsAdmin.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDeleteAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Inspect(f func(ctx context.Context, id int64)) *mUserDomainServiceMockDeleteAsAdmin {
	if mmDeleteAsAdmin.mock.inspectFuncDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.DeleteAsAdmin")
	}

	mmDeleteAsAdmin.mock.inspectFuncDeleteAsAdmin = f

	return mmDeleteAsAdmin
}

// Return sets up results that will be returned by UserDomainService.DeleteAsAdmin
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Return(err error) *UserDomainServiceMock {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	if mmDeleteAsAdmin.defaultExpectation == nil {
		mmDeleteAsAdmin.defaultExpectation = &UserDomainServiceMockDeleteAsAdminExpectation{mock: mmDeleteAsAdmin.mock}
	}
	mmDeleteAsAdmin.defaultExpectation.results = &UserDomainServiceMockDeleteAsAdminResults{err}
	mmDeleteAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteAsAdmin.mock
}

// Set uses given function f to mock the UserDomainService.DeleteAsAdmin method
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Set(f func(ctx context.Context, id int64) (err error)) *UserDomainServiceMock {
	if mmDeleteAsAdmin.defaultExpectation != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("Default expectation is already set for the UserDomainService.DeleteAsAdmin method")
	}

	if len(mmDeleteAsAdmin.expectations) > 0 {
		mmDeleteAsAdmin.mock.t.Fatalf("Some expectations are already set for the UserDomainService.DeleteAsAdmin method")
	}

	mmDeleteAsAdmin.mock.funcDeleteAsAdmin = f
	mmDeleteAsAdmin.mock.funcDeleteAsAdminOrigin = minimock.CallerInfo(1)
	return mmDeleteAsAdmin.mock
}

// When sets expectation for the UserDomainService.DeleteAsAdmin which will trigger the result defined by the following
// Then helper
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) When(ctx context.Context, id int64) *UserDomainServiceMockDeleteAsAdminExpectation {
	if mmDeleteAsAdmin.mock.funcDeleteAsAdmin != nil {
		mmDeleteAsAdmin.mock.t.Fatalf("UserDomainServiceMock.DeleteAsAdmin mock is already set by Set")
	}

	expectation := &UserDomainServiceMockDeleteAsAdminExpectation{
		mock:               mmDeleteAsAdmin.mock,
		params:             &UserDomainServiceMockDeleteAsAdminParams{ctx, id},
		expectationOrigins: UserDomainServiceMockDeleteAsAdminExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteAsAdmin.expectations = append(mmDeleteAsAdmin.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.DeleteAsAdmin return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockDeleteAsAdminExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockDeleteAsAdminResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.DeleteAsAdmin should be invoked
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Times(n uint64) *mUserDomainServiceMockDeleteAsAdmin {
	if n == 0 {
		mmDeleteAsAdmin.mock.t.Fatalf("Times of UserDomainServiceMock.DeleteAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteAsAdmin.expectedInvocations, n)
	mmDeleteAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteAsAdmin
}

func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) invocationsDone() bool {
	if len(mmDeleteAsAdmin.expectations) == 0 && mmDeleteAsAdmin.defaultExpectation == nil && mmDeleteAsAdmin.mock.funcDeleteAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteAsAdmin.mock.afterDeleteAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteAsAdmin implements mm_service.UserDomainService
func (mmDeleteAsAdmin *UserDomainServiceMock) DeleteAsAdmin(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteAsAdmin.beforeDeleteAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteAsAdmin.afterDeleteAsAdminCounter, 1)

	mmDeleteAsAdmin.t.Helper()

	if mmDeleteAsAdmin.inspectFuncDeleteAsAdmin != nil {
		mmDeleteAsAdmin.inspectFuncDeleteAsAdmin(ctx, id)
	}

	mm_params := UserDomainServiceMockDeleteAsAdminParams{ctx, id}

	// Record call args
	mmDeleteAsAdmin.DeleteAsAdminMock.mutex.Lock()
	mmDeleteAsAdmin.DeleteAsAdminMock.callArgs = append(mmDeleteAsAdmin.DeleteAsAdminMock.callArgs, &mm_params)
	mmDeleteAsAdmin.DeleteAsAdminMock.mutex.Unlock()

	for _, e := range mmDeleteAsAdmin.DeleteAsAdminMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockDeleteAsAdminParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteAsAdmin.t.Errorf("UserDomainServiceMock.DeleteAsAdmin got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDeleteAsAdmin.t.Errorf("UserDomainServiceMock.DeleteAsAdmin got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteAsAdmin.t.Errorf("UserDomainServiceMock.DeleteAsAdmin got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteAsAdmin.DeleteAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteAsAdmin.t.Fatal("No results are set for the UserDomainServiceMock.DeleteAsAdmin")
		}
		return (*mm_results).err
	}
	if mmDeleteAsAdmin.funcDeleteAsAdmin != nil {
		return mmDeleteAsAdmin.funcDeleteAsAdmin(ctx, id)
	}
	mmDeleteAsAdmin.t.Fatalf("Unexpected call to UserDomainServiceMock.DeleteAsAdmin. %v %v", ctx, id)
	return
}

// DeleteAsAdminAfterCounter returns a count of finished UserDomainServiceMock.DeleteAsAdmin invocations
func (mmDeleteAsAdmin *UserDomainServiceMock) DeleteAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteAsAdmin.afterDeleteAsAdminCounter)
}

// DeleteAsAdminBeforeCounter returns a count of UserDomainServiceMock.DeleteAsAdmin invocations
func (mmDeleteAsAdmin *UserDomainServiceMock) DeleteAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteAsAdmin.beforeDeleteAsAdminCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.DeleteAsAdmin.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteAsAdmin *mUserDomainServiceMockDeleteAsAdmin) Calls() []*UserDomainServiceMockDeleteAsAdminParams {
	mmDeleteAsAdmin.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockDeleteAsAdminParams, len(mmDeleteAsAdmin.callArgs))
	copy(argCopy, mmDeleteAsAdmin.callArgs)

	mmDeleteAsAdmin.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteAsAdminDone returns true if the count of the DeleteAsAdmin invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockDeleteAsAdminDone() bool {
	if m.DeleteAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteAsAdminMock.invocationsDone()
}

// MinimockDeleteAsAdminInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockDeleteAsAdminInspect() {
	for _, e := range m.DeleteAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteAsAdmin at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteAsAdminCounter := mm_atomic.LoadUint64(&m.afterDeleteAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteAsAdminMock.defaultExpectation != nil && afterDeleteAsAdminCounter < 1 {
		if m.DeleteAsAdminMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteAsAdmin at\n%s", m.DeleteAsAdminMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteAsAdmin at\n%s with params: %#v", m.DeleteAsAdminMock.defaultExpectation.expectationOrigins.origin, *m.DeleteAsAdminMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteAsAdmin != nil && afterDeleteAsAdminCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.DeleteAsAdmin at\n%s", m.funcDeleteAsAdminOrigin)
	}

	if !m.DeleteAsAdminMock.invocationsDone() && afterDeleteAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.DeleteAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteAsAdminMock.expectedInvocations), m.DeleteAsAdminMock.expectedInvocationsOrigin, afterDeleteAsAdminCounter)
	}
}

type mUserDomainServiceMockDeleteByUserID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockDeleteByUserIDExpectation
	expectations       []*UserDomainServiceMockDeleteByUserIDExpectation

	callArgs []*UserDomainServiceMockDeleteByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockDeleteByUserIDExpectation specifies expectation struct of the UserDomainService.DeleteByUserID
type UserDomainServiceMockDeleteByUserIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockDeleteByUserIDParams
	paramPtrs          *UserDomainServiceMockDeleteByUserIDParamPtrs
	expectationOrigins UserDomainServiceMockDeleteByUserIDExpectationOrigins
	results            *UserDomainServiceMockDeleteByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockDeleteByUserIDParams contains parameters of the UserDomainService.DeleteByUserID
type UserDomainServiceMockDeleteByUserIDParams struct {
	ctx    context.Context
	userID int64
}

// UserDomainServiceMockDeleteByUserIDParamPtrs contains pointers to parameters of the UserDomainService.DeleteByUserID
type UserDomainServiceMockDeleteByUserIDParamPtrs struct {
	ctx    *context.Context
	userID *int64
}

// UserDomainServiceMockDeleteByUserIDResults contains results of the UserDomainService.DeleteByUserID
type UserDomainServiceMockDeleteByUserIDResults struct {
	err error
}

// UserDomainServiceMockDeleteByUserIDOrigins contains origins of expectations of the UserDomainService.DeleteByUserID
type UserDomainServiceMockDeleteByUserIDExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Optional() *mUserDomainServiceMockDeleteByUserID {
	mmDeleteByUserID.optional = true
	return mmDeleteByUserID
}

// Expect sets up expected params for UserDomainService.DeleteByUserID
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Expect(ctx context.Context, userID int64) *mUserDomainServiceMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserDomainServiceMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by ExpectParams functions")
	}

	mmDeleteByUserID.defaultExpectation.params = &UserDomainServiceMockDeleteByUserIDParams{ctx, userID}
	mmDeleteByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserID.expectations {
		if minimock.Equal(e.params, mmDeleteByUserID.defaultExpectation.params) {
			mmDeleteByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserID.defaultExpectation.params)
		}
	}

	return mmDeleteByUserID
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.DeleteByUserID
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserDomainServiceMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.params != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Expect")
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserID.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDParamPtrs{}
	}
	mmDeleteByUserID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserID
}

// ExpectUserIDParam2 sets up expected param userID for UserDomainService.DeleteByUserID
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) ExpectUserIDParam2(userID int64) *mUserDomainServiceMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserDomainServiceMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.params != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Expect")
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserID.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDParamPtrs{}
	}
	mmDeleteByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.DeleteByUserID
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Inspect(f func(ctx context.Context, userID int64)) *mUserDomainServiceMockDeleteByUserID {
	if mmDeleteByUserID.mock.inspectFuncDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.DeleteByUserID")
	}

	mmDeleteByUserID.mock.inspectFuncDeleteByUserID = f

	return mmDeleteByUserID
}

// Return sets up results that will be returned by UserDomainService.DeleteByUserID
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Return(err error) *UserDomainServiceMock {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserDomainServiceMockDeleteByUserIDExpectation{mock: mmDeleteByUserID.mock}
	}
	mmDeleteByUserID.defaultExpectation.results = &UserDomainServiceMockDeleteByUserIDResults{err}
	mmDeleteByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID.mock
}

// Set uses given function f to mock the UserDomainService.DeleteByUserID method
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Set(f func(ctx context.Context, userID int64) (err error)) *UserDomainServiceMock {
	if mmDeleteByUserID.defaultExpectation != nil {
		mmDeleteByUserID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.DeleteByUserID method")
	}

	if len(mmDeleteByUserID.expectations) > 0 {
		mmDeleteByUserID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.DeleteByUserID method")
	}

	mmDeleteByUserID.mock.funcDeleteByUserID = f
	mmDeleteByUserID.mock.funcDeleteByUserIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID.mock
}

// When sets expectation for the UserDomainService.DeleteByUserID which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) When(ctx context.Context, userID int64) *UserDomainServiceMockDeleteByUserIDExpectation {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockDeleteByUserIDExpectation{
		mock:               mmDeleteByUserID.mock,
		params:             &UserDomainServiceMockDeleteByUserIDParams{ctx, userID},
		expectationOrigins: UserDomainServiceMockDeleteByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserID.expectations = append(mmDeleteByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.DeleteByUserID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockDeleteByUserIDExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockDeleteByUserIDResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.DeleteByUserID should be invoked
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Times(n uint64) *mUserDomainServiceMockDeleteByUserID {
	if n == 0 {
		mmDeleteByUserID.mock.t.Fatalf("Times of UserDomainServiceMock.DeleteByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserID.expectedInvocations, n)
	mmDeleteByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID
}

func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) invocationsDone() bool {
	if len(mmDeleteByUserID.expectations) == 0 && mmDeleteByUserID.defaultExpectation == nil && mmDeleteByUserID.mock.funcDeleteByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserID.mock.afterDeleteByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserID implements mm_service.UserDomainService
func (mmDeleteByUserID *UserDomainServiceMock) DeleteByUserID(ctx context.Context, userID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserID.beforeDeleteByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserID.afterDeleteByUserIDCounter, 1)

	mmDeleteByUserID.t.Helper()

	if mmDeleteByUserID.inspectFuncDeleteByUserID != nil {
		mmDeleteByUserID.inspectFuncDeleteByUserID(ctx, userID)
	}

	mm_params := UserDomainServiceMockDeleteByUserIDParams{ctx, userID}

	// Record call args
	mmDeleteByUserID.DeleteByUserIDMock.mutex.Lock()
	mmDeleteByUserID.DeleteByUserIDMock.callArgs = append(mmDeleteByUserID.DeleteByUserIDMock.callArgs, &mm_params)
	mmDeleteByUserID.DeleteByUserIDMock.mutex.Unlock()

	for _, e := range mmDeleteByUserID.DeleteByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockDeleteByUserIDParams{ctx, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserID.t.Errorf("UserDomainServiceMock.DeleteByUserID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserID.t.Errorf("UserDomainServiceMock.DeleteByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserID.t.Errorf("UserDomainServiceMock.DeleteByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserID.t.Fatal("No results are set for the UserDomainServiceMock.DeleteByUserID")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserID.funcDeleteByUserID != nil {
		return mmDeleteByUserID.funcDeleteByUserID(ctx, userID)
	}
	mmDeleteByUserID.t.Fatalf("Unexpected call to UserDomainServiceMock.DeleteByUserID. %v %v", ctx, userID)
	return
}

// DeleteByUserIDAfterCounter returns a count of finished UserDomainServiceMock.DeleteByUserID invocations
func (mmDeleteByUserID *UserDomainServiceMock) DeleteByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserID.afterDeleteByUserIDCounter)
}

// DeleteByUserIDBeforeCounter returns a count of UserDomainServiceMock.DeleteByUserID invocations
func (mmDeleteByUserID *UserDomainServiceMock) DeleteByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserID.beforeDeleteByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.DeleteByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserID *mUserDomainServiceMockDeleteByUserID) Calls() []*UserDomainServiceMockDeleteByUserIDParams {
	mmDeleteByUserID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockDeleteByUserIDParams, len(mmDeleteByUserID.callArgs))
	copy(argCopy, mmDeleteByUserID.callArgs)

	mmDeleteByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDDone returns true if the count of the DeleteByUserID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockDeleteByUserIDDone() bool {
	if m.DeleteByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDMock.invocationsDone()
}

// MinimockDeleteByUserIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockDeleteByUserIDInspect() {
	for _, e := range m.DeleteByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDMock.defaultExpectation != nil && afterDeleteByUserIDCounter < 1 {
		if m.DeleteByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserID at\n%s", m.DeleteByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserID at\n%s with params: %#v", m.DeleteByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserID != nil && afterDeleteByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserID at\n%s", m.funcDeleteByUserIDOrigin)
	}

	if !m.DeleteByUserIDMock.invocationsDone() && afterDeleteByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.DeleteByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDMock.expectedInvocations), m.DeleteByUserIDMock.expectedInvocationsOrigin, afterDeleteByUserIDCounter)
	}
}

type mUserDomainServiceMockDeleteByUserIDAndGroupIDs struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation
	expectations       []*UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation

	callArgs []*UserDomainServiceMockDeleteByUserIDAndGroupIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation specifies expectation struct of the UserDomainService.DeleteByUserIDAndGroupIDs
type UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockDeleteByUserIDAndGroupIDsParams
	paramPtrs          *UserDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs
	expectationOrigins UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectationOrigins
	results            *UserDomainServiceMockDeleteByUserIDAndGroupIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockDeleteByUserIDAndGroupIDsParams contains parameters of the UserDomainService.DeleteByUserIDAndGroupIDs
type UserDomainServiceMockDeleteByUserIDAndGroupIDsParams struct {
	ctx      context.Context
	userID   int64
	groupIDs []int64
}

// UserDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs contains pointers to parameters of the UserDomainService.DeleteByUserIDAndGroupIDs
type UserDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs struct {
	ctx      *context.Context
	userID   *int64
	groupIDs *[]int64
}

// UserDomainServiceMockDeleteByUserIDAndGroupIDsResults contains results of the UserDomainService.DeleteByUserIDAndGroupIDs
type UserDomainServiceMockDeleteByUserIDAndGroupIDsResults struct {
	err error
}

// UserDomainServiceMockDeleteByUserIDAndGroupIDsOrigins contains origins of expectations of the UserDomainService.DeleteByUserIDAndGroupIDs
type UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectationOrigins struct {
	origin         string
	originCtx      string
	originUserID   string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Optional() *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	mmDeleteByUserIDAndGroupIDs.optional = true
	return mmDeleteByUserIDAndGroupIDs
}

// Expect sets up expected params for UserDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Expect(ctx context.Context, userID int64, groupIDs []int64) *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndGroupIDs.defaultExpectation.params = &UserDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndGroupIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndGroupIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndGroupIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectUserIDParam2 sets up expected param userID for UserDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) ExpectUserIDParam2(userID int64) *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for UserDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) ExpectGroupIDsParam3(groupIDs []int64) *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Inspect(f func(ctx context.Context, userID int64, groupIDs []int64)) *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.DeleteByUserIDAndGroupIDs")
	}

	mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs = f

	return mmDeleteByUserIDAndGroupIDs
}

// Return sets up results that will be returned by UserDomainService.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Return(err error) *UserDomainServiceMock {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{mock: mmDeleteByUserIDAndGroupIDs.mock}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.results = &UserDomainServiceMockDeleteByUserIDAndGroupIDsResults{err}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// Set uses given function f to mock the UserDomainService.DeleteByUserIDAndGroupIDs method
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Set(f func(ctx context.Context, userID int64, groupIDs []int64) (err error)) *UserDomainServiceMock {
	if mmDeleteByUserIDAndGroupIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Default expectation is already set for the UserDomainService.DeleteByUserIDAndGroupIDs method")
	}

	if len(mmDeleteByUserIDAndGroupIDs.expectations) > 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Some expectations are already set for the UserDomainService.DeleteByUserIDAndGroupIDs method")
	}

	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs = f
	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// When sets expectation for the UserDomainService.DeleteByUserIDAndGroupIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) When(ctx context.Context, userID int64, groupIDs []int64) *UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	expectation := &UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation{
		mock:               mmDeleteByUserIDAndGroupIDs.mock,
		params:             &UserDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs},
		expectationOrigins: UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndGroupIDs.expectations = append(mmDeleteByUserIDAndGroupIDs.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.DeleteByUserIDAndGroupIDs return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockDeleteByUserIDAndGroupIDsExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockDeleteByUserIDAndGroupIDsResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.DeleteByUserIDAndGroupIDs should be invoked
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Times(n uint64) *mUserDomainServiceMockDeleteByUserIDAndGroupIDs {
	if n == 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Times of UserDomainServiceMock.DeleteByUserIDAndGroupIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations, n)
	mmDeleteByUserIDAndGroupIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs
}

func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndGroupIDs.expectations) == 0 && mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil && mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.mock.afterDeleteByUserIDAndGroupIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndGroupIDs implements mm_service.UserDomainService
func (mmDeleteByUserIDAndGroupIDs *UserDomainServiceMock) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter, 1)

	mmDeleteByUserIDAndGroupIDs.t.Helper()

	if mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}

	mm_params := UserDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

	// Record call args
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Lock()
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs = append(mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndGroupIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndGroupIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndGroupIDs.t.Fatal("No results are set for the UserDomainServiceMock.DeleteByUserIDAndGroupIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs != nil {
		return mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}
	mmDeleteByUserIDAndGroupIDs.t.Fatalf("Unexpected call to UserDomainServiceMock.DeleteByUserIDAndGroupIDs. %v %v %v", ctx, userID, groupIDs)
	return
}

// DeleteByUserIDAndGroupIDsAfterCounter returns a count of finished UserDomainServiceMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *UserDomainServiceMock) DeleteByUserIDAndGroupIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter)
}

// DeleteByUserIDAndGroupIDsBeforeCounter returns a count of UserDomainServiceMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *UserDomainServiceMock) DeleteByUserIDAndGroupIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.DeleteByUserIDAndGroupIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndGroupIDs *mUserDomainServiceMockDeleteByUserIDAndGroupIDs) Calls() []*UserDomainServiceMockDeleteByUserIDAndGroupIDsParams {
	mmDeleteByUserIDAndGroupIDs.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockDeleteByUserIDAndGroupIDsParams, len(mmDeleteByUserIDAndGroupIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndGroupIDs.callArgs)

	mmDeleteByUserIDAndGroupIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndGroupIDsDone returns true if the count of the DeleteByUserIDAndGroupIDs invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockDeleteByUserIDAndGroupIDsDone() bool {
	if m.DeleteByUserIDAndGroupIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndGroupIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndGroupIDsInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockDeleteByUserIDAndGroupIDsInspect() {
	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndGroupIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndGroupIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndGroupIDs != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s", m.funcDeleteByUserIDAndGroupIDsOrigin)
	}

	if !m.DeleteByUserIDAndGroupIDsMock.invocationsDone() && afterDeleteByUserIDAndGroupIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.DeleteByUserIDAndGroupIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndGroupIDsMock.expectedInvocations), m.DeleteByUserIDAndGroupIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndGroupIDsCounter)
	}
}

type mUserDomainServiceMockDeleteByUserIDAndRoleIDs struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation
	expectations       []*UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation

	callArgs []*UserDomainServiceMockDeleteByUserIDAndRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation specifies expectation struct of the UserDomainService.DeleteByUserIDAndRoleIDs
type UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockDeleteByUserIDAndRoleIDsParams
	paramPtrs          *UserDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs
	expectationOrigins UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectationOrigins
	results            *UserDomainServiceMockDeleteByUserIDAndRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockDeleteByUserIDAndRoleIDsParams contains parameters of the UserDomainService.DeleteByUserIDAndRoleIDs
type UserDomainServiceMockDeleteByUserIDAndRoleIDsParams struct {
	ctx     context.Context
	userID  int64
	roleIDs []int64
}

// UserDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs contains pointers to parameters of the UserDomainService.DeleteByUserIDAndRoleIDs
type UserDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs struct {
	ctx     *context.Context
	userID  *int64
	roleIDs *[]int64
}

// UserDomainServiceMockDeleteByUserIDAndRoleIDsResults contains results of the UserDomainService.DeleteByUserIDAndRoleIDs
type UserDomainServiceMockDeleteByUserIDAndRoleIDsResults struct {
	err error
}

// UserDomainServiceMockDeleteByUserIDAndRoleIDsOrigins contains origins of expectations of the UserDomainService.DeleteByUserIDAndRoleIDs
type UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originUserID  string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Optional() *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	mmDeleteByUserIDAndRoleIDs.optional = true
	return mmDeleteByUserIDAndRoleIDs
}

// Expect sets up expected params for UserDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Expect(ctx context.Context, userID int64, roleIDs []int64) *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndRoleIDs.defaultExpectation.params = &UserDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndRoleIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndRoleIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndRoleIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectUserIDParam2 sets up expected param userID for UserDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) ExpectUserIDParam2(userID int64) *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for UserDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) ExpectRoleIDsParam3(roleIDs []int64) *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserDomainServiceMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Inspect(f func(ctx context.Context, userID int64, roleIDs []int64)) *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.DeleteByUserIDAndRoleIDs")
	}

	mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs = f

	return mmDeleteByUserIDAndRoleIDs
}

// Return sets up results that will be returned by UserDomainService.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Return(err error) *UserDomainServiceMock {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{mock: mmDeleteByUserIDAndRoleIDs.mock}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.results = &UserDomainServiceMockDeleteByUserIDAndRoleIDsResults{err}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// Set uses given function f to mock the UserDomainService.DeleteByUserIDAndRoleIDs method
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Set(f func(ctx context.Context, userID int64, roleIDs []int64) (err error)) *UserDomainServiceMock {
	if mmDeleteByUserIDAndRoleIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Default expectation is already set for the UserDomainService.DeleteByUserIDAndRoleIDs method")
	}

	if len(mmDeleteByUserIDAndRoleIDs.expectations) > 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Some expectations are already set for the UserDomainService.DeleteByUserIDAndRoleIDs method")
	}

	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs = f
	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// When sets expectation for the UserDomainService.DeleteByUserIDAndRoleIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) When(ctx context.Context, userID int64, roleIDs []int64) *UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	expectation := &UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation{
		mock:               mmDeleteByUserIDAndRoleIDs.mock,
		params:             &UserDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs},
		expectationOrigins: UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndRoleIDs.expectations = append(mmDeleteByUserIDAndRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.DeleteByUserIDAndRoleIDs return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockDeleteByUserIDAndRoleIDsExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockDeleteByUserIDAndRoleIDsResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.DeleteByUserIDAndRoleIDs should be invoked
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Times(n uint64) *mUserDomainServiceMockDeleteByUserIDAndRoleIDs {
	if n == 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Times of UserDomainServiceMock.DeleteByUserIDAndRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations, n)
	mmDeleteByUserIDAndRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs
}

func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndRoleIDs.expectations) == 0 && mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil && mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.mock.afterDeleteByUserIDAndRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndRoleIDs implements mm_service.UserDomainService
func (mmDeleteByUserIDAndRoleIDs *UserDomainServiceMock) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter, 1)

	mmDeleteByUserIDAndRoleIDs.t.Helper()

	if mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}

	mm_params := UserDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

	// Record call args
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Lock()
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs = append(mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndRoleIDs.t.Errorf("UserDomainServiceMock.DeleteByUserIDAndRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndRoleIDs.t.Fatal("No results are set for the UserDomainServiceMock.DeleteByUserIDAndRoleIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs != nil {
		return mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}
	mmDeleteByUserIDAndRoleIDs.t.Fatalf("Unexpected call to UserDomainServiceMock.DeleteByUserIDAndRoleIDs. %v %v %v", ctx, userID, roleIDs)
	return
}

// DeleteByUserIDAndRoleIDsAfterCounter returns a count of finished UserDomainServiceMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *UserDomainServiceMock) DeleteByUserIDAndRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter)
}

// DeleteByUserIDAndRoleIDsBeforeCounter returns a count of UserDomainServiceMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *UserDomainServiceMock) DeleteByUserIDAndRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.DeleteByUserIDAndRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndRoleIDs *mUserDomainServiceMockDeleteByUserIDAndRoleIDs) Calls() []*UserDomainServiceMockDeleteByUserIDAndRoleIDsParams {
	mmDeleteByUserIDAndRoleIDs.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockDeleteByUserIDAndRoleIDsParams, len(mmDeleteByUserIDAndRoleIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndRoleIDs.callArgs)

	mmDeleteByUserIDAndRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndRoleIDsDone returns true if the count of the DeleteByUserIDAndRoleIDs invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockDeleteByUserIDAndRoleIDsDone() bool {
	if m.DeleteByUserIDAndRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndRoleIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndRoleIDsInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockDeleteByUserIDAndRoleIDsInspect() {
	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndRoleIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndRoleIDs != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s", m.funcDeleteByUserIDAndRoleIDsOrigin)
	}

	if !m.DeleteByUserIDAndRoleIDsMock.invocationsDone() && afterDeleteByUserIDAndRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.DeleteByUserIDAndRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndRoleIDsMock.expectedInvocations), m.DeleteByUserIDAndRoleIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndRoleIDsCounter)
	}
}

type mUserDomainServiceMockGetAdmins struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetAdminsExpectation
	expectations       []*UserDomainServiceMockGetAdminsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetAdminsExpectation specifies expectation struct of the UserDomainService.GetAdmins
type UserDomainServiceMockGetAdminsExpectation struct {
	mock *UserDomainServiceMock

	results      *UserDomainServiceMockGetAdminsResults
	returnOrigin string
	Counter      uint64
}

// UserDomainServiceMockGetAdminsResults contains results of the UserDomainService.GetAdmins
type UserDomainServiceMockGetAdminsResults struct {
	ua1 []userentity.User
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAdmins *mUserDomainServiceMockGetAdmins) Optional() *mUserDomainServiceMockGetAdmins {
	mmGetAdmins.optional = true
	return mmGetAdmins
}

// Expect sets up expected params for UserDomainService.GetAdmins
func (mmGetAdmins *mUserDomainServiceMockGetAdmins) Expect() *mUserDomainServiceMockGetAdmins {
	if mmGetAdmins.mock.funcGetAdmins != nil {
		mmGetAdmins.mock.t.Fatalf("UserDomainServiceMock.GetAdmins mock is already set by Set")
	}

	if mmGetAdmins.defaultExpectation == nil {
		mmGetAdmins.defaultExpectation = &UserDomainServiceMockGetAdminsExpectation{}
	}

	return mmGetAdmins
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetAdmins
func (mmGetAdmins *mUserDomainServiceMockGetAdmins) Inspect(f func()) *mUserDomainServiceMockGetAdmins {
	if mmGetAdmins.mock.inspectFuncGetAdmins != nil {
		mmGetAdmins.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetAdmins")
	}

	mmGetAdmins.mock.inspectFuncGetAdmins = f

	return mmGetAdmins
}

// Return sets up results that will be returned by UserDomainService.GetAdmins
func (mmGetAdmins *mUserDomainServiceMockGetAdmins) Return(ua1 []userentity.User, err error) *UserDomainServiceMock {
	if mmGetAdmins.mock.funcGetAdmins != nil {
		mmGetAdmins.mock.t.Fatalf("UserDomainServiceMock.GetAdmins mock is already set by Set")
	}

	if mmGetAdmins.defaultExpectation == nil {
		mmGetAdmins.defaultExpectation = &UserDomainServiceMockGetAdminsExpectation{mock: mmGetAdmins.mock}
	}
	mmGetAdmins.defaultExpectation.results = &UserDomainServiceMockGetAdminsResults{ua1, err}
	mmGetAdmins.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAdmins.mock
}

// Set uses given function f to mock the UserDomainService.GetAdmins method
func (mmGetAdmins *mUserDomainServiceMockGetAdmins) Set(f func() (ua1 []userentity.User, err error)) *UserDomainServiceMock {
	if mmGetAdmins.defaultExpectation != nil {
		mmGetAdmins.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetAdmins method")
	}

	if len(mmGetAdmins.expectations) > 0 {
		mmGetAdmins.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetAdmins method")
	}

	mmGetAdmins.mock.funcGetAdmins = f
	mmGetAdmins.mock.funcGetAdminsOrigin = minimock.CallerInfo(1)
	return mmGetAdmins.mock
}

// Times sets number of times UserDomainService.GetAdmins should be invoked
func (mmGetAdmins *mUserDomainServiceMockGetAdmins) Times(n uint64) *mUserDomainServiceMockGetAdmins {
	if n == 0 {
		mmGetAdmins.mock.t.Fatalf("Times of UserDomainServiceMock.GetAdmins mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAdmins.expectedInvocations, n)
	mmGetAdmins.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAdmins
}

func (mmGetAdmins *mUserDomainServiceMockGetAdmins) invocationsDone() bool {
	if len(mmGetAdmins.expectations) == 0 && mmGetAdmins.defaultExpectation == nil && mmGetAdmins.mock.funcGetAdmins == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAdmins.mock.afterGetAdminsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAdmins.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAdmins implements mm_service.UserDomainService
func (mmGetAdmins *UserDomainServiceMock) GetAdmins() (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetAdmins.beforeGetAdminsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAdmins.afterGetAdminsCounter, 1)

	mmGetAdmins.t.Helper()

	if mmGetAdmins.inspectFuncGetAdmins != nil {
		mmGetAdmins.inspectFuncGetAdmins()
	}

	if mmGetAdmins.GetAdminsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAdmins.GetAdminsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAdmins.GetAdminsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAdmins.t.Fatal("No results are set for the UserDomainServiceMock.GetAdmins")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetAdmins.funcGetAdmins != nil {
		return mmGetAdmins.funcGetAdmins()
	}
	mmGetAdmins.t.Fatalf("Unexpected call to UserDomainServiceMock.GetAdmins.")
	return
}

// GetAdminsAfterCounter returns a count of finished UserDomainServiceMock.GetAdmins invocations
func (mmGetAdmins *UserDomainServiceMock) GetAdminsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAdmins.afterGetAdminsCounter)
}

// GetAdminsBeforeCounter returns a count of UserDomainServiceMock.GetAdmins invocations
func (mmGetAdmins *UserDomainServiceMock) GetAdminsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAdmins.beforeGetAdminsCounter)
}

// MinimockGetAdminsDone returns true if the count of the GetAdmins invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetAdminsDone() bool {
	if m.GetAdminsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAdminsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAdminsMock.invocationsDone()
}

// MinimockGetAdminsInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetAdminsInspect() {
	for _, e := range m.GetAdminsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to UserDomainServiceMock.GetAdmins")
		}
	}

	afterGetAdminsCounter := mm_atomic.LoadUint64(&m.afterGetAdminsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAdminsMock.defaultExpectation != nil && afterGetAdminsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAdmins at\n%s", m.GetAdminsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAdmins != nil && afterGetAdminsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAdmins at\n%s", m.funcGetAdminsOrigin)
	}

	if !m.GetAdminsMock.invocationsDone() && afterGetAdminsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetAdmins at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAdminsMock.expectedInvocations), m.GetAdminsMock.expectedInvocationsOrigin, afterGetAdminsCounter)
	}
}

type mUserDomainServiceMockGetAll struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetAllExpectation
	expectations       []*UserDomainServiceMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetAllExpectation specifies expectation struct of the UserDomainService.GetAll
type UserDomainServiceMockGetAllExpectation struct {
	mock *UserDomainServiceMock

	results      *UserDomainServiceMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// UserDomainServiceMockGetAllResults contains results of the UserDomainService.GetAll
type UserDomainServiceMockGetAllResults struct {
	ua1 []userentity.User
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mUserDomainServiceMockGetAll) Optional() *mUserDomainServiceMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for UserDomainService.GetAll
func (mmGetAll *mUserDomainServiceMockGetAll) Expect() *mUserDomainServiceMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("UserDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &UserDomainServiceMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetAll
func (mmGetAll *mUserDomainServiceMockGetAll) Inspect(f func()) *mUserDomainServiceMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by UserDomainService.GetAll
func (mmGetAll *mUserDomainServiceMockGetAll) Return(ua1 []userentity.User, err error) *UserDomainServiceMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("UserDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &UserDomainServiceMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &UserDomainServiceMockGetAllResults{ua1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the UserDomainService.GetAll method
func (mmGetAll *mUserDomainServiceMockGetAll) Set(f func() (ua1 []userentity.User, err error)) *UserDomainServiceMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times UserDomainService.GetAll should be invoked
func (mmGetAll *mUserDomainServiceMockGetAll) Times(n uint64) *mUserDomainServiceMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of UserDomainServiceMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mUserDomainServiceMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_service.UserDomainService
func (mmGetAll *UserDomainServiceMock) GetAll() (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the UserDomainServiceMock.GetAll")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to UserDomainServiceMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished UserDomainServiceMock.GetAll invocations
func (mmGetAll *UserDomainServiceMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of UserDomainServiceMock.GetAll invocations
func (mmGetAll *UserDomainServiceMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to UserDomainServiceMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mUserDomainServiceMockGetAllAsAdmin struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetAllAsAdminExpectation
	expectations       []*UserDomainServiceMockGetAllAsAdminExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetAllAsAdminExpectation specifies expectation struct of the UserDomainService.GetAllAsAdmin
type UserDomainServiceMockGetAllAsAdminExpectation struct {
	mock *UserDomainServiceMock

	results      *UserDomainServiceMockGetAllAsAdminResults
	returnOrigin string
	Counter      uint64
}

// UserDomainServiceMockGetAllAsAdminResults contains results of the UserDomainService.GetAllAsAdmin
type UserDomainServiceMockGetAllAsAdminResults struct {
	aa1 []userentity.AdminUser
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) Optional() *mUserDomainServiceMockGetAllAsAdmin {
	mmGetAllAsAdmin.optional = true
	return mmGetAllAsAdmin
}

// Expect sets up expected params for UserDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) Expect() *mUserDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("UserDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &UserDomainServiceMockGetAllAsAdminExpectation{}
	}

	return mmGetAllAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) Inspect(f func()) *mUserDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetAllAsAdmin")
	}

	mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin = f

	return mmGetAllAsAdmin
}

// Return sets up results that will be returned by UserDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) Return(aa1 []userentity.AdminUser, err error) *UserDomainServiceMock {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("UserDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &UserDomainServiceMockGetAllAsAdminExpectation{mock: mmGetAllAsAdmin.mock}
	}
	mmGetAllAsAdmin.defaultExpectation.results = &UserDomainServiceMockGetAllAsAdminResults{aa1, err}
	mmGetAllAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Set uses given function f to mock the UserDomainService.GetAllAsAdmin method
func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) Set(f func() (aa1 []userentity.AdminUser, err error)) *UserDomainServiceMock {
	if mmGetAllAsAdmin.defaultExpectation != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetAllAsAdmin method")
	}

	if len(mmGetAllAsAdmin.expectations) > 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetAllAsAdmin method")
	}

	mmGetAllAsAdmin.mock.funcGetAllAsAdmin = f
	mmGetAllAsAdmin.mock.funcGetAllAsAdminOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Times sets number of times UserDomainService.GetAllAsAdmin should be invoked
func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) Times(n uint64) *mUserDomainServiceMockGetAllAsAdmin {
	if n == 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Times of UserDomainServiceMock.GetAllAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllAsAdmin.expectedInvocations, n)
	mmGetAllAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin
}

func (mmGetAllAsAdmin *mUserDomainServiceMockGetAllAsAdmin) invocationsDone() bool {
	if len(mmGetAllAsAdmin.expectations) == 0 && mmGetAllAsAdmin.defaultExpectation == nil && mmGetAllAsAdmin.mock.funcGetAllAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.mock.afterGetAllAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllAsAdmin implements mm_service.UserDomainService
func (mmGetAllAsAdmin *UserDomainServiceMock) GetAllAsAdmin() (aa1 []userentity.AdminUser, err error) {
	mm_atomic.AddUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter, 1)

	mmGetAllAsAdmin.t.Helper()

	if mmGetAllAsAdmin.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.inspectFuncGetAllAsAdmin()
	}

	if mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllAsAdmin.t.Fatal("No results are set for the UserDomainServiceMock.GetAllAsAdmin")
		}
		return (*mm_results).aa1, (*mm_results).err
	}
	if mmGetAllAsAdmin.funcGetAllAsAdmin != nil {
		return mmGetAllAsAdmin.funcGetAllAsAdmin()
	}
	mmGetAllAsAdmin.t.Fatalf("Unexpected call to UserDomainServiceMock.GetAllAsAdmin.")
	return
}

// GetAllAsAdminAfterCounter returns a count of finished UserDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *UserDomainServiceMock) GetAllAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter)
}

// GetAllAsAdminBeforeCounter returns a count of UserDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *UserDomainServiceMock) GetAllAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter)
}

// MinimockGetAllAsAdminDone returns true if the count of the GetAllAsAdmin invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetAllAsAdminDone() bool {
	if m.GetAllAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllAsAdminMock.invocationsDone()
}

// MinimockGetAllAsAdminInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetAllAsAdminInspect() {
	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to UserDomainServiceMock.GetAllAsAdmin")
		}
	}

	afterGetAllAsAdminCounter := mm_atomic.LoadUint64(&m.afterGetAllAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllAsAdminMock.defaultExpectation != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAllAsAdmin at\n%s", m.GetAllAsAdminMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllAsAdmin != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAllAsAdmin at\n%s", m.funcGetAllAsAdminOrigin)
	}

	if !m.GetAllAsAdminMock.invocationsDone() && afterGetAllAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetAllAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllAsAdminMock.expectedInvocations), m.GetAllAsAdminMock.expectedInvocationsOrigin, afterGetAllAsAdminCounter)
	}
}

type mUserDomainServiceMockGetAllPaginated struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetAllPaginatedExpectation
	expectations       []*UserDomainServiceMockGetAllPaginatedExpectation

	callArgs []*UserDomainServiceMockGetAllPaginatedParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetAllPaginatedExpectation specifies expectation struct of the UserDomainService.GetAllPaginated
type UserDomainServiceMockGetAllPaginatedExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetAllPaginatedParams
	paramPtrs          *UserDomainServiceMockGetAllPaginatedParamPtrs
	expectationOrigins UserDomainServiceMockGetAllPaginatedExpectationOrigins
	results            *UserDomainServiceMockGetAllPaginatedResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetAllPaginatedParams contains parameters of the UserDomainService.GetAllPaginated
type UserDomainServiceMockGetAllPaginatedParams struct {
	pagination sharedentity.PaginationParams
}

// UserDomainServiceMockGetAllPaginatedParamPtrs contains pointers to parameters of the UserDomainService.GetAllPaginated
type UserDomainServiceMockGetAllPaginatedParamPtrs struct {
	pagination *sharedentity.PaginationParams
}

// UserDomainServiceMockGetAllPaginatedResults contains results of the UserDomainService.GetAllPaginated
type UserDomainServiceMockGetAllPaginatedResults struct {
	p1  sharedentity.PaginatedResult[userentity.User]
	err error
}

// UserDomainServiceMockGetAllPaginatedOrigins contains origins of expectations of the UserDomainService.GetAllPaginated
type UserDomainServiceMockGetAllPaginatedExpectationOrigins struct {
	origin           string
	originPagination string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Optional() *mUserDomainServiceMockGetAllPaginated {
	mmGetAllPaginated.optional = true
	return mmGetAllPaginated
}

// Expect sets up expected params for UserDomainService.GetAllPaginated
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Expect(pagination sharedentity.PaginationParams) *mUserDomainServiceMockGetAllPaginated {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserDomainServiceMock.GetAllPaginated mock is already set by Set")
	}

	if mmGetAllPaginated.defaultExpectation == nil {
		mmGetAllPaginated.defaultExpectation = &UserDomainServiceMockGetAllPaginatedExpectation{}
	}

	if mmGetAllPaginated.defaultExpectation.paramPtrs != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserDomainServiceMock.GetAllPaginated mock is already set by ExpectParams functions")
	}

	mmGetAllPaginated.defaultExpectation.params = &UserDomainServiceMockGetAllPaginatedParams{pagination}
	mmGetAllPaginated.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAllPaginated.expectations {
		if minimock.Equal(e.params, mmGetAllPaginated.defaultExpectation.params) {
			mmGetAllPaginated.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAllPaginated.defaultExpectation.params)
		}
	}

	return mmGetAllPaginated
}

// ExpectPaginationParam1 sets up expected param pagination for UserDomainService.GetAllPaginated
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) ExpectPaginationParam1(pagination sharedentity.PaginationParams) *mUserDomainServiceMockGetAllPaginated {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserDomainServiceMock.GetAllPaginated mock is already set by Set")
	}

	if mmGetAllPaginated.defaultExpectation == nil {
		mmGetAllPaginated.defaultExpectation = &UserDomainServiceMockGetAllPaginatedExpectation{}
	}

	if mmGetAllPaginated.defaultExpectation.params != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserDomainServiceMock.GetAllPaginated mock is already set by Expect")
	}

	if mmGetAllPaginated.defaultExpectation.paramPtrs == nil {
		mmGetAllPaginated.defaultExpectation.paramPtrs = &UserDomainServiceMockGetAllPaginatedParamPtrs{}
	}
	mmGetAllPaginated.defaultExpectation.paramPtrs.pagination = &pagination
	mmGetAllPaginated.defaultExpectation.expectationOrigins.originPagination = minimock.CallerInfo(1)

	return mmGetAllPaginated
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetAllPaginated
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Inspect(f func(pagination sharedentity.PaginationParams)) *mUserDomainServiceMockGetAllPaginated {
	if mmGetAllPaginated.mock.inspectFuncGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetAllPaginated")
	}

	mmGetAllPaginated.mock.inspectFuncGetAllPaginated = f

	return mmGetAllPaginated
}

// Return sets up results that will be returned by UserDomainService.GetAllPaginated
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Return(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserDomainServiceMock {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserDomainServiceMock.GetAllPaginated mock is already set by Set")
	}

	if mmGetAllPaginated.defaultExpectation == nil {
		mmGetAllPaginated.defaultExpectation = &UserDomainServiceMockGetAllPaginatedExpectation{mock: mmGetAllPaginated.mock}
	}
	mmGetAllPaginated.defaultExpectation.results = &UserDomainServiceMockGetAllPaginatedResults{p1, err}
	mmGetAllPaginated.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllPaginated.mock
}

// Set uses given function f to mock the UserDomainService.GetAllPaginated method
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Set(f func(pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)) *UserDomainServiceMock {
	if mmGetAllPaginated.defaultExpectation != nil {
		mmGetAllPaginated.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetAllPaginated method")
	}

	if len(mmGetAllPaginated.expectations) > 0 {
		mmGetAllPaginated.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetAllPaginated method")
	}

	mmGetAllPaginated.mock.funcGetAllPaginated = f
	mmGetAllPaginated.mock.funcGetAllPaginatedOrigin = minimock.CallerInfo(1)
	return mmGetAllPaginated.mock
}

// When sets expectation for the UserDomainService.GetAllPaginated which will trigger the result defined by the following
// Then helper
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) When(pagination sharedentity.PaginationParams) *UserDomainServiceMockGetAllPaginatedExpectation {
	if mmGetAllPaginated.mock.funcGetAllPaginated != nil {
		mmGetAllPaginated.mock.t.Fatalf("UserDomainServiceMock.GetAllPaginated mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetAllPaginatedExpectation{
		mock:               mmGetAllPaginated.mock,
		params:             &UserDomainServiceMockGetAllPaginatedParams{pagination},
		expectationOrigins: UserDomainServiceMockGetAllPaginatedExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAllPaginated.expectations = append(mmGetAllPaginated.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetAllPaginated return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetAllPaginatedExpectation) Then(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetAllPaginatedResults{p1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetAllPaginated should be invoked
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Times(n uint64) *mUserDomainServiceMockGetAllPaginated {
	if n == 0 {
		mmGetAllPaginated.mock.t.Fatalf("Times of UserDomainServiceMock.GetAllPaginated mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllPaginated.expectedInvocations, n)
	mmGetAllPaginated.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllPaginated
}

func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) invocationsDone() bool {
	if len(mmGetAllPaginated.expectations) == 0 && mmGetAllPaginated.defaultExpectation == nil && mmGetAllPaginated.mock.funcGetAllPaginated == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllPaginated.mock.afterGetAllPaginatedCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllPaginated.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllPaginated implements mm_service.UserDomainService
func (mmGetAllPaginated *UserDomainServiceMock) GetAllPaginated(pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error) {
	mm_atomic.AddUint64(&mmGetAllPaginated.beforeGetAllPaginatedCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllPaginated.afterGetAllPaginatedCounter, 1)

	mmGetAllPaginated.t.Helper()

	if mmGetAllPaginated.inspectFuncGetAllPaginated != nil {
		mmGetAllPaginated.inspectFuncGetAllPaginated(pagination)
	}

	mm_params := UserDomainServiceMockGetAllPaginatedParams{pagination}

	// Record call args
	mmGetAllPaginated.GetAllPaginatedMock.mutex.Lock()
	mmGetAllPaginated.GetAllPaginatedMock.callArgs = append(mmGetAllPaginated.GetAllPaginatedMock.callArgs, &mm_params)
	mmGetAllPaginated.GetAllPaginatedMock.mutex.Unlock()

	for _, e := range mmGetAllPaginated.GetAllPaginatedMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.params
		mm_want_ptrs := mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetAllPaginatedParams{pagination}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.pagination != nil && !minimock.Equal(*mm_want_ptrs.pagination, mm_got.pagination) {
				mmGetAllPaginated.t.Errorf("UserDomainServiceMock.GetAllPaginated got unexpected parameter pagination, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.expectationOrigins.originPagination, *mm_want_ptrs.pagination, mm_got.pagination, minimock.Diff(*mm_want_ptrs.pagination, mm_got.pagination))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAllPaginated.t.Errorf("UserDomainServiceMock.GetAllPaginated got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAllPaginated.GetAllPaginatedMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllPaginated.t.Fatal("No results are set for the UserDomainServiceMock.GetAllPaginated")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetAllPaginated.funcGetAllPaginated != nil {
		return mmGetAllPaginated.funcGetAllPaginated(pagination)
	}
	mmGetAllPaginated.t.Fatalf("Unexpected call to UserDomainServiceMock.GetAllPaginated. %v", pagination)
	return
}

// GetAllPaginatedAfterCounter returns a count of finished UserDomainServiceMock.GetAllPaginated invocations
func (mmGetAllPaginated *UserDomainServiceMock) GetAllPaginatedAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllPaginated.afterGetAllPaginatedCounter)
}

// GetAllPaginatedBeforeCounter returns a count of UserDomainServiceMock.GetAllPaginated invocations
func (mmGetAllPaginated *UserDomainServiceMock) GetAllPaginatedBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllPaginated.beforeGetAllPaginatedCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetAllPaginated.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAllPaginated *mUserDomainServiceMockGetAllPaginated) Calls() []*UserDomainServiceMockGetAllPaginatedParams {
	mmGetAllPaginated.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetAllPaginatedParams, len(mmGetAllPaginated.callArgs))
	copy(argCopy, mmGetAllPaginated.callArgs)

	mmGetAllPaginated.mutex.RUnlock()

	return argCopy
}

// MinimockGetAllPaginatedDone returns true if the count of the GetAllPaginated invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetAllPaginatedDone() bool {
	if m.GetAllPaginatedMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllPaginatedMock.invocationsDone()
}

// MinimockGetAllPaginatedInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetAllPaginatedInspect() {
	for _, e := range m.GetAllPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetAllPaginated at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAllPaginatedCounter := mm_atomic.LoadUint64(&m.afterGetAllPaginatedCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllPaginatedMock.defaultExpectation != nil && afterGetAllPaginatedCounter < 1 {
		if m.GetAllPaginatedMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetAllPaginated at\n%s", m.GetAllPaginatedMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetAllPaginated at\n%s with params: %#v", m.GetAllPaginatedMock.defaultExpectation.expectationOrigins.origin, *m.GetAllPaginatedMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllPaginated != nil && afterGetAllPaginatedCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetAllPaginated at\n%s", m.funcGetAllPaginatedOrigin)
	}

	if !m.GetAllPaginatedMock.invocationsDone() && afterGetAllPaginatedCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetAllPaginated at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllPaginatedMock.expectedInvocations), m.GetAllPaginatedMock.expectedInvocationsOrigin, afterGetAllPaginatedCounter)
	}
}

type mUserDomainServiceMockGetByCategoryID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetByCategoryIDExpectation
	expectations       []*UserDomainServiceMockGetByCategoryIDExpectation

	callArgs []*UserDomainServiceMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetByCategoryIDExpectation specifies expectation struct of the UserDomainService.GetByCategoryID
type UserDomainServiceMockGetByCategoryIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetByCategoryIDParams
	paramPtrs          *UserDomainServiceMockGetByCategoryIDParamPtrs
	expectationOrigins UserDomainServiceMockGetByCategoryIDExpectationOrigins
	results            *UserDomainServiceMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetByCategoryIDParams contains parameters of the UserDomainService.GetByCategoryID
type UserDomainServiceMockGetByCategoryIDParams struct {
	categoryID int64
}

// UserDomainServiceMockGetByCategoryIDParamPtrs contains pointers to parameters of the UserDomainService.GetByCategoryID
type UserDomainServiceMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// UserDomainServiceMockGetByCategoryIDResults contains results of the UserDomainService.GetByCategoryID
type UserDomainServiceMockGetByCategoryIDResults struct {
	ua1 []userentity.User
	err error
}

// UserDomainServiceMockGetByCategoryIDOrigins contains origins of expectations of the UserDomainService.GetByCategoryID
type UserDomainServiceMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Optional() *mUserDomainServiceMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for UserDomainService.GetByCategoryID
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Expect(categoryID int64) *mUserDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &UserDomainServiceMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserDomainServiceMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &UserDomainServiceMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for UserDomainService.GetByCategoryID
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mUserDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &UserDomainServiceMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserDomainServiceMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetByCategoryID
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Inspect(f func(categoryID int64)) *mUserDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by UserDomainService.GetByCategoryID
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Return(ua1 []userentity.User, err error) *UserDomainServiceMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &UserDomainServiceMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &UserDomainServiceMockGetByCategoryIDResults{ua1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the UserDomainService.GetByCategoryID method
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Set(f func(categoryID int64) (ua1 []userentity.User, err error)) *UserDomainServiceMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the UserDomainService.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) When(categoryID int64) *UserDomainServiceMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("UserDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &UserDomainServiceMockGetByCategoryIDParams{categoryID},
		expectationOrigins: UserDomainServiceMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetByCategoryIDExpectation) Then(ua1 []userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetByCategoryIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetByCategoryID should be invoked
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Times(n uint64) *mUserDomainServiceMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of UserDomainServiceMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_service.UserDomainService
func (mmGetByCategoryID *UserDomainServiceMock) GetByCategoryID(categoryID int64) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := UserDomainServiceMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("UserDomainServiceMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("UserDomainServiceMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the UserDomainServiceMock.GetByCategoryID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished UserDomainServiceMock.GetByCategoryID invocations
func (mmGetByCategoryID *UserDomainServiceMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of UserDomainServiceMock.GetByCategoryID invocations
func (mmGetByCategoryID *UserDomainServiceMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mUserDomainServiceMockGetByCategoryID) Calls() []*UserDomainServiceMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mUserDomainServiceMockGetByEmail struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetByEmailExpectation
	expectations       []*UserDomainServiceMockGetByEmailExpectation

	callArgs []*UserDomainServiceMockGetByEmailParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetByEmailExpectation specifies expectation struct of the UserDomainService.GetByEmail
type UserDomainServiceMockGetByEmailExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetByEmailParams
	paramPtrs          *UserDomainServiceMockGetByEmailParamPtrs
	expectationOrigins UserDomainServiceMockGetByEmailExpectationOrigins
	results            *UserDomainServiceMockGetByEmailResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetByEmailParams contains parameters of the UserDomainService.GetByEmail
type UserDomainServiceMockGetByEmailParams struct {
	email string
}

// UserDomainServiceMockGetByEmailParamPtrs contains pointers to parameters of the UserDomainService.GetByEmail
type UserDomainServiceMockGetByEmailParamPtrs struct {
	email *string
}

// UserDomainServiceMockGetByEmailResults contains results of the UserDomainService.GetByEmail
type UserDomainServiceMockGetByEmailResults struct {
	u1  userentity.User
	err error
}

// UserDomainServiceMockGetByEmailOrigins contains origins of expectations of the UserDomainService.GetByEmail
type UserDomainServiceMockGetByEmailExpectationOrigins struct {
	origin      string
	originEmail string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Optional() *mUserDomainServiceMockGetByEmail {
	mmGetByEmail.optional = true
	return mmGetByEmail
}

// Expect sets up expected params for UserDomainService.GetByEmail
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Expect(email string) *mUserDomainServiceMockGetByEmail {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserDomainServiceMock.GetByEmail mock is already set by Set")
	}

	if mmGetByEmail.defaultExpectation == nil {
		mmGetByEmail.defaultExpectation = &UserDomainServiceMockGetByEmailExpectation{}
	}

	if mmGetByEmail.defaultExpectation.paramPtrs != nil {
		mmGetByEmail.mock.t.Fatalf("UserDomainServiceMock.GetByEmail mock is already set by ExpectParams functions")
	}

	mmGetByEmail.defaultExpectation.params = &UserDomainServiceMockGetByEmailParams{email}
	mmGetByEmail.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByEmail.expectations {
		if minimock.Equal(e.params, mmGetByEmail.defaultExpectation.params) {
			mmGetByEmail.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByEmail.defaultExpectation.params)
		}
	}

	return mmGetByEmail
}

// ExpectEmailParam1 sets up expected param email for UserDomainService.GetByEmail
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) ExpectEmailParam1(email string) *mUserDomainServiceMockGetByEmail {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserDomainServiceMock.GetByEmail mock is already set by Set")
	}

	if mmGetByEmail.defaultExpectation == nil {
		mmGetByEmail.defaultExpectation = &UserDomainServiceMockGetByEmailExpectation{}
	}

	if mmGetByEmail.defaultExpectation.params != nil {
		mmGetByEmail.mock.t.Fatalf("UserDomainServiceMock.GetByEmail mock is already set by Expect")
	}

	if mmGetByEmail.defaultExpectation.paramPtrs == nil {
		mmGetByEmail.defaultExpectation.paramPtrs = &UserDomainServiceMockGetByEmailParamPtrs{}
	}
	mmGetByEmail.defaultExpectation.paramPtrs.email = &email
	mmGetByEmail.defaultExpectation.expectationOrigins.originEmail = minimock.CallerInfo(1)

	return mmGetByEmail
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetByEmail
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Inspect(f func(email string)) *mUserDomainServiceMockGetByEmail {
	if mmGetByEmail.mock.inspectFuncGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetByEmail")
	}

	mmGetByEmail.mock.inspectFuncGetByEmail = f

	return mmGetByEmail
}

// Return sets up results that will be returned by UserDomainService.GetByEmail
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Return(u1 userentity.User, err error) *UserDomainServiceMock {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserDomainServiceMock.GetByEmail mock is already set by Set")
	}

	if mmGetByEmail.defaultExpectation == nil {
		mmGetByEmail.defaultExpectation = &UserDomainServiceMockGetByEmailExpectation{mock: mmGetByEmail.mock}
	}
	mmGetByEmail.defaultExpectation.results = &UserDomainServiceMockGetByEmailResults{u1, err}
	mmGetByEmail.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByEmail.mock
}

// Set uses given function f to mock the UserDomainService.GetByEmail method
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Set(f func(email string) (u1 userentity.User, err error)) *UserDomainServiceMock {
	if mmGetByEmail.defaultExpectation != nil {
		mmGetByEmail.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetByEmail method")
	}

	if len(mmGetByEmail.expectations) > 0 {
		mmGetByEmail.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetByEmail method")
	}

	mmGetByEmail.mock.funcGetByEmail = f
	mmGetByEmail.mock.funcGetByEmailOrigin = minimock.CallerInfo(1)
	return mmGetByEmail.mock
}

// When sets expectation for the UserDomainService.GetByEmail which will trigger the result defined by the following
// Then helper
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) When(email string) *UserDomainServiceMockGetByEmailExpectation {
	if mmGetByEmail.mock.funcGetByEmail != nil {
		mmGetByEmail.mock.t.Fatalf("UserDomainServiceMock.GetByEmail mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetByEmailExpectation{
		mock:               mmGetByEmail.mock,
		params:             &UserDomainServiceMockGetByEmailParams{email},
		expectationOrigins: UserDomainServiceMockGetByEmailExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByEmail.expectations = append(mmGetByEmail.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetByEmail return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetByEmailExpectation) Then(u1 userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetByEmailResults{u1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetByEmail should be invoked
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Times(n uint64) *mUserDomainServiceMockGetByEmail {
	if n == 0 {
		mmGetByEmail.mock.t.Fatalf("Times of UserDomainServiceMock.GetByEmail mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByEmail.expectedInvocations, n)
	mmGetByEmail.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByEmail
}

func (mmGetByEmail *mUserDomainServiceMockGetByEmail) invocationsDone() bool {
	if len(mmGetByEmail.expectations) == 0 && mmGetByEmail.defaultExpectation == nil && mmGetByEmail.mock.funcGetByEmail == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByEmail.mock.afterGetByEmailCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByEmail.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByEmail implements mm_service.UserDomainService
func (mmGetByEmail *UserDomainServiceMock) GetByEmail(email string) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByEmail.beforeGetByEmailCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByEmail.afterGetByEmailCounter, 1)

	mmGetByEmail.t.Helper()

	if mmGetByEmail.inspectFuncGetByEmail != nil {
		mmGetByEmail.inspectFuncGetByEmail(email)
	}

	mm_params := UserDomainServiceMockGetByEmailParams{email}

	// Record call args
	mmGetByEmail.GetByEmailMock.mutex.Lock()
	mmGetByEmail.GetByEmailMock.callArgs = append(mmGetByEmail.GetByEmailMock.callArgs, &mm_params)
	mmGetByEmail.GetByEmailMock.mutex.Unlock()

	for _, e := range mmGetByEmail.GetByEmailMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByEmail.GetByEmailMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByEmail.GetByEmailMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByEmail.GetByEmailMock.defaultExpectation.params
		mm_want_ptrs := mmGetByEmail.GetByEmailMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetByEmailParams{email}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.email != nil && !minimock.Equal(*mm_want_ptrs.email, mm_got.email) {
				mmGetByEmail.t.Errorf("UserDomainServiceMock.GetByEmail got unexpected parameter email, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByEmail.GetByEmailMock.defaultExpectation.expectationOrigins.originEmail, *mm_want_ptrs.email, mm_got.email, minimock.Diff(*mm_want_ptrs.email, mm_got.email))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByEmail.t.Errorf("UserDomainServiceMock.GetByEmail got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByEmail.GetByEmailMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByEmail.GetByEmailMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByEmail.t.Fatal("No results are set for the UserDomainServiceMock.GetByEmail")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByEmail.funcGetByEmail != nil {
		return mmGetByEmail.funcGetByEmail(email)
	}
	mmGetByEmail.t.Fatalf("Unexpected call to UserDomainServiceMock.GetByEmail. %v", email)
	return
}

// GetByEmailAfterCounter returns a count of finished UserDomainServiceMock.GetByEmail invocations
func (mmGetByEmail *UserDomainServiceMock) GetByEmailAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByEmail.afterGetByEmailCounter)
}

// GetByEmailBeforeCounter returns a count of UserDomainServiceMock.GetByEmail invocations
func (mmGetByEmail *UserDomainServiceMock) GetByEmailBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByEmail.beforeGetByEmailCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetByEmail.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByEmail *mUserDomainServiceMockGetByEmail) Calls() []*UserDomainServiceMockGetByEmailParams {
	mmGetByEmail.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetByEmailParams, len(mmGetByEmail.callArgs))
	copy(argCopy, mmGetByEmail.callArgs)

	mmGetByEmail.mutex.RUnlock()

	return argCopy
}

// MinimockGetByEmailDone returns true if the count of the GetByEmail invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetByEmailDone() bool {
	if m.GetByEmailMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByEmailMock.invocationsDone()
}

// MinimockGetByEmailInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetByEmailInspect() {
	for _, e := range m.GetByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByEmail at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByEmailCounter := mm_atomic.LoadUint64(&m.afterGetByEmailCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByEmailMock.defaultExpectation != nil && afterGetByEmailCounter < 1 {
		if m.GetByEmailMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByEmail at\n%s", m.GetByEmailMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByEmail at\n%s with params: %#v", m.GetByEmailMock.defaultExpectation.expectationOrigins.origin, *m.GetByEmailMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByEmail != nil && afterGetByEmailCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetByEmail at\n%s", m.funcGetByEmailOrigin)
	}

	if !m.GetByEmailMock.invocationsDone() && afterGetByEmailCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetByEmail at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByEmailMock.expectedInvocations), m.GetByEmailMock.expectedInvocationsOrigin, afterGetByEmailCounter)
	}
}

type mUserDomainServiceMockGetByFilters struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetByFiltersExpectation
	expectations       []*UserDomainServiceMockGetByFiltersExpectation

	callArgs []*UserDomainServiceMockGetByFiltersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetByFiltersExpectation specifies expectation struct of the UserDomainService.GetByFilters
type UserDomainServiceMockGetByFiltersExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetByFiltersParams
	paramPtrs          *UserDomainServiceMockGetByFiltersParamPtrs
	expectationOrigins UserDomainServiceMockGetByFiltersExpectationOrigins
	results            *UserDomainServiceMockGetByFiltersResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetByFiltersParams contains parameters of the UserDomainService.GetByFilters
type UserDomainServiceMockGetByFiltersParams struct {
	userFilters userentity.UserFiltersData
}

// UserDomainServiceMockGetByFiltersParamPtrs contains pointers to parameters of the UserDomainService.GetByFilters
type UserDomainServiceMockGetByFiltersParamPtrs struct {
	userFilters *userentity.UserFiltersData
}

// UserDomainServiceMockGetByFiltersResults contains results of the UserDomainService.GetByFilters
type UserDomainServiceMockGetByFiltersResults struct {
	ua1 []userentity.User
	err error
}

// UserDomainServiceMockGetByFiltersOrigins contains origins of expectations of the UserDomainService.GetByFilters
type UserDomainServiceMockGetByFiltersExpectationOrigins struct {
	origin            string
	originUserFilters string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Optional() *mUserDomainServiceMockGetByFilters {
	mmGetByFilters.optional = true
	return mmGetByFilters
}

// Expect sets up expected params for UserDomainService.GetByFilters
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Expect(userFilters userentity.UserFiltersData) *mUserDomainServiceMockGetByFilters {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserDomainServiceMock.GetByFilters mock is already set by Set")
	}

	if mmGetByFilters.defaultExpectation == nil {
		mmGetByFilters.defaultExpectation = &UserDomainServiceMockGetByFiltersExpectation{}
	}

	if mmGetByFilters.defaultExpectation.paramPtrs != nil {
		mmGetByFilters.mock.t.Fatalf("UserDomainServiceMock.GetByFilters mock is already set by ExpectParams functions")
	}

	mmGetByFilters.defaultExpectation.params = &UserDomainServiceMockGetByFiltersParams{userFilters}
	mmGetByFilters.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByFilters.expectations {
		if minimock.Equal(e.params, mmGetByFilters.defaultExpectation.params) {
			mmGetByFilters.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByFilters.defaultExpectation.params)
		}
	}

	return mmGetByFilters
}

// ExpectUserFiltersParam1 sets up expected param userFilters for UserDomainService.GetByFilters
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) ExpectUserFiltersParam1(userFilters userentity.UserFiltersData) *mUserDomainServiceMockGetByFilters {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserDomainServiceMock.GetByFilters mock is already set by Set")
	}

	if mmGetByFilters.defaultExpectation == nil {
		mmGetByFilters.defaultExpectation = &UserDomainServiceMockGetByFiltersExpectation{}
	}

	if mmGetByFilters.defaultExpectation.params != nil {
		mmGetByFilters.mock.t.Fatalf("UserDomainServiceMock.GetByFilters mock is already set by Expect")
	}

	if mmGetByFilters.defaultExpectation.paramPtrs == nil {
		mmGetByFilters.defaultExpectation.paramPtrs = &UserDomainServiceMockGetByFiltersParamPtrs{}
	}
	mmGetByFilters.defaultExpectation.paramPtrs.userFilters = &userFilters
	mmGetByFilters.defaultExpectation.expectationOrigins.originUserFilters = minimock.CallerInfo(1)

	return mmGetByFilters
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetByFilters
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Inspect(f func(userFilters userentity.UserFiltersData)) *mUserDomainServiceMockGetByFilters {
	if mmGetByFilters.mock.inspectFuncGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetByFilters")
	}

	mmGetByFilters.mock.inspectFuncGetByFilters = f

	return mmGetByFilters
}

// Return sets up results that will be returned by UserDomainService.GetByFilters
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Return(ua1 []userentity.User, err error) *UserDomainServiceMock {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserDomainServiceMock.GetByFilters mock is already set by Set")
	}

	if mmGetByFilters.defaultExpectation == nil {
		mmGetByFilters.defaultExpectation = &UserDomainServiceMockGetByFiltersExpectation{mock: mmGetByFilters.mock}
	}
	mmGetByFilters.defaultExpectation.results = &UserDomainServiceMockGetByFiltersResults{ua1, err}
	mmGetByFilters.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByFilters.mock
}

// Set uses given function f to mock the UserDomainService.GetByFilters method
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Set(f func(userFilters userentity.UserFiltersData) (ua1 []userentity.User, err error)) *UserDomainServiceMock {
	if mmGetByFilters.defaultExpectation != nil {
		mmGetByFilters.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetByFilters method")
	}

	if len(mmGetByFilters.expectations) > 0 {
		mmGetByFilters.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetByFilters method")
	}

	mmGetByFilters.mock.funcGetByFilters = f
	mmGetByFilters.mock.funcGetByFiltersOrigin = minimock.CallerInfo(1)
	return mmGetByFilters.mock
}

// When sets expectation for the UserDomainService.GetByFilters which will trigger the result defined by the following
// Then helper
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) When(userFilters userentity.UserFiltersData) *UserDomainServiceMockGetByFiltersExpectation {
	if mmGetByFilters.mock.funcGetByFilters != nil {
		mmGetByFilters.mock.t.Fatalf("UserDomainServiceMock.GetByFilters mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetByFiltersExpectation{
		mock:               mmGetByFilters.mock,
		params:             &UserDomainServiceMockGetByFiltersParams{userFilters},
		expectationOrigins: UserDomainServiceMockGetByFiltersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByFilters.expectations = append(mmGetByFilters.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetByFilters return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetByFiltersExpectation) Then(ua1 []userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetByFiltersResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetByFilters should be invoked
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Times(n uint64) *mUserDomainServiceMockGetByFilters {
	if n == 0 {
		mmGetByFilters.mock.t.Fatalf("Times of UserDomainServiceMock.GetByFilters mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByFilters.expectedInvocations, n)
	mmGetByFilters.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByFilters
}

func (mmGetByFilters *mUserDomainServiceMockGetByFilters) invocationsDone() bool {
	if len(mmGetByFilters.expectations) == 0 && mmGetByFilters.defaultExpectation == nil && mmGetByFilters.mock.funcGetByFilters == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByFilters.mock.afterGetByFiltersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByFilters.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByFilters implements mm_service.UserDomainService
func (mmGetByFilters *UserDomainServiceMock) GetByFilters(userFilters userentity.UserFiltersData) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByFilters.beforeGetByFiltersCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByFilters.afterGetByFiltersCounter, 1)

	mmGetByFilters.t.Helper()

	if mmGetByFilters.inspectFuncGetByFilters != nil {
		mmGetByFilters.inspectFuncGetByFilters(userFilters)
	}

	mm_params := UserDomainServiceMockGetByFiltersParams{userFilters}

	// Record call args
	mmGetByFilters.GetByFiltersMock.mutex.Lock()
	mmGetByFilters.GetByFiltersMock.callArgs = append(mmGetByFilters.GetByFiltersMock.callArgs, &mm_params)
	mmGetByFilters.GetByFiltersMock.mutex.Unlock()

	for _, e := range mmGetByFilters.GetByFiltersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByFilters.GetByFiltersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByFilters.GetByFiltersMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByFilters.GetByFiltersMock.defaultExpectation.params
		mm_want_ptrs := mmGetByFilters.GetByFiltersMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetByFiltersParams{userFilters}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userFilters != nil && !minimock.Equal(*mm_want_ptrs.userFilters, mm_got.userFilters) {
				mmGetByFilters.t.Errorf("UserDomainServiceMock.GetByFilters got unexpected parameter userFilters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFilters.GetByFiltersMock.defaultExpectation.expectationOrigins.originUserFilters, *mm_want_ptrs.userFilters, mm_got.userFilters, minimock.Diff(*mm_want_ptrs.userFilters, mm_got.userFilters))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByFilters.t.Errorf("UserDomainServiceMock.GetByFilters got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByFilters.GetByFiltersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByFilters.GetByFiltersMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByFilters.t.Fatal("No results are set for the UserDomainServiceMock.GetByFilters")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByFilters.funcGetByFilters != nil {
		return mmGetByFilters.funcGetByFilters(userFilters)
	}
	mmGetByFilters.t.Fatalf("Unexpected call to UserDomainServiceMock.GetByFilters. %v", userFilters)
	return
}

// GetByFiltersAfterCounter returns a count of finished UserDomainServiceMock.GetByFilters invocations
func (mmGetByFilters *UserDomainServiceMock) GetByFiltersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFilters.afterGetByFiltersCounter)
}

// GetByFiltersBeforeCounter returns a count of UserDomainServiceMock.GetByFilters invocations
func (mmGetByFilters *UserDomainServiceMock) GetByFiltersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFilters.beforeGetByFiltersCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetByFilters.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByFilters *mUserDomainServiceMockGetByFilters) Calls() []*UserDomainServiceMockGetByFiltersParams {
	mmGetByFilters.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetByFiltersParams, len(mmGetByFilters.callArgs))
	copy(argCopy, mmGetByFilters.callArgs)

	mmGetByFilters.mutex.RUnlock()

	return argCopy
}

// MinimockGetByFiltersDone returns true if the count of the GetByFilters invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetByFiltersDone() bool {
	if m.GetByFiltersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByFiltersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByFiltersMock.invocationsDone()
}

// MinimockGetByFiltersInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetByFiltersInspect() {
	for _, e := range m.GetByFiltersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByFilters at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByFiltersCounter := mm_atomic.LoadUint64(&m.afterGetByFiltersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByFiltersMock.defaultExpectation != nil && afterGetByFiltersCounter < 1 {
		if m.GetByFiltersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByFilters at\n%s", m.GetByFiltersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByFilters at\n%s with params: %#v", m.GetByFiltersMock.defaultExpectation.expectationOrigins.origin, *m.GetByFiltersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByFilters != nil && afterGetByFiltersCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetByFilters at\n%s", m.funcGetByFiltersOrigin)
	}

	if !m.GetByFiltersMock.invocationsDone() && afterGetByFiltersCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetByFilters at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByFiltersMock.expectedInvocations), m.GetByFiltersMock.expectedInvocationsOrigin, afterGetByFiltersCounter)
	}
}

type mUserDomainServiceMockGetByFiltersPaginated struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetByFiltersPaginatedExpectation
	expectations       []*UserDomainServiceMockGetByFiltersPaginatedExpectation

	callArgs []*UserDomainServiceMockGetByFiltersPaginatedParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetByFiltersPaginatedExpectation specifies expectation struct of the UserDomainService.GetByFiltersPaginated
type UserDomainServiceMockGetByFiltersPaginatedExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetByFiltersPaginatedParams
	paramPtrs          *UserDomainServiceMockGetByFiltersPaginatedParamPtrs
	expectationOrigins UserDomainServiceMockGetByFiltersPaginatedExpectationOrigins
	results            *UserDomainServiceMockGetByFiltersPaginatedResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetByFiltersPaginatedParams contains parameters of the UserDomainService.GetByFiltersPaginated
type UserDomainServiceMockGetByFiltersPaginatedParams struct {
	userFilters userentity.UserFiltersData
	pagination  sharedentity.PaginationParams
}

// UserDomainServiceMockGetByFiltersPaginatedParamPtrs contains pointers to parameters of the UserDomainService.GetByFiltersPaginated
type UserDomainServiceMockGetByFiltersPaginatedParamPtrs struct {
	userFilters *userentity.UserFiltersData
	pagination  *sharedentity.PaginationParams
}

// UserDomainServiceMockGetByFiltersPaginatedResults contains results of the UserDomainService.GetByFiltersPaginated
type UserDomainServiceMockGetByFiltersPaginatedResults struct {
	p1  sharedentity.PaginatedResult[userentity.User]
	err error
}

// UserDomainServiceMockGetByFiltersPaginatedOrigins contains origins of expectations of the UserDomainService.GetByFiltersPaginated
type UserDomainServiceMockGetByFiltersPaginatedExpectationOrigins struct {
	origin            string
	originUserFilters string
	originPagination  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Optional() *mUserDomainServiceMockGetByFiltersPaginated {
	mmGetByFiltersPaginated.optional = true
	return mmGetByFiltersPaginated
}

// Expect sets up expected params for UserDomainService.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Expect(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) *mUserDomainServiceMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserDomainServiceMockGetByFiltersPaginatedExpectation{}
	}

	if mmGetByFiltersPaginated.defaultExpectation.paramPtrs != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by ExpectParams functions")
	}

	mmGetByFiltersPaginated.defaultExpectation.params = &UserDomainServiceMockGetByFiltersPaginatedParams{userFilters, pagination}
	mmGetByFiltersPaginated.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByFiltersPaginated.expectations {
		if minimock.Equal(e.params, mmGetByFiltersPaginated.defaultExpectation.params) {
			mmGetByFiltersPaginated.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByFiltersPaginated.defaultExpectation.params)
		}
	}

	return mmGetByFiltersPaginated
}

// ExpectUserFiltersParam1 sets up expected param userFilters for UserDomainService.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) ExpectUserFiltersParam1(userFilters userentity.UserFiltersData) *mUserDomainServiceMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserDomainServiceMockGetByFiltersPaginatedExpectation{}
	}

	if mmGetByFiltersPaginated.defaultExpectation.params != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Expect")
	}

	if mmGetByFiltersPaginated.defaultExpectation.paramPtrs == nil {
		mmGetByFiltersPaginated.defaultExpectation.paramPtrs = &UserDomainServiceMockGetByFiltersPaginatedParamPtrs{}
	}
	mmGetByFiltersPaginated.defaultExpectation.paramPtrs.userFilters = &userFilters
	mmGetByFiltersPaginated.defaultExpectation.expectationOrigins.originUserFilters = minimock.CallerInfo(1)

	return mmGetByFiltersPaginated
}

// ExpectPaginationParam2 sets up expected param pagination for UserDomainService.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) ExpectPaginationParam2(pagination sharedentity.PaginationParams) *mUserDomainServiceMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserDomainServiceMockGetByFiltersPaginatedExpectation{}
	}

	if mmGetByFiltersPaginated.defaultExpectation.params != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Expect")
	}

	if mmGetByFiltersPaginated.defaultExpectation.paramPtrs == nil {
		mmGetByFiltersPaginated.defaultExpectation.paramPtrs = &UserDomainServiceMockGetByFiltersPaginatedParamPtrs{}
	}
	mmGetByFiltersPaginated.defaultExpectation.paramPtrs.pagination = &pagination
	mmGetByFiltersPaginated.defaultExpectation.expectationOrigins.originPagination = minimock.CallerInfo(1)

	return mmGetByFiltersPaginated
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Inspect(f func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams)) *mUserDomainServiceMockGetByFiltersPaginated {
	if mmGetByFiltersPaginated.mock.inspectFuncGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetByFiltersPaginated")
	}

	mmGetByFiltersPaginated.mock.inspectFuncGetByFiltersPaginated = f

	return mmGetByFiltersPaginated
}

// Return sets up results that will be returned by UserDomainService.GetByFiltersPaginated
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Return(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserDomainServiceMock {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Set")
	}

	if mmGetByFiltersPaginated.defaultExpectation == nil {
		mmGetByFiltersPaginated.defaultExpectation = &UserDomainServiceMockGetByFiltersPaginatedExpectation{mock: mmGetByFiltersPaginated.mock}
	}
	mmGetByFiltersPaginated.defaultExpectation.results = &UserDomainServiceMockGetByFiltersPaginatedResults{p1, err}
	mmGetByFiltersPaginated.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersPaginated.mock
}

// Set uses given function f to mock the UserDomainService.GetByFiltersPaginated method
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Set(f func(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error)) *UserDomainServiceMock {
	if mmGetByFiltersPaginated.defaultExpectation != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetByFiltersPaginated method")
	}

	if len(mmGetByFiltersPaginated.expectations) > 0 {
		mmGetByFiltersPaginated.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetByFiltersPaginated method")
	}

	mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated = f
	mmGetByFiltersPaginated.mock.funcGetByFiltersPaginatedOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersPaginated.mock
}

// When sets expectation for the UserDomainService.GetByFiltersPaginated which will trigger the result defined by the following
// Then helper
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) When(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) *UserDomainServiceMockGetByFiltersPaginatedExpectation {
	if mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.mock.t.Fatalf("UserDomainServiceMock.GetByFiltersPaginated mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetByFiltersPaginatedExpectation{
		mock:               mmGetByFiltersPaginated.mock,
		params:             &UserDomainServiceMockGetByFiltersPaginatedParams{userFilters, pagination},
		expectationOrigins: UserDomainServiceMockGetByFiltersPaginatedExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByFiltersPaginated.expectations = append(mmGetByFiltersPaginated.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetByFiltersPaginated return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetByFiltersPaginatedExpectation) Then(p1 sharedentity.PaginatedResult[userentity.User], err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetByFiltersPaginatedResults{p1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetByFiltersPaginated should be invoked
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Times(n uint64) *mUserDomainServiceMockGetByFiltersPaginated {
	if n == 0 {
		mmGetByFiltersPaginated.mock.t.Fatalf("Times of UserDomainServiceMock.GetByFiltersPaginated mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByFiltersPaginated.expectedInvocations, n)
	mmGetByFiltersPaginated.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByFiltersPaginated
}

func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) invocationsDone() bool {
	if len(mmGetByFiltersPaginated.expectations) == 0 && mmGetByFiltersPaginated.defaultExpectation == nil && mmGetByFiltersPaginated.mock.funcGetByFiltersPaginated == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByFiltersPaginated.mock.afterGetByFiltersPaginatedCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByFiltersPaginated.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByFiltersPaginated implements mm_service.UserDomainService
func (mmGetByFiltersPaginated *UserDomainServiceMock) GetByFiltersPaginated(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (p1 sharedentity.PaginatedResult[userentity.User], err error) {
	mm_atomic.AddUint64(&mmGetByFiltersPaginated.beforeGetByFiltersPaginatedCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByFiltersPaginated.afterGetByFiltersPaginatedCounter, 1)

	mmGetByFiltersPaginated.t.Helper()

	if mmGetByFiltersPaginated.inspectFuncGetByFiltersPaginated != nil {
		mmGetByFiltersPaginated.inspectFuncGetByFiltersPaginated(userFilters, pagination)
	}

	mm_params := UserDomainServiceMockGetByFiltersPaginatedParams{userFilters, pagination}

	// Record call args
	mmGetByFiltersPaginated.GetByFiltersPaginatedMock.mutex.Lock()
	mmGetByFiltersPaginated.GetByFiltersPaginatedMock.callArgs = append(mmGetByFiltersPaginated.GetByFiltersPaginatedMock.callArgs, &mm_params)
	mmGetByFiltersPaginated.GetByFiltersPaginatedMock.mutex.Unlock()

	for _, e := range mmGetByFiltersPaginated.GetByFiltersPaginatedMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.params
		mm_want_ptrs := mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetByFiltersPaginatedParams{userFilters, pagination}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userFilters != nil && !minimock.Equal(*mm_want_ptrs.userFilters, mm_got.userFilters) {
				mmGetByFiltersPaginated.t.Errorf("UserDomainServiceMock.GetByFiltersPaginated got unexpected parameter userFilters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.originUserFilters, *mm_want_ptrs.userFilters, mm_got.userFilters, minimock.Diff(*mm_want_ptrs.userFilters, mm_got.userFilters))
			}

			if mm_want_ptrs.pagination != nil && !minimock.Equal(*mm_want_ptrs.pagination, mm_got.pagination) {
				mmGetByFiltersPaginated.t.Errorf("UserDomainServiceMock.GetByFiltersPaginated got unexpected parameter pagination, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.originPagination, *mm_want_ptrs.pagination, mm_got.pagination, minimock.Diff(*mm_want_ptrs.pagination, mm_got.pagination))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByFiltersPaginated.t.Errorf("UserDomainServiceMock.GetByFiltersPaginated got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByFiltersPaginated.GetByFiltersPaginatedMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByFiltersPaginated.t.Fatal("No results are set for the UserDomainServiceMock.GetByFiltersPaginated")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByFiltersPaginated.funcGetByFiltersPaginated != nil {
		return mmGetByFiltersPaginated.funcGetByFiltersPaginated(userFilters, pagination)
	}
	mmGetByFiltersPaginated.t.Fatalf("Unexpected call to UserDomainServiceMock.GetByFiltersPaginated. %v %v", userFilters, pagination)
	return
}

// GetByFiltersPaginatedAfterCounter returns a count of finished UserDomainServiceMock.GetByFiltersPaginated invocations
func (mmGetByFiltersPaginated *UserDomainServiceMock) GetByFiltersPaginatedAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFiltersPaginated.afterGetByFiltersPaginatedCounter)
}

// GetByFiltersPaginatedBeforeCounter returns a count of UserDomainServiceMock.GetByFiltersPaginated invocations
func (mmGetByFiltersPaginated *UserDomainServiceMock) GetByFiltersPaginatedBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByFiltersPaginated.beforeGetByFiltersPaginatedCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetByFiltersPaginated.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByFiltersPaginated *mUserDomainServiceMockGetByFiltersPaginated) Calls() []*UserDomainServiceMockGetByFiltersPaginatedParams {
	mmGetByFiltersPaginated.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetByFiltersPaginatedParams, len(mmGetByFiltersPaginated.callArgs))
	copy(argCopy, mmGetByFiltersPaginated.callArgs)

	mmGetByFiltersPaginated.mutex.RUnlock()

	return argCopy
}

// MinimockGetByFiltersPaginatedDone returns true if the count of the GetByFiltersPaginated invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetByFiltersPaginatedDone() bool {
	if m.GetByFiltersPaginatedMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByFiltersPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByFiltersPaginatedMock.invocationsDone()
}

// MinimockGetByFiltersPaginatedInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetByFiltersPaginatedInspect() {
	for _, e := range m.GetByFiltersPaginatedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByFiltersPaginated at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByFiltersPaginatedCounter := mm_atomic.LoadUint64(&m.afterGetByFiltersPaginatedCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByFiltersPaginatedMock.defaultExpectation != nil && afterGetByFiltersPaginatedCounter < 1 {
		if m.GetByFiltersPaginatedMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByFiltersPaginated at\n%s", m.GetByFiltersPaginatedMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByFiltersPaginated at\n%s with params: %#v", m.GetByFiltersPaginatedMock.defaultExpectation.expectationOrigins.origin, *m.GetByFiltersPaginatedMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByFiltersPaginated != nil && afterGetByFiltersPaginatedCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetByFiltersPaginated at\n%s", m.funcGetByFiltersPaginatedOrigin)
	}

	if !m.GetByFiltersPaginatedMock.invocationsDone() && afterGetByFiltersPaginatedCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetByFiltersPaginated at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByFiltersPaginatedMock.expectedInvocations), m.GetByFiltersPaginatedMock.expectedInvocationsOrigin, afterGetByFiltersPaginatedCounter)
	}
}

type mUserDomainServiceMockGetByID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetByIDExpectation
	expectations       []*UserDomainServiceMockGetByIDExpectation

	callArgs []*UserDomainServiceMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetByIDExpectation specifies expectation struct of the UserDomainService.GetByID
type UserDomainServiceMockGetByIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetByIDParams
	paramPtrs          *UserDomainServiceMockGetByIDParamPtrs
	expectationOrigins UserDomainServiceMockGetByIDExpectationOrigins
	results            *UserDomainServiceMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetByIDParams contains parameters of the UserDomainService.GetByID
type UserDomainServiceMockGetByIDParams struct {
	id int64
}

// UserDomainServiceMockGetByIDParamPtrs contains pointers to parameters of the UserDomainService.GetByID
type UserDomainServiceMockGetByIDParamPtrs struct {
	id *int64
}

// UserDomainServiceMockGetByIDResults contains results of the UserDomainService.GetByID
type UserDomainServiceMockGetByIDResults struct {
	u1  userentity.User
	err error
}

// UserDomainServiceMockGetByIDOrigins contains origins of expectations of the UserDomainService.GetByID
type UserDomainServiceMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mUserDomainServiceMockGetByID) Optional() *mUserDomainServiceMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for UserDomainService.GetByID
func (mmGetByID *mUserDomainServiceMockGetByID) Expect(id int64) *mUserDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("UserDomainServiceMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &UserDomainServiceMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for UserDomainService.GetByID
func (mmGetByID *mUserDomainServiceMockGetByID) ExpectIdParam1(id int64) *mUserDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("UserDomainServiceMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetByID
func (mmGetByID *mUserDomainServiceMockGetByID) Inspect(f func(id int64)) *mUserDomainServiceMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by UserDomainService.GetByID
func (mmGetByID *mUserDomainServiceMockGetByID) Return(u1 userentity.User, err error) *UserDomainServiceMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserDomainServiceMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &UserDomainServiceMockGetByIDResults{u1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the UserDomainService.GetByID method
func (mmGetByID *mUserDomainServiceMockGetByID) Set(f func(id int64) (u1 userentity.User, err error)) *UserDomainServiceMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the UserDomainService.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mUserDomainServiceMockGetByID) When(id int64) *UserDomainServiceMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserDomainServiceMock.GetByID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &UserDomainServiceMockGetByIDParams{id},
		expectationOrigins: UserDomainServiceMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetByID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetByIDExpectation) Then(u1 userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetByIDResults{u1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetByID should be invoked
func (mmGetByID *mUserDomainServiceMockGetByID) Times(n uint64) *mUserDomainServiceMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of UserDomainServiceMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mUserDomainServiceMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_service.UserDomainService
func (mmGetByID *UserDomainServiceMock) GetByID(id int64) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := UserDomainServiceMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("UserDomainServiceMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("UserDomainServiceMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the UserDomainServiceMock.GetByID")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished UserDomainServiceMock.GetByID invocations
func (mmGetByID *UserDomainServiceMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of UserDomainServiceMock.GetByID invocations
func (mmGetByID *UserDomainServiceMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mUserDomainServiceMockGetByID) Calls() []*UserDomainServiceMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mUserDomainServiceMockGetUserGroups struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUserGroupsExpectation
	expectations       []*UserDomainServiceMockGetUserGroupsExpectation

	callArgs []*UserDomainServiceMockGetUserGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUserGroupsExpectation specifies expectation struct of the UserDomainService.GetUserGroups
type UserDomainServiceMockGetUserGroupsExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUserGroupsParams
	paramPtrs          *UserDomainServiceMockGetUserGroupsParamPtrs
	expectationOrigins UserDomainServiceMockGetUserGroupsExpectationOrigins
	results            *UserDomainServiceMockGetUserGroupsResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUserGroupsParams contains parameters of the UserDomainService.GetUserGroups
type UserDomainServiceMockGetUserGroupsParams struct {
	userID int64
}

// UserDomainServiceMockGetUserGroupsParamPtrs contains pointers to parameters of the UserDomainService.GetUserGroups
type UserDomainServiceMockGetUserGroupsParamPtrs struct {
	userID *int64
}

// UserDomainServiceMockGetUserGroupsResults contains results of the UserDomainService.GetUserGroups
type UserDomainServiceMockGetUserGroupsResults struct {
	ia1 []int64
	err error
}

// UserDomainServiceMockGetUserGroupsOrigins contains origins of expectations of the UserDomainService.GetUserGroups
type UserDomainServiceMockGetUserGroupsExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Optional() *mUserDomainServiceMockGetUserGroups {
	mmGetUserGroups.optional = true
	return mmGetUserGroups
}

// Expect sets up expected params for UserDomainService.GetUserGroups
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Expect(userID int64) *mUserDomainServiceMockGetUserGroups {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserDomainServiceMock.GetUserGroups mock is already set by Set")
	}

	if mmGetUserGroups.defaultExpectation == nil {
		mmGetUserGroups.defaultExpectation = &UserDomainServiceMockGetUserGroupsExpectation{}
	}

	if mmGetUserGroups.defaultExpectation.paramPtrs != nil {
		mmGetUserGroups.mock.t.Fatalf("UserDomainServiceMock.GetUserGroups mock is already set by ExpectParams functions")
	}

	mmGetUserGroups.defaultExpectation.params = &UserDomainServiceMockGetUserGroupsParams{userID}
	mmGetUserGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserGroups.expectations {
		if minimock.Equal(e.params, mmGetUserGroups.defaultExpectation.params) {
			mmGetUserGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserGroups.defaultExpectation.params)
		}
	}

	return mmGetUserGroups
}

// ExpectUserIDParam1 sets up expected param userID for UserDomainService.GetUserGroups
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) ExpectUserIDParam1(userID int64) *mUserDomainServiceMockGetUserGroups {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserDomainServiceMock.GetUserGroups mock is already set by Set")
	}

	if mmGetUserGroups.defaultExpectation == nil {
		mmGetUserGroups.defaultExpectation = &UserDomainServiceMockGetUserGroupsExpectation{}
	}

	if mmGetUserGroups.defaultExpectation.params != nil {
		mmGetUserGroups.mock.t.Fatalf("UserDomainServiceMock.GetUserGroups mock is already set by Expect")
	}

	if mmGetUserGroups.defaultExpectation.paramPtrs == nil {
		mmGetUserGroups.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUserGroupsParamPtrs{}
	}
	mmGetUserGroups.defaultExpectation.paramPtrs.userID = &userID
	mmGetUserGroups.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUserGroups
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUserGroups
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Inspect(f func(userID int64)) *mUserDomainServiceMockGetUserGroups {
	if mmGetUserGroups.mock.inspectFuncGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUserGroups")
	}

	mmGetUserGroups.mock.inspectFuncGetUserGroups = f

	return mmGetUserGroups
}

// Return sets up results that will be returned by UserDomainService.GetUserGroups
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Return(ia1 []int64, err error) *UserDomainServiceMock {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserDomainServiceMock.GetUserGroups mock is already set by Set")
	}

	if mmGetUserGroups.defaultExpectation == nil {
		mmGetUserGroups.defaultExpectation = &UserDomainServiceMockGetUserGroupsExpectation{mock: mmGetUserGroups.mock}
	}
	mmGetUserGroups.defaultExpectation.results = &UserDomainServiceMockGetUserGroupsResults{ia1, err}
	mmGetUserGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserGroups.mock
}

// Set uses given function f to mock the UserDomainService.GetUserGroups method
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Set(f func(userID int64) (ia1 []int64, err error)) *UserDomainServiceMock {
	if mmGetUserGroups.defaultExpectation != nil {
		mmGetUserGroups.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUserGroups method")
	}

	if len(mmGetUserGroups.expectations) > 0 {
		mmGetUserGroups.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUserGroups method")
	}

	mmGetUserGroups.mock.funcGetUserGroups = f
	mmGetUserGroups.mock.funcGetUserGroupsOrigin = minimock.CallerInfo(1)
	return mmGetUserGroups.mock
}

// When sets expectation for the UserDomainService.GetUserGroups which will trigger the result defined by the following
// Then helper
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) When(userID int64) *UserDomainServiceMockGetUserGroupsExpectation {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserDomainServiceMock.GetUserGroups mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUserGroupsExpectation{
		mock:               mmGetUserGroups.mock,
		params:             &UserDomainServiceMockGetUserGroupsParams{userID},
		expectationOrigins: UserDomainServiceMockGetUserGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserGroups.expectations = append(mmGetUserGroups.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUserGroups return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUserGroupsExpectation) Then(ia1 []int64, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUserGroupsResults{ia1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUserGroups should be invoked
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Times(n uint64) *mUserDomainServiceMockGetUserGroups {
	if n == 0 {
		mmGetUserGroups.mock.t.Fatalf("Times of UserDomainServiceMock.GetUserGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserGroups.expectedInvocations, n)
	mmGetUserGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserGroups
}

func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) invocationsDone() bool {
	if len(mmGetUserGroups.expectations) == 0 && mmGetUserGroups.defaultExpectation == nil && mmGetUserGroups.mock.funcGetUserGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserGroups.mock.afterGetUserGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserGroups implements mm_service.UserDomainService
func (mmGetUserGroups *UserDomainServiceMock) GetUserGroups(userID int64) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmGetUserGroups.beforeGetUserGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserGroups.afterGetUserGroupsCounter, 1)

	mmGetUserGroups.t.Helper()

	if mmGetUserGroups.inspectFuncGetUserGroups != nil {
		mmGetUserGroups.inspectFuncGetUserGroups(userID)
	}

	mm_params := UserDomainServiceMockGetUserGroupsParams{userID}

	// Record call args
	mmGetUserGroups.GetUserGroupsMock.mutex.Lock()
	mmGetUserGroups.GetUserGroupsMock.callArgs = append(mmGetUserGroups.GetUserGroupsMock.callArgs, &mm_params)
	mmGetUserGroups.GetUserGroupsMock.mutex.Unlock()

	for _, e := range mmGetUserGroups.GetUserGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmGetUserGroups.GetUserGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserGroups.GetUserGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserGroups.GetUserGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserGroups.GetUserGroupsMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUserGroupsParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUserGroups.t.Errorf("UserDomainServiceMock.GetUserGroups got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserGroups.GetUserGroupsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserGroups.t.Errorf("UserDomainServiceMock.GetUserGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserGroups.GetUserGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserGroups.GetUserGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserGroups.t.Fatal("No results are set for the UserDomainServiceMock.GetUserGroups")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmGetUserGroups.funcGetUserGroups != nil {
		return mmGetUserGroups.funcGetUserGroups(userID)
	}
	mmGetUserGroups.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUserGroups. %v", userID)
	return
}

// GetUserGroupsAfterCounter returns a count of finished UserDomainServiceMock.GetUserGroups invocations
func (mmGetUserGroups *UserDomainServiceMock) GetUserGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroups.afterGetUserGroupsCounter)
}

// GetUserGroupsBeforeCounter returns a count of UserDomainServiceMock.GetUserGroups invocations
func (mmGetUserGroups *UserDomainServiceMock) GetUserGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroups.beforeGetUserGroupsCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUserGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserGroups *mUserDomainServiceMockGetUserGroups) Calls() []*UserDomainServiceMockGetUserGroupsParams {
	mmGetUserGroups.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUserGroupsParams, len(mmGetUserGroups.callArgs))
	copy(argCopy, mmGetUserGroups.callArgs)

	mmGetUserGroups.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserGroupsDone returns true if the count of the GetUserGroups invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUserGroupsDone() bool {
	if m.GetUserGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserGroupsMock.invocationsDone()
}

// MinimockGetUserGroupsInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUserGroupsInspect() {
	for _, e := range m.GetUserGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserGroupsCounter := mm_atomic.LoadUint64(&m.afterGetUserGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserGroupsMock.defaultExpectation != nil && afterGetUserGroupsCounter < 1 {
		if m.GetUserGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroups at\n%s", m.GetUserGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroups at\n%s with params: %#v", m.GetUserGroupsMock.defaultExpectation.expectationOrigins.origin, *m.GetUserGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserGroups != nil && afterGetUserGroupsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroups at\n%s", m.funcGetUserGroupsOrigin)
	}

	if !m.GetUserGroupsMock.invocationsDone() && afterGetUserGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUserGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserGroupsMock.expectedInvocations), m.GetUserGroupsMock.expectedInvocationsOrigin, afterGetUserGroupsCounter)
	}
}

type mUserDomainServiceMockGetUserGroupsByGroupID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUserGroupsByGroupIDExpectation
	expectations       []*UserDomainServiceMockGetUserGroupsByGroupIDExpectation

	callArgs []*UserDomainServiceMockGetUserGroupsByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUserGroupsByGroupIDExpectation specifies expectation struct of the UserDomainService.GetUserGroupsByGroupID
type UserDomainServiceMockGetUserGroupsByGroupIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUserGroupsByGroupIDParams
	paramPtrs          *UserDomainServiceMockGetUserGroupsByGroupIDParamPtrs
	expectationOrigins UserDomainServiceMockGetUserGroupsByGroupIDExpectationOrigins
	results            *UserDomainServiceMockGetUserGroupsByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUserGroupsByGroupIDParams contains parameters of the UserDomainService.GetUserGroupsByGroupID
type UserDomainServiceMockGetUserGroupsByGroupIDParams struct {
	groupID int64
}

// UserDomainServiceMockGetUserGroupsByGroupIDParamPtrs contains pointers to parameters of the UserDomainService.GetUserGroupsByGroupID
type UserDomainServiceMockGetUserGroupsByGroupIDParamPtrs struct {
	groupID *int64
}

// UserDomainServiceMockGetUserGroupsByGroupIDResults contains results of the UserDomainService.GetUserGroupsByGroupID
type UserDomainServiceMockGetUserGroupsByGroupIDResults struct {
	ua1 []userentity.UserWithProduct
	err error
}

// UserDomainServiceMockGetUserGroupsByGroupIDOrigins contains origins of expectations of the UserDomainService.GetUserGroupsByGroupID
type UserDomainServiceMockGetUserGroupsByGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Optional() *mUserDomainServiceMockGetUserGroupsByGroupID {
	mmGetUserGroupsByGroupID.optional = true
	return mmGetUserGroupsByGroupID
}

// Expect sets up expected params for UserDomainService.GetUserGroupsByGroupID
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Expect(groupID int64) *mUserDomainServiceMockGetUserGroupsByGroupID {
	if mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupID != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByGroupID mock is already set by Set")
	}

	if mmGetUserGroupsByGroupID.defaultExpectation == nil {
		mmGetUserGroupsByGroupID.defaultExpectation = &UserDomainServiceMockGetUserGroupsByGroupIDExpectation{}
	}

	if mmGetUserGroupsByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByGroupID mock is already set by ExpectParams functions")
	}

	mmGetUserGroupsByGroupID.defaultExpectation.params = &UserDomainServiceMockGetUserGroupsByGroupIDParams{groupID}
	mmGetUserGroupsByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserGroupsByGroupID.expectations {
		if minimock.Equal(e.params, mmGetUserGroupsByGroupID.defaultExpectation.params) {
			mmGetUserGroupsByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserGroupsByGroupID.defaultExpectation.params)
		}
	}

	return mmGetUserGroupsByGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for UserDomainService.GetUserGroupsByGroupID
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) ExpectGroupIDParam1(groupID int64) *mUserDomainServiceMockGetUserGroupsByGroupID {
	if mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupID != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByGroupID mock is already set by Set")
	}

	if mmGetUserGroupsByGroupID.defaultExpectation == nil {
		mmGetUserGroupsByGroupID.defaultExpectation = &UserDomainServiceMockGetUserGroupsByGroupIDExpectation{}
	}

	if mmGetUserGroupsByGroupID.defaultExpectation.params != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByGroupID mock is already set by Expect")
	}

	if mmGetUserGroupsByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetUserGroupsByGroupID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUserGroupsByGroupIDParamPtrs{}
	}
	mmGetUserGroupsByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetUserGroupsByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetUserGroupsByGroupID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUserGroupsByGroupID
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Inspect(f func(groupID int64)) *mUserDomainServiceMockGetUserGroupsByGroupID {
	if mmGetUserGroupsByGroupID.mock.inspectFuncGetUserGroupsByGroupID != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUserGroupsByGroupID")
	}

	mmGetUserGroupsByGroupID.mock.inspectFuncGetUserGroupsByGroupID = f

	return mmGetUserGroupsByGroupID
}

// Return sets up results that will be returned by UserDomainService.GetUserGroupsByGroupID
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Return(ua1 []userentity.UserWithProduct, err error) *UserDomainServiceMock {
	if mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupID != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByGroupID mock is already set by Set")
	}

	if mmGetUserGroupsByGroupID.defaultExpectation == nil {
		mmGetUserGroupsByGroupID.defaultExpectation = &UserDomainServiceMockGetUserGroupsByGroupIDExpectation{mock: mmGetUserGroupsByGroupID.mock}
	}
	mmGetUserGroupsByGroupID.defaultExpectation.results = &UserDomainServiceMockGetUserGroupsByGroupIDResults{ua1, err}
	mmGetUserGroupsByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserGroupsByGroupID.mock
}

// Set uses given function f to mock the UserDomainService.GetUserGroupsByGroupID method
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Set(f func(groupID int64) (ua1 []userentity.UserWithProduct, err error)) *UserDomainServiceMock {
	if mmGetUserGroupsByGroupID.defaultExpectation != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUserGroupsByGroupID method")
	}

	if len(mmGetUserGroupsByGroupID.expectations) > 0 {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUserGroupsByGroupID method")
	}

	mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupID = f
	mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetUserGroupsByGroupID.mock
}

// When sets expectation for the UserDomainService.GetUserGroupsByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) When(groupID int64) *UserDomainServiceMockGetUserGroupsByGroupIDExpectation {
	if mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupID != nil {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByGroupID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUserGroupsByGroupIDExpectation{
		mock:               mmGetUserGroupsByGroupID.mock,
		params:             &UserDomainServiceMockGetUserGroupsByGroupIDParams{groupID},
		expectationOrigins: UserDomainServiceMockGetUserGroupsByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserGroupsByGroupID.expectations = append(mmGetUserGroupsByGroupID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUserGroupsByGroupID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUserGroupsByGroupIDExpectation) Then(ua1 []userentity.UserWithProduct, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUserGroupsByGroupIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUserGroupsByGroupID should be invoked
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Times(n uint64) *mUserDomainServiceMockGetUserGroupsByGroupID {
	if n == 0 {
		mmGetUserGroupsByGroupID.mock.t.Fatalf("Times of UserDomainServiceMock.GetUserGroupsByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserGroupsByGroupID.expectedInvocations, n)
	mmGetUserGroupsByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserGroupsByGroupID
}

func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) invocationsDone() bool {
	if len(mmGetUserGroupsByGroupID.expectations) == 0 && mmGetUserGroupsByGroupID.defaultExpectation == nil && mmGetUserGroupsByGroupID.mock.funcGetUserGroupsByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserGroupsByGroupID.mock.afterGetUserGroupsByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserGroupsByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserGroupsByGroupID implements mm_service.UserDomainService
func (mmGetUserGroupsByGroupID *UserDomainServiceMock) GetUserGroupsByGroupID(groupID int64) (ua1 []userentity.UserWithProduct, err error) {
	mm_atomic.AddUint64(&mmGetUserGroupsByGroupID.beforeGetUserGroupsByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserGroupsByGroupID.afterGetUserGroupsByGroupIDCounter, 1)

	mmGetUserGroupsByGroupID.t.Helper()

	if mmGetUserGroupsByGroupID.inspectFuncGetUserGroupsByGroupID != nil {
		mmGetUserGroupsByGroupID.inspectFuncGetUserGroupsByGroupID(groupID)
	}

	mm_params := UserDomainServiceMockGetUserGroupsByGroupIDParams{groupID}

	// Record call args
	mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.mutex.Lock()
	mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.callArgs = append(mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.callArgs, &mm_params)
	mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUserGroupsByGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetUserGroupsByGroupID.t.Errorf("UserDomainServiceMock.GetUserGroupsByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserGroupsByGroupID.t.Errorf("UserDomainServiceMock.GetUserGroupsByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserGroupsByGroupID.GetUserGroupsByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserGroupsByGroupID.t.Fatal("No results are set for the UserDomainServiceMock.GetUserGroupsByGroupID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUserGroupsByGroupID.funcGetUserGroupsByGroupID != nil {
		return mmGetUserGroupsByGroupID.funcGetUserGroupsByGroupID(groupID)
	}
	mmGetUserGroupsByGroupID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUserGroupsByGroupID. %v", groupID)
	return
}

// GetUserGroupsByGroupIDAfterCounter returns a count of finished UserDomainServiceMock.GetUserGroupsByGroupID invocations
func (mmGetUserGroupsByGroupID *UserDomainServiceMock) GetUserGroupsByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroupsByGroupID.afterGetUserGroupsByGroupIDCounter)
}

// GetUserGroupsByGroupIDBeforeCounter returns a count of UserDomainServiceMock.GetUserGroupsByGroupID invocations
func (mmGetUserGroupsByGroupID *UserDomainServiceMock) GetUserGroupsByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroupsByGroupID.beforeGetUserGroupsByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUserGroupsByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserGroupsByGroupID *mUserDomainServiceMockGetUserGroupsByGroupID) Calls() []*UserDomainServiceMockGetUserGroupsByGroupIDParams {
	mmGetUserGroupsByGroupID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUserGroupsByGroupIDParams, len(mmGetUserGroupsByGroupID.callArgs))
	copy(argCopy, mmGetUserGroupsByGroupID.callArgs)

	mmGetUserGroupsByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserGroupsByGroupIDDone returns true if the count of the GetUserGroupsByGroupID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUserGroupsByGroupIDDone() bool {
	if m.GetUserGroupsByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserGroupsByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserGroupsByGroupIDMock.invocationsDone()
}

// MinimockGetUserGroupsByGroupIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUserGroupsByGroupIDInspect() {
	for _, e := range m.GetUserGroupsByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserGroupsByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetUserGroupsByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserGroupsByGroupIDMock.defaultExpectation != nil && afterGetUserGroupsByGroupIDCounter < 1 {
		if m.GetUserGroupsByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByGroupID at\n%s", m.GetUserGroupsByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByGroupID at\n%s with params: %#v", m.GetUserGroupsByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUserGroupsByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserGroupsByGroupID != nil && afterGetUserGroupsByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByGroupID at\n%s", m.funcGetUserGroupsByGroupIDOrigin)
	}

	if !m.GetUserGroupsByGroupIDMock.invocationsDone() && afterGetUserGroupsByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUserGroupsByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserGroupsByGroupIDMock.expectedInvocations), m.GetUserGroupsByGroupIDMock.expectedInvocationsOrigin, afterGetUserGroupsByGroupIDCounter)
	}
}

type mUserDomainServiceMockGetUserGroupsByUserID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUserGroupsByUserIDExpectation
	expectations       []*UserDomainServiceMockGetUserGroupsByUserIDExpectation

	callArgs []*UserDomainServiceMockGetUserGroupsByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUserGroupsByUserIDExpectation specifies expectation struct of the UserDomainService.GetUserGroupsByUserID
type UserDomainServiceMockGetUserGroupsByUserIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUserGroupsByUserIDParams
	paramPtrs          *UserDomainServiceMockGetUserGroupsByUserIDParamPtrs
	expectationOrigins UserDomainServiceMockGetUserGroupsByUserIDExpectationOrigins
	results            *UserDomainServiceMockGetUserGroupsByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUserGroupsByUserIDParams contains parameters of the UserDomainService.GetUserGroupsByUserID
type UserDomainServiceMockGetUserGroupsByUserIDParams struct {
	id int64
}

// UserDomainServiceMockGetUserGroupsByUserIDParamPtrs contains pointers to parameters of the UserDomainService.GetUserGroupsByUserID
type UserDomainServiceMockGetUserGroupsByUserIDParamPtrs struct {
	id *int64
}

// UserDomainServiceMockGetUserGroupsByUserIDResults contains results of the UserDomainService.GetUserGroupsByUserID
type UserDomainServiceMockGetUserGroupsByUserIDResults struct {
	ua1 []userentity.UserGroup
	err error
}

// UserDomainServiceMockGetUserGroupsByUserIDOrigins contains origins of expectations of the UserDomainService.GetUserGroupsByUserID
type UserDomainServiceMockGetUserGroupsByUserIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Optional() *mUserDomainServiceMockGetUserGroupsByUserID {
	mmGetUserGroupsByUserID.optional = true
	return mmGetUserGroupsByUserID
}

// Expect sets up expected params for UserDomainService.GetUserGroupsByUserID
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Expect(id int64) *mUserDomainServiceMockGetUserGroupsByUserID {
	if mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserID != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByUserID mock is already set by Set")
	}

	if mmGetUserGroupsByUserID.defaultExpectation == nil {
		mmGetUserGroupsByUserID.defaultExpectation = &UserDomainServiceMockGetUserGroupsByUserIDExpectation{}
	}

	if mmGetUserGroupsByUserID.defaultExpectation.paramPtrs != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByUserID mock is already set by ExpectParams functions")
	}

	mmGetUserGroupsByUserID.defaultExpectation.params = &UserDomainServiceMockGetUserGroupsByUserIDParams{id}
	mmGetUserGroupsByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserGroupsByUserID.expectations {
		if minimock.Equal(e.params, mmGetUserGroupsByUserID.defaultExpectation.params) {
			mmGetUserGroupsByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserGroupsByUserID.defaultExpectation.params)
		}
	}

	return mmGetUserGroupsByUserID
}

// ExpectIdParam1 sets up expected param id for UserDomainService.GetUserGroupsByUserID
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) ExpectIdParam1(id int64) *mUserDomainServiceMockGetUserGroupsByUserID {
	if mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserID != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByUserID mock is already set by Set")
	}

	if mmGetUserGroupsByUserID.defaultExpectation == nil {
		mmGetUserGroupsByUserID.defaultExpectation = &UserDomainServiceMockGetUserGroupsByUserIDExpectation{}
	}

	if mmGetUserGroupsByUserID.defaultExpectation.params != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByUserID mock is already set by Expect")
	}

	if mmGetUserGroupsByUserID.defaultExpectation.paramPtrs == nil {
		mmGetUserGroupsByUserID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUserGroupsByUserIDParamPtrs{}
	}
	mmGetUserGroupsByUserID.defaultExpectation.paramPtrs.id = &id
	mmGetUserGroupsByUserID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetUserGroupsByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUserGroupsByUserID
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Inspect(f func(id int64)) *mUserDomainServiceMockGetUserGroupsByUserID {
	if mmGetUserGroupsByUserID.mock.inspectFuncGetUserGroupsByUserID != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUserGroupsByUserID")
	}

	mmGetUserGroupsByUserID.mock.inspectFuncGetUserGroupsByUserID = f

	return mmGetUserGroupsByUserID
}

// Return sets up results that will be returned by UserDomainService.GetUserGroupsByUserID
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Return(ua1 []userentity.UserGroup, err error) *UserDomainServiceMock {
	if mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserID != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByUserID mock is already set by Set")
	}

	if mmGetUserGroupsByUserID.defaultExpectation == nil {
		mmGetUserGroupsByUserID.defaultExpectation = &UserDomainServiceMockGetUserGroupsByUserIDExpectation{mock: mmGetUserGroupsByUserID.mock}
	}
	mmGetUserGroupsByUserID.defaultExpectation.results = &UserDomainServiceMockGetUserGroupsByUserIDResults{ua1, err}
	mmGetUserGroupsByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserGroupsByUserID.mock
}

// Set uses given function f to mock the UserDomainService.GetUserGroupsByUserID method
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Set(f func(id int64) (ua1 []userentity.UserGroup, err error)) *UserDomainServiceMock {
	if mmGetUserGroupsByUserID.defaultExpectation != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUserGroupsByUserID method")
	}

	if len(mmGetUserGroupsByUserID.expectations) > 0 {
		mmGetUserGroupsByUserID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUserGroupsByUserID method")
	}

	mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserID = f
	mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetUserGroupsByUserID.mock
}

// When sets expectation for the UserDomainService.GetUserGroupsByUserID which will trigger the result defined by the following
// Then helper
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) When(id int64) *UserDomainServiceMockGetUserGroupsByUserIDExpectation {
	if mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserID != nil {
		mmGetUserGroupsByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserGroupsByUserID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUserGroupsByUserIDExpectation{
		mock:               mmGetUserGroupsByUserID.mock,
		params:             &UserDomainServiceMockGetUserGroupsByUserIDParams{id},
		expectationOrigins: UserDomainServiceMockGetUserGroupsByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserGroupsByUserID.expectations = append(mmGetUserGroupsByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUserGroupsByUserID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUserGroupsByUserIDExpectation) Then(ua1 []userentity.UserGroup, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUserGroupsByUserIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUserGroupsByUserID should be invoked
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Times(n uint64) *mUserDomainServiceMockGetUserGroupsByUserID {
	if n == 0 {
		mmGetUserGroupsByUserID.mock.t.Fatalf("Times of UserDomainServiceMock.GetUserGroupsByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserGroupsByUserID.expectedInvocations, n)
	mmGetUserGroupsByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserGroupsByUserID
}

func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) invocationsDone() bool {
	if len(mmGetUserGroupsByUserID.expectations) == 0 && mmGetUserGroupsByUserID.defaultExpectation == nil && mmGetUserGroupsByUserID.mock.funcGetUserGroupsByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserGroupsByUserID.mock.afterGetUserGroupsByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserGroupsByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserGroupsByUserID implements mm_service.UserDomainService
func (mmGetUserGroupsByUserID *UserDomainServiceMock) GetUserGroupsByUserID(id int64) (ua1 []userentity.UserGroup, err error) {
	mm_atomic.AddUint64(&mmGetUserGroupsByUserID.beforeGetUserGroupsByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserGroupsByUserID.afterGetUserGroupsByUserIDCounter, 1)

	mmGetUserGroupsByUserID.t.Helper()

	if mmGetUserGroupsByUserID.inspectFuncGetUserGroupsByUserID != nil {
		mmGetUserGroupsByUserID.inspectFuncGetUserGroupsByUserID(id)
	}

	mm_params := UserDomainServiceMockGetUserGroupsByUserIDParams{id}

	// Record call args
	mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.mutex.Lock()
	mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.callArgs = append(mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.callArgs, &mm_params)
	mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.mutex.Unlock()

	for _, e := range mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUserGroupsByUserIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetUserGroupsByUserID.t.Errorf("UserDomainServiceMock.GetUserGroupsByUserID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserGroupsByUserID.t.Errorf("UserDomainServiceMock.GetUserGroupsByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserGroupsByUserID.GetUserGroupsByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserGroupsByUserID.t.Fatal("No results are set for the UserDomainServiceMock.GetUserGroupsByUserID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUserGroupsByUserID.funcGetUserGroupsByUserID != nil {
		return mmGetUserGroupsByUserID.funcGetUserGroupsByUserID(id)
	}
	mmGetUserGroupsByUserID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUserGroupsByUserID. %v", id)
	return
}

// GetUserGroupsByUserIDAfterCounter returns a count of finished UserDomainServiceMock.GetUserGroupsByUserID invocations
func (mmGetUserGroupsByUserID *UserDomainServiceMock) GetUserGroupsByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroupsByUserID.afterGetUserGroupsByUserIDCounter)
}

// GetUserGroupsByUserIDBeforeCounter returns a count of UserDomainServiceMock.GetUserGroupsByUserID invocations
func (mmGetUserGroupsByUserID *UserDomainServiceMock) GetUserGroupsByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroupsByUserID.beforeGetUserGroupsByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUserGroupsByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserGroupsByUserID *mUserDomainServiceMockGetUserGroupsByUserID) Calls() []*UserDomainServiceMockGetUserGroupsByUserIDParams {
	mmGetUserGroupsByUserID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUserGroupsByUserIDParams, len(mmGetUserGroupsByUserID.callArgs))
	copy(argCopy, mmGetUserGroupsByUserID.callArgs)

	mmGetUserGroupsByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserGroupsByUserIDDone returns true if the count of the GetUserGroupsByUserID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUserGroupsByUserIDDone() bool {
	if m.GetUserGroupsByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserGroupsByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserGroupsByUserIDMock.invocationsDone()
}

// MinimockGetUserGroupsByUserIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUserGroupsByUserIDInspect() {
	for _, e := range m.GetUserGroupsByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserGroupsByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetUserGroupsByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserGroupsByUserIDMock.defaultExpectation != nil && afterGetUserGroupsByUserIDCounter < 1 {
		if m.GetUserGroupsByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByUserID at\n%s", m.GetUserGroupsByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByUserID at\n%s with params: %#v", m.GetUserGroupsByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUserGroupsByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserGroupsByUserID != nil && afterGetUserGroupsByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUserGroupsByUserID at\n%s", m.funcGetUserGroupsByUserIDOrigin)
	}

	if !m.GetUserGroupsByUserIDMock.invocationsDone() && afterGetUserGroupsByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUserGroupsByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserGroupsByUserIDMock.expectedInvocations), m.GetUserGroupsByUserIDMock.expectedInvocationsOrigin, afterGetUserGroupsByUserIDCounter)
	}
}

type mUserDomainServiceMockGetUserRolesByRoleID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUserRolesByRoleIDExpectation
	expectations       []*UserDomainServiceMockGetUserRolesByRoleIDExpectation

	callArgs []*UserDomainServiceMockGetUserRolesByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUserRolesByRoleIDExpectation specifies expectation struct of the UserDomainService.GetUserRolesByRoleID
type UserDomainServiceMockGetUserRolesByRoleIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUserRolesByRoleIDParams
	paramPtrs          *UserDomainServiceMockGetUserRolesByRoleIDParamPtrs
	expectationOrigins UserDomainServiceMockGetUserRolesByRoleIDExpectationOrigins
	results            *UserDomainServiceMockGetUserRolesByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUserRolesByRoleIDParams contains parameters of the UserDomainService.GetUserRolesByRoleID
type UserDomainServiceMockGetUserRolesByRoleIDParams struct {
	roleID int64
}

// UserDomainServiceMockGetUserRolesByRoleIDParamPtrs contains pointers to parameters of the UserDomainService.GetUserRolesByRoleID
type UserDomainServiceMockGetUserRolesByRoleIDParamPtrs struct {
	roleID *int64
}

// UserDomainServiceMockGetUserRolesByRoleIDResults contains results of the UserDomainService.GetUserRolesByRoleID
type UserDomainServiceMockGetUserRolesByRoleIDResults struct {
	ua1 []userentity.User
	err error
}

// UserDomainServiceMockGetUserRolesByRoleIDOrigins contains origins of expectations of the UserDomainService.GetUserRolesByRoleID
type UserDomainServiceMockGetUserRolesByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Optional() *mUserDomainServiceMockGetUserRolesByRoleID {
	mmGetUserRolesByRoleID.optional = true
	return mmGetUserRolesByRoleID
}

// Expect sets up expected params for UserDomainService.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Expect(roleID int64) *mUserDomainServiceMockGetUserRolesByRoleID {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByRoleID mock is already set by Set")
	}

	if mmGetUserRolesByRoleID.defaultExpectation == nil {
		mmGetUserRolesByRoleID.defaultExpectation = &UserDomainServiceMockGetUserRolesByRoleIDExpectation{}
	}

	if mmGetUserRolesByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByRoleID mock is already set by ExpectParams functions")
	}

	mmGetUserRolesByRoleID.defaultExpectation.params = &UserDomainServiceMockGetUserRolesByRoleIDParams{roleID}
	mmGetUserRolesByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserRolesByRoleID.expectations {
		if minimock.Equal(e.params, mmGetUserRolesByRoleID.defaultExpectation.params) {
			mmGetUserRolesByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserRolesByRoleID.defaultExpectation.params)
		}
	}

	return mmGetUserRolesByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for UserDomainService.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) ExpectRoleIDParam1(roleID int64) *mUserDomainServiceMockGetUserRolesByRoleID {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByRoleID mock is already set by Set")
	}

	if mmGetUserRolesByRoleID.defaultExpectation == nil {
		mmGetUserRolesByRoleID.defaultExpectation = &UserDomainServiceMockGetUserRolesByRoleIDExpectation{}
	}

	if mmGetUserRolesByRoleID.defaultExpectation.params != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByRoleID mock is already set by Expect")
	}

	if mmGetUserRolesByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetUserRolesByRoleID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUserRolesByRoleIDParamPtrs{}
	}
	mmGetUserRolesByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetUserRolesByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetUserRolesByRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Inspect(f func(roleID int64)) *mUserDomainServiceMockGetUserRolesByRoleID {
	if mmGetUserRolesByRoleID.mock.inspectFuncGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUserRolesByRoleID")
	}

	mmGetUserRolesByRoleID.mock.inspectFuncGetUserRolesByRoleID = f

	return mmGetUserRolesByRoleID
}

// Return sets up results that will be returned by UserDomainService.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Return(ua1 []userentity.User, err error) *UserDomainServiceMock {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByRoleID mock is already set by Set")
	}

	if mmGetUserRolesByRoleID.defaultExpectation == nil {
		mmGetUserRolesByRoleID.defaultExpectation = &UserDomainServiceMockGetUserRolesByRoleIDExpectation{mock: mmGetUserRolesByRoleID.mock}
	}
	mmGetUserRolesByRoleID.defaultExpectation.results = &UserDomainServiceMockGetUserRolesByRoleIDResults{ua1, err}
	mmGetUserRolesByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByRoleID.mock
}

// Set uses given function f to mock the UserDomainService.GetUserRolesByRoleID method
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Set(f func(roleID int64) (ua1 []userentity.User, err error)) *UserDomainServiceMock {
	if mmGetUserRolesByRoleID.defaultExpectation != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUserRolesByRoleID method")
	}

	if len(mmGetUserRolesByRoleID.expectations) > 0 {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUserRolesByRoleID method")
	}

	mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID = f
	mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByRoleID.mock
}

// When sets expectation for the UserDomainService.GetUserRolesByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) When(roleID int64) *UserDomainServiceMockGetUserRolesByRoleIDExpectation {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByRoleID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUserRolesByRoleIDExpectation{
		mock:               mmGetUserRolesByRoleID.mock,
		params:             &UserDomainServiceMockGetUserRolesByRoleIDParams{roleID},
		expectationOrigins: UserDomainServiceMockGetUserRolesByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserRolesByRoleID.expectations = append(mmGetUserRolesByRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUserRolesByRoleID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUserRolesByRoleIDExpectation) Then(ua1 []userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUserRolesByRoleIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUserRolesByRoleID should be invoked
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Times(n uint64) *mUserDomainServiceMockGetUserRolesByRoleID {
	if n == 0 {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Times of UserDomainServiceMock.GetUserRolesByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserRolesByRoleID.expectedInvocations, n)
	mmGetUserRolesByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByRoleID
}

func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) invocationsDone() bool {
	if len(mmGetUserRolesByRoleID.expectations) == 0 && mmGetUserRolesByRoleID.defaultExpectation == nil && mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.mock.afterGetUserRolesByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserRolesByRoleID implements mm_service.UserDomainService
func (mmGetUserRolesByRoleID *UserDomainServiceMock) GetUserRolesByRoleID(roleID int64) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetUserRolesByRoleID.beforeGetUserRolesByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserRolesByRoleID.afterGetUserRolesByRoleIDCounter, 1)

	mmGetUserRolesByRoleID.t.Helper()

	if mmGetUserRolesByRoleID.inspectFuncGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.inspectFuncGetUserRolesByRoleID(roleID)
	}

	mm_params := UserDomainServiceMockGetUserRolesByRoleIDParams{roleID}

	// Record call args
	mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.mutex.Lock()
	mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.callArgs = append(mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.callArgs, &mm_params)
	mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUserRolesByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetUserRolesByRoleID.t.Errorf("UserDomainServiceMock.GetUserRolesByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserRolesByRoleID.t.Errorf("UserDomainServiceMock.GetUserRolesByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserRolesByRoleID.t.Fatal("No results are set for the UserDomainServiceMock.GetUserRolesByRoleID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUserRolesByRoleID.funcGetUserRolesByRoleID != nil {
		return mmGetUserRolesByRoleID.funcGetUserRolesByRoleID(roleID)
	}
	mmGetUserRolesByRoleID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUserRolesByRoleID. %v", roleID)
	return
}

// GetUserRolesByRoleIDAfterCounter returns a count of finished UserDomainServiceMock.GetUserRolesByRoleID invocations
func (mmGetUserRolesByRoleID *UserDomainServiceMock) GetUserRolesByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.afterGetUserRolesByRoleIDCounter)
}

// GetUserRolesByRoleIDBeforeCounter returns a count of UserDomainServiceMock.GetUserRolesByRoleID invocations
func (mmGetUserRolesByRoleID *UserDomainServiceMock) GetUserRolesByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.beforeGetUserRolesByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUserRolesByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserRolesByRoleID *mUserDomainServiceMockGetUserRolesByRoleID) Calls() []*UserDomainServiceMockGetUserRolesByRoleIDParams {
	mmGetUserRolesByRoleID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUserRolesByRoleIDParams, len(mmGetUserRolesByRoleID.callArgs))
	copy(argCopy, mmGetUserRolesByRoleID.callArgs)

	mmGetUserRolesByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserRolesByRoleIDDone returns true if the count of the GetUserRolesByRoleID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUserRolesByRoleIDDone() bool {
	if m.GetUserRolesByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserRolesByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserRolesByRoleIDMock.invocationsDone()
}

// MinimockGetUserRolesByRoleIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUserRolesByRoleIDInspect() {
	for _, e := range m.GetUserRolesByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserRolesByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetUserRolesByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserRolesByRoleIDMock.defaultExpectation != nil && afterGetUserRolesByRoleIDCounter < 1 {
		if m.GetUserRolesByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByRoleID at\n%s", m.GetUserRolesByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByRoleID at\n%s with params: %#v", m.GetUserRolesByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUserRolesByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserRolesByRoleID != nil && afterGetUserRolesByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByRoleID at\n%s", m.funcGetUserRolesByRoleIDOrigin)
	}

	if !m.GetUserRolesByRoleIDMock.invocationsDone() && afterGetUserRolesByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUserRolesByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserRolesByRoleIDMock.expectedInvocations), m.GetUserRolesByRoleIDMock.expectedInvocationsOrigin, afterGetUserRolesByRoleIDCounter)
	}
}

type mUserDomainServiceMockGetUserRolesByUserID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUserRolesByUserIDExpectation
	expectations       []*UserDomainServiceMockGetUserRolesByUserIDExpectation

	callArgs []*UserDomainServiceMockGetUserRolesByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUserRolesByUserIDExpectation specifies expectation struct of the UserDomainService.GetUserRolesByUserID
type UserDomainServiceMockGetUserRolesByUserIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUserRolesByUserIDParams
	paramPtrs          *UserDomainServiceMockGetUserRolesByUserIDParamPtrs
	expectationOrigins UserDomainServiceMockGetUserRolesByUserIDExpectationOrigins
	results            *UserDomainServiceMockGetUserRolesByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUserRolesByUserIDParams contains parameters of the UserDomainService.GetUserRolesByUserID
type UserDomainServiceMockGetUserRolesByUserIDParams struct {
	id int64
}

// UserDomainServiceMockGetUserRolesByUserIDParamPtrs contains pointers to parameters of the UserDomainService.GetUserRolesByUserID
type UserDomainServiceMockGetUserRolesByUserIDParamPtrs struct {
	id *int64
}

// UserDomainServiceMockGetUserRolesByUserIDResults contains results of the UserDomainService.GetUserRolesByUserID
type UserDomainServiceMockGetUserRolesByUserIDResults struct {
	ua1 []userentity.UserRole
	err error
}

// UserDomainServiceMockGetUserRolesByUserIDOrigins contains origins of expectations of the UserDomainService.GetUserRolesByUserID
type UserDomainServiceMockGetUserRolesByUserIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Optional() *mUserDomainServiceMockGetUserRolesByUserID {
	mmGetUserRolesByUserID.optional = true
	return mmGetUserRolesByUserID
}

// Expect sets up expected params for UserDomainService.GetUserRolesByUserID
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Expect(id int64) *mUserDomainServiceMockGetUserRolesByUserID {
	if mmGetUserRolesByUserID.mock.funcGetUserRolesByUserID != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByUserID mock is already set by Set")
	}

	if mmGetUserRolesByUserID.defaultExpectation == nil {
		mmGetUserRolesByUserID.defaultExpectation = &UserDomainServiceMockGetUserRolesByUserIDExpectation{}
	}

	if mmGetUserRolesByUserID.defaultExpectation.paramPtrs != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByUserID mock is already set by ExpectParams functions")
	}

	mmGetUserRolesByUserID.defaultExpectation.params = &UserDomainServiceMockGetUserRolesByUserIDParams{id}
	mmGetUserRolesByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserRolesByUserID.expectations {
		if minimock.Equal(e.params, mmGetUserRolesByUserID.defaultExpectation.params) {
			mmGetUserRolesByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserRolesByUserID.defaultExpectation.params)
		}
	}

	return mmGetUserRolesByUserID
}

// ExpectIdParam1 sets up expected param id for UserDomainService.GetUserRolesByUserID
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) ExpectIdParam1(id int64) *mUserDomainServiceMockGetUserRolesByUserID {
	if mmGetUserRolesByUserID.mock.funcGetUserRolesByUserID != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByUserID mock is already set by Set")
	}

	if mmGetUserRolesByUserID.defaultExpectation == nil {
		mmGetUserRolesByUserID.defaultExpectation = &UserDomainServiceMockGetUserRolesByUserIDExpectation{}
	}

	if mmGetUserRolesByUserID.defaultExpectation.params != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByUserID mock is already set by Expect")
	}

	if mmGetUserRolesByUserID.defaultExpectation.paramPtrs == nil {
		mmGetUserRolesByUserID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUserRolesByUserIDParamPtrs{}
	}
	mmGetUserRolesByUserID.defaultExpectation.paramPtrs.id = &id
	mmGetUserRolesByUserID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetUserRolesByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUserRolesByUserID
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Inspect(f func(id int64)) *mUserDomainServiceMockGetUserRolesByUserID {
	if mmGetUserRolesByUserID.mock.inspectFuncGetUserRolesByUserID != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUserRolesByUserID")
	}

	mmGetUserRolesByUserID.mock.inspectFuncGetUserRolesByUserID = f

	return mmGetUserRolesByUserID
}

// Return sets up results that will be returned by UserDomainService.GetUserRolesByUserID
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Return(ua1 []userentity.UserRole, err error) *UserDomainServiceMock {
	if mmGetUserRolesByUserID.mock.funcGetUserRolesByUserID != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByUserID mock is already set by Set")
	}

	if mmGetUserRolesByUserID.defaultExpectation == nil {
		mmGetUserRolesByUserID.defaultExpectation = &UserDomainServiceMockGetUserRolesByUserIDExpectation{mock: mmGetUserRolesByUserID.mock}
	}
	mmGetUserRolesByUserID.defaultExpectation.results = &UserDomainServiceMockGetUserRolesByUserIDResults{ua1, err}
	mmGetUserRolesByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByUserID.mock
}

// Set uses given function f to mock the UserDomainService.GetUserRolesByUserID method
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Set(f func(id int64) (ua1 []userentity.UserRole, err error)) *UserDomainServiceMock {
	if mmGetUserRolesByUserID.defaultExpectation != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUserRolesByUserID method")
	}

	if len(mmGetUserRolesByUserID.expectations) > 0 {
		mmGetUserRolesByUserID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUserRolesByUserID method")
	}

	mmGetUserRolesByUserID.mock.funcGetUserRolesByUserID = f
	mmGetUserRolesByUserID.mock.funcGetUserRolesByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByUserID.mock
}

// When sets expectation for the UserDomainService.GetUserRolesByUserID which will trigger the result defined by the following
// Then helper
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) When(id int64) *UserDomainServiceMockGetUserRolesByUserIDExpectation {
	if mmGetUserRolesByUserID.mock.funcGetUserRolesByUserID != nil {
		mmGetUserRolesByUserID.mock.t.Fatalf("UserDomainServiceMock.GetUserRolesByUserID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUserRolesByUserIDExpectation{
		mock:               mmGetUserRolesByUserID.mock,
		params:             &UserDomainServiceMockGetUserRolesByUserIDParams{id},
		expectationOrigins: UserDomainServiceMockGetUserRolesByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserRolesByUserID.expectations = append(mmGetUserRolesByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUserRolesByUserID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUserRolesByUserIDExpectation) Then(ua1 []userentity.UserRole, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUserRolesByUserIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUserRolesByUserID should be invoked
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Times(n uint64) *mUserDomainServiceMockGetUserRolesByUserID {
	if n == 0 {
		mmGetUserRolesByUserID.mock.t.Fatalf("Times of UserDomainServiceMock.GetUserRolesByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserRolesByUserID.expectedInvocations, n)
	mmGetUserRolesByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByUserID
}

func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) invocationsDone() bool {
	if len(mmGetUserRolesByUserID.expectations) == 0 && mmGetUserRolesByUserID.defaultExpectation == nil && mmGetUserRolesByUserID.mock.funcGetUserRolesByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserRolesByUserID.mock.afterGetUserRolesByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserRolesByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserRolesByUserID implements mm_service.UserDomainService
func (mmGetUserRolesByUserID *UserDomainServiceMock) GetUserRolesByUserID(id int64) (ua1 []userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmGetUserRolesByUserID.beforeGetUserRolesByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserRolesByUserID.afterGetUserRolesByUserIDCounter, 1)

	mmGetUserRolesByUserID.t.Helper()

	if mmGetUserRolesByUserID.inspectFuncGetUserRolesByUserID != nil {
		mmGetUserRolesByUserID.inspectFuncGetUserRolesByUserID(id)
	}

	mm_params := UserDomainServiceMockGetUserRolesByUserIDParams{id}

	// Record call args
	mmGetUserRolesByUserID.GetUserRolesByUserIDMock.mutex.Lock()
	mmGetUserRolesByUserID.GetUserRolesByUserIDMock.callArgs = append(mmGetUserRolesByUserID.GetUserRolesByUserIDMock.callArgs, &mm_params)
	mmGetUserRolesByUserID.GetUserRolesByUserIDMock.mutex.Unlock()

	for _, e := range mmGetUserRolesByUserID.GetUserRolesByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUserRolesByUserIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetUserRolesByUserID.t.Errorf("UserDomainServiceMock.GetUserRolesByUserID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserRolesByUserID.t.Errorf("UserDomainServiceMock.GetUserRolesByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserRolesByUserID.GetUserRolesByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserRolesByUserID.t.Fatal("No results are set for the UserDomainServiceMock.GetUserRolesByUserID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUserRolesByUserID.funcGetUserRolesByUserID != nil {
		return mmGetUserRolesByUserID.funcGetUserRolesByUserID(id)
	}
	mmGetUserRolesByUserID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUserRolesByUserID. %v", id)
	return
}

// GetUserRolesByUserIDAfterCounter returns a count of finished UserDomainServiceMock.GetUserRolesByUserID invocations
func (mmGetUserRolesByUserID *UserDomainServiceMock) GetUserRolesByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRolesByUserID.afterGetUserRolesByUserIDCounter)
}

// GetUserRolesByUserIDBeforeCounter returns a count of UserDomainServiceMock.GetUserRolesByUserID invocations
func (mmGetUserRolesByUserID *UserDomainServiceMock) GetUserRolesByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRolesByUserID.beforeGetUserRolesByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUserRolesByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserRolesByUserID *mUserDomainServiceMockGetUserRolesByUserID) Calls() []*UserDomainServiceMockGetUserRolesByUserIDParams {
	mmGetUserRolesByUserID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUserRolesByUserIDParams, len(mmGetUserRolesByUserID.callArgs))
	copy(argCopy, mmGetUserRolesByUserID.callArgs)

	mmGetUserRolesByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserRolesByUserIDDone returns true if the count of the GetUserRolesByUserID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUserRolesByUserIDDone() bool {
	if m.GetUserRolesByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserRolesByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserRolesByUserIDMock.invocationsDone()
}

// MinimockGetUserRolesByUserIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUserRolesByUserIDInspect() {
	for _, e := range m.GetUserRolesByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserRolesByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetUserRolesByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserRolesByUserIDMock.defaultExpectation != nil && afterGetUserRolesByUserIDCounter < 1 {
		if m.GetUserRolesByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByUserID at\n%s", m.GetUserRolesByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByUserID at\n%s with params: %#v", m.GetUserRolesByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUserRolesByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserRolesByUserID != nil && afterGetUserRolesByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUserRolesByUserID at\n%s", m.funcGetUserRolesByUserIDOrigin)
	}

	if !m.GetUserRolesByUserIDMock.invocationsDone() && afterGetUserRolesByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUserRolesByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserRolesByUserIDMock.expectedInvocations), m.GetUserRolesByUserIDMock.expectedInvocationsOrigin, afterGetUserRolesByUserIDCounter)
	}
}

type mUserDomainServiceMockGetUsersWithProductsByGroupID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation
	expectations       []*UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation

	callArgs []*UserDomainServiceMockGetUsersWithProductsByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation specifies expectation struct of the UserDomainService.GetUsersWithProductsByGroupID
type UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUsersWithProductsByGroupIDParams
	paramPtrs          *UserDomainServiceMockGetUsersWithProductsByGroupIDParamPtrs
	expectationOrigins UserDomainServiceMockGetUsersWithProductsByGroupIDExpectationOrigins
	results            *UserDomainServiceMockGetUsersWithProductsByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUsersWithProductsByGroupIDParams contains parameters of the UserDomainService.GetUsersWithProductsByGroupID
type UserDomainServiceMockGetUsersWithProductsByGroupIDParams struct {
	groupID int64
}

// UserDomainServiceMockGetUsersWithProductsByGroupIDParamPtrs contains pointers to parameters of the UserDomainService.GetUsersWithProductsByGroupID
type UserDomainServiceMockGetUsersWithProductsByGroupIDParamPtrs struct {
	groupID *int64
}

// UserDomainServiceMockGetUsersWithProductsByGroupIDResults contains results of the UserDomainService.GetUsersWithProductsByGroupID
type UserDomainServiceMockGetUsersWithProductsByGroupIDResults struct {
	ua1 []userentity.UserWithProduct
	err error
}

// UserDomainServiceMockGetUsersWithProductsByGroupIDOrigins contains origins of expectations of the UserDomainService.GetUsersWithProductsByGroupID
type UserDomainServiceMockGetUsersWithProductsByGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Optional() *mUserDomainServiceMockGetUsersWithProductsByGroupID {
	mmGetUsersWithProductsByGroupID.optional = true
	return mmGetUsersWithProductsByGroupID
}

// Expect sets up expected params for UserDomainService.GetUsersWithProductsByGroupID
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Expect(groupID int64) *mUserDomainServiceMockGetUsersWithProductsByGroupID {
	if mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupID != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByGroupID mock is already set by Set")
	}

	if mmGetUsersWithProductsByGroupID.defaultExpectation == nil {
		mmGetUsersWithProductsByGroupID.defaultExpectation = &UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation{}
	}

	if mmGetUsersWithProductsByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByGroupID mock is already set by ExpectParams functions")
	}

	mmGetUsersWithProductsByGroupID.defaultExpectation.params = &UserDomainServiceMockGetUsersWithProductsByGroupIDParams{groupID}
	mmGetUsersWithProductsByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersWithProductsByGroupID.expectations {
		if minimock.Equal(e.params, mmGetUsersWithProductsByGroupID.defaultExpectation.params) {
			mmGetUsersWithProductsByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersWithProductsByGroupID.defaultExpectation.params)
		}
	}

	return mmGetUsersWithProductsByGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for UserDomainService.GetUsersWithProductsByGroupID
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) ExpectGroupIDParam1(groupID int64) *mUserDomainServiceMockGetUsersWithProductsByGroupID {
	if mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupID != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByGroupID mock is already set by Set")
	}

	if mmGetUsersWithProductsByGroupID.defaultExpectation == nil {
		mmGetUsersWithProductsByGroupID.defaultExpectation = &UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation{}
	}

	if mmGetUsersWithProductsByGroupID.defaultExpectation.params != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByGroupID mock is already set by Expect")
	}

	if mmGetUsersWithProductsByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetUsersWithProductsByGroupID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUsersWithProductsByGroupIDParamPtrs{}
	}
	mmGetUsersWithProductsByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetUsersWithProductsByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetUsersWithProductsByGroupID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUsersWithProductsByGroupID
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Inspect(f func(groupID int64)) *mUserDomainServiceMockGetUsersWithProductsByGroupID {
	if mmGetUsersWithProductsByGroupID.mock.inspectFuncGetUsersWithProductsByGroupID != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUsersWithProductsByGroupID")
	}

	mmGetUsersWithProductsByGroupID.mock.inspectFuncGetUsersWithProductsByGroupID = f

	return mmGetUsersWithProductsByGroupID
}

// Return sets up results that will be returned by UserDomainService.GetUsersWithProductsByGroupID
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Return(ua1 []userentity.UserWithProduct, err error) *UserDomainServiceMock {
	if mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupID != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByGroupID mock is already set by Set")
	}

	if mmGetUsersWithProductsByGroupID.defaultExpectation == nil {
		mmGetUsersWithProductsByGroupID.defaultExpectation = &UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation{mock: mmGetUsersWithProductsByGroupID.mock}
	}
	mmGetUsersWithProductsByGroupID.defaultExpectation.results = &UserDomainServiceMockGetUsersWithProductsByGroupIDResults{ua1, err}
	mmGetUsersWithProductsByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersWithProductsByGroupID.mock
}

// Set uses given function f to mock the UserDomainService.GetUsersWithProductsByGroupID method
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Set(f func(groupID int64) (ua1 []userentity.UserWithProduct, err error)) *UserDomainServiceMock {
	if mmGetUsersWithProductsByGroupID.defaultExpectation != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUsersWithProductsByGroupID method")
	}

	if len(mmGetUsersWithProductsByGroupID.expectations) > 0 {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUsersWithProductsByGroupID method")
	}

	mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupID = f
	mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetUsersWithProductsByGroupID.mock
}

// When sets expectation for the UserDomainService.GetUsersWithProductsByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) When(groupID int64) *UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation {
	if mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupID != nil {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByGroupID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation{
		mock:               mmGetUsersWithProductsByGroupID.mock,
		params:             &UserDomainServiceMockGetUsersWithProductsByGroupIDParams{groupID},
		expectationOrigins: UserDomainServiceMockGetUsersWithProductsByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersWithProductsByGroupID.expectations = append(mmGetUsersWithProductsByGroupID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUsersWithProductsByGroupID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUsersWithProductsByGroupIDExpectation) Then(ua1 []userentity.UserWithProduct, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUsersWithProductsByGroupIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUsersWithProductsByGroupID should be invoked
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Times(n uint64) *mUserDomainServiceMockGetUsersWithProductsByGroupID {
	if n == 0 {
		mmGetUsersWithProductsByGroupID.mock.t.Fatalf("Times of UserDomainServiceMock.GetUsersWithProductsByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersWithProductsByGroupID.expectedInvocations, n)
	mmGetUsersWithProductsByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersWithProductsByGroupID
}

func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) invocationsDone() bool {
	if len(mmGetUsersWithProductsByGroupID.expectations) == 0 && mmGetUsersWithProductsByGroupID.defaultExpectation == nil && mmGetUsersWithProductsByGroupID.mock.funcGetUsersWithProductsByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersWithProductsByGroupID.mock.afterGetUsersWithProductsByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersWithProductsByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersWithProductsByGroupID implements mm_service.UserDomainService
func (mmGetUsersWithProductsByGroupID *UserDomainServiceMock) GetUsersWithProductsByGroupID(groupID int64) (ua1 []userentity.UserWithProduct, err error) {
	mm_atomic.AddUint64(&mmGetUsersWithProductsByGroupID.beforeGetUsersWithProductsByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersWithProductsByGroupID.afterGetUsersWithProductsByGroupIDCounter, 1)

	mmGetUsersWithProductsByGroupID.t.Helper()

	if mmGetUsersWithProductsByGroupID.inspectFuncGetUsersWithProductsByGroupID != nil {
		mmGetUsersWithProductsByGroupID.inspectFuncGetUsersWithProductsByGroupID(groupID)
	}

	mm_params := UserDomainServiceMockGetUsersWithProductsByGroupIDParams{groupID}

	// Record call args
	mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.mutex.Lock()
	mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.callArgs = append(mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.callArgs, &mm_params)
	mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUsersWithProductsByGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetUsersWithProductsByGroupID.t.Errorf("UserDomainServiceMock.GetUsersWithProductsByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersWithProductsByGroupID.t.Errorf("UserDomainServiceMock.GetUsersWithProductsByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersWithProductsByGroupID.GetUsersWithProductsByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersWithProductsByGroupID.t.Fatal("No results are set for the UserDomainServiceMock.GetUsersWithProductsByGroupID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUsersWithProductsByGroupID.funcGetUsersWithProductsByGroupID != nil {
		return mmGetUsersWithProductsByGroupID.funcGetUsersWithProductsByGroupID(groupID)
	}
	mmGetUsersWithProductsByGroupID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUsersWithProductsByGroupID. %v", groupID)
	return
}

// GetUsersWithProductsByGroupIDAfterCounter returns a count of finished UserDomainServiceMock.GetUsersWithProductsByGroupID invocations
func (mmGetUsersWithProductsByGroupID *UserDomainServiceMock) GetUsersWithProductsByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersWithProductsByGroupID.afterGetUsersWithProductsByGroupIDCounter)
}

// GetUsersWithProductsByGroupIDBeforeCounter returns a count of UserDomainServiceMock.GetUsersWithProductsByGroupID invocations
func (mmGetUsersWithProductsByGroupID *UserDomainServiceMock) GetUsersWithProductsByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersWithProductsByGroupID.beforeGetUsersWithProductsByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUsersWithProductsByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersWithProductsByGroupID *mUserDomainServiceMockGetUsersWithProductsByGroupID) Calls() []*UserDomainServiceMockGetUsersWithProductsByGroupIDParams {
	mmGetUsersWithProductsByGroupID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUsersWithProductsByGroupIDParams, len(mmGetUsersWithProductsByGroupID.callArgs))
	copy(argCopy, mmGetUsersWithProductsByGroupID.callArgs)

	mmGetUsersWithProductsByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersWithProductsByGroupIDDone returns true if the count of the GetUsersWithProductsByGroupID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUsersWithProductsByGroupIDDone() bool {
	if m.GetUsersWithProductsByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersWithProductsByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersWithProductsByGroupIDMock.invocationsDone()
}

// MinimockGetUsersWithProductsByGroupIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUsersWithProductsByGroupIDInspect() {
	for _, e := range m.GetUsersWithProductsByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersWithProductsByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetUsersWithProductsByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersWithProductsByGroupIDMock.defaultExpectation != nil && afterGetUsersWithProductsByGroupIDCounter < 1 {
		if m.GetUsersWithProductsByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByGroupID at\n%s", m.GetUsersWithProductsByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByGroupID at\n%s with params: %#v", m.GetUsersWithProductsByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersWithProductsByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersWithProductsByGroupID != nil && afterGetUsersWithProductsByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByGroupID at\n%s", m.funcGetUsersWithProductsByGroupIDOrigin)
	}

	if !m.GetUsersWithProductsByGroupIDMock.invocationsDone() && afterGetUsersWithProductsByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUsersWithProductsByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersWithProductsByGroupIDMock.expectedInvocations), m.GetUsersWithProductsByGroupIDMock.expectedInvocationsOrigin, afterGetUsersWithProductsByGroupIDCounter)
	}
}

type mUserDomainServiceMockGetUsersWithProductsByRoleID struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation
	expectations       []*UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation

	callArgs []*UserDomainServiceMockGetUsersWithProductsByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation specifies expectation struct of the UserDomainService.GetUsersWithProductsByRoleID
type UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockGetUsersWithProductsByRoleIDParams
	paramPtrs          *UserDomainServiceMockGetUsersWithProductsByRoleIDParamPtrs
	expectationOrigins UserDomainServiceMockGetUsersWithProductsByRoleIDExpectationOrigins
	results            *UserDomainServiceMockGetUsersWithProductsByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockGetUsersWithProductsByRoleIDParams contains parameters of the UserDomainService.GetUsersWithProductsByRoleID
type UserDomainServiceMockGetUsersWithProductsByRoleIDParams struct {
	roleID int64
}

// UserDomainServiceMockGetUsersWithProductsByRoleIDParamPtrs contains pointers to parameters of the UserDomainService.GetUsersWithProductsByRoleID
type UserDomainServiceMockGetUsersWithProductsByRoleIDParamPtrs struct {
	roleID *int64
}

// UserDomainServiceMockGetUsersWithProductsByRoleIDResults contains results of the UserDomainService.GetUsersWithProductsByRoleID
type UserDomainServiceMockGetUsersWithProductsByRoleIDResults struct {
	ua1 []userentity.UserWithProduct
	err error
}

// UserDomainServiceMockGetUsersWithProductsByRoleIDOrigins contains origins of expectations of the UserDomainService.GetUsersWithProductsByRoleID
type UserDomainServiceMockGetUsersWithProductsByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Optional() *mUserDomainServiceMockGetUsersWithProductsByRoleID {
	mmGetUsersWithProductsByRoleID.optional = true
	return mmGetUsersWithProductsByRoleID
}

// Expect sets up expected params for UserDomainService.GetUsersWithProductsByRoleID
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Expect(roleID int64) *mUserDomainServiceMockGetUsersWithProductsByRoleID {
	if mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleID != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByRoleID mock is already set by Set")
	}

	if mmGetUsersWithProductsByRoleID.defaultExpectation == nil {
		mmGetUsersWithProductsByRoleID.defaultExpectation = &UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation{}
	}

	if mmGetUsersWithProductsByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByRoleID mock is already set by ExpectParams functions")
	}

	mmGetUsersWithProductsByRoleID.defaultExpectation.params = &UserDomainServiceMockGetUsersWithProductsByRoleIDParams{roleID}
	mmGetUsersWithProductsByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersWithProductsByRoleID.expectations {
		if minimock.Equal(e.params, mmGetUsersWithProductsByRoleID.defaultExpectation.params) {
			mmGetUsersWithProductsByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersWithProductsByRoleID.defaultExpectation.params)
		}
	}

	return mmGetUsersWithProductsByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for UserDomainService.GetUsersWithProductsByRoleID
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) ExpectRoleIDParam1(roleID int64) *mUserDomainServiceMockGetUsersWithProductsByRoleID {
	if mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleID != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByRoleID mock is already set by Set")
	}

	if mmGetUsersWithProductsByRoleID.defaultExpectation == nil {
		mmGetUsersWithProductsByRoleID.defaultExpectation = &UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation{}
	}

	if mmGetUsersWithProductsByRoleID.defaultExpectation.params != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByRoleID mock is already set by Expect")
	}

	if mmGetUsersWithProductsByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetUsersWithProductsByRoleID.defaultExpectation.paramPtrs = &UserDomainServiceMockGetUsersWithProductsByRoleIDParamPtrs{}
	}
	mmGetUsersWithProductsByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetUsersWithProductsByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetUsersWithProductsByRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.GetUsersWithProductsByRoleID
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Inspect(f func(roleID int64)) *mUserDomainServiceMockGetUsersWithProductsByRoleID {
	if mmGetUsersWithProductsByRoleID.mock.inspectFuncGetUsersWithProductsByRoleID != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.GetUsersWithProductsByRoleID")
	}

	mmGetUsersWithProductsByRoleID.mock.inspectFuncGetUsersWithProductsByRoleID = f

	return mmGetUsersWithProductsByRoleID
}

// Return sets up results that will be returned by UserDomainService.GetUsersWithProductsByRoleID
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Return(ua1 []userentity.UserWithProduct, err error) *UserDomainServiceMock {
	if mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleID != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByRoleID mock is already set by Set")
	}

	if mmGetUsersWithProductsByRoleID.defaultExpectation == nil {
		mmGetUsersWithProductsByRoleID.defaultExpectation = &UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation{mock: mmGetUsersWithProductsByRoleID.mock}
	}
	mmGetUsersWithProductsByRoleID.defaultExpectation.results = &UserDomainServiceMockGetUsersWithProductsByRoleIDResults{ua1, err}
	mmGetUsersWithProductsByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersWithProductsByRoleID.mock
}

// Set uses given function f to mock the UserDomainService.GetUsersWithProductsByRoleID method
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Set(f func(roleID int64) (ua1 []userentity.UserWithProduct, err error)) *UserDomainServiceMock {
	if mmGetUsersWithProductsByRoleID.defaultExpectation != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("Default expectation is already set for the UserDomainService.GetUsersWithProductsByRoleID method")
	}

	if len(mmGetUsersWithProductsByRoleID.expectations) > 0 {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("Some expectations are already set for the UserDomainService.GetUsersWithProductsByRoleID method")
	}

	mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleID = f
	mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetUsersWithProductsByRoleID.mock
}

// When sets expectation for the UserDomainService.GetUsersWithProductsByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) When(roleID int64) *UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation {
	if mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleID != nil {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("UserDomainServiceMock.GetUsersWithProductsByRoleID mock is already set by Set")
	}

	expectation := &UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation{
		mock:               mmGetUsersWithProductsByRoleID.mock,
		params:             &UserDomainServiceMockGetUsersWithProductsByRoleIDParams{roleID},
		expectationOrigins: UserDomainServiceMockGetUsersWithProductsByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersWithProductsByRoleID.expectations = append(mmGetUsersWithProductsByRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.GetUsersWithProductsByRoleID return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockGetUsersWithProductsByRoleIDExpectation) Then(ua1 []userentity.UserWithProduct, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockGetUsersWithProductsByRoleIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserDomainService.GetUsersWithProductsByRoleID should be invoked
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Times(n uint64) *mUserDomainServiceMockGetUsersWithProductsByRoleID {
	if n == 0 {
		mmGetUsersWithProductsByRoleID.mock.t.Fatalf("Times of UserDomainServiceMock.GetUsersWithProductsByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersWithProductsByRoleID.expectedInvocations, n)
	mmGetUsersWithProductsByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersWithProductsByRoleID
}

func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) invocationsDone() bool {
	if len(mmGetUsersWithProductsByRoleID.expectations) == 0 && mmGetUsersWithProductsByRoleID.defaultExpectation == nil && mmGetUsersWithProductsByRoleID.mock.funcGetUsersWithProductsByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersWithProductsByRoleID.mock.afterGetUsersWithProductsByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersWithProductsByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersWithProductsByRoleID implements mm_service.UserDomainService
func (mmGetUsersWithProductsByRoleID *UserDomainServiceMock) GetUsersWithProductsByRoleID(roleID int64) (ua1 []userentity.UserWithProduct, err error) {
	mm_atomic.AddUint64(&mmGetUsersWithProductsByRoleID.beforeGetUsersWithProductsByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersWithProductsByRoleID.afterGetUsersWithProductsByRoleIDCounter, 1)

	mmGetUsersWithProductsByRoleID.t.Helper()

	if mmGetUsersWithProductsByRoleID.inspectFuncGetUsersWithProductsByRoleID != nil {
		mmGetUsersWithProductsByRoleID.inspectFuncGetUsersWithProductsByRoleID(roleID)
	}

	mm_params := UserDomainServiceMockGetUsersWithProductsByRoleIDParams{roleID}

	// Record call args
	mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.mutex.Lock()
	mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.callArgs = append(mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.callArgs, &mm_params)
	mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockGetUsersWithProductsByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetUsersWithProductsByRoleID.t.Errorf("UserDomainServiceMock.GetUsersWithProductsByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersWithProductsByRoleID.t.Errorf("UserDomainServiceMock.GetUsersWithProductsByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersWithProductsByRoleID.GetUsersWithProductsByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersWithProductsByRoleID.t.Fatal("No results are set for the UserDomainServiceMock.GetUsersWithProductsByRoleID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUsersWithProductsByRoleID.funcGetUsersWithProductsByRoleID != nil {
		return mmGetUsersWithProductsByRoleID.funcGetUsersWithProductsByRoleID(roleID)
	}
	mmGetUsersWithProductsByRoleID.t.Fatalf("Unexpected call to UserDomainServiceMock.GetUsersWithProductsByRoleID. %v", roleID)
	return
}

// GetUsersWithProductsByRoleIDAfterCounter returns a count of finished UserDomainServiceMock.GetUsersWithProductsByRoleID invocations
func (mmGetUsersWithProductsByRoleID *UserDomainServiceMock) GetUsersWithProductsByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersWithProductsByRoleID.afterGetUsersWithProductsByRoleIDCounter)
}

// GetUsersWithProductsByRoleIDBeforeCounter returns a count of UserDomainServiceMock.GetUsersWithProductsByRoleID invocations
func (mmGetUsersWithProductsByRoleID *UserDomainServiceMock) GetUsersWithProductsByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersWithProductsByRoleID.beforeGetUsersWithProductsByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.GetUsersWithProductsByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersWithProductsByRoleID *mUserDomainServiceMockGetUsersWithProductsByRoleID) Calls() []*UserDomainServiceMockGetUsersWithProductsByRoleIDParams {
	mmGetUsersWithProductsByRoleID.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockGetUsersWithProductsByRoleIDParams, len(mmGetUsersWithProductsByRoleID.callArgs))
	copy(argCopy, mmGetUsersWithProductsByRoleID.callArgs)

	mmGetUsersWithProductsByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersWithProductsByRoleIDDone returns true if the count of the GetUsersWithProductsByRoleID invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockGetUsersWithProductsByRoleIDDone() bool {
	if m.GetUsersWithProductsByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersWithProductsByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersWithProductsByRoleIDMock.invocationsDone()
}

// MinimockGetUsersWithProductsByRoleIDInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockGetUsersWithProductsByRoleIDInspect() {
	for _, e := range m.GetUsersWithProductsByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersWithProductsByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetUsersWithProductsByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersWithProductsByRoleIDMock.defaultExpectation != nil && afterGetUsersWithProductsByRoleIDCounter < 1 {
		if m.GetUsersWithProductsByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByRoleID at\n%s", m.GetUsersWithProductsByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByRoleID at\n%s with params: %#v", m.GetUsersWithProductsByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersWithProductsByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersWithProductsByRoleID != nil && afterGetUsersWithProductsByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.GetUsersWithProductsByRoleID at\n%s", m.funcGetUsersWithProductsByRoleIDOrigin)
	}

	if !m.GetUsersWithProductsByRoleIDMock.invocationsDone() && afterGetUsersWithProductsByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.GetUsersWithProductsByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersWithProductsByRoleIDMock.expectedInvocations), m.GetUsersWithProductsByRoleIDMock.expectedInvocationsOrigin, afterGetUsersWithProductsByRoleIDCounter)
	}
}

type mUserDomainServiceMockUpdate struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockUpdateExpectation
	expectations       []*UserDomainServiceMockUpdateExpectation

	callArgs []*UserDomainServiceMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockUpdateExpectation specifies expectation struct of the UserDomainService.Update
type UserDomainServiceMockUpdateExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockUpdateParams
	paramPtrs          *UserDomainServiceMockUpdateParamPtrs
	expectationOrigins UserDomainServiceMockUpdateExpectationOrigins
	results            *UserDomainServiceMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockUpdateParams contains parameters of the UserDomainService.Update
type UserDomainServiceMockUpdateParams struct {
	data userentity.UserUpdateData
}

// UserDomainServiceMockUpdateParamPtrs contains pointers to parameters of the UserDomainService.Update
type UserDomainServiceMockUpdateParamPtrs struct {
	data *userentity.UserUpdateData
}

// UserDomainServiceMockUpdateResults contains results of the UserDomainService.Update
type UserDomainServiceMockUpdateResults struct {
	u1  userentity.User
	err error
}

// UserDomainServiceMockUpdateOrigins contains origins of expectations of the UserDomainService.Update
type UserDomainServiceMockUpdateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mUserDomainServiceMockUpdate) Optional() *mUserDomainServiceMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for UserDomainService.Update
func (mmUpdate *mUserDomainServiceMockUpdate) Expect(data userentity.UserUpdateData) *mUserDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("UserDomainServiceMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &UserDomainServiceMockUpdateParams{data}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectDataParam1 sets up expected param data for UserDomainService.Update
func (mmUpdate *mUserDomainServiceMockUpdate) ExpectDataParam1(data userentity.UserUpdateData) *mUserDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("UserDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.data = &data
	mmUpdate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.Update
func (mmUpdate *mUserDomainServiceMockUpdate) Inspect(f func(data userentity.UserUpdateData)) *mUserDomainServiceMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by UserDomainService.Update
func (mmUpdate *mUserDomainServiceMockUpdate) Return(u1 userentity.User, err error) *UserDomainServiceMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserDomainServiceMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &UserDomainServiceMockUpdateResults{u1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the UserDomainService.Update method
func (mmUpdate *mUserDomainServiceMockUpdate) Set(f func(data userentity.UserUpdateData) (u1 userentity.User, err error)) *UserDomainServiceMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the UserDomainService.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the UserDomainService.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the UserDomainService.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mUserDomainServiceMockUpdate) When(data userentity.UserUpdateData) *UserDomainServiceMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserDomainServiceMock.Update mock is already set by Set")
	}

	expectation := &UserDomainServiceMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &UserDomainServiceMockUpdateParams{data},
		expectationOrigins: UserDomainServiceMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.Update return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockUpdateExpectation) Then(u1 userentity.User, err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockUpdateResults{u1, err}
	return e.mock
}

// Times sets number of times UserDomainService.Update should be invoked
func (mmUpdate *mUserDomainServiceMockUpdate) Times(n uint64) *mUserDomainServiceMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of UserDomainServiceMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mUserDomainServiceMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_service.UserDomainService
func (mmUpdate *UserDomainServiceMock) Update(data userentity.UserUpdateData) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(data)
	}

	mm_params := UserDomainServiceMockUpdateParams{data}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockUpdateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdate.t.Errorf("UserDomainServiceMock.Update got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("UserDomainServiceMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the UserDomainServiceMock.Update")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(data)
	}
	mmUpdate.t.Fatalf("Unexpected call to UserDomainServiceMock.Update. %v", data)
	return
}

// UpdateAfterCounter returns a count of finished UserDomainServiceMock.Update invocations
func (mmUpdate *UserDomainServiceMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of UserDomainServiceMock.Update invocations
func (mmUpdate *UserDomainServiceMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mUserDomainServiceMockUpdate) Calls() []*UserDomainServiceMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

type mUserDomainServiceMockUpdateLinksWithGroups struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockUpdateLinksWithGroupsExpectation
	expectations       []*UserDomainServiceMockUpdateLinksWithGroupsExpectation

	callArgs []*UserDomainServiceMockUpdateLinksWithGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockUpdateLinksWithGroupsExpectation specifies expectation struct of the UserDomainService.UpdateLinksWithGroups
type UserDomainServiceMockUpdateLinksWithGroupsExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockUpdateLinksWithGroupsParams
	paramPtrs          *UserDomainServiceMockUpdateLinksWithGroupsParamPtrs
	expectationOrigins UserDomainServiceMockUpdateLinksWithGroupsExpectationOrigins
	results            *UserDomainServiceMockUpdateLinksWithGroupsResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockUpdateLinksWithGroupsParams contains parameters of the UserDomainService.UpdateLinksWithGroups
type UserDomainServiceMockUpdateLinksWithGroupsParams struct {
	ctx    context.Context
	userID int64
	groups []groupentity.GroupProductLink
}

// UserDomainServiceMockUpdateLinksWithGroupsParamPtrs contains pointers to parameters of the UserDomainService.UpdateLinksWithGroups
type UserDomainServiceMockUpdateLinksWithGroupsParamPtrs struct {
	ctx    *context.Context
	userID *int64
	groups *[]groupentity.GroupProductLink
}

// UserDomainServiceMockUpdateLinksWithGroupsResults contains results of the UserDomainService.UpdateLinksWithGroups
type UserDomainServiceMockUpdateLinksWithGroupsResults struct {
	err error
}

// UserDomainServiceMockUpdateLinksWithGroupsOrigins contains origins of expectations of the UserDomainService.UpdateLinksWithGroups
type UserDomainServiceMockUpdateLinksWithGroupsExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
	originGroups string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Optional() *mUserDomainServiceMockUpdateLinksWithGroups {
	mmUpdateLinksWithGroups.optional = true
	return mmUpdateLinksWithGroups
}

// Expect sets up expected params for UserDomainService.UpdateLinksWithGroups
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Expect(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) *mUserDomainServiceMockUpdateLinksWithGroups {
	if mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Set")
	}

	if mmUpdateLinksWithGroups.defaultExpectation == nil {
		mmUpdateLinksWithGroups.defaultExpectation = &UserDomainServiceMockUpdateLinksWithGroupsExpectation{}
	}

	if mmUpdateLinksWithGroups.defaultExpectation.paramPtrs != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by ExpectParams functions")
	}

	mmUpdateLinksWithGroups.defaultExpectation.params = &UserDomainServiceMockUpdateLinksWithGroupsParams{ctx, userID, groups}
	mmUpdateLinksWithGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLinksWithGroups.expectations {
		if minimock.Equal(e.params, mmUpdateLinksWithGroups.defaultExpectation.params) {
			mmUpdateLinksWithGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLinksWithGroups.defaultExpectation.params)
		}
	}

	return mmUpdateLinksWithGroups
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.UpdateLinksWithGroups
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockUpdateLinksWithGroups {
	if mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Set")
	}

	if mmUpdateLinksWithGroups.defaultExpectation == nil {
		mmUpdateLinksWithGroups.defaultExpectation = &UserDomainServiceMockUpdateLinksWithGroupsExpectation{}
	}

	if mmUpdateLinksWithGroups.defaultExpectation.params != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Expect")
	}

	if mmUpdateLinksWithGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithGroups.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithGroupsParamPtrs{}
	}
	mmUpdateLinksWithGroups.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLinksWithGroups.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLinksWithGroups
}

// ExpectUserIDParam2 sets up expected param userID for UserDomainService.UpdateLinksWithGroups
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) ExpectUserIDParam2(userID int64) *mUserDomainServiceMockUpdateLinksWithGroups {
	if mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Set")
	}

	if mmUpdateLinksWithGroups.defaultExpectation == nil {
		mmUpdateLinksWithGroups.defaultExpectation = &UserDomainServiceMockUpdateLinksWithGroupsExpectation{}
	}

	if mmUpdateLinksWithGroups.defaultExpectation.params != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Expect")
	}

	if mmUpdateLinksWithGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithGroups.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithGroupsParamPtrs{}
	}
	mmUpdateLinksWithGroups.defaultExpectation.paramPtrs.userID = &userID
	mmUpdateLinksWithGroups.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmUpdateLinksWithGroups
}

// ExpectGroupsParam3 sets up expected param groups for UserDomainService.UpdateLinksWithGroups
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) ExpectGroupsParam3(groups []groupentity.GroupProductLink) *mUserDomainServiceMockUpdateLinksWithGroups {
	if mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Set")
	}

	if mmUpdateLinksWithGroups.defaultExpectation == nil {
		mmUpdateLinksWithGroups.defaultExpectation = &UserDomainServiceMockUpdateLinksWithGroupsExpectation{}
	}

	if mmUpdateLinksWithGroups.defaultExpectation.params != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Expect")
	}

	if mmUpdateLinksWithGroups.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithGroups.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithGroupsParamPtrs{}
	}
	mmUpdateLinksWithGroups.defaultExpectation.paramPtrs.groups = &groups
	mmUpdateLinksWithGroups.defaultExpectation.expectationOrigins.originGroups = minimock.CallerInfo(1)

	return mmUpdateLinksWithGroups
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.UpdateLinksWithGroups
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Inspect(f func(ctx context.Context, userID int64, groups []groupentity.GroupProductLink)) *mUserDomainServiceMockUpdateLinksWithGroups {
	if mmUpdateLinksWithGroups.mock.inspectFuncUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.UpdateLinksWithGroups")
	}

	mmUpdateLinksWithGroups.mock.inspectFuncUpdateLinksWithGroups = f

	return mmUpdateLinksWithGroups
}

// Return sets up results that will be returned by UserDomainService.UpdateLinksWithGroups
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Return(err error) *UserDomainServiceMock {
	if mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Set")
	}

	if mmUpdateLinksWithGroups.defaultExpectation == nil {
		mmUpdateLinksWithGroups.defaultExpectation = &UserDomainServiceMockUpdateLinksWithGroupsExpectation{mock: mmUpdateLinksWithGroups.mock}
	}
	mmUpdateLinksWithGroups.defaultExpectation.results = &UserDomainServiceMockUpdateLinksWithGroupsResults{err}
	mmUpdateLinksWithGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithGroups.mock
}

// Set uses given function f to mock the UserDomainService.UpdateLinksWithGroups method
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Set(f func(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) (err error)) *UserDomainServiceMock {
	if mmUpdateLinksWithGroups.defaultExpectation != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("Default expectation is already set for the UserDomainService.UpdateLinksWithGroups method")
	}

	if len(mmUpdateLinksWithGroups.expectations) > 0 {
		mmUpdateLinksWithGroups.mock.t.Fatalf("Some expectations are already set for the UserDomainService.UpdateLinksWithGroups method")
	}

	mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups = f
	mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroupsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithGroups.mock
}

// When sets expectation for the UserDomainService.UpdateLinksWithGroups which will trigger the result defined by the following
// Then helper
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) When(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) *UserDomainServiceMockUpdateLinksWithGroupsExpectation {
	if mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithGroups mock is already set by Set")
	}

	expectation := &UserDomainServiceMockUpdateLinksWithGroupsExpectation{
		mock:               mmUpdateLinksWithGroups.mock,
		params:             &UserDomainServiceMockUpdateLinksWithGroupsParams{ctx, userID, groups},
		expectationOrigins: UserDomainServiceMockUpdateLinksWithGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLinksWithGroups.expectations = append(mmUpdateLinksWithGroups.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.UpdateLinksWithGroups return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockUpdateLinksWithGroupsExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockUpdateLinksWithGroupsResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.UpdateLinksWithGroups should be invoked
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Times(n uint64) *mUserDomainServiceMockUpdateLinksWithGroups {
	if n == 0 {
		mmUpdateLinksWithGroups.mock.t.Fatalf("Times of UserDomainServiceMock.UpdateLinksWithGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLinksWithGroups.expectedInvocations, n)
	mmUpdateLinksWithGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithGroups
}

func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) invocationsDone() bool {
	if len(mmUpdateLinksWithGroups.expectations) == 0 && mmUpdateLinksWithGroups.defaultExpectation == nil && mmUpdateLinksWithGroups.mock.funcUpdateLinksWithGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithGroups.mock.afterUpdateLinksWithGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLinksWithGroups implements mm_service.UserDomainService
func (mmUpdateLinksWithGroups *UserDomainServiceMock) UpdateLinksWithGroups(ctx context.Context, userID int64, groups []groupentity.GroupProductLink) (err error) {
	mm_atomic.AddUint64(&mmUpdateLinksWithGroups.beforeUpdateLinksWithGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLinksWithGroups.afterUpdateLinksWithGroupsCounter, 1)

	mmUpdateLinksWithGroups.t.Helper()

	if mmUpdateLinksWithGroups.inspectFuncUpdateLinksWithGroups != nil {
		mmUpdateLinksWithGroups.inspectFuncUpdateLinksWithGroups(ctx, userID, groups)
	}

	mm_params := UserDomainServiceMockUpdateLinksWithGroupsParams{ctx, userID, groups}

	// Record call args
	mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.mutex.Lock()
	mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.callArgs = append(mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.callArgs, &mm_params)
	mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.mutex.Unlock()

	for _, e := range mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockUpdateLinksWithGroupsParams{ctx, userID, groups}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLinksWithGroups.t.Errorf("UserDomainServiceMock.UpdateLinksWithGroups got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmUpdateLinksWithGroups.t.Errorf("UserDomainServiceMock.UpdateLinksWithGroups got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.groups != nil && !minimock.Equal(*mm_want_ptrs.groups, mm_got.groups) {
				mmUpdateLinksWithGroups.t.Errorf("UserDomainServiceMock.UpdateLinksWithGroups got unexpected parameter groups, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.expectationOrigins.originGroups, *mm_want_ptrs.groups, mm_got.groups, minimock.Diff(*mm_want_ptrs.groups, mm_got.groups))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLinksWithGroups.t.Errorf("UserDomainServiceMock.UpdateLinksWithGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLinksWithGroups.UpdateLinksWithGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLinksWithGroups.t.Fatal("No results are set for the UserDomainServiceMock.UpdateLinksWithGroups")
		}
		return (*mm_results).err
	}
	if mmUpdateLinksWithGroups.funcUpdateLinksWithGroups != nil {
		return mmUpdateLinksWithGroups.funcUpdateLinksWithGroups(ctx, userID, groups)
	}
	mmUpdateLinksWithGroups.t.Fatalf("Unexpected call to UserDomainServiceMock.UpdateLinksWithGroups. %v %v %v", ctx, userID, groups)
	return
}

// UpdateLinksWithGroupsAfterCounter returns a count of finished UserDomainServiceMock.UpdateLinksWithGroups invocations
func (mmUpdateLinksWithGroups *UserDomainServiceMock) UpdateLinksWithGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithGroups.afterUpdateLinksWithGroupsCounter)
}

// UpdateLinksWithGroupsBeforeCounter returns a count of UserDomainServiceMock.UpdateLinksWithGroups invocations
func (mmUpdateLinksWithGroups *UserDomainServiceMock) UpdateLinksWithGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithGroups.beforeUpdateLinksWithGroupsCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.UpdateLinksWithGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLinksWithGroups *mUserDomainServiceMockUpdateLinksWithGroups) Calls() []*UserDomainServiceMockUpdateLinksWithGroupsParams {
	mmUpdateLinksWithGroups.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockUpdateLinksWithGroupsParams, len(mmUpdateLinksWithGroups.callArgs))
	copy(argCopy, mmUpdateLinksWithGroups.callArgs)

	mmUpdateLinksWithGroups.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLinksWithGroupsDone returns true if the count of the UpdateLinksWithGroups invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockUpdateLinksWithGroupsDone() bool {
	if m.UpdateLinksWithGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLinksWithGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLinksWithGroupsMock.invocationsDone()
}

// MinimockUpdateLinksWithGroupsInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockUpdateLinksWithGroupsInspect() {
	for _, e := range m.UpdateLinksWithGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLinksWithGroupsCounter := mm_atomic.LoadUint64(&m.afterUpdateLinksWithGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLinksWithGroupsMock.defaultExpectation != nil && afterUpdateLinksWithGroupsCounter < 1 {
		if m.UpdateLinksWithGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithGroups at\n%s", m.UpdateLinksWithGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithGroups at\n%s with params: %#v", m.UpdateLinksWithGroupsMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLinksWithGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLinksWithGroups != nil && afterUpdateLinksWithGroupsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithGroups at\n%s", m.funcUpdateLinksWithGroupsOrigin)
	}

	if !m.UpdateLinksWithGroupsMock.invocationsDone() && afterUpdateLinksWithGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.UpdateLinksWithGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLinksWithGroupsMock.expectedInvocations), m.UpdateLinksWithGroupsMock.expectedInvocationsOrigin, afterUpdateLinksWithGroupsCounter)
	}
}

type mUserDomainServiceMockUpdateLinksWithProducts struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockUpdateLinksWithProductsExpectation
	expectations       []*UserDomainServiceMockUpdateLinksWithProductsExpectation

	callArgs []*UserDomainServiceMockUpdateLinksWithProductsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockUpdateLinksWithProductsExpectation specifies expectation struct of the UserDomainService.UpdateLinksWithProducts
type UserDomainServiceMockUpdateLinksWithProductsExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockUpdateLinksWithProductsParams
	paramPtrs          *UserDomainServiceMockUpdateLinksWithProductsParamPtrs
	expectationOrigins UserDomainServiceMockUpdateLinksWithProductsExpectationOrigins
	results            *UserDomainServiceMockUpdateLinksWithProductsResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockUpdateLinksWithProductsParams contains parameters of the UserDomainService.UpdateLinksWithProducts
type UserDomainServiceMockUpdateLinksWithProductsParams struct {
	ctx        context.Context
	userID     int64
	productIDs []int64
}

// UserDomainServiceMockUpdateLinksWithProductsParamPtrs contains pointers to parameters of the UserDomainService.UpdateLinksWithProducts
type UserDomainServiceMockUpdateLinksWithProductsParamPtrs struct {
	ctx        *context.Context
	userID     *int64
	productIDs *[]int64
}

// UserDomainServiceMockUpdateLinksWithProductsResults contains results of the UserDomainService.UpdateLinksWithProducts
type UserDomainServiceMockUpdateLinksWithProductsResults struct {
	err error
}

// UserDomainServiceMockUpdateLinksWithProductsOrigins contains origins of expectations of the UserDomainService.UpdateLinksWithProducts
type UserDomainServiceMockUpdateLinksWithProductsExpectationOrigins struct {
	origin           string
	originCtx        string
	originUserID     string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Optional() *mUserDomainServiceMockUpdateLinksWithProducts {
	mmUpdateLinksWithProducts.optional = true
	return mmUpdateLinksWithProducts
}

// Expect sets up expected params for UserDomainService.UpdateLinksWithProducts
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Expect(ctx context.Context, userID int64, productIDs []int64) *mUserDomainServiceMockUpdateLinksWithProducts {
	if mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Set")
	}

	if mmUpdateLinksWithProducts.defaultExpectation == nil {
		mmUpdateLinksWithProducts.defaultExpectation = &UserDomainServiceMockUpdateLinksWithProductsExpectation{}
	}

	if mmUpdateLinksWithProducts.defaultExpectation.paramPtrs != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by ExpectParams functions")
	}

	mmUpdateLinksWithProducts.defaultExpectation.params = &UserDomainServiceMockUpdateLinksWithProductsParams{ctx, userID, productIDs}
	mmUpdateLinksWithProducts.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLinksWithProducts.expectations {
		if minimock.Equal(e.params, mmUpdateLinksWithProducts.defaultExpectation.params) {
			mmUpdateLinksWithProducts.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLinksWithProducts.defaultExpectation.params)
		}
	}

	return mmUpdateLinksWithProducts
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.UpdateLinksWithProducts
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockUpdateLinksWithProducts {
	if mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Set")
	}

	if mmUpdateLinksWithProducts.defaultExpectation == nil {
		mmUpdateLinksWithProducts.defaultExpectation = &UserDomainServiceMockUpdateLinksWithProductsExpectation{}
	}

	if mmUpdateLinksWithProducts.defaultExpectation.params != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Expect")
	}

	if mmUpdateLinksWithProducts.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithProducts.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithProductsParamPtrs{}
	}
	mmUpdateLinksWithProducts.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLinksWithProducts.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLinksWithProducts
}

// ExpectUserIDParam2 sets up expected param userID for UserDomainService.UpdateLinksWithProducts
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) ExpectUserIDParam2(userID int64) *mUserDomainServiceMockUpdateLinksWithProducts {
	if mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Set")
	}

	if mmUpdateLinksWithProducts.defaultExpectation == nil {
		mmUpdateLinksWithProducts.defaultExpectation = &UserDomainServiceMockUpdateLinksWithProductsExpectation{}
	}

	if mmUpdateLinksWithProducts.defaultExpectation.params != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Expect")
	}

	if mmUpdateLinksWithProducts.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithProducts.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithProductsParamPtrs{}
	}
	mmUpdateLinksWithProducts.defaultExpectation.paramPtrs.userID = &userID
	mmUpdateLinksWithProducts.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmUpdateLinksWithProducts
}

// ExpectProductIDsParam3 sets up expected param productIDs for UserDomainService.UpdateLinksWithProducts
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) ExpectProductIDsParam3(productIDs []int64) *mUserDomainServiceMockUpdateLinksWithProducts {
	if mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Set")
	}

	if mmUpdateLinksWithProducts.defaultExpectation == nil {
		mmUpdateLinksWithProducts.defaultExpectation = &UserDomainServiceMockUpdateLinksWithProductsExpectation{}
	}

	if mmUpdateLinksWithProducts.defaultExpectation.params != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Expect")
	}

	if mmUpdateLinksWithProducts.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithProducts.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithProductsParamPtrs{}
	}
	mmUpdateLinksWithProducts.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmUpdateLinksWithProducts.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmUpdateLinksWithProducts
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.UpdateLinksWithProducts
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Inspect(f func(ctx context.Context, userID int64, productIDs []int64)) *mUserDomainServiceMockUpdateLinksWithProducts {
	if mmUpdateLinksWithProducts.mock.inspectFuncUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.UpdateLinksWithProducts")
	}

	mmUpdateLinksWithProducts.mock.inspectFuncUpdateLinksWithProducts = f

	return mmUpdateLinksWithProducts
}

// Return sets up results that will be returned by UserDomainService.UpdateLinksWithProducts
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Return(err error) *UserDomainServiceMock {
	if mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Set")
	}

	if mmUpdateLinksWithProducts.defaultExpectation == nil {
		mmUpdateLinksWithProducts.defaultExpectation = &UserDomainServiceMockUpdateLinksWithProductsExpectation{mock: mmUpdateLinksWithProducts.mock}
	}
	mmUpdateLinksWithProducts.defaultExpectation.results = &UserDomainServiceMockUpdateLinksWithProductsResults{err}
	mmUpdateLinksWithProducts.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithProducts.mock
}

// Set uses given function f to mock the UserDomainService.UpdateLinksWithProducts method
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Set(f func(ctx context.Context, userID int64, productIDs []int64) (err error)) *UserDomainServiceMock {
	if mmUpdateLinksWithProducts.defaultExpectation != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("Default expectation is already set for the UserDomainService.UpdateLinksWithProducts method")
	}

	if len(mmUpdateLinksWithProducts.expectations) > 0 {
		mmUpdateLinksWithProducts.mock.t.Fatalf("Some expectations are already set for the UserDomainService.UpdateLinksWithProducts method")
	}

	mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts = f
	mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProductsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithProducts.mock
}

// When sets expectation for the UserDomainService.UpdateLinksWithProducts which will trigger the result defined by the following
// Then helper
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) When(ctx context.Context, userID int64, productIDs []int64) *UserDomainServiceMockUpdateLinksWithProductsExpectation {
	if mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithProducts mock is already set by Set")
	}

	expectation := &UserDomainServiceMockUpdateLinksWithProductsExpectation{
		mock:               mmUpdateLinksWithProducts.mock,
		params:             &UserDomainServiceMockUpdateLinksWithProductsParams{ctx, userID, productIDs},
		expectationOrigins: UserDomainServiceMockUpdateLinksWithProductsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLinksWithProducts.expectations = append(mmUpdateLinksWithProducts.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.UpdateLinksWithProducts return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockUpdateLinksWithProductsExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockUpdateLinksWithProductsResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.UpdateLinksWithProducts should be invoked
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Times(n uint64) *mUserDomainServiceMockUpdateLinksWithProducts {
	if n == 0 {
		mmUpdateLinksWithProducts.mock.t.Fatalf("Times of UserDomainServiceMock.UpdateLinksWithProducts mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLinksWithProducts.expectedInvocations, n)
	mmUpdateLinksWithProducts.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithProducts
}

func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) invocationsDone() bool {
	if len(mmUpdateLinksWithProducts.expectations) == 0 && mmUpdateLinksWithProducts.defaultExpectation == nil && mmUpdateLinksWithProducts.mock.funcUpdateLinksWithProducts == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithProducts.mock.afterUpdateLinksWithProductsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithProducts.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLinksWithProducts implements mm_service.UserDomainService
func (mmUpdateLinksWithProducts *UserDomainServiceMock) UpdateLinksWithProducts(ctx context.Context, userID int64, productIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmUpdateLinksWithProducts.beforeUpdateLinksWithProductsCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLinksWithProducts.afterUpdateLinksWithProductsCounter, 1)

	mmUpdateLinksWithProducts.t.Helper()

	if mmUpdateLinksWithProducts.inspectFuncUpdateLinksWithProducts != nil {
		mmUpdateLinksWithProducts.inspectFuncUpdateLinksWithProducts(ctx, userID, productIDs)
	}

	mm_params := UserDomainServiceMockUpdateLinksWithProductsParams{ctx, userID, productIDs}

	// Record call args
	mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.mutex.Lock()
	mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.callArgs = append(mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.callArgs, &mm_params)
	mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.mutex.Unlock()

	for _, e := range mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockUpdateLinksWithProductsParams{ctx, userID, productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLinksWithProducts.t.Errorf("UserDomainServiceMock.UpdateLinksWithProducts got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmUpdateLinksWithProducts.t.Errorf("UserDomainServiceMock.UpdateLinksWithProducts got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmUpdateLinksWithProducts.t.Errorf("UserDomainServiceMock.UpdateLinksWithProducts got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLinksWithProducts.t.Errorf("UserDomainServiceMock.UpdateLinksWithProducts got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLinksWithProducts.UpdateLinksWithProductsMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLinksWithProducts.t.Fatal("No results are set for the UserDomainServiceMock.UpdateLinksWithProducts")
		}
		return (*mm_results).err
	}
	if mmUpdateLinksWithProducts.funcUpdateLinksWithProducts != nil {
		return mmUpdateLinksWithProducts.funcUpdateLinksWithProducts(ctx, userID, productIDs)
	}
	mmUpdateLinksWithProducts.t.Fatalf("Unexpected call to UserDomainServiceMock.UpdateLinksWithProducts. %v %v %v", ctx, userID, productIDs)
	return
}

// UpdateLinksWithProductsAfterCounter returns a count of finished UserDomainServiceMock.UpdateLinksWithProducts invocations
func (mmUpdateLinksWithProducts *UserDomainServiceMock) UpdateLinksWithProductsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithProducts.afterUpdateLinksWithProductsCounter)
}

// UpdateLinksWithProductsBeforeCounter returns a count of UserDomainServiceMock.UpdateLinksWithProducts invocations
func (mmUpdateLinksWithProducts *UserDomainServiceMock) UpdateLinksWithProductsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithProducts.beforeUpdateLinksWithProductsCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.UpdateLinksWithProducts.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLinksWithProducts *mUserDomainServiceMockUpdateLinksWithProducts) Calls() []*UserDomainServiceMockUpdateLinksWithProductsParams {
	mmUpdateLinksWithProducts.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockUpdateLinksWithProductsParams, len(mmUpdateLinksWithProducts.callArgs))
	copy(argCopy, mmUpdateLinksWithProducts.callArgs)

	mmUpdateLinksWithProducts.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLinksWithProductsDone returns true if the count of the UpdateLinksWithProducts invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockUpdateLinksWithProductsDone() bool {
	if m.UpdateLinksWithProductsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLinksWithProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLinksWithProductsMock.invocationsDone()
}

// MinimockUpdateLinksWithProductsInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockUpdateLinksWithProductsInspect() {
	for _, e := range m.UpdateLinksWithProductsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithProducts at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLinksWithProductsCounter := mm_atomic.LoadUint64(&m.afterUpdateLinksWithProductsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLinksWithProductsMock.defaultExpectation != nil && afterUpdateLinksWithProductsCounter < 1 {
		if m.UpdateLinksWithProductsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithProducts at\n%s", m.UpdateLinksWithProductsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithProducts at\n%s with params: %#v", m.UpdateLinksWithProductsMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLinksWithProductsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLinksWithProducts != nil && afterUpdateLinksWithProductsCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithProducts at\n%s", m.funcUpdateLinksWithProductsOrigin)
	}

	if !m.UpdateLinksWithProductsMock.invocationsDone() && afterUpdateLinksWithProductsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.UpdateLinksWithProducts at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLinksWithProductsMock.expectedInvocations), m.UpdateLinksWithProductsMock.expectedInvocationsOrigin, afterUpdateLinksWithProductsCounter)
	}
}

type mUserDomainServiceMockUpdateLinksWithRoles struct {
	optional           bool
	mock               *UserDomainServiceMock
	defaultExpectation *UserDomainServiceMockUpdateLinksWithRolesExpectation
	expectations       []*UserDomainServiceMockUpdateLinksWithRolesExpectation

	callArgs []*UserDomainServiceMockUpdateLinksWithRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserDomainServiceMockUpdateLinksWithRolesExpectation specifies expectation struct of the UserDomainService.UpdateLinksWithRoles
type UserDomainServiceMockUpdateLinksWithRolesExpectation struct {
	mock               *UserDomainServiceMock
	params             *UserDomainServiceMockUpdateLinksWithRolesParams
	paramPtrs          *UserDomainServiceMockUpdateLinksWithRolesParamPtrs
	expectationOrigins UserDomainServiceMockUpdateLinksWithRolesExpectationOrigins
	results            *UserDomainServiceMockUpdateLinksWithRolesResults
	returnOrigin       string
	Counter            uint64
}

// UserDomainServiceMockUpdateLinksWithRolesParams contains parameters of the UserDomainService.UpdateLinksWithRoles
type UserDomainServiceMockUpdateLinksWithRolesParams struct {
	ctx    context.Context
	userID int64
	roles  []roleentity.RoleProductLink
}

// UserDomainServiceMockUpdateLinksWithRolesParamPtrs contains pointers to parameters of the UserDomainService.UpdateLinksWithRoles
type UserDomainServiceMockUpdateLinksWithRolesParamPtrs struct {
	ctx    *context.Context
	userID *int64
	roles  *[]roleentity.RoleProductLink
}

// UserDomainServiceMockUpdateLinksWithRolesResults contains results of the UserDomainService.UpdateLinksWithRoles
type UserDomainServiceMockUpdateLinksWithRolesResults struct {
	err error
}

// UserDomainServiceMockUpdateLinksWithRolesOrigins contains origins of expectations of the UserDomainService.UpdateLinksWithRoles
type UserDomainServiceMockUpdateLinksWithRolesExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
	originRoles  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Optional() *mUserDomainServiceMockUpdateLinksWithRoles {
	mmUpdateLinksWithRoles.optional = true
	return mmUpdateLinksWithRoles
}

// Expect sets up expected params for UserDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Expect(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) *mUserDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &UserDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by ExpectParams functions")
	}

	mmUpdateLinksWithRoles.defaultExpectation.params = &UserDomainServiceMockUpdateLinksWithRolesParams{ctx, userID, roles}
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLinksWithRoles.expectations {
		if minimock.Equal(e.params, mmUpdateLinksWithRoles.defaultExpectation.params) {
			mmUpdateLinksWithRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLinksWithRoles.defaultExpectation.params)
		}
	}

	return mmUpdateLinksWithRoles
}

// ExpectCtxParam1 sets up expected param ctx for UserDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) ExpectCtxParam1(ctx context.Context) *mUserDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &UserDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.params != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Expect")
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithRoles.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithRolesParamPtrs{}
	}
	mmUpdateLinksWithRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLinksWithRoles
}

// ExpectUserIDParam2 sets up expected param userID for UserDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) ExpectUserIDParam2(userID int64) *mUserDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &UserDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.params != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Expect")
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithRoles.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithRolesParamPtrs{}
	}
	mmUpdateLinksWithRoles.defaultExpectation.paramPtrs.userID = &userID
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmUpdateLinksWithRoles
}

// ExpectRolesParam3 sets up expected param roles for UserDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) ExpectRolesParam3(roles []roleentity.RoleProductLink) *mUserDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &UserDomainServiceMockUpdateLinksWithRolesExpectation{}
	}

	if mmUpdateLinksWithRoles.defaultExpectation.params != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Expect")
	}

	if mmUpdateLinksWithRoles.defaultExpectation.paramPtrs == nil {
		mmUpdateLinksWithRoles.defaultExpectation.paramPtrs = &UserDomainServiceMockUpdateLinksWithRolesParamPtrs{}
	}
	mmUpdateLinksWithRoles.defaultExpectation.paramPtrs.roles = &roles
	mmUpdateLinksWithRoles.defaultExpectation.expectationOrigins.originRoles = minimock.CallerInfo(1)

	return mmUpdateLinksWithRoles
}

// Inspect accepts an inspector function that has same arguments as the UserDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Inspect(f func(ctx context.Context, userID int64, roles []roleentity.RoleProductLink)) *mUserDomainServiceMockUpdateLinksWithRoles {
	if mmUpdateLinksWithRoles.mock.inspectFuncUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Inspect function is already set for UserDomainServiceMock.UpdateLinksWithRoles")
	}

	mmUpdateLinksWithRoles.mock.inspectFuncUpdateLinksWithRoles = f

	return mmUpdateLinksWithRoles
}

// Return sets up results that will be returned by UserDomainService.UpdateLinksWithRoles
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Return(err error) *UserDomainServiceMock {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	if mmUpdateLinksWithRoles.defaultExpectation == nil {
		mmUpdateLinksWithRoles.defaultExpectation = &UserDomainServiceMockUpdateLinksWithRolesExpectation{mock: mmUpdateLinksWithRoles.mock}
	}
	mmUpdateLinksWithRoles.defaultExpectation.results = &UserDomainServiceMockUpdateLinksWithRolesResults{err}
	mmUpdateLinksWithRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithRoles.mock
}

// Set uses given function f to mock the UserDomainService.UpdateLinksWithRoles method
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Set(f func(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) (err error)) *UserDomainServiceMock {
	if mmUpdateLinksWithRoles.defaultExpectation != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Default expectation is already set for the UserDomainService.UpdateLinksWithRoles method")
	}

	if len(mmUpdateLinksWithRoles.expectations) > 0 {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Some expectations are already set for the UserDomainService.UpdateLinksWithRoles method")
	}

	mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles = f
	mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRolesOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithRoles.mock
}

// When sets expectation for the UserDomainService.UpdateLinksWithRoles which will trigger the result defined by the following
// Then helper
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) When(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) *UserDomainServiceMockUpdateLinksWithRolesExpectation {
	if mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.mock.t.Fatalf("UserDomainServiceMock.UpdateLinksWithRoles mock is already set by Set")
	}

	expectation := &UserDomainServiceMockUpdateLinksWithRolesExpectation{
		mock:               mmUpdateLinksWithRoles.mock,
		params:             &UserDomainServiceMockUpdateLinksWithRolesParams{ctx, userID, roles},
		expectationOrigins: UserDomainServiceMockUpdateLinksWithRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLinksWithRoles.expectations = append(mmUpdateLinksWithRoles.expectations, expectation)
	return expectation
}

// Then sets up UserDomainService.UpdateLinksWithRoles return parameters for the expectation previously defined by the When method
func (e *UserDomainServiceMockUpdateLinksWithRolesExpectation) Then(err error) *UserDomainServiceMock {
	e.results = &UserDomainServiceMockUpdateLinksWithRolesResults{err}
	return e.mock
}

// Times sets number of times UserDomainService.UpdateLinksWithRoles should be invoked
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Times(n uint64) *mUserDomainServiceMockUpdateLinksWithRoles {
	if n == 0 {
		mmUpdateLinksWithRoles.mock.t.Fatalf("Times of UserDomainServiceMock.UpdateLinksWithRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLinksWithRoles.expectedInvocations, n)
	mmUpdateLinksWithRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLinksWithRoles
}

func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) invocationsDone() bool {
	if len(mmUpdateLinksWithRoles.expectations) == 0 && mmUpdateLinksWithRoles.defaultExpectation == nil && mmUpdateLinksWithRoles.mock.funcUpdateLinksWithRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.mock.afterUpdateLinksWithRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLinksWithRoles implements mm_service.UserDomainService
func (mmUpdateLinksWithRoles *UserDomainServiceMock) UpdateLinksWithRoles(ctx context.Context, userID int64, roles []roleentity.RoleProductLink) (err error) {
	mm_atomic.AddUint64(&mmUpdateLinksWithRoles.beforeUpdateLinksWithRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLinksWithRoles.afterUpdateLinksWithRolesCounter, 1)

	mmUpdateLinksWithRoles.t.Helper()

	if mmUpdateLinksWithRoles.inspectFuncUpdateLinksWithRoles != nil {
		mmUpdateLinksWithRoles.inspectFuncUpdateLinksWithRoles(ctx, userID, roles)
	}

	mm_params := UserDomainServiceMockUpdateLinksWithRolesParams{ctx, userID, roles}

	// Record call args
	mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.mutex.Lock()
	mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.callArgs = append(mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.callArgs, &mm_params)
	mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.mutex.Unlock()

	for _, e := range mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.paramPtrs

		mm_got := UserDomainServiceMockUpdateLinksWithRolesParams{ctx, userID, roles}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLinksWithRoles.t.Errorf("UserDomainServiceMock.UpdateLinksWithRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmUpdateLinksWithRoles.t.Errorf("UserDomainServiceMock.UpdateLinksWithRoles got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roles != nil && !minimock.Equal(*mm_want_ptrs.roles, mm_got.roles) {
				mmUpdateLinksWithRoles.t.Errorf("UserDomainServiceMock.UpdateLinksWithRoles got unexpected parameter roles, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.originRoles, *mm_want_ptrs.roles, mm_got.roles, minimock.Diff(*mm_want_ptrs.roles, mm_got.roles))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLinksWithRoles.t.Errorf("UserDomainServiceMock.UpdateLinksWithRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLinksWithRoles.UpdateLinksWithRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLinksWithRoles.t.Fatal("No results are set for the UserDomainServiceMock.UpdateLinksWithRoles")
		}
		return (*mm_results).err
	}
	if mmUpdateLinksWithRoles.funcUpdateLinksWithRoles != nil {
		return mmUpdateLinksWithRoles.funcUpdateLinksWithRoles(ctx, userID, roles)
	}
	mmUpdateLinksWithRoles.t.Fatalf("Unexpected call to UserDomainServiceMock.UpdateLinksWithRoles. %v %v %v", ctx, userID, roles)
	return
}

// UpdateLinksWithRolesAfterCounter returns a count of finished UserDomainServiceMock.UpdateLinksWithRoles invocations
func (mmUpdateLinksWithRoles *UserDomainServiceMock) UpdateLinksWithRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.afterUpdateLinksWithRolesCounter)
}

// UpdateLinksWithRolesBeforeCounter returns a count of UserDomainServiceMock.UpdateLinksWithRoles invocations
func (mmUpdateLinksWithRoles *UserDomainServiceMock) UpdateLinksWithRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLinksWithRoles.beforeUpdateLinksWithRolesCounter)
}

// Calls returns a list of arguments used in each call to UserDomainServiceMock.UpdateLinksWithRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLinksWithRoles *mUserDomainServiceMockUpdateLinksWithRoles) Calls() []*UserDomainServiceMockUpdateLinksWithRolesParams {
	mmUpdateLinksWithRoles.mutex.RLock()

	argCopy := make([]*UserDomainServiceMockUpdateLinksWithRolesParams, len(mmUpdateLinksWithRoles.callArgs))
	copy(argCopy, mmUpdateLinksWithRoles.callArgs)

	mmUpdateLinksWithRoles.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLinksWithRolesDone returns true if the count of the UpdateLinksWithRoles invocations corresponds
// the number of defined expectations
func (m *UserDomainServiceMock) MinimockUpdateLinksWithRolesDone() bool {
	if m.UpdateLinksWithRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLinksWithRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLinksWithRolesMock.invocationsDone()
}

// MinimockUpdateLinksWithRolesInspect logs each unmet expectation
func (m *UserDomainServiceMock) MinimockUpdateLinksWithRolesInspect() {
	for _, e := range m.UpdateLinksWithRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLinksWithRolesCounter := mm_atomic.LoadUint64(&m.afterUpdateLinksWithRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLinksWithRolesMock.defaultExpectation != nil && afterUpdateLinksWithRolesCounter < 1 {
		if m.UpdateLinksWithRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithRoles at\n%s", m.UpdateLinksWithRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithRoles at\n%s with params: %#v", m.UpdateLinksWithRolesMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLinksWithRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLinksWithRoles != nil && afterUpdateLinksWithRolesCounter < 1 {
		m.t.Errorf("Expected call to UserDomainServiceMock.UpdateLinksWithRoles at\n%s", m.funcUpdateLinksWithRolesOrigin)
	}

	if !m.UpdateLinksWithRolesMock.invocationsDone() && afterUpdateLinksWithRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to UserDomainServiceMock.UpdateLinksWithRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLinksWithRolesMock.expectedInvocations), m.UpdateLinksWithRolesMock.expectedInvocationsOrigin, afterUpdateLinksWithRolesCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *UserDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByEmailInspect()

			m.MinimockDeleteAsAdminInspect()

			m.MinimockDeleteByUserIDInspect()

			m.MinimockDeleteByUserIDAndGroupIDsInspect()

			m.MinimockDeleteByUserIDAndRoleIDsInspect()

			m.MinimockGetAdminsInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllAsAdminInspect()

			m.MinimockGetAllPaginatedInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByEmailInspect()

			m.MinimockGetByFiltersInspect()

			m.MinimockGetByFiltersPaginatedInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetUserGroupsInspect()

			m.MinimockGetUserGroupsByGroupIDInspect()

			m.MinimockGetUserGroupsByUserIDInspect()

			m.MinimockGetUserRolesByRoleIDInspect()

			m.MinimockGetUserRolesByUserIDInspect()

			m.MinimockGetUsersWithProductsByGroupIDInspect()

			m.MinimockGetUsersWithProductsByRoleIDInspect()

			m.MinimockUpdateInspect()

			m.MinimockUpdateLinksWithGroupsInspect()

			m.MinimockUpdateLinksWithProductsInspect()

			m.MinimockUpdateLinksWithRolesInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *UserDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *UserDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByEmailDone() &&
		m.MinimockDeleteAsAdminDone() &&
		m.MinimockDeleteByUserIDDone() &&
		m.MinimockDeleteByUserIDAndGroupIDsDone() &&
		m.MinimockDeleteByUserIDAndRoleIDsDone() &&
		m.MinimockGetAdminsDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllAsAdminDone() &&
		m.MinimockGetAllPaginatedDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByEmailDone() &&
		m.MinimockGetByFiltersDone() &&
		m.MinimockGetByFiltersPaginatedDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetUserGroupsDone() &&
		m.MinimockGetUserGroupsByGroupIDDone() &&
		m.MinimockGetUserGroupsByUserIDDone() &&
		m.MinimockGetUserRolesByRoleIDDone() &&
		m.MinimockGetUserRolesByUserIDDone() &&
		m.MinimockGetUsersWithProductsByGroupIDDone() &&
		m.MinimockGetUsersWithProductsByRoleIDDone() &&
		m.MinimockUpdateDone() &&
		m.MinimockUpdateLinksWithGroupsDone() &&
		m.MinimockUpdateLinksWithProductsDone() &&
		m.MinimockUpdateLinksWithRolesDone()
}
