// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository.UserCache -o user_cache_mock.go -n UserCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// UserCacheMock implements mm_repository.UserCache
type UserCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcDeleteUser          func(ctx context.Context, id int64) (err error)
	funcDeleteUserOrigin    string
	inspectFuncDeleteUser   func(ctx context.Context, id int64)
	afterDeleteUserCounter  uint64
	beforeDeleteUserCounter uint64
	DeleteUserMock          mUserCacheMockDeleteUser

	funcDeleteUsers          func(ctx context.Context, ids []int64) (err error)
	funcDeleteUsersOrigin    string
	inspectFuncDeleteUsers   func(ctx context.Context, ids []int64)
	afterDeleteUsersCounter  uint64
	beforeDeleteUsersCounter uint64
	DeleteUsersMock          mUserCacheMockDeleteUsers

	funcGetUser          func(ctx context.Context, id int64) (u1 userentity.User, err error)
	funcGetUserOrigin    string
	inspectFuncGetUser   func(ctx context.Context, id int64)
	afterGetUserCounter  uint64
	beforeGetUserCounter uint64
	GetUserMock          mUserCacheMockGetUser

	funcGetUsers          func(ctx context.Context) (ua1 []userentity.User, err error)
	funcGetUsersOrigin    string
	inspectFuncGetUsers   func(ctx context.Context)
	afterGetUsersCounter  uint64
	beforeGetUsersCounter uint64
	GetUsersMock          mUserCacheMockGetUsers

	funcSetUser          func(ctx context.Context, user userentity.User) (err error)
	funcSetUserOrigin    string
	inspectFuncSetUser   func(ctx context.Context, user userentity.User)
	afterSetUserCounter  uint64
	beforeSetUserCounter uint64
	SetUserMock          mUserCacheMockSetUser

	funcSetUsers          func(ctx context.Context, users []userentity.User) (err error)
	funcSetUsersOrigin    string
	inspectFuncSetUsers   func(ctx context.Context, users []userentity.User)
	afterSetUsersCounter  uint64
	beforeSetUsersCounter uint64
	SetUsersMock          mUserCacheMockSetUsers
}

// NewUserCacheMock returns a mock for mm_repository.UserCache
func NewUserCacheMock(t minimock.Tester) *UserCacheMock {
	m := &UserCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.DeleteUserMock = mUserCacheMockDeleteUser{mock: m}
	m.DeleteUserMock.callArgs = []*UserCacheMockDeleteUserParams{}

	m.DeleteUsersMock = mUserCacheMockDeleteUsers{mock: m}
	m.DeleteUsersMock.callArgs = []*UserCacheMockDeleteUsersParams{}

	m.GetUserMock = mUserCacheMockGetUser{mock: m}
	m.GetUserMock.callArgs = []*UserCacheMockGetUserParams{}

	m.GetUsersMock = mUserCacheMockGetUsers{mock: m}
	m.GetUsersMock.callArgs = []*UserCacheMockGetUsersParams{}

	m.SetUserMock = mUserCacheMockSetUser{mock: m}
	m.SetUserMock.callArgs = []*UserCacheMockSetUserParams{}

	m.SetUsersMock = mUserCacheMockSetUsers{mock: m}
	m.SetUsersMock.callArgs = []*UserCacheMockSetUsersParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mUserCacheMockDeleteUser struct {
	optional           bool
	mock               *UserCacheMock
	defaultExpectation *UserCacheMockDeleteUserExpectation
	expectations       []*UserCacheMockDeleteUserExpectation

	callArgs []*UserCacheMockDeleteUserParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserCacheMockDeleteUserExpectation specifies expectation struct of the UserCache.DeleteUser
type UserCacheMockDeleteUserExpectation struct {
	mock               *UserCacheMock
	params             *UserCacheMockDeleteUserParams
	paramPtrs          *UserCacheMockDeleteUserParamPtrs
	expectationOrigins UserCacheMockDeleteUserExpectationOrigins
	results            *UserCacheMockDeleteUserResults
	returnOrigin       string
	Counter            uint64
}

// UserCacheMockDeleteUserParams contains parameters of the UserCache.DeleteUser
type UserCacheMockDeleteUserParams struct {
	ctx context.Context
	id  int64
}

// UserCacheMockDeleteUserParamPtrs contains pointers to parameters of the UserCache.DeleteUser
type UserCacheMockDeleteUserParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// UserCacheMockDeleteUserResults contains results of the UserCache.DeleteUser
type UserCacheMockDeleteUserResults struct {
	err error
}

// UserCacheMockDeleteUserOrigins contains origins of expectations of the UserCache.DeleteUser
type UserCacheMockDeleteUserExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteUser *mUserCacheMockDeleteUser) Optional() *mUserCacheMockDeleteUser {
	mmDeleteUser.optional = true
	return mmDeleteUser
}

// Expect sets up expected params for UserCache.DeleteUser
func (mmDeleteUser *mUserCacheMockDeleteUser) Expect(ctx context.Context, id int64) *mUserCacheMockDeleteUser {
	if mmDeleteUser.mock.funcDeleteUser != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Set")
	}

	if mmDeleteUser.defaultExpectation == nil {
		mmDeleteUser.defaultExpectation = &UserCacheMockDeleteUserExpectation{}
	}

	if mmDeleteUser.defaultExpectation.paramPtrs != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by ExpectParams functions")
	}

	mmDeleteUser.defaultExpectation.params = &UserCacheMockDeleteUserParams{ctx, id}
	mmDeleteUser.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteUser.expectations {
		if minimock.Equal(e.params, mmDeleteUser.defaultExpectation.params) {
			mmDeleteUser.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteUser.defaultExpectation.params)
		}
	}

	return mmDeleteUser
}

// ExpectCtxParam1 sets up expected param ctx for UserCache.DeleteUser
func (mmDeleteUser *mUserCacheMockDeleteUser) ExpectCtxParam1(ctx context.Context) *mUserCacheMockDeleteUser {
	if mmDeleteUser.mock.funcDeleteUser != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Set")
	}

	if mmDeleteUser.defaultExpectation == nil {
		mmDeleteUser.defaultExpectation = &UserCacheMockDeleteUserExpectation{}
	}

	if mmDeleteUser.defaultExpectation.params != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Expect")
	}

	if mmDeleteUser.defaultExpectation.paramPtrs == nil {
		mmDeleteUser.defaultExpectation.paramPtrs = &UserCacheMockDeleteUserParamPtrs{}
	}
	mmDeleteUser.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteUser.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteUser
}

// ExpectIdParam2 sets up expected param id for UserCache.DeleteUser
func (mmDeleteUser *mUserCacheMockDeleteUser) ExpectIdParam2(id int64) *mUserCacheMockDeleteUser {
	if mmDeleteUser.mock.funcDeleteUser != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Set")
	}

	if mmDeleteUser.defaultExpectation == nil {
		mmDeleteUser.defaultExpectation = &UserCacheMockDeleteUserExpectation{}
	}

	if mmDeleteUser.defaultExpectation.params != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Expect")
	}

	if mmDeleteUser.defaultExpectation.paramPtrs == nil {
		mmDeleteUser.defaultExpectation.paramPtrs = &UserCacheMockDeleteUserParamPtrs{}
	}
	mmDeleteUser.defaultExpectation.paramPtrs.id = &id
	mmDeleteUser.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDeleteUser
}

// Inspect accepts an inspector function that has same arguments as the UserCache.DeleteUser
func (mmDeleteUser *mUserCacheMockDeleteUser) Inspect(f func(ctx context.Context, id int64)) *mUserCacheMockDeleteUser {
	if mmDeleteUser.mock.inspectFuncDeleteUser != nil {
		mmDeleteUser.mock.t.Fatalf("Inspect function is already set for UserCacheMock.DeleteUser")
	}

	mmDeleteUser.mock.inspectFuncDeleteUser = f

	return mmDeleteUser
}

// Return sets up results that will be returned by UserCache.DeleteUser
func (mmDeleteUser *mUserCacheMockDeleteUser) Return(err error) *UserCacheMock {
	if mmDeleteUser.mock.funcDeleteUser != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Set")
	}

	if mmDeleteUser.defaultExpectation == nil {
		mmDeleteUser.defaultExpectation = &UserCacheMockDeleteUserExpectation{mock: mmDeleteUser.mock}
	}
	mmDeleteUser.defaultExpectation.results = &UserCacheMockDeleteUserResults{err}
	mmDeleteUser.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteUser.mock
}

// Set uses given function f to mock the UserCache.DeleteUser method
func (mmDeleteUser *mUserCacheMockDeleteUser) Set(f func(ctx context.Context, id int64) (err error)) *UserCacheMock {
	if mmDeleteUser.defaultExpectation != nil {
		mmDeleteUser.mock.t.Fatalf("Default expectation is already set for the UserCache.DeleteUser method")
	}

	if len(mmDeleteUser.expectations) > 0 {
		mmDeleteUser.mock.t.Fatalf("Some expectations are already set for the UserCache.DeleteUser method")
	}

	mmDeleteUser.mock.funcDeleteUser = f
	mmDeleteUser.mock.funcDeleteUserOrigin = minimock.CallerInfo(1)
	return mmDeleteUser.mock
}

// When sets expectation for the UserCache.DeleteUser which will trigger the result defined by the following
// Then helper
func (mmDeleteUser *mUserCacheMockDeleteUser) When(ctx context.Context, id int64) *UserCacheMockDeleteUserExpectation {
	if mmDeleteUser.mock.funcDeleteUser != nil {
		mmDeleteUser.mock.t.Fatalf("UserCacheMock.DeleteUser mock is already set by Set")
	}

	expectation := &UserCacheMockDeleteUserExpectation{
		mock:               mmDeleteUser.mock,
		params:             &UserCacheMockDeleteUserParams{ctx, id},
		expectationOrigins: UserCacheMockDeleteUserExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteUser.expectations = append(mmDeleteUser.expectations, expectation)
	return expectation
}

// Then sets up UserCache.DeleteUser return parameters for the expectation previously defined by the When method
func (e *UserCacheMockDeleteUserExpectation) Then(err error) *UserCacheMock {
	e.results = &UserCacheMockDeleteUserResults{err}
	return e.mock
}

// Times sets number of times UserCache.DeleteUser should be invoked
func (mmDeleteUser *mUserCacheMockDeleteUser) Times(n uint64) *mUserCacheMockDeleteUser {
	if n == 0 {
		mmDeleteUser.mock.t.Fatalf("Times of UserCacheMock.DeleteUser mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteUser.expectedInvocations, n)
	mmDeleteUser.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteUser
}

func (mmDeleteUser *mUserCacheMockDeleteUser) invocationsDone() bool {
	if len(mmDeleteUser.expectations) == 0 && mmDeleteUser.defaultExpectation == nil && mmDeleteUser.mock.funcDeleteUser == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteUser.mock.afterDeleteUserCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteUser.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteUser implements mm_repository.UserCache
func (mmDeleteUser *UserCacheMock) DeleteUser(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteUser.beforeDeleteUserCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteUser.afterDeleteUserCounter, 1)

	mmDeleteUser.t.Helper()

	if mmDeleteUser.inspectFuncDeleteUser != nil {
		mmDeleteUser.inspectFuncDeleteUser(ctx, id)
	}

	mm_params := UserCacheMockDeleteUserParams{ctx, id}

	// Record call args
	mmDeleteUser.DeleteUserMock.mutex.Lock()
	mmDeleteUser.DeleteUserMock.callArgs = append(mmDeleteUser.DeleteUserMock.callArgs, &mm_params)
	mmDeleteUser.DeleteUserMock.mutex.Unlock()

	for _, e := range mmDeleteUser.DeleteUserMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteUser.DeleteUserMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteUser.DeleteUserMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteUser.DeleteUserMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteUser.DeleteUserMock.defaultExpectation.paramPtrs

		mm_got := UserCacheMockDeleteUserParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteUser.t.Errorf("UserCacheMock.DeleteUser got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteUser.DeleteUserMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDeleteUser.t.Errorf("UserCacheMock.DeleteUser got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteUser.DeleteUserMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteUser.t.Errorf("UserCacheMock.DeleteUser got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteUser.DeleteUserMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteUser.DeleteUserMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteUser.t.Fatal("No results are set for the UserCacheMock.DeleteUser")
		}
		return (*mm_results).err
	}
	if mmDeleteUser.funcDeleteUser != nil {
		return mmDeleteUser.funcDeleteUser(ctx, id)
	}
	mmDeleteUser.t.Fatalf("Unexpected call to UserCacheMock.DeleteUser. %v %v", ctx, id)
	return
}

// DeleteUserAfterCounter returns a count of finished UserCacheMock.DeleteUser invocations
func (mmDeleteUser *UserCacheMock) DeleteUserAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteUser.afterDeleteUserCounter)
}

// DeleteUserBeforeCounter returns a count of UserCacheMock.DeleteUser invocations
func (mmDeleteUser *UserCacheMock) DeleteUserBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteUser.beforeDeleteUserCounter)
}

// Calls returns a list of arguments used in each call to UserCacheMock.DeleteUser.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteUser *mUserCacheMockDeleteUser) Calls() []*UserCacheMockDeleteUserParams {
	mmDeleteUser.mutex.RLock()

	argCopy := make([]*UserCacheMockDeleteUserParams, len(mmDeleteUser.callArgs))
	copy(argCopy, mmDeleteUser.callArgs)

	mmDeleteUser.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteUserDone returns true if the count of the DeleteUser invocations corresponds
// the number of defined expectations
func (m *UserCacheMock) MinimockDeleteUserDone() bool {
	if m.DeleteUserMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteUserMock.invocationsDone()
}

// MinimockDeleteUserInspect logs each unmet expectation
func (m *UserCacheMock) MinimockDeleteUserInspect() {
	for _, e := range m.DeleteUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserCacheMock.DeleteUser at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteUserCounter := mm_atomic.LoadUint64(&m.afterDeleteUserCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteUserMock.defaultExpectation != nil && afterDeleteUserCounter < 1 {
		if m.DeleteUserMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserCacheMock.DeleteUser at\n%s", m.DeleteUserMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserCacheMock.DeleteUser at\n%s with params: %#v", m.DeleteUserMock.defaultExpectation.expectationOrigins.origin, *m.DeleteUserMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteUser != nil && afterDeleteUserCounter < 1 {
		m.t.Errorf("Expected call to UserCacheMock.DeleteUser at\n%s", m.funcDeleteUserOrigin)
	}

	if !m.DeleteUserMock.invocationsDone() && afterDeleteUserCounter > 0 {
		m.t.Errorf("Expected %d calls to UserCacheMock.DeleteUser at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteUserMock.expectedInvocations), m.DeleteUserMock.expectedInvocationsOrigin, afterDeleteUserCounter)
	}
}

type mUserCacheMockDeleteUsers struct {
	optional           bool
	mock               *UserCacheMock
	defaultExpectation *UserCacheMockDeleteUsersExpectation
	expectations       []*UserCacheMockDeleteUsersExpectation

	callArgs []*UserCacheMockDeleteUsersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserCacheMockDeleteUsersExpectation specifies expectation struct of the UserCache.DeleteUsers
type UserCacheMockDeleteUsersExpectation struct {
	mock               *UserCacheMock
	params             *UserCacheMockDeleteUsersParams
	paramPtrs          *UserCacheMockDeleteUsersParamPtrs
	expectationOrigins UserCacheMockDeleteUsersExpectationOrigins
	results            *UserCacheMockDeleteUsersResults
	returnOrigin       string
	Counter            uint64
}

// UserCacheMockDeleteUsersParams contains parameters of the UserCache.DeleteUsers
type UserCacheMockDeleteUsersParams struct {
	ctx context.Context
	ids []int64
}

// UserCacheMockDeleteUsersParamPtrs contains pointers to parameters of the UserCache.DeleteUsers
type UserCacheMockDeleteUsersParamPtrs struct {
	ctx *context.Context
	ids *[]int64
}

// UserCacheMockDeleteUsersResults contains results of the UserCache.DeleteUsers
type UserCacheMockDeleteUsersResults struct {
	err error
}

// UserCacheMockDeleteUsersOrigins contains origins of expectations of the UserCache.DeleteUsers
type UserCacheMockDeleteUsersExpectationOrigins struct {
	origin    string
	originCtx string
	originIds string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Optional() *mUserCacheMockDeleteUsers {
	mmDeleteUsers.optional = true
	return mmDeleteUsers
}

// Expect sets up expected params for UserCache.DeleteUsers
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Expect(ctx context.Context, ids []int64) *mUserCacheMockDeleteUsers {
	if mmDeleteUsers.mock.funcDeleteUsers != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Set")
	}

	if mmDeleteUsers.defaultExpectation == nil {
		mmDeleteUsers.defaultExpectation = &UserCacheMockDeleteUsersExpectation{}
	}

	if mmDeleteUsers.defaultExpectation.paramPtrs != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by ExpectParams functions")
	}

	mmDeleteUsers.defaultExpectation.params = &UserCacheMockDeleteUsersParams{ctx, ids}
	mmDeleteUsers.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteUsers.expectations {
		if minimock.Equal(e.params, mmDeleteUsers.defaultExpectation.params) {
			mmDeleteUsers.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteUsers.defaultExpectation.params)
		}
	}

	return mmDeleteUsers
}

// ExpectCtxParam1 sets up expected param ctx for UserCache.DeleteUsers
func (mmDeleteUsers *mUserCacheMockDeleteUsers) ExpectCtxParam1(ctx context.Context) *mUserCacheMockDeleteUsers {
	if mmDeleteUsers.mock.funcDeleteUsers != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Set")
	}

	if mmDeleteUsers.defaultExpectation == nil {
		mmDeleteUsers.defaultExpectation = &UserCacheMockDeleteUsersExpectation{}
	}

	if mmDeleteUsers.defaultExpectation.params != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Expect")
	}

	if mmDeleteUsers.defaultExpectation.paramPtrs == nil {
		mmDeleteUsers.defaultExpectation.paramPtrs = &UserCacheMockDeleteUsersParamPtrs{}
	}
	mmDeleteUsers.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteUsers.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteUsers
}

// ExpectIdsParam2 sets up expected param ids for UserCache.DeleteUsers
func (mmDeleteUsers *mUserCacheMockDeleteUsers) ExpectIdsParam2(ids []int64) *mUserCacheMockDeleteUsers {
	if mmDeleteUsers.mock.funcDeleteUsers != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Set")
	}

	if mmDeleteUsers.defaultExpectation == nil {
		mmDeleteUsers.defaultExpectation = &UserCacheMockDeleteUsersExpectation{}
	}

	if mmDeleteUsers.defaultExpectation.params != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Expect")
	}

	if mmDeleteUsers.defaultExpectation.paramPtrs == nil {
		mmDeleteUsers.defaultExpectation.paramPtrs = &UserCacheMockDeleteUsersParamPtrs{}
	}
	mmDeleteUsers.defaultExpectation.paramPtrs.ids = &ids
	mmDeleteUsers.defaultExpectation.expectationOrigins.originIds = minimock.CallerInfo(1)

	return mmDeleteUsers
}

// Inspect accepts an inspector function that has same arguments as the UserCache.DeleteUsers
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Inspect(f func(ctx context.Context, ids []int64)) *mUserCacheMockDeleteUsers {
	if mmDeleteUsers.mock.inspectFuncDeleteUsers != nil {
		mmDeleteUsers.mock.t.Fatalf("Inspect function is already set for UserCacheMock.DeleteUsers")
	}

	mmDeleteUsers.mock.inspectFuncDeleteUsers = f

	return mmDeleteUsers
}

// Return sets up results that will be returned by UserCache.DeleteUsers
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Return(err error) *UserCacheMock {
	if mmDeleteUsers.mock.funcDeleteUsers != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Set")
	}

	if mmDeleteUsers.defaultExpectation == nil {
		mmDeleteUsers.defaultExpectation = &UserCacheMockDeleteUsersExpectation{mock: mmDeleteUsers.mock}
	}
	mmDeleteUsers.defaultExpectation.results = &UserCacheMockDeleteUsersResults{err}
	mmDeleteUsers.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteUsers.mock
}

// Set uses given function f to mock the UserCache.DeleteUsers method
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Set(f func(ctx context.Context, ids []int64) (err error)) *UserCacheMock {
	if mmDeleteUsers.defaultExpectation != nil {
		mmDeleteUsers.mock.t.Fatalf("Default expectation is already set for the UserCache.DeleteUsers method")
	}

	if len(mmDeleteUsers.expectations) > 0 {
		mmDeleteUsers.mock.t.Fatalf("Some expectations are already set for the UserCache.DeleteUsers method")
	}

	mmDeleteUsers.mock.funcDeleteUsers = f
	mmDeleteUsers.mock.funcDeleteUsersOrigin = minimock.CallerInfo(1)
	return mmDeleteUsers.mock
}

// When sets expectation for the UserCache.DeleteUsers which will trigger the result defined by the following
// Then helper
func (mmDeleteUsers *mUserCacheMockDeleteUsers) When(ctx context.Context, ids []int64) *UserCacheMockDeleteUsersExpectation {
	if mmDeleteUsers.mock.funcDeleteUsers != nil {
		mmDeleteUsers.mock.t.Fatalf("UserCacheMock.DeleteUsers mock is already set by Set")
	}

	expectation := &UserCacheMockDeleteUsersExpectation{
		mock:               mmDeleteUsers.mock,
		params:             &UserCacheMockDeleteUsersParams{ctx, ids},
		expectationOrigins: UserCacheMockDeleteUsersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteUsers.expectations = append(mmDeleteUsers.expectations, expectation)
	return expectation
}

// Then sets up UserCache.DeleteUsers return parameters for the expectation previously defined by the When method
func (e *UserCacheMockDeleteUsersExpectation) Then(err error) *UserCacheMock {
	e.results = &UserCacheMockDeleteUsersResults{err}
	return e.mock
}

// Times sets number of times UserCache.DeleteUsers should be invoked
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Times(n uint64) *mUserCacheMockDeleteUsers {
	if n == 0 {
		mmDeleteUsers.mock.t.Fatalf("Times of UserCacheMock.DeleteUsers mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteUsers.expectedInvocations, n)
	mmDeleteUsers.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteUsers
}

func (mmDeleteUsers *mUserCacheMockDeleteUsers) invocationsDone() bool {
	if len(mmDeleteUsers.expectations) == 0 && mmDeleteUsers.defaultExpectation == nil && mmDeleteUsers.mock.funcDeleteUsers == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteUsers.mock.afterDeleteUsersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteUsers.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteUsers implements mm_repository.UserCache
func (mmDeleteUsers *UserCacheMock) DeleteUsers(ctx context.Context, ids []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteUsers.beforeDeleteUsersCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteUsers.afterDeleteUsersCounter, 1)

	mmDeleteUsers.t.Helper()

	if mmDeleteUsers.inspectFuncDeleteUsers != nil {
		mmDeleteUsers.inspectFuncDeleteUsers(ctx, ids)
	}

	mm_params := UserCacheMockDeleteUsersParams{ctx, ids}

	// Record call args
	mmDeleteUsers.DeleteUsersMock.mutex.Lock()
	mmDeleteUsers.DeleteUsersMock.callArgs = append(mmDeleteUsers.DeleteUsersMock.callArgs, &mm_params)
	mmDeleteUsers.DeleteUsersMock.mutex.Unlock()

	for _, e := range mmDeleteUsers.DeleteUsersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteUsers.DeleteUsersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteUsers.DeleteUsersMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteUsers.DeleteUsersMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteUsers.DeleteUsersMock.defaultExpectation.paramPtrs

		mm_got := UserCacheMockDeleteUsersParams{ctx, ids}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteUsers.t.Errorf("UserCacheMock.DeleteUsers got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteUsers.DeleteUsersMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.ids != nil && !minimock.Equal(*mm_want_ptrs.ids, mm_got.ids) {
				mmDeleteUsers.t.Errorf("UserCacheMock.DeleteUsers got unexpected parameter ids, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteUsers.DeleteUsersMock.defaultExpectation.expectationOrigins.originIds, *mm_want_ptrs.ids, mm_got.ids, minimock.Diff(*mm_want_ptrs.ids, mm_got.ids))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteUsers.t.Errorf("UserCacheMock.DeleteUsers got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteUsers.DeleteUsersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteUsers.DeleteUsersMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteUsers.t.Fatal("No results are set for the UserCacheMock.DeleteUsers")
		}
		return (*mm_results).err
	}
	if mmDeleteUsers.funcDeleteUsers != nil {
		return mmDeleteUsers.funcDeleteUsers(ctx, ids)
	}
	mmDeleteUsers.t.Fatalf("Unexpected call to UserCacheMock.DeleteUsers. %v %v", ctx, ids)
	return
}

// DeleteUsersAfterCounter returns a count of finished UserCacheMock.DeleteUsers invocations
func (mmDeleteUsers *UserCacheMock) DeleteUsersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteUsers.afterDeleteUsersCounter)
}

// DeleteUsersBeforeCounter returns a count of UserCacheMock.DeleteUsers invocations
func (mmDeleteUsers *UserCacheMock) DeleteUsersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteUsers.beforeDeleteUsersCounter)
}

// Calls returns a list of arguments used in each call to UserCacheMock.DeleteUsers.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteUsers *mUserCacheMockDeleteUsers) Calls() []*UserCacheMockDeleteUsersParams {
	mmDeleteUsers.mutex.RLock()

	argCopy := make([]*UserCacheMockDeleteUsersParams, len(mmDeleteUsers.callArgs))
	copy(argCopy, mmDeleteUsers.callArgs)

	mmDeleteUsers.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteUsersDone returns true if the count of the DeleteUsers invocations corresponds
// the number of defined expectations
func (m *UserCacheMock) MinimockDeleteUsersDone() bool {
	if m.DeleteUsersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteUsersMock.invocationsDone()
}

// MinimockDeleteUsersInspect logs each unmet expectation
func (m *UserCacheMock) MinimockDeleteUsersInspect() {
	for _, e := range m.DeleteUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserCacheMock.DeleteUsers at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteUsersCounter := mm_atomic.LoadUint64(&m.afterDeleteUsersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteUsersMock.defaultExpectation != nil && afterDeleteUsersCounter < 1 {
		if m.DeleteUsersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserCacheMock.DeleteUsers at\n%s", m.DeleteUsersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserCacheMock.DeleteUsers at\n%s with params: %#v", m.DeleteUsersMock.defaultExpectation.expectationOrigins.origin, *m.DeleteUsersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteUsers != nil && afterDeleteUsersCounter < 1 {
		m.t.Errorf("Expected call to UserCacheMock.DeleteUsers at\n%s", m.funcDeleteUsersOrigin)
	}

	if !m.DeleteUsersMock.invocationsDone() && afterDeleteUsersCounter > 0 {
		m.t.Errorf("Expected %d calls to UserCacheMock.DeleteUsers at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteUsersMock.expectedInvocations), m.DeleteUsersMock.expectedInvocationsOrigin, afterDeleteUsersCounter)
	}
}

type mUserCacheMockGetUser struct {
	optional           bool
	mock               *UserCacheMock
	defaultExpectation *UserCacheMockGetUserExpectation
	expectations       []*UserCacheMockGetUserExpectation

	callArgs []*UserCacheMockGetUserParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserCacheMockGetUserExpectation specifies expectation struct of the UserCache.GetUser
type UserCacheMockGetUserExpectation struct {
	mock               *UserCacheMock
	params             *UserCacheMockGetUserParams
	paramPtrs          *UserCacheMockGetUserParamPtrs
	expectationOrigins UserCacheMockGetUserExpectationOrigins
	results            *UserCacheMockGetUserResults
	returnOrigin       string
	Counter            uint64
}

// UserCacheMockGetUserParams contains parameters of the UserCache.GetUser
type UserCacheMockGetUserParams struct {
	ctx context.Context
	id  int64
}

// UserCacheMockGetUserParamPtrs contains pointers to parameters of the UserCache.GetUser
type UserCacheMockGetUserParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// UserCacheMockGetUserResults contains results of the UserCache.GetUser
type UserCacheMockGetUserResults struct {
	u1  userentity.User
	err error
}

// UserCacheMockGetUserOrigins contains origins of expectations of the UserCache.GetUser
type UserCacheMockGetUserExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUser *mUserCacheMockGetUser) Optional() *mUserCacheMockGetUser {
	mmGetUser.optional = true
	return mmGetUser
}

// Expect sets up expected params for UserCache.GetUser
func (mmGetUser *mUserCacheMockGetUser) Expect(ctx context.Context, id int64) *mUserCacheMockGetUser {
	if mmGetUser.mock.funcGetUser != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Set")
	}

	if mmGetUser.defaultExpectation == nil {
		mmGetUser.defaultExpectation = &UserCacheMockGetUserExpectation{}
	}

	if mmGetUser.defaultExpectation.paramPtrs != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by ExpectParams functions")
	}

	mmGetUser.defaultExpectation.params = &UserCacheMockGetUserParams{ctx, id}
	mmGetUser.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUser.expectations {
		if minimock.Equal(e.params, mmGetUser.defaultExpectation.params) {
			mmGetUser.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUser.defaultExpectation.params)
		}
	}

	return mmGetUser
}

// ExpectCtxParam1 sets up expected param ctx for UserCache.GetUser
func (mmGetUser *mUserCacheMockGetUser) ExpectCtxParam1(ctx context.Context) *mUserCacheMockGetUser {
	if mmGetUser.mock.funcGetUser != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Set")
	}

	if mmGetUser.defaultExpectation == nil {
		mmGetUser.defaultExpectation = &UserCacheMockGetUserExpectation{}
	}

	if mmGetUser.defaultExpectation.params != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Expect")
	}

	if mmGetUser.defaultExpectation.paramPtrs == nil {
		mmGetUser.defaultExpectation.paramPtrs = &UserCacheMockGetUserParamPtrs{}
	}
	mmGetUser.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetUser.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetUser
}

// ExpectIdParam2 sets up expected param id for UserCache.GetUser
func (mmGetUser *mUserCacheMockGetUser) ExpectIdParam2(id int64) *mUserCacheMockGetUser {
	if mmGetUser.mock.funcGetUser != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Set")
	}

	if mmGetUser.defaultExpectation == nil {
		mmGetUser.defaultExpectation = &UserCacheMockGetUserExpectation{}
	}

	if mmGetUser.defaultExpectation.params != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Expect")
	}

	if mmGetUser.defaultExpectation.paramPtrs == nil {
		mmGetUser.defaultExpectation.paramPtrs = &UserCacheMockGetUserParamPtrs{}
	}
	mmGetUser.defaultExpectation.paramPtrs.id = &id
	mmGetUser.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetUser
}

// Inspect accepts an inspector function that has same arguments as the UserCache.GetUser
func (mmGetUser *mUserCacheMockGetUser) Inspect(f func(ctx context.Context, id int64)) *mUserCacheMockGetUser {
	if mmGetUser.mock.inspectFuncGetUser != nil {
		mmGetUser.mock.t.Fatalf("Inspect function is already set for UserCacheMock.GetUser")
	}

	mmGetUser.mock.inspectFuncGetUser = f

	return mmGetUser
}

// Return sets up results that will be returned by UserCache.GetUser
func (mmGetUser *mUserCacheMockGetUser) Return(u1 userentity.User, err error) *UserCacheMock {
	if mmGetUser.mock.funcGetUser != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Set")
	}

	if mmGetUser.defaultExpectation == nil {
		mmGetUser.defaultExpectation = &UserCacheMockGetUserExpectation{mock: mmGetUser.mock}
	}
	mmGetUser.defaultExpectation.results = &UserCacheMockGetUserResults{u1, err}
	mmGetUser.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUser.mock
}

// Set uses given function f to mock the UserCache.GetUser method
func (mmGetUser *mUserCacheMockGetUser) Set(f func(ctx context.Context, id int64) (u1 userentity.User, err error)) *UserCacheMock {
	if mmGetUser.defaultExpectation != nil {
		mmGetUser.mock.t.Fatalf("Default expectation is already set for the UserCache.GetUser method")
	}

	if len(mmGetUser.expectations) > 0 {
		mmGetUser.mock.t.Fatalf("Some expectations are already set for the UserCache.GetUser method")
	}

	mmGetUser.mock.funcGetUser = f
	mmGetUser.mock.funcGetUserOrigin = minimock.CallerInfo(1)
	return mmGetUser.mock
}

// When sets expectation for the UserCache.GetUser which will trigger the result defined by the following
// Then helper
func (mmGetUser *mUserCacheMockGetUser) When(ctx context.Context, id int64) *UserCacheMockGetUserExpectation {
	if mmGetUser.mock.funcGetUser != nil {
		mmGetUser.mock.t.Fatalf("UserCacheMock.GetUser mock is already set by Set")
	}

	expectation := &UserCacheMockGetUserExpectation{
		mock:               mmGetUser.mock,
		params:             &UserCacheMockGetUserParams{ctx, id},
		expectationOrigins: UserCacheMockGetUserExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUser.expectations = append(mmGetUser.expectations, expectation)
	return expectation
}

// Then sets up UserCache.GetUser return parameters for the expectation previously defined by the When method
func (e *UserCacheMockGetUserExpectation) Then(u1 userentity.User, err error) *UserCacheMock {
	e.results = &UserCacheMockGetUserResults{u1, err}
	return e.mock
}

// Times sets number of times UserCache.GetUser should be invoked
func (mmGetUser *mUserCacheMockGetUser) Times(n uint64) *mUserCacheMockGetUser {
	if n == 0 {
		mmGetUser.mock.t.Fatalf("Times of UserCacheMock.GetUser mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUser.expectedInvocations, n)
	mmGetUser.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUser
}

func (mmGetUser *mUserCacheMockGetUser) invocationsDone() bool {
	if len(mmGetUser.expectations) == 0 && mmGetUser.defaultExpectation == nil && mmGetUser.mock.funcGetUser == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUser.mock.afterGetUserCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUser.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUser implements mm_repository.UserCache
func (mmGetUser *UserCacheMock) GetUser(ctx context.Context, id int64) (u1 userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetUser.beforeGetUserCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUser.afterGetUserCounter, 1)

	mmGetUser.t.Helper()

	if mmGetUser.inspectFuncGetUser != nil {
		mmGetUser.inspectFuncGetUser(ctx, id)
	}

	mm_params := UserCacheMockGetUserParams{ctx, id}

	// Record call args
	mmGetUser.GetUserMock.mutex.Lock()
	mmGetUser.GetUserMock.callArgs = append(mmGetUser.GetUserMock.callArgs, &mm_params)
	mmGetUser.GetUserMock.mutex.Unlock()

	for _, e := range mmGetUser.GetUserMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetUser.GetUserMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUser.GetUserMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUser.GetUserMock.defaultExpectation.params
		mm_want_ptrs := mmGetUser.GetUserMock.defaultExpectation.paramPtrs

		mm_got := UserCacheMockGetUserParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetUser.t.Errorf("UserCacheMock.GetUser got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUser.GetUserMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetUser.t.Errorf("UserCacheMock.GetUser got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUser.GetUserMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUser.t.Errorf("UserCacheMock.GetUser got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUser.GetUserMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUser.GetUserMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUser.t.Fatal("No results are set for the UserCacheMock.GetUser")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetUser.funcGetUser != nil {
		return mmGetUser.funcGetUser(ctx, id)
	}
	mmGetUser.t.Fatalf("Unexpected call to UserCacheMock.GetUser. %v %v", ctx, id)
	return
}

// GetUserAfterCounter returns a count of finished UserCacheMock.GetUser invocations
func (mmGetUser *UserCacheMock) GetUserAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUser.afterGetUserCounter)
}

// GetUserBeforeCounter returns a count of UserCacheMock.GetUser invocations
func (mmGetUser *UserCacheMock) GetUserBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUser.beforeGetUserCounter)
}

// Calls returns a list of arguments used in each call to UserCacheMock.GetUser.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUser *mUserCacheMockGetUser) Calls() []*UserCacheMockGetUserParams {
	mmGetUser.mutex.RLock()

	argCopy := make([]*UserCacheMockGetUserParams, len(mmGetUser.callArgs))
	copy(argCopy, mmGetUser.callArgs)

	mmGetUser.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserDone returns true if the count of the GetUser invocations corresponds
// the number of defined expectations
func (m *UserCacheMock) MinimockGetUserDone() bool {
	if m.GetUserMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserMock.invocationsDone()
}

// MinimockGetUserInspect logs each unmet expectation
func (m *UserCacheMock) MinimockGetUserInspect() {
	for _, e := range m.GetUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserCacheMock.GetUser at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserCounter := mm_atomic.LoadUint64(&m.afterGetUserCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserMock.defaultExpectation != nil && afterGetUserCounter < 1 {
		if m.GetUserMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserCacheMock.GetUser at\n%s", m.GetUserMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserCacheMock.GetUser at\n%s with params: %#v", m.GetUserMock.defaultExpectation.expectationOrigins.origin, *m.GetUserMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUser != nil && afterGetUserCounter < 1 {
		m.t.Errorf("Expected call to UserCacheMock.GetUser at\n%s", m.funcGetUserOrigin)
	}

	if !m.GetUserMock.invocationsDone() && afterGetUserCounter > 0 {
		m.t.Errorf("Expected %d calls to UserCacheMock.GetUser at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserMock.expectedInvocations), m.GetUserMock.expectedInvocationsOrigin, afterGetUserCounter)
	}
}

type mUserCacheMockGetUsers struct {
	optional           bool
	mock               *UserCacheMock
	defaultExpectation *UserCacheMockGetUsersExpectation
	expectations       []*UserCacheMockGetUsersExpectation

	callArgs []*UserCacheMockGetUsersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserCacheMockGetUsersExpectation specifies expectation struct of the UserCache.GetUsers
type UserCacheMockGetUsersExpectation struct {
	mock               *UserCacheMock
	params             *UserCacheMockGetUsersParams
	paramPtrs          *UserCacheMockGetUsersParamPtrs
	expectationOrigins UserCacheMockGetUsersExpectationOrigins
	results            *UserCacheMockGetUsersResults
	returnOrigin       string
	Counter            uint64
}

// UserCacheMockGetUsersParams contains parameters of the UserCache.GetUsers
type UserCacheMockGetUsersParams struct {
	ctx context.Context
}

// UserCacheMockGetUsersParamPtrs contains pointers to parameters of the UserCache.GetUsers
type UserCacheMockGetUsersParamPtrs struct {
	ctx *context.Context
}

// UserCacheMockGetUsersResults contains results of the UserCache.GetUsers
type UserCacheMockGetUsersResults struct {
	ua1 []userentity.User
	err error
}

// UserCacheMockGetUsersOrigins contains origins of expectations of the UserCache.GetUsers
type UserCacheMockGetUsersExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsers *mUserCacheMockGetUsers) Optional() *mUserCacheMockGetUsers {
	mmGetUsers.optional = true
	return mmGetUsers
}

// Expect sets up expected params for UserCache.GetUsers
func (mmGetUsers *mUserCacheMockGetUsers) Expect(ctx context.Context) *mUserCacheMockGetUsers {
	if mmGetUsers.mock.funcGetUsers != nil {
		mmGetUsers.mock.t.Fatalf("UserCacheMock.GetUsers mock is already set by Set")
	}

	if mmGetUsers.defaultExpectation == nil {
		mmGetUsers.defaultExpectation = &UserCacheMockGetUsersExpectation{}
	}

	if mmGetUsers.defaultExpectation.paramPtrs != nil {
		mmGetUsers.mock.t.Fatalf("UserCacheMock.GetUsers mock is already set by ExpectParams functions")
	}

	mmGetUsers.defaultExpectation.params = &UserCacheMockGetUsersParams{ctx}
	mmGetUsers.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsers.expectations {
		if minimock.Equal(e.params, mmGetUsers.defaultExpectation.params) {
			mmGetUsers.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsers.defaultExpectation.params)
		}
	}

	return mmGetUsers
}

// ExpectCtxParam1 sets up expected param ctx for UserCache.GetUsers
func (mmGetUsers *mUserCacheMockGetUsers) ExpectCtxParam1(ctx context.Context) *mUserCacheMockGetUsers {
	if mmGetUsers.mock.funcGetUsers != nil {
		mmGetUsers.mock.t.Fatalf("UserCacheMock.GetUsers mock is already set by Set")
	}

	if mmGetUsers.defaultExpectation == nil {
		mmGetUsers.defaultExpectation = &UserCacheMockGetUsersExpectation{}
	}

	if mmGetUsers.defaultExpectation.params != nil {
		mmGetUsers.mock.t.Fatalf("UserCacheMock.GetUsers mock is already set by Expect")
	}

	if mmGetUsers.defaultExpectation.paramPtrs == nil {
		mmGetUsers.defaultExpectation.paramPtrs = &UserCacheMockGetUsersParamPtrs{}
	}
	mmGetUsers.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetUsers.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetUsers
}

// Inspect accepts an inspector function that has same arguments as the UserCache.GetUsers
func (mmGetUsers *mUserCacheMockGetUsers) Inspect(f func(ctx context.Context)) *mUserCacheMockGetUsers {
	if mmGetUsers.mock.inspectFuncGetUsers != nil {
		mmGetUsers.mock.t.Fatalf("Inspect function is already set for UserCacheMock.GetUsers")
	}

	mmGetUsers.mock.inspectFuncGetUsers = f

	return mmGetUsers
}

// Return sets up results that will be returned by UserCache.GetUsers
func (mmGetUsers *mUserCacheMockGetUsers) Return(ua1 []userentity.User, err error) *UserCacheMock {
	if mmGetUsers.mock.funcGetUsers != nil {
		mmGetUsers.mock.t.Fatalf("UserCacheMock.GetUsers mock is already set by Set")
	}

	if mmGetUsers.defaultExpectation == nil {
		mmGetUsers.defaultExpectation = &UserCacheMockGetUsersExpectation{mock: mmGetUsers.mock}
	}
	mmGetUsers.defaultExpectation.results = &UserCacheMockGetUsersResults{ua1, err}
	mmGetUsers.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsers.mock
}

// Set uses given function f to mock the UserCache.GetUsers method
func (mmGetUsers *mUserCacheMockGetUsers) Set(f func(ctx context.Context) (ua1 []userentity.User, err error)) *UserCacheMock {
	if mmGetUsers.defaultExpectation != nil {
		mmGetUsers.mock.t.Fatalf("Default expectation is already set for the UserCache.GetUsers method")
	}

	if len(mmGetUsers.expectations) > 0 {
		mmGetUsers.mock.t.Fatalf("Some expectations are already set for the UserCache.GetUsers method")
	}

	mmGetUsers.mock.funcGetUsers = f
	mmGetUsers.mock.funcGetUsersOrigin = minimock.CallerInfo(1)
	return mmGetUsers.mock
}

// When sets expectation for the UserCache.GetUsers which will trigger the result defined by the following
// Then helper
func (mmGetUsers *mUserCacheMockGetUsers) When(ctx context.Context) *UserCacheMockGetUsersExpectation {
	if mmGetUsers.mock.funcGetUsers != nil {
		mmGetUsers.mock.t.Fatalf("UserCacheMock.GetUsers mock is already set by Set")
	}

	expectation := &UserCacheMockGetUsersExpectation{
		mock:               mmGetUsers.mock,
		params:             &UserCacheMockGetUsersParams{ctx},
		expectationOrigins: UserCacheMockGetUsersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsers.expectations = append(mmGetUsers.expectations, expectation)
	return expectation
}

// Then sets up UserCache.GetUsers return parameters for the expectation previously defined by the When method
func (e *UserCacheMockGetUsersExpectation) Then(ua1 []userentity.User, err error) *UserCacheMock {
	e.results = &UserCacheMockGetUsersResults{ua1, err}
	return e.mock
}

// Times sets number of times UserCache.GetUsers should be invoked
func (mmGetUsers *mUserCacheMockGetUsers) Times(n uint64) *mUserCacheMockGetUsers {
	if n == 0 {
		mmGetUsers.mock.t.Fatalf("Times of UserCacheMock.GetUsers mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsers.expectedInvocations, n)
	mmGetUsers.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsers
}

func (mmGetUsers *mUserCacheMockGetUsers) invocationsDone() bool {
	if len(mmGetUsers.expectations) == 0 && mmGetUsers.defaultExpectation == nil && mmGetUsers.mock.funcGetUsers == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsers.mock.afterGetUsersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsers.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsers implements mm_repository.UserCache
func (mmGetUsers *UserCacheMock) GetUsers(ctx context.Context) (ua1 []userentity.User, err error) {
	mm_atomic.AddUint64(&mmGetUsers.beforeGetUsersCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsers.afterGetUsersCounter, 1)

	mmGetUsers.t.Helper()

	if mmGetUsers.inspectFuncGetUsers != nil {
		mmGetUsers.inspectFuncGetUsers(ctx)
	}

	mm_params := UserCacheMockGetUsersParams{ctx}

	// Record call args
	mmGetUsers.GetUsersMock.mutex.Lock()
	mmGetUsers.GetUsersMock.callArgs = append(mmGetUsers.GetUsersMock.callArgs, &mm_params)
	mmGetUsers.GetUsersMock.mutex.Unlock()

	for _, e := range mmGetUsers.GetUsersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUsers.GetUsersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsers.GetUsersMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsers.GetUsersMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsers.GetUsersMock.defaultExpectation.paramPtrs

		mm_got := UserCacheMockGetUsersParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetUsers.t.Errorf("UserCacheMock.GetUsers got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsers.GetUsersMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsers.t.Errorf("UserCacheMock.GetUsers got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsers.GetUsersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsers.GetUsersMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsers.t.Fatal("No results are set for the UserCacheMock.GetUsers")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUsers.funcGetUsers != nil {
		return mmGetUsers.funcGetUsers(ctx)
	}
	mmGetUsers.t.Fatalf("Unexpected call to UserCacheMock.GetUsers. %v", ctx)
	return
}

// GetUsersAfterCounter returns a count of finished UserCacheMock.GetUsers invocations
func (mmGetUsers *UserCacheMock) GetUsersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsers.afterGetUsersCounter)
}

// GetUsersBeforeCounter returns a count of UserCacheMock.GetUsers invocations
func (mmGetUsers *UserCacheMock) GetUsersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsers.beforeGetUsersCounter)
}

// Calls returns a list of arguments used in each call to UserCacheMock.GetUsers.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsers *mUserCacheMockGetUsers) Calls() []*UserCacheMockGetUsersParams {
	mmGetUsers.mutex.RLock()

	argCopy := make([]*UserCacheMockGetUsersParams, len(mmGetUsers.callArgs))
	copy(argCopy, mmGetUsers.callArgs)

	mmGetUsers.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersDone returns true if the count of the GetUsers invocations corresponds
// the number of defined expectations
func (m *UserCacheMock) MinimockGetUsersDone() bool {
	if m.GetUsersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersMock.invocationsDone()
}

// MinimockGetUsersInspect logs each unmet expectation
func (m *UserCacheMock) MinimockGetUsersInspect() {
	for _, e := range m.GetUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserCacheMock.GetUsers at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersCounter := mm_atomic.LoadUint64(&m.afterGetUsersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersMock.defaultExpectation != nil && afterGetUsersCounter < 1 {
		if m.GetUsersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserCacheMock.GetUsers at\n%s", m.GetUsersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserCacheMock.GetUsers at\n%s with params: %#v", m.GetUsersMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsers != nil && afterGetUsersCounter < 1 {
		m.t.Errorf("Expected call to UserCacheMock.GetUsers at\n%s", m.funcGetUsersOrigin)
	}

	if !m.GetUsersMock.invocationsDone() && afterGetUsersCounter > 0 {
		m.t.Errorf("Expected %d calls to UserCacheMock.GetUsers at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersMock.expectedInvocations), m.GetUsersMock.expectedInvocationsOrigin, afterGetUsersCounter)
	}
}

type mUserCacheMockSetUser struct {
	optional           bool
	mock               *UserCacheMock
	defaultExpectation *UserCacheMockSetUserExpectation
	expectations       []*UserCacheMockSetUserExpectation

	callArgs []*UserCacheMockSetUserParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserCacheMockSetUserExpectation specifies expectation struct of the UserCache.SetUser
type UserCacheMockSetUserExpectation struct {
	mock               *UserCacheMock
	params             *UserCacheMockSetUserParams
	paramPtrs          *UserCacheMockSetUserParamPtrs
	expectationOrigins UserCacheMockSetUserExpectationOrigins
	results            *UserCacheMockSetUserResults
	returnOrigin       string
	Counter            uint64
}

// UserCacheMockSetUserParams contains parameters of the UserCache.SetUser
type UserCacheMockSetUserParams struct {
	ctx  context.Context
	user userentity.User
}

// UserCacheMockSetUserParamPtrs contains pointers to parameters of the UserCache.SetUser
type UserCacheMockSetUserParamPtrs struct {
	ctx  *context.Context
	user *userentity.User
}

// UserCacheMockSetUserResults contains results of the UserCache.SetUser
type UserCacheMockSetUserResults struct {
	err error
}

// UserCacheMockSetUserOrigins contains origins of expectations of the UserCache.SetUser
type UserCacheMockSetUserExpectationOrigins struct {
	origin     string
	originCtx  string
	originUser string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetUser *mUserCacheMockSetUser) Optional() *mUserCacheMockSetUser {
	mmSetUser.optional = true
	return mmSetUser
}

// Expect sets up expected params for UserCache.SetUser
func (mmSetUser *mUserCacheMockSetUser) Expect(ctx context.Context, user userentity.User) *mUserCacheMockSetUser {
	if mmSetUser.mock.funcSetUser != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Set")
	}

	if mmSetUser.defaultExpectation == nil {
		mmSetUser.defaultExpectation = &UserCacheMockSetUserExpectation{}
	}

	if mmSetUser.defaultExpectation.paramPtrs != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by ExpectParams functions")
	}

	mmSetUser.defaultExpectation.params = &UserCacheMockSetUserParams{ctx, user}
	mmSetUser.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetUser.expectations {
		if minimock.Equal(e.params, mmSetUser.defaultExpectation.params) {
			mmSetUser.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetUser.defaultExpectation.params)
		}
	}

	return mmSetUser
}

// ExpectCtxParam1 sets up expected param ctx for UserCache.SetUser
func (mmSetUser *mUserCacheMockSetUser) ExpectCtxParam1(ctx context.Context) *mUserCacheMockSetUser {
	if mmSetUser.mock.funcSetUser != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Set")
	}

	if mmSetUser.defaultExpectation == nil {
		mmSetUser.defaultExpectation = &UserCacheMockSetUserExpectation{}
	}

	if mmSetUser.defaultExpectation.params != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Expect")
	}

	if mmSetUser.defaultExpectation.paramPtrs == nil {
		mmSetUser.defaultExpectation.paramPtrs = &UserCacheMockSetUserParamPtrs{}
	}
	mmSetUser.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetUser.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetUser
}

// ExpectUserParam2 sets up expected param user for UserCache.SetUser
func (mmSetUser *mUserCacheMockSetUser) ExpectUserParam2(user userentity.User) *mUserCacheMockSetUser {
	if mmSetUser.mock.funcSetUser != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Set")
	}

	if mmSetUser.defaultExpectation == nil {
		mmSetUser.defaultExpectation = &UserCacheMockSetUserExpectation{}
	}

	if mmSetUser.defaultExpectation.params != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Expect")
	}

	if mmSetUser.defaultExpectation.paramPtrs == nil {
		mmSetUser.defaultExpectation.paramPtrs = &UserCacheMockSetUserParamPtrs{}
	}
	mmSetUser.defaultExpectation.paramPtrs.user = &user
	mmSetUser.defaultExpectation.expectationOrigins.originUser = minimock.CallerInfo(1)

	return mmSetUser
}

// Inspect accepts an inspector function that has same arguments as the UserCache.SetUser
func (mmSetUser *mUserCacheMockSetUser) Inspect(f func(ctx context.Context, user userentity.User)) *mUserCacheMockSetUser {
	if mmSetUser.mock.inspectFuncSetUser != nil {
		mmSetUser.mock.t.Fatalf("Inspect function is already set for UserCacheMock.SetUser")
	}

	mmSetUser.mock.inspectFuncSetUser = f

	return mmSetUser
}

// Return sets up results that will be returned by UserCache.SetUser
func (mmSetUser *mUserCacheMockSetUser) Return(err error) *UserCacheMock {
	if mmSetUser.mock.funcSetUser != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Set")
	}

	if mmSetUser.defaultExpectation == nil {
		mmSetUser.defaultExpectation = &UserCacheMockSetUserExpectation{mock: mmSetUser.mock}
	}
	mmSetUser.defaultExpectation.results = &UserCacheMockSetUserResults{err}
	mmSetUser.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetUser.mock
}

// Set uses given function f to mock the UserCache.SetUser method
func (mmSetUser *mUserCacheMockSetUser) Set(f func(ctx context.Context, user userentity.User) (err error)) *UserCacheMock {
	if mmSetUser.defaultExpectation != nil {
		mmSetUser.mock.t.Fatalf("Default expectation is already set for the UserCache.SetUser method")
	}

	if len(mmSetUser.expectations) > 0 {
		mmSetUser.mock.t.Fatalf("Some expectations are already set for the UserCache.SetUser method")
	}

	mmSetUser.mock.funcSetUser = f
	mmSetUser.mock.funcSetUserOrigin = minimock.CallerInfo(1)
	return mmSetUser.mock
}

// When sets expectation for the UserCache.SetUser which will trigger the result defined by the following
// Then helper
func (mmSetUser *mUserCacheMockSetUser) When(ctx context.Context, user userentity.User) *UserCacheMockSetUserExpectation {
	if mmSetUser.mock.funcSetUser != nil {
		mmSetUser.mock.t.Fatalf("UserCacheMock.SetUser mock is already set by Set")
	}

	expectation := &UserCacheMockSetUserExpectation{
		mock:               mmSetUser.mock,
		params:             &UserCacheMockSetUserParams{ctx, user},
		expectationOrigins: UserCacheMockSetUserExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetUser.expectations = append(mmSetUser.expectations, expectation)
	return expectation
}

// Then sets up UserCache.SetUser return parameters for the expectation previously defined by the When method
func (e *UserCacheMockSetUserExpectation) Then(err error) *UserCacheMock {
	e.results = &UserCacheMockSetUserResults{err}
	return e.mock
}

// Times sets number of times UserCache.SetUser should be invoked
func (mmSetUser *mUserCacheMockSetUser) Times(n uint64) *mUserCacheMockSetUser {
	if n == 0 {
		mmSetUser.mock.t.Fatalf("Times of UserCacheMock.SetUser mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetUser.expectedInvocations, n)
	mmSetUser.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetUser
}

func (mmSetUser *mUserCacheMockSetUser) invocationsDone() bool {
	if len(mmSetUser.expectations) == 0 && mmSetUser.defaultExpectation == nil && mmSetUser.mock.funcSetUser == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetUser.mock.afterSetUserCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetUser.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetUser implements mm_repository.UserCache
func (mmSetUser *UserCacheMock) SetUser(ctx context.Context, user userentity.User) (err error) {
	mm_atomic.AddUint64(&mmSetUser.beforeSetUserCounter, 1)
	defer mm_atomic.AddUint64(&mmSetUser.afterSetUserCounter, 1)

	mmSetUser.t.Helper()

	if mmSetUser.inspectFuncSetUser != nil {
		mmSetUser.inspectFuncSetUser(ctx, user)
	}

	mm_params := UserCacheMockSetUserParams{ctx, user}

	// Record call args
	mmSetUser.SetUserMock.mutex.Lock()
	mmSetUser.SetUserMock.callArgs = append(mmSetUser.SetUserMock.callArgs, &mm_params)
	mmSetUser.SetUserMock.mutex.Unlock()

	for _, e := range mmSetUser.SetUserMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetUser.SetUserMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetUser.SetUserMock.defaultExpectation.Counter, 1)
		mm_want := mmSetUser.SetUserMock.defaultExpectation.params
		mm_want_ptrs := mmSetUser.SetUserMock.defaultExpectation.paramPtrs

		mm_got := UserCacheMockSetUserParams{ctx, user}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetUser.t.Errorf("UserCacheMock.SetUser got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetUser.SetUserMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.user != nil && !minimock.Equal(*mm_want_ptrs.user, mm_got.user) {
				mmSetUser.t.Errorf("UserCacheMock.SetUser got unexpected parameter user, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetUser.SetUserMock.defaultExpectation.expectationOrigins.originUser, *mm_want_ptrs.user, mm_got.user, minimock.Diff(*mm_want_ptrs.user, mm_got.user))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetUser.t.Errorf("UserCacheMock.SetUser got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetUser.SetUserMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetUser.SetUserMock.defaultExpectation.results
		if mm_results == nil {
			mmSetUser.t.Fatal("No results are set for the UserCacheMock.SetUser")
		}
		return (*mm_results).err
	}
	if mmSetUser.funcSetUser != nil {
		return mmSetUser.funcSetUser(ctx, user)
	}
	mmSetUser.t.Fatalf("Unexpected call to UserCacheMock.SetUser. %v %v", ctx, user)
	return
}

// SetUserAfterCounter returns a count of finished UserCacheMock.SetUser invocations
func (mmSetUser *UserCacheMock) SetUserAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetUser.afterSetUserCounter)
}

// SetUserBeforeCounter returns a count of UserCacheMock.SetUser invocations
func (mmSetUser *UserCacheMock) SetUserBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetUser.beforeSetUserCounter)
}

// Calls returns a list of arguments used in each call to UserCacheMock.SetUser.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetUser *mUserCacheMockSetUser) Calls() []*UserCacheMockSetUserParams {
	mmSetUser.mutex.RLock()

	argCopy := make([]*UserCacheMockSetUserParams, len(mmSetUser.callArgs))
	copy(argCopy, mmSetUser.callArgs)

	mmSetUser.mutex.RUnlock()

	return argCopy
}

// MinimockSetUserDone returns true if the count of the SetUser invocations corresponds
// the number of defined expectations
func (m *UserCacheMock) MinimockSetUserDone() bool {
	if m.SetUserMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetUserMock.invocationsDone()
}

// MinimockSetUserInspect logs each unmet expectation
func (m *UserCacheMock) MinimockSetUserInspect() {
	for _, e := range m.SetUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserCacheMock.SetUser at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetUserCounter := mm_atomic.LoadUint64(&m.afterSetUserCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetUserMock.defaultExpectation != nil && afterSetUserCounter < 1 {
		if m.SetUserMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserCacheMock.SetUser at\n%s", m.SetUserMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserCacheMock.SetUser at\n%s with params: %#v", m.SetUserMock.defaultExpectation.expectationOrigins.origin, *m.SetUserMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetUser != nil && afterSetUserCounter < 1 {
		m.t.Errorf("Expected call to UserCacheMock.SetUser at\n%s", m.funcSetUserOrigin)
	}

	if !m.SetUserMock.invocationsDone() && afterSetUserCounter > 0 {
		m.t.Errorf("Expected %d calls to UserCacheMock.SetUser at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetUserMock.expectedInvocations), m.SetUserMock.expectedInvocationsOrigin, afterSetUserCounter)
	}
}

type mUserCacheMockSetUsers struct {
	optional           bool
	mock               *UserCacheMock
	defaultExpectation *UserCacheMockSetUsersExpectation
	expectations       []*UserCacheMockSetUsersExpectation

	callArgs []*UserCacheMockSetUsersParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserCacheMockSetUsersExpectation specifies expectation struct of the UserCache.SetUsers
type UserCacheMockSetUsersExpectation struct {
	mock               *UserCacheMock
	params             *UserCacheMockSetUsersParams
	paramPtrs          *UserCacheMockSetUsersParamPtrs
	expectationOrigins UserCacheMockSetUsersExpectationOrigins
	results            *UserCacheMockSetUsersResults
	returnOrigin       string
	Counter            uint64
}

// UserCacheMockSetUsersParams contains parameters of the UserCache.SetUsers
type UserCacheMockSetUsersParams struct {
	ctx   context.Context
	users []userentity.User
}

// UserCacheMockSetUsersParamPtrs contains pointers to parameters of the UserCache.SetUsers
type UserCacheMockSetUsersParamPtrs struct {
	ctx   *context.Context
	users *[]userentity.User
}

// UserCacheMockSetUsersResults contains results of the UserCache.SetUsers
type UserCacheMockSetUsersResults struct {
	err error
}

// UserCacheMockSetUsersOrigins contains origins of expectations of the UserCache.SetUsers
type UserCacheMockSetUsersExpectationOrigins struct {
	origin      string
	originCtx   string
	originUsers string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetUsers *mUserCacheMockSetUsers) Optional() *mUserCacheMockSetUsers {
	mmSetUsers.optional = true
	return mmSetUsers
}

// Expect sets up expected params for UserCache.SetUsers
func (mmSetUsers *mUserCacheMockSetUsers) Expect(ctx context.Context, users []userentity.User) *mUserCacheMockSetUsers {
	if mmSetUsers.mock.funcSetUsers != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Set")
	}

	if mmSetUsers.defaultExpectation == nil {
		mmSetUsers.defaultExpectation = &UserCacheMockSetUsersExpectation{}
	}

	if mmSetUsers.defaultExpectation.paramPtrs != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by ExpectParams functions")
	}

	mmSetUsers.defaultExpectation.params = &UserCacheMockSetUsersParams{ctx, users}
	mmSetUsers.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetUsers.expectations {
		if minimock.Equal(e.params, mmSetUsers.defaultExpectation.params) {
			mmSetUsers.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetUsers.defaultExpectation.params)
		}
	}

	return mmSetUsers
}

// ExpectCtxParam1 sets up expected param ctx for UserCache.SetUsers
func (mmSetUsers *mUserCacheMockSetUsers) ExpectCtxParam1(ctx context.Context) *mUserCacheMockSetUsers {
	if mmSetUsers.mock.funcSetUsers != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Set")
	}

	if mmSetUsers.defaultExpectation == nil {
		mmSetUsers.defaultExpectation = &UserCacheMockSetUsersExpectation{}
	}

	if mmSetUsers.defaultExpectation.params != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Expect")
	}

	if mmSetUsers.defaultExpectation.paramPtrs == nil {
		mmSetUsers.defaultExpectation.paramPtrs = &UserCacheMockSetUsersParamPtrs{}
	}
	mmSetUsers.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetUsers.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetUsers
}

// ExpectUsersParam2 sets up expected param users for UserCache.SetUsers
func (mmSetUsers *mUserCacheMockSetUsers) ExpectUsersParam2(users []userentity.User) *mUserCacheMockSetUsers {
	if mmSetUsers.mock.funcSetUsers != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Set")
	}

	if mmSetUsers.defaultExpectation == nil {
		mmSetUsers.defaultExpectation = &UserCacheMockSetUsersExpectation{}
	}

	if mmSetUsers.defaultExpectation.params != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Expect")
	}

	if mmSetUsers.defaultExpectation.paramPtrs == nil {
		mmSetUsers.defaultExpectation.paramPtrs = &UserCacheMockSetUsersParamPtrs{}
	}
	mmSetUsers.defaultExpectation.paramPtrs.users = &users
	mmSetUsers.defaultExpectation.expectationOrigins.originUsers = minimock.CallerInfo(1)

	return mmSetUsers
}

// Inspect accepts an inspector function that has same arguments as the UserCache.SetUsers
func (mmSetUsers *mUserCacheMockSetUsers) Inspect(f func(ctx context.Context, users []userentity.User)) *mUserCacheMockSetUsers {
	if mmSetUsers.mock.inspectFuncSetUsers != nil {
		mmSetUsers.mock.t.Fatalf("Inspect function is already set for UserCacheMock.SetUsers")
	}

	mmSetUsers.mock.inspectFuncSetUsers = f

	return mmSetUsers
}

// Return sets up results that will be returned by UserCache.SetUsers
func (mmSetUsers *mUserCacheMockSetUsers) Return(err error) *UserCacheMock {
	if mmSetUsers.mock.funcSetUsers != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Set")
	}

	if mmSetUsers.defaultExpectation == nil {
		mmSetUsers.defaultExpectation = &UserCacheMockSetUsersExpectation{mock: mmSetUsers.mock}
	}
	mmSetUsers.defaultExpectation.results = &UserCacheMockSetUsersResults{err}
	mmSetUsers.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetUsers.mock
}

// Set uses given function f to mock the UserCache.SetUsers method
func (mmSetUsers *mUserCacheMockSetUsers) Set(f func(ctx context.Context, users []userentity.User) (err error)) *UserCacheMock {
	if mmSetUsers.defaultExpectation != nil {
		mmSetUsers.mock.t.Fatalf("Default expectation is already set for the UserCache.SetUsers method")
	}

	if len(mmSetUsers.expectations) > 0 {
		mmSetUsers.mock.t.Fatalf("Some expectations are already set for the UserCache.SetUsers method")
	}

	mmSetUsers.mock.funcSetUsers = f
	mmSetUsers.mock.funcSetUsersOrigin = minimock.CallerInfo(1)
	return mmSetUsers.mock
}

// When sets expectation for the UserCache.SetUsers which will trigger the result defined by the following
// Then helper
func (mmSetUsers *mUserCacheMockSetUsers) When(ctx context.Context, users []userentity.User) *UserCacheMockSetUsersExpectation {
	if mmSetUsers.mock.funcSetUsers != nil {
		mmSetUsers.mock.t.Fatalf("UserCacheMock.SetUsers mock is already set by Set")
	}

	expectation := &UserCacheMockSetUsersExpectation{
		mock:               mmSetUsers.mock,
		params:             &UserCacheMockSetUsersParams{ctx, users},
		expectationOrigins: UserCacheMockSetUsersExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetUsers.expectations = append(mmSetUsers.expectations, expectation)
	return expectation
}

// Then sets up UserCache.SetUsers return parameters for the expectation previously defined by the When method
func (e *UserCacheMockSetUsersExpectation) Then(err error) *UserCacheMock {
	e.results = &UserCacheMockSetUsersResults{err}
	return e.mock
}

// Times sets number of times UserCache.SetUsers should be invoked
func (mmSetUsers *mUserCacheMockSetUsers) Times(n uint64) *mUserCacheMockSetUsers {
	if n == 0 {
		mmSetUsers.mock.t.Fatalf("Times of UserCacheMock.SetUsers mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetUsers.expectedInvocations, n)
	mmSetUsers.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetUsers
}

func (mmSetUsers *mUserCacheMockSetUsers) invocationsDone() bool {
	if len(mmSetUsers.expectations) == 0 && mmSetUsers.defaultExpectation == nil && mmSetUsers.mock.funcSetUsers == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetUsers.mock.afterSetUsersCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetUsers.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetUsers implements mm_repository.UserCache
func (mmSetUsers *UserCacheMock) SetUsers(ctx context.Context, users []userentity.User) (err error) {
	mm_atomic.AddUint64(&mmSetUsers.beforeSetUsersCounter, 1)
	defer mm_atomic.AddUint64(&mmSetUsers.afterSetUsersCounter, 1)

	mmSetUsers.t.Helper()

	if mmSetUsers.inspectFuncSetUsers != nil {
		mmSetUsers.inspectFuncSetUsers(ctx, users)
	}

	mm_params := UserCacheMockSetUsersParams{ctx, users}

	// Record call args
	mmSetUsers.SetUsersMock.mutex.Lock()
	mmSetUsers.SetUsersMock.callArgs = append(mmSetUsers.SetUsersMock.callArgs, &mm_params)
	mmSetUsers.SetUsersMock.mutex.Unlock()

	for _, e := range mmSetUsers.SetUsersMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetUsers.SetUsersMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetUsers.SetUsersMock.defaultExpectation.Counter, 1)
		mm_want := mmSetUsers.SetUsersMock.defaultExpectation.params
		mm_want_ptrs := mmSetUsers.SetUsersMock.defaultExpectation.paramPtrs

		mm_got := UserCacheMockSetUsersParams{ctx, users}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetUsers.t.Errorf("UserCacheMock.SetUsers got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetUsers.SetUsersMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.users != nil && !minimock.Equal(*mm_want_ptrs.users, mm_got.users) {
				mmSetUsers.t.Errorf("UserCacheMock.SetUsers got unexpected parameter users, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetUsers.SetUsersMock.defaultExpectation.expectationOrigins.originUsers, *mm_want_ptrs.users, mm_got.users, minimock.Diff(*mm_want_ptrs.users, mm_got.users))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetUsers.t.Errorf("UserCacheMock.SetUsers got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetUsers.SetUsersMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetUsers.SetUsersMock.defaultExpectation.results
		if mm_results == nil {
			mmSetUsers.t.Fatal("No results are set for the UserCacheMock.SetUsers")
		}
		return (*mm_results).err
	}
	if mmSetUsers.funcSetUsers != nil {
		return mmSetUsers.funcSetUsers(ctx, users)
	}
	mmSetUsers.t.Fatalf("Unexpected call to UserCacheMock.SetUsers. %v %v", ctx, users)
	return
}

// SetUsersAfterCounter returns a count of finished UserCacheMock.SetUsers invocations
func (mmSetUsers *UserCacheMock) SetUsersAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetUsers.afterSetUsersCounter)
}

// SetUsersBeforeCounter returns a count of UserCacheMock.SetUsers invocations
func (mmSetUsers *UserCacheMock) SetUsersBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetUsers.beforeSetUsersCounter)
}

// Calls returns a list of arguments used in each call to UserCacheMock.SetUsers.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetUsers *mUserCacheMockSetUsers) Calls() []*UserCacheMockSetUsersParams {
	mmSetUsers.mutex.RLock()

	argCopy := make([]*UserCacheMockSetUsersParams, len(mmSetUsers.callArgs))
	copy(argCopy, mmSetUsers.callArgs)

	mmSetUsers.mutex.RUnlock()

	return argCopy
}

// MinimockSetUsersDone returns true if the count of the SetUsers invocations corresponds
// the number of defined expectations
func (m *UserCacheMock) MinimockSetUsersDone() bool {
	if m.SetUsersMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetUsersMock.invocationsDone()
}

// MinimockSetUsersInspect logs each unmet expectation
func (m *UserCacheMock) MinimockSetUsersInspect() {
	for _, e := range m.SetUsersMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserCacheMock.SetUsers at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetUsersCounter := mm_atomic.LoadUint64(&m.afterSetUsersCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetUsersMock.defaultExpectation != nil && afterSetUsersCounter < 1 {
		if m.SetUsersMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserCacheMock.SetUsers at\n%s", m.SetUsersMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserCacheMock.SetUsers at\n%s with params: %#v", m.SetUsersMock.defaultExpectation.expectationOrigins.origin, *m.SetUsersMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetUsers != nil && afterSetUsersCounter < 1 {
		m.t.Errorf("Expected call to UserCacheMock.SetUsers at\n%s", m.funcSetUsersOrigin)
	}

	if !m.SetUsersMock.invocationsDone() && afterSetUsersCounter > 0 {
		m.t.Errorf("Expected %d calls to UserCacheMock.SetUsers at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetUsersMock.expectedInvocations), m.SetUsersMock.expectedInvocationsOrigin, afterSetUsersCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *UserCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockDeleteUserInspect()

			m.MinimockDeleteUsersInspect()

			m.MinimockGetUserInspect()

			m.MinimockGetUsersInspect()

			m.MinimockSetUserInspect()

			m.MinimockSetUsersInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *UserCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *UserCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockDeleteUserDone() &&
		m.MinimockDeleteUsersDone() &&
		m.MinimockGetUserDone() &&
		m.MinimockGetUsersDone() &&
		m.MinimockSetUserDone() &&
		m.MinimockSetUsersDone()
}
