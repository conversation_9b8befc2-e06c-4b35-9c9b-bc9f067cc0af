// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository.UserRolePrimeDB -o user_role_prime_db_mock.go -n UserRolePrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// UserRolePrimeDBMock implements mm_repository.UserRolePrimeDB
type UserRolePrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, userRole userentity.UserRoleCreateData) (u1 userentity.UserRole, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, userRole userentity.UserRoleCreateData)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mUserRolePrimeDBMockCreate

	funcCreateByRoleIDAndUserIDs          func(ctx context.Context, roleID int64, userIDs []int64) (err error)
	funcCreateByRoleIDAndUserIDsOrigin    string
	inspectFuncCreateByRoleIDAndUserIDs   func(ctx context.Context, roleID int64, userIDs []int64)
	afterCreateByRoleIDAndUserIDsCounter  uint64
	beforeCreateByRoleIDAndUserIDsCounter uint64
	CreateByRoleIDAndUserIDsMock          mUserRolePrimeDBMockCreateByRoleIDAndUserIDs

	funcCreateByUserIDAndRoleIDs          func(ctx context.Context, userID int64, roleIDs []int64) (err error)
	funcCreateByUserIDAndRoleIDsOrigin    string
	inspectFuncCreateByUserIDAndRoleIDs   func(ctx context.Context, userID int64, roleIDs []int64)
	afterCreateByUserIDAndRoleIDsCounter  uint64
	beforeCreateByUserIDAndRoleIDsCounter uint64
	CreateByUserIDAndRoleIDsMock          mUserRolePrimeDBMockCreateByUserIDAndRoleIDs

	funcDeleteByRoleID          func(ctx context.Context, roleID int64) (err error)
	funcDeleteByRoleIDOrigin    string
	inspectFuncDeleteByRoleID   func(ctx context.Context, roleID int64)
	afterDeleteByRoleIDCounter  uint64
	beforeDeleteByRoleIDCounter uint64
	DeleteByRoleIDMock          mUserRolePrimeDBMockDeleteByRoleID

	funcDeleteByRoleIDAndUserIDs          func(ctx context.Context, roleID int64, userIDs []int64) (err error)
	funcDeleteByRoleIDAndUserIDsOrigin    string
	inspectFuncDeleteByRoleIDAndUserIDs   func(ctx context.Context, roleID int64, userIDs []int64)
	afterDeleteByRoleIDAndUserIDsCounter  uint64
	beforeDeleteByRoleIDAndUserIDsCounter uint64
	DeleteByRoleIDAndUserIDsMock          mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs

	funcDeleteByUserID          func(userID int64) (err error)
	funcDeleteByUserIDOrigin    string
	inspectFuncDeleteByUserID   func(userID int64)
	afterDeleteByUserIDCounter  uint64
	beforeDeleteByUserIDCounter uint64
	DeleteByUserIDMock          mUserRolePrimeDBMockDeleteByUserID

	funcDeleteByUserIDAndRoleID          func(ctx context.Context, userID int64, roleID int64) (err error)
	funcDeleteByUserIDAndRoleIDOrigin    string
	inspectFuncDeleteByUserIDAndRoleID   func(ctx context.Context, userID int64, roleID int64)
	afterDeleteByUserIDAndRoleIDCounter  uint64
	beforeDeleteByUserIDAndRoleIDCounter uint64
	DeleteByUserIDAndRoleIDMock          mUserRolePrimeDBMockDeleteByUserIDAndRoleID

	funcDeleteByUserIDAndRoleIDs          func(ctx context.Context, userID int64, roleIDs []int64) (err error)
	funcDeleteByUserIDAndRoleIDsOrigin    string
	inspectFuncDeleteByUserIDAndRoleIDs   func(ctx context.Context, userID int64, roleIDs []int64)
	afterDeleteByUserIDAndRoleIDsCounter  uint64
	beforeDeleteByUserIDAndRoleIDsCounter uint64
	DeleteByUserIDAndRoleIDsMock          mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs

	funcGetAll          func() (ua1 []userentity.UserRole, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mUserRolePrimeDBMockGetAll

	funcGetByID          func(id int64) (u1 userentity.UserRole, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mUserRolePrimeDBMockGetByID

	funcGetByUserID          func(userID int64) (ua1 []userentity.UserRole, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mUserRolePrimeDBMockGetByUserID

	funcGetByUserIDAndRoleID          func(userID int64, roleID int64) (u1 userentity.UserRole, err error)
	funcGetByUserIDAndRoleIDOrigin    string
	inspectFuncGetByUserIDAndRoleID   func(userID int64, roleID int64)
	afterGetByUserIDAndRoleIDCounter  uint64
	beforeGetByUserIDAndRoleIDCounter uint64
	GetByUserIDAndRoleIDMock          mUserRolePrimeDBMockGetByUserIDAndRoleID

	funcGetUserRoleIDs          func(userID int64) (ia1 []int64, err error)
	funcGetUserRoleIDsOrigin    string
	inspectFuncGetUserRoleIDs   func(userID int64)
	afterGetUserRoleIDsCounter  uint64
	beforeGetUserRoleIDsCounter uint64
	GetUserRoleIDsMock          mUserRolePrimeDBMockGetUserRoleIDs

	funcGetUserRolesByRoleID          func(roleID int64) (ua1 []userentity.UserRole, err error)
	funcGetUserRolesByRoleIDOrigin    string
	inspectFuncGetUserRolesByRoleID   func(roleID int64)
	afterGetUserRolesByRoleIDCounter  uint64
	beforeGetUserRolesByRoleIDCounter uint64
	GetUserRolesByRoleIDMock          mUserRolePrimeDBMockGetUserRolesByRoleID

	funcUpdate          func(userRole userentity.UserRoleUpdateData) (u1 userentity.UserRole, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(userRole userentity.UserRoleUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mUserRolePrimeDBMockUpdate
}

// NewUserRolePrimeDBMock returns a mock for mm_repository.UserRolePrimeDB
func NewUserRolePrimeDBMock(t minimock.Tester) *UserRolePrimeDBMock {
	m := &UserRolePrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mUserRolePrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*UserRolePrimeDBMockCreateParams{}

	m.CreateByRoleIDAndUserIDsMock = mUserRolePrimeDBMockCreateByRoleIDAndUserIDs{mock: m}
	m.CreateByRoleIDAndUserIDsMock.callArgs = []*UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams{}

	m.CreateByUserIDAndRoleIDsMock = mUserRolePrimeDBMockCreateByUserIDAndRoleIDs{mock: m}
	m.CreateByUserIDAndRoleIDsMock.callArgs = []*UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams{}

	m.DeleteByRoleIDMock = mUserRolePrimeDBMockDeleteByRoleID{mock: m}
	m.DeleteByRoleIDMock.callArgs = []*UserRolePrimeDBMockDeleteByRoleIDParams{}

	m.DeleteByRoleIDAndUserIDsMock = mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs{mock: m}
	m.DeleteByRoleIDAndUserIDsMock.callArgs = []*UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams{}

	m.DeleteByUserIDMock = mUserRolePrimeDBMockDeleteByUserID{mock: m}
	m.DeleteByUserIDMock.callArgs = []*UserRolePrimeDBMockDeleteByUserIDParams{}

	m.DeleteByUserIDAndRoleIDMock = mUserRolePrimeDBMockDeleteByUserIDAndRoleID{mock: m}
	m.DeleteByUserIDAndRoleIDMock.callArgs = []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams{}

	m.DeleteByUserIDAndRoleIDsMock = mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs{mock: m}
	m.DeleteByUserIDAndRoleIDsMock.callArgs = []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams{}

	m.GetAllMock = mUserRolePrimeDBMockGetAll{mock: m}

	m.GetByIDMock = mUserRolePrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*UserRolePrimeDBMockGetByIDParams{}

	m.GetByUserIDMock = mUserRolePrimeDBMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*UserRolePrimeDBMockGetByUserIDParams{}

	m.GetByUserIDAndRoleIDMock = mUserRolePrimeDBMockGetByUserIDAndRoleID{mock: m}
	m.GetByUserIDAndRoleIDMock.callArgs = []*UserRolePrimeDBMockGetByUserIDAndRoleIDParams{}

	m.GetUserRoleIDsMock = mUserRolePrimeDBMockGetUserRoleIDs{mock: m}
	m.GetUserRoleIDsMock.callArgs = []*UserRolePrimeDBMockGetUserRoleIDsParams{}

	m.GetUserRolesByRoleIDMock = mUserRolePrimeDBMockGetUserRolesByRoleID{mock: m}
	m.GetUserRolesByRoleIDMock.callArgs = []*UserRolePrimeDBMockGetUserRolesByRoleIDParams{}

	m.UpdateMock = mUserRolePrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*UserRolePrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mUserRolePrimeDBMockCreate struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockCreateExpectation
	expectations       []*UserRolePrimeDBMockCreateExpectation

	callArgs []*UserRolePrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockCreateExpectation specifies expectation struct of the UserRolePrimeDB.Create
type UserRolePrimeDBMockCreateExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockCreateParams
	paramPtrs          *UserRolePrimeDBMockCreateParamPtrs
	expectationOrigins UserRolePrimeDBMockCreateExpectationOrigins
	results            *UserRolePrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockCreateParams contains parameters of the UserRolePrimeDB.Create
type UserRolePrimeDBMockCreateParams struct {
	ctx      context.Context
	userRole userentity.UserRoleCreateData
}

// UserRolePrimeDBMockCreateParamPtrs contains pointers to parameters of the UserRolePrimeDB.Create
type UserRolePrimeDBMockCreateParamPtrs struct {
	ctx      *context.Context
	userRole *userentity.UserRoleCreateData
}

// UserRolePrimeDBMockCreateResults contains results of the UserRolePrimeDB.Create
type UserRolePrimeDBMockCreateResults struct {
	u1  userentity.UserRole
	err error
}

// UserRolePrimeDBMockCreateOrigins contains origins of expectations of the UserRolePrimeDB.Create
type UserRolePrimeDBMockCreateExpectationOrigins struct {
	origin         string
	originCtx      string
	originUserRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mUserRolePrimeDBMockCreate) Optional() *mUserRolePrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for UserRolePrimeDB.Create
func (mmCreate *mUserRolePrimeDBMockCreate) Expect(ctx context.Context, userRole userentity.UserRoleCreateData) *mUserRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &UserRolePrimeDBMockCreateParams{ctx, userRole}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.Create
func (mmCreate *mUserRolePrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectUserRoleParam2 sets up expected param userRole for UserRolePrimeDB.Create
func (mmCreate *mUserRolePrimeDBMockCreate) ExpectUserRoleParam2(userRole userentity.UserRoleCreateData) *mUserRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.userRole = &userRole
	mmCreate.defaultExpectation.expectationOrigins.originUserRole = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.Create
func (mmCreate *mUserRolePrimeDBMockCreate) Inspect(f func(ctx context.Context, userRole userentity.UserRoleCreateData)) *mUserRolePrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by UserRolePrimeDB.Create
func (mmCreate *mUserRolePrimeDBMockCreate) Return(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserRolePrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &UserRolePrimeDBMockCreateResults{u1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the UserRolePrimeDB.Create method
func (mmCreate *mUserRolePrimeDBMockCreate) Set(f func(ctx context.Context, userRole userentity.UserRoleCreateData) (u1 userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the UserRolePrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mUserRolePrimeDBMockCreate) When(ctx context.Context, userRole userentity.UserRoleCreateData) *UserRolePrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserRolePrimeDBMock.Create mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &UserRolePrimeDBMockCreateParams{ctx, userRole},
		expectationOrigins: UserRolePrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockCreateExpectation) Then(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockCreateResults{u1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.Create should be invoked
func (mmCreate *mUserRolePrimeDBMockCreate) Times(n uint64) *mUserRolePrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of UserRolePrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mUserRolePrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.UserRolePrimeDB
func (mmCreate *UserRolePrimeDBMock) Create(ctx context.Context, userRole userentity.UserRoleCreateData) (u1 userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, userRole)
	}

	mm_params := UserRolePrimeDBMockCreateParams{ctx, userRole}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockCreateParams{ctx, userRole}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("UserRolePrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userRole != nil && !minimock.Equal(*mm_want_ptrs.userRole, mm_got.userRole) {
				mmCreate.t.Errorf("UserRolePrimeDBMock.Create got unexpected parameter userRole, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originUserRole, *mm_want_ptrs.userRole, mm_got.userRole, minimock.Diff(*mm_want_ptrs.userRole, mm_got.userRole))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("UserRolePrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the UserRolePrimeDBMock.Create")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, userRole)
	}
	mmCreate.t.Fatalf("Unexpected call to UserRolePrimeDBMock.Create. %v %v", ctx, userRole)
	return
}

// CreateAfterCounter returns a count of finished UserRolePrimeDBMock.Create invocations
func (mmCreate *UserRolePrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of UserRolePrimeDBMock.Create invocations
func (mmCreate *UserRolePrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mUserRolePrimeDBMockCreate) Calls() []*UserRolePrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mUserRolePrimeDBMockCreateByRoleIDAndUserIDs struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation
	expectations       []*UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation

	callArgs []*UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation specifies expectation struct of the UserRolePrimeDB.CreateByRoleIDAndUserIDs
type UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams
	paramPtrs          *UserRolePrimeDBMockCreateByRoleIDAndUserIDsParamPtrs
	expectationOrigins UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectationOrigins
	results            *UserRolePrimeDBMockCreateByRoleIDAndUserIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams contains parameters of the UserRolePrimeDB.CreateByRoleIDAndUserIDs
type UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams struct {
	ctx     context.Context
	roleID  int64
	userIDs []int64
}

// UserRolePrimeDBMockCreateByRoleIDAndUserIDsParamPtrs contains pointers to parameters of the UserRolePrimeDB.CreateByRoleIDAndUserIDs
type UserRolePrimeDBMockCreateByRoleIDAndUserIDsParamPtrs struct {
	ctx     *context.Context
	roleID  *int64
	userIDs *[]int64
}

// UserRolePrimeDBMockCreateByRoleIDAndUserIDsResults contains results of the UserRolePrimeDB.CreateByRoleIDAndUserIDs
type UserRolePrimeDBMockCreateByRoleIDAndUserIDsResults struct {
	err error
}

// UserRolePrimeDBMockCreateByRoleIDAndUserIDsOrigins contains origins of expectations of the UserRolePrimeDB.CreateByRoleIDAndUserIDs
type UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originRoleID  string
	originUserIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Optional() *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	mmCreateByRoleIDAndUserIDs.optional = true
	return mmCreateByRoleIDAndUserIDs
}

// Expect sets up expected params for UserRolePrimeDB.CreateByRoleIDAndUserIDs
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Expect(ctx context.Context, roleID int64, userIDs []int64) *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	if mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation{}
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by ExpectParams functions")
	}

	mmCreateByRoleIDAndUserIDs.defaultExpectation.params = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams{ctx, roleID, userIDs}
	mmCreateByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByRoleIDAndUserIDs.expectations {
		if minimock.Equal(e.params, mmCreateByRoleIDAndUserIDs.defaultExpectation.params) {
			mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByRoleIDAndUserIDs.defaultExpectation.params)
		}
	}

	return mmCreateByRoleIDAndUserIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.CreateByRoleIDAndUserIDs
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	if mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation{}
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsParamPtrs{}
	}
	mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndUserIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for UserRolePrimeDB.CreateByRoleIDAndUserIDs
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) ExpectRoleIDParam2(roleID int64) *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	if mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation{}
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsParamPtrs{}
	}
	mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmCreateByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndUserIDs
}

// ExpectUserIDsParam3 sets up expected param userIDs for UserRolePrimeDB.CreateByRoleIDAndUserIDs
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) ExpectUserIDsParam3(userIDs []int64) *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	if mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation{}
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsParamPtrs{}
	}
	mmCreateByRoleIDAndUserIDs.defaultExpectation.paramPtrs.userIDs = &userIDs
	mmCreateByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.originUserIDs = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndUserIDs
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.CreateByRoleIDAndUserIDs
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Inspect(f func(ctx context.Context, roleID int64, userIDs []int64)) *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	if mmCreateByRoleIDAndUserIDs.mock.inspectFuncCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.CreateByRoleIDAndUserIDs")
	}

	mmCreateByRoleIDAndUserIDs.mock.inspectFuncCreateByRoleIDAndUserIDs = f

	return mmCreateByRoleIDAndUserIDs
}

// Return sets up results that will be returned by UserRolePrimeDB.CreateByRoleIDAndUserIDs
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Return(err error) *UserRolePrimeDBMock {
	if mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndUserIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation{mock: mmCreateByRoleIDAndUserIDs.mock}
	}
	mmCreateByRoleIDAndUserIDs.defaultExpectation.results = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsResults{err}
	mmCreateByRoleIDAndUserIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndUserIDs.mock
}

// Set uses given function f to mock the UserRolePrimeDB.CreateByRoleIDAndUserIDs method
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Set(f func(ctx context.Context, roleID int64, userIDs []int64) (err error)) *UserRolePrimeDBMock {
	if mmCreateByRoleIDAndUserIDs.defaultExpectation != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.CreateByRoleIDAndUserIDs method")
	}

	if len(mmCreateByRoleIDAndUserIDs.expectations) > 0 {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.CreateByRoleIDAndUserIDs method")
	}

	mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs = f
	mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndUserIDs.mock
}

// When sets expectation for the UserRolePrimeDB.CreateByRoleIDAndUserIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) When(ctx context.Context, roleID int64, userIDs []int64) *UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation {
	if mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation{
		mock:               mmCreateByRoleIDAndUserIDs.mock,
		params:             &UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams{ctx, roleID, userIDs},
		expectationOrigins: UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByRoleIDAndUserIDs.expectations = append(mmCreateByRoleIDAndUserIDs.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.CreateByRoleIDAndUserIDs return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockCreateByRoleIDAndUserIDsExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockCreateByRoleIDAndUserIDsResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.CreateByRoleIDAndUserIDs should be invoked
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Times(n uint64) *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs {
	if n == 0 {
		mmCreateByRoleIDAndUserIDs.mock.t.Fatalf("Times of UserRolePrimeDBMock.CreateByRoleIDAndUserIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByRoleIDAndUserIDs.expectedInvocations, n)
	mmCreateByRoleIDAndUserIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndUserIDs
}

func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) invocationsDone() bool {
	if len(mmCreateByRoleIDAndUserIDs.expectations) == 0 && mmCreateByRoleIDAndUserIDs.defaultExpectation == nil && mmCreateByRoleIDAndUserIDs.mock.funcCreateByRoleIDAndUserIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndUserIDs.mock.afterCreateByRoleIDAndUserIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndUserIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByRoleIDAndUserIDs implements mm_repository.UserRolePrimeDB
func (mmCreateByRoleIDAndUserIDs *UserRolePrimeDBMock) CreateByRoleIDAndUserIDs(ctx context.Context, roleID int64, userIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByRoleIDAndUserIDs.beforeCreateByRoleIDAndUserIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByRoleIDAndUserIDs.afterCreateByRoleIDAndUserIDsCounter, 1)

	mmCreateByRoleIDAndUserIDs.t.Helper()

	if mmCreateByRoleIDAndUserIDs.inspectFuncCreateByRoleIDAndUserIDs != nil {
		mmCreateByRoleIDAndUserIDs.inspectFuncCreateByRoleIDAndUserIDs(ctx, roleID, userIDs)
	}

	mm_params := UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams{ctx, roleID, userIDs}

	// Record call args
	mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.mutex.Lock()
	mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.callArgs = append(mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.callArgs, &mm_params)
	mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.mutex.Unlock()

	for _, e := range mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams{ctx, roleID, userIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmCreateByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.userIDs != nil && !minimock.Equal(*mm_want_ptrs.userIDs, mm_got.userIDs) {
				mmCreateByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs got unexpected parameter userIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.originUserIDs, *mm_want_ptrs.userIDs, mm_got.userIDs, minimock.Diff(*mm_want_ptrs.userIDs, mm_got.userIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.CreateByRoleIDAndUserIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByRoleIDAndUserIDs.CreateByRoleIDAndUserIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByRoleIDAndUserIDs.t.Fatal("No results are set for the UserRolePrimeDBMock.CreateByRoleIDAndUserIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByRoleIDAndUserIDs.funcCreateByRoleIDAndUserIDs != nil {
		return mmCreateByRoleIDAndUserIDs.funcCreateByRoleIDAndUserIDs(ctx, roleID, userIDs)
	}
	mmCreateByRoleIDAndUserIDs.t.Fatalf("Unexpected call to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs. %v %v %v", ctx, roleID, userIDs)
	return
}

// CreateByRoleIDAndUserIDsAfterCounter returns a count of finished UserRolePrimeDBMock.CreateByRoleIDAndUserIDs invocations
func (mmCreateByRoleIDAndUserIDs *UserRolePrimeDBMock) CreateByRoleIDAndUserIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndUserIDs.afterCreateByRoleIDAndUserIDsCounter)
}

// CreateByRoleIDAndUserIDsBeforeCounter returns a count of UserRolePrimeDBMock.CreateByRoleIDAndUserIDs invocations
func (mmCreateByRoleIDAndUserIDs *UserRolePrimeDBMock) CreateByRoleIDAndUserIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndUserIDs.beforeCreateByRoleIDAndUserIDsCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByRoleIDAndUserIDs *mUserRolePrimeDBMockCreateByRoleIDAndUserIDs) Calls() []*UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams {
	mmCreateByRoleIDAndUserIDs.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockCreateByRoleIDAndUserIDsParams, len(mmCreateByRoleIDAndUserIDs.callArgs))
	copy(argCopy, mmCreateByRoleIDAndUserIDs.callArgs)

	mmCreateByRoleIDAndUserIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByRoleIDAndUserIDsDone returns true if the count of the CreateByRoleIDAndUserIDs invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockCreateByRoleIDAndUserIDsDone() bool {
	if m.CreateByRoleIDAndUserIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByRoleIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByRoleIDAndUserIDsMock.invocationsDone()
}

// MinimockCreateByRoleIDAndUserIDsInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockCreateByRoleIDAndUserIDsInspect() {
	for _, e := range m.CreateByRoleIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByRoleIDAndUserIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByRoleIDAndUserIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByRoleIDAndUserIDsMock.defaultExpectation != nil && afterCreateByRoleIDAndUserIDsCounter < 1 {
		if m.CreateByRoleIDAndUserIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs at\n%s", m.CreateByRoleIDAndUserIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs at\n%s with params: %#v", m.CreateByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByRoleIDAndUserIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByRoleIDAndUserIDs != nil && afterCreateByRoleIDAndUserIDsCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs at\n%s", m.funcCreateByRoleIDAndUserIDsOrigin)
	}

	if !m.CreateByRoleIDAndUserIDsMock.invocationsDone() && afterCreateByRoleIDAndUserIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.CreateByRoleIDAndUserIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByRoleIDAndUserIDsMock.expectedInvocations), m.CreateByRoleIDAndUserIDsMock.expectedInvocationsOrigin, afterCreateByRoleIDAndUserIDsCounter)
	}
}

type mUserRolePrimeDBMockCreateByUserIDAndRoleIDs struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation
	expectations       []*UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation

	callArgs []*UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation specifies expectation struct of the UserRolePrimeDB.CreateByUserIDAndRoleIDs
type UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams
	paramPtrs          *UserRolePrimeDBMockCreateByUserIDAndRoleIDsParamPtrs
	expectationOrigins UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectationOrigins
	results            *UserRolePrimeDBMockCreateByUserIDAndRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams contains parameters of the UserRolePrimeDB.CreateByUserIDAndRoleIDs
type UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams struct {
	ctx     context.Context
	userID  int64
	roleIDs []int64
}

// UserRolePrimeDBMockCreateByUserIDAndRoleIDsParamPtrs contains pointers to parameters of the UserRolePrimeDB.CreateByUserIDAndRoleIDs
type UserRolePrimeDBMockCreateByUserIDAndRoleIDsParamPtrs struct {
	ctx     *context.Context
	userID  *int64
	roleIDs *[]int64
}

// UserRolePrimeDBMockCreateByUserIDAndRoleIDsResults contains results of the UserRolePrimeDB.CreateByUserIDAndRoleIDs
type UserRolePrimeDBMockCreateByUserIDAndRoleIDsResults struct {
	err error
}

// UserRolePrimeDBMockCreateByUserIDAndRoleIDsOrigins contains origins of expectations of the UserRolePrimeDB.CreateByUserIDAndRoleIDs
type UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originUserID  string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Optional() *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	mmCreateByUserIDAndRoleIDs.optional = true
	return mmCreateByUserIDAndRoleIDs
}

// Expect sets up expected params for UserRolePrimeDB.CreateByUserIDAndRoleIDs
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Expect(ctx context.Context, userID int64, roleIDs []int64) *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	if mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation{}
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by ExpectParams functions")
	}

	mmCreateByUserIDAndRoleIDs.defaultExpectation.params = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams{ctx, userID, roleIDs}
	mmCreateByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByUserIDAndRoleIDs.expectations {
		if minimock.Equal(e.params, mmCreateByUserIDAndRoleIDs.defaultExpectation.params) {
			mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByUserIDAndRoleIDs.defaultExpectation.params)
		}
	}

	return mmCreateByUserIDAndRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.CreateByUserIDAndRoleIDs
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	if mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation{}
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsParamPtrs{}
	}
	mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByUserIDAndRoleIDs
}

// ExpectUserIDParam2 sets up expected param userID for UserRolePrimeDB.CreateByUserIDAndRoleIDs
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) ExpectUserIDParam2(userID int64) *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	if mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation{}
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsParamPtrs{}
	}
	mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmCreateByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmCreateByUserIDAndRoleIDs
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for UserRolePrimeDB.CreateByUserIDAndRoleIDs
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) ExpectRoleIDsParam3(roleIDs []int64) *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	if mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation{}
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsParamPtrs{}
	}
	mmCreateByUserIDAndRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmCreateByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmCreateByUserIDAndRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.CreateByUserIDAndRoleIDs
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Inspect(f func(ctx context.Context, userID int64, roleIDs []int64)) *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	if mmCreateByUserIDAndRoleIDs.mock.inspectFuncCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.CreateByUserIDAndRoleIDs")
	}

	mmCreateByUserIDAndRoleIDs.mock.inspectFuncCreateByUserIDAndRoleIDs = f

	return mmCreateByUserIDAndRoleIDs
}

// Return sets up results that will be returned by UserRolePrimeDB.CreateByUserIDAndRoleIDs
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Return(err error) *UserRolePrimeDBMock {
	if mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmCreateByUserIDAndRoleIDs.defaultExpectation == nil {
		mmCreateByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation{mock: mmCreateByUserIDAndRoleIDs.mock}
	}
	mmCreateByUserIDAndRoleIDs.defaultExpectation.results = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsResults{err}
	mmCreateByUserIDAndRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByUserIDAndRoleIDs.mock
}

// Set uses given function f to mock the UserRolePrimeDB.CreateByUserIDAndRoleIDs method
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Set(f func(ctx context.Context, userID int64, roleIDs []int64) (err error)) *UserRolePrimeDBMock {
	if mmCreateByUserIDAndRoleIDs.defaultExpectation != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.CreateByUserIDAndRoleIDs method")
	}

	if len(mmCreateByUserIDAndRoleIDs.expectations) > 0 {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.CreateByUserIDAndRoleIDs method")
	}

	mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs = f
	mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByUserIDAndRoleIDs.mock
}

// When sets expectation for the UserRolePrimeDB.CreateByUserIDAndRoleIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) When(ctx context.Context, userID int64, roleIDs []int64) *UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation {
	if mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation{
		mock:               mmCreateByUserIDAndRoleIDs.mock,
		params:             &UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams{ctx, userID, roleIDs},
		expectationOrigins: UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByUserIDAndRoleIDs.expectations = append(mmCreateByUserIDAndRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.CreateByUserIDAndRoleIDs return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockCreateByUserIDAndRoleIDsExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockCreateByUserIDAndRoleIDsResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.CreateByUserIDAndRoleIDs should be invoked
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Times(n uint64) *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs {
	if n == 0 {
		mmCreateByUserIDAndRoleIDs.mock.t.Fatalf("Times of UserRolePrimeDBMock.CreateByUserIDAndRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByUserIDAndRoleIDs.expectedInvocations, n)
	mmCreateByUserIDAndRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByUserIDAndRoleIDs
}

func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) invocationsDone() bool {
	if len(mmCreateByUserIDAndRoleIDs.expectations) == 0 && mmCreateByUserIDAndRoleIDs.defaultExpectation == nil && mmCreateByUserIDAndRoleIDs.mock.funcCreateByUserIDAndRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByUserIDAndRoleIDs.mock.afterCreateByUserIDAndRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByUserIDAndRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByUserIDAndRoleIDs implements mm_repository.UserRolePrimeDB
func (mmCreateByUserIDAndRoleIDs *UserRolePrimeDBMock) CreateByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByUserIDAndRoleIDs.beforeCreateByUserIDAndRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByUserIDAndRoleIDs.afterCreateByUserIDAndRoleIDsCounter, 1)

	mmCreateByUserIDAndRoleIDs.t.Helper()

	if mmCreateByUserIDAndRoleIDs.inspectFuncCreateByUserIDAndRoleIDs != nil {
		mmCreateByUserIDAndRoleIDs.inspectFuncCreateByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}

	mm_params := UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

	// Record call args
	mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.mutex.Lock()
	mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.callArgs = append(mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.callArgs, &mm_params)
	mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.mutex.Unlock()

	for _, e := range mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmCreateByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmCreateByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.CreateByUserIDAndRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByUserIDAndRoleIDs.CreateByUserIDAndRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByUserIDAndRoleIDs.t.Fatal("No results are set for the UserRolePrimeDBMock.CreateByUserIDAndRoleIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByUserIDAndRoleIDs.funcCreateByUserIDAndRoleIDs != nil {
		return mmCreateByUserIDAndRoleIDs.funcCreateByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}
	mmCreateByUserIDAndRoleIDs.t.Fatalf("Unexpected call to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs. %v %v %v", ctx, userID, roleIDs)
	return
}

// CreateByUserIDAndRoleIDsAfterCounter returns a count of finished UserRolePrimeDBMock.CreateByUserIDAndRoleIDs invocations
func (mmCreateByUserIDAndRoleIDs *UserRolePrimeDBMock) CreateByUserIDAndRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByUserIDAndRoleIDs.afterCreateByUserIDAndRoleIDsCounter)
}

// CreateByUserIDAndRoleIDsBeforeCounter returns a count of UserRolePrimeDBMock.CreateByUserIDAndRoleIDs invocations
func (mmCreateByUserIDAndRoleIDs *UserRolePrimeDBMock) CreateByUserIDAndRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByUserIDAndRoleIDs.beforeCreateByUserIDAndRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByUserIDAndRoleIDs *mUserRolePrimeDBMockCreateByUserIDAndRoleIDs) Calls() []*UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams {
	mmCreateByUserIDAndRoleIDs.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockCreateByUserIDAndRoleIDsParams, len(mmCreateByUserIDAndRoleIDs.callArgs))
	copy(argCopy, mmCreateByUserIDAndRoleIDs.callArgs)

	mmCreateByUserIDAndRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByUserIDAndRoleIDsDone returns true if the count of the CreateByUserIDAndRoleIDs invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockCreateByUserIDAndRoleIDsDone() bool {
	if m.CreateByUserIDAndRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByUserIDAndRoleIDsMock.invocationsDone()
}

// MinimockCreateByUserIDAndRoleIDsInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockCreateByUserIDAndRoleIDsInspect() {
	for _, e := range m.CreateByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByUserIDAndRoleIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByUserIDAndRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByUserIDAndRoleIDsMock.defaultExpectation != nil && afterCreateByUserIDAndRoleIDsCounter < 1 {
		if m.CreateByUserIDAndRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs at\n%s", m.CreateByUserIDAndRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs at\n%s with params: %#v", m.CreateByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByUserIDAndRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByUserIDAndRoleIDs != nil && afterCreateByUserIDAndRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs at\n%s", m.funcCreateByUserIDAndRoleIDsOrigin)
	}

	if !m.CreateByUserIDAndRoleIDsMock.invocationsDone() && afterCreateByUserIDAndRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.CreateByUserIDAndRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByUserIDAndRoleIDsMock.expectedInvocations), m.CreateByUserIDAndRoleIDsMock.expectedInvocationsOrigin, afterCreateByUserIDAndRoleIDsCounter)
	}
}

type mUserRolePrimeDBMockDeleteByRoleID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockDeleteByRoleIDExpectation
	expectations       []*UserRolePrimeDBMockDeleteByRoleIDExpectation

	callArgs []*UserRolePrimeDBMockDeleteByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockDeleteByRoleIDExpectation specifies expectation struct of the UserRolePrimeDB.DeleteByRoleID
type UserRolePrimeDBMockDeleteByRoleIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockDeleteByRoleIDParams
	paramPtrs          *UserRolePrimeDBMockDeleteByRoleIDParamPtrs
	expectationOrigins UserRolePrimeDBMockDeleteByRoleIDExpectationOrigins
	results            *UserRolePrimeDBMockDeleteByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockDeleteByRoleIDParams contains parameters of the UserRolePrimeDB.DeleteByRoleID
type UserRolePrimeDBMockDeleteByRoleIDParams struct {
	ctx    context.Context
	roleID int64
}

// UserRolePrimeDBMockDeleteByRoleIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.DeleteByRoleID
type UserRolePrimeDBMockDeleteByRoleIDParamPtrs struct {
	ctx    *context.Context
	roleID *int64
}

// UserRolePrimeDBMockDeleteByRoleIDResults contains results of the UserRolePrimeDB.DeleteByRoleID
type UserRolePrimeDBMockDeleteByRoleIDResults struct {
	err error
}

// UserRolePrimeDBMockDeleteByRoleIDOrigins contains origins of expectations of the UserRolePrimeDB.DeleteByRoleID
type UserRolePrimeDBMockDeleteByRoleIDExpectationOrigins struct {
	origin       string
	originCtx    string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Optional() *mUserRolePrimeDBMockDeleteByRoleID {
	mmDeleteByRoleID.optional = true
	return mmDeleteByRoleID
}

// Expect sets up expected params for UserRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Expect(ctx context.Context, roleID int64) *mUserRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleID.defaultExpectation.params = &UserRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID}
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleID.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleID.defaultExpectation.params) {
			mmDeleteByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleID.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleID
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.params != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Expect")
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByRoleIDParamPtrs{}
	}
	mmDeleteByRoleID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleID
}

// ExpectRoleIDParam2 sets up expected param roleID for UserRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) ExpectRoleIDParam2(roleID int64) *mUserRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.params != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Expect")
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByRoleIDParamPtrs{}
	}
	mmDeleteByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Inspect(f func(ctx context.Context, roleID int64)) *mUserRolePrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.inspectFuncDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.DeleteByRoleID")
	}

	mmDeleteByRoleID.mock.inspectFuncDeleteByRoleID = f

	return mmDeleteByRoleID
}

// Return sets up results that will be returned by UserRolePrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Return(err error) *UserRolePrimeDBMock {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDExpectation{mock: mmDeleteByRoleID.mock}
	}
	mmDeleteByRoleID.defaultExpectation.results = &UserRolePrimeDBMockDeleteByRoleIDResults{err}
	mmDeleteByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.DeleteByRoleID method
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Set(f func(ctx context.Context, roleID int64) (err error)) *UserRolePrimeDBMock {
	if mmDeleteByRoleID.defaultExpectation != nil {
		mmDeleteByRoleID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.DeleteByRoleID method")
	}

	if len(mmDeleteByRoleID.expectations) > 0 {
		mmDeleteByRoleID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.DeleteByRoleID method")
	}

	mmDeleteByRoleID.mock.funcDeleteByRoleID = f
	mmDeleteByRoleID.mock.funcDeleteByRoleIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID.mock
}

// When sets expectation for the UserRolePrimeDB.DeleteByRoleID which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) When(ctx context.Context, roleID int64) *UserRolePrimeDBMockDeleteByRoleIDExpectation {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockDeleteByRoleIDExpectation{
		mock:               mmDeleteByRoleID.mock,
		params:             &UserRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID},
		expectationOrigins: UserRolePrimeDBMockDeleteByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleID.expectations = append(mmDeleteByRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.DeleteByRoleID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockDeleteByRoleIDExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockDeleteByRoleIDResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.DeleteByRoleID should be invoked
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Times(n uint64) *mUserRolePrimeDBMockDeleteByRoleID {
	if n == 0 {
		mmDeleteByRoleID.mock.t.Fatalf("Times of UserRolePrimeDBMock.DeleteByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleID.expectedInvocations, n)
	mmDeleteByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID
}

func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) invocationsDone() bool {
	if len(mmDeleteByRoleID.expectations) == 0 && mmDeleteByRoleID.defaultExpectation == nil && mmDeleteByRoleID.mock.funcDeleteByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleID.mock.afterDeleteByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleID implements mm_repository.UserRolePrimeDB
func (mmDeleteByRoleID *UserRolePrimeDBMock) DeleteByRoleID(ctx context.Context, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleID.beforeDeleteByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleID.afterDeleteByRoleIDCounter, 1)

	mmDeleteByRoleID.t.Helper()

	if mmDeleteByRoleID.inspectFuncDeleteByRoleID != nil {
		mmDeleteByRoleID.inspectFuncDeleteByRoleID(ctx, roleID)
	}

	mm_params := UserRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID}

	// Record call args
	mmDeleteByRoleID.DeleteByRoleIDMock.mutex.Lock()
	mmDeleteByRoleID.DeleteByRoleIDMock.callArgs = append(mmDeleteByRoleID.DeleteByRoleIDMock.callArgs, &mm_params)
	mmDeleteByRoleID.DeleteByRoleIDMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleID.DeleteByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockDeleteByRoleIDParams{ctx, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByRoleID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleID.t.Fatal("No results are set for the UserRolePrimeDBMock.DeleteByRoleID")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleID.funcDeleteByRoleID != nil {
		return mmDeleteByRoleID.funcDeleteByRoleID(ctx, roleID)
	}
	mmDeleteByRoleID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.DeleteByRoleID. %v %v", ctx, roleID)
	return
}

// DeleteByRoleIDAfterCounter returns a count of finished UserRolePrimeDBMock.DeleteByRoleID invocations
func (mmDeleteByRoleID *UserRolePrimeDBMock) DeleteByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleID.afterDeleteByRoleIDCounter)
}

// DeleteByRoleIDBeforeCounter returns a count of UserRolePrimeDBMock.DeleteByRoleID invocations
func (mmDeleteByRoleID *UserRolePrimeDBMock) DeleteByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleID.beforeDeleteByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.DeleteByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleID *mUserRolePrimeDBMockDeleteByRoleID) Calls() []*UserRolePrimeDBMockDeleteByRoleIDParams {
	mmDeleteByRoleID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockDeleteByRoleIDParams, len(mmDeleteByRoleID.callArgs))
	copy(argCopy, mmDeleteByRoleID.callArgs)

	mmDeleteByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDDone returns true if the count of the DeleteByRoleID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockDeleteByRoleIDDone() bool {
	if m.DeleteByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDMock.invocationsDone()
}

// MinimockDeleteByRoleIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockDeleteByRoleIDInspect() {
	for _, e := range m.DeleteByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDMock.defaultExpectation != nil && afterDeleteByRoleIDCounter < 1 {
		if m.DeleteByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleID at\n%s", m.DeleteByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleID at\n%s with params: %#v", m.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleID != nil && afterDeleteByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleID at\n%s", m.funcDeleteByRoleIDOrigin)
	}

	if !m.DeleteByRoleIDMock.invocationsDone() && afterDeleteByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.DeleteByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDMock.expectedInvocations), m.DeleteByRoleIDMock.expectedInvocationsOrigin, afterDeleteByRoleIDCounter)
	}
}

type mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation
	expectations       []*UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation

	callArgs []*UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation specifies expectation struct of the UserRolePrimeDB.DeleteByRoleIDAndUserIDs
type UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams
	paramPtrs          *UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParamPtrs
	expectationOrigins UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectationOrigins
	results            *UserRolePrimeDBMockDeleteByRoleIDAndUserIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams contains parameters of the UserRolePrimeDB.DeleteByRoleIDAndUserIDs
type UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams struct {
	ctx     context.Context
	roleID  int64
	userIDs []int64
}

// UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParamPtrs contains pointers to parameters of the UserRolePrimeDB.DeleteByRoleIDAndUserIDs
type UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParamPtrs struct {
	ctx     *context.Context
	roleID  *int64
	userIDs *[]int64
}

// UserRolePrimeDBMockDeleteByRoleIDAndUserIDsResults contains results of the UserRolePrimeDB.DeleteByRoleIDAndUserIDs
type UserRolePrimeDBMockDeleteByRoleIDAndUserIDsResults struct {
	err error
}

// UserRolePrimeDBMockDeleteByRoleIDAndUserIDsOrigins contains origins of expectations of the UserRolePrimeDB.DeleteByRoleIDAndUserIDs
type UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originRoleID  string
	originUserIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Optional() *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	mmDeleteByRoleIDAndUserIDs.optional = true
	return mmDeleteByRoleIDAndUserIDs
}

// Expect sets up expected params for UserRolePrimeDB.DeleteByRoleIDAndUserIDs
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Expect(ctx context.Context, roleID int64, userIDs []int64) *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	if mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation{}
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleIDAndUserIDs.defaultExpectation.params = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams{ctx, roleID, userIDs}
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleIDAndUserIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleIDAndUserIDs.defaultExpectation.params) {
			mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleIDAndUserIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleIDAndUserIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.DeleteByRoleIDAndUserIDs
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	if mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation{}
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndUserIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for UserRolePrimeDB.DeleteByRoleIDAndUserIDs
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) ExpectRoleIDParam2(roleID int64) *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	if mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation{}
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndUserIDs
}

// ExpectUserIDsParam3 sets up expected param userIDs for UserRolePrimeDB.DeleteByRoleIDAndUserIDs
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) ExpectUserIDsParam3(userIDs []int64) *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	if mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation{}
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.paramPtrs.userIDs = &userIDs
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.expectationOrigins.originUserIDs = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndUserIDs
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.DeleteByRoleIDAndUserIDs
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Inspect(f func(ctx context.Context, roleID int64, userIDs []int64)) *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	if mmDeleteByRoleIDAndUserIDs.mock.inspectFuncDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs")
	}

	mmDeleteByRoleIDAndUserIDs.mock.inspectFuncDeleteByRoleIDAndUserIDs = f

	return mmDeleteByRoleIDAndUserIDs
}

// Return sets up results that will be returned by UserRolePrimeDB.DeleteByRoleIDAndUserIDs
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Return(err error) *UserRolePrimeDBMock {
	if mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndUserIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation{mock: mmDeleteByRoleIDAndUserIDs.mock}
	}
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.results = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsResults{err}
	mmDeleteByRoleIDAndUserIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndUserIDs.mock
}

// Set uses given function f to mock the UserRolePrimeDB.DeleteByRoleIDAndUserIDs method
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Set(f func(ctx context.Context, roleID int64, userIDs []int64) (err error)) *UserRolePrimeDBMock {
	if mmDeleteByRoleIDAndUserIDs.defaultExpectation != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.DeleteByRoleIDAndUserIDs method")
	}

	if len(mmDeleteByRoleIDAndUserIDs.expectations) > 0 {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.DeleteByRoleIDAndUserIDs method")
	}

	mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs = f
	mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndUserIDs.mock
}

// When sets expectation for the UserRolePrimeDB.DeleteByRoleIDAndUserIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) When(ctx context.Context, roleID int64, userIDs []int64) *UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation {
	if mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation{
		mock:               mmDeleteByRoleIDAndUserIDs.mock,
		params:             &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams{ctx, roleID, userIDs},
		expectationOrigins: UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleIDAndUserIDs.expectations = append(mmDeleteByRoleIDAndUserIDs.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.DeleteByRoleIDAndUserIDs return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockDeleteByRoleIDAndUserIDsExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockDeleteByRoleIDAndUserIDsResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.DeleteByRoleIDAndUserIDs should be invoked
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Times(n uint64) *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs {
	if n == 0 {
		mmDeleteByRoleIDAndUserIDs.mock.t.Fatalf("Times of UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleIDAndUserIDs.expectedInvocations, n)
	mmDeleteByRoleIDAndUserIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndUserIDs
}

func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) invocationsDone() bool {
	if len(mmDeleteByRoleIDAndUserIDs.expectations) == 0 && mmDeleteByRoleIDAndUserIDs.defaultExpectation == nil && mmDeleteByRoleIDAndUserIDs.mock.funcDeleteByRoleIDAndUserIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndUserIDs.mock.afterDeleteByRoleIDAndUserIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndUserIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleIDAndUserIDs implements mm_repository.UserRolePrimeDB
func (mmDeleteByRoleIDAndUserIDs *UserRolePrimeDBMock) DeleteByRoleIDAndUserIDs(ctx context.Context, roleID int64, userIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleIDAndUserIDs.beforeDeleteByRoleIDAndUserIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleIDAndUserIDs.afterDeleteByRoleIDAndUserIDsCounter, 1)

	mmDeleteByRoleIDAndUserIDs.t.Helper()

	if mmDeleteByRoleIDAndUserIDs.inspectFuncDeleteByRoleIDAndUserIDs != nil {
		mmDeleteByRoleIDAndUserIDs.inspectFuncDeleteByRoleIDAndUserIDs(ctx, roleID, userIDs)
	}

	mm_params := UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams{ctx, roleID, userIDs}

	// Record call args
	mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.mutex.Lock()
	mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.callArgs = append(mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.callArgs, &mm_params)
	mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams{ctx, roleID, userIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.userIDs != nil && !minimock.Equal(*mm_want_ptrs.userIDs, mm_got.userIDs) {
				mmDeleteByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs got unexpected parameter userIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.originUserIDs, *mm_want_ptrs.userIDs, mm_got.userIDs, minimock.Diff(*mm_want_ptrs.userIDs, mm_got.userIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleIDAndUserIDs.t.Errorf("UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleIDAndUserIDs.DeleteByRoleIDAndUserIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleIDAndUserIDs.t.Fatal("No results are set for the UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleIDAndUserIDs.funcDeleteByRoleIDAndUserIDs != nil {
		return mmDeleteByRoleIDAndUserIDs.funcDeleteByRoleIDAndUserIDs(ctx, roleID, userIDs)
	}
	mmDeleteByRoleIDAndUserIDs.t.Fatalf("Unexpected call to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs. %v %v %v", ctx, roleID, userIDs)
	return
}

// DeleteByRoleIDAndUserIDsAfterCounter returns a count of finished UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs invocations
func (mmDeleteByRoleIDAndUserIDs *UserRolePrimeDBMock) DeleteByRoleIDAndUserIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndUserIDs.afterDeleteByRoleIDAndUserIDsCounter)
}

// DeleteByRoleIDAndUserIDsBeforeCounter returns a count of UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs invocations
func (mmDeleteByRoleIDAndUserIDs *UserRolePrimeDBMock) DeleteByRoleIDAndUserIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndUserIDs.beforeDeleteByRoleIDAndUserIDsCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleIDAndUserIDs *mUserRolePrimeDBMockDeleteByRoleIDAndUserIDs) Calls() []*UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams {
	mmDeleteByRoleIDAndUserIDs.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockDeleteByRoleIDAndUserIDsParams, len(mmDeleteByRoleIDAndUserIDs.callArgs))
	copy(argCopy, mmDeleteByRoleIDAndUserIDs.callArgs)

	mmDeleteByRoleIDAndUserIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDAndUserIDsDone returns true if the count of the DeleteByRoleIDAndUserIDs invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockDeleteByRoleIDAndUserIDsDone() bool {
	if m.DeleteByRoleIDAndUserIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDAndUserIDsMock.invocationsDone()
}

// MinimockDeleteByRoleIDAndUserIDsInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockDeleteByRoleIDAndUserIDsInspect() {
	for _, e := range m.DeleteByRoleIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDAndUserIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDAndUserIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDAndUserIDsMock.defaultExpectation != nil && afterDeleteByRoleIDAndUserIDsCounter < 1 {
		if m.DeleteByRoleIDAndUserIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs at\n%s", m.DeleteByRoleIDAndUserIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs at\n%s with params: %#v", m.DeleteByRoleIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDAndUserIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleIDAndUserIDs != nil && afterDeleteByRoleIDAndUserIDsCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs at\n%s", m.funcDeleteByRoleIDAndUserIDsOrigin)
	}

	if !m.DeleteByRoleIDAndUserIDsMock.invocationsDone() && afterDeleteByRoleIDAndUserIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.DeleteByRoleIDAndUserIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDAndUserIDsMock.expectedInvocations), m.DeleteByRoleIDAndUserIDsMock.expectedInvocationsOrigin, afterDeleteByRoleIDAndUserIDsCounter)
	}
}

type mUserRolePrimeDBMockDeleteByUserID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockDeleteByUserIDExpectation
	expectations       []*UserRolePrimeDBMockDeleteByUserIDExpectation

	callArgs []*UserRolePrimeDBMockDeleteByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockDeleteByUserIDExpectation specifies expectation struct of the UserRolePrimeDB.DeleteByUserID
type UserRolePrimeDBMockDeleteByUserIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockDeleteByUserIDParams
	paramPtrs          *UserRolePrimeDBMockDeleteByUserIDParamPtrs
	expectationOrigins UserRolePrimeDBMockDeleteByUserIDExpectationOrigins
	results            *UserRolePrimeDBMockDeleteByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockDeleteByUserIDParams contains parameters of the UserRolePrimeDB.DeleteByUserID
type UserRolePrimeDBMockDeleteByUserIDParams struct {
	userID int64
}

// UserRolePrimeDBMockDeleteByUserIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.DeleteByUserID
type UserRolePrimeDBMockDeleteByUserIDParamPtrs struct {
	userID *int64
}

// UserRolePrimeDBMockDeleteByUserIDResults contains results of the UserRolePrimeDB.DeleteByUserID
type UserRolePrimeDBMockDeleteByUserIDResults struct {
	err error
}

// UserRolePrimeDBMockDeleteByUserIDOrigins contains origins of expectations of the UserRolePrimeDB.DeleteByUserID
type UserRolePrimeDBMockDeleteByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Optional() *mUserRolePrimeDBMockDeleteByUserID {
	mmDeleteByUserID.optional = true
	return mmDeleteByUserID
}

// Expect sets up expected params for UserRolePrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Expect(userID int64) *mUserRolePrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserID mock is already set by ExpectParams functions")
	}

	mmDeleteByUserID.defaultExpectation.params = &UserRolePrimeDBMockDeleteByUserIDParams{userID}
	mmDeleteByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserID.expectations {
		if minimock.Equal(e.params, mmDeleteByUserID.defaultExpectation.params) {
			mmDeleteByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserID.defaultExpectation.params)
		}
	}

	return mmDeleteByUserID
}

// ExpectUserIDParam1 sets up expected param userID for UserRolePrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) ExpectUserIDParam1(userID int64) *mUserRolePrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.params != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserID mock is already set by Expect")
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDParamPtrs{}
	}
	mmDeleteByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Inspect(f func(userID int64)) *mUserRolePrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.inspectFuncDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.DeleteByUserID")
	}

	mmDeleteByUserID.mock.inspectFuncDeleteByUserID = f

	return mmDeleteByUserID
}

// Return sets up results that will be returned by UserRolePrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Return(err error) *UserRolePrimeDBMock {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDExpectation{mock: mmDeleteByUserID.mock}
	}
	mmDeleteByUserID.defaultExpectation.results = &UserRolePrimeDBMockDeleteByUserIDResults{err}
	mmDeleteByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.DeleteByUserID method
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Set(f func(userID int64) (err error)) *UserRolePrimeDBMock {
	if mmDeleteByUserID.defaultExpectation != nil {
		mmDeleteByUserID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.DeleteByUserID method")
	}

	if len(mmDeleteByUserID.expectations) > 0 {
		mmDeleteByUserID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.DeleteByUserID method")
	}

	mmDeleteByUserID.mock.funcDeleteByUserID = f
	mmDeleteByUserID.mock.funcDeleteByUserIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID.mock
}

// When sets expectation for the UserRolePrimeDB.DeleteByUserID which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) When(userID int64) *UserRolePrimeDBMockDeleteByUserIDExpectation {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockDeleteByUserIDExpectation{
		mock:               mmDeleteByUserID.mock,
		params:             &UserRolePrimeDBMockDeleteByUserIDParams{userID},
		expectationOrigins: UserRolePrimeDBMockDeleteByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserID.expectations = append(mmDeleteByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.DeleteByUserID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockDeleteByUserIDExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockDeleteByUserIDResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.DeleteByUserID should be invoked
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Times(n uint64) *mUserRolePrimeDBMockDeleteByUserID {
	if n == 0 {
		mmDeleteByUserID.mock.t.Fatalf("Times of UserRolePrimeDBMock.DeleteByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserID.expectedInvocations, n)
	mmDeleteByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID
}

func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) invocationsDone() bool {
	if len(mmDeleteByUserID.expectations) == 0 && mmDeleteByUserID.defaultExpectation == nil && mmDeleteByUserID.mock.funcDeleteByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserID.mock.afterDeleteByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserID implements mm_repository.UserRolePrimeDB
func (mmDeleteByUserID *UserRolePrimeDBMock) DeleteByUserID(userID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserID.beforeDeleteByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserID.afterDeleteByUserIDCounter, 1)

	mmDeleteByUserID.t.Helper()

	if mmDeleteByUserID.inspectFuncDeleteByUserID != nil {
		mmDeleteByUserID.inspectFuncDeleteByUserID(userID)
	}

	mm_params := UserRolePrimeDBMockDeleteByUserIDParams{userID}

	// Record call args
	mmDeleteByUserID.DeleteByUserIDMock.mutex.Lock()
	mmDeleteByUserID.DeleteByUserIDMock.callArgs = append(mmDeleteByUserID.DeleteByUserIDMock.callArgs, &mm_params)
	mmDeleteByUserID.DeleteByUserIDMock.mutex.Unlock()

	for _, e := range mmDeleteByUserID.DeleteByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockDeleteByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserID.t.Errorf("UserRolePrimeDBMock.DeleteByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserID.t.Errorf("UserRolePrimeDBMock.DeleteByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserID.t.Fatal("No results are set for the UserRolePrimeDBMock.DeleteByUserID")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserID.funcDeleteByUserID != nil {
		return mmDeleteByUserID.funcDeleteByUserID(userID)
	}
	mmDeleteByUserID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.DeleteByUserID. %v", userID)
	return
}

// DeleteByUserIDAfterCounter returns a count of finished UserRolePrimeDBMock.DeleteByUserID invocations
func (mmDeleteByUserID *UserRolePrimeDBMock) DeleteByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserID.afterDeleteByUserIDCounter)
}

// DeleteByUserIDBeforeCounter returns a count of UserRolePrimeDBMock.DeleteByUserID invocations
func (mmDeleteByUserID *UserRolePrimeDBMock) DeleteByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserID.beforeDeleteByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.DeleteByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserID *mUserRolePrimeDBMockDeleteByUserID) Calls() []*UserRolePrimeDBMockDeleteByUserIDParams {
	mmDeleteByUserID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockDeleteByUserIDParams, len(mmDeleteByUserID.callArgs))
	copy(argCopy, mmDeleteByUserID.callArgs)

	mmDeleteByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDDone returns true if the count of the DeleteByUserID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockDeleteByUserIDDone() bool {
	if m.DeleteByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDMock.invocationsDone()
}

// MinimockDeleteByUserIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockDeleteByUserIDInspect() {
	for _, e := range m.DeleteByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDMock.defaultExpectation != nil && afterDeleteByUserIDCounter < 1 {
		if m.DeleteByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserID at\n%s", m.DeleteByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserID at\n%s with params: %#v", m.DeleteByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserID != nil && afterDeleteByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserID at\n%s", m.funcDeleteByUserIDOrigin)
	}

	if !m.DeleteByUserIDMock.invocationsDone() && afterDeleteByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.DeleteByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDMock.expectedInvocations), m.DeleteByUserIDMock.expectedInvocationsOrigin, afterDeleteByUserIDCounter)
	}
}

type mUserRolePrimeDBMockDeleteByUserIDAndRoleID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation
	expectations       []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation

	callArgs []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation specifies expectation struct of the UserRolePrimeDB.DeleteByUserIDAndRoleID
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams
	paramPtrs          *UserRolePrimeDBMockDeleteByUserIDAndRoleIDParamPtrs
	expectationOrigins UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectationOrigins
	results            *UserRolePrimeDBMockDeleteByUserIDAndRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams contains parameters of the UserRolePrimeDB.DeleteByUserIDAndRoleID
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams struct {
	ctx    context.Context
	userID int64
	roleID int64
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.DeleteByUserIDAndRoleID
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDParamPtrs struct {
	ctx    *context.Context
	userID *int64
	roleID *int64
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDResults contains results of the UserRolePrimeDB.DeleteByUserIDAndRoleID
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDResults struct {
	err error
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDOrigins contains origins of expectations of the UserRolePrimeDB.DeleteByUserIDAndRoleID
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Optional() *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	mmDeleteByUserIDAndRoleID.optional = true
	return mmDeleteByUserIDAndRoleID
}

// Expect sets up expected params for UserRolePrimeDB.DeleteByUserIDAndRoleID
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Expect(ctx context.Context, userID int64, roleID int64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	if mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation{}
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndRoleID.defaultExpectation.params = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams{ctx, userID, roleID}
	mmDeleteByUserIDAndRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndRoleID.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndRoleID.defaultExpectation.params) {
			mmDeleteByUserIDAndRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndRoleID.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndRoleID
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.DeleteByUserIDAndRoleID
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	if mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation{}
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDParamPtrs{}
	}
	mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndRoleID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleID
}

// ExpectUserIDParam2 sets up expected param userID for UserRolePrimeDB.DeleteByUserIDAndRoleID
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) ExpectUserIDParam2(userID int64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	if mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation{}
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDParamPtrs{}
	}
	mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndRoleID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleID
}

// ExpectRoleIDParam3 sets up expected param roleID for UserRolePrimeDB.DeleteByUserIDAndRoleID
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) ExpectRoleIDParam3(roleID int64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	if mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation{}
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDParamPtrs{}
	}
	mmDeleteByUserIDAndRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByUserIDAndRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.DeleteByUserIDAndRoleID
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Inspect(f func(ctx context.Context, userID int64, roleID int64)) *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	if mmDeleteByUserIDAndRoleID.mock.inspectFuncDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.DeleteByUserIDAndRoleID")
	}

	mmDeleteByUserIDAndRoleID.mock.inspectFuncDeleteByUserIDAndRoleID = f

	return mmDeleteByUserIDAndRoleID
}

// Return sets up results that will be returned by UserRolePrimeDB.DeleteByUserIDAndRoleID
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Return(err error) *UserRolePrimeDBMock {
	if mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleID.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation{mock: mmDeleteByUserIDAndRoleID.mock}
	}
	mmDeleteByUserIDAndRoleID.defaultExpectation.results = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDResults{err}
	mmDeleteByUserIDAndRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.DeleteByUserIDAndRoleID method
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Set(f func(ctx context.Context, userID int64, roleID int64) (err error)) *UserRolePrimeDBMock {
	if mmDeleteByUserIDAndRoleID.defaultExpectation != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.DeleteByUserIDAndRoleID method")
	}

	if len(mmDeleteByUserIDAndRoleID.expectations) > 0 {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.DeleteByUserIDAndRoleID method")
	}

	mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID = f
	mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleID.mock
}

// When sets expectation for the UserRolePrimeDB.DeleteByUserIDAndRoleID which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) When(ctx context.Context, userID int64, roleID int64) *UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation {
	if mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation{
		mock:               mmDeleteByUserIDAndRoleID.mock,
		params:             &UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams{ctx, userID, roleID},
		expectationOrigins: UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndRoleID.expectations = append(mmDeleteByUserIDAndRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.DeleteByUserIDAndRoleID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockDeleteByUserIDAndRoleIDExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.DeleteByUserIDAndRoleID should be invoked
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Times(n uint64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleID {
	if n == 0 {
		mmDeleteByUserIDAndRoleID.mock.t.Fatalf("Times of UserRolePrimeDBMock.DeleteByUserIDAndRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndRoleID.expectedInvocations, n)
	mmDeleteByUserIDAndRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleID
}

func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) invocationsDone() bool {
	if len(mmDeleteByUserIDAndRoleID.expectations) == 0 && mmDeleteByUserIDAndRoleID.defaultExpectation == nil && mmDeleteByUserIDAndRoleID.mock.funcDeleteByUserIDAndRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleID.mock.afterDeleteByUserIDAndRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndRoleID implements mm_repository.UserRolePrimeDB
func (mmDeleteByUserIDAndRoleID *UserRolePrimeDBMock) DeleteByUserIDAndRoleID(ctx context.Context, userID int64, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleID.beforeDeleteByUserIDAndRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleID.afterDeleteByUserIDAndRoleIDCounter, 1)

	mmDeleteByUserIDAndRoleID.t.Helper()

	if mmDeleteByUserIDAndRoleID.inspectFuncDeleteByUserIDAndRoleID != nil {
		mmDeleteByUserIDAndRoleID.inspectFuncDeleteByUserIDAndRoleID(ctx, userID, roleID)
	}

	mm_params := UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams{ctx, userID, roleID}

	// Record call args
	mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.mutex.Lock()
	mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.callArgs = append(mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.callArgs, &mm_params)
	mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams{ctx, userID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndRoleID.DeleteByUserIDAndRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndRoleID.t.Fatal("No results are set for the UserRolePrimeDBMock.DeleteByUserIDAndRoleID")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndRoleID.funcDeleteByUserIDAndRoleID != nil {
		return mmDeleteByUserIDAndRoleID.funcDeleteByUserIDAndRoleID(ctx, userID, roleID)
	}
	mmDeleteByUserIDAndRoleID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleID. %v %v %v", ctx, userID, roleID)
	return
}

// DeleteByUserIDAndRoleIDAfterCounter returns a count of finished UserRolePrimeDBMock.DeleteByUserIDAndRoleID invocations
func (mmDeleteByUserIDAndRoleID *UserRolePrimeDBMock) DeleteByUserIDAndRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleID.afterDeleteByUserIDAndRoleIDCounter)
}

// DeleteByUserIDAndRoleIDBeforeCounter returns a count of UserRolePrimeDBMock.DeleteByUserIDAndRoleID invocations
func (mmDeleteByUserIDAndRoleID *UserRolePrimeDBMock) DeleteByUserIDAndRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleID.beforeDeleteByUserIDAndRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.DeleteByUserIDAndRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndRoleID *mUserRolePrimeDBMockDeleteByUserIDAndRoleID) Calls() []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams {
	mmDeleteByUserIDAndRoleID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockDeleteByUserIDAndRoleIDParams, len(mmDeleteByUserIDAndRoleID.callArgs))
	copy(argCopy, mmDeleteByUserIDAndRoleID.callArgs)

	mmDeleteByUserIDAndRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndRoleIDDone returns true if the count of the DeleteByUserIDAndRoleID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockDeleteByUserIDAndRoleIDDone() bool {
	if m.DeleteByUserIDAndRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndRoleIDMock.invocationsDone()
}

// MinimockDeleteByUserIDAndRoleIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockDeleteByUserIDAndRoleIDInspect() {
	for _, e := range m.DeleteByUserIDAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndRoleIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndRoleIDMock.defaultExpectation != nil && afterDeleteByUserIDAndRoleIDCounter < 1 {
		if m.DeleteByUserIDAndRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleID at\n%s", m.DeleteByUserIDAndRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleID at\n%s with params: %#v", m.DeleteByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndRoleID != nil && afterDeleteByUserIDAndRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleID at\n%s", m.funcDeleteByUserIDAndRoleIDOrigin)
	}

	if !m.DeleteByUserIDAndRoleIDMock.invocationsDone() && afterDeleteByUserIDAndRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.DeleteByUserIDAndRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndRoleIDMock.expectedInvocations), m.DeleteByUserIDAndRoleIDMock.expectedInvocationsOrigin, afterDeleteByUserIDAndRoleIDCounter)
	}
}

type mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation
	expectations       []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation

	callArgs []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation specifies expectation struct of the UserRolePrimeDB.DeleteByUserIDAndRoleIDs
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams
	paramPtrs          *UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs
	expectationOrigins UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectationOrigins
	results            *UserRolePrimeDBMockDeleteByUserIDAndRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams contains parameters of the UserRolePrimeDB.DeleteByUserIDAndRoleIDs
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams struct {
	ctx     context.Context
	userID  int64
	roleIDs []int64
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs contains pointers to parameters of the UserRolePrimeDB.DeleteByUserIDAndRoleIDs
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs struct {
	ctx     *context.Context
	userID  *int64
	roleIDs *[]int64
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDsResults contains results of the UserRolePrimeDB.DeleteByUserIDAndRoleIDs
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDsResults struct {
	err error
}

// UserRolePrimeDBMockDeleteByUserIDAndRoleIDsOrigins contains origins of expectations of the UserRolePrimeDB.DeleteByUserIDAndRoleIDs
type UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originUserID  string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Optional() *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	mmDeleteByUserIDAndRoleIDs.optional = true
	return mmDeleteByUserIDAndRoleIDs
}

// Expect sets up expected params for UserRolePrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Expect(ctx context.Context, userID int64, roleIDs []int64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndRoleIDs.defaultExpectation.params = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndRoleIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndRoleIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndRoleIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserRolePrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) ExpectCtxParam1(ctx context.Context) *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectUserIDParam2 sets up expected param userID for UserRolePrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) ExpectUserIDParam2(userID int64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// ExpectRoleIDsParam3 sets up expected param roleIDs for UserRolePrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) ExpectRoleIDsParam3(roleIDs []int64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation{}
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParamPtrs{}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Inspect(f func(ctx context.Context, userID int64, roleIDs []int64)) *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	if mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs")
	}

	mmDeleteByUserIDAndRoleIDs.mock.inspectFuncDeleteByUserIDAndRoleIDs = f

	return mmDeleteByUserIDAndRoleIDs
}

// Return sets up results that will be returned by UserRolePrimeDB.DeleteByUserIDAndRoleIDs
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Return(err error) *UserRolePrimeDBMock {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndRoleIDs.defaultExpectation = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation{mock: mmDeleteByUserIDAndRoleIDs.mock}
	}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.results = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsResults{err}
	mmDeleteByUserIDAndRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// Set uses given function f to mock the UserRolePrimeDB.DeleteByUserIDAndRoleIDs method
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Set(f func(ctx context.Context, userID int64, roleIDs []int64) (err error)) *UserRolePrimeDBMock {
	if mmDeleteByUserIDAndRoleIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.DeleteByUserIDAndRoleIDs method")
	}

	if len(mmDeleteByUserIDAndRoleIDs.expectations) > 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.DeleteByUserIDAndRoleIDs method")
	}

	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs = f
	mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs.mock
}

// When sets expectation for the UserRolePrimeDB.DeleteByUserIDAndRoleIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) When(ctx context.Context, userID int64, roleIDs []int64) *UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation {
	if mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation{
		mock:               mmDeleteByUserIDAndRoleIDs.mock,
		params:             &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs},
		expectationOrigins: UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndRoleIDs.expectations = append(mmDeleteByUserIDAndRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.DeleteByUserIDAndRoleIDs return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockDeleteByUserIDAndRoleIDsExpectation) Then(err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockDeleteByUserIDAndRoleIDsResults{err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.DeleteByUserIDAndRoleIDs should be invoked
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Times(n uint64) *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs {
	if n == 0 {
		mmDeleteByUserIDAndRoleIDs.mock.t.Fatalf("Times of UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations, n)
	mmDeleteByUserIDAndRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndRoleIDs
}

func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndRoleIDs.expectations) == 0 && mmDeleteByUserIDAndRoleIDs.defaultExpectation == nil && mmDeleteByUserIDAndRoleIDs.mock.funcDeleteByUserIDAndRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.mock.afterDeleteByUserIDAndRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndRoleIDs implements mm_repository.UserRolePrimeDB
func (mmDeleteByUserIDAndRoleIDs *UserRolePrimeDBMock) DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter, 1)

	mmDeleteByUserIDAndRoleIDs.t.Helper()

	if mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs != nil {
		mmDeleteByUserIDAndRoleIDs.inspectFuncDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}

	mm_params := UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

	// Record call args
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Lock()
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs = append(mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams{ctx, userID, roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeleteByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndRoleIDs.t.Errorf("UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndRoleIDs.DeleteByUserIDAndRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndRoleIDs.t.Fatal("No results are set for the UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs != nil {
		return mmDeleteByUserIDAndRoleIDs.funcDeleteByUserIDAndRoleIDs(ctx, userID, roleIDs)
	}
	mmDeleteByUserIDAndRoleIDs.t.Fatalf("Unexpected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs. %v %v %v", ctx, userID, roleIDs)
	return
}

// DeleteByUserIDAndRoleIDsAfterCounter returns a count of finished UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *UserRolePrimeDBMock) DeleteByUserIDAndRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.afterDeleteByUserIDAndRoleIDsCounter)
}

// DeleteByUserIDAndRoleIDsBeforeCounter returns a count of UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs invocations
func (mmDeleteByUserIDAndRoleIDs *UserRolePrimeDBMock) DeleteByUserIDAndRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndRoleIDs.beforeDeleteByUserIDAndRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndRoleIDs *mUserRolePrimeDBMockDeleteByUserIDAndRoleIDs) Calls() []*UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams {
	mmDeleteByUserIDAndRoleIDs.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockDeleteByUserIDAndRoleIDsParams, len(mmDeleteByUserIDAndRoleIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndRoleIDs.callArgs)

	mmDeleteByUserIDAndRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndRoleIDsDone returns true if the count of the DeleteByUserIDAndRoleIDs invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockDeleteByUserIDAndRoleIDsDone() bool {
	if m.DeleteByUserIDAndRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndRoleIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndRoleIDsInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockDeleteByUserIDAndRoleIDsInspect() {
	for _, e := range m.DeleteByUserIDAndRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndRoleIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		if m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s with params: %#v", m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndRoleIDs != nil && afterDeleteByUserIDAndRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s", m.funcDeleteByUserIDAndRoleIDsOrigin)
	}

	if !m.DeleteByUserIDAndRoleIDsMock.invocationsDone() && afterDeleteByUserIDAndRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.DeleteByUserIDAndRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndRoleIDsMock.expectedInvocations), m.DeleteByUserIDAndRoleIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndRoleIDsCounter)
	}
}

type mUserRolePrimeDBMockGetAll struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockGetAllExpectation
	expectations       []*UserRolePrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockGetAllExpectation specifies expectation struct of the UserRolePrimeDB.GetAll
type UserRolePrimeDBMockGetAllExpectation struct {
	mock *UserRolePrimeDBMock

	results      *UserRolePrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// UserRolePrimeDBMockGetAllResults contains results of the UserRolePrimeDB.GetAll
type UserRolePrimeDBMockGetAllResults struct {
	ua1 []userentity.UserRole
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mUserRolePrimeDBMockGetAll) Optional() *mUserRolePrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for UserRolePrimeDB.GetAll
func (mmGetAll *mUserRolePrimeDBMockGetAll) Expect() *mUserRolePrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("UserRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &UserRolePrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.GetAll
func (mmGetAll *mUserRolePrimeDBMockGetAll) Inspect(f func()) *mUserRolePrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by UserRolePrimeDB.GetAll
func (mmGetAll *mUserRolePrimeDBMockGetAll) Return(ua1 []userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("UserRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &UserRolePrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &UserRolePrimeDBMockGetAllResults{ua1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the UserRolePrimeDB.GetAll method
func (mmGetAll *mUserRolePrimeDBMockGetAll) Set(f func() (ua1 []userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times UserRolePrimeDB.GetAll should be invoked
func (mmGetAll *mUserRolePrimeDBMockGetAll) Times(n uint64) *mUserRolePrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of UserRolePrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mUserRolePrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.UserRolePrimeDB
func (mmGetAll *UserRolePrimeDBMock) GetAll() (ua1 []userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the UserRolePrimeDBMock.GetAll")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to UserRolePrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished UserRolePrimeDBMock.GetAll invocations
func (mmGetAll *UserRolePrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of UserRolePrimeDBMock.GetAll invocations
func (mmGetAll *UserRolePrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to UserRolePrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mUserRolePrimeDBMockGetByID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockGetByIDExpectation
	expectations       []*UserRolePrimeDBMockGetByIDExpectation

	callArgs []*UserRolePrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockGetByIDExpectation specifies expectation struct of the UserRolePrimeDB.GetByID
type UserRolePrimeDBMockGetByIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockGetByIDParams
	paramPtrs          *UserRolePrimeDBMockGetByIDParamPtrs
	expectationOrigins UserRolePrimeDBMockGetByIDExpectationOrigins
	results            *UserRolePrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockGetByIDParams contains parameters of the UserRolePrimeDB.GetByID
type UserRolePrimeDBMockGetByIDParams struct {
	id int64
}

// UserRolePrimeDBMockGetByIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.GetByID
type UserRolePrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// UserRolePrimeDBMockGetByIDResults contains results of the UserRolePrimeDB.GetByID
type UserRolePrimeDBMockGetByIDResults struct {
	u1  userentity.UserRole
	err error
}

// UserRolePrimeDBMockGetByIDOrigins contains origins of expectations of the UserRolePrimeDB.GetByID
type UserRolePrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mUserRolePrimeDBMockGetByID) Optional() *mUserRolePrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for UserRolePrimeDB.GetByID
func (mmGetByID *mUserRolePrimeDBMockGetByID) Expect(id int64) *mUserRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("UserRolePrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &UserRolePrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for UserRolePrimeDB.GetByID
func (mmGetByID *mUserRolePrimeDBMockGetByID) ExpectIdParam1(id int64) *mUserRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("UserRolePrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.GetByID
func (mmGetByID *mUserRolePrimeDBMockGetByID) Inspect(f func(id int64)) *mUserRolePrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by UserRolePrimeDB.GetByID
func (mmGetByID *mUserRolePrimeDBMockGetByID) Return(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &UserRolePrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &UserRolePrimeDBMockGetByIDResults{u1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.GetByID method
func (mmGetByID *mUserRolePrimeDBMockGetByID) Set(f func(id int64) (u1 userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the UserRolePrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mUserRolePrimeDBMockGetByID) When(id int64) *UserRolePrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("UserRolePrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &UserRolePrimeDBMockGetByIDParams{id},
		expectationOrigins: UserRolePrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockGetByIDExpectation) Then(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockGetByIDResults{u1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.GetByID should be invoked
func (mmGetByID *mUserRolePrimeDBMockGetByID) Times(n uint64) *mUserRolePrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of UserRolePrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mUserRolePrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.UserRolePrimeDB
func (mmGetByID *UserRolePrimeDBMock) GetByID(id int64) (u1 userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := UserRolePrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("UserRolePrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("UserRolePrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the UserRolePrimeDBMock.GetByID")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished UserRolePrimeDBMock.GetByID invocations
func (mmGetByID *UserRolePrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of UserRolePrimeDBMock.GetByID invocations
func (mmGetByID *UserRolePrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mUserRolePrimeDBMockGetByID) Calls() []*UserRolePrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mUserRolePrimeDBMockGetByUserID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockGetByUserIDExpectation
	expectations       []*UserRolePrimeDBMockGetByUserIDExpectation

	callArgs []*UserRolePrimeDBMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockGetByUserIDExpectation specifies expectation struct of the UserRolePrimeDB.GetByUserID
type UserRolePrimeDBMockGetByUserIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockGetByUserIDParams
	paramPtrs          *UserRolePrimeDBMockGetByUserIDParamPtrs
	expectationOrigins UserRolePrimeDBMockGetByUserIDExpectationOrigins
	results            *UserRolePrimeDBMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockGetByUserIDParams contains parameters of the UserRolePrimeDB.GetByUserID
type UserRolePrimeDBMockGetByUserIDParams struct {
	userID int64
}

// UserRolePrimeDBMockGetByUserIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.GetByUserID
type UserRolePrimeDBMockGetByUserIDParamPtrs struct {
	userID *int64
}

// UserRolePrimeDBMockGetByUserIDResults contains results of the UserRolePrimeDB.GetByUserID
type UserRolePrimeDBMockGetByUserIDResults struct {
	ua1 []userentity.UserRole
	err error
}

// UserRolePrimeDBMockGetByUserIDOrigins contains origins of expectations of the UserRolePrimeDB.GetByUserID
type UserRolePrimeDBMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Optional() *mUserRolePrimeDBMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for UserRolePrimeDB.GetByUserID
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Expect(userID int64) *mUserRolePrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &UserRolePrimeDBMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for UserRolePrimeDB.GetByUserID
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) ExpectUserIDParam1(userID int64) *mUserRolePrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.GetByUserID
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Inspect(f func(userID int64)) *mUserRolePrimeDBMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by UserRolePrimeDB.GetByUserID
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Return(ua1 []userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &UserRolePrimeDBMockGetByUserIDResults{ua1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.GetByUserID method
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Set(f func(userID int64) (ua1 []userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the UserRolePrimeDB.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) When(userID int64) *UserRolePrimeDBMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &UserRolePrimeDBMockGetByUserIDParams{userID},
		expectationOrigins: UserRolePrimeDBMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.GetByUserID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockGetByUserIDExpectation) Then(ua1 []userentity.UserRole, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockGetByUserIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.GetByUserID should be invoked
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Times(n uint64) *mUserRolePrimeDBMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of UserRolePrimeDBMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.UserRolePrimeDB
func (mmGetByUserID *UserRolePrimeDBMock) GetByUserID(userID int64) (ua1 []userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := UserRolePrimeDBMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("UserRolePrimeDBMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("UserRolePrimeDBMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the UserRolePrimeDBMock.GetByUserID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished UserRolePrimeDBMock.GetByUserID invocations
func (mmGetByUserID *UserRolePrimeDBMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of UserRolePrimeDBMock.GetByUserID invocations
func (mmGetByUserID *UserRolePrimeDBMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mUserRolePrimeDBMockGetByUserID) Calls() []*UserRolePrimeDBMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mUserRolePrimeDBMockGetByUserIDAndRoleID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation
	expectations       []*UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation

	callArgs []*UserRolePrimeDBMockGetByUserIDAndRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation specifies expectation struct of the UserRolePrimeDB.GetByUserIDAndRoleID
type UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockGetByUserIDAndRoleIDParams
	paramPtrs          *UserRolePrimeDBMockGetByUserIDAndRoleIDParamPtrs
	expectationOrigins UserRolePrimeDBMockGetByUserIDAndRoleIDExpectationOrigins
	results            *UserRolePrimeDBMockGetByUserIDAndRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockGetByUserIDAndRoleIDParams contains parameters of the UserRolePrimeDB.GetByUserIDAndRoleID
type UserRolePrimeDBMockGetByUserIDAndRoleIDParams struct {
	userID int64
	roleID int64
}

// UserRolePrimeDBMockGetByUserIDAndRoleIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.GetByUserIDAndRoleID
type UserRolePrimeDBMockGetByUserIDAndRoleIDParamPtrs struct {
	userID *int64
	roleID *int64
}

// UserRolePrimeDBMockGetByUserIDAndRoleIDResults contains results of the UserRolePrimeDB.GetByUserIDAndRoleID
type UserRolePrimeDBMockGetByUserIDAndRoleIDResults struct {
	u1  userentity.UserRole
	err error
}

// UserRolePrimeDBMockGetByUserIDAndRoleIDOrigins contains origins of expectations of the UserRolePrimeDB.GetByUserIDAndRoleID
type UserRolePrimeDBMockGetByUserIDAndRoleIDExpectationOrigins struct {
	origin       string
	originUserID string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Optional() *mUserRolePrimeDBMockGetByUserIDAndRoleID {
	mmGetByUserIDAndRoleID.optional = true
	return mmGetByUserIDAndRoleID
}

// Expect sets up expected params for UserRolePrimeDB.GetByUserIDAndRoleID
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Expect(userID int64, roleID int64) *mUserRolePrimeDBMockGetByUserIDAndRoleID {
	if mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Set")
	}

	if mmGetByUserIDAndRoleID.defaultExpectation == nil {
		mmGetByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation{}
	}

	if mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by ExpectParams functions")
	}

	mmGetByUserIDAndRoleID.defaultExpectation.params = &UserRolePrimeDBMockGetByUserIDAndRoleIDParams{userID, roleID}
	mmGetByUserIDAndRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserIDAndRoleID.expectations {
		if minimock.Equal(e.params, mmGetByUserIDAndRoleID.defaultExpectation.params) {
			mmGetByUserIDAndRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserIDAndRoleID.defaultExpectation.params)
		}
	}

	return mmGetByUserIDAndRoleID
}

// ExpectUserIDParam1 sets up expected param userID for UserRolePrimeDB.GetByUserIDAndRoleID
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) ExpectUserIDParam1(userID int64) *mUserRolePrimeDBMockGetByUserIDAndRoleID {
	if mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Set")
	}

	if mmGetByUserIDAndRoleID.defaultExpectation == nil {
		mmGetByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation{}
	}

	if mmGetByUserIDAndRoleID.defaultExpectation.params != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Expect")
	}

	if mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockGetByUserIDAndRoleIDParamPtrs{}
	}
	mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserIDAndRoleID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserIDAndRoleID
}

// ExpectRoleIDParam2 sets up expected param roleID for UserRolePrimeDB.GetByUserIDAndRoleID
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) ExpectRoleIDParam2(roleID int64) *mUserRolePrimeDBMockGetByUserIDAndRoleID {
	if mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Set")
	}

	if mmGetByUserIDAndRoleID.defaultExpectation == nil {
		mmGetByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation{}
	}

	if mmGetByUserIDAndRoleID.defaultExpectation.params != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Expect")
	}

	if mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockGetByUserIDAndRoleIDParamPtrs{}
	}
	mmGetByUserIDAndRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByUserIDAndRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByUserIDAndRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.GetByUserIDAndRoleID
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Inspect(f func(userID int64, roleID int64)) *mUserRolePrimeDBMockGetByUserIDAndRoleID {
	if mmGetByUserIDAndRoleID.mock.inspectFuncGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.GetByUserIDAndRoleID")
	}

	mmGetByUserIDAndRoleID.mock.inspectFuncGetByUserIDAndRoleID = f

	return mmGetByUserIDAndRoleID
}

// Return sets up results that will be returned by UserRolePrimeDB.GetByUserIDAndRoleID
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Return(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Set")
	}

	if mmGetByUserIDAndRoleID.defaultExpectation == nil {
		mmGetByUserIDAndRoleID.defaultExpectation = &UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation{mock: mmGetByUserIDAndRoleID.mock}
	}
	mmGetByUserIDAndRoleID.defaultExpectation.results = &UserRolePrimeDBMockGetByUserIDAndRoleIDResults{u1, err}
	mmGetByUserIDAndRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndRoleID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.GetByUserIDAndRoleID method
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Set(f func(userID int64, roleID int64) (u1 userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmGetByUserIDAndRoleID.defaultExpectation != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.GetByUserIDAndRoleID method")
	}

	if len(mmGetByUserIDAndRoleID.expectations) > 0 {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.GetByUserIDAndRoleID method")
	}

	mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID = f
	mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndRoleID.mock
}

// When sets expectation for the UserRolePrimeDB.GetByUserIDAndRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) When(userID int64, roleID int64) *UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation {
	if mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetByUserIDAndRoleID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation{
		mock:               mmGetByUserIDAndRoleID.mock,
		params:             &UserRolePrimeDBMockGetByUserIDAndRoleIDParams{userID, roleID},
		expectationOrigins: UserRolePrimeDBMockGetByUserIDAndRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserIDAndRoleID.expectations = append(mmGetByUserIDAndRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.GetByUserIDAndRoleID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockGetByUserIDAndRoleIDExpectation) Then(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockGetByUserIDAndRoleIDResults{u1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.GetByUserIDAndRoleID should be invoked
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Times(n uint64) *mUserRolePrimeDBMockGetByUserIDAndRoleID {
	if n == 0 {
		mmGetByUserIDAndRoleID.mock.t.Fatalf("Times of UserRolePrimeDBMock.GetByUserIDAndRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserIDAndRoleID.expectedInvocations, n)
	mmGetByUserIDAndRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserIDAndRoleID
}

func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) invocationsDone() bool {
	if len(mmGetByUserIDAndRoleID.expectations) == 0 && mmGetByUserIDAndRoleID.defaultExpectation == nil && mmGetByUserIDAndRoleID.mock.funcGetByUserIDAndRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndRoleID.mock.afterGetByUserIDAndRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserIDAndRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserIDAndRoleID implements mm_repository.UserRolePrimeDB
func (mmGetByUserIDAndRoleID *UserRolePrimeDBMock) GetByUserIDAndRoleID(userID int64, roleID int64) (u1 userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmGetByUserIDAndRoleID.beforeGetByUserIDAndRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserIDAndRoleID.afterGetByUserIDAndRoleIDCounter, 1)

	mmGetByUserIDAndRoleID.t.Helper()

	if mmGetByUserIDAndRoleID.inspectFuncGetByUserIDAndRoleID != nil {
		mmGetByUserIDAndRoleID.inspectFuncGetByUserIDAndRoleID(userID, roleID)
	}

	mm_params := UserRolePrimeDBMockGetByUserIDAndRoleIDParams{userID, roleID}

	// Record call args
	mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.mutex.Lock()
	mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.callArgs = append(mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.callArgs, &mm_params)
	mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockGetByUserIDAndRoleIDParams{userID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.GetByUserIDAndRoleID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.GetByUserIDAndRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserIDAndRoleID.t.Errorf("UserRolePrimeDBMock.GetByUserIDAndRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserIDAndRoleID.GetByUserIDAndRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserIDAndRoleID.t.Fatal("No results are set for the UserRolePrimeDBMock.GetByUserIDAndRoleID")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByUserIDAndRoleID.funcGetByUserIDAndRoleID != nil {
		return mmGetByUserIDAndRoleID.funcGetByUserIDAndRoleID(userID, roleID)
	}
	mmGetByUserIDAndRoleID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.GetByUserIDAndRoleID. %v %v", userID, roleID)
	return
}

// GetByUserIDAndRoleIDAfterCounter returns a count of finished UserRolePrimeDBMock.GetByUserIDAndRoleID invocations
func (mmGetByUserIDAndRoleID *UserRolePrimeDBMock) GetByUserIDAndRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndRoleID.afterGetByUserIDAndRoleIDCounter)
}

// GetByUserIDAndRoleIDBeforeCounter returns a count of UserRolePrimeDBMock.GetByUserIDAndRoleID invocations
func (mmGetByUserIDAndRoleID *UserRolePrimeDBMock) GetByUserIDAndRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserIDAndRoleID.beforeGetByUserIDAndRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.GetByUserIDAndRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserIDAndRoleID *mUserRolePrimeDBMockGetByUserIDAndRoleID) Calls() []*UserRolePrimeDBMockGetByUserIDAndRoleIDParams {
	mmGetByUserIDAndRoleID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockGetByUserIDAndRoleIDParams, len(mmGetByUserIDAndRoleID.callArgs))
	copy(argCopy, mmGetByUserIDAndRoleID.callArgs)

	mmGetByUserIDAndRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDAndRoleIDDone returns true if the count of the GetByUserIDAndRoleID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockGetByUserIDAndRoleIDDone() bool {
	if m.GetByUserIDAndRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDAndRoleIDMock.invocationsDone()
}

// MinimockGetByUserIDAndRoleIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockGetByUserIDAndRoleIDInspect() {
	for _, e := range m.GetByUserIDAndRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserIDAndRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDAndRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDAndRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDAndRoleIDMock.defaultExpectation != nil && afterGetByUserIDAndRoleIDCounter < 1 {
		if m.GetByUserIDAndRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserIDAndRoleID at\n%s", m.GetByUserIDAndRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserIDAndRoleID at\n%s with params: %#v", m.GetByUserIDAndRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDAndRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserIDAndRoleID != nil && afterGetByUserIDAndRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetByUserIDAndRoleID at\n%s", m.funcGetByUserIDAndRoleIDOrigin)
	}

	if !m.GetByUserIDAndRoleIDMock.invocationsDone() && afterGetByUserIDAndRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.GetByUserIDAndRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDAndRoleIDMock.expectedInvocations), m.GetByUserIDAndRoleIDMock.expectedInvocationsOrigin, afterGetByUserIDAndRoleIDCounter)
	}
}

type mUserRolePrimeDBMockGetUserRoleIDs struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockGetUserRoleIDsExpectation
	expectations       []*UserRolePrimeDBMockGetUserRoleIDsExpectation

	callArgs []*UserRolePrimeDBMockGetUserRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockGetUserRoleIDsExpectation specifies expectation struct of the UserRolePrimeDB.GetUserRoleIDs
type UserRolePrimeDBMockGetUserRoleIDsExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockGetUserRoleIDsParams
	paramPtrs          *UserRolePrimeDBMockGetUserRoleIDsParamPtrs
	expectationOrigins UserRolePrimeDBMockGetUserRoleIDsExpectationOrigins
	results            *UserRolePrimeDBMockGetUserRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockGetUserRoleIDsParams contains parameters of the UserRolePrimeDB.GetUserRoleIDs
type UserRolePrimeDBMockGetUserRoleIDsParams struct {
	userID int64
}

// UserRolePrimeDBMockGetUserRoleIDsParamPtrs contains pointers to parameters of the UserRolePrimeDB.GetUserRoleIDs
type UserRolePrimeDBMockGetUserRoleIDsParamPtrs struct {
	userID *int64
}

// UserRolePrimeDBMockGetUserRoleIDsResults contains results of the UserRolePrimeDB.GetUserRoleIDs
type UserRolePrimeDBMockGetUserRoleIDsResults struct {
	ia1 []int64
	err error
}

// UserRolePrimeDBMockGetUserRoleIDsOrigins contains origins of expectations of the UserRolePrimeDB.GetUserRoleIDs
type UserRolePrimeDBMockGetUserRoleIDsExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Optional() *mUserRolePrimeDBMockGetUserRoleIDs {
	mmGetUserRoleIDs.optional = true
	return mmGetUserRoleIDs
}

// Expect sets up expected params for UserRolePrimeDB.GetUserRoleIDs
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Expect(userID int64) *mUserRolePrimeDBMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &UserRolePrimeDBMockGetUserRoleIDsExpectation{}
	}

	if mmGetUserRoleIDs.defaultExpectation.paramPtrs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRoleIDs mock is already set by ExpectParams functions")
	}

	mmGetUserRoleIDs.defaultExpectation.params = &UserRolePrimeDBMockGetUserRoleIDsParams{userID}
	mmGetUserRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserRoleIDs.expectations {
		if minimock.Equal(e.params, mmGetUserRoleIDs.defaultExpectation.params) {
			mmGetUserRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserRoleIDs.defaultExpectation.params)
		}
	}

	return mmGetUserRoleIDs
}

// ExpectUserIDParam1 sets up expected param userID for UserRolePrimeDB.GetUserRoleIDs
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) ExpectUserIDParam1(userID int64) *mUserRolePrimeDBMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &UserRolePrimeDBMockGetUserRoleIDsExpectation{}
	}

	if mmGetUserRoleIDs.defaultExpectation.params != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRoleIDs mock is already set by Expect")
	}

	if mmGetUserRoleIDs.defaultExpectation.paramPtrs == nil {
		mmGetUserRoleIDs.defaultExpectation.paramPtrs = &UserRolePrimeDBMockGetUserRoleIDsParamPtrs{}
	}
	mmGetUserRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmGetUserRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUserRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.GetUserRoleIDs
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Inspect(f func(userID int64)) *mUserRolePrimeDBMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.inspectFuncGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.GetUserRoleIDs")
	}

	mmGetUserRoleIDs.mock.inspectFuncGetUserRoleIDs = f

	return mmGetUserRoleIDs
}

// Return sets up results that will be returned by UserRolePrimeDB.GetUserRoleIDs
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Return(ia1 []int64, err error) *UserRolePrimeDBMock {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &UserRolePrimeDBMockGetUserRoleIDsExpectation{mock: mmGetUserRoleIDs.mock}
	}
	mmGetUserRoleIDs.defaultExpectation.results = &UserRolePrimeDBMockGetUserRoleIDsResults{ia1, err}
	mmGetUserRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserRoleIDs.mock
}

// Set uses given function f to mock the UserRolePrimeDB.GetUserRoleIDs method
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Set(f func(userID int64) (ia1 []int64, err error)) *UserRolePrimeDBMock {
	if mmGetUserRoleIDs.defaultExpectation != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.GetUserRoleIDs method")
	}

	if len(mmGetUserRoleIDs.expectations) > 0 {
		mmGetUserRoleIDs.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.GetUserRoleIDs method")
	}

	mmGetUserRoleIDs.mock.funcGetUserRoleIDs = f
	mmGetUserRoleIDs.mock.funcGetUserRoleIDsOrigin = minimock.CallerInfo(1)
	return mmGetUserRoleIDs.mock
}

// When sets expectation for the UserRolePrimeDB.GetUserRoleIDs which will trigger the result defined by the following
// Then helper
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) When(userID int64) *UserRolePrimeDBMockGetUserRoleIDsExpectation {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRoleIDs mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockGetUserRoleIDsExpectation{
		mock:               mmGetUserRoleIDs.mock,
		params:             &UserRolePrimeDBMockGetUserRoleIDsParams{userID},
		expectationOrigins: UserRolePrimeDBMockGetUserRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserRoleIDs.expectations = append(mmGetUserRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.GetUserRoleIDs return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockGetUserRoleIDsExpectation) Then(ia1 []int64, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockGetUserRoleIDsResults{ia1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.GetUserRoleIDs should be invoked
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Times(n uint64) *mUserRolePrimeDBMockGetUserRoleIDs {
	if n == 0 {
		mmGetUserRoleIDs.mock.t.Fatalf("Times of UserRolePrimeDBMock.GetUserRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserRoleIDs.expectedInvocations, n)
	mmGetUserRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserRoleIDs
}

func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) invocationsDone() bool {
	if len(mmGetUserRoleIDs.expectations) == 0 && mmGetUserRoleIDs.defaultExpectation == nil && mmGetUserRoleIDs.mock.funcGetUserRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserRoleIDs.mock.afterGetUserRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserRoleIDs implements mm_repository.UserRolePrimeDB
func (mmGetUserRoleIDs *UserRolePrimeDBMock) GetUserRoleIDs(userID int64) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmGetUserRoleIDs.beforeGetUserRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserRoleIDs.afterGetUserRoleIDsCounter, 1)

	mmGetUserRoleIDs.t.Helper()

	if mmGetUserRoleIDs.inspectFuncGetUserRoleIDs != nil {
		mmGetUserRoleIDs.inspectFuncGetUserRoleIDs(userID)
	}

	mm_params := UserRolePrimeDBMockGetUserRoleIDsParams{userID}

	// Record call args
	mmGetUserRoleIDs.GetUserRoleIDsMock.mutex.Lock()
	mmGetUserRoleIDs.GetUserRoleIDsMock.callArgs = append(mmGetUserRoleIDs.GetUserRoleIDsMock.callArgs, &mm_params)
	mmGetUserRoleIDs.GetUserRoleIDsMock.mutex.Unlock()

	for _, e := range mmGetUserRoleIDs.GetUserRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockGetUserRoleIDsParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUserRoleIDs.t.Errorf("UserRolePrimeDBMock.GetUserRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserRoleIDs.t.Errorf("UserRolePrimeDBMock.GetUserRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserRoleIDs.t.Fatal("No results are set for the UserRolePrimeDBMock.GetUserRoleIDs")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmGetUserRoleIDs.funcGetUserRoleIDs != nil {
		return mmGetUserRoleIDs.funcGetUserRoleIDs(userID)
	}
	mmGetUserRoleIDs.t.Fatalf("Unexpected call to UserRolePrimeDBMock.GetUserRoleIDs. %v", userID)
	return
}

// GetUserRoleIDsAfterCounter returns a count of finished UserRolePrimeDBMock.GetUserRoleIDs invocations
func (mmGetUserRoleIDs *UserRolePrimeDBMock) GetUserRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRoleIDs.afterGetUserRoleIDsCounter)
}

// GetUserRoleIDsBeforeCounter returns a count of UserRolePrimeDBMock.GetUserRoleIDs invocations
func (mmGetUserRoleIDs *UserRolePrimeDBMock) GetUserRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRoleIDs.beforeGetUserRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.GetUserRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserRoleIDs *mUserRolePrimeDBMockGetUserRoleIDs) Calls() []*UserRolePrimeDBMockGetUserRoleIDsParams {
	mmGetUserRoleIDs.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockGetUserRoleIDsParams, len(mmGetUserRoleIDs.callArgs))
	copy(argCopy, mmGetUserRoleIDs.callArgs)

	mmGetUserRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserRoleIDsDone returns true if the count of the GetUserRoleIDs invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockGetUserRoleIDsDone() bool {
	if m.GetUserRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserRoleIDsMock.invocationsDone()
}

// MinimockGetUserRoleIDsInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockGetUserRoleIDsInspect() {
	for _, e := range m.GetUserRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserRoleIDsCounter := mm_atomic.LoadUint64(&m.afterGetUserRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserRoleIDsMock.defaultExpectation != nil && afterGetUserRoleIDsCounter < 1 {
		if m.GetUserRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRoleIDs at\n%s", m.GetUserRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRoleIDs at\n%s with params: %#v", m.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetUserRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserRoleIDs != nil && afterGetUserRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRoleIDs at\n%s", m.funcGetUserRoleIDsOrigin)
	}

	if !m.GetUserRoleIDsMock.invocationsDone() && afterGetUserRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.GetUserRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserRoleIDsMock.expectedInvocations), m.GetUserRoleIDsMock.expectedInvocationsOrigin, afterGetUserRoleIDsCounter)
	}
}

type mUserRolePrimeDBMockGetUserRolesByRoleID struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockGetUserRolesByRoleIDExpectation
	expectations       []*UserRolePrimeDBMockGetUserRolesByRoleIDExpectation

	callArgs []*UserRolePrimeDBMockGetUserRolesByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockGetUserRolesByRoleIDExpectation specifies expectation struct of the UserRolePrimeDB.GetUserRolesByRoleID
type UserRolePrimeDBMockGetUserRolesByRoleIDExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockGetUserRolesByRoleIDParams
	paramPtrs          *UserRolePrimeDBMockGetUserRolesByRoleIDParamPtrs
	expectationOrigins UserRolePrimeDBMockGetUserRolesByRoleIDExpectationOrigins
	results            *UserRolePrimeDBMockGetUserRolesByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockGetUserRolesByRoleIDParams contains parameters of the UserRolePrimeDB.GetUserRolesByRoleID
type UserRolePrimeDBMockGetUserRolesByRoleIDParams struct {
	roleID int64
}

// UserRolePrimeDBMockGetUserRolesByRoleIDParamPtrs contains pointers to parameters of the UserRolePrimeDB.GetUserRolesByRoleID
type UserRolePrimeDBMockGetUserRolesByRoleIDParamPtrs struct {
	roleID *int64
}

// UserRolePrimeDBMockGetUserRolesByRoleIDResults contains results of the UserRolePrimeDB.GetUserRolesByRoleID
type UserRolePrimeDBMockGetUserRolesByRoleIDResults struct {
	ua1 []userentity.UserRole
	err error
}

// UserRolePrimeDBMockGetUserRolesByRoleIDOrigins contains origins of expectations of the UserRolePrimeDB.GetUserRolesByRoleID
type UserRolePrimeDBMockGetUserRolesByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Optional() *mUserRolePrimeDBMockGetUserRolesByRoleID {
	mmGetUserRolesByRoleID.optional = true
	return mmGetUserRolesByRoleID
}

// Expect sets up expected params for UserRolePrimeDB.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Expect(roleID int64) *mUserRolePrimeDBMockGetUserRolesByRoleID {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRolesByRoleID mock is already set by Set")
	}

	if mmGetUserRolesByRoleID.defaultExpectation == nil {
		mmGetUserRolesByRoleID.defaultExpectation = &UserRolePrimeDBMockGetUserRolesByRoleIDExpectation{}
	}

	if mmGetUserRolesByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRolesByRoleID mock is already set by ExpectParams functions")
	}

	mmGetUserRolesByRoleID.defaultExpectation.params = &UserRolePrimeDBMockGetUserRolesByRoleIDParams{roleID}
	mmGetUserRolesByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserRolesByRoleID.expectations {
		if minimock.Equal(e.params, mmGetUserRolesByRoleID.defaultExpectation.params) {
			mmGetUserRolesByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserRolesByRoleID.defaultExpectation.params)
		}
	}

	return mmGetUserRolesByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for UserRolePrimeDB.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) ExpectRoleIDParam1(roleID int64) *mUserRolePrimeDBMockGetUserRolesByRoleID {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRolesByRoleID mock is already set by Set")
	}

	if mmGetUserRolesByRoleID.defaultExpectation == nil {
		mmGetUserRolesByRoleID.defaultExpectation = &UserRolePrimeDBMockGetUserRolesByRoleIDExpectation{}
	}

	if mmGetUserRolesByRoleID.defaultExpectation.params != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRolesByRoleID mock is already set by Expect")
	}

	if mmGetUserRolesByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetUserRolesByRoleID.defaultExpectation.paramPtrs = &UserRolePrimeDBMockGetUserRolesByRoleIDParamPtrs{}
	}
	mmGetUserRolesByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetUserRolesByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetUserRolesByRoleID
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Inspect(f func(roleID int64)) *mUserRolePrimeDBMockGetUserRolesByRoleID {
	if mmGetUserRolesByRoleID.mock.inspectFuncGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.GetUserRolesByRoleID")
	}

	mmGetUserRolesByRoleID.mock.inspectFuncGetUserRolesByRoleID = f

	return mmGetUserRolesByRoleID
}

// Return sets up results that will be returned by UserRolePrimeDB.GetUserRolesByRoleID
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Return(ua1 []userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRolesByRoleID mock is already set by Set")
	}

	if mmGetUserRolesByRoleID.defaultExpectation == nil {
		mmGetUserRolesByRoleID.defaultExpectation = &UserRolePrimeDBMockGetUserRolesByRoleIDExpectation{mock: mmGetUserRolesByRoleID.mock}
	}
	mmGetUserRolesByRoleID.defaultExpectation.results = &UserRolePrimeDBMockGetUserRolesByRoleIDResults{ua1, err}
	mmGetUserRolesByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByRoleID.mock
}

// Set uses given function f to mock the UserRolePrimeDB.GetUserRolesByRoleID method
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Set(f func(roleID int64) (ua1 []userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmGetUserRolesByRoleID.defaultExpectation != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.GetUserRolesByRoleID method")
	}

	if len(mmGetUserRolesByRoleID.expectations) > 0 {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.GetUserRolesByRoleID method")
	}

	mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID = f
	mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByRoleID.mock
}

// When sets expectation for the UserRolePrimeDB.GetUserRolesByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) When(roleID int64) *UserRolePrimeDBMockGetUserRolesByRoleIDExpectation {
	if mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.mock.t.Fatalf("UserRolePrimeDBMock.GetUserRolesByRoleID mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockGetUserRolesByRoleIDExpectation{
		mock:               mmGetUserRolesByRoleID.mock,
		params:             &UserRolePrimeDBMockGetUserRolesByRoleIDParams{roleID},
		expectationOrigins: UserRolePrimeDBMockGetUserRolesByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserRolesByRoleID.expectations = append(mmGetUserRolesByRoleID.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.GetUserRolesByRoleID return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockGetUserRolesByRoleIDExpectation) Then(ua1 []userentity.UserRole, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockGetUserRolesByRoleIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.GetUserRolesByRoleID should be invoked
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Times(n uint64) *mUserRolePrimeDBMockGetUserRolesByRoleID {
	if n == 0 {
		mmGetUserRolesByRoleID.mock.t.Fatalf("Times of UserRolePrimeDBMock.GetUserRolesByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserRolesByRoleID.expectedInvocations, n)
	mmGetUserRolesByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserRolesByRoleID
}

func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) invocationsDone() bool {
	if len(mmGetUserRolesByRoleID.expectations) == 0 && mmGetUserRolesByRoleID.defaultExpectation == nil && mmGetUserRolesByRoleID.mock.funcGetUserRolesByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.mock.afterGetUserRolesByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserRolesByRoleID implements mm_repository.UserRolePrimeDB
func (mmGetUserRolesByRoleID *UserRolePrimeDBMock) GetUserRolesByRoleID(roleID int64) (ua1 []userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmGetUserRolesByRoleID.beforeGetUserRolesByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserRolesByRoleID.afterGetUserRolesByRoleIDCounter, 1)

	mmGetUserRolesByRoleID.t.Helper()

	if mmGetUserRolesByRoleID.inspectFuncGetUserRolesByRoleID != nil {
		mmGetUserRolesByRoleID.inspectFuncGetUserRolesByRoleID(roleID)
	}

	mm_params := UserRolePrimeDBMockGetUserRolesByRoleIDParams{roleID}

	// Record call args
	mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.mutex.Lock()
	mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.callArgs = append(mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.callArgs, &mm_params)
	mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockGetUserRolesByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetUserRolesByRoleID.t.Errorf("UserRolePrimeDBMock.GetUserRolesByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserRolesByRoleID.t.Errorf("UserRolePrimeDBMock.GetUserRolesByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserRolesByRoleID.GetUserRolesByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserRolesByRoleID.t.Fatal("No results are set for the UserRolePrimeDBMock.GetUserRolesByRoleID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetUserRolesByRoleID.funcGetUserRolesByRoleID != nil {
		return mmGetUserRolesByRoleID.funcGetUserRolesByRoleID(roleID)
	}
	mmGetUserRolesByRoleID.t.Fatalf("Unexpected call to UserRolePrimeDBMock.GetUserRolesByRoleID. %v", roleID)
	return
}

// GetUserRolesByRoleIDAfterCounter returns a count of finished UserRolePrimeDBMock.GetUserRolesByRoleID invocations
func (mmGetUserRolesByRoleID *UserRolePrimeDBMock) GetUserRolesByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.afterGetUserRolesByRoleIDCounter)
}

// GetUserRolesByRoleIDBeforeCounter returns a count of UserRolePrimeDBMock.GetUserRolesByRoleID invocations
func (mmGetUserRolesByRoleID *UserRolePrimeDBMock) GetUserRolesByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRolesByRoleID.beforeGetUserRolesByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.GetUserRolesByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserRolesByRoleID *mUserRolePrimeDBMockGetUserRolesByRoleID) Calls() []*UserRolePrimeDBMockGetUserRolesByRoleIDParams {
	mmGetUserRolesByRoleID.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockGetUserRolesByRoleIDParams, len(mmGetUserRolesByRoleID.callArgs))
	copy(argCopy, mmGetUserRolesByRoleID.callArgs)

	mmGetUserRolesByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserRolesByRoleIDDone returns true if the count of the GetUserRolesByRoleID invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockGetUserRolesByRoleIDDone() bool {
	if m.GetUserRolesByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserRolesByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserRolesByRoleIDMock.invocationsDone()
}

// MinimockGetUserRolesByRoleIDInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockGetUserRolesByRoleIDInspect() {
	for _, e := range m.GetUserRolesByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRolesByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserRolesByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetUserRolesByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserRolesByRoleIDMock.defaultExpectation != nil && afterGetUserRolesByRoleIDCounter < 1 {
		if m.GetUserRolesByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRolesByRoleID at\n%s", m.GetUserRolesByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRolesByRoleID at\n%s with params: %#v", m.GetUserRolesByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetUserRolesByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserRolesByRoleID != nil && afterGetUserRolesByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.GetUserRolesByRoleID at\n%s", m.funcGetUserRolesByRoleIDOrigin)
	}

	if !m.GetUserRolesByRoleIDMock.invocationsDone() && afterGetUserRolesByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.GetUserRolesByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserRolesByRoleIDMock.expectedInvocations), m.GetUserRolesByRoleIDMock.expectedInvocationsOrigin, afterGetUserRolesByRoleIDCounter)
	}
}

type mUserRolePrimeDBMockUpdate struct {
	optional           bool
	mock               *UserRolePrimeDBMock
	defaultExpectation *UserRolePrimeDBMockUpdateExpectation
	expectations       []*UserRolePrimeDBMockUpdateExpectation

	callArgs []*UserRolePrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserRolePrimeDBMockUpdateExpectation specifies expectation struct of the UserRolePrimeDB.Update
type UserRolePrimeDBMockUpdateExpectation struct {
	mock               *UserRolePrimeDBMock
	params             *UserRolePrimeDBMockUpdateParams
	paramPtrs          *UserRolePrimeDBMockUpdateParamPtrs
	expectationOrigins UserRolePrimeDBMockUpdateExpectationOrigins
	results            *UserRolePrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// UserRolePrimeDBMockUpdateParams contains parameters of the UserRolePrimeDB.Update
type UserRolePrimeDBMockUpdateParams struct {
	userRole userentity.UserRoleUpdateData
}

// UserRolePrimeDBMockUpdateParamPtrs contains pointers to parameters of the UserRolePrimeDB.Update
type UserRolePrimeDBMockUpdateParamPtrs struct {
	userRole *userentity.UserRoleUpdateData
}

// UserRolePrimeDBMockUpdateResults contains results of the UserRolePrimeDB.Update
type UserRolePrimeDBMockUpdateResults struct {
	u1  userentity.UserRole
	err error
}

// UserRolePrimeDBMockUpdateOrigins contains origins of expectations of the UserRolePrimeDB.Update
type UserRolePrimeDBMockUpdateExpectationOrigins struct {
	origin         string
	originUserRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mUserRolePrimeDBMockUpdate) Optional() *mUserRolePrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for UserRolePrimeDB.Update
func (mmUpdate *mUserRolePrimeDBMockUpdate) Expect(userRole userentity.UserRoleUpdateData) *mUserRolePrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserRolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserRolePrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("UserRolePrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &UserRolePrimeDBMockUpdateParams{userRole}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectUserRoleParam1 sets up expected param userRole for UserRolePrimeDB.Update
func (mmUpdate *mUserRolePrimeDBMockUpdate) ExpectUserRoleParam1(userRole userentity.UserRoleUpdateData) *mUserRolePrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserRolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserRolePrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("UserRolePrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &UserRolePrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.userRole = &userRole
	mmUpdate.defaultExpectation.expectationOrigins.originUserRole = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the UserRolePrimeDB.Update
func (mmUpdate *mUserRolePrimeDBMockUpdate) Inspect(f func(userRole userentity.UserRoleUpdateData)) *mUserRolePrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for UserRolePrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by UserRolePrimeDB.Update
func (mmUpdate *mUserRolePrimeDBMockUpdate) Return(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserRolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserRolePrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &UserRolePrimeDBMockUpdateResults{u1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the UserRolePrimeDB.Update method
func (mmUpdate *mUserRolePrimeDBMockUpdate) Set(f func(userRole userentity.UserRoleUpdateData) (u1 userentity.UserRole, err error)) *UserRolePrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the UserRolePrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the UserRolePrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the UserRolePrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mUserRolePrimeDBMockUpdate) When(userRole userentity.UserRoleUpdateData) *UserRolePrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserRolePrimeDBMock.Update mock is already set by Set")
	}

	expectation := &UserRolePrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &UserRolePrimeDBMockUpdateParams{userRole},
		expectationOrigins: UserRolePrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up UserRolePrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *UserRolePrimeDBMockUpdateExpectation) Then(u1 userentity.UserRole, err error) *UserRolePrimeDBMock {
	e.results = &UserRolePrimeDBMockUpdateResults{u1, err}
	return e.mock
}

// Times sets number of times UserRolePrimeDB.Update should be invoked
func (mmUpdate *mUserRolePrimeDBMockUpdate) Times(n uint64) *mUserRolePrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of UserRolePrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mUserRolePrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.UserRolePrimeDB
func (mmUpdate *UserRolePrimeDBMock) Update(userRole userentity.UserRoleUpdateData) (u1 userentity.UserRole, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(userRole)
	}

	mm_params := UserRolePrimeDBMockUpdateParams{userRole}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := UserRolePrimeDBMockUpdateParams{userRole}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userRole != nil && !minimock.Equal(*mm_want_ptrs.userRole, mm_got.userRole) {
				mmUpdate.t.Errorf("UserRolePrimeDBMock.Update got unexpected parameter userRole, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originUserRole, *mm_want_ptrs.userRole, mm_got.userRole, minimock.Diff(*mm_want_ptrs.userRole, mm_got.userRole))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("UserRolePrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the UserRolePrimeDBMock.Update")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(userRole)
	}
	mmUpdate.t.Fatalf("Unexpected call to UserRolePrimeDBMock.Update. %v", userRole)
	return
}

// UpdateAfterCounter returns a count of finished UserRolePrimeDBMock.Update invocations
func (mmUpdate *UserRolePrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of UserRolePrimeDBMock.Update invocations
func (mmUpdate *UserRolePrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to UserRolePrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mUserRolePrimeDBMockUpdate) Calls() []*UserRolePrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*UserRolePrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *UserRolePrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *UserRolePrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserRolePrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to UserRolePrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserRolePrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *UserRolePrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByRoleIDAndUserIDsInspect()

			m.MinimockCreateByUserIDAndRoleIDsInspect()

			m.MinimockDeleteByRoleIDInspect()

			m.MinimockDeleteByRoleIDAndUserIDsInspect()

			m.MinimockDeleteByUserIDInspect()

			m.MinimockDeleteByUserIDAndRoleIDInspect()

			m.MinimockDeleteByUserIDAndRoleIDsInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetByUserIDAndRoleIDInspect()

			m.MinimockGetUserRoleIDsInspect()

			m.MinimockGetUserRolesByRoleIDInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *UserRolePrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *UserRolePrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByRoleIDAndUserIDsDone() &&
		m.MinimockCreateByUserIDAndRoleIDsDone() &&
		m.MinimockDeleteByRoleIDDone() &&
		m.MinimockDeleteByRoleIDAndUserIDsDone() &&
		m.MinimockDeleteByUserIDDone() &&
		m.MinimockDeleteByUserIDAndRoleIDDone() &&
		m.MinimockDeleteByUserIDAndRoleIDsDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetByUserIDAndRoleIDDone() &&
		m.MinimockGetUserRoleIDsDone() &&
		m.MinimockGetUserRolesByRoleIDDone() &&
		m.MinimockUpdateDone()
}
