// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository.UserGroupPrimeDB -o user_group_prime_db_mock.go -n UserGroupPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"github.com/gojuno/minimock/v3"
)

// UserGroupPrimeDBMock implements mm_repository.UserGroupPrimeDB
type UserGroupPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcAssignUserToGroups          func(userID int64, groupIDs []int64) (err error)
	funcAssignUserToGroupsOrigin    string
	inspectFuncAssignUserToGroups   func(userID int64, groupIDs []int64)
	afterAssignUserToGroupsCounter  uint64
	beforeAssignUserToGroupsCounter uint64
	AssignUserToGroupsMock          mUserGroupPrimeDBMockAssignUserToGroups

	funcCreate          func(ctx context.Context, userGroup userentity.UserGroupCreateData) (u1 userentity.UserGroup, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, userGroup userentity.UserGroupCreateData)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mUserGroupPrimeDBMockCreate

	funcCreateByGroupIDAndUserIDs          func(ctx context.Context, groupID int64, userIDs []int64) (err error)
	funcCreateByGroupIDAndUserIDsOrigin    string
	inspectFuncCreateByGroupIDAndUserIDs   func(ctx context.Context, groupID int64, userIDs []int64)
	afterCreateByGroupIDAndUserIDsCounter  uint64
	beforeCreateByGroupIDAndUserIDsCounter uint64
	CreateByGroupIDAndUserIDsMock          mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs

	funcDeleteByGroupID          func(ctx context.Context, groupID int64) (err error)
	funcDeleteByGroupIDOrigin    string
	inspectFuncDeleteByGroupID   func(ctx context.Context, groupID int64)
	afterDeleteByGroupIDCounter  uint64
	beforeDeleteByGroupIDCounter uint64
	DeleteByGroupIDMock          mUserGroupPrimeDBMockDeleteByGroupID

	funcDeleteByGroupIDAndUserIDs          func(ctx context.Context, groupID int64, userIDs []int64) (err error)
	funcDeleteByGroupIDAndUserIDsOrigin    string
	inspectFuncDeleteByGroupIDAndUserIDs   func(ctx context.Context, groupID int64, userIDs []int64)
	afterDeleteByGroupIDAndUserIDsCounter  uint64
	beforeDeleteByGroupIDAndUserIDsCounter uint64
	DeleteByGroupIDAndUserIDsMock          mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs

	funcDeleteByUserID          func(ctx context.Context, userID int64) (err error)
	funcDeleteByUserIDOrigin    string
	inspectFuncDeleteByUserID   func(ctx context.Context, userID int64)
	afterDeleteByUserIDCounter  uint64
	beforeDeleteByUserIDCounter uint64
	DeleteByUserIDMock          mUserGroupPrimeDBMockDeleteByUserID

	funcDeleteByUserIDAndGroupIDs          func(ctx context.Context, userID int64, groupIDs []int64) (err error)
	funcDeleteByUserIDAndGroupIDsOrigin    string
	inspectFuncDeleteByUserIDAndGroupIDs   func(ctx context.Context, userID int64, groupIDs []int64)
	afterDeleteByUserIDAndGroupIDsCounter  uint64
	beforeDeleteByUserIDAndGroupIDsCounter uint64
	DeleteByUserIDAndGroupIDsMock          mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs

	funcGetByGroupID          func(groupID int64) (ua1 []userentity.UserGroup, err error)
	funcGetByGroupIDOrigin    string
	inspectFuncGetByGroupID   func(groupID int64)
	afterGetByGroupIDCounter  uint64
	beforeGetByGroupIDCounter uint64
	GetByGroupIDMock          mUserGroupPrimeDBMockGetByGroupID

	funcGetByUserID          func(userID int64) (ua1 []userentity.UserGroup, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mUserGroupPrimeDBMockGetByUserID

	funcGetGroupsByIDs          func(groupIDs []int64) (ga1 []groupentity.Group, err error)
	funcGetGroupsByIDsOrigin    string
	inspectFuncGetGroupsByIDs   func(groupIDs []int64)
	afterGetGroupsByIDsCounter  uint64
	beforeGetGroupsByIDsCounter uint64
	GetGroupsByIDsMock          mUserGroupPrimeDBMockGetGroupsByIDs

	funcGetParticipantGroupsByUserID          func(userID int64) (ga1 []groupentity.Group, err error)
	funcGetParticipantGroupsByUserIDOrigin    string
	inspectFuncGetParticipantGroupsByUserID   func(userID int64)
	afterGetParticipantGroupsByUserIDCounter  uint64
	beforeGetParticipantGroupsByUserIDCounter uint64
	GetParticipantGroupsByUserIDMock          mUserGroupPrimeDBMockGetParticipantGroupsByUserID

	funcGetProductsByIDs          func(productIDs []int64) (pa1 []productentity.Product, err error)
	funcGetProductsByIDsOrigin    string
	inspectFuncGetProductsByIDs   func(productIDs []int64)
	afterGetProductsByIDsCounter  uint64
	beforeGetProductsByIDsCounter uint64
	GetProductsByIDsMock          mUserGroupPrimeDBMockGetProductsByIDs

	funcGetUserGroups          func(userID int64) (ia1 []int64, err error)
	funcGetUserGroupsOrigin    string
	inspectFuncGetUserGroups   func(userID int64)
	afterGetUserGroupsCounter  uint64
	beforeGetUserGroupsCounter uint64
	GetUserGroupsMock          mUserGroupPrimeDBMockGetUserGroups
}

// NewUserGroupPrimeDBMock returns a mock for mm_repository.UserGroupPrimeDB
func NewUserGroupPrimeDBMock(t minimock.Tester) *UserGroupPrimeDBMock {
	m := &UserGroupPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.AssignUserToGroupsMock = mUserGroupPrimeDBMockAssignUserToGroups{mock: m}
	m.AssignUserToGroupsMock.callArgs = []*UserGroupPrimeDBMockAssignUserToGroupsParams{}

	m.CreateMock = mUserGroupPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*UserGroupPrimeDBMockCreateParams{}

	m.CreateByGroupIDAndUserIDsMock = mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs{mock: m}
	m.CreateByGroupIDAndUserIDsMock.callArgs = []*UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams{}

	m.DeleteByGroupIDMock = mUserGroupPrimeDBMockDeleteByGroupID{mock: m}
	m.DeleteByGroupIDMock.callArgs = []*UserGroupPrimeDBMockDeleteByGroupIDParams{}

	m.DeleteByGroupIDAndUserIDsMock = mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs{mock: m}
	m.DeleteByGroupIDAndUserIDsMock.callArgs = []*UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams{}

	m.DeleteByUserIDMock = mUserGroupPrimeDBMockDeleteByUserID{mock: m}
	m.DeleteByUserIDMock.callArgs = []*UserGroupPrimeDBMockDeleteByUserIDParams{}

	m.DeleteByUserIDAndGroupIDsMock = mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs{mock: m}
	m.DeleteByUserIDAndGroupIDsMock.callArgs = []*UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams{}

	m.GetByGroupIDMock = mUserGroupPrimeDBMockGetByGroupID{mock: m}
	m.GetByGroupIDMock.callArgs = []*UserGroupPrimeDBMockGetByGroupIDParams{}

	m.GetByUserIDMock = mUserGroupPrimeDBMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*UserGroupPrimeDBMockGetByUserIDParams{}

	m.GetGroupsByIDsMock = mUserGroupPrimeDBMockGetGroupsByIDs{mock: m}
	m.GetGroupsByIDsMock.callArgs = []*UserGroupPrimeDBMockGetGroupsByIDsParams{}

	m.GetParticipantGroupsByUserIDMock = mUserGroupPrimeDBMockGetParticipantGroupsByUserID{mock: m}
	m.GetParticipantGroupsByUserIDMock.callArgs = []*UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams{}

	m.GetProductsByIDsMock = mUserGroupPrimeDBMockGetProductsByIDs{mock: m}
	m.GetProductsByIDsMock.callArgs = []*UserGroupPrimeDBMockGetProductsByIDsParams{}

	m.GetUserGroupsMock = mUserGroupPrimeDBMockGetUserGroups{mock: m}
	m.GetUserGroupsMock.callArgs = []*UserGroupPrimeDBMockGetUserGroupsParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mUserGroupPrimeDBMockAssignUserToGroups struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockAssignUserToGroupsExpectation
	expectations       []*UserGroupPrimeDBMockAssignUserToGroupsExpectation

	callArgs []*UserGroupPrimeDBMockAssignUserToGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockAssignUserToGroupsExpectation specifies expectation struct of the UserGroupPrimeDB.AssignUserToGroups
type UserGroupPrimeDBMockAssignUserToGroupsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockAssignUserToGroupsParams
	paramPtrs          *UserGroupPrimeDBMockAssignUserToGroupsParamPtrs
	expectationOrigins UserGroupPrimeDBMockAssignUserToGroupsExpectationOrigins
	results            *UserGroupPrimeDBMockAssignUserToGroupsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockAssignUserToGroupsParams contains parameters of the UserGroupPrimeDB.AssignUserToGroups
type UserGroupPrimeDBMockAssignUserToGroupsParams struct {
	userID   int64
	groupIDs []int64
}

// UserGroupPrimeDBMockAssignUserToGroupsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.AssignUserToGroups
type UserGroupPrimeDBMockAssignUserToGroupsParamPtrs struct {
	userID   *int64
	groupIDs *[]int64
}

// UserGroupPrimeDBMockAssignUserToGroupsResults contains results of the UserGroupPrimeDB.AssignUserToGroups
type UserGroupPrimeDBMockAssignUserToGroupsResults struct {
	err error
}

// UserGroupPrimeDBMockAssignUserToGroupsOrigins contains origins of expectations of the UserGroupPrimeDB.AssignUserToGroups
type UserGroupPrimeDBMockAssignUserToGroupsExpectationOrigins struct {
	origin         string
	originUserID   string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Optional() *mUserGroupPrimeDBMockAssignUserToGroups {
	mmAssignUserToGroups.optional = true
	return mmAssignUserToGroups
}

// Expect sets up expected params for UserGroupPrimeDB.AssignUserToGroups
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Expect(userID int64, groupIDs []int64) *mUserGroupPrimeDBMockAssignUserToGroups {
	if mmAssignUserToGroups.mock.funcAssignUserToGroups != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Set")
	}

	if mmAssignUserToGroups.defaultExpectation == nil {
		mmAssignUserToGroups.defaultExpectation = &UserGroupPrimeDBMockAssignUserToGroupsExpectation{}
	}

	if mmAssignUserToGroups.defaultExpectation.paramPtrs != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by ExpectParams functions")
	}

	mmAssignUserToGroups.defaultExpectation.params = &UserGroupPrimeDBMockAssignUserToGroupsParams{userID, groupIDs}
	mmAssignUserToGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmAssignUserToGroups.expectations {
		if minimock.Equal(e.params, mmAssignUserToGroups.defaultExpectation.params) {
			mmAssignUserToGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmAssignUserToGroups.defaultExpectation.params)
		}
	}

	return mmAssignUserToGroups
}

// ExpectUserIDParam1 sets up expected param userID for UserGroupPrimeDB.AssignUserToGroups
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) ExpectUserIDParam1(userID int64) *mUserGroupPrimeDBMockAssignUserToGroups {
	if mmAssignUserToGroups.mock.funcAssignUserToGroups != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Set")
	}

	if mmAssignUserToGroups.defaultExpectation == nil {
		mmAssignUserToGroups.defaultExpectation = &UserGroupPrimeDBMockAssignUserToGroupsExpectation{}
	}

	if mmAssignUserToGroups.defaultExpectation.params != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Expect")
	}

	if mmAssignUserToGroups.defaultExpectation.paramPtrs == nil {
		mmAssignUserToGroups.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockAssignUserToGroupsParamPtrs{}
	}
	mmAssignUserToGroups.defaultExpectation.paramPtrs.userID = &userID
	mmAssignUserToGroups.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmAssignUserToGroups
}

// ExpectGroupIDsParam2 sets up expected param groupIDs for UserGroupPrimeDB.AssignUserToGroups
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) ExpectGroupIDsParam2(groupIDs []int64) *mUserGroupPrimeDBMockAssignUserToGroups {
	if mmAssignUserToGroups.mock.funcAssignUserToGroups != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Set")
	}

	if mmAssignUserToGroups.defaultExpectation == nil {
		mmAssignUserToGroups.defaultExpectation = &UserGroupPrimeDBMockAssignUserToGroupsExpectation{}
	}

	if mmAssignUserToGroups.defaultExpectation.params != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Expect")
	}

	if mmAssignUserToGroups.defaultExpectation.paramPtrs == nil {
		mmAssignUserToGroups.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockAssignUserToGroupsParamPtrs{}
	}
	mmAssignUserToGroups.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmAssignUserToGroups.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmAssignUserToGroups
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.AssignUserToGroups
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Inspect(f func(userID int64, groupIDs []int64)) *mUserGroupPrimeDBMockAssignUserToGroups {
	if mmAssignUserToGroups.mock.inspectFuncAssignUserToGroups != nil {
		mmAssignUserToGroups.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.AssignUserToGroups")
	}

	mmAssignUserToGroups.mock.inspectFuncAssignUserToGroups = f

	return mmAssignUserToGroups
}

// Return sets up results that will be returned by UserGroupPrimeDB.AssignUserToGroups
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Return(err error) *UserGroupPrimeDBMock {
	if mmAssignUserToGroups.mock.funcAssignUserToGroups != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Set")
	}

	if mmAssignUserToGroups.defaultExpectation == nil {
		mmAssignUserToGroups.defaultExpectation = &UserGroupPrimeDBMockAssignUserToGroupsExpectation{mock: mmAssignUserToGroups.mock}
	}
	mmAssignUserToGroups.defaultExpectation.results = &UserGroupPrimeDBMockAssignUserToGroupsResults{err}
	mmAssignUserToGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmAssignUserToGroups.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.AssignUserToGroups method
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Set(f func(userID int64, groupIDs []int64) (err error)) *UserGroupPrimeDBMock {
	if mmAssignUserToGroups.defaultExpectation != nil {
		mmAssignUserToGroups.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.AssignUserToGroups method")
	}

	if len(mmAssignUserToGroups.expectations) > 0 {
		mmAssignUserToGroups.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.AssignUserToGroups method")
	}

	mmAssignUserToGroups.mock.funcAssignUserToGroups = f
	mmAssignUserToGroups.mock.funcAssignUserToGroupsOrigin = minimock.CallerInfo(1)
	return mmAssignUserToGroups.mock
}

// When sets expectation for the UserGroupPrimeDB.AssignUserToGroups which will trigger the result defined by the following
// Then helper
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) When(userID int64, groupIDs []int64) *UserGroupPrimeDBMockAssignUserToGroupsExpectation {
	if mmAssignUserToGroups.mock.funcAssignUserToGroups != nil {
		mmAssignUserToGroups.mock.t.Fatalf("UserGroupPrimeDBMock.AssignUserToGroups mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockAssignUserToGroupsExpectation{
		mock:               mmAssignUserToGroups.mock,
		params:             &UserGroupPrimeDBMockAssignUserToGroupsParams{userID, groupIDs},
		expectationOrigins: UserGroupPrimeDBMockAssignUserToGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmAssignUserToGroups.expectations = append(mmAssignUserToGroups.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.AssignUserToGroups return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockAssignUserToGroupsExpectation) Then(err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockAssignUserToGroupsResults{err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.AssignUserToGroups should be invoked
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Times(n uint64) *mUserGroupPrimeDBMockAssignUserToGroups {
	if n == 0 {
		mmAssignUserToGroups.mock.t.Fatalf("Times of UserGroupPrimeDBMock.AssignUserToGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmAssignUserToGroups.expectedInvocations, n)
	mmAssignUserToGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmAssignUserToGroups
}

func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) invocationsDone() bool {
	if len(mmAssignUserToGroups.expectations) == 0 && mmAssignUserToGroups.defaultExpectation == nil && mmAssignUserToGroups.mock.funcAssignUserToGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmAssignUserToGroups.mock.afterAssignUserToGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmAssignUserToGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// AssignUserToGroups implements mm_repository.UserGroupPrimeDB
func (mmAssignUserToGroups *UserGroupPrimeDBMock) AssignUserToGroups(userID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmAssignUserToGroups.beforeAssignUserToGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmAssignUserToGroups.afterAssignUserToGroupsCounter, 1)

	mmAssignUserToGroups.t.Helper()

	if mmAssignUserToGroups.inspectFuncAssignUserToGroups != nil {
		mmAssignUserToGroups.inspectFuncAssignUserToGroups(userID, groupIDs)
	}

	mm_params := UserGroupPrimeDBMockAssignUserToGroupsParams{userID, groupIDs}

	// Record call args
	mmAssignUserToGroups.AssignUserToGroupsMock.mutex.Lock()
	mmAssignUserToGroups.AssignUserToGroupsMock.callArgs = append(mmAssignUserToGroups.AssignUserToGroupsMock.callArgs, &mm_params)
	mmAssignUserToGroups.AssignUserToGroupsMock.mutex.Unlock()

	for _, e := range mmAssignUserToGroups.AssignUserToGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockAssignUserToGroupsParams{userID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmAssignUserToGroups.t.Errorf("UserGroupPrimeDBMock.AssignUserToGroups got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmAssignUserToGroups.t.Errorf("UserGroupPrimeDBMock.AssignUserToGroups got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmAssignUserToGroups.t.Errorf("UserGroupPrimeDBMock.AssignUserToGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmAssignUserToGroups.AssignUserToGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmAssignUserToGroups.t.Fatal("No results are set for the UserGroupPrimeDBMock.AssignUserToGroups")
		}
		return (*mm_results).err
	}
	if mmAssignUserToGroups.funcAssignUserToGroups != nil {
		return mmAssignUserToGroups.funcAssignUserToGroups(userID, groupIDs)
	}
	mmAssignUserToGroups.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.AssignUserToGroups. %v %v", userID, groupIDs)
	return
}

// AssignUserToGroupsAfterCounter returns a count of finished UserGroupPrimeDBMock.AssignUserToGroups invocations
func (mmAssignUserToGroups *UserGroupPrimeDBMock) AssignUserToGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignUserToGroups.afterAssignUserToGroupsCounter)
}

// AssignUserToGroupsBeforeCounter returns a count of UserGroupPrimeDBMock.AssignUserToGroups invocations
func (mmAssignUserToGroups *UserGroupPrimeDBMock) AssignUserToGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignUserToGroups.beforeAssignUserToGroupsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.AssignUserToGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmAssignUserToGroups *mUserGroupPrimeDBMockAssignUserToGroups) Calls() []*UserGroupPrimeDBMockAssignUserToGroupsParams {
	mmAssignUserToGroups.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockAssignUserToGroupsParams, len(mmAssignUserToGroups.callArgs))
	copy(argCopy, mmAssignUserToGroups.callArgs)

	mmAssignUserToGroups.mutex.RUnlock()

	return argCopy
}

// MinimockAssignUserToGroupsDone returns true if the count of the AssignUserToGroups invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockAssignUserToGroupsDone() bool {
	if m.AssignUserToGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.AssignUserToGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.AssignUserToGroupsMock.invocationsDone()
}

// MinimockAssignUserToGroupsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockAssignUserToGroupsInspect() {
	for _, e := range m.AssignUserToGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.AssignUserToGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterAssignUserToGroupsCounter := mm_atomic.LoadUint64(&m.afterAssignUserToGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.AssignUserToGroupsMock.defaultExpectation != nil && afterAssignUserToGroupsCounter < 1 {
		if m.AssignUserToGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.AssignUserToGroups at\n%s", m.AssignUserToGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.AssignUserToGroups at\n%s with params: %#v", m.AssignUserToGroupsMock.defaultExpectation.expectationOrigins.origin, *m.AssignUserToGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcAssignUserToGroups != nil && afterAssignUserToGroupsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.AssignUserToGroups at\n%s", m.funcAssignUserToGroupsOrigin)
	}

	if !m.AssignUserToGroupsMock.invocationsDone() && afterAssignUserToGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.AssignUserToGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.AssignUserToGroupsMock.expectedInvocations), m.AssignUserToGroupsMock.expectedInvocationsOrigin, afterAssignUserToGroupsCounter)
	}
}

type mUserGroupPrimeDBMockCreate struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockCreateExpectation
	expectations       []*UserGroupPrimeDBMockCreateExpectation

	callArgs []*UserGroupPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockCreateExpectation specifies expectation struct of the UserGroupPrimeDB.Create
type UserGroupPrimeDBMockCreateExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockCreateParams
	paramPtrs          *UserGroupPrimeDBMockCreateParamPtrs
	expectationOrigins UserGroupPrimeDBMockCreateExpectationOrigins
	results            *UserGroupPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockCreateParams contains parameters of the UserGroupPrimeDB.Create
type UserGroupPrimeDBMockCreateParams struct {
	ctx       context.Context
	userGroup userentity.UserGroupCreateData
}

// UserGroupPrimeDBMockCreateParamPtrs contains pointers to parameters of the UserGroupPrimeDB.Create
type UserGroupPrimeDBMockCreateParamPtrs struct {
	ctx       *context.Context
	userGroup *userentity.UserGroupCreateData
}

// UserGroupPrimeDBMockCreateResults contains results of the UserGroupPrimeDB.Create
type UserGroupPrimeDBMockCreateResults struct {
	u1  userentity.UserGroup
	err error
}

// UserGroupPrimeDBMockCreateOrigins contains origins of expectations of the UserGroupPrimeDB.Create
type UserGroupPrimeDBMockCreateExpectationOrigins struct {
	origin          string
	originCtx       string
	originUserGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mUserGroupPrimeDBMockCreate) Optional() *mUserGroupPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for UserGroupPrimeDB.Create
func (mmCreate *mUserGroupPrimeDBMockCreate) Expect(ctx context.Context, userGroup userentity.UserGroupCreateData) *mUserGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &UserGroupPrimeDBMockCreateParams{ctx, userGroup}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for UserGroupPrimeDB.Create
func (mmCreate *mUserGroupPrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mUserGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectUserGroupParam2 sets up expected param userGroup for UserGroupPrimeDB.Create
func (mmCreate *mUserGroupPrimeDBMockCreate) ExpectUserGroupParam2(userGroup userentity.UserGroupCreateData) *mUserGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.userGroup = &userGroup
	mmCreate.defaultExpectation.expectationOrigins.originUserGroup = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.Create
func (mmCreate *mUserGroupPrimeDBMockCreate) Inspect(f func(ctx context.Context, userGroup userentity.UserGroupCreateData)) *mUserGroupPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by UserGroupPrimeDB.Create
func (mmCreate *mUserGroupPrimeDBMockCreate) Return(u1 userentity.UserGroup, err error) *UserGroupPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &UserGroupPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &UserGroupPrimeDBMockCreateResults{u1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.Create method
func (mmCreate *mUserGroupPrimeDBMockCreate) Set(f func(ctx context.Context, userGroup userentity.UserGroupCreateData) (u1 userentity.UserGroup, err error)) *UserGroupPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the UserGroupPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mUserGroupPrimeDBMockCreate) When(ctx context.Context, userGroup userentity.UserGroupCreateData) *UserGroupPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("UserGroupPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &UserGroupPrimeDBMockCreateParams{ctx, userGroup},
		expectationOrigins: UserGroupPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockCreateExpectation) Then(u1 userentity.UserGroup, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockCreateResults{u1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.Create should be invoked
func (mmCreate *mUserGroupPrimeDBMockCreate) Times(n uint64) *mUserGroupPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of UserGroupPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mUserGroupPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.UserGroupPrimeDB
func (mmCreate *UserGroupPrimeDBMock) Create(ctx context.Context, userGroup userentity.UserGroupCreateData) (u1 userentity.UserGroup, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, userGroup)
	}

	mm_params := UserGroupPrimeDBMockCreateParams{ctx, userGroup}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockCreateParams{ctx, userGroup}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("UserGroupPrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userGroup != nil && !minimock.Equal(*mm_want_ptrs.userGroup, mm_got.userGroup) {
				mmCreate.t.Errorf("UserGroupPrimeDBMock.Create got unexpected parameter userGroup, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originUserGroup, *mm_want_ptrs.userGroup, mm_got.userGroup, minimock.Diff(*mm_want_ptrs.userGroup, mm_got.userGroup))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("UserGroupPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the UserGroupPrimeDBMock.Create")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, userGroup)
	}
	mmCreate.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.Create. %v %v", ctx, userGroup)
	return
}

// CreateAfterCounter returns a count of finished UserGroupPrimeDBMock.Create invocations
func (mmCreate *UserGroupPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of UserGroupPrimeDBMock.Create invocations
func (mmCreate *UserGroupPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mUserGroupPrimeDBMockCreate) Calls() []*UserGroupPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation
	expectations       []*UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation

	callArgs []*UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation specifies expectation struct of the UserGroupPrimeDB.CreateByGroupIDAndUserIDs
type UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams
	paramPtrs          *UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParamPtrs
	expectationOrigins UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectationOrigins
	results            *UserGroupPrimeDBMockCreateByGroupIDAndUserIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams contains parameters of the UserGroupPrimeDB.CreateByGroupIDAndUserIDs
type UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams struct {
	ctx     context.Context
	groupID int64
	userIDs []int64
}

// UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.CreateByGroupIDAndUserIDs
type UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParamPtrs struct {
	ctx     *context.Context
	groupID *int64
	userIDs *[]int64
}

// UserGroupPrimeDBMockCreateByGroupIDAndUserIDsResults contains results of the UserGroupPrimeDB.CreateByGroupIDAndUserIDs
type UserGroupPrimeDBMockCreateByGroupIDAndUserIDsResults struct {
	err error
}

// UserGroupPrimeDBMockCreateByGroupIDAndUserIDsOrigins contains origins of expectations of the UserGroupPrimeDB.CreateByGroupIDAndUserIDs
type UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
	originUserIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Optional() *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	mmCreateByGroupIDAndUserIDs.optional = true
	return mmCreateByGroupIDAndUserIDs
}

// Expect sets up expected params for UserGroupPrimeDB.CreateByGroupIDAndUserIDs
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Expect(ctx context.Context, groupID int64, userIDs []int64) *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	if mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation{}
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by ExpectParams functions")
	}

	mmCreateByGroupIDAndUserIDs.defaultExpectation.params = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams{ctx, groupID, userIDs}
	mmCreateByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByGroupIDAndUserIDs.expectations {
		if minimock.Equal(e.params, mmCreateByGroupIDAndUserIDs.defaultExpectation.params) {
			mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByGroupIDAndUserIDs.defaultExpectation.params)
		}
	}

	return mmCreateByGroupIDAndUserIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserGroupPrimeDB.CreateByGroupIDAndUserIDs
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) ExpectCtxParam1(ctx context.Context) *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	if mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation{}
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.params != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Expect")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParamPtrs{}
	}
	mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByGroupIDAndUserIDs
}

// ExpectGroupIDParam2 sets up expected param groupID for UserGroupPrimeDB.CreateByGroupIDAndUserIDs
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) ExpectGroupIDParam2(groupID int64) *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	if mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation{}
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.params != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Expect")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParamPtrs{}
	}
	mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs.groupID = &groupID
	mmCreateByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmCreateByGroupIDAndUserIDs
}

// ExpectUserIDsParam3 sets up expected param userIDs for UserGroupPrimeDB.CreateByGroupIDAndUserIDs
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) ExpectUserIDsParam3(userIDs []int64) *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	if mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation{}
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.params != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Expect")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParamPtrs{}
	}
	mmCreateByGroupIDAndUserIDs.defaultExpectation.paramPtrs.userIDs = &userIDs
	mmCreateByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.originUserIDs = minimock.CallerInfo(1)

	return mmCreateByGroupIDAndUserIDs
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.CreateByGroupIDAndUserIDs
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Inspect(f func(ctx context.Context, groupID int64, userIDs []int64)) *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	if mmCreateByGroupIDAndUserIDs.mock.inspectFuncCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs")
	}

	mmCreateByGroupIDAndUserIDs.mock.inspectFuncCreateByGroupIDAndUserIDs = f

	return mmCreateByGroupIDAndUserIDs
}

// Return sets up results that will be returned by UserGroupPrimeDB.CreateByGroupIDAndUserIDs
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Return(err error) *UserGroupPrimeDBMock {
	if mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmCreateByGroupIDAndUserIDs.defaultExpectation == nil {
		mmCreateByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation{mock: mmCreateByGroupIDAndUserIDs.mock}
	}
	mmCreateByGroupIDAndUserIDs.defaultExpectation.results = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsResults{err}
	mmCreateByGroupIDAndUserIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByGroupIDAndUserIDs.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.CreateByGroupIDAndUserIDs method
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Set(f func(ctx context.Context, groupID int64, userIDs []int64) (err error)) *UserGroupPrimeDBMock {
	if mmCreateByGroupIDAndUserIDs.defaultExpectation != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.CreateByGroupIDAndUserIDs method")
	}

	if len(mmCreateByGroupIDAndUserIDs.expectations) > 0 {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.CreateByGroupIDAndUserIDs method")
	}

	mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs = f
	mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByGroupIDAndUserIDs.mock
}

// When sets expectation for the UserGroupPrimeDB.CreateByGroupIDAndUserIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) When(ctx context.Context, groupID int64, userIDs []int64) *UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation {
	if mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation{
		mock:               mmCreateByGroupIDAndUserIDs.mock,
		params:             &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams{ctx, groupID, userIDs},
		expectationOrigins: UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByGroupIDAndUserIDs.expectations = append(mmCreateByGroupIDAndUserIDs.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.CreateByGroupIDAndUserIDs return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockCreateByGroupIDAndUserIDsExpectation) Then(err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockCreateByGroupIDAndUserIDsResults{err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.CreateByGroupIDAndUserIDs should be invoked
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Times(n uint64) *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs {
	if n == 0 {
		mmCreateByGroupIDAndUserIDs.mock.t.Fatalf("Times of UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByGroupIDAndUserIDs.expectedInvocations, n)
	mmCreateByGroupIDAndUserIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByGroupIDAndUserIDs
}

func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) invocationsDone() bool {
	if len(mmCreateByGroupIDAndUserIDs.expectations) == 0 && mmCreateByGroupIDAndUserIDs.defaultExpectation == nil && mmCreateByGroupIDAndUserIDs.mock.funcCreateByGroupIDAndUserIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByGroupIDAndUserIDs.mock.afterCreateByGroupIDAndUserIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByGroupIDAndUserIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByGroupIDAndUserIDs implements mm_repository.UserGroupPrimeDB
func (mmCreateByGroupIDAndUserIDs *UserGroupPrimeDBMock) CreateByGroupIDAndUserIDs(ctx context.Context, groupID int64, userIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByGroupIDAndUserIDs.beforeCreateByGroupIDAndUserIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByGroupIDAndUserIDs.afterCreateByGroupIDAndUserIDsCounter, 1)

	mmCreateByGroupIDAndUserIDs.t.Helper()

	if mmCreateByGroupIDAndUserIDs.inspectFuncCreateByGroupIDAndUserIDs != nil {
		mmCreateByGroupIDAndUserIDs.inspectFuncCreateByGroupIDAndUserIDs(ctx, groupID, userIDs)
	}

	mm_params := UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams{ctx, groupID, userIDs}

	// Record call args
	mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.mutex.Lock()
	mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.callArgs = append(mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.callArgs, &mm_params)
	mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.mutex.Unlock()

	for _, e := range mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams{ctx, groupID, userIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmCreateByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.userIDs != nil && !minimock.Equal(*mm_want_ptrs.userIDs, mm_got.userIDs) {
				mmCreateByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs got unexpected parameter userIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.originUserIDs, *mm_want_ptrs.userIDs, mm_got.userIDs, minimock.Diff(*mm_want_ptrs.userIDs, mm_got.userIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByGroupIDAndUserIDs.CreateByGroupIDAndUserIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByGroupIDAndUserIDs.t.Fatal("No results are set for the UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByGroupIDAndUserIDs.funcCreateByGroupIDAndUserIDs != nil {
		return mmCreateByGroupIDAndUserIDs.funcCreateByGroupIDAndUserIDs(ctx, groupID, userIDs)
	}
	mmCreateByGroupIDAndUserIDs.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs. %v %v %v", ctx, groupID, userIDs)
	return
}

// CreateByGroupIDAndUserIDsAfterCounter returns a count of finished UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs invocations
func (mmCreateByGroupIDAndUserIDs *UserGroupPrimeDBMock) CreateByGroupIDAndUserIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByGroupIDAndUserIDs.afterCreateByGroupIDAndUserIDsCounter)
}

// CreateByGroupIDAndUserIDsBeforeCounter returns a count of UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs invocations
func (mmCreateByGroupIDAndUserIDs *UserGroupPrimeDBMock) CreateByGroupIDAndUserIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByGroupIDAndUserIDs.beforeCreateByGroupIDAndUserIDsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByGroupIDAndUserIDs *mUserGroupPrimeDBMockCreateByGroupIDAndUserIDs) Calls() []*UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams {
	mmCreateByGroupIDAndUserIDs.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockCreateByGroupIDAndUserIDsParams, len(mmCreateByGroupIDAndUserIDs.callArgs))
	copy(argCopy, mmCreateByGroupIDAndUserIDs.callArgs)

	mmCreateByGroupIDAndUserIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByGroupIDAndUserIDsDone returns true if the count of the CreateByGroupIDAndUserIDs invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockCreateByGroupIDAndUserIDsDone() bool {
	if m.CreateByGroupIDAndUserIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByGroupIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByGroupIDAndUserIDsMock.invocationsDone()
}

// MinimockCreateByGroupIDAndUserIDsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockCreateByGroupIDAndUserIDsInspect() {
	for _, e := range m.CreateByGroupIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByGroupIDAndUserIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByGroupIDAndUserIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByGroupIDAndUserIDsMock.defaultExpectation != nil && afterCreateByGroupIDAndUserIDsCounter < 1 {
		if m.CreateByGroupIDAndUserIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs at\n%s", m.CreateByGroupIDAndUserIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs at\n%s with params: %#v", m.CreateByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByGroupIDAndUserIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByGroupIDAndUserIDs != nil && afterCreateByGroupIDAndUserIDsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs at\n%s", m.funcCreateByGroupIDAndUserIDsOrigin)
	}

	if !m.CreateByGroupIDAndUserIDsMock.invocationsDone() && afterCreateByGroupIDAndUserIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.CreateByGroupIDAndUserIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByGroupIDAndUserIDsMock.expectedInvocations), m.CreateByGroupIDAndUserIDsMock.expectedInvocationsOrigin, afterCreateByGroupIDAndUserIDsCounter)
	}
}

type mUserGroupPrimeDBMockDeleteByGroupID struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockDeleteByGroupIDExpectation
	expectations       []*UserGroupPrimeDBMockDeleteByGroupIDExpectation

	callArgs []*UserGroupPrimeDBMockDeleteByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockDeleteByGroupIDExpectation specifies expectation struct of the UserGroupPrimeDB.DeleteByGroupID
type UserGroupPrimeDBMockDeleteByGroupIDExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockDeleteByGroupIDParams
	paramPtrs          *UserGroupPrimeDBMockDeleteByGroupIDParamPtrs
	expectationOrigins UserGroupPrimeDBMockDeleteByGroupIDExpectationOrigins
	results            *UserGroupPrimeDBMockDeleteByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockDeleteByGroupIDParams contains parameters of the UserGroupPrimeDB.DeleteByGroupID
type UserGroupPrimeDBMockDeleteByGroupIDParams struct {
	ctx     context.Context
	groupID int64
}

// UserGroupPrimeDBMockDeleteByGroupIDParamPtrs contains pointers to parameters of the UserGroupPrimeDB.DeleteByGroupID
type UserGroupPrimeDBMockDeleteByGroupIDParamPtrs struct {
	ctx     *context.Context
	groupID *int64
}

// UserGroupPrimeDBMockDeleteByGroupIDResults contains results of the UserGroupPrimeDB.DeleteByGroupID
type UserGroupPrimeDBMockDeleteByGroupIDResults struct {
	err error
}

// UserGroupPrimeDBMockDeleteByGroupIDOrigins contains origins of expectations of the UserGroupPrimeDB.DeleteByGroupID
type UserGroupPrimeDBMockDeleteByGroupIDExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Optional() *mUserGroupPrimeDBMockDeleteByGroupID {
	mmDeleteByGroupID.optional = true
	return mmDeleteByGroupID
}

// Expect sets up expected params for UserGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Expect(ctx context.Context, groupID int64) *mUserGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by ExpectParams functions")
	}

	mmDeleteByGroupID.defaultExpectation.params = &UserGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID}
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByGroupID.expectations {
		if minimock.Equal(e.params, mmDeleteByGroupID.defaultExpectation.params) {
			mmDeleteByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByGroupID.defaultExpectation.params)
		}
	}

	return mmDeleteByGroupID
}

// ExpectCtxParam1 sets up expected param ctx for UserGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) ExpectCtxParam1(ctx context.Context) *mUserGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.params != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Expect")
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByGroupIDParamPtrs{}
	}
	mmDeleteByGroupID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByGroupID
}

// ExpectGroupIDParam2 sets up expected param groupID for UserGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) ExpectGroupIDParam2(groupID int64) *mUserGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDExpectation{}
	}

	if mmDeleteByGroupID.defaultExpectation.params != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Expect")
	}

	if mmDeleteByGroupID.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByGroupIDParamPtrs{}
	}
	mmDeleteByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByGroupID
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Inspect(f func(ctx context.Context, groupID int64)) *mUserGroupPrimeDBMockDeleteByGroupID {
	if mmDeleteByGroupID.mock.inspectFuncDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.DeleteByGroupID")
	}

	mmDeleteByGroupID.mock.inspectFuncDeleteByGroupID = f

	return mmDeleteByGroupID
}

// Return sets up results that will be returned by UserGroupPrimeDB.DeleteByGroupID
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Return(err error) *UserGroupPrimeDBMock {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	if mmDeleteByGroupID.defaultExpectation == nil {
		mmDeleteByGroupID.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDExpectation{mock: mmDeleteByGroupID.mock}
	}
	mmDeleteByGroupID.defaultExpectation.results = &UserGroupPrimeDBMockDeleteByGroupIDResults{err}
	mmDeleteByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.DeleteByGroupID method
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Set(f func(ctx context.Context, groupID int64) (err error)) *UserGroupPrimeDBMock {
	if mmDeleteByGroupID.defaultExpectation != nil {
		mmDeleteByGroupID.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.DeleteByGroupID method")
	}

	if len(mmDeleteByGroupID.expectations) > 0 {
		mmDeleteByGroupID.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.DeleteByGroupID method")
	}

	mmDeleteByGroupID.mock.funcDeleteByGroupID = f
	mmDeleteByGroupID.mock.funcDeleteByGroupIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID.mock
}

// When sets expectation for the UserGroupPrimeDB.DeleteByGroupID which will trigger the result defined by the following
// Then helper
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) When(ctx context.Context, groupID int64) *UserGroupPrimeDBMockDeleteByGroupIDExpectation {
	if mmDeleteByGroupID.mock.funcDeleteByGroupID != nil {
		mmDeleteByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupID mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockDeleteByGroupIDExpectation{
		mock:               mmDeleteByGroupID.mock,
		params:             &UserGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID},
		expectationOrigins: UserGroupPrimeDBMockDeleteByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByGroupID.expectations = append(mmDeleteByGroupID.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.DeleteByGroupID return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockDeleteByGroupIDExpectation) Then(err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockDeleteByGroupIDResults{err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.DeleteByGroupID should be invoked
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Times(n uint64) *mUserGroupPrimeDBMockDeleteByGroupID {
	if n == 0 {
		mmDeleteByGroupID.mock.t.Fatalf("Times of UserGroupPrimeDBMock.DeleteByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByGroupID.expectedInvocations, n)
	mmDeleteByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupID
}

func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) invocationsDone() bool {
	if len(mmDeleteByGroupID.expectations) == 0 && mmDeleteByGroupID.defaultExpectation == nil && mmDeleteByGroupID.mock.funcDeleteByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupID.mock.afterDeleteByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByGroupID implements mm_repository.UserGroupPrimeDB
func (mmDeleteByGroupID *UserGroupPrimeDBMock) DeleteByGroupID(ctx context.Context, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByGroupID.beforeDeleteByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByGroupID.afterDeleteByGroupIDCounter, 1)

	mmDeleteByGroupID.t.Helper()

	if mmDeleteByGroupID.inspectFuncDeleteByGroupID != nil {
		mmDeleteByGroupID.inspectFuncDeleteByGroupID(ctx, groupID)
	}

	mm_params := UserGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID}

	// Record call args
	mmDeleteByGroupID.DeleteByGroupIDMock.mutex.Lock()
	mmDeleteByGroupID.DeleteByGroupIDMock.callArgs = append(mmDeleteByGroupID.DeleteByGroupIDMock.callArgs, &mm_params)
	mmDeleteByGroupID.DeleteByGroupIDMock.mutex.Unlock()

	for _, e := range mmDeleteByGroupID.DeleteByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockDeleteByGroupIDParams{ctx, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByGroupID.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByGroupID.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByGroupID.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByGroupID.DeleteByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByGroupID.t.Fatal("No results are set for the UserGroupPrimeDBMock.DeleteByGroupID")
		}
		return (*mm_results).err
	}
	if mmDeleteByGroupID.funcDeleteByGroupID != nil {
		return mmDeleteByGroupID.funcDeleteByGroupID(ctx, groupID)
	}
	mmDeleteByGroupID.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.DeleteByGroupID. %v %v", ctx, groupID)
	return
}

// DeleteByGroupIDAfterCounter returns a count of finished UserGroupPrimeDBMock.DeleteByGroupID invocations
func (mmDeleteByGroupID *UserGroupPrimeDBMock) DeleteByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupID.afterDeleteByGroupIDCounter)
}

// DeleteByGroupIDBeforeCounter returns a count of UserGroupPrimeDBMock.DeleteByGroupID invocations
func (mmDeleteByGroupID *UserGroupPrimeDBMock) DeleteByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupID.beforeDeleteByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.DeleteByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByGroupID *mUserGroupPrimeDBMockDeleteByGroupID) Calls() []*UserGroupPrimeDBMockDeleteByGroupIDParams {
	mmDeleteByGroupID.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockDeleteByGroupIDParams, len(mmDeleteByGroupID.callArgs))
	copy(argCopy, mmDeleteByGroupID.callArgs)

	mmDeleteByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByGroupIDDone returns true if the count of the DeleteByGroupID invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockDeleteByGroupIDDone() bool {
	if m.DeleteByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByGroupIDMock.invocationsDone()
}

// MinimockDeleteByGroupIDInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockDeleteByGroupIDInspect() {
	for _, e := range m.DeleteByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByGroupIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByGroupIDMock.defaultExpectation != nil && afterDeleteByGroupIDCounter < 1 {
		if m.DeleteByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupID at\n%s", m.DeleteByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupID at\n%s with params: %#v", m.DeleteByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByGroupID != nil && afterDeleteByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupID at\n%s", m.funcDeleteByGroupIDOrigin)
	}

	if !m.DeleteByGroupIDMock.invocationsDone() && afterDeleteByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.DeleteByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByGroupIDMock.expectedInvocations), m.DeleteByGroupIDMock.expectedInvocationsOrigin, afterDeleteByGroupIDCounter)
	}
}

type mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation
	expectations       []*UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation

	callArgs []*UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation specifies expectation struct of the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
type UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams
	paramPtrs          *UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParamPtrs
	expectationOrigins UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectationOrigins
	results            *UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams contains parameters of the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
type UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams struct {
	ctx     context.Context
	groupID int64
	userIDs []int64
}

// UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
type UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParamPtrs struct {
	ctx     *context.Context
	groupID *int64
	userIDs *[]int64
}

// UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsResults contains results of the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
type UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsResults struct {
	err error
}

// UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsOrigins contains origins of expectations of the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
type UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectationOrigins struct {
	origin        string
	originCtx     string
	originGroupID string
	originUserIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Optional() *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	mmDeleteByGroupIDAndUserIDs.optional = true
	return mmDeleteByGroupIDAndUserIDs
}

// Expect sets up expected params for UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Expect(ctx context.Context, groupID int64, userIDs []int64) *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	if mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation{}
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByGroupIDAndUserIDs.defaultExpectation.params = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams{ctx, groupID, userIDs}
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByGroupIDAndUserIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByGroupIDAndUserIDs.defaultExpectation.params) {
			mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByGroupIDAndUserIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByGroupIDAndUserIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) ExpectCtxParam1(ctx context.Context) *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	if mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation{}
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.params != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Expect")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParamPtrs{}
	}
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByGroupIDAndUserIDs
}

// ExpectGroupIDParam2 sets up expected param groupID for UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) ExpectGroupIDParam2(groupID int64) *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	if mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation{}
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.params != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Expect")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParamPtrs{}
	}
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs.groupID = &groupID
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDeleteByGroupIDAndUserIDs
}

// ExpectUserIDsParam3 sets up expected param userIDs for UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) ExpectUserIDsParam3(userIDs []int64) *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	if mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation{}
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.params != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Expect")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParamPtrs{}
	}
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.paramPtrs.userIDs = &userIDs
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.expectationOrigins.originUserIDs = minimock.CallerInfo(1)

	return mmDeleteByGroupIDAndUserIDs
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Inspect(f func(ctx context.Context, groupID int64, userIDs []int64)) *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	if mmDeleteByGroupIDAndUserIDs.mock.inspectFuncDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs")
	}

	mmDeleteByGroupIDAndUserIDs.mock.inspectFuncDeleteByGroupIDAndUserIDs = f

	return mmDeleteByGroupIDAndUserIDs
}

// Return sets up results that will be returned by UserGroupPrimeDB.DeleteByGroupIDAndUserIDs
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Return(err error) *UserGroupPrimeDBMock {
	if mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Set")
	}

	if mmDeleteByGroupIDAndUserIDs.defaultExpectation == nil {
		mmDeleteByGroupIDAndUserIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation{mock: mmDeleteByGroupIDAndUserIDs.mock}
	}
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.results = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsResults{err}
	mmDeleteByGroupIDAndUserIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupIDAndUserIDs.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs method
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Set(f func(ctx context.Context, groupID int64, userIDs []int64) (err error)) *UserGroupPrimeDBMock {
	if mmDeleteByGroupIDAndUserIDs.defaultExpectation != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs method")
	}

	if len(mmDeleteByGroupIDAndUserIDs.expectations) > 0 {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs method")
	}

	mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs = f
	mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupIDAndUserIDs.mock
}

// When sets expectation for the UserGroupPrimeDB.DeleteByGroupIDAndUserIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) When(ctx context.Context, groupID int64, userIDs []int64) *UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation {
	if mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation{
		mock:               mmDeleteByGroupIDAndUserIDs.mock,
		params:             &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams{ctx, groupID, userIDs},
		expectationOrigins: UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByGroupIDAndUserIDs.expectations = append(mmDeleteByGroupIDAndUserIDs.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.DeleteByGroupIDAndUserIDs return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsExpectation) Then(err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsResults{err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.DeleteByGroupIDAndUserIDs should be invoked
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Times(n uint64) *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs {
	if n == 0 {
		mmDeleteByGroupIDAndUserIDs.mock.t.Fatalf("Times of UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByGroupIDAndUserIDs.expectedInvocations, n)
	mmDeleteByGroupIDAndUserIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByGroupIDAndUserIDs
}

func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) invocationsDone() bool {
	if len(mmDeleteByGroupIDAndUserIDs.expectations) == 0 && mmDeleteByGroupIDAndUserIDs.defaultExpectation == nil && mmDeleteByGroupIDAndUserIDs.mock.funcDeleteByGroupIDAndUserIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupIDAndUserIDs.mock.afterDeleteByGroupIDAndUserIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByGroupIDAndUserIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByGroupIDAndUserIDs implements mm_repository.UserGroupPrimeDB
func (mmDeleteByGroupIDAndUserIDs *UserGroupPrimeDBMock) DeleteByGroupIDAndUserIDs(ctx context.Context, groupID int64, userIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByGroupIDAndUserIDs.beforeDeleteByGroupIDAndUserIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByGroupIDAndUserIDs.afterDeleteByGroupIDAndUserIDsCounter, 1)

	mmDeleteByGroupIDAndUserIDs.t.Helper()

	if mmDeleteByGroupIDAndUserIDs.inspectFuncDeleteByGroupIDAndUserIDs != nil {
		mmDeleteByGroupIDAndUserIDs.inspectFuncDeleteByGroupIDAndUserIDs(ctx, groupID, userIDs)
	}

	mm_params := UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams{ctx, groupID, userIDs}

	// Record call args
	mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.mutex.Lock()
	mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.callArgs = append(mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.callArgs, &mm_params)
	mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams{ctx, groupID, userIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDeleteByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

			if mm_want_ptrs.userIDs != nil && !minimock.Equal(*mm_want_ptrs.userIDs, mm_got.userIDs) {
				mmDeleteByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs got unexpected parameter userIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.originUserIDs, *mm_want_ptrs.userIDs, mm_got.userIDs, minimock.Diff(*mm_want_ptrs.userIDs, mm_got.userIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByGroupIDAndUserIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByGroupIDAndUserIDs.DeleteByGroupIDAndUserIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByGroupIDAndUserIDs.t.Fatal("No results are set for the UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByGroupIDAndUserIDs.funcDeleteByGroupIDAndUserIDs != nil {
		return mmDeleteByGroupIDAndUserIDs.funcDeleteByGroupIDAndUserIDs(ctx, groupID, userIDs)
	}
	mmDeleteByGroupIDAndUserIDs.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs. %v %v %v", ctx, groupID, userIDs)
	return
}

// DeleteByGroupIDAndUserIDsAfterCounter returns a count of finished UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs invocations
func (mmDeleteByGroupIDAndUserIDs *UserGroupPrimeDBMock) DeleteByGroupIDAndUserIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupIDAndUserIDs.afterDeleteByGroupIDAndUserIDsCounter)
}

// DeleteByGroupIDAndUserIDsBeforeCounter returns a count of UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs invocations
func (mmDeleteByGroupIDAndUserIDs *UserGroupPrimeDBMock) DeleteByGroupIDAndUserIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByGroupIDAndUserIDs.beforeDeleteByGroupIDAndUserIDsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByGroupIDAndUserIDs *mUserGroupPrimeDBMockDeleteByGroupIDAndUserIDs) Calls() []*UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams {
	mmDeleteByGroupIDAndUserIDs.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockDeleteByGroupIDAndUserIDsParams, len(mmDeleteByGroupIDAndUserIDs.callArgs))
	copy(argCopy, mmDeleteByGroupIDAndUserIDs.callArgs)

	mmDeleteByGroupIDAndUserIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByGroupIDAndUserIDsDone returns true if the count of the DeleteByGroupIDAndUserIDs invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockDeleteByGroupIDAndUserIDsDone() bool {
	if m.DeleteByGroupIDAndUserIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByGroupIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByGroupIDAndUserIDsMock.invocationsDone()
}

// MinimockDeleteByGroupIDAndUserIDsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockDeleteByGroupIDAndUserIDsInspect() {
	for _, e := range m.DeleteByGroupIDAndUserIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByGroupIDAndUserIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByGroupIDAndUserIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByGroupIDAndUserIDsMock.defaultExpectation != nil && afterDeleteByGroupIDAndUserIDsCounter < 1 {
		if m.DeleteByGroupIDAndUserIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs at\n%s", m.DeleteByGroupIDAndUserIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs at\n%s with params: %#v", m.DeleteByGroupIDAndUserIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByGroupIDAndUserIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByGroupIDAndUserIDs != nil && afterDeleteByGroupIDAndUserIDsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs at\n%s", m.funcDeleteByGroupIDAndUserIDsOrigin)
	}

	if !m.DeleteByGroupIDAndUserIDsMock.invocationsDone() && afterDeleteByGroupIDAndUserIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.DeleteByGroupIDAndUserIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByGroupIDAndUserIDsMock.expectedInvocations), m.DeleteByGroupIDAndUserIDsMock.expectedInvocationsOrigin, afterDeleteByGroupIDAndUserIDsCounter)
	}
}

type mUserGroupPrimeDBMockDeleteByUserID struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockDeleteByUserIDExpectation
	expectations       []*UserGroupPrimeDBMockDeleteByUserIDExpectation

	callArgs []*UserGroupPrimeDBMockDeleteByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockDeleteByUserIDExpectation specifies expectation struct of the UserGroupPrimeDB.DeleteByUserID
type UserGroupPrimeDBMockDeleteByUserIDExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockDeleteByUserIDParams
	paramPtrs          *UserGroupPrimeDBMockDeleteByUserIDParamPtrs
	expectationOrigins UserGroupPrimeDBMockDeleteByUserIDExpectationOrigins
	results            *UserGroupPrimeDBMockDeleteByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockDeleteByUserIDParams contains parameters of the UserGroupPrimeDB.DeleteByUserID
type UserGroupPrimeDBMockDeleteByUserIDParams struct {
	ctx    context.Context
	userID int64
}

// UserGroupPrimeDBMockDeleteByUserIDParamPtrs contains pointers to parameters of the UserGroupPrimeDB.DeleteByUserID
type UserGroupPrimeDBMockDeleteByUserIDParamPtrs struct {
	ctx    *context.Context
	userID *int64
}

// UserGroupPrimeDBMockDeleteByUserIDResults contains results of the UserGroupPrimeDB.DeleteByUserID
type UserGroupPrimeDBMockDeleteByUserIDResults struct {
	err error
}

// UserGroupPrimeDBMockDeleteByUserIDOrigins contains origins of expectations of the UserGroupPrimeDB.DeleteByUserID
type UserGroupPrimeDBMockDeleteByUserIDExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Optional() *mUserGroupPrimeDBMockDeleteByUserID {
	mmDeleteByUserID.optional = true
	return mmDeleteByUserID
}

// Expect sets up expected params for UserGroupPrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Expect(ctx context.Context, userID int64) *mUserGroupPrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by ExpectParams functions")
	}

	mmDeleteByUserID.defaultExpectation.params = &UserGroupPrimeDBMockDeleteByUserIDParams{ctx, userID}
	mmDeleteByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserID.expectations {
		if minimock.Equal(e.params, mmDeleteByUserID.defaultExpectation.params) {
			mmDeleteByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserID.defaultExpectation.params)
		}
	}

	return mmDeleteByUserID
}

// ExpectCtxParam1 sets up expected param ctx for UserGroupPrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) ExpectCtxParam1(ctx context.Context) *mUserGroupPrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.params != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Expect")
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByUserIDParamPtrs{}
	}
	mmDeleteByUserID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserID
}

// ExpectUserIDParam2 sets up expected param userID for UserGroupPrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) ExpectUserIDParam2(userID int64) *mUserGroupPrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDExpectation{}
	}

	if mmDeleteByUserID.defaultExpectation.params != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Expect")
	}

	if mmDeleteByUserID.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByUserIDParamPtrs{}
	}
	mmDeleteByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Inspect(f func(ctx context.Context, userID int64)) *mUserGroupPrimeDBMockDeleteByUserID {
	if mmDeleteByUserID.mock.inspectFuncDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.DeleteByUserID")
	}

	mmDeleteByUserID.mock.inspectFuncDeleteByUserID = f

	return mmDeleteByUserID
}

// Return sets up results that will be returned by UserGroupPrimeDB.DeleteByUserID
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Return(err error) *UserGroupPrimeDBMock {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	if mmDeleteByUserID.defaultExpectation == nil {
		mmDeleteByUserID.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDExpectation{mock: mmDeleteByUserID.mock}
	}
	mmDeleteByUserID.defaultExpectation.results = &UserGroupPrimeDBMockDeleteByUserIDResults{err}
	mmDeleteByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.DeleteByUserID method
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Set(f func(ctx context.Context, userID int64) (err error)) *UserGroupPrimeDBMock {
	if mmDeleteByUserID.defaultExpectation != nil {
		mmDeleteByUserID.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.DeleteByUserID method")
	}

	if len(mmDeleteByUserID.expectations) > 0 {
		mmDeleteByUserID.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.DeleteByUserID method")
	}

	mmDeleteByUserID.mock.funcDeleteByUserID = f
	mmDeleteByUserID.mock.funcDeleteByUserIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID.mock
}

// When sets expectation for the UserGroupPrimeDB.DeleteByUserID which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) When(ctx context.Context, userID int64) *UserGroupPrimeDBMockDeleteByUserIDExpectation {
	if mmDeleteByUserID.mock.funcDeleteByUserID != nil {
		mmDeleteByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserID mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockDeleteByUserIDExpectation{
		mock:               mmDeleteByUserID.mock,
		params:             &UserGroupPrimeDBMockDeleteByUserIDParams{ctx, userID},
		expectationOrigins: UserGroupPrimeDBMockDeleteByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserID.expectations = append(mmDeleteByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.DeleteByUserID return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockDeleteByUserIDExpectation) Then(err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockDeleteByUserIDResults{err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.DeleteByUserID should be invoked
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Times(n uint64) *mUserGroupPrimeDBMockDeleteByUserID {
	if n == 0 {
		mmDeleteByUserID.mock.t.Fatalf("Times of UserGroupPrimeDBMock.DeleteByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserID.expectedInvocations, n)
	mmDeleteByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserID
}

func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) invocationsDone() bool {
	if len(mmDeleteByUserID.expectations) == 0 && mmDeleteByUserID.defaultExpectation == nil && mmDeleteByUserID.mock.funcDeleteByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserID.mock.afterDeleteByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserID implements mm_repository.UserGroupPrimeDB
func (mmDeleteByUserID *UserGroupPrimeDBMock) DeleteByUserID(ctx context.Context, userID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserID.beforeDeleteByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserID.afterDeleteByUserIDCounter, 1)

	mmDeleteByUserID.t.Helper()

	if mmDeleteByUserID.inspectFuncDeleteByUserID != nil {
		mmDeleteByUserID.inspectFuncDeleteByUserID(ctx, userID)
	}

	mm_params := UserGroupPrimeDBMockDeleteByUserIDParams{ctx, userID}

	// Record call args
	mmDeleteByUserID.DeleteByUserIDMock.mutex.Lock()
	mmDeleteByUserID.DeleteByUserIDMock.callArgs = append(mmDeleteByUserID.DeleteByUserIDMock.callArgs, &mm_params)
	mmDeleteByUserID.DeleteByUserIDMock.mutex.Unlock()

	for _, e := range mmDeleteByUserID.DeleteByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockDeleteByUserIDParams{ctx, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserID.t.Errorf("UserGroupPrimeDBMock.DeleteByUserID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserID.t.Errorf("UserGroupPrimeDBMock.DeleteByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserID.t.Errorf("UserGroupPrimeDBMock.DeleteByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserID.DeleteByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserID.t.Fatal("No results are set for the UserGroupPrimeDBMock.DeleteByUserID")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserID.funcDeleteByUserID != nil {
		return mmDeleteByUserID.funcDeleteByUserID(ctx, userID)
	}
	mmDeleteByUserID.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.DeleteByUserID. %v %v", ctx, userID)
	return
}

// DeleteByUserIDAfterCounter returns a count of finished UserGroupPrimeDBMock.DeleteByUserID invocations
func (mmDeleteByUserID *UserGroupPrimeDBMock) DeleteByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserID.afterDeleteByUserIDCounter)
}

// DeleteByUserIDBeforeCounter returns a count of UserGroupPrimeDBMock.DeleteByUserID invocations
func (mmDeleteByUserID *UserGroupPrimeDBMock) DeleteByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserID.beforeDeleteByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.DeleteByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserID *mUserGroupPrimeDBMockDeleteByUserID) Calls() []*UserGroupPrimeDBMockDeleteByUserIDParams {
	mmDeleteByUserID.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockDeleteByUserIDParams, len(mmDeleteByUserID.callArgs))
	copy(argCopy, mmDeleteByUserID.callArgs)

	mmDeleteByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDDone returns true if the count of the DeleteByUserID invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockDeleteByUserIDDone() bool {
	if m.DeleteByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDMock.invocationsDone()
}

// MinimockDeleteByUserIDInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockDeleteByUserIDInspect() {
	for _, e := range m.DeleteByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDMock.defaultExpectation != nil && afterDeleteByUserIDCounter < 1 {
		if m.DeleteByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserID at\n%s", m.DeleteByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserID at\n%s with params: %#v", m.DeleteByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserID != nil && afterDeleteByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserID at\n%s", m.funcDeleteByUserIDOrigin)
	}

	if !m.DeleteByUserIDMock.invocationsDone() && afterDeleteByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.DeleteByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDMock.expectedInvocations), m.DeleteByUserIDMock.expectedInvocationsOrigin, afterDeleteByUserIDCounter)
	}
}

type mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation
	expectations       []*UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation

	callArgs []*UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation specifies expectation struct of the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
type UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams
	paramPtrs          *UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs
	expectationOrigins UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectationOrigins
	results            *UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams contains parameters of the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
type UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams struct {
	ctx      context.Context
	userID   int64
	groupIDs []int64
}

// UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
type UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs struct {
	ctx      *context.Context
	userID   *int64
	groupIDs *[]int64
}

// UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsResults contains results of the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
type UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsResults struct {
	err error
}

// UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsOrigins contains origins of expectations of the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
type UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectationOrigins struct {
	origin         string
	originCtx      string
	originUserID   string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Optional() *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	mmDeleteByUserIDAndGroupIDs.optional = true
	return mmDeleteByUserIDAndGroupIDs
}

// Expect sets up expected params for UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Expect(ctx context.Context, userID int64, groupIDs []int64) *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByUserIDAndGroupIDs.defaultExpectation.params = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByUserIDAndGroupIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByUserIDAndGroupIDs.defaultExpectation.params) {
			mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByUserIDAndGroupIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectCtxParam1 sets up expected param ctx for UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) ExpectCtxParam1(ctx context.Context) *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectUserIDParam2 sets up expected param userID for UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) ExpectUserIDParam2(userID int64) *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.userID = &userID
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// ExpectGroupIDsParam3 sets up expected param groupIDs for UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) ExpectGroupIDsParam3(groupIDs []int64) *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{}
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.params != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Expect")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParamPtrs{}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmDeleteByUserIDAndGroupIDs
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Inspect(f func(ctx context.Context, userID int64, groupIDs []int64)) *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	if mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs")
	}

	mmDeleteByUserIDAndGroupIDs.mock.inspectFuncDeleteByUserIDAndGroupIDs = f

	return mmDeleteByUserIDAndGroupIDs
}

// Return sets up results that will be returned by UserGroupPrimeDB.DeleteByUserIDAndGroupIDs
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Return(err error) *UserGroupPrimeDBMock {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	if mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil {
		mmDeleteByUserIDAndGroupIDs.defaultExpectation = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{mock: mmDeleteByUserIDAndGroupIDs.mock}
	}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.results = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsResults{err}
	mmDeleteByUserIDAndGroupIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs method
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Set(f func(ctx context.Context, userID int64, groupIDs []int64) (err error)) *UserGroupPrimeDBMock {
	if mmDeleteByUserIDAndGroupIDs.defaultExpectation != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs method")
	}

	if len(mmDeleteByUserIDAndGroupIDs.expectations) > 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs method")
	}

	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs = f
	mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs.mock
}

// When sets expectation for the UserGroupPrimeDB.DeleteByUserIDAndGroupIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) When(ctx context.Context, userID int64, groupIDs []int64) *UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation {
	if mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation{
		mock:               mmDeleteByUserIDAndGroupIDs.mock,
		params:             &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs},
		expectationOrigins: UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByUserIDAndGroupIDs.expectations = append(mmDeleteByUserIDAndGroupIDs.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.DeleteByUserIDAndGroupIDs return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsExpectation) Then(err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsResults{err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.DeleteByUserIDAndGroupIDs should be invoked
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Times(n uint64) *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs {
	if n == 0 {
		mmDeleteByUserIDAndGroupIDs.mock.t.Fatalf("Times of UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations, n)
	mmDeleteByUserIDAndGroupIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByUserIDAndGroupIDs
}

func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) invocationsDone() bool {
	if len(mmDeleteByUserIDAndGroupIDs.expectations) == 0 && mmDeleteByUserIDAndGroupIDs.defaultExpectation == nil && mmDeleteByUserIDAndGroupIDs.mock.funcDeleteByUserIDAndGroupIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.mock.afterDeleteByUserIDAndGroupIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByUserIDAndGroupIDs implements mm_repository.UserGroupPrimeDB
func (mmDeleteByUserIDAndGroupIDs *UserGroupPrimeDBMock) DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter, 1)

	mmDeleteByUserIDAndGroupIDs.t.Helper()

	if mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs != nil {
		mmDeleteByUserIDAndGroupIDs.inspectFuncDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}

	mm_params := UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

	// Record call args
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Lock()
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs = append(mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.callArgs, &mm_params)
	mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams{ctx, userID, groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmDeleteByUserIDAndGroupIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByUserIDAndGroupIDs.t.Errorf("UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByUserIDAndGroupIDs.DeleteByUserIDAndGroupIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByUserIDAndGroupIDs.t.Fatal("No results are set for the UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs != nil {
		return mmDeleteByUserIDAndGroupIDs.funcDeleteByUserIDAndGroupIDs(ctx, userID, groupIDs)
	}
	mmDeleteByUserIDAndGroupIDs.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs. %v %v %v", ctx, userID, groupIDs)
	return
}

// DeleteByUserIDAndGroupIDsAfterCounter returns a count of finished UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *UserGroupPrimeDBMock) DeleteByUserIDAndGroupIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.afterDeleteByUserIDAndGroupIDsCounter)
}

// DeleteByUserIDAndGroupIDsBeforeCounter returns a count of UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs invocations
func (mmDeleteByUserIDAndGroupIDs *UserGroupPrimeDBMock) DeleteByUserIDAndGroupIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByUserIDAndGroupIDs.beforeDeleteByUserIDAndGroupIDsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByUserIDAndGroupIDs *mUserGroupPrimeDBMockDeleteByUserIDAndGroupIDs) Calls() []*UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams {
	mmDeleteByUserIDAndGroupIDs.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockDeleteByUserIDAndGroupIDsParams, len(mmDeleteByUserIDAndGroupIDs.callArgs))
	copy(argCopy, mmDeleteByUserIDAndGroupIDs.callArgs)

	mmDeleteByUserIDAndGroupIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByUserIDAndGroupIDsDone returns true if the count of the DeleteByUserIDAndGroupIDs invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockDeleteByUserIDAndGroupIDsDone() bool {
	if m.DeleteByUserIDAndGroupIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByUserIDAndGroupIDsMock.invocationsDone()
}

// MinimockDeleteByUserIDAndGroupIDsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockDeleteByUserIDAndGroupIDsInspect() {
	for _, e := range m.DeleteByUserIDAndGroupIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByUserIDAndGroupIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByUserIDAndGroupIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		if m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s with params: %#v", m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByUserIDAndGroupIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByUserIDAndGroupIDs != nil && afterDeleteByUserIDAndGroupIDsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s", m.funcDeleteByUserIDAndGroupIDsOrigin)
	}

	if !m.DeleteByUserIDAndGroupIDsMock.invocationsDone() && afterDeleteByUserIDAndGroupIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.DeleteByUserIDAndGroupIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByUserIDAndGroupIDsMock.expectedInvocations), m.DeleteByUserIDAndGroupIDsMock.expectedInvocationsOrigin, afterDeleteByUserIDAndGroupIDsCounter)
	}
}

type mUserGroupPrimeDBMockGetByGroupID struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockGetByGroupIDExpectation
	expectations       []*UserGroupPrimeDBMockGetByGroupIDExpectation

	callArgs []*UserGroupPrimeDBMockGetByGroupIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockGetByGroupIDExpectation specifies expectation struct of the UserGroupPrimeDB.GetByGroupID
type UserGroupPrimeDBMockGetByGroupIDExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockGetByGroupIDParams
	paramPtrs          *UserGroupPrimeDBMockGetByGroupIDParamPtrs
	expectationOrigins UserGroupPrimeDBMockGetByGroupIDExpectationOrigins
	results            *UserGroupPrimeDBMockGetByGroupIDResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockGetByGroupIDParams contains parameters of the UserGroupPrimeDB.GetByGroupID
type UserGroupPrimeDBMockGetByGroupIDParams struct {
	groupID int64
}

// UserGroupPrimeDBMockGetByGroupIDParamPtrs contains pointers to parameters of the UserGroupPrimeDB.GetByGroupID
type UserGroupPrimeDBMockGetByGroupIDParamPtrs struct {
	groupID *int64
}

// UserGroupPrimeDBMockGetByGroupIDResults contains results of the UserGroupPrimeDB.GetByGroupID
type UserGroupPrimeDBMockGetByGroupIDResults struct {
	ua1 []userentity.UserGroup
	err error
}

// UserGroupPrimeDBMockGetByGroupIDOrigins contains origins of expectations of the UserGroupPrimeDB.GetByGroupID
type UserGroupPrimeDBMockGetByGroupIDExpectationOrigins struct {
	origin        string
	originGroupID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Optional() *mUserGroupPrimeDBMockGetByGroupID {
	mmGetByGroupID.optional = true
	return mmGetByGroupID
}

// Expect sets up expected params for UserGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Expect(groupID int64) *mUserGroupPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &UserGroupPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs != nil {
		mmGetByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByGroupID mock is already set by ExpectParams functions")
	}

	mmGetByGroupID.defaultExpectation.params = &UserGroupPrimeDBMockGetByGroupIDParams{groupID}
	mmGetByGroupID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByGroupID.expectations {
		if minimock.Equal(e.params, mmGetByGroupID.defaultExpectation.params) {
			mmGetByGroupID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByGroupID.defaultExpectation.params)
		}
	}

	return mmGetByGroupID
}

// ExpectGroupIDParam1 sets up expected param groupID for UserGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) ExpectGroupIDParam1(groupID int64) *mUserGroupPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &UserGroupPrimeDBMockGetByGroupIDExpectation{}
	}

	if mmGetByGroupID.defaultExpectation.params != nil {
		mmGetByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByGroupID mock is already set by Expect")
	}

	if mmGetByGroupID.defaultExpectation.paramPtrs == nil {
		mmGetByGroupID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockGetByGroupIDParamPtrs{}
	}
	mmGetByGroupID.defaultExpectation.paramPtrs.groupID = &groupID
	mmGetByGroupID.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmGetByGroupID
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Inspect(f func(groupID int64)) *mUserGroupPrimeDBMockGetByGroupID {
	if mmGetByGroupID.mock.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.GetByGroupID")
	}

	mmGetByGroupID.mock.inspectFuncGetByGroupID = f

	return mmGetByGroupID
}

// Return sets up results that will be returned by UserGroupPrimeDB.GetByGroupID
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Return(ua1 []userentity.UserGroup, err error) *UserGroupPrimeDBMock {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	if mmGetByGroupID.defaultExpectation == nil {
		mmGetByGroupID.defaultExpectation = &UserGroupPrimeDBMockGetByGroupIDExpectation{mock: mmGetByGroupID.mock}
	}
	mmGetByGroupID.defaultExpectation.results = &UserGroupPrimeDBMockGetByGroupIDResults{ua1, err}
	mmGetByGroupID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.GetByGroupID method
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Set(f func(groupID int64) (ua1 []userentity.UserGroup, err error)) *UserGroupPrimeDBMock {
	if mmGetByGroupID.defaultExpectation != nil {
		mmGetByGroupID.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.GetByGroupID method")
	}

	if len(mmGetByGroupID.expectations) > 0 {
		mmGetByGroupID.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.GetByGroupID method")
	}

	mmGetByGroupID.mock.funcGetByGroupID = f
	mmGetByGroupID.mock.funcGetByGroupIDOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID.mock
}

// When sets expectation for the UserGroupPrimeDB.GetByGroupID which will trigger the result defined by the following
// Then helper
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) When(groupID int64) *UserGroupPrimeDBMockGetByGroupIDExpectation {
	if mmGetByGroupID.mock.funcGetByGroupID != nil {
		mmGetByGroupID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByGroupID mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockGetByGroupIDExpectation{
		mock:               mmGetByGroupID.mock,
		params:             &UserGroupPrimeDBMockGetByGroupIDParams{groupID},
		expectationOrigins: UserGroupPrimeDBMockGetByGroupIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByGroupID.expectations = append(mmGetByGroupID.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.GetByGroupID return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockGetByGroupIDExpectation) Then(ua1 []userentity.UserGroup, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockGetByGroupIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.GetByGroupID should be invoked
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Times(n uint64) *mUserGroupPrimeDBMockGetByGroupID {
	if n == 0 {
		mmGetByGroupID.mock.t.Fatalf("Times of UserGroupPrimeDBMock.GetByGroupID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByGroupID.expectedInvocations, n)
	mmGetByGroupID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByGroupID
}

func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) invocationsDone() bool {
	if len(mmGetByGroupID.expectations) == 0 && mmGetByGroupID.defaultExpectation == nil && mmGetByGroupID.mock.funcGetByGroupID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.mock.afterGetByGroupIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByGroupID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByGroupID implements mm_repository.UserGroupPrimeDB
func (mmGetByGroupID *UserGroupPrimeDBMock) GetByGroupID(groupID int64) (ua1 []userentity.UserGroup, err error) {
	mm_atomic.AddUint64(&mmGetByGroupID.beforeGetByGroupIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByGroupID.afterGetByGroupIDCounter, 1)

	mmGetByGroupID.t.Helper()

	if mmGetByGroupID.inspectFuncGetByGroupID != nil {
		mmGetByGroupID.inspectFuncGetByGroupID(groupID)
	}

	mm_params := UserGroupPrimeDBMockGetByGroupIDParams{groupID}

	// Record call args
	mmGetByGroupID.GetByGroupIDMock.mutex.Lock()
	mmGetByGroupID.GetByGroupIDMock.callArgs = append(mmGetByGroupID.GetByGroupIDMock.callArgs, &mm_params)
	mmGetByGroupID.GetByGroupIDMock.mutex.Unlock()

	for _, e := range mmGetByGroupID.GetByGroupIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByGroupID.GetByGroupIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByGroupID.GetByGroupIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockGetByGroupIDParams{groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmGetByGroupID.t.Errorf("UserGroupPrimeDBMock.GetByGroupID got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByGroupID.t.Errorf("UserGroupPrimeDBMock.GetByGroupID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByGroupID.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByGroupID.GetByGroupIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByGroupID.t.Fatal("No results are set for the UserGroupPrimeDBMock.GetByGroupID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByGroupID.funcGetByGroupID != nil {
		return mmGetByGroupID.funcGetByGroupID(groupID)
	}
	mmGetByGroupID.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.GetByGroupID. %v", groupID)
	return
}

// GetByGroupIDAfterCounter returns a count of finished UserGroupPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *UserGroupPrimeDBMock) GetByGroupIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.afterGetByGroupIDCounter)
}

// GetByGroupIDBeforeCounter returns a count of UserGroupPrimeDBMock.GetByGroupID invocations
func (mmGetByGroupID *UserGroupPrimeDBMock) GetByGroupIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByGroupID.beforeGetByGroupIDCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.GetByGroupID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByGroupID *mUserGroupPrimeDBMockGetByGroupID) Calls() []*UserGroupPrimeDBMockGetByGroupIDParams {
	mmGetByGroupID.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockGetByGroupIDParams, len(mmGetByGroupID.callArgs))
	copy(argCopy, mmGetByGroupID.callArgs)

	mmGetByGroupID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByGroupIDDone returns true if the count of the GetByGroupID invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockGetByGroupIDDone() bool {
	if m.GetByGroupIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByGroupIDMock.invocationsDone()
}

// MinimockGetByGroupIDInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockGetByGroupIDInspect() {
	for _, e := range m.GetByGroupIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByGroupID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByGroupIDCounter := mm_atomic.LoadUint64(&m.afterGetByGroupIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByGroupIDMock.defaultExpectation != nil && afterGetByGroupIDCounter < 1 {
		if m.GetByGroupIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByGroupID at\n%s", m.GetByGroupIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByGroupID at\n%s with params: %#v", m.GetByGroupIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByGroupIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByGroupID != nil && afterGetByGroupIDCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByGroupID at\n%s", m.funcGetByGroupIDOrigin)
	}

	if !m.GetByGroupIDMock.invocationsDone() && afterGetByGroupIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.GetByGroupID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByGroupIDMock.expectedInvocations), m.GetByGroupIDMock.expectedInvocationsOrigin, afterGetByGroupIDCounter)
	}
}

type mUserGroupPrimeDBMockGetByUserID struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockGetByUserIDExpectation
	expectations       []*UserGroupPrimeDBMockGetByUserIDExpectation

	callArgs []*UserGroupPrimeDBMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockGetByUserIDExpectation specifies expectation struct of the UserGroupPrimeDB.GetByUserID
type UserGroupPrimeDBMockGetByUserIDExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockGetByUserIDParams
	paramPtrs          *UserGroupPrimeDBMockGetByUserIDParamPtrs
	expectationOrigins UserGroupPrimeDBMockGetByUserIDExpectationOrigins
	results            *UserGroupPrimeDBMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockGetByUserIDParams contains parameters of the UserGroupPrimeDB.GetByUserID
type UserGroupPrimeDBMockGetByUserIDParams struct {
	userID int64
}

// UserGroupPrimeDBMockGetByUserIDParamPtrs contains pointers to parameters of the UserGroupPrimeDB.GetByUserID
type UserGroupPrimeDBMockGetByUserIDParamPtrs struct {
	userID *int64
}

// UserGroupPrimeDBMockGetByUserIDResults contains results of the UserGroupPrimeDB.GetByUserID
type UserGroupPrimeDBMockGetByUserIDResults struct {
	ua1 []userentity.UserGroup
	err error
}

// UserGroupPrimeDBMockGetByUserIDOrigins contains origins of expectations of the UserGroupPrimeDB.GetByUserID
type UserGroupPrimeDBMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Optional() *mUserGroupPrimeDBMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for UserGroupPrimeDB.GetByUserID
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Expect(userID int64) *mUserGroupPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &UserGroupPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &UserGroupPrimeDBMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for UserGroupPrimeDB.GetByUserID
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) ExpectUserIDParam1(userID int64) *mUserGroupPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &UserGroupPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.GetByUserID
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Inspect(f func(userID int64)) *mUserGroupPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by UserGroupPrimeDB.GetByUserID
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Return(ua1 []userentity.UserGroup, err error) *UserGroupPrimeDBMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &UserGroupPrimeDBMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &UserGroupPrimeDBMockGetByUserIDResults{ua1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.GetByUserID method
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Set(f func(userID int64) (ua1 []userentity.UserGroup, err error)) *UserGroupPrimeDBMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the UserGroupPrimeDB.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) When(userID int64) *UserGroupPrimeDBMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetByUserID mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &UserGroupPrimeDBMockGetByUserIDParams{userID},
		expectationOrigins: UserGroupPrimeDBMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.GetByUserID return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockGetByUserIDExpectation) Then(ua1 []userentity.UserGroup, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockGetByUserIDResults{ua1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.GetByUserID should be invoked
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Times(n uint64) *mUserGroupPrimeDBMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of UserGroupPrimeDBMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.UserGroupPrimeDB
func (mmGetByUserID *UserGroupPrimeDBMock) GetByUserID(userID int64) (ua1 []userentity.UserGroup, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := UserGroupPrimeDBMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ua1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("UserGroupPrimeDBMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("UserGroupPrimeDBMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the UserGroupPrimeDBMock.GetByUserID")
		}
		return (*mm_results).ua1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished UserGroupPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *UserGroupPrimeDBMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of UserGroupPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *UserGroupPrimeDBMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mUserGroupPrimeDBMockGetByUserID) Calls() []*UserGroupPrimeDBMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mUserGroupPrimeDBMockGetGroupsByIDs struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockGetGroupsByIDsExpectation
	expectations       []*UserGroupPrimeDBMockGetGroupsByIDsExpectation

	callArgs []*UserGroupPrimeDBMockGetGroupsByIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockGetGroupsByIDsExpectation specifies expectation struct of the UserGroupPrimeDB.GetGroupsByIDs
type UserGroupPrimeDBMockGetGroupsByIDsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockGetGroupsByIDsParams
	paramPtrs          *UserGroupPrimeDBMockGetGroupsByIDsParamPtrs
	expectationOrigins UserGroupPrimeDBMockGetGroupsByIDsExpectationOrigins
	results            *UserGroupPrimeDBMockGetGroupsByIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockGetGroupsByIDsParams contains parameters of the UserGroupPrimeDB.GetGroupsByIDs
type UserGroupPrimeDBMockGetGroupsByIDsParams struct {
	groupIDs []int64
}

// UserGroupPrimeDBMockGetGroupsByIDsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.GetGroupsByIDs
type UserGroupPrimeDBMockGetGroupsByIDsParamPtrs struct {
	groupIDs *[]int64
}

// UserGroupPrimeDBMockGetGroupsByIDsResults contains results of the UserGroupPrimeDB.GetGroupsByIDs
type UserGroupPrimeDBMockGetGroupsByIDsResults struct {
	ga1 []groupentity.Group
	err error
}

// UserGroupPrimeDBMockGetGroupsByIDsOrigins contains origins of expectations of the UserGroupPrimeDB.GetGroupsByIDs
type UserGroupPrimeDBMockGetGroupsByIDsExpectationOrigins struct {
	origin         string
	originGroupIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Optional() *mUserGroupPrimeDBMockGetGroupsByIDs {
	mmGetGroupsByIDs.optional = true
	return mmGetGroupsByIDs
}

// Expect sets up expected params for UserGroupPrimeDB.GetGroupsByIDs
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Expect(groupIDs []int64) *mUserGroupPrimeDBMockGetGroupsByIDs {
	if mmGetGroupsByIDs.mock.funcGetGroupsByIDs != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetGroupsByIDs mock is already set by Set")
	}

	if mmGetGroupsByIDs.defaultExpectation == nil {
		mmGetGroupsByIDs.defaultExpectation = &UserGroupPrimeDBMockGetGroupsByIDsExpectation{}
	}

	if mmGetGroupsByIDs.defaultExpectation.paramPtrs != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetGroupsByIDs mock is already set by ExpectParams functions")
	}

	mmGetGroupsByIDs.defaultExpectation.params = &UserGroupPrimeDBMockGetGroupsByIDsParams{groupIDs}
	mmGetGroupsByIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetGroupsByIDs.expectations {
		if minimock.Equal(e.params, mmGetGroupsByIDs.defaultExpectation.params) {
			mmGetGroupsByIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetGroupsByIDs.defaultExpectation.params)
		}
	}

	return mmGetGroupsByIDs
}

// ExpectGroupIDsParam1 sets up expected param groupIDs for UserGroupPrimeDB.GetGroupsByIDs
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) ExpectGroupIDsParam1(groupIDs []int64) *mUserGroupPrimeDBMockGetGroupsByIDs {
	if mmGetGroupsByIDs.mock.funcGetGroupsByIDs != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetGroupsByIDs mock is already set by Set")
	}

	if mmGetGroupsByIDs.defaultExpectation == nil {
		mmGetGroupsByIDs.defaultExpectation = &UserGroupPrimeDBMockGetGroupsByIDsExpectation{}
	}

	if mmGetGroupsByIDs.defaultExpectation.params != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetGroupsByIDs mock is already set by Expect")
	}

	if mmGetGroupsByIDs.defaultExpectation.paramPtrs == nil {
		mmGetGroupsByIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockGetGroupsByIDsParamPtrs{}
	}
	mmGetGroupsByIDs.defaultExpectation.paramPtrs.groupIDs = &groupIDs
	mmGetGroupsByIDs.defaultExpectation.expectationOrigins.originGroupIDs = minimock.CallerInfo(1)

	return mmGetGroupsByIDs
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.GetGroupsByIDs
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Inspect(f func(groupIDs []int64)) *mUserGroupPrimeDBMockGetGroupsByIDs {
	if mmGetGroupsByIDs.mock.inspectFuncGetGroupsByIDs != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.GetGroupsByIDs")
	}

	mmGetGroupsByIDs.mock.inspectFuncGetGroupsByIDs = f

	return mmGetGroupsByIDs
}

// Return sets up results that will be returned by UserGroupPrimeDB.GetGroupsByIDs
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Return(ga1 []groupentity.Group, err error) *UserGroupPrimeDBMock {
	if mmGetGroupsByIDs.mock.funcGetGroupsByIDs != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetGroupsByIDs mock is already set by Set")
	}

	if mmGetGroupsByIDs.defaultExpectation == nil {
		mmGetGroupsByIDs.defaultExpectation = &UserGroupPrimeDBMockGetGroupsByIDsExpectation{mock: mmGetGroupsByIDs.mock}
	}
	mmGetGroupsByIDs.defaultExpectation.results = &UserGroupPrimeDBMockGetGroupsByIDsResults{ga1, err}
	mmGetGroupsByIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetGroupsByIDs.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.GetGroupsByIDs method
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Set(f func(groupIDs []int64) (ga1 []groupentity.Group, err error)) *UserGroupPrimeDBMock {
	if mmGetGroupsByIDs.defaultExpectation != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.GetGroupsByIDs method")
	}

	if len(mmGetGroupsByIDs.expectations) > 0 {
		mmGetGroupsByIDs.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.GetGroupsByIDs method")
	}

	mmGetGroupsByIDs.mock.funcGetGroupsByIDs = f
	mmGetGroupsByIDs.mock.funcGetGroupsByIDsOrigin = minimock.CallerInfo(1)
	return mmGetGroupsByIDs.mock
}

// When sets expectation for the UserGroupPrimeDB.GetGroupsByIDs which will trigger the result defined by the following
// Then helper
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) When(groupIDs []int64) *UserGroupPrimeDBMockGetGroupsByIDsExpectation {
	if mmGetGroupsByIDs.mock.funcGetGroupsByIDs != nil {
		mmGetGroupsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetGroupsByIDs mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockGetGroupsByIDsExpectation{
		mock:               mmGetGroupsByIDs.mock,
		params:             &UserGroupPrimeDBMockGetGroupsByIDsParams{groupIDs},
		expectationOrigins: UserGroupPrimeDBMockGetGroupsByIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetGroupsByIDs.expectations = append(mmGetGroupsByIDs.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.GetGroupsByIDs return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockGetGroupsByIDsExpectation) Then(ga1 []groupentity.Group, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockGetGroupsByIDsResults{ga1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.GetGroupsByIDs should be invoked
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Times(n uint64) *mUserGroupPrimeDBMockGetGroupsByIDs {
	if n == 0 {
		mmGetGroupsByIDs.mock.t.Fatalf("Times of UserGroupPrimeDBMock.GetGroupsByIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetGroupsByIDs.expectedInvocations, n)
	mmGetGroupsByIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetGroupsByIDs
}

func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) invocationsDone() bool {
	if len(mmGetGroupsByIDs.expectations) == 0 && mmGetGroupsByIDs.defaultExpectation == nil && mmGetGroupsByIDs.mock.funcGetGroupsByIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetGroupsByIDs.mock.afterGetGroupsByIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetGroupsByIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetGroupsByIDs implements mm_repository.UserGroupPrimeDB
func (mmGetGroupsByIDs *UserGroupPrimeDBMock) GetGroupsByIDs(groupIDs []int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetGroupsByIDs.beforeGetGroupsByIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetGroupsByIDs.afterGetGroupsByIDsCounter, 1)

	mmGetGroupsByIDs.t.Helper()

	if mmGetGroupsByIDs.inspectFuncGetGroupsByIDs != nil {
		mmGetGroupsByIDs.inspectFuncGetGroupsByIDs(groupIDs)
	}

	mm_params := UserGroupPrimeDBMockGetGroupsByIDsParams{groupIDs}

	// Record call args
	mmGetGroupsByIDs.GetGroupsByIDsMock.mutex.Lock()
	mmGetGroupsByIDs.GetGroupsByIDsMock.callArgs = append(mmGetGroupsByIDs.GetGroupsByIDsMock.callArgs, &mm_params)
	mmGetGroupsByIDs.GetGroupsByIDsMock.mutex.Unlock()

	for _, e := range mmGetGroupsByIDs.GetGroupsByIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockGetGroupsByIDsParams{groupIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.groupIDs != nil && !minimock.Equal(*mm_want_ptrs.groupIDs, mm_got.groupIDs) {
				mmGetGroupsByIDs.t.Errorf("UserGroupPrimeDBMock.GetGroupsByIDs got unexpected parameter groupIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation.expectationOrigins.originGroupIDs, *mm_want_ptrs.groupIDs, mm_got.groupIDs, minimock.Diff(*mm_want_ptrs.groupIDs, mm_got.groupIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetGroupsByIDs.t.Errorf("UserGroupPrimeDBMock.GetGroupsByIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetGroupsByIDs.GetGroupsByIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetGroupsByIDs.t.Fatal("No results are set for the UserGroupPrimeDBMock.GetGroupsByIDs")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetGroupsByIDs.funcGetGroupsByIDs != nil {
		return mmGetGroupsByIDs.funcGetGroupsByIDs(groupIDs)
	}
	mmGetGroupsByIDs.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.GetGroupsByIDs. %v", groupIDs)
	return
}

// GetGroupsByIDsAfterCounter returns a count of finished UserGroupPrimeDBMock.GetGroupsByIDs invocations
func (mmGetGroupsByIDs *UserGroupPrimeDBMock) GetGroupsByIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroupsByIDs.afterGetGroupsByIDsCounter)
}

// GetGroupsByIDsBeforeCounter returns a count of UserGroupPrimeDBMock.GetGroupsByIDs invocations
func (mmGetGroupsByIDs *UserGroupPrimeDBMock) GetGroupsByIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetGroupsByIDs.beforeGetGroupsByIDsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.GetGroupsByIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetGroupsByIDs *mUserGroupPrimeDBMockGetGroupsByIDs) Calls() []*UserGroupPrimeDBMockGetGroupsByIDsParams {
	mmGetGroupsByIDs.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockGetGroupsByIDsParams, len(mmGetGroupsByIDs.callArgs))
	copy(argCopy, mmGetGroupsByIDs.callArgs)

	mmGetGroupsByIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetGroupsByIDsDone returns true if the count of the GetGroupsByIDs invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockGetGroupsByIDsDone() bool {
	if m.GetGroupsByIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetGroupsByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetGroupsByIDsMock.invocationsDone()
}

// MinimockGetGroupsByIDsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockGetGroupsByIDsInspect() {
	for _, e := range m.GetGroupsByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetGroupsByIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetGroupsByIDsCounter := mm_atomic.LoadUint64(&m.afterGetGroupsByIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetGroupsByIDsMock.defaultExpectation != nil && afterGetGroupsByIDsCounter < 1 {
		if m.GetGroupsByIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetGroupsByIDs at\n%s", m.GetGroupsByIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetGroupsByIDs at\n%s with params: %#v", m.GetGroupsByIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetGroupsByIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetGroupsByIDs != nil && afterGetGroupsByIDsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetGroupsByIDs at\n%s", m.funcGetGroupsByIDsOrigin)
	}

	if !m.GetGroupsByIDsMock.invocationsDone() && afterGetGroupsByIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.GetGroupsByIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetGroupsByIDsMock.expectedInvocations), m.GetGroupsByIDsMock.expectedInvocationsOrigin, afterGetGroupsByIDsCounter)
	}
}

type mUserGroupPrimeDBMockGetParticipantGroupsByUserID struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation
	expectations       []*UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation

	callArgs []*UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation specifies expectation struct of the UserGroupPrimeDB.GetParticipantGroupsByUserID
type UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams
	paramPtrs          *UserGroupPrimeDBMockGetParticipantGroupsByUserIDParamPtrs
	expectationOrigins UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectationOrigins
	results            *UserGroupPrimeDBMockGetParticipantGroupsByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams contains parameters of the UserGroupPrimeDB.GetParticipantGroupsByUserID
type UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams struct {
	userID int64
}

// UserGroupPrimeDBMockGetParticipantGroupsByUserIDParamPtrs contains pointers to parameters of the UserGroupPrimeDB.GetParticipantGroupsByUserID
type UserGroupPrimeDBMockGetParticipantGroupsByUserIDParamPtrs struct {
	userID *int64
}

// UserGroupPrimeDBMockGetParticipantGroupsByUserIDResults contains results of the UserGroupPrimeDB.GetParticipantGroupsByUserID
type UserGroupPrimeDBMockGetParticipantGroupsByUserIDResults struct {
	ga1 []groupentity.Group
	err error
}

// UserGroupPrimeDBMockGetParticipantGroupsByUserIDOrigins contains origins of expectations of the UserGroupPrimeDB.GetParticipantGroupsByUserID
type UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Optional() *mUserGroupPrimeDBMockGetParticipantGroupsByUserID {
	mmGetParticipantGroupsByUserID.optional = true
	return mmGetParticipantGroupsByUserID
}

// Expect sets up expected params for UserGroupPrimeDB.GetParticipantGroupsByUserID
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Expect(userID int64) *mUserGroupPrimeDBMockGetParticipantGroupsByUserID {
	if mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserID != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock is already set by Set")
	}

	if mmGetParticipantGroupsByUserID.defaultExpectation == nil {
		mmGetParticipantGroupsByUserID.defaultExpectation = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation{}
	}

	if mmGetParticipantGroupsByUserID.defaultExpectation.paramPtrs != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock is already set by ExpectParams functions")
	}

	mmGetParticipantGroupsByUserID.defaultExpectation.params = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams{userID}
	mmGetParticipantGroupsByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetParticipantGroupsByUserID.expectations {
		if minimock.Equal(e.params, mmGetParticipantGroupsByUserID.defaultExpectation.params) {
			mmGetParticipantGroupsByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetParticipantGroupsByUserID.defaultExpectation.params)
		}
	}

	return mmGetParticipantGroupsByUserID
}

// ExpectUserIDParam1 sets up expected param userID for UserGroupPrimeDB.GetParticipantGroupsByUserID
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) ExpectUserIDParam1(userID int64) *mUserGroupPrimeDBMockGetParticipantGroupsByUserID {
	if mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserID != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock is already set by Set")
	}

	if mmGetParticipantGroupsByUserID.defaultExpectation == nil {
		mmGetParticipantGroupsByUserID.defaultExpectation = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation{}
	}

	if mmGetParticipantGroupsByUserID.defaultExpectation.params != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock is already set by Expect")
	}

	if mmGetParticipantGroupsByUserID.defaultExpectation.paramPtrs == nil {
		mmGetParticipantGroupsByUserID.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDParamPtrs{}
	}
	mmGetParticipantGroupsByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetParticipantGroupsByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetParticipantGroupsByUserID
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.GetParticipantGroupsByUserID
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Inspect(f func(userID int64)) *mUserGroupPrimeDBMockGetParticipantGroupsByUserID {
	if mmGetParticipantGroupsByUserID.mock.inspectFuncGetParticipantGroupsByUserID != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.GetParticipantGroupsByUserID")
	}

	mmGetParticipantGroupsByUserID.mock.inspectFuncGetParticipantGroupsByUserID = f

	return mmGetParticipantGroupsByUserID
}

// Return sets up results that will be returned by UserGroupPrimeDB.GetParticipantGroupsByUserID
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Return(ga1 []groupentity.Group, err error) *UserGroupPrimeDBMock {
	if mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserID != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock is already set by Set")
	}

	if mmGetParticipantGroupsByUserID.defaultExpectation == nil {
		mmGetParticipantGroupsByUserID.defaultExpectation = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation{mock: mmGetParticipantGroupsByUserID.mock}
	}
	mmGetParticipantGroupsByUserID.defaultExpectation.results = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDResults{ga1, err}
	mmGetParticipantGroupsByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetParticipantGroupsByUserID.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.GetParticipantGroupsByUserID method
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Set(f func(userID int64) (ga1 []groupentity.Group, err error)) *UserGroupPrimeDBMock {
	if mmGetParticipantGroupsByUserID.defaultExpectation != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.GetParticipantGroupsByUserID method")
	}

	if len(mmGetParticipantGroupsByUserID.expectations) > 0 {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.GetParticipantGroupsByUserID method")
	}

	mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserID = f
	mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetParticipantGroupsByUserID.mock
}

// When sets expectation for the UserGroupPrimeDB.GetParticipantGroupsByUserID which will trigger the result defined by the following
// Then helper
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) When(userID int64) *UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation {
	if mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserID != nil {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation{
		mock:               mmGetParticipantGroupsByUserID.mock,
		params:             &UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams{userID},
		expectationOrigins: UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetParticipantGroupsByUserID.expectations = append(mmGetParticipantGroupsByUserID.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.GetParticipantGroupsByUserID return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockGetParticipantGroupsByUserIDExpectation) Then(ga1 []groupentity.Group, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockGetParticipantGroupsByUserIDResults{ga1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.GetParticipantGroupsByUserID should be invoked
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Times(n uint64) *mUserGroupPrimeDBMockGetParticipantGroupsByUserID {
	if n == 0 {
		mmGetParticipantGroupsByUserID.mock.t.Fatalf("Times of UserGroupPrimeDBMock.GetParticipantGroupsByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetParticipantGroupsByUserID.expectedInvocations, n)
	mmGetParticipantGroupsByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetParticipantGroupsByUserID
}

func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) invocationsDone() bool {
	if len(mmGetParticipantGroupsByUserID.expectations) == 0 && mmGetParticipantGroupsByUserID.defaultExpectation == nil && mmGetParticipantGroupsByUserID.mock.funcGetParticipantGroupsByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetParticipantGroupsByUserID.mock.afterGetParticipantGroupsByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetParticipantGroupsByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetParticipantGroupsByUserID implements mm_repository.UserGroupPrimeDB
func (mmGetParticipantGroupsByUserID *UserGroupPrimeDBMock) GetParticipantGroupsByUserID(userID int64) (ga1 []groupentity.Group, err error) {
	mm_atomic.AddUint64(&mmGetParticipantGroupsByUserID.beforeGetParticipantGroupsByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetParticipantGroupsByUserID.afterGetParticipantGroupsByUserIDCounter, 1)

	mmGetParticipantGroupsByUserID.t.Helper()

	if mmGetParticipantGroupsByUserID.inspectFuncGetParticipantGroupsByUserID != nil {
		mmGetParticipantGroupsByUserID.inspectFuncGetParticipantGroupsByUserID(userID)
	}

	mm_params := UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams{userID}

	// Record call args
	mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.mutex.Lock()
	mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.callArgs = append(mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.callArgs, &mm_params)
	mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.mutex.Unlock()

	for _, e := range mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ga1, e.results.err
		}
	}

	if mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetParticipantGroupsByUserID.t.Errorf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetParticipantGroupsByUserID.t.Errorf("UserGroupPrimeDBMock.GetParticipantGroupsByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetParticipantGroupsByUserID.GetParticipantGroupsByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetParticipantGroupsByUserID.t.Fatal("No results are set for the UserGroupPrimeDBMock.GetParticipantGroupsByUserID")
		}
		return (*mm_results).ga1, (*mm_results).err
	}
	if mmGetParticipantGroupsByUserID.funcGetParticipantGroupsByUserID != nil {
		return mmGetParticipantGroupsByUserID.funcGetParticipantGroupsByUserID(userID)
	}
	mmGetParticipantGroupsByUserID.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.GetParticipantGroupsByUserID. %v", userID)
	return
}

// GetParticipantGroupsByUserIDAfterCounter returns a count of finished UserGroupPrimeDBMock.GetParticipantGroupsByUserID invocations
func (mmGetParticipantGroupsByUserID *UserGroupPrimeDBMock) GetParticipantGroupsByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantGroupsByUserID.afterGetParticipantGroupsByUserIDCounter)
}

// GetParticipantGroupsByUserIDBeforeCounter returns a count of UserGroupPrimeDBMock.GetParticipantGroupsByUserID invocations
func (mmGetParticipantGroupsByUserID *UserGroupPrimeDBMock) GetParticipantGroupsByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetParticipantGroupsByUserID.beforeGetParticipantGroupsByUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.GetParticipantGroupsByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetParticipantGroupsByUserID *mUserGroupPrimeDBMockGetParticipantGroupsByUserID) Calls() []*UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams {
	mmGetParticipantGroupsByUserID.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockGetParticipantGroupsByUserIDParams, len(mmGetParticipantGroupsByUserID.callArgs))
	copy(argCopy, mmGetParticipantGroupsByUserID.callArgs)

	mmGetParticipantGroupsByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetParticipantGroupsByUserIDDone returns true if the count of the GetParticipantGroupsByUserID invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockGetParticipantGroupsByUserIDDone() bool {
	if m.GetParticipantGroupsByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetParticipantGroupsByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetParticipantGroupsByUserIDMock.invocationsDone()
}

// MinimockGetParticipantGroupsByUserIDInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockGetParticipantGroupsByUserIDInspect() {
	for _, e := range m.GetParticipantGroupsByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetParticipantGroupsByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetParticipantGroupsByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetParticipantGroupsByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetParticipantGroupsByUserIDMock.defaultExpectation != nil && afterGetParticipantGroupsByUserIDCounter < 1 {
		if m.GetParticipantGroupsByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetParticipantGroupsByUserID at\n%s", m.GetParticipantGroupsByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetParticipantGroupsByUserID at\n%s with params: %#v", m.GetParticipantGroupsByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetParticipantGroupsByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetParticipantGroupsByUserID != nil && afterGetParticipantGroupsByUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetParticipantGroupsByUserID at\n%s", m.funcGetParticipantGroupsByUserIDOrigin)
	}

	if !m.GetParticipantGroupsByUserIDMock.invocationsDone() && afterGetParticipantGroupsByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.GetParticipantGroupsByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetParticipantGroupsByUserIDMock.expectedInvocations), m.GetParticipantGroupsByUserIDMock.expectedInvocationsOrigin, afterGetParticipantGroupsByUserIDCounter)
	}
}

type mUserGroupPrimeDBMockGetProductsByIDs struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockGetProductsByIDsExpectation
	expectations       []*UserGroupPrimeDBMockGetProductsByIDsExpectation

	callArgs []*UserGroupPrimeDBMockGetProductsByIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockGetProductsByIDsExpectation specifies expectation struct of the UserGroupPrimeDB.GetProductsByIDs
type UserGroupPrimeDBMockGetProductsByIDsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockGetProductsByIDsParams
	paramPtrs          *UserGroupPrimeDBMockGetProductsByIDsParamPtrs
	expectationOrigins UserGroupPrimeDBMockGetProductsByIDsExpectationOrigins
	results            *UserGroupPrimeDBMockGetProductsByIDsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockGetProductsByIDsParams contains parameters of the UserGroupPrimeDB.GetProductsByIDs
type UserGroupPrimeDBMockGetProductsByIDsParams struct {
	productIDs []int64
}

// UserGroupPrimeDBMockGetProductsByIDsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.GetProductsByIDs
type UserGroupPrimeDBMockGetProductsByIDsParamPtrs struct {
	productIDs *[]int64
}

// UserGroupPrimeDBMockGetProductsByIDsResults contains results of the UserGroupPrimeDB.GetProductsByIDs
type UserGroupPrimeDBMockGetProductsByIDsResults struct {
	pa1 []productentity.Product
	err error
}

// UserGroupPrimeDBMockGetProductsByIDsOrigins contains origins of expectations of the UserGroupPrimeDB.GetProductsByIDs
type UserGroupPrimeDBMockGetProductsByIDsExpectationOrigins struct {
	origin           string
	originProductIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Optional() *mUserGroupPrimeDBMockGetProductsByIDs {
	mmGetProductsByIDs.optional = true
	return mmGetProductsByIDs
}

// Expect sets up expected params for UserGroupPrimeDB.GetProductsByIDs
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Expect(productIDs []int64) *mUserGroupPrimeDBMockGetProductsByIDs {
	if mmGetProductsByIDs.mock.funcGetProductsByIDs != nil {
		mmGetProductsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetProductsByIDs mock is already set by Set")
	}

	if mmGetProductsByIDs.defaultExpectation == nil {
		mmGetProductsByIDs.defaultExpectation = &UserGroupPrimeDBMockGetProductsByIDsExpectation{}
	}

	if mmGetProductsByIDs.defaultExpectation.paramPtrs != nil {
		mmGetProductsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetProductsByIDs mock is already set by ExpectParams functions")
	}

	mmGetProductsByIDs.defaultExpectation.params = &UserGroupPrimeDBMockGetProductsByIDsParams{productIDs}
	mmGetProductsByIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetProductsByIDs.expectations {
		if minimock.Equal(e.params, mmGetProductsByIDs.defaultExpectation.params) {
			mmGetProductsByIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetProductsByIDs.defaultExpectation.params)
		}
	}

	return mmGetProductsByIDs
}

// ExpectProductIDsParam1 sets up expected param productIDs for UserGroupPrimeDB.GetProductsByIDs
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) ExpectProductIDsParam1(productIDs []int64) *mUserGroupPrimeDBMockGetProductsByIDs {
	if mmGetProductsByIDs.mock.funcGetProductsByIDs != nil {
		mmGetProductsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetProductsByIDs mock is already set by Set")
	}

	if mmGetProductsByIDs.defaultExpectation == nil {
		mmGetProductsByIDs.defaultExpectation = &UserGroupPrimeDBMockGetProductsByIDsExpectation{}
	}

	if mmGetProductsByIDs.defaultExpectation.params != nil {
		mmGetProductsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetProductsByIDs mock is already set by Expect")
	}

	if mmGetProductsByIDs.defaultExpectation.paramPtrs == nil {
		mmGetProductsByIDs.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockGetProductsByIDsParamPtrs{}
	}
	mmGetProductsByIDs.defaultExpectation.paramPtrs.productIDs = &productIDs
	mmGetProductsByIDs.defaultExpectation.expectationOrigins.originProductIDs = minimock.CallerInfo(1)

	return mmGetProductsByIDs
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.GetProductsByIDs
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Inspect(f func(productIDs []int64)) *mUserGroupPrimeDBMockGetProductsByIDs {
	if mmGetProductsByIDs.mock.inspectFuncGetProductsByIDs != nil {
		mmGetProductsByIDs.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.GetProductsByIDs")
	}

	mmGetProductsByIDs.mock.inspectFuncGetProductsByIDs = f

	return mmGetProductsByIDs
}

// Return sets up results that will be returned by UserGroupPrimeDB.GetProductsByIDs
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Return(pa1 []productentity.Product, err error) *UserGroupPrimeDBMock {
	if mmGetProductsByIDs.mock.funcGetProductsByIDs != nil {
		mmGetProductsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetProductsByIDs mock is already set by Set")
	}

	if mmGetProductsByIDs.defaultExpectation == nil {
		mmGetProductsByIDs.defaultExpectation = &UserGroupPrimeDBMockGetProductsByIDsExpectation{mock: mmGetProductsByIDs.mock}
	}
	mmGetProductsByIDs.defaultExpectation.results = &UserGroupPrimeDBMockGetProductsByIDsResults{pa1, err}
	mmGetProductsByIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetProductsByIDs.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.GetProductsByIDs method
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Set(f func(productIDs []int64) (pa1 []productentity.Product, err error)) *UserGroupPrimeDBMock {
	if mmGetProductsByIDs.defaultExpectation != nil {
		mmGetProductsByIDs.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.GetProductsByIDs method")
	}

	if len(mmGetProductsByIDs.expectations) > 0 {
		mmGetProductsByIDs.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.GetProductsByIDs method")
	}

	mmGetProductsByIDs.mock.funcGetProductsByIDs = f
	mmGetProductsByIDs.mock.funcGetProductsByIDsOrigin = minimock.CallerInfo(1)
	return mmGetProductsByIDs.mock
}

// When sets expectation for the UserGroupPrimeDB.GetProductsByIDs which will trigger the result defined by the following
// Then helper
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) When(productIDs []int64) *UserGroupPrimeDBMockGetProductsByIDsExpectation {
	if mmGetProductsByIDs.mock.funcGetProductsByIDs != nil {
		mmGetProductsByIDs.mock.t.Fatalf("UserGroupPrimeDBMock.GetProductsByIDs mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockGetProductsByIDsExpectation{
		mock:               mmGetProductsByIDs.mock,
		params:             &UserGroupPrimeDBMockGetProductsByIDsParams{productIDs},
		expectationOrigins: UserGroupPrimeDBMockGetProductsByIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetProductsByIDs.expectations = append(mmGetProductsByIDs.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.GetProductsByIDs return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockGetProductsByIDsExpectation) Then(pa1 []productentity.Product, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockGetProductsByIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.GetProductsByIDs should be invoked
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Times(n uint64) *mUserGroupPrimeDBMockGetProductsByIDs {
	if n == 0 {
		mmGetProductsByIDs.mock.t.Fatalf("Times of UserGroupPrimeDBMock.GetProductsByIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetProductsByIDs.expectedInvocations, n)
	mmGetProductsByIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetProductsByIDs
}

func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) invocationsDone() bool {
	if len(mmGetProductsByIDs.expectations) == 0 && mmGetProductsByIDs.defaultExpectation == nil && mmGetProductsByIDs.mock.funcGetProductsByIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetProductsByIDs.mock.afterGetProductsByIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetProductsByIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetProductsByIDs implements mm_repository.UserGroupPrimeDB
func (mmGetProductsByIDs *UserGroupPrimeDBMock) GetProductsByIDs(productIDs []int64) (pa1 []productentity.Product, err error) {
	mm_atomic.AddUint64(&mmGetProductsByIDs.beforeGetProductsByIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetProductsByIDs.afterGetProductsByIDsCounter, 1)

	mmGetProductsByIDs.t.Helper()

	if mmGetProductsByIDs.inspectFuncGetProductsByIDs != nil {
		mmGetProductsByIDs.inspectFuncGetProductsByIDs(productIDs)
	}

	mm_params := UserGroupPrimeDBMockGetProductsByIDsParams{productIDs}

	// Record call args
	mmGetProductsByIDs.GetProductsByIDsMock.mutex.Lock()
	mmGetProductsByIDs.GetProductsByIDsMock.callArgs = append(mmGetProductsByIDs.GetProductsByIDsMock.callArgs, &mm_params)
	mmGetProductsByIDs.GetProductsByIDsMock.mutex.Unlock()

	for _, e := range mmGetProductsByIDs.GetProductsByIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockGetProductsByIDsParams{productIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productIDs != nil && !minimock.Equal(*mm_want_ptrs.productIDs, mm_got.productIDs) {
				mmGetProductsByIDs.t.Errorf("UserGroupPrimeDBMock.GetProductsByIDs got unexpected parameter productIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation.expectationOrigins.originProductIDs, *mm_want_ptrs.productIDs, mm_got.productIDs, minimock.Diff(*mm_want_ptrs.productIDs, mm_got.productIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetProductsByIDs.t.Errorf("UserGroupPrimeDBMock.GetProductsByIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetProductsByIDs.GetProductsByIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetProductsByIDs.t.Fatal("No results are set for the UserGroupPrimeDBMock.GetProductsByIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetProductsByIDs.funcGetProductsByIDs != nil {
		return mmGetProductsByIDs.funcGetProductsByIDs(productIDs)
	}
	mmGetProductsByIDs.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.GetProductsByIDs. %v", productIDs)
	return
}

// GetProductsByIDsAfterCounter returns a count of finished UserGroupPrimeDBMock.GetProductsByIDs invocations
func (mmGetProductsByIDs *UserGroupPrimeDBMock) GetProductsByIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductsByIDs.afterGetProductsByIDsCounter)
}

// GetProductsByIDsBeforeCounter returns a count of UserGroupPrimeDBMock.GetProductsByIDs invocations
func (mmGetProductsByIDs *UserGroupPrimeDBMock) GetProductsByIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductsByIDs.beforeGetProductsByIDsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.GetProductsByIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetProductsByIDs *mUserGroupPrimeDBMockGetProductsByIDs) Calls() []*UserGroupPrimeDBMockGetProductsByIDsParams {
	mmGetProductsByIDs.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockGetProductsByIDsParams, len(mmGetProductsByIDs.callArgs))
	copy(argCopy, mmGetProductsByIDs.callArgs)

	mmGetProductsByIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetProductsByIDsDone returns true if the count of the GetProductsByIDs invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockGetProductsByIDsDone() bool {
	if m.GetProductsByIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetProductsByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetProductsByIDsMock.invocationsDone()
}

// MinimockGetProductsByIDsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockGetProductsByIDsInspect() {
	for _, e := range m.GetProductsByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetProductsByIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetProductsByIDsCounter := mm_atomic.LoadUint64(&m.afterGetProductsByIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetProductsByIDsMock.defaultExpectation != nil && afterGetProductsByIDsCounter < 1 {
		if m.GetProductsByIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetProductsByIDs at\n%s", m.GetProductsByIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetProductsByIDs at\n%s with params: %#v", m.GetProductsByIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetProductsByIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetProductsByIDs != nil && afterGetProductsByIDsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetProductsByIDs at\n%s", m.funcGetProductsByIDsOrigin)
	}

	if !m.GetProductsByIDsMock.invocationsDone() && afterGetProductsByIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.GetProductsByIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetProductsByIDsMock.expectedInvocations), m.GetProductsByIDsMock.expectedInvocationsOrigin, afterGetProductsByIDsCounter)
	}
}

type mUserGroupPrimeDBMockGetUserGroups struct {
	optional           bool
	mock               *UserGroupPrimeDBMock
	defaultExpectation *UserGroupPrimeDBMockGetUserGroupsExpectation
	expectations       []*UserGroupPrimeDBMockGetUserGroupsExpectation

	callArgs []*UserGroupPrimeDBMockGetUserGroupsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserGroupPrimeDBMockGetUserGroupsExpectation specifies expectation struct of the UserGroupPrimeDB.GetUserGroups
type UserGroupPrimeDBMockGetUserGroupsExpectation struct {
	mock               *UserGroupPrimeDBMock
	params             *UserGroupPrimeDBMockGetUserGroupsParams
	paramPtrs          *UserGroupPrimeDBMockGetUserGroupsParamPtrs
	expectationOrigins UserGroupPrimeDBMockGetUserGroupsExpectationOrigins
	results            *UserGroupPrimeDBMockGetUserGroupsResults
	returnOrigin       string
	Counter            uint64
}

// UserGroupPrimeDBMockGetUserGroupsParams contains parameters of the UserGroupPrimeDB.GetUserGroups
type UserGroupPrimeDBMockGetUserGroupsParams struct {
	userID int64
}

// UserGroupPrimeDBMockGetUserGroupsParamPtrs contains pointers to parameters of the UserGroupPrimeDB.GetUserGroups
type UserGroupPrimeDBMockGetUserGroupsParamPtrs struct {
	userID *int64
}

// UserGroupPrimeDBMockGetUserGroupsResults contains results of the UserGroupPrimeDB.GetUserGroups
type UserGroupPrimeDBMockGetUserGroupsResults struct {
	ia1 []int64
	err error
}

// UserGroupPrimeDBMockGetUserGroupsOrigins contains origins of expectations of the UserGroupPrimeDB.GetUserGroups
type UserGroupPrimeDBMockGetUserGroupsExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Optional() *mUserGroupPrimeDBMockGetUserGroups {
	mmGetUserGroups.optional = true
	return mmGetUserGroups
}

// Expect sets up expected params for UserGroupPrimeDB.GetUserGroups
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Expect(userID int64) *mUserGroupPrimeDBMockGetUserGroups {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserGroupPrimeDBMock.GetUserGroups mock is already set by Set")
	}

	if mmGetUserGroups.defaultExpectation == nil {
		mmGetUserGroups.defaultExpectation = &UserGroupPrimeDBMockGetUserGroupsExpectation{}
	}

	if mmGetUserGroups.defaultExpectation.paramPtrs != nil {
		mmGetUserGroups.mock.t.Fatalf("UserGroupPrimeDBMock.GetUserGroups mock is already set by ExpectParams functions")
	}

	mmGetUserGroups.defaultExpectation.params = &UserGroupPrimeDBMockGetUserGroupsParams{userID}
	mmGetUserGroups.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserGroups.expectations {
		if minimock.Equal(e.params, mmGetUserGroups.defaultExpectation.params) {
			mmGetUserGroups.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserGroups.defaultExpectation.params)
		}
	}

	return mmGetUserGroups
}

// ExpectUserIDParam1 sets up expected param userID for UserGroupPrimeDB.GetUserGroups
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) ExpectUserIDParam1(userID int64) *mUserGroupPrimeDBMockGetUserGroups {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserGroupPrimeDBMock.GetUserGroups mock is already set by Set")
	}

	if mmGetUserGroups.defaultExpectation == nil {
		mmGetUserGroups.defaultExpectation = &UserGroupPrimeDBMockGetUserGroupsExpectation{}
	}

	if mmGetUserGroups.defaultExpectation.params != nil {
		mmGetUserGroups.mock.t.Fatalf("UserGroupPrimeDBMock.GetUserGroups mock is already set by Expect")
	}

	if mmGetUserGroups.defaultExpectation.paramPtrs == nil {
		mmGetUserGroups.defaultExpectation.paramPtrs = &UserGroupPrimeDBMockGetUserGroupsParamPtrs{}
	}
	mmGetUserGroups.defaultExpectation.paramPtrs.userID = &userID
	mmGetUserGroups.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUserGroups
}

// Inspect accepts an inspector function that has same arguments as the UserGroupPrimeDB.GetUserGroups
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Inspect(f func(userID int64)) *mUserGroupPrimeDBMockGetUserGroups {
	if mmGetUserGroups.mock.inspectFuncGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("Inspect function is already set for UserGroupPrimeDBMock.GetUserGroups")
	}

	mmGetUserGroups.mock.inspectFuncGetUserGroups = f

	return mmGetUserGroups
}

// Return sets up results that will be returned by UserGroupPrimeDB.GetUserGroups
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Return(ia1 []int64, err error) *UserGroupPrimeDBMock {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserGroupPrimeDBMock.GetUserGroups mock is already set by Set")
	}

	if mmGetUserGroups.defaultExpectation == nil {
		mmGetUserGroups.defaultExpectation = &UserGroupPrimeDBMockGetUserGroupsExpectation{mock: mmGetUserGroups.mock}
	}
	mmGetUserGroups.defaultExpectation.results = &UserGroupPrimeDBMockGetUserGroupsResults{ia1, err}
	mmGetUserGroups.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserGroups.mock
}

// Set uses given function f to mock the UserGroupPrimeDB.GetUserGroups method
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Set(f func(userID int64) (ia1 []int64, err error)) *UserGroupPrimeDBMock {
	if mmGetUserGroups.defaultExpectation != nil {
		mmGetUserGroups.mock.t.Fatalf("Default expectation is already set for the UserGroupPrimeDB.GetUserGroups method")
	}

	if len(mmGetUserGroups.expectations) > 0 {
		mmGetUserGroups.mock.t.Fatalf("Some expectations are already set for the UserGroupPrimeDB.GetUserGroups method")
	}

	mmGetUserGroups.mock.funcGetUserGroups = f
	mmGetUserGroups.mock.funcGetUserGroupsOrigin = minimock.CallerInfo(1)
	return mmGetUserGroups.mock
}

// When sets expectation for the UserGroupPrimeDB.GetUserGroups which will trigger the result defined by the following
// Then helper
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) When(userID int64) *UserGroupPrimeDBMockGetUserGroupsExpectation {
	if mmGetUserGroups.mock.funcGetUserGroups != nil {
		mmGetUserGroups.mock.t.Fatalf("UserGroupPrimeDBMock.GetUserGroups mock is already set by Set")
	}

	expectation := &UserGroupPrimeDBMockGetUserGroupsExpectation{
		mock:               mmGetUserGroups.mock,
		params:             &UserGroupPrimeDBMockGetUserGroupsParams{userID},
		expectationOrigins: UserGroupPrimeDBMockGetUserGroupsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserGroups.expectations = append(mmGetUserGroups.expectations, expectation)
	return expectation
}

// Then sets up UserGroupPrimeDB.GetUserGroups return parameters for the expectation previously defined by the When method
func (e *UserGroupPrimeDBMockGetUserGroupsExpectation) Then(ia1 []int64, err error) *UserGroupPrimeDBMock {
	e.results = &UserGroupPrimeDBMockGetUserGroupsResults{ia1, err}
	return e.mock
}

// Times sets number of times UserGroupPrimeDB.GetUserGroups should be invoked
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Times(n uint64) *mUserGroupPrimeDBMockGetUserGroups {
	if n == 0 {
		mmGetUserGroups.mock.t.Fatalf("Times of UserGroupPrimeDBMock.GetUserGroups mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserGroups.expectedInvocations, n)
	mmGetUserGroups.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserGroups
}

func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) invocationsDone() bool {
	if len(mmGetUserGroups.expectations) == 0 && mmGetUserGroups.defaultExpectation == nil && mmGetUserGroups.mock.funcGetUserGroups == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserGroups.mock.afterGetUserGroupsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserGroups.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserGroups implements mm_repository.UserGroupPrimeDB
func (mmGetUserGroups *UserGroupPrimeDBMock) GetUserGroups(userID int64) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmGetUserGroups.beforeGetUserGroupsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserGroups.afterGetUserGroupsCounter, 1)

	mmGetUserGroups.t.Helper()

	if mmGetUserGroups.inspectFuncGetUserGroups != nil {
		mmGetUserGroups.inspectFuncGetUserGroups(userID)
	}

	mm_params := UserGroupPrimeDBMockGetUserGroupsParams{userID}

	// Record call args
	mmGetUserGroups.GetUserGroupsMock.mutex.Lock()
	mmGetUserGroups.GetUserGroupsMock.callArgs = append(mmGetUserGroups.GetUserGroupsMock.callArgs, &mm_params)
	mmGetUserGroups.GetUserGroupsMock.mutex.Unlock()

	for _, e := range mmGetUserGroups.GetUserGroupsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmGetUserGroups.GetUserGroupsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserGroups.GetUserGroupsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserGroups.GetUserGroupsMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserGroups.GetUserGroupsMock.defaultExpectation.paramPtrs

		mm_got := UserGroupPrimeDBMockGetUserGroupsParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUserGroups.t.Errorf("UserGroupPrimeDBMock.GetUserGroups got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserGroups.GetUserGroupsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserGroups.t.Errorf("UserGroupPrimeDBMock.GetUserGroups got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserGroups.GetUserGroupsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserGroups.GetUserGroupsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserGroups.t.Fatal("No results are set for the UserGroupPrimeDBMock.GetUserGroups")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmGetUserGroups.funcGetUserGroups != nil {
		return mmGetUserGroups.funcGetUserGroups(userID)
	}
	mmGetUserGroups.t.Fatalf("Unexpected call to UserGroupPrimeDBMock.GetUserGroups. %v", userID)
	return
}

// GetUserGroupsAfterCounter returns a count of finished UserGroupPrimeDBMock.GetUserGroups invocations
func (mmGetUserGroups *UserGroupPrimeDBMock) GetUserGroupsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroups.afterGetUserGroupsCounter)
}

// GetUserGroupsBeforeCounter returns a count of UserGroupPrimeDBMock.GetUserGroups invocations
func (mmGetUserGroups *UserGroupPrimeDBMock) GetUserGroupsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserGroups.beforeGetUserGroupsCounter)
}

// Calls returns a list of arguments used in each call to UserGroupPrimeDBMock.GetUserGroups.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserGroups *mUserGroupPrimeDBMockGetUserGroups) Calls() []*UserGroupPrimeDBMockGetUserGroupsParams {
	mmGetUserGroups.mutex.RLock()

	argCopy := make([]*UserGroupPrimeDBMockGetUserGroupsParams, len(mmGetUserGroups.callArgs))
	copy(argCopy, mmGetUserGroups.callArgs)

	mmGetUserGroups.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserGroupsDone returns true if the count of the GetUserGroups invocations corresponds
// the number of defined expectations
func (m *UserGroupPrimeDBMock) MinimockGetUserGroupsDone() bool {
	if m.GetUserGroupsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserGroupsMock.invocationsDone()
}

// MinimockGetUserGroupsInspect logs each unmet expectation
func (m *UserGroupPrimeDBMock) MinimockGetUserGroupsInspect() {
	for _, e := range m.GetUserGroupsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetUserGroups at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserGroupsCounter := mm_atomic.LoadUint64(&m.afterGetUserGroupsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserGroupsMock.defaultExpectation != nil && afterGetUserGroupsCounter < 1 {
		if m.GetUserGroupsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetUserGroups at\n%s", m.GetUserGroupsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetUserGroups at\n%s with params: %#v", m.GetUserGroupsMock.defaultExpectation.expectationOrigins.origin, *m.GetUserGroupsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserGroups != nil && afterGetUserGroupsCounter < 1 {
		m.t.Errorf("Expected call to UserGroupPrimeDBMock.GetUserGroups at\n%s", m.funcGetUserGroupsOrigin)
	}

	if !m.GetUserGroupsMock.invocationsDone() && afterGetUserGroupsCounter > 0 {
		m.t.Errorf("Expected %d calls to UserGroupPrimeDBMock.GetUserGroups at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserGroupsMock.expectedInvocations), m.GetUserGroupsMock.expectedInvocationsOrigin, afterGetUserGroupsCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *UserGroupPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockAssignUserToGroupsInspect()

			m.MinimockCreateInspect()

			m.MinimockCreateByGroupIDAndUserIDsInspect()

			m.MinimockDeleteByGroupIDInspect()

			m.MinimockDeleteByGroupIDAndUserIDsInspect()

			m.MinimockDeleteByUserIDInspect()

			m.MinimockDeleteByUserIDAndGroupIDsInspect()

			m.MinimockGetByGroupIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetGroupsByIDsInspect()

			m.MinimockGetParticipantGroupsByUserIDInspect()

			m.MinimockGetProductsByIDsInspect()

			m.MinimockGetUserGroupsInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *UserGroupPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *UserGroupPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockAssignUserToGroupsDone() &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByGroupIDAndUserIDsDone() &&
		m.MinimockDeleteByGroupIDDone() &&
		m.MinimockDeleteByGroupIDAndUserIDsDone() &&
		m.MinimockDeleteByUserIDDone() &&
		m.MinimockDeleteByUserIDAndGroupIDsDone() &&
		m.MinimockGetByGroupIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetGroupsByIDsDone() &&
		m.MinimockGetParticipantGroupsByUserIDDone() &&
		m.MinimockGetProductsByIDsDone() &&
		m.MinimockGetUserGroupsDone()
}
