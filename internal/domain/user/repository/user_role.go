package repository

import (
	"context"

	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

//go:generate minimock -i UserRolePrimeDB -o ../mocks/user_role_prime_db_mock.go -s _mock.go
type UserRolePrimeDB interface {
	Create(ctx context.Context, userRole userentity.UserRoleCreateData) (userentity.UserRole, error)
	CreateByRoleIDAndUserIDs(ctx context.Context, roleID int64, userIDs []int64) error
	CreateByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error
	GetAll() ([]userentity.UserRole, error)
	GetByID(id int64) (userentity.UserRole, error)
	GetByUserID(userID int64) ([]userentity.UserRole, error)
	GetByUserIDAndRoleID(userID int64, roleID int64) (userentity.UserRole, error)
	GetUserRoleIDs(userID int64) ([]int64, error)
	GetUserRolesByRoleID(roleID int64) ([]userentity.UserRole, error)
	Update(userRole userentity.UserRoleUpdateData) (userentity.UserRole, error)
	DeleteByRoleID(ctx context.Context, roleID int64) error
	DeleteByRoleIDAndUserIDs(ctx context.Context, roleID int64, userIDs []int64) error
	DeleteByUserID(userID int64) error
	DeleteByUserIDAndRoleID(ctx context.Context, userID int64, roleID int64) error
	DeleteByUserIDAndRoleIDs(ctx context.Context, userID int64, roleIDs []int64) error
}
