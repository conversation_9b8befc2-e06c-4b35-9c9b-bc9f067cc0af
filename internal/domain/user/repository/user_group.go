package repository

import (
	"context"

	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

//go:generate minimock -i UserGroupPrimeDB -o ../mocks/user_group_prime_db_mock.go -s _mock.go
type UserGroupPrimeDB interface {
	Create(ctx context.Context, userGroup userentity.UserGroupCreateData) (userentity.UserGroup, error)
	CreateByGroupIDAndUserIDs(ctx context.Context, groupID int64, userIDs []int64) error
	AssignUserToGroups(userID int64, groupIDs []int64) error
	GetByGroupID(groupID int64) ([]userentity.UserGroup, error)
	GetByUserID(userID int64) ([]userentity.UserGroup, error)
	GetGroupsByIDs(groupIDs []int64) ([]groupentity.Group, error)
	GetParticipantGroupsByUserID(userID int64) ([]groupentity.Group, error)
	GetProductsByIDs(productIDs []int64) ([]productentity.Product, error)
	GetUserGroups(userID int64) ([]int64, error)
	DeleteByGroupID(ctx context.Context, groupID int64) error
	DeleteByGroupIDAndUserIDs(ctx context.Context, groupID int64, userIDs []int64) error
	DeleteByUserID(ctx context.Context, userID int64) error
	DeleteByUserIDAndGroupIDs(ctx context.Context, userID int64, groupIDs []int64) error
}
