package repository

import (
	"context"

	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

//go:generate minimock -i UserPrimeDB -o ../mocks/user_prime_db_mock.go -s _mock.go
type UserPrimeDB interface {
	Create(ctx context.Context, user userentity.User) (userentity.User, error)
	GetAdmins() ([]userentity.User, error)
	GetAll() ([]userentity.User, error)
	GetAllPaginated(pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error)
	GetByCategoryID(categoryID int64) ([]userentity.User, error)
	GetByEmail(email string) (userentity.User, error)
	GetByFilters(userFilters userentity.UserFiltersData) ([]userentity.User, error)
	GetByFiltersPaginated(userFilters userentity.UserFiltersData, pagination sharedentity.PaginationParams) (sharedentity.PaginatedResult[userentity.User], error)
	GetByGroupID(id int64) ([]userentity.User, error)
	GetByID(id int64) (userentity.User, error)
	GetUsersByParticipantGroupID(groupID int64) ([]userentity.User, error)
	GetUsersByParticipantRoleID(roleID int64) ([]userentity.User, error)
	Update(user userentity.UserUpdateData) (userentity.User, error)
}

//go:generate minimock -i UserCache -o ../mocks/user_cache_mock.go -s _mock.go
type UserCache interface {
	GetUser(ctx context.Context, id int64) (userentity.User, error)
	GetUsers(ctx context.Context) ([]userentity.User, error)
	SetUser(ctx context.Context, user userentity.User) error
	SetUsers(ctx context.Context, users []userentity.User) error
	DeleteUser(ctx context.Context, id int64) error
	DeleteUsers(ctx context.Context, ids []int64) error
}
