package entity

import (
	"encoding/json"
	"time"

	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

type Proposal struct {
	ID            int64
	ProductID     int64
	ProdPropSeq   int64
	Price         float64
	Type          string
	Status        string
	CreatorID     int64
	LastViewed    time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     time.Time
	ActiveFlg     bool
	ProductOwners []string
	Product       productentity.Product
	Number        string
}

type Stand struct {
	ID        int64
	ProductID int64
	SeqNum    int64
	Num       int64
	Data      json.RawMessage
}
