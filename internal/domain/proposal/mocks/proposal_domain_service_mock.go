// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/service.ProposalDomainService -o proposal_domain_service_mock.go -n ProposalDomainServiceMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"github.com/gojuno/minimock/v3"
)

// ProposalDomainServiceMock implements mm_service.ProposalDomainService
type ProposalDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(data proposalentity.Proposal, values proposalvalueobject.Values) (p1 proposalvalueobject.ProposalFull, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(data proposalentity.Proposal, values proposalvalueobject.Values)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mProposalDomainServiceMockCreate

	funcCreateMessage          func(proposalID int64, message string, userID int64) (err error)
	funcCreateMessageOrigin    string
	inspectFuncCreateMessage   func(proposalID int64, message string, userID int64)
	afterCreateMessageCounter  uint64
	beforeCreateMessageCounter uint64
	CreateMessageMock          mProposalDomainServiceMockCreateMessage

	funcDelete          func(data proposalentity.Proposal) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(data proposalentity.Proposal)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mProposalDomainServiceMockDelete

	funcGetAllAsAdmin          func() (pa1 []proposalvalueobject.ProposalFull, err error)
	funcGetAllAsAdminOrigin    string
	inspectFuncGetAllAsAdmin   func()
	afterGetAllAsAdminCounter  uint64
	beforeGetAllAsAdminCounter uint64
	GetAllAsAdminMock          mProposalDomainServiceMockGetAllAsAdmin

	funcGetByID          func(id int64) (p1 proposalvalueobject.ProposalFull, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mProposalDomainServiceMockGetByID

	funcGetByProductID          func(productID int64) (pa1 []proposalentity.Proposal, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mProposalDomainServiceMockGetByProductID

	funcGetByProductIDAndProposalID          func(productID int64, proposalID int64) (p1 proposalvalueobject.ProposalFull, err error)
	funcGetByProductIDAndProposalIDOrigin    string
	inspectFuncGetByProductIDAndProposalID   func(productID int64, proposalID int64)
	afterGetByProductIDAndProposalIDCounter  uint64
	beforeGetByProductIDAndProposalIDCounter uint64
	GetByProductIDAndProposalIDMock          mProposalDomainServiceMockGetByProductIDAndProposalID

	funcGetHistory          func(proposalID int64) (ha1 []proposalvalueobject.HistoryRecord, err error)
	funcGetHistoryOrigin    string
	inspectFuncGetHistory   func(proposalID int64)
	afterGetHistoryCounter  uint64
	beforeGetHistoryCounter uint64
	GetHistoryMock          mProposalDomainServiceMockGetHistory

	funcGetUnreadEventsForUser          func(userID int64) (ha1 []proposalvalueobject.HistoryRecord, err error)
	funcGetUnreadEventsForUserOrigin    string
	inspectFuncGetUnreadEventsForUser   func(userID int64)
	afterGetUnreadEventsForUserCounter  uint64
	beforeGetUnreadEventsForUserCounter uint64
	GetUnreadEventsForUserMock          mProposalDomainServiceMockGetUnreadEventsForUser

	funcUpdate          func(ctx context.Context, data proposalaggregate.ProposalUpdate) (p1 proposalaggregate.Proposal, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(ctx context.Context, data proposalaggregate.ProposalUpdate)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mProposalDomainServiceMockUpdate

	funcUpdateLastViewed          func(ctx context.Context, data proposalvalueobject.UserView) (err error)
	funcUpdateLastViewedOrigin    string
	inspectFuncUpdateLastViewed   func(ctx context.Context, data proposalvalueobject.UserView)
	afterUpdateLastViewedCounter  uint64
	beforeUpdateLastViewedCounter uint64
	UpdateLastViewedMock          mProposalDomainServiceMockUpdateLastViewed
}

// NewProposalDomainServiceMock returns a mock for mm_service.ProposalDomainService
func NewProposalDomainServiceMock(t minimock.Tester) *ProposalDomainServiceMock {
	m := &ProposalDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mProposalDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*ProposalDomainServiceMockCreateParams{}

	m.CreateMessageMock = mProposalDomainServiceMockCreateMessage{mock: m}
	m.CreateMessageMock.callArgs = []*ProposalDomainServiceMockCreateMessageParams{}

	m.DeleteMock = mProposalDomainServiceMockDelete{mock: m}
	m.DeleteMock.callArgs = []*ProposalDomainServiceMockDeleteParams{}

	m.GetAllAsAdminMock = mProposalDomainServiceMockGetAllAsAdmin{mock: m}

	m.GetByIDMock = mProposalDomainServiceMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ProposalDomainServiceMockGetByIDParams{}

	m.GetByProductIDMock = mProposalDomainServiceMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*ProposalDomainServiceMockGetByProductIDParams{}

	m.GetByProductIDAndProposalIDMock = mProposalDomainServiceMockGetByProductIDAndProposalID{mock: m}
	m.GetByProductIDAndProposalIDMock.callArgs = []*ProposalDomainServiceMockGetByProductIDAndProposalIDParams{}

	m.GetHistoryMock = mProposalDomainServiceMockGetHistory{mock: m}
	m.GetHistoryMock.callArgs = []*ProposalDomainServiceMockGetHistoryParams{}

	m.GetUnreadEventsForUserMock = mProposalDomainServiceMockGetUnreadEventsForUser{mock: m}
	m.GetUnreadEventsForUserMock.callArgs = []*ProposalDomainServiceMockGetUnreadEventsForUserParams{}

	m.UpdateMock = mProposalDomainServiceMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*ProposalDomainServiceMockUpdateParams{}

	m.UpdateLastViewedMock = mProposalDomainServiceMockUpdateLastViewed{mock: m}
	m.UpdateLastViewedMock.callArgs = []*ProposalDomainServiceMockUpdateLastViewedParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProposalDomainServiceMockCreate struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockCreateExpectation
	expectations       []*ProposalDomainServiceMockCreateExpectation

	callArgs []*ProposalDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockCreateExpectation specifies expectation struct of the ProposalDomainService.Create
type ProposalDomainServiceMockCreateExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockCreateParams
	paramPtrs          *ProposalDomainServiceMockCreateParamPtrs
	expectationOrigins ProposalDomainServiceMockCreateExpectationOrigins
	results            *ProposalDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockCreateParams contains parameters of the ProposalDomainService.Create
type ProposalDomainServiceMockCreateParams struct {
	data   proposalentity.Proposal
	values proposalvalueobject.Values
}

// ProposalDomainServiceMockCreateParamPtrs contains pointers to parameters of the ProposalDomainService.Create
type ProposalDomainServiceMockCreateParamPtrs struct {
	data   *proposalentity.Proposal
	values *proposalvalueobject.Values
}

// ProposalDomainServiceMockCreateResults contains results of the ProposalDomainService.Create
type ProposalDomainServiceMockCreateResults struct {
	p1  proposalvalueobject.ProposalFull
	err error
}

// ProposalDomainServiceMockCreateOrigins contains origins of expectations of the ProposalDomainService.Create
type ProposalDomainServiceMockCreateExpectationOrigins struct {
	origin       string
	originData   string
	originValues string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mProposalDomainServiceMockCreate) Optional() *mProposalDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ProposalDomainService.Create
func (mmCreate *mProposalDomainServiceMockCreate) Expect(data proposalentity.Proposal, values proposalvalueobject.Values) *mProposalDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ProposalDomainServiceMockCreateParams{data, values}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectDataParam1 sets up expected param data for ProposalDomainService.Create
func (mmCreate *mProposalDomainServiceMockCreate) ExpectDataParam1(data proposalentity.Proposal) *mProposalDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProposalDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.data = &data
	mmCreate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectValuesParam2 sets up expected param values for ProposalDomainService.Create
func (mmCreate *mProposalDomainServiceMockCreate) ExpectValuesParam2(values proposalvalueobject.Values) *mProposalDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProposalDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.values = &values
	mmCreate.defaultExpectation.expectationOrigins.originValues = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.Create
func (mmCreate *mProposalDomainServiceMockCreate) Inspect(f func(data proposalentity.Proposal, values proposalvalueobject.Values)) *mProposalDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ProposalDomainService.Create
func (mmCreate *mProposalDomainServiceMockCreate) Return(p1 proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ProposalDomainServiceMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ProposalDomainService.Create method
func (mmCreate *mProposalDomainServiceMockCreate) Set(f func(data proposalentity.Proposal, values proposalvalueobject.Values) (p1 proposalvalueobject.ProposalFull, err error)) *ProposalDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ProposalDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mProposalDomainServiceMockCreate) When(data proposalentity.Proposal, values proposalvalueobject.Values) *ProposalDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ProposalDomainServiceMockCreateParams{data, values},
		expectationOrigins: ProposalDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.Create return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockCreateExpectation) Then(p1 proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.Create should be invoked
func (mmCreate *mProposalDomainServiceMockCreate) Times(n uint64) *mProposalDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ProposalDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mProposalDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.ProposalDomainService
func (mmCreate *ProposalDomainServiceMock) Create(data proposalentity.Proposal, values proposalvalueobject.Values) (p1 proposalvalueobject.ProposalFull, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(data, values)
	}

	mm_params := ProposalDomainServiceMockCreateParams{data, values}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockCreateParams{data, values}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreate.t.Errorf("ProposalDomainServiceMock.Create got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

			if mm_want_ptrs.values != nil && !minimock.Equal(*mm_want_ptrs.values, mm_got.values) {
				mmCreate.t.Errorf("ProposalDomainServiceMock.Create got unexpected parameter values, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originValues, *mm_want_ptrs.values, mm_got.values, minimock.Diff(*mm_want_ptrs.values, mm_got.values))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ProposalDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ProposalDomainServiceMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(data, values)
	}
	mmCreate.t.Fatalf("Unexpected call to ProposalDomainServiceMock.Create. %v %v", data, values)
	return
}

// CreateAfterCounter returns a count of finished ProposalDomainServiceMock.Create invocations
func (mmCreate *ProposalDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ProposalDomainServiceMock.Create invocations
func (mmCreate *ProposalDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mProposalDomainServiceMockCreate) Calls() []*ProposalDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mProposalDomainServiceMockCreateMessage struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockCreateMessageExpectation
	expectations       []*ProposalDomainServiceMockCreateMessageExpectation

	callArgs []*ProposalDomainServiceMockCreateMessageParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockCreateMessageExpectation specifies expectation struct of the ProposalDomainService.CreateMessage
type ProposalDomainServiceMockCreateMessageExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockCreateMessageParams
	paramPtrs          *ProposalDomainServiceMockCreateMessageParamPtrs
	expectationOrigins ProposalDomainServiceMockCreateMessageExpectationOrigins
	results            *ProposalDomainServiceMockCreateMessageResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockCreateMessageParams contains parameters of the ProposalDomainService.CreateMessage
type ProposalDomainServiceMockCreateMessageParams struct {
	proposalID int64
	message    string
	userID     int64
}

// ProposalDomainServiceMockCreateMessageParamPtrs contains pointers to parameters of the ProposalDomainService.CreateMessage
type ProposalDomainServiceMockCreateMessageParamPtrs struct {
	proposalID *int64
	message    *string
	userID     *int64
}

// ProposalDomainServiceMockCreateMessageResults contains results of the ProposalDomainService.CreateMessage
type ProposalDomainServiceMockCreateMessageResults struct {
	err error
}

// ProposalDomainServiceMockCreateMessageOrigins contains origins of expectations of the ProposalDomainService.CreateMessage
type ProposalDomainServiceMockCreateMessageExpectationOrigins struct {
	origin           string
	originProposalID string
	originMessage    string
	originUserID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Optional() *mProposalDomainServiceMockCreateMessage {
	mmCreateMessage.optional = true
	return mmCreateMessage
}

// Expect sets up expected params for ProposalDomainService.CreateMessage
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Expect(proposalID int64, message string, userID int64) *mProposalDomainServiceMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalDomainServiceMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.paramPtrs != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by ExpectParams functions")
	}

	mmCreateMessage.defaultExpectation.params = &ProposalDomainServiceMockCreateMessageParams{proposalID, message, userID}
	mmCreateMessage.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateMessage.expectations {
		if minimock.Equal(e.params, mmCreateMessage.defaultExpectation.params) {
			mmCreateMessage.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateMessage.defaultExpectation.params)
		}
	}

	return mmCreateMessage
}

// ExpectProposalIDParam1 sets up expected param proposalID for ProposalDomainService.CreateMessage
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) ExpectProposalIDParam1(proposalID int64) *mProposalDomainServiceMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalDomainServiceMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.params != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Expect")
	}

	if mmCreateMessage.defaultExpectation.paramPtrs == nil {
		mmCreateMessage.defaultExpectation.paramPtrs = &ProposalDomainServiceMockCreateMessageParamPtrs{}
	}
	mmCreateMessage.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmCreateMessage.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmCreateMessage
}

// ExpectMessageParam2 sets up expected param message for ProposalDomainService.CreateMessage
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) ExpectMessageParam2(message string) *mProposalDomainServiceMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalDomainServiceMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.params != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Expect")
	}

	if mmCreateMessage.defaultExpectation.paramPtrs == nil {
		mmCreateMessage.defaultExpectation.paramPtrs = &ProposalDomainServiceMockCreateMessageParamPtrs{}
	}
	mmCreateMessage.defaultExpectation.paramPtrs.message = &message
	mmCreateMessage.defaultExpectation.expectationOrigins.originMessage = minimock.CallerInfo(1)

	return mmCreateMessage
}

// ExpectUserIDParam3 sets up expected param userID for ProposalDomainService.CreateMessage
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) ExpectUserIDParam3(userID int64) *mProposalDomainServiceMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalDomainServiceMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.params != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Expect")
	}

	if mmCreateMessage.defaultExpectation.paramPtrs == nil {
		mmCreateMessage.defaultExpectation.paramPtrs = &ProposalDomainServiceMockCreateMessageParamPtrs{}
	}
	mmCreateMessage.defaultExpectation.paramPtrs.userID = &userID
	mmCreateMessage.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmCreateMessage
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.CreateMessage
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Inspect(f func(proposalID int64, message string, userID int64)) *mProposalDomainServiceMockCreateMessage {
	if mmCreateMessage.mock.inspectFuncCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.CreateMessage")
	}

	mmCreateMessage.mock.inspectFuncCreateMessage = f

	return mmCreateMessage
}

// Return sets up results that will be returned by ProposalDomainService.CreateMessage
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Return(err error) *ProposalDomainServiceMock {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalDomainServiceMockCreateMessageExpectation{mock: mmCreateMessage.mock}
	}
	mmCreateMessage.defaultExpectation.results = &ProposalDomainServiceMockCreateMessageResults{err}
	mmCreateMessage.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateMessage.mock
}

// Set uses given function f to mock the ProposalDomainService.CreateMessage method
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Set(f func(proposalID int64, message string, userID int64) (err error)) *ProposalDomainServiceMock {
	if mmCreateMessage.defaultExpectation != nil {
		mmCreateMessage.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.CreateMessage method")
	}

	if len(mmCreateMessage.expectations) > 0 {
		mmCreateMessage.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.CreateMessage method")
	}

	mmCreateMessage.mock.funcCreateMessage = f
	mmCreateMessage.mock.funcCreateMessageOrigin = minimock.CallerInfo(1)
	return mmCreateMessage.mock
}

// When sets expectation for the ProposalDomainService.CreateMessage which will trigger the result defined by the following
// Then helper
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) When(proposalID int64, message string, userID int64) *ProposalDomainServiceMockCreateMessageExpectation {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalDomainServiceMock.CreateMessage mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockCreateMessageExpectation{
		mock:               mmCreateMessage.mock,
		params:             &ProposalDomainServiceMockCreateMessageParams{proposalID, message, userID},
		expectationOrigins: ProposalDomainServiceMockCreateMessageExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateMessage.expectations = append(mmCreateMessage.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.CreateMessage return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockCreateMessageExpectation) Then(err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockCreateMessageResults{err}
	return e.mock
}

// Times sets number of times ProposalDomainService.CreateMessage should be invoked
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Times(n uint64) *mProposalDomainServiceMockCreateMessage {
	if n == 0 {
		mmCreateMessage.mock.t.Fatalf("Times of ProposalDomainServiceMock.CreateMessage mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateMessage.expectedInvocations, n)
	mmCreateMessage.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateMessage
}

func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) invocationsDone() bool {
	if len(mmCreateMessage.expectations) == 0 && mmCreateMessage.defaultExpectation == nil && mmCreateMessage.mock.funcCreateMessage == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateMessage.mock.afterCreateMessageCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateMessage.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateMessage implements mm_service.ProposalDomainService
func (mmCreateMessage *ProposalDomainServiceMock) CreateMessage(proposalID int64, message string, userID int64) (err error) {
	mm_atomic.AddUint64(&mmCreateMessage.beforeCreateMessageCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateMessage.afterCreateMessageCounter, 1)

	mmCreateMessage.t.Helper()

	if mmCreateMessage.inspectFuncCreateMessage != nil {
		mmCreateMessage.inspectFuncCreateMessage(proposalID, message, userID)
	}

	mm_params := ProposalDomainServiceMockCreateMessageParams{proposalID, message, userID}

	// Record call args
	mmCreateMessage.CreateMessageMock.mutex.Lock()
	mmCreateMessage.CreateMessageMock.callArgs = append(mmCreateMessage.CreateMessageMock.callArgs, &mm_params)
	mmCreateMessage.CreateMessageMock.mutex.Unlock()

	for _, e := range mmCreateMessage.CreateMessageMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateMessage.CreateMessageMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateMessage.CreateMessageMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateMessage.CreateMessageMock.defaultExpectation.params
		mm_want_ptrs := mmCreateMessage.CreateMessageMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockCreateMessageParams{proposalID, message, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmCreateMessage.t.Errorf("ProposalDomainServiceMock.CreateMessage got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

			if mm_want_ptrs.message != nil && !minimock.Equal(*mm_want_ptrs.message, mm_got.message) {
				mmCreateMessage.t.Errorf("ProposalDomainServiceMock.CreateMessage got unexpected parameter message, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.originMessage, *mm_want_ptrs.message, mm_got.message, minimock.Diff(*mm_want_ptrs.message, mm_got.message))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmCreateMessage.t.Errorf("ProposalDomainServiceMock.CreateMessage got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateMessage.t.Errorf("ProposalDomainServiceMock.CreateMessage got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateMessage.CreateMessageMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateMessage.t.Fatal("No results are set for the ProposalDomainServiceMock.CreateMessage")
		}
		return (*mm_results).err
	}
	if mmCreateMessage.funcCreateMessage != nil {
		return mmCreateMessage.funcCreateMessage(proposalID, message, userID)
	}
	mmCreateMessage.t.Fatalf("Unexpected call to ProposalDomainServiceMock.CreateMessage. %v %v %v", proposalID, message, userID)
	return
}

// CreateMessageAfterCounter returns a count of finished ProposalDomainServiceMock.CreateMessage invocations
func (mmCreateMessage *ProposalDomainServiceMock) CreateMessageAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateMessage.afterCreateMessageCounter)
}

// CreateMessageBeforeCounter returns a count of ProposalDomainServiceMock.CreateMessage invocations
func (mmCreateMessage *ProposalDomainServiceMock) CreateMessageBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateMessage.beforeCreateMessageCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.CreateMessage.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateMessage *mProposalDomainServiceMockCreateMessage) Calls() []*ProposalDomainServiceMockCreateMessageParams {
	mmCreateMessage.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockCreateMessageParams, len(mmCreateMessage.callArgs))
	copy(argCopy, mmCreateMessage.callArgs)

	mmCreateMessage.mutex.RUnlock()

	return argCopy
}

// MinimockCreateMessageDone returns true if the count of the CreateMessage invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockCreateMessageDone() bool {
	if m.CreateMessageMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMessageMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMessageMock.invocationsDone()
}

// MinimockCreateMessageInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockCreateMessageInspect() {
	for _, e := range m.CreateMessageMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.CreateMessage at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateMessageCounter := mm_atomic.LoadUint64(&m.afterCreateMessageCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMessageMock.defaultExpectation != nil && afterCreateMessageCounter < 1 {
		if m.CreateMessageMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.CreateMessage at\n%s", m.CreateMessageMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.CreateMessage at\n%s with params: %#v", m.CreateMessageMock.defaultExpectation.expectationOrigins.origin, *m.CreateMessageMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateMessage != nil && afterCreateMessageCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.CreateMessage at\n%s", m.funcCreateMessageOrigin)
	}

	if !m.CreateMessageMock.invocationsDone() && afterCreateMessageCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.CreateMessage at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMessageMock.expectedInvocations), m.CreateMessageMock.expectedInvocationsOrigin, afterCreateMessageCounter)
	}
}

type mProposalDomainServiceMockDelete struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockDeleteExpectation
	expectations       []*ProposalDomainServiceMockDeleteExpectation

	callArgs []*ProposalDomainServiceMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockDeleteExpectation specifies expectation struct of the ProposalDomainService.Delete
type ProposalDomainServiceMockDeleteExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockDeleteParams
	paramPtrs          *ProposalDomainServiceMockDeleteParamPtrs
	expectationOrigins ProposalDomainServiceMockDeleteExpectationOrigins
	results            *ProposalDomainServiceMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockDeleteParams contains parameters of the ProposalDomainService.Delete
type ProposalDomainServiceMockDeleteParams struct {
	data proposalentity.Proposal
}

// ProposalDomainServiceMockDeleteParamPtrs contains pointers to parameters of the ProposalDomainService.Delete
type ProposalDomainServiceMockDeleteParamPtrs struct {
	data *proposalentity.Proposal
}

// ProposalDomainServiceMockDeleteResults contains results of the ProposalDomainService.Delete
type ProposalDomainServiceMockDeleteResults struct {
	err error
}

// ProposalDomainServiceMockDeleteOrigins contains origins of expectations of the ProposalDomainService.Delete
type ProposalDomainServiceMockDeleteExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mProposalDomainServiceMockDelete) Optional() *mProposalDomainServiceMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for ProposalDomainService.Delete
func (mmDelete *mProposalDomainServiceMockDelete) Expect(data proposalentity.Proposal) *mProposalDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ProposalDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("ProposalDomainServiceMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &ProposalDomainServiceMockDeleteParams{data}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectDataParam1 sets up expected param data for ProposalDomainService.Delete
func (mmDelete *mProposalDomainServiceMockDelete) ExpectDataParam1(data proposalentity.Proposal) *mProposalDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ProposalDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ProposalDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ProposalDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.data = &data
	mmDelete.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.Delete
func (mmDelete *mProposalDomainServiceMockDelete) Inspect(f func(data proposalentity.Proposal)) *mProposalDomainServiceMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by ProposalDomainService.Delete
func (mmDelete *mProposalDomainServiceMockDelete) Return(err error) *ProposalDomainServiceMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ProposalDomainServiceMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &ProposalDomainServiceMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the ProposalDomainService.Delete method
func (mmDelete *mProposalDomainServiceMockDelete) Set(f func(data proposalentity.Proposal) (err error)) *ProposalDomainServiceMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the ProposalDomainService.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mProposalDomainServiceMockDelete) When(data proposalentity.Proposal) *ProposalDomainServiceMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalDomainServiceMock.Delete mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &ProposalDomainServiceMockDeleteParams{data},
		expectationOrigins: ProposalDomainServiceMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.Delete return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockDeleteExpectation) Then(err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockDeleteResults{err}
	return e.mock
}

// Times sets number of times ProposalDomainService.Delete should be invoked
func (mmDelete *mProposalDomainServiceMockDelete) Times(n uint64) *mProposalDomainServiceMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of ProposalDomainServiceMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mProposalDomainServiceMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_service.ProposalDomainService
func (mmDelete *ProposalDomainServiceMock) Delete(data proposalentity.Proposal) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(data)
	}

	mm_params := ProposalDomainServiceMockDeleteParams{data}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockDeleteParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmDelete.t.Errorf("ProposalDomainServiceMock.Delete got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("ProposalDomainServiceMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the ProposalDomainServiceMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(data)
	}
	mmDelete.t.Fatalf("Unexpected call to ProposalDomainServiceMock.Delete. %v", data)
	return
}

// DeleteAfterCounter returns a count of finished ProposalDomainServiceMock.Delete invocations
func (mmDelete *ProposalDomainServiceMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of ProposalDomainServiceMock.Delete invocations
func (mmDelete *ProposalDomainServiceMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mProposalDomainServiceMockDelete) Calls() []*ProposalDomainServiceMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mProposalDomainServiceMockGetAllAsAdmin struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockGetAllAsAdminExpectation
	expectations       []*ProposalDomainServiceMockGetAllAsAdminExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockGetAllAsAdminExpectation specifies expectation struct of the ProposalDomainService.GetAllAsAdmin
type ProposalDomainServiceMockGetAllAsAdminExpectation struct {
	mock *ProposalDomainServiceMock

	results      *ProposalDomainServiceMockGetAllAsAdminResults
	returnOrigin string
	Counter      uint64
}

// ProposalDomainServiceMockGetAllAsAdminResults contains results of the ProposalDomainService.GetAllAsAdmin
type ProposalDomainServiceMockGetAllAsAdminResults struct {
	pa1 []proposalvalueobject.ProposalFull
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) Optional() *mProposalDomainServiceMockGetAllAsAdmin {
	mmGetAllAsAdmin.optional = true
	return mmGetAllAsAdmin
}

// Expect sets up expected params for ProposalDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) Expect() *mProposalDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("ProposalDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &ProposalDomainServiceMockGetAllAsAdminExpectation{}
	}

	return mmGetAllAsAdmin
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) Inspect(f func()) *mProposalDomainServiceMockGetAllAsAdmin {
	if mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.GetAllAsAdmin")
	}

	mmGetAllAsAdmin.mock.inspectFuncGetAllAsAdmin = f

	return mmGetAllAsAdmin
}

// Return sets up results that will be returned by ProposalDomainService.GetAllAsAdmin
func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) Return(pa1 []proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	if mmGetAllAsAdmin.mock.funcGetAllAsAdmin != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("ProposalDomainServiceMock.GetAllAsAdmin mock is already set by Set")
	}

	if mmGetAllAsAdmin.defaultExpectation == nil {
		mmGetAllAsAdmin.defaultExpectation = &ProposalDomainServiceMockGetAllAsAdminExpectation{mock: mmGetAllAsAdmin.mock}
	}
	mmGetAllAsAdmin.defaultExpectation.results = &ProposalDomainServiceMockGetAllAsAdminResults{pa1, err}
	mmGetAllAsAdmin.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Set uses given function f to mock the ProposalDomainService.GetAllAsAdmin method
func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) Set(f func() (pa1 []proposalvalueobject.ProposalFull, err error)) *ProposalDomainServiceMock {
	if mmGetAllAsAdmin.defaultExpectation != nil {
		mmGetAllAsAdmin.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.GetAllAsAdmin method")
	}

	if len(mmGetAllAsAdmin.expectations) > 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.GetAllAsAdmin method")
	}

	mmGetAllAsAdmin.mock.funcGetAllAsAdmin = f
	mmGetAllAsAdmin.mock.funcGetAllAsAdminOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin.mock
}

// Times sets number of times ProposalDomainService.GetAllAsAdmin should be invoked
func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) Times(n uint64) *mProposalDomainServiceMockGetAllAsAdmin {
	if n == 0 {
		mmGetAllAsAdmin.mock.t.Fatalf("Times of ProposalDomainServiceMock.GetAllAsAdmin mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllAsAdmin.expectedInvocations, n)
	mmGetAllAsAdmin.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllAsAdmin
}

func (mmGetAllAsAdmin *mProposalDomainServiceMockGetAllAsAdmin) invocationsDone() bool {
	if len(mmGetAllAsAdmin.expectations) == 0 && mmGetAllAsAdmin.defaultExpectation == nil && mmGetAllAsAdmin.mock.funcGetAllAsAdmin == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.mock.afterGetAllAsAdminCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllAsAdmin.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllAsAdmin implements mm_service.ProposalDomainService
func (mmGetAllAsAdmin *ProposalDomainServiceMock) GetAllAsAdmin() (pa1 []proposalvalueobject.ProposalFull, err error) {
	mm_atomic.AddUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter, 1)

	mmGetAllAsAdmin.t.Helper()

	if mmGetAllAsAdmin.inspectFuncGetAllAsAdmin != nil {
		mmGetAllAsAdmin.inspectFuncGetAllAsAdmin()
	}

	if mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllAsAdmin.GetAllAsAdminMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllAsAdmin.t.Fatal("No results are set for the ProposalDomainServiceMock.GetAllAsAdmin")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAllAsAdmin.funcGetAllAsAdmin != nil {
		return mmGetAllAsAdmin.funcGetAllAsAdmin()
	}
	mmGetAllAsAdmin.t.Fatalf("Unexpected call to ProposalDomainServiceMock.GetAllAsAdmin.")
	return
}

// GetAllAsAdminAfterCounter returns a count of finished ProposalDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *ProposalDomainServiceMock) GetAllAsAdminAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.afterGetAllAsAdminCounter)
}

// GetAllAsAdminBeforeCounter returns a count of ProposalDomainServiceMock.GetAllAsAdmin invocations
func (mmGetAllAsAdmin *ProposalDomainServiceMock) GetAllAsAdminBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllAsAdmin.beforeGetAllAsAdminCounter)
}

// MinimockGetAllAsAdminDone returns true if the count of the GetAllAsAdmin invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockGetAllAsAdminDone() bool {
	if m.GetAllAsAdminMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllAsAdminMock.invocationsDone()
}

// MinimockGetAllAsAdminInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockGetAllAsAdminInspect() {
	for _, e := range m.GetAllAsAdminMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ProposalDomainServiceMock.GetAllAsAdmin")
		}
	}

	afterGetAllAsAdminCounter := mm_atomic.LoadUint64(&m.afterGetAllAsAdminCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllAsAdminMock.defaultExpectation != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetAllAsAdmin at\n%s", m.GetAllAsAdminMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllAsAdmin != nil && afterGetAllAsAdminCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetAllAsAdmin at\n%s", m.funcGetAllAsAdminOrigin)
	}

	if !m.GetAllAsAdminMock.invocationsDone() && afterGetAllAsAdminCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.GetAllAsAdmin at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllAsAdminMock.expectedInvocations), m.GetAllAsAdminMock.expectedInvocationsOrigin, afterGetAllAsAdminCounter)
	}
}

type mProposalDomainServiceMockGetByID struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockGetByIDExpectation
	expectations       []*ProposalDomainServiceMockGetByIDExpectation

	callArgs []*ProposalDomainServiceMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockGetByIDExpectation specifies expectation struct of the ProposalDomainService.GetByID
type ProposalDomainServiceMockGetByIDExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockGetByIDParams
	paramPtrs          *ProposalDomainServiceMockGetByIDParamPtrs
	expectationOrigins ProposalDomainServiceMockGetByIDExpectationOrigins
	results            *ProposalDomainServiceMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockGetByIDParams contains parameters of the ProposalDomainService.GetByID
type ProposalDomainServiceMockGetByIDParams struct {
	id int64
}

// ProposalDomainServiceMockGetByIDParamPtrs contains pointers to parameters of the ProposalDomainService.GetByID
type ProposalDomainServiceMockGetByIDParamPtrs struct {
	id *int64
}

// ProposalDomainServiceMockGetByIDResults contains results of the ProposalDomainService.GetByID
type ProposalDomainServiceMockGetByIDResults struct {
	p1  proposalvalueobject.ProposalFull
	err error
}

// ProposalDomainServiceMockGetByIDOrigins contains origins of expectations of the ProposalDomainService.GetByID
type ProposalDomainServiceMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mProposalDomainServiceMockGetByID) Optional() *mProposalDomainServiceMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ProposalDomainService.GetByID
func (mmGetByID *mProposalDomainServiceMockGetByID) Expect(id int64) *mProposalDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ProposalDomainServiceMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ProposalDomainServiceMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ProposalDomainService.GetByID
func (mmGetByID *mProposalDomainServiceMockGetByID) ExpectIdParam1(id int64) *mProposalDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ProposalDomainServiceMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ProposalDomainServiceMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.GetByID
func (mmGetByID *mProposalDomainServiceMockGetByID) Inspect(f func(id int64)) *mProposalDomainServiceMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ProposalDomainService.GetByID
func (mmGetByID *mProposalDomainServiceMockGetByID) Return(p1 proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalDomainServiceMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ProposalDomainServiceMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ProposalDomainService.GetByID method
func (mmGetByID *mProposalDomainServiceMockGetByID) Set(f func(id int64) (p1 proposalvalueobject.ProposalFull, err error)) *ProposalDomainServiceMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ProposalDomainService.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mProposalDomainServiceMockGetByID) When(id int64) *ProposalDomainServiceMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalDomainServiceMock.GetByID mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ProposalDomainServiceMockGetByIDParams{id},
		expectationOrigins: ProposalDomainServiceMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.GetByID return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockGetByIDExpectation) Then(p1 proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.GetByID should be invoked
func (mmGetByID *mProposalDomainServiceMockGetByID) Times(n uint64) *mProposalDomainServiceMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ProposalDomainServiceMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mProposalDomainServiceMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_service.ProposalDomainService
func (mmGetByID *ProposalDomainServiceMock) GetByID(id int64) (p1 proposalvalueobject.ProposalFull, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ProposalDomainServiceMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ProposalDomainServiceMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ProposalDomainServiceMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ProposalDomainServiceMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ProposalDomainServiceMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ProposalDomainServiceMock.GetByID invocations
func (mmGetByID *ProposalDomainServiceMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ProposalDomainServiceMock.GetByID invocations
func (mmGetByID *ProposalDomainServiceMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mProposalDomainServiceMockGetByID) Calls() []*ProposalDomainServiceMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mProposalDomainServiceMockGetByProductID struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockGetByProductIDExpectation
	expectations       []*ProposalDomainServiceMockGetByProductIDExpectation

	callArgs []*ProposalDomainServiceMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockGetByProductIDExpectation specifies expectation struct of the ProposalDomainService.GetByProductID
type ProposalDomainServiceMockGetByProductIDExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockGetByProductIDParams
	paramPtrs          *ProposalDomainServiceMockGetByProductIDParamPtrs
	expectationOrigins ProposalDomainServiceMockGetByProductIDExpectationOrigins
	results            *ProposalDomainServiceMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockGetByProductIDParams contains parameters of the ProposalDomainService.GetByProductID
type ProposalDomainServiceMockGetByProductIDParams struct {
	productID int64
}

// ProposalDomainServiceMockGetByProductIDParamPtrs contains pointers to parameters of the ProposalDomainService.GetByProductID
type ProposalDomainServiceMockGetByProductIDParamPtrs struct {
	productID *int64
}

// ProposalDomainServiceMockGetByProductIDResults contains results of the ProposalDomainService.GetByProductID
type ProposalDomainServiceMockGetByProductIDResults struct {
	pa1 []proposalentity.Proposal
	err error
}

// ProposalDomainServiceMockGetByProductIDOrigins contains origins of expectations of the ProposalDomainService.GetByProductID
type ProposalDomainServiceMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Optional() *mProposalDomainServiceMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for ProposalDomainService.GetByProductID
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Expect(productID int64) *mProposalDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &ProposalDomainServiceMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ProposalDomainService.GetByProductID
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) ExpectProductIDParam1(productID int64) *mProposalDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &ProposalDomainServiceMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.GetByProductID
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Inspect(f func(productID int64)) *mProposalDomainServiceMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by ProposalDomainService.GetByProductID
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Return(pa1 []proposalentity.Proposal, err error) *ProposalDomainServiceMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &ProposalDomainServiceMockGetByProductIDResults{pa1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the ProposalDomainService.GetByProductID method
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Set(f func(productID int64) (pa1 []proposalentity.Proposal, err error)) *ProposalDomainServiceMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the ProposalDomainService.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) When(productID int64) *ProposalDomainServiceMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductID mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &ProposalDomainServiceMockGetByProductIDParams{productID},
		expectationOrigins: ProposalDomainServiceMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.GetByProductID return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockGetByProductIDExpectation) Then(pa1 []proposalentity.Proposal, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockGetByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.GetByProductID should be invoked
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Times(n uint64) *mProposalDomainServiceMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of ProposalDomainServiceMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_service.ProposalDomainService
func (mmGetByProductID *ProposalDomainServiceMock) GetByProductID(productID int64) (pa1 []proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := ProposalDomainServiceMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("ProposalDomainServiceMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("ProposalDomainServiceMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the ProposalDomainServiceMock.GetByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to ProposalDomainServiceMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished ProposalDomainServiceMock.GetByProductID invocations
func (mmGetByProductID *ProposalDomainServiceMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of ProposalDomainServiceMock.GetByProductID invocations
func (mmGetByProductID *ProposalDomainServiceMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mProposalDomainServiceMockGetByProductID) Calls() []*ProposalDomainServiceMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mProposalDomainServiceMockGetByProductIDAndProposalID struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation
	expectations       []*ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation

	callArgs []*ProposalDomainServiceMockGetByProductIDAndProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation specifies expectation struct of the ProposalDomainService.GetByProductIDAndProposalID
type ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockGetByProductIDAndProposalIDParams
	paramPtrs          *ProposalDomainServiceMockGetByProductIDAndProposalIDParamPtrs
	expectationOrigins ProposalDomainServiceMockGetByProductIDAndProposalIDExpectationOrigins
	results            *ProposalDomainServiceMockGetByProductIDAndProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockGetByProductIDAndProposalIDParams contains parameters of the ProposalDomainService.GetByProductIDAndProposalID
type ProposalDomainServiceMockGetByProductIDAndProposalIDParams struct {
	productID  int64
	proposalID int64
}

// ProposalDomainServiceMockGetByProductIDAndProposalIDParamPtrs contains pointers to parameters of the ProposalDomainService.GetByProductIDAndProposalID
type ProposalDomainServiceMockGetByProductIDAndProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
}

// ProposalDomainServiceMockGetByProductIDAndProposalIDResults contains results of the ProposalDomainService.GetByProductIDAndProposalID
type ProposalDomainServiceMockGetByProductIDAndProposalIDResults struct {
	p1  proposalvalueobject.ProposalFull
	err error
}

// ProposalDomainServiceMockGetByProductIDAndProposalIDOrigins contains origins of expectations of the ProposalDomainService.GetByProductIDAndProposalID
type ProposalDomainServiceMockGetByProductIDAndProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Optional() *mProposalDomainServiceMockGetByProductIDAndProposalID {
	mmGetByProductIDAndProposalID.optional = true
	return mmGetByProductIDAndProposalID
}

// Expect sets up expected params for ProposalDomainService.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Expect(productID int64, proposalID int64) *mProposalDomainServiceMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by ExpectParams functions")
	}

	mmGetByProductIDAndProposalID.defaultExpectation.params = &ProposalDomainServiceMockGetByProductIDAndProposalIDParams{productID, proposalID}
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductIDAndProposalID.expectations {
		if minimock.Equal(e.params, mmGetByProductIDAndProposalID.defaultExpectation.params) {
			mmGetByProductIDAndProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductIDAndProposalID.defaultExpectation.params)
		}
	}

	return mmGetByProductIDAndProposalID
}

// ExpectProductIDParam1 sets up expected param productID for ProposalDomainService.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) ExpectProductIDParam1(productID int64) *mProposalDomainServiceMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.params != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Expect")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs = &ProposalDomainServiceMockGetByProductIDAndProposalIDParamPtrs{}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductIDAndProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for ProposalDomainService.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) ExpectProposalIDParam2(proposalID int64) *mProposalDomainServiceMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.params != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Expect")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs = &ProposalDomainServiceMockGetByProductIDAndProposalIDParamPtrs{}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetByProductIDAndProposalID
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Inspect(f func(productID int64, proposalID int64)) *mProposalDomainServiceMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.inspectFuncGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.GetByProductIDAndProposalID")
	}

	mmGetByProductIDAndProposalID.mock.inspectFuncGetByProductIDAndProposalID = f

	return mmGetByProductIDAndProposalID
}

// Return sets up results that will be returned by ProposalDomainService.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Return(p1 proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation{mock: mmGetByProductIDAndProposalID.mock}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.results = &ProposalDomainServiceMockGetByProductIDAndProposalIDResults{p1, err}
	mmGetByProductIDAndProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID.mock
}

// Set uses given function f to mock the ProposalDomainService.GetByProductIDAndProposalID method
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Set(f func(productID int64, proposalID int64) (p1 proposalvalueobject.ProposalFull, err error)) *ProposalDomainServiceMock {
	if mmGetByProductIDAndProposalID.defaultExpectation != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.GetByProductIDAndProposalID method")
	}

	if len(mmGetByProductIDAndProposalID.expectations) > 0 {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.GetByProductIDAndProposalID method")
	}

	mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID = f
	mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID.mock
}

// When sets expectation for the ProposalDomainService.GetByProductIDAndProposalID which will trigger the result defined by the following
// Then helper
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) When(productID int64, proposalID int64) *ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalDomainServiceMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation{
		mock:               mmGetByProductIDAndProposalID.mock,
		params:             &ProposalDomainServiceMockGetByProductIDAndProposalIDParams{productID, proposalID},
		expectationOrigins: ProposalDomainServiceMockGetByProductIDAndProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductIDAndProposalID.expectations = append(mmGetByProductIDAndProposalID.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.GetByProductIDAndProposalID return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockGetByProductIDAndProposalIDExpectation) Then(p1 proposalvalueobject.ProposalFull, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockGetByProductIDAndProposalIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.GetByProductIDAndProposalID should be invoked
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Times(n uint64) *mProposalDomainServiceMockGetByProductIDAndProposalID {
	if n == 0 {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Times of ProposalDomainServiceMock.GetByProductIDAndProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductIDAndProposalID.expectedInvocations, n)
	mmGetByProductIDAndProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID
}

func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) invocationsDone() bool {
	if len(mmGetByProductIDAndProposalID.expectations) == 0 && mmGetByProductIDAndProposalID.defaultExpectation == nil && mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.mock.afterGetByProductIDAndProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductIDAndProposalID implements mm_service.ProposalDomainService
func (mmGetByProductIDAndProposalID *ProposalDomainServiceMock) GetByProductIDAndProposalID(productID int64, proposalID int64) (p1 proposalvalueobject.ProposalFull, err error) {
	mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.beforeGetByProductIDAndProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.afterGetByProductIDAndProposalIDCounter, 1)

	mmGetByProductIDAndProposalID.t.Helper()

	if mmGetByProductIDAndProposalID.inspectFuncGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.inspectFuncGetByProductIDAndProposalID(productID, proposalID)
	}

	mm_params := ProposalDomainServiceMockGetByProductIDAndProposalIDParams{productID, proposalID}

	// Record call args
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.mutex.Lock()
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.callArgs = append(mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.callArgs, &mm_params)
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.mutex.Unlock()

	for _, e := range mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockGetByProductIDAndProposalIDParams{productID, proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductIDAndProposalID.t.Errorf("ProposalDomainServiceMock.GetByProductIDAndProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetByProductIDAndProposalID.t.Errorf("ProposalDomainServiceMock.GetByProductIDAndProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductIDAndProposalID.t.Errorf("ProposalDomainServiceMock.GetByProductIDAndProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductIDAndProposalID.t.Fatal("No results are set for the ProposalDomainServiceMock.GetByProductIDAndProposalID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByProductIDAndProposalID.funcGetByProductIDAndProposalID != nil {
		return mmGetByProductIDAndProposalID.funcGetByProductIDAndProposalID(productID, proposalID)
	}
	mmGetByProductIDAndProposalID.t.Fatalf("Unexpected call to ProposalDomainServiceMock.GetByProductIDAndProposalID. %v %v", productID, proposalID)
	return
}

// GetByProductIDAndProposalIDAfterCounter returns a count of finished ProposalDomainServiceMock.GetByProductIDAndProposalID invocations
func (mmGetByProductIDAndProposalID *ProposalDomainServiceMock) GetByProductIDAndProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.afterGetByProductIDAndProposalIDCounter)
}

// GetByProductIDAndProposalIDBeforeCounter returns a count of ProposalDomainServiceMock.GetByProductIDAndProposalID invocations
func (mmGetByProductIDAndProposalID *ProposalDomainServiceMock) GetByProductIDAndProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.beforeGetByProductIDAndProposalIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.GetByProductIDAndProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductIDAndProposalID *mProposalDomainServiceMockGetByProductIDAndProposalID) Calls() []*ProposalDomainServiceMockGetByProductIDAndProposalIDParams {
	mmGetByProductIDAndProposalID.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockGetByProductIDAndProposalIDParams, len(mmGetByProductIDAndProposalID.callArgs))
	copy(argCopy, mmGetByProductIDAndProposalID.callArgs)

	mmGetByProductIDAndProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDAndProposalIDDone returns true if the count of the GetByProductIDAndProposalID invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockGetByProductIDAndProposalIDDone() bool {
	if m.GetByProductIDAndProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDAndProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDAndProposalIDMock.invocationsDone()
}

// MinimockGetByProductIDAndProposalIDInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockGetByProductIDAndProposalIDInspect() {
	for _, e := range m.GetByProductIDAndProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductIDAndProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDAndProposalIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDAndProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDAndProposalIDMock.defaultExpectation != nil && afterGetByProductIDAndProposalIDCounter < 1 {
		if m.GetByProductIDAndProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductIDAndProposalID at\n%s", m.GetByProductIDAndProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductIDAndProposalID at\n%s with params: %#v", m.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDAndProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductIDAndProposalID != nil && afterGetByProductIDAndProposalIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetByProductIDAndProposalID at\n%s", m.funcGetByProductIDAndProposalIDOrigin)
	}

	if !m.GetByProductIDAndProposalIDMock.invocationsDone() && afterGetByProductIDAndProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.GetByProductIDAndProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDAndProposalIDMock.expectedInvocations), m.GetByProductIDAndProposalIDMock.expectedInvocationsOrigin, afterGetByProductIDAndProposalIDCounter)
	}
}

type mProposalDomainServiceMockGetHistory struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockGetHistoryExpectation
	expectations       []*ProposalDomainServiceMockGetHistoryExpectation

	callArgs []*ProposalDomainServiceMockGetHistoryParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockGetHistoryExpectation specifies expectation struct of the ProposalDomainService.GetHistory
type ProposalDomainServiceMockGetHistoryExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockGetHistoryParams
	paramPtrs          *ProposalDomainServiceMockGetHistoryParamPtrs
	expectationOrigins ProposalDomainServiceMockGetHistoryExpectationOrigins
	results            *ProposalDomainServiceMockGetHistoryResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockGetHistoryParams contains parameters of the ProposalDomainService.GetHistory
type ProposalDomainServiceMockGetHistoryParams struct {
	proposalID int64
}

// ProposalDomainServiceMockGetHistoryParamPtrs contains pointers to parameters of the ProposalDomainService.GetHistory
type ProposalDomainServiceMockGetHistoryParamPtrs struct {
	proposalID *int64
}

// ProposalDomainServiceMockGetHistoryResults contains results of the ProposalDomainService.GetHistory
type ProposalDomainServiceMockGetHistoryResults struct {
	ha1 []proposalvalueobject.HistoryRecord
	err error
}

// ProposalDomainServiceMockGetHistoryOrigins contains origins of expectations of the ProposalDomainService.GetHistory
type ProposalDomainServiceMockGetHistoryExpectationOrigins struct {
	origin           string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Optional() *mProposalDomainServiceMockGetHistory {
	mmGetHistory.optional = true
	return mmGetHistory
}

// Expect sets up expected params for ProposalDomainService.GetHistory
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Expect(proposalID int64) *mProposalDomainServiceMockGetHistory {
	if mmGetHistory.mock.funcGetHistory != nil {
		mmGetHistory.mock.t.Fatalf("ProposalDomainServiceMock.GetHistory mock is already set by Set")
	}

	if mmGetHistory.defaultExpectation == nil {
		mmGetHistory.defaultExpectation = &ProposalDomainServiceMockGetHistoryExpectation{}
	}

	if mmGetHistory.defaultExpectation.paramPtrs != nil {
		mmGetHistory.mock.t.Fatalf("ProposalDomainServiceMock.GetHistory mock is already set by ExpectParams functions")
	}

	mmGetHistory.defaultExpectation.params = &ProposalDomainServiceMockGetHistoryParams{proposalID}
	mmGetHistory.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetHistory.expectations {
		if minimock.Equal(e.params, mmGetHistory.defaultExpectation.params) {
			mmGetHistory.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetHistory.defaultExpectation.params)
		}
	}

	return mmGetHistory
}

// ExpectProposalIDParam1 sets up expected param proposalID for ProposalDomainService.GetHistory
func (mmGetHistory *mProposalDomainServiceMockGetHistory) ExpectProposalIDParam1(proposalID int64) *mProposalDomainServiceMockGetHistory {
	if mmGetHistory.mock.funcGetHistory != nil {
		mmGetHistory.mock.t.Fatalf("ProposalDomainServiceMock.GetHistory mock is already set by Set")
	}

	if mmGetHistory.defaultExpectation == nil {
		mmGetHistory.defaultExpectation = &ProposalDomainServiceMockGetHistoryExpectation{}
	}

	if mmGetHistory.defaultExpectation.params != nil {
		mmGetHistory.mock.t.Fatalf("ProposalDomainServiceMock.GetHistory mock is already set by Expect")
	}

	if mmGetHistory.defaultExpectation.paramPtrs == nil {
		mmGetHistory.defaultExpectation.paramPtrs = &ProposalDomainServiceMockGetHistoryParamPtrs{}
	}
	mmGetHistory.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetHistory.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetHistory
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.GetHistory
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Inspect(f func(proposalID int64)) *mProposalDomainServiceMockGetHistory {
	if mmGetHistory.mock.inspectFuncGetHistory != nil {
		mmGetHistory.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.GetHistory")
	}

	mmGetHistory.mock.inspectFuncGetHistory = f

	return mmGetHistory
}

// Return sets up results that will be returned by ProposalDomainService.GetHistory
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Return(ha1 []proposalvalueobject.HistoryRecord, err error) *ProposalDomainServiceMock {
	if mmGetHistory.mock.funcGetHistory != nil {
		mmGetHistory.mock.t.Fatalf("ProposalDomainServiceMock.GetHistory mock is already set by Set")
	}

	if mmGetHistory.defaultExpectation == nil {
		mmGetHistory.defaultExpectation = &ProposalDomainServiceMockGetHistoryExpectation{mock: mmGetHistory.mock}
	}
	mmGetHistory.defaultExpectation.results = &ProposalDomainServiceMockGetHistoryResults{ha1, err}
	mmGetHistory.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetHistory.mock
}

// Set uses given function f to mock the ProposalDomainService.GetHistory method
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Set(f func(proposalID int64) (ha1 []proposalvalueobject.HistoryRecord, err error)) *ProposalDomainServiceMock {
	if mmGetHistory.defaultExpectation != nil {
		mmGetHistory.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.GetHistory method")
	}

	if len(mmGetHistory.expectations) > 0 {
		mmGetHistory.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.GetHistory method")
	}

	mmGetHistory.mock.funcGetHistory = f
	mmGetHistory.mock.funcGetHistoryOrigin = minimock.CallerInfo(1)
	return mmGetHistory.mock
}

// When sets expectation for the ProposalDomainService.GetHistory which will trigger the result defined by the following
// Then helper
func (mmGetHistory *mProposalDomainServiceMockGetHistory) When(proposalID int64) *ProposalDomainServiceMockGetHistoryExpectation {
	if mmGetHistory.mock.funcGetHistory != nil {
		mmGetHistory.mock.t.Fatalf("ProposalDomainServiceMock.GetHistory mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockGetHistoryExpectation{
		mock:               mmGetHistory.mock,
		params:             &ProposalDomainServiceMockGetHistoryParams{proposalID},
		expectationOrigins: ProposalDomainServiceMockGetHistoryExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetHistory.expectations = append(mmGetHistory.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.GetHistory return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockGetHistoryExpectation) Then(ha1 []proposalvalueobject.HistoryRecord, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockGetHistoryResults{ha1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.GetHistory should be invoked
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Times(n uint64) *mProposalDomainServiceMockGetHistory {
	if n == 0 {
		mmGetHistory.mock.t.Fatalf("Times of ProposalDomainServiceMock.GetHistory mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetHistory.expectedInvocations, n)
	mmGetHistory.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetHistory
}

func (mmGetHistory *mProposalDomainServiceMockGetHistory) invocationsDone() bool {
	if len(mmGetHistory.expectations) == 0 && mmGetHistory.defaultExpectation == nil && mmGetHistory.mock.funcGetHistory == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetHistory.mock.afterGetHistoryCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetHistory.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetHistory implements mm_service.ProposalDomainService
func (mmGetHistory *ProposalDomainServiceMock) GetHistory(proposalID int64) (ha1 []proposalvalueobject.HistoryRecord, err error) {
	mm_atomic.AddUint64(&mmGetHistory.beforeGetHistoryCounter, 1)
	defer mm_atomic.AddUint64(&mmGetHistory.afterGetHistoryCounter, 1)

	mmGetHistory.t.Helper()

	if mmGetHistory.inspectFuncGetHistory != nil {
		mmGetHistory.inspectFuncGetHistory(proposalID)
	}

	mm_params := ProposalDomainServiceMockGetHistoryParams{proposalID}

	// Record call args
	mmGetHistory.GetHistoryMock.mutex.Lock()
	mmGetHistory.GetHistoryMock.callArgs = append(mmGetHistory.GetHistoryMock.callArgs, &mm_params)
	mmGetHistory.GetHistoryMock.mutex.Unlock()

	for _, e := range mmGetHistory.GetHistoryMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ha1, e.results.err
		}
	}

	if mmGetHistory.GetHistoryMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetHistory.GetHistoryMock.defaultExpectation.Counter, 1)
		mm_want := mmGetHistory.GetHistoryMock.defaultExpectation.params
		mm_want_ptrs := mmGetHistory.GetHistoryMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockGetHistoryParams{proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetHistory.t.Errorf("ProposalDomainServiceMock.GetHistory got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetHistory.GetHistoryMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetHistory.t.Errorf("ProposalDomainServiceMock.GetHistory got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetHistory.GetHistoryMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetHistory.GetHistoryMock.defaultExpectation.results
		if mm_results == nil {
			mmGetHistory.t.Fatal("No results are set for the ProposalDomainServiceMock.GetHistory")
		}
		return (*mm_results).ha1, (*mm_results).err
	}
	if mmGetHistory.funcGetHistory != nil {
		return mmGetHistory.funcGetHistory(proposalID)
	}
	mmGetHistory.t.Fatalf("Unexpected call to ProposalDomainServiceMock.GetHistory. %v", proposalID)
	return
}

// GetHistoryAfterCounter returns a count of finished ProposalDomainServiceMock.GetHistory invocations
func (mmGetHistory *ProposalDomainServiceMock) GetHistoryAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetHistory.afterGetHistoryCounter)
}

// GetHistoryBeforeCounter returns a count of ProposalDomainServiceMock.GetHistory invocations
func (mmGetHistory *ProposalDomainServiceMock) GetHistoryBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetHistory.beforeGetHistoryCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.GetHistory.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetHistory *mProposalDomainServiceMockGetHistory) Calls() []*ProposalDomainServiceMockGetHistoryParams {
	mmGetHistory.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockGetHistoryParams, len(mmGetHistory.callArgs))
	copy(argCopy, mmGetHistory.callArgs)

	mmGetHistory.mutex.RUnlock()

	return argCopy
}

// MinimockGetHistoryDone returns true if the count of the GetHistory invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockGetHistoryDone() bool {
	if m.GetHistoryMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetHistoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetHistoryMock.invocationsDone()
}

// MinimockGetHistoryInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockGetHistoryInspect() {
	for _, e := range m.GetHistoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetHistory at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetHistoryCounter := mm_atomic.LoadUint64(&m.afterGetHistoryCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetHistoryMock.defaultExpectation != nil && afterGetHistoryCounter < 1 {
		if m.GetHistoryMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetHistory at\n%s", m.GetHistoryMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetHistory at\n%s with params: %#v", m.GetHistoryMock.defaultExpectation.expectationOrigins.origin, *m.GetHistoryMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetHistory != nil && afterGetHistoryCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetHistory at\n%s", m.funcGetHistoryOrigin)
	}

	if !m.GetHistoryMock.invocationsDone() && afterGetHistoryCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.GetHistory at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetHistoryMock.expectedInvocations), m.GetHistoryMock.expectedInvocationsOrigin, afterGetHistoryCounter)
	}
}

type mProposalDomainServiceMockGetUnreadEventsForUser struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockGetUnreadEventsForUserExpectation
	expectations       []*ProposalDomainServiceMockGetUnreadEventsForUserExpectation

	callArgs []*ProposalDomainServiceMockGetUnreadEventsForUserParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockGetUnreadEventsForUserExpectation specifies expectation struct of the ProposalDomainService.GetUnreadEventsForUser
type ProposalDomainServiceMockGetUnreadEventsForUserExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockGetUnreadEventsForUserParams
	paramPtrs          *ProposalDomainServiceMockGetUnreadEventsForUserParamPtrs
	expectationOrigins ProposalDomainServiceMockGetUnreadEventsForUserExpectationOrigins
	results            *ProposalDomainServiceMockGetUnreadEventsForUserResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockGetUnreadEventsForUserParams contains parameters of the ProposalDomainService.GetUnreadEventsForUser
type ProposalDomainServiceMockGetUnreadEventsForUserParams struct {
	userID int64
}

// ProposalDomainServiceMockGetUnreadEventsForUserParamPtrs contains pointers to parameters of the ProposalDomainService.GetUnreadEventsForUser
type ProposalDomainServiceMockGetUnreadEventsForUserParamPtrs struct {
	userID *int64
}

// ProposalDomainServiceMockGetUnreadEventsForUserResults contains results of the ProposalDomainService.GetUnreadEventsForUser
type ProposalDomainServiceMockGetUnreadEventsForUserResults struct {
	ha1 []proposalvalueobject.HistoryRecord
	err error
}

// ProposalDomainServiceMockGetUnreadEventsForUserOrigins contains origins of expectations of the ProposalDomainService.GetUnreadEventsForUser
type ProposalDomainServiceMockGetUnreadEventsForUserExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Optional() *mProposalDomainServiceMockGetUnreadEventsForUser {
	mmGetUnreadEventsForUser.optional = true
	return mmGetUnreadEventsForUser
}

// Expect sets up expected params for ProposalDomainService.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Expect(userID int64) *mProposalDomainServiceMockGetUnreadEventsForUser {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalDomainServiceMock.GetUnreadEventsForUser mock is already set by Set")
	}

	if mmGetUnreadEventsForUser.defaultExpectation == nil {
		mmGetUnreadEventsForUser.defaultExpectation = &ProposalDomainServiceMockGetUnreadEventsForUserExpectation{}
	}

	if mmGetUnreadEventsForUser.defaultExpectation.paramPtrs != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalDomainServiceMock.GetUnreadEventsForUser mock is already set by ExpectParams functions")
	}

	mmGetUnreadEventsForUser.defaultExpectation.params = &ProposalDomainServiceMockGetUnreadEventsForUserParams{userID}
	mmGetUnreadEventsForUser.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUnreadEventsForUser.expectations {
		if minimock.Equal(e.params, mmGetUnreadEventsForUser.defaultExpectation.params) {
			mmGetUnreadEventsForUser.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUnreadEventsForUser.defaultExpectation.params)
		}
	}

	return mmGetUnreadEventsForUser
}

// ExpectUserIDParam1 sets up expected param userID for ProposalDomainService.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) ExpectUserIDParam1(userID int64) *mProposalDomainServiceMockGetUnreadEventsForUser {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalDomainServiceMock.GetUnreadEventsForUser mock is already set by Set")
	}

	if mmGetUnreadEventsForUser.defaultExpectation == nil {
		mmGetUnreadEventsForUser.defaultExpectation = &ProposalDomainServiceMockGetUnreadEventsForUserExpectation{}
	}

	if mmGetUnreadEventsForUser.defaultExpectation.params != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalDomainServiceMock.GetUnreadEventsForUser mock is already set by Expect")
	}

	if mmGetUnreadEventsForUser.defaultExpectation.paramPtrs == nil {
		mmGetUnreadEventsForUser.defaultExpectation.paramPtrs = &ProposalDomainServiceMockGetUnreadEventsForUserParamPtrs{}
	}
	mmGetUnreadEventsForUser.defaultExpectation.paramPtrs.userID = &userID
	mmGetUnreadEventsForUser.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUnreadEventsForUser
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Inspect(f func(userID int64)) *mProposalDomainServiceMockGetUnreadEventsForUser {
	if mmGetUnreadEventsForUser.mock.inspectFuncGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.GetUnreadEventsForUser")
	}

	mmGetUnreadEventsForUser.mock.inspectFuncGetUnreadEventsForUser = f

	return mmGetUnreadEventsForUser
}

// Return sets up results that will be returned by ProposalDomainService.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Return(ha1 []proposalvalueobject.HistoryRecord, err error) *ProposalDomainServiceMock {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalDomainServiceMock.GetUnreadEventsForUser mock is already set by Set")
	}

	if mmGetUnreadEventsForUser.defaultExpectation == nil {
		mmGetUnreadEventsForUser.defaultExpectation = &ProposalDomainServiceMockGetUnreadEventsForUserExpectation{mock: mmGetUnreadEventsForUser.mock}
	}
	mmGetUnreadEventsForUser.defaultExpectation.results = &ProposalDomainServiceMockGetUnreadEventsForUserResults{ha1, err}
	mmGetUnreadEventsForUser.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUnreadEventsForUser.mock
}

// Set uses given function f to mock the ProposalDomainService.GetUnreadEventsForUser method
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Set(f func(userID int64) (ha1 []proposalvalueobject.HistoryRecord, err error)) *ProposalDomainServiceMock {
	if mmGetUnreadEventsForUser.defaultExpectation != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.GetUnreadEventsForUser method")
	}

	if len(mmGetUnreadEventsForUser.expectations) > 0 {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.GetUnreadEventsForUser method")
	}

	mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser = f
	mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUserOrigin = minimock.CallerInfo(1)
	return mmGetUnreadEventsForUser.mock
}

// When sets expectation for the ProposalDomainService.GetUnreadEventsForUser which will trigger the result defined by the following
// Then helper
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) When(userID int64) *ProposalDomainServiceMockGetUnreadEventsForUserExpectation {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalDomainServiceMock.GetUnreadEventsForUser mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockGetUnreadEventsForUserExpectation{
		mock:               mmGetUnreadEventsForUser.mock,
		params:             &ProposalDomainServiceMockGetUnreadEventsForUserParams{userID},
		expectationOrigins: ProposalDomainServiceMockGetUnreadEventsForUserExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUnreadEventsForUser.expectations = append(mmGetUnreadEventsForUser.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.GetUnreadEventsForUser return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockGetUnreadEventsForUserExpectation) Then(ha1 []proposalvalueobject.HistoryRecord, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockGetUnreadEventsForUserResults{ha1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.GetUnreadEventsForUser should be invoked
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Times(n uint64) *mProposalDomainServiceMockGetUnreadEventsForUser {
	if n == 0 {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Times of ProposalDomainServiceMock.GetUnreadEventsForUser mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUnreadEventsForUser.expectedInvocations, n)
	mmGetUnreadEventsForUser.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUnreadEventsForUser
}

func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) invocationsDone() bool {
	if len(mmGetUnreadEventsForUser.expectations) == 0 && mmGetUnreadEventsForUser.defaultExpectation == nil && mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.mock.afterGetUnreadEventsForUserCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUnreadEventsForUser implements mm_service.ProposalDomainService
func (mmGetUnreadEventsForUser *ProposalDomainServiceMock) GetUnreadEventsForUser(userID int64) (ha1 []proposalvalueobject.HistoryRecord, err error) {
	mm_atomic.AddUint64(&mmGetUnreadEventsForUser.beforeGetUnreadEventsForUserCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUnreadEventsForUser.afterGetUnreadEventsForUserCounter, 1)

	mmGetUnreadEventsForUser.t.Helper()

	if mmGetUnreadEventsForUser.inspectFuncGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.inspectFuncGetUnreadEventsForUser(userID)
	}

	mm_params := ProposalDomainServiceMockGetUnreadEventsForUserParams{userID}

	// Record call args
	mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.mutex.Lock()
	mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.callArgs = append(mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.callArgs, &mm_params)
	mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.mutex.Unlock()

	for _, e := range mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ha1, e.results.err
		}
	}

	if mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.params
		mm_want_ptrs := mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockGetUnreadEventsForUserParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUnreadEventsForUser.t.Errorf("ProposalDomainServiceMock.GetUnreadEventsForUser got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUnreadEventsForUser.t.Errorf("ProposalDomainServiceMock.GetUnreadEventsForUser got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUnreadEventsForUser.t.Fatal("No results are set for the ProposalDomainServiceMock.GetUnreadEventsForUser")
		}
		return (*mm_results).ha1, (*mm_results).err
	}
	if mmGetUnreadEventsForUser.funcGetUnreadEventsForUser != nil {
		return mmGetUnreadEventsForUser.funcGetUnreadEventsForUser(userID)
	}
	mmGetUnreadEventsForUser.t.Fatalf("Unexpected call to ProposalDomainServiceMock.GetUnreadEventsForUser. %v", userID)
	return
}

// GetUnreadEventsForUserAfterCounter returns a count of finished ProposalDomainServiceMock.GetUnreadEventsForUser invocations
func (mmGetUnreadEventsForUser *ProposalDomainServiceMock) GetUnreadEventsForUserAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.afterGetUnreadEventsForUserCounter)
}

// GetUnreadEventsForUserBeforeCounter returns a count of ProposalDomainServiceMock.GetUnreadEventsForUser invocations
func (mmGetUnreadEventsForUser *ProposalDomainServiceMock) GetUnreadEventsForUserBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.beforeGetUnreadEventsForUserCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.GetUnreadEventsForUser.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUnreadEventsForUser *mProposalDomainServiceMockGetUnreadEventsForUser) Calls() []*ProposalDomainServiceMockGetUnreadEventsForUserParams {
	mmGetUnreadEventsForUser.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockGetUnreadEventsForUserParams, len(mmGetUnreadEventsForUser.callArgs))
	copy(argCopy, mmGetUnreadEventsForUser.callArgs)

	mmGetUnreadEventsForUser.mutex.RUnlock()

	return argCopy
}

// MinimockGetUnreadEventsForUserDone returns true if the count of the GetUnreadEventsForUser invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockGetUnreadEventsForUserDone() bool {
	if m.GetUnreadEventsForUserMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUnreadEventsForUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUnreadEventsForUserMock.invocationsDone()
}

// MinimockGetUnreadEventsForUserInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockGetUnreadEventsForUserInspect() {
	for _, e := range m.GetUnreadEventsForUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetUnreadEventsForUser at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUnreadEventsForUserCounter := mm_atomic.LoadUint64(&m.afterGetUnreadEventsForUserCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUnreadEventsForUserMock.defaultExpectation != nil && afterGetUnreadEventsForUserCounter < 1 {
		if m.GetUnreadEventsForUserMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetUnreadEventsForUser at\n%s", m.GetUnreadEventsForUserMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.GetUnreadEventsForUser at\n%s with params: %#v", m.GetUnreadEventsForUserMock.defaultExpectation.expectationOrigins.origin, *m.GetUnreadEventsForUserMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUnreadEventsForUser != nil && afterGetUnreadEventsForUserCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.GetUnreadEventsForUser at\n%s", m.funcGetUnreadEventsForUserOrigin)
	}

	if !m.GetUnreadEventsForUserMock.invocationsDone() && afterGetUnreadEventsForUserCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.GetUnreadEventsForUser at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUnreadEventsForUserMock.expectedInvocations), m.GetUnreadEventsForUserMock.expectedInvocationsOrigin, afterGetUnreadEventsForUserCounter)
	}
}

type mProposalDomainServiceMockUpdate struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockUpdateExpectation
	expectations       []*ProposalDomainServiceMockUpdateExpectation

	callArgs []*ProposalDomainServiceMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockUpdateExpectation specifies expectation struct of the ProposalDomainService.Update
type ProposalDomainServiceMockUpdateExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockUpdateParams
	paramPtrs          *ProposalDomainServiceMockUpdateParamPtrs
	expectationOrigins ProposalDomainServiceMockUpdateExpectationOrigins
	results            *ProposalDomainServiceMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockUpdateParams contains parameters of the ProposalDomainService.Update
type ProposalDomainServiceMockUpdateParams struct {
	ctx  context.Context
	data proposalaggregate.ProposalUpdate
}

// ProposalDomainServiceMockUpdateParamPtrs contains pointers to parameters of the ProposalDomainService.Update
type ProposalDomainServiceMockUpdateParamPtrs struct {
	ctx  *context.Context
	data *proposalaggregate.ProposalUpdate
}

// ProposalDomainServiceMockUpdateResults contains results of the ProposalDomainService.Update
type ProposalDomainServiceMockUpdateResults struct {
	p1  proposalaggregate.Proposal
	err error
}

// ProposalDomainServiceMockUpdateOrigins contains origins of expectations of the ProposalDomainService.Update
type ProposalDomainServiceMockUpdateExpectationOrigins struct {
	origin     string
	originCtx  string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mProposalDomainServiceMockUpdate) Optional() *mProposalDomainServiceMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for ProposalDomainService.Update
func (mmUpdate *mProposalDomainServiceMockUpdate) Expect(ctx context.Context, data proposalaggregate.ProposalUpdate) *mProposalDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &ProposalDomainServiceMockUpdateParams{ctx, data}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectCtxParam1 sets up expected param ctx for ProposalDomainService.Update
func (mmUpdate *mProposalDomainServiceMockUpdate) ExpectCtxParam1(ctx context.Context) *mProposalDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ProposalDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdate
}

// ExpectDataParam2 sets up expected param data for ProposalDomainService.Update
func (mmUpdate *mProposalDomainServiceMockUpdate) ExpectDataParam2(data proposalaggregate.ProposalUpdate) *mProposalDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ProposalDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.data = &data
	mmUpdate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.Update
func (mmUpdate *mProposalDomainServiceMockUpdate) Inspect(f func(ctx context.Context, data proposalaggregate.ProposalUpdate)) *mProposalDomainServiceMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by ProposalDomainService.Update
func (mmUpdate *mProposalDomainServiceMockUpdate) Return(p1 proposalaggregate.Proposal, err error) *ProposalDomainServiceMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalDomainServiceMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &ProposalDomainServiceMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the ProposalDomainService.Update method
func (mmUpdate *mProposalDomainServiceMockUpdate) Set(f func(ctx context.Context, data proposalaggregate.ProposalUpdate) (p1 proposalaggregate.Proposal, err error)) *ProposalDomainServiceMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the ProposalDomainService.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mProposalDomainServiceMockUpdate) When(ctx context.Context, data proposalaggregate.ProposalUpdate) *ProposalDomainServiceMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalDomainServiceMock.Update mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &ProposalDomainServiceMockUpdateParams{ctx, data},
		expectationOrigins: ProposalDomainServiceMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.Update return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockUpdateExpectation) Then(p1 proposalaggregate.Proposal, err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalDomainService.Update should be invoked
func (mmUpdate *mProposalDomainServiceMockUpdate) Times(n uint64) *mProposalDomainServiceMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of ProposalDomainServiceMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mProposalDomainServiceMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_service.ProposalDomainService
func (mmUpdate *ProposalDomainServiceMock) Update(ctx context.Context, data proposalaggregate.ProposalUpdate) (p1 proposalaggregate.Proposal, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(ctx, data)
	}

	mm_params := ProposalDomainServiceMockUpdateParams{ctx, data}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockUpdateParams{ctx, data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdate.t.Errorf("ProposalDomainServiceMock.Update got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdate.t.Errorf("ProposalDomainServiceMock.Update got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("ProposalDomainServiceMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the ProposalDomainServiceMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(ctx, data)
	}
	mmUpdate.t.Fatalf("Unexpected call to ProposalDomainServiceMock.Update. %v %v", ctx, data)
	return
}

// UpdateAfterCounter returns a count of finished ProposalDomainServiceMock.Update invocations
func (mmUpdate *ProposalDomainServiceMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of ProposalDomainServiceMock.Update invocations
func (mmUpdate *ProposalDomainServiceMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mProposalDomainServiceMockUpdate) Calls() []*ProposalDomainServiceMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

type mProposalDomainServiceMockUpdateLastViewed struct {
	optional           bool
	mock               *ProposalDomainServiceMock
	defaultExpectation *ProposalDomainServiceMockUpdateLastViewedExpectation
	expectations       []*ProposalDomainServiceMockUpdateLastViewedExpectation

	callArgs []*ProposalDomainServiceMockUpdateLastViewedParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalDomainServiceMockUpdateLastViewedExpectation specifies expectation struct of the ProposalDomainService.UpdateLastViewed
type ProposalDomainServiceMockUpdateLastViewedExpectation struct {
	mock               *ProposalDomainServiceMock
	params             *ProposalDomainServiceMockUpdateLastViewedParams
	paramPtrs          *ProposalDomainServiceMockUpdateLastViewedParamPtrs
	expectationOrigins ProposalDomainServiceMockUpdateLastViewedExpectationOrigins
	results            *ProposalDomainServiceMockUpdateLastViewedResults
	returnOrigin       string
	Counter            uint64
}

// ProposalDomainServiceMockUpdateLastViewedParams contains parameters of the ProposalDomainService.UpdateLastViewed
type ProposalDomainServiceMockUpdateLastViewedParams struct {
	ctx  context.Context
	data proposalvalueobject.UserView
}

// ProposalDomainServiceMockUpdateLastViewedParamPtrs contains pointers to parameters of the ProposalDomainService.UpdateLastViewed
type ProposalDomainServiceMockUpdateLastViewedParamPtrs struct {
	ctx  *context.Context
	data *proposalvalueobject.UserView
}

// ProposalDomainServiceMockUpdateLastViewedResults contains results of the ProposalDomainService.UpdateLastViewed
type ProposalDomainServiceMockUpdateLastViewedResults struct {
	err error
}

// ProposalDomainServiceMockUpdateLastViewedOrigins contains origins of expectations of the ProposalDomainService.UpdateLastViewed
type ProposalDomainServiceMockUpdateLastViewedExpectationOrigins struct {
	origin     string
	originCtx  string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Optional() *mProposalDomainServiceMockUpdateLastViewed {
	mmUpdateLastViewed.optional = true
	return mmUpdateLastViewed
}

// Expect sets up expected params for ProposalDomainService.UpdateLastViewed
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Expect(ctx context.Context, data proposalvalueobject.UserView) *mProposalDomainServiceMockUpdateLastViewed {
	if mmUpdateLastViewed.mock.funcUpdateLastViewed != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Set")
	}

	if mmUpdateLastViewed.defaultExpectation == nil {
		mmUpdateLastViewed.defaultExpectation = &ProposalDomainServiceMockUpdateLastViewedExpectation{}
	}

	if mmUpdateLastViewed.defaultExpectation.paramPtrs != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by ExpectParams functions")
	}

	mmUpdateLastViewed.defaultExpectation.params = &ProposalDomainServiceMockUpdateLastViewedParams{ctx, data}
	mmUpdateLastViewed.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateLastViewed.expectations {
		if minimock.Equal(e.params, mmUpdateLastViewed.defaultExpectation.params) {
			mmUpdateLastViewed.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateLastViewed.defaultExpectation.params)
		}
	}

	return mmUpdateLastViewed
}

// ExpectCtxParam1 sets up expected param ctx for ProposalDomainService.UpdateLastViewed
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) ExpectCtxParam1(ctx context.Context) *mProposalDomainServiceMockUpdateLastViewed {
	if mmUpdateLastViewed.mock.funcUpdateLastViewed != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Set")
	}

	if mmUpdateLastViewed.defaultExpectation == nil {
		mmUpdateLastViewed.defaultExpectation = &ProposalDomainServiceMockUpdateLastViewedExpectation{}
	}

	if mmUpdateLastViewed.defaultExpectation.params != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Expect")
	}

	if mmUpdateLastViewed.defaultExpectation.paramPtrs == nil {
		mmUpdateLastViewed.defaultExpectation.paramPtrs = &ProposalDomainServiceMockUpdateLastViewedParamPtrs{}
	}
	mmUpdateLastViewed.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdateLastViewed.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdateLastViewed
}

// ExpectDataParam2 sets up expected param data for ProposalDomainService.UpdateLastViewed
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) ExpectDataParam2(data proposalvalueobject.UserView) *mProposalDomainServiceMockUpdateLastViewed {
	if mmUpdateLastViewed.mock.funcUpdateLastViewed != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Set")
	}

	if mmUpdateLastViewed.defaultExpectation == nil {
		mmUpdateLastViewed.defaultExpectation = &ProposalDomainServiceMockUpdateLastViewedExpectation{}
	}

	if mmUpdateLastViewed.defaultExpectation.params != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Expect")
	}

	if mmUpdateLastViewed.defaultExpectation.paramPtrs == nil {
		mmUpdateLastViewed.defaultExpectation.paramPtrs = &ProposalDomainServiceMockUpdateLastViewedParamPtrs{}
	}
	mmUpdateLastViewed.defaultExpectation.paramPtrs.data = &data
	mmUpdateLastViewed.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdateLastViewed
}

// Inspect accepts an inspector function that has same arguments as the ProposalDomainService.UpdateLastViewed
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Inspect(f func(ctx context.Context, data proposalvalueobject.UserView)) *mProposalDomainServiceMockUpdateLastViewed {
	if mmUpdateLastViewed.mock.inspectFuncUpdateLastViewed != nil {
		mmUpdateLastViewed.mock.t.Fatalf("Inspect function is already set for ProposalDomainServiceMock.UpdateLastViewed")
	}

	mmUpdateLastViewed.mock.inspectFuncUpdateLastViewed = f

	return mmUpdateLastViewed
}

// Return sets up results that will be returned by ProposalDomainService.UpdateLastViewed
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Return(err error) *ProposalDomainServiceMock {
	if mmUpdateLastViewed.mock.funcUpdateLastViewed != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Set")
	}

	if mmUpdateLastViewed.defaultExpectation == nil {
		mmUpdateLastViewed.defaultExpectation = &ProposalDomainServiceMockUpdateLastViewedExpectation{mock: mmUpdateLastViewed.mock}
	}
	mmUpdateLastViewed.defaultExpectation.results = &ProposalDomainServiceMockUpdateLastViewedResults{err}
	mmUpdateLastViewed.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateLastViewed.mock
}

// Set uses given function f to mock the ProposalDomainService.UpdateLastViewed method
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Set(f func(ctx context.Context, data proposalvalueobject.UserView) (err error)) *ProposalDomainServiceMock {
	if mmUpdateLastViewed.defaultExpectation != nil {
		mmUpdateLastViewed.mock.t.Fatalf("Default expectation is already set for the ProposalDomainService.UpdateLastViewed method")
	}

	if len(mmUpdateLastViewed.expectations) > 0 {
		mmUpdateLastViewed.mock.t.Fatalf("Some expectations are already set for the ProposalDomainService.UpdateLastViewed method")
	}

	mmUpdateLastViewed.mock.funcUpdateLastViewed = f
	mmUpdateLastViewed.mock.funcUpdateLastViewedOrigin = minimock.CallerInfo(1)
	return mmUpdateLastViewed.mock
}

// When sets expectation for the ProposalDomainService.UpdateLastViewed which will trigger the result defined by the following
// Then helper
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) When(ctx context.Context, data proposalvalueobject.UserView) *ProposalDomainServiceMockUpdateLastViewedExpectation {
	if mmUpdateLastViewed.mock.funcUpdateLastViewed != nil {
		mmUpdateLastViewed.mock.t.Fatalf("ProposalDomainServiceMock.UpdateLastViewed mock is already set by Set")
	}

	expectation := &ProposalDomainServiceMockUpdateLastViewedExpectation{
		mock:               mmUpdateLastViewed.mock,
		params:             &ProposalDomainServiceMockUpdateLastViewedParams{ctx, data},
		expectationOrigins: ProposalDomainServiceMockUpdateLastViewedExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateLastViewed.expectations = append(mmUpdateLastViewed.expectations, expectation)
	return expectation
}

// Then sets up ProposalDomainService.UpdateLastViewed return parameters for the expectation previously defined by the When method
func (e *ProposalDomainServiceMockUpdateLastViewedExpectation) Then(err error) *ProposalDomainServiceMock {
	e.results = &ProposalDomainServiceMockUpdateLastViewedResults{err}
	return e.mock
}

// Times sets number of times ProposalDomainService.UpdateLastViewed should be invoked
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Times(n uint64) *mProposalDomainServiceMockUpdateLastViewed {
	if n == 0 {
		mmUpdateLastViewed.mock.t.Fatalf("Times of ProposalDomainServiceMock.UpdateLastViewed mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateLastViewed.expectedInvocations, n)
	mmUpdateLastViewed.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateLastViewed
}

func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) invocationsDone() bool {
	if len(mmUpdateLastViewed.expectations) == 0 && mmUpdateLastViewed.defaultExpectation == nil && mmUpdateLastViewed.mock.funcUpdateLastViewed == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateLastViewed.mock.afterUpdateLastViewedCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateLastViewed.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateLastViewed implements mm_service.ProposalDomainService
func (mmUpdateLastViewed *ProposalDomainServiceMock) UpdateLastViewed(ctx context.Context, data proposalvalueobject.UserView) (err error) {
	mm_atomic.AddUint64(&mmUpdateLastViewed.beforeUpdateLastViewedCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateLastViewed.afterUpdateLastViewedCounter, 1)

	mmUpdateLastViewed.t.Helper()

	if mmUpdateLastViewed.inspectFuncUpdateLastViewed != nil {
		mmUpdateLastViewed.inspectFuncUpdateLastViewed(ctx, data)
	}

	mm_params := ProposalDomainServiceMockUpdateLastViewedParams{ctx, data}

	// Record call args
	mmUpdateLastViewed.UpdateLastViewedMock.mutex.Lock()
	mmUpdateLastViewed.UpdateLastViewedMock.callArgs = append(mmUpdateLastViewed.UpdateLastViewedMock.callArgs, &mm_params)
	mmUpdateLastViewed.UpdateLastViewedMock.mutex.Unlock()

	for _, e := range mmUpdateLastViewed.UpdateLastViewedMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.paramPtrs

		mm_got := ProposalDomainServiceMockUpdateLastViewedParams{ctx, data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdateLastViewed.t.Errorf("ProposalDomainServiceMock.UpdateLastViewed got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdateLastViewed.t.Errorf("ProposalDomainServiceMock.UpdateLastViewed got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateLastViewed.t.Errorf("ProposalDomainServiceMock.UpdateLastViewed got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateLastViewed.UpdateLastViewedMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateLastViewed.t.Fatal("No results are set for the ProposalDomainServiceMock.UpdateLastViewed")
		}
		return (*mm_results).err
	}
	if mmUpdateLastViewed.funcUpdateLastViewed != nil {
		return mmUpdateLastViewed.funcUpdateLastViewed(ctx, data)
	}
	mmUpdateLastViewed.t.Fatalf("Unexpected call to ProposalDomainServiceMock.UpdateLastViewed. %v %v", ctx, data)
	return
}

// UpdateLastViewedAfterCounter returns a count of finished ProposalDomainServiceMock.UpdateLastViewed invocations
func (mmUpdateLastViewed *ProposalDomainServiceMock) UpdateLastViewedAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLastViewed.afterUpdateLastViewedCounter)
}

// UpdateLastViewedBeforeCounter returns a count of ProposalDomainServiceMock.UpdateLastViewed invocations
func (mmUpdateLastViewed *ProposalDomainServiceMock) UpdateLastViewedBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateLastViewed.beforeUpdateLastViewedCounter)
}

// Calls returns a list of arguments used in each call to ProposalDomainServiceMock.UpdateLastViewed.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateLastViewed *mProposalDomainServiceMockUpdateLastViewed) Calls() []*ProposalDomainServiceMockUpdateLastViewedParams {
	mmUpdateLastViewed.mutex.RLock()

	argCopy := make([]*ProposalDomainServiceMockUpdateLastViewedParams, len(mmUpdateLastViewed.callArgs))
	copy(argCopy, mmUpdateLastViewed.callArgs)

	mmUpdateLastViewed.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateLastViewedDone returns true if the count of the UpdateLastViewed invocations corresponds
// the number of defined expectations
func (m *ProposalDomainServiceMock) MinimockUpdateLastViewedDone() bool {
	if m.UpdateLastViewedMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateLastViewedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateLastViewedMock.invocationsDone()
}

// MinimockUpdateLastViewedInspect logs each unmet expectation
func (m *ProposalDomainServiceMock) MinimockUpdateLastViewedInspect() {
	for _, e := range m.UpdateLastViewedMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.UpdateLastViewed at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateLastViewedCounter := mm_atomic.LoadUint64(&m.afterUpdateLastViewedCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateLastViewedMock.defaultExpectation != nil && afterUpdateLastViewedCounter < 1 {
		if m.UpdateLastViewedMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.UpdateLastViewed at\n%s", m.UpdateLastViewedMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalDomainServiceMock.UpdateLastViewed at\n%s with params: %#v", m.UpdateLastViewedMock.defaultExpectation.expectationOrigins.origin, *m.UpdateLastViewedMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateLastViewed != nil && afterUpdateLastViewedCounter < 1 {
		m.t.Errorf("Expected call to ProposalDomainServiceMock.UpdateLastViewed at\n%s", m.funcUpdateLastViewedOrigin)
	}

	if !m.UpdateLastViewedMock.invocationsDone() && afterUpdateLastViewedCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalDomainServiceMock.UpdateLastViewed at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateLastViewedMock.expectedInvocations), m.UpdateLastViewedMock.expectedInvocationsOrigin, afterUpdateLastViewedCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProposalDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateMessageInspect()

			m.MinimockDeleteInspect()

			m.MinimockGetAllAsAdminInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByProductIDAndProposalIDInspect()

			m.MinimockGetHistoryInspect()

			m.MinimockGetUnreadEventsForUserInspect()

			m.MinimockUpdateInspect()

			m.MinimockUpdateLastViewedInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProposalDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProposalDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateMessageDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockGetAllAsAdminDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByProductIDAndProposalIDDone() &&
		m.MinimockGetHistoryDone() &&
		m.MinimockGetUnreadEventsForUserDone() &&
		m.MinimockUpdateDone() &&
		m.MinimockUpdateLastViewedDone()
}
