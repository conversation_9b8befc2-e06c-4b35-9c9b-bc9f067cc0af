// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository.ProposalPrimeDB -o proposal_prime_db_mock.go -n ProposalPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"github.com/gojuno/minimock/v3"
)

// ProposalPrimeDBMock implements mm_repository.ProposalPrimeDB
type ProposalPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(proposal proposalentity.Proposal, values proposalvalueobject.Values) (p1 proposalentity.Proposal, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(proposal proposalentity.Proposal, values proposalvalueobject.Values)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mProposalPrimeDBMockCreate

	funcDelete          func(proposal proposalentity.Proposal) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(proposal proposalentity.Proposal)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mProposalPrimeDBMockDelete

	funcGetAll          func() (pa1 []proposalentity.Proposal, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mProposalPrimeDBMockGetAll

	funcGetByID          func(id int64) (p1 proposalentity.Proposal, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mProposalPrimeDBMockGetByID

	funcGetByProductID          func(productID int64) (pa1 []proposalentity.Proposal, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mProposalPrimeDBMockGetByProductID

	funcGetByProductIDAndProposalID          func(productID int64, proposalID int64) (p1 proposalentity.Proposal, err error)
	funcGetByProductIDAndProposalIDOrigin    string
	inspectFuncGetByProductIDAndProposalID   func(productID int64, proposalID int64)
	afterGetByProductIDAndProposalIDCounter  uint64
	beforeGetByProductIDAndProposalIDCounter uint64
	GetByProductIDAndProposalIDMock          mProposalPrimeDBMockGetByProductIDAndProposalID

	funcUpdate          func(ctx context.Context, proposal proposalaggregate.ProposalUpdate) (p1 proposalaggregate.Proposal, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(ctx context.Context, proposal proposalaggregate.ProposalUpdate)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mProposalPrimeDBMockUpdate
}

// NewProposalPrimeDBMock returns a mock for mm_repository.ProposalPrimeDB
func NewProposalPrimeDBMock(t minimock.Tester) *ProposalPrimeDBMock {
	m := &ProposalPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mProposalPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*ProposalPrimeDBMockCreateParams{}

	m.DeleteMock = mProposalPrimeDBMockDelete{mock: m}
	m.DeleteMock.callArgs = []*ProposalPrimeDBMockDeleteParams{}

	m.GetAllMock = mProposalPrimeDBMockGetAll{mock: m}

	m.GetByIDMock = mProposalPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ProposalPrimeDBMockGetByIDParams{}

	m.GetByProductIDMock = mProposalPrimeDBMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*ProposalPrimeDBMockGetByProductIDParams{}

	m.GetByProductIDAndProposalIDMock = mProposalPrimeDBMockGetByProductIDAndProposalID{mock: m}
	m.GetByProductIDAndProposalIDMock.callArgs = []*ProposalPrimeDBMockGetByProductIDAndProposalIDParams{}

	m.UpdateMock = mProposalPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*ProposalPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProposalPrimeDBMockCreate struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockCreateExpectation
	expectations       []*ProposalPrimeDBMockCreateExpectation

	callArgs []*ProposalPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockCreateExpectation specifies expectation struct of the ProposalPrimeDB.Create
type ProposalPrimeDBMockCreateExpectation struct {
	mock               *ProposalPrimeDBMock
	params             *ProposalPrimeDBMockCreateParams
	paramPtrs          *ProposalPrimeDBMockCreateParamPtrs
	expectationOrigins ProposalPrimeDBMockCreateExpectationOrigins
	results            *ProposalPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// ProposalPrimeDBMockCreateParams contains parameters of the ProposalPrimeDB.Create
type ProposalPrimeDBMockCreateParams struct {
	proposal proposalentity.Proposal
	values   proposalvalueobject.Values
}

// ProposalPrimeDBMockCreateParamPtrs contains pointers to parameters of the ProposalPrimeDB.Create
type ProposalPrimeDBMockCreateParamPtrs struct {
	proposal *proposalentity.Proposal
	values   *proposalvalueobject.Values
}

// ProposalPrimeDBMockCreateResults contains results of the ProposalPrimeDB.Create
type ProposalPrimeDBMockCreateResults struct {
	p1  proposalentity.Proposal
	err error
}

// ProposalPrimeDBMockCreateOrigins contains origins of expectations of the ProposalPrimeDB.Create
type ProposalPrimeDBMockCreateExpectationOrigins struct {
	origin         string
	originProposal string
	originValues   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mProposalPrimeDBMockCreate) Optional() *mProposalPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for ProposalPrimeDB.Create
func (mmCreate *mProposalPrimeDBMockCreate) Expect(proposal proposalentity.Proposal, values proposalvalueobject.Values) *mProposalPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &ProposalPrimeDBMockCreateParams{proposal, values}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectProposalParam1 sets up expected param proposal for ProposalPrimeDB.Create
func (mmCreate *mProposalPrimeDBMockCreate) ExpectProposalParam1(proposal proposalentity.Proposal) *mProposalPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProposalPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.proposal = &proposal
	mmCreate.defaultExpectation.expectationOrigins.originProposal = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectValuesParam2 sets up expected param values for ProposalPrimeDB.Create
func (mmCreate *mProposalPrimeDBMockCreate) ExpectValuesParam2(values proposalvalueobject.Values) *mProposalPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &ProposalPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.values = &values
	mmCreate.defaultExpectation.expectationOrigins.originValues = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.Create
func (mmCreate *mProposalPrimeDBMockCreate) Inspect(f func(proposal proposalentity.Proposal, values proposalvalueobject.Values)) *mProposalPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by ProposalPrimeDB.Create
func (mmCreate *mProposalPrimeDBMockCreate) Return(p1 proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &ProposalPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &ProposalPrimeDBMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the ProposalPrimeDB.Create method
func (mmCreate *mProposalPrimeDBMockCreate) Set(f func(proposal proposalentity.Proposal, values proposalvalueobject.Values) (p1 proposalentity.Proposal, err error)) *ProposalPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the ProposalPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mProposalPrimeDBMockCreate) When(proposal proposalentity.Proposal, values proposalvalueobject.Values) *ProposalPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("ProposalPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &ProposalPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &ProposalPrimeDBMockCreateParams{proposal, values},
		expectationOrigins: ProposalPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up ProposalPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *ProposalPrimeDBMockCreateExpectation) Then(p1 proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	e.results = &ProposalPrimeDBMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalPrimeDB.Create should be invoked
func (mmCreate *mProposalPrimeDBMockCreate) Times(n uint64) *mProposalPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of ProposalPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mProposalPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.ProposalPrimeDB
func (mmCreate *ProposalPrimeDBMock) Create(proposal proposalentity.Proposal, values proposalvalueobject.Values) (p1 proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(proposal, values)
	}

	mm_params := ProposalPrimeDBMockCreateParams{proposal, values}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := ProposalPrimeDBMockCreateParams{proposal, values}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposal != nil && !minimock.Equal(*mm_want_ptrs.proposal, mm_got.proposal) {
				mmCreate.t.Errorf("ProposalPrimeDBMock.Create got unexpected parameter proposal, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originProposal, *mm_want_ptrs.proposal, mm_got.proposal, minimock.Diff(*mm_want_ptrs.proposal, mm_got.proposal))
			}

			if mm_want_ptrs.values != nil && !minimock.Equal(*mm_want_ptrs.values, mm_got.values) {
				mmCreate.t.Errorf("ProposalPrimeDBMock.Create got unexpected parameter values, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originValues, *mm_want_ptrs.values, mm_got.values, minimock.Diff(*mm_want_ptrs.values, mm_got.values))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("ProposalPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the ProposalPrimeDBMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(proposal, values)
	}
	mmCreate.t.Fatalf("Unexpected call to ProposalPrimeDBMock.Create. %v %v", proposal, values)
	return
}

// CreateAfterCounter returns a count of finished ProposalPrimeDBMock.Create invocations
func (mmCreate *ProposalPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of ProposalPrimeDBMock.Create invocations
func (mmCreate *ProposalPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to ProposalPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mProposalPrimeDBMockCreate) Calls() []*ProposalPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*ProposalPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mProposalPrimeDBMockDelete struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockDeleteExpectation
	expectations       []*ProposalPrimeDBMockDeleteExpectation

	callArgs []*ProposalPrimeDBMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockDeleteExpectation specifies expectation struct of the ProposalPrimeDB.Delete
type ProposalPrimeDBMockDeleteExpectation struct {
	mock               *ProposalPrimeDBMock
	params             *ProposalPrimeDBMockDeleteParams
	paramPtrs          *ProposalPrimeDBMockDeleteParamPtrs
	expectationOrigins ProposalPrimeDBMockDeleteExpectationOrigins
	results            *ProposalPrimeDBMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// ProposalPrimeDBMockDeleteParams contains parameters of the ProposalPrimeDB.Delete
type ProposalPrimeDBMockDeleteParams struct {
	proposal proposalentity.Proposal
}

// ProposalPrimeDBMockDeleteParamPtrs contains pointers to parameters of the ProposalPrimeDB.Delete
type ProposalPrimeDBMockDeleteParamPtrs struct {
	proposal *proposalentity.Proposal
}

// ProposalPrimeDBMockDeleteResults contains results of the ProposalPrimeDB.Delete
type ProposalPrimeDBMockDeleteResults struct {
	err error
}

// ProposalPrimeDBMockDeleteOrigins contains origins of expectations of the ProposalPrimeDB.Delete
type ProposalPrimeDBMockDeleteExpectationOrigins struct {
	origin         string
	originProposal string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mProposalPrimeDBMockDelete) Optional() *mProposalPrimeDBMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for ProposalPrimeDB.Delete
func (mmDelete *mProposalPrimeDBMockDelete) Expect(proposal proposalentity.Proposal) *mProposalPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ProposalPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("ProposalPrimeDBMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &ProposalPrimeDBMockDeleteParams{proposal}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectProposalParam1 sets up expected param proposal for ProposalPrimeDB.Delete
func (mmDelete *mProposalPrimeDBMockDelete) ExpectProposalParam1(proposal proposalentity.Proposal) *mProposalPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ProposalPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("ProposalPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &ProposalPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.proposal = &proposal
	mmDelete.defaultExpectation.expectationOrigins.originProposal = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.Delete
func (mmDelete *mProposalPrimeDBMockDelete) Inspect(f func(proposal proposalentity.Proposal)) *mProposalPrimeDBMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by ProposalPrimeDB.Delete
func (mmDelete *mProposalPrimeDBMockDelete) Return(err error) *ProposalPrimeDBMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &ProposalPrimeDBMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &ProposalPrimeDBMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the ProposalPrimeDB.Delete method
func (mmDelete *mProposalPrimeDBMockDelete) Set(f func(proposal proposalentity.Proposal) (err error)) *ProposalPrimeDBMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the ProposalPrimeDB.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mProposalPrimeDBMockDelete) When(proposal proposalentity.Proposal) *ProposalPrimeDBMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("ProposalPrimeDBMock.Delete mock is already set by Set")
	}

	expectation := &ProposalPrimeDBMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &ProposalPrimeDBMockDeleteParams{proposal},
		expectationOrigins: ProposalPrimeDBMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up ProposalPrimeDB.Delete return parameters for the expectation previously defined by the When method
func (e *ProposalPrimeDBMockDeleteExpectation) Then(err error) *ProposalPrimeDBMock {
	e.results = &ProposalPrimeDBMockDeleteResults{err}
	return e.mock
}

// Times sets number of times ProposalPrimeDB.Delete should be invoked
func (mmDelete *mProposalPrimeDBMockDelete) Times(n uint64) *mProposalPrimeDBMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of ProposalPrimeDBMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mProposalPrimeDBMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_repository.ProposalPrimeDB
func (mmDelete *ProposalPrimeDBMock) Delete(proposal proposalentity.Proposal) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(proposal)
	}

	mm_params := ProposalPrimeDBMockDeleteParams{proposal}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := ProposalPrimeDBMockDeleteParams{proposal}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposal != nil && !minimock.Equal(*mm_want_ptrs.proposal, mm_got.proposal) {
				mmDelete.t.Errorf("ProposalPrimeDBMock.Delete got unexpected parameter proposal, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originProposal, *mm_want_ptrs.proposal, mm_got.proposal, minimock.Diff(*mm_want_ptrs.proposal, mm_got.proposal))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("ProposalPrimeDBMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the ProposalPrimeDBMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(proposal)
	}
	mmDelete.t.Fatalf("Unexpected call to ProposalPrimeDBMock.Delete. %v", proposal)
	return
}

// DeleteAfterCounter returns a count of finished ProposalPrimeDBMock.Delete invocations
func (mmDelete *ProposalPrimeDBMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of ProposalPrimeDBMock.Delete invocations
func (mmDelete *ProposalPrimeDBMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to ProposalPrimeDBMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mProposalPrimeDBMockDelete) Calls() []*ProposalPrimeDBMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*ProposalPrimeDBMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mProposalPrimeDBMockGetAll struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockGetAllExpectation
	expectations       []*ProposalPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockGetAllExpectation specifies expectation struct of the ProposalPrimeDB.GetAll
type ProposalPrimeDBMockGetAllExpectation struct {
	mock *ProposalPrimeDBMock

	results      *ProposalPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// ProposalPrimeDBMockGetAllResults contains results of the ProposalPrimeDB.GetAll
type ProposalPrimeDBMockGetAllResults struct {
	pa1 []proposalentity.Proposal
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mProposalPrimeDBMockGetAll) Optional() *mProposalPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for ProposalPrimeDB.GetAll
func (mmGetAll *mProposalPrimeDBMockGetAll) Expect() *mProposalPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ProposalPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ProposalPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.GetAll
func (mmGetAll *mProposalPrimeDBMockGetAll) Inspect(f func()) *mProposalPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by ProposalPrimeDB.GetAll
func (mmGetAll *mProposalPrimeDBMockGetAll) Return(pa1 []proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("ProposalPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &ProposalPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &ProposalPrimeDBMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the ProposalPrimeDB.GetAll method
func (mmGetAll *mProposalPrimeDBMockGetAll) Set(f func() (pa1 []proposalentity.Proposal, err error)) *ProposalPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times ProposalPrimeDB.GetAll should be invoked
func (mmGetAll *mProposalPrimeDBMockGetAll) Times(n uint64) *mProposalPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of ProposalPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mProposalPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.ProposalPrimeDB
func (mmGetAll *ProposalPrimeDBMock) GetAll() (pa1 []proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the ProposalPrimeDBMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to ProposalPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished ProposalPrimeDBMock.GetAll invocations
func (mmGetAll *ProposalPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of ProposalPrimeDBMock.GetAll invocations
func (mmGetAll *ProposalPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to ProposalPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mProposalPrimeDBMockGetByID struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockGetByIDExpectation
	expectations       []*ProposalPrimeDBMockGetByIDExpectation

	callArgs []*ProposalPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockGetByIDExpectation specifies expectation struct of the ProposalPrimeDB.GetByID
type ProposalPrimeDBMockGetByIDExpectation struct {
	mock               *ProposalPrimeDBMock
	params             *ProposalPrimeDBMockGetByIDParams
	paramPtrs          *ProposalPrimeDBMockGetByIDParamPtrs
	expectationOrigins ProposalPrimeDBMockGetByIDExpectationOrigins
	results            *ProposalPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalPrimeDBMockGetByIDParams contains parameters of the ProposalPrimeDB.GetByID
type ProposalPrimeDBMockGetByIDParams struct {
	id int64
}

// ProposalPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the ProposalPrimeDB.GetByID
type ProposalPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// ProposalPrimeDBMockGetByIDResults contains results of the ProposalPrimeDB.GetByID
type ProposalPrimeDBMockGetByIDResults struct {
	p1  proposalentity.Proposal
	err error
}

// ProposalPrimeDBMockGetByIDOrigins contains origins of expectations of the ProposalPrimeDB.GetByID
type ProposalPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mProposalPrimeDBMockGetByID) Optional() *mProposalPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ProposalPrimeDB.GetByID
func (mmGetByID *mProposalPrimeDBMockGetByID) Expect(id int64) *mProposalPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ProposalPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ProposalPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ProposalPrimeDB.GetByID
func (mmGetByID *mProposalPrimeDBMockGetByID) ExpectIdParam1(id int64) *mProposalPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ProposalPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ProposalPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.GetByID
func (mmGetByID *mProposalPrimeDBMockGetByID) Inspect(f func(id int64)) *mProposalPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ProposalPrimeDB.GetByID
func (mmGetByID *mProposalPrimeDBMockGetByID) Return(p1 proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ProposalPrimeDBMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ProposalPrimeDB.GetByID method
func (mmGetByID *mProposalPrimeDBMockGetByID) Set(f func(id int64) (p1 proposalentity.Proposal, err error)) *ProposalPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ProposalPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mProposalPrimeDBMockGetByID) When(id int64) *ProposalPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &ProposalPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ProposalPrimeDBMockGetByIDParams{id},
		expectationOrigins: ProposalPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ProposalPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *ProposalPrimeDBMockGetByIDExpectation) Then(p1 proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	e.results = &ProposalPrimeDBMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalPrimeDB.GetByID should be invoked
func (mmGetByID *mProposalPrimeDBMockGetByID) Times(n uint64) *mProposalPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ProposalPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mProposalPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.ProposalPrimeDB
func (mmGetByID *ProposalPrimeDBMock) GetByID(id int64) (p1 proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ProposalPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ProposalPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ProposalPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ProposalPrimeDBMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ProposalPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ProposalPrimeDBMock.GetByID invocations
func (mmGetByID *ProposalPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ProposalPrimeDBMock.GetByID invocations
func (mmGetByID *ProposalPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mProposalPrimeDBMockGetByID) Calls() []*ProposalPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ProposalPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mProposalPrimeDBMockGetByProductID struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockGetByProductIDExpectation
	expectations       []*ProposalPrimeDBMockGetByProductIDExpectation

	callArgs []*ProposalPrimeDBMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockGetByProductIDExpectation specifies expectation struct of the ProposalPrimeDB.GetByProductID
type ProposalPrimeDBMockGetByProductIDExpectation struct {
	mock               *ProposalPrimeDBMock
	params             *ProposalPrimeDBMockGetByProductIDParams
	paramPtrs          *ProposalPrimeDBMockGetByProductIDParamPtrs
	expectationOrigins ProposalPrimeDBMockGetByProductIDExpectationOrigins
	results            *ProposalPrimeDBMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalPrimeDBMockGetByProductIDParams contains parameters of the ProposalPrimeDB.GetByProductID
type ProposalPrimeDBMockGetByProductIDParams struct {
	productID int64
}

// ProposalPrimeDBMockGetByProductIDParamPtrs contains pointers to parameters of the ProposalPrimeDB.GetByProductID
type ProposalPrimeDBMockGetByProductIDParamPtrs struct {
	productID *int64
}

// ProposalPrimeDBMockGetByProductIDResults contains results of the ProposalPrimeDB.GetByProductID
type ProposalPrimeDBMockGetByProductIDResults struct {
	pa1 []proposalentity.Proposal
	err error
}

// ProposalPrimeDBMockGetByProductIDOrigins contains origins of expectations of the ProposalPrimeDB.GetByProductID
type ProposalPrimeDBMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Optional() *mProposalPrimeDBMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for ProposalPrimeDB.GetByProductID
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Expect(productID int64) *mProposalPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &ProposalPrimeDBMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ProposalPrimeDB.GetByProductID
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) ExpectProductIDParam1(productID int64) *mProposalPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &ProposalPrimeDBMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.GetByProductID
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Inspect(f func(productID int64)) *mProposalPrimeDBMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by ProposalPrimeDB.GetByProductID
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Return(pa1 []proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &ProposalPrimeDBMockGetByProductIDResults{pa1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the ProposalPrimeDB.GetByProductID method
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Set(f func(productID int64) (pa1 []proposalentity.Proposal, err error)) *ProposalPrimeDBMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the ProposalPrimeDB.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) When(productID int64) *ProposalPrimeDBMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductID mock is already set by Set")
	}

	expectation := &ProposalPrimeDBMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &ProposalPrimeDBMockGetByProductIDParams{productID},
		expectationOrigins: ProposalPrimeDBMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up ProposalPrimeDB.GetByProductID return parameters for the expectation previously defined by the When method
func (e *ProposalPrimeDBMockGetByProductIDExpectation) Then(pa1 []proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	e.results = &ProposalPrimeDBMockGetByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ProposalPrimeDB.GetByProductID should be invoked
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Times(n uint64) *mProposalPrimeDBMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of ProposalPrimeDBMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_repository.ProposalPrimeDB
func (mmGetByProductID *ProposalPrimeDBMock) GetByProductID(productID int64) (pa1 []proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := ProposalPrimeDBMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalPrimeDBMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("ProposalPrimeDBMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("ProposalPrimeDBMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the ProposalPrimeDBMock.GetByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to ProposalPrimeDBMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished ProposalPrimeDBMock.GetByProductID invocations
func (mmGetByProductID *ProposalPrimeDBMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of ProposalPrimeDBMock.GetByProductID invocations
func (mmGetByProductID *ProposalPrimeDBMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalPrimeDBMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mProposalPrimeDBMockGetByProductID) Calls() []*ProposalPrimeDBMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*ProposalPrimeDBMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mProposalPrimeDBMockGetByProductIDAndProposalID struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation
	expectations       []*ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation

	callArgs []*ProposalPrimeDBMockGetByProductIDAndProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation specifies expectation struct of the ProposalPrimeDB.GetByProductIDAndProposalID
type ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation struct {
	mock               *ProposalPrimeDBMock
	params             *ProposalPrimeDBMockGetByProductIDAndProposalIDParams
	paramPtrs          *ProposalPrimeDBMockGetByProductIDAndProposalIDParamPtrs
	expectationOrigins ProposalPrimeDBMockGetByProductIDAndProposalIDExpectationOrigins
	results            *ProposalPrimeDBMockGetByProductIDAndProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalPrimeDBMockGetByProductIDAndProposalIDParams contains parameters of the ProposalPrimeDB.GetByProductIDAndProposalID
type ProposalPrimeDBMockGetByProductIDAndProposalIDParams struct {
	productID  int64
	proposalID int64
}

// ProposalPrimeDBMockGetByProductIDAndProposalIDParamPtrs contains pointers to parameters of the ProposalPrimeDB.GetByProductIDAndProposalID
type ProposalPrimeDBMockGetByProductIDAndProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
}

// ProposalPrimeDBMockGetByProductIDAndProposalIDResults contains results of the ProposalPrimeDB.GetByProductIDAndProposalID
type ProposalPrimeDBMockGetByProductIDAndProposalIDResults struct {
	p1  proposalentity.Proposal
	err error
}

// ProposalPrimeDBMockGetByProductIDAndProposalIDOrigins contains origins of expectations of the ProposalPrimeDB.GetByProductIDAndProposalID
type ProposalPrimeDBMockGetByProductIDAndProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Optional() *mProposalPrimeDBMockGetByProductIDAndProposalID {
	mmGetByProductIDAndProposalID.optional = true
	return mmGetByProductIDAndProposalID
}

// Expect sets up expected params for ProposalPrimeDB.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Expect(productID int64, proposalID int64) *mProposalPrimeDBMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by ExpectParams functions")
	}

	mmGetByProductIDAndProposalID.defaultExpectation.params = &ProposalPrimeDBMockGetByProductIDAndProposalIDParams{productID, proposalID}
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductIDAndProposalID.expectations {
		if minimock.Equal(e.params, mmGetByProductIDAndProposalID.defaultExpectation.params) {
			mmGetByProductIDAndProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductIDAndProposalID.defaultExpectation.params)
		}
	}

	return mmGetByProductIDAndProposalID
}

// ExpectProductIDParam1 sets up expected param productID for ProposalPrimeDB.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) ExpectProductIDParam1(productID int64) *mProposalPrimeDBMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.params != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Expect")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs = &ProposalPrimeDBMockGetByProductIDAndProposalIDParamPtrs{}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductIDAndProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for ProposalPrimeDB.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) ExpectProposalIDParam2(proposalID int64) *mProposalPrimeDBMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.params != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Expect")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs = &ProposalPrimeDBMockGetByProductIDAndProposalIDParamPtrs{}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetByProductIDAndProposalID
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Inspect(f func(productID int64, proposalID int64)) *mProposalPrimeDBMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.inspectFuncGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.GetByProductIDAndProposalID")
	}

	mmGetByProductIDAndProposalID.mock.inspectFuncGetByProductIDAndProposalID = f

	return mmGetByProductIDAndProposalID
}

// Return sets up results that will be returned by ProposalPrimeDB.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Return(p1 proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation{mock: mmGetByProductIDAndProposalID.mock}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.results = &ProposalPrimeDBMockGetByProductIDAndProposalIDResults{p1, err}
	mmGetByProductIDAndProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID.mock
}

// Set uses given function f to mock the ProposalPrimeDB.GetByProductIDAndProposalID method
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Set(f func(productID int64, proposalID int64) (p1 proposalentity.Proposal, err error)) *ProposalPrimeDBMock {
	if mmGetByProductIDAndProposalID.defaultExpectation != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.GetByProductIDAndProposalID method")
	}

	if len(mmGetByProductIDAndProposalID.expectations) > 0 {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.GetByProductIDAndProposalID method")
	}

	mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID = f
	mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID.mock
}

// When sets expectation for the ProposalPrimeDB.GetByProductIDAndProposalID which will trigger the result defined by the following
// Then helper
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) When(productID int64, proposalID int64) *ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalPrimeDBMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	expectation := &ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation{
		mock:               mmGetByProductIDAndProposalID.mock,
		params:             &ProposalPrimeDBMockGetByProductIDAndProposalIDParams{productID, proposalID},
		expectationOrigins: ProposalPrimeDBMockGetByProductIDAndProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductIDAndProposalID.expectations = append(mmGetByProductIDAndProposalID.expectations, expectation)
	return expectation
}

// Then sets up ProposalPrimeDB.GetByProductIDAndProposalID return parameters for the expectation previously defined by the When method
func (e *ProposalPrimeDBMockGetByProductIDAndProposalIDExpectation) Then(p1 proposalentity.Proposal, err error) *ProposalPrimeDBMock {
	e.results = &ProposalPrimeDBMockGetByProductIDAndProposalIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalPrimeDB.GetByProductIDAndProposalID should be invoked
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Times(n uint64) *mProposalPrimeDBMockGetByProductIDAndProposalID {
	if n == 0 {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Times of ProposalPrimeDBMock.GetByProductIDAndProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductIDAndProposalID.expectedInvocations, n)
	mmGetByProductIDAndProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID
}

func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) invocationsDone() bool {
	if len(mmGetByProductIDAndProposalID.expectations) == 0 && mmGetByProductIDAndProposalID.defaultExpectation == nil && mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.mock.afterGetByProductIDAndProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductIDAndProposalID implements mm_repository.ProposalPrimeDB
func (mmGetByProductIDAndProposalID *ProposalPrimeDBMock) GetByProductIDAndProposalID(productID int64, proposalID int64) (p1 proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.beforeGetByProductIDAndProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.afterGetByProductIDAndProposalIDCounter, 1)

	mmGetByProductIDAndProposalID.t.Helper()

	if mmGetByProductIDAndProposalID.inspectFuncGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.inspectFuncGetByProductIDAndProposalID(productID, proposalID)
	}

	mm_params := ProposalPrimeDBMockGetByProductIDAndProposalIDParams{productID, proposalID}

	// Record call args
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.mutex.Lock()
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.callArgs = append(mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.callArgs, &mm_params)
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.mutex.Unlock()

	for _, e := range mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalPrimeDBMockGetByProductIDAndProposalIDParams{productID, proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductIDAndProposalID.t.Errorf("ProposalPrimeDBMock.GetByProductIDAndProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetByProductIDAndProposalID.t.Errorf("ProposalPrimeDBMock.GetByProductIDAndProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductIDAndProposalID.t.Errorf("ProposalPrimeDBMock.GetByProductIDAndProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductIDAndProposalID.t.Fatal("No results are set for the ProposalPrimeDBMock.GetByProductIDAndProposalID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByProductIDAndProposalID.funcGetByProductIDAndProposalID != nil {
		return mmGetByProductIDAndProposalID.funcGetByProductIDAndProposalID(productID, proposalID)
	}
	mmGetByProductIDAndProposalID.t.Fatalf("Unexpected call to ProposalPrimeDBMock.GetByProductIDAndProposalID. %v %v", productID, proposalID)
	return
}

// GetByProductIDAndProposalIDAfterCounter returns a count of finished ProposalPrimeDBMock.GetByProductIDAndProposalID invocations
func (mmGetByProductIDAndProposalID *ProposalPrimeDBMock) GetByProductIDAndProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.afterGetByProductIDAndProposalIDCounter)
}

// GetByProductIDAndProposalIDBeforeCounter returns a count of ProposalPrimeDBMock.GetByProductIDAndProposalID invocations
func (mmGetByProductIDAndProposalID *ProposalPrimeDBMock) GetByProductIDAndProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.beforeGetByProductIDAndProposalIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalPrimeDBMock.GetByProductIDAndProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductIDAndProposalID *mProposalPrimeDBMockGetByProductIDAndProposalID) Calls() []*ProposalPrimeDBMockGetByProductIDAndProposalIDParams {
	mmGetByProductIDAndProposalID.mutex.RLock()

	argCopy := make([]*ProposalPrimeDBMockGetByProductIDAndProposalIDParams, len(mmGetByProductIDAndProposalID.callArgs))
	copy(argCopy, mmGetByProductIDAndProposalID.callArgs)

	mmGetByProductIDAndProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDAndProposalIDDone returns true if the count of the GetByProductIDAndProposalID invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockGetByProductIDAndProposalIDDone() bool {
	if m.GetByProductIDAndProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDAndProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDAndProposalIDMock.invocationsDone()
}

// MinimockGetByProductIDAndProposalIDInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockGetByProductIDAndProposalIDInspect() {
	for _, e := range m.GetByProductIDAndProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductIDAndProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDAndProposalIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDAndProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDAndProposalIDMock.defaultExpectation != nil && afterGetByProductIDAndProposalIDCounter < 1 {
		if m.GetByProductIDAndProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductIDAndProposalID at\n%s", m.GetByProductIDAndProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductIDAndProposalID at\n%s with params: %#v", m.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDAndProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductIDAndProposalID != nil && afterGetByProductIDAndProposalIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.GetByProductIDAndProposalID at\n%s", m.funcGetByProductIDAndProposalIDOrigin)
	}

	if !m.GetByProductIDAndProposalIDMock.invocationsDone() && afterGetByProductIDAndProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.GetByProductIDAndProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDAndProposalIDMock.expectedInvocations), m.GetByProductIDAndProposalIDMock.expectedInvocationsOrigin, afterGetByProductIDAndProposalIDCounter)
	}
}

type mProposalPrimeDBMockUpdate struct {
	optional           bool
	mock               *ProposalPrimeDBMock
	defaultExpectation *ProposalPrimeDBMockUpdateExpectation
	expectations       []*ProposalPrimeDBMockUpdateExpectation

	callArgs []*ProposalPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalPrimeDBMockUpdateExpectation specifies expectation struct of the ProposalPrimeDB.Update
type ProposalPrimeDBMockUpdateExpectation struct {
	mock               *ProposalPrimeDBMock
	params             *ProposalPrimeDBMockUpdateParams
	paramPtrs          *ProposalPrimeDBMockUpdateParamPtrs
	expectationOrigins ProposalPrimeDBMockUpdateExpectationOrigins
	results            *ProposalPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// ProposalPrimeDBMockUpdateParams contains parameters of the ProposalPrimeDB.Update
type ProposalPrimeDBMockUpdateParams struct {
	ctx      context.Context
	proposal proposalaggregate.ProposalUpdate
}

// ProposalPrimeDBMockUpdateParamPtrs contains pointers to parameters of the ProposalPrimeDB.Update
type ProposalPrimeDBMockUpdateParamPtrs struct {
	ctx      *context.Context
	proposal *proposalaggregate.ProposalUpdate
}

// ProposalPrimeDBMockUpdateResults contains results of the ProposalPrimeDB.Update
type ProposalPrimeDBMockUpdateResults struct {
	p1  proposalaggregate.Proposal
	err error
}

// ProposalPrimeDBMockUpdateOrigins contains origins of expectations of the ProposalPrimeDB.Update
type ProposalPrimeDBMockUpdateExpectationOrigins struct {
	origin         string
	originCtx      string
	originProposal string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mProposalPrimeDBMockUpdate) Optional() *mProposalPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for ProposalPrimeDB.Update
func (mmUpdate *mProposalPrimeDBMockUpdate) Expect(ctx context.Context, proposal proposalaggregate.ProposalUpdate) *mProposalPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &ProposalPrimeDBMockUpdateParams{ctx, proposal}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectCtxParam1 sets up expected param ctx for ProposalPrimeDB.Update
func (mmUpdate *mProposalPrimeDBMockUpdate) ExpectCtxParam1(ctx context.Context) *mProposalPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ProposalPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdate
}

// ExpectProposalParam2 sets up expected param proposal for ProposalPrimeDB.Update
func (mmUpdate *mProposalPrimeDBMockUpdate) ExpectProposalParam2(proposal proposalaggregate.ProposalUpdate) *mProposalPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &ProposalPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.proposal = &proposal
	mmUpdate.defaultExpectation.expectationOrigins.originProposal = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the ProposalPrimeDB.Update
func (mmUpdate *mProposalPrimeDBMockUpdate) Inspect(f func(ctx context.Context, proposal proposalaggregate.ProposalUpdate)) *mProposalPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for ProposalPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by ProposalPrimeDB.Update
func (mmUpdate *mProposalPrimeDBMockUpdate) Return(p1 proposalaggregate.Proposal, err error) *ProposalPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &ProposalPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &ProposalPrimeDBMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the ProposalPrimeDB.Update method
func (mmUpdate *mProposalPrimeDBMockUpdate) Set(f func(ctx context.Context, proposal proposalaggregate.ProposalUpdate) (p1 proposalaggregate.Proposal, err error)) *ProposalPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the ProposalPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the ProposalPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the ProposalPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mProposalPrimeDBMockUpdate) When(ctx context.Context, proposal proposalaggregate.ProposalUpdate) *ProposalPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("ProposalPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &ProposalPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &ProposalPrimeDBMockUpdateParams{ctx, proposal},
		expectationOrigins: ProposalPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up ProposalPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *ProposalPrimeDBMockUpdateExpectation) Then(p1 proposalaggregate.Proposal, err error) *ProposalPrimeDBMock {
	e.results = &ProposalPrimeDBMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalPrimeDB.Update should be invoked
func (mmUpdate *mProposalPrimeDBMockUpdate) Times(n uint64) *mProposalPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of ProposalPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mProposalPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.ProposalPrimeDB
func (mmUpdate *ProposalPrimeDBMock) Update(ctx context.Context, proposal proposalaggregate.ProposalUpdate) (p1 proposalaggregate.Proposal, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(ctx, proposal)
	}

	mm_params := ProposalPrimeDBMockUpdateParams{ctx, proposal}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := ProposalPrimeDBMockUpdateParams{ctx, proposal}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdate.t.Errorf("ProposalPrimeDBMock.Update got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.proposal != nil && !minimock.Equal(*mm_want_ptrs.proposal, mm_got.proposal) {
				mmUpdate.t.Errorf("ProposalPrimeDBMock.Update got unexpected parameter proposal, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originProposal, *mm_want_ptrs.proposal, mm_got.proposal, minimock.Diff(*mm_want_ptrs.proposal, mm_got.proposal))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("ProposalPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the ProposalPrimeDBMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(ctx, proposal)
	}
	mmUpdate.t.Fatalf("Unexpected call to ProposalPrimeDBMock.Update. %v %v", ctx, proposal)
	return
}

// UpdateAfterCounter returns a count of finished ProposalPrimeDBMock.Update invocations
func (mmUpdate *ProposalPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of ProposalPrimeDBMock.Update invocations
func (mmUpdate *ProposalPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to ProposalPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mProposalPrimeDBMockUpdate) Calls() []*ProposalPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*ProposalPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *ProposalPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *ProposalPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to ProposalPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProposalPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeleteInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByProductIDAndProposalIDInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProposalPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProposalPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByProductIDAndProposalIDDone() &&
		m.MinimockUpdateDone()
}
