// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository.SectionPrimeDB -o section_prime_db_mock.go -n SectionPrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"github.com/gojuno/minimock/v3"
)

// SectionPrimeDBMock implements mm_repository.SectionPrimeDB
type SectionPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetSectionsByProposalID          func(productID int64, proposalID int64) (sa1 []proposalentity.Stand, err error)
	funcGetSectionsByProposalIDOrigin    string
	inspectFuncGetSectionsByProposalID   func(productID int64, proposalID int64)
	afterGetSectionsByProposalIDCounter  uint64
	beforeGetSectionsByProposalIDCounter uint64
	GetSectionsByProposalIDMock          mSectionPrimeDBMockGetSectionsByProposalID

	funcUpdateSectionsByProposalID          func(productID int64, proposalID int64, sections proposalvalueobject.Values) (sa1 []proposalentity.Stand, err error)
	funcUpdateSectionsByProposalIDOrigin    string
	inspectFuncUpdateSectionsByProposalID   func(productID int64, proposalID int64, sections proposalvalueobject.Values)
	afterUpdateSectionsByProposalIDCounter  uint64
	beforeUpdateSectionsByProposalIDCounter uint64
	UpdateSectionsByProposalIDMock          mSectionPrimeDBMockUpdateSectionsByProposalID
}

// NewSectionPrimeDBMock returns a mock for mm_repository.SectionPrimeDB
func NewSectionPrimeDBMock(t minimock.Tester) *SectionPrimeDBMock {
	m := &SectionPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetSectionsByProposalIDMock = mSectionPrimeDBMockGetSectionsByProposalID{mock: m}
	m.GetSectionsByProposalIDMock.callArgs = []*SectionPrimeDBMockGetSectionsByProposalIDParams{}

	m.UpdateSectionsByProposalIDMock = mSectionPrimeDBMockUpdateSectionsByProposalID{mock: m}
	m.UpdateSectionsByProposalIDMock.callArgs = []*SectionPrimeDBMockUpdateSectionsByProposalIDParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mSectionPrimeDBMockGetSectionsByProposalID struct {
	optional           bool
	mock               *SectionPrimeDBMock
	defaultExpectation *SectionPrimeDBMockGetSectionsByProposalIDExpectation
	expectations       []*SectionPrimeDBMockGetSectionsByProposalIDExpectation

	callArgs []*SectionPrimeDBMockGetSectionsByProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// SectionPrimeDBMockGetSectionsByProposalIDExpectation specifies expectation struct of the SectionPrimeDB.GetSectionsByProposalID
type SectionPrimeDBMockGetSectionsByProposalIDExpectation struct {
	mock               *SectionPrimeDBMock
	params             *SectionPrimeDBMockGetSectionsByProposalIDParams
	paramPtrs          *SectionPrimeDBMockGetSectionsByProposalIDParamPtrs
	expectationOrigins SectionPrimeDBMockGetSectionsByProposalIDExpectationOrigins
	results            *SectionPrimeDBMockGetSectionsByProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// SectionPrimeDBMockGetSectionsByProposalIDParams contains parameters of the SectionPrimeDB.GetSectionsByProposalID
type SectionPrimeDBMockGetSectionsByProposalIDParams struct {
	productID  int64
	proposalID int64
}

// SectionPrimeDBMockGetSectionsByProposalIDParamPtrs contains pointers to parameters of the SectionPrimeDB.GetSectionsByProposalID
type SectionPrimeDBMockGetSectionsByProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
}

// SectionPrimeDBMockGetSectionsByProposalIDResults contains results of the SectionPrimeDB.GetSectionsByProposalID
type SectionPrimeDBMockGetSectionsByProposalIDResults struct {
	sa1 []proposalentity.Stand
	err error
}

// SectionPrimeDBMockGetSectionsByProposalIDOrigins contains origins of expectations of the SectionPrimeDB.GetSectionsByProposalID
type SectionPrimeDBMockGetSectionsByProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Optional() *mSectionPrimeDBMockGetSectionsByProposalID {
	mmGetSectionsByProposalID.optional = true
	return mmGetSectionsByProposalID
}

// Expect sets up expected params for SectionPrimeDB.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Expect(productID int64, proposalID int64) *mSectionPrimeDBMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockGetSectionsByProposalIDExpectation{}
	}

	if mmGetSectionsByProposalID.defaultExpectation.paramPtrs != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by ExpectParams functions")
	}

	mmGetSectionsByProposalID.defaultExpectation.params = &SectionPrimeDBMockGetSectionsByProposalIDParams{productID, proposalID}
	mmGetSectionsByProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetSectionsByProposalID.expectations {
		if minimock.Equal(e.params, mmGetSectionsByProposalID.defaultExpectation.params) {
			mmGetSectionsByProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetSectionsByProposalID.defaultExpectation.params)
		}
	}

	return mmGetSectionsByProposalID
}

// ExpectProductIDParam1 sets up expected param productID for SectionPrimeDB.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) ExpectProductIDParam1(productID int64) *mSectionPrimeDBMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockGetSectionsByProposalIDExpectation{}
	}

	if mmGetSectionsByProposalID.defaultExpectation.params != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Expect")
	}

	if mmGetSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmGetSectionsByProposalID.defaultExpectation.paramPtrs = &SectionPrimeDBMockGetSectionsByProposalIDParamPtrs{}
	}
	mmGetSectionsByProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmGetSectionsByProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetSectionsByProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for SectionPrimeDB.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) ExpectProposalIDParam2(proposalID int64) *mSectionPrimeDBMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockGetSectionsByProposalIDExpectation{}
	}

	if mmGetSectionsByProposalID.defaultExpectation.params != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Expect")
	}

	if mmGetSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmGetSectionsByProposalID.defaultExpectation.paramPtrs = &SectionPrimeDBMockGetSectionsByProposalIDParamPtrs{}
	}
	mmGetSectionsByProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetSectionsByProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetSectionsByProposalID
}

// Inspect accepts an inspector function that has same arguments as the SectionPrimeDB.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Inspect(f func(productID int64, proposalID int64)) *mSectionPrimeDBMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.inspectFuncGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("Inspect function is already set for SectionPrimeDBMock.GetSectionsByProposalID")
	}

	mmGetSectionsByProposalID.mock.inspectFuncGetSectionsByProposalID = f

	return mmGetSectionsByProposalID
}

// Return sets up results that will be returned by SectionPrimeDB.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Return(sa1 []proposalentity.Stand, err error) *SectionPrimeDBMock {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockGetSectionsByProposalIDExpectation{mock: mmGetSectionsByProposalID.mock}
	}
	mmGetSectionsByProposalID.defaultExpectation.results = &SectionPrimeDBMockGetSectionsByProposalIDResults{sa1, err}
	mmGetSectionsByProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetSectionsByProposalID.mock
}

// Set uses given function f to mock the SectionPrimeDB.GetSectionsByProposalID method
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Set(f func(productID int64, proposalID int64) (sa1 []proposalentity.Stand, err error)) *SectionPrimeDBMock {
	if mmGetSectionsByProposalID.defaultExpectation != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("Default expectation is already set for the SectionPrimeDB.GetSectionsByProposalID method")
	}

	if len(mmGetSectionsByProposalID.expectations) > 0 {
		mmGetSectionsByProposalID.mock.t.Fatalf("Some expectations are already set for the SectionPrimeDB.GetSectionsByProposalID method")
	}

	mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID = f
	mmGetSectionsByProposalID.mock.funcGetSectionsByProposalIDOrigin = minimock.CallerInfo(1)
	return mmGetSectionsByProposalID.mock
}

// When sets expectation for the SectionPrimeDB.GetSectionsByProposalID which will trigger the result defined by the following
// Then helper
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) When(productID int64, proposalID int64) *SectionPrimeDBMockGetSectionsByProposalIDExpectation {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.GetSectionsByProposalID mock is already set by Set")
	}

	expectation := &SectionPrimeDBMockGetSectionsByProposalIDExpectation{
		mock:               mmGetSectionsByProposalID.mock,
		params:             &SectionPrimeDBMockGetSectionsByProposalIDParams{productID, proposalID},
		expectationOrigins: SectionPrimeDBMockGetSectionsByProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetSectionsByProposalID.expectations = append(mmGetSectionsByProposalID.expectations, expectation)
	return expectation
}

// Then sets up SectionPrimeDB.GetSectionsByProposalID return parameters for the expectation previously defined by the When method
func (e *SectionPrimeDBMockGetSectionsByProposalIDExpectation) Then(sa1 []proposalentity.Stand, err error) *SectionPrimeDBMock {
	e.results = &SectionPrimeDBMockGetSectionsByProposalIDResults{sa1, err}
	return e.mock
}

// Times sets number of times SectionPrimeDB.GetSectionsByProposalID should be invoked
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Times(n uint64) *mSectionPrimeDBMockGetSectionsByProposalID {
	if n == 0 {
		mmGetSectionsByProposalID.mock.t.Fatalf("Times of SectionPrimeDBMock.GetSectionsByProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetSectionsByProposalID.expectedInvocations, n)
	mmGetSectionsByProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetSectionsByProposalID
}

func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) invocationsDone() bool {
	if len(mmGetSectionsByProposalID.expectations) == 0 && mmGetSectionsByProposalID.defaultExpectation == nil && mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetSectionsByProposalID.mock.afterGetSectionsByProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetSectionsByProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetSectionsByProposalID implements mm_repository.SectionPrimeDB
func (mmGetSectionsByProposalID *SectionPrimeDBMock) GetSectionsByProposalID(productID int64, proposalID int64) (sa1 []proposalentity.Stand, err error) {
	mm_atomic.AddUint64(&mmGetSectionsByProposalID.beforeGetSectionsByProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetSectionsByProposalID.afterGetSectionsByProposalIDCounter, 1)

	mmGetSectionsByProposalID.t.Helper()

	if mmGetSectionsByProposalID.inspectFuncGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.inspectFuncGetSectionsByProposalID(productID, proposalID)
	}

	mm_params := SectionPrimeDBMockGetSectionsByProposalIDParams{productID, proposalID}

	// Record call args
	mmGetSectionsByProposalID.GetSectionsByProposalIDMock.mutex.Lock()
	mmGetSectionsByProposalID.GetSectionsByProposalIDMock.callArgs = append(mmGetSectionsByProposalID.GetSectionsByProposalIDMock.callArgs, &mm_params)
	mmGetSectionsByProposalID.GetSectionsByProposalIDMock.mutex.Unlock()

	for _, e := range mmGetSectionsByProposalID.GetSectionsByProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.sa1, e.results.err
		}
	}

	if mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.paramPtrs

		mm_got := SectionPrimeDBMockGetSectionsByProposalIDParams{productID, proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetSectionsByProposalID.t.Errorf("SectionPrimeDBMock.GetSectionsByProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetSectionsByProposalID.t.Errorf("SectionPrimeDBMock.GetSectionsByProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetSectionsByProposalID.t.Errorf("SectionPrimeDBMock.GetSectionsByProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetSectionsByProposalID.t.Fatal("No results are set for the SectionPrimeDBMock.GetSectionsByProposalID")
		}
		return (*mm_results).sa1, (*mm_results).err
	}
	if mmGetSectionsByProposalID.funcGetSectionsByProposalID != nil {
		return mmGetSectionsByProposalID.funcGetSectionsByProposalID(productID, proposalID)
	}
	mmGetSectionsByProposalID.t.Fatalf("Unexpected call to SectionPrimeDBMock.GetSectionsByProposalID. %v %v", productID, proposalID)
	return
}

// GetSectionsByProposalIDAfterCounter returns a count of finished SectionPrimeDBMock.GetSectionsByProposalID invocations
func (mmGetSectionsByProposalID *SectionPrimeDBMock) GetSectionsByProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSectionsByProposalID.afterGetSectionsByProposalIDCounter)
}

// GetSectionsByProposalIDBeforeCounter returns a count of SectionPrimeDBMock.GetSectionsByProposalID invocations
func (mmGetSectionsByProposalID *SectionPrimeDBMock) GetSectionsByProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSectionsByProposalID.beforeGetSectionsByProposalIDCounter)
}

// Calls returns a list of arguments used in each call to SectionPrimeDBMock.GetSectionsByProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetSectionsByProposalID *mSectionPrimeDBMockGetSectionsByProposalID) Calls() []*SectionPrimeDBMockGetSectionsByProposalIDParams {
	mmGetSectionsByProposalID.mutex.RLock()

	argCopy := make([]*SectionPrimeDBMockGetSectionsByProposalIDParams, len(mmGetSectionsByProposalID.callArgs))
	copy(argCopy, mmGetSectionsByProposalID.callArgs)

	mmGetSectionsByProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockGetSectionsByProposalIDDone returns true if the count of the GetSectionsByProposalID invocations corresponds
// the number of defined expectations
func (m *SectionPrimeDBMock) MinimockGetSectionsByProposalIDDone() bool {
	if m.GetSectionsByProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetSectionsByProposalIDMock.invocationsDone()
}

// MinimockGetSectionsByProposalIDInspect logs each unmet expectation
func (m *SectionPrimeDBMock) MinimockGetSectionsByProposalIDInspect() {
	for _, e := range m.GetSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to SectionPrimeDBMock.GetSectionsByProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetSectionsByProposalIDCounter := mm_atomic.LoadUint64(&m.afterGetSectionsByProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetSectionsByProposalIDMock.defaultExpectation != nil && afterGetSectionsByProposalIDCounter < 1 {
		if m.GetSectionsByProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to SectionPrimeDBMock.GetSectionsByProposalID at\n%s", m.GetSectionsByProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to SectionPrimeDBMock.GetSectionsByProposalID at\n%s with params: %#v", m.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.GetSectionsByProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetSectionsByProposalID != nil && afterGetSectionsByProposalIDCounter < 1 {
		m.t.Errorf("Expected call to SectionPrimeDBMock.GetSectionsByProposalID at\n%s", m.funcGetSectionsByProposalIDOrigin)
	}

	if !m.GetSectionsByProposalIDMock.invocationsDone() && afterGetSectionsByProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to SectionPrimeDBMock.GetSectionsByProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetSectionsByProposalIDMock.expectedInvocations), m.GetSectionsByProposalIDMock.expectedInvocationsOrigin, afterGetSectionsByProposalIDCounter)
	}
}

type mSectionPrimeDBMockUpdateSectionsByProposalID struct {
	optional           bool
	mock               *SectionPrimeDBMock
	defaultExpectation *SectionPrimeDBMockUpdateSectionsByProposalIDExpectation
	expectations       []*SectionPrimeDBMockUpdateSectionsByProposalIDExpectation

	callArgs []*SectionPrimeDBMockUpdateSectionsByProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// SectionPrimeDBMockUpdateSectionsByProposalIDExpectation specifies expectation struct of the SectionPrimeDB.UpdateSectionsByProposalID
type SectionPrimeDBMockUpdateSectionsByProposalIDExpectation struct {
	mock               *SectionPrimeDBMock
	params             *SectionPrimeDBMockUpdateSectionsByProposalIDParams
	paramPtrs          *SectionPrimeDBMockUpdateSectionsByProposalIDParamPtrs
	expectationOrigins SectionPrimeDBMockUpdateSectionsByProposalIDExpectationOrigins
	results            *SectionPrimeDBMockUpdateSectionsByProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// SectionPrimeDBMockUpdateSectionsByProposalIDParams contains parameters of the SectionPrimeDB.UpdateSectionsByProposalID
type SectionPrimeDBMockUpdateSectionsByProposalIDParams struct {
	productID  int64
	proposalID int64
	sections   proposalvalueobject.Values
}

// SectionPrimeDBMockUpdateSectionsByProposalIDParamPtrs contains pointers to parameters of the SectionPrimeDB.UpdateSectionsByProposalID
type SectionPrimeDBMockUpdateSectionsByProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
	sections   *proposalvalueobject.Values
}

// SectionPrimeDBMockUpdateSectionsByProposalIDResults contains results of the SectionPrimeDB.UpdateSectionsByProposalID
type SectionPrimeDBMockUpdateSectionsByProposalIDResults struct {
	sa1 []proposalentity.Stand
	err error
}

// SectionPrimeDBMockUpdateSectionsByProposalIDOrigins contains origins of expectations of the SectionPrimeDB.UpdateSectionsByProposalID
type SectionPrimeDBMockUpdateSectionsByProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
	originSections   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Optional() *mSectionPrimeDBMockUpdateSectionsByProposalID {
	mmUpdateSectionsByProposalID.optional = true
	return mmUpdateSectionsByProposalID
}

// Expect sets up expected params for SectionPrimeDB.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Expect(productID int64, proposalID int64, sections proposalvalueobject.Values) *mSectionPrimeDBMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by ExpectParams functions")
	}

	mmUpdateSectionsByProposalID.defaultExpectation.params = &SectionPrimeDBMockUpdateSectionsByProposalIDParams{productID, proposalID, sections}
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateSectionsByProposalID.expectations {
		if minimock.Equal(e.params, mmUpdateSectionsByProposalID.defaultExpectation.params) {
			mmUpdateSectionsByProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateSectionsByProposalID.defaultExpectation.params)
		}
	}

	return mmUpdateSectionsByProposalID
}

// ExpectProductIDParam1 sets up expected param productID for SectionPrimeDB.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) ExpectProductIDParam1(productID int64) *mSectionPrimeDBMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.params != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Expect")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs = &SectionPrimeDBMockUpdateSectionsByProposalIDParamPtrs{}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmUpdateSectionsByProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for SectionPrimeDB.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) ExpectProposalIDParam2(proposalID int64) *mSectionPrimeDBMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.params != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Expect")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs = &SectionPrimeDBMockUpdateSectionsByProposalIDParamPtrs{}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmUpdateSectionsByProposalID
}

// ExpectSectionsParam3 sets up expected param sections for SectionPrimeDB.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) ExpectSectionsParam3(sections proposalvalueobject.Values) *mSectionPrimeDBMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.params != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Expect")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs = &SectionPrimeDBMockUpdateSectionsByProposalIDParamPtrs{}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs.sections = &sections
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.originSections = minimock.CallerInfo(1)

	return mmUpdateSectionsByProposalID
}

// Inspect accepts an inspector function that has same arguments as the SectionPrimeDB.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Inspect(f func(productID int64, proposalID int64, sections proposalvalueobject.Values)) *mSectionPrimeDBMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.inspectFuncUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Inspect function is already set for SectionPrimeDBMock.UpdateSectionsByProposalID")
	}

	mmUpdateSectionsByProposalID.mock.inspectFuncUpdateSectionsByProposalID = f

	return mmUpdateSectionsByProposalID
}

// Return sets up results that will be returned by SectionPrimeDB.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Return(sa1 []proposalentity.Stand, err error) *SectionPrimeDBMock {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionPrimeDBMockUpdateSectionsByProposalIDExpectation{mock: mmUpdateSectionsByProposalID.mock}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.results = &SectionPrimeDBMockUpdateSectionsByProposalIDResults{sa1, err}
	mmUpdateSectionsByProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateSectionsByProposalID.mock
}

// Set uses given function f to mock the SectionPrimeDB.UpdateSectionsByProposalID method
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Set(f func(productID int64, proposalID int64, sections proposalvalueobject.Values) (sa1 []proposalentity.Stand, err error)) *SectionPrimeDBMock {
	if mmUpdateSectionsByProposalID.defaultExpectation != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Default expectation is already set for the SectionPrimeDB.UpdateSectionsByProposalID method")
	}

	if len(mmUpdateSectionsByProposalID.expectations) > 0 {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Some expectations are already set for the SectionPrimeDB.UpdateSectionsByProposalID method")
	}

	mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID = f
	mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalIDOrigin = minimock.CallerInfo(1)
	return mmUpdateSectionsByProposalID.mock
}

// When sets expectation for the SectionPrimeDB.UpdateSectionsByProposalID which will trigger the result defined by the following
// Then helper
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) When(productID int64, proposalID int64, sections proposalvalueobject.Values) *SectionPrimeDBMockUpdateSectionsByProposalIDExpectation {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionPrimeDBMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	expectation := &SectionPrimeDBMockUpdateSectionsByProposalIDExpectation{
		mock:               mmUpdateSectionsByProposalID.mock,
		params:             &SectionPrimeDBMockUpdateSectionsByProposalIDParams{productID, proposalID, sections},
		expectationOrigins: SectionPrimeDBMockUpdateSectionsByProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateSectionsByProposalID.expectations = append(mmUpdateSectionsByProposalID.expectations, expectation)
	return expectation
}

// Then sets up SectionPrimeDB.UpdateSectionsByProposalID return parameters for the expectation previously defined by the When method
func (e *SectionPrimeDBMockUpdateSectionsByProposalIDExpectation) Then(sa1 []proposalentity.Stand, err error) *SectionPrimeDBMock {
	e.results = &SectionPrimeDBMockUpdateSectionsByProposalIDResults{sa1, err}
	return e.mock
}

// Times sets number of times SectionPrimeDB.UpdateSectionsByProposalID should be invoked
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Times(n uint64) *mSectionPrimeDBMockUpdateSectionsByProposalID {
	if n == 0 {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Times of SectionPrimeDBMock.UpdateSectionsByProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateSectionsByProposalID.expectedInvocations, n)
	mmUpdateSectionsByProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateSectionsByProposalID
}

func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) invocationsDone() bool {
	if len(mmUpdateSectionsByProposalID.expectations) == 0 && mmUpdateSectionsByProposalID.defaultExpectation == nil && mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.mock.afterUpdateSectionsByProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateSectionsByProposalID implements mm_repository.SectionPrimeDB
func (mmUpdateSectionsByProposalID *SectionPrimeDBMock) UpdateSectionsByProposalID(productID int64, proposalID int64, sections proposalvalueobject.Values) (sa1 []proposalentity.Stand, err error) {
	mm_atomic.AddUint64(&mmUpdateSectionsByProposalID.beforeUpdateSectionsByProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateSectionsByProposalID.afterUpdateSectionsByProposalIDCounter, 1)

	mmUpdateSectionsByProposalID.t.Helper()

	if mmUpdateSectionsByProposalID.inspectFuncUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.inspectFuncUpdateSectionsByProposalID(productID, proposalID, sections)
	}

	mm_params := SectionPrimeDBMockUpdateSectionsByProposalIDParams{productID, proposalID, sections}

	// Record call args
	mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.mutex.Lock()
	mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.callArgs = append(mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.callArgs, &mm_params)
	mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.mutex.Unlock()

	for _, e := range mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.sa1, e.results.err
		}
	}

	if mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.paramPtrs

		mm_got := SectionPrimeDBMockUpdateSectionsByProposalIDParams{productID, proposalID, sections}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmUpdateSectionsByProposalID.t.Errorf("SectionPrimeDBMock.UpdateSectionsByProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmUpdateSectionsByProposalID.t.Errorf("SectionPrimeDBMock.UpdateSectionsByProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

			if mm_want_ptrs.sections != nil && !minimock.Equal(*mm_want_ptrs.sections, mm_got.sections) {
				mmUpdateSectionsByProposalID.t.Errorf("SectionPrimeDBMock.UpdateSectionsByProposalID got unexpected parameter sections, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originSections, *mm_want_ptrs.sections, mm_got.sections, minimock.Diff(*mm_want_ptrs.sections, mm_got.sections))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateSectionsByProposalID.t.Errorf("SectionPrimeDBMock.UpdateSectionsByProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateSectionsByProposalID.t.Fatal("No results are set for the SectionPrimeDBMock.UpdateSectionsByProposalID")
		}
		return (*mm_results).sa1, (*mm_results).err
	}
	if mmUpdateSectionsByProposalID.funcUpdateSectionsByProposalID != nil {
		return mmUpdateSectionsByProposalID.funcUpdateSectionsByProposalID(productID, proposalID, sections)
	}
	mmUpdateSectionsByProposalID.t.Fatalf("Unexpected call to SectionPrimeDBMock.UpdateSectionsByProposalID. %v %v %v", productID, proposalID, sections)
	return
}

// UpdateSectionsByProposalIDAfterCounter returns a count of finished SectionPrimeDBMock.UpdateSectionsByProposalID invocations
func (mmUpdateSectionsByProposalID *SectionPrimeDBMock) UpdateSectionsByProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.afterUpdateSectionsByProposalIDCounter)
}

// UpdateSectionsByProposalIDBeforeCounter returns a count of SectionPrimeDBMock.UpdateSectionsByProposalID invocations
func (mmUpdateSectionsByProposalID *SectionPrimeDBMock) UpdateSectionsByProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.beforeUpdateSectionsByProposalIDCounter)
}

// Calls returns a list of arguments used in each call to SectionPrimeDBMock.UpdateSectionsByProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateSectionsByProposalID *mSectionPrimeDBMockUpdateSectionsByProposalID) Calls() []*SectionPrimeDBMockUpdateSectionsByProposalIDParams {
	mmUpdateSectionsByProposalID.mutex.RLock()

	argCopy := make([]*SectionPrimeDBMockUpdateSectionsByProposalIDParams, len(mmUpdateSectionsByProposalID.callArgs))
	copy(argCopy, mmUpdateSectionsByProposalID.callArgs)

	mmUpdateSectionsByProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateSectionsByProposalIDDone returns true if the count of the UpdateSectionsByProposalID invocations corresponds
// the number of defined expectations
func (m *SectionPrimeDBMock) MinimockUpdateSectionsByProposalIDDone() bool {
	if m.UpdateSectionsByProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateSectionsByProposalIDMock.invocationsDone()
}

// MinimockUpdateSectionsByProposalIDInspect logs each unmet expectation
func (m *SectionPrimeDBMock) MinimockUpdateSectionsByProposalIDInspect() {
	for _, e := range m.UpdateSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to SectionPrimeDBMock.UpdateSectionsByProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateSectionsByProposalIDCounter := mm_atomic.LoadUint64(&m.afterUpdateSectionsByProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateSectionsByProposalIDMock.defaultExpectation != nil && afterUpdateSectionsByProposalIDCounter < 1 {
		if m.UpdateSectionsByProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to SectionPrimeDBMock.UpdateSectionsByProposalID at\n%s", m.UpdateSectionsByProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to SectionPrimeDBMock.UpdateSectionsByProposalID at\n%s with params: %#v", m.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.UpdateSectionsByProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateSectionsByProposalID != nil && afterUpdateSectionsByProposalIDCounter < 1 {
		m.t.Errorf("Expected call to SectionPrimeDBMock.UpdateSectionsByProposalID at\n%s", m.funcUpdateSectionsByProposalIDOrigin)
	}

	if !m.UpdateSectionsByProposalIDMock.invocationsDone() && afterUpdateSectionsByProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to SectionPrimeDBMock.UpdateSectionsByProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateSectionsByProposalIDMock.expectedInvocations), m.UpdateSectionsByProposalIDMock.expectedInvocationsOrigin, afterUpdateSectionsByProposalIDCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *SectionPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetSectionsByProposalIDInspect()

			m.MinimockUpdateSectionsByProposalIDInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *SectionPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *SectionPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetSectionsByProposalIDDone() &&
		m.MinimockUpdateSectionsByProposalIDDone()
}
