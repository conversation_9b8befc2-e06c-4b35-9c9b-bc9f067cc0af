// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository.ProposalCache -o proposal_cache_mock.go -n ProposalCacheMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	"github.com/gojuno/minimock/v3"
)

// ProposalCacheMock implements mm_repository.ProposalCache
type ProposalCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetByID          func(id int64) (p1 proposalentity.Proposal, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mProposalCacheMockGetByID

	funcGetByProductID          func(productID int64) (pa1 []proposalentity.Proposal, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mProposalCacheMockGetByProductID

	funcGetByProductIDAndProposalID          func(productID int64, proposalID int64) (p1 proposalentity.Proposal, err error)
	funcGetByProductIDAndProposalIDOrigin    string
	inspectFuncGetByProductIDAndProposalID   func(productID int64, proposalID int64)
	afterGetByProductIDAndProposalIDCounter  uint64
	beforeGetByProductIDAndProposalIDCounter uint64
	GetByProductIDAndProposalIDMock          mProposalCacheMockGetByProductIDAndProposalID

	funcSet          func(proposal proposalentity.Proposal) (err error)
	funcSetOrigin    string
	inspectFuncSet   func(proposal proposalentity.Proposal)
	afterSetCounter  uint64
	beforeSetCounter uint64
	SetMock          mProposalCacheMockSet
}

// NewProposalCacheMock returns a mock for mm_repository.ProposalCache
func NewProposalCacheMock(t minimock.Tester) *ProposalCacheMock {
	m := &ProposalCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetByIDMock = mProposalCacheMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*ProposalCacheMockGetByIDParams{}

	m.GetByProductIDMock = mProposalCacheMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*ProposalCacheMockGetByProductIDParams{}

	m.GetByProductIDAndProposalIDMock = mProposalCacheMockGetByProductIDAndProposalID{mock: m}
	m.GetByProductIDAndProposalIDMock.callArgs = []*ProposalCacheMockGetByProductIDAndProposalIDParams{}

	m.SetMock = mProposalCacheMockSet{mock: m}
	m.SetMock.callArgs = []*ProposalCacheMockSetParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProposalCacheMockGetByID struct {
	optional           bool
	mock               *ProposalCacheMock
	defaultExpectation *ProposalCacheMockGetByIDExpectation
	expectations       []*ProposalCacheMockGetByIDExpectation

	callArgs []*ProposalCacheMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalCacheMockGetByIDExpectation specifies expectation struct of the ProposalCache.GetByID
type ProposalCacheMockGetByIDExpectation struct {
	mock               *ProposalCacheMock
	params             *ProposalCacheMockGetByIDParams
	paramPtrs          *ProposalCacheMockGetByIDParamPtrs
	expectationOrigins ProposalCacheMockGetByIDExpectationOrigins
	results            *ProposalCacheMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalCacheMockGetByIDParams contains parameters of the ProposalCache.GetByID
type ProposalCacheMockGetByIDParams struct {
	id int64
}

// ProposalCacheMockGetByIDParamPtrs contains pointers to parameters of the ProposalCache.GetByID
type ProposalCacheMockGetByIDParamPtrs struct {
	id *int64
}

// ProposalCacheMockGetByIDResults contains results of the ProposalCache.GetByID
type ProposalCacheMockGetByIDResults struct {
	p1  proposalentity.Proposal
	err error
}

// ProposalCacheMockGetByIDOrigins contains origins of expectations of the ProposalCache.GetByID
type ProposalCacheMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mProposalCacheMockGetByID) Optional() *mProposalCacheMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for ProposalCache.GetByID
func (mmGetByID *mProposalCacheMockGetByID) Expect(id int64) *mProposalCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("ProposalCacheMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &ProposalCacheMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for ProposalCache.GetByID
func (mmGetByID *mProposalCacheMockGetByID) ExpectIdParam1(id int64) *mProposalCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("ProposalCacheMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &ProposalCacheMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the ProposalCache.GetByID
func (mmGetByID *mProposalCacheMockGetByID) Inspect(f func(id int64)) *mProposalCacheMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for ProposalCacheMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by ProposalCache.GetByID
func (mmGetByID *mProposalCacheMockGetByID) Return(p1 proposalentity.Proposal, err error) *ProposalCacheMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &ProposalCacheMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &ProposalCacheMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the ProposalCache.GetByID method
func (mmGetByID *mProposalCacheMockGetByID) Set(f func(id int64) (p1 proposalentity.Proposal, err error)) *ProposalCacheMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the ProposalCache.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the ProposalCache.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the ProposalCache.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mProposalCacheMockGetByID) When(id int64) *ProposalCacheMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("ProposalCacheMock.GetByID mock is already set by Set")
	}

	expectation := &ProposalCacheMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &ProposalCacheMockGetByIDParams{id},
		expectationOrigins: ProposalCacheMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up ProposalCache.GetByID return parameters for the expectation previously defined by the When method
func (e *ProposalCacheMockGetByIDExpectation) Then(p1 proposalentity.Proposal, err error) *ProposalCacheMock {
	e.results = &ProposalCacheMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalCache.GetByID should be invoked
func (mmGetByID *mProposalCacheMockGetByID) Times(n uint64) *mProposalCacheMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of ProposalCacheMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mProposalCacheMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.ProposalCache
func (mmGetByID *ProposalCacheMock) GetByID(id int64) (p1 proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := ProposalCacheMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalCacheMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("ProposalCacheMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("ProposalCacheMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the ProposalCacheMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to ProposalCacheMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished ProposalCacheMock.GetByID invocations
func (mmGetByID *ProposalCacheMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of ProposalCacheMock.GetByID invocations
func (mmGetByID *ProposalCacheMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalCacheMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mProposalCacheMockGetByID) Calls() []*ProposalCacheMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*ProposalCacheMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *ProposalCacheMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *ProposalCacheMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalCacheMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalCacheMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mProposalCacheMockGetByProductID struct {
	optional           bool
	mock               *ProposalCacheMock
	defaultExpectation *ProposalCacheMockGetByProductIDExpectation
	expectations       []*ProposalCacheMockGetByProductIDExpectation

	callArgs []*ProposalCacheMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalCacheMockGetByProductIDExpectation specifies expectation struct of the ProposalCache.GetByProductID
type ProposalCacheMockGetByProductIDExpectation struct {
	mock               *ProposalCacheMock
	params             *ProposalCacheMockGetByProductIDParams
	paramPtrs          *ProposalCacheMockGetByProductIDParamPtrs
	expectationOrigins ProposalCacheMockGetByProductIDExpectationOrigins
	results            *ProposalCacheMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalCacheMockGetByProductIDParams contains parameters of the ProposalCache.GetByProductID
type ProposalCacheMockGetByProductIDParams struct {
	productID int64
}

// ProposalCacheMockGetByProductIDParamPtrs contains pointers to parameters of the ProposalCache.GetByProductID
type ProposalCacheMockGetByProductIDParamPtrs struct {
	productID *int64
}

// ProposalCacheMockGetByProductIDResults contains results of the ProposalCache.GetByProductID
type ProposalCacheMockGetByProductIDResults struct {
	pa1 []proposalentity.Proposal
	err error
}

// ProposalCacheMockGetByProductIDOrigins contains origins of expectations of the ProposalCache.GetByProductID
type ProposalCacheMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mProposalCacheMockGetByProductID) Optional() *mProposalCacheMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for ProposalCache.GetByProductID
func (mmGetByProductID *mProposalCacheMockGetByProductID) Expect(productID int64) *mProposalCacheMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalCacheMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalCacheMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalCacheMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &ProposalCacheMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for ProposalCache.GetByProductID
func (mmGetByProductID *mProposalCacheMockGetByProductID) ExpectProductIDParam1(productID int64) *mProposalCacheMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalCacheMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalCacheMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalCacheMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &ProposalCacheMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the ProposalCache.GetByProductID
func (mmGetByProductID *mProposalCacheMockGetByProductID) Inspect(f func(productID int64)) *mProposalCacheMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for ProposalCacheMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by ProposalCache.GetByProductID
func (mmGetByProductID *mProposalCacheMockGetByProductID) Return(pa1 []proposalentity.Proposal, err error) *ProposalCacheMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalCacheMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &ProposalCacheMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &ProposalCacheMockGetByProductIDResults{pa1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the ProposalCache.GetByProductID method
func (mmGetByProductID *mProposalCacheMockGetByProductID) Set(f func(productID int64) (pa1 []proposalentity.Proposal, err error)) *ProposalCacheMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the ProposalCache.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the ProposalCache.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the ProposalCache.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mProposalCacheMockGetByProductID) When(productID int64) *ProposalCacheMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("ProposalCacheMock.GetByProductID mock is already set by Set")
	}

	expectation := &ProposalCacheMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &ProposalCacheMockGetByProductIDParams{productID},
		expectationOrigins: ProposalCacheMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up ProposalCache.GetByProductID return parameters for the expectation previously defined by the When method
func (e *ProposalCacheMockGetByProductIDExpectation) Then(pa1 []proposalentity.Proposal, err error) *ProposalCacheMock {
	e.results = &ProposalCacheMockGetByProductIDResults{pa1, err}
	return e.mock
}

// Times sets number of times ProposalCache.GetByProductID should be invoked
func (mmGetByProductID *mProposalCacheMockGetByProductID) Times(n uint64) *mProposalCacheMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of ProposalCacheMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mProposalCacheMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_repository.ProposalCache
func (mmGetByProductID *ProposalCacheMock) GetByProductID(productID int64) (pa1 []proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := ProposalCacheMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalCacheMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("ProposalCacheMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("ProposalCacheMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the ProposalCacheMock.GetByProductID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to ProposalCacheMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished ProposalCacheMock.GetByProductID invocations
func (mmGetByProductID *ProposalCacheMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of ProposalCacheMock.GetByProductID invocations
func (mmGetByProductID *ProposalCacheMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalCacheMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mProposalCacheMockGetByProductID) Calls() []*ProposalCacheMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*ProposalCacheMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *ProposalCacheMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *ProposalCacheMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalCacheMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalCacheMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mProposalCacheMockGetByProductIDAndProposalID struct {
	optional           bool
	mock               *ProposalCacheMock
	defaultExpectation *ProposalCacheMockGetByProductIDAndProposalIDExpectation
	expectations       []*ProposalCacheMockGetByProductIDAndProposalIDExpectation

	callArgs []*ProposalCacheMockGetByProductIDAndProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalCacheMockGetByProductIDAndProposalIDExpectation specifies expectation struct of the ProposalCache.GetByProductIDAndProposalID
type ProposalCacheMockGetByProductIDAndProposalIDExpectation struct {
	mock               *ProposalCacheMock
	params             *ProposalCacheMockGetByProductIDAndProposalIDParams
	paramPtrs          *ProposalCacheMockGetByProductIDAndProposalIDParamPtrs
	expectationOrigins ProposalCacheMockGetByProductIDAndProposalIDExpectationOrigins
	results            *ProposalCacheMockGetByProductIDAndProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalCacheMockGetByProductIDAndProposalIDParams contains parameters of the ProposalCache.GetByProductIDAndProposalID
type ProposalCacheMockGetByProductIDAndProposalIDParams struct {
	productID  int64
	proposalID int64
}

// ProposalCacheMockGetByProductIDAndProposalIDParamPtrs contains pointers to parameters of the ProposalCache.GetByProductIDAndProposalID
type ProposalCacheMockGetByProductIDAndProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
}

// ProposalCacheMockGetByProductIDAndProposalIDResults contains results of the ProposalCache.GetByProductIDAndProposalID
type ProposalCacheMockGetByProductIDAndProposalIDResults struct {
	p1  proposalentity.Proposal
	err error
}

// ProposalCacheMockGetByProductIDAndProposalIDOrigins contains origins of expectations of the ProposalCache.GetByProductIDAndProposalID
type ProposalCacheMockGetByProductIDAndProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Optional() *mProposalCacheMockGetByProductIDAndProposalID {
	mmGetByProductIDAndProposalID.optional = true
	return mmGetByProductIDAndProposalID
}

// Expect sets up expected params for ProposalCache.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Expect(productID int64, proposalID int64) *mProposalCacheMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalCacheMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by ExpectParams functions")
	}

	mmGetByProductIDAndProposalID.defaultExpectation.params = &ProposalCacheMockGetByProductIDAndProposalIDParams{productID, proposalID}
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductIDAndProposalID.expectations {
		if minimock.Equal(e.params, mmGetByProductIDAndProposalID.defaultExpectation.params) {
			mmGetByProductIDAndProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductIDAndProposalID.defaultExpectation.params)
		}
	}

	return mmGetByProductIDAndProposalID
}

// ExpectProductIDParam1 sets up expected param productID for ProposalCache.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) ExpectProductIDParam1(productID int64) *mProposalCacheMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalCacheMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.params != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Expect")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs = &ProposalCacheMockGetByProductIDAndProposalIDParamPtrs{}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductIDAndProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for ProposalCache.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) ExpectProposalIDParam2(proposalID int64) *mProposalCacheMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalCacheMockGetByProductIDAndProposalIDExpectation{}
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.params != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Expect")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs = &ProposalCacheMockGetByProductIDAndProposalIDParamPtrs{}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetByProductIDAndProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetByProductIDAndProposalID
}

// Inspect accepts an inspector function that has same arguments as the ProposalCache.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Inspect(f func(productID int64, proposalID int64)) *mProposalCacheMockGetByProductIDAndProposalID {
	if mmGetByProductIDAndProposalID.mock.inspectFuncGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Inspect function is already set for ProposalCacheMock.GetByProductIDAndProposalID")
	}

	mmGetByProductIDAndProposalID.mock.inspectFuncGetByProductIDAndProposalID = f

	return mmGetByProductIDAndProposalID
}

// Return sets up results that will be returned by ProposalCache.GetByProductIDAndProposalID
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Return(p1 proposalentity.Proposal, err error) *ProposalCacheMock {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	if mmGetByProductIDAndProposalID.defaultExpectation == nil {
		mmGetByProductIDAndProposalID.defaultExpectation = &ProposalCacheMockGetByProductIDAndProposalIDExpectation{mock: mmGetByProductIDAndProposalID.mock}
	}
	mmGetByProductIDAndProposalID.defaultExpectation.results = &ProposalCacheMockGetByProductIDAndProposalIDResults{p1, err}
	mmGetByProductIDAndProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID.mock
}

// Set uses given function f to mock the ProposalCache.GetByProductIDAndProposalID method
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Set(f func(productID int64, proposalID int64) (p1 proposalentity.Proposal, err error)) *ProposalCacheMock {
	if mmGetByProductIDAndProposalID.defaultExpectation != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Default expectation is already set for the ProposalCache.GetByProductIDAndProposalID method")
	}

	if len(mmGetByProductIDAndProposalID.expectations) > 0 {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Some expectations are already set for the ProposalCache.GetByProductIDAndProposalID method")
	}

	mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID = f
	mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID.mock
}

// When sets expectation for the ProposalCache.GetByProductIDAndProposalID which will trigger the result defined by the following
// Then helper
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) When(productID int64, proposalID int64) *ProposalCacheMockGetByProductIDAndProposalIDExpectation {
	if mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("ProposalCacheMock.GetByProductIDAndProposalID mock is already set by Set")
	}

	expectation := &ProposalCacheMockGetByProductIDAndProposalIDExpectation{
		mock:               mmGetByProductIDAndProposalID.mock,
		params:             &ProposalCacheMockGetByProductIDAndProposalIDParams{productID, proposalID},
		expectationOrigins: ProposalCacheMockGetByProductIDAndProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductIDAndProposalID.expectations = append(mmGetByProductIDAndProposalID.expectations, expectation)
	return expectation
}

// Then sets up ProposalCache.GetByProductIDAndProposalID return parameters for the expectation previously defined by the When method
func (e *ProposalCacheMockGetByProductIDAndProposalIDExpectation) Then(p1 proposalentity.Proposal, err error) *ProposalCacheMock {
	e.results = &ProposalCacheMockGetByProductIDAndProposalIDResults{p1, err}
	return e.mock
}

// Times sets number of times ProposalCache.GetByProductIDAndProposalID should be invoked
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Times(n uint64) *mProposalCacheMockGetByProductIDAndProposalID {
	if n == 0 {
		mmGetByProductIDAndProposalID.mock.t.Fatalf("Times of ProposalCacheMock.GetByProductIDAndProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductIDAndProposalID.expectedInvocations, n)
	mmGetByProductIDAndProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndProposalID
}

func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) invocationsDone() bool {
	if len(mmGetByProductIDAndProposalID.expectations) == 0 && mmGetByProductIDAndProposalID.defaultExpectation == nil && mmGetByProductIDAndProposalID.mock.funcGetByProductIDAndProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.mock.afterGetByProductIDAndProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductIDAndProposalID implements mm_repository.ProposalCache
func (mmGetByProductIDAndProposalID *ProposalCacheMock) GetByProductIDAndProposalID(productID int64, proposalID int64) (p1 proposalentity.Proposal, err error) {
	mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.beforeGetByProductIDAndProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.afterGetByProductIDAndProposalIDCounter, 1)

	mmGetByProductIDAndProposalID.t.Helper()

	if mmGetByProductIDAndProposalID.inspectFuncGetByProductIDAndProposalID != nil {
		mmGetByProductIDAndProposalID.inspectFuncGetByProductIDAndProposalID(productID, proposalID)
	}

	mm_params := ProposalCacheMockGetByProductIDAndProposalIDParams{productID, proposalID}

	// Record call args
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.mutex.Lock()
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.callArgs = append(mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.callArgs, &mm_params)
	mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.mutex.Unlock()

	for _, e := range mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalCacheMockGetByProductIDAndProposalIDParams{productID, proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductIDAndProposalID.t.Errorf("ProposalCacheMock.GetByProductIDAndProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetByProductIDAndProposalID.t.Errorf("ProposalCacheMock.GetByProductIDAndProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductIDAndProposalID.t.Errorf("ProposalCacheMock.GetByProductIDAndProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductIDAndProposalID.GetByProductIDAndProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductIDAndProposalID.t.Fatal("No results are set for the ProposalCacheMock.GetByProductIDAndProposalID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByProductIDAndProposalID.funcGetByProductIDAndProposalID != nil {
		return mmGetByProductIDAndProposalID.funcGetByProductIDAndProposalID(productID, proposalID)
	}
	mmGetByProductIDAndProposalID.t.Fatalf("Unexpected call to ProposalCacheMock.GetByProductIDAndProposalID. %v %v", productID, proposalID)
	return
}

// GetByProductIDAndProposalIDAfterCounter returns a count of finished ProposalCacheMock.GetByProductIDAndProposalID invocations
func (mmGetByProductIDAndProposalID *ProposalCacheMock) GetByProductIDAndProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.afterGetByProductIDAndProposalIDCounter)
}

// GetByProductIDAndProposalIDBeforeCounter returns a count of ProposalCacheMock.GetByProductIDAndProposalID invocations
func (mmGetByProductIDAndProposalID *ProposalCacheMock) GetByProductIDAndProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndProposalID.beforeGetByProductIDAndProposalIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalCacheMock.GetByProductIDAndProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductIDAndProposalID *mProposalCacheMockGetByProductIDAndProposalID) Calls() []*ProposalCacheMockGetByProductIDAndProposalIDParams {
	mmGetByProductIDAndProposalID.mutex.RLock()

	argCopy := make([]*ProposalCacheMockGetByProductIDAndProposalIDParams, len(mmGetByProductIDAndProposalID.callArgs))
	copy(argCopy, mmGetByProductIDAndProposalID.callArgs)

	mmGetByProductIDAndProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDAndProposalIDDone returns true if the count of the GetByProductIDAndProposalID invocations corresponds
// the number of defined expectations
func (m *ProposalCacheMock) MinimockGetByProductIDAndProposalIDDone() bool {
	if m.GetByProductIDAndProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDAndProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDAndProposalIDMock.invocationsDone()
}

// MinimockGetByProductIDAndProposalIDInspect logs each unmet expectation
func (m *ProposalCacheMock) MinimockGetByProductIDAndProposalIDInspect() {
	for _, e := range m.GetByProductIDAndProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByProductIDAndProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDAndProposalIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDAndProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDAndProposalIDMock.defaultExpectation != nil && afterGetByProductIDAndProposalIDCounter < 1 {
		if m.GetByProductIDAndProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByProductIDAndProposalID at\n%s", m.GetByProductIDAndProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalCacheMock.GetByProductIDAndProposalID at\n%s with params: %#v", m.GetByProductIDAndProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDAndProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductIDAndProposalID != nil && afterGetByProductIDAndProposalIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalCacheMock.GetByProductIDAndProposalID at\n%s", m.funcGetByProductIDAndProposalIDOrigin)
	}

	if !m.GetByProductIDAndProposalIDMock.invocationsDone() && afterGetByProductIDAndProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalCacheMock.GetByProductIDAndProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDAndProposalIDMock.expectedInvocations), m.GetByProductIDAndProposalIDMock.expectedInvocationsOrigin, afterGetByProductIDAndProposalIDCounter)
	}
}

type mProposalCacheMockSet struct {
	optional           bool
	mock               *ProposalCacheMock
	defaultExpectation *ProposalCacheMockSetExpectation
	expectations       []*ProposalCacheMockSetExpectation

	callArgs []*ProposalCacheMockSetParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalCacheMockSetExpectation specifies expectation struct of the ProposalCache.Set
type ProposalCacheMockSetExpectation struct {
	mock               *ProposalCacheMock
	params             *ProposalCacheMockSetParams
	paramPtrs          *ProposalCacheMockSetParamPtrs
	expectationOrigins ProposalCacheMockSetExpectationOrigins
	results            *ProposalCacheMockSetResults
	returnOrigin       string
	Counter            uint64
}

// ProposalCacheMockSetParams contains parameters of the ProposalCache.Set
type ProposalCacheMockSetParams struct {
	proposal proposalentity.Proposal
}

// ProposalCacheMockSetParamPtrs contains pointers to parameters of the ProposalCache.Set
type ProposalCacheMockSetParamPtrs struct {
	proposal *proposalentity.Proposal
}

// ProposalCacheMockSetResults contains results of the ProposalCache.Set
type ProposalCacheMockSetResults struct {
	err error
}

// ProposalCacheMockSetOrigins contains origins of expectations of the ProposalCache.Set
type ProposalCacheMockSetExpectationOrigins struct {
	origin         string
	originProposal string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSet *mProposalCacheMockSet) Optional() *mProposalCacheMockSet {
	mmSet.optional = true
	return mmSet
}

// Expect sets up expected params for ProposalCache.Set
func (mmSet *mProposalCacheMockSet) Expect(proposal proposalentity.Proposal) *mProposalCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ProposalCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &ProposalCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.paramPtrs != nil {
		mmSet.mock.t.Fatalf("ProposalCacheMock.Set mock is already set by ExpectParams functions")
	}

	mmSet.defaultExpectation.params = &ProposalCacheMockSetParams{proposal}
	mmSet.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSet.expectations {
		if minimock.Equal(e.params, mmSet.defaultExpectation.params) {
			mmSet.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSet.defaultExpectation.params)
		}
	}

	return mmSet
}

// ExpectProposalParam1 sets up expected param proposal for ProposalCache.Set
func (mmSet *mProposalCacheMockSet) ExpectProposalParam1(proposal proposalentity.Proposal) *mProposalCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ProposalCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &ProposalCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.params != nil {
		mmSet.mock.t.Fatalf("ProposalCacheMock.Set mock is already set by Expect")
	}

	if mmSet.defaultExpectation.paramPtrs == nil {
		mmSet.defaultExpectation.paramPtrs = &ProposalCacheMockSetParamPtrs{}
	}
	mmSet.defaultExpectation.paramPtrs.proposal = &proposal
	mmSet.defaultExpectation.expectationOrigins.originProposal = minimock.CallerInfo(1)

	return mmSet
}

// Inspect accepts an inspector function that has same arguments as the ProposalCache.Set
func (mmSet *mProposalCacheMockSet) Inspect(f func(proposal proposalentity.Proposal)) *mProposalCacheMockSet {
	if mmSet.mock.inspectFuncSet != nil {
		mmSet.mock.t.Fatalf("Inspect function is already set for ProposalCacheMock.Set")
	}

	mmSet.mock.inspectFuncSet = f

	return mmSet
}

// Return sets up results that will be returned by ProposalCache.Set
func (mmSet *mProposalCacheMockSet) Return(err error) *ProposalCacheMock {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ProposalCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &ProposalCacheMockSetExpectation{mock: mmSet.mock}
	}
	mmSet.defaultExpectation.results = &ProposalCacheMockSetResults{err}
	mmSet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// Set uses given function f to mock the ProposalCache.Set method
func (mmSet *mProposalCacheMockSet) Set(f func(proposal proposalentity.Proposal) (err error)) *ProposalCacheMock {
	if mmSet.defaultExpectation != nil {
		mmSet.mock.t.Fatalf("Default expectation is already set for the ProposalCache.Set method")
	}

	if len(mmSet.expectations) > 0 {
		mmSet.mock.t.Fatalf("Some expectations are already set for the ProposalCache.Set method")
	}

	mmSet.mock.funcSet = f
	mmSet.mock.funcSetOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// When sets expectation for the ProposalCache.Set which will trigger the result defined by the following
// Then helper
func (mmSet *mProposalCacheMockSet) When(proposal proposalentity.Proposal) *ProposalCacheMockSetExpectation {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("ProposalCacheMock.Set mock is already set by Set")
	}

	expectation := &ProposalCacheMockSetExpectation{
		mock:               mmSet.mock,
		params:             &ProposalCacheMockSetParams{proposal},
		expectationOrigins: ProposalCacheMockSetExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSet.expectations = append(mmSet.expectations, expectation)
	return expectation
}

// Then sets up ProposalCache.Set return parameters for the expectation previously defined by the When method
func (e *ProposalCacheMockSetExpectation) Then(err error) *ProposalCacheMock {
	e.results = &ProposalCacheMockSetResults{err}
	return e.mock
}

// Times sets number of times ProposalCache.Set should be invoked
func (mmSet *mProposalCacheMockSet) Times(n uint64) *mProposalCacheMockSet {
	if n == 0 {
		mmSet.mock.t.Fatalf("Times of ProposalCacheMock.Set mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSet.expectedInvocations, n)
	mmSet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSet
}

func (mmSet *mProposalCacheMockSet) invocationsDone() bool {
	if len(mmSet.expectations) == 0 && mmSet.defaultExpectation == nil && mmSet.mock.funcSet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSet.mock.afterSetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Set implements mm_repository.ProposalCache
func (mmSet *ProposalCacheMock) Set(proposal proposalentity.Proposal) (err error) {
	mm_atomic.AddUint64(&mmSet.beforeSetCounter, 1)
	defer mm_atomic.AddUint64(&mmSet.afterSetCounter, 1)

	mmSet.t.Helper()

	if mmSet.inspectFuncSet != nil {
		mmSet.inspectFuncSet(proposal)
	}

	mm_params := ProposalCacheMockSetParams{proposal}

	// Record call args
	mmSet.SetMock.mutex.Lock()
	mmSet.SetMock.callArgs = append(mmSet.SetMock.callArgs, &mm_params)
	mmSet.SetMock.mutex.Unlock()

	for _, e := range mmSet.SetMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSet.SetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSet.SetMock.defaultExpectation.Counter, 1)
		mm_want := mmSet.SetMock.defaultExpectation.params
		mm_want_ptrs := mmSet.SetMock.defaultExpectation.paramPtrs

		mm_got := ProposalCacheMockSetParams{proposal}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposal != nil && !minimock.Equal(*mm_want_ptrs.proposal, mm_got.proposal) {
				mmSet.t.Errorf("ProposalCacheMock.Set got unexpected parameter proposal, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSet.SetMock.defaultExpectation.expectationOrigins.originProposal, *mm_want_ptrs.proposal, mm_got.proposal, minimock.Diff(*mm_want_ptrs.proposal, mm_got.proposal))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSet.t.Errorf("ProposalCacheMock.Set got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSet.SetMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSet.SetMock.defaultExpectation.results
		if mm_results == nil {
			mmSet.t.Fatal("No results are set for the ProposalCacheMock.Set")
		}
		return (*mm_results).err
	}
	if mmSet.funcSet != nil {
		return mmSet.funcSet(proposal)
	}
	mmSet.t.Fatalf("Unexpected call to ProposalCacheMock.Set. %v", proposal)
	return
}

// SetAfterCounter returns a count of finished ProposalCacheMock.Set invocations
func (mmSet *ProposalCacheMock) SetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.afterSetCounter)
}

// SetBeforeCounter returns a count of ProposalCacheMock.Set invocations
func (mmSet *ProposalCacheMock) SetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.beforeSetCounter)
}

// Calls returns a list of arguments used in each call to ProposalCacheMock.Set.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSet *mProposalCacheMockSet) Calls() []*ProposalCacheMockSetParams {
	mmSet.mutex.RLock()

	argCopy := make([]*ProposalCacheMockSetParams, len(mmSet.callArgs))
	copy(argCopy, mmSet.callArgs)

	mmSet.mutex.RUnlock()

	return argCopy
}

// MinimockSetDone returns true if the count of the Set invocations corresponds
// the number of defined expectations
func (m *ProposalCacheMock) MinimockSetDone() bool {
	if m.SetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetMock.invocationsDone()
}

// MinimockSetInspect logs each unmet expectation
func (m *ProposalCacheMock) MinimockSetInspect() {
	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalCacheMock.Set at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCounter := mm_atomic.LoadUint64(&m.afterSetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetMock.defaultExpectation != nil && afterSetCounter < 1 {
		if m.SetMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalCacheMock.Set at\n%s", m.SetMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalCacheMock.Set at\n%s with params: %#v", m.SetMock.defaultExpectation.expectationOrigins.origin, *m.SetMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSet != nil && afterSetCounter < 1 {
		m.t.Errorf("Expected call to ProposalCacheMock.Set at\n%s", m.funcSetOrigin)
	}

	if !m.SetMock.invocationsDone() && afterSetCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalCacheMock.Set at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetMock.expectedInvocations), m.SetMock.expectedInvocationsOrigin, afterSetCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProposalCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetByIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByProductIDAndProposalIDInspect()

			m.MinimockSetInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProposalCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProposalCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByProductIDAndProposalIDDone() &&
		m.MinimockSetDone()
}
