// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository.UserViewDB -o user_view_db_mock.go -n UserViewDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"github.com/gojuno/minimock/v3"
)

// UserViewDBMock implements mm_repository.UserViewDB
type UserViewDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetByProposalIDAndUserID          func(proposalID int64, userID int64) (u1 voproposal.UserView, err error)
	funcGetByProposalIDAndUserIDOrigin    string
	inspectFuncGetByProposalIDAndUserID   func(proposalID int64, userID int64)
	afterGetByProposalIDAndUserIDCounter  uint64
	beforeGetByProposalIDAndUserIDCounter uint64
	GetByProposalIDAndUserIDMock          mUserViewDBMockGetByProposalIDAndUserID

	funcUpdate          func(data voproposal.UserView) (u1 voproposal.UserView, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(data voproposal.UserView)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mUserViewDBMockUpdate
}

// NewUserViewDBMock returns a mock for mm_repository.UserViewDB
func NewUserViewDBMock(t minimock.Tester) *UserViewDBMock {
	m := &UserViewDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetByProposalIDAndUserIDMock = mUserViewDBMockGetByProposalIDAndUserID{mock: m}
	m.GetByProposalIDAndUserIDMock.callArgs = []*UserViewDBMockGetByProposalIDAndUserIDParams{}

	m.UpdateMock = mUserViewDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*UserViewDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mUserViewDBMockGetByProposalIDAndUserID struct {
	optional           bool
	mock               *UserViewDBMock
	defaultExpectation *UserViewDBMockGetByProposalIDAndUserIDExpectation
	expectations       []*UserViewDBMockGetByProposalIDAndUserIDExpectation

	callArgs []*UserViewDBMockGetByProposalIDAndUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserViewDBMockGetByProposalIDAndUserIDExpectation specifies expectation struct of the UserViewDB.GetByProposalIDAndUserID
type UserViewDBMockGetByProposalIDAndUserIDExpectation struct {
	mock               *UserViewDBMock
	params             *UserViewDBMockGetByProposalIDAndUserIDParams
	paramPtrs          *UserViewDBMockGetByProposalIDAndUserIDParamPtrs
	expectationOrigins UserViewDBMockGetByProposalIDAndUserIDExpectationOrigins
	results            *UserViewDBMockGetByProposalIDAndUserIDResults
	returnOrigin       string
	Counter            uint64
}

// UserViewDBMockGetByProposalIDAndUserIDParams contains parameters of the UserViewDB.GetByProposalIDAndUserID
type UserViewDBMockGetByProposalIDAndUserIDParams struct {
	proposalID int64
	userID     int64
}

// UserViewDBMockGetByProposalIDAndUserIDParamPtrs contains pointers to parameters of the UserViewDB.GetByProposalIDAndUserID
type UserViewDBMockGetByProposalIDAndUserIDParamPtrs struct {
	proposalID *int64
	userID     *int64
}

// UserViewDBMockGetByProposalIDAndUserIDResults contains results of the UserViewDB.GetByProposalIDAndUserID
type UserViewDBMockGetByProposalIDAndUserIDResults struct {
	u1  voproposal.UserView
	err error
}

// UserViewDBMockGetByProposalIDAndUserIDOrigins contains origins of expectations of the UserViewDB.GetByProposalIDAndUserID
type UserViewDBMockGetByProposalIDAndUserIDExpectationOrigins struct {
	origin           string
	originProposalID string
	originUserID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Optional() *mUserViewDBMockGetByProposalIDAndUserID {
	mmGetByProposalIDAndUserID.optional = true
	return mmGetByProposalIDAndUserID
}

// Expect sets up expected params for UserViewDB.GetByProposalIDAndUserID
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Expect(proposalID int64, userID int64) *mUserViewDBMockGetByProposalIDAndUserID {
	if mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Set")
	}

	if mmGetByProposalIDAndUserID.defaultExpectation == nil {
		mmGetByProposalIDAndUserID.defaultExpectation = &UserViewDBMockGetByProposalIDAndUserIDExpectation{}
	}

	if mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by ExpectParams functions")
	}

	mmGetByProposalIDAndUserID.defaultExpectation.params = &UserViewDBMockGetByProposalIDAndUserIDParams{proposalID, userID}
	mmGetByProposalIDAndUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProposalIDAndUserID.expectations {
		if minimock.Equal(e.params, mmGetByProposalIDAndUserID.defaultExpectation.params) {
			mmGetByProposalIDAndUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProposalIDAndUserID.defaultExpectation.params)
		}
	}

	return mmGetByProposalIDAndUserID
}

// ExpectProposalIDParam1 sets up expected param proposalID for UserViewDB.GetByProposalIDAndUserID
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) ExpectProposalIDParam1(proposalID int64) *mUserViewDBMockGetByProposalIDAndUserID {
	if mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Set")
	}

	if mmGetByProposalIDAndUserID.defaultExpectation == nil {
		mmGetByProposalIDAndUserID.defaultExpectation = &UserViewDBMockGetByProposalIDAndUserIDExpectation{}
	}

	if mmGetByProposalIDAndUserID.defaultExpectation.params != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Expect")
	}

	if mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs == nil {
		mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs = &UserViewDBMockGetByProposalIDAndUserIDParamPtrs{}
	}
	mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetByProposalIDAndUserID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetByProposalIDAndUserID
}

// ExpectUserIDParam2 sets up expected param userID for UserViewDB.GetByProposalIDAndUserID
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) ExpectUserIDParam2(userID int64) *mUserViewDBMockGetByProposalIDAndUserID {
	if mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Set")
	}

	if mmGetByProposalIDAndUserID.defaultExpectation == nil {
		mmGetByProposalIDAndUserID.defaultExpectation = &UserViewDBMockGetByProposalIDAndUserIDExpectation{}
	}

	if mmGetByProposalIDAndUserID.defaultExpectation.params != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Expect")
	}

	if mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs == nil {
		mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs = &UserViewDBMockGetByProposalIDAndUserIDParamPtrs{}
	}
	mmGetByProposalIDAndUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByProposalIDAndUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByProposalIDAndUserID
}

// Inspect accepts an inspector function that has same arguments as the UserViewDB.GetByProposalIDAndUserID
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Inspect(f func(proposalID int64, userID int64)) *mUserViewDBMockGetByProposalIDAndUserID {
	if mmGetByProposalIDAndUserID.mock.inspectFuncGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("Inspect function is already set for UserViewDBMock.GetByProposalIDAndUserID")
	}

	mmGetByProposalIDAndUserID.mock.inspectFuncGetByProposalIDAndUserID = f

	return mmGetByProposalIDAndUserID
}

// Return sets up results that will be returned by UserViewDB.GetByProposalIDAndUserID
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Return(u1 voproposal.UserView, err error) *UserViewDBMock {
	if mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Set")
	}

	if mmGetByProposalIDAndUserID.defaultExpectation == nil {
		mmGetByProposalIDAndUserID.defaultExpectation = &UserViewDBMockGetByProposalIDAndUserIDExpectation{mock: mmGetByProposalIDAndUserID.mock}
	}
	mmGetByProposalIDAndUserID.defaultExpectation.results = &UserViewDBMockGetByProposalIDAndUserIDResults{u1, err}
	mmGetByProposalIDAndUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProposalIDAndUserID.mock
}

// Set uses given function f to mock the UserViewDB.GetByProposalIDAndUserID method
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Set(f func(proposalID int64, userID int64) (u1 voproposal.UserView, err error)) *UserViewDBMock {
	if mmGetByProposalIDAndUserID.defaultExpectation != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("Default expectation is already set for the UserViewDB.GetByProposalIDAndUserID method")
	}

	if len(mmGetByProposalIDAndUserID.expectations) > 0 {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("Some expectations are already set for the UserViewDB.GetByProposalIDAndUserID method")
	}

	mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID = f
	mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByProposalIDAndUserID.mock
}

// When sets expectation for the UserViewDB.GetByProposalIDAndUserID which will trigger the result defined by the following
// Then helper
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) When(proposalID int64, userID int64) *UserViewDBMockGetByProposalIDAndUserIDExpectation {
	if mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("UserViewDBMock.GetByProposalIDAndUserID mock is already set by Set")
	}

	expectation := &UserViewDBMockGetByProposalIDAndUserIDExpectation{
		mock:               mmGetByProposalIDAndUserID.mock,
		params:             &UserViewDBMockGetByProposalIDAndUserIDParams{proposalID, userID},
		expectationOrigins: UserViewDBMockGetByProposalIDAndUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProposalIDAndUserID.expectations = append(mmGetByProposalIDAndUserID.expectations, expectation)
	return expectation
}

// Then sets up UserViewDB.GetByProposalIDAndUserID return parameters for the expectation previously defined by the When method
func (e *UserViewDBMockGetByProposalIDAndUserIDExpectation) Then(u1 voproposal.UserView, err error) *UserViewDBMock {
	e.results = &UserViewDBMockGetByProposalIDAndUserIDResults{u1, err}
	return e.mock
}

// Times sets number of times UserViewDB.GetByProposalIDAndUserID should be invoked
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Times(n uint64) *mUserViewDBMockGetByProposalIDAndUserID {
	if n == 0 {
		mmGetByProposalIDAndUserID.mock.t.Fatalf("Times of UserViewDBMock.GetByProposalIDAndUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProposalIDAndUserID.expectedInvocations, n)
	mmGetByProposalIDAndUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProposalIDAndUserID
}

func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) invocationsDone() bool {
	if len(mmGetByProposalIDAndUserID.expectations) == 0 && mmGetByProposalIDAndUserID.defaultExpectation == nil && mmGetByProposalIDAndUserID.mock.funcGetByProposalIDAndUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProposalIDAndUserID.mock.afterGetByProposalIDAndUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProposalIDAndUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProposalIDAndUserID implements mm_repository.UserViewDB
func (mmGetByProposalIDAndUserID *UserViewDBMock) GetByProposalIDAndUserID(proposalID int64, userID int64) (u1 voproposal.UserView, err error) {
	mm_atomic.AddUint64(&mmGetByProposalIDAndUserID.beforeGetByProposalIDAndUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProposalIDAndUserID.afterGetByProposalIDAndUserIDCounter, 1)

	mmGetByProposalIDAndUserID.t.Helper()

	if mmGetByProposalIDAndUserID.inspectFuncGetByProposalIDAndUserID != nil {
		mmGetByProposalIDAndUserID.inspectFuncGetByProposalIDAndUserID(proposalID, userID)
	}

	mm_params := UserViewDBMockGetByProposalIDAndUserIDParams{proposalID, userID}

	// Record call args
	mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.mutex.Lock()
	mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.callArgs = append(mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.callArgs, &mm_params)
	mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.mutex.Unlock()

	for _, e := range mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.paramPtrs

		mm_got := UserViewDBMockGetByProposalIDAndUserIDParams{proposalID, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetByProposalIDAndUserID.t.Errorf("UserViewDBMock.GetByProposalIDAndUserID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByProposalIDAndUserID.t.Errorf("UserViewDBMock.GetByProposalIDAndUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProposalIDAndUserID.t.Errorf("UserViewDBMock.GetByProposalIDAndUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProposalIDAndUserID.GetByProposalIDAndUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProposalIDAndUserID.t.Fatal("No results are set for the UserViewDBMock.GetByProposalIDAndUserID")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmGetByProposalIDAndUserID.funcGetByProposalIDAndUserID != nil {
		return mmGetByProposalIDAndUserID.funcGetByProposalIDAndUserID(proposalID, userID)
	}
	mmGetByProposalIDAndUserID.t.Fatalf("Unexpected call to UserViewDBMock.GetByProposalIDAndUserID. %v %v", proposalID, userID)
	return
}

// GetByProposalIDAndUserIDAfterCounter returns a count of finished UserViewDBMock.GetByProposalIDAndUserID invocations
func (mmGetByProposalIDAndUserID *UserViewDBMock) GetByProposalIDAndUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProposalIDAndUserID.afterGetByProposalIDAndUserIDCounter)
}

// GetByProposalIDAndUserIDBeforeCounter returns a count of UserViewDBMock.GetByProposalIDAndUserID invocations
func (mmGetByProposalIDAndUserID *UserViewDBMock) GetByProposalIDAndUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProposalIDAndUserID.beforeGetByProposalIDAndUserIDCounter)
}

// Calls returns a list of arguments used in each call to UserViewDBMock.GetByProposalIDAndUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProposalIDAndUserID *mUserViewDBMockGetByProposalIDAndUserID) Calls() []*UserViewDBMockGetByProposalIDAndUserIDParams {
	mmGetByProposalIDAndUserID.mutex.RLock()

	argCopy := make([]*UserViewDBMockGetByProposalIDAndUserIDParams, len(mmGetByProposalIDAndUserID.callArgs))
	copy(argCopy, mmGetByProposalIDAndUserID.callArgs)

	mmGetByProposalIDAndUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProposalIDAndUserIDDone returns true if the count of the GetByProposalIDAndUserID invocations corresponds
// the number of defined expectations
func (m *UserViewDBMock) MinimockGetByProposalIDAndUserIDDone() bool {
	if m.GetByProposalIDAndUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProposalIDAndUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProposalIDAndUserIDMock.invocationsDone()
}

// MinimockGetByProposalIDAndUserIDInspect logs each unmet expectation
func (m *UserViewDBMock) MinimockGetByProposalIDAndUserIDInspect() {
	for _, e := range m.GetByProposalIDAndUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserViewDBMock.GetByProposalIDAndUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProposalIDAndUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByProposalIDAndUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProposalIDAndUserIDMock.defaultExpectation != nil && afterGetByProposalIDAndUserIDCounter < 1 {
		if m.GetByProposalIDAndUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserViewDBMock.GetByProposalIDAndUserID at\n%s", m.GetByProposalIDAndUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserViewDBMock.GetByProposalIDAndUserID at\n%s with params: %#v", m.GetByProposalIDAndUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProposalIDAndUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProposalIDAndUserID != nil && afterGetByProposalIDAndUserIDCounter < 1 {
		m.t.Errorf("Expected call to UserViewDBMock.GetByProposalIDAndUserID at\n%s", m.funcGetByProposalIDAndUserIDOrigin)
	}

	if !m.GetByProposalIDAndUserIDMock.invocationsDone() && afterGetByProposalIDAndUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to UserViewDBMock.GetByProposalIDAndUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProposalIDAndUserIDMock.expectedInvocations), m.GetByProposalIDAndUserIDMock.expectedInvocationsOrigin, afterGetByProposalIDAndUserIDCounter)
	}
}

type mUserViewDBMockUpdate struct {
	optional           bool
	mock               *UserViewDBMock
	defaultExpectation *UserViewDBMockUpdateExpectation
	expectations       []*UserViewDBMockUpdateExpectation

	callArgs []*UserViewDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// UserViewDBMockUpdateExpectation specifies expectation struct of the UserViewDB.Update
type UserViewDBMockUpdateExpectation struct {
	mock               *UserViewDBMock
	params             *UserViewDBMockUpdateParams
	paramPtrs          *UserViewDBMockUpdateParamPtrs
	expectationOrigins UserViewDBMockUpdateExpectationOrigins
	results            *UserViewDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// UserViewDBMockUpdateParams contains parameters of the UserViewDB.Update
type UserViewDBMockUpdateParams struct {
	data voproposal.UserView
}

// UserViewDBMockUpdateParamPtrs contains pointers to parameters of the UserViewDB.Update
type UserViewDBMockUpdateParamPtrs struct {
	data *voproposal.UserView
}

// UserViewDBMockUpdateResults contains results of the UserViewDB.Update
type UserViewDBMockUpdateResults struct {
	u1  voproposal.UserView
	err error
}

// UserViewDBMockUpdateOrigins contains origins of expectations of the UserViewDB.Update
type UserViewDBMockUpdateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mUserViewDBMockUpdate) Optional() *mUserViewDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for UserViewDB.Update
func (mmUpdate *mUserViewDBMockUpdate) Expect(data voproposal.UserView) *mUserViewDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserViewDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserViewDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("UserViewDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &UserViewDBMockUpdateParams{data}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectDataParam1 sets up expected param data for UserViewDB.Update
func (mmUpdate *mUserViewDBMockUpdate) ExpectDataParam1(data voproposal.UserView) *mUserViewDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserViewDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserViewDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("UserViewDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &UserViewDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.data = &data
	mmUpdate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the UserViewDB.Update
func (mmUpdate *mUserViewDBMockUpdate) Inspect(f func(data voproposal.UserView)) *mUserViewDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for UserViewDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by UserViewDB.Update
func (mmUpdate *mUserViewDBMockUpdate) Return(u1 voproposal.UserView, err error) *UserViewDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserViewDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &UserViewDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &UserViewDBMockUpdateResults{u1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the UserViewDB.Update method
func (mmUpdate *mUserViewDBMockUpdate) Set(f func(data voproposal.UserView) (u1 voproposal.UserView, err error)) *UserViewDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the UserViewDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the UserViewDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the UserViewDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mUserViewDBMockUpdate) When(data voproposal.UserView) *UserViewDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("UserViewDBMock.Update mock is already set by Set")
	}

	expectation := &UserViewDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &UserViewDBMockUpdateParams{data},
		expectationOrigins: UserViewDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up UserViewDB.Update return parameters for the expectation previously defined by the When method
func (e *UserViewDBMockUpdateExpectation) Then(u1 voproposal.UserView, err error) *UserViewDBMock {
	e.results = &UserViewDBMockUpdateResults{u1, err}
	return e.mock
}

// Times sets number of times UserViewDB.Update should be invoked
func (mmUpdate *mUserViewDBMockUpdate) Times(n uint64) *mUserViewDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of UserViewDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mUserViewDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.UserViewDB
func (mmUpdate *UserViewDBMock) Update(data voproposal.UserView) (u1 voproposal.UserView, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(data)
	}

	mm_params := UserViewDBMockUpdateParams{data}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.u1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := UserViewDBMockUpdateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdate.t.Errorf("UserViewDBMock.Update got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("UserViewDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the UserViewDBMock.Update")
		}
		return (*mm_results).u1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(data)
	}
	mmUpdate.t.Fatalf("Unexpected call to UserViewDBMock.Update. %v", data)
	return
}

// UpdateAfterCounter returns a count of finished UserViewDBMock.Update invocations
func (mmUpdate *UserViewDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of UserViewDBMock.Update invocations
func (mmUpdate *UserViewDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to UserViewDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mUserViewDBMockUpdate) Calls() []*UserViewDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*UserViewDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *UserViewDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *UserViewDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to UserViewDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to UserViewDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to UserViewDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to UserViewDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to UserViewDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *UserViewDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetByProposalIDAndUserIDInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *UserViewDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *UserViewDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetByProposalIDAndUserIDDone() &&
		m.MinimockUpdateDone()
}
