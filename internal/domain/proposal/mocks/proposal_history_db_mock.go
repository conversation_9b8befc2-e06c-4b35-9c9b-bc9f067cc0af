// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository.ProposalHistoryDB -o proposal_history_db_mock.go -n ProposalHistoryDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"github.com/gojuno/minimock/v3"
)

// ProposalHistoryDBMock implements mm_repository.ProposalHistoryDB
type ProposalHistoryDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreateMessage          func(proposalID int64, message string, userID int64) (err error)
	funcCreateMessageOrigin    string
	inspectFuncCreateMessage   func(proposalID int64, message string, userID int64)
	afterCreateMessageCounter  uint64
	beforeCreateMessageCounter uint64
	CreateMessageMock          mProposalHistoryDBMockCreateMessage

	funcGetByProposalID          func(proposalID int64) (ha1 []voproposal.HistoryRecord, err error)
	funcGetByProposalIDOrigin    string
	inspectFuncGetByProposalID   func(proposalID int64)
	afterGetByProposalIDCounter  uint64
	beforeGetByProposalIDCounter uint64
	GetByProposalIDMock          mProposalHistoryDBMockGetByProposalID

	funcGetByUserID          func(userID int64) (ha1 []voproposal.HistoryRecord, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mProposalHistoryDBMockGetByUserID

	funcGetUnreadEventsForUser          func(userID int64) (ha1 []voproposal.HistoryRecord, err error)
	funcGetUnreadEventsForUserOrigin    string
	inspectFuncGetUnreadEventsForUser   func(userID int64)
	afterGetUnreadEventsForUserCounter  uint64
	beforeGetUnreadEventsForUserCounter uint64
	GetUnreadEventsForUserMock          mProposalHistoryDBMockGetUnreadEventsForUser
}

// NewProposalHistoryDBMock returns a mock for mm_repository.ProposalHistoryDB
func NewProposalHistoryDBMock(t minimock.Tester) *ProposalHistoryDBMock {
	m := &ProposalHistoryDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMessageMock = mProposalHistoryDBMockCreateMessage{mock: m}
	m.CreateMessageMock.callArgs = []*ProposalHistoryDBMockCreateMessageParams{}

	m.GetByProposalIDMock = mProposalHistoryDBMockGetByProposalID{mock: m}
	m.GetByProposalIDMock.callArgs = []*ProposalHistoryDBMockGetByProposalIDParams{}

	m.GetByUserIDMock = mProposalHistoryDBMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*ProposalHistoryDBMockGetByUserIDParams{}

	m.GetUnreadEventsForUserMock = mProposalHistoryDBMockGetUnreadEventsForUser{mock: m}
	m.GetUnreadEventsForUserMock.callArgs = []*ProposalHistoryDBMockGetUnreadEventsForUserParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mProposalHistoryDBMockCreateMessage struct {
	optional           bool
	mock               *ProposalHistoryDBMock
	defaultExpectation *ProposalHistoryDBMockCreateMessageExpectation
	expectations       []*ProposalHistoryDBMockCreateMessageExpectation

	callArgs []*ProposalHistoryDBMockCreateMessageParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalHistoryDBMockCreateMessageExpectation specifies expectation struct of the ProposalHistoryDB.CreateMessage
type ProposalHistoryDBMockCreateMessageExpectation struct {
	mock               *ProposalHistoryDBMock
	params             *ProposalHistoryDBMockCreateMessageParams
	paramPtrs          *ProposalHistoryDBMockCreateMessageParamPtrs
	expectationOrigins ProposalHistoryDBMockCreateMessageExpectationOrigins
	results            *ProposalHistoryDBMockCreateMessageResults
	returnOrigin       string
	Counter            uint64
}

// ProposalHistoryDBMockCreateMessageParams contains parameters of the ProposalHistoryDB.CreateMessage
type ProposalHistoryDBMockCreateMessageParams struct {
	proposalID int64
	message    string
	userID     int64
}

// ProposalHistoryDBMockCreateMessageParamPtrs contains pointers to parameters of the ProposalHistoryDB.CreateMessage
type ProposalHistoryDBMockCreateMessageParamPtrs struct {
	proposalID *int64
	message    *string
	userID     *int64
}

// ProposalHistoryDBMockCreateMessageResults contains results of the ProposalHistoryDB.CreateMessage
type ProposalHistoryDBMockCreateMessageResults struct {
	err error
}

// ProposalHistoryDBMockCreateMessageOrigins contains origins of expectations of the ProposalHistoryDB.CreateMessage
type ProposalHistoryDBMockCreateMessageExpectationOrigins struct {
	origin           string
	originProposalID string
	originMessage    string
	originUserID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Optional() *mProposalHistoryDBMockCreateMessage {
	mmCreateMessage.optional = true
	return mmCreateMessage
}

// Expect sets up expected params for ProposalHistoryDB.CreateMessage
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Expect(proposalID int64, message string, userID int64) *mProposalHistoryDBMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalHistoryDBMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.paramPtrs != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by ExpectParams functions")
	}

	mmCreateMessage.defaultExpectation.params = &ProposalHistoryDBMockCreateMessageParams{proposalID, message, userID}
	mmCreateMessage.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateMessage.expectations {
		if minimock.Equal(e.params, mmCreateMessage.defaultExpectation.params) {
			mmCreateMessage.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateMessage.defaultExpectation.params)
		}
	}

	return mmCreateMessage
}

// ExpectProposalIDParam1 sets up expected param proposalID for ProposalHistoryDB.CreateMessage
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) ExpectProposalIDParam1(proposalID int64) *mProposalHistoryDBMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalHistoryDBMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.params != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Expect")
	}

	if mmCreateMessage.defaultExpectation.paramPtrs == nil {
		mmCreateMessage.defaultExpectation.paramPtrs = &ProposalHistoryDBMockCreateMessageParamPtrs{}
	}
	mmCreateMessage.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmCreateMessage.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmCreateMessage
}

// ExpectMessageParam2 sets up expected param message for ProposalHistoryDB.CreateMessage
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) ExpectMessageParam2(message string) *mProposalHistoryDBMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalHistoryDBMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.params != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Expect")
	}

	if mmCreateMessage.defaultExpectation.paramPtrs == nil {
		mmCreateMessage.defaultExpectation.paramPtrs = &ProposalHistoryDBMockCreateMessageParamPtrs{}
	}
	mmCreateMessage.defaultExpectation.paramPtrs.message = &message
	mmCreateMessage.defaultExpectation.expectationOrigins.originMessage = minimock.CallerInfo(1)

	return mmCreateMessage
}

// ExpectUserIDParam3 sets up expected param userID for ProposalHistoryDB.CreateMessage
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) ExpectUserIDParam3(userID int64) *mProposalHistoryDBMockCreateMessage {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalHistoryDBMockCreateMessageExpectation{}
	}

	if mmCreateMessage.defaultExpectation.params != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Expect")
	}

	if mmCreateMessage.defaultExpectation.paramPtrs == nil {
		mmCreateMessage.defaultExpectation.paramPtrs = &ProposalHistoryDBMockCreateMessageParamPtrs{}
	}
	mmCreateMessage.defaultExpectation.paramPtrs.userID = &userID
	mmCreateMessage.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmCreateMessage
}

// Inspect accepts an inspector function that has same arguments as the ProposalHistoryDB.CreateMessage
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Inspect(f func(proposalID int64, message string, userID int64)) *mProposalHistoryDBMockCreateMessage {
	if mmCreateMessage.mock.inspectFuncCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("Inspect function is already set for ProposalHistoryDBMock.CreateMessage")
	}

	mmCreateMessage.mock.inspectFuncCreateMessage = f

	return mmCreateMessage
}

// Return sets up results that will be returned by ProposalHistoryDB.CreateMessage
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Return(err error) *ProposalHistoryDBMock {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Set")
	}

	if mmCreateMessage.defaultExpectation == nil {
		mmCreateMessage.defaultExpectation = &ProposalHistoryDBMockCreateMessageExpectation{mock: mmCreateMessage.mock}
	}
	mmCreateMessage.defaultExpectation.results = &ProposalHistoryDBMockCreateMessageResults{err}
	mmCreateMessage.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateMessage.mock
}

// Set uses given function f to mock the ProposalHistoryDB.CreateMessage method
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Set(f func(proposalID int64, message string, userID int64) (err error)) *ProposalHistoryDBMock {
	if mmCreateMessage.defaultExpectation != nil {
		mmCreateMessage.mock.t.Fatalf("Default expectation is already set for the ProposalHistoryDB.CreateMessage method")
	}

	if len(mmCreateMessage.expectations) > 0 {
		mmCreateMessage.mock.t.Fatalf("Some expectations are already set for the ProposalHistoryDB.CreateMessage method")
	}

	mmCreateMessage.mock.funcCreateMessage = f
	mmCreateMessage.mock.funcCreateMessageOrigin = minimock.CallerInfo(1)
	return mmCreateMessage.mock
}

// When sets expectation for the ProposalHistoryDB.CreateMessage which will trigger the result defined by the following
// Then helper
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) When(proposalID int64, message string, userID int64) *ProposalHistoryDBMockCreateMessageExpectation {
	if mmCreateMessage.mock.funcCreateMessage != nil {
		mmCreateMessage.mock.t.Fatalf("ProposalHistoryDBMock.CreateMessage mock is already set by Set")
	}

	expectation := &ProposalHistoryDBMockCreateMessageExpectation{
		mock:               mmCreateMessage.mock,
		params:             &ProposalHistoryDBMockCreateMessageParams{proposalID, message, userID},
		expectationOrigins: ProposalHistoryDBMockCreateMessageExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateMessage.expectations = append(mmCreateMessage.expectations, expectation)
	return expectation
}

// Then sets up ProposalHistoryDB.CreateMessage return parameters for the expectation previously defined by the When method
func (e *ProposalHistoryDBMockCreateMessageExpectation) Then(err error) *ProposalHistoryDBMock {
	e.results = &ProposalHistoryDBMockCreateMessageResults{err}
	return e.mock
}

// Times sets number of times ProposalHistoryDB.CreateMessage should be invoked
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Times(n uint64) *mProposalHistoryDBMockCreateMessage {
	if n == 0 {
		mmCreateMessage.mock.t.Fatalf("Times of ProposalHistoryDBMock.CreateMessage mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateMessage.expectedInvocations, n)
	mmCreateMessage.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateMessage
}

func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) invocationsDone() bool {
	if len(mmCreateMessage.expectations) == 0 && mmCreateMessage.defaultExpectation == nil && mmCreateMessage.mock.funcCreateMessage == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateMessage.mock.afterCreateMessageCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateMessage.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateMessage implements mm_repository.ProposalHistoryDB
func (mmCreateMessage *ProposalHistoryDBMock) CreateMessage(proposalID int64, message string, userID int64) (err error) {
	mm_atomic.AddUint64(&mmCreateMessage.beforeCreateMessageCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateMessage.afterCreateMessageCounter, 1)

	mmCreateMessage.t.Helper()

	if mmCreateMessage.inspectFuncCreateMessage != nil {
		mmCreateMessage.inspectFuncCreateMessage(proposalID, message, userID)
	}

	mm_params := ProposalHistoryDBMockCreateMessageParams{proposalID, message, userID}

	// Record call args
	mmCreateMessage.CreateMessageMock.mutex.Lock()
	mmCreateMessage.CreateMessageMock.callArgs = append(mmCreateMessage.CreateMessageMock.callArgs, &mm_params)
	mmCreateMessage.CreateMessageMock.mutex.Unlock()

	for _, e := range mmCreateMessage.CreateMessageMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateMessage.CreateMessageMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateMessage.CreateMessageMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateMessage.CreateMessageMock.defaultExpectation.params
		mm_want_ptrs := mmCreateMessage.CreateMessageMock.defaultExpectation.paramPtrs

		mm_got := ProposalHistoryDBMockCreateMessageParams{proposalID, message, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmCreateMessage.t.Errorf("ProposalHistoryDBMock.CreateMessage got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

			if mm_want_ptrs.message != nil && !minimock.Equal(*mm_want_ptrs.message, mm_got.message) {
				mmCreateMessage.t.Errorf("ProposalHistoryDBMock.CreateMessage got unexpected parameter message, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.originMessage, *mm_want_ptrs.message, mm_got.message, minimock.Diff(*mm_want_ptrs.message, mm_got.message))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmCreateMessage.t.Errorf("ProposalHistoryDBMock.CreateMessage got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateMessage.t.Errorf("ProposalHistoryDBMock.CreateMessage got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateMessage.CreateMessageMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateMessage.CreateMessageMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateMessage.t.Fatal("No results are set for the ProposalHistoryDBMock.CreateMessage")
		}
		return (*mm_results).err
	}
	if mmCreateMessage.funcCreateMessage != nil {
		return mmCreateMessage.funcCreateMessage(proposalID, message, userID)
	}
	mmCreateMessage.t.Fatalf("Unexpected call to ProposalHistoryDBMock.CreateMessage. %v %v %v", proposalID, message, userID)
	return
}

// CreateMessageAfterCounter returns a count of finished ProposalHistoryDBMock.CreateMessage invocations
func (mmCreateMessage *ProposalHistoryDBMock) CreateMessageAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateMessage.afterCreateMessageCounter)
}

// CreateMessageBeforeCounter returns a count of ProposalHistoryDBMock.CreateMessage invocations
func (mmCreateMessage *ProposalHistoryDBMock) CreateMessageBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateMessage.beforeCreateMessageCounter)
}

// Calls returns a list of arguments used in each call to ProposalHistoryDBMock.CreateMessage.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateMessage *mProposalHistoryDBMockCreateMessage) Calls() []*ProposalHistoryDBMockCreateMessageParams {
	mmCreateMessage.mutex.RLock()

	argCopy := make([]*ProposalHistoryDBMockCreateMessageParams, len(mmCreateMessage.callArgs))
	copy(argCopy, mmCreateMessage.callArgs)

	mmCreateMessage.mutex.RUnlock()

	return argCopy
}

// MinimockCreateMessageDone returns true if the count of the CreateMessage invocations corresponds
// the number of defined expectations
func (m *ProposalHistoryDBMock) MinimockCreateMessageDone() bool {
	if m.CreateMessageMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMessageMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMessageMock.invocationsDone()
}

// MinimockCreateMessageInspect logs each unmet expectation
func (m *ProposalHistoryDBMock) MinimockCreateMessageInspect() {
	for _, e := range m.CreateMessageMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.CreateMessage at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateMessageCounter := mm_atomic.LoadUint64(&m.afterCreateMessageCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMessageMock.defaultExpectation != nil && afterCreateMessageCounter < 1 {
		if m.CreateMessageMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.CreateMessage at\n%s", m.CreateMessageMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.CreateMessage at\n%s with params: %#v", m.CreateMessageMock.defaultExpectation.expectationOrigins.origin, *m.CreateMessageMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateMessage != nil && afterCreateMessageCounter < 1 {
		m.t.Errorf("Expected call to ProposalHistoryDBMock.CreateMessage at\n%s", m.funcCreateMessageOrigin)
	}

	if !m.CreateMessageMock.invocationsDone() && afterCreateMessageCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalHistoryDBMock.CreateMessage at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMessageMock.expectedInvocations), m.CreateMessageMock.expectedInvocationsOrigin, afterCreateMessageCounter)
	}
}

type mProposalHistoryDBMockGetByProposalID struct {
	optional           bool
	mock               *ProposalHistoryDBMock
	defaultExpectation *ProposalHistoryDBMockGetByProposalIDExpectation
	expectations       []*ProposalHistoryDBMockGetByProposalIDExpectation

	callArgs []*ProposalHistoryDBMockGetByProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalHistoryDBMockGetByProposalIDExpectation specifies expectation struct of the ProposalHistoryDB.GetByProposalID
type ProposalHistoryDBMockGetByProposalIDExpectation struct {
	mock               *ProposalHistoryDBMock
	params             *ProposalHistoryDBMockGetByProposalIDParams
	paramPtrs          *ProposalHistoryDBMockGetByProposalIDParamPtrs
	expectationOrigins ProposalHistoryDBMockGetByProposalIDExpectationOrigins
	results            *ProposalHistoryDBMockGetByProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalHistoryDBMockGetByProposalIDParams contains parameters of the ProposalHistoryDB.GetByProposalID
type ProposalHistoryDBMockGetByProposalIDParams struct {
	proposalID int64
}

// ProposalHistoryDBMockGetByProposalIDParamPtrs contains pointers to parameters of the ProposalHistoryDB.GetByProposalID
type ProposalHistoryDBMockGetByProposalIDParamPtrs struct {
	proposalID *int64
}

// ProposalHistoryDBMockGetByProposalIDResults contains results of the ProposalHistoryDB.GetByProposalID
type ProposalHistoryDBMockGetByProposalIDResults struct {
	ha1 []voproposal.HistoryRecord
	err error
}

// ProposalHistoryDBMockGetByProposalIDOrigins contains origins of expectations of the ProposalHistoryDB.GetByProposalID
type ProposalHistoryDBMockGetByProposalIDExpectationOrigins struct {
	origin           string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Optional() *mProposalHistoryDBMockGetByProposalID {
	mmGetByProposalID.optional = true
	return mmGetByProposalID
}

// Expect sets up expected params for ProposalHistoryDB.GetByProposalID
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Expect(proposalID int64) *mProposalHistoryDBMockGetByProposalID {
	if mmGetByProposalID.mock.funcGetByProposalID != nil {
		mmGetByProposalID.mock.t.Fatalf("ProposalHistoryDBMock.GetByProposalID mock is already set by Set")
	}

	if mmGetByProposalID.defaultExpectation == nil {
		mmGetByProposalID.defaultExpectation = &ProposalHistoryDBMockGetByProposalIDExpectation{}
	}

	if mmGetByProposalID.defaultExpectation.paramPtrs != nil {
		mmGetByProposalID.mock.t.Fatalf("ProposalHistoryDBMock.GetByProposalID mock is already set by ExpectParams functions")
	}

	mmGetByProposalID.defaultExpectation.params = &ProposalHistoryDBMockGetByProposalIDParams{proposalID}
	mmGetByProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProposalID.expectations {
		if minimock.Equal(e.params, mmGetByProposalID.defaultExpectation.params) {
			mmGetByProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProposalID.defaultExpectation.params)
		}
	}

	return mmGetByProposalID
}

// ExpectProposalIDParam1 sets up expected param proposalID for ProposalHistoryDB.GetByProposalID
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) ExpectProposalIDParam1(proposalID int64) *mProposalHistoryDBMockGetByProposalID {
	if mmGetByProposalID.mock.funcGetByProposalID != nil {
		mmGetByProposalID.mock.t.Fatalf("ProposalHistoryDBMock.GetByProposalID mock is already set by Set")
	}

	if mmGetByProposalID.defaultExpectation == nil {
		mmGetByProposalID.defaultExpectation = &ProposalHistoryDBMockGetByProposalIDExpectation{}
	}

	if mmGetByProposalID.defaultExpectation.params != nil {
		mmGetByProposalID.mock.t.Fatalf("ProposalHistoryDBMock.GetByProposalID mock is already set by Expect")
	}

	if mmGetByProposalID.defaultExpectation.paramPtrs == nil {
		mmGetByProposalID.defaultExpectation.paramPtrs = &ProposalHistoryDBMockGetByProposalIDParamPtrs{}
	}
	mmGetByProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetByProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetByProposalID
}

// Inspect accepts an inspector function that has same arguments as the ProposalHistoryDB.GetByProposalID
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Inspect(f func(proposalID int64)) *mProposalHistoryDBMockGetByProposalID {
	if mmGetByProposalID.mock.inspectFuncGetByProposalID != nil {
		mmGetByProposalID.mock.t.Fatalf("Inspect function is already set for ProposalHistoryDBMock.GetByProposalID")
	}

	mmGetByProposalID.mock.inspectFuncGetByProposalID = f

	return mmGetByProposalID
}

// Return sets up results that will be returned by ProposalHistoryDB.GetByProposalID
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Return(ha1 []voproposal.HistoryRecord, err error) *ProposalHistoryDBMock {
	if mmGetByProposalID.mock.funcGetByProposalID != nil {
		mmGetByProposalID.mock.t.Fatalf("ProposalHistoryDBMock.GetByProposalID mock is already set by Set")
	}

	if mmGetByProposalID.defaultExpectation == nil {
		mmGetByProposalID.defaultExpectation = &ProposalHistoryDBMockGetByProposalIDExpectation{mock: mmGetByProposalID.mock}
	}
	mmGetByProposalID.defaultExpectation.results = &ProposalHistoryDBMockGetByProposalIDResults{ha1, err}
	mmGetByProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProposalID.mock
}

// Set uses given function f to mock the ProposalHistoryDB.GetByProposalID method
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Set(f func(proposalID int64) (ha1 []voproposal.HistoryRecord, err error)) *ProposalHistoryDBMock {
	if mmGetByProposalID.defaultExpectation != nil {
		mmGetByProposalID.mock.t.Fatalf("Default expectation is already set for the ProposalHistoryDB.GetByProposalID method")
	}

	if len(mmGetByProposalID.expectations) > 0 {
		mmGetByProposalID.mock.t.Fatalf("Some expectations are already set for the ProposalHistoryDB.GetByProposalID method")
	}

	mmGetByProposalID.mock.funcGetByProposalID = f
	mmGetByProposalID.mock.funcGetByProposalIDOrigin = minimock.CallerInfo(1)
	return mmGetByProposalID.mock
}

// When sets expectation for the ProposalHistoryDB.GetByProposalID which will trigger the result defined by the following
// Then helper
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) When(proposalID int64) *ProposalHistoryDBMockGetByProposalIDExpectation {
	if mmGetByProposalID.mock.funcGetByProposalID != nil {
		mmGetByProposalID.mock.t.Fatalf("ProposalHistoryDBMock.GetByProposalID mock is already set by Set")
	}

	expectation := &ProposalHistoryDBMockGetByProposalIDExpectation{
		mock:               mmGetByProposalID.mock,
		params:             &ProposalHistoryDBMockGetByProposalIDParams{proposalID},
		expectationOrigins: ProposalHistoryDBMockGetByProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProposalID.expectations = append(mmGetByProposalID.expectations, expectation)
	return expectation
}

// Then sets up ProposalHistoryDB.GetByProposalID return parameters for the expectation previously defined by the When method
func (e *ProposalHistoryDBMockGetByProposalIDExpectation) Then(ha1 []voproposal.HistoryRecord, err error) *ProposalHistoryDBMock {
	e.results = &ProposalHistoryDBMockGetByProposalIDResults{ha1, err}
	return e.mock
}

// Times sets number of times ProposalHistoryDB.GetByProposalID should be invoked
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Times(n uint64) *mProposalHistoryDBMockGetByProposalID {
	if n == 0 {
		mmGetByProposalID.mock.t.Fatalf("Times of ProposalHistoryDBMock.GetByProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProposalID.expectedInvocations, n)
	mmGetByProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProposalID
}

func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) invocationsDone() bool {
	if len(mmGetByProposalID.expectations) == 0 && mmGetByProposalID.defaultExpectation == nil && mmGetByProposalID.mock.funcGetByProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProposalID.mock.afterGetByProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProposalID implements mm_repository.ProposalHistoryDB
func (mmGetByProposalID *ProposalHistoryDBMock) GetByProposalID(proposalID int64) (ha1 []voproposal.HistoryRecord, err error) {
	mm_atomic.AddUint64(&mmGetByProposalID.beforeGetByProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProposalID.afterGetByProposalIDCounter, 1)

	mmGetByProposalID.t.Helper()

	if mmGetByProposalID.inspectFuncGetByProposalID != nil {
		mmGetByProposalID.inspectFuncGetByProposalID(proposalID)
	}

	mm_params := ProposalHistoryDBMockGetByProposalIDParams{proposalID}

	// Record call args
	mmGetByProposalID.GetByProposalIDMock.mutex.Lock()
	mmGetByProposalID.GetByProposalIDMock.callArgs = append(mmGetByProposalID.GetByProposalIDMock.callArgs, &mm_params)
	mmGetByProposalID.GetByProposalIDMock.mutex.Unlock()

	for _, e := range mmGetByProposalID.GetByProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ha1, e.results.err
		}
	}

	if mmGetByProposalID.GetByProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProposalID.GetByProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProposalID.GetByProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProposalID.GetByProposalIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalHistoryDBMockGetByProposalIDParams{proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetByProposalID.t.Errorf("ProposalHistoryDBMock.GetByProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProposalID.GetByProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProposalID.t.Errorf("ProposalHistoryDBMock.GetByProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProposalID.GetByProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProposalID.GetByProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProposalID.t.Fatal("No results are set for the ProposalHistoryDBMock.GetByProposalID")
		}
		return (*mm_results).ha1, (*mm_results).err
	}
	if mmGetByProposalID.funcGetByProposalID != nil {
		return mmGetByProposalID.funcGetByProposalID(proposalID)
	}
	mmGetByProposalID.t.Fatalf("Unexpected call to ProposalHistoryDBMock.GetByProposalID. %v", proposalID)
	return
}

// GetByProposalIDAfterCounter returns a count of finished ProposalHistoryDBMock.GetByProposalID invocations
func (mmGetByProposalID *ProposalHistoryDBMock) GetByProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProposalID.afterGetByProposalIDCounter)
}

// GetByProposalIDBeforeCounter returns a count of ProposalHistoryDBMock.GetByProposalID invocations
func (mmGetByProposalID *ProposalHistoryDBMock) GetByProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProposalID.beforeGetByProposalIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalHistoryDBMock.GetByProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProposalID *mProposalHistoryDBMockGetByProposalID) Calls() []*ProposalHistoryDBMockGetByProposalIDParams {
	mmGetByProposalID.mutex.RLock()

	argCopy := make([]*ProposalHistoryDBMockGetByProposalIDParams, len(mmGetByProposalID.callArgs))
	copy(argCopy, mmGetByProposalID.callArgs)

	mmGetByProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProposalIDDone returns true if the count of the GetByProposalID invocations corresponds
// the number of defined expectations
func (m *ProposalHistoryDBMock) MinimockGetByProposalIDDone() bool {
	if m.GetByProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProposalIDMock.invocationsDone()
}

// MinimockGetByProposalIDInspect logs each unmet expectation
func (m *ProposalHistoryDBMock) MinimockGetByProposalIDInspect() {
	for _, e := range m.GetByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProposalIDCounter := mm_atomic.LoadUint64(&m.afterGetByProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProposalIDMock.defaultExpectation != nil && afterGetByProposalIDCounter < 1 {
		if m.GetByProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByProposalID at\n%s", m.GetByProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByProposalID at\n%s with params: %#v", m.GetByProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProposalID != nil && afterGetByProposalIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByProposalID at\n%s", m.funcGetByProposalIDOrigin)
	}

	if !m.GetByProposalIDMock.invocationsDone() && afterGetByProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalHistoryDBMock.GetByProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProposalIDMock.expectedInvocations), m.GetByProposalIDMock.expectedInvocationsOrigin, afterGetByProposalIDCounter)
	}
}

type mProposalHistoryDBMockGetByUserID struct {
	optional           bool
	mock               *ProposalHistoryDBMock
	defaultExpectation *ProposalHistoryDBMockGetByUserIDExpectation
	expectations       []*ProposalHistoryDBMockGetByUserIDExpectation

	callArgs []*ProposalHistoryDBMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalHistoryDBMockGetByUserIDExpectation specifies expectation struct of the ProposalHistoryDB.GetByUserID
type ProposalHistoryDBMockGetByUserIDExpectation struct {
	mock               *ProposalHistoryDBMock
	params             *ProposalHistoryDBMockGetByUserIDParams
	paramPtrs          *ProposalHistoryDBMockGetByUserIDParamPtrs
	expectationOrigins ProposalHistoryDBMockGetByUserIDExpectationOrigins
	results            *ProposalHistoryDBMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// ProposalHistoryDBMockGetByUserIDParams contains parameters of the ProposalHistoryDB.GetByUserID
type ProposalHistoryDBMockGetByUserIDParams struct {
	userID int64
}

// ProposalHistoryDBMockGetByUserIDParamPtrs contains pointers to parameters of the ProposalHistoryDB.GetByUserID
type ProposalHistoryDBMockGetByUserIDParamPtrs struct {
	userID *int64
}

// ProposalHistoryDBMockGetByUserIDResults contains results of the ProposalHistoryDB.GetByUserID
type ProposalHistoryDBMockGetByUserIDResults struct {
	ha1 []voproposal.HistoryRecord
	err error
}

// ProposalHistoryDBMockGetByUserIDOrigins contains origins of expectations of the ProposalHistoryDB.GetByUserID
type ProposalHistoryDBMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Optional() *mProposalHistoryDBMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for ProposalHistoryDB.GetByUserID
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Expect(userID int64) *mProposalHistoryDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProposalHistoryDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProposalHistoryDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("ProposalHistoryDBMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &ProposalHistoryDBMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for ProposalHistoryDB.GetByUserID
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) ExpectUserIDParam1(userID int64) *mProposalHistoryDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProposalHistoryDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProposalHistoryDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("ProposalHistoryDBMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &ProposalHistoryDBMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the ProposalHistoryDB.GetByUserID
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Inspect(f func(userID int64)) *mProposalHistoryDBMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for ProposalHistoryDBMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by ProposalHistoryDB.GetByUserID
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Return(ha1 []voproposal.HistoryRecord, err error) *ProposalHistoryDBMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProposalHistoryDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &ProposalHistoryDBMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &ProposalHistoryDBMockGetByUserIDResults{ha1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the ProposalHistoryDB.GetByUserID method
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Set(f func(userID int64) (ha1 []voproposal.HistoryRecord, err error)) *ProposalHistoryDBMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the ProposalHistoryDB.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the ProposalHistoryDB.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the ProposalHistoryDB.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) When(userID int64) *ProposalHistoryDBMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("ProposalHistoryDBMock.GetByUserID mock is already set by Set")
	}

	expectation := &ProposalHistoryDBMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &ProposalHistoryDBMockGetByUserIDParams{userID},
		expectationOrigins: ProposalHistoryDBMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up ProposalHistoryDB.GetByUserID return parameters for the expectation previously defined by the When method
func (e *ProposalHistoryDBMockGetByUserIDExpectation) Then(ha1 []voproposal.HistoryRecord, err error) *ProposalHistoryDBMock {
	e.results = &ProposalHistoryDBMockGetByUserIDResults{ha1, err}
	return e.mock
}

// Times sets number of times ProposalHistoryDB.GetByUserID should be invoked
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Times(n uint64) *mProposalHistoryDBMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of ProposalHistoryDBMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.ProposalHistoryDB
func (mmGetByUserID *ProposalHistoryDBMock) GetByUserID(userID int64) (ha1 []voproposal.HistoryRecord, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := ProposalHistoryDBMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ha1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := ProposalHistoryDBMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("ProposalHistoryDBMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("ProposalHistoryDBMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the ProposalHistoryDBMock.GetByUserID")
		}
		return (*mm_results).ha1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to ProposalHistoryDBMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished ProposalHistoryDBMock.GetByUserID invocations
func (mmGetByUserID *ProposalHistoryDBMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of ProposalHistoryDBMock.GetByUserID invocations
func (mmGetByUserID *ProposalHistoryDBMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to ProposalHistoryDBMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mProposalHistoryDBMockGetByUserID) Calls() []*ProposalHistoryDBMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*ProposalHistoryDBMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *ProposalHistoryDBMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *ProposalHistoryDBMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to ProposalHistoryDBMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalHistoryDBMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mProposalHistoryDBMockGetUnreadEventsForUser struct {
	optional           bool
	mock               *ProposalHistoryDBMock
	defaultExpectation *ProposalHistoryDBMockGetUnreadEventsForUserExpectation
	expectations       []*ProposalHistoryDBMockGetUnreadEventsForUserExpectation

	callArgs []*ProposalHistoryDBMockGetUnreadEventsForUserParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// ProposalHistoryDBMockGetUnreadEventsForUserExpectation specifies expectation struct of the ProposalHistoryDB.GetUnreadEventsForUser
type ProposalHistoryDBMockGetUnreadEventsForUserExpectation struct {
	mock               *ProposalHistoryDBMock
	params             *ProposalHistoryDBMockGetUnreadEventsForUserParams
	paramPtrs          *ProposalHistoryDBMockGetUnreadEventsForUserParamPtrs
	expectationOrigins ProposalHistoryDBMockGetUnreadEventsForUserExpectationOrigins
	results            *ProposalHistoryDBMockGetUnreadEventsForUserResults
	returnOrigin       string
	Counter            uint64
}

// ProposalHistoryDBMockGetUnreadEventsForUserParams contains parameters of the ProposalHistoryDB.GetUnreadEventsForUser
type ProposalHistoryDBMockGetUnreadEventsForUserParams struct {
	userID int64
}

// ProposalHistoryDBMockGetUnreadEventsForUserParamPtrs contains pointers to parameters of the ProposalHistoryDB.GetUnreadEventsForUser
type ProposalHistoryDBMockGetUnreadEventsForUserParamPtrs struct {
	userID *int64
}

// ProposalHistoryDBMockGetUnreadEventsForUserResults contains results of the ProposalHistoryDB.GetUnreadEventsForUser
type ProposalHistoryDBMockGetUnreadEventsForUserResults struct {
	ha1 []voproposal.HistoryRecord
	err error
}

// ProposalHistoryDBMockGetUnreadEventsForUserOrigins contains origins of expectations of the ProposalHistoryDB.GetUnreadEventsForUser
type ProposalHistoryDBMockGetUnreadEventsForUserExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Optional() *mProposalHistoryDBMockGetUnreadEventsForUser {
	mmGetUnreadEventsForUser.optional = true
	return mmGetUnreadEventsForUser
}

// Expect sets up expected params for ProposalHistoryDB.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Expect(userID int64) *mProposalHistoryDBMockGetUnreadEventsForUser {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalHistoryDBMock.GetUnreadEventsForUser mock is already set by Set")
	}

	if mmGetUnreadEventsForUser.defaultExpectation == nil {
		mmGetUnreadEventsForUser.defaultExpectation = &ProposalHistoryDBMockGetUnreadEventsForUserExpectation{}
	}

	if mmGetUnreadEventsForUser.defaultExpectation.paramPtrs != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalHistoryDBMock.GetUnreadEventsForUser mock is already set by ExpectParams functions")
	}

	mmGetUnreadEventsForUser.defaultExpectation.params = &ProposalHistoryDBMockGetUnreadEventsForUserParams{userID}
	mmGetUnreadEventsForUser.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUnreadEventsForUser.expectations {
		if minimock.Equal(e.params, mmGetUnreadEventsForUser.defaultExpectation.params) {
			mmGetUnreadEventsForUser.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUnreadEventsForUser.defaultExpectation.params)
		}
	}

	return mmGetUnreadEventsForUser
}

// ExpectUserIDParam1 sets up expected param userID for ProposalHistoryDB.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) ExpectUserIDParam1(userID int64) *mProposalHistoryDBMockGetUnreadEventsForUser {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalHistoryDBMock.GetUnreadEventsForUser mock is already set by Set")
	}

	if mmGetUnreadEventsForUser.defaultExpectation == nil {
		mmGetUnreadEventsForUser.defaultExpectation = &ProposalHistoryDBMockGetUnreadEventsForUserExpectation{}
	}

	if mmGetUnreadEventsForUser.defaultExpectation.params != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalHistoryDBMock.GetUnreadEventsForUser mock is already set by Expect")
	}

	if mmGetUnreadEventsForUser.defaultExpectation.paramPtrs == nil {
		mmGetUnreadEventsForUser.defaultExpectation.paramPtrs = &ProposalHistoryDBMockGetUnreadEventsForUserParamPtrs{}
	}
	mmGetUnreadEventsForUser.defaultExpectation.paramPtrs.userID = &userID
	mmGetUnreadEventsForUser.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUnreadEventsForUser
}

// Inspect accepts an inspector function that has same arguments as the ProposalHistoryDB.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Inspect(f func(userID int64)) *mProposalHistoryDBMockGetUnreadEventsForUser {
	if mmGetUnreadEventsForUser.mock.inspectFuncGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Inspect function is already set for ProposalHistoryDBMock.GetUnreadEventsForUser")
	}

	mmGetUnreadEventsForUser.mock.inspectFuncGetUnreadEventsForUser = f

	return mmGetUnreadEventsForUser
}

// Return sets up results that will be returned by ProposalHistoryDB.GetUnreadEventsForUser
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Return(ha1 []voproposal.HistoryRecord, err error) *ProposalHistoryDBMock {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalHistoryDBMock.GetUnreadEventsForUser mock is already set by Set")
	}

	if mmGetUnreadEventsForUser.defaultExpectation == nil {
		mmGetUnreadEventsForUser.defaultExpectation = &ProposalHistoryDBMockGetUnreadEventsForUserExpectation{mock: mmGetUnreadEventsForUser.mock}
	}
	mmGetUnreadEventsForUser.defaultExpectation.results = &ProposalHistoryDBMockGetUnreadEventsForUserResults{ha1, err}
	mmGetUnreadEventsForUser.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUnreadEventsForUser.mock
}

// Set uses given function f to mock the ProposalHistoryDB.GetUnreadEventsForUser method
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Set(f func(userID int64) (ha1 []voproposal.HistoryRecord, err error)) *ProposalHistoryDBMock {
	if mmGetUnreadEventsForUser.defaultExpectation != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Default expectation is already set for the ProposalHistoryDB.GetUnreadEventsForUser method")
	}

	if len(mmGetUnreadEventsForUser.expectations) > 0 {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Some expectations are already set for the ProposalHistoryDB.GetUnreadEventsForUser method")
	}

	mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser = f
	mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUserOrigin = minimock.CallerInfo(1)
	return mmGetUnreadEventsForUser.mock
}

// When sets expectation for the ProposalHistoryDB.GetUnreadEventsForUser which will trigger the result defined by the following
// Then helper
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) When(userID int64) *ProposalHistoryDBMockGetUnreadEventsForUserExpectation {
	if mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.mock.t.Fatalf("ProposalHistoryDBMock.GetUnreadEventsForUser mock is already set by Set")
	}

	expectation := &ProposalHistoryDBMockGetUnreadEventsForUserExpectation{
		mock:               mmGetUnreadEventsForUser.mock,
		params:             &ProposalHistoryDBMockGetUnreadEventsForUserParams{userID},
		expectationOrigins: ProposalHistoryDBMockGetUnreadEventsForUserExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUnreadEventsForUser.expectations = append(mmGetUnreadEventsForUser.expectations, expectation)
	return expectation
}

// Then sets up ProposalHistoryDB.GetUnreadEventsForUser return parameters for the expectation previously defined by the When method
func (e *ProposalHistoryDBMockGetUnreadEventsForUserExpectation) Then(ha1 []voproposal.HistoryRecord, err error) *ProposalHistoryDBMock {
	e.results = &ProposalHistoryDBMockGetUnreadEventsForUserResults{ha1, err}
	return e.mock
}

// Times sets number of times ProposalHistoryDB.GetUnreadEventsForUser should be invoked
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Times(n uint64) *mProposalHistoryDBMockGetUnreadEventsForUser {
	if n == 0 {
		mmGetUnreadEventsForUser.mock.t.Fatalf("Times of ProposalHistoryDBMock.GetUnreadEventsForUser mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUnreadEventsForUser.expectedInvocations, n)
	mmGetUnreadEventsForUser.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUnreadEventsForUser
}

func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) invocationsDone() bool {
	if len(mmGetUnreadEventsForUser.expectations) == 0 && mmGetUnreadEventsForUser.defaultExpectation == nil && mmGetUnreadEventsForUser.mock.funcGetUnreadEventsForUser == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.mock.afterGetUnreadEventsForUserCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUnreadEventsForUser implements mm_repository.ProposalHistoryDB
func (mmGetUnreadEventsForUser *ProposalHistoryDBMock) GetUnreadEventsForUser(userID int64) (ha1 []voproposal.HistoryRecord, err error) {
	mm_atomic.AddUint64(&mmGetUnreadEventsForUser.beforeGetUnreadEventsForUserCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUnreadEventsForUser.afterGetUnreadEventsForUserCounter, 1)

	mmGetUnreadEventsForUser.t.Helper()

	if mmGetUnreadEventsForUser.inspectFuncGetUnreadEventsForUser != nil {
		mmGetUnreadEventsForUser.inspectFuncGetUnreadEventsForUser(userID)
	}

	mm_params := ProposalHistoryDBMockGetUnreadEventsForUserParams{userID}

	// Record call args
	mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.mutex.Lock()
	mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.callArgs = append(mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.callArgs, &mm_params)
	mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.mutex.Unlock()

	for _, e := range mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ha1, e.results.err
		}
	}

	if mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.params
		mm_want_ptrs := mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.paramPtrs

		mm_got := ProposalHistoryDBMockGetUnreadEventsForUserParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUnreadEventsForUser.t.Errorf("ProposalHistoryDBMock.GetUnreadEventsForUser got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUnreadEventsForUser.t.Errorf("ProposalHistoryDBMock.GetUnreadEventsForUser got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUnreadEventsForUser.GetUnreadEventsForUserMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUnreadEventsForUser.t.Fatal("No results are set for the ProposalHistoryDBMock.GetUnreadEventsForUser")
		}
		return (*mm_results).ha1, (*mm_results).err
	}
	if mmGetUnreadEventsForUser.funcGetUnreadEventsForUser != nil {
		return mmGetUnreadEventsForUser.funcGetUnreadEventsForUser(userID)
	}
	mmGetUnreadEventsForUser.t.Fatalf("Unexpected call to ProposalHistoryDBMock.GetUnreadEventsForUser. %v", userID)
	return
}

// GetUnreadEventsForUserAfterCounter returns a count of finished ProposalHistoryDBMock.GetUnreadEventsForUser invocations
func (mmGetUnreadEventsForUser *ProposalHistoryDBMock) GetUnreadEventsForUserAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.afterGetUnreadEventsForUserCounter)
}

// GetUnreadEventsForUserBeforeCounter returns a count of ProposalHistoryDBMock.GetUnreadEventsForUser invocations
func (mmGetUnreadEventsForUser *ProposalHistoryDBMock) GetUnreadEventsForUserBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUnreadEventsForUser.beforeGetUnreadEventsForUserCounter)
}

// Calls returns a list of arguments used in each call to ProposalHistoryDBMock.GetUnreadEventsForUser.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUnreadEventsForUser *mProposalHistoryDBMockGetUnreadEventsForUser) Calls() []*ProposalHistoryDBMockGetUnreadEventsForUserParams {
	mmGetUnreadEventsForUser.mutex.RLock()

	argCopy := make([]*ProposalHistoryDBMockGetUnreadEventsForUserParams, len(mmGetUnreadEventsForUser.callArgs))
	copy(argCopy, mmGetUnreadEventsForUser.callArgs)

	mmGetUnreadEventsForUser.mutex.RUnlock()

	return argCopy
}

// MinimockGetUnreadEventsForUserDone returns true if the count of the GetUnreadEventsForUser invocations corresponds
// the number of defined expectations
func (m *ProposalHistoryDBMock) MinimockGetUnreadEventsForUserDone() bool {
	if m.GetUnreadEventsForUserMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUnreadEventsForUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUnreadEventsForUserMock.invocationsDone()
}

// MinimockGetUnreadEventsForUserInspect logs each unmet expectation
func (m *ProposalHistoryDBMock) MinimockGetUnreadEventsForUserInspect() {
	for _, e := range m.GetUnreadEventsForUserMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetUnreadEventsForUser at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUnreadEventsForUserCounter := mm_atomic.LoadUint64(&m.afterGetUnreadEventsForUserCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUnreadEventsForUserMock.defaultExpectation != nil && afterGetUnreadEventsForUserCounter < 1 {
		if m.GetUnreadEventsForUserMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetUnreadEventsForUser at\n%s", m.GetUnreadEventsForUserMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to ProposalHistoryDBMock.GetUnreadEventsForUser at\n%s with params: %#v", m.GetUnreadEventsForUserMock.defaultExpectation.expectationOrigins.origin, *m.GetUnreadEventsForUserMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUnreadEventsForUser != nil && afterGetUnreadEventsForUserCounter < 1 {
		m.t.Errorf("Expected call to ProposalHistoryDBMock.GetUnreadEventsForUser at\n%s", m.funcGetUnreadEventsForUserOrigin)
	}

	if !m.GetUnreadEventsForUserMock.invocationsDone() && afterGetUnreadEventsForUserCounter > 0 {
		m.t.Errorf("Expected %d calls to ProposalHistoryDBMock.GetUnreadEventsForUser at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUnreadEventsForUserMock.expectedInvocations), m.GetUnreadEventsForUserMock.expectedInvocationsOrigin, afterGetUnreadEventsForUserCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *ProposalHistoryDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateMessageInspect()

			m.MinimockGetByProposalIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetUnreadEventsForUserInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *ProposalHistoryDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *ProposalHistoryDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateMessageDone() &&
		m.MinimockGetByProposalIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetUnreadEventsForUserDone()
}
