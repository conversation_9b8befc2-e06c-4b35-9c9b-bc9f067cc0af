// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository.SectionCache -o section_cache_mock.go -n SectionCacheMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	"github.com/gojuno/minimock/v3"
)

// SectionCacheMock implements mm_repository.SectionCache
type SectionCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetSectionsByProposalID          func(productID int64, proposalID int64) (sa1 []proposalentity.Stand, err error)
	funcGetSectionsByProposalIDOrigin    string
	inspectFuncGetSectionsByProposalID   func(productID int64, proposalID int64)
	afterGetSectionsByProposalIDCounter  uint64
	beforeGetSectionsByProposalIDCounter uint64
	GetSectionsByProposalIDMock          mSectionCacheMockGetSectionsByProposalID

	funcSet          func(section proposalentity.Stand) (err error)
	funcSetOrigin    string
	inspectFuncSet   func(section proposalentity.Stand)
	afterSetCounter  uint64
	beforeSetCounter uint64
	SetMock          mSectionCacheMockSet

	funcUpdateSectionsByProposalID          func(productID int64, proposalID int64, sections proposalvalueobject.Values) (sa1 []proposalentity.Stand, err error)
	funcUpdateSectionsByProposalIDOrigin    string
	inspectFuncUpdateSectionsByProposalID   func(productID int64, proposalID int64, sections proposalvalueobject.Values)
	afterUpdateSectionsByProposalIDCounter  uint64
	beforeUpdateSectionsByProposalIDCounter uint64
	UpdateSectionsByProposalIDMock          mSectionCacheMockUpdateSectionsByProposalID
}

// NewSectionCacheMock returns a mock for mm_repository.SectionCache
func NewSectionCacheMock(t minimock.Tester) *SectionCacheMock {
	m := &SectionCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetSectionsByProposalIDMock = mSectionCacheMockGetSectionsByProposalID{mock: m}
	m.GetSectionsByProposalIDMock.callArgs = []*SectionCacheMockGetSectionsByProposalIDParams{}

	m.SetMock = mSectionCacheMockSet{mock: m}
	m.SetMock.callArgs = []*SectionCacheMockSetParams{}

	m.UpdateSectionsByProposalIDMock = mSectionCacheMockUpdateSectionsByProposalID{mock: m}
	m.UpdateSectionsByProposalIDMock.callArgs = []*SectionCacheMockUpdateSectionsByProposalIDParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mSectionCacheMockGetSectionsByProposalID struct {
	optional           bool
	mock               *SectionCacheMock
	defaultExpectation *SectionCacheMockGetSectionsByProposalIDExpectation
	expectations       []*SectionCacheMockGetSectionsByProposalIDExpectation

	callArgs []*SectionCacheMockGetSectionsByProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// SectionCacheMockGetSectionsByProposalIDExpectation specifies expectation struct of the SectionCache.GetSectionsByProposalID
type SectionCacheMockGetSectionsByProposalIDExpectation struct {
	mock               *SectionCacheMock
	params             *SectionCacheMockGetSectionsByProposalIDParams
	paramPtrs          *SectionCacheMockGetSectionsByProposalIDParamPtrs
	expectationOrigins SectionCacheMockGetSectionsByProposalIDExpectationOrigins
	results            *SectionCacheMockGetSectionsByProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// SectionCacheMockGetSectionsByProposalIDParams contains parameters of the SectionCache.GetSectionsByProposalID
type SectionCacheMockGetSectionsByProposalIDParams struct {
	productID  int64
	proposalID int64
}

// SectionCacheMockGetSectionsByProposalIDParamPtrs contains pointers to parameters of the SectionCache.GetSectionsByProposalID
type SectionCacheMockGetSectionsByProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
}

// SectionCacheMockGetSectionsByProposalIDResults contains results of the SectionCache.GetSectionsByProposalID
type SectionCacheMockGetSectionsByProposalIDResults struct {
	sa1 []proposalentity.Stand
	err error
}

// SectionCacheMockGetSectionsByProposalIDOrigins contains origins of expectations of the SectionCache.GetSectionsByProposalID
type SectionCacheMockGetSectionsByProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Optional() *mSectionCacheMockGetSectionsByProposalID {
	mmGetSectionsByProposalID.optional = true
	return mmGetSectionsByProposalID
}

// Expect sets up expected params for SectionCache.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Expect(productID int64, proposalID int64) *mSectionCacheMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionCacheMockGetSectionsByProposalIDExpectation{}
	}

	if mmGetSectionsByProposalID.defaultExpectation.paramPtrs != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by ExpectParams functions")
	}

	mmGetSectionsByProposalID.defaultExpectation.params = &SectionCacheMockGetSectionsByProposalIDParams{productID, proposalID}
	mmGetSectionsByProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetSectionsByProposalID.expectations {
		if minimock.Equal(e.params, mmGetSectionsByProposalID.defaultExpectation.params) {
			mmGetSectionsByProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetSectionsByProposalID.defaultExpectation.params)
		}
	}

	return mmGetSectionsByProposalID
}

// ExpectProductIDParam1 sets up expected param productID for SectionCache.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) ExpectProductIDParam1(productID int64) *mSectionCacheMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionCacheMockGetSectionsByProposalIDExpectation{}
	}

	if mmGetSectionsByProposalID.defaultExpectation.params != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Expect")
	}

	if mmGetSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmGetSectionsByProposalID.defaultExpectation.paramPtrs = &SectionCacheMockGetSectionsByProposalIDParamPtrs{}
	}
	mmGetSectionsByProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmGetSectionsByProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetSectionsByProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for SectionCache.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) ExpectProposalIDParam2(proposalID int64) *mSectionCacheMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionCacheMockGetSectionsByProposalIDExpectation{}
	}

	if mmGetSectionsByProposalID.defaultExpectation.params != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Expect")
	}

	if mmGetSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmGetSectionsByProposalID.defaultExpectation.paramPtrs = &SectionCacheMockGetSectionsByProposalIDParamPtrs{}
	}
	mmGetSectionsByProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmGetSectionsByProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmGetSectionsByProposalID
}

// Inspect accepts an inspector function that has same arguments as the SectionCache.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Inspect(f func(productID int64, proposalID int64)) *mSectionCacheMockGetSectionsByProposalID {
	if mmGetSectionsByProposalID.mock.inspectFuncGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("Inspect function is already set for SectionCacheMock.GetSectionsByProposalID")
	}

	mmGetSectionsByProposalID.mock.inspectFuncGetSectionsByProposalID = f

	return mmGetSectionsByProposalID
}

// Return sets up results that will be returned by SectionCache.GetSectionsByProposalID
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Return(sa1 []proposalentity.Stand, err error) *SectionCacheMock {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Set")
	}

	if mmGetSectionsByProposalID.defaultExpectation == nil {
		mmGetSectionsByProposalID.defaultExpectation = &SectionCacheMockGetSectionsByProposalIDExpectation{mock: mmGetSectionsByProposalID.mock}
	}
	mmGetSectionsByProposalID.defaultExpectation.results = &SectionCacheMockGetSectionsByProposalIDResults{sa1, err}
	mmGetSectionsByProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetSectionsByProposalID.mock
}

// Set uses given function f to mock the SectionCache.GetSectionsByProposalID method
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Set(f func(productID int64, proposalID int64) (sa1 []proposalentity.Stand, err error)) *SectionCacheMock {
	if mmGetSectionsByProposalID.defaultExpectation != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("Default expectation is already set for the SectionCache.GetSectionsByProposalID method")
	}

	if len(mmGetSectionsByProposalID.expectations) > 0 {
		mmGetSectionsByProposalID.mock.t.Fatalf("Some expectations are already set for the SectionCache.GetSectionsByProposalID method")
	}

	mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID = f
	mmGetSectionsByProposalID.mock.funcGetSectionsByProposalIDOrigin = minimock.CallerInfo(1)
	return mmGetSectionsByProposalID.mock
}

// When sets expectation for the SectionCache.GetSectionsByProposalID which will trigger the result defined by the following
// Then helper
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) When(productID int64, proposalID int64) *SectionCacheMockGetSectionsByProposalIDExpectation {
	if mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.GetSectionsByProposalID mock is already set by Set")
	}

	expectation := &SectionCacheMockGetSectionsByProposalIDExpectation{
		mock:               mmGetSectionsByProposalID.mock,
		params:             &SectionCacheMockGetSectionsByProposalIDParams{productID, proposalID},
		expectationOrigins: SectionCacheMockGetSectionsByProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetSectionsByProposalID.expectations = append(mmGetSectionsByProposalID.expectations, expectation)
	return expectation
}

// Then sets up SectionCache.GetSectionsByProposalID return parameters for the expectation previously defined by the When method
func (e *SectionCacheMockGetSectionsByProposalIDExpectation) Then(sa1 []proposalentity.Stand, err error) *SectionCacheMock {
	e.results = &SectionCacheMockGetSectionsByProposalIDResults{sa1, err}
	return e.mock
}

// Times sets number of times SectionCache.GetSectionsByProposalID should be invoked
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Times(n uint64) *mSectionCacheMockGetSectionsByProposalID {
	if n == 0 {
		mmGetSectionsByProposalID.mock.t.Fatalf("Times of SectionCacheMock.GetSectionsByProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetSectionsByProposalID.expectedInvocations, n)
	mmGetSectionsByProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetSectionsByProposalID
}

func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) invocationsDone() bool {
	if len(mmGetSectionsByProposalID.expectations) == 0 && mmGetSectionsByProposalID.defaultExpectation == nil && mmGetSectionsByProposalID.mock.funcGetSectionsByProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetSectionsByProposalID.mock.afterGetSectionsByProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetSectionsByProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetSectionsByProposalID implements mm_repository.SectionCache
func (mmGetSectionsByProposalID *SectionCacheMock) GetSectionsByProposalID(productID int64, proposalID int64) (sa1 []proposalentity.Stand, err error) {
	mm_atomic.AddUint64(&mmGetSectionsByProposalID.beforeGetSectionsByProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetSectionsByProposalID.afterGetSectionsByProposalIDCounter, 1)

	mmGetSectionsByProposalID.t.Helper()

	if mmGetSectionsByProposalID.inspectFuncGetSectionsByProposalID != nil {
		mmGetSectionsByProposalID.inspectFuncGetSectionsByProposalID(productID, proposalID)
	}

	mm_params := SectionCacheMockGetSectionsByProposalIDParams{productID, proposalID}

	// Record call args
	mmGetSectionsByProposalID.GetSectionsByProposalIDMock.mutex.Lock()
	mmGetSectionsByProposalID.GetSectionsByProposalIDMock.callArgs = append(mmGetSectionsByProposalID.GetSectionsByProposalIDMock.callArgs, &mm_params)
	mmGetSectionsByProposalID.GetSectionsByProposalIDMock.mutex.Unlock()

	for _, e := range mmGetSectionsByProposalID.GetSectionsByProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.sa1, e.results.err
		}
	}

	if mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.paramPtrs

		mm_got := SectionCacheMockGetSectionsByProposalIDParams{productID, proposalID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetSectionsByProposalID.t.Errorf("SectionCacheMock.GetSectionsByProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmGetSectionsByProposalID.t.Errorf("SectionCacheMock.GetSectionsByProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetSectionsByProposalID.t.Errorf("SectionCacheMock.GetSectionsByProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetSectionsByProposalID.GetSectionsByProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetSectionsByProposalID.t.Fatal("No results are set for the SectionCacheMock.GetSectionsByProposalID")
		}
		return (*mm_results).sa1, (*mm_results).err
	}
	if mmGetSectionsByProposalID.funcGetSectionsByProposalID != nil {
		return mmGetSectionsByProposalID.funcGetSectionsByProposalID(productID, proposalID)
	}
	mmGetSectionsByProposalID.t.Fatalf("Unexpected call to SectionCacheMock.GetSectionsByProposalID. %v %v", productID, proposalID)
	return
}

// GetSectionsByProposalIDAfterCounter returns a count of finished SectionCacheMock.GetSectionsByProposalID invocations
func (mmGetSectionsByProposalID *SectionCacheMock) GetSectionsByProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSectionsByProposalID.afterGetSectionsByProposalIDCounter)
}

// GetSectionsByProposalIDBeforeCounter returns a count of SectionCacheMock.GetSectionsByProposalID invocations
func (mmGetSectionsByProposalID *SectionCacheMock) GetSectionsByProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSectionsByProposalID.beforeGetSectionsByProposalIDCounter)
}

// Calls returns a list of arguments used in each call to SectionCacheMock.GetSectionsByProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetSectionsByProposalID *mSectionCacheMockGetSectionsByProposalID) Calls() []*SectionCacheMockGetSectionsByProposalIDParams {
	mmGetSectionsByProposalID.mutex.RLock()

	argCopy := make([]*SectionCacheMockGetSectionsByProposalIDParams, len(mmGetSectionsByProposalID.callArgs))
	copy(argCopy, mmGetSectionsByProposalID.callArgs)

	mmGetSectionsByProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockGetSectionsByProposalIDDone returns true if the count of the GetSectionsByProposalID invocations corresponds
// the number of defined expectations
func (m *SectionCacheMock) MinimockGetSectionsByProposalIDDone() bool {
	if m.GetSectionsByProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetSectionsByProposalIDMock.invocationsDone()
}

// MinimockGetSectionsByProposalIDInspect logs each unmet expectation
func (m *SectionCacheMock) MinimockGetSectionsByProposalIDInspect() {
	for _, e := range m.GetSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to SectionCacheMock.GetSectionsByProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetSectionsByProposalIDCounter := mm_atomic.LoadUint64(&m.afterGetSectionsByProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetSectionsByProposalIDMock.defaultExpectation != nil && afterGetSectionsByProposalIDCounter < 1 {
		if m.GetSectionsByProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to SectionCacheMock.GetSectionsByProposalID at\n%s", m.GetSectionsByProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to SectionCacheMock.GetSectionsByProposalID at\n%s with params: %#v", m.GetSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.GetSectionsByProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetSectionsByProposalID != nil && afterGetSectionsByProposalIDCounter < 1 {
		m.t.Errorf("Expected call to SectionCacheMock.GetSectionsByProposalID at\n%s", m.funcGetSectionsByProposalIDOrigin)
	}

	if !m.GetSectionsByProposalIDMock.invocationsDone() && afterGetSectionsByProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to SectionCacheMock.GetSectionsByProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetSectionsByProposalIDMock.expectedInvocations), m.GetSectionsByProposalIDMock.expectedInvocationsOrigin, afterGetSectionsByProposalIDCounter)
	}
}

type mSectionCacheMockSet struct {
	optional           bool
	mock               *SectionCacheMock
	defaultExpectation *SectionCacheMockSetExpectation
	expectations       []*SectionCacheMockSetExpectation

	callArgs []*SectionCacheMockSetParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// SectionCacheMockSetExpectation specifies expectation struct of the SectionCache.Set
type SectionCacheMockSetExpectation struct {
	mock               *SectionCacheMock
	params             *SectionCacheMockSetParams
	paramPtrs          *SectionCacheMockSetParamPtrs
	expectationOrigins SectionCacheMockSetExpectationOrigins
	results            *SectionCacheMockSetResults
	returnOrigin       string
	Counter            uint64
}

// SectionCacheMockSetParams contains parameters of the SectionCache.Set
type SectionCacheMockSetParams struct {
	section proposalentity.Stand
}

// SectionCacheMockSetParamPtrs contains pointers to parameters of the SectionCache.Set
type SectionCacheMockSetParamPtrs struct {
	section *proposalentity.Stand
}

// SectionCacheMockSetResults contains results of the SectionCache.Set
type SectionCacheMockSetResults struct {
	err error
}

// SectionCacheMockSetOrigins contains origins of expectations of the SectionCache.Set
type SectionCacheMockSetExpectationOrigins struct {
	origin        string
	originSection string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSet *mSectionCacheMockSet) Optional() *mSectionCacheMockSet {
	mmSet.optional = true
	return mmSet
}

// Expect sets up expected params for SectionCache.Set
func (mmSet *mSectionCacheMockSet) Expect(section proposalentity.Stand) *mSectionCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("SectionCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &SectionCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.paramPtrs != nil {
		mmSet.mock.t.Fatalf("SectionCacheMock.Set mock is already set by ExpectParams functions")
	}

	mmSet.defaultExpectation.params = &SectionCacheMockSetParams{section}
	mmSet.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSet.expectations {
		if minimock.Equal(e.params, mmSet.defaultExpectation.params) {
			mmSet.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSet.defaultExpectation.params)
		}
	}

	return mmSet
}

// ExpectSectionParam1 sets up expected param section for SectionCache.Set
func (mmSet *mSectionCacheMockSet) ExpectSectionParam1(section proposalentity.Stand) *mSectionCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("SectionCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &SectionCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.params != nil {
		mmSet.mock.t.Fatalf("SectionCacheMock.Set mock is already set by Expect")
	}

	if mmSet.defaultExpectation.paramPtrs == nil {
		mmSet.defaultExpectation.paramPtrs = &SectionCacheMockSetParamPtrs{}
	}
	mmSet.defaultExpectation.paramPtrs.section = &section
	mmSet.defaultExpectation.expectationOrigins.originSection = minimock.CallerInfo(1)

	return mmSet
}

// Inspect accepts an inspector function that has same arguments as the SectionCache.Set
func (mmSet *mSectionCacheMockSet) Inspect(f func(section proposalentity.Stand)) *mSectionCacheMockSet {
	if mmSet.mock.inspectFuncSet != nil {
		mmSet.mock.t.Fatalf("Inspect function is already set for SectionCacheMock.Set")
	}

	mmSet.mock.inspectFuncSet = f

	return mmSet
}

// Return sets up results that will be returned by SectionCache.Set
func (mmSet *mSectionCacheMockSet) Return(err error) *SectionCacheMock {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("SectionCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &SectionCacheMockSetExpectation{mock: mmSet.mock}
	}
	mmSet.defaultExpectation.results = &SectionCacheMockSetResults{err}
	mmSet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// Set uses given function f to mock the SectionCache.Set method
func (mmSet *mSectionCacheMockSet) Set(f func(section proposalentity.Stand) (err error)) *SectionCacheMock {
	if mmSet.defaultExpectation != nil {
		mmSet.mock.t.Fatalf("Default expectation is already set for the SectionCache.Set method")
	}

	if len(mmSet.expectations) > 0 {
		mmSet.mock.t.Fatalf("Some expectations are already set for the SectionCache.Set method")
	}

	mmSet.mock.funcSet = f
	mmSet.mock.funcSetOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// When sets expectation for the SectionCache.Set which will trigger the result defined by the following
// Then helper
func (mmSet *mSectionCacheMockSet) When(section proposalentity.Stand) *SectionCacheMockSetExpectation {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("SectionCacheMock.Set mock is already set by Set")
	}

	expectation := &SectionCacheMockSetExpectation{
		mock:               mmSet.mock,
		params:             &SectionCacheMockSetParams{section},
		expectationOrigins: SectionCacheMockSetExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSet.expectations = append(mmSet.expectations, expectation)
	return expectation
}

// Then sets up SectionCache.Set return parameters for the expectation previously defined by the When method
func (e *SectionCacheMockSetExpectation) Then(err error) *SectionCacheMock {
	e.results = &SectionCacheMockSetResults{err}
	return e.mock
}

// Times sets number of times SectionCache.Set should be invoked
func (mmSet *mSectionCacheMockSet) Times(n uint64) *mSectionCacheMockSet {
	if n == 0 {
		mmSet.mock.t.Fatalf("Times of SectionCacheMock.Set mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSet.expectedInvocations, n)
	mmSet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSet
}

func (mmSet *mSectionCacheMockSet) invocationsDone() bool {
	if len(mmSet.expectations) == 0 && mmSet.defaultExpectation == nil && mmSet.mock.funcSet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSet.mock.afterSetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Set implements mm_repository.SectionCache
func (mmSet *SectionCacheMock) Set(section proposalentity.Stand) (err error) {
	mm_atomic.AddUint64(&mmSet.beforeSetCounter, 1)
	defer mm_atomic.AddUint64(&mmSet.afterSetCounter, 1)

	mmSet.t.Helper()

	if mmSet.inspectFuncSet != nil {
		mmSet.inspectFuncSet(section)
	}

	mm_params := SectionCacheMockSetParams{section}

	// Record call args
	mmSet.SetMock.mutex.Lock()
	mmSet.SetMock.callArgs = append(mmSet.SetMock.callArgs, &mm_params)
	mmSet.SetMock.mutex.Unlock()

	for _, e := range mmSet.SetMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSet.SetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSet.SetMock.defaultExpectation.Counter, 1)
		mm_want := mmSet.SetMock.defaultExpectation.params
		mm_want_ptrs := mmSet.SetMock.defaultExpectation.paramPtrs

		mm_got := SectionCacheMockSetParams{section}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.section != nil && !minimock.Equal(*mm_want_ptrs.section, mm_got.section) {
				mmSet.t.Errorf("SectionCacheMock.Set got unexpected parameter section, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSet.SetMock.defaultExpectation.expectationOrigins.originSection, *mm_want_ptrs.section, mm_got.section, minimock.Diff(*mm_want_ptrs.section, mm_got.section))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSet.t.Errorf("SectionCacheMock.Set got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSet.SetMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSet.SetMock.defaultExpectation.results
		if mm_results == nil {
			mmSet.t.Fatal("No results are set for the SectionCacheMock.Set")
		}
		return (*mm_results).err
	}
	if mmSet.funcSet != nil {
		return mmSet.funcSet(section)
	}
	mmSet.t.Fatalf("Unexpected call to SectionCacheMock.Set. %v", section)
	return
}

// SetAfterCounter returns a count of finished SectionCacheMock.Set invocations
func (mmSet *SectionCacheMock) SetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.afterSetCounter)
}

// SetBeforeCounter returns a count of SectionCacheMock.Set invocations
func (mmSet *SectionCacheMock) SetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.beforeSetCounter)
}

// Calls returns a list of arguments used in each call to SectionCacheMock.Set.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSet *mSectionCacheMockSet) Calls() []*SectionCacheMockSetParams {
	mmSet.mutex.RLock()

	argCopy := make([]*SectionCacheMockSetParams, len(mmSet.callArgs))
	copy(argCopy, mmSet.callArgs)

	mmSet.mutex.RUnlock()

	return argCopy
}

// MinimockSetDone returns true if the count of the Set invocations corresponds
// the number of defined expectations
func (m *SectionCacheMock) MinimockSetDone() bool {
	if m.SetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetMock.invocationsDone()
}

// MinimockSetInspect logs each unmet expectation
func (m *SectionCacheMock) MinimockSetInspect() {
	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to SectionCacheMock.Set at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCounter := mm_atomic.LoadUint64(&m.afterSetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetMock.defaultExpectation != nil && afterSetCounter < 1 {
		if m.SetMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to SectionCacheMock.Set at\n%s", m.SetMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to SectionCacheMock.Set at\n%s with params: %#v", m.SetMock.defaultExpectation.expectationOrigins.origin, *m.SetMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSet != nil && afterSetCounter < 1 {
		m.t.Errorf("Expected call to SectionCacheMock.Set at\n%s", m.funcSetOrigin)
	}

	if !m.SetMock.invocationsDone() && afterSetCounter > 0 {
		m.t.Errorf("Expected %d calls to SectionCacheMock.Set at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetMock.expectedInvocations), m.SetMock.expectedInvocationsOrigin, afterSetCounter)
	}
}

type mSectionCacheMockUpdateSectionsByProposalID struct {
	optional           bool
	mock               *SectionCacheMock
	defaultExpectation *SectionCacheMockUpdateSectionsByProposalIDExpectation
	expectations       []*SectionCacheMockUpdateSectionsByProposalIDExpectation

	callArgs []*SectionCacheMockUpdateSectionsByProposalIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// SectionCacheMockUpdateSectionsByProposalIDExpectation specifies expectation struct of the SectionCache.UpdateSectionsByProposalID
type SectionCacheMockUpdateSectionsByProposalIDExpectation struct {
	mock               *SectionCacheMock
	params             *SectionCacheMockUpdateSectionsByProposalIDParams
	paramPtrs          *SectionCacheMockUpdateSectionsByProposalIDParamPtrs
	expectationOrigins SectionCacheMockUpdateSectionsByProposalIDExpectationOrigins
	results            *SectionCacheMockUpdateSectionsByProposalIDResults
	returnOrigin       string
	Counter            uint64
}

// SectionCacheMockUpdateSectionsByProposalIDParams contains parameters of the SectionCache.UpdateSectionsByProposalID
type SectionCacheMockUpdateSectionsByProposalIDParams struct {
	productID  int64
	proposalID int64
	sections   proposalvalueobject.Values
}

// SectionCacheMockUpdateSectionsByProposalIDParamPtrs contains pointers to parameters of the SectionCache.UpdateSectionsByProposalID
type SectionCacheMockUpdateSectionsByProposalIDParamPtrs struct {
	productID  *int64
	proposalID *int64
	sections   *proposalvalueobject.Values
}

// SectionCacheMockUpdateSectionsByProposalIDResults contains results of the SectionCache.UpdateSectionsByProposalID
type SectionCacheMockUpdateSectionsByProposalIDResults struct {
	sa1 []proposalentity.Stand
	err error
}

// SectionCacheMockUpdateSectionsByProposalIDOrigins contains origins of expectations of the SectionCache.UpdateSectionsByProposalID
type SectionCacheMockUpdateSectionsByProposalIDExpectationOrigins struct {
	origin           string
	originProductID  string
	originProposalID string
	originSections   string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Optional() *mSectionCacheMockUpdateSectionsByProposalID {
	mmUpdateSectionsByProposalID.optional = true
	return mmUpdateSectionsByProposalID
}

// Expect sets up expected params for SectionCache.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Expect(productID int64, proposalID int64, sections proposalvalueobject.Values) *mSectionCacheMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionCacheMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by ExpectParams functions")
	}

	mmUpdateSectionsByProposalID.defaultExpectation.params = &SectionCacheMockUpdateSectionsByProposalIDParams{productID, proposalID, sections}
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdateSectionsByProposalID.expectations {
		if minimock.Equal(e.params, mmUpdateSectionsByProposalID.defaultExpectation.params) {
			mmUpdateSectionsByProposalID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdateSectionsByProposalID.defaultExpectation.params)
		}
	}

	return mmUpdateSectionsByProposalID
}

// ExpectProductIDParam1 sets up expected param productID for SectionCache.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) ExpectProductIDParam1(productID int64) *mSectionCacheMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionCacheMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.params != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Expect")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs = &SectionCacheMockUpdateSectionsByProposalIDParamPtrs{}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs.productID = &productID
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmUpdateSectionsByProposalID
}

// ExpectProposalIDParam2 sets up expected param proposalID for SectionCache.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) ExpectProposalIDParam2(proposalID int64) *mSectionCacheMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionCacheMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.params != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Expect")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs = &SectionCacheMockUpdateSectionsByProposalIDParamPtrs{}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs.proposalID = &proposalID
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.originProposalID = minimock.CallerInfo(1)

	return mmUpdateSectionsByProposalID
}

// ExpectSectionsParam3 sets up expected param sections for SectionCache.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) ExpectSectionsParam3(sections proposalvalueobject.Values) *mSectionCacheMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionCacheMockUpdateSectionsByProposalIDExpectation{}
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.params != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Expect")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs == nil {
		mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs = &SectionCacheMockUpdateSectionsByProposalIDParamPtrs{}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.paramPtrs.sections = &sections
	mmUpdateSectionsByProposalID.defaultExpectation.expectationOrigins.originSections = minimock.CallerInfo(1)

	return mmUpdateSectionsByProposalID
}

// Inspect accepts an inspector function that has same arguments as the SectionCache.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Inspect(f func(productID int64, proposalID int64, sections proposalvalueobject.Values)) *mSectionCacheMockUpdateSectionsByProposalID {
	if mmUpdateSectionsByProposalID.mock.inspectFuncUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Inspect function is already set for SectionCacheMock.UpdateSectionsByProposalID")
	}

	mmUpdateSectionsByProposalID.mock.inspectFuncUpdateSectionsByProposalID = f

	return mmUpdateSectionsByProposalID
}

// Return sets up results that will be returned by SectionCache.UpdateSectionsByProposalID
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Return(sa1 []proposalentity.Stand, err error) *SectionCacheMock {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	if mmUpdateSectionsByProposalID.defaultExpectation == nil {
		mmUpdateSectionsByProposalID.defaultExpectation = &SectionCacheMockUpdateSectionsByProposalIDExpectation{mock: mmUpdateSectionsByProposalID.mock}
	}
	mmUpdateSectionsByProposalID.defaultExpectation.results = &SectionCacheMockUpdateSectionsByProposalIDResults{sa1, err}
	mmUpdateSectionsByProposalID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdateSectionsByProposalID.mock
}

// Set uses given function f to mock the SectionCache.UpdateSectionsByProposalID method
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Set(f func(productID int64, proposalID int64, sections proposalvalueobject.Values) (sa1 []proposalentity.Stand, err error)) *SectionCacheMock {
	if mmUpdateSectionsByProposalID.defaultExpectation != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Default expectation is already set for the SectionCache.UpdateSectionsByProposalID method")
	}

	if len(mmUpdateSectionsByProposalID.expectations) > 0 {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Some expectations are already set for the SectionCache.UpdateSectionsByProposalID method")
	}

	mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID = f
	mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalIDOrigin = minimock.CallerInfo(1)
	return mmUpdateSectionsByProposalID.mock
}

// When sets expectation for the SectionCache.UpdateSectionsByProposalID which will trigger the result defined by the following
// Then helper
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) When(productID int64, proposalID int64, sections proposalvalueobject.Values) *SectionCacheMockUpdateSectionsByProposalIDExpectation {
	if mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("SectionCacheMock.UpdateSectionsByProposalID mock is already set by Set")
	}

	expectation := &SectionCacheMockUpdateSectionsByProposalIDExpectation{
		mock:               mmUpdateSectionsByProposalID.mock,
		params:             &SectionCacheMockUpdateSectionsByProposalIDParams{productID, proposalID, sections},
		expectationOrigins: SectionCacheMockUpdateSectionsByProposalIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdateSectionsByProposalID.expectations = append(mmUpdateSectionsByProposalID.expectations, expectation)
	return expectation
}

// Then sets up SectionCache.UpdateSectionsByProposalID return parameters for the expectation previously defined by the When method
func (e *SectionCacheMockUpdateSectionsByProposalIDExpectation) Then(sa1 []proposalentity.Stand, err error) *SectionCacheMock {
	e.results = &SectionCacheMockUpdateSectionsByProposalIDResults{sa1, err}
	return e.mock
}

// Times sets number of times SectionCache.UpdateSectionsByProposalID should be invoked
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Times(n uint64) *mSectionCacheMockUpdateSectionsByProposalID {
	if n == 0 {
		mmUpdateSectionsByProposalID.mock.t.Fatalf("Times of SectionCacheMock.UpdateSectionsByProposalID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdateSectionsByProposalID.expectedInvocations, n)
	mmUpdateSectionsByProposalID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdateSectionsByProposalID
}

func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) invocationsDone() bool {
	if len(mmUpdateSectionsByProposalID.expectations) == 0 && mmUpdateSectionsByProposalID.defaultExpectation == nil && mmUpdateSectionsByProposalID.mock.funcUpdateSectionsByProposalID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.mock.afterUpdateSectionsByProposalIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UpdateSectionsByProposalID implements mm_repository.SectionCache
func (mmUpdateSectionsByProposalID *SectionCacheMock) UpdateSectionsByProposalID(productID int64, proposalID int64, sections proposalvalueobject.Values) (sa1 []proposalentity.Stand, err error) {
	mm_atomic.AddUint64(&mmUpdateSectionsByProposalID.beforeUpdateSectionsByProposalIDCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdateSectionsByProposalID.afterUpdateSectionsByProposalIDCounter, 1)

	mmUpdateSectionsByProposalID.t.Helper()

	if mmUpdateSectionsByProposalID.inspectFuncUpdateSectionsByProposalID != nil {
		mmUpdateSectionsByProposalID.inspectFuncUpdateSectionsByProposalID(productID, proposalID, sections)
	}

	mm_params := SectionCacheMockUpdateSectionsByProposalIDParams{productID, proposalID, sections}

	// Record call args
	mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.mutex.Lock()
	mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.callArgs = append(mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.callArgs, &mm_params)
	mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.mutex.Unlock()

	for _, e := range mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.sa1, e.results.err
		}
	}

	if mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.params
		mm_want_ptrs := mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.paramPtrs

		mm_got := SectionCacheMockUpdateSectionsByProposalIDParams{productID, proposalID, sections}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmUpdateSectionsByProposalID.t.Errorf("SectionCacheMock.UpdateSectionsByProposalID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.proposalID != nil && !minimock.Equal(*mm_want_ptrs.proposalID, mm_got.proposalID) {
				mmUpdateSectionsByProposalID.t.Errorf("SectionCacheMock.UpdateSectionsByProposalID got unexpected parameter proposalID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originProposalID, *mm_want_ptrs.proposalID, mm_got.proposalID, minimock.Diff(*mm_want_ptrs.proposalID, mm_got.proposalID))
			}

			if mm_want_ptrs.sections != nil && !minimock.Equal(*mm_want_ptrs.sections, mm_got.sections) {
				mmUpdateSectionsByProposalID.t.Errorf("SectionCacheMock.UpdateSectionsByProposalID got unexpected parameter sections, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.originSections, *mm_want_ptrs.sections, mm_got.sections, minimock.Diff(*mm_want_ptrs.sections, mm_got.sections))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdateSectionsByProposalID.t.Errorf("SectionCacheMock.UpdateSectionsByProposalID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdateSectionsByProposalID.UpdateSectionsByProposalIDMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdateSectionsByProposalID.t.Fatal("No results are set for the SectionCacheMock.UpdateSectionsByProposalID")
		}
		return (*mm_results).sa1, (*mm_results).err
	}
	if mmUpdateSectionsByProposalID.funcUpdateSectionsByProposalID != nil {
		return mmUpdateSectionsByProposalID.funcUpdateSectionsByProposalID(productID, proposalID, sections)
	}
	mmUpdateSectionsByProposalID.t.Fatalf("Unexpected call to SectionCacheMock.UpdateSectionsByProposalID. %v %v %v", productID, proposalID, sections)
	return
}

// UpdateSectionsByProposalIDAfterCounter returns a count of finished SectionCacheMock.UpdateSectionsByProposalID invocations
func (mmUpdateSectionsByProposalID *SectionCacheMock) UpdateSectionsByProposalIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.afterUpdateSectionsByProposalIDCounter)
}

// UpdateSectionsByProposalIDBeforeCounter returns a count of SectionCacheMock.UpdateSectionsByProposalID invocations
func (mmUpdateSectionsByProposalID *SectionCacheMock) UpdateSectionsByProposalIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdateSectionsByProposalID.beforeUpdateSectionsByProposalIDCounter)
}

// Calls returns a list of arguments used in each call to SectionCacheMock.UpdateSectionsByProposalID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdateSectionsByProposalID *mSectionCacheMockUpdateSectionsByProposalID) Calls() []*SectionCacheMockUpdateSectionsByProposalIDParams {
	mmUpdateSectionsByProposalID.mutex.RLock()

	argCopy := make([]*SectionCacheMockUpdateSectionsByProposalIDParams, len(mmUpdateSectionsByProposalID.callArgs))
	copy(argCopy, mmUpdateSectionsByProposalID.callArgs)

	mmUpdateSectionsByProposalID.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateSectionsByProposalIDDone returns true if the count of the UpdateSectionsByProposalID invocations corresponds
// the number of defined expectations
func (m *SectionCacheMock) MinimockUpdateSectionsByProposalIDDone() bool {
	if m.UpdateSectionsByProposalIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateSectionsByProposalIDMock.invocationsDone()
}

// MinimockUpdateSectionsByProposalIDInspect logs each unmet expectation
func (m *SectionCacheMock) MinimockUpdateSectionsByProposalIDInspect() {
	for _, e := range m.UpdateSectionsByProposalIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to SectionCacheMock.UpdateSectionsByProposalID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateSectionsByProposalIDCounter := mm_atomic.LoadUint64(&m.afterUpdateSectionsByProposalIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateSectionsByProposalIDMock.defaultExpectation != nil && afterUpdateSectionsByProposalIDCounter < 1 {
		if m.UpdateSectionsByProposalIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to SectionCacheMock.UpdateSectionsByProposalID at\n%s", m.UpdateSectionsByProposalIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to SectionCacheMock.UpdateSectionsByProposalID at\n%s with params: %#v", m.UpdateSectionsByProposalIDMock.defaultExpectation.expectationOrigins.origin, *m.UpdateSectionsByProposalIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdateSectionsByProposalID != nil && afterUpdateSectionsByProposalIDCounter < 1 {
		m.t.Errorf("Expected call to SectionCacheMock.UpdateSectionsByProposalID at\n%s", m.funcUpdateSectionsByProposalIDOrigin)
	}

	if !m.UpdateSectionsByProposalIDMock.invocationsDone() && afterUpdateSectionsByProposalIDCounter > 0 {
		m.t.Errorf("Expected %d calls to SectionCacheMock.UpdateSectionsByProposalID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateSectionsByProposalIDMock.expectedInvocations), m.UpdateSectionsByProposalIDMock.expectedInvocationsOrigin, afterUpdateSectionsByProposalIDCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *SectionCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetSectionsByProposalIDInspect()

			m.MinimockSetInspect()

			m.MinimockUpdateSectionsByProposalIDInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *SectionCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *SectionCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetSectionsByProposalIDDone() &&
		m.MinimockSetDone() &&
		m.MinimockUpdateSectionsByProposalIDDone()
}
