package valueobject

import (
	"testing"
)

func TestType_String(t *testing.T) {
	tests := []struct {
		name string
		t    Type
		want string
	}{
		{
			name: "TypeOR",
			t:    TypeOR,
			want: "OR",
		},
		{
			name: "TypeER",
			t:    TypeER,
			want: "ER",
		},
		{
			name: "TypeEmpty",
			t:    TypeEmpty,
			want: "",
		},
		{
			name: "Custom type",
			t:    Type("CUSTOM"),
			want: "CUSTOM",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.t.String()
			if got != tt.want {
				t.Errorf("Type.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestType_IsValid(t *testing.T) {
	tests := []struct {
		name string
		t    Type
		want bool
	}{
		{
			name: "TypeOR is valid",
			t:    TypeOR,
			want: true,
		},
		{
			name: "TypeER is valid",
			t:    TypeER,
			want: true,
		},
		{
			name: "TypeEmpty is invalid",
			t:    TypeEmpty,
			want: false,
		},
		{
			name: "Custom type is invalid",
			t:    Type("CUSTOM"),
			want: false,
		},
		{
			name: "Lowercase type is invalid",
			t:    Type("or"),
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.t.IsValid()
			if got != tt.want {
				t.Errorf("Type.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewType(t *testing.T) {
	tests := []struct {
		name  string
		value string
		want  Type
	}{
		{
			name:  "Valid OR type",
			value: "OR",
			want:  TypeOR,
		},
		{
			name:  "Valid ER type",
			value: "ER",
			want:  TypeER,
		},
		{
			name:  "Empty string returns default OR",
			value: "",
			want:  TypeOR,
		},
		{
			name:  "Invalid type returns default OR",
			value: "INVALID",
			want:  TypeOR,
		},
		{
			name:  "Lowercase or returns default OR",
			value: "or",
			want:  TypeOR,
		},
		{
			name:  "Mixed case returns default OR",
			value: "Or",
			want:  TypeOR,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewType(tt.value)
			if got != tt.want {
				t.Errorf("NewType() = %v, want %v", got, tt.want)
			}
		})
	}
}
