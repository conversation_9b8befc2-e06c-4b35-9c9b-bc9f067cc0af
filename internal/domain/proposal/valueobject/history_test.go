package valueobject

import (
	"reflect"
	"testing"
	"time"
)

func TestHistoryRecord_Fields(t *testing.T) {
	now := time.Now()
	message := "Test message"
	userID := int64(123)

	hr := HistoryRecord{
		ID:         1,
		ProposalID: 100,
		Status:     "draft",
		Message:    &message,
		EventType:  "status_change",
		UserID:     &userID,
		CreatedAt:  now,
	}

	if hr.ID != 1 {
		t.<PERSON>("HistoryRecord.ID = %v, want %v", hr.ID, 1)
	}
	if hr.ProposalID != 100 {
		t.<PERSON><PERSON><PERSON>("HistoryRecord.ProposalID = %v, want %v", hr.ProposalID, 100)
	}
	if hr.Status != "draft" {
		t.<PERSON><PERSON>rf("HistoryRecord.Status = %v, want %v", hr.Status, "draft")
	}
	if hr.Message == nil || *hr.Message != message {
		t.Errorf("HistoryRecord.Message = %v, want %v", hr.Message, &message)
	}
	if hr.EventType != "status_change" {
		t.<PERSON><PERSON><PERSON>("HistoryRecord.EventType = %v, want %v", hr.EventType, "status_change")
	}
	if hr.UserID == nil || *hr.UserID != userID {
		t.<PERSON>rf("HistoryRecord.UserID = %v, want %v", hr.UserID, &userID)
	}
	if !hr.CreatedAt.Equal(now) {
		t.Errorf("HistoryRecord.CreatedAt = %v, want %v", hr.CreatedAt, now)
	}
}

func TestHistoryRecord_NilFields(t *testing.T) {
	now := time.Now()

	hr := HistoryRecord{
		ID:         1,
		ProposalID: 100,
		Status:     "draft",
		Message:    nil,
		EventType:  "status_change",
		UserID:     nil,
		CreatedAt:  now,
	}

	if hr.Message != nil {
		t.Errorf("HistoryRecord.Message = %v, want nil", hr.Message)
	}
	if hr.UserID != nil {
		t.Errorf("HistoryRecord.UserID = %v, want nil", hr.UserID)
	}
}

func TestHistoryRecord_ZeroValues(t *testing.T) {
	var hr HistoryRecord

	if hr.ID != 0 {
		t.Errorf("HistoryRecord.ID = %v, want %v", hr.ID, 0)
	}
	if hr.ProposalID != 0 {
		t.Errorf("HistoryRecord.ProposalID = %v, want %v", hr.ProposalID, 0)
	}
	if hr.Status != "" {
		t.Errorf("HistoryRecord.Status = %v, want empty string", hr.Status)
	}
	if hr.Message != nil {
		t.Errorf("HistoryRecord.Message = %v, want nil", hr.Message)
	}
	if hr.EventType != "" {
		t.Errorf("HistoryRecord.EventType = %v, want empty string", hr.EventType)
	}
	if hr.UserID != nil {
		t.Errorf("HistoryRecord.UserID = %v, want nil", hr.UserID)
	}
	if !hr.CreatedAt.IsZero() {
		t.Errorf("HistoryRecord.CreatedAt = %v, want zero time", hr.CreatedAt)
	}
}

func TestHistoryRecord_Equality(t *testing.T) {
	now := time.Now()
	message := "Test message"
	userID := int64(123)

	hr1 := HistoryRecord{
		ID:         1,
		ProposalID: 100,
		Status:     "draft",
		Message:    &message,
		EventType:  "status_change",
		UserID:     &userID,
		CreatedAt:  now,
	}

	hr2 := HistoryRecord{
		ID:         1,
		ProposalID: 100,
		Status:     "draft",
		Message:    &message,
		EventType:  "status_change",
		UserID:     &userID,
		CreatedAt:  now,
	}

	if !reflect.DeepEqual(hr1, hr2) {
		t.Errorf("HistoryRecord structs should be equal")
	}
}
