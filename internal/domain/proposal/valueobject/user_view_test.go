package valueobject

import (
	"reflect"
	"testing"
	"time"
)

func TestUserView_Fields(t *testing.T) {
	now := time.Now()

	uv := UserView{
		ID:         1,
		ProposalID: 100,
		UserID:     200,
		ViewedAt:   now,
	}

	if uv.ID != 1 {
		t.<PERSON><PERSON><PERSON>("UserView.ID = %v, want %v", uv.ID, 1)
	}
	if uv.ProposalID != 100 {
		t.<PERSON><PERSON>("UserView.ProposalID = %v, want %v", uv.ProposalID, 100)
	}
	if uv.UserID != 200 {
		t.Errorf("UserView.UserID = %v, want %v", uv.UserID, 200)
	}
	if !uv.ViewedAt.Equal(now) {
		t.<PERSON><PERSON>("UserView.ViewedAt = %v, want %v", uv.ViewedAt, now)
	}
}

func TestUserView_ZeroValues(t *testing.T) {
	var uv UserView

	if uv.ID != 0 {
		t.<PERSON>("UserView.ID = %v, want %v", uv.ID, 0)
	}
	if uv.ProposalID != 0 {
		t.<PERSON><PERSON><PERSON>("UserView.ProposalID = %v, want %v", uv.ProposalID, 0)
	}
	if uv.UserID != 0 {
		t.Errorf("UserView.UserID = %v, want %v", uv.UserID, 0)
	}
	if !uv.ViewedAt.IsZero() {
		t.Errorf("UserView.ViewedAt = %v, want zero time", uv.ViewedAt)
	}
}

func TestUserView_Equality(t *testing.T) {
	now := time.Now()

	uv1 := UserView{
		ID:         1,
		ProposalID: 100,
		UserID:     200,
		ViewedAt:   now,
	}

	uv2 := UserView{
		ID:         1,
		ProposalID: 100,
		UserID:     200,
		ViewedAt:   now,
	}

	if !reflect.DeepEqual(uv1, uv2) {
		t.Errorf("UserView structs should be equal")
	}
}

func TestUserView_DifferentValues(t *testing.T) {
	now := time.Now()
	later := now.Add(time.Hour)

	uv1 := UserView{
		ID:         1,
		ProposalID: 100,
		UserID:     200,
		ViewedAt:   now,
	}

	uv2 := UserView{
		ID:         2,
		ProposalID: 101,
		UserID:     201,
		ViewedAt:   later,
	}

	if reflect.DeepEqual(uv1, uv2) {
		t.Errorf("UserView structs should not be equal")
	}
}
