package valueobject

import (
	"encoding/json"
	"reflect"
	"testing"
	"time"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
)

func TestProposalFull_Fields(t *testing.T) {
	now := time.Now()
	proposal := proposalentity.Proposal{
		ID:        1,
		ProductID: 100,
		Type:      "OR",
		Status:    "draft",
		CreatorID: 200,
		CreatedAt: now,
		UpdatedAt: now,
	}

	data := []byte(`{"test": "data"}`)

	pf := ProposalFull{
		Proposal: proposal,
		Data:     data,
	}

	if pf.ID != 1 {
		t.Errorf("ProposalFull.ID = %v, want %v", pf.ID, 1)
	}
	if pf.ProductID != 100 {
		t.<PERSON><PERSON>rf("ProposalFull.ProductID = %v, want %v", pf.ProductID, 100)
	}
	if pf.Type != "OR" {
		t.<PERSON>rrorf("ProposalFull.Type = %v, want %v", pf.Type, "OR")
	}
	if !reflect.DeepEqual(pf.Data, data) {
		t.<PERSON>rrorf("ProposalFull.Data = %v, want %v", pf.Data, data)
	}
}

func TestStandsData_Fields(t *testing.T) {
	staticSections := json.RawMessage(`{"section1": "value1"}`)
	variables := json.RawMessage(`{"var1": "value1"}`)
	catalog := []json.RawMessage{
		json.RawMessage(`{"item1": "value1"}`),
		json.RawMessage(`{"item2": "value2"}`),
	}

	sd := StandsData{
		GeneralStand: GeneralStand{
			StaticSections: staticSections,
			Variables:      variables,
		},
		Catalog: catalog,
	}

	if !reflect.DeepEqual(sd.StaticSections, staticSections) {
		t.Errorf("StandsData.StaticSections = %v, want %v", sd.StaticSections, staticSections)
	}
	if !reflect.DeepEqual(sd.Variables, variables) {
		t.Errorf("StandsData.Variables = %v, want %v", sd.Variables, variables)
	}
	if len(sd.Catalog) != 2 {
		t.Errorf("len(StandsData.Catalog) = %v, want %v", len(sd.Catalog), 2)
	}
	if !reflect.DeepEqual(sd.Catalog[0], catalog[0]) {
		t.Errorf("StandsData.Catalog[0] = %v, want %v", sd.Catalog[0], catalog[0])
	}
}

func TestGeneralStand_Fields(t *testing.T) {
	staticSections := json.RawMessage(`{"section1": "value1"}`)
	variables := json.RawMessage(`{"var1": "value1"}`)

	gs := GeneralStand{
		StaticSections: staticSections,
		Variables:      variables,
	}

	if !reflect.DeepEqual(gs.StaticSections, staticSections) {
		t.Errorf("GeneralStand.StaticSections = %v, want %v", gs.StaticSections, staticSections)
	}
	if !reflect.DeepEqual(gs.Variables, variables) {
		t.Errorf("GeneralStand.Variables = %v, want %v", gs.Variables, variables)
	}
}

func TestValues_Fields(t *testing.T) {
	values := Values{
		json.RawMessage(`{"value1": "test1"}`),
		json.RawMessage(`{"value2": "test2"}`),
	}

	if len(values) != 2 {
		t.Errorf("len(Values) = %v, want %v", len(values), 2)
	}

	expected1 := json.RawMessage(`{"value1": "test1"}`)
	expected2 := json.RawMessage(`{"value2": "test2"}`)

	if !reflect.DeepEqual(values[0], expected1) {
		t.Errorf("Values[0] = %v, want %v", values[0], expected1)
	}
	if !reflect.DeepEqual(values[1], expected2) {
		t.Errorf("Values[1] = %v, want %v", values[1], expected2)
	}
}
