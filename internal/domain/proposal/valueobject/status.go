package valueobject

type status string

const (
	statusDraft      status = "draft"
	statusArchive    status = "archive"
	statusOnApproval status = "on_approval"
	statusRejected   status = "rejected"
	statusApproved   status = "approved"
	statusEmpty      status = ""
)

func (s status) String() string {
	return string(s)
}

func (s status) IsValid() bool {
	switch s {
	case statusDraft,
		statusArchive,
		statusOnApproval,
		statusRejected,
		statusApproved:
		return true
	default:
		return false
	}
}

func NewStatus(value string) status {
	status := status(value)
	if status != statusEmpty && status.IsValid() {
		return status
	}
	return statusDraft
}
