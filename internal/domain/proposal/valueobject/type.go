package valueobject

type Type string

const (
	TypeOR    Type = "OR" // default type
	TypeER    Type = "ER"
	TypeEmpty Type = ""
)

func (t Type) String() string {
	return string(t)
}

func (t Type) IsValid() bool {
	switch t {
	case TypeOR, TypeER:
		return true
	}
	return false
}

func NewType(value string) Type {
	t := Type(value)
	if t != TypeEmpty && t.IsValid() {
		return t
	}
	return TypeOR
}
