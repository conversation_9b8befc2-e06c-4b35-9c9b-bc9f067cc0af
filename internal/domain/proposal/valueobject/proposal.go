package valueobject

import (
	"encoding/json"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
)

type ProposalFull struct {
	proposalentity.Proposal
	Data []byte
}

type StandsData struct {
	GeneralStand
	Catalog []json.RawMessage `json:"catalog"`
}

type GeneralStand struct {
	StaticSections json.RawMessage `json:"staticSections"`
	Variables      json.RawMessage `json:"variables"`
}

type Values []json.RawMessage
