package valueobject

import (
	"testing"
)

func Test_status_String(t *testing.T) {
	tests := []struct {
		name string
		s    status
		want string
	}{
		{
			name: "statusDraft",
			s:    statusDraft,
			want: "draft",
		},
		{
			name: "statusArchive",
			s:    statusArchive,
			want: "archive",
		},
		{
			name: "statusOnApproval",
			s:    statusOnApproval,
			want: "on_approval",
		},
		{
			name: "statusRejected",
			s:    statusRejected,
			want: "rejected",
		},
		{
			name: "statusApproved",
			s:    statusApproved,
			want: "approved",
		},
		{
			name: "statusEmpty",
			s:    statusEmpty,
			want: "",
		},
		{
			name: "Custom status",
			s:    status("custom"),
			want: "custom",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.s.String()
			if got != tt.want {
				t.Errorf("status.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_status_IsValid(t *testing.T) {
	tests := []struct {
		name string
		s    status
		want bool
	}{
		{
			name: "statusDraft is valid",
			s:    statusDraft,
			want: true,
		},
		{
			name: "statusArchive is valid",
			s:    statusArchive,
			want: true,
		},
		{
			name: "statusOnApproval is valid",
			s:    statusOnApproval,
			want: true,
		},
		{
			name: "statusRejected is valid",
			s:    statusRejected,
			want: true,
		},
		{
			name: "statusApproved is valid",
			s:    statusApproved,
			want: true,
		},
		{
			name: "statusEmpty is invalid",
			s:    statusEmpty,
			want: false,
		},
		{
			name: "Custom status is invalid",
			s:    status("custom"),
			want: false,
		},
		{
			name: "Unknown status is invalid",
			s:    status("unknown"),
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.s.IsValid()
			if got != tt.want {
				t.Errorf("status.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewStatus(t *testing.T) {
	tests := []struct {
		name  string
		value string
		want  status
	}{
		{
			name:  "Valid draft status",
			value: "draft",
			want:  statusDraft,
		},
		{
			name:  "Valid archive status",
			value: "archive",
			want:  statusArchive,
		},
		{
			name:  "Valid on_approval status",
			value: "on_approval",
			want:  statusOnApproval,
		},
		{
			name:  "Valid rejected status",
			value: "rejected",
			want:  statusRejected,
		},
		{
			name:  "Valid approved status",
			value: "approved",
			want:  statusApproved,
		},
		{
			name:  "Empty string returns default draft",
			value: "",
			want:  statusDraft,
		},
		{
			name:  "Invalid status returns default draft",
			value: "invalid",
			want:  statusDraft,
		},
		{
			name:  "Unknown status returns default draft",
			value: "unknown",
			want:  statusDraft,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := NewStatus(tt.value)
			if got != tt.want {
				t.Errorf("NewStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}
