package service

import (
	"context"
	"encoding/json"
	"sort"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	participantrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/repository"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

//go:generate minimock -i ProposalDomainService -o ../mocks/proposal_domain_service_mock.go -s _mock.go
type ProposalDomainService interface {
	Create(data proposalentity.Proposal, values proposalvalueobject.Values) (proposalvalueobject.ProposalFull, error)
	CreateMessage(proposalID int64, message string, userID int64) error
	GetAllAsAdmin() ([]proposalvalueobject.ProposalFull, error)
	GetByID(id int64) (proposalvalueobject.ProposalFull, error)
	GetByProductID(productID int64) ([]proposalentity.Proposal, error)
	GetByProductIDAndProposalID(productID, proposalID int64) (proposalvalueobject.ProposalFull, error)
	GetHistory(proposalID int64) ([]proposalvalueobject.HistoryRecord, error)
	GetUnreadEventsForUser(userID int64) ([]proposalvalueobject.HistoryRecord, error)
	Update(ctx context.Context, data proposalaggregate.ProposalUpdate) (proposalaggregate.Proposal, error)
	UpdateLastViewed(ctx context.Context, data proposalvalueobject.UserView) error
	Delete(data proposalentity.Proposal) error
}

type proposalDomainService struct {
	proposal        proposalrepository.ProposalPrimeDB
	section         proposalrepository.SectionPrimeDB
	userView        proposalrepository.UserViewDB
	proposalHistory proposalrepository.ProposalHistoryDB
	productRepo     productrepository.ProductPrimeDB
	participantRepo participantrepository.ParticipantPrimeDB
}

func NewProposalDomainService(proposal proposalrepository.ProposalPrimeDB,
	section proposalrepository.SectionPrimeDB,
	userView proposalrepository.UserViewDB,
	proposalHistory proposalrepository.ProposalHistoryDB,
	productRepo productrepository.ProductPrimeDB,
	participantRepo participantrepository.ParticipantPrimeDB,
) ProposalDomainService {
	return &proposalDomainService{
		proposal:        proposal,
		section:         section,
		userView:        userView,
		proposalHistory: proposalHistory,
		productRepo:     productRepo,
		participantRepo: participantRepo,
	}
}

func (s *proposalDomainService) Create(data proposalentity.Proposal, values proposalvalueobject.Values) (proposalvalueobject.ProposalFull, error) {
	proposal, err := s.proposal.Create(data, values)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	stands, err := s.section.GetSectionsByProposalID(proposal.ProductID, proposal.ProdPropSeq)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	createdDataBytes, err := getDataBytesFromStands(stands)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	return proposalvalueobject.ProposalFull{
		Proposal: proposal,
		Data:     createdDataBytes,
	}, nil
}

func (s *proposalDomainService) CreateMessage(proposalID int64, message string, userID int64) error {
	return s.proposalHistory.CreateMessage(proposalID, message, userID)
}

func (s *proposalDomainService) GetAllAsAdmin() (ret []proposalvalueobject.ProposalFull, err error) {
	proposals, err := s.proposal.GetAll()
	if err != nil {
		return nil, err
	}

	for _, p := range proposals {
		propFull, err := s.GetByProductIDAndProposalID(p.ProductID, p.ID)
		if err != nil {
			return nil, err
		}

		product, err := s.productRepo.GetByID(p.ProductID)
		if err != nil {
			return nil, err
		}

		productOwners, err := s.participantRepo.GetOwnersByProductID(p.ProductID)
		if err != nil {
			return nil, err
		}
		propFull.ProductOwners = make([]string, 0)
		for _, owner := range productOwners {
			propFull.ProductOwners = append(propFull.ProductOwners, owner.FullName)
		}

		propFull.Product = productentity.Product{
			ID:       product.ID,
			IID:      product.IID,
			TechName: product.TechName,
			Name:     product.Name,
		}

		ret = append(ret, propFull)
	}

	return ret, nil
}

func (s *proposalDomainService) GetByID(id int64) (proposalvalueobject.ProposalFull, error) {
	proposal, err := s.proposal.GetByID(id)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}
	stands, err := s.section.GetSectionsByProposalID(proposal.ProductID, proposal.ProdPropSeq)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	createdDataBytes, err := getDataBytesFromStands(stands)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	return proposalvalueobject.ProposalFull{
		Proposal: proposal,
		Data:     createdDataBytes,
	}, nil
}

func (s *proposalDomainService) GetByProductID(productID int64) ([]proposalentity.Proposal, error) {
	proposals, err := s.proposal.GetByProductID(productID)
	if err != nil {
		return nil, err
	}

	return proposals, nil
}

func (s *proposalDomainService) GetByProductIDAndProposalID(productID, proposalID int64) (proposalvalueobject.ProposalFull, error) {
	proposal, err := s.proposal.GetByProductIDAndProposalID(productID, proposalID)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	stands, err := s.section.GetSectionsByProposalID(proposal.ProductID, proposal.ProdPropSeq)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	createdDataBytes, err := getDataBytesFromStands(stands)
	if err != nil {
		return proposalvalueobject.ProposalFull{}, err
	}

	return proposalvalueobject.ProposalFull{
		Proposal: proposal,
		Data:     createdDataBytes,
	}, nil
}

func (s *proposalDomainService) GetHistory(proposalID int64) ([]proposalvalueobject.HistoryRecord, error) {

	proposal, err := s.proposalHistory.GetByProposalID(proposalID)
	if err != nil {
		return nil, err
	}

	sort.Slice(proposal, func(i, j int) bool {
		return proposal[i].CreatedAt.Before(proposal[j].CreatedAt)
	})

	return proposal, nil
}

func (s *proposalDomainService) GetUnreadEventsForUser(userID int64) ([]proposalvalueobject.HistoryRecord, error) {
	return s.proposalHistory.GetUnreadEventsForUser(userID)
}

func (s *proposalDomainService) Update(ctx context.Context, data proposalaggregate.ProposalUpdate) (proposalaggregate.Proposal, error) {
	return s.proposal.Update(ctx, data)
}

func (s *proposalDomainService) Delete(data proposalentity.Proposal) error {
	return s.proposal.Delete(data)
}

func getDataBytesFromStands(stands []proposalentity.Stand) ([]byte, error) {

	var generalStand valueobject.GeneralStand
	for i, stand := range stands {
		if stand.Num == constants.ProposalGeneralStandNum {
			if err := json.Unmarshal(stands[i].Data, &generalStand); err != nil {
				return nil, err
			}
			break
		}
	}

	createdData := valueobject.StandsData{
		GeneralStand: generalStand,
		Catalog:      make([]json.RawMessage, 0),
	}

	for i := 1; i < len(stands); i++ {
		createdData.Catalog = append(createdData.Catalog, stands[i].Data)
	}

	// Note: json.Marshal error is practically unreachable here.
	// StandsData contains only json.RawMessage fields which always marshal successfully.
	// Error could only occur due to system-level issues (out of memory, etc.).
	createdDataBytes, err := json.Marshal(createdData)
	if err != nil {
		return nil, err
	}

	return createdDataBytes, nil
}
