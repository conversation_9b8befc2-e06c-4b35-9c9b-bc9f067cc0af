package service

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	participantmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/mocks"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	productmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/mocks"
	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/mocks"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

func createProposalMocks(t *testing.T) (*proposalmocks.ProposalPrimeDBMock, *proposalmocks.SectionPrimeDBMock, *proposalmocks.UserViewDBMock, *proposalmocks.ProposalHistoryDBMock, *productmocks.ProductPrimeDBMock, *participantmocks.ParticipantPrimeDBMock) {
	return proposalmocks.NewProposalPrimeDBMock(t),
		proposalmocks.NewSectionPrimeDBMock(t),
		proposalmocks.NewUserViewDBMock(t),
		proposalmocks.NewProposalHistoryDBMock(t),
		productmocks.NewProductPrimeDBMock(t),
		participantmocks.NewParticipantPrimeDBMock(t)
}

func TestNewProposalDomainService(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)

	service := NewProposalDomainService(
		proposalRepo,
		sectionRepo,
		userViewRepo,
		historyRepo,
		productRepo,
		participantRepo,
	)

	require.NotNil(t, service)

	domainService, ok := service.(*proposalDomainService)
	require.True(t, ok)

	require.Equal(t, proposalRepo, domainService.proposal)
	require.Equal(t, sectionRepo, domainService.section)
	require.Equal(t, userViewRepo, domainService.userView)
	require.Equal(t, historyRepo, domainService.proposalHistory)
	require.Equal(t, productRepo, domainService.productRepo)
	require.Equal(t, participantRepo, domainService.participantRepo)
}

func TestProposalDomainService_Create_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	now := time.Now()
	inputProposal := proposalentity.Proposal{
		ProductID: 100,
		Type:      "OR",
		Status:    "draft",
		CreatedAt: now,
	}

	createdProposal := proposalentity.Proposal{
		ID:          1,
		ProductID:   100,
		ProdPropSeq: 1,
		Type:        "OR",
		Status:      "draft",
		CreatedAt:   now,
	}

	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{},"variables":{}}`),
		},
		{
			ID:        2,
			ProductID: 100,
			SeqNum:    1,
			Num:       1,
			Data:      json.RawMessage(`{"field":"value"}`),
		},
	}

	values := proposalvalueobject.Values{
		json.RawMessage(`{"test": "data"}`),
	}

	expectedData, _ := json.Marshal(proposalvalueobject.StandsData{
		GeneralStand: proposalvalueobject.GeneralStand{
			StaticSections: json.RawMessage(`{}`),
			Variables:      json.RawMessage(`{}`),
		},
		Catalog: []json.RawMessage{
			json.RawMessage(`{"field":"value"}`),
		},
	})

	proposalRepo.CreateMock.Expect(inputProposal, values).Return(createdProposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(createdProposal.ProductID, createdProposal.ProdPropSeq).Return(stands, nil)

	result, err := service.Create(inputProposal, values)

	require.NoError(t, err)
	require.Equal(t, createdProposal, result.Proposal)
	require.Equal(t, expectedData, result.Data)
}

func TestProposalDomainService_Create_CreateError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	inputProposal := proposalentity.Proposal{ProductID: 100}
	values := proposalvalueobject.Values{}
	expectedError := errors.New("create error")

	proposalRepo.CreateMock.Expect(inputProposal, values).Return(proposalentity.Proposal{}, expectedError)

	result, err := service.Create(inputProposal, values)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
}

func TestProposalDomainService_Create_GetSectionsError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	inputProposal := proposalentity.Proposal{ProductID: 100}
	values := proposalvalueobject.Values{}
	createdProposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	expectedError := errors.New("sections error")

	proposalRepo.CreateMock.Expect(inputProposal, values).Return(createdProposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(createdProposal.ProductID, createdProposal.ProdPropSeq).Return(nil, expectedError)

	result, err := service.Create(inputProposal, values)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
}

func TestProposalDomainService_Create_GetDataBytesError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	inputProposal := proposalentity.Proposal{ProductID: 100}
	values := proposalvalueobject.Values{}
	createdProposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	// Create stands with invalid JSON that will cause getDataBytesFromStands to fail
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`invalid json data`),
		},
	}

	proposalRepo.CreateMock.Expect(inputProposal, values).Return(createdProposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(createdProposal.ProductID, createdProposal.ProdPropSeq).Return(stands, nil)

	result, err := service.Create(inputProposal, values)

	require.Error(t, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
	require.Equal(t, uint64(1), proposalRepo.CreateAfterCounter())
	require.Equal(t, uint64(1), sectionRepo.GetSectionsByProposalIDAfterCounter())
}

func TestProposalDomainService_GetAllAsAdmin_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{
		ID: 1, ProductID: 100, ProdPropSeq: 1, Type: "OR", Status: "draft",
	}
	proposals := []proposalentity.Proposal{proposal}

	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{},"variables":{}}`),
		},
	}

	product := productentity.Product{ID: 100, IID: "prod-100", TechName: "Product100", Name: "Product 100"}
	owners := []productentity.Owner{
		{ProductID: 100, ParticipantID: 1, UserID: 1, FullName: "Owner 1", Email: "<EMAIL>"},
	}

	proposalRepo.GetAllMock.Expect().Return(proposals, nil)
	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)
	productRepo.GetByIDMock.Expect(int64(100)).Return(product, nil)
	participantRepo.GetOwnersByProductIDMock.Expect(int64(100)).Return(owners, nil)

	result, err := service.GetAllAsAdmin()

	require.NoError(t, err)
	require.Len(t, result, 1)
	require.Equal(t, []string{"Owner 1"}, result[0].ProductOwners)
	require.Equal(t, product, result[0].Product)
}

func TestProposalDomainService_GetAllAsAdmin_GetAllError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	expectedError := errors.New("get all error")

	proposalRepo.GetAllMock.Expect().Return(nil, expectedError)

	result, err := service.GetAllAsAdmin()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
}

func TestProposalDomainService_GetAllAsAdmin_GetByProductIDAndProposalIDError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{
		ID: 1, ProductID: 100, ProdPropSeq: 1, Type: "OR", Status: "draft",
	}
	proposals := []proposalentity.Proposal{proposal}
	expectedError := errors.New("get proposal full error")

	proposalRepo.GetAllMock.Expect().Return(proposals, nil)
	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposalentity.Proposal{}, expectedError)

	result, err := service.GetAllAsAdmin()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), proposalRepo.GetAllAfterCounter())
	require.Equal(t, uint64(1), proposalRepo.GetByProductIDAndProposalIDAfterCounter())
}

func TestProposalDomainService_GetAllAsAdmin_GetProductError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{
		ID: 1, ProductID: 100, ProdPropSeq: 1, Type: "OR", Status: "draft",
	}
	proposals := []proposalentity.Proposal{proposal}
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{},"variables":{}}`),
		},
	}
	expectedError := errors.New("product not found")

	proposalRepo.GetAllMock.Expect().Return(proposals, nil)
	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)
	productRepo.GetByIDMock.Expect(int64(100)).Return(productentity.Product{}, expectedError)

	result, err := service.GetAllAsAdmin()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), proposalRepo.GetAllAfterCounter())
	require.Equal(t, uint64(1), proposalRepo.GetByProductIDAndProposalIDAfterCounter())
	require.Equal(t, uint64(1), sectionRepo.GetSectionsByProposalIDAfterCounter())
	require.Equal(t, uint64(1), productRepo.GetByIDAfterCounter())
}

func TestProposalDomainService_GetAllAsAdmin_GetOwnersError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{
		ID: 1, ProductID: 100, ProdPropSeq: 1, Type: "OR", Status: "draft",
	}
	proposals := []proposalentity.Proposal{proposal}
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{},"variables":{}}`),
		},
	}
	product := productentity.Product{ID: 100, IID: "prod-100", TechName: "Product100", Name: "Product 100"}
	expectedError := errors.New("owners not found")

	proposalRepo.GetAllMock.Expect().Return(proposals, nil)
	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)
	productRepo.GetByIDMock.Expect(int64(100)).Return(product, nil)
	participantRepo.GetOwnersByProductIDMock.Expect(int64(100)).Return(nil, expectedError)

	result, err := service.GetAllAsAdmin()

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), proposalRepo.GetAllAfterCounter())
	require.Equal(t, uint64(1), proposalRepo.GetByProductIDAndProposalIDAfterCounter())
	require.Equal(t, uint64(1), sectionRepo.GetSectionsByProposalIDAfterCounter())
	require.Equal(t, uint64(1), productRepo.GetByIDAfterCounter())
	require.Equal(t, uint64(1), participantRepo.GetOwnersByProductIDAfterCounter())
}

func TestProposalDomainService_GetByID_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposalID := int64(1)
	productID := int64(100)

	proposal := proposalentity.Proposal{
		ID:          proposalID,
		ProductID:   productID,
		ProdPropSeq: 1,
	}
	proposalRepo.GetByIDMock.Expect(int64(1)).Return(proposal, nil)
	stands := make([]proposalentity.Stand, 0)
	sectionRepo.GetSectionsByProposalIDMock.Expect(productID, proposalID).Return(stands, nil)
	proposalFullData, _ := getDataBytesFromStands(stands)
	result, err := service.GetByID(int64(1))
	proposalFull := proposalvalueobject.ProposalFull{
		Proposal: proposalentity.Proposal{
			ID:          proposalID,
			ProductID:   productID,
			ProdPropSeq: 1,
		},
		Data: proposalFullData,
	}
	require.NoError(t, err)
	require.Equal(t, proposalFull, result)
}

func TestProposalDomainService_GetByID_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	expectedError := errors.New("not found")

	proposalRepo.GetByIDMock.Expect(int64(1)).Return(proposalentity.Proposal{}, expectedError)

	result, err := service.GetByID(1)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
}

func TestProposalDomainService_GetByID_GetSectionsError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposalID := int64(1)
	productID := int64(100)
	proposal := proposalentity.Proposal{
		ID:          proposalID,
		ProductID:   productID,
		ProdPropSeq: 1,
	}
	expectedError := errors.New("sections database error")

	proposalRepo.GetByIDMock.Expect(proposalID).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(productID, int64(1)).Return(nil, expectedError)

	result, err := service.GetByID(proposalID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
	require.Equal(t, uint64(1), proposalRepo.GetByIDAfterCounter())
	require.Equal(t, uint64(1), sectionRepo.GetSectionsByProposalIDAfterCounter())
}

func TestProposalDomainService_GetByID_GetDataBytesError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposalID := int64(1)
	productID := int64(100)
	proposal := proposalentity.Proposal{
		ID:          proposalID,
		ProductID:   productID,
		ProdPropSeq: 1,
	}
	// Create stands with invalid JSON that will cause getDataBytesFromStands to fail
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: productID,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`invalid json data`),
		},
	}

	proposalRepo.GetByIDMock.Expect(proposalID).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(productID, int64(1)).Return(stands, nil)

	result, err := service.GetByID(proposalID)

	require.Error(t, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
	require.Equal(t, uint64(1), proposalRepo.GetByIDAfterCounter())
	require.Equal(t, uint64(1), sectionRepo.GetSectionsByProposalIDAfterCounter())
}

func TestProposalDomainService_GetByProductID_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposals := []proposalentity.Proposal{
		{ID: 1, ProductID: 100},
		{ID: 2, ProductID: 100},
	}

	proposalRepo.GetByProductIDMock.Expect(int64(100)).Return(proposals, nil)

	result, err := service.GetByProductID(100)

	require.NoError(t, err)
	require.Equal(t, proposals, result)
}

func TestProposalDomainService_GetByProductID_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	expectedError := errors.New("db error")

	proposalRepo.GetByProductIDMock.Expect(int64(100)).Return(nil, expectedError)

	result, err := service.GetByProductID(100)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
}

func TestProposalDomainService_GetByProductIDAndProposalID_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{},"variables":{}}`),
		},
	}

	expectedData, _ := json.Marshal(proposalvalueobject.StandsData{
		GeneralStand: proposalvalueobject.GeneralStand{
			StaticSections: json.RawMessage(`{}`),
			Variables:      json.RawMessage(`{}`),
		},
		Catalog: []json.RawMessage{},
	})

	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)

	result, err := service.GetByProductIDAndProposalID(100, 1)

	require.NoError(t, err)
	require.Equal(t, proposal, result.Proposal)
	require.Equal(t, expectedData, result.Data)
}

func TestProposalDomainService_GetByProductIDAndProposalID_GetProposalError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	expectedError := errors.New("proposal not found")

	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposalentity.Proposal{}, expectedError)

	result, err := service.GetByProductIDAndProposalID(100, 1)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
}

func TestProposalDomainService_GetByProductIDAndProposalID_GetSectionsError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	expectedError := errors.New("sections error")

	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(nil, expectedError)

	result, err := service.GetByProductIDAndProposalID(100, 1)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
}

func TestProposalDomainService_GetHistory_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	history := []proposalvalueobject.HistoryRecord{
		{ID: 1, ProposalID: 1, CreatedAt: time.Now().Add(-time.Hour)},
		{ID: 2, ProposalID: 1, CreatedAt: time.Now()},
	}

	historyRepo.GetByProposalIDMock.Expect(int64(1)).Return(history, nil)

	result, err := service.GetHistory(1)

	require.NoError(t, err)
	require.Len(t, result, 2)
	require.True(t, result[0].CreatedAt.Before(result[1].CreatedAt))
}

func TestProposalDomainService_GetHistory_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	expectedError := errors.New("history error")

	historyRepo.GetByProposalIDMock.Expect(int64(1)).Return(nil, expectedError)

	result, err := service.GetHistory(1)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
}

func TestProposalDomainService_Update_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	ctx := context.Background()
	updateData := proposalaggregate.ProposalUpdate{
		Proposal: proposalentity.Proposal{ID: 1, Status: "approved"},
	}
	updatedProposal := proposalaggregate.Proposal{
		Proposal: proposalentity.Proposal{ID: 1, Status: "approved"},
	}

	proposalRepo.UpdateMock.Expect(ctx, updateData).Return(updatedProposal, nil)

	result, err := service.Update(ctx, updateData)

	require.NoError(t, err)
	require.Equal(t, updatedProposal, result)
}

func TestProposalDomainService_Update_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	ctx := context.Background()
	updateData := proposalaggregate.ProposalUpdate{
		Proposal: proposalentity.Proposal{ID: 1},
	}
	expectedError := errors.New("update error")

	proposalRepo.UpdateMock.Expect(ctx, updateData).Return(proposalaggregate.Proposal{}, expectedError)

	result, err := service.Update(ctx, updateData)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, proposalaggregate.Proposal{}, result)
}

func TestProposalDomainService_UpdateLastViewed_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	ctx := context.Background()
	userView := proposalvalueobject.UserView{ProposalID: 1, UserID: 100}

	userViewRepo.UpdateMock.Expect(userView).Return(userView, nil)

	err := service.UpdateLastViewed(ctx, userView)

	require.NoError(t, err)
}

func TestProposalDomainService_UpdateLastViewed_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	ctx := context.Background()
	userView := proposalvalueobject.UserView{ProposalID: 1, UserID: 100}
	expectedError := errors.New("update view error")

	userViewRepo.UpdateMock.Expect(userView).Return(proposalvalueobject.UserView{}, expectedError)

	err := service.UpdateLastViewed(ctx, userView)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestProposalDomainService_Delete_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{ID: 1, ProductID: 100}

	proposalRepo.DeleteMock.Expect(proposal).Return(nil)

	err := service.Delete(proposal)

	require.NoError(t, err)
}

func TestProposalDomainService_Delete_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{ID: 1, ProductID: 100}
	expectedError := errors.New("delete error")

	proposalRepo.DeleteMock.Expect(proposal).Return(expectedError)

	err := service.Delete(proposal)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
}

func TestProposalDomainService_Create_WithNilValues(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	inputProposal := proposalentity.Proposal{ProductID: 100}
	var values proposalvalueobject.Values
	createdProposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{},"variables":{}}`),
		},
	}

	proposalRepo.CreateMock.Expect(inputProposal, values).Return(createdProposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)

	result, err := service.Create(inputProposal, values)

	require.NoError(t, err)
	require.Equal(t, createdProposal, result.Proposal)
	require.NotNil(t, result.Data)
}

func TestProposalDomainService_GetByProductIDAndProposalID_WithEmptyStands(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	var stands []proposalentity.Stand

	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)

	result, err := service.GetByProductIDAndProposalID(100, 1)

	require.NoError(t, err)
	require.Equal(t, proposal, result.Proposal)
	require.NotNil(t, result.Data)
}

func TestProposalDomainService_GetAllAsAdmin_WithEmptyProposals(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	var proposals []proposalentity.Proposal

	proposalRepo.GetAllMock.Expect().Return(proposals, nil)

	result, err := service.GetAllAsAdmin()

	require.NoError(t, err)
	require.Len(t, result, 0)
}

func TestProposalDomainService_GetHistory_WithEmptyHistory(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	var history []proposalvalueobject.HistoryRecord

	historyRepo.GetByProposalIDMock.Expect(int64(1)).Return(history, nil)

	result, err := service.GetHistory(1)

	require.NoError(t, err)
	require.Len(t, result, 0)
}

func TestGetDataBytesFromStands_WithValidData(t *testing.T) {
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`{"staticSections":{"field":"value"},"variables":{"var":"test"}}`),
		},
		{
			ID:        2,
			ProductID: 100,
			SeqNum:    1,
			Num:       1,
			Data:      json.RawMessage(`{"catalog1":"data1"}`),
		},
		{
			ID:        3,
			ProductID: 100,
			SeqNum:    1,
			Num:       2,
			Data:      json.RawMessage(`{"catalog2":"data2"}`),
		},
	}

	result, err := getDataBytesFromStands(stands)

	require.NoError(t, err)
	require.NotNil(t, result)

	var standsData proposalvalueobject.StandsData
	err = json.Unmarshal(result, &standsData)
	require.NoError(t, err)

	require.Equal(t, json.RawMessage(`{"field":"value"}`), standsData.StaticSections)
	require.Equal(t, json.RawMessage(`{"var":"test"}`), standsData.Variables)
	require.Len(t, standsData.Catalog, 2)
	require.Equal(t, json.RawMessage(`{"catalog1":"data1"}`), standsData.Catalog[0])
	require.Equal(t, json.RawMessage(`{"catalog2":"data2"}`), standsData.Catalog[1])
}

func TestGetDataBytesFromStands_WithEmptyStands(t *testing.T) {
	var stands []proposalentity.Stand

	result, err := getDataBytesFromStands(stands)

	require.NoError(t, err)
	require.NotNil(t, result)

	var standsData proposalvalueobject.StandsData
	err = json.Unmarshal(result, &standsData)
	require.NoError(t, err)

	require.Equal(t, json.RawMessage(`null`), standsData.StaticSections)
	require.Equal(t, json.RawMessage(`null`), standsData.Variables)
	require.Len(t, standsData.Catalog, 0)
}

func TestGetDataBytesFromStands_WithInvalidJSON(t *testing.T) {
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`invalid json`),
		},
	}

	result, err := getDataBytesFromStands(stands)

	require.Error(t, err)
	require.Nil(t, result)
}

func TestProposalDomainService_GetUnreadEventsForUser_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	userID := int64(123)
	expectedEvents := []proposalvalueobject.HistoryRecord{
		{
			ID:         1,
			ProposalID: 100,
			Status:     "draft",
			Message:    stringPtr("New proposal created"),
			EventType:  "status_change",
			UserID:     &userID,
			CreatedAt:  time.Now().Add(-time.Hour),
		},
		{
			ID:         2,
			ProposalID: 101,
			Status:     "approved",
			Message:    nil,
			EventType:  "status_change",
			UserID:     &userID,
			CreatedAt:  time.Now(),
		},
	}

	historyRepo.GetUnreadEventsForUserMock.Expect(userID).Return(expectedEvents, nil)

	result, err := service.GetUnreadEventsForUser(userID)

	require.NoError(t, err)
	require.Equal(t, expectedEvents, result)
	require.Equal(t, uint64(1), historyRepo.GetUnreadEventsForUserAfterCounter())
}

func TestProposalDomainService_GetUnreadEventsForUser_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	userID := int64(123)
	expectedError := errors.New("database connection error")

	historyRepo.GetUnreadEventsForUserMock.Expect(userID).Return(nil, expectedError)

	result, err := service.GetUnreadEventsForUser(userID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Equal(t, uint64(1), historyRepo.GetUnreadEventsForUserAfterCounter())
}

func TestProposalDomainService_GetUnreadEventsForUser_EmptyResult(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	userID := int64(123)
	var expectedEvents []proposalvalueobject.HistoryRecord

	historyRepo.GetUnreadEventsForUserMock.Expect(userID).Return(expectedEvents, nil)

	result, err := service.GetUnreadEventsForUser(userID)

	require.NoError(t, err)
	require.Equal(t, expectedEvents, result)
	require.Len(t, result, 0)
	require.Equal(t, uint64(1), historyRepo.GetUnreadEventsForUserAfterCounter())
}

func TestProposalDomainService_CreateMessage_Success(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposalID := int64(100)
	message := "Test message for proposal"
	userID := int64(123)

	historyRepo.CreateMessageMock.Expect(proposalID, message, userID).Return(nil)

	err := service.CreateMessage(proposalID, message, userID)

	require.NoError(t, err)
	require.Equal(t, uint64(1), historyRepo.CreateMessageAfterCounter())
}

func TestProposalDomainService_CreateMessage_Error(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposalID := int64(100)
	message := "Test message for proposal"
	userID := int64(123)
	expectedError := errors.New("failed to create message")

	historyRepo.CreateMessageMock.Expect(proposalID, message, userID).Return(expectedError)

	err := service.CreateMessage(proposalID, message, userID)

	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Equal(t, uint64(1), historyRepo.CreateMessageAfterCounter())
}

func TestProposalDomainService_CreateMessage_WithEmptyMessage(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposalID := int64(100)
	message := ""
	userID := int64(123)

	historyRepo.CreateMessageMock.Expect(proposalID, message, userID).Return(nil)

	err := service.CreateMessage(proposalID, message, userID)

	require.NoError(t, err)
	require.Equal(t, uint64(1), historyRepo.CreateMessageAfterCounter())
}

func TestProposalDomainService_GetByProductIDAndProposalID_GetDataBytesError(t *testing.T) {
	proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo := createProposalMocks(t)
	service := NewProposalDomainService(proposalRepo, sectionRepo, userViewRepo, historyRepo, productRepo, participantRepo)

	proposal := proposalentity.Proposal{ID: 1, ProductID: 100, ProdPropSeq: 1}
	// Create stands with invalid JSON that will cause getDataBytesFromStands to fail
	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Num:       constants.ProposalGeneralStandNum,
			Data:      json.RawMessage(`invalid json data`),
		},
	}

	proposalRepo.GetByProductIDAndProposalIDMock.Expect(int64(100), int64(1)).Return(proposal, nil)
	sectionRepo.GetSectionsByProposalIDMock.Expect(int64(100), int64(1)).Return(stands, nil)

	result, err := service.GetByProductIDAndProposalID(100, 1)

	require.Error(t, err)
	require.Equal(t, proposalvalueobject.ProposalFull{}, result)
	require.Equal(t, uint64(1), proposalRepo.GetByProductIDAndProposalIDAfterCounter())
	require.Equal(t, uint64(1), sectionRepo.GetSectionsByProposalIDAfterCounter())
}

func stringPtr(s string) *string {
	return &s
}
