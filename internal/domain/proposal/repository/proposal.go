package repository

import (
	"context"

	proposalaggregate "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/aggregate"
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

//go:generate minimock -i ProposalPrimeDB -o ../mocks/proposal_prime_db_mock.go -s _mock.go
type ProposalPrimeDB interface {
	Create(proposal proposalentity.Proposal, values proposalvalueobject.Values) (proposalentity.Proposal, error)
	GetAll() ([]proposalentity.Proposal, error)
	GetByID(id int64) (proposalentity.Proposal, error)
	GetByProductID(productID int64) ([]proposalentity.Proposal, error)
	GetByProductIDAndProposalID(productID, proposalID int64) (proposalentity.Proposal, error)
	Update(ctx context.Context, proposal proposalaggregate.ProposalUpdate) (proposalaggregate.Proposal, error)
	Delete(proposal proposalentity.Proposal) error
}

//go:generate minimock -i ProposalCache -o ../mocks/proposal_cache_mock.go -s _mock.go
type ProposalCache interface {
	GetByID(id int64) (proposalentity.Proposal, error)
	GetByProductID(productID int64) ([]proposalentity.Proposal, error)
	GetByProductIDAndProposalID(productID, proposalID int64) (proposalentity.Proposal, error)
	Set(proposal proposalentity.Proposal) error
}
