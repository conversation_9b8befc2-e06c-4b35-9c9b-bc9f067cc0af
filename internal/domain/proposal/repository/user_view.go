package repository

import (
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

//go:generate minimock -i UserViewDB -o ../mocks/user_view_db_mock.go -s _mock.go
type UserViewDB interface {
	GetByProposalIDAndUserID(proposalID int64, userID int64) (voproposal.UserView, error)
	Update(data voproposal.UserView) (voproposal.UserView, error)
}

type UserViewCache interface {
	GetByProposalIDAndUserID(proposalID int64, userID int64) (voproposal.UserView, error)
	Update(data voproposal.UserView) (voproposal.UserView, error)
}
