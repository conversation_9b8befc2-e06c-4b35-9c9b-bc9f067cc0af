package repository

import (
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

//go:generate minimock -i ProposalHistoryDB -o ../mocks/proposal_history_db_mock.go -s _mock.go
type ProposalHistoryDB interface {
	CreateMessage(proposalID int64, message string, userID int64) error
	GetByProposalID(proposalID int64) ([]voproposal.HistoryRecord, error)
	GetByUserID(userID int64) ([]voproposal.HistoryRecord, error)
	GetUnreadEventsForUser(userID int64) ([]voproposal.HistoryRecord, error)
}

type ProposalHistoryCache interface {
	GetByProposalID(proposalID int64) ([]voproposal.HistoryRecord, error)
}
