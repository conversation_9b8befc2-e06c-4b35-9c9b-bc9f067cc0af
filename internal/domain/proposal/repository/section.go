package repository

import (
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	proposalvalueobject "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

//go:generate minimock -i SectionPrimeDB -o ../mocks/section_prime_db_mock.go -s _mock.go
type SectionPrimeDB interface {
	GetSectionsByProposalID(productID int64, proposalID int64) ([]proposalentity.Stand, error)
	UpdateSectionsByProposalID(productID int64, proposalID int64, sections proposalvalueobject.Values) ([]proposalentity.Stand, error)
}

//go:generate minimock -i SectionCache -o ../mocks/section_cache_mock.go -s _mock.go
type SectionCache interface {
	GetSectionsByProposalID(productID int64, proposalID int64) ([]proposalentity.Stand, error)
	UpdateSectionsByProposalID(productID int64, proposalID int64, sections proposalvalueobject.Values) ([]proposalentity.Stand, error)
	Set(section proposalentity.Stand) error
}
