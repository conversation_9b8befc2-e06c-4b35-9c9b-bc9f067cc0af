package aggregate

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
)

func TestNewProposalSend(t *testing.T) {
	now := time.Now()
	proposal := proposalentity.Proposal{
		ID:        1,
		ProductID: 100,
		Type:      "OR",
		Status:    "draft",
		CreatedAt: now,
	}

	message := "Test message"

	proposalSend := NewProposalSend(proposal, &message)

	require.Equal(t, proposal, proposalSend.Proposal)
	require.NotNil(t, proposalSend.Message)
	require.Equal(t, message, *proposalSend.Message)
}

func TestNewProposalSend_NilMessage(t *testing.T) {
	proposal := proposalentity.Proposal{
		ID:     1,
		Status: "draft",
	}

	proposalSend := NewProposalSend(proposal, nil)

	require.Equal(t, proposal, proposalSend.Proposal)
	require.Nil(t, proposalSend.Message)
}

func TestNewProposalSend_EmptyMessage(t *testing.T) {
	proposal := proposalentity.Proposal{
		ID:     1,
		Status: "draft",
	}

	message := ""
	proposalSend := NewProposalSend(proposal, &message)

	require.Equal(t, proposal, proposalSend.Proposal)
	require.NotNil(t, proposalSend.Message)
	require.Equal(t, message, *proposalSend.Message)
}

func TestNewProposalSend_ZeroProposal(t *testing.T) {
	var proposal proposalentity.Proposal
	message := "Test message"

	proposalSend := NewProposalSend(proposal, &message)

	require.Equal(t, proposal, proposalSend.Proposal)
	require.Equal(t, int64(0), proposalSend.Proposal.ID)
	require.NotNil(t, proposalSend.Message)
	require.Equal(t, message, *proposalSend.Message)
}

func TestProposalSend_Fields(t *testing.T) {
	now := time.Now()
	proposal := proposalentity.Proposal{
		ID:        123,
		ProductID: 456,
		Price:     99.99,
		Type:      "ER",
		Status:    "approved",
		CreatorID: 789,
		CreatedAt: now,
		UpdatedAt: now,
	}

	message := "Important message"
	proposalSend := NewProposalSend(proposal, &message)

	require.Equal(t, int64(123), proposalSend.Proposal.ID)
	require.Equal(t, int64(456), proposalSend.Proposal.ProductID)
	require.Equal(t, 99.99, proposalSend.Proposal.Price)
	require.Equal(t, "ER", proposalSend.Proposal.Type)
	require.Equal(t, "approved", proposalSend.Proposal.Status)
}

func TestProposalSend_MessageModification(t *testing.T) {
	proposal := proposalentity.Proposal{ID: 1}
	originalMessage := "Original message"

	proposalSend := NewProposalSend(proposal, &originalMessage)

	require.NotNil(t, proposalSend.Message)
	require.Equal(t, originalMessage, *proposalSend.Message)

	newMessage := "Modified message"
	proposalSend.Message = &newMessage

	require.NotNil(t, proposalSend.Message)
	require.Equal(t, newMessage, *proposalSend.Message)
}

func TestProposalSend_ValueType(t *testing.T) {
	proposal := proposalentity.Proposal{ID: 1}
	message := "Test"

	proposalSend1 := NewProposalSend(proposal, &message)
	proposalSend2 := NewProposalSend(proposal, &message)

	require.Equal(t, proposalSend1, proposalSend2, "Two ProposalSend with same data should be equal")

	proposalSend1.Message = nil
	require.NotEqual(t, proposalSend1, proposalSend2, "ProposalSend with different messages should not be equal")
}
