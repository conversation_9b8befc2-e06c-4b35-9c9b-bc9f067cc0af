package aggregate

import (
	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
)

type ProposalUpdate struct {
	Proposal proposalentity.Proposal
	Stands   []proposalentity.Stand
	Message  *string
}

func NewProposalUpdate(
	proposal proposalentity.Proposal,
	stands []proposalentity.Stand,
) *ProposalUpdate {
	return &ProposalUpdate{
		Proposal: proposal,
		Stands:   stands,
	}
}

func (p *ProposalUpdate) Validate() error {
	return nil
}
