package aggregate

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
)

func TestNewProposalUpdate(t *testing.T) {
	now := time.Now()
	proposal := proposalentity.Proposal{
		ID:        1,
		ProductID: 100,
		Type:      "OR",
		Status:    "draft",
		CreatedAt: now,
	}

	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Data:      json.RawMessage(`{"test": "data"}`),
		},
		{
			ID:        2,
			ProductID: 100,
			SeqNum:    2,
			Data:      json.RawMessage(`{"test": "data2"}`),
		},
	}

	update := NewProposalUpdate(proposal, stands)

	require.NotNil(t, update)
	require.Equal(t, proposal, update.Proposal)
	require.Equal(t, stands, update.Stands)
	require.Nil(t, update.Message)
}

func TestNewProposalUpdate_EmptyStands(t *testing.T) {
	proposal := proposalentity.Proposal{
		ID:     1,
		Status: "draft",
	}

	update := NewProposalUpdate(proposal, nil)

	require.NotNil(t, update)
	require.Equal(t, proposal, update.Proposal)
	require.Nil(t, update.Stands)
}

func TestNewProposalUpdate_EmptySliceStands(t *testing.T) {
	proposal := proposalentity.Proposal{
		ID:     1,
		Status: "draft",
	}

	stands := []proposalentity.Stand{}
	update := NewProposalUpdate(proposal, stands)

	require.NotNil(t, update)
	require.Equal(t, stands, update.Stands)
	require.Len(t, update.Stands, 0)
}

func TestProposalUpdate_Validate(t *testing.T) {
	proposal := proposalentity.Proposal{
		ID:     1,
		Status: "draft",
	}

	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
		},
	}

	update := NewProposalUpdate(proposal, stands)

	err := update.Validate()
	require.NoError(t, err)
}

func TestProposalUpdate_MessageField(t *testing.T) {
	proposal := proposalentity.Proposal{ID: 1}
	update := NewProposalUpdate(proposal, nil)

	require.Nil(t, update.Message)

	message := "Test message"
	update.Message = &message

	require.NotNil(t, update.Message)
	require.Equal(t, message, *update.Message)
}

func TestProposalUpdate_ZeroValues(t *testing.T) {
	var proposal proposalentity.Proposal
	var stands []proposalentity.Stand

	update := NewProposalUpdate(proposal, stands)

	require.NotNil(t, update)
	require.Equal(t, int64(0), update.Proposal.ID)
	require.Len(t, update.Stands, 0)
}
