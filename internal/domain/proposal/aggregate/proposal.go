package aggregate

import (
	"slices"
	"time"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

type Proposal struct {
	Proposal proposalentity.Proposal
	Stands   []proposalentity.Stand
	History  []voproposal.HistoryRecord
}

func NewProposalAggregate(
	prop proposalentity.Proposal,
	stands []proposalentity.Stand,
	history []voproposal.HistoryRecord,
) *Proposal {
	return &Proposal{
		Proposal: prop,
		Stands:   stands,
		History:  history,
	}
}

func (p *Proposal) UpdatePrice(newPrice float64) {
	p.Proposal.Price = newPrice
	p.Proposal.UpdatedAt = time.Now()
}

func (p *Proposal) AddSection(stand proposalentity.Stand) {
	p.Stands = append(p.Stands, stand)
}

func (p *Proposal) RemoveSection(standID int64) {
	for i, stand := range p.Stands {
		if stand.ID == standID {
			p.Stands = slices.Delete(p.Stands, i, i+1)
			break
		}
	}
}

func (p *Proposal) Validate() error {
	return nil
}

func (p *Proposal) AddHistoryRecord(status string, message *string) {
	p.History = append(p.History, voproposal.HistoryRecord{
		Status:    status,
		CreatedAt: time.Now().UTC(),
		Message:   message,
	})
}

func (p *Proposal) GetHistory() []voproposal.HistoryRecord {
	return p.History
}
