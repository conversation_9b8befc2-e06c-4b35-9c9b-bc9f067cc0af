package aggregate

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	proposalentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/entity"
	voproposal "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/proposal/valueobject"
)

func TestNewProposalAggregate(t *testing.T) {
	now := time.Now()
	proposal := proposalentity.Proposal{
		ID:        1,
		ProductID: 100,
		Type:      "OR",
		Status:    "draft",
		CreatedAt: now,
	}

	stands := []proposalentity.Stand{
		{
			ID:        1,
			ProductID: 100,
			SeqNum:    1,
			Data:      json.RawMessage(`{"test": "data"}`),
		},
	}

	history := []voproposal.HistoryRecord{
		{
			ID:        1,
			Status:    "draft",
			CreatedAt: now,
		},
	}

	aggregate := NewProposalAggregate(proposal, stands, history)

	require.NotNil(t, aggregate)
	require.Equal(t, proposal, aggregate.Proposal)
	require.Equal(t, stands, aggregate.Stands)
	require.Equal(t, history, aggregate.History)
}

func TestProposal_UpdatePrice(t *testing.T) {
	proposal := proposalentity.Proposal{
		ID:    1,
		Price: 100.0,
	}

	aggregate := NewProposalAggregate(proposal, nil, nil)
	oldTime := aggregate.Proposal.UpdatedAt

	time.Sleep(time.Millisecond)
	newPrice := 200.0
	aggregate.UpdatePrice(newPrice)

	require.Equal(t, newPrice, aggregate.Proposal.Price)
	require.True(t, aggregate.Proposal.UpdatedAt.After(oldTime))
}

func TestProposal_AddSection(t *testing.T) {
	aggregate := NewProposalAggregate(proposalentity.Proposal{}, nil, nil)

	stand := proposalentity.Stand{
		ID:        1,
		ProductID: 100,
		SeqNum:    1,
		Data:      json.RawMessage(`{"test": "data"}`),
	}

	require.Len(t, aggregate.Stands, 0)

	aggregate.AddSection(stand)

	require.Len(t, aggregate.Stands, 1)
	require.Equal(t, stand, aggregate.Stands[0])
}

func TestProposal_RemoveSection(t *testing.T) {
	stands := []proposalentity.Stand{
		{ID: 1, ProductID: 100, SeqNum: 1},
		{ID: 2, ProductID: 100, SeqNum: 2},
		{ID: 3, ProductID: 100, SeqNum: 3},
	}

	aggregate := NewProposalAggregate(proposalentity.Proposal{}, stands, nil)

	require.Len(t, aggregate.Stands, 3)

	aggregate.RemoveSection(2)

	require.Len(t, aggregate.Stands, 2)

	for _, stand := range aggregate.Stands {
		require.NotEqual(t, int64(2), stand.ID, "Stand with ID 2 should be removed")
	}

	require.Equal(t, int64(1), aggregate.Stands[0].ID)
	require.Equal(t, int64(3), aggregate.Stands[1].ID)
}

func TestProposal_RemoveSection_NotFound(t *testing.T) {
	stands := []proposalentity.Stand{
		{ID: 1, ProductID: 100, SeqNum: 1},
		{ID: 2, ProductID: 100, SeqNum: 2},
	}

	aggregate := NewProposalAggregate(proposalentity.Proposal{}, stands, nil)

	require.Len(t, aggregate.Stands, 2)

	aggregate.RemoveSection(999)

	require.Len(t, aggregate.Stands, 2, "Stands count should remain 2 when removing non-existent stand")
}

func TestProposal_Validate(t *testing.T) {
	aggregate := NewProposalAggregate(proposalentity.Proposal{}, nil, nil)

	err := aggregate.Validate()
	require.NoError(t, err)
}

func TestProposal_AddHistoryRecord(t *testing.T) {
	aggregate := NewProposalAggregate(proposalentity.Proposal{}, nil, nil)

	require.Len(t, aggregate.History, 0)

	status := "approved"
	message := "Test message"
	beforeAdd := time.Now().UTC()

	aggregate.AddHistoryRecord(status, &message)

	require.Len(t, aggregate.History, 1)

	record := aggregate.History[0]
	require.Equal(t, status, record.Status)
	require.NotNil(t, record.Message)
	require.Equal(t, message, *record.Message)
	require.True(t, record.CreatedAt.After(beforeAdd) || record.CreatedAt.Equal(beforeAdd))
}

func TestProposal_AddHistoryRecord_NilMessage(t *testing.T) {
	aggregate := NewProposalAggregate(proposalentity.Proposal{}, nil, nil)

	status := "draft"
	aggregate.AddHistoryRecord(status, nil)

	require.Len(t, aggregate.History, 1)

	record := aggregate.History[0]
	require.Equal(t, status, record.Status)
	require.Nil(t, record.Message)
}

func TestProposal_GetHistory(t *testing.T) {
	history := []voproposal.HistoryRecord{
		{ID: 1, Status: "draft", CreatedAt: time.Now()},
		{ID: 2, Status: "approved", CreatedAt: time.Now()},
	}

	aggregate := NewProposalAggregate(proposalentity.Proposal{}, nil, history)

	result := aggregate.GetHistory()

	require.Equal(t, history, result)
}

func TestProposal_GetHistory_Empty(t *testing.T) {
	aggregate := NewProposalAggregate(proposalentity.Proposal{}, nil, nil)

	result := aggregate.GetHistory()

	require.Len(t, result, 0)
}
