package repository

import (
	"context"

	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
)

//go:generate minimock -i RolePermissionPrimeDB -o ../mocks/role_permission_prime_db_mock.go -s _mock.go
type RolePermissionPrimeDB interface {
	Create(ctx context.Context, data roleentity.RolePermission) (roleentity.RolePermission, error)
	CreateByRoleIDAndPermissionIDs(ctx context.Context, roleID int64, permissionIDs []int64) error
	GetAll() ([]roleentity.RolePermission, error)
	GetByID(id int64) (roleentity.RolePermission, error)
	GetByPermissionID(permissionID int64) ([]roleentity.RolePermission, error)
	GetByRoleID(roleID int64) ([]roleentity.RolePermission, error)
	GetByRolePermissionID(roleID, permissionID int64) (roleentity.RolePermission, error)
	DeleteByPermissionID(permissionID int64) error
	DeleteByRoleID(ctx context.Context, roleID int64) error
	DeleteByRoleIDAndPermissionIDs(ctx context.Context, roleID int64, permissionIDs []int64) error
	DeleteByRolePermissionID(roleID, permissionID int64) error
}
