package repository

import (
	"context"

	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
)

//go:generate minimock -i RolePrimeDB -o ../mocks/role_prime_db_mock.go -s _mock.go
type RolePrimeDB interface {
	Create(ctx context.Context, role roleentity.Role) (roleentity.Role, error)
	GetAll() ([]roleentity.Role, error)
	GetAllByUserID(userID int64) ([]roleentity.Role, error)
	GetAllWithProductIDByUserID(userID int64) ([]roleentity.RoleWithProductID, error)
	GetByID(id int64) (roleentity.Role, error)
	GetByProductID(productID int64) ([]roleentity.Role, error)
	GetByProductIDAndIsActive(productID int64, isActive bool) ([]roleentity.AdminRole, error)
	GetByRoleIDAndProductID(roleID int64, productID int64) (roleentity.Role, error)
	GetBySystemType(isSystemRole bool) ([]roleentity.Role, error)
	GetOwnerRole() (roleentity.Role, error)
	GetRoleCategoryLinks(ctx context.Context) ([]roleentity.RoleCategoryLink, error)
	GetRolesWithProductByParticipantIDs(participantIDs []int64) ([]roleentity.Role, error)
	GetRolesWithStats(productID int64) ([]roleentity.RoleWithStats, error)
	GetRoleWithProductByParticipantID(participantID int64) ([]roleentity.Role, error)
	GetSystemRolesWithStats(ctx context.Context) ([]roleentity.RoleWithStats, error)
	ExistByName(name string) (bool, error)
	Update(ctx context.Context, role roleentity.RoleUpdateData) (roleentity.Role, error)
	DeactivateByIDs(roleIDs []int64) error
}

//go:generate minimock -i RoleCache -o ../mocks/role_cache_mock.go -s _mock.go
type RoleCache interface {
	AssignProductRole(ctx context.Context, roleID, productID int64) error
	AssignUserRole(ctx context.Context, userID, roleID int64) error
	GetProductRoles(ctx context.Context, productID int64) ([]roleentity.Role, error)
	GetRole(ctx context.Context, id int64) (roleentity.Role, error)
	GetRoles(ctx context.Context) ([]roleentity.Role, error)
	GetUserRoleIDs(ctx context.Context, userID int64) ([]int64, error)
	GetUserRoles(ctx context.Context, userID int64) ([]roleentity.Role, error)
	SetRole(ctx context.Context, role roleentity.Role) error
	SetRoles(ctx context.Context, roles []roleentity.Role) error
	DeleteRole(ctx context.Context, id int64) error
	DeleteRoles(ctx context.Context, ids []int64) error
	RemoveProductRole(ctx context.Context, roleID, productID int64) error
	RemoveUserRole(ctx context.Context, userID, roleID int64) error
}
