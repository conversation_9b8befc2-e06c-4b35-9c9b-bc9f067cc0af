// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository.RolePermissionPrimeDB -o role_permission_prime_db_mock.go -n RolePermissionPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"github.com/gojuno/minimock/v3"
)

// RolePermissionPrimeDBMock implements mm_repository.RolePermissionPrimeDB
type RolePermissionPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, data roleentity.RolePermission) (r1 roleentity.RolePermission, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, data roleentity.RolePermission)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mRolePermissionPrimeDBMockCreate

	funcCreateByRoleIDAndPermissionIDs          func(ctx context.Context, roleID int64, permissionIDs []int64) (err error)
	funcCreateByRoleIDAndPermissionIDsOrigin    string
	inspectFuncCreateByRoleIDAndPermissionIDs   func(ctx context.Context, roleID int64, permissionIDs []int64)
	afterCreateByRoleIDAndPermissionIDsCounter  uint64
	beforeCreateByRoleIDAndPermissionIDsCounter uint64
	CreateByRoleIDAndPermissionIDsMock          mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs

	funcDeleteByPermissionID          func(permissionID int64) (err error)
	funcDeleteByPermissionIDOrigin    string
	inspectFuncDeleteByPermissionID   func(permissionID int64)
	afterDeleteByPermissionIDCounter  uint64
	beforeDeleteByPermissionIDCounter uint64
	DeleteByPermissionIDMock          mRolePermissionPrimeDBMockDeleteByPermissionID

	funcDeleteByRoleID          func(ctx context.Context, roleID int64) (err error)
	funcDeleteByRoleIDOrigin    string
	inspectFuncDeleteByRoleID   func(ctx context.Context, roleID int64)
	afterDeleteByRoleIDCounter  uint64
	beforeDeleteByRoleIDCounter uint64
	DeleteByRoleIDMock          mRolePermissionPrimeDBMockDeleteByRoleID

	funcDeleteByRoleIDAndPermissionIDs          func(ctx context.Context, roleID int64, permissionIDs []int64) (err error)
	funcDeleteByRoleIDAndPermissionIDsOrigin    string
	inspectFuncDeleteByRoleIDAndPermissionIDs   func(ctx context.Context, roleID int64, permissionIDs []int64)
	afterDeleteByRoleIDAndPermissionIDsCounter  uint64
	beforeDeleteByRoleIDAndPermissionIDsCounter uint64
	DeleteByRoleIDAndPermissionIDsMock          mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs

	funcDeleteByRolePermissionID          func(roleID int64, permissionID int64) (err error)
	funcDeleteByRolePermissionIDOrigin    string
	inspectFuncDeleteByRolePermissionID   func(roleID int64, permissionID int64)
	afterDeleteByRolePermissionIDCounter  uint64
	beforeDeleteByRolePermissionIDCounter uint64
	DeleteByRolePermissionIDMock          mRolePermissionPrimeDBMockDeleteByRolePermissionID

	funcGetAll          func() (ra1 []roleentity.RolePermission, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mRolePermissionPrimeDBMockGetAll

	funcGetByID          func(id int64) (r1 roleentity.RolePermission, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mRolePermissionPrimeDBMockGetByID

	funcGetByPermissionID          func(permissionID int64) (ra1 []roleentity.RolePermission, err error)
	funcGetByPermissionIDOrigin    string
	inspectFuncGetByPermissionID   func(permissionID int64)
	afterGetByPermissionIDCounter  uint64
	beforeGetByPermissionIDCounter uint64
	GetByPermissionIDMock          mRolePermissionPrimeDBMockGetByPermissionID

	funcGetByRoleID          func(roleID int64) (ra1 []roleentity.RolePermission, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mRolePermissionPrimeDBMockGetByRoleID

	funcGetByRolePermissionID          func(roleID int64, permissionID int64) (r1 roleentity.RolePermission, err error)
	funcGetByRolePermissionIDOrigin    string
	inspectFuncGetByRolePermissionID   func(roleID int64, permissionID int64)
	afterGetByRolePermissionIDCounter  uint64
	beforeGetByRolePermissionIDCounter uint64
	GetByRolePermissionIDMock          mRolePermissionPrimeDBMockGetByRolePermissionID
}

// NewRolePermissionPrimeDBMock returns a mock for mm_repository.RolePermissionPrimeDB
func NewRolePermissionPrimeDBMock(t minimock.Tester) *RolePermissionPrimeDBMock {
	m := &RolePermissionPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mRolePermissionPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*RolePermissionPrimeDBMockCreateParams{}

	m.CreateByRoleIDAndPermissionIDsMock = mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs{mock: m}
	m.CreateByRoleIDAndPermissionIDsMock.callArgs = []*RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams{}

	m.DeleteByPermissionIDMock = mRolePermissionPrimeDBMockDeleteByPermissionID{mock: m}
	m.DeleteByPermissionIDMock.callArgs = []*RolePermissionPrimeDBMockDeleteByPermissionIDParams{}

	m.DeleteByRoleIDMock = mRolePermissionPrimeDBMockDeleteByRoleID{mock: m}
	m.DeleteByRoleIDMock.callArgs = []*RolePermissionPrimeDBMockDeleteByRoleIDParams{}

	m.DeleteByRoleIDAndPermissionIDsMock = mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs{mock: m}
	m.DeleteByRoleIDAndPermissionIDsMock.callArgs = []*RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams{}

	m.DeleteByRolePermissionIDMock = mRolePermissionPrimeDBMockDeleteByRolePermissionID{mock: m}
	m.DeleteByRolePermissionIDMock.callArgs = []*RolePermissionPrimeDBMockDeleteByRolePermissionIDParams{}

	m.GetAllMock = mRolePermissionPrimeDBMockGetAll{mock: m}

	m.GetByIDMock = mRolePermissionPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*RolePermissionPrimeDBMockGetByIDParams{}

	m.GetByPermissionIDMock = mRolePermissionPrimeDBMockGetByPermissionID{mock: m}
	m.GetByPermissionIDMock.callArgs = []*RolePermissionPrimeDBMockGetByPermissionIDParams{}

	m.GetByRoleIDMock = mRolePermissionPrimeDBMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*RolePermissionPrimeDBMockGetByRoleIDParams{}

	m.GetByRolePermissionIDMock = mRolePermissionPrimeDBMockGetByRolePermissionID{mock: m}
	m.GetByRolePermissionIDMock.callArgs = []*RolePermissionPrimeDBMockGetByRolePermissionIDParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mRolePermissionPrimeDBMockCreate struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockCreateExpectation
	expectations       []*RolePermissionPrimeDBMockCreateExpectation

	callArgs []*RolePermissionPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockCreateExpectation specifies expectation struct of the RolePermissionPrimeDB.Create
type RolePermissionPrimeDBMockCreateExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockCreateParams
	paramPtrs          *RolePermissionPrimeDBMockCreateParamPtrs
	expectationOrigins RolePermissionPrimeDBMockCreateExpectationOrigins
	results            *RolePermissionPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockCreateParams contains parameters of the RolePermissionPrimeDB.Create
type RolePermissionPrimeDBMockCreateParams struct {
	ctx  context.Context
	data roleentity.RolePermission
}

// RolePermissionPrimeDBMockCreateParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.Create
type RolePermissionPrimeDBMockCreateParamPtrs struct {
	ctx  *context.Context
	data *roleentity.RolePermission
}

// RolePermissionPrimeDBMockCreateResults contains results of the RolePermissionPrimeDB.Create
type RolePermissionPrimeDBMockCreateResults struct {
	r1  roleentity.RolePermission
	err error
}

// RolePermissionPrimeDBMockCreateOrigins contains origins of expectations of the RolePermissionPrimeDB.Create
type RolePermissionPrimeDBMockCreateExpectationOrigins struct {
	origin     string
	originCtx  string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mRolePermissionPrimeDBMockCreate) Optional() *mRolePermissionPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for RolePermissionPrimeDB.Create
func (mmCreate *mRolePermissionPrimeDBMockCreate) Expect(ctx context.Context, data roleentity.RolePermission) *mRolePermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &RolePermissionPrimeDBMockCreateParams{ctx, data}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for RolePermissionPrimeDB.Create
func (mmCreate *mRolePermissionPrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mRolePermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectDataParam2 sets up expected param data for RolePermissionPrimeDB.Create
func (mmCreate *mRolePermissionPrimeDBMockCreate) ExpectDataParam2(data roleentity.RolePermission) *mRolePermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.data = &data
	mmCreate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.Create
func (mmCreate *mRolePermissionPrimeDBMockCreate) Inspect(f func(ctx context.Context, data roleentity.RolePermission)) *mRolePermissionPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by RolePermissionPrimeDB.Create
func (mmCreate *mRolePermissionPrimeDBMockCreate) Return(r1 roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePermissionPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &RolePermissionPrimeDBMockCreateResults{r1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.Create method
func (mmCreate *mRolePermissionPrimeDBMockCreate) Set(f func(ctx context.Context, data roleentity.RolePermission) (r1 roleentity.RolePermission, err error)) *RolePermissionPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the RolePermissionPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mRolePermissionPrimeDBMockCreate) When(ctx context.Context, data roleentity.RolePermission) *RolePermissionPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePermissionPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &RolePermissionPrimeDBMockCreateParams{ctx, data},
		expectationOrigins: RolePermissionPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockCreateExpectation) Then(r1 roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockCreateResults{r1, err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.Create should be invoked
func (mmCreate *mRolePermissionPrimeDBMockCreate) Times(n uint64) *mRolePermissionPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mRolePermissionPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.RolePermissionPrimeDB
func (mmCreate *RolePermissionPrimeDBMock) Create(ctx context.Context, data roleentity.RolePermission) (r1 roleentity.RolePermission, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, data)
	}

	mm_params := RolePermissionPrimeDBMockCreateParams{ctx, data}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockCreateParams{ctx, data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("RolePermissionPrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreate.t.Errorf("RolePermissionPrimeDBMock.Create got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("RolePermissionPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the RolePermissionPrimeDBMock.Create")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, data)
	}
	mmCreate.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.Create. %v %v", ctx, data)
	return
}

// CreateAfterCounter returns a count of finished RolePermissionPrimeDBMock.Create invocations
func (mmCreate *RolePermissionPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of RolePermissionPrimeDBMock.Create invocations
func (mmCreate *RolePermissionPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mRolePermissionPrimeDBMockCreate) Calls() []*RolePermissionPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation
	expectations       []*RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation

	callArgs []*RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation specifies expectation struct of the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams
	paramPtrs          *RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParamPtrs
	expectationOrigins RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectationOrigins
	results            *RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams contains parameters of the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams struct {
	ctx           context.Context
	roleID        int64
	permissionIDs []int64
}

// RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParamPtrs struct {
	ctx           *context.Context
	roleID        *int64
	permissionIDs *[]int64
}

// RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsResults contains results of the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsResults struct {
	err error
}

// RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsOrigins contains origins of expectations of the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectationOrigins struct {
	origin              string
	originCtx           string
	originRoleID        string
	originPermissionIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Optional() *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	mmCreateByRoleIDAndPermissionIDs.optional = true
	return mmCreateByRoleIDAndPermissionIDs
}

// Expect sets up expected params for RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Expect(ctx context.Context, roleID int64, permissionIDs []int64) *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	if mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation{}
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by ExpectParams functions")
	}

	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.params = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs}
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreateByRoleIDAndPermissionIDs.expectations {
		if minimock.Equal(e.params, mmCreateByRoleIDAndPermissionIDs.defaultExpectation.params) {
			mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreateByRoleIDAndPermissionIDs.defaultExpectation.params)
		}
	}

	return mmCreateByRoleIDAndPermissionIDs
}

// ExpectCtxParam1 sets up expected param ctx for RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) ExpectCtxParam1(ctx context.Context) *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	if mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation{}
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParamPtrs{}
	}
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndPermissionIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) ExpectRoleIDParam2(roleID int64) *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	if mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation{}
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParamPtrs{}
	}
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndPermissionIDs
}

// ExpectPermissionIDsParam3 sets up expected param permissionIDs for RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) ExpectPermissionIDsParam3(permissionIDs []int64) *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	if mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation{}
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.params != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Expect")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParamPtrs{}
	}
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs.permissionIDs = &permissionIDs
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.originPermissionIDs = minimock.CallerInfo(1)

	return mmCreateByRoleIDAndPermissionIDs
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Inspect(f func(ctx context.Context, roleID int64, permissionIDs []int64)) *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	if mmCreateByRoleIDAndPermissionIDs.mock.inspectFuncCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs")
	}

	mmCreateByRoleIDAndPermissionIDs.mock.inspectFuncCreateByRoleIDAndPermissionIDs = f

	return mmCreateByRoleIDAndPermissionIDs
}

// Return sets up results that will be returned by RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Return(err error) *RolePermissionPrimeDBMock {
	if mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmCreateByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation{mock: mmCreateByRoleIDAndPermissionIDs.mock}
	}
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.results = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsResults{err}
	mmCreateByRoleIDAndPermissionIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndPermissionIDs.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs method
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Set(f func(ctx context.Context, roleID int64, permissionIDs []int64) (err error)) *RolePermissionPrimeDBMock {
	if mmCreateByRoleIDAndPermissionIDs.defaultExpectation != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs method")
	}

	if len(mmCreateByRoleIDAndPermissionIDs.expectations) > 0 {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs method")
	}

	mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs = f
	mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndPermissionIDs.mock
}

// When sets expectation for the RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs which will trigger the result defined by the following
// Then helper
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) When(ctx context.Context, roleID int64, permissionIDs []int64) *RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation {
	if mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation{
		mock:               mmCreateByRoleIDAndPermissionIDs.mock,
		params:             &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs},
		expectationOrigins: RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreateByRoleIDAndPermissionIDs.expectations = append(mmCreateByRoleIDAndPermissionIDs.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsExpectation) Then(err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsResults{err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.CreateByRoleIDAndPermissionIDs should be invoked
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Times(n uint64) *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs {
	if n == 0 {
		mmCreateByRoleIDAndPermissionIDs.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreateByRoleIDAndPermissionIDs.expectedInvocations, n)
	mmCreateByRoleIDAndPermissionIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreateByRoleIDAndPermissionIDs
}

func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) invocationsDone() bool {
	if len(mmCreateByRoleIDAndPermissionIDs.expectations) == 0 && mmCreateByRoleIDAndPermissionIDs.defaultExpectation == nil && mmCreateByRoleIDAndPermissionIDs.mock.funcCreateByRoleIDAndPermissionIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndPermissionIDs.mock.afterCreateByRoleIDAndPermissionIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreateByRoleIDAndPermissionIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// CreateByRoleIDAndPermissionIDs implements mm_repository.RolePermissionPrimeDB
func (mmCreateByRoleIDAndPermissionIDs *RolePermissionPrimeDBMock) CreateByRoleIDAndPermissionIDs(ctx context.Context, roleID int64, permissionIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmCreateByRoleIDAndPermissionIDs.beforeCreateByRoleIDAndPermissionIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmCreateByRoleIDAndPermissionIDs.afterCreateByRoleIDAndPermissionIDsCounter, 1)

	mmCreateByRoleIDAndPermissionIDs.t.Helper()

	if mmCreateByRoleIDAndPermissionIDs.inspectFuncCreateByRoleIDAndPermissionIDs != nil {
		mmCreateByRoleIDAndPermissionIDs.inspectFuncCreateByRoleIDAndPermissionIDs(ctx, roleID, permissionIDs)
	}

	mm_params := RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs}

	// Record call args
	mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.mutex.Lock()
	mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.callArgs = append(mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.callArgs, &mm_params)
	mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.mutex.Unlock()

	for _, e := range mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.params
		mm_want_ptrs := mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreateByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmCreateByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.permissionIDs != nil && !minimock.Equal(*mm_want_ptrs.permissionIDs, mm_got.permissionIDs) {
				mmCreateByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs got unexpected parameter permissionIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.originPermissionIDs, *mm_want_ptrs.permissionIDs, mm_got.permissionIDs, minimock.Diff(*mm_want_ptrs.permissionIDs, mm_got.permissionIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreateByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreateByRoleIDAndPermissionIDs.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmCreateByRoleIDAndPermissionIDs.t.Fatal("No results are set for the RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs")
		}
		return (*mm_results).err
	}
	if mmCreateByRoleIDAndPermissionIDs.funcCreateByRoleIDAndPermissionIDs != nil {
		return mmCreateByRoleIDAndPermissionIDs.funcCreateByRoleIDAndPermissionIDs(ctx, roleID, permissionIDs)
	}
	mmCreateByRoleIDAndPermissionIDs.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs. %v %v %v", ctx, roleID, permissionIDs)
	return
}

// CreateByRoleIDAndPermissionIDsAfterCounter returns a count of finished RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs invocations
func (mmCreateByRoleIDAndPermissionIDs *RolePermissionPrimeDBMock) CreateByRoleIDAndPermissionIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndPermissionIDs.afterCreateByRoleIDAndPermissionIDsCounter)
}

// CreateByRoleIDAndPermissionIDsBeforeCounter returns a count of RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs invocations
func (mmCreateByRoleIDAndPermissionIDs *RolePermissionPrimeDBMock) CreateByRoleIDAndPermissionIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreateByRoleIDAndPermissionIDs.beforeCreateByRoleIDAndPermissionIDsCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreateByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDs) Calls() []*RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams {
	mmCreateByRoleIDAndPermissionIDs.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockCreateByRoleIDAndPermissionIDsParams, len(mmCreateByRoleIDAndPermissionIDs.callArgs))
	copy(argCopy, mmCreateByRoleIDAndPermissionIDs.callArgs)

	mmCreateByRoleIDAndPermissionIDs.mutex.RUnlock()

	return argCopy
}

// MinimockCreateByRoleIDAndPermissionIDsDone returns true if the count of the CreateByRoleIDAndPermissionIDs invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockCreateByRoleIDAndPermissionIDsDone() bool {
	if m.CreateByRoleIDAndPermissionIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateByRoleIDAndPermissionIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateByRoleIDAndPermissionIDsMock.invocationsDone()
}

// MinimockCreateByRoleIDAndPermissionIDsInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockCreateByRoleIDAndPermissionIDsInspect() {
	for _, e := range m.CreateByRoleIDAndPermissionIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateByRoleIDAndPermissionIDsCounter := mm_atomic.LoadUint64(&m.afterCreateByRoleIDAndPermissionIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateByRoleIDAndPermissionIDsMock.defaultExpectation != nil && afterCreateByRoleIDAndPermissionIDsCounter < 1 {
		if m.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs at\n%s", m.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs at\n%s with params: %#v", m.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.origin, *m.CreateByRoleIDAndPermissionIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreateByRoleIDAndPermissionIDs != nil && afterCreateByRoleIDAndPermissionIDsCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs at\n%s", m.funcCreateByRoleIDAndPermissionIDsOrigin)
	}

	if !m.CreateByRoleIDAndPermissionIDsMock.invocationsDone() && afterCreateByRoleIDAndPermissionIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.CreateByRoleIDAndPermissionIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateByRoleIDAndPermissionIDsMock.expectedInvocations), m.CreateByRoleIDAndPermissionIDsMock.expectedInvocationsOrigin, afterCreateByRoleIDAndPermissionIDsCounter)
	}
}

type mRolePermissionPrimeDBMockDeleteByPermissionID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockDeleteByPermissionIDExpectation
	expectations       []*RolePermissionPrimeDBMockDeleteByPermissionIDExpectation

	callArgs []*RolePermissionPrimeDBMockDeleteByPermissionIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockDeleteByPermissionIDExpectation specifies expectation struct of the RolePermissionPrimeDB.DeleteByPermissionID
type RolePermissionPrimeDBMockDeleteByPermissionIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockDeleteByPermissionIDParams
	paramPtrs          *RolePermissionPrimeDBMockDeleteByPermissionIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockDeleteByPermissionIDExpectationOrigins
	results            *RolePermissionPrimeDBMockDeleteByPermissionIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockDeleteByPermissionIDParams contains parameters of the RolePermissionPrimeDB.DeleteByPermissionID
type RolePermissionPrimeDBMockDeleteByPermissionIDParams struct {
	permissionID int64
}

// RolePermissionPrimeDBMockDeleteByPermissionIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.DeleteByPermissionID
type RolePermissionPrimeDBMockDeleteByPermissionIDParamPtrs struct {
	permissionID *int64
}

// RolePermissionPrimeDBMockDeleteByPermissionIDResults contains results of the RolePermissionPrimeDB.DeleteByPermissionID
type RolePermissionPrimeDBMockDeleteByPermissionIDResults struct {
	err error
}

// RolePermissionPrimeDBMockDeleteByPermissionIDOrigins contains origins of expectations of the RolePermissionPrimeDB.DeleteByPermissionID
type RolePermissionPrimeDBMockDeleteByPermissionIDExpectationOrigins struct {
	origin             string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Optional() *mRolePermissionPrimeDBMockDeleteByPermissionID {
	mmDeleteByPermissionID.optional = true
	return mmDeleteByPermissionID
}

// Expect sets up expected params for RolePermissionPrimeDB.DeleteByPermissionID
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Expect(permissionID int64) *mRolePermissionPrimeDBMockDeleteByPermissionID {
	if mmDeleteByPermissionID.mock.funcDeleteByPermissionID != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByPermissionID mock is already set by Set")
	}

	if mmDeleteByPermissionID.defaultExpectation == nil {
		mmDeleteByPermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByPermissionIDExpectation{}
	}

	if mmDeleteByPermissionID.defaultExpectation.paramPtrs != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByPermissionID mock is already set by ExpectParams functions")
	}

	mmDeleteByPermissionID.defaultExpectation.params = &RolePermissionPrimeDBMockDeleteByPermissionIDParams{permissionID}
	mmDeleteByPermissionID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByPermissionID.expectations {
		if minimock.Equal(e.params, mmDeleteByPermissionID.defaultExpectation.params) {
			mmDeleteByPermissionID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByPermissionID.defaultExpectation.params)
		}
	}

	return mmDeleteByPermissionID
}

// ExpectPermissionIDParam1 sets up expected param permissionID for RolePermissionPrimeDB.DeleteByPermissionID
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) ExpectPermissionIDParam1(permissionID int64) *mRolePermissionPrimeDBMockDeleteByPermissionID {
	if mmDeleteByPermissionID.mock.funcDeleteByPermissionID != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByPermissionID mock is already set by Set")
	}

	if mmDeleteByPermissionID.defaultExpectation == nil {
		mmDeleteByPermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByPermissionIDExpectation{}
	}

	if mmDeleteByPermissionID.defaultExpectation.params != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByPermissionID mock is already set by Expect")
	}

	if mmDeleteByPermissionID.defaultExpectation.paramPtrs == nil {
		mmDeleteByPermissionID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByPermissionIDParamPtrs{}
	}
	mmDeleteByPermissionID.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmDeleteByPermissionID.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmDeleteByPermissionID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.DeleteByPermissionID
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Inspect(f func(permissionID int64)) *mRolePermissionPrimeDBMockDeleteByPermissionID {
	if mmDeleteByPermissionID.mock.inspectFuncDeleteByPermissionID != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.DeleteByPermissionID")
	}

	mmDeleteByPermissionID.mock.inspectFuncDeleteByPermissionID = f

	return mmDeleteByPermissionID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.DeleteByPermissionID
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Return(err error) *RolePermissionPrimeDBMock {
	if mmDeleteByPermissionID.mock.funcDeleteByPermissionID != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByPermissionID mock is already set by Set")
	}

	if mmDeleteByPermissionID.defaultExpectation == nil {
		mmDeleteByPermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByPermissionIDExpectation{mock: mmDeleteByPermissionID.mock}
	}
	mmDeleteByPermissionID.defaultExpectation.results = &RolePermissionPrimeDBMockDeleteByPermissionIDResults{err}
	mmDeleteByPermissionID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByPermissionID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.DeleteByPermissionID method
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Set(f func(permissionID int64) (err error)) *RolePermissionPrimeDBMock {
	if mmDeleteByPermissionID.defaultExpectation != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.DeleteByPermissionID method")
	}

	if len(mmDeleteByPermissionID.expectations) > 0 {
		mmDeleteByPermissionID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.DeleteByPermissionID method")
	}

	mmDeleteByPermissionID.mock.funcDeleteByPermissionID = f
	mmDeleteByPermissionID.mock.funcDeleteByPermissionIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByPermissionID.mock
}

// When sets expectation for the RolePermissionPrimeDB.DeleteByPermissionID which will trigger the result defined by the following
// Then helper
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) When(permissionID int64) *RolePermissionPrimeDBMockDeleteByPermissionIDExpectation {
	if mmDeleteByPermissionID.mock.funcDeleteByPermissionID != nil {
		mmDeleteByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByPermissionID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockDeleteByPermissionIDExpectation{
		mock:               mmDeleteByPermissionID.mock,
		params:             &RolePermissionPrimeDBMockDeleteByPermissionIDParams{permissionID},
		expectationOrigins: RolePermissionPrimeDBMockDeleteByPermissionIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByPermissionID.expectations = append(mmDeleteByPermissionID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.DeleteByPermissionID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockDeleteByPermissionIDExpectation) Then(err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockDeleteByPermissionIDResults{err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.DeleteByPermissionID should be invoked
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Times(n uint64) *mRolePermissionPrimeDBMockDeleteByPermissionID {
	if n == 0 {
		mmDeleteByPermissionID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.DeleteByPermissionID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByPermissionID.expectedInvocations, n)
	mmDeleteByPermissionID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByPermissionID
}

func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) invocationsDone() bool {
	if len(mmDeleteByPermissionID.expectations) == 0 && mmDeleteByPermissionID.defaultExpectation == nil && mmDeleteByPermissionID.mock.funcDeleteByPermissionID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByPermissionID.mock.afterDeleteByPermissionIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByPermissionID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByPermissionID implements mm_repository.RolePermissionPrimeDB
func (mmDeleteByPermissionID *RolePermissionPrimeDBMock) DeleteByPermissionID(permissionID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByPermissionID.beforeDeleteByPermissionIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByPermissionID.afterDeleteByPermissionIDCounter, 1)

	mmDeleteByPermissionID.t.Helper()

	if mmDeleteByPermissionID.inspectFuncDeleteByPermissionID != nil {
		mmDeleteByPermissionID.inspectFuncDeleteByPermissionID(permissionID)
	}

	mm_params := RolePermissionPrimeDBMockDeleteByPermissionIDParams{permissionID}

	// Record call args
	mmDeleteByPermissionID.DeleteByPermissionIDMock.mutex.Lock()
	mmDeleteByPermissionID.DeleteByPermissionIDMock.callArgs = append(mmDeleteByPermissionID.DeleteByPermissionIDMock.callArgs, &mm_params)
	mmDeleteByPermissionID.DeleteByPermissionIDMock.mutex.Unlock()

	for _, e := range mmDeleteByPermissionID.DeleteByPermissionIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockDeleteByPermissionIDParams{permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmDeleteByPermissionID.t.Errorf("RolePermissionPrimeDBMock.DeleteByPermissionID got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByPermissionID.t.Errorf("RolePermissionPrimeDBMock.DeleteByPermissionID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByPermissionID.DeleteByPermissionIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByPermissionID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.DeleteByPermissionID")
		}
		return (*mm_results).err
	}
	if mmDeleteByPermissionID.funcDeleteByPermissionID != nil {
		return mmDeleteByPermissionID.funcDeleteByPermissionID(permissionID)
	}
	mmDeleteByPermissionID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.DeleteByPermissionID. %v", permissionID)
	return
}

// DeleteByPermissionIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.DeleteByPermissionID invocations
func (mmDeleteByPermissionID *RolePermissionPrimeDBMock) DeleteByPermissionIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByPermissionID.afterDeleteByPermissionIDCounter)
}

// DeleteByPermissionIDBeforeCounter returns a count of RolePermissionPrimeDBMock.DeleteByPermissionID invocations
func (mmDeleteByPermissionID *RolePermissionPrimeDBMock) DeleteByPermissionIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByPermissionID.beforeDeleteByPermissionIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.DeleteByPermissionID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByPermissionID *mRolePermissionPrimeDBMockDeleteByPermissionID) Calls() []*RolePermissionPrimeDBMockDeleteByPermissionIDParams {
	mmDeleteByPermissionID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockDeleteByPermissionIDParams, len(mmDeleteByPermissionID.callArgs))
	copy(argCopy, mmDeleteByPermissionID.callArgs)

	mmDeleteByPermissionID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByPermissionIDDone returns true if the count of the DeleteByPermissionID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockDeleteByPermissionIDDone() bool {
	if m.DeleteByPermissionIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByPermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByPermissionIDMock.invocationsDone()
}

// MinimockDeleteByPermissionIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockDeleteByPermissionIDInspect() {
	for _, e := range m.DeleteByPermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByPermissionID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByPermissionIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByPermissionIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByPermissionIDMock.defaultExpectation != nil && afterDeleteByPermissionIDCounter < 1 {
		if m.DeleteByPermissionIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByPermissionID at\n%s", m.DeleteByPermissionIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByPermissionID at\n%s with params: %#v", m.DeleteByPermissionIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByPermissionIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByPermissionID != nil && afterDeleteByPermissionIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByPermissionID at\n%s", m.funcDeleteByPermissionIDOrigin)
	}

	if !m.DeleteByPermissionIDMock.invocationsDone() && afterDeleteByPermissionIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.DeleteByPermissionID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByPermissionIDMock.expectedInvocations), m.DeleteByPermissionIDMock.expectedInvocationsOrigin, afterDeleteByPermissionIDCounter)
	}
}

type mRolePermissionPrimeDBMockDeleteByRoleID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockDeleteByRoleIDExpectation
	expectations       []*RolePermissionPrimeDBMockDeleteByRoleIDExpectation

	callArgs []*RolePermissionPrimeDBMockDeleteByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockDeleteByRoleIDExpectation specifies expectation struct of the RolePermissionPrimeDB.DeleteByRoleID
type RolePermissionPrimeDBMockDeleteByRoleIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockDeleteByRoleIDParams
	paramPtrs          *RolePermissionPrimeDBMockDeleteByRoleIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockDeleteByRoleIDExpectationOrigins
	results            *RolePermissionPrimeDBMockDeleteByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockDeleteByRoleIDParams contains parameters of the RolePermissionPrimeDB.DeleteByRoleID
type RolePermissionPrimeDBMockDeleteByRoleIDParams struct {
	ctx    context.Context
	roleID int64
}

// RolePermissionPrimeDBMockDeleteByRoleIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.DeleteByRoleID
type RolePermissionPrimeDBMockDeleteByRoleIDParamPtrs struct {
	ctx    *context.Context
	roleID *int64
}

// RolePermissionPrimeDBMockDeleteByRoleIDResults contains results of the RolePermissionPrimeDB.DeleteByRoleID
type RolePermissionPrimeDBMockDeleteByRoleIDResults struct {
	err error
}

// RolePermissionPrimeDBMockDeleteByRoleIDOrigins contains origins of expectations of the RolePermissionPrimeDB.DeleteByRoleID
type RolePermissionPrimeDBMockDeleteByRoleIDExpectationOrigins struct {
	origin       string
	originCtx    string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Optional() *mRolePermissionPrimeDBMockDeleteByRoleID {
	mmDeleteByRoleID.optional = true
	return mmDeleteByRoleID
}

// Expect sets up expected params for RolePermissionPrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Expect(ctx context.Context, roleID int64) *mRolePermissionPrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleID.defaultExpectation.params = &RolePermissionPrimeDBMockDeleteByRoleIDParams{ctx, roleID}
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleID.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleID.defaultExpectation.params) {
			mmDeleteByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleID.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleID
}

// ExpectCtxParam1 sets up expected param ctx for RolePermissionPrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) ExpectCtxParam1(ctx context.Context) *mRolePermissionPrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.params != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Expect")
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRoleIDParamPtrs{}
	}
	mmDeleteByRoleID.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleID
}

// ExpectRoleIDParam2 sets up expected param roleID for RolePermissionPrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) ExpectRoleIDParam2(roleID int64) *mRolePermissionPrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDExpectation{}
	}

	if mmDeleteByRoleID.defaultExpectation.params != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Expect")
	}

	if mmDeleteByRoleID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRoleIDParamPtrs{}
	}
	mmDeleteByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRoleID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Inspect(f func(ctx context.Context, roleID int64)) *mRolePermissionPrimeDBMockDeleteByRoleID {
	if mmDeleteByRoleID.mock.inspectFuncDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.DeleteByRoleID")
	}

	mmDeleteByRoleID.mock.inspectFuncDeleteByRoleID = f

	return mmDeleteByRoleID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.DeleteByRoleID
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Return(err error) *RolePermissionPrimeDBMock {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	if mmDeleteByRoleID.defaultExpectation == nil {
		mmDeleteByRoleID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDExpectation{mock: mmDeleteByRoleID.mock}
	}
	mmDeleteByRoleID.defaultExpectation.results = &RolePermissionPrimeDBMockDeleteByRoleIDResults{err}
	mmDeleteByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.DeleteByRoleID method
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Set(f func(ctx context.Context, roleID int64) (err error)) *RolePermissionPrimeDBMock {
	if mmDeleteByRoleID.defaultExpectation != nil {
		mmDeleteByRoleID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.DeleteByRoleID method")
	}

	if len(mmDeleteByRoleID.expectations) > 0 {
		mmDeleteByRoleID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.DeleteByRoleID method")
	}

	mmDeleteByRoleID.mock.funcDeleteByRoleID = f
	mmDeleteByRoleID.mock.funcDeleteByRoleIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID.mock
}

// When sets expectation for the RolePermissionPrimeDB.DeleteByRoleID which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) When(ctx context.Context, roleID int64) *RolePermissionPrimeDBMockDeleteByRoleIDExpectation {
	if mmDeleteByRoleID.mock.funcDeleteByRoleID != nil {
		mmDeleteByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockDeleteByRoleIDExpectation{
		mock:               mmDeleteByRoleID.mock,
		params:             &RolePermissionPrimeDBMockDeleteByRoleIDParams{ctx, roleID},
		expectationOrigins: RolePermissionPrimeDBMockDeleteByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleID.expectations = append(mmDeleteByRoleID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.DeleteByRoleID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockDeleteByRoleIDExpectation) Then(err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockDeleteByRoleIDResults{err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.DeleteByRoleID should be invoked
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Times(n uint64) *mRolePermissionPrimeDBMockDeleteByRoleID {
	if n == 0 {
		mmDeleteByRoleID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.DeleteByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleID.expectedInvocations, n)
	mmDeleteByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleID
}

func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) invocationsDone() bool {
	if len(mmDeleteByRoleID.expectations) == 0 && mmDeleteByRoleID.defaultExpectation == nil && mmDeleteByRoleID.mock.funcDeleteByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleID.mock.afterDeleteByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleID implements mm_repository.RolePermissionPrimeDB
func (mmDeleteByRoleID *RolePermissionPrimeDBMock) DeleteByRoleID(ctx context.Context, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleID.beforeDeleteByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleID.afterDeleteByRoleIDCounter, 1)

	mmDeleteByRoleID.t.Helper()

	if mmDeleteByRoleID.inspectFuncDeleteByRoleID != nil {
		mmDeleteByRoleID.inspectFuncDeleteByRoleID(ctx, roleID)
	}

	mm_params := RolePermissionPrimeDBMockDeleteByRoleIDParams{ctx, roleID}

	// Record call args
	mmDeleteByRoleID.DeleteByRoleIDMock.mutex.Lock()
	mmDeleteByRoleID.DeleteByRoleIDMock.callArgs = append(mmDeleteByRoleID.DeleteByRoleIDMock.callArgs, &mm_params)
	mmDeleteByRoleID.DeleteByRoleIDMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleID.DeleteByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockDeleteByRoleIDParams{ctx, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleID.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleID got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRoleID.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleID.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleID.DeleteByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.DeleteByRoleID")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleID.funcDeleteByRoleID != nil {
		return mmDeleteByRoleID.funcDeleteByRoleID(ctx, roleID)
	}
	mmDeleteByRoleID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.DeleteByRoleID. %v %v", ctx, roleID)
	return
}

// DeleteByRoleIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.DeleteByRoleID invocations
func (mmDeleteByRoleID *RolePermissionPrimeDBMock) DeleteByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleID.afterDeleteByRoleIDCounter)
}

// DeleteByRoleIDBeforeCounter returns a count of RolePermissionPrimeDBMock.DeleteByRoleID invocations
func (mmDeleteByRoleID *RolePermissionPrimeDBMock) DeleteByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleID.beforeDeleteByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.DeleteByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleID *mRolePermissionPrimeDBMockDeleteByRoleID) Calls() []*RolePermissionPrimeDBMockDeleteByRoleIDParams {
	mmDeleteByRoleID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockDeleteByRoleIDParams, len(mmDeleteByRoleID.callArgs))
	copy(argCopy, mmDeleteByRoleID.callArgs)

	mmDeleteByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDDone returns true if the count of the DeleteByRoleID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockDeleteByRoleIDDone() bool {
	if m.DeleteByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDMock.invocationsDone()
}

// MinimockDeleteByRoleIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockDeleteByRoleIDInspect() {
	for _, e := range m.DeleteByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDMock.defaultExpectation != nil && afterDeleteByRoleIDCounter < 1 {
		if m.DeleteByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleID at\n%s", m.DeleteByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleID at\n%s with params: %#v", m.DeleteByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleID != nil && afterDeleteByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleID at\n%s", m.funcDeleteByRoleIDOrigin)
	}

	if !m.DeleteByRoleIDMock.invocationsDone() && afterDeleteByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.DeleteByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDMock.expectedInvocations), m.DeleteByRoleIDMock.expectedInvocationsOrigin, afterDeleteByRoleIDCounter)
	}
}

type mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation
	expectations       []*RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation

	callArgs []*RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation specifies expectation struct of the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams
	paramPtrs          *RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParamPtrs
	expectationOrigins RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectationOrigins
	results            *RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams contains parameters of the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams struct {
	ctx           context.Context
	roleID        int64
	permissionIDs []int64
}

// RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParamPtrs struct {
	ctx           *context.Context
	roleID        *int64
	permissionIDs *[]int64
}

// RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsResults contains results of the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsResults struct {
	err error
}

// RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsOrigins contains origins of expectations of the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
type RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectationOrigins struct {
	origin              string
	originCtx           string
	originRoleID        string
	originPermissionIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Optional() *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	mmDeleteByRoleIDAndPermissionIDs.optional = true
	return mmDeleteByRoleIDAndPermissionIDs
}

// Expect sets up expected params for RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Expect(ctx context.Context, roleID int64, permissionIDs []int64) *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	if mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation{}
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by ExpectParams functions")
	}

	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.params = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs}
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRoleIDAndPermissionIDs.expectations {
		if minimock.Equal(e.params, mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.params) {
			mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.params)
		}
	}

	return mmDeleteByRoleIDAndPermissionIDs
}

// ExpectCtxParam1 sets up expected param ctx for RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) ExpectCtxParam1(ctx context.Context) *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	if mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation{}
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndPermissionIDs
}

// ExpectRoleIDParam2 sets up expected param roleID for RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) ExpectRoleIDParam2(roleID int64) *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	if mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation{}
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndPermissionIDs
}

// ExpectPermissionIDsParam3 sets up expected param permissionIDs for RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) ExpectPermissionIDsParam3(permissionIDs []int64) *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	if mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation{}
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.params != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Expect")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParamPtrs{}
	}
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.paramPtrs.permissionIDs = &permissionIDs
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.expectationOrigins.originPermissionIDs = minimock.CallerInfo(1)

	return mmDeleteByRoleIDAndPermissionIDs
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Inspect(f func(ctx context.Context, roleID int64, permissionIDs []int64)) *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	if mmDeleteByRoleIDAndPermissionIDs.mock.inspectFuncDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs")
	}

	mmDeleteByRoleIDAndPermissionIDs.mock.inspectFuncDeleteByRoleIDAndPermissionIDs = f

	return mmDeleteByRoleIDAndPermissionIDs
}

// Return sets up results that will be returned by RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Return(err error) *RolePermissionPrimeDBMock {
	if mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Set")
	}

	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation == nil {
		mmDeleteByRoleIDAndPermissionIDs.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation{mock: mmDeleteByRoleIDAndPermissionIDs.mock}
	}
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.results = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsResults{err}
	mmDeleteByRoleIDAndPermissionIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndPermissionIDs.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs method
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Set(f func(ctx context.Context, roleID int64, permissionIDs []int64) (err error)) *RolePermissionPrimeDBMock {
	if mmDeleteByRoleIDAndPermissionIDs.defaultExpectation != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs method")
	}

	if len(mmDeleteByRoleIDAndPermissionIDs.expectations) > 0 {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs method")
	}

	mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs = f
	mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndPermissionIDs.mock
}

// When sets expectation for the RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs which will trigger the result defined by the following
// Then helper
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) When(ctx context.Context, roleID int64, permissionIDs []int64) *RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation {
	if mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation{
		mock:               mmDeleteByRoleIDAndPermissionIDs.mock,
		params:             &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs},
		expectationOrigins: RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRoleIDAndPermissionIDs.expectations = append(mmDeleteByRoleIDAndPermissionIDs.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsExpectation) Then(err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsResults{err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.DeleteByRoleIDAndPermissionIDs should be invoked
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Times(n uint64) *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs {
	if n == 0 {
		mmDeleteByRoleIDAndPermissionIDs.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRoleIDAndPermissionIDs.expectedInvocations, n)
	mmDeleteByRoleIDAndPermissionIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRoleIDAndPermissionIDs
}

func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) invocationsDone() bool {
	if len(mmDeleteByRoleIDAndPermissionIDs.expectations) == 0 && mmDeleteByRoleIDAndPermissionIDs.defaultExpectation == nil && mmDeleteByRoleIDAndPermissionIDs.mock.funcDeleteByRoleIDAndPermissionIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndPermissionIDs.mock.afterDeleteByRoleIDAndPermissionIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRoleIDAndPermissionIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRoleIDAndPermissionIDs implements mm_repository.RolePermissionPrimeDB
func (mmDeleteByRoleIDAndPermissionIDs *RolePermissionPrimeDBMock) DeleteByRoleIDAndPermissionIDs(ctx context.Context, roleID int64, permissionIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRoleIDAndPermissionIDs.beforeDeleteByRoleIDAndPermissionIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRoleIDAndPermissionIDs.afterDeleteByRoleIDAndPermissionIDsCounter, 1)

	mmDeleteByRoleIDAndPermissionIDs.t.Helper()

	if mmDeleteByRoleIDAndPermissionIDs.inspectFuncDeleteByRoleIDAndPermissionIDs != nil {
		mmDeleteByRoleIDAndPermissionIDs.inspectFuncDeleteByRoleIDAndPermissionIDs(ctx, roleID, permissionIDs)
	}

	mm_params := RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs}

	// Record call args
	mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.mutex.Lock()
	mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.callArgs = append(mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.callArgs, &mm_params)
	mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.mutex.Unlock()

	for _, e := range mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams{ctx, roleID, permissionIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.permissionIDs != nil && !minimock.Equal(*mm_want_ptrs.permissionIDs, mm_got.permissionIDs) {
				mmDeleteByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs got unexpected parameter permissionIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.originPermissionIDs, *mm_want_ptrs.permissionIDs, mm_got.permissionIDs, minimock.Diff(*mm_want_ptrs.permissionIDs, mm_got.permissionIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRoleIDAndPermissionIDs.t.Errorf("RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRoleIDAndPermissionIDs.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRoleIDAndPermissionIDs.t.Fatal("No results are set for the RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs")
		}
		return (*mm_results).err
	}
	if mmDeleteByRoleIDAndPermissionIDs.funcDeleteByRoleIDAndPermissionIDs != nil {
		return mmDeleteByRoleIDAndPermissionIDs.funcDeleteByRoleIDAndPermissionIDs(ctx, roleID, permissionIDs)
	}
	mmDeleteByRoleIDAndPermissionIDs.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs. %v %v %v", ctx, roleID, permissionIDs)
	return
}

// DeleteByRoleIDAndPermissionIDsAfterCounter returns a count of finished RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs invocations
func (mmDeleteByRoleIDAndPermissionIDs *RolePermissionPrimeDBMock) DeleteByRoleIDAndPermissionIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndPermissionIDs.afterDeleteByRoleIDAndPermissionIDsCounter)
}

// DeleteByRoleIDAndPermissionIDsBeforeCounter returns a count of RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs invocations
func (mmDeleteByRoleIDAndPermissionIDs *RolePermissionPrimeDBMock) DeleteByRoleIDAndPermissionIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRoleIDAndPermissionIDs.beforeDeleteByRoleIDAndPermissionIDsCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRoleIDAndPermissionIDs *mRolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDs) Calls() []*RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams {
	mmDeleteByRoleIDAndPermissionIDs.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockDeleteByRoleIDAndPermissionIDsParams, len(mmDeleteByRoleIDAndPermissionIDs.callArgs))
	copy(argCopy, mmDeleteByRoleIDAndPermissionIDs.callArgs)

	mmDeleteByRoleIDAndPermissionIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRoleIDAndPermissionIDsDone returns true if the count of the DeleteByRoleIDAndPermissionIDs invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockDeleteByRoleIDAndPermissionIDsDone() bool {
	if m.DeleteByRoleIDAndPermissionIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRoleIDAndPermissionIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRoleIDAndPermissionIDsMock.invocationsDone()
}

// MinimockDeleteByRoleIDAndPermissionIDsInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockDeleteByRoleIDAndPermissionIDsInspect() {
	for _, e := range m.DeleteByRoleIDAndPermissionIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRoleIDAndPermissionIDsCounter := mm_atomic.LoadUint64(&m.afterDeleteByRoleIDAndPermissionIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation != nil && afterDeleteByRoleIDAndPermissionIDsCounter < 1 {
		if m.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs at\n%s", m.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs at\n%s with params: %#v", m.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRoleIDAndPermissionIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRoleIDAndPermissionIDs != nil && afterDeleteByRoleIDAndPermissionIDsCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs at\n%s", m.funcDeleteByRoleIDAndPermissionIDsOrigin)
	}

	if !m.DeleteByRoleIDAndPermissionIDsMock.invocationsDone() && afterDeleteByRoleIDAndPermissionIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.DeleteByRoleIDAndPermissionIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRoleIDAndPermissionIDsMock.expectedInvocations), m.DeleteByRoleIDAndPermissionIDsMock.expectedInvocationsOrigin, afterDeleteByRoleIDAndPermissionIDsCounter)
	}
}

type mRolePermissionPrimeDBMockDeleteByRolePermissionID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation
	expectations       []*RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation

	callArgs []*RolePermissionPrimeDBMockDeleteByRolePermissionIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation specifies expectation struct of the RolePermissionPrimeDB.DeleteByRolePermissionID
type RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockDeleteByRolePermissionIDParams
	paramPtrs          *RolePermissionPrimeDBMockDeleteByRolePermissionIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectationOrigins
	results            *RolePermissionPrimeDBMockDeleteByRolePermissionIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockDeleteByRolePermissionIDParams contains parameters of the RolePermissionPrimeDB.DeleteByRolePermissionID
type RolePermissionPrimeDBMockDeleteByRolePermissionIDParams struct {
	roleID       int64
	permissionID int64
}

// RolePermissionPrimeDBMockDeleteByRolePermissionIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.DeleteByRolePermissionID
type RolePermissionPrimeDBMockDeleteByRolePermissionIDParamPtrs struct {
	roleID       *int64
	permissionID *int64
}

// RolePermissionPrimeDBMockDeleteByRolePermissionIDResults contains results of the RolePermissionPrimeDB.DeleteByRolePermissionID
type RolePermissionPrimeDBMockDeleteByRolePermissionIDResults struct {
	err error
}

// RolePermissionPrimeDBMockDeleteByRolePermissionIDOrigins contains origins of expectations of the RolePermissionPrimeDB.DeleteByRolePermissionID
type RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectationOrigins struct {
	origin             string
	originRoleID       string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Optional() *mRolePermissionPrimeDBMockDeleteByRolePermissionID {
	mmDeleteByRolePermissionID.optional = true
	return mmDeleteByRolePermissionID
}

// Expect sets up expected params for RolePermissionPrimeDB.DeleteByRolePermissionID
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Expect(roleID int64, permissionID int64) *mRolePermissionPrimeDBMockDeleteByRolePermissionID {
	if mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Set")
	}

	if mmDeleteByRolePermissionID.defaultExpectation == nil {
		mmDeleteByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation{}
	}

	if mmDeleteByRolePermissionID.defaultExpectation.paramPtrs != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by ExpectParams functions")
	}

	mmDeleteByRolePermissionID.defaultExpectation.params = &RolePermissionPrimeDBMockDeleteByRolePermissionIDParams{roleID, permissionID}
	mmDeleteByRolePermissionID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteByRolePermissionID.expectations {
		if minimock.Equal(e.params, mmDeleteByRolePermissionID.defaultExpectation.params) {
			mmDeleteByRolePermissionID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteByRolePermissionID.defaultExpectation.params)
		}
	}

	return mmDeleteByRolePermissionID
}

// ExpectRoleIDParam1 sets up expected param roleID for RolePermissionPrimeDB.DeleteByRolePermissionID
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) ExpectRoleIDParam1(roleID int64) *mRolePermissionPrimeDBMockDeleteByRolePermissionID {
	if mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Set")
	}

	if mmDeleteByRolePermissionID.defaultExpectation == nil {
		mmDeleteByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation{}
	}

	if mmDeleteByRolePermissionID.defaultExpectation.params != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Expect")
	}

	if mmDeleteByRolePermissionID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRolePermissionID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRolePermissionIDParamPtrs{}
	}
	mmDeleteByRolePermissionID.defaultExpectation.paramPtrs.roleID = &roleID
	mmDeleteByRolePermissionID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDeleteByRolePermissionID
}

// ExpectPermissionIDParam2 sets up expected param permissionID for RolePermissionPrimeDB.DeleteByRolePermissionID
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) ExpectPermissionIDParam2(permissionID int64) *mRolePermissionPrimeDBMockDeleteByRolePermissionID {
	if mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Set")
	}

	if mmDeleteByRolePermissionID.defaultExpectation == nil {
		mmDeleteByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation{}
	}

	if mmDeleteByRolePermissionID.defaultExpectation.params != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Expect")
	}

	if mmDeleteByRolePermissionID.defaultExpectation.paramPtrs == nil {
		mmDeleteByRolePermissionID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockDeleteByRolePermissionIDParamPtrs{}
	}
	mmDeleteByRolePermissionID.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmDeleteByRolePermissionID.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmDeleteByRolePermissionID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.DeleteByRolePermissionID
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Inspect(f func(roleID int64, permissionID int64)) *mRolePermissionPrimeDBMockDeleteByRolePermissionID {
	if mmDeleteByRolePermissionID.mock.inspectFuncDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.DeleteByRolePermissionID")
	}

	mmDeleteByRolePermissionID.mock.inspectFuncDeleteByRolePermissionID = f

	return mmDeleteByRolePermissionID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.DeleteByRolePermissionID
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Return(err error) *RolePermissionPrimeDBMock {
	if mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Set")
	}

	if mmDeleteByRolePermissionID.defaultExpectation == nil {
		mmDeleteByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation{mock: mmDeleteByRolePermissionID.mock}
	}
	mmDeleteByRolePermissionID.defaultExpectation.results = &RolePermissionPrimeDBMockDeleteByRolePermissionIDResults{err}
	mmDeleteByRolePermissionID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteByRolePermissionID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.DeleteByRolePermissionID method
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Set(f func(roleID int64, permissionID int64) (err error)) *RolePermissionPrimeDBMock {
	if mmDeleteByRolePermissionID.defaultExpectation != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.DeleteByRolePermissionID method")
	}

	if len(mmDeleteByRolePermissionID.expectations) > 0 {
		mmDeleteByRolePermissionID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.DeleteByRolePermissionID method")
	}

	mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID = f
	mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionIDOrigin = minimock.CallerInfo(1)
	return mmDeleteByRolePermissionID.mock
}

// When sets expectation for the RolePermissionPrimeDB.DeleteByRolePermissionID which will trigger the result defined by the following
// Then helper
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) When(roleID int64, permissionID int64) *RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation {
	if mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.DeleteByRolePermissionID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation{
		mock:               mmDeleteByRolePermissionID.mock,
		params:             &RolePermissionPrimeDBMockDeleteByRolePermissionIDParams{roleID, permissionID},
		expectationOrigins: RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteByRolePermissionID.expectations = append(mmDeleteByRolePermissionID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.DeleteByRolePermissionID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockDeleteByRolePermissionIDExpectation) Then(err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockDeleteByRolePermissionIDResults{err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.DeleteByRolePermissionID should be invoked
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Times(n uint64) *mRolePermissionPrimeDBMockDeleteByRolePermissionID {
	if n == 0 {
		mmDeleteByRolePermissionID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.DeleteByRolePermissionID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteByRolePermissionID.expectedInvocations, n)
	mmDeleteByRolePermissionID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteByRolePermissionID
}

func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) invocationsDone() bool {
	if len(mmDeleteByRolePermissionID.expectations) == 0 && mmDeleteByRolePermissionID.defaultExpectation == nil && mmDeleteByRolePermissionID.mock.funcDeleteByRolePermissionID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteByRolePermissionID.mock.afterDeleteByRolePermissionIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteByRolePermissionID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteByRolePermissionID implements mm_repository.RolePermissionPrimeDB
func (mmDeleteByRolePermissionID *RolePermissionPrimeDBMock) DeleteByRolePermissionID(roleID int64, permissionID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteByRolePermissionID.beforeDeleteByRolePermissionIDCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteByRolePermissionID.afterDeleteByRolePermissionIDCounter, 1)

	mmDeleteByRolePermissionID.t.Helper()

	if mmDeleteByRolePermissionID.inspectFuncDeleteByRolePermissionID != nil {
		mmDeleteByRolePermissionID.inspectFuncDeleteByRolePermissionID(roleID, permissionID)
	}

	mm_params := RolePermissionPrimeDBMockDeleteByRolePermissionIDParams{roleID, permissionID}

	// Record call args
	mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.mutex.Lock()
	mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.callArgs = append(mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.callArgs, &mm_params)
	mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.mutex.Unlock()

	for _, e := range mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockDeleteByRolePermissionIDParams{roleID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDeleteByRolePermissionID.t.Errorf("RolePermissionPrimeDBMock.DeleteByRolePermissionID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmDeleteByRolePermissionID.t.Errorf("RolePermissionPrimeDBMock.DeleteByRolePermissionID got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteByRolePermissionID.t.Errorf("RolePermissionPrimeDBMock.DeleteByRolePermissionID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteByRolePermissionID.DeleteByRolePermissionIDMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteByRolePermissionID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.DeleteByRolePermissionID")
		}
		return (*mm_results).err
	}
	if mmDeleteByRolePermissionID.funcDeleteByRolePermissionID != nil {
		return mmDeleteByRolePermissionID.funcDeleteByRolePermissionID(roleID, permissionID)
	}
	mmDeleteByRolePermissionID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.DeleteByRolePermissionID. %v %v", roleID, permissionID)
	return
}

// DeleteByRolePermissionIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.DeleteByRolePermissionID invocations
func (mmDeleteByRolePermissionID *RolePermissionPrimeDBMock) DeleteByRolePermissionIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRolePermissionID.afterDeleteByRolePermissionIDCounter)
}

// DeleteByRolePermissionIDBeforeCounter returns a count of RolePermissionPrimeDBMock.DeleteByRolePermissionID invocations
func (mmDeleteByRolePermissionID *RolePermissionPrimeDBMock) DeleteByRolePermissionIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteByRolePermissionID.beforeDeleteByRolePermissionIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.DeleteByRolePermissionID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteByRolePermissionID *mRolePermissionPrimeDBMockDeleteByRolePermissionID) Calls() []*RolePermissionPrimeDBMockDeleteByRolePermissionIDParams {
	mmDeleteByRolePermissionID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockDeleteByRolePermissionIDParams, len(mmDeleteByRolePermissionID.callArgs))
	copy(argCopy, mmDeleteByRolePermissionID.callArgs)

	mmDeleteByRolePermissionID.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteByRolePermissionIDDone returns true if the count of the DeleteByRolePermissionID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockDeleteByRolePermissionIDDone() bool {
	if m.DeleteByRolePermissionIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteByRolePermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteByRolePermissionIDMock.invocationsDone()
}

// MinimockDeleteByRolePermissionIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockDeleteByRolePermissionIDInspect() {
	for _, e := range m.DeleteByRolePermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRolePermissionID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteByRolePermissionIDCounter := mm_atomic.LoadUint64(&m.afterDeleteByRolePermissionIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteByRolePermissionIDMock.defaultExpectation != nil && afterDeleteByRolePermissionIDCounter < 1 {
		if m.DeleteByRolePermissionIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRolePermissionID at\n%s", m.DeleteByRolePermissionIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRolePermissionID at\n%s with params: %#v", m.DeleteByRolePermissionIDMock.defaultExpectation.expectationOrigins.origin, *m.DeleteByRolePermissionIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteByRolePermissionID != nil && afterDeleteByRolePermissionIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.DeleteByRolePermissionID at\n%s", m.funcDeleteByRolePermissionIDOrigin)
	}

	if !m.DeleteByRolePermissionIDMock.invocationsDone() && afterDeleteByRolePermissionIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.DeleteByRolePermissionID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteByRolePermissionIDMock.expectedInvocations), m.DeleteByRolePermissionIDMock.expectedInvocationsOrigin, afterDeleteByRolePermissionIDCounter)
	}
}

type mRolePermissionPrimeDBMockGetAll struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockGetAllExpectation
	expectations       []*RolePermissionPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockGetAllExpectation specifies expectation struct of the RolePermissionPrimeDB.GetAll
type RolePermissionPrimeDBMockGetAllExpectation struct {
	mock *RolePermissionPrimeDBMock

	results      *RolePermissionPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// RolePermissionPrimeDBMockGetAllResults contains results of the RolePermissionPrimeDB.GetAll
type RolePermissionPrimeDBMockGetAllResults struct {
	ra1 []roleentity.RolePermission
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mRolePermissionPrimeDBMockGetAll) Optional() *mRolePermissionPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for RolePermissionPrimeDB.GetAll
func (mmGetAll *mRolePermissionPrimeDBMockGetAll) Expect() *mRolePermissionPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("RolePermissionPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &RolePermissionPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.GetAll
func (mmGetAll *mRolePermissionPrimeDBMockGetAll) Inspect(f func()) *mRolePermissionPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by RolePermissionPrimeDB.GetAll
func (mmGetAll *mRolePermissionPrimeDBMockGetAll) Return(ra1 []roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("RolePermissionPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &RolePermissionPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &RolePermissionPrimeDBMockGetAllResults{ra1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.GetAll method
func (mmGetAll *mRolePermissionPrimeDBMockGetAll) Set(f func() (ra1 []roleentity.RolePermission, err error)) *RolePermissionPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times RolePermissionPrimeDB.GetAll should be invoked
func (mmGetAll *mRolePermissionPrimeDBMockGetAll) Times(n uint64) *mRolePermissionPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mRolePermissionPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.RolePermissionPrimeDB
func (mmGetAll *RolePermissionPrimeDBMock) GetAll() (ra1 []roleentity.RolePermission, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the RolePermissionPrimeDBMock.GetAll")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished RolePermissionPrimeDBMock.GetAll invocations
func (mmGetAll *RolePermissionPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of RolePermissionPrimeDBMock.GetAll invocations
func (mmGetAll *RolePermissionPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to RolePermissionPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mRolePermissionPrimeDBMockGetByID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockGetByIDExpectation
	expectations       []*RolePermissionPrimeDBMockGetByIDExpectation

	callArgs []*RolePermissionPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockGetByIDExpectation specifies expectation struct of the RolePermissionPrimeDB.GetByID
type RolePermissionPrimeDBMockGetByIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockGetByIDParams
	paramPtrs          *RolePermissionPrimeDBMockGetByIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockGetByIDExpectationOrigins
	results            *RolePermissionPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockGetByIDParams contains parameters of the RolePermissionPrimeDB.GetByID
type RolePermissionPrimeDBMockGetByIDParams struct {
	id int64
}

// RolePermissionPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.GetByID
type RolePermissionPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// RolePermissionPrimeDBMockGetByIDResults contains results of the RolePermissionPrimeDB.GetByID
type RolePermissionPrimeDBMockGetByIDResults struct {
	r1  roleentity.RolePermission
	err error
}

// RolePermissionPrimeDBMockGetByIDOrigins contains origins of expectations of the RolePermissionPrimeDB.GetByID
type RolePermissionPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Optional() *mRolePermissionPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for RolePermissionPrimeDB.GetByID
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Expect(id int64) *mRolePermissionPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &RolePermissionPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &RolePermissionPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for RolePermissionPrimeDB.GetByID
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) ExpectIdParam1(id int64) *mRolePermissionPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &RolePermissionPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.GetByID
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Inspect(f func(id int64)) *mRolePermissionPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.GetByID
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Return(r1 roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &RolePermissionPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &RolePermissionPrimeDBMockGetByIDResults{r1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.GetByID method
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Set(f func(id int64) (r1 roleentity.RolePermission, err error)) *RolePermissionPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the RolePermissionPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) When(id int64) *RolePermissionPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &RolePermissionPrimeDBMockGetByIDParams{id},
		expectationOrigins: RolePermissionPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockGetByIDExpectation) Then(r1 roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockGetByIDResults{r1, err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.GetByID should be invoked
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Times(n uint64) *mRolePermissionPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mRolePermissionPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.RolePermissionPrimeDB
func (mmGetByID *RolePermissionPrimeDBMock) GetByID(id int64) (r1 roleentity.RolePermission, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := RolePermissionPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("RolePermissionPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("RolePermissionPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.GetByID")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.GetByID invocations
func (mmGetByID *RolePermissionPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of RolePermissionPrimeDBMock.GetByID invocations
func (mmGetByID *RolePermissionPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mRolePermissionPrimeDBMockGetByID) Calls() []*RolePermissionPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mRolePermissionPrimeDBMockGetByPermissionID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockGetByPermissionIDExpectation
	expectations       []*RolePermissionPrimeDBMockGetByPermissionIDExpectation

	callArgs []*RolePermissionPrimeDBMockGetByPermissionIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockGetByPermissionIDExpectation specifies expectation struct of the RolePermissionPrimeDB.GetByPermissionID
type RolePermissionPrimeDBMockGetByPermissionIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockGetByPermissionIDParams
	paramPtrs          *RolePermissionPrimeDBMockGetByPermissionIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockGetByPermissionIDExpectationOrigins
	results            *RolePermissionPrimeDBMockGetByPermissionIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockGetByPermissionIDParams contains parameters of the RolePermissionPrimeDB.GetByPermissionID
type RolePermissionPrimeDBMockGetByPermissionIDParams struct {
	permissionID int64
}

// RolePermissionPrimeDBMockGetByPermissionIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.GetByPermissionID
type RolePermissionPrimeDBMockGetByPermissionIDParamPtrs struct {
	permissionID *int64
}

// RolePermissionPrimeDBMockGetByPermissionIDResults contains results of the RolePermissionPrimeDB.GetByPermissionID
type RolePermissionPrimeDBMockGetByPermissionIDResults struct {
	ra1 []roleentity.RolePermission
	err error
}

// RolePermissionPrimeDBMockGetByPermissionIDOrigins contains origins of expectations of the RolePermissionPrimeDB.GetByPermissionID
type RolePermissionPrimeDBMockGetByPermissionIDExpectationOrigins struct {
	origin             string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Optional() *mRolePermissionPrimeDBMockGetByPermissionID {
	mmGetByPermissionID.optional = true
	return mmGetByPermissionID
}

// Expect sets up expected params for RolePermissionPrimeDB.GetByPermissionID
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Expect(permissionID int64) *mRolePermissionPrimeDBMockGetByPermissionID {
	if mmGetByPermissionID.mock.funcGetByPermissionID != nil {
		mmGetByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByPermissionID mock is already set by Set")
	}

	if mmGetByPermissionID.defaultExpectation == nil {
		mmGetByPermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByPermissionIDExpectation{}
	}

	if mmGetByPermissionID.defaultExpectation.paramPtrs != nil {
		mmGetByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByPermissionID mock is already set by ExpectParams functions")
	}

	mmGetByPermissionID.defaultExpectation.params = &RolePermissionPrimeDBMockGetByPermissionIDParams{permissionID}
	mmGetByPermissionID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByPermissionID.expectations {
		if minimock.Equal(e.params, mmGetByPermissionID.defaultExpectation.params) {
			mmGetByPermissionID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByPermissionID.defaultExpectation.params)
		}
	}

	return mmGetByPermissionID
}

// ExpectPermissionIDParam1 sets up expected param permissionID for RolePermissionPrimeDB.GetByPermissionID
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) ExpectPermissionIDParam1(permissionID int64) *mRolePermissionPrimeDBMockGetByPermissionID {
	if mmGetByPermissionID.mock.funcGetByPermissionID != nil {
		mmGetByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByPermissionID mock is already set by Set")
	}

	if mmGetByPermissionID.defaultExpectation == nil {
		mmGetByPermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByPermissionIDExpectation{}
	}

	if mmGetByPermissionID.defaultExpectation.params != nil {
		mmGetByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByPermissionID mock is already set by Expect")
	}

	if mmGetByPermissionID.defaultExpectation.paramPtrs == nil {
		mmGetByPermissionID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockGetByPermissionIDParamPtrs{}
	}
	mmGetByPermissionID.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmGetByPermissionID.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmGetByPermissionID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.GetByPermissionID
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Inspect(f func(permissionID int64)) *mRolePermissionPrimeDBMockGetByPermissionID {
	if mmGetByPermissionID.mock.inspectFuncGetByPermissionID != nil {
		mmGetByPermissionID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.GetByPermissionID")
	}

	mmGetByPermissionID.mock.inspectFuncGetByPermissionID = f

	return mmGetByPermissionID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.GetByPermissionID
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Return(ra1 []roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	if mmGetByPermissionID.mock.funcGetByPermissionID != nil {
		mmGetByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByPermissionID mock is already set by Set")
	}

	if mmGetByPermissionID.defaultExpectation == nil {
		mmGetByPermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByPermissionIDExpectation{mock: mmGetByPermissionID.mock}
	}
	mmGetByPermissionID.defaultExpectation.results = &RolePermissionPrimeDBMockGetByPermissionIDResults{ra1, err}
	mmGetByPermissionID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByPermissionID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.GetByPermissionID method
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Set(f func(permissionID int64) (ra1 []roleentity.RolePermission, err error)) *RolePermissionPrimeDBMock {
	if mmGetByPermissionID.defaultExpectation != nil {
		mmGetByPermissionID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.GetByPermissionID method")
	}

	if len(mmGetByPermissionID.expectations) > 0 {
		mmGetByPermissionID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.GetByPermissionID method")
	}

	mmGetByPermissionID.mock.funcGetByPermissionID = f
	mmGetByPermissionID.mock.funcGetByPermissionIDOrigin = minimock.CallerInfo(1)
	return mmGetByPermissionID.mock
}

// When sets expectation for the RolePermissionPrimeDB.GetByPermissionID which will trigger the result defined by the following
// Then helper
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) When(permissionID int64) *RolePermissionPrimeDBMockGetByPermissionIDExpectation {
	if mmGetByPermissionID.mock.funcGetByPermissionID != nil {
		mmGetByPermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByPermissionID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockGetByPermissionIDExpectation{
		mock:               mmGetByPermissionID.mock,
		params:             &RolePermissionPrimeDBMockGetByPermissionIDParams{permissionID},
		expectationOrigins: RolePermissionPrimeDBMockGetByPermissionIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByPermissionID.expectations = append(mmGetByPermissionID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.GetByPermissionID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockGetByPermissionIDExpectation) Then(ra1 []roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockGetByPermissionIDResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.GetByPermissionID should be invoked
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Times(n uint64) *mRolePermissionPrimeDBMockGetByPermissionID {
	if n == 0 {
		mmGetByPermissionID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.GetByPermissionID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByPermissionID.expectedInvocations, n)
	mmGetByPermissionID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByPermissionID
}

func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) invocationsDone() bool {
	if len(mmGetByPermissionID.expectations) == 0 && mmGetByPermissionID.defaultExpectation == nil && mmGetByPermissionID.mock.funcGetByPermissionID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByPermissionID.mock.afterGetByPermissionIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByPermissionID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByPermissionID implements mm_repository.RolePermissionPrimeDB
func (mmGetByPermissionID *RolePermissionPrimeDBMock) GetByPermissionID(permissionID int64) (ra1 []roleentity.RolePermission, err error) {
	mm_atomic.AddUint64(&mmGetByPermissionID.beforeGetByPermissionIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByPermissionID.afterGetByPermissionIDCounter, 1)

	mmGetByPermissionID.t.Helper()

	if mmGetByPermissionID.inspectFuncGetByPermissionID != nil {
		mmGetByPermissionID.inspectFuncGetByPermissionID(permissionID)
	}

	mm_params := RolePermissionPrimeDBMockGetByPermissionIDParams{permissionID}

	// Record call args
	mmGetByPermissionID.GetByPermissionIDMock.mutex.Lock()
	mmGetByPermissionID.GetByPermissionIDMock.callArgs = append(mmGetByPermissionID.GetByPermissionIDMock.callArgs, &mm_params)
	mmGetByPermissionID.GetByPermissionIDMock.mutex.Unlock()

	for _, e := range mmGetByPermissionID.GetByPermissionIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockGetByPermissionIDParams{permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmGetByPermissionID.t.Errorf("RolePermissionPrimeDBMock.GetByPermissionID got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByPermissionID.t.Errorf("RolePermissionPrimeDBMock.GetByPermissionID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByPermissionID.GetByPermissionIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByPermissionID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.GetByPermissionID")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetByPermissionID.funcGetByPermissionID != nil {
		return mmGetByPermissionID.funcGetByPermissionID(permissionID)
	}
	mmGetByPermissionID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.GetByPermissionID. %v", permissionID)
	return
}

// GetByPermissionIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.GetByPermissionID invocations
func (mmGetByPermissionID *RolePermissionPrimeDBMock) GetByPermissionIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByPermissionID.afterGetByPermissionIDCounter)
}

// GetByPermissionIDBeforeCounter returns a count of RolePermissionPrimeDBMock.GetByPermissionID invocations
func (mmGetByPermissionID *RolePermissionPrimeDBMock) GetByPermissionIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByPermissionID.beforeGetByPermissionIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.GetByPermissionID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByPermissionID *mRolePermissionPrimeDBMockGetByPermissionID) Calls() []*RolePermissionPrimeDBMockGetByPermissionIDParams {
	mmGetByPermissionID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockGetByPermissionIDParams, len(mmGetByPermissionID.callArgs))
	copy(argCopy, mmGetByPermissionID.callArgs)

	mmGetByPermissionID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByPermissionIDDone returns true if the count of the GetByPermissionID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockGetByPermissionIDDone() bool {
	if m.GetByPermissionIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByPermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByPermissionIDMock.invocationsDone()
}

// MinimockGetByPermissionIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockGetByPermissionIDInspect() {
	for _, e := range m.GetByPermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByPermissionID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByPermissionIDCounter := mm_atomic.LoadUint64(&m.afterGetByPermissionIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByPermissionIDMock.defaultExpectation != nil && afterGetByPermissionIDCounter < 1 {
		if m.GetByPermissionIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByPermissionID at\n%s", m.GetByPermissionIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByPermissionID at\n%s with params: %#v", m.GetByPermissionIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByPermissionIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByPermissionID != nil && afterGetByPermissionIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByPermissionID at\n%s", m.funcGetByPermissionIDOrigin)
	}

	if !m.GetByPermissionIDMock.invocationsDone() && afterGetByPermissionIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.GetByPermissionID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByPermissionIDMock.expectedInvocations), m.GetByPermissionIDMock.expectedInvocationsOrigin, afterGetByPermissionIDCounter)
	}
}

type mRolePermissionPrimeDBMockGetByRoleID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockGetByRoleIDExpectation
	expectations       []*RolePermissionPrimeDBMockGetByRoleIDExpectation

	callArgs []*RolePermissionPrimeDBMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockGetByRoleIDExpectation specifies expectation struct of the RolePermissionPrimeDB.GetByRoleID
type RolePermissionPrimeDBMockGetByRoleIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockGetByRoleIDParams
	paramPtrs          *RolePermissionPrimeDBMockGetByRoleIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockGetByRoleIDExpectationOrigins
	results            *RolePermissionPrimeDBMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockGetByRoleIDParams contains parameters of the RolePermissionPrimeDB.GetByRoleID
type RolePermissionPrimeDBMockGetByRoleIDParams struct {
	roleID int64
}

// RolePermissionPrimeDBMockGetByRoleIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.GetByRoleID
type RolePermissionPrimeDBMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// RolePermissionPrimeDBMockGetByRoleIDResults contains results of the RolePermissionPrimeDB.GetByRoleID
type RolePermissionPrimeDBMockGetByRoleIDResults struct {
	ra1 []roleentity.RolePermission
	err error
}

// RolePermissionPrimeDBMockGetByRoleIDOrigins contains origins of expectations of the RolePermissionPrimeDB.GetByRoleID
type RolePermissionPrimeDBMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Optional() *mRolePermissionPrimeDBMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for RolePermissionPrimeDB.GetByRoleID
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Expect(roleID int64) *mRolePermissionPrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &RolePermissionPrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &RolePermissionPrimeDBMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for RolePermissionPrimeDB.GetByRoleID
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mRolePermissionPrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &RolePermissionPrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.GetByRoleID
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Inspect(f func(roleID int64)) *mRolePermissionPrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.GetByRoleID
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Return(ra1 []roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &RolePermissionPrimeDBMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &RolePermissionPrimeDBMockGetByRoleIDResults{ra1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.GetByRoleID method
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Set(f func(roleID int64) (ra1 []roleentity.RolePermission, err error)) *RolePermissionPrimeDBMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the RolePermissionPrimeDB.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) When(roleID int64) *RolePermissionPrimeDBMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRoleID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &RolePermissionPrimeDBMockGetByRoleIDParams{roleID},
		expectationOrigins: RolePermissionPrimeDBMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockGetByRoleIDExpectation) Then(ra1 []roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockGetByRoleIDResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.GetByRoleID should be invoked
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Times(n uint64) *mRolePermissionPrimeDBMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_repository.RolePermissionPrimeDB
func (mmGetByRoleID *RolePermissionPrimeDBMock) GetByRoleID(roleID int64) (ra1 []roleentity.RolePermission, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := RolePermissionPrimeDBMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("RolePermissionPrimeDBMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("RolePermissionPrimeDBMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.GetByRoleID")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *RolePermissionPrimeDBMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of RolePermissionPrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *RolePermissionPrimeDBMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mRolePermissionPrimeDBMockGetByRoleID) Calls() []*RolePermissionPrimeDBMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

type mRolePermissionPrimeDBMockGetByRolePermissionID struct {
	optional           bool
	mock               *RolePermissionPrimeDBMock
	defaultExpectation *RolePermissionPrimeDBMockGetByRolePermissionIDExpectation
	expectations       []*RolePermissionPrimeDBMockGetByRolePermissionIDExpectation

	callArgs []*RolePermissionPrimeDBMockGetByRolePermissionIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePermissionPrimeDBMockGetByRolePermissionIDExpectation specifies expectation struct of the RolePermissionPrimeDB.GetByRolePermissionID
type RolePermissionPrimeDBMockGetByRolePermissionIDExpectation struct {
	mock               *RolePermissionPrimeDBMock
	params             *RolePermissionPrimeDBMockGetByRolePermissionIDParams
	paramPtrs          *RolePermissionPrimeDBMockGetByRolePermissionIDParamPtrs
	expectationOrigins RolePermissionPrimeDBMockGetByRolePermissionIDExpectationOrigins
	results            *RolePermissionPrimeDBMockGetByRolePermissionIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePermissionPrimeDBMockGetByRolePermissionIDParams contains parameters of the RolePermissionPrimeDB.GetByRolePermissionID
type RolePermissionPrimeDBMockGetByRolePermissionIDParams struct {
	roleID       int64
	permissionID int64
}

// RolePermissionPrimeDBMockGetByRolePermissionIDParamPtrs contains pointers to parameters of the RolePermissionPrimeDB.GetByRolePermissionID
type RolePermissionPrimeDBMockGetByRolePermissionIDParamPtrs struct {
	roleID       *int64
	permissionID *int64
}

// RolePermissionPrimeDBMockGetByRolePermissionIDResults contains results of the RolePermissionPrimeDB.GetByRolePermissionID
type RolePermissionPrimeDBMockGetByRolePermissionIDResults struct {
	r1  roleentity.RolePermission
	err error
}

// RolePermissionPrimeDBMockGetByRolePermissionIDOrigins contains origins of expectations of the RolePermissionPrimeDB.GetByRolePermissionID
type RolePermissionPrimeDBMockGetByRolePermissionIDExpectationOrigins struct {
	origin             string
	originRoleID       string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Optional() *mRolePermissionPrimeDBMockGetByRolePermissionID {
	mmGetByRolePermissionID.optional = true
	return mmGetByRolePermissionID
}

// Expect sets up expected params for RolePermissionPrimeDB.GetByRolePermissionID
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Expect(roleID int64, permissionID int64) *mRolePermissionPrimeDBMockGetByRolePermissionID {
	if mmGetByRolePermissionID.mock.funcGetByRolePermissionID != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Set")
	}

	if mmGetByRolePermissionID.defaultExpectation == nil {
		mmGetByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByRolePermissionIDExpectation{}
	}

	if mmGetByRolePermissionID.defaultExpectation.paramPtrs != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by ExpectParams functions")
	}

	mmGetByRolePermissionID.defaultExpectation.params = &RolePermissionPrimeDBMockGetByRolePermissionIDParams{roleID, permissionID}
	mmGetByRolePermissionID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRolePermissionID.expectations {
		if minimock.Equal(e.params, mmGetByRolePermissionID.defaultExpectation.params) {
			mmGetByRolePermissionID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRolePermissionID.defaultExpectation.params)
		}
	}

	return mmGetByRolePermissionID
}

// ExpectRoleIDParam1 sets up expected param roleID for RolePermissionPrimeDB.GetByRolePermissionID
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) ExpectRoleIDParam1(roleID int64) *mRolePermissionPrimeDBMockGetByRolePermissionID {
	if mmGetByRolePermissionID.mock.funcGetByRolePermissionID != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Set")
	}

	if mmGetByRolePermissionID.defaultExpectation == nil {
		mmGetByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByRolePermissionIDExpectation{}
	}

	if mmGetByRolePermissionID.defaultExpectation.params != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Expect")
	}

	if mmGetByRolePermissionID.defaultExpectation.paramPtrs == nil {
		mmGetByRolePermissionID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockGetByRolePermissionIDParamPtrs{}
	}
	mmGetByRolePermissionID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRolePermissionID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRolePermissionID
}

// ExpectPermissionIDParam2 sets up expected param permissionID for RolePermissionPrimeDB.GetByRolePermissionID
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) ExpectPermissionIDParam2(permissionID int64) *mRolePermissionPrimeDBMockGetByRolePermissionID {
	if mmGetByRolePermissionID.mock.funcGetByRolePermissionID != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Set")
	}

	if mmGetByRolePermissionID.defaultExpectation == nil {
		mmGetByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByRolePermissionIDExpectation{}
	}

	if mmGetByRolePermissionID.defaultExpectation.params != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Expect")
	}

	if mmGetByRolePermissionID.defaultExpectation.paramPtrs == nil {
		mmGetByRolePermissionID.defaultExpectation.paramPtrs = &RolePermissionPrimeDBMockGetByRolePermissionIDParamPtrs{}
	}
	mmGetByRolePermissionID.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmGetByRolePermissionID.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmGetByRolePermissionID
}

// Inspect accepts an inspector function that has same arguments as the RolePermissionPrimeDB.GetByRolePermissionID
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Inspect(f func(roleID int64, permissionID int64)) *mRolePermissionPrimeDBMockGetByRolePermissionID {
	if mmGetByRolePermissionID.mock.inspectFuncGetByRolePermissionID != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("Inspect function is already set for RolePermissionPrimeDBMock.GetByRolePermissionID")
	}

	mmGetByRolePermissionID.mock.inspectFuncGetByRolePermissionID = f

	return mmGetByRolePermissionID
}

// Return sets up results that will be returned by RolePermissionPrimeDB.GetByRolePermissionID
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Return(r1 roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	if mmGetByRolePermissionID.mock.funcGetByRolePermissionID != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Set")
	}

	if mmGetByRolePermissionID.defaultExpectation == nil {
		mmGetByRolePermissionID.defaultExpectation = &RolePermissionPrimeDBMockGetByRolePermissionIDExpectation{mock: mmGetByRolePermissionID.mock}
	}
	mmGetByRolePermissionID.defaultExpectation.results = &RolePermissionPrimeDBMockGetByRolePermissionIDResults{r1, err}
	mmGetByRolePermissionID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRolePermissionID.mock
}

// Set uses given function f to mock the RolePermissionPrimeDB.GetByRolePermissionID method
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Set(f func(roleID int64, permissionID int64) (r1 roleentity.RolePermission, err error)) *RolePermissionPrimeDBMock {
	if mmGetByRolePermissionID.defaultExpectation != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("Default expectation is already set for the RolePermissionPrimeDB.GetByRolePermissionID method")
	}

	if len(mmGetByRolePermissionID.expectations) > 0 {
		mmGetByRolePermissionID.mock.t.Fatalf("Some expectations are already set for the RolePermissionPrimeDB.GetByRolePermissionID method")
	}

	mmGetByRolePermissionID.mock.funcGetByRolePermissionID = f
	mmGetByRolePermissionID.mock.funcGetByRolePermissionIDOrigin = minimock.CallerInfo(1)
	return mmGetByRolePermissionID.mock
}

// When sets expectation for the RolePermissionPrimeDB.GetByRolePermissionID which will trigger the result defined by the following
// Then helper
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) When(roleID int64, permissionID int64) *RolePermissionPrimeDBMockGetByRolePermissionIDExpectation {
	if mmGetByRolePermissionID.mock.funcGetByRolePermissionID != nil {
		mmGetByRolePermissionID.mock.t.Fatalf("RolePermissionPrimeDBMock.GetByRolePermissionID mock is already set by Set")
	}

	expectation := &RolePermissionPrimeDBMockGetByRolePermissionIDExpectation{
		mock:               mmGetByRolePermissionID.mock,
		params:             &RolePermissionPrimeDBMockGetByRolePermissionIDParams{roleID, permissionID},
		expectationOrigins: RolePermissionPrimeDBMockGetByRolePermissionIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRolePermissionID.expectations = append(mmGetByRolePermissionID.expectations, expectation)
	return expectation
}

// Then sets up RolePermissionPrimeDB.GetByRolePermissionID return parameters for the expectation previously defined by the When method
func (e *RolePermissionPrimeDBMockGetByRolePermissionIDExpectation) Then(r1 roleentity.RolePermission, err error) *RolePermissionPrimeDBMock {
	e.results = &RolePermissionPrimeDBMockGetByRolePermissionIDResults{r1, err}
	return e.mock
}

// Times sets number of times RolePermissionPrimeDB.GetByRolePermissionID should be invoked
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Times(n uint64) *mRolePermissionPrimeDBMockGetByRolePermissionID {
	if n == 0 {
		mmGetByRolePermissionID.mock.t.Fatalf("Times of RolePermissionPrimeDBMock.GetByRolePermissionID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRolePermissionID.expectedInvocations, n)
	mmGetByRolePermissionID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRolePermissionID
}

func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) invocationsDone() bool {
	if len(mmGetByRolePermissionID.expectations) == 0 && mmGetByRolePermissionID.defaultExpectation == nil && mmGetByRolePermissionID.mock.funcGetByRolePermissionID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRolePermissionID.mock.afterGetByRolePermissionIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRolePermissionID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRolePermissionID implements mm_repository.RolePermissionPrimeDB
func (mmGetByRolePermissionID *RolePermissionPrimeDBMock) GetByRolePermissionID(roleID int64, permissionID int64) (r1 roleentity.RolePermission, err error) {
	mm_atomic.AddUint64(&mmGetByRolePermissionID.beforeGetByRolePermissionIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRolePermissionID.afterGetByRolePermissionIDCounter, 1)

	mmGetByRolePermissionID.t.Helper()

	if mmGetByRolePermissionID.inspectFuncGetByRolePermissionID != nil {
		mmGetByRolePermissionID.inspectFuncGetByRolePermissionID(roleID, permissionID)
	}

	mm_params := RolePermissionPrimeDBMockGetByRolePermissionIDParams{roleID, permissionID}

	// Record call args
	mmGetByRolePermissionID.GetByRolePermissionIDMock.mutex.Lock()
	mmGetByRolePermissionID.GetByRolePermissionIDMock.callArgs = append(mmGetByRolePermissionID.GetByRolePermissionIDMock.callArgs, &mm_params)
	mmGetByRolePermissionID.GetByRolePermissionIDMock.mutex.Unlock()

	for _, e := range mmGetByRolePermissionID.GetByRolePermissionIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.paramPtrs

		mm_got := RolePermissionPrimeDBMockGetByRolePermissionIDParams{roleID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRolePermissionID.t.Errorf("RolePermissionPrimeDBMock.GetByRolePermissionID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmGetByRolePermissionID.t.Errorf("RolePermissionPrimeDBMock.GetByRolePermissionID got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRolePermissionID.t.Errorf("RolePermissionPrimeDBMock.GetByRolePermissionID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRolePermissionID.GetByRolePermissionIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRolePermissionID.t.Fatal("No results are set for the RolePermissionPrimeDBMock.GetByRolePermissionID")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmGetByRolePermissionID.funcGetByRolePermissionID != nil {
		return mmGetByRolePermissionID.funcGetByRolePermissionID(roleID, permissionID)
	}
	mmGetByRolePermissionID.t.Fatalf("Unexpected call to RolePermissionPrimeDBMock.GetByRolePermissionID. %v %v", roleID, permissionID)
	return
}

// GetByRolePermissionIDAfterCounter returns a count of finished RolePermissionPrimeDBMock.GetByRolePermissionID invocations
func (mmGetByRolePermissionID *RolePermissionPrimeDBMock) GetByRolePermissionIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRolePermissionID.afterGetByRolePermissionIDCounter)
}

// GetByRolePermissionIDBeforeCounter returns a count of RolePermissionPrimeDBMock.GetByRolePermissionID invocations
func (mmGetByRolePermissionID *RolePermissionPrimeDBMock) GetByRolePermissionIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRolePermissionID.beforeGetByRolePermissionIDCounter)
}

// Calls returns a list of arguments used in each call to RolePermissionPrimeDBMock.GetByRolePermissionID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRolePermissionID *mRolePermissionPrimeDBMockGetByRolePermissionID) Calls() []*RolePermissionPrimeDBMockGetByRolePermissionIDParams {
	mmGetByRolePermissionID.mutex.RLock()

	argCopy := make([]*RolePermissionPrimeDBMockGetByRolePermissionIDParams, len(mmGetByRolePermissionID.callArgs))
	copy(argCopy, mmGetByRolePermissionID.callArgs)

	mmGetByRolePermissionID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRolePermissionIDDone returns true if the count of the GetByRolePermissionID invocations corresponds
// the number of defined expectations
func (m *RolePermissionPrimeDBMock) MinimockGetByRolePermissionIDDone() bool {
	if m.GetByRolePermissionIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRolePermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRolePermissionIDMock.invocationsDone()
}

// MinimockGetByRolePermissionIDInspect logs each unmet expectation
func (m *RolePermissionPrimeDBMock) MinimockGetByRolePermissionIDInspect() {
	for _, e := range m.GetByRolePermissionIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRolePermissionID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRolePermissionIDCounter := mm_atomic.LoadUint64(&m.afterGetByRolePermissionIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRolePermissionIDMock.defaultExpectation != nil && afterGetByRolePermissionIDCounter < 1 {
		if m.GetByRolePermissionIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRolePermissionID at\n%s", m.GetByRolePermissionIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRolePermissionID at\n%s with params: %#v", m.GetByRolePermissionIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRolePermissionIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRolePermissionID != nil && afterGetByRolePermissionIDCounter < 1 {
		m.t.Errorf("Expected call to RolePermissionPrimeDBMock.GetByRolePermissionID at\n%s", m.funcGetByRolePermissionIDOrigin)
	}

	if !m.GetByRolePermissionIDMock.invocationsDone() && afterGetByRolePermissionIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePermissionPrimeDBMock.GetByRolePermissionID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRolePermissionIDMock.expectedInvocations), m.GetByRolePermissionIDMock.expectedInvocationsOrigin, afterGetByRolePermissionIDCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *RolePermissionPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockCreateByRoleIDAndPermissionIDsInspect()

			m.MinimockDeleteByPermissionIDInspect()

			m.MinimockDeleteByRoleIDInspect()

			m.MinimockDeleteByRoleIDAndPermissionIDsInspect()

			m.MinimockDeleteByRolePermissionIDInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByPermissionIDInspect()

			m.MinimockGetByRoleIDInspect()

			m.MinimockGetByRolePermissionIDInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *RolePermissionPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *RolePermissionPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockCreateByRoleIDAndPermissionIDsDone() &&
		m.MinimockDeleteByPermissionIDDone() &&
		m.MinimockDeleteByRoleIDDone() &&
		m.MinimockDeleteByRoleIDAndPermissionIDsDone() &&
		m.MinimockDeleteByRolePermissionIDDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByPermissionIDDone() &&
		m.MinimockGetByRoleIDDone() &&
		m.MinimockGetByRolePermissionIDDone()
}
