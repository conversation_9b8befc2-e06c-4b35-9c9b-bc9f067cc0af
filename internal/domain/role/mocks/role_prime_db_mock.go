// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository.RolePrimeDB -o role_prime_db_mock.go -n RolePrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"github.com/gojuno/minimock/v3"
)

// RolePrimeDBMock implements mm_repository.RolePrimeDB
type RolePrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(ctx context.Context, role roleentity.Role) (r1 roleentity.Role, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(ctx context.Context, role roleentity.Role)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mRolePrimeDBMockCreate

	funcDeactivateByIDs          func(roleIDs []int64) (err error)
	funcDeactivateByIDsOrigin    string
	inspectFuncDeactivateByIDs   func(roleIDs []int64)
	afterDeactivateByIDsCounter  uint64
	beforeDeactivateByIDsCounter uint64
	DeactivateByIDsMock          mRolePrimeDBMockDeactivateByIDs

	funcExistByName          func(name string) (b1 bool, err error)
	funcExistByNameOrigin    string
	inspectFuncExistByName   func(name string)
	afterExistByNameCounter  uint64
	beforeExistByNameCounter uint64
	ExistByNameMock          mRolePrimeDBMockExistByName

	funcGetAll          func() (ra1 []roleentity.Role, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mRolePrimeDBMockGetAll

	funcGetAllByUserID          func(userID int64) (ra1 []roleentity.Role, err error)
	funcGetAllByUserIDOrigin    string
	inspectFuncGetAllByUserID   func(userID int64)
	afterGetAllByUserIDCounter  uint64
	beforeGetAllByUserIDCounter uint64
	GetAllByUserIDMock          mRolePrimeDBMockGetAllByUserID

	funcGetAllWithProductIDByUserID          func(userID int64) (ra1 []roleentity.RoleWithProductID, err error)
	funcGetAllWithProductIDByUserIDOrigin    string
	inspectFuncGetAllWithProductIDByUserID   func(userID int64)
	afterGetAllWithProductIDByUserIDCounter  uint64
	beforeGetAllWithProductIDByUserIDCounter uint64
	GetAllWithProductIDByUserIDMock          mRolePrimeDBMockGetAllWithProductIDByUserID

	funcGetByID          func(id int64) (r1 roleentity.Role, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mRolePrimeDBMockGetByID

	funcGetByProductID          func(productID int64) (ra1 []roleentity.Role, err error)
	funcGetByProductIDOrigin    string
	inspectFuncGetByProductID   func(productID int64)
	afterGetByProductIDCounter  uint64
	beforeGetByProductIDCounter uint64
	GetByProductIDMock          mRolePrimeDBMockGetByProductID

	funcGetByProductIDAndIsActive          func(productID int64, isActive bool) (aa1 []roleentity.AdminRole, err error)
	funcGetByProductIDAndIsActiveOrigin    string
	inspectFuncGetByProductIDAndIsActive   func(productID int64, isActive bool)
	afterGetByProductIDAndIsActiveCounter  uint64
	beforeGetByProductIDAndIsActiveCounter uint64
	GetByProductIDAndIsActiveMock          mRolePrimeDBMockGetByProductIDAndIsActive

	funcGetByRoleIDAndProductID          func(roleID int64, productID int64) (r1 roleentity.Role, err error)
	funcGetByRoleIDAndProductIDOrigin    string
	inspectFuncGetByRoleIDAndProductID   func(roleID int64, productID int64)
	afterGetByRoleIDAndProductIDCounter  uint64
	beforeGetByRoleIDAndProductIDCounter uint64
	GetByRoleIDAndProductIDMock          mRolePrimeDBMockGetByRoleIDAndProductID

	funcGetBySystemType          func(isSystemRole bool) (ra1 []roleentity.Role, err error)
	funcGetBySystemTypeOrigin    string
	inspectFuncGetBySystemType   func(isSystemRole bool)
	afterGetBySystemTypeCounter  uint64
	beforeGetBySystemTypeCounter uint64
	GetBySystemTypeMock          mRolePrimeDBMockGetBySystemType

	funcGetOwnerRole          func() (r1 roleentity.Role, err error)
	funcGetOwnerRoleOrigin    string
	inspectFuncGetOwnerRole   func()
	afterGetOwnerRoleCounter  uint64
	beforeGetOwnerRoleCounter uint64
	GetOwnerRoleMock          mRolePrimeDBMockGetOwnerRole

	funcGetRoleCategoryLinks          func(ctx context.Context) (ra1 []roleentity.RoleCategoryLink, err error)
	funcGetRoleCategoryLinksOrigin    string
	inspectFuncGetRoleCategoryLinks   func(ctx context.Context)
	afterGetRoleCategoryLinksCounter  uint64
	beforeGetRoleCategoryLinksCounter uint64
	GetRoleCategoryLinksMock          mRolePrimeDBMockGetRoleCategoryLinks

	funcGetRoleWithProductByParticipantID          func(participantID int64) (ra1 []roleentity.Role, err error)
	funcGetRoleWithProductByParticipantIDOrigin    string
	inspectFuncGetRoleWithProductByParticipantID   func(participantID int64)
	afterGetRoleWithProductByParticipantIDCounter  uint64
	beforeGetRoleWithProductByParticipantIDCounter uint64
	GetRoleWithProductByParticipantIDMock          mRolePrimeDBMockGetRoleWithProductByParticipantID

	funcGetRolesWithProductByParticipantIDs          func(participantIDs []int64) (ra1 []roleentity.Role, err error)
	funcGetRolesWithProductByParticipantIDsOrigin    string
	inspectFuncGetRolesWithProductByParticipantIDs   func(participantIDs []int64)
	afterGetRolesWithProductByParticipantIDsCounter  uint64
	beforeGetRolesWithProductByParticipantIDsCounter uint64
	GetRolesWithProductByParticipantIDsMock          mRolePrimeDBMockGetRolesWithProductByParticipantIDs

	funcGetRolesWithStats          func(productID int64) (ra1 []roleentity.RoleWithStats, err error)
	funcGetRolesWithStatsOrigin    string
	inspectFuncGetRolesWithStats   func(productID int64)
	afterGetRolesWithStatsCounter  uint64
	beforeGetRolesWithStatsCounter uint64
	GetRolesWithStatsMock          mRolePrimeDBMockGetRolesWithStats

	funcGetSystemRolesWithStats          func(ctx context.Context) (ra1 []roleentity.RoleWithStats, err error)
	funcGetSystemRolesWithStatsOrigin    string
	inspectFuncGetSystemRolesWithStats   func(ctx context.Context)
	afterGetSystemRolesWithStatsCounter  uint64
	beforeGetSystemRolesWithStatsCounter uint64
	GetSystemRolesWithStatsMock          mRolePrimeDBMockGetSystemRolesWithStats

	funcUpdate          func(ctx context.Context, role roleentity.RoleUpdateData) (r1 roleentity.Role, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(ctx context.Context, role roleentity.RoleUpdateData)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mRolePrimeDBMockUpdate
}

// NewRolePrimeDBMock returns a mock for mm_repository.RolePrimeDB
func NewRolePrimeDBMock(t minimock.Tester) *RolePrimeDBMock {
	m := &RolePrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mRolePrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*RolePrimeDBMockCreateParams{}

	m.DeactivateByIDsMock = mRolePrimeDBMockDeactivateByIDs{mock: m}
	m.DeactivateByIDsMock.callArgs = []*RolePrimeDBMockDeactivateByIDsParams{}

	m.ExistByNameMock = mRolePrimeDBMockExistByName{mock: m}
	m.ExistByNameMock.callArgs = []*RolePrimeDBMockExistByNameParams{}

	m.GetAllMock = mRolePrimeDBMockGetAll{mock: m}

	m.GetAllByUserIDMock = mRolePrimeDBMockGetAllByUserID{mock: m}
	m.GetAllByUserIDMock.callArgs = []*RolePrimeDBMockGetAllByUserIDParams{}

	m.GetAllWithProductIDByUserIDMock = mRolePrimeDBMockGetAllWithProductIDByUserID{mock: m}
	m.GetAllWithProductIDByUserIDMock.callArgs = []*RolePrimeDBMockGetAllWithProductIDByUserIDParams{}

	m.GetByIDMock = mRolePrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*RolePrimeDBMockGetByIDParams{}

	m.GetByProductIDMock = mRolePrimeDBMockGetByProductID{mock: m}
	m.GetByProductIDMock.callArgs = []*RolePrimeDBMockGetByProductIDParams{}

	m.GetByProductIDAndIsActiveMock = mRolePrimeDBMockGetByProductIDAndIsActive{mock: m}
	m.GetByProductIDAndIsActiveMock.callArgs = []*RolePrimeDBMockGetByProductIDAndIsActiveParams{}

	m.GetByRoleIDAndProductIDMock = mRolePrimeDBMockGetByRoleIDAndProductID{mock: m}
	m.GetByRoleIDAndProductIDMock.callArgs = []*RolePrimeDBMockGetByRoleIDAndProductIDParams{}

	m.GetBySystemTypeMock = mRolePrimeDBMockGetBySystemType{mock: m}
	m.GetBySystemTypeMock.callArgs = []*RolePrimeDBMockGetBySystemTypeParams{}

	m.GetOwnerRoleMock = mRolePrimeDBMockGetOwnerRole{mock: m}

	m.GetRoleCategoryLinksMock = mRolePrimeDBMockGetRoleCategoryLinks{mock: m}
	m.GetRoleCategoryLinksMock.callArgs = []*RolePrimeDBMockGetRoleCategoryLinksParams{}

	m.GetRoleWithProductByParticipantIDMock = mRolePrimeDBMockGetRoleWithProductByParticipantID{mock: m}
	m.GetRoleWithProductByParticipantIDMock.callArgs = []*RolePrimeDBMockGetRoleWithProductByParticipantIDParams{}

	m.GetRolesWithProductByParticipantIDsMock = mRolePrimeDBMockGetRolesWithProductByParticipantIDs{mock: m}
	m.GetRolesWithProductByParticipantIDsMock.callArgs = []*RolePrimeDBMockGetRolesWithProductByParticipantIDsParams{}

	m.GetRolesWithStatsMock = mRolePrimeDBMockGetRolesWithStats{mock: m}
	m.GetRolesWithStatsMock.callArgs = []*RolePrimeDBMockGetRolesWithStatsParams{}

	m.GetSystemRolesWithStatsMock = mRolePrimeDBMockGetSystemRolesWithStats{mock: m}
	m.GetSystemRolesWithStatsMock.callArgs = []*RolePrimeDBMockGetSystemRolesWithStatsParams{}

	m.UpdateMock = mRolePrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*RolePrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mRolePrimeDBMockCreate struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockCreateExpectation
	expectations       []*RolePrimeDBMockCreateExpectation

	callArgs []*RolePrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockCreateExpectation specifies expectation struct of the RolePrimeDB.Create
type RolePrimeDBMockCreateExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockCreateParams
	paramPtrs          *RolePrimeDBMockCreateParamPtrs
	expectationOrigins RolePrimeDBMockCreateExpectationOrigins
	results            *RolePrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockCreateParams contains parameters of the RolePrimeDB.Create
type RolePrimeDBMockCreateParams struct {
	ctx  context.Context
	role roleentity.Role
}

// RolePrimeDBMockCreateParamPtrs contains pointers to parameters of the RolePrimeDB.Create
type RolePrimeDBMockCreateParamPtrs struct {
	ctx  *context.Context
	role *roleentity.Role
}

// RolePrimeDBMockCreateResults contains results of the RolePrimeDB.Create
type RolePrimeDBMockCreateResults struct {
	r1  roleentity.Role
	err error
}

// RolePrimeDBMockCreateOrigins contains origins of expectations of the RolePrimeDB.Create
type RolePrimeDBMockCreateExpectationOrigins struct {
	origin     string
	originCtx  string
	originRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mRolePrimeDBMockCreate) Optional() *mRolePrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for RolePrimeDB.Create
func (mmCreate *mRolePrimeDBMockCreate) Expect(ctx context.Context, role roleentity.Role) *mRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &RolePrimeDBMockCreateParams{ctx, role}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCtxParam1 sets up expected param ctx for RolePrimeDB.Create
func (mmCreate *mRolePrimeDBMockCreate) ExpectCtxParam1(ctx context.Context) *mRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &RolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.ctx = &ctx
	mmCreate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmCreate
}

// ExpectRoleParam2 sets up expected param role for RolePrimeDB.Create
func (mmCreate *mRolePrimeDBMockCreate) ExpectRoleParam2(role roleentity.Role) *mRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &RolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.role = &role
	mmCreate.defaultExpectation.expectationOrigins.originRole = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.Create
func (mmCreate *mRolePrimeDBMockCreate) Inspect(f func(ctx context.Context, role roleentity.Role)) *mRolePrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by RolePrimeDB.Create
func (mmCreate *mRolePrimeDBMockCreate) Return(r1 roleentity.Role, err error) *RolePrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &RolePrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &RolePrimeDBMockCreateResults{r1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the RolePrimeDB.Create method
func (mmCreate *mRolePrimeDBMockCreate) Set(f func(ctx context.Context, role roleentity.Role) (r1 roleentity.Role, err error)) *RolePrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the RolePrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mRolePrimeDBMockCreate) When(ctx context.Context, role roleentity.Role) *RolePrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("RolePrimeDBMock.Create mock is already set by Set")
	}

	expectation := &RolePrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &RolePrimeDBMockCreateParams{ctx, role},
		expectationOrigins: RolePrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockCreateExpectation) Then(r1 roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockCreateResults{r1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.Create should be invoked
func (mmCreate *mRolePrimeDBMockCreate) Times(n uint64) *mRolePrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of RolePrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mRolePrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.RolePrimeDB
func (mmCreate *RolePrimeDBMock) Create(ctx context.Context, role roleentity.Role) (r1 roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(ctx, role)
	}

	mm_params := RolePrimeDBMockCreateParams{ctx, role}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockCreateParams{ctx, role}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmCreate.t.Errorf("RolePrimeDBMock.Create got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.role != nil && !minimock.Equal(*mm_want_ptrs.role, mm_got.role) {
				mmCreate.t.Errorf("RolePrimeDBMock.Create got unexpected parameter role, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originRole, *mm_want_ptrs.role, mm_got.role, minimock.Diff(*mm_want_ptrs.role, mm_got.role))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("RolePrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the RolePrimeDBMock.Create")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(ctx, role)
	}
	mmCreate.t.Fatalf("Unexpected call to RolePrimeDBMock.Create. %v %v", ctx, role)
	return
}

// CreateAfterCounter returns a count of finished RolePrimeDBMock.Create invocations
func (mmCreate *RolePrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of RolePrimeDBMock.Create invocations
func (mmCreate *RolePrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mRolePrimeDBMockCreate) Calls() []*RolePrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mRolePrimeDBMockDeactivateByIDs struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockDeactivateByIDsExpectation
	expectations       []*RolePrimeDBMockDeactivateByIDsExpectation

	callArgs []*RolePrimeDBMockDeactivateByIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockDeactivateByIDsExpectation specifies expectation struct of the RolePrimeDB.DeactivateByIDs
type RolePrimeDBMockDeactivateByIDsExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockDeactivateByIDsParams
	paramPtrs          *RolePrimeDBMockDeactivateByIDsParamPtrs
	expectationOrigins RolePrimeDBMockDeactivateByIDsExpectationOrigins
	results            *RolePrimeDBMockDeactivateByIDsResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockDeactivateByIDsParams contains parameters of the RolePrimeDB.DeactivateByIDs
type RolePrimeDBMockDeactivateByIDsParams struct {
	roleIDs []int64
}

// RolePrimeDBMockDeactivateByIDsParamPtrs contains pointers to parameters of the RolePrimeDB.DeactivateByIDs
type RolePrimeDBMockDeactivateByIDsParamPtrs struct {
	roleIDs *[]int64
}

// RolePrimeDBMockDeactivateByIDsResults contains results of the RolePrimeDB.DeactivateByIDs
type RolePrimeDBMockDeactivateByIDsResults struct {
	err error
}

// RolePrimeDBMockDeactivateByIDsOrigins contains origins of expectations of the RolePrimeDB.DeactivateByIDs
type RolePrimeDBMockDeactivateByIDsExpectationOrigins struct {
	origin        string
	originRoleIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Optional() *mRolePrimeDBMockDeactivateByIDs {
	mmDeactivateByIDs.optional = true
	return mmDeactivateByIDs
}

// Expect sets up expected params for RolePrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Expect(roleIDs []int64) *mRolePrimeDBMockDeactivateByIDs {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("RolePrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	if mmDeactivateByIDs.defaultExpectation == nil {
		mmDeactivateByIDs.defaultExpectation = &RolePrimeDBMockDeactivateByIDsExpectation{}
	}

	if mmDeactivateByIDs.defaultExpectation.paramPtrs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("RolePrimeDBMock.DeactivateByIDs mock is already set by ExpectParams functions")
	}

	mmDeactivateByIDs.defaultExpectation.params = &RolePrimeDBMockDeactivateByIDsParams{roleIDs}
	mmDeactivateByIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeactivateByIDs.expectations {
		if minimock.Equal(e.params, mmDeactivateByIDs.defaultExpectation.params) {
			mmDeactivateByIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeactivateByIDs.defaultExpectation.params)
		}
	}

	return mmDeactivateByIDs
}

// ExpectRoleIDsParam1 sets up expected param roleIDs for RolePrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) ExpectRoleIDsParam1(roleIDs []int64) *mRolePrimeDBMockDeactivateByIDs {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("RolePrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	if mmDeactivateByIDs.defaultExpectation == nil {
		mmDeactivateByIDs.defaultExpectation = &RolePrimeDBMockDeactivateByIDsExpectation{}
	}

	if mmDeactivateByIDs.defaultExpectation.params != nil {
		mmDeactivateByIDs.mock.t.Fatalf("RolePrimeDBMock.DeactivateByIDs mock is already set by Expect")
	}

	if mmDeactivateByIDs.defaultExpectation.paramPtrs == nil {
		mmDeactivateByIDs.defaultExpectation.paramPtrs = &RolePrimeDBMockDeactivateByIDsParamPtrs{}
	}
	mmDeactivateByIDs.defaultExpectation.paramPtrs.roleIDs = &roleIDs
	mmDeactivateByIDs.defaultExpectation.expectationOrigins.originRoleIDs = minimock.CallerInfo(1)

	return mmDeactivateByIDs
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Inspect(f func(roleIDs []int64)) *mRolePrimeDBMockDeactivateByIDs {
	if mmDeactivateByIDs.mock.inspectFuncDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.DeactivateByIDs")
	}

	mmDeactivateByIDs.mock.inspectFuncDeactivateByIDs = f

	return mmDeactivateByIDs
}

// Return sets up results that will be returned by RolePrimeDB.DeactivateByIDs
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Return(err error) *RolePrimeDBMock {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("RolePrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	if mmDeactivateByIDs.defaultExpectation == nil {
		mmDeactivateByIDs.defaultExpectation = &RolePrimeDBMockDeactivateByIDsExpectation{mock: mmDeactivateByIDs.mock}
	}
	mmDeactivateByIDs.defaultExpectation.results = &RolePrimeDBMockDeactivateByIDsResults{err}
	mmDeactivateByIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeactivateByIDs.mock
}

// Set uses given function f to mock the RolePrimeDB.DeactivateByIDs method
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Set(f func(roleIDs []int64) (err error)) *RolePrimeDBMock {
	if mmDeactivateByIDs.defaultExpectation != nil {
		mmDeactivateByIDs.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.DeactivateByIDs method")
	}

	if len(mmDeactivateByIDs.expectations) > 0 {
		mmDeactivateByIDs.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.DeactivateByIDs method")
	}

	mmDeactivateByIDs.mock.funcDeactivateByIDs = f
	mmDeactivateByIDs.mock.funcDeactivateByIDsOrigin = minimock.CallerInfo(1)
	return mmDeactivateByIDs.mock
}

// When sets expectation for the RolePrimeDB.DeactivateByIDs which will trigger the result defined by the following
// Then helper
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) When(roleIDs []int64) *RolePrimeDBMockDeactivateByIDsExpectation {
	if mmDeactivateByIDs.mock.funcDeactivateByIDs != nil {
		mmDeactivateByIDs.mock.t.Fatalf("RolePrimeDBMock.DeactivateByIDs mock is already set by Set")
	}

	expectation := &RolePrimeDBMockDeactivateByIDsExpectation{
		mock:               mmDeactivateByIDs.mock,
		params:             &RolePrimeDBMockDeactivateByIDsParams{roleIDs},
		expectationOrigins: RolePrimeDBMockDeactivateByIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeactivateByIDs.expectations = append(mmDeactivateByIDs.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.DeactivateByIDs return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockDeactivateByIDsExpectation) Then(err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockDeactivateByIDsResults{err}
	return e.mock
}

// Times sets number of times RolePrimeDB.DeactivateByIDs should be invoked
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Times(n uint64) *mRolePrimeDBMockDeactivateByIDs {
	if n == 0 {
		mmDeactivateByIDs.mock.t.Fatalf("Times of RolePrimeDBMock.DeactivateByIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeactivateByIDs.expectedInvocations, n)
	mmDeactivateByIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeactivateByIDs
}

func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) invocationsDone() bool {
	if len(mmDeactivateByIDs.expectations) == 0 && mmDeactivateByIDs.defaultExpectation == nil && mmDeactivateByIDs.mock.funcDeactivateByIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeactivateByIDs.mock.afterDeactivateByIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeactivateByIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeactivateByIDs implements mm_repository.RolePrimeDB
func (mmDeactivateByIDs *RolePrimeDBMock) DeactivateByIDs(roleIDs []int64) (err error) {
	mm_atomic.AddUint64(&mmDeactivateByIDs.beforeDeactivateByIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmDeactivateByIDs.afterDeactivateByIDsCounter, 1)

	mmDeactivateByIDs.t.Helper()

	if mmDeactivateByIDs.inspectFuncDeactivateByIDs != nil {
		mmDeactivateByIDs.inspectFuncDeactivateByIDs(roleIDs)
	}

	mm_params := RolePrimeDBMockDeactivateByIDsParams{roleIDs}

	// Record call args
	mmDeactivateByIDs.DeactivateByIDsMock.mutex.Lock()
	mmDeactivateByIDs.DeactivateByIDsMock.callArgs = append(mmDeactivateByIDs.DeactivateByIDsMock.callArgs, &mm_params)
	mmDeactivateByIDs.DeactivateByIDsMock.mutex.Unlock()

	for _, e := range mmDeactivateByIDs.DeactivateByIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.params
		mm_want_ptrs := mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockDeactivateByIDsParams{roleIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleIDs != nil && !minimock.Equal(*mm_want_ptrs.roleIDs, mm_got.roleIDs) {
				mmDeactivateByIDs.t.Errorf("RolePrimeDBMock.DeactivateByIDs got unexpected parameter roleIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.expectationOrigins.originRoleIDs, *mm_want_ptrs.roleIDs, mm_got.roleIDs, minimock.Diff(*mm_want_ptrs.roleIDs, mm_got.roleIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeactivateByIDs.t.Errorf("RolePrimeDBMock.DeactivateByIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeactivateByIDs.DeactivateByIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmDeactivateByIDs.t.Fatal("No results are set for the RolePrimeDBMock.DeactivateByIDs")
		}
		return (*mm_results).err
	}
	if mmDeactivateByIDs.funcDeactivateByIDs != nil {
		return mmDeactivateByIDs.funcDeactivateByIDs(roleIDs)
	}
	mmDeactivateByIDs.t.Fatalf("Unexpected call to RolePrimeDBMock.DeactivateByIDs. %v", roleIDs)
	return
}

// DeactivateByIDsAfterCounter returns a count of finished RolePrimeDBMock.DeactivateByIDs invocations
func (mmDeactivateByIDs *RolePrimeDBMock) DeactivateByIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeactivateByIDs.afterDeactivateByIDsCounter)
}

// DeactivateByIDsBeforeCounter returns a count of RolePrimeDBMock.DeactivateByIDs invocations
func (mmDeactivateByIDs *RolePrimeDBMock) DeactivateByIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeactivateByIDs.beforeDeactivateByIDsCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.DeactivateByIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeactivateByIDs *mRolePrimeDBMockDeactivateByIDs) Calls() []*RolePrimeDBMockDeactivateByIDsParams {
	mmDeactivateByIDs.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockDeactivateByIDsParams, len(mmDeactivateByIDs.callArgs))
	copy(argCopy, mmDeactivateByIDs.callArgs)

	mmDeactivateByIDs.mutex.RUnlock()

	return argCopy
}

// MinimockDeactivateByIDsDone returns true if the count of the DeactivateByIDs invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockDeactivateByIDsDone() bool {
	if m.DeactivateByIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeactivateByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeactivateByIDsMock.invocationsDone()
}

// MinimockDeactivateByIDsInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockDeactivateByIDsInspect() {
	for _, e := range m.DeactivateByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.DeactivateByIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeactivateByIDsCounter := mm_atomic.LoadUint64(&m.afterDeactivateByIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeactivateByIDsMock.defaultExpectation != nil && afterDeactivateByIDsCounter < 1 {
		if m.DeactivateByIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.DeactivateByIDs at\n%s", m.DeactivateByIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.DeactivateByIDs at\n%s with params: %#v", m.DeactivateByIDsMock.defaultExpectation.expectationOrigins.origin, *m.DeactivateByIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeactivateByIDs != nil && afterDeactivateByIDsCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.DeactivateByIDs at\n%s", m.funcDeactivateByIDsOrigin)
	}

	if !m.DeactivateByIDsMock.invocationsDone() && afterDeactivateByIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.DeactivateByIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeactivateByIDsMock.expectedInvocations), m.DeactivateByIDsMock.expectedInvocationsOrigin, afterDeactivateByIDsCounter)
	}
}

type mRolePrimeDBMockExistByName struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockExistByNameExpectation
	expectations       []*RolePrimeDBMockExistByNameExpectation

	callArgs []*RolePrimeDBMockExistByNameParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockExistByNameExpectation specifies expectation struct of the RolePrimeDB.ExistByName
type RolePrimeDBMockExistByNameExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockExistByNameParams
	paramPtrs          *RolePrimeDBMockExistByNameParamPtrs
	expectationOrigins RolePrimeDBMockExistByNameExpectationOrigins
	results            *RolePrimeDBMockExistByNameResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockExistByNameParams contains parameters of the RolePrimeDB.ExistByName
type RolePrimeDBMockExistByNameParams struct {
	name string
}

// RolePrimeDBMockExistByNameParamPtrs contains pointers to parameters of the RolePrimeDB.ExistByName
type RolePrimeDBMockExistByNameParamPtrs struct {
	name *string
}

// RolePrimeDBMockExistByNameResults contains results of the RolePrimeDB.ExistByName
type RolePrimeDBMockExistByNameResults struct {
	b1  bool
	err error
}

// RolePrimeDBMockExistByNameOrigins contains origins of expectations of the RolePrimeDB.ExistByName
type RolePrimeDBMockExistByNameExpectationOrigins struct {
	origin     string
	originName string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistByName *mRolePrimeDBMockExistByName) Optional() *mRolePrimeDBMockExistByName {
	mmExistByName.optional = true
	return mmExistByName
}

// Expect sets up expected params for RolePrimeDB.ExistByName
func (mmExistByName *mRolePrimeDBMockExistByName) Expect(name string) *mRolePrimeDBMockExistByName {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("RolePrimeDBMock.ExistByName mock is already set by Set")
	}

	if mmExistByName.defaultExpectation == nil {
		mmExistByName.defaultExpectation = &RolePrimeDBMockExistByNameExpectation{}
	}

	if mmExistByName.defaultExpectation.paramPtrs != nil {
		mmExistByName.mock.t.Fatalf("RolePrimeDBMock.ExistByName mock is already set by ExpectParams functions")
	}

	mmExistByName.defaultExpectation.params = &RolePrimeDBMockExistByNameParams{name}
	mmExistByName.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistByName.expectations {
		if minimock.Equal(e.params, mmExistByName.defaultExpectation.params) {
			mmExistByName.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistByName.defaultExpectation.params)
		}
	}

	return mmExistByName
}

// ExpectNameParam1 sets up expected param name for RolePrimeDB.ExistByName
func (mmExistByName *mRolePrimeDBMockExistByName) ExpectNameParam1(name string) *mRolePrimeDBMockExistByName {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("RolePrimeDBMock.ExistByName mock is already set by Set")
	}

	if mmExistByName.defaultExpectation == nil {
		mmExistByName.defaultExpectation = &RolePrimeDBMockExistByNameExpectation{}
	}

	if mmExistByName.defaultExpectation.params != nil {
		mmExistByName.mock.t.Fatalf("RolePrimeDBMock.ExistByName mock is already set by Expect")
	}

	if mmExistByName.defaultExpectation.paramPtrs == nil {
		mmExistByName.defaultExpectation.paramPtrs = &RolePrimeDBMockExistByNameParamPtrs{}
	}
	mmExistByName.defaultExpectation.paramPtrs.name = &name
	mmExistByName.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmExistByName
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.ExistByName
func (mmExistByName *mRolePrimeDBMockExistByName) Inspect(f func(name string)) *mRolePrimeDBMockExistByName {
	if mmExistByName.mock.inspectFuncExistByName != nil {
		mmExistByName.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.ExistByName")
	}

	mmExistByName.mock.inspectFuncExistByName = f

	return mmExistByName
}

// Return sets up results that will be returned by RolePrimeDB.ExistByName
func (mmExistByName *mRolePrimeDBMockExistByName) Return(b1 bool, err error) *RolePrimeDBMock {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("RolePrimeDBMock.ExistByName mock is already set by Set")
	}

	if mmExistByName.defaultExpectation == nil {
		mmExistByName.defaultExpectation = &RolePrimeDBMockExistByNameExpectation{mock: mmExistByName.mock}
	}
	mmExistByName.defaultExpectation.results = &RolePrimeDBMockExistByNameResults{b1, err}
	mmExistByName.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistByName.mock
}

// Set uses given function f to mock the RolePrimeDB.ExistByName method
func (mmExistByName *mRolePrimeDBMockExistByName) Set(f func(name string) (b1 bool, err error)) *RolePrimeDBMock {
	if mmExistByName.defaultExpectation != nil {
		mmExistByName.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.ExistByName method")
	}

	if len(mmExistByName.expectations) > 0 {
		mmExistByName.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.ExistByName method")
	}

	mmExistByName.mock.funcExistByName = f
	mmExistByName.mock.funcExistByNameOrigin = minimock.CallerInfo(1)
	return mmExistByName.mock
}

// When sets expectation for the RolePrimeDB.ExistByName which will trigger the result defined by the following
// Then helper
func (mmExistByName *mRolePrimeDBMockExistByName) When(name string) *RolePrimeDBMockExistByNameExpectation {
	if mmExistByName.mock.funcExistByName != nil {
		mmExistByName.mock.t.Fatalf("RolePrimeDBMock.ExistByName mock is already set by Set")
	}

	expectation := &RolePrimeDBMockExistByNameExpectation{
		mock:               mmExistByName.mock,
		params:             &RolePrimeDBMockExistByNameParams{name},
		expectationOrigins: RolePrimeDBMockExistByNameExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistByName.expectations = append(mmExistByName.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.ExistByName return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockExistByNameExpectation) Then(b1 bool, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockExistByNameResults{b1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.ExistByName should be invoked
func (mmExistByName *mRolePrimeDBMockExistByName) Times(n uint64) *mRolePrimeDBMockExistByName {
	if n == 0 {
		mmExistByName.mock.t.Fatalf("Times of RolePrimeDBMock.ExistByName mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistByName.expectedInvocations, n)
	mmExistByName.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistByName
}

func (mmExistByName *mRolePrimeDBMockExistByName) invocationsDone() bool {
	if len(mmExistByName.expectations) == 0 && mmExistByName.defaultExpectation == nil && mmExistByName.mock.funcExistByName == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistByName.mock.afterExistByNameCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistByName.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistByName implements mm_repository.RolePrimeDB
func (mmExistByName *RolePrimeDBMock) ExistByName(name string) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistByName.beforeExistByNameCounter, 1)
	defer mm_atomic.AddUint64(&mmExistByName.afterExistByNameCounter, 1)

	mmExistByName.t.Helper()

	if mmExistByName.inspectFuncExistByName != nil {
		mmExistByName.inspectFuncExistByName(name)
	}

	mm_params := RolePrimeDBMockExistByNameParams{name}

	// Record call args
	mmExistByName.ExistByNameMock.mutex.Lock()
	mmExistByName.ExistByNameMock.callArgs = append(mmExistByName.ExistByNameMock.callArgs, &mm_params)
	mmExistByName.ExistByNameMock.mutex.Unlock()

	for _, e := range mmExistByName.ExistByNameMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistByName.ExistByNameMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistByName.ExistByNameMock.defaultExpectation.Counter, 1)
		mm_want := mmExistByName.ExistByNameMock.defaultExpectation.params
		mm_want_ptrs := mmExistByName.ExistByNameMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockExistByNameParams{name}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmExistByName.t.Errorf("RolePrimeDBMock.ExistByName got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistByName.ExistByNameMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistByName.t.Errorf("RolePrimeDBMock.ExistByName got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistByName.ExistByNameMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistByName.ExistByNameMock.defaultExpectation.results
		if mm_results == nil {
			mmExistByName.t.Fatal("No results are set for the RolePrimeDBMock.ExistByName")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistByName.funcExistByName != nil {
		return mmExistByName.funcExistByName(name)
	}
	mmExistByName.t.Fatalf("Unexpected call to RolePrimeDBMock.ExistByName. %v", name)
	return
}

// ExistByNameAfterCounter returns a count of finished RolePrimeDBMock.ExistByName invocations
func (mmExistByName *RolePrimeDBMock) ExistByNameAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByName.afterExistByNameCounter)
}

// ExistByNameBeforeCounter returns a count of RolePrimeDBMock.ExistByName invocations
func (mmExistByName *RolePrimeDBMock) ExistByNameBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistByName.beforeExistByNameCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.ExistByName.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistByName *mRolePrimeDBMockExistByName) Calls() []*RolePrimeDBMockExistByNameParams {
	mmExistByName.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockExistByNameParams, len(mmExistByName.callArgs))
	copy(argCopy, mmExistByName.callArgs)

	mmExistByName.mutex.RUnlock()

	return argCopy
}

// MinimockExistByNameDone returns true if the count of the ExistByName invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockExistByNameDone() bool {
	if m.ExistByNameMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistByNameMock.invocationsDone()
}

// MinimockExistByNameInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockExistByNameInspect() {
	for _, e := range m.ExistByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.ExistByName at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistByNameCounter := mm_atomic.LoadUint64(&m.afterExistByNameCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistByNameMock.defaultExpectation != nil && afterExistByNameCounter < 1 {
		if m.ExistByNameMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.ExistByName at\n%s", m.ExistByNameMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.ExistByName at\n%s with params: %#v", m.ExistByNameMock.defaultExpectation.expectationOrigins.origin, *m.ExistByNameMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistByName != nil && afterExistByNameCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.ExistByName at\n%s", m.funcExistByNameOrigin)
	}

	if !m.ExistByNameMock.invocationsDone() && afterExistByNameCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.ExistByName at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistByNameMock.expectedInvocations), m.ExistByNameMock.expectedInvocationsOrigin, afterExistByNameCounter)
	}
}

type mRolePrimeDBMockGetAll struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetAllExpectation
	expectations       []*RolePrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetAllExpectation specifies expectation struct of the RolePrimeDB.GetAll
type RolePrimeDBMockGetAllExpectation struct {
	mock *RolePrimeDBMock

	results      *RolePrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// RolePrimeDBMockGetAllResults contains results of the RolePrimeDB.GetAll
type RolePrimeDBMockGetAllResults struct {
	ra1 []roleentity.Role
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mRolePrimeDBMockGetAll) Optional() *mRolePrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for RolePrimeDB.GetAll
func (mmGetAll *mRolePrimeDBMockGetAll) Expect() *mRolePrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("RolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &RolePrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetAll
func (mmGetAll *mRolePrimeDBMockGetAll) Inspect(f func()) *mRolePrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by RolePrimeDB.GetAll
func (mmGetAll *mRolePrimeDBMockGetAll) Return(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("RolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &RolePrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &RolePrimeDBMockGetAllResults{ra1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the RolePrimeDB.GetAll method
func (mmGetAll *mRolePrimeDBMockGetAll) Set(f func() (ra1 []roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times RolePrimeDB.GetAll should be invoked
func (mmGetAll *mRolePrimeDBMockGetAll) Times(n uint64) *mRolePrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of RolePrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mRolePrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.RolePrimeDB
func (mmGetAll *RolePrimeDBMock) GetAll() (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the RolePrimeDBMock.GetAll")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to RolePrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished RolePrimeDBMock.GetAll invocations
func (mmGetAll *RolePrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of RolePrimeDBMock.GetAll invocations
func (mmGetAll *RolePrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to RolePrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mRolePrimeDBMockGetAllByUserID struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetAllByUserIDExpectation
	expectations       []*RolePrimeDBMockGetAllByUserIDExpectation

	callArgs []*RolePrimeDBMockGetAllByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetAllByUserIDExpectation specifies expectation struct of the RolePrimeDB.GetAllByUserID
type RolePrimeDBMockGetAllByUserIDExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetAllByUserIDParams
	paramPtrs          *RolePrimeDBMockGetAllByUserIDParamPtrs
	expectationOrigins RolePrimeDBMockGetAllByUserIDExpectationOrigins
	results            *RolePrimeDBMockGetAllByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetAllByUserIDParams contains parameters of the RolePrimeDB.GetAllByUserID
type RolePrimeDBMockGetAllByUserIDParams struct {
	userID int64
}

// RolePrimeDBMockGetAllByUserIDParamPtrs contains pointers to parameters of the RolePrimeDB.GetAllByUserID
type RolePrimeDBMockGetAllByUserIDParamPtrs struct {
	userID *int64
}

// RolePrimeDBMockGetAllByUserIDResults contains results of the RolePrimeDB.GetAllByUserID
type RolePrimeDBMockGetAllByUserIDResults struct {
	ra1 []roleentity.Role
	err error
}

// RolePrimeDBMockGetAllByUserIDOrigins contains origins of expectations of the RolePrimeDB.GetAllByUserID
type RolePrimeDBMockGetAllByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Optional() *mRolePrimeDBMockGetAllByUserID {
	mmGetAllByUserID.optional = true
	return mmGetAllByUserID
}

// Expect sets up expected params for RolePrimeDB.GetAllByUserID
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Expect(userID int64) *mRolePrimeDBMockGetAllByUserID {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	if mmGetAllByUserID.defaultExpectation == nil {
		mmGetAllByUserID.defaultExpectation = &RolePrimeDBMockGetAllByUserIDExpectation{}
	}

	if mmGetAllByUserID.defaultExpectation.paramPtrs != nil {
		mmGetAllByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllByUserID mock is already set by ExpectParams functions")
	}

	mmGetAllByUserID.defaultExpectation.params = &RolePrimeDBMockGetAllByUserIDParams{userID}
	mmGetAllByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAllByUserID.expectations {
		if minimock.Equal(e.params, mmGetAllByUserID.defaultExpectation.params) {
			mmGetAllByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAllByUserID.defaultExpectation.params)
		}
	}

	return mmGetAllByUserID
}

// ExpectUserIDParam1 sets up expected param userID for RolePrimeDB.GetAllByUserID
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) ExpectUserIDParam1(userID int64) *mRolePrimeDBMockGetAllByUserID {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	if mmGetAllByUserID.defaultExpectation == nil {
		mmGetAllByUserID.defaultExpectation = &RolePrimeDBMockGetAllByUserIDExpectation{}
	}

	if mmGetAllByUserID.defaultExpectation.params != nil {
		mmGetAllByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllByUserID mock is already set by Expect")
	}

	if mmGetAllByUserID.defaultExpectation.paramPtrs == nil {
		mmGetAllByUserID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetAllByUserIDParamPtrs{}
	}
	mmGetAllByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetAllByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetAllByUserID
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetAllByUserID
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Inspect(f func(userID int64)) *mRolePrimeDBMockGetAllByUserID {
	if mmGetAllByUserID.mock.inspectFuncGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetAllByUserID")
	}

	mmGetAllByUserID.mock.inspectFuncGetAllByUserID = f

	return mmGetAllByUserID
}

// Return sets up results that will be returned by RolePrimeDB.GetAllByUserID
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Return(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	if mmGetAllByUserID.defaultExpectation == nil {
		mmGetAllByUserID.defaultExpectation = &RolePrimeDBMockGetAllByUserIDExpectation{mock: mmGetAllByUserID.mock}
	}
	mmGetAllByUserID.defaultExpectation.results = &RolePrimeDBMockGetAllByUserIDResults{ra1, err}
	mmGetAllByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllByUserID.mock
}

// Set uses given function f to mock the RolePrimeDB.GetAllByUserID method
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Set(f func(userID int64) (ra1 []roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetAllByUserID.defaultExpectation != nil {
		mmGetAllByUserID.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetAllByUserID method")
	}

	if len(mmGetAllByUserID.expectations) > 0 {
		mmGetAllByUserID.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetAllByUserID method")
	}

	mmGetAllByUserID.mock.funcGetAllByUserID = f
	mmGetAllByUserID.mock.funcGetAllByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetAllByUserID.mock
}

// When sets expectation for the RolePrimeDB.GetAllByUserID which will trigger the result defined by the following
// Then helper
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) When(userID int64) *RolePrimeDBMockGetAllByUserIDExpectation {
	if mmGetAllByUserID.mock.funcGetAllByUserID != nil {
		mmGetAllByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllByUserID mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetAllByUserIDExpectation{
		mock:               mmGetAllByUserID.mock,
		params:             &RolePrimeDBMockGetAllByUserIDParams{userID},
		expectationOrigins: RolePrimeDBMockGetAllByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAllByUserID.expectations = append(mmGetAllByUserID.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetAllByUserID return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetAllByUserIDExpectation) Then(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetAllByUserIDResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetAllByUserID should be invoked
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Times(n uint64) *mRolePrimeDBMockGetAllByUserID {
	if n == 0 {
		mmGetAllByUserID.mock.t.Fatalf("Times of RolePrimeDBMock.GetAllByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllByUserID.expectedInvocations, n)
	mmGetAllByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllByUserID
}

func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) invocationsDone() bool {
	if len(mmGetAllByUserID.expectations) == 0 && mmGetAllByUserID.defaultExpectation == nil && mmGetAllByUserID.mock.funcGetAllByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllByUserID.mock.afterGetAllByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllByUserID implements mm_repository.RolePrimeDB
func (mmGetAllByUserID *RolePrimeDBMock) GetAllByUserID(userID int64) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetAllByUserID.beforeGetAllByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllByUserID.afterGetAllByUserIDCounter, 1)

	mmGetAllByUserID.t.Helper()

	if mmGetAllByUserID.inspectFuncGetAllByUserID != nil {
		mmGetAllByUserID.inspectFuncGetAllByUserID(userID)
	}

	mm_params := RolePrimeDBMockGetAllByUserIDParams{userID}

	// Record call args
	mmGetAllByUserID.GetAllByUserIDMock.mutex.Lock()
	mmGetAllByUserID.GetAllByUserIDMock.callArgs = append(mmGetAllByUserID.GetAllByUserIDMock.callArgs, &mm_params)
	mmGetAllByUserID.GetAllByUserIDMock.mutex.Unlock()

	for _, e := range mmGetAllByUserID.GetAllByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetAllByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetAllByUserID.t.Errorf("RolePrimeDBMock.GetAllByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAllByUserID.t.Errorf("RolePrimeDBMock.GetAllByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAllByUserID.GetAllByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllByUserID.t.Fatal("No results are set for the RolePrimeDBMock.GetAllByUserID")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetAllByUserID.funcGetAllByUserID != nil {
		return mmGetAllByUserID.funcGetAllByUserID(userID)
	}
	mmGetAllByUserID.t.Fatalf("Unexpected call to RolePrimeDBMock.GetAllByUserID. %v", userID)
	return
}

// GetAllByUserIDAfterCounter returns a count of finished RolePrimeDBMock.GetAllByUserID invocations
func (mmGetAllByUserID *RolePrimeDBMock) GetAllByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllByUserID.afterGetAllByUserIDCounter)
}

// GetAllByUserIDBeforeCounter returns a count of RolePrimeDBMock.GetAllByUserID invocations
func (mmGetAllByUserID *RolePrimeDBMock) GetAllByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllByUserID.beforeGetAllByUserIDCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetAllByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAllByUserID *mRolePrimeDBMockGetAllByUserID) Calls() []*RolePrimeDBMockGetAllByUserIDParams {
	mmGetAllByUserID.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetAllByUserIDParams, len(mmGetAllByUserID.callArgs))
	copy(argCopy, mmGetAllByUserID.callArgs)

	mmGetAllByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetAllByUserIDDone returns true if the count of the GetAllByUserID invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetAllByUserIDDone() bool {
	if m.GetAllByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllByUserIDMock.invocationsDone()
}

// MinimockGetAllByUserIDInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetAllByUserIDInspect() {
	for _, e := range m.GetAllByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetAllByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAllByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetAllByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllByUserIDMock.defaultExpectation != nil && afterGetAllByUserIDCounter < 1 {
		if m.GetAllByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetAllByUserID at\n%s", m.GetAllByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetAllByUserID at\n%s with params: %#v", m.GetAllByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetAllByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllByUserID != nil && afterGetAllByUserIDCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetAllByUserID at\n%s", m.funcGetAllByUserIDOrigin)
	}

	if !m.GetAllByUserIDMock.invocationsDone() && afterGetAllByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetAllByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllByUserIDMock.expectedInvocations), m.GetAllByUserIDMock.expectedInvocationsOrigin, afterGetAllByUserIDCounter)
	}
}

type mRolePrimeDBMockGetAllWithProductIDByUserID struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetAllWithProductIDByUserIDExpectation
	expectations       []*RolePrimeDBMockGetAllWithProductIDByUserIDExpectation

	callArgs []*RolePrimeDBMockGetAllWithProductIDByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetAllWithProductIDByUserIDExpectation specifies expectation struct of the RolePrimeDB.GetAllWithProductIDByUserID
type RolePrimeDBMockGetAllWithProductIDByUserIDExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetAllWithProductIDByUserIDParams
	paramPtrs          *RolePrimeDBMockGetAllWithProductIDByUserIDParamPtrs
	expectationOrigins RolePrimeDBMockGetAllWithProductIDByUserIDExpectationOrigins
	results            *RolePrimeDBMockGetAllWithProductIDByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetAllWithProductIDByUserIDParams contains parameters of the RolePrimeDB.GetAllWithProductIDByUserID
type RolePrimeDBMockGetAllWithProductIDByUserIDParams struct {
	userID int64
}

// RolePrimeDBMockGetAllWithProductIDByUserIDParamPtrs contains pointers to parameters of the RolePrimeDB.GetAllWithProductIDByUserID
type RolePrimeDBMockGetAllWithProductIDByUserIDParamPtrs struct {
	userID *int64
}

// RolePrimeDBMockGetAllWithProductIDByUserIDResults contains results of the RolePrimeDB.GetAllWithProductIDByUserID
type RolePrimeDBMockGetAllWithProductIDByUserIDResults struct {
	ra1 []roleentity.RoleWithProductID
	err error
}

// RolePrimeDBMockGetAllWithProductIDByUserIDOrigins contains origins of expectations of the RolePrimeDB.GetAllWithProductIDByUserID
type RolePrimeDBMockGetAllWithProductIDByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Optional() *mRolePrimeDBMockGetAllWithProductIDByUserID {
	mmGetAllWithProductIDByUserID.optional = true
	return mmGetAllWithProductIDByUserID
}

// Expect sets up expected params for RolePrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Expect(userID int64) *mRolePrimeDBMockGetAllWithProductIDByUserID {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation = &RolePrimeDBMockGetAllWithProductIDByUserIDExpectation{}
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllWithProductIDByUserID mock is already set by ExpectParams functions")
	}

	mmGetAllWithProductIDByUserID.defaultExpectation.params = &RolePrimeDBMockGetAllWithProductIDByUserIDParams{userID}
	mmGetAllWithProductIDByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetAllWithProductIDByUserID.expectations {
		if minimock.Equal(e.params, mmGetAllWithProductIDByUserID.defaultExpectation.params) {
			mmGetAllWithProductIDByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetAllWithProductIDByUserID.defaultExpectation.params)
		}
	}

	return mmGetAllWithProductIDByUserID
}

// ExpectUserIDParam1 sets up expected param userID for RolePrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) ExpectUserIDParam1(userID int64) *mRolePrimeDBMockGetAllWithProductIDByUserID {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation = &RolePrimeDBMockGetAllWithProductIDByUserIDExpectation{}
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation.params != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllWithProductIDByUserID mock is already set by Expect")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetAllWithProductIDByUserIDParamPtrs{}
	}
	mmGetAllWithProductIDByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetAllWithProductIDByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetAllWithProductIDByUserID
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Inspect(f func(userID int64)) *mRolePrimeDBMockGetAllWithProductIDByUserID {
	if mmGetAllWithProductIDByUserID.mock.inspectFuncGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetAllWithProductIDByUserID")
	}

	mmGetAllWithProductIDByUserID.mock.inspectFuncGetAllWithProductIDByUserID = f

	return mmGetAllWithProductIDByUserID
}

// Return sets up results that will be returned by RolePrimeDB.GetAllWithProductIDByUserID
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Return(ra1 []roleentity.RoleWithProductID, err error) *RolePrimeDBMock {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	if mmGetAllWithProductIDByUserID.defaultExpectation == nil {
		mmGetAllWithProductIDByUserID.defaultExpectation = &RolePrimeDBMockGetAllWithProductIDByUserIDExpectation{mock: mmGetAllWithProductIDByUserID.mock}
	}
	mmGetAllWithProductIDByUserID.defaultExpectation.results = &RolePrimeDBMockGetAllWithProductIDByUserIDResults{ra1, err}
	mmGetAllWithProductIDByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllWithProductIDByUserID.mock
}

// Set uses given function f to mock the RolePrimeDB.GetAllWithProductIDByUserID method
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Set(f func(userID int64) (ra1 []roleentity.RoleWithProductID, err error)) *RolePrimeDBMock {
	if mmGetAllWithProductIDByUserID.defaultExpectation != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetAllWithProductIDByUserID method")
	}

	if len(mmGetAllWithProductIDByUserID.expectations) > 0 {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetAllWithProductIDByUserID method")
	}

	mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID = f
	mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetAllWithProductIDByUserID.mock
}

// When sets expectation for the RolePrimeDB.GetAllWithProductIDByUserID which will trigger the result defined by the following
// Then helper
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) When(userID int64) *RolePrimeDBMockGetAllWithProductIDByUserIDExpectation {
	if mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("RolePrimeDBMock.GetAllWithProductIDByUserID mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetAllWithProductIDByUserIDExpectation{
		mock:               mmGetAllWithProductIDByUserID.mock,
		params:             &RolePrimeDBMockGetAllWithProductIDByUserIDParams{userID},
		expectationOrigins: RolePrimeDBMockGetAllWithProductIDByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetAllWithProductIDByUserID.expectations = append(mmGetAllWithProductIDByUserID.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetAllWithProductIDByUserID return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetAllWithProductIDByUserIDExpectation) Then(ra1 []roleentity.RoleWithProductID, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetAllWithProductIDByUserIDResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetAllWithProductIDByUserID should be invoked
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Times(n uint64) *mRolePrimeDBMockGetAllWithProductIDByUserID {
	if n == 0 {
		mmGetAllWithProductIDByUserID.mock.t.Fatalf("Times of RolePrimeDBMock.GetAllWithProductIDByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllWithProductIDByUserID.expectedInvocations, n)
	mmGetAllWithProductIDByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllWithProductIDByUserID
}

func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) invocationsDone() bool {
	if len(mmGetAllWithProductIDByUserID.expectations) == 0 && mmGetAllWithProductIDByUserID.defaultExpectation == nil && mmGetAllWithProductIDByUserID.mock.funcGetAllWithProductIDByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.mock.afterGetAllWithProductIDByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllWithProductIDByUserID implements mm_repository.RolePrimeDB
func (mmGetAllWithProductIDByUserID *RolePrimeDBMock) GetAllWithProductIDByUserID(userID int64) (ra1 []roleentity.RoleWithProductID, err error) {
	mm_atomic.AddUint64(&mmGetAllWithProductIDByUserID.beforeGetAllWithProductIDByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllWithProductIDByUserID.afterGetAllWithProductIDByUserIDCounter, 1)

	mmGetAllWithProductIDByUserID.t.Helper()

	if mmGetAllWithProductIDByUserID.inspectFuncGetAllWithProductIDByUserID != nil {
		mmGetAllWithProductIDByUserID.inspectFuncGetAllWithProductIDByUserID(userID)
	}

	mm_params := RolePrimeDBMockGetAllWithProductIDByUserIDParams{userID}

	// Record call args
	mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.mutex.Lock()
	mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.callArgs = append(mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.callArgs, &mm_params)
	mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.mutex.Unlock()

	for _, e := range mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetAllWithProductIDByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetAllWithProductIDByUserID.t.Errorf("RolePrimeDBMock.GetAllWithProductIDByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetAllWithProductIDByUserID.t.Errorf("RolePrimeDBMock.GetAllWithProductIDByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetAllWithProductIDByUserID.GetAllWithProductIDByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllWithProductIDByUserID.t.Fatal("No results are set for the RolePrimeDBMock.GetAllWithProductIDByUserID")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetAllWithProductIDByUserID.funcGetAllWithProductIDByUserID != nil {
		return mmGetAllWithProductIDByUserID.funcGetAllWithProductIDByUserID(userID)
	}
	mmGetAllWithProductIDByUserID.t.Fatalf("Unexpected call to RolePrimeDBMock.GetAllWithProductIDByUserID. %v", userID)
	return
}

// GetAllWithProductIDByUserIDAfterCounter returns a count of finished RolePrimeDBMock.GetAllWithProductIDByUserID invocations
func (mmGetAllWithProductIDByUserID *RolePrimeDBMock) GetAllWithProductIDByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.afterGetAllWithProductIDByUserIDCounter)
}

// GetAllWithProductIDByUserIDBeforeCounter returns a count of RolePrimeDBMock.GetAllWithProductIDByUserID invocations
func (mmGetAllWithProductIDByUserID *RolePrimeDBMock) GetAllWithProductIDByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllWithProductIDByUserID.beforeGetAllWithProductIDByUserIDCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetAllWithProductIDByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetAllWithProductIDByUserID *mRolePrimeDBMockGetAllWithProductIDByUserID) Calls() []*RolePrimeDBMockGetAllWithProductIDByUserIDParams {
	mmGetAllWithProductIDByUserID.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetAllWithProductIDByUserIDParams, len(mmGetAllWithProductIDByUserID.callArgs))
	copy(argCopy, mmGetAllWithProductIDByUserID.callArgs)

	mmGetAllWithProductIDByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetAllWithProductIDByUserIDDone returns true if the count of the GetAllWithProductIDByUserID invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetAllWithProductIDByUserIDDone() bool {
	if m.GetAllWithProductIDByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllWithProductIDByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllWithProductIDByUserIDMock.invocationsDone()
}

// MinimockGetAllWithProductIDByUserIDInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetAllWithProductIDByUserIDInspect() {
	for _, e := range m.GetAllWithProductIDByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetAllWithProductIDByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetAllWithProductIDByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetAllWithProductIDByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllWithProductIDByUserIDMock.defaultExpectation != nil && afterGetAllWithProductIDByUserIDCounter < 1 {
		if m.GetAllWithProductIDByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetAllWithProductIDByUserID at\n%s", m.GetAllWithProductIDByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetAllWithProductIDByUserID at\n%s with params: %#v", m.GetAllWithProductIDByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetAllWithProductIDByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllWithProductIDByUserID != nil && afterGetAllWithProductIDByUserIDCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetAllWithProductIDByUserID at\n%s", m.funcGetAllWithProductIDByUserIDOrigin)
	}

	if !m.GetAllWithProductIDByUserIDMock.invocationsDone() && afterGetAllWithProductIDByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetAllWithProductIDByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllWithProductIDByUserIDMock.expectedInvocations), m.GetAllWithProductIDByUserIDMock.expectedInvocationsOrigin, afterGetAllWithProductIDByUserIDCounter)
	}
}

type mRolePrimeDBMockGetByID struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetByIDExpectation
	expectations       []*RolePrimeDBMockGetByIDExpectation

	callArgs []*RolePrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetByIDExpectation specifies expectation struct of the RolePrimeDB.GetByID
type RolePrimeDBMockGetByIDExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetByIDParams
	paramPtrs          *RolePrimeDBMockGetByIDParamPtrs
	expectationOrigins RolePrimeDBMockGetByIDExpectationOrigins
	results            *RolePrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetByIDParams contains parameters of the RolePrimeDB.GetByID
type RolePrimeDBMockGetByIDParams struct {
	id int64
}

// RolePrimeDBMockGetByIDParamPtrs contains pointers to parameters of the RolePrimeDB.GetByID
type RolePrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// RolePrimeDBMockGetByIDResults contains results of the RolePrimeDB.GetByID
type RolePrimeDBMockGetByIDResults struct {
	r1  roleentity.Role
	err error
}

// RolePrimeDBMockGetByIDOrigins contains origins of expectations of the RolePrimeDB.GetByID
type RolePrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mRolePrimeDBMockGetByID) Optional() *mRolePrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for RolePrimeDB.GetByID
func (mmGetByID *mRolePrimeDBMockGetByID) Expect(id int64) *mRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &RolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("RolePrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &RolePrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for RolePrimeDB.GetByID
func (mmGetByID *mRolePrimeDBMockGetByID) ExpectIdParam1(id int64) *mRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &RolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("RolePrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetByID
func (mmGetByID *mRolePrimeDBMockGetByID) Inspect(f func(id int64)) *mRolePrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by RolePrimeDB.GetByID
func (mmGetByID *mRolePrimeDBMockGetByID) Return(r1 roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &RolePrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &RolePrimeDBMockGetByIDResults{r1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the RolePrimeDB.GetByID method
func (mmGetByID *mRolePrimeDBMockGetByID) Set(f func(id int64) (r1 roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the RolePrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mRolePrimeDBMockGetByID) When(id int64) *RolePrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("RolePrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &RolePrimeDBMockGetByIDParams{id},
		expectationOrigins: RolePrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetByIDExpectation) Then(r1 roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetByIDResults{r1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetByID should be invoked
func (mmGetByID *mRolePrimeDBMockGetByID) Times(n uint64) *mRolePrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of RolePrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mRolePrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.RolePrimeDB
func (mmGetByID *RolePrimeDBMock) GetByID(id int64) (r1 roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := RolePrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("RolePrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("RolePrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the RolePrimeDBMock.GetByID")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to RolePrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished RolePrimeDBMock.GetByID invocations
func (mmGetByID *RolePrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of RolePrimeDBMock.GetByID invocations
func (mmGetByID *RolePrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mRolePrimeDBMockGetByID) Calls() []*RolePrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mRolePrimeDBMockGetByProductID struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetByProductIDExpectation
	expectations       []*RolePrimeDBMockGetByProductIDExpectation

	callArgs []*RolePrimeDBMockGetByProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetByProductIDExpectation specifies expectation struct of the RolePrimeDB.GetByProductID
type RolePrimeDBMockGetByProductIDExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetByProductIDParams
	paramPtrs          *RolePrimeDBMockGetByProductIDParamPtrs
	expectationOrigins RolePrimeDBMockGetByProductIDExpectationOrigins
	results            *RolePrimeDBMockGetByProductIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetByProductIDParams contains parameters of the RolePrimeDB.GetByProductID
type RolePrimeDBMockGetByProductIDParams struct {
	productID int64
}

// RolePrimeDBMockGetByProductIDParamPtrs contains pointers to parameters of the RolePrimeDB.GetByProductID
type RolePrimeDBMockGetByProductIDParamPtrs struct {
	productID *int64
}

// RolePrimeDBMockGetByProductIDResults contains results of the RolePrimeDB.GetByProductID
type RolePrimeDBMockGetByProductIDResults struct {
	ra1 []roleentity.Role
	err error
}

// RolePrimeDBMockGetByProductIDOrigins contains origins of expectations of the RolePrimeDB.GetByProductID
type RolePrimeDBMockGetByProductIDExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Optional() *mRolePrimeDBMockGetByProductID {
	mmGetByProductID.optional = true
	return mmGetByProductID
}

// Expect sets up expected params for RolePrimeDB.GetByProductID
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Expect(productID int64) *mRolePrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("RolePrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &RolePrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.paramPtrs != nil {
		mmGetByProductID.mock.t.Fatalf("RolePrimeDBMock.GetByProductID mock is already set by ExpectParams functions")
	}

	mmGetByProductID.defaultExpectation.params = &RolePrimeDBMockGetByProductIDParams{productID}
	mmGetByProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductID.expectations {
		if minimock.Equal(e.params, mmGetByProductID.defaultExpectation.params) {
			mmGetByProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductID.defaultExpectation.params)
		}
	}

	return mmGetByProductID
}

// ExpectProductIDParam1 sets up expected param productID for RolePrimeDB.GetByProductID
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) ExpectProductIDParam1(productID int64) *mRolePrimeDBMockGetByProductID {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("RolePrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &RolePrimeDBMockGetByProductIDExpectation{}
	}

	if mmGetByProductID.defaultExpectation.params != nil {
		mmGetByProductID.mock.t.Fatalf("RolePrimeDBMock.GetByProductID mock is already set by Expect")
	}

	if mmGetByProductID.defaultExpectation.paramPtrs == nil {
		mmGetByProductID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetByProductIDParamPtrs{}
	}
	mmGetByProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductID
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetByProductID
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Inspect(f func(productID int64)) *mRolePrimeDBMockGetByProductID {
	if mmGetByProductID.mock.inspectFuncGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetByProductID")
	}

	mmGetByProductID.mock.inspectFuncGetByProductID = f

	return mmGetByProductID
}

// Return sets up results that will be returned by RolePrimeDB.GetByProductID
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Return(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("RolePrimeDBMock.GetByProductID mock is already set by Set")
	}

	if mmGetByProductID.defaultExpectation == nil {
		mmGetByProductID.defaultExpectation = &RolePrimeDBMockGetByProductIDExpectation{mock: mmGetByProductID.mock}
	}
	mmGetByProductID.defaultExpectation.results = &RolePrimeDBMockGetByProductIDResults{ra1, err}
	mmGetByProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// Set uses given function f to mock the RolePrimeDB.GetByProductID method
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Set(f func(productID int64) (ra1 []roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetByProductID.defaultExpectation != nil {
		mmGetByProductID.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetByProductID method")
	}

	if len(mmGetByProductID.expectations) > 0 {
		mmGetByProductID.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetByProductID method")
	}

	mmGetByProductID.mock.funcGetByProductID = f
	mmGetByProductID.mock.funcGetByProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByProductID.mock
}

// When sets expectation for the RolePrimeDB.GetByProductID which will trigger the result defined by the following
// Then helper
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) When(productID int64) *RolePrimeDBMockGetByProductIDExpectation {
	if mmGetByProductID.mock.funcGetByProductID != nil {
		mmGetByProductID.mock.t.Fatalf("RolePrimeDBMock.GetByProductID mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetByProductIDExpectation{
		mock:               mmGetByProductID.mock,
		params:             &RolePrimeDBMockGetByProductIDParams{productID},
		expectationOrigins: RolePrimeDBMockGetByProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductID.expectations = append(mmGetByProductID.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetByProductID return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetByProductIDExpectation) Then(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetByProductIDResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetByProductID should be invoked
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Times(n uint64) *mRolePrimeDBMockGetByProductID {
	if n == 0 {
		mmGetByProductID.mock.t.Fatalf("Times of RolePrimeDBMock.GetByProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductID.expectedInvocations, n)
	mmGetByProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductID
}

func (mmGetByProductID *mRolePrimeDBMockGetByProductID) invocationsDone() bool {
	if len(mmGetByProductID.expectations) == 0 && mmGetByProductID.defaultExpectation == nil && mmGetByProductID.mock.funcGetByProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductID.mock.afterGetByProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductID implements mm_repository.RolePrimeDB
func (mmGetByProductID *RolePrimeDBMock) GetByProductID(productID int64) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetByProductID.beforeGetByProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductID.afterGetByProductIDCounter, 1)

	mmGetByProductID.t.Helper()

	if mmGetByProductID.inspectFuncGetByProductID != nil {
		mmGetByProductID.inspectFuncGetByProductID(productID)
	}

	mm_params := RolePrimeDBMockGetByProductIDParams{productID}

	// Record call args
	mmGetByProductID.GetByProductIDMock.mutex.Lock()
	mmGetByProductID.GetByProductIDMock.callArgs = append(mmGetByProductID.GetByProductIDMock.callArgs, &mm_params)
	mmGetByProductID.GetByProductIDMock.mutex.Unlock()

	for _, e := range mmGetByProductID.GetByProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetByProductID.GetByProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductID.GetByProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductID.GetByProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductID.GetByProductIDMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetByProductIDParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductID.t.Errorf("RolePrimeDBMock.GetByProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductID.t.Errorf("RolePrimeDBMock.GetByProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductID.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductID.GetByProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductID.t.Fatal("No results are set for the RolePrimeDBMock.GetByProductID")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetByProductID.funcGetByProductID != nil {
		return mmGetByProductID.funcGetByProductID(productID)
	}
	mmGetByProductID.t.Fatalf("Unexpected call to RolePrimeDBMock.GetByProductID. %v", productID)
	return
}

// GetByProductIDAfterCounter returns a count of finished RolePrimeDBMock.GetByProductID invocations
func (mmGetByProductID *RolePrimeDBMock) GetByProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.afterGetByProductIDCounter)
}

// GetByProductIDBeforeCounter returns a count of RolePrimeDBMock.GetByProductID invocations
func (mmGetByProductID *RolePrimeDBMock) GetByProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductID.beforeGetByProductIDCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetByProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductID *mRolePrimeDBMockGetByProductID) Calls() []*RolePrimeDBMockGetByProductIDParams {
	mmGetByProductID.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetByProductIDParams, len(mmGetByProductID.callArgs))
	copy(argCopy, mmGetByProductID.callArgs)

	mmGetByProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDDone returns true if the count of the GetByProductID invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetByProductIDDone() bool {
	if m.GetByProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDMock.invocationsDone()
}

// MinimockGetByProductIDInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetByProductIDInspect() {
	for _, e := range m.GetByProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDMock.defaultExpectation != nil && afterGetByProductIDCounter < 1 {
		if m.GetByProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductID at\n%s", m.GetByProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductID at\n%s with params: %#v", m.GetByProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductID != nil && afterGetByProductIDCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductID at\n%s", m.funcGetByProductIDOrigin)
	}

	if !m.GetByProductIDMock.invocationsDone() && afterGetByProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetByProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDMock.expectedInvocations), m.GetByProductIDMock.expectedInvocationsOrigin, afterGetByProductIDCounter)
	}
}

type mRolePrimeDBMockGetByProductIDAndIsActive struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetByProductIDAndIsActiveExpectation
	expectations       []*RolePrimeDBMockGetByProductIDAndIsActiveExpectation

	callArgs []*RolePrimeDBMockGetByProductIDAndIsActiveParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetByProductIDAndIsActiveExpectation specifies expectation struct of the RolePrimeDB.GetByProductIDAndIsActive
type RolePrimeDBMockGetByProductIDAndIsActiveExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetByProductIDAndIsActiveParams
	paramPtrs          *RolePrimeDBMockGetByProductIDAndIsActiveParamPtrs
	expectationOrigins RolePrimeDBMockGetByProductIDAndIsActiveExpectationOrigins
	results            *RolePrimeDBMockGetByProductIDAndIsActiveResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetByProductIDAndIsActiveParams contains parameters of the RolePrimeDB.GetByProductIDAndIsActive
type RolePrimeDBMockGetByProductIDAndIsActiveParams struct {
	productID int64
	isActive  bool
}

// RolePrimeDBMockGetByProductIDAndIsActiveParamPtrs contains pointers to parameters of the RolePrimeDB.GetByProductIDAndIsActive
type RolePrimeDBMockGetByProductIDAndIsActiveParamPtrs struct {
	productID *int64
	isActive  *bool
}

// RolePrimeDBMockGetByProductIDAndIsActiveResults contains results of the RolePrimeDB.GetByProductIDAndIsActive
type RolePrimeDBMockGetByProductIDAndIsActiveResults struct {
	aa1 []roleentity.AdminRole
	err error
}

// RolePrimeDBMockGetByProductIDAndIsActiveOrigins contains origins of expectations of the RolePrimeDB.GetByProductIDAndIsActive
type RolePrimeDBMockGetByProductIDAndIsActiveExpectationOrigins struct {
	origin          string
	originProductID string
	originIsActive  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Optional() *mRolePrimeDBMockGetByProductIDAndIsActive {
	mmGetByProductIDAndIsActive.optional = true
	return mmGetByProductIDAndIsActive
}

// Expect sets up expected params for RolePrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Expect(productID int64, isActive bool) *mRolePrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &RolePrimeDBMockGetByProductIDAndIsActiveExpectation{}
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by ExpectParams functions")
	}

	mmGetByProductIDAndIsActive.defaultExpectation.params = &RolePrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive}
	mmGetByProductIDAndIsActive.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByProductIDAndIsActive.expectations {
		if minimock.Equal(e.params, mmGetByProductIDAndIsActive.defaultExpectation.params) {
			mmGetByProductIDAndIsActive.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByProductIDAndIsActive.defaultExpectation.params)
		}
	}

	return mmGetByProductIDAndIsActive
}

// ExpectProductIDParam1 sets up expected param productID for RolePrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) ExpectProductIDParam1(productID int64) *mRolePrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &RolePrimeDBMockGetByProductIDAndIsActiveExpectation{}
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.params != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Expect")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs = &RolePrimeDBMockGetByProductIDAndIsActiveParamPtrs{}
	}
	mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs.productID = &productID
	mmGetByProductIDAndIsActive.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByProductIDAndIsActive
}

// ExpectIsActiveParam2 sets up expected param isActive for RolePrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) ExpectIsActiveParam2(isActive bool) *mRolePrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &RolePrimeDBMockGetByProductIDAndIsActiveExpectation{}
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.params != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Expect")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs == nil {
		mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs = &RolePrimeDBMockGetByProductIDAndIsActiveParamPtrs{}
	}
	mmGetByProductIDAndIsActive.defaultExpectation.paramPtrs.isActive = &isActive
	mmGetByProductIDAndIsActive.defaultExpectation.expectationOrigins.originIsActive = minimock.CallerInfo(1)

	return mmGetByProductIDAndIsActive
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Inspect(f func(productID int64, isActive bool)) *mRolePrimeDBMockGetByProductIDAndIsActive {
	if mmGetByProductIDAndIsActive.mock.inspectFuncGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetByProductIDAndIsActive")
	}

	mmGetByProductIDAndIsActive.mock.inspectFuncGetByProductIDAndIsActive = f

	return mmGetByProductIDAndIsActive
}

// Return sets up results that will be returned by RolePrimeDB.GetByProductIDAndIsActive
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Return(aa1 []roleentity.AdminRole, err error) *RolePrimeDBMock {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	if mmGetByProductIDAndIsActive.defaultExpectation == nil {
		mmGetByProductIDAndIsActive.defaultExpectation = &RolePrimeDBMockGetByProductIDAndIsActiveExpectation{mock: mmGetByProductIDAndIsActive.mock}
	}
	mmGetByProductIDAndIsActive.defaultExpectation.results = &RolePrimeDBMockGetByProductIDAndIsActiveResults{aa1, err}
	mmGetByProductIDAndIsActive.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndIsActive.mock
}

// Set uses given function f to mock the RolePrimeDB.GetByProductIDAndIsActive method
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Set(f func(productID int64, isActive bool) (aa1 []roleentity.AdminRole, err error)) *RolePrimeDBMock {
	if mmGetByProductIDAndIsActive.defaultExpectation != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetByProductIDAndIsActive method")
	}

	if len(mmGetByProductIDAndIsActive.expectations) > 0 {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetByProductIDAndIsActive method")
	}

	mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive = f
	mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActiveOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndIsActive.mock
}

// When sets expectation for the RolePrimeDB.GetByProductIDAndIsActive which will trigger the result defined by the following
// Then helper
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) When(productID int64, isActive bool) *RolePrimeDBMockGetByProductIDAndIsActiveExpectation {
	if mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("RolePrimeDBMock.GetByProductIDAndIsActive mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetByProductIDAndIsActiveExpectation{
		mock:               mmGetByProductIDAndIsActive.mock,
		params:             &RolePrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive},
		expectationOrigins: RolePrimeDBMockGetByProductIDAndIsActiveExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByProductIDAndIsActive.expectations = append(mmGetByProductIDAndIsActive.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetByProductIDAndIsActive return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetByProductIDAndIsActiveExpectation) Then(aa1 []roleentity.AdminRole, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetByProductIDAndIsActiveResults{aa1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetByProductIDAndIsActive should be invoked
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Times(n uint64) *mRolePrimeDBMockGetByProductIDAndIsActive {
	if n == 0 {
		mmGetByProductIDAndIsActive.mock.t.Fatalf("Times of RolePrimeDBMock.GetByProductIDAndIsActive mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByProductIDAndIsActive.expectedInvocations, n)
	mmGetByProductIDAndIsActive.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByProductIDAndIsActive
}

func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) invocationsDone() bool {
	if len(mmGetByProductIDAndIsActive.expectations) == 0 && mmGetByProductIDAndIsActive.defaultExpectation == nil && mmGetByProductIDAndIsActive.mock.funcGetByProductIDAndIsActive == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.mock.afterGetByProductIDAndIsActiveCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByProductIDAndIsActive implements mm_repository.RolePrimeDB
func (mmGetByProductIDAndIsActive *RolePrimeDBMock) GetByProductIDAndIsActive(productID int64, isActive bool) (aa1 []roleentity.AdminRole, err error) {
	mm_atomic.AddUint64(&mmGetByProductIDAndIsActive.beforeGetByProductIDAndIsActiveCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByProductIDAndIsActive.afterGetByProductIDAndIsActiveCounter, 1)

	mmGetByProductIDAndIsActive.t.Helper()

	if mmGetByProductIDAndIsActive.inspectFuncGetByProductIDAndIsActive != nil {
		mmGetByProductIDAndIsActive.inspectFuncGetByProductIDAndIsActive(productID, isActive)
	}

	mm_params := RolePrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive}

	// Record call args
	mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.mutex.Lock()
	mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.callArgs = append(mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.callArgs, &mm_params)
	mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.mutex.Unlock()

	for _, e := range mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.aa1, e.results.err
		}
	}

	if mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.params
		mm_want_ptrs := mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetByProductIDAndIsActiveParams{productID, isActive}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByProductIDAndIsActive.t.Errorf("RolePrimeDBMock.GetByProductIDAndIsActive got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

			if mm_want_ptrs.isActive != nil && !minimock.Equal(*mm_want_ptrs.isActive, mm_got.isActive) {
				mmGetByProductIDAndIsActive.t.Errorf("RolePrimeDBMock.GetByProductIDAndIsActive got unexpected parameter isActive, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.originIsActive, *mm_want_ptrs.isActive, mm_got.isActive, minimock.Diff(*mm_want_ptrs.isActive, mm_got.isActive))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByProductIDAndIsActive.t.Errorf("RolePrimeDBMock.GetByProductIDAndIsActive got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByProductIDAndIsActive.GetByProductIDAndIsActiveMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByProductIDAndIsActive.t.Fatal("No results are set for the RolePrimeDBMock.GetByProductIDAndIsActive")
		}
		return (*mm_results).aa1, (*mm_results).err
	}
	if mmGetByProductIDAndIsActive.funcGetByProductIDAndIsActive != nil {
		return mmGetByProductIDAndIsActive.funcGetByProductIDAndIsActive(productID, isActive)
	}
	mmGetByProductIDAndIsActive.t.Fatalf("Unexpected call to RolePrimeDBMock.GetByProductIDAndIsActive. %v %v", productID, isActive)
	return
}

// GetByProductIDAndIsActiveAfterCounter returns a count of finished RolePrimeDBMock.GetByProductIDAndIsActive invocations
func (mmGetByProductIDAndIsActive *RolePrimeDBMock) GetByProductIDAndIsActiveAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.afterGetByProductIDAndIsActiveCounter)
}

// GetByProductIDAndIsActiveBeforeCounter returns a count of RolePrimeDBMock.GetByProductIDAndIsActive invocations
func (mmGetByProductIDAndIsActive *RolePrimeDBMock) GetByProductIDAndIsActiveBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByProductIDAndIsActive.beforeGetByProductIDAndIsActiveCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetByProductIDAndIsActive.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByProductIDAndIsActive *mRolePrimeDBMockGetByProductIDAndIsActive) Calls() []*RolePrimeDBMockGetByProductIDAndIsActiveParams {
	mmGetByProductIDAndIsActive.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetByProductIDAndIsActiveParams, len(mmGetByProductIDAndIsActive.callArgs))
	copy(argCopy, mmGetByProductIDAndIsActive.callArgs)

	mmGetByProductIDAndIsActive.mutex.RUnlock()

	return argCopy
}

// MinimockGetByProductIDAndIsActiveDone returns true if the count of the GetByProductIDAndIsActive invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetByProductIDAndIsActiveDone() bool {
	if m.GetByProductIDAndIsActiveMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByProductIDAndIsActiveMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByProductIDAndIsActiveMock.invocationsDone()
}

// MinimockGetByProductIDAndIsActiveInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetByProductIDAndIsActiveInspect() {
	for _, e := range m.GetByProductIDAndIsActiveMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductIDAndIsActive at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByProductIDAndIsActiveCounter := mm_atomic.LoadUint64(&m.afterGetByProductIDAndIsActiveCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByProductIDAndIsActiveMock.defaultExpectation != nil && afterGetByProductIDAndIsActiveCounter < 1 {
		if m.GetByProductIDAndIsActiveMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductIDAndIsActive at\n%s", m.GetByProductIDAndIsActiveMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductIDAndIsActive at\n%s with params: %#v", m.GetByProductIDAndIsActiveMock.defaultExpectation.expectationOrigins.origin, *m.GetByProductIDAndIsActiveMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByProductIDAndIsActive != nil && afterGetByProductIDAndIsActiveCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetByProductIDAndIsActive at\n%s", m.funcGetByProductIDAndIsActiveOrigin)
	}

	if !m.GetByProductIDAndIsActiveMock.invocationsDone() && afterGetByProductIDAndIsActiveCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetByProductIDAndIsActive at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByProductIDAndIsActiveMock.expectedInvocations), m.GetByProductIDAndIsActiveMock.expectedInvocationsOrigin, afterGetByProductIDAndIsActiveCounter)
	}
}

type mRolePrimeDBMockGetByRoleIDAndProductID struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetByRoleIDAndProductIDExpectation
	expectations       []*RolePrimeDBMockGetByRoleIDAndProductIDExpectation

	callArgs []*RolePrimeDBMockGetByRoleIDAndProductIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetByRoleIDAndProductIDExpectation specifies expectation struct of the RolePrimeDB.GetByRoleIDAndProductID
type RolePrimeDBMockGetByRoleIDAndProductIDExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetByRoleIDAndProductIDParams
	paramPtrs          *RolePrimeDBMockGetByRoleIDAndProductIDParamPtrs
	expectationOrigins RolePrimeDBMockGetByRoleIDAndProductIDExpectationOrigins
	results            *RolePrimeDBMockGetByRoleIDAndProductIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetByRoleIDAndProductIDParams contains parameters of the RolePrimeDB.GetByRoleIDAndProductID
type RolePrimeDBMockGetByRoleIDAndProductIDParams struct {
	roleID    int64
	productID int64
}

// RolePrimeDBMockGetByRoleIDAndProductIDParamPtrs contains pointers to parameters of the RolePrimeDB.GetByRoleIDAndProductID
type RolePrimeDBMockGetByRoleIDAndProductIDParamPtrs struct {
	roleID    *int64
	productID *int64
}

// RolePrimeDBMockGetByRoleIDAndProductIDResults contains results of the RolePrimeDB.GetByRoleIDAndProductID
type RolePrimeDBMockGetByRoleIDAndProductIDResults struct {
	r1  roleentity.Role
	err error
}

// RolePrimeDBMockGetByRoleIDAndProductIDOrigins contains origins of expectations of the RolePrimeDB.GetByRoleIDAndProductID
type RolePrimeDBMockGetByRoleIDAndProductIDExpectationOrigins struct {
	origin          string
	originRoleID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Optional() *mRolePrimeDBMockGetByRoleIDAndProductID {
	mmGetByRoleIDAndProductID.optional = true
	return mmGetByRoleIDAndProductID
}

// Expect sets up expected params for RolePrimeDB.GetByRoleIDAndProductID
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Expect(roleID int64, productID int64) *mRolePrimeDBMockGetByRoleIDAndProductID {
	if mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Set")
	}

	if mmGetByRoleIDAndProductID.defaultExpectation == nil {
		mmGetByRoleIDAndProductID.defaultExpectation = &RolePrimeDBMockGetByRoleIDAndProductIDExpectation{}
	}

	if mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by ExpectParams functions")
	}

	mmGetByRoleIDAndProductID.defaultExpectation.params = &RolePrimeDBMockGetByRoleIDAndProductIDParams{roleID, productID}
	mmGetByRoleIDAndProductID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleIDAndProductID.expectations {
		if minimock.Equal(e.params, mmGetByRoleIDAndProductID.defaultExpectation.params) {
			mmGetByRoleIDAndProductID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleIDAndProductID.defaultExpectation.params)
		}
	}

	return mmGetByRoleIDAndProductID
}

// ExpectRoleIDParam1 sets up expected param roleID for RolePrimeDB.GetByRoleIDAndProductID
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) ExpectRoleIDParam1(roleID int64) *mRolePrimeDBMockGetByRoleIDAndProductID {
	if mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Set")
	}

	if mmGetByRoleIDAndProductID.defaultExpectation == nil {
		mmGetByRoleIDAndProductID.defaultExpectation = &RolePrimeDBMockGetByRoleIDAndProductIDExpectation{}
	}

	if mmGetByRoleIDAndProductID.defaultExpectation.params != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Expect")
	}

	if mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetByRoleIDAndProductIDParamPtrs{}
	}
	mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleIDAndProductID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleIDAndProductID
}

// ExpectProductIDParam2 sets up expected param productID for RolePrimeDB.GetByRoleIDAndProductID
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) ExpectProductIDParam2(productID int64) *mRolePrimeDBMockGetByRoleIDAndProductID {
	if mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Set")
	}

	if mmGetByRoleIDAndProductID.defaultExpectation == nil {
		mmGetByRoleIDAndProductID.defaultExpectation = &RolePrimeDBMockGetByRoleIDAndProductIDExpectation{}
	}

	if mmGetByRoleIDAndProductID.defaultExpectation.params != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Expect")
	}

	if mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetByRoleIDAndProductIDParamPtrs{}
	}
	mmGetByRoleIDAndProductID.defaultExpectation.paramPtrs.productID = &productID
	mmGetByRoleIDAndProductID.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetByRoleIDAndProductID
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetByRoleIDAndProductID
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Inspect(f func(roleID int64, productID int64)) *mRolePrimeDBMockGetByRoleIDAndProductID {
	if mmGetByRoleIDAndProductID.mock.inspectFuncGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetByRoleIDAndProductID")
	}

	mmGetByRoleIDAndProductID.mock.inspectFuncGetByRoleIDAndProductID = f

	return mmGetByRoleIDAndProductID
}

// Return sets up results that will be returned by RolePrimeDB.GetByRoleIDAndProductID
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Return(r1 roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Set")
	}

	if mmGetByRoleIDAndProductID.defaultExpectation == nil {
		mmGetByRoleIDAndProductID.defaultExpectation = &RolePrimeDBMockGetByRoleIDAndProductIDExpectation{mock: mmGetByRoleIDAndProductID.mock}
	}
	mmGetByRoleIDAndProductID.defaultExpectation.results = &RolePrimeDBMockGetByRoleIDAndProductIDResults{r1, err}
	mmGetByRoleIDAndProductID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleIDAndProductID.mock
}

// Set uses given function f to mock the RolePrimeDB.GetByRoleIDAndProductID method
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Set(f func(roleID int64, productID int64) (r1 roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetByRoleIDAndProductID.defaultExpectation != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetByRoleIDAndProductID method")
	}

	if len(mmGetByRoleIDAndProductID.expectations) > 0 {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetByRoleIDAndProductID method")
	}

	mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID = f
	mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleIDAndProductID.mock
}

// When sets expectation for the RolePrimeDB.GetByRoleIDAndProductID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) When(roleID int64, productID int64) *RolePrimeDBMockGetByRoleIDAndProductIDExpectation {
	if mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("RolePrimeDBMock.GetByRoleIDAndProductID mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetByRoleIDAndProductIDExpectation{
		mock:               mmGetByRoleIDAndProductID.mock,
		params:             &RolePrimeDBMockGetByRoleIDAndProductIDParams{roleID, productID},
		expectationOrigins: RolePrimeDBMockGetByRoleIDAndProductIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleIDAndProductID.expectations = append(mmGetByRoleIDAndProductID.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetByRoleIDAndProductID return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetByRoleIDAndProductIDExpectation) Then(r1 roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetByRoleIDAndProductIDResults{r1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetByRoleIDAndProductID should be invoked
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Times(n uint64) *mRolePrimeDBMockGetByRoleIDAndProductID {
	if n == 0 {
		mmGetByRoleIDAndProductID.mock.t.Fatalf("Times of RolePrimeDBMock.GetByRoleIDAndProductID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleIDAndProductID.expectedInvocations, n)
	mmGetByRoleIDAndProductID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleIDAndProductID
}

func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) invocationsDone() bool {
	if len(mmGetByRoleIDAndProductID.expectations) == 0 && mmGetByRoleIDAndProductID.defaultExpectation == nil && mmGetByRoleIDAndProductID.mock.funcGetByRoleIDAndProductID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleIDAndProductID.mock.afterGetByRoleIDAndProductIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleIDAndProductID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleIDAndProductID implements mm_repository.RolePrimeDB
func (mmGetByRoleIDAndProductID *RolePrimeDBMock) GetByRoleIDAndProductID(roleID int64, productID int64) (r1 roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetByRoleIDAndProductID.beforeGetByRoleIDAndProductIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleIDAndProductID.afterGetByRoleIDAndProductIDCounter, 1)

	mmGetByRoleIDAndProductID.t.Helper()

	if mmGetByRoleIDAndProductID.inspectFuncGetByRoleIDAndProductID != nil {
		mmGetByRoleIDAndProductID.inspectFuncGetByRoleIDAndProductID(roleID, productID)
	}

	mm_params := RolePrimeDBMockGetByRoleIDAndProductIDParams{roleID, productID}

	// Record call args
	mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.mutex.Lock()
	mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.callArgs = append(mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.callArgs, &mm_params)
	mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetByRoleIDAndProductIDParams{roleID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleIDAndProductID.t.Errorf("RolePrimeDBMock.GetByRoleIDAndProductID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetByRoleIDAndProductID.t.Errorf("RolePrimeDBMock.GetByRoleIDAndProductID got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleIDAndProductID.t.Errorf("RolePrimeDBMock.GetByRoleIDAndProductID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleIDAndProductID.GetByRoleIDAndProductIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleIDAndProductID.t.Fatal("No results are set for the RolePrimeDBMock.GetByRoleIDAndProductID")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmGetByRoleIDAndProductID.funcGetByRoleIDAndProductID != nil {
		return mmGetByRoleIDAndProductID.funcGetByRoleIDAndProductID(roleID, productID)
	}
	mmGetByRoleIDAndProductID.t.Fatalf("Unexpected call to RolePrimeDBMock.GetByRoleIDAndProductID. %v %v", roleID, productID)
	return
}

// GetByRoleIDAndProductIDAfterCounter returns a count of finished RolePrimeDBMock.GetByRoleIDAndProductID invocations
func (mmGetByRoleIDAndProductID *RolePrimeDBMock) GetByRoleIDAndProductIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleIDAndProductID.afterGetByRoleIDAndProductIDCounter)
}

// GetByRoleIDAndProductIDBeforeCounter returns a count of RolePrimeDBMock.GetByRoleIDAndProductID invocations
func (mmGetByRoleIDAndProductID *RolePrimeDBMock) GetByRoleIDAndProductIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleIDAndProductID.beforeGetByRoleIDAndProductIDCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetByRoleIDAndProductID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleIDAndProductID *mRolePrimeDBMockGetByRoleIDAndProductID) Calls() []*RolePrimeDBMockGetByRoleIDAndProductIDParams {
	mmGetByRoleIDAndProductID.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetByRoleIDAndProductIDParams, len(mmGetByRoleIDAndProductID.callArgs))
	copy(argCopy, mmGetByRoleIDAndProductID.callArgs)

	mmGetByRoleIDAndProductID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDAndProductIDDone returns true if the count of the GetByRoleIDAndProductID invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetByRoleIDAndProductIDDone() bool {
	if m.GetByRoleIDAndProductIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDAndProductIDMock.invocationsDone()
}

// MinimockGetByRoleIDAndProductIDInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetByRoleIDAndProductIDInspect() {
	for _, e := range m.GetByRoleIDAndProductIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByRoleIDAndProductID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDAndProductIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDAndProductIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDAndProductIDMock.defaultExpectation != nil && afterGetByRoleIDAndProductIDCounter < 1 {
		if m.GetByRoleIDAndProductIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByRoleIDAndProductID at\n%s", m.GetByRoleIDAndProductIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetByRoleIDAndProductID at\n%s with params: %#v", m.GetByRoleIDAndProductIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDAndProductIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleIDAndProductID != nil && afterGetByRoleIDAndProductIDCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetByRoleIDAndProductID at\n%s", m.funcGetByRoleIDAndProductIDOrigin)
	}

	if !m.GetByRoleIDAndProductIDMock.invocationsDone() && afterGetByRoleIDAndProductIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetByRoleIDAndProductID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDAndProductIDMock.expectedInvocations), m.GetByRoleIDAndProductIDMock.expectedInvocationsOrigin, afterGetByRoleIDAndProductIDCounter)
	}
}

type mRolePrimeDBMockGetBySystemType struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetBySystemTypeExpectation
	expectations       []*RolePrimeDBMockGetBySystemTypeExpectation

	callArgs []*RolePrimeDBMockGetBySystemTypeParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetBySystemTypeExpectation specifies expectation struct of the RolePrimeDB.GetBySystemType
type RolePrimeDBMockGetBySystemTypeExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetBySystemTypeParams
	paramPtrs          *RolePrimeDBMockGetBySystemTypeParamPtrs
	expectationOrigins RolePrimeDBMockGetBySystemTypeExpectationOrigins
	results            *RolePrimeDBMockGetBySystemTypeResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetBySystemTypeParams contains parameters of the RolePrimeDB.GetBySystemType
type RolePrimeDBMockGetBySystemTypeParams struct {
	isSystemRole bool
}

// RolePrimeDBMockGetBySystemTypeParamPtrs contains pointers to parameters of the RolePrimeDB.GetBySystemType
type RolePrimeDBMockGetBySystemTypeParamPtrs struct {
	isSystemRole *bool
}

// RolePrimeDBMockGetBySystemTypeResults contains results of the RolePrimeDB.GetBySystemType
type RolePrimeDBMockGetBySystemTypeResults struct {
	ra1 []roleentity.Role
	err error
}

// RolePrimeDBMockGetBySystemTypeOrigins contains origins of expectations of the RolePrimeDB.GetBySystemType
type RolePrimeDBMockGetBySystemTypeExpectationOrigins struct {
	origin             string
	originIsSystemRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Optional() *mRolePrimeDBMockGetBySystemType {
	mmGetBySystemType.optional = true
	return mmGetBySystemType
}

// Expect sets up expected params for RolePrimeDB.GetBySystemType
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Expect(isSystemRole bool) *mRolePrimeDBMockGetBySystemType {
	if mmGetBySystemType.mock.funcGetBySystemType != nil {
		mmGetBySystemType.mock.t.Fatalf("RolePrimeDBMock.GetBySystemType mock is already set by Set")
	}

	if mmGetBySystemType.defaultExpectation == nil {
		mmGetBySystemType.defaultExpectation = &RolePrimeDBMockGetBySystemTypeExpectation{}
	}

	if mmGetBySystemType.defaultExpectation.paramPtrs != nil {
		mmGetBySystemType.mock.t.Fatalf("RolePrimeDBMock.GetBySystemType mock is already set by ExpectParams functions")
	}

	mmGetBySystemType.defaultExpectation.params = &RolePrimeDBMockGetBySystemTypeParams{isSystemRole}
	mmGetBySystemType.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetBySystemType.expectations {
		if minimock.Equal(e.params, mmGetBySystemType.defaultExpectation.params) {
			mmGetBySystemType.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetBySystemType.defaultExpectation.params)
		}
	}

	return mmGetBySystemType
}

// ExpectIsSystemRoleParam1 sets up expected param isSystemRole for RolePrimeDB.GetBySystemType
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) ExpectIsSystemRoleParam1(isSystemRole bool) *mRolePrimeDBMockGetBySystemType {
	if mmGetBySystemType.mock.funcGetBySystemType != nil {
		mmGetBySystemType.mock.t.Fatalf("RolePrimeDBMock.GetBySystemType mock is already set by Set")
	}

	if mmGetBySystemType.defaultExpectation == nil {
		mmGetBySystemType.defaultExpectation = &RolePrimeDBMockGetBySystemTypeExpectation{}
	}

	if mmGetBySystemType.defaultExpectation.params != nil {
		mmGetBySystemType.mock.t.Fatalf("RolePrimeDBMock.GetBySystemType mock is already set by Expect")
	}

	if mmGetBySystemType.defaultExpectation.paramPtrs == nil {
		mmGetBySystemType.defaultExpectation.paramPtrs = &RolePrimeDBMockGetBySystemTypeParamPtrs{}
	}
	mmGetBySystemType.defaultExpectation.paramPtrs.isSystemRole = &isSystemRole
	mmGetBySystemType.defaultExpectation.expectationOrigins.originIsSystemRole = minimock.CallerInfo(1)

	return mmGetBySystemType
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetBySystemType
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Inspect(f func(isSystemRole bool)) *mRolePrimeDBMockGetBySystemType {
	if mmGetBySystemType.mock.inspectFuncGetBySystemType != nil {
		mmGetBySystemType.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetBySystemType")
	}

	mmGetBySystemType.mock.inspectFuncGetBySystemType = f

	return mmGetBySystemType
}

// Return sets up results that will be returned by RolePrimeDB.GetBySystemType
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Return(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetBySystemType.mock.funcGetBySystemType != nil {
		mmGetBySystemType.mock.t.Fatalf("RolePrimeDBMock.GetBySystemType mock is already set by Set")
	}

	if mmGetBySystemType.defaultExpectation == nil {
		mmGetBySystemType.defaultExpectation = &RolePrimeDBMockGetBySystemTypeExpectation{mock: mmGetBySystemType.mock}
	}
	mmGetBySystemType.defaultExpectation.results = &RolePrimeDBMockGetBySystemTypeResults{ra1, err}
	mmGetBySystemType.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetBySystemType.mock
}

// Set uses given function f to mock the RolePrimeDB.GetBySystemType method
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Set(f func(isSystemRole bool) (ra1 []roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetBySystemType.defaultExpectation != nil {
		mmGetBySystemType.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetBySystemType method")
	}

	if len(mmGetBySystemType.expectations) > 0 {
		mmGetBySystemType.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetBySystemType method")
	}

	mmGetBySystemType.mock.funcGetBySystemType = f
	mmGetBySystemType.mock.funcGetBySystemTypeOrigin = minimock.CallerInfo(1)
	return mmGetBySystemType.mock
}

// When sets expectation for the RolePrimeDB.GetBySystemType which will trigger the result defined by the following
// Then helper
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) When(isSystemRole bool) *RolePrimeDBMockGetBySystemTypeExpectation {
	if mmGetBySystemType.mock.funcGetBySystemType != nil {
		mmGetBySystemType.mock.t.Fatalf("RolePrimeDBMock.GetBySystemType mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetBySystemTypeExpectation{
		mock:               mmGetBySystemType.mock,
		params:             &RolePrimeDBMockGetBySystemTypeParams{isSystemRole},
		expectationOrigins: RolePrimeDBMockGetBySystemTypeExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetBySystemType.expectations = append(mmGetBySystemType.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetBySystemType return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetBySystemTypeExpectation) Then(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetBySystemTypeResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetBySystemType should be invoked
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Times(n uint64) *mRolePrimeDBMockGetBySystemType {
	if n == 0 {
		mmGetBySystemType.mock.t.Fatalf("Times of RolePrimeDBMock.GetBySystemType mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetBySystemType.expectedInvocations, n)
	mmGetBySystemType.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetBySystemType
}

func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) invocationsDone() bool {
	if len(mmGetBySystemType.expectations) == 0 && mmGetBySystemType.defaultExpectation == nil && mmGetBySystemType.mock.funcGetBySystemType == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetBySystemType.mock.afterGetBySystemTypeCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetBySystemType.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetBySystemType implements mm_repository.RolePrimeDB
func (mmGetBySystemType *RolePrimeDBMock) GetBySystemType(isSystemRole bool) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetBySystemType.beforeGetBySystemTypeCounter, 1)
	defer mm_atomic.AddUint64(&mmGetBySystemType.afterGetBySystemTypeCounter, 1)

	mmGetBySystemType.t.Helper()

	if mmGetBySystemType.inspectFuncGetBySystemType != nil {
		mmGetBySystemType.inspectFuncGetBySystemType(isSystemRole)
	}

	mm_params := RolePrimeDBMockGetBySystemTypeParams{isSystemRole}

	// Record call args
	mmGetBySystemType.GetBySystemTypeMock.mutex.Lock()
	mmGetBySystemType.GetBySystemTypeMock.callArgs = append(mmGetBySystemType.GetBySystemTypeMock.callArgs, &mm_params)
	mmGetBySystemType.GetBySystemTypeMock.mutex.Unlock()

	for _, e := range mmGetBySystemType.GetBySystemTypeMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetBySystemType.GetBySystemTypeMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetBySystemType.GetBySystemTypeMock.defaultExpectation.Counter, 1)
		mm_want := mmGetBySystemType.GetBySystemTypeMock.defaultExpectation.params
		mm_want_ptrs := mmGetBySystemType.GetBySystemTypeMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetBySystemTypeParams{isSystemRole}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.isSystemRole != nil && !minimock.Equal(*mm_want_ptrs.isSystemRole, mm_got.isSystemRole) {
				mmGetBySystemType.t.Errorf("RolePrimeDBMock.GetBySystemType got unexpected parameter isSystemRole, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetBySystemType.GetBySystemTypeMock.defaultExpectation.expectationOrigins.originIsSystemRole, *mm_want_ptrs.isSystemRole, mm_got.isSystemRole, minimock.Diff(*mm_want_ptrs.isSystemRole, mm_got.isSystemRole))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetBySystemType.t.Errorf("RolePrimeDBMock.GetBySystemType got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetBySystemType.GetBySystemTypeMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetBySystemType.GetBySystemTypeMock.defaultExpectation.results
		if mm_results == nil {
			mmGetBySystemType.t.Fatal("No results are set for the RolePrimeDBMock.GetBySystemType")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetBySystemType.funcGetBySystemType != nil {
		return mmGetBySystemType.funcGetBySystemType(isSystemRole)
	}
	mmGetBySystemType.t.Fatalf("Unexpected call to RolePrimeDBMock.GetBySystemType. %v", isSystemRole)
	return
}

// GetBySystemTypeAfterCounter returns a count of finished RolePrimeDBMock.GetBySystemType invocations
func (mmGetBySystemType *RolePrimeDBMock) GetBySystemTypeAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetBySystemType.afterGetBySystemTypeCounter)
}

// GetBySystemTypeBeforeCounter returns a count of RolePrimeDBMock.GetBySystemType invocations
func (mmGetBySystemType *RolePrimeDBMock) GetBySystemTypeBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetBySystemType.beforeGetBySystemTypeCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetBySystemType.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetBySystemType *mRolePrimeDBMockGetBySystemType) Calls() []*RolePrimeDBMockGetBySystemTypeParams {
	mmGetBySystemType.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetBySystemTypeParams, len(mmGetBySystemType.callArgs))
	copy(argCopy, mmGetBySystemType.callArgs)

	mmGetBySystemType.mutex.RUnlock()

	return argCopy
}

// MinimockGetBySystemTypeDone returns true if the count of the GetBySystemType invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetBySystemTypeDone() bool {
	if m.GetBySystemTypeMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetBySystemTypeMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetBySystemTypeMock.invocationsDone()
}

// MinimockGetBySystemTypeInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetBySystemTypeInspect() {
	for _, e := range m.GetBySystemTypeMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetBySystemType at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetBySystemTypeCounter := mm_atomic.LoadUint64(&m.afterGetBySystemTypeCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetBySystemTypeMock.defaultExpectation != nil && afterGetBySystemTypeCounter < 1 {
		if m.GetBySystemTypeMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetBySystemType at\n%s", m.GetBySystemTypeMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetBySystemType at\n%s with params: %#v", m.GetBySystemTypeMock.defaultExpectation.expectationOrigins.origin, *m.GetBySystemTypeMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetBySystemType != nil && afterGetBySystemTypeCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetBySystemType at\n%s", m.funcGetBySystemTypeOrigin)
	}

	if !m.GetBySystemTypeMock.invocationsDone() && afterGetBySystemTypeCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetBySystemType at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetBySystemTypeMock.expectedInvocations), m.GetBySystemTypeMock.expectedInvocationsOrigin, afterGetBySystemTypeCounter)
	}
}

type mRolePrimeDBMockGetOwnerRole struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetOwnerRoleExpectation
	expectations       []*RolePrimeDBMockGetOwnerRoleExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetOwnerRoleExpectation specifies expectation struct of the RolePrimeDB.GetOwnerRole
type RolePrimeDBMockGetOwnerRoleExpectation struct {
	mock *RolePrimeDBMock

	results      *RolePrimeDBMockGetOwnerRoleResults
	returnOrigin string
	Counter      uint64
}

// RolePrimeDBMockGetOwnerRoleResults contains results of the RolePrimeDB.GetOwnerRole
type RolePrimeDBMockGetOwnerRoleResults struct {
	r1  roleentity.Role
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) Optional() *mRolePrimeDBMockGetOwnerRole {
	mmGetOwnerRole.optional = true
	return mmGetOwnerRole
}

// Expect sets up expected params for RolePrimeDB.GetOwnerRole
func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) Expect() *mRolePrimeDBMockGetOwnerRole {
	if mmGetOwnerRole.mock.funcGetOwnerRole != nil {
		mmGetOwnerRole.mock.t.Fatalf("RolePrimeDBMock.GetOwnerRole mock is already set by Set")
	}

	if mmGetOwnerRole.defaultExpectation == nil {
		mmGetOwnerRole.defaultExpectation = &RolePrimeDBMockGetOwnerRoleExpectation{}
	}

	return mmGetOwnerRole
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetOwnerRole
func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) Inspect(f func()) *mRolePrimeDBMockGetOwnerRole {
	if mmGetOwnerRole.mock.inspectFuncGetOwnerRole != nil {
		mmGetOwnerRole.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetOwnerRole")
	}

	mmGetOwnerRole.mock.inspectFuncGetOwnerRole = f

	return mmGetOwnerRole
}

// Return sets up results that will be returned by RolePrimeDB.GetOwnerRole
func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) Return(r1 roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetOwnerRole.mock.funcGetOwnerRole != nil {
		mmGetOwnerRole.mock.t.Fatalf("RolePrimeDBMock.GetOwnerRole mock is already set by Set")
	}

	if mmGetOwnerRole.defaultExpectation == nil {
		mmGetOwnerRole.defaultExpectation = &RolePrimeDBMockGetOwnerRoleExpectation{mock: mmGetOwnerRole.mock}
	}
	mmGetOwnerRole.defaultExpectation.results = &RolePrimeDBMockGetOwnerRoleResults{r1, err}
	mmGetOwnerRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetOwnerRole.mock
}

// Set uses given function f to mock the RolePrimeDB.GetOwnerRole method
func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) Set(f func() (r1 roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetOwnerRole.defaultExpectation != nil {
		mmGetOwnerRole.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetOwnerRole method")
	}

	if len(mmGetOwnerRole.expectations) > 0 {
		mmGetOwnerRole.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetOwnerRole method")
	}

	mmGetOwnerRole.mock.funcGetOwnerRole = f
	mmGetOwnerRole.mock.funcGetOwnerRoleOrigin = minimock.CallerInfo(1)
	return mmGetOwnerRole.mock
}

// Times sets number of times RolePrimeDB.GetOwnerRole should be invoked
func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) Times(n uint64) *mRolePrimeDBMockGetOwnerRole {
	if n == 0 {
		mmGetOwnerRole.mock.t.Fatalf("Times of RolePrimeDBMock.GetOwnerRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetOwnerRole.expectedInvocations, n)
	mmGetOwnerRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetOwnerRole
}

func (mmGetOwnerRole *mRolePrimeDBMockGetOwnerRole) invocationsDone() bool {
	if len(mmGetOwnerRole.expectations) == 0 && mmGetOwnerRole.defaultExpectation == nil && mmGetOwnerRole.mock.funcGetOwnerRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetOwnerRole.mock.afterGetOwnerRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetOwnerRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetOwnerRole implements mm_repository.RolePrimeDB
func (mmGetOwnerRole *RolePrimeDBMock) GetOwnerRole() (r1 roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetOwnerRole.beforeGetOwnerRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmGetOwnerRole.afterGetOwnerRoleCounter, 1)

	mmGetOwnerRole.t.Helper()

	if mmGetOwnerRole.inspectFuncGetOwnerRole != nil {
		mmGetOwnerRole.inspectFuncGetOwnerRole()
	}

	if mmGetOwnerRole.GetOwnerRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetOwnerRole.GetOwnerRoleMock.defaultExpectation.Counter, 1)

		mm_results := mmGetOwnerRole.GetOwnerRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmGetOwnerRole.t.Fatal("No results are set for the RolePrimeDBMock.GetOwnerRole")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmGetOwnerRole.funcGetOwnerRole != nil {
		return mmGetOwnerRole.funcGetOwnerRole()
	}
	mmGetOwnerRole.t.Fatalf("Unexpected call to RolePrimeDBMock.GetOwnerRole.")
	return
}

// GetOwnerRoleAfterCounter returns a count of finished RolePrimeDBMock.GetOwnerRole invocations
func (mmGetOwnerRole *RolePrimeDBMock) GetOwnerRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnerRole.afterGetOwnerRoleCounter)
}

// GetOwnerRoleBeforeCounter returns a count of RolePrimeDBMock.GetOwnerRole invocations
func (mmGetOwnerRole *RolePrimeDBMock) GetOwnerRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetOwnerRole.beforeGetOwnerRoleCounter)
}

// MinimockGetOwnerRoleDone returns true if the count of the GetOwnerRole invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetOwnerRoleDone() bool {
	if m.GetOwnerRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetOwnerRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetOwnerRoleMock.invocationsDone()
}

// MinimockGetOwnerRoleInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetOwnerRoleInspect() {
	for _, e := range m.GetOwnerRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to RolePrimeDBMock.GetOwnerRole")
		}
	}

	afterGetOwnerRoleCounter := mm_atomic.LoadUint64(&m.afterGetOwnerRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetOwnerRoleMock.defaultExpectation != nil && afterGetOwnerRoleCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetOwnerRole at\n%s", m.GetOwnerRoleMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetOwnerRole != nil && afterGetOwnerRoleCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetOwnerRole at\n%s", m.funcGetOwnerRoleOrigin)
	}

	if !m.GetOwnerRoleMock.invocationsDone() && afterGetOwnerRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetOwnerRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetOwnerRoleMock.expectedInvocations), m.GetOwnerRoleMock.expectedInvocationsOrigin, afterGetOwnerRoleCounter)
	}
}

type mRolePrimeDBMockGetRoleCategoryLinks struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetRoleCategoryLinksExpectation
	expectations       []*RolePrimeDBMockGetRoleCategoryLinksExpectation

	callArgs []*RolePrimeDBMockGetRoleCategoryLinksParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetRoleCategoryLinksExpectation specifies expectation struct of the RolePrimeDB.GetRoleCategoryLinks
type RolePrimeDBMockGetRoleCategoryLinksExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetRoleCategoryLinksParams
	paramPtrs          *RolePrimeDBMockGetRoleCategoryLinksParamPtrs
	expectationOrigins RolePrimeDBMockGetRoleCategoryLinksExpectationOrigins
	results            *RolePrimeDBMockGetRoleCategoryLinksResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetRoleCategoryLinksParams contains parameters of the RolePrimeDB.GetRoleCategoryLinks
type RolePrimeDBMockGetRoleCategoryLinksParams struct {
	ctx context.Context
}

// RolePrimeDBMockGetRoleCategoryLinksParamPtrs contains pointers to parameters of the RolePrimeDB.GetRoleCategoryLinks
type RolePrimeDBMockGetRoleCategoryLinksParamPtrs struct {
	ctx *context.Context
}

// RolePrimeDBMockGetRoleCategoryLinksResults contains results of the RolePrimeDB.GetRoleCategoryLinks
type RolePrimeDBMockGetRoleCategoryLinksResults struct {
	ra1 []roleentity.RoleCategoryLink
	err error
}

// RolePrimeDBMockGetRoleCategoryLinksOrigins contains origins of expectations of the RolePrimeDB.GetRoleCategoryLinks
type RolePrimeDBMockGetRoleCategoryLinksExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Optional() *mRolePrimeDBMockGetRoleCategoryLinks {
	mmGetRoleCategoryLinks.optional = true
	return mmGetRoleCategoryLinks
}

// Expect sets up expected params for RolePrimeDB.GetRoleCategoryLinks
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Expect(ctx context.Context) *mRolePrimeDBMockGetRoleCategoryLinks {
	if mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinks != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("RolePrimeDBMock.GetRoleCategoryLinks mock is already set by Set")
	}

	if mmGetRoleCategoryLinks.defaultExpectation == nil {
		mmGetRoleCategoryLinks.defaultExpectation = &RolePrimeDBMockGetRoleCategoryLinksExpectation{}
	}

	if mmGetRoleCategoryLinks.defaultExpectation.paramPtrs != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("RolePrimeDBMock.GetRoleCategoryLinks mock is already set by ExpectParams functions")
	}

	mmGetRoleCategoryLinks.defaultExpectation.params = &RolePrimeDBMockGetRoleCategoryLinksParams{ctx}
	mmGetRoleCategoryLinks.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetRoleCategoryLinks.expectations {
		if minimock.Equal(e.params, mmGetRoleCategoryLinks.defaultExpectation.params) {
			mmGetRoleCategoryLinks.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetRoleCategoryLinks.defaultExpectation.params)
		}
	}

	return mmGetRoleCategoryLinks
}

// ExpectCtxParam1 sets up expected param ctx for RolePrimeDB.GetRoleCategoryLinks
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) ExpectCtxParam1(ctx context.Context) *mRolePrimeDBMockGetRoleCategoryLinks {
	if mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinks != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("RolePrimeDBMock.GetRoleCategoryLinks mock is already set by Set")
	}

	if mmGetRoleCategoryLinks.defaultExpectation == nil {
		mmGetRoleCategoryLinks.defaultExpectation = &RolePrimeDBMockGetRoleCategoryLinksExpectation{}
	}

	if mmGetRoleCategoryLinks.defaultExpectation.params != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("RolePrimeDBMock.GetRoleCategoryLinks mock is already set by Expect")
	}

	if mmGetRoleCategoryLinks.defaultExpectation.paramPtrs == nil {
		mmGetRoleCategoryLinks.defaultExpectation.paramPtrs = &RolePrimeDBMockGetRoleCategoryLinksParamPtrs{}
	}
	mmGetRoleCategoryLinks.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetRoleCategoryLinks.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetRoleCategoryLinks
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetRoleCategoryLinks
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Inspect(f func(ctx context.Context)) *mRolePrimeDBMockGetRoleCategoryLinks {
	if mmGetRoleCategoryLinks.mock.inspectFuncGetRoleCategoryLinks != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetRoleCategoryLinks")
	}

	mmGetRoleCategoryLinks.mock.inspectFuncGetRoleCategoryLinks = f

	return mmGetRoleCategoryLinks
}

// Return sets up results that will be returned by RolePrimeDB.GetRoleCategoryLinks
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Return(ra1 []roleentity.RoleCategoryLink, err error) *RolePrimeDBMock {
	if mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinks != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("RolePrimeDBMock.GetRoleCategoryLinks mock is already set by Set")
	}

	if mmGetRoleCategoryLinks.defaultExpectation == nil {
		mmGetRoleCategoryLinks.defaultExpectation = &RolePrimeDBMockGetRoleCategoryLinksExpectation{mock: mmGetRoleCategoryLinks.mock}
	}
	mmGetRoleCategoryLinks.defaultExpectation.results = &RolePrimeDBMockGetRoleCategoryLinksResults{ra1, err}
	mmGetRoleCategoryLinks.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRoleCategoryLinks.mock
}

// Set uses given function f to mock the RolePrimeDB.GetRoleCategoryLinks method
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Set(f func(ctx context.Context) (ra1 []roleentity.RoleCategoryLink, err error)) *RolePrimeDBMock {
	if mmGetRoleCategoryLinks.defaultExpectation != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetRoleCategoryLinks method")
	}

	if len(mmGetRoleCategoryLinks.expectations) > 0 {
		mmGetRoleCategoryLinks.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetRoleCategoryLinks method")
	}

	mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinks = f
	mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinksOrigin = minimock.CallerInfo(1)
	return mmGetRoleCategoryLinks.mock
}

// When sets expectation for the RolePrimeDB.GetRoleCategoryLinks which will trigger the result defined by the following
// Then helper
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) When(ctx context.Context) *RolePrimeDBMockGetRoleCategoryLinksExpectation {
	if mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinks != nil {
		mmGetRoleCategoryLinks.mock.t.Fatalf("RolePrimeDBMock.GetRoleCategoryLinks mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetRoleCategoryLinksExpectation{
		mock:               mmGetRoleCategoryLinks.mock,
		params:             &RolePrimeDBMockGetRoleCategoryLinksParams{ctx},
		expectationOrigins: RolePrimeDBMockGetRoleCategoryLinksExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetRoleCategoryLinks.expectations = append(mmGetRoleCategoryLinks.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetRoleCategoryLinks return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetRoleCategoryLinksExpectation) Then(ra1 []roleentity.RoleCategoryLink, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetRoleCategoryLinksResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetRoleCategoryLinks should be invoked
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Times(n uint64) *mRolePrimeDBMockGetRoleCategoryLinks {
	if n == 0 {
		mmGetRoleCategoryLinks.mock.t.Fatalf("Times of RolePrimeDBMock.GetRoleCategoryLinks mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRoleCategoryLinks.expectedInvocations, n)
	mmGetRoleCategoryLinks.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRoleCategoryLinks
}

func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) invocationsDone() bool {
	if len(mmGetRoleCategoryLinks.expectations) == 0 && mmGetRoleCategoryLinks.defaultExpectation == nil && mmGetRoleCategoryLinks.mock.funcGetRoleCategoryLinks == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRoleCategoryLinks.mock.afterGetRoleCategoryLinksCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRoleCategoryLinks.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRoleCategoryLinks implements mm_repository.RolePrimeDB
func (mmGetRoleCategoryLinks *RolePrimeDBMock) GetRoleCategoryLinks(ctx context.Context) (ra1 []roleentity.RoleCategoryLink, err error) {
	mm_atomic.AddUint64(&mmGetRoleCategoryLinks.beforeGetRoleCategoryLinksCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRoleCategoryLinks.afterGetRoleCategoryLinksCounter, 1)

	mmGetRoleCategoryLinks.t.Helper()

	if mmGetRoleCategoryLinks.inspectFuncGetRoleCategoryLinks != nil {
		mmGetRoleCategoryLinks.inspectFuncGetRoleCategoryLinks(ctx)
	}

	mm_params := RolePrimeDBMockGetRoleCategoryLinksParams{ctx}

	// Record call args
	mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.mutex.Lock()
	mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.callArgs = append(mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.callArgs, &mm_params)
	mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.mutex.Unlock()

	for _, e := range mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation.Counter, 1)
		mm_want := mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation.params
		mm_want_ptrs := mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetRoleCategoryLinksParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetRoleCategoryLinks.t.Errorf("RolePrimeDBMock.GetRoleCategoryLinks got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetRoleCategoryLinks.t.Errorf("RolePrimeDBMock.GetRoleCategoryLinks got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetRoleCategoryLinks.GetRoleCategoryLinksMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRoleCategoryLinks.t.Fatal("No results are set for the RolePrimeDBMock.GetRoleCategoryLinks")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetRoleCategoryLinks.funcGetRoleCategoryLinks != nil {
		return mmGetRoleCategoryLinks.funcGetRoleCategoryLinks(ctx)
	}
	mmGetRoleCategoryLinks.t.Fatalf("Unexpected call to RolePrimeDBMock.GetRoleCategoryLinks. %v", ctx)
	return
}

// GetRoleCategoryLinksAfterCounter returns a count of finished RolePrimeDBMock.GetRoleCategoryLinks invocations
func (mmGetRoleCategoryLinks *RolePrimeDBMock) GetRoleCategoryLinksAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRoleCategoryLinks.afterGetRoleCategoryLinksCounter)
}

// GetRoleCategoryLinksBeforeCounter returns a count of RolePrimeDBMock.GetRoleCategoryLinks invocations
func (mmGetRoleCategoryLinks *RolePrimeDBMock) GetRoleCategoryLinksBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRoleCategoryLinks.beforeGetRoleCategoryLinksCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetRoleCategoryLinks.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetRoleCategoryLinks *mRolePrimeDBMockGetRoleCategoryLinks) Calls() []*RolePrimeDBMockGetRoleCategoryLinksParams {
	mmGetRoleCategoryLinks.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetRoleCategoryLinksParams, len(mmGetRoleCategoryLinks.callArgs))
	copy(argCopy, mmGetRoleCategoryLinks.callArgs)

	mmGetRoleCategoryLinks.mutex.RUnlock()

	return argCopy
}

// MinimockGetRoleCategoryLinksDone returns true if the count of the GetRoleCategoryLinks invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetRoleCategoryLinksDone() bool {
	if m.GetRoleCategoryLinksMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRoleCategoryLinksMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRoleCategoryLinksMock.invocationsDone()
}

// MinimockGetRoleCategoryLinksInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetRoleCategoryLinksInspect() {
	for _, e := range m.GetRoleCategoryLinksMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleCategoryLinks at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetRoleCategoryLinksCounter := mm_atomic.LoadUint64(&m.afterGetRoleCategoryLinksCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRoleCategoryLinksMock.defaultExpectation != nil && afterGetRoleCategoryLinksCounter < 1 {
		if m.GetRoleCategoryLinksMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleCategoryLinks at\n%s", m.GetRoleCategoryLinksMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleCategoryLinks at\n%s with params: %#v", m.GetRoleCategoryLinksMock.defaultExpectation.expectationOrigins.origin, *m.GetRoleCategoryLinksMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRoleCategoryLinks != nil && afterGetRoleCategoryLinksCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleCategoryLinks at\n%s", m.funcGetRoleCategoryLinksOrigin)
	}

	if !m.GetRoleCategoryLinksMock.invocationsDone() && afterGetRoleCategoryLinksCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetRoleCategoryLinks at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRoleCategoryLinksMock.expectedInvocations), m.GetRoleCategoryLinksMock.expectedInvocationsOrigin, afterGetRoleCategoryLinksCounter)
	}
}

type mRolePrimeDBMockGetRoleWithProductByParticipantID struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation
	expectations       []*RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation

	callArgs []*RolePrimeDBMockGetRoleWithProductByParticipantIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation specifies expectation struct of the RolePrimeDB.GetRoleWithProductByParticipantID
type RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetRoleWithProductByParticipantIDParams
	paramPtrs          *RolePrimeDBMockGetRoleWithProductByParticipantIDParamPtrs
	expectationOrigins RolePrimeDBMockGetRoleWithProductByParticipantIDExpectationOrigins
	results            *RolePrimeDBMockGetRoleWithProductByParticipantIDResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetRoleWithProductByParticipantIDParams contains parameters of the RolePrimeDB.GetRoleWithProductByParticipantID
type RolePrimeDBMockGetRoleWithProductByParticipantIDParams struct {
	participantID int64
}

// RolePrimeDBMockGetRoleWithProductByParticipantIDParamPtrs contains pointers to parameters of the RolePrimeDB.GetRoleWithProductByParticipantID
type RolePrimeDBMockGetRoleWithProductByParticipantIDParamPtrs struct {
	participantID *int64
}

// RolePrimeDBMockGetRoleWithProductByParticipantIDResults contains results of the RolePrimeDB.GetRoleWithProductByParticipantID
type RolePrimeDBMockGetRoleWithProductByParticipantIDResults struct {
	ra1 []roleentity.Role
	err error
}

// RolePrimeDBMockGetRoleWithProductByParticipantIDOrigins contains origins of expectations of the RolePrimeDB.GetRoleWithProductByParticipantID
type RolePrimeDBMockGetRoleWithProductByParticipantIDExpectationOrigins struct {
	origin              string
	originParticipantID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Optional() *mRolePrimeDBMockGetRoleWithProductByParticipantID {
	mmGetRoleWithProductByParticipantID.optional = true
	return mmGetRoleWithProductByParticipantID
}

// Expect sets up expected params for RolePrimeDB.GetRoleWithProductByParticipantID
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Expect(participantID int64) *mRolePrimeDBMockGetRoleWithProductByParticipantID {
	if mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantID != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("RolePrimeDBMock.GetRoleWithProductByParticipantID mock is already set by Set")
	}

	if mmGetRoleWithProductByParticipantID.defaultExpectation == nil {
		mmGetRoleWithProductByParticipantID.defaultExpectation = &RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation{}
	}

	if mmGetRoleWithProductByParticipantID.defaultExpectation.paramPtrs != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("RolePrimeDBMock.GetRoleWithProductByParticipantID mock is already set by ExpectParams functions")
	}

	mmGetRoleWithProductByParticipantID.defaultExpectation.params = &RolePrimeDBMockGetRoleWithProductByParticipantIDParams{participantID}
	mmGetRoleWithProductByParticipantID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetRoleWithProductByParticipantID.expectations {
		if minimock.Equal(e.params, mmGetRoleWithProductByParticipantID.defaultExpectation.params) {
			mmGetRoleWithProductByParticipantID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetRoleWithProductByParticipantID.defaultExpectation.params)
		}
	}

	return mmGetRoleWithProductByParticipantID
}

// ExpectParticipantIDParam1 sets up expected param participantID for RolePrimeDB.GetRoleWithProductByParticipantID
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) ExpectParticipantIDParam1(participantID int64) *mRolePrimeDBMockGetRoleWithProductByParticipantID {
	if mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantID != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("RolePrimeDBMock.GetRoleWithProductByParticipantID mock is already set by Set")
	}

	if mmGetRoleWithProductByParticipantID.defaultExpectation == nil {
		mmGetRoleWithProductByParticipantID.defaultExpectation = &RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation{}
	}

	if mmGetRoleWithProductByParticipantID.defaultExpectation.params != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("RolePrimeDBMock.GetRoleWithProductByParticipantID mock is already set by Expect")
	}

	if mmGetRoleWithProductByParticipantID.defaultExpectation.paramPtrs == nil {
		mmGetRoleWithProductByParticipantID.defaultExpectation.paramPtrs = &RolePrimeDBMockGetRoleWithProductByParticipantIDParamPtrs{}
	}
	mmGetRoleWithProductByParticipantID.defaultExpectation.paramPtrs.participantID = &participantID
	mmGetRoleWithProductByParticipantID.defaultExpectation.expectationOrigins.originParticipantID = minimock.CallerInfo(1)

	return mmGetRoleWithProductByParticipantID
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetRoleWithProductByParticipantID
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Inspect(f func(participantID int64)) *mRolePrimeDBMockGetRoleWithProductByParticipantID {
	if mmGetRoleWithProductByParticipantID.mock.inspectFuncGetRoleWithProductByParticipantID != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetRoleWithProductByParticipantID")
	}

	mmGetRoleWithProductByParticipantID.mock.inspectFuncGetRoleWithProductByParticipantID = f

	return mmGetRoleWithProductByParticipantID
}

// Return sets up results that will be returned by RolePrimeDB.GetRoleWithProductByParticipantID
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Return(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantID != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("RolePrimeDBMock.GetRoleWithProductByParticipantID mock is already set by Set")
	}

	if mmGetRoleWithProductByParticipantID.defaultExpectation == nil {
		mmGetRoleWithProductByParticipantID.defaultExpectation = &RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation{mock: mmGetRoleWithProductByParticipantID.mock}
	}
	mmGetRoleWithProductByParticipantID.defaultExpectation.results = &RolePrimeDBMockGetRoleWithProductByParticipantIDResults{ra1, err}
	mmGetRoleWithProductByParticipantID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRoleWithProductByParticipantID.mock
}

// Set uses given function f to mock the RolePrimeDB.GetRoleWithProductByParticipantID method
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Set(f func(participantID int64) (ra1 []roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetRoleWithProductByParticipantID.defaultExpectation != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetRoleWithProductByParticipantID method")
	}

	if len(mmGetRoleWithProductByParticipantID.expectations) > 0 {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetRoleWithProductByParticipantID method")
	}

	mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantID = f
	mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantIDOrigin = minimock.CallerInfo(1)
	return mmGetRoleWithProductByParticipantID.mock
}

// When sets expectation for the RolePrimeDB.GetRoleWithProductByParticipantID which will trigger the result defined by the following
// Then helper
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) When(participantID int64) *RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation {
	if mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantID != nil {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("RolePrimeDBMock.GetRoleWithProductByParticipantID mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation{
		mock:               mmGetRoleWithProductByParticipantID.mock,
		params:             &RolePrimeDBMockGetRoleWithProductByParticipantIDParams{participantID},
		expectationOrigins: RolePrimeDBMockGetRoleWithProductByParticipantIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetRoleWithProductByParticipantID.expectations = append(mmGetRoleWithProductByParticipantID.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetRoleWithProductByParticipantID return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetRoleWithProductByParticipantIDExpectation) Then(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetRoleWithProductByParticipantIDResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetRoleWithProductByParticipantID should be invoked
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Times(n uint64) *mRolePrimeDBMockGetRoleWithProductByParticipantID {
	if n == 0 {
		mmGetRoleWithProductByParticipantID.mock.t.Fatalf("Times of RolePrimeDBMock.GetRoleWithProductByParticipantID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRoleWithProductByParticipantID.expectedInvocations, n)
	mmGetRoleWithProductByParticipantID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRoleWithProductByParticipantID
}

func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) invocationsDone() bool {
	if len(mmGetRoleWithProductByParticipantID.expectations) == 0 && mmGetRoleWithProductByParticipantID.defaultExpectation == nil && mmGetRoleWithProductByParticipantID.mock.funcGetRoleWithProductByParticipantID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRoleWithProductByParticipantID.mock.afterGetRoleWithProductByParticipantIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRoleWithProductByParticipantID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRoleWithProductByParticipantID implements mm_repository.RolePrimeDB
func (mmGetRoleWithProductByParticipantID *RolePrimeDBMock) GetRoleWithProductByParticipantID(participantID int64) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetRoleWithProductByParticipantID.beforeGetRoleWithProductByParticipantIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRoleWithProductByParticipantID.afterGetRoleWithProductByParticipantIDCounter, 1)

	mmGetRoleWithProductByParticipantID.t.Helper()

	if mmGetRoleWithProductByParticipantID.inspectFuncGetRoleWithProductByParticipantID != nil {
		mmGetRoleWithProductByParticipantID.inspectFuncGetRoleWithProductByParticipantID(participantID)
	}

	mm_params := RolePrimeDBMockGetRoleWithProductByParticipantIDParams{participantID}

	// Record call args
	mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.mutex.Lock()
	mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.callArgs = append(mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.callArgs, &mm_params)
	mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.mutex.Unlock()

	for _, e := range mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetRoleWithProductByParticipantIDParams{participantID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantID != nil && !minimock.Equal(*mm_want_ptrs.participantID, mm_got.participantID) {
				mmGetRoleWithProductByParticipantID.t.Errorf("RolePrimeDBMock.GetRoleWithProductByParticipantID got unexpected parameter participantID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation.expectationOrigins.originParticipantID, *mm_want_ptrs.participantID, mm_got.participantID, minimock.Diff(*mm_want_ptrs.participantID, mm_got.participantID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetRoleWithProductByParticipantID.t.Errorf("RolePrimeDBMock.GetRoleWithProductByParticipantID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetRoleWithProductByParticipantID.GetRoleWithProductByParticipantIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRoleWithProductByParticipantID.t.Fatal("No results are set for the RolePrimeDBMock.GetRoleWithProductByParticipantID")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetRoleWithProductByParticipantID.funcGetRoleWithProductByParticipantID != nil {
		return mmGetRoleWithProductByParticipantID.funcGetRoleWithProductByParticipantID(participantID)
	}
	mmGetRoleWithProductByParticipantID.t.Fatalf("Unexpected call to RolePrimeDBMock.GetRoleWithProductByParticipantID. %v", participantID)
	return
}

// GetRoleWithProductByParticipantIDAfterCounter returns a count of finished RolePrimeDBMock.GetRoleWithProductByParticipantID invocations
func (mmGetRoleWithProductByParticipantID *RolePrimeDBMock) GetRoleWithProductByParticipantIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRoleWithProductByParticipantID.afterGetRoleWithProductByParticipantIDCounter)
}

// GetRoleWithProductByParticipantIDBeforeCounter returns a count of RolePrimeDBMock.GetRoleWithProductByParticipantID invocations
func (mmGetRoleWithProductByParticipantID *RolePrimeDBMock) GetRoleWithProductByParticipantIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRoleWithProductByParticipantID.beforeGetRoleWithProductByParticipantIDCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetRoleWithProductByParticipantID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetRoleWithProductByParticipantID *mRolePrimeDBMockGetRoleWithProductByParticipantID) Calls() []*RolePrimeDBMockGetRoleWithProductByParticipantIDParams {
	mmGetRoleWithProductByParticipantID.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetRoleWithProductByParticipantIDParams, len(mmGetRoleWithProductByParticipantID.callArgs))
	copy(argCopy, mmGetRoleWithProductByParticipantID.callArgs)

	mmGetRoleWithProductByParticipantID.mutex.RUnlock()

	return argCopy
}

// MinimockGetRoleWithProductByParticipantIDDone returns true if the count of the GetRoleWithProductByParticipantID invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetRoleWithProductByParticipantIDDone() bool {
	if m.GetRoleWithProductByParticipantIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRoleWithProductByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRoleWithProductByParticipantIDMock.invocationsDone()
}

// MinimockGetRoleWithProductByParticipantIDInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetRoleWithProductByParticipantIDInspect() {
	for _, e := range m.GetRoleWithProductByParticipantIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleWithProductByParticipantID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetRoleWithProductByParticipantIDCounter := mm_atomic.LoadUint64(&m.afterGetRoleWithProductByParticipantIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRoleWithProductByParticipantIDMock.defaultExpectation != nil && afterGetRoleWithProductByParticipantIDCounter < 1 {
		if m.GetRoleWithProductByParticipantIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleWithProductByParticipantID at\n%s", m.GetRoleWithProductByParticipantIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleWithProductByParticipantID at\n%s with params: %#v", m.GetRoleWithProductByParticipantIDMock.defaultExpectation.expectationOrigins.origin, *m.GetRoleWithProductByParticipantIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRoleWithProductByParticipantID != nil && afterGetRoleWithProductByParticipantIDCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetRoleWithProductByParticipantID at\n%s", m.funcGetRoleWithProductByParticipantIDOrigin)
	}

	if !m.GetRoleWithProductByParticipantIDMock.invocationsDone() && afterGetRoleWithProductByParticipantIDCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetRoleWithProductByParticipantID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRoleWithProductByParticipantIDMock.expectedInvocations), m.GetRoleWithProductByParticipantIDMock.expectedInvocationsOrigin, afterGetRoleWithProductByParticipantIDCounter)
	}
}

type mRolePrimeDBMockGetRolesWithProductByParticipantIDs struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation
	expectations       []*RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation

	callArgs []*RolePrimeDBMockGetRolesWithProductByParticipantIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation specifies expectation struct of the RolePrimeDB.GetRolesWithProductByParticipantIDs
type RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetRolesWithProductByParticipantIDsParams
	paramPtrs          *RolePrimeDBMockGetRolesWithProductByParticipantIDsParamPtrs
	expectationOrigins RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectationOrigins
	results            *RolePrimeDBMockGetRolesWithProductByParticipantIDsResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetRolesWithProductByParticipantIDsParams contains parameters of the RolePrimeDB.GetRolesWithProductByParticipantIDs
type RolePrimeDBMockGetRolesWithProductByParticipantIDsParams struct {
	participantIDs []int64
}

// RolePrimeDBMockGetRolesWithProductByParticipantIDsParamPtrs contains pointers to parameters of the RolePrimeDB.GetRolesWithProductByParticipantIDs
type RolePrimeDBMockGetRolesWithProductByParticipantIDsParamPtrs struct {
	participantIDs *[]int64
}

// RolePrimeDBMockGetRolesWithProductByParticipantIDsResults contains results of the RolePrimeDB.GetRolesWithProductByParticipantIDs
type RolePrimeDBMockGetRolesWithProductByParticipantIDsResults struct {
	ra1 []roleentity.Role
	err error
}

// RolePrimeDBMockGetRolesWithProductByParticipantIDsOrigins contains origins of expectations of the RolePrimeDB.GetRolesWithProductByParticipantIDs
type RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectationOrigins struct {
	origin               string
	originParticipantIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Optional() *mRolePrimeDBMockGetRolesWithProductByParticipantIDs {
	mmGetRolesWithProductByParticipantIDs.optional = true
	return mmGetRolesWithProductByParticipantIDs
}

// Expect sets up expected params for RolePrimeDB.GetRolesWithProductByParticipantIDs
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Expect(participantIDs []int64) *mRolePrimeDBMockGetRolesWithProductByParticipantIDs {
	if mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDs != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock is already set by Set")
	}

	if mmGetRolesWithProductByParticipantIDs.defaultExpectation == nil {
		mmGetRolesWithProductByParticipantIDs.defaultExpectation = &RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation{}
	}

	if mmGetRolesWithProductByParticipantIDs.defaultExpectation.paramPtrs != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock is already set by ExpectParams functions")
	}

	mmGetRolesWithProductByParticipantIDs.defaultExpectation.params = &RolePrimeDBMockGetRolesWithProductByParticipantIDsParams{participantIDs}
	mmGetRolesWithProductByParticipantIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetRolesWithProductByParticipantIDs.expectations {
		if minimock.Equal(e.params, mmGetRolesWithProductByParticipantIDs.defaultExpectation.params) {
			mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetRolesWithProductByParticipantIDs.defaultExpectation.params)
		}
	}

	return mmGetRolesWithProductByParticipantIDs
}

// ExpectParticipantIDsParam1 sets up expected param participantIDs for RolePrimeDB.GetRolesWithProductByParticipantIDs
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) ExpectParticipantIDsParam1(participantIDs []int64) *mRolePrimeDBMockGetRolesWithProductByParticipantIDs {
	if mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDs != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock is already set by Set")
	}

	if mmGetRolesWithProductByParticipantIDs.defaultExpectation == nil {
		mmGetRolesWithProductByParticipantIDs.defaultExpectation = &RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation{}
	}

	if mmGetRolesWithProductByParticipantIDs.defaultExpectation.params != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock is already set by Expect")
	}

	if mmGetRolesWithProductByParticipantIDs.defaultExpectation.paramPtrs == nil {
		mmGetRolesWithProductByParticipantIDs.defaultExpectation.paramPtrs = &RolePrimeDBMockGetRolesWithProductByParticipantIDsParamPtrs{}
	}
	mmGetRolesWithProductByParticipantIDs.defaultExpectation.paramPtrs.participantIDs = &participantIDs
	mmGetRolesWithProductByParticipantIDs.defaultExpectation.expectationOrigins.originParticipantIDs = minimock.CallerInfo(1)

	return mmGetRolesWithProductByParticipantIDs
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetRolesWithProductByParticipantIDs
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Inspect(f func(participantIDs []int64)) *mRolePrimeDBMockGetRolesWithProductByParticipantIDs {
	if mmGetRolesWithProductByParticipantIDs.mock.inspectFuncGetRolesWithProductByParticipantIDs != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetRolesWithProductByParticipantIDs")
	}

	mmGetRolesWithProductByParticipantIDs.mock.inspectFuncGetRolesWithProductByParticipantIDs = f

	return mmGetRolesWithProductByParticipantIDs
}

// Return sets up results that will be returned by RolePrimeDB.GetRolesWithProductByParticipantIDs
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Return(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	if mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDs != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock is already set by Set")
	}

	if mmGetRolesWithProductByParticipantIDs.defaultExpectation == nil {
		mmGetRolesWithProductByParticipantIDs.defaultExpectation = &RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation{mock: mmGetRolesWithProductByParticipantIDs.mock}
	}
	mmGetRolesWithProductByParticipantIDs.defaultExpectation.results = &RolePrimeDBMockGetRolesWithProductByParticipantIDsResults{ra1, err}
	mmGetRolesWithProductByParticipantIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRolesWithProductByParticipantIDs.mock
}

// Set uses given function f to mock the RolePrimeDB.GetRolesWithProductByParticipantIDs method
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Set(f func(participantIDs []int64) (ra1 []roleentity.Role, err error)) *RolePrimeDBMock {
	if mmGetRolesWithProductByParticipantIDs.defaultExpectation != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetRolesWithProductByParticipantIDs method")
	}

	if len(mmGetRolesWithProductByParticipantIDs.expectations) > 0 {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetRolesWithProductByParticipantIDs method")
	}

	mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDs = f
	mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDsOrigin = minimock.CallerInfo(1)
	return mmGetRolesWithProductByParticipantIDs.mock
}

// When sets expectation for the RolePrimeDB.GetRolesWithProductByParticipantIDs which will trigger the result defined by the following
// Then helper
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) When(participantIDs []int64) *RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation {
	if mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDs != nil {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation{
		mock:               mmGetRolesWithProductByParticipantIDs.mock,
		params:             &RolePrimeDBMockGetRolesWithProductByParticipantIDsParams{participantIDs},
		expectationOrigins: RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetRolesWithProductByParticipantIDs.expectations = append(mmGetRolesWithProductByParticipantIDs.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetRolesWithProductByParticipantIDs return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetRolesWithProductByParticipantIDsExpectation) Then(ra1 []roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetRolesWithProductByParticipantIDsResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetRolesWithProductByParticipantIDs should be invoked
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Times(n uint64) *mRolePrimeDBMockGetRolesWithProductByParticipantIDs {
	if n == 0 {
		mmGetRolesWithProductByParticipantIDs.mock.t.Fatalf("Times of RolePrimeDBMock.GetRolesWithProductByParticipantIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRolesWithProductByParticipantIDs.expectedInvocations, n)
	mmGetRolesWithProductByParticipantIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRolesWithProductByParticipantIDs
}

func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) invocationsDone() bool {
	if len(mmGetRolesWithProductByParticipantIDs.expectations) == 0 && mmGetRolesWithProductByParticipantIDs.defaultExpectation == nil && mmGetRolesWithProductByParticipantIDs.mock.funcGetRolesWithProductByParticipantIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRolesWithProductByParticipantIDs.mock.afterGetRolesWithProductByParticipantIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRolesWithProductByParticipantIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRolesWithProductByParticipantIDs implements mm_repository.RolePrimeDB
func (mmGetRolesWithProductByParticipantIDs *RolePrimeDBMock) GetRolesWithProductByParticipantIDs(participantIDs []int64) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetRolesWithProductByParticipantIDs.beforeGetRolesWithProductByParticipantIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRolesWithProductByParticipantIDs.afterGetRolesWithProductByParticipantIDsCounter, 1)

	mmGetRolesWithProductByParticipantIDs.t.Helper()

	if mmGetRolesWithProductByParticipantIDs.inspectFuncGetRolesWithProductByParticipantIDs != nil {
		mmGetRolesWithProductByParticipantIDs.inspectFuncGetRolesWithProductByParticipantIDs(participantIDs)
	}

	mm_params := RolePrimeDBMockGetRolesWithProductByParticipantIDsParams{participantIDs}

	// Record call args
	mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.mutex.Lock()
	mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.callArgs = append(mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.callArgs, &mm_params)
	mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.mutex.Unlock()

	for _, e := range mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetRolesWithProductByParticipantIDsParams{participantIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.participantIDs != nil && !minimock.Equal(*mm_want_ptrs.participantIDs, mm_got.participantIDs) {
				mmGetRolesWithProductByParticipantIDs.t.Errorf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs got unexpected parameter participantIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation.expectationOrigins.originParticipantIDs, *mm_want_ptrs.participantIDs, mm_got.participantIDs, minimock.Diff(*mm_want_ptrs.participantIDs, mm_got.participantIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetRolesWithProductByParticipantIDs.t.Errorf("RolePrimeDBMock.GetRolesWithProductByParticipantIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetRolesWithProductByParticipantIDs.GetRolesWithProductByParticipantIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRolesWithProductByParticipantIDs.t.Fatal("No results are set for the RolePrimeDBMock.GetRolesWithProductByParticipantIDs")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetRolesWithProductByParticipantIDs.funcGetRolesWithProductByParticipantIDs != nil {
		return mmGetRolesWithProductByParticipantIDs.funcGetRolesWithProductByParticipantIDs(participantIDs)
	}
	mmGetRolesWithProductByParticipantIDs.t.Fatalf("Unexpected call to RolePrimeDBMock.GetRolesWithProductByParticipantIDs. %v", participantIDs)
	return
}

// GetRolesWithProductByParticipantIDsAfterCounter returns a count of finished RolePrimeDBMock.GetRolesWithProductByParticipantIDs invocations
func (mmGetRolesWithProductByParticipantIDs *RolePrimeDBMock) GetRolesWithProductByParticipantIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRolesWithProductByParticipantIDs.afterGetRolesWithProductByParticipantIDsCounter)
}

// GetRolesWithProductByParticipantIDsBeforeCounter returns a count of RolePrimeDBMock.GetRolesWithProductByParticipantIDs invocations
func (mmGetRolesWithProductByParticipantIDs *RolePrimeDBMock) GetRolesWithProductByParticipantIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRolesWithProductByParticipantIDs.beforeGetRolesWithProductByParticipantIDsCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetRolesWithProductByParticipantIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetRolesWithProductByParticipantIDs *mRolePrimeDBMockGetRolesWithProductByParticipantIDs) Calls() []*RolePrimeDBMockGetRolesWithProductByParticipantIDsParams {
	mmGetRolesWithProductByParticipantIDs.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetRolesWithProductByParticipantIDsParams, len(mmGetRolesWithProductByParticipantIDs.callArgs))
	copy(argCopy, mmGetRolesWithProductByParticipantIDs.callArgs)

	mmGetRolesWithProductByParticipantIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetRolesWithProductByParticipantIDsDone returns true if the count of the GetRolesWithProductByParticipantIDs invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetRolesWithProductByParticipantIDsDone() bool {
	if m.GetRolesWithProductByParticipantIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRolesWithProductByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRolesWithProductByParticipantIDsMock.invocationsDone()
}

// MinimockGetRolesWithProductByParticipantIDsInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetRolesWithProductByParticipantIDsInspect() {
	for _, e := range m.GetRolesWithProductByParticipantIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithProductByParticipantIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetRolesWithProductByParticipantIDsCounter := mm_atomic.LoadUint64(&m.afterGetRolesWithProductByParticipantIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRolesWithProductByParticipantIDsMock.defaultExpectation != nil && afterGetRolesWithProductByParticipantIDsCounter < 1 {
		if m.GetRolesWithProductByParticipantIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithProductByParticipantIDs at\n%s", m.GetRolesWithProductByParticipantIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithProductByParticipantIDs at\n%s with params: %#v", m.GetRolesWithProductByParticipantIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetRolesWithProductByParticipantIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRolesWithProductByParticipantIDs != nil && afterGetRolesWithProductByParticipantIDsCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithProductByParticipantIDs at\n%s", m.funcGetRolesWithProductByParticipantIDsOrigin)
	}

	if !m.GetRolesWithProductByParticipantIDsMock.invocationsDone() && afterGetRolesWithProductByParticipantIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetRolesWithProductByParticipantIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRolesWithProductByParticipantIDsMock.expectedInvocations), m.GetRolesWithProductByParticipantIDsMock.expectedInvocationsOrigin, afterGetRolesWithProductByParticipantIDsCounter)
	}
}

type mRolePrimeDBMockGetRolesWithStats struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetRolesWithStatsExpectation
	expectations       []*RolePrimeDBMockGetRolesWithStatsExpectation

	callArgs []*RolePrimeDBMockGetRolesWithStatsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetRolesWithStatsExpectation specifies expectation struct of the RolePrimeDB.GetRolesWithStats
type RolePrimeDBMockGetRolesWithStatsExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetRolesWithStatsParams
	paramPtrs          *RolePrimeDBMockGetRolesWithStatsParamPtrs
	expectationOrigins RolePrimeDBMockGetRolesWithStatsExpectationOrigins
	results            *RolePrimeDBMockGetRolesWithStatsResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetRolesWithStatsParams contains parameters of the RolePrimeDB.GetRolesWithStats
type RolePrimeDBMockGetRolesWithStatsParams struct {
	productID int64
}

// RolePrimeDBMockGetRolesWithStatsParamPtrs contains pointers to parameters of the RolePrimeDB.GetRolesWithStats
type RolePrimeDBMockGetRolesWithStatsParamPtrs struct {
	productID *int64
}

// RolePrimeDBMockGetRolesWithStatsResults contains results of the RolePrimeDB.GetRolesWithStats
type RolePrimeDBMockGetRolesWithStatsResults struct {
	ra1 []roleentity.RoleWithStats
	err error
}

// RolePrimeDBMockGetRolesWithStatsOrigins contains origins of expectations of the RolePrimeDB.GetRolesWithStats
type RolePrimeDBMockGetRolesWithStatsExpectationOrigins struct {
	origin          string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Optional() *mRolePrimeDBMockGetRolesWithStats {
	mmGetRolesWithStats.optional = true
	return mmGetRolesWithStats
}

// Expect sets up expected params for RolePrimeDB.GetRolesWithStats
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Expect(productID int64) *mRolePrimeDBMockGetRolesWithStats {
	if mmGetRolesWithStats.mock.funcGetRolesWithStats != nil {
		mmGetRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithStats mock is already set by Set")
	}

	if mmGetRolesWithStats.defaultExpectation == nil {
		mmGetRolesWithStats.defaultExpectation = &RolePrimeDBMockGetRolesWithStatsExpectation{}
	}

	if mmGetRolesWithStats.defaultExpectation.paramPtrs != nil {
		mmGetRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithStats mock is already set by ExpectParams functions")
	}

	mmGetRolesWithStats.defaultExpectation.params = &RolePrimeDBMockGetRolesWithStatsParams{productID}
	mmGetRolesWithStats.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetRolesWithStats.expectations {
		if minimock.Equal(e.params, mmGetRolesWithStats.defaultExpectation.params) {
			mmGetRolesWithStats.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetRolesWithStats.defaultExpectation.params)
		}
	}

	return mmGetRolesWithStats
}

// ExpectProductIDParam1 sets up expected param productID for RolePrimeDB.GetRolesWithStats
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) ExpectProductIDParam1(productID int64) *mRolePrimeDBMockGetRolesWithStats {
	if mmGetRolesWithStats.mock.funcGetRolesWithStats != nil {
		mmGetRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithStats mock is already set by Set")
	}

	if mmGetRolesWithStats.defaultExpectation == nil {
		mmGetRolesWithStats.defaultExpectation = &RolePrimeDBMockGetRolesWithStatsExpectation{}
	}

	if mmGetRolesWithStats.defaultExpectation.params != nil {
		mmGetRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithStats mock is already set by Expect")
	}

	if mmGetRolesWithStats.defaultExpectation.paramPtrs == nil {
		mmGetRolesWithStats.defaultExpectation.paramPtrs = &RolePrimeDBMockGetRolesWithStatsParamPtrs{}
	}
	mmGetRolesWithStats.defaultExpectation.paramPtrs.productID = &productID
	mmGetRolesWithStats.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetRolesWithStats
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetRolesWithStats
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Inspect(f func(productID int64)) *mRolePrimeDBMockGetRolesWithStats {
	if mmGetRolesWithStats.mock.inspectFuncGetRolesWithStats != nil {
		mmGetRolesWithStats.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetRolesWithStats")
	}

	mmGetRolesWithStats.mock.inspectFuncGetRolesWithStats = f

	return mmGetRolesWithStats
}

// Return sets up results that will be returned by RolePrimeDB.GetRolesWithStats
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Return(ra1 []roleentity.RoleWithStats, err error) *RolePrimeDBMock {
	if mmGetRolesWithStats.mock.funcGetRolesWithStats != nil {
		mmGetRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithStats mock is already set by Set")
	}

	if mmGetRolesWithStats.defaultExpectation == nil {
		mmGetRolesWithStats.defaultExpectation = &RolePrimeDBMockGetRolesWithStatsExpectation{mock: mmGetRolesWithStats.mock}
	}
	mmGetRolesWithStats.defaultExpectation.results = &RolePrimeDBMockGetRolesWithStatsResults{ra1, err}
	mmGetRolesWithStats.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRolesWithStats.mock
}

// Set uses given function f to mock the RolePrimeDB.GetRolesWithStats method
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Set(f func(productID int64) (ra1 []roleentity.RoleWithStats, err error)) *RolePrimeDBMock {
	if mmGetRolesWithStats.defaultExpectation != nil {
		mmGetRolesWithStats.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetRolesWithStats method")
	}

	if len(mmGetRolesWithStats.expectations) > 0 {
		mmGetRolesWithStats.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetRolesWithStats method")
	}

	mmGetRolesWithStats.mock.funcGetRolesWithStats = f
	mmGetRolesWithStats.mock.funcGetRolesWithStatsOrigin = minimock.CallerInfo(1)
	return mmGetRolesWithStats.mock
}

// When sets expectation for the RolePrimeDB.GetRolesWithStats which will trigger the result defined by the following
// Then helper
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) When(productID int64) *RolePrimeDBMockGetRolesWithStatsExpectation {
	if mmGetRolesWithStats.mock.funcGetRolesWithStats != nil {
		mmGetRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetRolesWithStats mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetRolesWithStatsExpectation{
		mock:               mmGetRolesWithStats.mock,
		params:             &RolePrimeDBMockGetRolesWithStatsParams{productID},
		expectationOrigins: RolePrimeDBMockGetRolesWithStatsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetRolesWithStats.expectations = append(mmGetRolesWithStats.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetRolesWithStats return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetRolesWithStatsExpectation) Then(ra1 []roleentity.RoleWithStats, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetRolesWithStatsResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetRolesWithStats should be invoked
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Times(n uint64) *mRolePrimeDBMockGetRolesWithStats {
	if n == 0 {
		mmGetRolesWithStats.mock.t.Fatalf("Times of RolePrimeDBMock.GetRolesWithStats mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRolesWithStats.expectedInvocations, n)
	mmGetRolesWithStats.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRolesWithStats
}

func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) invocationsDone() bool {
	if len(mmGetRolesWithStats.expectations) == 0 && mmGetRolesWithStats.defaultExpectation == nil && mmGetRolesWithStats.mock.funcGetRolesWithStats == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRolesWithStats.mock.afterGetRolesWithStatsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRolesWithStats.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRolesWithStats implements mm_repository.RolePrimeDB
func (mmGetRolesWithStats *RolePrimeDBMock) GetRolesWithStats(productID int64) (ra1 []roleentity.RoleWithStats, err error) {
	mm_atomic.AddUint64(&mmGetRolesWithStats.beforeGetRolesWithStatsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRolesWithStats.afterGetRolesWithStatsCounter, 1)

	mmGetRolesWithStats.t.Helper()

	if mmGetRolesWithStats.inspectFuncGetRolesWithStats != nil {
		mmGetRolesWithStats.inspectFuncGetRolesWithStats(productID)
	}

	mm_params := RolePrimeDBMockGetRolesWithStatsParams{productID}

	// Record call args
	mmGetRolesWithStats.GetRolesWithStatsMock.mutex.Lock()
	mmGetRolesWithStats.GetRolesWithStatsMock.callArgs = append(mmGetRolesWithStats.GetRolesWithStatsMock.callArgs, &mm_params)
	mmGetRolesWithStats.GetRolesWithStatsMock.mutex.Unlock()

	for _, e := range mmGetRolesWithStats.GetRolesWithStatsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation.params
		mm_want_ptrs := mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetRolesWithStatsParams{productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetRolesWithStats.t.Errorf("RolePrimeDBMock.GetRolesWithStats got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetRolesWithStats.t.Errorf("RolePrimeDBMock.GetRolesWithStats got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetRolesWithStats.GetRolesWithStatsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRolesWithStats.t.Fatal("No results are set for the RolePrimeDBMock.GetRolesWithStats")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetRolesWithStats.funcGetRolesWithStats != nil {
		return mmGetRolesWithStats.funcGetRolesWithStats(productID)
	}
	mmGetRolesWithStats.t.Fatalf("Unexpected call to RolePrimeDBMock.GetRolesWithStats. %v", productID)
	return
}

// GetRolesWithStatsAfterCounter returns a count of finished RolePrimeDBMock.GetRolesWithStats invocations
func (mmGetRolesWithStats *RolePrimeDBMock) GetRolesWithStatsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRolesWithStats.afterGetRolesWithStatsCounter)
}

// GetRolesWithStatsBeforeCounter returns a count of RolePrimeDBMock.GetRolesWithStats invocations
func (mmGetRolesWithStats *RolePrimeDBMock) GetRolesWithStatsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRolesWithStats.beforeGetRolesWithStatsCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetRolesWithStats.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetRolesWithStats *mRolePrimeDBMockGetRolesWithStats) Calls() []*RolePrimeDBMockGetRolesWithStatsParams {
	mmGetRolesWithStats.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetRolesWithStatsParams, len(mmGetRolesWithStats.callArgs))
	copy(argCopy, mmGetRolesWithStats.callArgs)

	mmGetRolesWithStats.mutex.RUnlock()

	return argCopy
}

// MinimockGetRolesWithStatsDone returns true if the count of the GetRolesWithStats invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetRolesWithStatsDone() bool {
	if m.GetRolesWithStatsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRolesWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRolesWithStatsMock.invocationsDone()
}

// MinimockGetRolesWithStatsInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetRolesWithStatsInspect() {
	for _, e := range m.GetRolesWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithStats at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetRolesWithStatsCounter := mm_atomic.LoadUint64(&m.afterGetRolesWithStatsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRolesWithStatsMock.defaultExpectation != nil && afterGetRolesWithStatsCounter < 1 {
		if m.GetRolesWithStatsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithStats at\n%s", m.GetRolesWithStatsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithStats at\n%s with params: %#v", m.GetRolesWithStatsMock.defaultExpectation.expectationOrigins.origin, *m.GetRolesWithStatsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRolesWithStats != nil && afterGetRolesWithStatsCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetRolesWithStats at\n%s", m.funcGetRolesWithStatsOrigin)
	}

	if !m.GetRolesWithStatsMock.invocationsDone() && afterGetRolesWithStatsCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetRolesWithStats at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRolesWithStatsMock.expectedInvocations), m.GetRolesWithStatsMock.expectedInvocationsOrigin, afterGetRolesWithStatsCounter)
	}
}

type mRolePrimeDBMockGetSystemRolesWithStats struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockGetSystemRolesWithStatsExpectation
	expectations       []*RolePrimeDBMockGetSystemRolesWithStatsExpectation

	callArgs []*RolePrimeDBMockGetSystemRolesWithStatsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockGetSystemRolesWithStatsExpectation specifies expectation struct of the RolePrimeDB.GetSystemRolesWithStats
type RolePrimeDBMockGetSystemRolesWithStatsExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockGetSystemRolesWithStatsParams
	paramPtrs          *RolePrimeDBMockGetSystemRolesWithStatsParamPtrs
	expectationOrigins RolePrimeDBMockGetSystemRolesWithStatsExpectationOrigins
	results            *RolePrimeDBMockGetSystemRolesWithStatsResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockGetSystemRolesWithStatsParams contains parameters of the RolePrimeDB.GetSystemRolesWithStats
type RolePrimeDBMockGetSystemRolesWithStatsParams struct {
	ctx context.Context
}

// RolePrimeDBMockGetSystemRolesWithStatsParamPtrs contains pointers to parameters of the RolePrimeDB.GetSystemRolesWithStats
type RolePrimeDBMockGetSystemRolesWithStatsParamPtrs struct {
	ctx *context.Context
}

// RolePrimeDBMockGetSystemRolesWithStatsResults contains results of the RolePrimeDB.GetSystemRolesWithStats
type RolePrimeDBMockGetSystemRolesWithStatsResults struct {
	ra1 []roleentity.RoleWithStats
	err error
}

// RolePrimeDBMockGetSystemRolesWithStatsOrigins contains origins of expectations of the RolePrimeDB.GetSystemRolesWithStats
type RolePrimeDBMockGetSystemRolesWithStatsExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Optional() *mRolePrimeDBMockGetSystemRolesWithStats {
	mmGetSystemRolesWithStats.optional = true
	return mmGetSystemRolesWithStats
}

// Expect sets up expected params for RolePrimeDB.GetSystemRolesWithStats
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Expect(ctx context.Context) *mRolePrimeDBMockGetSystemRolesWithStats {
	if mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStats != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetSystemRolesWithStats mock is already set by Set")
	}

	if mmGetSystemRolesWithStats.defaultExpectation == nil {
		mmGetSystemRolesWithStats.defaultExpectation = &RolePrimeDBMockGetSystemRolesWithStatsExpectation{}
	}

	if mmGetSystemRolesWithStats.defaultExpectation.paramPtrs != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetSystemRolesWithStats mock is already set by ExpectParams functions")
	}

	mmGetSystemRolesWithStats.defaultExpectation.params = &RolePrimeDBMockGetSystemRolesWithStatsParams{ctx}
	mmGetSystemRolesWithStats.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetSystemRolesWithStats.expectations {
		if minimock.Equal(e.params, mmGetSystemRolesWithStats.defaultExpectation.params) {
			mmGetSystemRolesWithStats.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetSystemRolesWithStats.defaultExpectation.params)
		}
	}

	return mmGetSystemRolesWithStats
}

// ExpectCtxParam1 sets up expected param ctx for RolePrimeDB.GetSystemRolesWithStats
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) ExpectCtxParam1(ctx context.Context) *mRolePrimeDBMockGetSystemRolesWithStats {
	if mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStats != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetSystemRolesWithStats mock is already set by Set")
	}

	if mmGetSystemRolesWithStats.defaultExpectation == nil {
		mmGetSystemRolesWithStats.defaultExpectation = &RolePrimeDBMockGetSystemRolesWithStatsExpectation{}
	}

	if mmGetSystemRolesWithStats.defaultExpectation.params != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetSystemRolesWithStats mock is already set by Expect")
	}

	if mmGetSystemRolesWithStats.defaultExpectation.paramPtrs == nil {
		mmGetSystemRolesWithStats.defaultExpectation.paramPtrs = &RolePrimeDBMockGetSystemRolesWithStatsParamPtrs{}
	}
	mmGetSystemRolesWithStats.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetSystemRolesWithStats.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetSystemRolesWithStats
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.GetSystemRolesWithStats
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Inspect(f func(ctx context.Context)) *mRolePrimeDBMockGetSystemRolesWithStats {
	if mmGetSystemRolesWithStats.mock.inspectFuncGetSystemRolesWithStats != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.GetSystemRolesWithStats")
	}

	mmGetSystemRolesWithStats.mock.inspectFuncGetSystemRolesWithStats = f

	return mmGetSystemRolesWithStats
}

// Return sets up results that will be returned by RolePrimeDB.GetSystemRolesWithStats
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Return(ra1 []roleentity.RoleWithStats, err error) *RolePrimeDBMock {
	if mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStats != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetSystemRolesWithStats mock is already set by Set")
	}

	if mmGetSystemRolesWithStats.defaultExpectation == nil {
		mmGetSystemRolesWithStats.defaultExpectation = &RolePrimeDBMockGetSystemRolesWithStatsExpectation{mock: mmGetSystemRolesWithStats.mock}
	}
	mmGetSystemRolesWithStats.defaultExpectation.results = &RolePrimeDBMockGetSystemRolesWithStatsResults{ra1, err}
	mmGetSystemRolesWithStats.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetSystemRolesWithStats.mock
}

// Set uses given function f to mock the RolePrimeDB.GetSystemRolesWithStats method
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Set(f func(ctx context.Context) (ra1 []roleentity.RoleWithStats, err error)) *RolePrimeDBMock {
	if mmGetSystemRolesWithStats.defaultExpectation != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.GetSystemRolesWithStats method")
	}

	if len(mmGetSystemRolesWithStats.expectations) > 0 {
		mmGetSystemRolesWithStats.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.GetSystemRolesWithStats method")
	}

	mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStats = f
	mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStatsOrigin = minimock.CallerInfo(1)
	return mmGetSystemRolesWithStats.mock
}

// When sets expectation for the RolePrimeDB.GetSystemRolesWithStats which will trigger the result defined by the following
// Then helper
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) When(ctx context.Context) *RolePrimeDBMockGetSystemRolesWithStatsExpectation {
	if mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStats != nil {
		mmGetSystemRolesWithStats.mock.t.Fatalf("RolePrimeDBMock.GetSystemRolesWithStats mock is already set by Set")
	}

	expectation := &RolePrimeDBMockGetSystemRolesWithStatsExpectation{
		mock:               mmGetSystemRolesWithStats.mock,
		params:             &RolePrimeDBMockGetSystemRolesWithStatsParams{ctx},
		expectationOrigins: RolePrimeDBMockGetSystemRolesWithStatsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetSystemRolesWithStats.expectations = append(mmGetSystemRolesWithStats.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.GetSystemRolesWithStats return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockGetSystemRolesWithStatsExpectation) Then(ra1 []roleentity.RoleWithStats, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockGetSystemRolesWithStatsResults{ra1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.GetSystemRolesWithStats should be invoked
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Times(n uint64) *mRolePrimeDBMockGetSystemRolesWithStats {
	if n == 0 {
		mmGetSystemRolesWithStats.mock.t.Fatalf("Times of RolePrimeDBMock.GetSystemRolesWithStats mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetSystemRolesWithStats.expectedInvocations, n)
	mmGetSystemRolesWithStats.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetSystemRolesWithStats
}

func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) invocationsDone() bool {
	if len(mmGetSystemRolesWithStats.expectations) == 0 && mmGetSystemRolesWithStats.defaultExpectation == nil && mmGetSystemRolesWithStats.mock.funcGetSystemRolesWithStats == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetSystemRolesWithStats.mock.afterGetSystemRolesWithStatsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetSystemRolesWithStats.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetSystemRolesWithStats implements mm_repository.RolePrimeDB
func (mmGetSystemRolesWithStats *RolePrimeDBMock) GetSystemRolesWithStats(ctx context.Context) (ra1 []roleentity.RoleWithStats, err error) {
	mm_atomic.AddUint64(&mmGetSystemRolesWithStats.beforeGetSystemRolesWithStatsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetSystemRolesWithStats.afterGetSystemRolesWithStatsCounter, 1)

	mmGetSystemRolesWithStats.t.Helper()

	if mmGetSystemRolesWithStats.inspectFuncGetSystemRolesWithStats != nil {
		mmGetSystemRolesWithStats.inspectFuncGetSystemRolesWithStats(ctx)
	}

	mm_params := RolePrimeDBMockGetSystemRolesWithStatsParams{ctx}

	// Record call args
	mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.mutex.Lock()
	mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.callArgs = append(mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.callArgs, &mm_params)
	mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.mutex.Unlock()

	for _, e := range mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation.params
		mm_want_ptrs := mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockGetSystemRolesWithStatsParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetSystemRolesWithStats.t.Errorf("RolePrimeDBMock.GetSystemRolesWithStats got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetSystemRolesWithStats.t.Errorf("RolePrimeDBMock.GetSystemRolesWithStats got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetSystemRolesWithStats.GetSystemRolesWithStatsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetSystemRolesWithStats.t.Fatal("No results are set for the RolePrimeDBMock.GetSystemRolesWithStats")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetSystemRolesWithStats.funcGetSystemRolesWithStats != nil {
		return mmGetSystemRolesWithStats.funcGetSystemRolesWithStats(ctx)
	}
	mmGetSystemRolesWithStats.t.Fatalf("Unexpected call to RolePrimeDBMock.GetSystemRolesWithStats. %v", ctx)
	return
}

// GetSystemRolesWithStatsAfterCounter returns a count of finished RolePrimeDBMock.GetSystemRolesWithStats invocations
func (mmGetSystemRolesWithStats *RolePrimeDBMock) GetSystemRolesWithStatsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemRolesWithStats.afterGetSystemRolesWithStatsCounter)
}

// GetSystemRolesWithStatsBeforeCounter returns a count of RolePrimeDBMock.GetSystemRolesWithStats invocations
func (mmGetSystemRolesWithStats *RolePrimeDBMock) GetSystemRolesWithStatsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetSystemRolesWithStats.beforeGetSystemRolesWithStatsCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.GetSystemRolesWithStats.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetSystemRolesWithStats *mRolePrimeDBMockGetSystemRolesWithStats) Calls() []*RolePrimeDBMockGetSystemRolesWithStatsParams {
	mmGetSystemRolesWithStats.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockGetSystemRolesWithStatsParams, len(mmGetSystemRolesWithStats.callArgs))
	copy(argCopy, mmGetSystemRolesWithStats.callArgs)

	mmGetSystemRolesWithStats.mutex.RUnlock()

	return argCopy
}

// MinimockGetSystemRolesWithStatsDone returns true if the count of the GetSystemRolesWithStats invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockGetSystemRolesWithStatsDone() bool {
	if m.GetSystemRolesWithStatsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetSystemRolesWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetSystemRolesWithStatsMock.invocationsDone()
}

// MinimockGetSystemRolesWithStatsInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockGetSystemRolesWithStatsInspect() {
	for _, e := range m.GetSystemRolesWithStatsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetSystemRolesWithStats at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetSystemRolesWithStatsCounter := mm_atomic.LoadUint64(&m.afterGetSystemRolesWithStatsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetSystemRolesWithStatsMock.defaultExpectation != nil && afterGetSystemRolesWithStatsCounter < 1 {
		if m.GetSystemRolesWithStatsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetSystemRolesWithStats at\n%s", m.GetSystemRolesWithStatsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.GetSystemRolesWithStats at\n%s with params: %#v", m.GetSystemRolesWithStatsMock.defaultExpectation.expectationOrigins.origin, *m.GetSystemRolesWithStatsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetSystemRolesWithStats != nil && afterGetSystemRolesWithStatsCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.GetSystemRolesWithStats at\n%s", m.funcGetSystemRolesWithStatsOrigin)
	}

	if !m.GetSystemRolesWithStatsMock.invocationsDone() && afterGetSystemRolesWithStatsCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.GetSystemRolesWithStats at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetSystemRolesWithStatsMock.expectedInvocations), m.GetSystemRolesWithStatsMock.expectedInvocationsOrigin, afterGetSystemRolesWithStatsCounter)
	}
}

type mRolePrimeDBMockUpdate struct {
	optional           bool
	mock               *RolePrimeDBMock
	defaultExpectation *RolePrimeDBMockUpdateExpectation
	expectations       []*RolePrimeDBMockUpdateExpectation

	callArgs []*RolePrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RolePrimeDBMockUpdateExpectation specifies expectation struct of the RolePrimeDB.Update
type RolePrimeDBMockUpdateExpectation struct {
	mock               *RolePrimeDBMock
	params             *RolePrimeDBMockUpdateParams
	paramPtrs          *RolePrimeDBMockUpdateParamPtrs
	expectationOrigins RolePrimeDBMockUpdateExpectationOrigins
	results            *RolePrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// RolePrimeDBMockUpdateParams contains parameters of the RolePrimeDB.Update
type RolePrimeDBMockUpdateParams struct {
	ctx  context.Context
	role roleentity.RoleUpdateData
}

// RolePrimeDBMockUpdateParamPtrs contains pointers to parameters of the RolePrimeDB.Update
type RolePrimeDBMockUpdateParamPtrs struct {
	ctx  *context.Context
	role *roleentity.RoleUpdateData
}

// RolePrimeDBMockUpdateResults contains results of the RolePrimeDB.Update
type RolePrimeDBMockUpdateResults struct {
	r1  roleentity.Role
	err error
}

// RolePrimeDBMockUpdateOrigins contains origins of expectations of the RolePrimeDB.Update
type RolePrimeDBMockUpdateExpectationOrigins struct {
	origin     string
	originCtx  string
	originRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mRolePrimeDBMockUpdate) Optional() *mRolePrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for RolePrimeDB.Update
func (mmUpdate *mRolePrimeDBMockUpdate) Expect(ctx context.Context, role roleentity.RoleUpdateData) *mRolePrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &RolePrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &RolePrimeDBMockUpdateParams{ctx, role}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectCtxParam1 sets up expected param ctx for RolePrimeDB.Update
func (mmUpdate *mRolePrimeDBMockUpdate) ExpectCtxParam1(ctx context.Context) *mRolePrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &RolePrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &RolePrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.ctx = &ctx
	mmUpdate.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmUpdate
}

// ExpectRoleParam2 sets up expected param role for RolePrimeDB.Update
func (mmUpdate *mRolePrimeDBMockUpdate) ExpectRoleParam2(role roleentity.RoleUpdateData) *mRolePrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &RolePrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &RolePrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.role = &role
	mmUpdate.defaultExpectation.expectationOrigins.originRole = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the RolePrimeDB.Update
func (mmUpdate *mRolePrimeDBMockUpdate) Inspect(f func(ctx context.Context, role roleentity.RoleUpdateData)) *mRolePrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for RolePrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by RolePrimeDB.Update
func (mmUpdate *mRolePrimeDBMockUpdate) Return(r1 roleentity.Role, err error) *RolePrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &RolePrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &RolePrimeDBMockUpdateResults{r1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the RolePrimeDB.Update method
func (mmUpdate *mRolePrimeDBMockUpdate) Set(f func(ctx context.Context, role roleentity.RoleUpdateData) (r1 roleentity.Role, err error)) *RolePrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the RolePrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the RolePrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the RolePrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mRolePrimeDBMockUpdate) When(ctx context.Context, role roleentity.RoleUpdateData) *RolePrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("RolePrimeDBMock.Update mock is already set by Set")
	}

	expectation := &RolePrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &RolePrimeDBMockUpdateParams{ctx, role},
		expectationOrigins: RolePrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up RolePrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *RolePrimeDBMockUpdateExpectation) Then(r1 roleentity.Role, err error) *RolePrimeDBMock {
	e.results = &RolePrimeDBMockUpdateResults{r1, err}
	return e.mock
}

// Times sets number of times RolePrimeDB.Update should be invoked
func (mmUpdate *mRolePrimeDBMockUpdate) Times(n uint64) *mRolePrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of RolePrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mRolePrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.RolePrimeDB
func (mmUpdate *RolePrimeDBMock) Update(ctx context.Context, role roleentity.RoleUpdateData) (r1 roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(ctx, role)
	}

	mm_params := RolePrimeDBMockUpdateParams{ctx, role}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := RolePrimeDBMockUpdateParams{ctx, role}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmUpdate.t.Errorf("RolePrimeDBMock.Update got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.role != nil && !minimock.Equal(*mm_want_ptrs.role, mm_got.role) {
				mmUpdate.t.Errorf("RolePrimeDBMock.Update got unexpected parameter role, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originRole, *mm_want_ptrs.role, mm_got.role, minimock.Diff(*mm_want_ptrs.role, mm_got.role))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("RolePrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the RolePrimeDBMock.Update")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(ctx, role)
	}
	mmUpdate.t.Fatalf("Unexpected call to RolePrimeDBMock.Update. %v %v", ctx, role)
	return
}

// UpdateAfterCounter returns a count of finished RolePrimeDBMock.Update invocations
func (mmUpdate *RolePrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of RolePrimeDBMock.Update invocations
func (mmUpdate *RolePrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to RolePrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mRolePrimeDBMockUpdate) Calls() []*RolePrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*RolePrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *RolePrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *RolePrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RolePrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RolePrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RolePrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to RolePrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to RolePrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *RolePrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeactivateByIDsInspect()

			m.MinimockExistByNameInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllByUserIDInspect()

			m.MinimockGetAllWithProductIDByUserIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByProductIDInspect()

			m.MinimockGetByProductIDAndIsActiveInspect()

			m.MinimockGetByRoleIDAndProductIDInspect()

			m.MinimockGetBySystemTypeInspect()

			m.MinimockGetOwnerRoleInspect()

			m.MinimockGetRoleCategoryLinksInspect()

			m.MinimockGetRoleWithProductByParticipantIDInspect()

			m.MinimockGetRolesWithProductByParticipantIDsInspect()

			m.MinimockGetRolesWithStatsInspect()

			m.MinimockGetSystemRolesWithStatsInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *RolePrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *RolePrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeactivateByIDsDone() &&
		m.MinimockExistByNameDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllByUserIDDone() &&
		m.MinimockGetAllWithProductIDByUserIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByProductIDDone() &&
		m.MinimockGetByProductIDAndIsActiveDone() &&
		m.MinimockGetByRoleIDAndProductIDDone() &&
		m.MinimockGetBySystemTypeDone() &&
		m.MinimockGetOwnerRoleDone() &&
		m.MinimockGetRoleCategoryLinksDone() &&
		m.MinimockGetRoleWithProductByParticipantIDDone() &&
		m.MinimockGetRolesWithProductByParticipantIDsDone() &&
		m.MinimockGetRolesWithStatsDone() &&
		m.MinimockGetSystemRolesWithStatsDone() &&
		m.MinimockUpdateDone()
}
