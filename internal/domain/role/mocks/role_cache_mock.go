// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository.RoleCache -o role_cache_mock.go -n RoleCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	"github.com/gojuno/minimock/v3"
)

// RoleCacheMock implements mm_repository.RoleCache
type RoleCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcAssignProductRole          func(ctx context.Context, roleID int64, productID int64) (err error)
	funcAssignProductRoleOrigin    string
	inspectFuncAssignProductRole   func(ctx context.Context, roleID int64, productID int64)
	afterAssignProductRoleCounter  uint64
	beforeAssignProductRoleCounter uint64
	AssignProductRoleMock          mRoleCacheMockAssignProductRole

	funcAssignUserRole          func(ctx context.Context, userID int64, roleID int64) (err error)
	funcAssignUserRoleOrigin    string
	inspectFuncAssignUserRole   func(ctx context.Context, userID int64, roleID int64)
	afterAssignUserRoleCounter  uint64
	beforeAssignUserRoleCounter uint64
	AssignUserRoleMock          mRoleCacheMockAssignUserRole

	funcDeleteRole          func(ctx context.Context, id int64) (err error)
	funcDeleteRoleOrigin    string
	inspectFuncDeleteRole   func(ctx context.Context, id int64)
	afterDeleteRoleCounter  uint64
	beforeDeleteRoleCounter uint64
	DeleteRoleMock          mRoleCacheMockDeleteRole

	funcDeleteRoles          func(ctx context.Context, ids []int64) (err error)
	funcDeleteRolesOrigin    string
	inspectFuncDeleteRoles   func(ctx context.Context, ids []int64)
	afterDeleteRolesCounter  uint64
	beforeDeleteRolesCounter uint64
	DeleteRolesMock          mRoleCacheMockDeleteRoles

	funcGetProductRoles          func(ctx context.Context, productID int64) (ra1 []roleentity.Role, err error)
	funcGetProductRolesOrigin    string
	inspectFuncGetProductRoles   func(ctx context.Context, productID int64)
	afterGetProductRolesCounter  uint64
	beforeGetProductRolesCounter uint64
	GetProductRolesMock          mRoleCacheMockGetProductRoles

	funcGetRole          func(ctx context.Context, id int64) (r1 roleentity.Role, err error)
	funcGetRoleOrigin    string
	inspectFuncGetRole   func(ctx context.Context, id int64)
	afterGetRoleCounter  uint64
	beforeGetRoleCounter uint64
	GetRoleMock          mRoleCacheMockGetRole

	funcGetRoles          func(ctx context.Context) (ra1 []roleentity.Role, err error)
	funcGetRolesOrigin    string
	inspectFuncGetRoles   func(ctx context.Context)
	afterGetRolesCounter  uint64
	beforeGetRolesCounter uint64
	GetRolesMock          mRoleCacheMockGetRoles

	funcGetUserRoleIDs          func(ctx context.Context, userID int64) (ia1 []int64, err error)
	funcGetUserRoleIDsOrigin    string
	inspectFuncGetUserRoleIDs   func(ctx context.Context, userID int64)
	afterGetUserRoleIDsCounter  uint64
	beforeGetUserRoleIDsCounter uint64
	GetUserRoleIDsMock          mRoleCacheMockGetUserRoleIDs

	funcGetUserRoles          func(ctx context.Context, userID int64) (ra1 []roleentity.Role, err error)
	funcGetUserRolesOrigin    string
	inspectFuncGetUserRoles   func(ctx context.Context, userID int64)
	afterGetUserRolesCounter  uint64
	beforeGetUserRolesCounter uint64
	GetUserRolesMock          mRoleCacheMockGetUserRoles

	funcRemoveProductRole          func(ctx context.Context, roleID int64, productID int64) (err error)
	funcRemoveProductRoleOrigin    string
	inspectFuncRemoveProductRole   func(ctx context.Context, roleID int64, productID int64)
	afterRemoveProductRoleCounter  uint64
	beforeRemoveProductRoleCounter uint64
	RemoveProductRoleMock          mRoleCacheMockRemoveProductRole

	funcRemoveUserRole          func(ctx context.Context, userID int64, roleID int64) (err error)
	funcRemoveUserRoleOrigin    string
	inspectFuncRemoveUserRole   func(ctx context.Context, userID int64, roleID int64)
	afterRemoveUserRoleCounter  uint64
	beforeRemoveUserRoleCounter uint64
	RemoveUserRoleMock          mRoleCacheMockRemoveUserRole

	funcSetRole          func(ctx context.Context, role roleentity.Role) (err error)
	funcSetRoleOrigin    string
	inspectFuncSetRole   func(ctx context.Context, role roleentity.Role)
	afterSetRoleCounter  uint64
	beforeSetRoleCounter uint64
	SetRoleMock          mRoleCacheMockSetRole

	funcSetRoles          func(ctx context.Context, roles []roleentity.Role) (err error)
	funcSetRolesOrigin    string
	inspectFuncSetRoles   func(ctx context.Context, roles []roleentity.Role)
	afterSetRolesCounter  uint64
	beforeSetRolesCounter uint64
	SetRolesMock          mRoleCacheMockSetRoles
}

// NewRoleCacheMock returns a mock for mm_repository.RoleCache
func NewRoleCacheMock(t minimock.Tester) *RoleCacheMock {
	m := &RoleCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.AssignProductRoleMock = mRoleCacheMockAssignProductRole{mock: m}
	m.AssignProductRoleMock.callArgs = []*RoleCacheMockAssignProductRoleParams{}

	m.AssignUserRoleMock = mRoleCacheMockAssignUserRole{mock: m}
	m.AssignUserRoleMock.callArgs = []*RoleCacheMockAssignUserRoleParams{}

	m.DeleteRoleMock = mRoleCacheMockDeleteRole{mock: m}
	m.DeleteRoleMock.callArgs = []*RoleCacheMockDeleteRoleParams{}

	m.DeleteRolesMock = mRoleCacheMockDeleteRoles{mock: m}
	m.DeleteRolesMock.callArgs = []*RoleCacheMockDeleteRolesParams{}

	m.GetProductRolesMock = mRoleCacheMockGetProductRoles{mock: m}
	m.GetProductRolesMock.callArgs = []*RoleCacheMockGetProductRolesParams{}

	m.GetRoleMock = mRoleCacheMockGetRole{mock: m}
	m.GetRoleMock.callArgs = []*RoleCacheMockGetRoleParams{}

	m.GetRolesMock = mRoleCacheMockGetRoles{mock: m}
	m.GetRolesMock.callArgs = []*RoleCacheMockGetRolesParams{}

	m.GetUserRoleIDsMock = mRoleCacheMockGetUserRoleIDs{mock: m}
	m.GetUserRoleIDsMock.callArgs = []*RoleCacheMockGetUserRoleIDsParams{}

	m.GetUserRolesMock = mRoleCacheMockGetUserRoles{mock: m}
	m.GetUserRolesMock.callArgs = []*RoleCacheMockGetUserRolesParams{}

	m.RemoveProductRoleMock = mRoleCacheMockRemoveProductRole{mock: m}
	m.RemoveProductRoleMock.callArgs = []*RoleCacheMockRemoveProductRoleParams{}

	m.RemoveUserRoleMock = mRoleCacheMockRemoveUserRole{mock: m}
	m.RemoveUserRoleMock.callArgs = []*RoleCacheMockRemoveUserRoleParams{}

	m.SetRoleMock = mRoleCacheMockSetRole{mock: m}
	m.SetRoleMock.callArgs = []*RoleCacheMockSetRoleParams{}

	m.SetRolesMock = mRoleCacheMockSetRoles{mock: m}
	m.SetRolesMock.callArgs = []*RoleCacheMockSetRolesParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mRoleCacheMockAssignProductRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockAssignProductRoleExpectation
	expectations       []*RoleCacheMockAssignProductRoleExpectation

	callArgs []*RoleCacheMockAssignProductRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockAssignProductRoleExpectation specifies expectation struct of the RoleCache.AssignProductRole
type RoleCacheMockAssignProductRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockAssignProductRoleParams
	paramPtrs          *RoleCacheMockAssignProductRoleParamPtrs
	expectationOrigins RoleCacheMockAssignProductRoleExpectationOrigins
	results            *RoleCacheMockAssignProductRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockAssignProductRoleParams contains parameters of the RoleCache.AssignProductRole
type RoleCacheMockAssignProductRoleParams struct {
	ctx       context.Context
	roleID    int64
	productID int64
}

// RoleCacheMockAssignProductRoleParamPtrs contains pointers to parameters of the RoleCache.AssignProductRole
type RoleCacheMockAssignProductRoleParamPtrs struct {
	ctx       *context.Context
	roleID    *int64
	productID *int64
}

// RoleCacheMockAssignProductRoleResults contains results of the RoleCache.AssignProductRole
type RoleCacheMockAssignProductRoleResults struct {
	err error
}

// RoleCacheMockAssignProductRoleOrigins contains origins of expectations of the RoleCache.AssignProductRole
type RoleCacheMockAssignProductRoleExpectationOrigins struct {
	origin          string
	originCtx       string
	originRoleID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Optional() *mRoleCacheMockAssignProductRole {
	mmAssignProductRole.optional = true
	return mmAssignProductRole
}

// Expect sets up expected params for RoleCache.AssignProductRole
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Expect(ctx context.Context, roleID int64, productID int64) *mRoleCacheMockAssignProductRole {
	if mmAssignProductRole.mock.funcAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Set")
	}

	if mmAssignProductRole.defaultExpectation == nil {
		mmAssignProductRole.defaultExpectation = &RoleCacheMockAssignProductRoleExpectation{}
	}

	if mmAssignProductRole.defaultExpectation.paramPtrs != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by ExpectParams functions")
	}

	mmAssignProductRole.defaultExpectation.params = &RoleCacheMockAssignProductRoleParams{ctx, roleID, productID}
	mmAssignProductRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmAssignProductRole.expectations {
		if minimock.Equal(e.params, mmAssignProductRole.defaultExpectation.params) {
			mmAssignProductRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmAssignProductRole.defaultExpectation.params)
		}
	}

	return mmAssignProductRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.AssignProductRole
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockAssignProductRole {
	if mmAssignProductRole.mock.funcAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Set")
	}

	if mmAssignProductRole.defaultExpectation == nil {
		mmAssignProductRole.defaultExpectation = &RoleCacheMockAssignProductRoleExpectation{}
	}

	if mmAssignProductRole.defaultExpectation.params != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Expect")
	}

	if mmAssignProductRole.defaultExpectation.paramPtrs == nil {
		mmAssignProductRole.defaultExpectation.paramPtrs = &RoleCacheMockAssignProductRoleParamPtrs{}
	}
	mmAssignProductRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmAssignProductRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmAssignProductRole
}

// ExpectRoleIDParam2 sets up expected param roleID for RoleCache.AssignProductRole
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) ExpectRoleIDParam2(roleID int64) *mRoleCacheMockAssignProductRole {
	if mmAssignProductRole.mock.funcAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Set")
	}

	if mmAssignProductRole.defaultExpectation == nil {
		mmAssignProductRole.defaultExpectation = &RoleCacheMockAssignProductRoleExpectation{}
	}

	if mmAssignProductRole.defaultExpectation.params != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Expect")
	}

	if mmAssignProductRole.defaultExpectation.paramPtrs == nil {
		mmAssignProductRole.defaultExpectation.paramPtrs = &RoleCacheMockAssignProductRoleParamPtrs{}
	}
	mmAssignProductRole.defaultExpectation.paramPtrs.roleID = &roleID
	mmAssignProductRole.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmAssignProductRole
}

// ExpectProductIDParam3 sets up expected param productID for RoleCache.AssignProductRole
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) ExpectProductIDParam3(productID int64) *mRoleCacheMockAssignProductRole {
	if mmAssignProductRole.mock.funcAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Set")
	}

	if mmAssignProductRole.defaultExpectation == nil {
		mmAssignProductRole.defaultExpectation = &RoleCacheMockAssignProductRoleExpectation{}
	}

	if mmAssignProductRole.defaultExpectation.params != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Expect")
	}

	if mmAssignProductRole.defaultExpectation.paramPtrs == nil {
		mmAssignProductRole.defaultExpectation.paramPtrs = &RoleCacheMockAssignProductRoleParamPtrs{}
	}
	mmAssignProductRole.defaultExpectation.paramPtrs.productID = &productID
	mmAssignProductRole.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmAssignProductRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.AssignProductRole
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Inspect(f func(ctx context.Context, roleID int64, productID int64)) *mRoleCacheMockAssignProductRole {
	if mmAssignProductRole.mock.inspectFuncAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.AssignProductRole")
	}

	mmAssignProductRole.mock.inspectFuncAssignProductRole = f

	return mmAssignProductRole
}

// Return sets up results that will be returned by RoleCache.AssignProductRole
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Return(err error) *RoleCacheMock {
	if mmAssignProductRole.mock.funcAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Set")
	}

	if mmAssignProductRole.defaultExpectation == nil {
		mmAssignProductRole.defaultExpectation = &RoleCacheMockAssignProductRoleExpectation{mock: mmAssignProductRole.mock}
	}
	mmAssignProductRole.defaultExpectation.results = &RoleCacheMockAssignProductRoleResults{err}
	mmAssignProductRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmAssignProductRole.mock
}

// Set uses given function f to mock the RoleCache.AssignProductRole method
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Set(f func(ctx context.Context, roleID int64, productID int64) (err error)) *RoleCacheMock {
	if mmAssignProductRole.defaultExpectation != nil {
		mmAssignProductRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.AssignProductRole method")
	}

	if len(mmAssignProductRole.expectations) > 0 {
		mmAssignProductRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.AssignProductRole method")
	}

	mmAssignProductRole.mock.funcAssignProductRole = f
	mmAssignProductRole.mock.funcAssignProductRoleOrigin = minimock.CallerInfo(1)
	return mmAssignProductRole.mock
}

// When sets expectation for the RoleCache.AssignProductRole which will trigger the result defined by the following
// Then helper
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) When(ctx context.Context, roleID int64, productID int64) *RoleCacheMockAssignProductRoleExpectation {
	if mmAssignProductRole.mock.funcAssignProductRole != nil {
		mmAssignProductRole.mock.t.Fatalf("RoleCacheMock.AssignProductRole mock is already set by Set")
	}

	expectation := &RoleCacheMockAssignProductRoleExpectation{
		mock:               mmAssignProductRole.mock,
		params:             &RoleCacheMockAssignProductRoleParams{ctx, roleID, productID},
		expectationOrigins: RoleCacheMockAssignProductRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmAssignProductRole.expectations = append(mmAssignProductRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.AssignProductRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockAssignProductRoleExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockAssignProductRoleResults{err}
	return e.mock
}

// Times sets number of times RoleCache.AssignProductRole should be invoked
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Times(n uint64) *mRoleCacheMockAssignProductRole {
	if n == 0 {
		mmAssignProductRole.mock.t.Fatalf("Times of RoleCacheMock.AssignProductRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmAssignProductRole.expectedInvocations, n)
	mmAssignProductRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmAssignProductRole
}

func (mmAssignProductRole *mRoleCacheMockAssignProductRole) invocationsDone() bool {
	if len(mmAssignProductRole.expectations) == 0 && mmAssignProductRole.defaultExpectation == nil && mmAssignProductRole.mock.funcAssignProductRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmAssignProductRole.mock.afterAssignProductRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmAssignProductRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// AssignProductRole implements mm_repository.RoleCache
func (mmAssignProductRole *RoleCacheMock) AssignProductRole(ctx context.Context, roleID int64, productID int64) (err error) {
	mm_atomic.AddUint64(&mmAssignProductRole.beforeAssignProductRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmAssignProductRole.afterAssignProductRoleCounter, 1)

	mmAssignProductRole.t.Helper()

	if mmAssignProductRole.inspectFuncAssignProductRole != nil {
		mmAssignProductRole.inspectFuncAssignProductRole(ctx, roleID, productID)
	}

	mm_params := RoleCacheMockAssignProductRoleParams{ctx, roleID, productID}

	// Record call args
	mmAssignProductRole.AssignProductRoleMock.mutex.Lock()
	mmAssignProductRole.AssignProductRoleMock.callArgs = append(mmAssignProductRole.AssignProductRoleMock.callArgs, &mm_params)
	mmAssignProductRole.AssignProductRoleMock.mutex.Unlock()

	for _, e := range mmAssignProductRole.AssignProductRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmAssignProductRole.AssignProductRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmAssignProductRole.AssignProductRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmAssignProductRole.AssignProductRoleMock.defaultExpectation.params
		mm_want_ptrs := mmAssignProductRole.AssignProductRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockAssignProductRoleParams{ctx, roleID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmAssignProductRole.t.Errorf("RoleCacheMock.AssignProductRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignProductRole.AssignProductRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmAssignProductRole.t.Errorf("RoleCacheMock.AssignProductRole got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignProductRole.AssignProductRoleMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmAssignProductRole.t.Errorf("RoleCacheMock.AssignProductRole got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignProductRole.AssignProductRoleMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmAssignProductRole.t.Errorf("RoleCacheMock.AssignProductRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmAssignProductRole.AssignProductRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmAssignProductRole.AssignProductRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmAssignProductRole.t.Fatal("No results are set for the RoleCacheMock.AssignProductRole")
		}
		return (*mm_results).err
	}
	if mmAssignProductRole.funcAssignProductRole != nil {
		return mmAssignProductRole.funcAssignProductRole(ctx, roleID, productID)
	}
	mmAssignProductRole.t.Fatalf("Unexpected call to RoleCacheMock.AssignProductRole. %v %v %v", ctx, roleID, productID)
	return
}

// AssignProductRoleAfterCounter returns a count of finished RoleCacheMock.AssignProductRole invocations
func (mmAssignProductRole *RoleCacheMock) AssignProductRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignProductRole.afterAssignProductRoleCounter)
}

// AssignProductRoleBeforeCounter returns a count of RoleCacheMock.AssignProductRole invocations
func (mmAssignProductRole *RoleCacheMock) AssignProductRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignProductRole.beforeAssignProductRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.AssignProductRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmAssignProductRole *mRoleCacheMockAssignProductRole) Calls() []*RoleCacheMockAssignProductRoleParams {
	mmAssignProductRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockAssignProductRoleParams, len(mmAssignProductRole.callArgs))
	copy(argCopy, mmAssignProductRole.callArgs)

	mmAssignProductRole.mutex.RUnlock()

	return argCopy
}

// MinimockAssignProductRoleDone returns true if the count of the AssignProductRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockAssignProductRoleDone() bool {
	if m.AssignProductRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.AssignProductRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.AssignProductRoleMock.invocationsDone()
}

// MinimockAssignProductRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockAssignProductRoleInspect() {
	for _, e := range m.AssignProductRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.AssignProductRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterAssignProductRoleCounter := mm_atomic.LoadUint64(&m.afterAssignProductRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.AssignProductRoleMock.defaultExpectation != nil && afterAssignProductRoleCounter < 1 {
		if m.AssignProductRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.AssignProductRole at\n%s", m.AssignProductRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.AssignProductRole at\n%s with params: %#v", m.AssignProductRoleMock.defaultExpectation.expectationOrigins.origin, *m.AssignProductRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcAssignProductRole != nil && afterAssignProductRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.AssignProductRole at\n%s", m.funcAssignProductRoleOrigin)
	}

	if !m.AssignProductRoleMock.invocationsDone() && afterAssignProductRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.AssignProductRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.AssignProductRoleMock.expectedInvocations), m.AssignProductRoleMock.expectedInvocationsOrigin, afterAssignProductRoleCounter)
	}
}

type mRoleCacheMockAssignUserRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockAssignUserRoleExpectation
	expectations       []*RoleCacheMockAssignUserRoleExpectation

	callArgs []*RoleCacheMockAssignUserRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockAssignUserRoleExpectation specifies expectation struct of the RoleCache.AssignUserRole
type RoleCacheMockAssignUserRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockAssignUserRoleParams
	paramPtrs          *RoleCacheMockAssignUserRoleParamPtrs
	expectationOrigins RoleCacheMockAssignUserRoleExpectationOrigins
	results            *RoleCacheMockAssignUserRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockAssignUserRoleParams contains parameters of the RoleCache.AssignUserRole
type RoleCacheMockAssignUserRoleParams struct {
	ctx    context.Context
	userID int64
	roleID int64
}

// RoleCacheMockAssignUserRoleParamPtrs contains pointers to parameters of the RoleCache.AssignUserRole
type RoleCacheMockAssignUserRoleParamPtrs struct {
	ctx    *context.Context
	userID *int64
	roleID *int64
}

// RoleCacheMockAssignUserRoleResults contains results of the RoleCache.AssignUserRole
type RoleCacheMockAssignUserRoleResults struct {
	err error
}

// RoleCacheMockAssignUserRoleOrigins contains origins of expectations of the RoleCache.AssignUserRole
type RoleCacheMockAssignUserRoleExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Optional() *mRoleCacheMockAssignUserRole {
	mmAssignUserRole.optional = true
	return mmAssignUserRole
}

// Expect sets up expected params for RoleCache.AssignUserRole
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Expect(ctx context.Context, userID int64, roleID int64) *mRoleCacheMockAssignUserRole {
	if mmAssignUserRole.mock.funcAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Set")
	}

	if mmAssignUserRole.defaultExpectation == nil {
		mmAssignUserRole.defaultExpectation = &RoleCacheMockAssignUserRoleExpectation{}
	}

	if mmAssignUserRole.defaultExpectation.paramPtrs != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by ExpectParams functions")
	}

	mmAssignUserRole.defaultExpectation.params = &RoleCacheMockAssignUserRoleParams{ctx, userID, roleID}
	mmAssignUserRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmAssignUserRole.expectations {
		if minimock.Equal(e.params, mmAssignUserRole.defaultExpectation.params) {
			mmAssignUserRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmAssignUserRole.defaultExpectation.params)
		}
	}

	return mmAssignUserRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.AssignUserRole
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockAssignUserRole {
	if mmAssignUserRole.mock.funcAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Set")
	}

	if mmAssignUserRole.defaultExpectation == nil {
		mmAssignUserRole.defaultExpectation = &RoleCacheMockAssignUserRoleExpectation{}
	}

	if mmAssignUserRole.defaultExpectation.params != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Expect")
	}

	if mmAssignUserRole.defaultExpectation.paramPtrs == nil {
		mmAssignUserRole.defaultExpectation.paramPtrs = &RoleCacheMockAssignUserRoleParamPtrs{}
	}
	mmAssignUserRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmAssignUserRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmAssignUserRole
}

// ExpectUserIDParam2 sets up expected param userID for RoleCache.AssignUserRole
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) ExpectUserIDParam2(userID int64) *mRoleCacheMockAssignUserRole {
	if mmAssignUserRole.mock.funcAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Set")
	}

	if mmAssignUserRole.defaultExpectation == nil {
		mmAssignUserRole.defaultExpectation = &RoleCacheMockAssignUserRoleExpectation{}
	}

	if mmAssignUserRole.defaultExpectation.params != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Expect")
	}

	if mmAssignUserRole.defaultExpectation.paramPtrs == nil {
		mmAssignUserRole.defaultExpectation.paramPtrs = &RoleCacheMockAssignUserRoleParamPtrs{}
	}
	mmAssignUserRole.defaultExpectation.paramPtrs.userID = &userID
	mmAssignUserRole.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmAssignUserRole
}

// ExpectRoleIDParam3 sets up expected param roleID for RoleCache.AssignUserRole
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) ExpectRoleIDParam3(roleID int64) *mRoleCacheMockAssignUserRole {
	if mmAssignUserRole.mock.funcAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Set")
	}

	if mmAssignUserRole.defaultExpectation == nil {
		mmAssignUserRole.defaultExpectation = &RoleCacheMockAssignUserRoleExpectation{}
	}

	if mmAssignUserRole.defaultExpectation.params != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Expect")
	}

	if mmAssignUserRole.defaultExpectation.paramPtrs == nil {
		mmAssignUserRole.defaultExpectation.paramPtrs = &RoleCacheMockAssignUserRoleParamPtrs{}
	}
	mmAssignUserRole.defaultExpectation.paramPtrs.roleID = &roleID
	mmAssignUserRole.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmAssignUserRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.AssignUserRole
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Inspect(f func(ctx context.Context, userID int64, roleID int64)) *mRoleCacheMockAssignUserRole {
	if mmAssignUserRole.mock.inspectFuncAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.AssignUserRole")
	}

	mmAssignUserRole.mock.inspectFuncAssignUserRole = f

	return mmAssignUserRole
}

// Return sets up results that will be returned by RoleCache.AssignUserRole
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Return(err error) *RoleCacheMock {
	if mmAssignUserRole.mock.funcAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Set")
	}

	if mmAssignUserRole.defaultExpectation == nil {
		mmAssignUserRole.defaultExpectation = &RoleCacheMockAssignUserRoleExpectation{mock: mmAssignUserRole.mock}
	}
	mmAssignUserRole.defaultExpectation.results = &RoleCacheMockAssignUserRoleResults{err}
	mmAssignUserRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmAssignUserRole.mock
}

// Set uses given function f to mock the RoleCache.AssignUserRole method
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Set(f func(ctx context.Context, userID int64, roleID int64) (err error)) *RoleCacheMock {
	if mmAssignUserRole.defaultExpectation != nil {
		mmAssignUserRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.AssignUserRole method")
	}

	if len(mmAssignUserRole.expectations) > 0 {
		mmAssignUserRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.AssignUserRole method")
	}

	mmAssignUserRole.mock.funcAssignUserRole = f
	mmAssignUserRole.mock.funcAssignUserRoleOrigin = minimock.CallerInfo(1)
	return mmAssignUserRole.mock
}

// When sets expectation for the RoleCache.AssignUserRole which will trigger the result defined by the following
// Then helper
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) When(ctx context.Context, userID int64, roleID int64) *RoleCacheMockAssignUserRoleExpectation {
	if mmAssignUserRole.mock.funcAssignUserRole != nil {
		mmAssignUserRole.mock.t.Fatalf("RoleCacheMock.AssignUserRole mock is already set by Set")
	}

	expectation := &RoleCacheMockAssignUserRoleExpectation{
		mock:               mmAssignUserRole.mock,
		params:             &RoleCacheMockAssignUserRoleParams{ctx, userID, roleID},
		expectationOrigins: RoleCacheMockAssignUserRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmAssignUserRole.expectations = append(mmAssignUserRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.AssignUserRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockAssignUserRoleExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockAssignUserRoleResults{err}
	return e.mock
}

// Times sets number of times RoleCache.AssignUserRole should be invoked
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Times(n uint64) *mRoleCacheMockAssignUserRole {
	if n == 0 {
		mmAssignUserRole.mock.t.Fatalf("Times of RoleCacheMock.AssignUserRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmAssignUserRole.expectedInvocations, n)
	mmAssignUserRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmAssignUserRole
}

func (mmAssignUserRole *mRoleCacheMockAssignUserRole) invocationsDone() bool {
	if len(mmAssignUserRole.expectations) == 0 && mmAssignUserRole.defaultExpectation == nil && mmAssignUserRole.mock.funcAssignUserRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmAssignUserRole.mock.afterAssignUserRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmAssignUserRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// AssignUserRole implements mm_repository.RoleCache
func (mmAssignUserRole *RoleCacheMock) AssignUserRole(ctx context.Context, userID int64, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmAssignUserRole.beforeAssignUserRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmAssignUserRole.afterAssignUserRoleCounter, 1)

	mmAssignUserRole.t.Helper()

	if mmAssignUserRole.inspectFuncAssignUserRole != nil {
		mmAssignUserRole.inspectFuncAssignUserRole(ctx, userID, roleID)
	}

	mm_params := RoleCacheMockAssignUserRoleParams{ctx, userID, roleID}

	// Record call args
	mmAssignUserRole.AssignUserRoleMock.mutex.Lock()
	mmAssignUserRole.AssignUserRoleMock.callArgs = append(mmAssignUserRole.AssignUserRoleMock.callArgs, &mm_params)
	mmAssignUserRole.AssignUserRoleMock.mutex.Unlock()

	for _, e := range mmAssignUserRole.AssignUserRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmAssignUserRole.AssignUserRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmAssignUserRole.AssignUserRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmAssignUserRole.AssignUserRoleMock.defaultExpectation.params
		mm_want_ptrs := mmAssignUserRole.AssignUserRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockAssignUserRoleParams{ctx, userID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmAssignUserRole.t.Errorf("RoleCacheMock.AssignUserRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignUserRole.AssignUserRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmAssignUserRole.t.Errorf("RoleCacheMock.AssignUserRole got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignUserRole.AssignUserRoleMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmAssignUserRole.t.Errorf("RoleCacheMock.AssignUserRole got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignUserRole.AssignUserRoleMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmAssignUserRole.t.Errorf("RoleCacheMock.AssignUserRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmAssignUserRole.AssignUserRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmAssignUserRole.AssignUserRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmAssignUserRole.t.Fatal("No results are set for the RoleCacheMock.AssignUserRole")
		}
		return (*mm_results).err
	}
	if mmAssignUserRole.funcAssignUserRole != nil {
		return mmAssignUserRole.funcAssignUserRole(ctx, userID, roleID)
	}
	mmAssignUserRole.t.Fatalf("Unexpected call to RoleCacheMock.AssignUserRole. %v %v %v", ctx, userID, roleID)
	return
}

// AssignUserRoleAfterCounter returns a count of finished RoleCacheMock.AssignUserRole invocations
func (mmAssignUserRole *RoleCacheMock) AssignUserRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignUserRole.afterAssignUserRoleCounter)
}

// AssignUserRoleBeforeCounter returns a count of RoleCacheMock.AssignUserRole invocations
func (mmAssignUserRole *RoleCacheMock) AssignUserRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignUserRole.beforeAssignUserRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.AssignUserRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmAssignUserRole *mRoleCacheMockAssignUserRole) Calls() []*RoleCacheMockAssignUserRoleParams {
	mmAssignUserRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockAssignUserRoleParams, len(mmAssignUserRole.callArgs))
	copy(argCopy, mmAssignUserRole.callArgs)

	mmAssignUserRole.mutex.RUnlock()

	return argCopy
}

// MinimockAssignUserRoleDone returns true if the count of the AssignUserRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockAssignUserRoleDone() bool {
	if m.AssignUserRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.AssignUserRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.AssignUserRoleMock.invocationsDone()
}

// MinimockAssignUserRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockAssignUserRoleInspect() {
	for _, e := range m.AssignUserRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.AssignUserRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterAssignUserRoleCounter := mm_atomic.LoadUint64(&m.afterAssignUserRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.AssignUserRoleMock.defaultExpectation != nil && afterAssignUserRoleCounter < 1 {
		if m.AssignUserRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.AssignUserRole at\n%s", m.AssignUserRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.AssignUserRole at\n%s with params: %#v", m.AssignUserRoleMock.defaultExpectation.expectationOrigins.origin, *m.AssignUserRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcAssignUserRole != nil && afterAssignUserRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.AssignUserRole at\n%s", m.funcAssignUserRoleOrigin)
	}

	if !m.AssignUserRoleMock.invocationsDone() && afterAssignUserRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.AssignUserRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.AssignUserRoleMock.expectedInvocations), m.AssignUserRoleMock.expectedInvocationsOrigin, afterAssignUserRoleCounter)
	}
}

type mRoleCacheMockDeleteRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockDeleteRoleExpectation
	expectations       []*RoleCacheMockDeleteRoleExpectation

	callArgs []*RoleCacheMockDeleteRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockDeleteRoleExpectation specifies expectation struct of the RoleCache.DeleteRole
type RoleCacheMockDeleteRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockDeleteRoleParams
	paramPtrs          *RoleCacheMockDeleteRoleParamPtrs
	expectationOrigins RoleCacheMockDeleteRoleExpectationOrigins
	results            *RoleCacheMockDeleteRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockDeleteRoleParams contains parameters of the RoleCache.DeleteRole
type RoleCacheMockDeleteRoleParams struct {
	ctx context.Context
	id  int64
}

// RoleCacheMockDeleteRoleParamPtrs contains pointers to parameters of the RoleCache.DeleteRole
type RoleCacheMockDeleteRoleParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// RoleCacheMockDeleteRoleResults contains results of the RoleCache.DeleteRole
type RoleCacheMockDeleteRoleResults struct {
	err error
}

// RoleCacheMockDeleteRoleOrigins contains origins of expectations of the RoleCache.DeleteRole
type RoleCacheMockDeleteRoleExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteRole *mRoleCacheMockDeleteRole) Optional() *mRoleCacheMockDeleteRole {
	mmDeleteRole.optional = true
	return mmDeleteRole
}

// Expect sets up expected params for RoleCache.DeleteRole
func (mmDeleteRole *mRoleCacheMockDeleteRole) Expect(ctx context.Context, id int64) *mRoleCacheMockDeleteRole {
	if mmDeleteRole.mock.funcDeleteRole != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Set")
	}

	if mmDeleteRole.defaultExpectation == nil {
		mmDeleteRole.defaultExpectation = &RoleCacheMockDeleteRoleExpectation{}
	}

	if mmDeleteRole.defaultExpectation.paramPtrs != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by ExpectParams functions")
	}

	mmDeleteRole.defaultExpectation.params = &RoleCacheMockDeleteRoleParams{ctx, id}
	mmDeleteRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteRole.expectations {
		if minimock.Equal(e.params, mmDeleteRole.defaultExpectation.params) {
			mmDeleteRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteRole.defaultExpectation.params)
		}
	}

	return mmDeleteRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.DeleteRole
func (mmDeleteRole *mRoleCacheMockDeleteRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockDeleteRole {
	if mmDeleteRole.mock.funcDeleteRole != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Set")
	}

	if mmDeleteRole.defaultExpectation == nil {
		mmDeleteRole.defaultExpectation = &RoleCacheMockDeleteRoleExpectation{}
	}

	if mmDeleteRole.defaultExpectation.params != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Expect")
	}

	if mmDeleteRole.defaultExpectation.paramPtrs == nil {
		mmDeleteRole.defaultExpectation.paramPtrs = &RoleCacheMockDeleteRoleParamPtrs{}
	}
	mmDeleteRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteRole
}

// ExpectIdParam2 sets up expected param id for RoleCache.DeleteRole
func (mmDeleteRole *mRoleCacheMockDeleteRole) ExpectIdParam2(id int64) *mRoleCacheMockDeleteRole {
	if mmDeleteRole.mock.funcDeleteRole != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Set")
	}

	if mmDeleteRole.defaultExpectation == nil {
		mmDeleteRole.defaultExpectation = &RoleCacheMockDeleteRoleExpectation{}
	}

	if mmDeleteRole.defaultExpectation.params != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Expect")
	}

	if mmDeleteRole.defaultExpectation.paramPtrs == nil {
		mmDeleteRole.defaultExpectation.paramPtrs = &RoleCacheMockDeleteRoleParamPtrs{}
	}
	mmDeleteRole.defaultExpectation.paramPtrs.id = &id
	mmDeleteRole.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDeleteRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.DeleteRole
func (mmDeleteRole *mRoleCacheMockDeleteRole) Inspect(f func(ctx context.Context, id int64)) *mRoleCacheMockDeleteRole {
	if mmDeleteRole.mock.inspectFuncDeleteRole != nil {
		mmDeleteRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.DeleteRole")
	}

	mmDeleteRole.mock.inspectFuncDeleteRole = f

	return mmDeleteRole
}

// Return sets up results that will be returned by RoleCache.DeleteRole
func (mmDeleteRole *mRoleCacheMockDeleteRole) Return(err error) *RoleCacheMock {
	if mmDeleteRole.mock.funcDeleteRole != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Set")
	}

	if mmDeleteRole.defaultExpectation == nil {
		mmDeleteRole.defaultExpectation = &RoleCacheMockDeleteRoleExpectation{mock: mmDeleteRole.mock}
	}
	mmDeleteRole.defaultExpectation.results = &RoleCacheMockDeleteRoleResults{err}
	mmDeleteRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteRole.mock
}

// Set uses given function f to mock the RoleCache.DeleteRole method
func (mmDeleteRole *mRoleCacheMockDeleteRole) Set(f func(ctx context.Context, id int64) (err error)) *RoleCacheMock {
	if mmDeleteRole.defaultExpectation != nil {
		mmDeleteRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.DeleteRole method")
	}

	if len(mmDeleteRole.expectations) > 0 {
		mmDeleteRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.DeleteRole method")
	}

	mmDeleteRole.mock.funcDeleteRole = f
	mmDeleteRole.mock.funcDeleteRoleOrigin = minimock.CallerInfo(1)
	return mmDeleteRole.mock
}

// When sets expectation for the RoleCache.DeleteRole which will trigger the result defined by the following
// Then helper
func (mmDeleteRole *mRoleCacheMockDeleteRole) When(ctx context.Context, id int64) *RoleCacheMockDeleteRoleExpectation {
	if mmDeleteRole.mock.funcDeleteRole != nil {
		mmDeleteRole.mock.t.Fatalf("RoleCacheMock.DeleteRole mock is already set by Set")
	}

	expectation := &RoleCacheMockDeleteRoleExpectation{
		mock:               mmDeleteRole.mock,
		params:             &RoleCacheMockDeleteRoleParams{ctx, id},
		expectationOrigins: RoleCacheMockDeleteRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteRole.expectations = append(mmDeleteRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.DeleteRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockDeleteRoleExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockDeleteRoleResults{err}
	return e.mock
}

// Times sets number of times RoleCache.DeleteRole should be invoked
func (mmDeleteRole *mRoleCacheMockDeleteRole) Times(n uint64) *mRoleCacheMockDeleteRole {
	if n == 0 {
		mmDeleteRole.mock.t.Fatalf("Times of RoleCacheMock.DeleteRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteRole.expectedInvocations, n)
	mmDeleteRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteRole
}

func (mmDeleteRole *mRoleCacheMockDeleteRole) invocationsDone() bool {
	if len(mmDeleteRole.expectations) == 0 && mmDeleteRole.defaultExpectation == nil && mmDeleteRole.mock.funcDeleteRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteRole.mock.afterDeleteRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteRole implements mm_repository.RoleCache
func (mmDeleteRole *RoleCacheMock) DeleteRole(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteRole.beforeDeleteRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteRole.afterDeleteRoleCounter, 1)

	mmDeleteRole.t.Helper()

	if mmDeleteRole.inspectFuncDeleteRole != nil {
		mmDeleteRole.inspectFuncDeleteRole(ctx, id)
	}

	mm_params := RoleCacheMockDeleteRoleParams{ctx, id}

	// Record call args
	mmDeleteRole.DeleteRoleMock.mutex.Lock()
	mmDeleteRole.DeleteRoleMock.callArgs = append(mmDeleteRole.DeleteRoleMock.callArgs, &mm_params)
	mmDeleteRole.DeleteRoleMock.mutex.Unlock()

	for _, e := range mmDeleteRole.DeleteRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteRole.DeleteRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteRole.DeleteRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteRole.DeleteRoleMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteRole.DeleteRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockDeleteRoleParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteRole.t.Errorf("RoleCacheMock.DeleteRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteRole.DeleteRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDeleteRole.t.Errorf("RoleCacheMock.DeleteRole got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteRole.DeleteRoleMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteRole.t.Errorf("RoleCacheMock.DeleteRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteRole.DeleteRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteRole.DeleteRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteRole.t.Fatal("No results are set for the RoleCacheMock.DeleteRole")
		}
		return (*mm_results).err
	}
	if mmDeleteRole.funcDeleteRole != nil {
		return mmDeleteRole.funcDeleteRole(ctx, id)
	}
	mmDeleteRole.t.Fatalf("Unexpected call to RoleCacheMock.DeleteRole. %v %v", ctx, id)
	return
}

// DeleteRoleAfterCounter returns a count of finished RoleCacheMock.DeleteRole invocations
func (mmDeleteRole *RoleCacheMock) DeleteRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteRole.afterDeleteRoleCounter)
}

// DeleteRoleBeforeCounter returns a count of RoleCacheMock.DeleteRole invocations
func (mmDeleteRole *RoleCacheMock) DeleteRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteRole.beforeDeleteRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.DeleteRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteRole *mRoleCacheMockDeleteRole) Calls() []*RoleCacheMockDeleteRoleParams {
	mmDeleteRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockDeleteRoleParams, len(mmDeleteRole.callArgs))
	copy(argCopy, mmDeleteRole.callArgs)

	mmDeleteRole.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteRoleDone returns true if the count of the DeleteRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockDeleteRoleDone() bool {
	if m.DeleteRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteRoleMock.invocationsDone()
}

// MinimockDeleteRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockDeleteRoleInspect() {
	for _, e := range m.DeleteRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.DeleteRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteRoleCounter := mm_atomic.LoadUint64(&m.afterDeleteRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteRoleMock.defaultExpectation != nil && afterDeleteRoleCounter < 1 {
		if m.DeleteRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.DeleteRole at\n%s", m.DeleteRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.DeleteRole at\n%s with params: %#v", m.DeleteRoleMock.defaultExpectation.expectationOrigins.origin, *m.DeleteRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteRole != nil && afterDeleteRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.DeleteRole at\n%s", m.funcDeleteRoleOrigin)
	}

	if !m.DeleteRoleMock.invocationsDone() && afterDeleteRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.DeleteRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteRoleMock.expectedInvocations), m.DeleteRoleMock.expectedInvocationsOrigin, afterDeleteRoleCounter)
	}
}

type mRoleCacheMockDeleteRoles struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockDeleteRolesExpectation
	expectations       []*RoleCacheMockDeleteRolesExpectation

	callArgs []*RoleCacheMockDeleteRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockDeleteRolesExpectation specifies expectation struct of the RoleCache.DeleteRoles
type RoleCacheMockDeleteRolesExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockDeleteRolesParams
	paramPtrs          *RoleCacheMockDeleteRolesParamPtrs
	expectationOrigins RoleCacheMockDeleteRolesExpectationOrigins
	results            *RoleCacheMockDeleteRolesResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockDeleteRolesParams contains parameters of the RoleCache.DeleteRoles
type RoleCacheMockDeleteRolesParams struct {
	ctx context.Context
	ids []int64
}

// RoleCacheMockDeleteRolesParamPtrs contains pointers to parameters of the RoleCache.DeleteRoles
type RoleCacheMockDeleteRolesParamPtrs struct {
	ctx *context.Context
	ids *[]int64
}

// RoleCacheMockDeleteRolesResults contains results of the RoleCache.DeleteRoles
type RoleCacheMockDeleteRolesResults struct {
	err error
}

// RoleCacheMockDeleteRolesOrigins contains origins of expectations of the RoleCache.DeleteRoles
type RoleCacheMockDeleteRolesExpectationOrigins struct {
	origin    string
	originCtx string
	originIds string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Optional() *mRoleCacheMockDeleteRoles {
	mmDeleteRoles.optional = true
	return mmDeleteRoles
}

// Expect sets up expected params for RoleCache.DeleteRoles
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Expect(ctx context.Context, ids []int64) *mRoleCacheMockDeleteRoles {
	if mmDeleteRoles.mock.funcDeleteRoles != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Set")
	}

	if mmDeleteRoles.defaultExpectation == nil {
		mmDeleteRoles.defaultExpectation = &RoleCacheMockDeleteRolesExpectation{}
	}

	if mmDeleteRoles.defaultExpectation.paramPtrs != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by ExpectParams functions")
	}

	mmDeleteRoles.defaultExpectation.params = &RoleCacheMockDeleteRolesParams{ctx, ids}
	mmDeleteRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteRoles.expectations {
		if minimock.Equal(e.params, mmDeleteRoles.defaultExpectation.params) {
			mmDeleteRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteRoles.defaultExpectation.params)
		}
	}

	return mmDeleteRoles
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.DeleteRoles
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockDeleteRoles {
	if mmDeleteRoles.mock.funcDeleteRoles != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Set")
	}

	if mmDeleteRoles.defaultExpectation == nil {
		mmDeleteRoles.defaultExpectation = &RoleCacheMockDeleteRolesExpectation{}
	}

	if mmDeleteRoles.defaultExpectation.params != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Expect")
	}

	if mmDeleteRoles.defaultExpectation.paramPtrs == nil {
		mmDeleteRoles.defaultExpectation.paramPtrs = &RoleCacheMockDeleteRolesParamPtrs{}
	}
	mmDeleteRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteRoles
}

// ExpectIdsParam2 sets up expected param ids for RoleCache.DeleteRoles
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) ExpectIdsParam2(ids []int64) *mRoleCacheMockDeleteRoles {
	if mmDeleteRoles.mock.funcDeleteRoles != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Set")
	}

	if mmDeleteRoles.defaultExpectation == nil {
		mmDeleteRoles.defaultExpectation = &RoleCacheMockDeleteRolesExpectation{}
	}

	if mmDeleteRoles.defaultExpectation.params != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Expect")
	}

	if mmDeleteRoles.defaultExpectation.paramPtrs == nil {
		mmDeleteRoles.defaultExpectation.paramPtrs = &RoleCacheMockDeleteRolesParamPtrs{}
	}
	mmDeleteRoles.defaultExpectation.paramPtrs.ids = &ids
	mmDeleteRoles.defaultExpectation.expectationOrigins.originIds = minimock.CallerInfo(1)

	return mmDeleteRoles
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.DeleteRoles
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Inspect(f func(ctx context.Context, ids []int64)) *mRoleCacheMockDeleteRoles {
	if mmDeleteRoles.mock.inspectFuncDeleteRoles != nil {
		mmDeleteRoles.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.DeleteRoles")
	}

	mmDeleteRoles.mock.inspectFuncDeleteRoles = f

	return mmDeleteRoles
}

// Return sets up results that will be returned by RoleCache.DeleteRoles
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Return(err error) *RoleCacheMock {
	if mmDeleteRoles.mock.funcDeleteRoles != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Set")
	}

	if mmDeleteRoles.defaultExpectation == nil {
		mmDeleteRoles.defaultExpectation = &RoleCacheMockDeleteRolesExpectation{mock: mmDeleteRoles.mock}
	}
	mmDeleteRoles.defaultExpectation.results = &RoleCacheMockDeleteRolesResults{err}
	mmDeleteRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteRoles.mock
}

// Set uses given function f to mock the RoleCache.DeleteRoles method
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Set(f func(ctx context.Context, ids []int64) (err error)) *RoleCacheMock {
	if mmDeleteRoles.defaultExpectation != nil {
		mmDeleteRoles.mock.t.Fatalf("Default expectation is already set for the RoleCache.DeleteRoles method")
	}

	if len(mmDeleteRoles.expectations) > 0 {
		mmDeleteRoles.mock.t.Fatalf("Some expectations are already set for the RoleCache.DeleteRoles method")
	}

	mmDeleteRoles.mock.funcDeleteRoles = f
	mmDeleteRoles.mock.funcDeleteRolesOrigin = minimock.CallerInfo(1)
	return mmDeleteRoles.mock
}

// When sets expectation for the RoleCache.DeleteRoles which will trigger the result defined by the following
// Then helper
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) When(ctx context.Context, ids []int64) *RoleCacheMockDeleteRolesExpectation {
	if mmDeleteRoles.mock.funcDeleteRoles != nil {
		mmDeleteRoles.mock.t.Fatalf("RoleCacheMock.DeleteRoles mock is already set by Set")
	}

	expectation := &RoleCacheMockDeleteRolesExpectation{
		mock:               mmDeleteRoles.mock,
		params:             &RoleCacheMockDeleteRolesParams{ctx, ids},
		expectationOrigins: RoleCacheMockDeleteRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteRoles.expectations = append(mmDeleteRoles.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.DeleteRoles return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockDeleteRolesExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockDeleteRolesResults{err}
	return e.mock
}

// Times sets number of times RoleCache.DeleteRoles should be invoked
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Times(n uint64) *mRoleCacheMockDeleteRoles {
	if n == 0 {
		mmDeleteRoles.mock.t.Fatalf("Times of RoleCacheMock.DeleteRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteRoles.expectedInvocations, n)
	mmDeleteRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteRoles
}

func (mmDeleteRoles *mRoleCacheMockDeleteRoles) invocationsDone() bool {
	if len(mmDeleteRoles.expectations) == 0 && mmDeleteRoles.defaultExpectation == nil && mmDeleteRoles.mock.funcDeleteRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteRoles.mock.afterDeleteRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteRoles implements mm_repository.RoleCache
func (mmDeleteRoles *RoleCacheMock) DeleteRoles(ctx context.Context, ids []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteRoles.beforeDeleteRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteRoles.afterDeleteRolesCounter, 1)

	mmDeleteRoles.t.Helper()

	if mmDeleteRoles.inspectFuncDeleteRoles != nil {
		mmDeleteRoles.inspectFuncDeleteRoles(ctx, ids)
	}

	mm_params := RoleCacheMockDeleteRolesParams{ctx, ids}

	// Record call args
	mmDeleteRoles.DeleteRolesMock.mutex.Lock()
	mmDeleteRoles.DeleteRolesMock.callArgs = append(mmDeleteRoles.DeleteRolesMock.callArgs, &mm_params)
	mmDeleteRoles.DeleteRolesMock.mutex.Unlock()

	for _, e := range mmDeleteRoles.DeleteRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteRoles.DeleteRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteRoles.DeleteRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteRoles.DeleteRolesMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteRoles.DeleteRolesMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockDeleteRolesParams{ctx, ids}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteRoles.t.Errorf("RoleCacheMock.DeleteRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteRoles.DeleteRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.ids != nil && !minimock.Equal(*mm_want_ptrs.ids, mm_got.ids) {
				mmDeleteRoles.t.Errorf("RoleCacheMock.DeleteRoles got unexpected parameter ids, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteRoles.DeleteRolesMock.defaultExpectation.expectationOrigins.originIds, *mm_want_ptrs.ids, mm_got.ids, minimock.Diff(*mm_want_ptrs.ids, mm_got.ids))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteRoles.t.Errorf("RoleCacheMock.DeleteRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteRoles.DeleteRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteRoles.DeleteRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteRoles.t.Fatal("No results are set for the RoleCacheMock.DeleteRoles")
		}
		return (*mm_results).err
	}
	if mmDeleteRoles.funcDeleteRoles != nil {
		return mmDeleteRoles.funcDeleteRoles(ctx, ids)
	}
	mmDeleteRoles.t.Fatalf("Unexpected call to RoleCacheMock.DeleteRoles. %v %v", ctx, ids)
	return
}

// DeleteRolesAfterCounter returns a count of finished RoleCacheMock.DeleteRoles invocations
func (mmDeleteRoles *RoleCacheMock) DeleteRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteRoles.afterDeleteRolesCounter)
}

// DeleteRolesBeforeCounter returns a count of RoleCacheMock.DeleteRoles invocations
func (mmDeleteRoles *RoleCacheMock) DeleteRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteRoles.beforeDeleteRolesCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.DeleteRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteRoles *mRoleCacheMockDeleteRoles) Calls() []*RoleCacheMockDeleteRolesParams {
	mmDeleteRoles.mutex.RLock()

	argCopy := make([]*RoleCacheMockDeleteRolesParams, len(mmDeleteRoles.callArgs))
	copy(argCopy, mmDeleteRoles.callArgs)

	mmDeleteRoles.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteRolesDone returns true if the count of the DeleteRoles invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockDeleteRolesDone() bool {
	if m.DeleteRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteRolesMock.invocationsDone()
}

// MinimockDeleteRolesInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockDeleteRolesInspect() {
	for _, e := range m.DeleteRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.DeleteRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteRolesCounter := mm_atomic.LoadUint64(&m.afterDeleteRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteRolesMock.defaultExpectation != nil && afterDeleteRolesCounter < 1 {
		if m.DeleteRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.DeleteRoles at\n%s", m.DeleteRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.DeleteRoles at\n%s with params: %#v", m.DeleteRolesMock.defaultExpectation.expectationOrigins.origin, *m.DeleteRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteRoles != nil && afterDeleteRolesCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.DeleteRoles at\n%s", m.funcDeleteRolesOrigin)
	}

	if !m.DeleteRolesMock.invocationsDone() && afterDeleteRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.DeleteRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteRolesMock.expectedInvocations), m.DeleteRolesMock.expectedInvocationsOrigin, afterDeleteRolesCounter)
	}
}

type mRoleCacheMockGetProductRoles struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockGetProductRolesExpectation
	expectations       []*RoleCacheMockGetProductRolesExpectation

	callArgs []*RoleCacheMockGetProductRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockGetProductRolesExpectation specifies expectation struct of the RoleCache.GetProductRoles
type RoleCacheMockGetProductRolesExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockGetProductRolesParams
	paramPtrs          *RoleCacheMockGetProductRolesParamPtrs
	expectationOrigins RoleCacheMockGetProductRolesExpectationOrigins
	results            *RoleCacheMockGetProductRolesResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockGetProductRolesParams contains parameters of the RoleCache.GetProductRoles
type RoleCacheMockGetProductRolesParams struct {
	ctx       context.Context
	productID int64
}

// RoleCacheMockGetProductRolesParamPtrs contains pointers to parameters of the RoleCache.GetProductRoles
type RoleCacheMockGetProductRolesParamPtrs struct {
	ctx       *context.Context
	productID *int64
}

// RoleCacheMockGetProductRolesResults contains results of the RoleCache.GetProductRoles
type RoleCacheMockGetProductRolesResults struct {
	ra1 []roleentity.Role
	err error
}

// RoleCacheMockGetProductRolesOrigins contains origins of expectations of the RoleCache.GetProductRoles
type RoleCacheMockGetProductRolesExpectationOrigins struct {
	origin          string
	originCtx       string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Optional() *mRoleCacheMockGetProductRoles {
	mmGetProductRoles.optional = true
	return mmGetProductRoles
}

// Expect sets up expected params for RoleCache.GetProductRoles
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Expect(ctx context.Context, productID int64) *mRoleCacheMockGetProductRoles {
	if mmGetProductRoles.mock.funcGetProductRoles != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Set")
	}

	if mmGetProductRoles.defaultExpectation == nil {
		mmGetProductRoles.defaultExpectation = &RoleCacheMockGetProductRolesExpectation{}
	}

	if mmGetProductRoles.defaultExpectation.paramPtrs != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by ExpectParams functions")
	}

	mmGetProductRoles.defaultExpectation.params = &RoleCacheMockGetProductRolesParams{ctx, productID}
	mmGetProductRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetProductRoles.expectations {
		if minimock.Equal(e.params, mmGetProductRoles.defaultExpectation.params) {
			mmGetProductRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetProductRoles.defaultExpectation.params)
		}
	}

	return mmGetProductRoles
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.GetProductRoles
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockGetProductRoles {
	if mmGetProductRoles.mock.funcGetProductRoles != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Set")
	}

	if mmGetProductRoles.defaultExpectation == nil {
		mmGetProductRoles.defaultExpectation = &RoleCacheMockGetProductRolesExpectation{}
	}

	if mmGetProductRoles.defaultExpectation.params != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Expect")
	}

	if mmGetProductRoles.defaultExpectation.paramPtrs == nil {
		mmGetProductRoles.defaultExpectation.paramPtrs = &RoleCacheMockGetProductRolesParamPtrs{}
	}
	mmGetProductRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetProductRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetProductRoles
}

// ExpectProductIDParam2 sets up expected param productID for RoleCache.GetProductRoles
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) ExpectProductIDParam2(productID int64) *mRoleCacheMockGetProductRoles {
	if mmGetProductRoles.mock.funcGetProductRoles != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Set")
	}

	if mmGetProductRoles.defaultExpectation == nil {
		mmGetProductRoles.defaultExpectation = &RoleCacheMockGetProductRolesExpectation{}
	}

	if mmGetProductRoles.defaultExpectation.params != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Expect")
	}

	if mmGetProductRoles.defaultExpectation.paramPtrs == nil {
		mmGetProductRoles.defaultExpectation.paramPtrs = &RoleCacheMockGetProductRolesParamPtrs{}
	}
	mmGetProductRoles.defaultExpectation.paramPtrs.productID = &productID
	mmGetProductRoles.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmGetProductRoles
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.GetProductRoles
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Inspect(f func(ctx context.Context, productID int64)) *mRoleCacheMockGetProductRoles {
	if mmGetProductRoles.mock.inspectFuncGetProductRoles != nil {
		mmGetProductRoles.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.GetProductRoles")
	}

	mmGetProductRoles.mock.inspectFuncGetProductRoles = f

	return mmGetProductRoles
}

// Return sets up results that will be returned by RoleCache.GetProductRoles
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Return(ra1 []roleentity.Role, err error) *RoleCacheMock {
	if mmGetProductRoles.mock.funcGetProductRoles != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Set")
	}

	if mmGetProductRoles.defaultExpectation == nil {
		mmGetProductRoles.defaultExpectation = &RoleCacheMockGetProductRolesExpectation{mock: mmGetProductRoles.mock}
	}
	mmGetProductRoles.defaultExpectation.results = &RoleCacheMockGetProductRolesResults{ra1, err}
	mmGetProductRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetProductRoles.mock
}

// Set uses given function f to mock the RoleCache.GetProductRoles method
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Set(f func(ctx context.Context, productID int64) (ra1 []roleentity.Role, err error)) *RoleCacheMock {
	if mmGetProductRoles.defaultExpectation != nil {
		mmGetProductRoles.mock.t.Fatalf("Default expectation is already set for the RoleCache.GetProductRoles method")
	}

	if len(mmGetProductRoles.expectations) > 0 {
		mmGetProductRoles.mock.t.Fatalf("Some expectations are already set for the RoleCache.GetProductRoles method")
	}

	mmGetProductRoles.mock.funcGetProductRoles = f
	mmGetProductRoles.mock.funcGetProductRolesOrigin = minimock.CallerInfo(1)
	return mmGetProductRoles.mock
}

// When sets expectation for the RoleCache.GetProductRoles which will trigger the result defined by the following
// Then helper
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) When(ctx context.Context, productID int64) *RoleCacheMockGetProductRolesExpectation {
	if mmGetProductRoles.mock.funcGetProductRoles != nil {
		mmGetProductRoles.mock.t.Fatalf("RoleCacheMock.GetProductRoles mock is already set by Set")
	}

	expectation := &RoleCacheMockGetProductRolesExpectation{
		mock:               mmGetProductRoles.mock,
		params:             &RoleCacheMockGetProductRolesParams{ctx, productID},
		expectationOrigins: RoleCacheMockGetProductRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetProductRoles.expectations = append(mmGetProductRoles.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.GetProductRoles return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockGetProductRolesExpectation) Then(ra1 []roleentity.Role, err error) *RoleCacheMock {
	e.results = &RoleCacheMockGetProductRolesResults{ra1, err}
	return e.mock
}

// Times sets number of times RoleCache.GetProductRoles should be invoked
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Times(n uint64) *mRoleCacheMockGetProductRoles {
	if n == 0 {
		mmGetProductRoles.mock.t.Fatalf("Times of RoleCacheMock.GetProductRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetProductRoles.expectedInvocations, n)
	mmGetProductRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetProductRoles
}

func (mmGetProductRoles *mRoleCacheMockGetProductRoles) invocationsDone() bool {
	if len(mmGetProductRoles.expectations) == 0 && mmGetProductRoles.defaultExpectation == nil && mmGetProductRoles.mock.funcGetProductRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetProductRoles.mock.afterGetProductRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetProductRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetProductRoles implements mm_repository.RoleCache
func (mmGetProductRoles *RoleCacheMock) GetProductRoles(ctx context.Context, productID int64) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetProductRoles.beforeGetProductRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetProductRoles.afterGetProductRolesCounter, 1)

	mmGetProductRoles.t.Helper()

	if mmGetProductRoles.inspectFuncGetProductRoles != nil {
		mmGetProductRoles.inspectFuncGetProductRoles(ctx, productID)
	}

	mm_params := RoleCacheMockGetProductRolesParams{ctx, productID}

	// Record call args
	mmGetProductRoles.GetProductRolesMock.mutex.Lock()
	mmGetProductRoles.GetProductRolesMock.callArgs = append(mmGetProductRoles.GetProductRolesMock.callArgs, &mm_params)
	mmGetProductRoles.GetProductRolesMock.mutex.Unlock()

	for _, e := range mmGetProductRoles.GetProductRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetProductRoles.GetProductRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetProductRoles.GetProductRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmGetProductRoles.GetProductRolesMock.defaultExpectation.params
		mm_want_ptrs := mmGetProductRoles.GetProductRolesMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockGetProductRolesParams{ctx, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetProductRoles.t.Errorf("RoleCacheMock.GetProductRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProductRoles.GetProductRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmGetProductRoles.t.Errorf("RoleCacheMock.GetProductRoles got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetProductRoles.GetProductRolesMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetProductRoles.t.Errorf("RoleCacheMock.GetProductRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetProductRoles.GetProductRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetProductRoles.GetProductRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetProductRoles.t.Fatal("No results are set for the RoleCacheMock.GetProductRoles")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetProductRoles.funcGetProductRoles != nil {
		return mmGetProductRoles.funcGetProductRoles(ctx, productID)
	}
	mmGetProductRoles.t.Fatalf("Unexpected call to RoleCacheMock.GetProductRoles. %v %v", ctx, productID)
	return
}

// GetProductRolesAfterCounter returns a count of finished RoleCacheMock.GetProductRoles invocations
func (mmGetProductRoles *RoleCacheMock) GetProductRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductRoles.afterGetProductRolesCounter)
}

// GetProductRolesBeforeCounter returns a count of RoleCacheMock.GetProductRoles invocations
func (mmGetProductRoles *RoleCacheMock) GetProductRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetProductRoles.beforeGetProductRolesCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.GetProductRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetProductRoles *mRoleCacheMockGetProductRoles) Calls() []*RoleCacheMockGetProductRolesParams {
	mmGetProductRoles.mutex.RLock()

	argCopy := make([]*RoleCacheMockGetProductRolesParams, len(mmGetProductRoles.callArgs))
	copy(argCopy, mmGetProductRoles.callArgs)

	mmGetProductRoles.mutex.RUnlock()

	return argCopy
}

// MinimockGetProductRolesDone returns true if the count of the GetProductRoles invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockGetProductRolesDone() bool {
	if m.GetProductRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetProductRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetProductRolesMock.invocationsDone()
}

// MinimockGetProductRolesInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockGetProductRolesInspect() {
	for _, e := range m.GetProductRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.GetProductRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetProductRolesCounter := mm_atomic.LoadUint64(&m.afterGetProductRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetProductRolesMock.defaultExpectation != nil && afterGetProductRolesCounter < 1 {
		if m.GetProductRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.GetProductRoles at\n%s", m.GetProductRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.GetProductRoles at\n%s with params: %#v", m.GetProductRolesMock.defaultExpectation.expectationOrigins.origin, *m.GetProductRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetProductRoles != nil && afterGetProductRolesCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.GetProductRoles at\n%s", m.funcGetProductRolesOrigin)
	}

	if !m.GetProductRolesMock.invocationsDone() && afterGetProductRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.GetProductRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetProductRolesMock.expectedInvocations), m.GetProductRolesMock.expectedInvocationsOrigin, afterGetProductRolesCounter)
	}
}

type mRoleCacheMockGetRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockGetRoleExpectation
	expectations       []*RoleCacheMockGetRoleExpectation

	callArgs []*RoleCacheMockGetRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockGetRoleExpectation specifies expectation struct of the RoleCache.GetRole
type RoleCacheMockGetRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockGetRoleParams
	paramPtrs          *RoleCacheMockGetRoleParamPtrs
	expectationOrigins RoleCacheMockGetRoleExpectationOrigins
	results            *RoleCacheMockGetRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockGetRoleParams contains parameters of the RoleCache.GetRole
type RoleCacheMockGetRoleParams struct {
	ctx context.Context
	id  int64
}

// RoleCacheMockGetRoleParamPtrs contains pointers to parameters of the RoleCache.GetRole
type RoleCacheMockGetRoleParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// RoleCacheMockGetRoleResults contains results of the RoleCache.GetRole
type RoleCacheMockGetRoleResults struct {
	r1  roleentity.Role
	err error
}

// RoleCacheMockGetRoleOrigins contains origins of expectations of the RoleCache.GetRole
type RoleCacheMockGetRoleExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRole *mRoleCacheMockGetRole) Optional() *mRoleCacheMockGetRole {
	mmGetRole.optional = true
	return mmGetRole
}

// Expect sets up expected params for RoleCache.GetRole
func (mmGetRole *mRoleCacheMockGetRole) Expect(ctx context.Context, id int64) *mRoleCacheMockGetRole {
	if mmGetRole.mock.funcGetRole != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Set")
	}

	if mmGetRole.defaultExpectation == nil {
		mmGetRole.defaultExpectation = &RoleCacheMockGetRoleExpectation{}
	}

	if mmGetRole.defaultExpectation.paramPtrs != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by ExpectParams functions")
	}

	mmGetRole.defaultExpectation.params = &RoleCacheMockGetRoleParams{ctx, id}
	mmGetRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetRole.expectations {
		if minimock.Equal(e.params, mmGetRole.defaultExpectation.params) {
			mmGetRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetRole.defaultExpectation.params)
		}
	}

	return mmGetRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.GetRole
func (mmGetRole *mRoleCacheMockGetRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockGetRole {
	if mmGetRole.mock.funcGetRole != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Set")
	}

	if mmGetRole.defaultExpectation == nil {
		mmGetRole.defaultExpectation = &RoleCacheMockGetRoleExpectation{}
	}

	if mmGetRole.defaultExpectation.params != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Expect")
	}

	if mmGetRole.defaultExpectation.paramPtrs == nil {
		mmGetRole.defaultExpectation.paramPtrs = &RoleCacheMockGetRoleParamPtrs{}
	}
	mmGetRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetRole
}

// ExpectIdParam2 sets up expected param id for RoleCache.GetRole
func (mmGetRole *mRoleCacheMockGetRole) ExpectIdParam2(id int64) *mRoleCacheMockGetRole {
	if mmGetRole.mock.funcGetRole != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Set")
	}

	if mmGetRole.defaultExpectation == nil {
		mmGetRole.defaultExpectation = &RoleCacheMockGetRoleExpectation{}
	}

	if mmGetRole.defaultExpectation.params != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Expect")
	}

	if mmGetRole.defaultExpectation.paramPtrs == nil {
		mmGetRole.defaultExpectation.paramPtrs = &RoleCacheMockGetRoleParamPtrs{}
	}
	mmGetRole.defaultExpectation.paramPtrs.id = &id
	mmGetRole.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.GetRole
func (mmGetRole *mRoleCacheMockGetRole) Inspect(f func(ctx context.Context, id int64)) *mRoleCacheMockGetRole {
	if mmGetRole.mock.inspectFuncGetRole != nil {
		mmGetRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.GetRole")
	}

	mmGetRole.mock.inspectFuncGetRole = f

	return mmGetRole
}

// Return sets up results that will be returned by RoleCache.GetRole
func (mmGetRole *mRoleCacheMockGetRole) Return(r1 roleentity.Role, err error) *RoleCacheMock {
	if mmGetRole.mock.funcGetRole != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Set")
	}

	if mmGetRole.defaultExpectation == nil {
		mmGetRole.defaultExpectation = &RoleCacheMockGetRoleExpectation{mock: mmGetRole.mock}
	}
	mmGetRole.defaultExpectation.results = &RoleCacheMockGetRoleResults{r1, err}
	mmGetRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRole.mock
}

// Set uses given function f to mock the RoleCache.GetRole method
func (mmGetRole *mRoleCacheMockGetRole) Set(f func(ctx context.Context, id int64) (r1 roleentity.Role, err error)) *RoleCacheMock {
	if mmGetRole.defaultExpectation != nil {
		mmGetRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.GetRole method")
	}

	if len(mmGetRole.expectations) > 0 {
		mmGetRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.GetRole method")
	}

	mmGetRole.mock.funcGetRole = f
	mmGetRole.mock.funcGetRoleOrigin = minimock.CallerInfo(1)
	return mmGetRole.mock
}

// When sets expectation for the RoleCache.GetRole which will trigger the result defined by the following
// Then helper
func (mmGetRole *mRoleCacheMockGetRole) When(ctx context.Context, id int64) *RoleCacheMockGetRoleExpectation {
	if mmGetRole.mock.funcGetRole != nil {
		mmGetRole.mock.t.Fatalf("RoleCacheMock.GetRole mock is already set by Set")
	}

	expectation := &RoleCacheMockGetRoleExpectation{
		mock:               mmGetRole.mock,
		params:             &RoleCacheMockGetRoleParams{ctx, id},
		expectationOrigins: RoleCacheMockGetRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetRole.expectations = append(mmGetRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.GetRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockGetRoleExpectation) Then(r1 roleentity.Role, err error) *RoleCacheMock {
	e.results = &RoleCacheMockGetRoleResults{r1, err}
	return e.mock
}

// Times sets number of times RoleCache.GetRole should be invoked
func (mmGetRole *mRoleCacheMockGetRole) Times(n uint64) *mRoleCacheMockGetRole {
	if n == 0 {
		mmGetRole.mock.t.Fatalf("Times of RoleCacheMock.GetRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRole.expectedInvocations, n)
	mmGetRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRole
}

func (mmGetRole *mRoleCacheMockGetRole) invocationsDone() bool {
	if len(mmGetRole.expectations) == 0 && mmGetRole.defaultExpectation == nil && mmGetRole.mock.funcGetRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRole.mock.afterGetRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRole implements mm_repository.RoleCache
func (mmGetRole *RoleCacheMock) GetRole(ctx context.Context, id int64) (r1 roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetRole.beforeGetRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRole.afterGetRoleCounter, 1)

	mmGetRole.t.Helper()

	if mmGetRole.inspectFuncGetRole != nil {
		mmGetRole.inspectFuncGetRole(ctx, id)
	}

	mm_params := RoleCacheMockGetRoleParams{ctx, id}

	// Record call args
	mmGetRole.GetRoleMock.mutex.Lock()
	mmGetRole.GetRoleMock.callArgs = append(mmGetRole.GetRoleMock.callArgs, &mm_params)
	mmGetRole.GetRoleMock.mutex.Unlock()

	for _, e := range mmGetRole.GetRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.r1, e.results.err
		}
	}

	if mmGetRole.GetRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRole.GetRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmGetRole.GetRoleMock.defaultExpectation.params
		mm_want_ptrs := mmGetRole.GetRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockGetRoleParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetRole.t.Errorf("RoleCacheMock.GetRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRole.GetRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetRole.t.Errorf("RoleCacheMock.GetRole got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRole.GetRoleMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetRole.t.Errorf("RoleCacheMock.GetRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetRole.GetRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetRole.GetRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRole.t.Fatal("No results are set for the RoleCacheMock.GetRole")
		}
		return (*mm_results).r1, (*mm_results).err
	}
	if mmGetRole.funcGetRole != nil {
		return mmGetRole.funcGetRole(ctx, id)
	}
	mmGetRole.t.Fatalf("Unexpected call to RoleCacheMock.GetRole. %v %v", ctx, id)
	return
}

// GetRoleAfterCounter returns a count of finished RoleCacheMock.GetRole invocations
func (mmGetRole *RoleCacheMock) GetRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRole.afterGetRoleCounter)
}

// GetRoleBeforeCounter returns a count of RoleCacheMock.GetRole invocations
func (mmGetRole *RoleCacheMock) GetRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRole.beforeGetRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.GetRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetRole *mRoleCacheMockGetRole) Calls() []*RoleCacheMockGetRoleParams {
	mmGetRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockGetRoleParams, len(mmGetRole.callArgs))
	copy(argCopy, mmGetRole.callArgs)

	mmGetRole.mutex.RUnlock()

	return argCopy
}

// MinimockGetRoleDone returns true if the count of the GetRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockGetRoleDone() bool {
	if m.GetRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRoleMock.invocationsDone()
}

// MinimockGetRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockGetRoleInspect() {
	for _, e := range m.GetRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.GetRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetRoleCounter := mm_atomic.LoadUint64(&m.afterGetRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRoleMock.defaultExpectation != nil && afterGetRoleCounter < 1 {
		if m.GetRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.GetRole at\n%s", m.GetRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.GetRole at\n%s with params: %#v", m.GetRoleMock.defaultExpectation.expectationOrigins.origin, *m.GetRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRole != nil && afterGetRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.GetRole at\n%s", m.funcGetRoleOrigin)
	}

	if !m.GetRoleMock.invocationsDone() && afterGetRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.GetRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRoleMock.expectedInvocations), m.GetRoleMock.expectedInvocationsOrigin, afterGetRoleCounter)
	}
}

type mRoleCacheMockGetRoles struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockGetRolesExpectation
	expectations       []*RoleCacheMockGetRolesExpectation

	callArgs []*RoleCacheMockGetRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockGetRolesExpectation specifies expectation struct of the RoleCache.GetRoles
type RoleCacheMockGetRolesExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockGetRolesParams
	paramPtrs          *RoleCacheMockGetRolesParamPtrs
	expectationOrigins RoleCacheMockGetRolesExpectationOrigins
	results            *RoleCacheMockGetRolesResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockGetRolesParams contains parameters of the RoleCache.GetRoles
type RoleCacheMockGetRolesParams struct {
	ctx context.Context
}

// RoleCacheMockGetRolesParamPtrs contains pointers to parameters of the RoleCache.GetRoles
type RoleCacheMockGetRolesParamPtrs struct {
	ctx *context.Context
}

// RoleCacheMockGetRolesResults contains results of the RoleCache.GetRoles
type RoleCacheMockGetRolesResults struct {
	ra1 []roleentity.Role
	err error
}

// RoleCacheMockGetRolesOrigins contains origins of expectations of the RoleCache.GetRoles
type RoleCacheMockGetRolesExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRoles *mRoleCacheMockGetRoles) Optional() *mRoleCacheMockGetRoles {
	mmGetRoles.optional = true
	return mmGetRoles
}

// Expect sets up expected params for RoleCache.GetRoles
func (mmGetRoles *mRoleCacheMockGetRoles) Expect(ctx context.Context) *mRoleCacheMockGetRoles {
	if mmGetRoles.mock.funcGetRoles != nil {
		mmGetRoles.mock.t.Fatalf("RoleCacheMock.GetRoles mock is already set by Set")
	}

	if mmGetRoles.defaultExpectation == nil {
		mmGetRoles.defaultExpectation = &RoleCacheMockGetRolesExpectation{}
	}

	if mmGetRoles.defaultExpectation.paramPtrs != nil {
		mmGetRoles.mock.t.Fatalf("RoleCacheMock.GetRoles mock is already set by ExpectParams functions")
	}

	mmGetRoles.defaultExpectation.params = &RoleCacheMockGetRolesParams{ctx}
	mmGetRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetRoles.expectations {
		if minimock.Equal(e.params, mmGetRoles.defaultExpectation.params) {
			mmGetRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetRoles.defaultExpectation.params)
		}
	}

	return mmGetRoles
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.GetRoles
func (mmGetRoles *mRoleCacheMockGetRoles) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockGetRoles {
	if mmGetRoles.mock.funcGetRoles != nil {
		mmGetRoles.mock.t.Fatalf("RoleCacheMock.GetRoles mock is already set by Set")
	}

	if mmGetRoles.defaultExpectation == nil {
		mmGetRoles.defaultExpectation = &RoleCacheMockGetRolesExpectation{}
	}

	if mmGetRoles.defaultExpectation.params != nil {
		mmGetRoles.mock.t.Fatalf("RoleCacheMock.GetRoles mock is already set by Expect")
	}

	if mmGetRoles.defaultExpectation.paramPtrs == nil {
		mmGetRoles.defaultExpectation.paramPtrs = &RoleCacheMockGetRolesParamPtrs{}
	}
	mmGetRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetRoles
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.GetRoles
func (mmGetRoles *mRoleCacheMockGetRoles) Inspect(f func(ctx context.Context)) *mRoleCacheMockGetRoles {
	if mmGetRoles.mock.inspectFuncGetRoles != nil {
		mmGetRoles.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.GetRoles")
	}

	mmGetRoles.mock.inspectFuncGetRoles = f

	return mmGetRoles
}

// Return sets up results that will be returned by RoleCache.GetRoles
func (mmGetRoles *mRoleCacheMockGetRoles) Return(ra1 []roleentity.Role, err error) *RoleCacheMock {
	if mmGetRoles.mock.funcGetRoles != nil {
		mmGetRoles.mock.t.Fatalf("RoleCacheMock.GetRoles mock is already set by Set")
	}

	if mmGetRoles.defaultExpectation == nil {
		mmGetRoles.defaultExpectation = &RoleCacheMockGetRolesExpectation{mock: mmGetRoles.mock}
	}
	mmGetRoles.defaultExpectation.results = &RoleCacheMockGetRolesResults{ra1, err}
	mmGetRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRoles.mock
}

// Set uses given function f to mock the RoleCache.GetRoles method
func (mmGetRoles *mRoleCacheMockGetRoles) Set(f func(ctx context.Context) (ra1 []roleentity.Role, err error)) *RoleCacheMock {
	if mmGetRoles.defaultExpectation != nil {
		mmGetRoles.mock.t.Fatalf("Default expectation is already set for the RoleCache.GetRoles method")
	}

	if len(mmGetRoles.expectations) > 0 {
		mmGetRoles.mock.t.Fatalf("Some expectations are already set for the RoleCache.GetRoles method")
	}

	mmGetRoles.mock.funcGetRoles = f
	mmGetRoles.mock.funcGetRolesOrigin = minimock.CallerInfo(1)
	return mmGetRoles.mock
}

// When sets expectation for the RoleCache.GetRoles which will trigger the result defined by the following
// Then helper
func (mmGetRoles *mRoleCacheMockGetRoles) When(ctx context.Context) *RoleCacheMockGetRolesExpectation {
	if mmGetRoles.mock.funcGetRoles != nil {
		mmGetRoles.mock.t.Fatalf("RoleCacheMock.GetRoles mock is already set by Set")
	}

	expectation := &RoleCacheMockGetRolesExpectation{
		mock:               mmGetRoles.mock,
		params:             &RoleCacheMockGetRolesParams{ctx},
		expectationOrigins: RoleCacheMockGetRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetRoles.expectations = append(mmGetRoles.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.GetRoles return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockGetRolesExpectation) Then(ra1 []roleentity.Role, err error) *RoleCacheMock {
	e.results = &RoleCacheMockGetRolesResults{ra1, err}
	return e.mock
}

// Times sets number of times RoleCache.GetRoles should be invoked
func (mmGetRoles *mRoleCacheMockGetRoles) Times(n uint64) *mRoleCacheMockGetRoles {
	if n == 0 {
		mmGetRoles.mock.t.Fatalf("Times of RoleCacheMock.GetRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRoles.expectedInvocations, n)
	mmGetRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRoles
}

func (mmGetRoles *mRoleCacheMockGetRoles) invocationsDone() bool {
	if len(mmGetRoles.expectations) == 0 && mmGetRoles.defaultExpectation == nil && mmGetRoles.mock.funcGetRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRoles.mock.afterGetRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRoles implements mm_repository.RoleCache
func (mmGetRoles *RoleCacheMock) GetRoles(ctx context.Context) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetRoles.beforeGetRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRoles.afterGetRolesCounter, 1)

	mmGetRoles.t.Helper()

	if mmGetRoles.inspectFuncGetRoles != nil {
		mmGetRoles.inspectFuncGetRoles(ctx)
	}

	mm_params := RoleCacheMockGetRolesParams{ctx}

	// Record call args
	mmGetRoles.GetRolesMock.mutex.Lock()
	mmGetRoles.GetRolesMock.callArgs = append(mmGetRoles.GetRolesMock.callArgs, &mm_params)
	mmGetRoles.GetRolesMock.mutex.Unlock()

	for _, e := range mmGetRoles.GetRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetRoles.GetRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRoles.GetRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmGetRoles.GetRolesMock.defaultExpectation.params
		mm_want_ptrs := mmGetRoles.GetRolesMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockGetRolesParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetRoles.t.Errorf("RoleCacheMock.GetRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetRoles.GetRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetRoles.t.Errorf("RoleCacheMock.GetRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetRoles.GetRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetRoles.GetRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRoles.t.Fatal("No results are set for the RoleCacheMock.GetRoles")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetRoles.funcGetRoles != nil {
		return mmGetRoles.funcGetRoles(ctx)
	}
	mmGetRoles.t.Fatalf("Unexpected call to RoleCacheMock.GetRoles. %v", ctx)
	return
}

// GetRolesAfterCounter returns a count of finished RoleCacheMock.GetRoles invocations
func (mmGetRoles *RoleCacheMock) GetRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRoles.afterGetRolesCounter)
}

// GetRolesBeforeCounter returns a count of RoleCacheMock.GetRoles invocations
func (mmGetRoles *RoleCacheMock) GetRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRoles.beforeGetRolesCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.GetRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetRoles *mRoleCacheMockGetRoles) Calls() []*RoleCacheMockGetRolesParams {
	mmGetRoles.mutex.RLock()

	argCopy := make([]*RoleCacheMockGetRolesParams, len(mmGetRoles.callArgs))
	copy(argCopy, mmGetRoles.callArgs)

	mmGetRoles.mutex.RUnlock()

	return argCopy
}

// MinimockGetRolesDone returns true if the count of the GetRoles invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockGetRolesDone() bool {
	if m.GetRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRolesMock.invocationsDone()
}

// MinimockGetRolesInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockGetRolesInspect() {
	for _, e := range m.GetRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.GetRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetRolesCounter := mm_atomic.LoadUint64(&m.afterGetRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRolesMock.defaultExpectation != nil && afterGetRolesCounter < 1 {
		if m.GetRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.GetRoles at\n%s", m.GetRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.GetRoles at\n%s with params: %#v", m.GetRolesMock.defaultExpectation.expectationOrigins.origin, *m.GetRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRoles != nil && afterGetRolesCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.GetRoles at\n%s", m.funcGetRolesOrigin)
	}

	if !m.GetRolesMock.invocationsDone() && afterGetRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.GetRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRolesMock.expectedInvocations), m.GetRolesMock.expectedInvocationsOrigin, afterGetRolesCounter)
	}
}

type mRoleCacheMockGetUserRoleIDs struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockGetUserRoleIDsExpectation
	expectations       []*RoleCacheMockGetUserRoleIDsExpectation

	callArgs []*RoleCacheMockGetUserRoleIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockGetUserRoleIDsExpectation specifies expectation struct of the RoleCache.GetUserRoleIDs
type RoleCacheMockGetUserRoleIDsExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockGetUserRoleIDsParams
	paramPtrs          *RoleCacheMockGetUserRoleIDsParamPtrs
	expectationOrigins RoleCacheMockGetUserRoleIDsExpectationOrigins
	results            *RoleCacheMockGetUserRoleIDsResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockGetUserRoleIDsParams contains parameters of the RoleCache.GetUserRoleIDs
type RoleCacheMockGetUserRoleIDsParams struct {
	ctx    context.Context
	userID int64
}

// RoleCacheMockGetUserRoleIDsParamPtrs contains pointers to parameters of the RoleCache.GetUserRoleIDs
type RoleCacheMockGetUserRoleIDsParamPtrs struct {
	ctx    *context.Context
	userID *int64
}

// RoleCacheMockGetUserRoleIDsResults contains results of the RoleCache.GetUserRoleIDs
type RoleCacheMockGetUserRoleIDsResults struct {
	ia1 []int64
	err error
}

// RoleCacheMockGetUserRoleIDsOrigins contains origins of expectations of the RoleCache.GetUserRoleIDs
type RoleCacheMockGetUserRoleIDsExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Optional() *mRoleCacheMockGetUserRoleIDs {
	mmGetUserRoleIDs.optional = true
	return mmGetUserRoleIDs
}

// Expect sets up expected params for RoleCache.GetUserRoleIDs
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Expect(ctx context.Context, userID int64) *mRoleCacheMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &RoleCacheMockGetUserRoleIDsExpectation{}
	}

	if mmGetUserRoleIDs.defaultExpectation.paramPtrs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by ExpectParams functions")
	}

	mmGetUserRoleIDs.defaultExpectation.params = &RoleCacheMockGetUserRoleIDsParams{ctx, userID}
	mmGetUserRoleIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserRoleIDs.expectations {
		if minimock.Equal(e.params, mmGetUserRoleIDs.defaultExpectation.params) {
			mmGetUserRoleIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserRoleIDs.defaultExpectation.params)
		}
	}

	return mmGetUserRoleIDs
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.GetUserRoleIDs
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &RoleCacheMockGetUserRoleIDsExpectation{}
	}

	if mmGetUserRoleIDs.defaultExpectation.params != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Expect")
	}

	if mmGetUserRoleIDs.defaultExpectation.paramPtrs == nil {
		mmGetUserRoleIDs.defaultExpectation.paramPtrs = &RoleCacheMockGetUserRoleIDsParamPtrs{}
	}
	mmGetUserRoleIDs.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetUserRoleIDs.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetUserRoleIDs
}

// ExpectUserIDParam2 sets up expected param userID for RoleCache.GetUserRoleIDs
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) ExpectUserIDParam2(userID int64) *mRoleCacheMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &RoleCacheMockGetUserRoleIDsExpectation{}
	}

	if mmGetUserRoleIDs.defaultExpectation.params != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Expect")
	}

	if mmGetUserRoleIDs.defaultExpectation.paramPtrs == nil {
		mmGetUserRoleIDs.defaultExpectation.paramPtrs = &RoleCacheMockGetUserRoleIDsParamPtrs{}
	}
	mmGetUserRoleIDs.defaultExpectation.paramPtrs.userID = &userID
	mmGetUserRoleIDs.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUserRoleIDs
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.GetUserRoleIDs
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Inspect(f func(ctx context.Context, userID int64)) *mRoleCacheMockGetUserRoleIDs {
	if mmGetUserRoleIDs.mock.inspectFuncGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.GetUserRoleIDs")
	}

	mmGetUserRoleIDs.mock.inspectFuncGetUserRoleIDs = f

	return mmGetUserRoleIDs
}

// Return sets up results that will be returned by RoleCache.GetUserRoleIDs
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Return(ia1 []int64, err error) *RoleCacheMock {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Set")
	}

	if mmGetUserRoleIDs.defaultExpectation == nil {
		mmGetUserRoleIDs.defaultExpectation = &RoleCacheMockGetUserRoleIDsExpectation{mock: mmGetUserRoleIDs.mock}
	}
	mmGetUserRoleIDs.defaultExpectation.results = &RoleCacheMockGetUserRoleIDsResults{ia1, err}
	mmGetUserRoleIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserRoleIDs.mock
}

// Set uses given function f to mock the RoleCache.GetUserRoleIDs method
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Set(f func(ctx context.Context, userID int64) (ia1 []int64, err error)) *RoleCacheMock {
	if mmGetUserRoleIDs.defaultExpectation != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("Default expectation is already set for the RoleCache.GetUserRoleIDs method")
	}

	if len(mmGetUserRoleIDs.expectations) > 0 {
		mmGetUserRoleIDs.mock.t.Fatalf("Some expectations are already set for the RoleCache.GetUserRoleIDs method")
	}

	mmGetUserRoleIDs.mock.funcGetUserRoleIDs = f
	mmGetUserRoleIDs.mock.funcGetUserRoleIDsOrigin = minimock.CallerInfo(1)
	return mmGetUserRoleIDs.mock
}

// When sets expectation for the RoleCache.GetUserRoleIDs which will trigger the result defined by the following
// Then helper
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) When(ctx context.Context, userID int64) *RoleCacheMockGetUserRoleIDsExpectation {
	if mmGetUserRoleIDs.mock.funcGetUserRoleIDs != nil {
		mmGetUserRoleIDs.mock.t.Fatalf("RoleCacheMock.GetUserRoleIDs mock is already set by Set")
	}

	expectation := &RoleCacheMockGetUserRoleIDsExpectation{
		mock:               mmGetUserRoleIDs.mock,
		params:             &RoleCacheMockGetUserRoleIDsParams{ctx, userID},
		expectationOrigins: RoleCacheMockGetUserRoleIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserRoleIDs.expectations = append(mmGetUserRoleIDs.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.GetUserRoleIDs return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockGetUserRoleIDsExpectation) Then(ia1 []int64, err error) *RoleCacheMock {
	e.results = &RoleCacheMockGetUserRoleIDsResults{ia1, err}
	return e.mock
}

// Times sets number of times RoleCache.GetUserRoleIDs should be invoked
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Times(n uint64) *mRoleCacheMockGetUserRoleIDs {
	if n == 0 {
		mmGetUserRoleIDs.mock.t.Fatalf("Times of RoleCacheMock.GetUserRoleIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserRoleIDs.expectedInvocations, n)
	mmGetUserRoleIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserRoleIDs
}

func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) invocationsDone() bool {
	if len(mmGetUserRoleIDs.expectations) == 0 && mmGetUserRoleIDs.defaultExpectation == nil && mmGetUserRoleIDs.mock.funcGetUserRoleIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserRoleIDs.mock.afterGetUserRoleIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserRoleIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserRoleIDs implements mm_repository.RoleCache
func (mmGetUserRoleIDs *RoleCacheMock) GetUserRoleIDs(ctx context.Context, userID int64) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmGetUserRoleIDs.beforeGetUserRoleIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserRoleIDs.afterGetUserRoleIDsCounter, 1)

	mmGetUserRoleIDs.t.Helper()

	if mmGetUserRoleIDs.inspectFuncGetUserRoleIDs != nil {
		mmGetUserRoleIDs.inspectFuncGetUserRoleIDs(ctx, userID)
	}

	mm_params := RoleCacheMockGetUserRoleIDsParams{ctx, userID}

	// Record call args
	mmGetUserRoleIDs.GetUserRoleIDsMock.mutex.Lock()
	mmGetUserRoleIDs.GetUserRoleIDsMock.callArgs = append(mmGetUserRoleIDs.GetUserRoleIDsMock.callArgs, &mm_params)
	mmGetUserRoleIDs.GetUserRoleIDsMock.mutex.Unlock()

	for _, e := range mmGetUserRoleIDs.GetUserRoleIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockGetUserRoleIDsParams{ctx, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetUserRoleIDs.t.Errorf("RoleCacheMock.GetUserRoleIDs got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUserRoleIDs.t.Errorf("RoleCacheMock.GetUserRoleIDs got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserRoleIDs.t.Errorf("RoleCacheMock.GetUserRoleIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserRoleIDs.GetUserRoleIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserRoleIDs.t.Fatal("No results are set for the RoleCacheMock.GetUserRoleIDs")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmGetUserRoleIDs.funcGetUserRoleIDs != nil {
		return mmGetUserRoleIDs.funcGetUserRoleIDs(ctx, userID)
	}
	mmGetUserRoleIDs.t.Fatalf("Unexpected call to RoleCacheMock.GetUserRoleIDs. %v %v", ctx, userID)
	return
}

// GetUserRoleIDsAfterCounter returns a count of finished RoleCacheMock.GetUserRoleIDs invocations
func (mmGetUserRoleIDs *RoleCacheMock) GetUserRoleIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRoleIDs.afterGetUserRoleIDsCounter)
}

// GetUserRoleIDsBeforeCounter returns a count of RoleCacheMock.GetUserRoleIDs invocations
func (mmGetUserRoleIDs *RoleCacheMock) GetUserRoleIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRoleIDs.beforeGetUserRoleIDsCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.GetUserRoleIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserRoleIDs *mRoleCacheMockGetUserRoleIDs) Calls() []*RoleCacheMockGetUserRoleIDsParams {
	mmGetUserRoleIDs.mutex.RLock()

	argCopy := make([]*RoleCacheMockGetUserRoleIDsParams, len(mmGetUserRoleIDs.callArgs))
	copy(argCopy, mmGetUserRoleIDs.callArgs)

	mmGetUserRoleIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserRoleIDsDone returns true if the count of the GetUserRoleIDs invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockGetUserRoleIDsDone() bool {
	if m.GetUserRoleIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserRoleIDsMock.invocationsDone()
}

// MinimockGetUserRoleIDsInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockGetUserRoleIDsInspect() {
	for _, e := range m.GetUserRoleIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.GetUserRoleIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserRoleIDsCounter := mm_atomic.LoadUint64(&m.afterGetUserRoleIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserRoleIDsMock.defaultExpectation != nil && afterGetUserRoleIDsCounter < 1 {
		if m.GetUserRoleIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.GetUserRoleIDs at\n%s", m.GetUserRoleIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.GetUserRoleIDs at\n%s with params: %#v", m.GetUserRoleIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetUserRoleIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserRoleIDs != nil && afterGetUserRoleIDsCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.GetUserRoleIDs at\n%s", m.funcGetUserRoleIDsOrigin)
	}

	if !m.GetUserRoleIDsMock.invocationsDone() && afterGetUserRoleIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.GetUserRoleIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserRoleIDsMock.expectedInvocations), m.GetUserRoleIDsMock.expectedInvocationsOrigin, afterGetUserRoleIDsCounter)
	}
}

type mRoleCacheMockGetUserRoles struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockGetUserRolesExpectation
	expectations       []*RoleCacheMockGetUserRolesExpectation

	callArgs []*RoleCacheMockGetUserRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockGetUserRolesExpectation specifies expectation struct of the RoleCache.GetUserRoles
type RoleCacheMockGetUserRolesExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockGetUserRolesParams
	paramPtrs          *RoleCacheMockGetUserRolesParamPtrs
	expectationOrigins RoleCacheMockGetUserRolesExpectationOrigins
	results            *RoleCacheMockGetUserRolesResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockGetUserRolesParams contains parameters of the RoleCache.GetUserRoles
type RoleCacheMockGetUserRolesParams struct {
	ctx    context.Context
	userID int64
}

// RoleCacheMockGetUserRolesParamPtrs contains pointers to parameters of the RoleCache.GetUserRoles
type RoleCacheMockGetUserRolesParamPtrs struct {
	ctx    *context.Context
	userID *int64
}

// RoleCacheMockGetUserRolesResults contains results of the RoleCache.GetUserRoles
type RoleCacheMockGetUserRolesResults struct {
	ra1 []roleentity.Role
	err error
}

// RoleCacheMockGetUserRolesOrigins contains origins of expectations of the RoleCache.GetUserRoles
type RoleCacheMockGetUserRolesExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Optional() *mRoleCacheMockGetUserRoles {
	mmGetUserRoles.optional = true
	return mmGetUserRoles
}

// Expect sets up expected params for RoleCache.GetUserRoles
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Expect(ctx context.Context, userID int64) *mRoleCacheMockGetUserRoles {
	if mmGetUserRoles.mock.funcGetUserRoles != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Set")
	}

	if mmGetUserRoles.defaultExpectation == nil {
		mmGetUserRoles.defaultExpectation = &RoleCacheMockGetUserRolesExpectation{}
	}

	if mmGetUserRoles.defaultExpectation.paramPtrs != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by ExpectParams functions")
	}

	mmGetUserRoles.defaultExpectation.params = &RoleCacheMockGetUserRolesParams{ctx, userID}
	mmGetUserRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUserRoles.expectations {
		if minimock.Equal(e.params, mmGetUserRoles.defaultExpectation.params) {
			mmGetUserRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUserRoles.defaultExpectation.params)
		}
	}

	return mmGetUserRoles
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.GetUserRoles
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockGetUserRoles {
	if mmGetUserRoles.mock.funcGetUserRoles != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Set")
	}

	if mmGetUserRoles.defaultExpectation == nil {
		mmGetUserRoles.defaultExpectation = &RoleCacheMockGetUserRolesExpectation{}
	}

	if mmGetUserRoles.defaultExpectation.params != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Expect")
	}

	if mmGetUserRoles.defaultExpectation.paramPtrs == nil {
		mmGetUserRoles.defaultExpectation.paramPtrs = &RoleCacheMockGetUserRolesParamPtrs{}
	}
	mmGetUserRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetUserRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetUserRoles
}

// ExpectUserIDParam2 sets up expected param userID for RoleCache.GetUserRoles
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) ExpectUserIDParam2(userID int64) *mRoleCacheMockGetUserRoles {
	if mmGetUserRoles.mock.funcGetUserRoles != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Set")
	}

	if mmGetUserRoles.defaultExpectation == nil {
		mmGetUserRoles.defaultExpectation = &RoleCacheMockGetUserRolesExpectation{}
	}

	if mmGetUserRoles.defaultExpectation.params != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Expect")
	}

	if mmGetUserRoles.defaultExpectation.paramPtrs == nil {
		mmGetUserRoles.defaultExpectation.paramPtrs = &RoleCacheMockGetUserRolesParamPtrs{}
	}
	mmGetUserRoles.defaultExpectation.paramPtrs.userID = &userID
	mmGetUserRoles.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetUserRoles
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.GetUserRoles
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Inspect(f func(ctx context.Context, userID int64)) *mRoleCacheMockGetUserRoles {
	if mmGetUserRoles.mock.inspectFuncGetUserRoles != nil {
		mmGetUserRoles.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.GetUserRoles")
	}

	mmGetUserRoles.mock.inspectFuncGetUserRoles = f

	return mmGetUserRoles
}

// Return sets up results that will be returned by RoleCache.GetUserRoles
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Return(ra1 []roleentity.Role, err error) *RoleCacheMock {
	if mmGetUserRoles.mock.funcGetUserRoles != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Set")
	}

	if mmGetUserRoles.defaultExpectation == nil {
		mmGetUserRoles.defaultExpectation = &RoleCacheMockGetUserRolesExpectation{mock: mmGetUserRoles.mock}
	}
	mmGetUserRoles.defaultExpectation.results = &RoleCacheMockGetUserRolesResults{ra1, err}
	mmGetUserRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUserRoles.mock
}

// Set uses given function f to mock the RoleCache.GetUserRoles method
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Set(f func(ctx context.Context, userID int64) (ra1 []roleentity.Role, err error)) *RoleCacheMock {
	if mmGetUserRoles.defaultExpectation != nil {
		mmGetUserRoles.mock.t.Fatalf("Default expectation is already set for the RoleCache.GetUserRoles method")
	}

	if len(mmGetUserRoles.expectations) > 0 {
		mmGetUserRoles.mock.t.Fatalf("Some expectations are already set for the RoleCache.GetUserRoles method")
	}

	mmGetUserRoles.mock.funcGetUserRoles = f
	mmGetUserRoles.mock.funcGetUserRolesOrigin = minimock.CallerInfo(1)
	return mmGetUserRoles.mock
}

// When sets expectation for the RoleCache.GetUserRoles which will trigger the result defined by the following
// Then helper
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) When(ctx context.Context, userID int64) *RoleCacheMockGetUserRolesExpectation {
	if mmGetUserRoles.mock.funcGetUserRoles != nil {
		mmGetUserRoles.mock.t.Fatalf("RoleCacheMock.GetUserRoles mock is already set by Set")
	}

	expectation := &RoleCacheMockGetUserRolesExpectation{
		mock:               mmGetUserRoles.mock,
		params:             &RoleCacheMockGetUserRolesParams{ctx, userID},
		expectationOrigins: RoleCacheMockGetUserRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUserRoles.expectations = append(mmGetUserRoles.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.GetUserRoles return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockGetUserRolesExpectation) Then(ra1 []roleentity.Role, err error) *RoleCacheMock {
	e.results = &RoleCacheMockGetUserRolesResults{ra1, err}
	return e.mock
}

// Times sets number of times RoleCache.GetUserRoles should be invoked
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Times(n uint64) *mRoleCacheMockGetUserRoles {
	if n == 0 {
		mmGetUserRoles.mock.t.Fatalf("Times of RoleCacheMock.GetUserRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUserRoles.expectedInvocations, n)
	mmGetUserRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUserRoles
}

func (mmGetUserRoles *mRoleCacheMockGetUserRoles) invocationsDone() bool {
	if len(mmGetUserRoles.expectations) == 0 && mmGetUserRoles.defaultExpectation == nil && mmGetUserRoles.mock.funcGetUserRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUserRoles.mock.afterGetUserRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUserRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUserRoles implements mm_repository.RoleCache
func (mmGetUserRoles *RoleCacheMock) GetUserRoles(ctx context.Context, userID int64) (ra1 []roleentity.Role, err error) {
	mm_atomic.AddUint64(&mmGetUserRoles.beforeGetUserRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUserRoles.afterGetUserRolesCounter, 1)

	mmGetUserRoles.t.Helper()

	if mmGetUserRoles.inspectFuncGetUserRoles != nil {
		mmGetUserRoles.inspectFuncGetUserRoles(ctx, userID)
	}

	mm_params := RoleCacheMockGetUserRolesParams{ctx, userID}

	// Record call args
	mmGetUserRoles.GetUserRolesMock.mutex.Lock()
	mmGetUserRoles.GetUserRolesMock.callArgs = append(mmGetUserRoles.GetUserRolesMock.callArgs, &mm_params)
	mmGetUserRoles.GetUserRolesMock.mutex.Unlock()

	for _, e := range mmGetUserRoles.GetUserRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ra1, e.results.err
		}
	}

	if mmGetUserRoles.GetUserRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUserRoles.GetUserRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUserRoles.GetUserRolesMock.defaultExpectation.params
		mm_want_ptrs := mmGetUserRoles.GetUserRolesMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockGetUserRolesParams{ctx, userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetUserRoles.t.Errorf("RoleCacheMock.GetUserRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRoles.GetUserRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetUserRoles.t.Errorf("RoleCacheMock.GetUserRoles got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUserRoles.GetUserRolesMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUserRoles.t.Errorf("RoleCacheMock.GetUserRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUserRoles.GetUserRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUserRoles.GetUserRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUserRoles.t.Fatal("No results are set for the RoleCacheMock.GetUserRoles")
		}
		return (*mm_results).ra1, (*mm_results).err
	}
	if mmGetUserRoles.funcGetUserRoles != nil {
		return mmGetUserRoles.funcGetUserRoles(ctx, userID)
	}
	mmGetUserRoles.t.Fatalf("Unexpected call to RoleCacheMock.GetUserRoles. %v %v", ctx, userID)
	return
}

// GetUserRolesAfterCounter returns a count of finished RoleCacheMock.GetUserRoles invocations
func (mmGetUserRoles *RoleCacheMock) GetUserRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRoles.afterGetUserRolesCounter)
}

// GetUserRolesBeforeCounter returns a count of RoleCacheMock.GetUserRoles invocations
func (mmGetUserRoles *RoleCacheMock) GetUserRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUserRoles.beforeGetUserRolesCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.GetUserRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUserRoles *mRoleCacheMockGetUserRoles) Calls() []*RoleCacheMockGetUserRolesParams {
	mmGetUserRoles.mutex.RLock()

	argCopy := make([]*RoleCacheMockGetUserRolesParams, len(mmGetUserRoles.callArgs))
	copy(argCopy, mmGetUserRoles.callArgs)

	mmGetUserRoles.mutex.RUnlock()

	return argCopy
}

// MinimockGetUserRolesDone returns true if the count of the GetUserRoles invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockGetUserRolesDone() bool {
	if m.GetUserRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUserRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUserRolesMock.invocationsDone()
}

// MinimockGetUserRolesInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockGetUserRolesInspect() {
	for _, e := range m.GetUserRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.GetUserRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUserRolesCounter := mm_atomic.LoadUint64(&m.afterGetUserRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUserRolesMock.defaultExpectation != nil && afterGetUserRolesCounter < 1 {
		if m.GetUserRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.GetUserRoles at\n%s", m.GetUserRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.GetUserRoles at\n%s with params: %#v", m.GetUserRolesMock.defaultExpectation.expectationOrigins.origin, *m.GetUserRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUserRoles != nil && afterGetUserRolesCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.GetUserRoles at\n%s", m.funcGetUserRolesOrigin)
	}

	if !m.GetUserRolesMock.invocationsDone() && afterGetUserRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.GetUserRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUserRolesMock.expectedInvocations), m.GetUserRolesMock.expectedInvocationsOrigin, afterGetUserRolesCounter)
	}
}

type mRoleCacheMockRemoveProductRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockRemoveProductRoleExpectation
	expectations       []*RoleCacheMockRemoveProductRoleExpectation

	callArgs []*RoleCacheMockRemoveProductRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockRemoveProductRoleExpectation specifies expectation struct of the RoleCache.RemoveProductRole
type RoleCacheMockRemoveProductRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockRemoveProductRoleParams
	paramPtrs          *RoleCacheMockRemoveProductRoleParamPtrs
	expectationOrigins RoleCacheMockRemoveProductRoleExpectationOrigins
	results            *RoleCacheMockRemoveProductRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockRemoveProductRoleParams contains parameters of the RoleCache.RemoveProductRole
type RoleCacheMockRemoveProductRoleParams struct {
	ctx       context.Context
	roleID    int64
	productID int64
}

// RoleCacheMockRemoveProductRoleParamPtrs contains pointers to parameters of the RoleCache.RemoveProductRole
type RoleCacheMockRemoveProductRoleParamPtrs struct {
	ctx       *context.Context
	roleID    *int64
	productID *int64
}

// RoleCacheMockRemoveProductRoleResults contains results of the RoleCache.RemoveProductRole
type RoleCacheMockRemoveProductRoleResults struct {
	err error
}

// RoleCacheMockRemoveProductRoleOrigins contains origins of expectations of the RoleCache.RemoveProductRole
type RoleCacheMockRemoveProductRoleExpectationOrigins struct {
	origin          string
	originCtx       string
	originRoleID    string
	originProductID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Optional() *mRoleCacheMockRemoveProductRole {
	mmRemoveProductRole.optional = true
	return mmRemoveProductRole
}

// Expect sets up expected params for RoleCache.RemoveProductRole
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Expect(ctx context.Context, roleID int64, productID int64) *mRoleCacheMockRemoveProductRole {
	if mmRemoveProductRole.mock.funcRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Set")
	}

	if mmRemoveProductRole.defaultExpectation == nil {
		mmRemoveProductRole.defaultExpectation = &RoleCacheMockRemoveProductRoleExpectation{}
	}

	if mmRemoveProductRole.defaultExpectation.paramPtrs != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by ExpectParams functions")
	}

	mmRemoveProductRole.defaultExpectation.params = &RoleCacheMockRemoveProductRoleParams{ctx, roleID, productID}
	mmRemoveProductRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmRemoveProductRole.expectations {
		if minimock.Equal(e.params, mmRemoveProductRole.defaultExpectation.params) {
			mmRemoveProductRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmRemoveProductRole.defaultExpectation.params)
		}
	}

	return mmRemoveProductRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.RemoveProductRole
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockRemoveProductRole {
	if mmRemoveProductRole.mock.funcRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Set")
	}

	if mmRemoveProductRole.defaultExpectation == nil {
		mmRemoveProductRole.defaultExpectation = &RoleCacheMockRemoveProductRoleExpectation{}
	}

	if mmRemoveProductRole.defaultExpectation.params != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Expect")
	}

	if mmRemoveProductRole.defaultExpectation.paramPtrs == nil {
		mmRemoveProductRole.defaultExpectation.paramPtrs = &RoleCacheMockRemoveProductRoleParamPtrs{}
	}
	mmRemoveProductRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmRemoveProductRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmRemoveProductRole
}

// ExpectRoleIDParam2 sets up expected param roleID for RoleCache.RemoveProductRole
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) ExpectRoleIDParam2(roleID int64) *mRoleCacheMockRemoveProductRole {
	if mmRemoveProductRole.mock.funcRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Set")
	}

	if mmRemoveProductRole.defaultExpectation == nil {
		mmRemoveProductRole.defaultExpectation = &RoleCacheMockRemoveProductRoleExpectation{}
	}

	if mmRemoveProductRole.defaultExpectation.params != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Expect")
	}

	if mmRemoveProductRole.defaultExpectation.paramPtrs == nil {
		mmRemoveProductRole.defaultExpectation.paramPtrs = &RoleCacheMockRemoveProductRoleParamPtrs{}
	}
	mmRemoveProductRole.defaultExpectation.paramPtrs.roleID = &roleID
	mmRemoveProductRole.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmRemoveProductRole
}

// ExpectProductIDParam3 sets up expected param productID for RoleCache.RemoveProductRole
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) ExpectProductIDParam3(productID int64) *mRoleCacheMockRemoveProductRole {
	if mmRemoveProductRole.mock.funcRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Set")
	}

	if mmRemoveProductRole.defaultExpectation == nil {
		mmRemoveProductRole.defaultExpectation = &RoleCacheMockRemoveProductRoleExpectation{}
	}

	if mmRemoveProductRole.defaultExpectation.params != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Expect")
	}

	if mmRemoveProductRole.defaultExpectation.paramPtrs == nil {
		mmRemoveProductRole.defaultExpectation.paramPtrs = &RoleCacheMockRemoveProductRoleParamPtrs{}
	}
	mmRemoveProductRole.defaultExpectation.paramPtrs.productID = &productID
	mmRemoveProductRole.defaultExpectation.expectationOrigins.originProductID = minimock.CallerInfo(1)

	return mmRemoveProductRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.RemoveProductRole
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Inspect(f func(ctx context.Context, roleID int64, productID int64)) *mRoleCacheMockRemoveProductRole {
	if mmRemoveProductRole.mock.inspectFuncRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.RemoveProductRole")
	}

	mmRemoveProductRole.mock.inspectFuncRemoveProductRole = f

	return mmRemoveProductRole
}

// Return sets up results that will be returned by RoleCache.RemoveProductRole
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Return(err error) *RoleCacheMock {
	if mmRemoveProductRole.mock.funcRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Set")
	}

	if mmRemoveProductRole.defaultExpectation == nil {
		mmRemoveProductRole.defaultExpectation = &RoleCacheMockRemoveProductRoleExpectation{mock: mmRemoveProductRole.mock}
	}
	mmRemoveProductRole.defaultExpectation.results = &RoleCacheMockRemoveProductRoleResults{err}
	mmRemoveProductRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmRemoveProductRole.mock
}

// Set uses given function f to mock the RoleCache.RemoveProductRole method
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Set(f func(ctx context.Context, roleID int64, productID int64) (err error)) *RoleCacheMock {
	if mmRemoveProductRole.defaultExpectation != nil {
		mmRemoveProductRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.RemoveProductRole method")
	}

	if len(mmRemoveProductRole.expectations) > 0 {
		mmRemoveProductRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.RemoveProductRole method")
	}

	mmRemoveProductRole.mock.funcRemoveProductRole = f
	mmRemoveProductRole.mock.funcRemoveProductRoleOrigin = minimock.CallerInfo(1)
	return mmRemoveProductRole.mock
}

// When sets expectation for the RoleCache.RemoveProductRole which will trigger the result defined by the following
// Then helper
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) When(ctx context.Context, roleID int64, productID int64) *RoleCacheMockRemoveProductRoleExpectation {
	if mmRemoveProductRole.mock.funcRemoveProductRole != nil {
		mmRemoveProductRole.mock.t.Fatalf("RoleCacheMock.RemoveProductRole mock is already set by Set")
	}

	expectation := &RoleCacheMockRemoveProductRoleExpectation{
		mock:               mmRemoveProductRole.mock,
		params:             &RoleCacheMockRemoveProductRoleParams{ctx, roleID, productID},
		expectationOrigins: RoleCacheMockRemoveProductRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmRemoveProductRole.expectations = append(mmRemoveProductRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.RemoveProductRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockRemoveProductRoleExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockRemoveProductRoleResults{err}
	return e.mock
}

// Times sets number of times RoleCache.RemoveProductRole should be invoked
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Times(n uint64) *mRoleCacheMockRemoveProductRole {
	if n == 0 {
		mmRemoveProductRole.mock.t.Fatalf("Times of RoleCacheMock.RemoveProductRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmRemoveProductRole.expectedInvocations, n)
	mmRemoveProductRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmRemoveProductRole
}

func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) invocationsDone() bool {
	if len(mmRemoveProductRole.expectations) == 0 && mmRemoveProductRole.defaultExpectation == nil && mmRemoveProductRole.mock.funcRemoveProductRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmRemoveProductRole.mock.afterRemoveProductRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmRemoveProductRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// RemoveProductRole implements mm_repository.RoleCache
func (mmRemoveProductRole *RoleCacheMock) RemoveProductRole(ctx context.Context, roleID int64, productID int64) (err error) {
	mm_atomic.AddUint64(&mmRemoveProductRole.beforeRemoveProductRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmRemoveProductRole.afterRemoveProductRoleCounter, 1)

	mmRemoveProductRole.t.Helper()

	if mmRemoveProductRole.inspectFuncRemoveProductRole != nil {
		mmRemoveProductRole.inspectFuncRemoveProductRole(ctx, roleID, productID)
	}

	mm_params := RoleCacheMockRemoveProductRoleParams{ctx, roleID, productID}

	// Record call args
	mmRemoveProductRole.RemoveProductRoleMock.mutex.Lock()
	mmRemoveProductRole.RemoveProductRoleMock.callArgs = append(mmRemoveProductRole.RemoveProductRoleMock.callArgs, &mm_params)
	mmRemoveProductRole.RemoveProductRoleMock.mutex.Unlock()

	for _, e := range mmRemoveProductRole.RemoveProductRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.params
		mm_want_ptrs := mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockRemoveProductRoleParams{ctx, roleID, productID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmRemoveProductRole.t.Errorf("RoleCacheMock.RemoveProductRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmRemoveProductRole.t.Errorf("RoleCacheMock.RemoveProductRole got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

			if mm_want_ptrs.productID != nil && !minimock.Equal(*mm_want_ptrs.productID, mm_got.productID) {
				mmRemoveProductRole.t.Errorf("RoleCacheMock.RemoveProductRole got unexpected parameter productID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.expectationOrigins.originProductID, *mm_want_ptrs.productID, mm_got.productID, minimock.Diff(*mm_want_ptrs.productID, mm_got.productID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmRemoveProductRole.t.Errorf("RoleCacheMock.RemoveProductRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmRemoveProductRole.RemoveProductRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmRemoveProductRole.t.Fatal("No results are set for the RoleCacheMock.RemoveProductRole")
		}
		return (*mm_results).err
	}
	if mmRemoveProductRole.funcRemoveProductRole != nil {
		return mmRemoveProductRole.funcRemoveProductRole(ctx, roleID, productID)
	}
	mmRemoveProductRole.t.Fatalf("Unexpected call to RoleCacheMock.RemoveProductRole. %v %v %v", ctx, roleID, productID)
	return
}

// RemoveProductRoleAfterCounter returns a count of finished RoleCacheMock.RemoveProductRole invocations
func (mmRemoveProductRole *RoleCacheMock) RemoveProductRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveProductRole.afterRemoveProductRoleCounter)
}

// RemoveProductRoleBeforeCounter returns a count of RoleCacheMock.RemoveProductRole invocations
func (mmRemoveProductRole *RoleCacheMock) RemoveProductRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveProductRole.beforeRemoveProductRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.RemoveProductRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmRemoveProductRole *mRoleCacheMockRemoveProductRole) Calls() []*RoleCacheMockRemoveProductRoleParams {
	mmRemoveProductRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockRemoveProductRoleParams, len(mmRemoveProductRole.callArgs))
	copy(argCopy, mmRemoveProductRole.callArgs)

	mmRemoveProductRole.mutex.RUnlock()

	return argCopy
}

// MinimockRemoveProductRoleDone returns true if the count of the RemoveProductRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockRemoveProductRoleDone() bool {
	if m.RemoveProductRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.RemoveProductRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.RemoveProductRoleMock.invocationsDone()
}

// MinimockRemoveProductRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockRemoveProductRoleInspect() {
	for _, e := range m.RemoveProductRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.RemoveProductRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterRemoveProductRoleCounter := mm_atomic.LoadUint64(&m.afterRemoveProductRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.RemoveProductRoleMock.defaultExpectation != nil && afterRemoveProductRoleCounter < 1 {
		if m.RemoveProductRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.RemoveProductRole at\n%s", m.RemoveProductRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.RemoveProductRole at\n%s with params: %#v", m.RemoveProductRoleMock.defaultExpectation.expectationOrigins.origin, *m.RemoveProductRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcRemoveProductRole != nil && afterRemoveProductRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.RemoveProductRole at\n%s", m.funcRemoveProductRoleOrigin)
	}

	if !m.RemoveProductRoleMock.invocationsDone() && afterRemoveProductRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.RemoveProductRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.RemoveProductRoleMock.expectedInvocations), m.RemoveProductRoleMock.expectedInvocationsOrigin, afterRemoveProductRoleCounter)
	}
}

type mRoleCacheMockRemoveUserRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockRemoveUserRoleExpectation
	expectations       []*RoleCacheMockRemoveUserRoleExpectation

	callArgs []*RoleCacheMockRemoveUserRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockRemoveUserRoleExpectation specifies expectation struct of the RoleCache.RemoveUserRole
type RoleCacheMockRemoveUserRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockRemoveUserRoleParams
	paramPtrs          *RoleCacheMockRemoveUserRoleParamPtrs
	expectationOrigins RoleCacheMockRemoveUserRoleExpectationOrigins
	results            *RoleCacheMockRemoveUserRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockRemoveUserRoleParams contains parameters of the RoleCache.RemoveUserRole
type RoleCacheMockRemoveUserRoleParams struct {
	ctx    context.Context
	userID int64
	roleID int64
}

// RoleCacheMockRemoveUserRoleParamPtrs contains pointers to parameters of the RoleCache.RemoveUserRole
type RoleCacheMockRemoveUserRoleParamPtrs struct {
	ctx    *context.Context
	userID *int64
	roleID *int64
}

// RoleCacheMockRemoveUserRoleResults contains results of the RoleCache.RemoveUserRole
type RoleCacheMockRemoveUserRoleResults struct {
	err error
}

// RoleCacheMockRemoveUserRoleOrigins contains origins of expectations of the RoleCache.RemoveUserRole
type RoleCacheMockRemoveUserRoleExpectationOrigins struct {
	origin       string
	originCtx    string
	originUserID string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Optional() *mRoleCacheMockRemoveUserRole {
	mmRemoveUserRole.optional = true
	return mmRemoveUserRole
}

// Expect sets up expected params for RoleCache.RemoveUserRole
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Expect(ctx context.Context, userID int64, roleID int64) *mRoleCacheMockRemoveUserRole {
	if mmRemoveUserRole.mock.funcRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Set")
	}

	if mmRemoveUserRole.defaultExpectation == nil {
		mmRemoveUserRole.defaultExpectation = &RoleCacheMockRemoveUserRoleExpectation{}
	}

	if mmRemoveUserRole.defaultExpectation.paramPtrs != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by ExpectParams functions")
	}

	mmRemoveUserRole.defaultExpectation.params = &RoleCacheMockRemoveUserRoleParams{ctx, userID, roleID}
	mmRemoveUserRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmRemoveUserRole.expectations {
		if minimock.Equal(e.params, mmRemoveUserRole.defaultExpectation.params) {
			mmRemoveUserRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmRemoveUserRole.defaultExpectation.params)
		}
	}

	return mmRemoveUserRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.RemoveUserRole
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockRemoveUserRole {
	if mmRemoveUserRole.mock.funcRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Set")
	}

	if mmRemoveUserRole.defaultExpectation == nil {
		mmRemoveUserRole.defaultExpectation = &RoleCacheMockRemoveUserRoleExpectation{}
	}

	if mmRemoveUserRole.defaultExpectation.params != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Expect")
	}

	if mmRemoveUserRole.defaultExpectation.paramPtrs == nil {
		mmRemoveUserRole.defaultExpectation.paramPtrs = &RoleCacheMockRemoveUserRoleParamPtrs{}
	}
	mmRemoveUserRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmRemoveUserRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmRemoveUserRole
}

// ExpectUserIDParam2 sets up expected param userID for RoleCache.RemoveUserRole
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) ExpectUserIDParam2(userID int64) *mRoleCacheMockRemoveUserRole {
	if mmRemoveUserRole.mock.funcRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Set")
	}

	if mmRemoveUserRole.defaultExpectation == nil {
		mmRemoveUserRole.defaultExpectation = &RoleCacheMockRemoveUserRoleExpectation{}
	}

	if mmRemoveUserRole.defaultExpectation.params != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Expect")
	}

	if mmRemoveUserRole.defaultExpectation.paramPtrs == nil {
		mmRemoveUserRole.defaultExpectation.paramPtrs = &RoleCacheMockRemoveUserRoleParamPtrs{}
	}
	mmRemoveUserRole.defaultExpectation.paramPtrs.userID = &userID
	mmRemoveUserRole.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmRemoveUserRole
}

// ExpectRoleIDParam3 sets up expected param roleID for RoleCache.RemoveUserRole
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) ExpectRoleIDParam3(roleID int64) *mRoleCacheMockRemoveUserRole {
	if mmRemoveUserRole.mock.funcRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Set")
	}

	if mmRemoveUserRole.defaultExpectation == nil {
		mmRemoveUserRole.defaultExpectation = &RoleCacheMockRemoveUserRoleExpectation{}
	}

	if mmRemoveUserRole.defaultExpectation.params != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Expect")
	}

	if mmRemoveUserRole.defaultExpectation.paramPtrs == nil {
		mmRemoveUserRole.defaultExpectation.paramPtrs = &RoleCacheMockRemoveUserRoleParamPtrs{}
	}
	mmRemoveUserRole.defaultExpectation.paramPtrs.roleID = &roleID
	mmRemoveUserRole.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmRemoveUserRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.RemoveUserRole
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Inspect(f func(ctx context.Context, userID int64, roleID int64)) *mRoleCacheMockRemoveUserRole {
	if mmRemoveUserRole.mock.inspectFuncRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.RemoveUserRole")
	}

	mmRemoveUserRole.mock.inspectFuncRemoveUserRole = f

	return mmRemoveUserRole
}

// Return sets up results that will be returned by RoleCache.RemoveUserRole
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Return(err error) *RoleCacheMock {
	if mmRemoveUserRole.mock.funcRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Set")
	}

	if mmRemoveUserRole.defaultExpectation == nil {
		mmRemoveUserRole.defaultExpectation = &RoleCacheMockRemoveUserRoleExpectation{mock: mmRemoveUserRole.mock}
	}
	mmRemoveUserRole.defaultExpectation.results = &RoleCacheMockRemoveUserRoleResults{err}
	mmRemoveUserRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmRemoveUserRole.mock
}

// Set uses given function f to mock the RoleCache.RemoveUserRole method
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Set(f func(ctx context.Context, userID int64, roleID int64) (err error)) *RoleCacheMock {
	if mmRemoveUserRole.defaultExpectation != nil {
		mmRemoveUserRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.RemoveUserRole method")
	}

	if len(mmRemoveUserRole.expectations) > 0 {
		mmRemoveUserRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.RemoveUserRole method")
	}

	mmRemoveUserRole.mock.funcRemoveUserRole = f
	mmRemoveUserRole.mock.funcRemoveUserRoleOrigin = minimock.CallerInfo(1)
	return mmRemoveUserRole.mock
}

// When sets expectation for the RoleCache.RemoveUserRole which will trigger the result defined by the following
// Then helper
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) When(ctx context.Context, userID int64, roleID int64) *RoleCacheMockRemoveUserRoleExpectation {
	if mmRemoveUserRole.mock.funcRemoveUserRole != nil {
		mmRemoveUserRole.mock.t.Fatalf("RoleCacheMock.RemoveUserRole mock is already set by Set")
	}

	expectation := &RoleCacheMockRemoveUserRoleExpectation{
		mock:               mmRemoveUserRole.mock,
		params:             &RoleCacheMockRemoveUserRoleParams{ctx, userID, roleID},
		expectationOrigins: RoleCacheMockRemoveUserRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmRemoveUserRole.expectations = append(mmRemoveUserRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.RemoveUserRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockRemoveUserRoleExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockRemoveUserRoleResults{err}
	return e.mock
}

// Times sets number of times RoleCache.RemoveUserRole should be invoked
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Times(n uint64) *mRoleCacheMockRemoveUserRole {
	if n == 0 {
		mmRemoveUserRole.mock.t.Fatalf("Times of RoleCacheMock.RemoveUserRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmRemoveUserRole.expectedInvocations, n)
	mmRemoveUserRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmRemoveUserRole
}

func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) invocationsDone() bool {
	if len(mmRemoveUserRole.expectations) == 0 && mmRemoveUserRole.defaultExpectation == nil && mmRemoveUserRole.mock.funcRemoveUserRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmRemoveUserRole.mock.afterRemoveUserRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmRemoveUserRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// RemoveUserRole implements mm_repository.RoleCache
func (mmRemoveUserRole *RoleCacheMock) RemoveUserRole(ctx context.Context, userID int64, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmRemoveUserRole.beforeRemoveUserRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmRemoveUserRole.afterRemoveUserRoleCounter, 1)

	mmRemoveUserRole.t.Helper()

	if mmRemoveUserRole.inspectFuncRemoveUserRole != nil {
		mmRemoveUserRole.inspectFuncRemoveUserRole(ctx, userID, roleID)
	}

	mm_params := RoleCacheMockRemoveUserRoleParams{ctx, userID, roleID}

	// Record call args
	mmRemoveUserRole.RemoveUserRoleMock.mutex.Lock()
	mmRemoveUserRole.RemoveUserRoleMock.callArgs = append(mmRemoveUserRole.RemoveUserRoleMock.callArgs, &mm_params)
	mmRemoveUserRole.RemoveUserRoleMock.mutex.Unlock()

	for _, e := range mmRemoveUserRole.RemoveUserRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.params
		mm_want_ptrs := mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockRemoveUserRoleParams{ctx, userID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmRemoveUserRole.t.Errorf("RoleCacheMock.RemoveUserRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmRemoveUserRole.t.Errorf("RoleCacheMock.RemoveUserRole got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmRemoveUserRole.t.Errorf("RoleCacheMock.RemoveUserRole got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmRemoveUserRole.t.Errorf("RoleCacheMock.RemoveUserRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmRemoveUserRole.RemoveUserRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmRemoveUserRole.t.Fatal("No results are set for the RoleCacheMock.RemoveUserRole")
		}
		return (*mm_results).err
	}
	if mmRemoveUserRole.funcRemoveUserRole != nil {
		return mmRemoveUserRole.funcRemoveUserRole(ctx, userID, roleID)
	}
	mmRemoveUserRole.t.Fatalf("Unexpected call to RoleCacheMock.RemoveUserRole. %v %v %v", ctx, userID, roleID)
	return
}

// RemoveUserRoleAfterCounter returns a count of finished RoleCacheMock.RemoveUserRole invocations
func (mmRemoveUserRole *RoleCacheMock) RemoveUserRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveUserRole.afterRemoveUserRoleCounter)
}

// RemoveUserRoleBeforeCounter returns a count of RoleCacheMock.RemoveUserRole invocations
func (mmRemoveUserRole *RoleCacheMock) RemoveUserRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmRemoveUserRole.beforeRemoveUserRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.RemoveUserRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmRemoveUserRole *mRoleCacheMockRemoveUserRole) Calls() []*RoleCacheMockRemoveUserRoleParams {
	mmRemoveUserRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockRemoveUserRoleParams, len(mmRemoveUserRole.callArgs))
	copy(argCopy, mmRemoveUserRole.callArgs)

	mmRemoveUserRole.mutex.RUnlock()

	return argCopy
}

// MinimockRemoveUserRoleDone returns true if the count of the RemoveUserRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockRemoveUserRoleDone() bool {
	if m.RemoveUserRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.RemoveUserRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.RemoveUserRoleMock.invocationsDone()
}

// MinimockRemoveUserRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockRemoveUserRoleInspect() {
	for _, e := range m.RemoveUserRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.RemoveUserRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterRemoveUserRoleCounter := mm_atomic.LoadUint64(&m.afterRemoveUserRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.RemoveUserRoleMock.defaultExpectation != nil && afterRemoveUserRoleCounter < 1 {
		if m.RemoveUserRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.RemoveUserRole at\n%s", m.RemoveUserRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.RemoveUserRole at\n%s with params: %#v", m.RemoveUserRoleMock.defaultExpectation.expectationOrigins.origin, *m.RemoveUserRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcRemoveUserRole != nil && afterRemoveUserRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.RemoveUserRole at\n%s", m.funcRemoveUserRoleOrigin)
	}

	if !m.RemoveUserRoleMock.invocationsDone() && afterRemoveUserRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.RemoveUserRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.RemoveUserRoleMock.expectedInvocations), m.RemoveUserRoleMock.expectedInvocationsOrigin, afterRemoveUserRoleCounter)
	}
}

type mRoleCacheMockSetRole struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockSetRoleExpectation
	expectations       []*RoleCacheMockSetRoleExpectation

	callArgs []*RoleCacheMockSetRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockSetRoleExpectation specifies expectation struct of the RoleCache.SetRole
type RoleCacheMockSetRoleExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockSetRoleParams
	paramPtrs          *RoleCacheMockSetRoleParamPtrs
	expectationOrigins RoleCacheMockSetRoleExpectationOrigins
	results            *RoleCacheMockSetRoleResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockSetRoleParams contains parameters of the RoleCache.SetRole
type RoleCacheMockSetRoleParams struct {
	ctx  context.Context
	role roleentity.Role
}

// RoleCacheMockSetRoleParamPtrs contains pointers to parameters of the RoleCache.SetRole
type RoleCacheMockSetRoleParamPtrs struct {
	ctx  *context.Context
	role *roleentity.Role
}

// RoleCacheMockSetRoleResults contains results of the RoleCache.SetRole
type RoleCacheMockSetRoleResults struct {
	err error
}

// RoleCacheMockSetRoleOrigins contains origins of expectations of the RoleCache.SetRole
type RoleCacheMockSetRoleExpectationOrigins struct {
	origin     string
	originCtx  string
	originRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetRole *mRoleCacheMockSetRole) Optional() *mRoleCacheMockSetRole {
	mmSetRole.optional = true
	return mmSetRole
}

// Expect sets up expected params for RoleCache.SetRole
func (mmSetRole *mRoleCacheMockSetRole) Expect(ctx context.Context, role roleentity.Role) *mRoleCacheMockSetRole {
	if mmSetRole.mock.funcSetRole != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Set")
	}

	if mmSetRole.defaultExpectation == nil {
		mmSetRole.defaultExpectation = &RoleCacheMockSetRoleExpectation{}
	}

	if mmSetRole.defaultExpectation.paramPtrs != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by ExpectParams functions")
	}

	mmSetRole.defaultExpectation.params = &RoleCacheMockSetRoleParams{ctx, role}
	mmSetRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetRole.expectations {
		if minimock.Equal(e.params, mmSetRole.defaultExpectation.params) {
			mmSetRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetRole.defaultExpectation.params)
		}
	}

	return mmSetRole
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.SetRole
func (mmSetRole *mRoleCacheMockSetRole) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockSetRole {
	if mmSetRole.mock.funcSetRole != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Set")
	}

	if mmSetRole.defaultExpectation == nil {
		mmSetRole.defaultExpectation = &RoleCacheMockSetRoleExpectation{}
	}

	if mmSetRole.defaultExpectation.params != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Expect")
	}

	if mmSetRole.defaultExpectation.paramPtrs == nil {
		mmSetRole.defaultExpectation.paramPtrs = &RoleCacheMockSetRoleParamPtrs{}
	}
	mmSetRole.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetRole.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetRole
}

// ExpectRoleParam2 sets up expected param role for RoleCache.SetRole
func (mmSetRole *mRoleCacheMockSetRole) ExpectRoleParam2(role roleentity.Role) *mRoleCacheMockSetRole {
	if mmSetRole.mock.funcSetRole != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Set")
	}

	if mmSetRole.defaultExpectation == nil {
		mmSetRole.defaultExpectation = &RoleCacheMockSetRoleExpectation{}
	}

	if mmSetRole.defaultExpectation.params != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Expect")
	}

	if mmSetRole.defaultExpectation.paramPtrs == nil {
		mmSetRole.defaultExpectation.paramPtrs = &RoleCacheMockSetRoleParamPtrs{}
	}
	mmSetRole.defaultExpectation.paramPtrs.role = &role
	mmSetRole.defaultExpectation.expectationOrigins.originRole = minimock.CallerInfo(1)

	return mmSetRole
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.SetRole
func (mmSetRole *mRoleCacheMockSetRole) Inspect(f func(ctx context.Context, role roleentity.Role)) *mRoleCacheMockSetRole {
	if mmSetRole.mock.inspectFuncSetRole != nil {
		mmSetRole.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.SetRole")
	}

	mmSetRole.mock.inspectFuncSetRole = f

	return mmSetRole
}

// Return sets up results that will be returned by RoleCache.SetRole
func (mmSetRole *mRoleCacheMockSetRole) Return(err error) *RoleCacheMock {
	if mmSetRole.mock.funcSetRole != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Set")
	}

	if mmSetRole.defaultExpectation == nil {
		mmSetRole.defaultExpectation = &RoleCacheMockSetRoleExpectation{mock: mmSetRole.mock}
	}
	mmSetRole.defaultExpectation.results = &RoleCacheMockSetRoleResults{err}
	mmSetRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetRole.mock
}

// Set uses given function f to mock the RoleCache.SetRole method
func (mmSetRole *mRoleCacheMockSetRole) Set(f func(ctx context.Context, role roleentity.Role) (err error)) *RoleCacheMock {
	if mmSetRole.defaultExpectation != nil {
		mmSetRole.mock.t.Fatalf("Default expectation is already set for the RoleCache.SetRole method")
	}

	if len(mmSetRole.expectations) > 0 {
		mmSetRole.mock.t.Fatalf("Some expectations are already set for the RoleCache.SetRole method")
	}

	mmSetRole.mock.funcSetRole = f
	mmSetRole.mock.funcSetRoleOrigin = minimock.CallerInfo(1)
	return mmSetRole.mock
}

// When sets expectation for the RoleCache.SetRole which will trigger the result defined by the following
// Then helper
func (mmSetRole *mRoleCacheMockSetRole) When(ctx context.Context, role roleentity.Role) *RoleCacheMockSetRoleExpectation {
	if mmSetRole.mock.funcSetRole != nil {
		mmSetRole.mock.t.Fatalf("RoleCacheMock.SetRole mock is already set by Set")
	}

	expectation := &RoleCacheMockSetRoleExpectation{
		mock:               mmSetRole.mock,
		params:             &RoleCacheMockSetRoleParams{ctx, role},
		expectationOrigins: RoleCacheMockSetRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetRole.expectations = append(mmSetRole.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.SetRole return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockSetRoleExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockSetRoleResults{err}
	return e.mock
}

// Times sets number of times RoleCache.SetRole should be invoked
func (mmSetRole *mRoleCacheMockSetRole) Times(n uint64) *mRoleCacheMockSetRole {
	if n == 0 {
		mmSetRole.mock.t.Fatalf("Times of RoleCacheMock.SetRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetRole.expectedInvocations, n)
	mmSetRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetRole
}

func (mmSetRole *mRoleCacheMockSetRole) invocationsDone() bool {
	if len(mmSetRole.expectations) == 0 && mmSetRole.defaultExpectation == nil && mmSetRole.mock.funcSetRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetRole.mock.afterSetRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetRole implements mm_repository.RoleCache
func (mmSetRole *RoleCacheMock) SetRole(ctx context.Context, role roleentity.Role) (err error) {
	mm_atomic.AddUint64(&mmSetRole.beforeSetRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmSetRole.afterSetRoleCounter, 1)

	mmSetRole.t.Helper()

	if mmSetRole.inspectFuncSetRole != nil {
		mmSetRole.inspectFuncSetRole(ctx, role)
	}

	mm_params := RoleCacheMockSetRoleParams{ctx, role}

	// Record call args
	mmSetRole.SetRoleMock.mutex.Lock()
	mmSetRole.SetRoleMock.callArgs = append(mmSetRole.SetRoleMock.callArgs, &mm_params)
	mmSetRole.SetRoleMock.mutex.Unlock()

	for _, e := range mmSetRole.SetRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetRole.SetRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetRole.SetRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmSetRole.SetRoleMock.defaultExpectation.params
		mm_want_ptrs := mmSetRole.SetRoleMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockSetRoleParams{ctx, role}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetRole.t.Errorf("RoleCacheMock.SetRole got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetRole.SetRoleMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.role != nil && !minimock.Equal(*mm_want_ptrs.role, mm_got.role) {
				mmSetRole.t.Errorf("RoleCacheMock.SetRole got unexpected parameter role, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetRole.SetRoleMock.defaultExpectation.expectationOrigins.originRole, *mm_want_ptrs.role, mm_got.role, minimock.Diff(*mm_want_ptrs.role, mm_got.role))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetRole.t.Errorf("RoleCacheMock.SetRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetRole.SetRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetRole.SetRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmSetRole.t.Fatal("No results are set for the RoleCacheMock.SetRole")
		}
		return (*mm_results).err
	}
	if mmSetRole.funcSetRole != nil {
		return mmSetRole.funcSetRole(ctx, role)
	}
	mmSetRole.t.Fatalf("Unexpected call to RoleCacheMock.SetRole. %v %v", ctx, role)
	return
}

// SetRoleAfterCounter returns a count of finished RoleCacheMock.SetRole invocations
func (mmSetRole *RoleCacheMock) SetRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetRole.afterSetRoleCounter)
}

// SetRoleBeforeCounter returns a count of RoleCacheMock.SetRole invocations
func (mmSetRole *RoleCacheMock) SetRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetRole.beforeSetRoleCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.SetRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetRole *mRoleCacheMockSetRole) Calls() []*RoleCacheMockSetRoleParams {
	mmSetRole.mutex.RLock()

	argCopy := make([]*RoleCacheMockSetRoleParams, len(mmSetRole.callArgs))
	copy(argCopy, mmSetRole.callArgs)

	mmSetRole.mutex.RUnlock()

	return argCopy
}

// MinimockSetRoleDone returns true if the count of the SetRole invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockSetRoleDone() bool {
	if m.SetRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetRoleMock.invocationsDone()
}

// MinimockSetRoleInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockSetRoleInspect() {
	for _, e := range m.SetRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.SetRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetRoleCounter := mm_atomic.LoadUint64(&m.afterSetRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetRoleMock.defaultExpectation != nil && afterSetRoleCounter < 1 {
		if m.SetRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.SetRole at\n%s", m.SetRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.SetRole at\n%s with params: %#v", m.SetRoleMock.defaultExpectation.expectationOrigins.origin, *m.SetRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetRole != nil && afterSetRoleCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.SetRole at\n%s", m.funcSetRoleOrigin)
	}

	if !m.SetRoleMock.invocationsDone() && afterSetRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.SetRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetRoleMock.expectedInvocations), m.SetRoleMock.expectedInvocationsOrigin, afterSetRoleCounter)
	}
}

type mRoleCacheMockSetRoles struct {
	optional           bool
	mock               *RoleCacheMock
	defaultExpectation *RoleCacheMockSetRolesExpectation
	expectations       []*RoleCacheMockSetRolesExpectation

	callArgs []*RoleCacheMockSetRolesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// RoleCacheMockSetRolesExpectation specifies expectation struct of the RoleCache.SetRoles
type RoleCacheMockSetRolesExpectation struct {
	mock               *RoleCacheMock
	params             *RoleCacheMockSetRolesParams
	paramPtrs          *RoleCacheMockSetRolesParamPtrs
	expectationOrigins RoleCacheMockSetRolesExpectationOrigins
	results            *RoleCacheMockSetRolesResults
	returnOrigin       string
	Counter            uint64
}

// RoleCacheMockSetRolesParams contains parameters of the RoleCache.SetRoles
type RoleCacheMockSetRolesParams struct {
	ctx   context.Context
	roles []roleentity.Role
}

// RoleCacheMockSetRolesParamPtrs contains pointers to parameters of the RoleCache.SetRoles
type RoleCacheMockSetRolesParamPtrs struct {
	ctx   *context.Context
	roles *[]roleentity.Role
}

// RoleCacheMockSetRolesResults contains results of the RoleCache.SetRoles
type RoleCacheMockSetRolesResults struct {
	err error
}

// RoleCacheMockSetRolesOrigins contains origins of expectations of the RoleCache.SetRoles
type RoleCacheMockSetRolesExpectationOrigins struct {
	origin      string
	originCtx   string
	originRoles string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetRoles *mRoleCacheMockSetRoles) Optional() *mRoleCacheMockSetRoles {
	mmSetRoles.optional = true
	return mmSetRoles
}

// Expect sets up expected params for RoleCache.SetRoles
func (mmSetRoles *mRoleCacheMockSetRoles) Expect(ctx context.Context, roles []roleentity.Role) *mRoleCacheMockSetRoles {
	if mmSetRoles.mock.funcSetRoles != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Set")
	}

	if mmSetRoles.defaultExpectation == nil {
		mmSetRoles.defaultExpectation = &RoleCacheMockSetRolesExpectation{}
	}

	if mmSetRoles.defaultExpectation.paramPtrs != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by ExpectParams functions")
	}

	mmSetRoles.defaultExpectation.params = &RoleCacheMockSetRolesParams{ctx, roles}
	mmSetRoles.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetRoles.expectations {
		if minimock.Equal(e.params, mmSetRoles.defaultExpectation.params) {
			mmSetRoles.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetRoles.defaultExpectation.params)
		}
	}

	return mmSetRoles
}

// ExpectCtxParam1 sets up expected param ctx for RoleCache.SetRoles
func (mmSetRoles *mRoleCacheMockSetRoles) ExpectCtxParam1(ctx context.Context) *mRoleCacheMockSetRoles {
	if mmSetRoles.mock.funcSetRoles != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Set")
	}

	if mmSetRoles.defaultExpectation == nil {
		mmSetRoles.defaultExpectation = &RoleCacheMockSetRolesExpectation{}
	}

	if mmSetRoles.defaultExpectation.params != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Expect")
	}

	if mmSetRoles.defaultExpectation.paramPtrs == nil {
		mmSetRoles.defaultExpectation.paramPtrs = &RoleCacheMockSetRolesParamPtrs{}
	}
	mmSetRoles.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetRoles.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetRoles
}

// ExpectRolesParam2 sets up expected param roles for RoleCache.SetRoles
func (mmSetRoles *mRoleCacheMockSetRoles) ExpectRolesParam2(roles []roleentity.Role) *mRoleCacheMockSetRoles {
	if mmSetRoles.mock.funcSetRoles != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Set")
	}

	if mmSetRoles.defaultExpectation == nil {
		mmSetRoles.defaultExpectation = &RoleCacheMockSetRolesExpectation{}
	}

	if mmSetRoles.defaultExpectation.params != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Expect")
	}

	if mmSetRoles.defaultExpectation.paramPtrs == nil {
		mmSetRoles.defaultExpectation.paramPtrs = &RoleCacheMockSetRolesParamPtrs{}
	}
	mmSetRoles.defaultExpectation.paramPtrs.roles = &roles
	mmSetRoles.defaultExpectation.expectationOrigins.originRoles = minimock.CallerInfo(1)

	return mmSetRoles
}

// Inspect accepts an inspector function that has same arguments as the RoleCache.SetRoles
func (mmSetRoles *mRoleCacheMockSetRoles) Inspect(f func(ctx context.Context, roles []roleentity.Role)) *mRoleCacheMockSetRoles {
	if mmSetRoles.mock.inspectFuncSetRoles != nil {
		mmSetRoles.mock.t.Fatalf("Inspect function is already set for RoleCacheMock.SetRoles")
	}

	mmSetRoles.mock.inspectFuncSetRoles = f

	return mmSetRoles
}

// Return sets up results that will be returned by RoleCache.SetRoles
func (mmSetRoles *mRoleCacheMockSetRoles) Return(err error) *RoleCacheMock {
	if mmSetRoles.mock.funcSetRoles != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Set")
	}

	if mmSetRoles.defaultExpectation == nil {
		mmSetRoles.defaultExpectation = &RoleCacheMockSetRolesExpectation{mock: mmSetRoles.mock}
	}
	mmSetRoles.defaultExpectation.results = &RoleCacheMockSetRolesResults{err}
	mmSetRoles.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetRoles.mock
}

// Set uses given function f to mock the RoleCache.SetRoles method
func (mmSetRoles *mRoleCacheMockSetRoles) Set(f func(ctx context.Context, roles []roleentity.Role) (err error)) *RoleCacheMock {
	if mmSetRoles.defaultExpectation != nil {
		mmSetRoles.mock.t.Fatalf("Default expectation is already set for the RoleCache.SetRoles method")
	}

	if len(mmSetRoles.expectations) > 0 {
		mmSetRoles.mock.t.Fatalf("Some expectations are already set for the RoleCache.SetRoles method")
	}

	mmSetRoles.mock.funcSetRoles = f
	mmSetRoles.mock.funcSetRolesOrigin = minimock.CallerInfo(1)
	return mmSetRoles.mock
}

// When sets expectation for the RoleCache.SetRoles which will trigger the result defined by the following
// Then helper
func (mmSetRoles *mRoleCacheMockSetRoles) When(ctx context.Context, roles []roleentity.Role) *RoleCacheMockSetRolesExpectation {
	if mmSetRoles.mock.funcSetRoles != nil {
		mmSetRoles.mock.t.Fatalf("RoleCacheMock.SetRoles mock is already set by Set")
	}

	expectation := &RoleCacheMockSetRolesExpectation{
		mock:               mmSetRoles.mock,
		params:             &RoleCacheMockSetRolesParams{ctx, roles},
		expectationOrigins: RoleCacheMockSetRolesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetRoles.expectations = append(mmSetRoles.expectations, expectation)
	return expectation
}

// Then sets up RoleCache.SetRoles return parameters for the expectation previously defined by the When method
func (e *RoleCacheMockSetRolesExpectation) Then(err error) *RoleCacheMock {
	e.results = &RoleCacheMockSetRolesResults{err}
	return e.mock
}

// Times sets number of times RoleCache.SetRoles should be invoked
func (mmSetRoles *mRoleCacheMockSetRoles) Times(n uint64) *mRoleCacheMockSetRoles {
	if n == 0 {
		mmSetRoles.mock.t.Fatalf("Times of RoleCacheMock.SetRoles mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetRoles.expectedInvocations, n)
	mmSetRoles.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetRoles
}

func (mmSetRoles *mRoleCacheMockSetRoles) invocationsDone() bool {
	if len(mmSetRoles.expectations) == 0 && mmSetRoles.defaultExpectation == nil && mmSetRoles.mock.funcSetRoles == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetRoles.mock.afterSetRolesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetRoles.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetRoles implements mm_repository.RoleCache
func (mmSetRoles *RoleCacheMock) SetRoles(ctx context.Context, roles []roleentity.Role) (err error) {
	mm_atomic.AddUint64(&mmSetRoles.beforeSetRolesCounter, 1)
	defer mm_atomic.AddUint64(&mmSetRoles.afterSetRolesCounter, 1)

	mmSetRoles.t.Helper()

	if mmSetRoles.inspectFuncSetRoles != nil {
		mmSetRoles.inspectFuncSetRoles(ctx, roles)
	}

	mm_params := RoleCacheMockSetRolesParams{ctx, roles}

	// Record call args
	mmSetRoles.SetRolesMock.mutex.Lock()
	mmSetRoles.SetRolesMock.callArgs = append(mmSetRoles.SetRolesMock.callArgs, &mm_params)
	mmSetRoles.SetRolesMock.mutex.Unlock()

	for _, e := range mmSetRoles.SetRolesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetRoles.SetRolesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetRoles.SetRolesMock.defaultExpectation.Counter, 1)
		mm_want := mmSetRoles.SetRolesMock.defaultExpectation.params
		mm_want_ptrs := mmSetRoles.SetRolesMock.defaultExpectation.paramPtrs

		mm_got := RoleCacheMockSetRolesParams{ctx, roles}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetRoles.t.Errorf("RoleCacheMock.SetRoles got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetRoles.SetRolesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.roles != nil && !minimock.Equal(*mm_want_ptrs.roles, mm_got.roles) {
				mmSetRoles.t.Errorf("RoleCacheMock.SetRoles got unexpected parameter roles, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetRoles.SetRolesMock.defaultExpectation.expectationOrigins.originRoles, *mm_want_ptrs.roles, mm_got.roles, minimock.Diff(*mm_want_ptrs.roles, mm_got.roles))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetRoles.t.Errorf("RoleCacheMock.SetRoles got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetRoles.SetRolesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetRoles.SetRolesMock.defaultExpectation.results
		if mm_results == nil {
			mmSetRoles.t.Fatal("No results are set for the RoleCacheMock.SetRoles")
		}
		return (*mm_results).err
	}
	if mmSetRoles.funcSetRoles != nil {
		return mmSetRoles.funcSetRoles(ctx, roles)
	}
	mmSetRoles.t.Fatalf("Unexpected call to RoleCacheMock.SetRoles. %v %v", ctx, roles)
	return
}

// SetRolesAfterCounter returns a count of finished RoleCacheMock.SetRoles invocations
func (mmSetRoles *RoleCacheMock) SetRolesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetRoles.afterSetRolesCounter)
}

// SetRolesBeforeCounter returns a count of RoleCacheMock.SetRoles invocations
func (mmSetRoles *RoleCacheMock) SetRolesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetRoles.beforeSetRolesCounter)
}

// Calls returns a list of arguments used in each call to RoleCacheMock.SetRoles.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetRoles *mRoleCacheMockSetRoles) Calls() []*RoleCacheMockSetRolesParams {
	mmSetRoles.mutex.RLock()

	argCopy := make([]*RoleCacheMockSetRolesParams, len(mmSetRoles.callArgs))
	copy(argCopy, mmSetRoles.callArgs)

	mmSetRoles.mutex.RUnlock()

	return argCopy
}

// MinimockSetRolesDone returns true if the count of the SetRoles invocations corresponds
// the number of defined expectations
func (m *RoleCacheMock) MinimockSetRolesDone() bool {
	if m.SetRolesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetRolesMock.invocationsDone()
}

// MinimockSetRolesInspect logs each unmet expectation
func (m *RoleCacheMock) MinimockSetRolesInspect() {
	for _, e := range m.SetRolesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to RoleCacheMock.SetRoles at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetRolesCounter := mm_atomic.LoadUint64(&m.afterSetRolesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetRolesMock.defaultExpectation != nil && afterSetRolesCounter < 1 {
		if m.SetRolesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to RoleCacheMock.SetRoles at\n%s", m.SetRolesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to RoleCacheMock.SetRoles at\n%s with params: %#v", m.SetRolesMock.defaultExpectation.expectationOrigins.origin, *m.SetRolesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetRoles != nil && afterSetRolesCounter < 1 {
		m.t.Errorf("Expected call to RoleCacheMock.SetRoles at\n%s", m.funcSetRolesOrigin)
	}

	if !m.SetRolesMock.invocationsDone() && afterSetRolesCounter > 0 {
		m.t.Errorf("Expected %d calls to RoleCacheMock.SetRoles at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetRolesMock.expectedInvocations), m.SetRolesMock.expectedInvocationsOrigin, afterSetRolesCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *RoleCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockAssignProductRoleInspect()

			m.MinimockAssignUserRoleInspect()

			m.MinimockDeleteRoleInspect()

			m.MinimockDeleteRolesInspect()

			m.MinimockGetProductRolesInspect()

			m.MinimockGetRoleInspect()

			m.MinimockGetRolesInspect()

			m.MinimockGetUserRoleIDsInspect()

			m.MinimockGetUserRolesInspect()

			m.MinimockRemoveProductRoleInspect()

			m.MinimockRemoveUserRoleInspect()

			m.MinimockSetRoleInspect()

			m.MinimockSetRolesInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *RoleCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *RoleCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockAssignProductRoleDone() &&
		m.MinimockAssignUserRoleDone() &&
		m.MinimockDeleteRoleDone() &&
		m.MinimockDeleteRolesDone() &&
		m.MinimockGetProductRolesDone() &&
		m.MinimockGetRoleDone() &&
		m.MinimockGetRolesDone() &&
		m.MinimockGetUserRoleIDsDone() &&
		m.MinimockGetUserRolesDone() &&
		m.MinimockRemoveProductRoleDone() &&
		m.MinimockRemoveUserRoleDone() &&
		m.MinimockSetRoleDone() &&
		m.MinimockSetRolesDone()
}
