package aggregate

import (
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
)

type AdminRoleUpdate struct {
	roleentity.RoleUpdateData
	UsersForUpdate  *[]userentity.UserProductLink
	GroupsForUpdate *[]groupentity.GroupID
	Permissions     *[]permissionentity.Permission
}
