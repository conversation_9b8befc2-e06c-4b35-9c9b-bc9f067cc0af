package aggregate

type RoleAggregate struct {
	categoryRoleLinks map[int64]map[int64]struct{}
}

func NewRoleAggregate() *RoleAggregate {
	return &RoleAggregate{
		categoryRoleLinks: make(map[int64]map[int64]struct{}),
	}
}

func (a *RoleAggregate) CacheCategoryRoleLinks(categoryRoleLinks map[int64]map[int64]struct{}) {
	a.categoryRoleLinks = categoryRoleLinks
}

func (a *RoleAggregate) HasCategoryRoleLink(categoryID int64, roleID int64) bool {
	_, ok := a.categoryRoleLinks[categoryID][roleID]
	return ok
}
