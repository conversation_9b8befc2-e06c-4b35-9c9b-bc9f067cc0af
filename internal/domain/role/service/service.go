package service

import (
	"context"

	categoryrepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	grouprepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	partrepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/repository"
	permentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	permrepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository"
	productrepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/repository"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolerepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	userrepo "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

//go:generate minimock -i RoleDomainService -o ../mocks/role_domain_service_mock.go -s _mock.go
type RoleDomainService interface {
	Create(ctx context.Context, role roleentity.RoleWithPermissions) (roleentity.RoleFull, error)
	CreateAsAdmin(ctx context.Context, role roleentity.Role, perms []permentity.Permission) (roleentity.Role, error)
	AssignToGroups(ctx context.Context, roleID int64, groupIDs []int64) error
	AssignToPermissions(ctx context.Context, roleID int64, permissionIDs []int64) error
	GetAllAsAdmin() ([]roleentity.AdminRole, error)
	GetAllWithCategoryStats() ([]roleentity.RoleWithCategoryStates, error)
	GetAllWithProductByUserID(userID int64) ([]roleentity.RoleWithProduct, error)
	GetByCategoryID(categoryID int64) ([]roleentity.Role, error)
	GetByGroupID(groupID int64) ([]roleentity.Role, error)
	GetByID(id int64) (roleentity.Role, error)
	GetByParticipantID(participantID int64) ([]roleentity.Role, error)
	GetByProductID(productID int64) ([]roleentity.RoleWithStats, error)
	GetBySystemTypeAndActive(isSystemRole, activeFlg bool) ([]roleentity.RoleWithStats, error)
	GetByUserID(userID int64) ([]roleentity.Role, error)
	GetByUserIDByProductIDs(userID int64, productIDs []int64) ([]roleentity.Role, error)
	GetFull(productID int64, roleID int64) (roleentity.RoleFull, error)
	GetRolesIDByUserID(userID int64) ([]int64, error)
	GetWithCountsByProductID(productID int64) ([]roleentity.AdminRole, error)
	GetWithProductByGroupID(groupID int64) ([]roleentity.RoleWithProduct, error)
	Update(ctx context.Context, data roleentity.RoleUpdateData) (roleentity.Role, error)
	UpdateByProductIDAndRoleFull(ctx context.Context, productID int64, ru roleentity.RoleFull) (roleentity.RoleFull, error)
	UpdateLinksWithGroups(ctx context.Context, roleID int64, groups []groupentity.GroupID) error
	UpdateLinksWithPermissions(ctx context.Context, roleID int64, permissions []permentity.Permission) error
	UpdateLinksWithUsers(ctx context.Context, roleID int64, users []userentity.UserProductLink) error
	UpdateParticipant(ctx context.Context, participantID int64, product int64, roleIDs []int64) ([]int64, error)
	UpdateUserRoles(ctx context.Context, userID int64, user userentity.UserUpdateData) error
	UpdateWithCategories(rolesWithCategories []roleentity.RoleWithCategoryStates) error
	Delete(ctx context.Context, productID, roleID int64) error
	DeleteAsAdmin(ctx context.Context, roleID int64) error
	UnassignFromGroups(ctx context.Context, roleID int64, groupIDs []int64) error
	UnassignFromPermissions(ctx context.Context, roleID int64, permissionIDs []int64) error
}

type roleDomainService struct {
	roleRepo             rolerepo.RolePrimeDB
	categoryRoleRepo     categoryrepo.CategoryRolePrimeDB
	groupRoleRepo        grouprepo.GroupRolePrimeDB
	participantRoleRepo  partrepo.ParticipantRolePrimeDB
	permissionRepo       permrepo.PermissionPrimeDB
	rolePermissionRepo   rolerepo.RolePermissionPrimeDB
	categoryRepo         categoryrepo.CategoryPrimeDB
	userRoleRepo         userrepo.UserRolePrimeDB
	participantGroupRepo partrepo.ParticipantGroupPrimeDB
	userGroupRepo        userrepo.UserGroupPrimeDB
	participantRepo      partrepo.ParticipantPrimeDB
	productRepo          productrepo.ProductPrimeDB
	userRepo             userrepo.UserPrimeDB
}

func NewRoleDomainService(
	roleRepo rolerepo.RolePrimeDB,
	categoryRoleRepo categoryrepo.CategoryRolePrimeDB,
	groupRoleRepo grouprepo.GroupRolePrimeDB,
	participantRoleRepo partrepo.ParticipantRolePrimeDB,
	permissionRepo permrepo.PermissionPrimeDB,
	rolePermissionRepo rolerepo.RolePermissionPrimeDB,
	categoryRepo categoryrepo.CategoryPrimeDB,
	userRoleRepo userrepo.UserRolePrimeDB,
	participantGroupRepo partrepo.ParticipantGroupPrimeDB,
	userGroupRepo userrepo.UserGroupPrimeDB,
	participantRepo partrepo.ParticipantPrimeDB,
	productRepo productrepo.ProductPrimeDB,
	userRepo userrepo.UserPrimeDB,
) RoleDomainService {
	return &roleDomainService{
		roleRepo:             roleRepo,
		categoryRoleRepo:     categoryRoleRepo,
		groupRoleRepo:        groupRoleRepo,
		participantRoleRepo:  participantRoleRepo,
		permissionRepo:       permissionRepo,
		rolePermissionRepo:   rolePermissionRepo,
		categoryRepo:         categoryRepo,
		userRoleRepo:         userRoleRepo,
		participantGroupRepo: participantGroupRepo,
		userGroupRepo:        userGroupRepo,
		participantRepo:      participantRepo,
		productRepo:          productRepo,
		userRepo:             userRepo,
	}
}
