package service

import (
	"context"
	"errors"

	permentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
)

func (s *roleDomainService) CreateAsAdmin(ctx context.Context, role roleentity.Role, perms []permentity.Permission) (roleentity.Role, error) {

	isExist, err := s.roleRepo.ExistByName(role.Name)
	if err != nil {
		return roleentity.Role{}, err
	}
	if isExist {
		return roleentity.Role{}, errors.New("role with these parameters already exists")
	}

	roleCreated, err := s.roleRepo.Create(ctx, role)
	if err != nil {
		return roleentity.Role{}, err
	}

	for _, perm := range perms {
		permData, err := s.permissionRepo.GetByNameAndMethod(perm.Name, perm.Method)
		if err != nil {
			return roleentity.Role{}, err
		}

		_, err = s.rolePermissionRepo.Create(ctx, roleentity.RolePermission{
			RoleID:       roleCreated.ID,
			PermissionID: permData.ID,
		})
		if err != nil {
			return roleentity.Role{}, err
		}
	}

	return roleCreated, nil
}

func (s *roleDomainService) GetAllAsAdmin() ([]roleentity.AdminRole, error) {
	roles, err := s.roleRepo.GetAll()
	if err != nil {
		return nil, err
	}

	adminRoles := make([]roleentity.AdminRole, 0, len(roles))
	for _, role := range roles {
		adminRole, err := s.buildAdminRole(role)
		if err != nil {
			return nil, err
		}
		adminRoles = append(adminRoles, adminRole)
	}

	return adminRoles, nil
}

func (s *roleDomainService) DeleteAsAdmin(ctx context.Context, roleID int64) error {

	flagFalse := false
	_, err := s.roleRepo.Update(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		ActiveFlg: &flagFalse,
	})

	return err
}

func (s *roleDomainService) buildAdminRole(role roleentity.Role) (roleentity.AdminRole, error) {
	adminRole := roleentity.AdminRole{
		ID:       role.ID,
		Name:     role.Name,
		IsActive: role.ActiveFlg,
		IsSystem: role.IsSystem,
	}

	var err error
	adminRole.UserCount, err = s.calculateUserCountForRole(role)
	if err != nil {
		return roleentity.AdminRole{}, err
	}

	adminRole.ParticipantCount, err = s.calculateParticipantCountForRole(role.ID)
	if err != nil {
		return roleentity.AdminRole{}, err
	}

	adminRole.GroupCount, err = s.calculateGroupCountForRole(role.ID)
	if err != nil {
		return roleentity.AdminRole{}, err
	}

	if role.ProductID != nil {
		product, err := s.getProductForRole(*role.ProductID)
		if err != nil {
			return roleentity.AdminRole{}, err
		}
		adminRole.Product = product
	}

	return adminRole, nil
}

func (s *roleDomainService) calculateGroupCountForRole(roleID int64) (int64, error) {
	groupRoles, err := s.groupRoleRepo.GetByRoleID(roleID)
	if err != nil {
		return 0, err
	}
	return int64(len(groupRoles)), nil
}

func (s *roleDomainService) calculateParticipantCountForRole(roleID int64) (int64, error) {
	participants, err := s.participantRoleRepo.GetByRoleID(roleID)
	if err != nil {
		return 0, err
	}
	return int64(len(participants)), nil
}

func (s *roleDomainService) calculateUserCountForRole(role roleentity.Role) (int64, error) {
	userIDs := make(map[int64]struct{})

	// Получаем пользователей через участников
	users, err := s.userRepo.GetUsersByParticipantRoleID(role.ID)
	if err != nil {
		return 0, err
	}
	for _, user := range users {
		userIDs[user.ID] = struct{}{}
	}

	// Для системных ролей добавляем прямых пользователей
	if role.IsSystem {
		userRoles, err := s.userRoleRepo.GetUserRolesByRoleID(role.ID)
		if err != nil {
			return 0, err
		}
		for _, userRole := range userRoles {
			userIDs[userRole.UserID] = struct{}{}
		}
	}

	return int64(len(userIDs)), nil
}

func (s *roleDomainService) getProductForRole(productID int64) (*productentity.ProductBasic, error) {
	product, err := s.productRepo.GetByID(productID)
	if err != nil {
		return nil, err
	}

	productBasic := product.ProductBasic()
	return &productBasic, nil
}
