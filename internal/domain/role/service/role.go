package service

import (
	"context"
	"errors"
	"strconv"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	participantentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/participant/entity"
	permentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	roleaggr "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/aggregate"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	sharedentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/errkit"
)

func (s *roleDomainService) Create(ctx context.Context, role roleentity.RoleWithPermissions) (roleentity.RoleFull, error) {

	roleCreated, err := s.roleRepo.Create(ctx, roleentity.Role{
		Name:      role.Name,
		ProductID: role.ProductID,
		IsSystem:  role.IsSystem,
	})
	if err != nil {
		return roleentity.RoleFull{}, err
	}

	err = s.assignRoleToSystemCategory(roleCreated.ID, role.IsSystem)
	if err != nil {
		return roleentity.RoleFull{}, err
	}

	err = s.UpdateLinksWithPermissions(ctx, roleCreated.ID, role.Permissions)
	if err != nil {
		return roleentity.RoleFull{}, err
	}

	return roleentity.RoleFull{
		ID:          roleCreated.ID,
		Name:        roleCreated.Name,
		IsSystem:    role.IsSystem,
		Permissions: role.Permissions,
	}, nil
}

func (s *roleDomainService) AssignToGroups(ctx context.Context, roleID int64, groupIDs []int64) error {
	err := s.groupRoleRepo.CreateByRoleIDAndGroupIDs(ctx, roleID, groupIDs)
	if err != nil {
		return err
	}

	return nil
}

func (s *roleDomainService) AssignToPermissions(ctx context.Context, roleID int64, permissionIDs []int64) error {
	return s.rolePermissionRepo.CreateByRoleIDAndPermissionIDs(ctx, roleID, permissionIDs)
}

func (s *roleDomainService) GetAllByUserID(userID int64) ([]roleentity.Role, error) {
	return s.roleRepo.GetAllByUserID(userID)
}

func (s *roleDomainService) GetAllWithCategoryStats() ([]roleentity.RoleWithCategoryStates, error) {
	roles, err := s.roleRepo.GetSystemRolesWithStats(context.Background())
	if err != nil {
		return nil, err
	}

	roleCategoryLink, err := s.roleRepo.GetRoleCategoryLinks(context.Background())
	if err != nil {
		return nil, err
	}

	return aggregateRolesWithCategoryStates(roles, roleCategoryLink), nil
}

func (s *roleDomainService) GetAllWithProductByUserID(userID int64) ([]roleentity.RoleWithProduct, error) {
	userRoles, err := s.roleRepo.GetAllWithProductIDByUserID(userID)
	if err != nil {
		return nil, err
	}

	rolesWithProduct := make([]roleentity.RoleWithProduct, 0, len(userRoles))
	for _, role := range userRoles {
		if role.ProductID == nil {
			rolesWithProduct = append(rolesWithProduct, roleentity.RoleWithProduct{
				ID:        role.ID,
				Name:      role.Name,
				IsSystem:  role.IsSystem,
				ActiveFlg: role.ActiveFlg,
			})
			continue
		}

		product, err := s.productRepo.GetByID(*role.ProductID)
		if err != nil {
			return nil, err
		}

		rolesWithProduct = append(rolesWithProduct, roleentity.RoleWithProduct{
			ID:        role.ID,
			Name:      role.Name,
			IsSystem:  role.IsSystem,
			ActiveFlg: role.ActiveFlg,
			Product:   product.ProductOrNil(),
		})
	}

	return rolesWithProduct, nil
}

func (s *roleDomainService) GetByCategoryID(categoryID int64) ([]roleentity.Role, error) {
	catRoles, err := s.categoryRoleRepo.GetByCategoryID(categoryID)
	if err != nil {
		return nil, err
	}

	var roles []roleentity.Role
	for _, catRole := range catRoles {
		role, err := s.roleRepo.GetByID(catRole.RoleID)
		if err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}

	return roles, nil
}

func (s *roleDomainService) GetByGroupID(groupID int64) ([]roleentity.Role, error) {
	groupRoles, err := s.groupRoleRepo.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	var roles []roleentity.Role
	for _, groupRole := range groupRoles {
		role, err := s.roleRepo.GetByID(groupRole.RoleID)
		if err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}

	return roles, nil
}

func (s *roleDomainService) GetByID(id int64) (roleentity.Role, error) {
	return s.roleRepo.GetByID(id)
}

func (s *roleDomainService) GetByParticipantID(participantID int64) ([]roleentity.Role, error) {
	participantRoles, err := s.participantRoleRepo.GetByParticipantID(participantID)
	if err != nil {
		return nil, err
	}

	var roles []roleentity.Role
	for _, participantRole := range participantRoles {
		role, err := s.roleRepo.GetByID(participantRole.RoleID)
		if err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}

	return roles, nil
}

func (s *roleDomainService) GetByProductID(productID int64) ([]roleentity.RoleWithStats, error) {
	rl, err := s.roleRepo.GetRolesWithStats(productID)
	if err != nil {
		return nil, errors.New("failed to get roles:" + strconv.FormatInt(productID, 10))
	}

	return rl, nil
}

func (s *roleDomainService) GetBySystemTypeAndActive(isSystemRole, activeFlg bool) ([]roleentity.RoleWithStats, error) {

	rolesRepo, err := s.roleRepo.GetBySystemType(isSystemRole)
	if err != nil {
		return nil, err
	}

	var rolesService []roleentity.RoleWithStats
	for _, role := range rolesRepo {
		if (activeFlg == role.ActiveFlg) && (role.IsSystem == isSystemRole) {

			roleGroups, err := s.groupRoleRepo.GetByRoleID(role.ID)
			if err != nil {
				return nil, err
			}

			participantRoles, err := s.participantRoleRepo.GetByRoleID(role.ID)
			if err != nil {
				return nil, err
			}

			userRoles, err := s.userRoleRepo.GetUserRolesByRoleID(role.ID)
			if err != nil {
				return nil, err
			}

			roleService := roleentity.RoleWithStats{
				RoleID:           role.ID,
				RoleName:         role.Name,
				IsSystem:         role.IsSystem,
				GroupCount:       int64(len(roleGroups)),
				ParticipantCount: int64(len(participantRoles)),
				UserCount:        int64(len(userRoles)),
			}
			rolesService = append(rolesService, roleService)
		}
	}

	return rolesService, nil
}

func (s *roleDomainService) GetByUserID(userID int64) ([]roleentity.Role, error) {
	userRoles, err := s.userRoleRepo.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	var roles []roleentity.Role
	for _, userRole := range userRoles {
		role, err := s.roleRepo.GetByID(userRole.RoleID)
		if err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}

	return roles, nil
}

func (s *roleDomainService) GetByUserIDByProductIDs(userID int64, productIDs []int64) ([]roleentity.Role, error) {

	participants, err := s.participantRepo.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	productIDsMap := make(map[int64]struct{})
	for _, productID := range productIDs {
		productIDsMap[productID] = struct{}{}
	}

	var roles []roleentity.Role
	if len(productIDs) > 0 {

		for _, participant := range participants {
			if _, ok := productIDsMap[participant.ProductID]; ok {

				role, err := s.roleRepo.GetRoleWithProductByParticipantID(participant.ID)
				if err != nil {
					return nil, err
				}

				roles = append(roles, role...)
			}
		}

	} else {

		var participantIDs []int64
		for _, participant := range participants {
			participantIDs = append(participantIDs, participant.ID)
		}

		role, err := s.roleRepo.GetRolesWithProductByParticipantIDs(participantIDs)
		if err != nil {
			return nil, err
		}

		roles = append(roles, role...)
	}

	return roles, nil
}

func (s *roleDomainService) GetFull(productID int64, roleID int64) (roleentity.RoleFull, error) {

	var roleFull roleentity.RoleFull
	if productID != 0 {
		role, err := s.roleRepo.GetByRoleIDAndProductID(roleID, productID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}
		if !role.ActiveFlg || role.IsSystem {
			return roleentity.RoleFull{}, errkit.NewObjectNotFound(errkit.ObjectTypeRole, strconv.FormatInt(roleID, 10), errkit.StateNotFound)
		}

		roleFull.ID = role.ID
		roleFull.Name = role.Name
		roleFull.IsSystem = role.IsSystem

		participantRoles, err := s.participantRoleRepo.GetByRoleID(roleID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}
		var participantIDs []int64
		for _, participantRole := range participantRoles {
			participantIDs = append(participantIDs, participantRole.ParticipantID)
		}
		roleFull.ParticipantIDs = &participantIDs

		groupRoles, err := s.groupRoleRepo.GetByRoleID(roleID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}
		var groupIDs []int64
		for _, groupRole := range groupRoles {
			groupIDs = append(groupIDs, groupRole.GroupID)
		}
		roleFull.GroupIDs = &groupIDs

	} else {
		role, err := s.roleRepo.GetByID(roleID)
		if err != nil || role.ID == 0 {
			return roleentity.RoleFull{}, err
		}
		if !role.ActiveFlg {
			return roleentity.RoleFull{}, errkit.NewObjectNotFound(errkit.ObjectTypeRole, strconv.FormatInt(roleID, 10), errkit.StateNotFound)
		}

		roleFull.ID = role.ID
		roleFull.Name = role.Name
		roleFull.IsSystem = role.IsSystem

		var usersIDs []int64
		userRoles, err := s.userRoleRepo.GetUserRolesByRoleID(roleID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}
		for _, userRole := range userRoles {
			usersIDs = append(usersIDs, userRole.UserID)
		}
		roleFull.UserIDs = &usersIDs
	}

	perms, err := s.rolePermissionRepo.GetByRoleID(roleID)
	if err != nil {
		return roleentity.RoleFull{}, err
	}

	permissions := make([]permentity.Permission, 0, len(perms))
	for _, perm := range perms {
		permDB, err := s.permissionRepo.GetByID(perm.PermissionID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}

		permissions = append(permissions, permentity.Permission{
			ID:     permDB.ID,
			Name:   permDB.Name,
			Method: permDB.Method,
		})
	}
	roleFull.Permissions = permissions

	return roleFull, nil
}

func (s *roleDomainService) GetRolesIDByUserID(userID int64) ([]int64, error) {
	roles, err := s.userRoleRepo.GetByUserID(userID)
	if err != nil {
		return nil, err
	}

	var rolesID []int64
	for _, role := range roles {
		rolesID = append(rolesID, role.RoleID)
	}

	return rolesID, nil
}

func (s *roleDomainService) GetWithCountsByProductID(productID int64) ([]roleentity.AdminRole, error) {
	return s.roleRepo.GetByProductIDAndIsActive(productID, true)
}

func (s *roleDomainService) GetWithProductByGroupID(groupID int64) ([]roleentity.RoleWithProduct, error) {
	groupRoleLinks, err := s.groupRoleRepo.GetByGroupID(groupID)
	if err != nil {
		return nil, err
	}

	rolesWithProduct := make([]roleentity.RoleWithProduct, 0, len(groupRoleLinks))
	for _, link := range groupRoleLinks {
		role, err := s.roleRepo.GetByID(link.RoleID)
		if err != nil {
			return nil, err
		}
		if role.ProductID == nil {
			rolesWithProduct = append(rolesWithProduct, roleentity.RoleWithProduct{
				ID:        role.ID,
				Name:      role.Name,
				IsSystem:  role.IsSystem,
				ActiveFlg: role.ActiveFlg,
				CreatedAt: role.CreatedAt,
				UpdatedAt: role.UpdatedAt,
				DeletedAt: role.DeletedAt,
			})
			continue
		}
		product, err := s.productRepo.GetByID(*role.ProductID)
		if err != nil {
			return nil, err
		}
		rolesWithProduct = append(rolesWithProduct, roleentity.RoleWithProduct{
			ID:        role.ID,
			Name:      role.Name,
			IsSystem:  role.IsSystem,
			ActiveFlg: role.ActiveFlg,
			CreatedAt: role.CreatedAt,
			UpdatedAt: role.UpdatedAt,
			DeletedAt: role.DeletedAt,
			Product:   product.ProductOrNil(),
		})
	}

	return rolesWithProduct, nil
}

func (s *roleDomainService) Update(ctx context.Context, data roleentity.RoleUpdateData) (roleentity.Role, error) {
	return s.roleRepo.Update(ctx, data)
}

func (s *roleDomainService) UpdateByProductIDAndRoleFull(ctx context.Context, productID int64, ru roleentity.RoleFull) (roleentity.RoleFull, error) {
	roleData, err := s.roleRepo.Update(ctx, roleentity.RoleUpdateData{
		ID:        ru.ID,
		Name:      sharedentity.StringPtrOrNil(ru.Name),
		ProductID: &productID,
		IsSystem:  &ru.IsSystem,
	})
	if err != nil {

		return roleentity.RoleFull{}, err
	}

	var roleFull roleentity.RoleFull
	if productID != 0 {

		var participantsIDs []int64
		if ru.ParticipantIDs != nil {

			err := s.participantRoleRepo.DeleteByRoleID(ctx, ru.ID)
			if err != nil {
				return roleentity.RoleFull{}, err
			}

			if ru.GroupIDs != nil {
				for _, groupID := range *ru.GroupIDs {
					err := s.participantGroupRepo.DeleteByGroupID(ctx, groupID)
					if err != nil {
						return roleentity.RoleFull{}, err
					}
				}
			}

			for _, participantID := range *ru.ParticipantIDs {

				_, err := s.participantRoleRepo.Create(ctx, participantID, ru.ID)

				if err != nil {
					return roleentity.RoleFull{}, err
				}

				if ru.GroupIDs != nil {
					for _, groupID := range *ru.GroupIDs {
						_, err := s.participantGroupRepo.Create(ctx, participantID, groupID)
						if err != nil {
							return roleentity.RoleFull{}, err
						}
					}
				}

				participantsIDs = append(participantsIDs, participantID)
			}

		} else {

			participantRoles, err := s.participantRoleRepo.GetByRoleID(ru.ID)
			if err != nil {
				return roleentity.RoleFull{}, err
			}
			for _, participantRole := range participantRoles {
				participantsIDs = append(participantsIDs, participantRole.ParticipantID)
			}
		}

		roleFull.ParticipantIDs = &participantsIDs

	} else {

		var usersIDs []int64
		if ru.UserIDs != nil {
			err := s.userRoleRepo.DeleteByRoleID(ctx, ru.ID)
			if err != nil {
				return roleentity.RoleFull{}, err
			}

			if ru.GroupIDs != nil {
				for _, groupID := range *ru.GroupIDs {
					err := s.userGroupRepo.DeleteByGroupID(ctx, groupID)
					if err != nil {
						return roleentity.RoleFull{}, err
					}
				}
			}

			for _, userID := range *ru.UserIDs {
				if userID == 0 {
					continue
				}
				_, err := s.userRoleRepo.Create(ctx, userentity.UserRoleCreateData{
					UserID: userID,
					RoleID: ru.ID,
				})
				if err != nil {
					return roleentity.RoleFull{}, err
				}

				if ru.GroupIDs != nil {
					for _, groupID := range *ru.GroupIDs {
						_, err := s.userGroupRepo.Create(ctx, userentity.UserGroupCreateData{
							UserID:  userID,
							GroupID: groupID,
						})
						if err != nil {
							return roleentity.RoleFull{}, err
						}
					}
				}

				usersIDs = append(usersIDs, userID)
			}

		} else {

			userRoles, err := s.userRoleRepo.GetUserRolesByRoleID(ru.ID)
			if err != nil {
				return roleentity.RoleFull{}, err
			}
			for _, userRole := range userRoles {
				usersIDs = append(usersIDs, userRole.UserID)
			}
		}

		roleFull.UserIDs = &usersIDs
	}

	roleFull.ID = roleData.ID
	roleFull.Name = roleData.Name
	roleFull.IsSystem = roleData.IsSystem

	if len(ru.Permissions) > 0 {
		if err := s.rolePermissionRepo.DeleteByRoleID(ctx, ru.ID); err != nil {
			return roleentity.RoleFull{}, err
		}

		perms := make([]permentity.Permission, 0, len(ru.Permissions))
		for _, perm := range ru.Permissions {
			permDB, err := s.permissionRepo.GetByNameAndMethod(perm.Name, perm.Method)
			if err != nil {
				return roleentity.RoleFull{}, err
			}

			_, err = s.rolePermissionRepo.Create(ctx, roleentity.RolePermission{
				RoleID:       ru.ID,
				PermissionID: permDB.ID,
			})
			if err != nil {
				return roleentity.RoleFull{}, err
			}

			perms = append(perms, permentity.Permission{
				ID:     permDB.ID,
				Name:   permDB.Name,
				Method: permDB.Method,
			})
		}
		roleFull.Permissions = perms
	} else {
		perms, err := s.rolePermissionRepo.GetByRoleID(ru.ID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}

		permissions := make([]permentity.Permission, 0, len(perms))
		for _, perm := range perms {
			permDB, err := s.permissionRepo.GetByID(perm.PermissionID)
			if err != nil {
				return roleentity.RoleFull{}, err
			}

			permissions = append(permissions, permentity.Permission{
				ID:     permDB.ID,
				Name:   permDB.Name,
				Method: permDB.Method,
			})
		}
		roleFull.Permissions = permissions
	}

	if ru.GroupIDs != nil {
		currentGroupRoles, err := s.groupRoleRepo.GetByRoleID(ru.ID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}

		currentGroupMap := make(map[int64]struct{})
		for _, groupRole := range currentGroupRoles {
			currentGroupMap[groupRole.GroupID] = struct{}{}
		}

		desiredGroupMap := make(map[int64]struct{})
		for _, groupID := range *ru.GroupIDs {
			if groupID != 0 {
				desiredGroupMap[groupID] = struct{}{}
			}
		}

		for groupID := range currentGroupMap {
			if _, exists := desiredGroupMap[groupID]; !exists {
				err := s.groupRoleRepo.DeleteByGroupID(ctx, groupID)
				if err != nil {
					return roleentity.RoleFull{}, err
				}
			}
		}

		var groupIDs []int64
		for groupID := range desiredGroupMap {
			if _, exists := currentGroupMap[groupID]; !exists {
				_, err := s.groupRoleRepo.Create(groupentity.GroupRole{
					RoleID:  ru.ID,
					GroupID: groupID,
				})
				if err != nil {
					return roleentity.RoleFull{}, err
				}
			}
			groupIDs = append(groupIDs, groupID)
		}
		roleFull.GroupIDs = &groupIDs
	} else {
		groupRoles, err := s.groupRoleRepo.GetByRoleID(ru.ID)
		if err != nil {
			return roleentity.RoleFull{}, err
		}
		var groupIDs []int64
		for _, groupRole := range groupRoles {
			groupIDs = append(groupIDs, groupRole.GroupID)
		}
		roleFull.GroupIDs = &groupIDs
	}

	return roleFull, nil
}

func (s *roleDomainService) UpdateLinksWithGroups(ctx context.Context, roleID int64, groups []groupentity.GroupID) error {

	groupRolesOld, err := s.groupRoleRepo.GetByRoleID(roleID)
	if err != nil {
		return err
	}

	var groupsNew []groupentity.GroupID
	for _, g := range groups {
		groupsNew = append(groupsNew, groupentity.GroupID{ID: g.ID})
	}

	toDelete, toAdd := diffGroupIDsForRole(groupRolesOld, groupsNew)

	if len(toDelete) > 0 {
		err = s.UnassignFromGroups(ctx, roleID, toDelete)
		if err != nil {
			return err
		}
	}

	if len(toAdd) > 0 {
		err = s.AssignToGroups(ctx, roleID, toAdd)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *roleDomainService) UpdateLinksWithPermissions(ctx context.Context, roleID int64, permissions []permentity.Permission) error {

	existingPermissions, err := s.rolePermissionRepo.GetByRoleID(roleID)
	if err != nil {
		return err
	}

	permissionsNew := make([]permentity.Permission, 0, len(permissions))
	for _, p := range permissions {
		permission, err := s.permissionRepo.GetByNameAndMethod(p.Name, p.Method)
		if err != nil {
			return err
		}
		permissionsNew = append(permissionsNew, permission)
	}

	toDelete, toAdd := diffPermissionIDsForRole(existingPermissions, permissionsNew)

	if len(toDelete) > 0 {
		err = s.UnassignFromPermissions(ctx, roleID, toDelete)
		if err != nil {
			return err
		}
	}

	if len(toAdd) > 0 {
		err = s.AssignToPermissions(ctx, roleID, toAdd)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *roleDomainService) UpdateLinksWithUsers(ctx context.Context, roleID int64, userLinks []userentity.UserProductLink) error {

	/*
		WITHOUT PRODUCTS. AS USERS
	*/

	existingUsersRoles, err := s.userRoleRepo.GetUserRolesByRoleID(roleID)
	if err != nil {
		return err
	}

	toDeleteForUsers, toAddForUsers := diffUserIDsForRole(existingUsersRoles, userLinks)

	if len(toDeleteForUsers) > 0 {
		err = s.userRoleRepo.DeleteByRoleIDAndUserIDs(ctx, roleID, toDeleteForUsers)
		if err != nil {
			return err
		}
	}

	if len(toAddForUsers) > 0 {
		err = s.userRoleRepo.CreateByRoleIDAndUserIDs(ctx, roleID, toAddForUsers)
		if err != nil {
			return err
		}
	}

	/*
		WITH PRODUCTS. AS PARTICIPANTS
	*/

	usersProducts := make(map[int64][]int64)
	for _, link := range userLinks {
		if link.ProductID == nil {
			continue
		}
		usersProducts[link.UserID] = append(usersProducts[link.UserID], *link.ProductID)
	}

	var existingParticipantIDs []int64
	for userID, productIDs := range usersProducts {
		userParticipants, err := s.participantRepo.GetByUserIDAndProductIDs(userID, productIDs)
		if err != nil {
			return err
		}
		for _, participant := range userParticipants {
			existingParticipantIDs = append(existingParticipantIDs, participant.ID)
		}
	}

	existingParticipantsRoles, err := s.participantRoleRepo.GetByRoleID(roleID)
	if err != nil {
		return err
	}

	toDeleteForParticipants, toAddForParticipants := diffParticipantIDsForRole(existingParticipantsRoles, existingParticipantIDs)

	if len(toDeleteForParticipants) > 0 {
		err = s.participantRoleRepo.DeleteByRoleIDAndParticipantIDs(ctx, roleID, toDeleteForParticipants)
		if err != nil {
			return err
		}
	}

	if len(toAddForParticipants) > 0 {
		err = s.participantRoleRepo.CreateByRoleIDAndParticipantIDs(ctx, roleID, toAddForParticipants)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *roleDomainService) UpdateParticipant(ctx context.Context, participantID int64, product int64, roleIDs []int64) ([]int64, error) {
	// TODO: getByParticipantIDAndProductID
	currentRoles, err := s.participantRoleRepo.GetByParticipantID(participantID)
	if err != nil {
		return nil, err
	}

	desiredRoleMap := sharedentity.ToSet(roleIDs)
	currentRoleMap := sharedentity.ToSet(extractRoleIDs(currentRoles))
	rolesToRemove, rolesToAdd := computeRoleDifferences(currentRoleMap, desiredRoleMap)

	if err := s.applyRoleChanges(ctx, participantID, rolesToRemove, rolesToAdd); err != nil {
		return nil, err
	}

	updatedRoles, err := s.participantRoleRepo.GetByParticipantID(participantID)
	if err != nil {
		return nil, err
	}

	return extractRoleIDs(updatedRoles), nil
}

func (s *roleDomainService) UpdateUserRoles(ctx context.Context, userID int64, user userentity.UserUpdateData) error {
	if user.RoleIDs == nil {
		return nil
	}

	userRoles, err := s.userRoleRepo.GetByUserID(userID)
	if err != nil {
		return err
	}

	roleIDMap := make(map[int64]struct{})
	for _, id := range *user.RoleIDs {
		roleIDMap[id] = struct{}{}
	}

	existingRoleIDs := make(map[int64]struct{})
	for _, userRole := range userRoles {
		existingRoleIDs[userRole.RoleID] = struct{}{}
	}

	for _, roleID := range *user.RoleIDs {
		_, exists := existingRoleIDs[roleID]
		if exists {
			continue
		}

		_, err = s.userRoleRepo.Create(ctx, userentity.UserRoleCreateData{
			UserID: userID,
			RoleID: roleID,
		})
		if err != nil {
			return err
		}
	}

	var toDeleteUserRoles []int64
	for _, userRole := range userRoles {
		if _, exists := roleIDMap[userRole.RoleID]; !exists && userRole.RoleID != 1 {
			toDeleteUserRoles = append(toDeleteUserRoles, userRole.RoleID)
		}
	}

	if len(toDeleteUserRoles) > 0 {
		if err := s.userRoleRepo.DeleteByUserIDAndRoleIDs(ctx, userID, toDeleteUserRoles); err != nil {
			return err
		}
	}

	return nil
}

func (s *roleDomainService) UpdateWithCategories(rolesWithCategories []roleentity.RoleWithCategoryStates) error {
	existingCategoryRoleLinks, err := s.getExistingCategoryRoleLinks()
	if err != nil {
		return err
	}

	roleAggregate := roleaggr.NewRoleAggregate()
	roleAggregate.CacheCategoryRoleLinks(existingCategoryRoleLinks)

	for _, roleWithCategory := range rolesWithCategories {
		for _, category := range roleWithCategory.CategoryStates {
			hasRoleLink := roleAggregate.HasCategoryRoleLink(category.CategoryID, roleWithCategory.RoleID)
			if category.IsActive && !hasRoleLink {
				if _, err := s.categoryRoleRepo.Create(categoryentity.CategoryRoleLink{
					CategoryID: category.CategoryID,
					RoleID:     roleWithCategory.RoleID,
				}); err != nil {
					return err
				}
			} else if !category.IsActive && hasRoleLink {
				if err := s.categoryRoleRepo.Delete(category.CategoryID, roleWithCategory.RoleID); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (s *roleDomainService) Delete(ctx context.Context, productID, roleID int64) error {

	if productID != 0 {
		_, err := s.roleRepo.GetByRoleIDAndProductID(roleID, productID)
		if err != nil {
			return err
		}
	}

	flagFalse := false
	_, err := s.roleRepo.Update(ctx, roleentity.RoleUpdateData{
		ID:        roleID,
		ActiveFlg: &flagFalse,
	})

	return err
}

func (s *roleDomainService) UnassignFromGroups(ctx context.Context, roleID int64, groupIDs []int64) error {
	err := s.groupRoleRepo.DeleteByGroupIDsAndRoleID(ctx, groupIDs, roleID)
	if err != nil {
		return err
	}
	return nil
}

func (s *roleDomainService) UnassignFromPermissions(ctx context.Context, roleID int64, permissionIDs []int64) error {
	return s.rolePermissionRepo.DeleteByRoleIDAndPermissionIDs(ctx, roleID, permissionIDs)
}

func (s *roleDomainService) addRoles(ctx context.Context, participantID int64, rolesToAdd map[int64]struct{}) error {
	for roleID := range rolesToAdd {
		_, err := s.participantRoleRepo.Create(ctx, participantID, roleID)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *roleDomainService) applyRoleChanges(ctx context.Context, participantID int64, rolesToRemove, rolesToAdd map[int64]struct{}) error {
	if err := s.removeRoles(ctx, participantID, rolesToRemove); err != nil {
		return err
	}
	if err := s.addRoles(ctx, participantID, rolesToAdd); err != nil {
		return err
	}
	return nil
}

func (s *roleDomainService) assignRoleToSystemCategory(roleID int64, isSystem bool) error {
	if isSystem {
		_, err := s.categoryRoleRepo.Create(categoryentity.CategoryRoleLink{
			CategoryID: constants.CategorySystemID,
			RoleID:     roleID,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *roleDomainService) getExistingCategoryRoleLinks() (map[int64]map[int64]struct{}, error) {
	allRelations, err := s.categoryRoleRepo.GetAll()
	if err != nil {
		return nil, err
	}

	result := make(map[int64]map[int64]struct{})
	for _, rel := range allRelations {
		if result[rel.CategoryID] == nil {
			result[rel.CategoryID] = make(map[int64]struct{})
		}
		result[rel.CategoryID][rel.RoleID] = struct{}{}
	}
	return result, nil
}

func (s *roleDomainService) removeRoles(ctx context.Context, participantID int64, rolesToRemove map[int64]struct{}) error {
	for roleID := range rolesToRemove {
		if err := s.participantRoleRepo.DeleteByParticipantAndRoleID(ctx, participantID, roleID); err != nil {
			return err
		}
	}
	return nil
}

func aggregateRolesWithCategoryStates(roles []roleentity.RoleWithStats, roleCategoryLink []roleentity.RoleCategoryLink) []roleentity.RoleWithCategoryStates {
	catMap := make(map[int64][]roleentity.RoleCategoryStates, len(roles))
	for _, link := range roleCategoryLink {
		catMap[link.RoleID] = append(catMap[link.RoleID], roleentity.RoleCategoryStates{
			CategoryID:   link.CategoryID,
			CategoryName: link.CategoryName,
			IsActive:     link.IsActive,
		})
	}

	result := make([]roleentity.RoleWithCategoryStates, 0, len(roles))
	for _, role := range roles {
		result = append(result, roleentity.RoleWithCategoryStates{
			RoleID:           role.RoleID,
			RoleName:         role.RoleName,
			IsSystem:         role.IsSystem,
			GroupCount:       role.GroupCount,
			ParticipantCount: role.ParticipantCount,
			UserCount:        role.UserCount,
			CategoryStates:   catMap[role.RoleID],
		})
	}

	return result
}

func computeRoleDifferences(current, desired map[int64]struct{}) (remove, add map[int64]struct{}) {
	remove = make(map[int64]struct{})
	add = make(map[int64]struct{})

	for role := range current {
		if _, exists := desired[role]; !exists {
			remove[role] = struct{}{}
		}
	}
	for role := range desired {
		if _, exists := current[role]; !exists {
			add[role] = struct{}{}
		}
	}

	return
}

func diffGroupIDsForRole(groupRolesOld []groupentity.GroupRole, groupsNew []groupentity.GroupID) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		groupRolesOld, groupsNew,
		func(r groupentity.GroupRole) int64 { return r.GroupID },
		func(g groupentity.GroupID) int64 { return g.ID },
	)
}

func diffParticipantIDsForRole(participantsOld []participantentity.ParticipantRole, participantIDsNew []int64) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		participantsOld,
		participantIDsNew,
		func(p participantentity.ParticipantRole) int64 { return p.ParticipantID },
		func(id int64) int64 { return id },
	)
}

func diffPermissionIDsForRole(permissionsOld []roleentity.RolePermission, permissionsNew []permentity.Permission) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		permissionsOld,
		permissionsNew,
		func(p roleentity.RolePermission) int64 { return p.PermissionID },
		func(p permentity.Permission) int64 { return p.ID },
	)
}

func diffUserIDsForRole(usersOld []userentity.UserRole, usersNew []userentity.UserProductLink) (toDelete []int64, toAdd []int64) {
	return sharedentity.DiffIDs(
		usersOld,
		usersNew,
		func(u userentity.UserRole) int64 { return u.UserID },
		func(u userentity.UserProductLink) int64 {
			if u.ProductID == nil {
				return u.UserID
			}
			return 0 // Skip users with ProductID set
		},
	)
}

func extractRoleIDs(roles []participantentity.ParticipantRole) []int64 {
	roleIDs := make([]int64, 0, len(roles))
	for _, role := range roles {
		roleIDs = append(roleIDs, role.RoleID)
	}
	return roleIDs
}
