package entity

import (
	"time"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	productentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/product/entity"
)

type RoleFull struct {
	ID             int64
	Name           string
	IsSystem       bool
	ParticipantIDs *[]int64
	GroupIDs       *[]int64
	UserIDs        *[]int64
	Permissions    []permissionentity.Permission
}

type RoleWithPermissions struct {
	ID          int64
	Name        string
	ProductID   *int64
	IsSystem    bool
	Permissions []permissionentity.Permission
}

type Role struct {
	ID          int64
	Name        string
	ProductID   *int64
	IsSystem    bool
	ActiveFlg   bool
	IsProtected bool
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time
}

type AdminRole struct {
	ID               int64
	Name             string
	IsActive         bool
	IsSystem         bool
	Product          *productentity.ProductBasic
	UserCount        int64
	ParticipantCount int64
	GroupCount       int64
}

func (r Role) TypeBoolToString() string {
	if r.IsSystem {
		return constants.SystemType
	}
	return constants.CustomType
}

type RoleFiltersData struct {
	Name       *string
	Type       *bool
	ProductIDs *[]int64
}

type RoleCreateData struct {
	Name      string
	ProductID *int64
	IsSystem  bool
}

type RoleUpdateData struct {
	ID        int64
	Name      *string
	ProductID *int64
	IsSystem  *bool
	ActiveFlg *bool
}

type RolePermission struct {
	ID           int64
	RoleID       int64
	PermissionID int64
	CreatedAt    time.Time
}

type RoleWithCategoryStates struct {
	RoleID           int64
	RoleName         string
	IsSystem         bool
	GroupCount       int64
	ParticipantCount int64
	UserCount        int64
	CategoryStates   []RoleCategoryStates
}

type RoleCategoryStates struct {
	CategoryID   int64
	CategoryName string
	IsActive     bool
}

type RoleWithStats struct {
	RoleID           int64
	RoleName         string
	IsSystem         bool
	GroupCount       int64
	ParticipantCount int64
	UserCount        int64
}

type RoleCategoryLink struct {
	RoleID       int64
	CategoryID   int64
	CategoryName string
	IsActive     bool
}

type RoleWithProductCollection struct {
	RoleID   int64
	RoleName string
	IsSystem bool
	Product  *productentity.Product
}

type RoleWithProduct struct {
	ID        int64
	Name      string
	IsSystem  bool
	ActiveFlg bool
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
	Product   *productentity.Product
}

type RoleWithProductID struct {
	ID        int64
	Name      string
	IsSystem  bool
	ActiveFlg bool
	ProductID *int64
}

type RoleID struct {
	ID int64
}

type RoleProductLink struct {
	RoleID    int64
	ProductID *int64
}
