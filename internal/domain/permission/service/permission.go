package service

import (
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categoryrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	permissionrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository"
	rolerepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

//go:generate minimock -i PermissionDomainService -o ../mocks/permission_domain_service_mock.go -s _mock.go
type PermissionDomainService interface {
	Create(permission permissionentity.Permission) (permissionentity.Permission, error)
	AssignPermissionToCategory(categoryID, permissionID int64) error
	GetAll() ([]permissionentity.Permission, error)
	GetAllCategoryPermissions() ([]categoryentity.CategoryPermission, error)
	GetAllUniqueNames() (map[string]struct{}, error)
	GetByCategoryID(categoryID int64) ([]permissionentity.Permission, error)
	GetByID(id int64) (permissionentity.Permission, error)
	GetByIDs(permissionIDs []int64) ([]permissionentity.Permission, error)
	GetByNameAndMethod(name, method string) (permissionentity.Permission, error)
	GetByRoleID(roleID int64) ([]permissionentity.Permission, error)
	GetByUserID(userID int64) ([]permissionentity.Permission, error)
	GetIDsByName(name string) ([]int64, error)
	HasCategoryPermission(categoryID, permissionID int64) (bool, error)
	Update(permission permissionentity.Permission) (permissionentity.Permission, error)
	UnassignPermissionFromCategory(categoryID, permissionID int64) error
}

type permissionDomainService struct {
	categoryPermissionRepo categoryrepository.CategoryPermissionPrimeDB
	permissionRepo         permissionrepository.PermissionPrimeDB
	rolePermissionRepo     rolerepository.RolePermissionPrimeDB
	userRoleRepo           userrepository.UserRolePrimeDB
}

func NewPermissionDomainService(
	categoryPermissionRepository categoryrepository.CategoryPermissionPrimeDB,
	permissionRepository permissionrepository.PermissionPrimeDB,
	rolePermissionRepository rolerepository.RolePermissionPrimeDB,
	userRoleRepository userrepository.UserRolePrimeDB,
) PermissionDomainService {
	return &permissionDomainService{
		categoryPermissionRepo: categoryPermissionRepository,
		permissionRepo:         permissionRepository,
		rolePermissionRepo:     rolePermissionRepository,
		userRoleRepo:           userRoleRepository,
	}
}

func (s *permissionDomainService) Create(permission permissionentity.Permission) (permissionentity.Permission, error) {
	return s.permissionRepo.Create(permission)
}

func (s *permissionDomainService) AssignPermissionToCategory(categoryID, permissionID int64) error {
	_, err := s.categoryPermissionRepo.Create(categoryentity.CategoryPermission{
		CategoryID:   categoryID,
		PermissionID: permissionID,
	})
	return err
}

func (s *permissionDomainService) GetAll() ([]permissionentity.Permission, error) {
	return s.permissionRepo.GetAll()
}

func (s *permissionDomainService) GetAllCategoryPermissions() ([]categoryentity.CategoryPermission, error) {
	return s.categoryPermissionRepo.GetAll()
}

func (s *permissionDomainService) GetAllUniqueNames() (map[string]struct{}, error) {
	return s.permissionRepo.GetAllUniqueNames()
}

func (s *permissionDomainService) GetByCategoryID(categoryID int64) ([]permissionentity.Permission, error) {
	catPerms, err := s.categoryPermissionRepo.GetByCategoryID(categoryID)
	if err != nil {
		return nil, err
	}

	var permissions []permissionentity.Permission
	for _, perm := range catPerms {
		perm, err := s.permissionRepo.GetByID(perm.PermissionID)
		if err != nil {
			return nil, err
		}
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

func (s *permissionDomainService) GetByID(id int64) (permissionentity.Permission, error) {
	return s.permissionRepo.GetByID(id)
}

func (s *permissionDomainService) GetByIDs(permissionIDs []int64) ([]permissionentity.Permission, error) {
	var permissions []permissionentity.Permission

	for _, permID := range permissionIDs {
		perm, err := s.permissionRepo.GetByID(permID)
		if err != nil {
			return nil, err
		}
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

func (s *permissionDomainService) GetByNameAndMethod(name, method string) (permissionentity.Permission, error) {
	return s.permissionRepo.GetByNameAndMethod(name, method)
}

func (s *permissionDomainService) GetByRoleID(roleID int64) ([]permissionentity.Permission, error) {
	rolePerms, err := s.rolePermissionRepo.GetByRoleID(roleID)
	if err != nil {
		return nil, err
	}

	var permissions []permissionentity.Permission
	for _, perm := range rolePerms {
		perm, err := s.permissionRepo.GetByID(perm.PermissionID)
		if err != nil {
			return nil, err
		}
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

func (s *permissionDomainService) GetByUserID(userID int64) ([]permissionentity.Permission, error) {
	return s.permissionRepo.GetByUserID(userID)
}

func (s *permissionDomainService) GetIDsByName(name string) ([]int64, error) {
	return s.permissionRepo.GetIDsByName(name)
}

func (s *permissionDomainService) HasCategoryPermission(categoryID, permissionID int64) (bool, error) {
	return s.categoryPermissionRepo.HasCategoryPermission(categoryID, permissionID)
}

func (s *permissionDomainService) Update(permission permissionentity.Permission) (permissionentity.Permission, error) {
	return s.permissionRepo.Update(permission)
}

func (s *permissionDomainService) UnassignPermissionFromCategory(categoryID, permissionID int64) error {
	return s.categoryPermissionRepo.DeleteCategoryPermission(categoryID, permissionID)
}
