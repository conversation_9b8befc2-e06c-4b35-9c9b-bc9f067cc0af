// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository.PermissionPrimeDB -o permission_prime_db_mock.go -n PermissionPrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"github.com/gojuno/minimock/v3"
)

// PermissionPrimeDBMock implements mm_repository.PermissionPrimeDB
type PermissionPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(permission permissionentity.Permission)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mPermissionPrimeDBMockCreate

	funcGetAll          func() (pa1 []permissionentity.Permission, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mPermissionPrimeDBMockGetAll

	funcGetAllUniqueNames func() (m1 map[string]struct {
	}, err error)
	funcGetAllUniqueNamesOrigin    string
	inspectFuncGetAllUniqueNames   func()
	afterGetAllUniqueNamesCounter  uint64
	beforeGetAllUniqueNamesCounter uint64
	GetAllUniqueNamesMock          mPermissionPrimeDBMockGetAllUniqueNames

	funcGetByID          func(id int64) (p1 permissionentity.Permission, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mPermissionPrimeDBMockGetByID

	funcGetByNameAndMethod          func(name string, method string) (p1 permissionentity.Permission, err error)
	funcGetByNameAndMethodOrigin    string
	inspectFuncGetByNameAndMethod   func(name string, method string)
	afterGetByNameAndMethodCounter  uint64
	beforeGetByNameAndMethodCounter uint64
	GetByNameAndMethodMock          mPermissionPrimeDBMockGetByNameAndMethod

	funcGetByUserID          func(userID int64) (pa1 []permissionentity.Permission, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mPermissionPrimeDBMockGetByUserID

	funcGetIDsByName          func(name string) (ia1 []int64, err error)
	funcGetIDsByNameOrigin    string
	inspectFuncGetIDsByName   func(name string)
	afterGetIDsByNameCounter  uint64
	beforeGetIDsByNameCounter uint64
	GetIDsByNameMock          mPermissionPrimeDBMockGetIDsByName

	funcUpdate          func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(permission permissionentity.Permission)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mPermissionPrimeDBMockUpdate
}

// NewPermissionPrimeDBMock returns a mock for mm_repository.PermissionPrimeDB
func NewPermissionPrimeDBMock(t minimock.Tester) *PermissionPrimeDBMock {
	m := &PermissionPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mPermissionPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*PermissionPrimeDBMockCreateParams{}

	m.GetAllMock = mPermissionPrimeDBMockGetAll{mock: m}

	m.GetAllUniqueNamesMock = mPermissionPrimeDBMockGetAllUniqueNames{mock: m}

	m.GetByIDMock = mPermissionPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*PermissionPrimeDBMockGetByIDParams{}

	m.GetByNameAndMethodMock = mPermissionPrimeDBMockGetByNameAndMethod{mock: m}
	m.GetByNameAndMethodMock.callArgs = []*PermissionPrimeDBMockGetByNameAndMethodParams{}

	m.GetByUserIDMock = mPermissionPrimeDBMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*PermissionPrimeDBMockGetByUserIDParams{}

	m.GetIDsByNameMock = mPermissionPrimeDBMockGetIDsByName{mock: m}
	m.GetIDsByNameMock.callArgs = []*PermissionPrimeDBMockGetIDsByNameParams{}

	m.UpdateMock = mPermissionPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*PermissionPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mPermissionPrimeDBMockCreate struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockCreateExpectation
	expectations       []*PermissionPrimeDBMockCreateExpectation

	callArgs []*PermissionPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockCreateExpectation specifies expectation struct of the PermissionPrimeDB.Create
type PermissionPrimeDBMockCreateExpectation struct {
	mock               *PermissionPrimeDBMock
	params             *PermissionPrimeDBMockCreateParams
	paramPtrs          *PermissionPrimeDBMockCreateParamPtrs
	expectationOrigins PermissionPrimeDBMockCreateExpectationOrigins
	results            *PermissionPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// PermissionPrimeDBMockCreateParams contains parameters of the PermissionPrimeDB.Create
type PermissionPrimeDBMockCreateParams struct {
	permission permissionentity.Permission
}

// PermissionPrimeDBMockCreateParamPtrs contains pointers to parameters of the PermissionPrimeDB.Create
type PermissionPrimeDBMockCreateParamPtrs struct {
	permission *permissionentity.Permission
}

// PermissionPrimeDBMockCreateResults contains results of the PermissionPrimeDB.Create
type PermissionPrimeDBMockCreateResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionPrimeDBMockCreateOrigins contains origins of expectations of the PermissionPrimeDB.Create
type PermissionPrimeDBMockCreateExpectationOrigins struct {
	origin           string
	originPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mPermissionPrimeDBMockCreate) Optional() *mPermissionPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for PermissionPrimeDB.Create
func (mmCreate *mPermissionPrimeDBMockCreate) Expect(permission permissionentity.Permission) *mPermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &PermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("PermissionPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &PermissionPrimeDBMockCreateParams{permission}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectPermissionParam1 sets up expected param permission for PermissionPrimeDB.Create
func (mmCreate *mPermissionPrimeDBMockCreate) ExpectPermissionParam1(permission permissionentity.Permission) *mPermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &PermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("PermissionPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &PermissionPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.permission = &permission
	mmCreate.defaultExpectation.expectationOrigins.originPermission = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.Create
func (mmCreate *mPermissionPrimeDBMockCreate) Inspect(f func(permission permissionentity.Permission)) *mPermissionPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by PermissionPrimeDB.Create
func (mmCreate *mPermissionPrimeDBMockCreate) Return(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &PermissionPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &PermissionPrimeDBMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the PermissionPrimeDB.Create method
func (mmCreate *mPermissionPrimeDBMockCreate) Set(f func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)) *PermissionPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the PermissionPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mPermissionPrimeDBMockCreate) When(permission permissionentity.Permission) *PermissionPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &PermissionPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &PermissionPrimeDBMockCreateParams{permission},
		expectationOrigins: PermissionPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up PermissionPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *PermissionPrimeDBMockCreateExpectation) Then(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	e.results = &PermissionPrimeDBMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionPrimeDB.Create should be invoked
func (mmCreate *mPermissionPrimeDBMockCreate) Times(n uint64) *mPermissionPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of PermissionPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mPermissionPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.PermissionPrimeDB
func (mmCreate *PermissionPrimeDBMock) Create(permission permissionentity.Permission) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(permission)
	}

	mm_params := PermissionPrimeDBMockCreateParams{permission}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := PermissionPrimeDBMockCreateParams{permission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permission != nil && !minimock.Equal(*mm_want_ptrs.permission, mm_got.permission) {
				mmCreate.t.Errorf("PermissionPrimeDBMock.Create got unexpected parameter permission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originPermission, *mm_want_ptrs.permission, mm_got.permission, minimock.Diff(*mm_want_ptrs.permission, mm_got.permission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("PermissionPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the PermissionPrimeDBMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(permission)
	}
	mmCreate.t.Fatalf("Unexpected call to PermissionPrimeDBMock.Create. %v", permission)
	return
}

// CreateAfterCounter returns a count of finished PermissionPrimeDBMock.Create invocations
func (mmCreate *PermissionPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of PermissionPrimeDBMock.Create invocations
func (mmCreate *PermissionPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to PermissionPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mPermissionPrimeDBMockCreate) Calls() []*PermissionPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*PermissionPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mPermissionPrimeDBMockGetAll struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockGetAllExpectation
	expectations       []*PermissionPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockGetAllExpectation specifies expectation struct of the PermissionPrimeDB.GetAll
type PermissionPrimeDBMockGetAllExpectation struct {
	mock *PermissionPrimeDBMock

	results      *PermissionPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// PermissionPrimeDBMockGetAllResults contains results of the PermissionPrimeDB.GetAll
type PermissionPrimeDBMockGetAllResults struct {
	pa1 []permissionentity.Permission
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mPermissionPrimeDBMockGetAll) Optional() *mPermissionPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for PermissionPrimeDB.GetAll
func (mmGetAll *mPermissionPrimeDBMockGetAll) Expect() *mPermissionPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("PermissionPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &PermissionPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.GetAll
func (mmGetAll *mPermissionPrimeDBMockGetAll) Inspect(f func()) *mPermissionPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by PermissionPrimeDB.GetAll
func (mmGetAll *mPermissionPrimeDBMockGetAll) Return(pa1 []permissionentity.Permission, err error) *PermissionPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("PermissionPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &PermissionPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &PermissionPrimeDBMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the PermissionPrimeDB.GetAll method
func (mmGetAll *mPermissionPrimeDBMockGetAll) Set(f func() (pa1 []permissionentity.Permission, err error)) *PermissionPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times PermissionPrimeDB.GetAll should be invoked
func (mmGetAll *mPermissionPrimeDBMockGetAll) Times(n uint64) *mPermissionPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of PermissionPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mPermissionPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.PermissionPrimeDB
func (mmGetAll *PermissionPrimeDBMock) GetAll() (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the PermissionPrimeDBMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to PermissionPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished PermissionPrimeDBMock.GetAll invocations
func (mmGetAll *PermissionPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of PermissionPrimeDBMock.GetAll invocations
func (mmGetAll *PermissionPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to PermissionPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mPermissionPrimeDBMockGetAllUniqueNames struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockGetAllUniqueNamesExpectation
	expectations       []*PermissionPrimeDBMockGetAllUniqueNamesExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockGetAllUniqueNamesExpectation specifies expectation struct of the PermissionPrimeDB.GetAllUniqueNames
type PermissionPrimeDBMockGetAllUniqueNamesExpectation struct {
	mock *PermissionPrimeDBMock

	results      *PermissionPrimeDBMockGetAllUniqueNamesResults
	returnOrigin string
	Counter      uint64
}

// PermissionPrimeDBMockGetAllUniqueNamesResults contains results of the PermissionPrimeDB.GetAllUniqueNames
type PermissionPrimeDBMockGetAllUniqueNamesResults struct {
	m1 map[string]struct {
	}
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) Optional() *mPermissionPrimeDBMockGetAllUniqueNames {
	mmGetAllUniqueNames.optional = true
	return mmGetAllUniqueNames
}

// Expect sets up expected params for PermissionPrimeDB.GetAllUniqueNames
func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) Expect() *mPermissionPrimeDBMockGetAllUniqueNames {
	if mmGetAllUniqueNames.mock.funcGetAllUniqueNames != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("PermissionPrimeDBMock.GetAllUniqueNames mock is already set by Set")
	}

	if mmGetAllUniqueNames.defaultExpectation == nil {
		mmGetAllUniqueNames.defaultExpectation = &PermissionPrimeDBMockGetAllUniqueNamesExpectation{}
	}

	return mmGetAllUniqueNames
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.GetAllUniqueNames
func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) Inspect(f func()) *mPermissionPrimeDBMockGetAllUniqueNames {
	if mmGetAllUniqueNames.mock.inspectFuncGetAllUniqueNames != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.GetAllUniqueNames")
	}

	mmGetAllUniqueNames.mock.inspectFuncGetAllUniqueNames = f

	return mmGetAllUniqueNames
}

// Return sets up results that will be returned by PermissionPrimeDB.GetAllUniqueNames
func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) Return(m1 map[string]struct {
}, err error) *PermissionPrimeDBMock {
	if mmGetAllUniqueNames.mock.funcGetAllUniqueNames != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("PermissionPrimeDBMock.GetAllUniqueNames mock is already set by Set")
	}

	if mmGetAllUniqueNames.defaultExpectation == nil {
		mmGetAllUniqueNames.defaultExpectation = &PermissionPrimeDBMockGetAllUniqueNamesExpectation{mock: mmGetAllUniqueNames.mock}
	}
	mmGetAllUniqueNames.defaultExpectation.results = &PermissionPrimeDBMockGetAllUniqueNamesResults{m1, err}
	mmGetAllUniqueNames.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllUniqueNames.mock
}

// Set uses given function f to mock the PermissionPrimeDB.GetAllUniqueNames method
func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) Set(f func() (m1 map[string]struct {
}, err error)) *PermissionPrimeDBMock {
	if mmGetAllUniqueNames.defaultExpectation != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.GetAllUniqueNames method")
	}

	if len(mmGetAllUniqueNames.expectations) > 0 {
		mmGetAllUniqueNames.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.GetAllUniqueNames method")
	}

	mmGetAllUniqueNames.mock.funcGetAllUniqueNames = f
	mmGetAllUniqueNames.mock.funcGetAllUniqueNamesOrigin = minimock.CallerInfo(1)
	return mmGetAllUniqueNames.mock
}

// Times sets number of times PermissionPrimeDB.GetAllUniqueNames should be invoked
func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) Times(n uint64) *mPermissionPrimeDBMockGetAllUniqueNames {
	if n == 0 {
		mmGetAllUniqueNames.mock.t.Fatalf("Times of PermissionPrimeDBMock.GetAllUniqueNames mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllUniqueNames.expectedInvocations, n)
	mmGetAllUniqueNames.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllUniqueNames
}

func (mmGetAllUniqueNames *mPermissionPrimeDBMockGetAllUniqueNames) invocationsDone() bool {
	if len(mmGetAllUniqueNames.expectations) == 0 && mmGetAllUniqueNames.defaultExpectation == nil && mmGetAllUniqueNames.mock.funcGetAllUniqueNames == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllUniqueNames.mock.afterGetAllUniqueNamesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllUniqueNames.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllUniqueNames implements mm_repository.PermissionPrimeDB
func (mmGetAllUniqueNames *PermissionPrimeDBMock) GetAllUniqueNames() (m1 map[string]struct {
}, err error) {
	mm_atomic.AddUint64(&mmGetAllUniqueNames.beforeGetAllUniqueNamesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllUniqueNames.afterGetAllUniqueNamesCounter, 1)

	mmGetAllUniqueNames.t.Helper()

	if mmGetAllUniqueNames.inspectFuncGetAllUniqueNames != nil {
		mmGetAllUniqueNames.inspectFuncGetAllUniqueNames()
	}

	if mmGetAllUniqueNames.GetAllUniqueNamesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllUniqueNames.GetAllUniqueNamesMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllUniqueNames.GetAllUniqueNamesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllUniqueNames.t.Fatal("No results are set for the PermissionPrimeDBMock.GetAllUniqueNames")
		}
		return (*mm_results).m1, (*mm_results).err
	}
	if mmGetAllUniqueNames.funcGetAllUniqueNames != nil {
		return mmGetAllUniqueNames.funcGetAllUniqueNames()
	}
	mmGetAllUniqueNames.t.Fatalf("Unexpected call to PermissionPrimeDBMock.GetAllUniqueNames.")
	return
}

// GetAllUniqueNamesAfterCounter returns a count of finished PermissionPrimeDBMock.GetAllUniqueNames invocations
func (mmGetAllUniqueNames *PermissionPrimeDBMock) GetAllUniqueNamesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllUniqueNames.afterGetAllUniqueNamesCounter)
}

// GetAllUniqueNamesBeforeCounter returns a count of PermissionPrimeDBMock.GetAllUniqueNames invocations
func (mmGetAllUniqueNames *PermissionPrimeDBMock) GetAllUniqueNamesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllUniqueNames.beforeGetAllUniqueNamesCounter)
}

// MinimockGetAllUniqueNamesDone returns true if the count of the GetAllUniqueNames invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockGetAllUniqueNamesDone() bool {
	if m.GetAllUniqueNamesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllUniqueNamesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllUniqueNamesMock.invocationsDone()
}

// MinimockGetAllUniqueNamesInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockGetAllUniqueNamesInspect() {
	for _, e := range m.GetAllUniqueNamesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to PermissionPrimeDBMock.GetAllUniqueNames")
		}
	}

	afterGetAllUniqueNamesCounter := mm_atomic.LoadUint64(&m.afterGetAllUniqueNamesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllUniqueNamesMock.defaultExpectation != nil && afterGetAllUniqueNamesCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetAllUniqueNames at\n%s", m.GetAllUniqueNamesMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllUniqueNames != nil && afterGetAllUniqueNamesCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetAllUniqueNames at\n%s", m.funcGetAllUniqueNamesOrigin)
	}

	if !m.GetAllUniqueNamesMock.invocationsDone() && afterGetAllUniqueNamesCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.GetAllUniqueNames at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllUniqueNamesMock.expectedInvocations), m.GetAllUniqueNamesMock.expectedInvocationsOrigin, afterGetAllUniqueNamesCounter)
	}
}

type mPermissionPrimeDBMockGetByID struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockGetByIDExpectation
	expectations       []*PermissionPrimeDBMockGetByIDExpectation

	callArgs []*PermissionPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockGetByIDExpectation specifies expectation struct of the PermissionPrimeDB.GetByID
type PermissionPrimeDBMockGetByIDExpectation struct {
	mock               *PermissionPrimeDBMock
	params             *PermissionPrimeDBMockGetByIDParams
	paramPtrs          *PermissionPrimeDBMockGetByIDParamPtrs
	expectationOrigins PermissionPrimeDBMockGetByIDExpectationOrigins
	results            *PermissionPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// PermissionPrimeDBMockGetByIDParams contains parameters of the PermissionPrimeDB.GetByID
type PermissionPrimeDBMockGetByIDParams struct {
	id int64
}

// PermissionPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the PermissionPrimeDB.GetByID
type PermissionPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// PermissionPrimeDBMockGetByIDResults contains results of the PermissionPrimeDB.GetByID
type PermissionPrimeDBMockGetByIDResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionPrimeDBMockGetByIDOrigins contains origins of expectations of the PermissionPrimeDB.GetByID
type PermissionPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mPermissionPrimeDBMockGetByID) Optional() *mPermissionPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for PermissionPrimeDB.GetByID
func (mmGetByID *mPermissionPrimeDBMockGetByID) Expect(id int64) *mPermissionPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &PermissionPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("PermissionPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &PermissionPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for PermissionPrimeDB.GetByID
func (mmGetByID *mPermissionPrimeDBMockGetByID) ExpectIdParam1(id int64) *mPermissionPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &PermissionPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("PermissionPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &PermissionPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.GetByID
func (mmGetByID *mPermissionPrimeDBMockGetByID) Inspect(f func(id int64)) *mPermissionPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by PermissionPrimeDB.GetByID
func (mmGetByID *mPermissionPrimeDBMockGetByID) Return(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &PermissionPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &PermissionPrimeDBMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the PermissionPrimeDB.GetByID method
func (mmGetByID *mPermissionPrimeDBMockGetByID) Set(f func(id int64) (p1 permissionentity.Permission, err error)) *PermissionPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the PermissionPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mPermissionPrimeDBMockGetByID) When(id int64) *PermissionPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &PermissionPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &PermissionPrimeDBMockGetByIDParams{id},
		expectationOrigins: PermissionPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up PermissionPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *PermissionPrimeDBMockGetByIDExpectation) Then(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	e.results = &PermissionPrimeDBMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionPrimeDB.GetByID should be invoked
func (mmGetByID *mPermissionPrimeDBMockGetByID) Times(n uint64) *mPermissionPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of PermissionPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mPermissionPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.PermissionPrimeDB
func (mmGetByID *PermissionPrimeDBMock) GetByID(id int64) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := PermissionPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := PermissionPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("PermissionPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("PermissionPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the PermissionPrimeDBMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to PermissionPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished PermissionPrimeDBMock.GetByID invocations
func (mmGetByID *PermissionPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of PermissionPrimeDBMock.GetByID invocations
func (mmGetByID *PermissionPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to PermissionPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mPermissionPrimeDBMockGetByID) Calls() []*PermissionPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*PermissionPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mPermissionPrimeDBMockGetByNameAndMethod struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockGetByNameAndMethodExpectation
	expectations       []*PermissionPrimeDBMockGetByNameAndMethodExpectation

	callArgs []*PermissionPrimeDBMockGetByNameAndMethodParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockGetByNameAndMethodExpectation specifies expectation struct of the PermissionPrimeDB.GetByNameAndMethod
type PermissionPrimeDBMockGetByNameAndMethodExpectation struct {
	mock               *PermissionPrimeDBMock
	params             *PermissionPrimeDBMockGetByNameAndMethodParams
	paramPtrs          *PermissionPrimeDBMockGetByNameAndMethodParamPtrs
	expectationOrigins PermissionPrimeDBMockGetByNameAndMethodExpectationOrigins
	results            *PermissionPrimeDBMockGetByNameAndMethodResults
	returnOrigin       string
	Counter            uint64
}

// PermissionPrimeDBMockGetByNameAndMethodParams contains parameters of the PermissionPrimeDB.GetByNameAndMethod
type PermissionPrimeDBMockGetByNameAndMethodParams struct {
	name   string
	method string
}

// PermissionPrimeDBMockGetByNameAndMethodParamPtrs contains pointers to parameters of the PermissionPrimeDB.GetByNameAndMethod
type PermissionPrimeDBMockGetByNameAndMethodParamPtrs struct {
	name   *string
	method *string
}

// PermissionPrimeDBMockGetByNameAndMethodResults contains results of the PermissionPrimeDB.GetByNameAndMethod
type PermissionPrimeDBMockGetByNameAndMethodResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionPrimeDBMockGetByNameAndMethodOrigins contains origins of expectations of the PermissionPrimeDB.GetByNameAndMethod
type PermissionPrimeDBMockGetByNameAndMethodExpectationOrigins struct {
	origin       string
	originName   string
	originMethod string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Optional() *mPermissionPrimeDBMockGetByNameAndMethod {
	mmGetByNameAndMethod.optional = true
	return mmGetByNameAndMethod
}

// Expect sets up expected params for PermissionPrimeDB.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Expect(name string, method string) *mPermissionPrimeDBMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionPrimeDBMockGetByNameAndMethodExpectation{}
	}

	if mmGetByNameAndMethod.defaultExpectation.paramPtrs != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by ExpectParams functions")
	}

	mmGetByNameAndMethod.defaultExpectation.params = &PermissionPrimeDBMockGetByNameAndMethodParams{name, method}
	mmGetByNameAndMethod.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByNameAndMethod.expectations {
		if minimock.Equal(e.params, mmGetByNameAndMethod.defaultExpectation.params) {
			mmGetByNameAndMethod.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByNameAndMethod.defaultExpectation.params)
		}
	}

	return mmGetByNameAndMethod
}

// ExpectNameParam1 sets up expected param name for PermissionPrimeDB.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) ExpectNameParam1(name string) *mPermissionPrimeDBMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionPrimeDBMockGetByNameAndMethodExpectation{}
	}

	if mmGetByNameAndMethod.defaultExpectation.params != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Expect")
	}

	if mmGetByNameAndMethod.defaultExpectation.paramPtrs == nil {
		mmGetByNameAndMethod.defaultExpectation.paramPtrs = &PermissionPrimeDBMockGetByNameAndMethodParamPtrs{}
	}
	mmGetByNameAndMethod.defaultExpectation.paramPtrs.name = &name
	mmGetByNameAndMethod.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmGetByNameAndMethod
}

// ExpectMethodParam2 sets up expected param method for PermissionPrimeDB.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) ExpectMethodParam2(method string) *mPermissionPrimeDBMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionPrimeDBMockGetByNameAndMethodExpectation{}
	}

	if mmGetByNameAndMethod.defaultExpectation.params != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Expect")
	}

	if mmGetByNameAndMethod.defaultExpectation.paramPtrs == nil {
		mmGetByNameAndMethod.defaultExpectation.paramPtrs = &PermissionPrimeDBMockGetByNameAndMethodParamPtrs{}
	}
	mmGetByNameAndMethod.defaultExpectation.paramPtrs.method = &method
	mmGetByNameAndMethod.defaultExpectation.expectationOrigins.originMethod = minimock.CallerInfo(1)

	return mmGetByNameAndMethod
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Inspect(f func(name string, method string)) *mPermissionPrimeDBMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.inspectFuncGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.GetByNameAndMethod")
	}

	mmGetByNameAndMethod.mock.inspectFuncGetByNameAndMethod = f

	return mmGetByNameAndMethod
}

// Return sets up results that will be returned by PermissionPrimeDB.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Return(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionPrimeDBMockGetByNameAndMethodExpectation{mock: mmGetByNameAndMethod.mock}
	}
	mmGetByNameAndMethod.defaultExpectation.results = &PermissionPrimeDBMockGetByNameAndMethodResults{p1, err}
	mmGetByNameAndMethod.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByNameAndMethod.mock
}

// Set uses given function f to mock the PermissionPrimeDB.GetByNameAndMethod method
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Set(f func(name string, method string) (p1 permissionentity.Permission, err error)) *PermissionPrimeDBMock {
	if mmGetByNameAndMethod.defaultExpectation != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.GetByNameAndMethod method")
	}

	if len(mmGetByNameAndMethod.expectations) > 0 {
		mmGetByNameAndMethod.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.GetByNameAndMethod method")
	}

	mmGetByNameAndMethod.mock.funcGetByNameAndMethod = f
	mmGetByNameAndMethod.mock.funcGetByNameAndMethodOrigin = minimock.CallerInfo(1)
	return mmGetByNameAndMethod.mock
}

// When sets expectation for the PermissionPrimeDB.GetByNameAndMethod which will trigger the result defined by the following
// Then helper
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) When(name string, method string) *PermissionPrimeDBMockGetByNameAndMethodExpectation {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionPrimeDBMock.GetByNameAndMethod mock is already set by Set")
	}

	expectation := &PermissionPrimeDBMockGetByNameAndMethodExpectation{
		mock:               mmGetByNameAndMethod.mock,
		params:             &PermissionPrimeDBMockGetByNameAndMethodParams{name, method},
		expectationOrigins: PermissionPrimeDBMockGetByNameAndMethodExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByNameAndMethod.expectations = append(mmGetByNameAndMethod.expectations, expectation)
	return expectation
}

// Then sets up PermissionPrimeDB.GetByNameAndMethod return parameters for the expectation previously defined by the When method
func (e *PermissionPrimeDBMockGetByNameAndMethodExpectation) Then(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	e.results = &PermissionPrimeDBMockGetByNameAndMethodResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionPrimeDB.GetByNameAndMethod should be invoked
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Times(n uint64) *mPermissionPrimeDBMockGetByNameAndMethod {
	if n == 0 {
		mmGetByNameAndMethod.mock.t.Fatalf("Times of PermissionPrimeDBMock.GetByNameAndMethod mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByNameAndMethod.expectedInvocations, n)
	mmGetByNameAndMethod.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByNameAndMethod
}

func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) invocationsDone() bool {
	if len(mmGetByNameAndMethod.expectations) == 0 && mmGetByNameAndMethod.defaultExpectation == nil && mmGetByNameAndMethod.mock.funcGetByNameAndMethod == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByNameAndMethod.mock.afterGetByNameAndMethodCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByNameAndMethod.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByNameAndMethod implements mm_repository.PermissionPrimeDB
func (mmGetByNameAndMethod *PermissionPrimeDBMock) GetByNameAndMethod(name string, method string) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByNameAndMethod.beforeGetByNameAndMethodCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByNameAndMethod.afterGetByNameAndMethodCounter, 1)

	mmGetByNameAndMethod.t.Helper()

	if mmGetByNameAndMethod.inspectFuncGetByNameAndMethod != nil {
		mmGetByNameAndMethod.inspectFuncGetByNameAndMethod(name, method)
	}

	mm_params := PermissionPrimeDBMockGetByNameAndMethodParams{name, method}

	// Record call args
	mmGetByNameAndMethod.GetByNameAndMethodMock.mutex.Lock()
	mmGetByNameAndMethod.GetByNameAndMethodMock.callArgs = append(mmGetByNameAndMethod.GetByNameAndMethodMock.callArgs, &mm_params)
	mmGetByNameAndMethod.GetByNameAndMethodMock.mutex.Unlock()

	for _, e := range mmGetByNameAndMethod.GetByNameAndMethodMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.params
		mm_want_ptrs := mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.paramPtrs

		mm_got := PermissionPrimeDBMockGetByNameAndMethodParams{name, method}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmGetByNameAndMethod.t.Errorf("PermissionPrimeDBMock.GetByNameAndMethod got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

			if mm_want_ptrs.method != nil && !minimock.Equal(*mm_want_ptrs.method, mm_got.method) {
				mmGetByNameAndMethod.t.Errorf("PermissionPrimeDBMock.GetByNameAndMethod got unexpected parameter method, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.originMethod, *mm_want_ptrs.method, mm_got.method, minimock.Diff(*mm_want_ptrs.method, mm_got.method))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByNameAndMethod.t.Errorf("PermissionPrimeDBMock.GetByNameAndMethod got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByNameAndMethod.t.Fatal("No results are set for the PermissionPrimeDBMock.GetByNameAndMethod")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByNameAndMethod.funcGetByNameAndMethod != nil {
		return mmGetByNameAndMethod.funcGetByNameAndMethod(name, method)
	}
	mmGetByNameAndMethod.t.Fatalf("Unexpected call to PermissionPrimeDBMock.GetByNameAndMethod. %v %v", name, method)
	return
}

// GetByNameAndMethodAfterCounter returns a count of finished PermissionPrimeDBMock.GetByNameAndMethod invocations
func (mmGetByNameAndMethod *PermissionPrimeDBMock) GetByNameAndMethodAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByNameAndMethod.afterGetByNameAndMethodCounter)
}

// GetByNameAndMethodBeforeCounter returns a count of PermissionPrimeDBMock.GetByNameAndMethod invocations
func (mmGetByNameAndMethod *PermissionPrimeDBMock) GetByNameAndMethodBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByNameAndMethod.beforeGetByNameAndMethodCounter)
}

// Calls returns a list of arguments used in each call to PermissionPrimeDBMock.GetByNameAndMethod.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByNameAndMethod *mPermissionPrimeDBMockGetByNameAndMethod) Calls() []*PermissionPrimeDBMockGetByNameAndMethodParams {
	mmGetByNameAndMethod.mutex.RLock()

	argCopy := make([]*PermissionPrimeDBMockGetByNameAndMethodParams, len(mmGetByNameAndMethod.callArgs))
	copy(argCopy, mmGetByNameAndMethod.callArgs)

	mmGetByNameAndMethod.mutex.RUnlock()

	return argCopy
}

// MinimockGetByNameAndMethodDone returns true if the count of the GetByNameAndMethod invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockGetByNameAndMethodDone() bool {
	if m.GetByNameAndMethodMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByNameAndMethodMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByNameAndMethodMock.invocationsDone()
}

// MinimockGetByNameAndMethodInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockGetByNameAndMethodInspect() {
	for _, e := range m.GetByNameAndMethodMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByNameAndMethod at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByNameAndMethodCounter := mm_atomic.LoadUint64(&m.afterGetByNameAndMethodCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByNameAndMethodMock.defaultExpectation != nil && afterGetByNameAndMethodCounter < 1 {
		if m.GetByNameAndMethodMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByNameAndMethod at\n%s", m.GetByNameAndMethodMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByNameAndMethod at\n%s with params: %#v", m.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.origin, *m.GetByNameAndMethodMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByNameAndMethod != nil && afterGetByNameAndMethodCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByNameAndMethod at\n%s", m.funcGetByNameAndMethodOrigin)
	}

	if !m.GetByNameAndMethodMock.invocationsDone() && afterGetByNameAndMethodCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.GetByNameAndMethod at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByNameAndMethodMock.expectedInvocations), m.GetByNameAndMethodMock.expectedInvocationsOrigin, afterGetByNameAndMethodCounter)
	}
}

type mPermissionPrimeDBMockGetByUserID struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockGetByUserIDExpectation
	expectations       []*PermissionPrimeDBMockGetByUserIDExpectation

	callArgs []*PermissionPrimeDBMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockGetByUserIDExpectation specifies expectation struct of the PermissionPrimeDB.GetByUserID
type PermissionPrimeDBMockGetByUserIDExpectation struct {
	mock               *PermissionPrimeDBMock
	params             *PermissionPrimeDBMockGetByUserIDParams
	paramPtrs          *PermissionPrimeDBMockGetByUserIDParamPtrs
	expectationOrigins PermissionPrimeDBMockGetByUserIDExpectationOrigins
	results            *PermissionPrimeDBMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// PermissionPrimeDBMockGetByUserIDParams contains parameters of the PermissionPrimeDB.GetByUserID
type PermissionPrimeDBMockGetByUserIDParams struct {
	userID int64
}

// PermissionPrimeDBMockGetByUserIDParamPtrs contains pointers to parameters of the PermissionPrimeDB.GetByUserID
type PermissionPrimeDBMockGetByUserIDParamPtrs struct {
	userID *int64
}

// PermissionPrimeDBMockGetByUserIDResults contains results of the PermissionPrimeDB.GetByUserID
type PermissionPrimeDBMockGetByUserIDResults struct {
	pa1 []permissionentity.Permission
	err error
}

// PermissionPrimeDBMockGetByUserIDOrigins contains origins of expectations of the PermissionPrimeDB.GetByUserID
type PermissionPrimeDBMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Optional() *mPermissionPrimeDBMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for PermissionPrimeDB.GetByUserID
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Expect(userID int64) *mPermissionPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &PermissionPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionPrimeDBMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &PermissionPrimeDBMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for PermissionPrimeDB.GetByUserID
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) ExpectUserIDParam1(userID int64) *mPermissionPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &PermissionPrimeDBMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionPrimeDBMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &PermissionPrimeDBMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.GetByUserID
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Inspect(f func(userID int64)) *mPermissionPrimeDBMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by PermissionPrimeDB.GetByUserID
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Return(pa1 []permissionentity.Permission, err error) *PermissionPrimeDBMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionPrimeDBMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &PermissionPrimeDBMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &PermissionPrimeDBMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the PermissionPrimeDB.GetByUserID method
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Set(f func(userID int64) (pa1 []permissionentity.Permission, err error)) *PermissionPrimeDBMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the PermissionPrimeDB.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) When(userID int64) *PermissionPrimeDBMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionPrimeDBMock.GetByUserID mock is already set by Set")
	}

	expectation := &PermissionPrimeDBMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &PermissionPrimeDBMockGetByUserIDParams{userID},
		expectationOrigins: PermissionPrimeDBMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up PermissionPrimeDB.GetByUserID return parameters for the expectation previously defined by the When method
func (e *PermissionPrimeDBMockGetByUserIDExpectation) Then(pa1 []permissionentity.Permission, err error) *PermissionPrimeDBMock {
	e.results = &PermissionPrimeDBMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times PermissionPrimeDB.GetByUserID should be invoked
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Times(n uint64) *mPermissionPrimeDBMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of PermissionPrimeDBMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_repository.PermissionPrimeDB
func (mmGetByUserID *PermissionPrimeDBMock) GetByUserID(userID int64) (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := PermissionPrimeDBMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := PermissionPrimeDBMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("PermissionPrimeDBMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("PermissionPrimeDBMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the PermissionPrimeDBMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to PermissionPrimeDBMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished PermissionPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *PermissionPrimeDBMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of PermissionPrimeDBMock.GetByUserID invocations
func (mmGetByUserID *PermissionPrimeDBMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to PermissionPrimeDBMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mPermissionPrimeDBMockGetByUserID) Calls() []*PermissionPrimeDBMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*PermissionPrimeDBMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mPermissionPrimeDBMockGetIDsByName struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockGetIDsByNameExpectation
	expectations       []*PermissionPrimeDBMockGetIDsByNameExpectation

	callArgs []*PermissionPrimeDBMockGetIDsByNameParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockGetIDsByNameExpectation specifies expectation struct of the PermissionPrimeDB.GetIDsByName
type PermissionPrimeDBMockGetIDsByNameExpectation struct {
	mock               *PermissionPrimeDBMock
	params             *PermissionPrimeDBMockGetIDsByNameParams
	paramPtrs          *PermissionPrimeDBMockGetIDsByNameParamPtrs
	expectationOrigins PermissionPrimeDBMockGetIDsByNameExpectationOrigins
	results            *PermissionPrimeDBMockGetIDsByNameResults
	returnOrigin       string
	Counter            uint64
}

// PermissionPrimeDBMockGetIDsByNameParams contains parameters of the PermissionPrimeDB.GetIDsByName
type PermissionPrimeDBMockGetIDsByNameParams struct {
	name string
}

// PermissionPrimeDBMockGetIDsByNameParamPtrs contains pointers to parameters of the PermissionPrimeDB.GetIDsByName
type PermissionPrimeDBMockGetIDsByNameParamPtrs struct {
	name *string
}

// PermissionPrimeDBMockGetIDsByNameResults contains results of the PermissionPrimeDB.GetIDsByName
type PermissionPrimeDBMockGetIDsByNameResults struct {
	ia1 []int64
	err error
}

// PermissionPrimeDBMockGetIDsByNameOrigins contains origins of expectations of the PermissionPrimeDB.GetIDsByName
type PermissionPrimeDBMockGetIDsByNameExpectationOrigins struct {
	origin     string
	originName string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Optional() *mPermissionPrimeDBMockGetIDsByName {
	mmGetIDsByName.optional = true
	return mmGetIDsByName
}

// Expect sets up expected params for PermissionPrimeDB.GetIDsByName
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Expect(name string) *mPermissionPrimeDBMockGetIDsByName {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionPrimeDBMock.GetIDsByName mock is already set by Set")
	}

	if mmGetIDsByName.defaultExpectation == nil {
		mmGetIDsByName.defaultExpectation = &PermissionPrimeDBMockGetIDsByNameExpectation{}
	}

	if mmGetIDsByName.defaultExpectation.paramPtrs != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionPrimeDBMock.GetIDsByName mock is already set by ExpectParams functions")
	}

	mmGetIDsByName.defaultExpectation.params = &PermissionPrimeDBMockGetIDsByNameParams{name}
	mmGetIDsByName.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetIDsByName.expectations {
		if minimock.Equal(e.params, mmGetIDsByName.defaultExpectation.params) {
			mmGetIDsByName.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetIDsByName.defaultExpectation.params)
		}
	}

	return mmGetIDsByName
}

// ExpectNameParam1 sets up expected param name for PermissionPrimeDB.GetIDsByName
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) ExpectNameParam1(name string) *mPermissionPrimeDBMockGetIDsByName {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionPrimeDBMock.GetIDsByName mock is already set by Set")
	}

	if mmGetIDsByName.defaultExpectation == nil {
		mmGetIDsByName.defaultExpectation = &PermissionPrimeDBMockGetIDsByNameExpectation{}
	}

	if mmGetIDsByName.defaultExpectation.params != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionPrimeDBMock.GetIDsByName mock is already set by Expect")
	}

	if mmGetIDsByName.defaultExpectation.paramPtrs == nil {
		mmGetIDsByName.defaultExpectation.paramPtrs = &PermissionPrimeDBMockGetIDsByNameParamPtrs{}
	}
	mmGetIDsByName.defaultExpectation.paramPtrs.name = &name
	mmGetIDsByName.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmGetIDsByName
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.GetIDsByName
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Inspect(f func(name string)) *mPermissionPrimeDBMockGetIDsByName {
	if mmGetIDsByName.mock.inspectFuncGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.GetIDsByName")
	}

	mmGetIDsByName.mock.inspectFuncGetIDsByName = f

	return mmGetIDsByName
}

// Return sets up results that will be returned by PermissionPrimeDB.GetIDsByName
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Return(ia1 []int64, err error) *PermissionPrimeDBMock {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionPrimeDBMock.GetIDsByName mock is already set by Set")
	}

	if mmGetIDsByName.defaultExpectation == nil {
		mmGetIDsByName.defaultExpectation = &PermissionPrimeDBMockGetIDsByNameExpectation{mock: mmGetIDsByName.mock}
	}
	mmGetIDsByName.defaultExpectation.results = &PermissionPrimeDBMockGetIDsByNameResults{ia1, err}
	mmGetIDsByName.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetIDsByName.mock
}

// Set uses given function f to mock the PermissionPrimeDB.GetIDsByName method
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Set(f func(name string) (ia1 []int64, err error)) *PermissionPrimeDBMock {
	if mmGetIDsByName.defaultExpectation != nil {
		mmGetIDsByName.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.GetIDsByName method")
	}

	if len(mmGetIDsByName.expectations) > 0 {
		mmGetIDsByName.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.GetIDsByName method")
	}

	mmGetIDsByName.mock.funcGetIDsByName = f
	mmGetIDsByName.mock.funcGetIDsByNameOrigin = minimock.CallerInfo(1)
	return mmGetIDsByName.mock
}

// When sets expectation for the PermissionPrimeDB.GetIDsByName which will trigger the result defined by the following
// Then helper
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) When(name string) *PermissionPrimeDBMockGetIDsByNameExpectation {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionPrimeDBMock.GetIDsByName mock is already set by Set")
	}

	expectation := &PermissionPrimeDBMockGetIDsByNameExpectation{
		mock:               mmGetIDsByName.mock,
		params:             &PermissionPrimeDBMockGetIDsByNameParams{name},
		expectationOrigins: PermissionPrimeDBMockGetIDsByNameExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetIDsByName.expectations = append(mmGetIDsByName.expectations, expectation)
	return expectation
}

// Then sets up PermissionPrimeDB.GetIDsByName return parameters for the expectation previously defined by the When method
func (e *PermissionPrimeDBMockGetIDsByNameExpectation) Then(ia1 []int64, err error) *PermissionPrimeDBMock {
	e.results = &PermissionPrimeDBMockGetIDsByNameResults{ia1, err}
	return e.mock
}

// Times sets number of times PermissionPrimeDB.GetIDsByName should be invoked
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Times(n uint64) *mPermissionPrimeDBMockGetIDsByName {
	if n == 0 {
		mmGetIDsByName.mock.t.Fatalf("Times of PermissionPrimeDBMock.GetIDsByName mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetIDsByName.expectedInvocations, n)
	mmGetIDsByName.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetIDsByName
}

func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) invocationsDone() bool {
	if len(mmGetIDsByName.expectations) == 0 && mmGetIDsByName.defaultExpectation == nil && mmGetIDsByName.mock.funcGetIDsByName == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetIDsByName.mock.afterGetIDsByNameCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetIDsByName.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetIDsByName implements mm_repository.PermissionPrimeDB
func (mmGetIDsByName *PermissionPrimeDBMock) GetIDsByName(name string) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmGetIDsByName.beforeGetIDsByNameCounter, 1)
	defer mm_atomic.AddUint64(&mmGetIDsByName.afterGetIDsByNameCounter, 1)

	mmGetIDsByName.t.Helper()

	if mmGetIDsByName.inspectFuncGetIDsByName != nil {
		mmGetIDsByName.inspectFuncGetIDsByName(name)
	}

	mm_params := PermissionPrimeDBMockGetIDsByNameParams{name}

	// Record call args
	mmGetIDsByName.GetIDsByNameMock.mutex.Lock()
	mmGetIDsByName.GetIDsByNameMock.callArgs = append(mmGetIDsByName.GetIDsByNameMock.callArgs, &mm_params)
	mmGetIDsByName.GetIDsByNameMock.mutex.Unlock()

	for _, e := range mmGetIDsByName.GetIDsByNameMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmGetIDsByName.GetIDsByNameMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetIDsByName.GetIDsByNameMock.defaultExpectation.Counter, 1)
		mm_want := mmGetIDsByName.GetIDsByNameMock.defaultExpectation.params
		mm_want_ptrs := mmGetIDsByName.GetIDsByNameMock.defaultExpectation.paramPtrs

		mm_got := PermissionPrimeDBMockGetIDsByNameParams{name}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmGetIDsByName.t.Errorf("PermissionPrimeDBMock.GetIDsByName got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetIDsByName.GetIDsByNameMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetIDsByName.t.Errorf("PermissionPrimeDBMock.GetIDsByName got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetIDsByName.GetIDsByNameMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetIDsByName.GetIDsByNameMock.defaultExpectation.results
		if mm_results == nil {
			mmGetIDsByName.t.Fatal("No results are set for the PermissionPrimeDBMock.GetIDsByName")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmGetIDsByName.funcGetIDsByName != nil {
		return mmGetIDsByName.funcGetIDsByName(name)
	}
	mmGetIDsByName.t.Fatalf("Unexpected call to PermissionPrimeDBMock.GetIDsByName. %v", name)
	return
}

// GetIDsByNameAfterCounter returns a count of finished PermissionPrimeDBMock.GetIDsByName invocations
func (mmGetIDsByName *PermissionPrimeDBMock) GetIDsByNameAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetIDsByName.afterGetIDsByNameCounter)
}

// GetIDsByNameBeforeCounter returns a count of PermissionPrimeDBMock.GetIDsByName invocations
func (mmGetIDsByName *PermissionPrimeDBMock) GetIDsByNameBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetIDsByName.beforeGetIDsByNameCounter)
}

// Calls returns a list of arguments used in each call to PermissionPrimeDBMock.GetIDsByName.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetIDsByName *mPermissionPrimeDBMockGetIDsByName) Calls() []*PermissionPrimeDBMockGetIDsByNameParams {
	mmGetIDsByName.mutex.RLock()

	argCopy := make([]*PermissionPrimeDBMockGetIDsByNameParams, len(mmGetIDsByName.callArgs))
	copy(argCopy, mmGetIDsByName.callArgs)

	mmGetIDsByName.mutex.RUnlock()

	return argCopy
}

// MinimockGetIDsByNameDone returns true if the count of the GetIDsByName invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockGetIDsByNameDone() bool {
	if m.GetIDsByNameMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetIDsByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetIDsByNameMock.invocationsDone()
}

// MinimockGetIDsByNameInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockGetIDsByNameInspect() {
	for _, e := range m.GetIDsByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetIDsByName at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetIDsByNameCounter := mm_atomic.LoadUint64(&m.afterGetIDsByNameCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetIDsByNameMock.defaultExpectation != nil && afterGetIDsByNameCounter < 1 {
		if m.GetIDsByNameMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetIDsByName at\n%s", m.GetIDsByNameMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.GetIDsByName at\n%s with params: %#v", m.GetIDsByNameMock.defaultExpectation.expectationOrigins.origin, *m.GetIDsByNameMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetIDsByName != nil && afterGetIDsByNameCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.GetIDsByName at\n%s", m.funcGetIDsByNameOrigin)
	}

	if !m.GetIDsByNameMock.invocationsDone() && afterGetIDsByNameCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.GetIDsByName at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetIDsByNameMock.expectedInvocations), m.GetIDsByNameMock.expectedInvocationsOrigin, afterGetIDsByNameCounter)
	}
}

type mPermissionPrimeDBMockUpdate struct {
	optional           bool
	mock               *PermissionPrimeDBMock
	defaultExpectation *PermissionPrimeDBMockUpdateExpectation
	expectations       []*PermissionPrimeDBMockUpdateExpectation

	callArgs []*PermissionPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionPrimeDBMockUpdateExpectation specifies expectation struct of the PermissionPrimeDB.Update
type PermissionPrimeDBMockUpdateExpectation struct {
	mock               *PermissionPrimeDBMock
	params             *PermissionPrimeDBMockUpdateParams
	paramPtrs          *PermissionPrimeDBMockUpdateParamPtrs
	expectationOrigins PermissionPrimeDBMockUpdateExpectationOrigins
	results            *PermissionPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// PermissionPrimeDBMockUpdateParams contains parameters of the PermissionPrimeDB.Update
type PermissionPrimeDBMockUpdateParams struct {
	permission permissionentity.Permission
}

// PermissionPrimeDBMockUpdateParamPtrs contains pointers to parameters of the PermissionPrimeDB.Update
type PermissionPrimeDBMockUpdateParamPtrs struct {
	permission *permissionentity.Permission
}

// PermissionPrimeDBMockUpdateResults contains results of the PermissionPrimeDB.Update
type PermissionPrimeDBMockUpdateResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionPrimeDBMockUpdateOrigins contains origins of expectations of the PermissionPrimeDB.Update
type PermissionPrimeDBMockUpdateExpectationOrigins struct {
	origin           string
	originPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mPermissionPrimeDBMockUpdate) Optional() *mPermissionPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for PermissionPrimeDB.Update
func (mmUpdate *mPermissionPrimeDBMockUpdate) Expect(permission permissionentity.Permission) *mPermissionPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &PermissionPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("PermissionPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &PermissionPrimeDBMockUpdateParams{permission}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectPermissionParam1 sets up expected param permission for PermissionPrimeDB.Update
func (mmUpdate *mPermissionPrimeDBMockUpdate) ExpectPermissionParam1(permission permissionentity.Permission) *mPermissionPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &PermissionPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("PermissionPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &PermissionPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.permission = &permission
	mmUpdate.defaultExpectation.expectationOrigins.originPermission = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the PermissionPrimeDB.Update
func (mmUpdate *mPermissionPrimeDBMockUpdate) Inspect(f func(permission permissionentity.Permission)) *mPermissionPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for PermissionPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by PermissionPrimeDB.Update
func (mmUpdate *mPermissionPrimeDBMockUpdate) Return(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &PermissionPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &PermissionPrimeDBMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the PermissionPrimeDB.Update method
func (mmUpdate *mPermissionPrimeDBMockUpdate) Set(f func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)) *PermissionPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the PermissionPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the PermissionPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the PermissionPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mPermissionPrimeDBMockUpdate) When(permission permissionentity.Permission) *PermissionPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &PermissionPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &PermissionPrimeDBMockUpdateParams{permission},
		expectationOrigins: PermissionPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up PermissionPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *PermissionPrimeDBMockUpdateExpectation) Then(p1 permissionentity.Permission, err error) *PermissionPrimeDBMock {
	e.results = &PermissionPrimeDBMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionPrimeDB.Update should be invoked
func (mmUpdate *mPermissionPrimeDBMockUpdate) Times(n uint64) *mPermissionPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of PermissionPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mPermissionPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.PermissionPrimeDB
func (mmUpdate *PermissionPrimeDBMock) Update(permission permissionentity.Permission) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(permission)
	}

	mm_params := PermissionPrimeDBMockUpdateParams{permission}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := PermissionPrimeDBMockUpdateParams{permission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permission != nil && !minimock.Equal(*mm_want_ptrs.permission, mm_got.permission) {
				mmUpdate.t.Errorf("PermissionPrimeDBMock.Update got unexpected parameter permission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originPermission, *mm_want_ptrs.permission, mm_got.permission, minimock.Diff(*mm_want_ptrs.permission, mm_got.permission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("PermissionPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the PermissionPrimeDBMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(permission)
	}
	mmUpdate.t.Fatalf("Unexpected call to PermissionPrimeDBMock.Update. %v", permission)
	return
}

// UpdateAfterCounter returns a count of finished PermissionPrimeDBMock.Update invocations
func (mmUpdate *PermissionPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of PermissionPrimeDBMock.Update invocations
func (mmUpdate *PermissionPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to PermissionPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mPermissionPrimeDBMockUpdate) Calls() []*PermissionPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*PermissionPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *PermissionPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *PermissionPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to PermissionPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *PermissionPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllUniqueNamesInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByNameAndMethodInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetIDsByNameInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *PermissionPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *PermissionPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllUniqueNamesDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByNameAndMethodDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetIDsByNameDone() &&
		m.MinimockUpdateDone()
}
