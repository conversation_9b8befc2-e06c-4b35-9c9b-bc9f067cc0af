// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/service.PermissionDomainService -o permission_domain_service_mock.go -n PermissionDomainServiceMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"github.com/gojuno/minimock/v3"
)

// PermissionDomainServiceMock implements mm_service.PermissionDomainService
type PermissionDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcAssignPermissionToCategory          func(categoryID int64, permissionID int64) (err error)
	funcAssignPermissionToCategoryOrigin    string
	inspectFuncAssignPermissionToCategory   func(categoryID int64, permissionID int64)
	afterAssignPermissionToCategoryCounter  uint64
	beforeAssignPermissionToCategoryCounter uint64
	AssignPermissionToCategoryMock          mPermissionDomainServiceMockAssignPermissionToCategory

	funcCreate          func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(permission permissionentity.Permission)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mPermissionDomainServiceMockCreate

	funcGetAll          func() (pa1 []permissionentity.Permission, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mPermissionDomainServiceMockGetAll

	funcGetAllCategoryPermissions          func() (ca1 []categoryentity.CategoryPermission, err error)
	funcGetAllCategoryPermissionsOrigin    string
	inspectFuncGetAllCategoryPermissions   func()
	afterGetAllCategoryPermissionsCounter  uint64
	beforeGetAllCategoryPermissionsCounter uint64
	GetAllCategoryPermissionsMock          mPermissionDomainServiceMockGetAllCategoryPermissions

	funcGetAllUniqueNames func() (m1 map[string]struct {
	}, err error)
	funcGetAllUniqueNamesOrigin    string
	inspectFuncGetAllUniqueNames   func()
	afterGetAllUniqueNamesCounter  uint64
	beforeGetAllUniqueNamesCounter uint64
	GetAllUniqueNamesMock          mPermissionDomainServiceMockGetAllUniqueNames

	funcGetByCategoryID          func(categoryID int64) (pa1 []permissionentity.Permission, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mPermissionDomainServiceMockGetByCategoryID

	funcGetByID          func(id int64) (p1 permissionentity.Permission, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mPermissionDomainServiceMockGetByID

	funcGetByIDs          func(permissionIDs []int64) (pa1 []permissionentity.Permission, err error)
	funcGetByIDsOrigin    string
	inspectFuncGetByIDs   func(permissionIDs []int64)
	afterGetByIDsCounter  uint64
	beforeGetByIDsCounter uint64
	GetByIDsMock          mPermissionDomainServiceMockGetByIDs

	funcGetByNameAndMethod          func(name string, method string) (p1 permissionentity.Permission, err error)
	funcGetByNameAndMethodOrigin    string
	inspectFuncGetByNameAndMethod   func(name string, method string)
	afterGetByNameAndMethodCounter  uint64
	beforeGetByNameAndMethodCounter uint64
	GetByNameAndMethodMock          mPermissionDomainServiceMockGetByNameAndMethod

	funcGetByRoleID          func(roleID int64) (pa1 []permissionentity.Permission, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mPermissionDomainServiceMockGetByRoleID

	funcGetByUserID          func(userID int64) (pa1 []permissionentity.Permission, err error)
	funcGetByUserIDOrigin    string
	inspectFuncGetByUserID   func(userID int64)
	afterGetByUserIDCounter  uint64
	beforeGetByUserIDCounter uint64
	GetByUserIDMock          mPermissionDomainServiceMockGetByUserID

	funcGetIDsByName          func(name string) (ia1 []int64, err error)
	funcGetIDsByNameOrigin    string
	inspectFuncGetIDsByName   func(name string)
	afterGetIDsByNameCounter  uint64
	beforeGetIDsByNameCounter uint64
	GetIDsByNameMock          mPermissionDomainServiceMockGetIDsByName

	funcHasCategoryPermission          func(categoryID int64, permissionID int64) (b1 bool, err error)
	funcHasCategoryPermissionOrigin    string
	inspectFuncHasCategoryPermission   func(categoryID int64, permissionID int64)
	afterHasCategoryPermissionCounter  uint64
	beforeHasCategoryPermissionCounter uint64
	HasCategoryPermissionMock          mPermissionDomainServiceMockHasCategoryPermission

	funcUnassignPermissionFromCategory          func(categoryID int64, permissionID int64) (err error)
	funcUnassignPermissionFromCategoryOrigin    string
	inspectFuncUnassignPermissionFromCategory   func(categoryID int64, permissionID int64)
	afterUnassignPermissionFromCategoryCounter  uint64
	beforeUnassignPermissionFromCategoryCounter uint64
	UnassignPermissionFromCategoryMock          mPermissionDomainServiceMockUnassignPermissionFromCategory

	funcUpdate          func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(permission permissionentity.Permission)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mPermissionDomainServiceMockUpdate
}

// NewPermissionDomainServiceMock returns a mock for mm_service.PermissionDomainService
func NewPermissionDomainServiceMock(t minimock.Tester) *PermissionDomainServiceMock {
	m := &PermissionDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.AssignPermissionToCategoryMock = mPermissionDomainServiceMockAssignPermissionToCategory{mock: m}
	m.AssignPermissionToCategoryMock.callArgs = []*PermissionDomainServiceMockAssignPermissionToCategoryParams{}

	m.CreateMock = mPermissionDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*PermissionDomainServiceMockCreateParams{}

	m.GetAllMock = mPermissionDomainServiceMockGetAll{mock: m}

	m.GetAllCategoryPermissionsMock = mPermissionDomainServiceMockGetAllCategoryPermissions{mock: m}

	m.GetAllUniqueNamesMock = mPermissionDomainServiceMockGetAllUniqueNames{mock: m}

	m.GetByCategoryIDMock = mPermissionDomainServiceMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*PermissionDomainServiceMockGetByCategoryIDParams{}

	m.GetByIDMock = mPermissionDomainServiceMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*PermissionDomainServiceMockGetByIDParams{}

	m.GetByIDsMock = mPermissionDomainServiceMockGetByIDs{mock: m}
	m.GetByIDsMock.callArgs = []*PermissionDomainServiceMockGetByIDsParams{}

	m.GetByNameAndMethodMock = mPermissionDomainServiceMockGetByNameAndMethod{mock: m}
	m.GetByNameAndMethodMock.callArgs = []*PermissionDomainServiceMockGetByNameAndMethodParams{}

	m.GetByRoleIDMock = mPermissionDomainServiceMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*PermissionDomainServiceMockGetByRoleIDParams{}

	m.GetByUserIDMock = mPermissionDomainServiceMockGetByUserID{mock: m}
	m.GetByUserIDMock.callArgs = []*PermissionDomainServiceMockGetByUserIDParams{}

	m.GetIDsByNameMock = mPermissionDomainServiceMockGetIDsByName{mock: m}
	m.GetIDsByNameMock.callArgs = []*PermissionDomainServiceMockGetIDsByNameParams{}

	m.HasCategoryPermissionMock = mPermissionDomainServiceMockHasCategoryPermission{mock: m}
	m.HasCategoryPermissionMock.callArgs = []*PermissionDomainServiceMockHasCategoryPermissionParams{}

	m.UnassignPermissionFromCategoryMock = mPermissionDomainServiceMockUnassignPermissionFromCategory{mock: m}
	m.UnassignPermissionFromCategoryMock.callArgs = []*PermissionDomainServiceMockUnassignPermissionFromCategoryParams{}

	m.UpdateMock = mPermissionDomainServiceMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*PermissionDomainServiceMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mPermissionDomainServiceMockAssignPermissionToCategory struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockAssignPermissionToCategoryExpectation
	expectations       []*PermissionDomainServiceMockAssignPermissionToCategoryExpectation

	callArgs []*PermissionDomainServiceMockAssignPermissionToCategoryParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockAssignPermissionToCategoryExpectation specifies expectation struct of the PermissionDomainService.AssignPermissionToCategory
type PermissionDomainServiceMockAssignPermissionToCategoryExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockAssignPermissionToCategoryParams
	paramPtrs          *PermissionDomainServiceMockAssignPermissionToCategoryParamPtrs
	expectationOrigins PermissionDomainServiceMockAssignPermissionToCategoryExpectationOrigins
	results            *PermissionDomainServiceMockAssignPermissionToCategoryResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockAssignPermissionToCategoryParams contains parameters of the PermissionDomainService.AssignPermissionToCategory
type PermissionDomainServiceMockAssignPermissionToCategoryParams struct {
	categoryID   int64
	permissionID int64
}

// PermissionDomainServiceMockAssignPermissionToCategoryParamPtrs contains pointers to parameters of the PermissionDomainService.AssignPermissionToCategory
type PermissionDomainServiceMockAssignPermissionToCategoryParamPtrs struct {
	categoryID   *int64
	permissionID *int64
}

// PermissionDomainServiceMockAssignPermissionToCategoryResults contains results of the PermissionDomainService.AssignPermissionToCategory
type PermissionDomainServiceMockAssignPermissionToCategoryResults struct {
	err error
}

// PermissionDomainServiceMockAssignPermissionToCategoryOrigins contains origins of expectations of the PermissionDomainService.AssignPermissionToCategory
type PermissionDomainServiceMockAssignPermissionToCategoryExpectationOrigins struct {
	origin             string
	originCategoryID   string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Optional() *mPermissionDomainServiceMockAssignPermissionToCategory {
	mmAssignPermissionToCategory.optional = true
	return mmAssignPermissionToCategory
}

// Expect sets up expected params for PermissionDomainService.AssignPermissionToCategory
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Expect(categoryID int64, permissionID int64) *mPermissionDomainServiceMockAssignPermissionToCategory {
	if mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Set")
	}

	if mmAssignPermissionToCategory.defaultExpectation == nil {
		mmAssignPermissionToCategory.defaultExpectation = &PermissionDomainServiceMockAssignPermissionToCategoryExpectation{}
	}

	if mmAssignPermissionToCategory.defaultExpectation.paramPtrs != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by ExpectParams functions")
	}

	mmAssignPermissionToCategory.defaultExpectation.params = &PermissionDomainServiceMockAssignPermissionToCategoryParams{categoryID, permissionID}
	mmAssignPermissionToCategory.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmAssignPermissionToCategory.expectations {
		if minimock.Equal(e.params, mmAssignPermissionToCategory.defaultExpectation.params) {
			mmAssignPermissionToCategory.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmAssignPermissionToCategory.defaultExpectation.params)
		}
	}

	return mmAssignPermissionToCategory
}

// ExpectCategoryIDParam1 sets up expected param categoryID for PermissionDomainService.AssignPermissionToCategory
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) ExpectCategoryIDParam1(categoryID int64) *mPermissionDomainServiceMockAssignPermissionToCategory {
	if mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Set")
	}

	if mmAssignPermissionToCategory.defaultExpectation == nil {
		mmAssignPermissionToCategory.defaultExpectation = &PermissionDomainServiceMockAssignPermissionToCategoryExpectation{}
	}

	if mmAssignPermissionToCategory.defaultExpectation.params != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Expect")
	}

	if mmAssignPermissionToCategory.defaultExpectation.paramPtrs == nil {
		mmAssignPermissionToCategory.defaultExpectation.paramPtrs = &PermissionDomainServiceMockAssignPermissionToCategoryParamPtrs{}
	}
	mmAssignPermissionToCategory.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmAssignPermissionToCategory.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmAssignPermissionToCategory
}

// ExpectPermissionIDParam2 sets up expected param permissionID for PermissionDomainService.AssignPermissionToCategory
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) ExpectPermissionIDParam2(permissionID int64) *mPermissionDomainServiceMockAssignPermissionToCategory {
	if mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Set")
	}

	if mmAssignPermissionToCategory.defaultExpectation == nil {
		mmAssignPermissionToCategory.defaultExpectation = &PermissionDomainServiceMockAssignPermissionToCategoryExpectation{}
	}

	if mmAssignPermissionToCategory.defaultExpectation.params != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Expect")
	}

	if mmAssignPermissionToCategory.defaultExpectation.paramPtrs == nil {
		mmAssignPermissionToCategory.defaultExpectation.paramPtrs = &PermissionDomainServiceMockAssignPermissionToCategoryParamPtrs{}
	}
	mmAssignPermissionToCategory.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmAssignPermissionToCategory.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmAssignPermissionToCategory
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.AssignPermissionToCategory
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Inspect(f func(categoryID int64, permissionID int64)) *mPermissionDomainServiceMockAssignPermissionToCategory {
	if mmAssignPermissionToCategory.mock.inspectFuncAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.AssignPermissionToCategory")
	}

	mmAssignPermissionToCategory.mock.inspectFuncAssignPermissionToCategory = f

	return mmAssignPermissionToCategory
}

// Return sets up results that will be returned by PermissionDomainService.AssignPermissionToCategory
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Return(err error) *PermissionDomainServiceMock {
	if mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Set")
	}

	if mmAssignPermissionToCategory.defaultExpectation == nil {
		mmAssignPermissionToCategory.defaultExpectation = &PermissionDomainServiceMockAssignPermissionToCategoryExpectation{mock: mmAssignPermissionToCategory.mock}
	}
	mmAssignPermissionToCategory.defaultExpectation.results = &PermissionDomainServiceMockAssignPermissionToCategoryResults{err}
	mmAssignPermissionToCategory.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmAssignPermissionToCategory.mock
}

// Set uses given function f to mock the PermissionDomainService.AssignPermissionToCategory method
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Set(f func(categoryID int64, permissionID int64) (err error)) *PermissionDomainServiceMock {
	if mmAssignPermissionToCategory.defaultExpectation != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.AssignPermissionToCategory method")
	}

	if len(mmAssignPermissionToCategory.expectations) > 0 {
		mmAssignPermissionToCategory.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.AssignPermissionToCategory method")
	}

	mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory = f
	mmAssignPermissionToCategory.mock.funcAssignPermissionToCategoryOrigin = minimock.CallerInfo(1)
	return mmAssignPermissionToCategory.mock
}

// When sets expectation for the PermissionDomainService.AssignPermissionToCategory which will trigger the result defined by the following
// Then helper
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) When(categoryID int64, permissionID int64) *PermissionDomainServiceMockAssignPermissionToCategoryExpectation {
	if mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.mock.t.Fatalf("PermissionDomainServiceMock.AssignPermissionToCategory mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockAssignPermissionToCategoryExpectation{
		mock:               mmAssignPermissionToCategory.mock,
		params:             &PermissionDomainServiceMockAssignPermissionToCategoryParams{categoryID, permissionID},
		expectationOrigins: PermissionDomainServiceMockAssignPermissionToCategoryExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmAssignPermissionToCategory.expectations = append(mmAssignPermissionToCategory.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.AssignPermissionToCategory return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockAssignPermissionToCategoryExpectation) Then(err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockAssignPermissionToCategoryResults{err}
	return e.mock
}

// Times sets number of times PermissionDomainService.AssignPermissionToCategory should be invoked
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Times(n uint64) *mPermissionDomainServiceMockAssignPermissionToCategory {
	if n == 0 {
		mmAssignPermissionToCategory.mock.t.Fatalf("Times of PermissionDomainServiceMock.AssignPermissionToCategory mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmAssignPermissionToCategory.expectedInvocations, n)
	mmAssignPermissionToCategory.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmAssignPermissionToCategory
}

func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) invocationsDone() bool {
	if len(mmAssignPermissionToCategory.expectations) == 0 && mmAssignPermissionToCategory.defaultExpectation == nil && mmAssignPermissionToCategory.mock.funcAssignPermissionToCategory == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmAssignPermissionToCategory.mock.afterAssignPermissionToCategoryCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmAssignPermissionToCategory.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// AssignPermissionToCategory implements mm_service.PermissionDomainService
func (mmAssignPermissionToCategory *PermissionDomainServiceMock) AssignPermissionToCategory(categoryID int64, permissionID int64) (err error) {
	mm_atomic.AddUint64(&mmAssignPermissionToCategory.beforeAssignPermissionToCategoryCounter, 1)
	defer mm_atomic.AddUint64(&mmAssignPermissionToCategory.afterAssignPermissionToCategoryCounter, 1)

	mmAssignPermissionToCategory.t.Helper()

	if mmAssignPermissionToCategory.inspectFuncAssignPermissionToCategory != nil {
		mmAssignPermissionToCategory.inspectFuncAssignPermissionToCategory(categoryID, permissionID)
	}

	mm_params := PermissionDomainServiceMockAssignPermissionToCategoryParams{categoryID, permissionID}

	// Record call args
	mmAssignPermissionToCategory.AssignPermissionToCategoryMock.mutex.Lock()
	mmAssignPermissionToCategory.AssignPermissionToCategoryMock.callArgs = append(mmAssignPermissionToCategory.AssignPermissionToCategoryMock.callArgs, &mm_params)
	mmAssignPermissionToCategory.AssignPermissionToCategoryMock.mutex.Unlock()

	for _, e := range mmAssignPermissionToCategory.AssignPermissionToCategoryMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.Counter, 1)
		mm_want := mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.params
		mm_want_ptrs := mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockAssignPermissionToCategoryParams{categoryID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmAssignPermissionToCategory.t.Errorf("PermissionDomainServiceMock.AssignPermissionToCategory got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmAssignPermissionToCategory.t.Errorf("PermissionDomainServiceMock.AssignPermissionToCategory got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmAssignPermissionToCategory.t.Errorf("PermissionDomainServiceMock.AssignPermissionToCategory got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmAssignPermissionToCategory.AssignPermissionToCategoryMock.defaultExpectation.results
		if mm_results == nil {
			mmAssignPermissionToCategory.t.Fatal("No results are set for the PermissionDomainServiceMock.AssignPermissionToCategory")
		}
		return (*mm_results).err
	}
	if mmAssignPermissionToCategory.funcAssignPermissionToCategory != nil {
		return mmAssignPermissionToCategory.funcAssignPermissionToCategory(categoryID, permissionID)
	}
	mmAssignPermissionToCategory.t.Fatalf("Unexpected call to PermissionDomainServiceMock.AssignPermissionToCategory. %v %v", categoryID, permissionID)
	return
}

// AssignPermissionToCategoryAfterCounter returns a count of finished PermissionDomainServiceMock.AssignPermissionToCategory invocations
func (mmAssignPermissionToCategory *PermissionDomainServiceMock) AssignPermissionToCategoryAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignPermissionToCategory.afterAssignPermissionToCategoryCounter)
}

// AssignPermissionToCategoryBeforeCounter returns a count of PermissionDomainServiceMock.AssignPermissionToCategory invocations
func (mmAssignPermissionToCategory *PermissionDomainServiceMock) AssignPermissionToCategoryBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmAssignPermissionToCategory.beforeAssignPermissionToCategoryCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.AssignPermissionToCategory.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmAssignPermissionToCategory *mPermissionDomainServiceMockAssignPermissionToCategory) Calls() []*PermissionDomainServiceMockAssignPermissionToCategoryParams {
	mmAssignPermissionToCategory.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockAssignPermissionToCategoryParams, len(mmAssignPermissionToCategory.callArgs))
	copy(argCopy, mmAssignPermissionToCategory.callArgs)

	mmAssignPermissionToCategory.mutex.RUnlock()

	return argCopy
}

// MinimockAssignPermissionToCategoryDone returns true if the count of the AssignPermissionToCategory invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockAssignPermissionToCategoryDone() bool {
	if m.AssignPermissionToCategoryMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.AssignPermissionToCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.AssignPermissionToCategoryMock.invocationsDone()
}

// MinimockAssignPermissionToCategoryInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockAssignPermissionToCategoryInspect() {
	for _, e := range m.AssignPermissionToCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.AssignPermissionToCategory at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterAssignPermissionToCategoryCounter := mm_atomic.LoadUint64(&m.afterAssignPermissionToCategoryCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.AssignPermissionToCategoryMock.defaultExpectation != nil && afterAssignPermissionToCategoryCounter < 1 {
		if m.AssignPermissionToCategoryMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.AssignPermissionToCategory at\n%s", m.AssignPermissionToCategoryMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.AssignPermissionToCategory at\n%s with params: %#v", m.AssignPermissionToCategoryMock.defaultExpectation.expectationOrigins.origin, *m.AssignPermissionToCategoryMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcAssignPermissionToCategory != nil && afterAssignPermissionToCategoryCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.AssignPermissionToCategory at\n%s", m.funcAssignPermissionToCategoryOrigin)
	}

	if !m.AssignPermissionToCategoryMock.invocationsDone() && afterAssignPermissionToCategoryCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.AssignPermissionToCategory at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.AssignPermissionToCategoryMock.expectedInvocations), m.AssignPermissionToCategoryMock.expectedInvocationsOrigin, afterAssignPermissionToCategoryCounter)
	}
}

type mPermissionDomainServiceMockCreate struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockCreateExpectation
	expectations       []*PermissionDomainServiceMockCreateExpectation

	callArgs []*PermissionDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockCreateExpectation specifies expectation struct of the PermissionDomainService.Create
type PermissionDomainServiceMockCreateExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockCreateParams
	paramPtrs          *PermissionDomainServiceMockCreateParamPtrs
	expectationOrigins PermissionDomainServiceMockCreateExpectationOrigins
	results            *PermissionDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockCreateParams contains parameters of the PermissionDomainService.Create
type PermissionDomainServiceMockCreateParams struct {
	permission permissionentity.Permission
}

// PermissionDomainServiceMockCreateParamPtrs contains pointers to parameters of the PermissionDomainService.Create
type PermissionDomainServiceMockCreateParamPtrs struct {
	permission *permissionentity.Permission
}

// PermissionDomainServiceMockCreateResults contains results of the PermissionDomainService.Create
type PermissionDomainServiceMockCreateResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionDomainServiceMockCreateOrigins contains origins of expectations of the PermissionDomainService.Create
type PermissionDomainServiceMockCreateExpectationOrigins struct {
	origin           string
	originPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mPermissionDomainServiceMockCreate) Optional() *mPermissionDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for PermissionDomainService.Create
func (mmCreate *mPermissionDomainServiceMockCreate) Expect(permission permissionentity.Permission) *mPermissionDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &PermissionDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("PermissionDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &PermissionDomainServiceMockCreateParams{permission}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectPermissionParam1 sets up expected param permission for PermissionDomainService.Create
func (mmCreate *mPermissionDomainServiceMockCreate) ExpectPermissionParam1(permission permissionentity.Permission) *mPermissionDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &PermissionDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("PermissionDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &PermissionDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.permission = &permission
	mmCreate.defaultExpectation.expectationOrigins.originPermission = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.Create
func (mmCreate *mPermissionDomainServiceMockCreate) Inspect(f func(permission permissionentity.Permission)) *mPermissionDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by PermissionDomainService.Create
func (mmCreate *mPermissionDomainServiceMockCreate) Return(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &PermissionDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &PermissionDomainServiceMockCreateResults{p1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the PermissionDomainService.Create method
func (mmCreate *mPermissionDomainServiceMockCreate) Set(f func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the PermissionDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mPermissionDomainServiceMockCreate) When(permission permissionentity.Permission) *PermissionDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("PermissionDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &PermissionDomainServiceMockCreateParams{permission},
		expectationOrigins: PermissionDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.Create return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockCreateExpectation) Then(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockCreateResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.Create should be invoked
func (mmCreate *mPermissionDomainServiceMockCreate) Times(n uint64) *mPermissionDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of PermissionDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mPermissionDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.PermissionDomainService
func (mmCreate *PermissionDomainServiceMock) Create(permission permissionentity.Permission) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(permission)
	}

	mm_params := PermissionDomainServiceMockCreateParams{permission}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockCreateParams{permission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permission != nil && !minimock.Equal(*mm_want_ptrs.permission, mm_got.permission) {
				mmCreate.t.Errorf("PermissionDomainServiceMock.Create got unexpected parameter permission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originPermission, *mm_want_ptrs.permission, mm_got.permission, minimock.Diff(*mm_want_ptrs.permission, mm_got.permission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("PermissionDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the PermissionDomainServiceMock.Create")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(permission)
	}
	mmCreate.t.Fatalf("Unexpected call to PermissionDomainServiceMock.Create. %v", permission)
	return
}

// CreateAfterCounter returns a count of finished PermissionDomainServiceMock.Create invocations
func (mmCreate *PermissionDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of PermissionDomainServiceMock.Create invocations
func (mmCreate *PermissionDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mPermissionDomainServiceMockCreate) Calls() []*PermissionDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mPermissionDomainServiceMockGetAll struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetAllExpectation
	expectations       []*PermissionDomainServiceMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetAllExpectation specifies expectation struct of the PermissionDomainService.GetAll
type PermissionDomainServiceMockGetAllExpectation struct {
	mock *PermissionDomainServiceMock

	results      *PermissionDomainServiceMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// PermissionDomainServiceMockGetAllResults contains results of the PermissionDomainService.GetAll
type PermissionDomainServiceMockGetAllResults struct {
	pa1 []permissionentity.Permission
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mPermissionDomainServiceMockGetAll) Optional() *mPermissionDomainServiceMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for PermissionDomainService.GetAll
func (mmGetAll *mPermissionDomainServiceMockGetAll) Expect() *mPermissionDomainServiceMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("PermissionDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &PermissionDomainServiceMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetAll
func (mmGetAll *mPermissionDomainServiceMockGetAll) Inspect(f func()) *mPermissionDomainServiceMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by PermissionDomainService.GetAll
func (mmGetAll *mPermissionDomainServiceMockGetAll) Return(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("PermissionDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &PermissionDomainServiceMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &PermissionDomainServiceMockGetAllResults{pa1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the PermissionDomainService.GetAll method
func (mmGetAll *mPermissionDomainServiceMockGetAll) Set(f func() (pa1 []permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times PermissionDomainService.GetAll should be invoked
func (mmGetAll *mPermissionDomainServiceMockGetAll) Times(n uint64) *mPermissionDomainServiceMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mPermissionDomainServiceMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_service.PermissionDomainService
func (mmGetAll *PermissionDomainServiceMock) GetAll() (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the PermissionDomainServiceMock.GetAll")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished PermissionDomainServiceMock.GetAll invocations
func (mmGetAll *PermissionDomainServiceMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of PermissionDomainServiceMock.GetAll invocations
func (mmGetAll *PermissionDomainServiceMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to PermissionDomainServiceMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mPermissionDomainServiceMockGetAllCategoryPermissions struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetAllCategoryPermissionsExpectation
	expectations       []*PermissionDomainServiceMockGetAllCategoryPermissionsExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetAllCategoryPermissionsExpectation specifies expectation struct of the PermissionDomainService.GetAllCategoryPermissions
type PermissionDomainServiceMockGetAllCategoryPermissionsExpectation struct {
	mock *PermissionDomainServiceMock

	results      *PermissionDomainServiceMockGetAllCategoryPermissionsResults
	returnOrigin string
	Counter      uint64
}

// PermissionDomainServiceMockGetAllCategoryPermissionsResults contains results of the PermissionDomainService.GetAllCategoryPermissions
type PermissionDomainServiceMockGetAllCategoryPermissionsResults struct {
	ca1 []categoryentity.CategoryPermission
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) Optional() *mPermissionDomainServiceMockGetAllCategoryPermissions {
	mmGetAllCategoryPermissions.optional = true
	return mmGetAllCategoryPermissions
}

// Expect sets up expected params for PermissionDomainService.GetAllCategoryPermissions
func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) Expect() *mPermissionDomainServiceMockGetAllCategoryPermissions {
	if mmGetAllCategoryPermissions.mock.funcGetAllCategoryPermissions != nil {
		mmGetAllCategoryPermissions.mock.t.Fatalf("PermissionDomainServiceMock.GetAllCategoryPermissions mock is already set by Set")
	}

	if mmGetAllCategoryPermissions.defaultExpectation == nil {
		mmGetAllCategoryPermissions.defaultExpectation = &PermissionDomainServiceMockGetAllCategoryPermissionsExpectation{}
	}

	return mmGetAllCategoryPermissions
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetAllCategoryPermissions
func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) Inspect(f func()) *mPermissionDomainServiceMockGetAllCategoryPermissions {
	if mmGetAllCategoryPermissions.mock.inspectFuncGetAllCategoryPermissions != nil {
		mmGetAllCategoryPermissions.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetAllCategoryPermissions")
	}

	mmGetAllCategoryPermissions.mock.inspectFuncGetAllCategoryPermissions = f

	return mmGetAllCategoryPermissions
}

// Return sets up results that will be returned by PermissionDomainService.GetAllCategoryPermissions
func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) Return(ca1 []categoryentity.CategoryPermission, err error) *PermissionDomainServiceMock {
	if mmGetAllCategoryPermissions.mock.funcGetAllCategoryPermissions != nil {
		mmGetAllCategoryPermissions.mock.t.Fatalf("PermissionDomainServiceMock.GetAllCategoryPermissions mock is already set by Set")
	}

	if mmGetAllCategoryPermissions.defaultExpectation == nil {
		mmGetAllCategoryPermissions.defaultExpectation = &PermissionDomainServiceMockGetAllCategoryPermissionsExpectation{mock: mmGetAllCategoryPermissions.mock}
	}
	mmGetAllCategoryPermissions.defaultExpectation.results = &PermissionDomainServiceMockGetAllCategoryPermissionsResults{ca1, err}
	mmGetAllCategoryPermissions.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllCategoryPermissions.mock
}

// Set uses given function f to mock the PermissionDomainService.GetAllCategoryPermissions method
func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) Set(f func() (ca1 []categoryentity.CategoryPermission, err error)) *PermissionDomainServiceMock {
	if mmGetAllCategoryPermissions.defaultExpectation != nil {
		mmGetAllCategoryPermissions.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetAllCategoryPermissions method")
	}

	if len(mmGetAllCategoryPermissions.expectations) > 0 {
		mmGetAllCategoryPermissions.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetAllCategoryPermissions method")
	}

	mmGetAllCategoryPermissions.mock.funcGetAllCategoryPermissions = f
	mmGetAllCategoryPermissions.mock.funcGetAllCategoryPermissionsOrigin = minimock.CallerInfo(1)
	return mmGetAllCategoryPermissions.mock
}

// Times sets number of times PermissionDomainService.GetAllCategoryPermissions should be invoked
func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) Times(n uint64) *mPermissionDomainServiceMockGetAllCategoryPermissions {
	if n == 0 {
		mmGetAllCategoryPermissions.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetAllCategoryPermissions mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllCategoryPermissions.expectedInvocations, n)
	mmGetAllCategoryPermissions.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllCategoryPermissions
}

func (mmGetAllCategoryPermissions *mPermissionDomainServiceMockGetAllCategoryPermissions) invocationsDone() bool {
	if len(mmGetAllCategoryPermissions.expectations) == 0 && mmGetAllCategoryPermissions.defaultExpectation == nil && mmGetAllCategoryPermissions.mock.funcGetAllCategoryPermissions == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllCategoryPermissions.mock.afterGetAllCategoryPermissionsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllCategoryPermissions.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllCategoryPermissions implements mm_service.PermissionDomainService
func (mmGetAllCategoryPermissions *PermissionDomainServiceMock) GetAllCategoryPermissions() (ca1 []categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetAllCategoryPermissions.beforeGetAllCategoryPermissionsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllCategoryPermissions.afterGetAllCategoryPermissionsCounter, 1)

	mmGetAllCategoryPermissions.t.Helper()

	if mmGetAllCategoryPermissions.inspectFuncGetAllCategoryPermissions != nil {
		mmGetAllCategoryPermissions.inspectFuncGetAllCategoryPermissions()
	}

	if mmGetAllCategoryPermissions.GetAllCategoryPermissionsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllCategoryPermissions.GetAllCategoryPermissionsMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllCategoryPermissions.GetAllCategoryPermissionsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllCategoryPermissions.t.Fatal("No results are set for the PermissionDomainServiceMock.GetAllCategoryPermissions")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAllCategoryPermissions.funcGetAllCategoryPermissions != nil {
		return mmGetAllCategoryPermissions.funcGetAllCategoryPermissions()
	}
	mmGetAllCategoryPermissions.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetAllCategoryPermissions.")
	return
}

// GetAllCategoryPermissionsAfterCounter returns a count of finished PermissionDomainServiceMock.GetAllCategoryPermissions invocations
func (mmGetAllCategoryPermissions *PermissionDomainServiceMock) GetAllCategoryPermissionsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllCategoryPermissions.afterGetAllCategoryPermissionsCounter)
}

// GetAllCategoryPermissionsBeforeCounter returns a count of PermissionDomainServiceMock.GetAllCategoryPermissions invocations
func (mmGetAllCategoryPermissions *PermissionDomainServiceMock) GetAllCategoryPermissionsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllCategoryPermissions.beforeGetAllCategoryPermissionsCounter)
}

// MinimockGetAllCategoryPermissionsDone returns true if the count of the GetAllCategoryPermissions invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetAllCategoryPermissionsDone() bool {
	if m.GetAllCategoryPermissionsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllCategoryPermissionsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllCategoryPermissionsMock.invocationsDone()
}

// MinimockGetAllCategoryPermissionsInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetAllCategoryPermissionsInspect() {
	for _, e := range m.GetAllCategoryPermissionsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to PermissionDomainServiceMock.GetAllCategoryPermissions")
		}
	}

	afterGetAllCategoryPermissionsCounter := mm_atomic.LoadUint64(&m.afterGetAllCategoryPermissionsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllCategoryPermissionsMock.defaultExpectation != nil && afterGetAllCategoryPermissionsCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetAllCategoryPermissions at\n%s", m.GetAllCategoryPermissionsMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllCategoryPermissions != nil && afterGetAllCategoryPermissionsCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetAllCategoryPermissions at\n%s", m.funcGetAllCategoryPermissionsOrigin)
	}

	if !m.GetAllCategoryPermissionsMock.invocationsDone() && afterGetAllCategoryPermissionsCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetAllCategoryPermissions at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllCategoryPermissionsMock.expectedInvocations), m.GetAllCategoryPermissionsMock.expectedInvocationsOrigin, afterGetAllCategoryPermissionsCounter)
	}
}

type mPermissionDomainServiceMockGetAllUniqueNames struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetAllUniqueNamesExpectation
	expectations       []*PermissionDomainServiceMockGetAllUniqueNamesExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetAllUniqueNamesExpectation specifies expectation struct of the PermissionDomainService.GetAllUniqueNames
type PermissionDomainServiceMockGetAllUniqueNamesExpectation struct {
	mock *PermissionDomainServiceMock

	results      *PermissionDomainServiceMockGetAllUniqueNamesResults
	returnOrigin string
	Counter      uint64
}

// PermissionDomainServiceMockGetAllUniqueNamesResults contains results of the PermissionDomainService.GetAllUniqueNames
type PermissionDomainServiceMockGetAllUniqueNamesResults struct {
	m1 map[string]struct {
	}
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) Optional() *mPermissionDomainServiceMockGetAllUniqueNames {
	mmGetAllUniqueNames.optional = true
	return mmGetAllUniqueNames
}

// Expect sets up expected params for PermissionDomainService.GetAllUniqueNames
func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) Expect() *mPermissionDomainServiceMockGetAllUniqueNames {
	if mmGetAllUniqueNames.mock.funcGetAllUniqueNames != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("PermissionDomainServiceMock.GetAllUniqueNames mock is already set by Set")
	}

	if mmGetAllUniqueNames.defaultExpectation == nil {
		mmGetAllUniqueNames.defaultExpectation = &PermissionDomainServiceMockGetAllUniqueNamesExpectation{}
	}

	return mmGetAllUniqueNames
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetAllUniqueNames
func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) Inspect(f func()) *mPermissionDomainServiceMockGetAllUniqueNames {
	if mmGetAllUniqueNames.mock.inspectFuncGetAllUniqueNames != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetAllUniqueNames")
	}

	mmGetAllUniqueNames.mock.inspectFuncGetAllUniqueNames = f

	return mmGetAllUniqueNames
}

// Return sets up results that will be returned by PermissionDomainService.GetAllUniqueNames
func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) Return(m1 map[string]struct {
}, err error) *PermissionDomainServiceMock {
	if mmGetAllUniqueNames.mock.funcGetAllUniqueNames != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("PermissionDomainServiceMock.GetAllUniqueNames mock is already set by Set")
	}

	if mmGetAllUniqueNames.defaultExpectation == nil {
		mmGetAllUniqueNames.defaultExpectation = &PermissionDomainServiceMockGetAllUniqueNamesExpectation{mock: mmGetAllUniqueNames.mock}
	}
	mmGetAllUniqueNames.defaultExpectation.results = &PermissionDomainServiceMockGetAllUniqueNamesResults{m1, err}
	mmGetAllUniqueNames.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAllUniqueNames.mock
}

// Set uses given function f to mock the PermissionDomainService.GetAllUniqueNames method
func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) Set(f func() (m1 map[string]struct {
}, err error)) *PermissionDomainServiceMock {
	if mmGetAllUniqueNames.defaultExpectation != nil {
		mmGetAllUniqueNames.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetAllUniqueNames method")
	}

	if len(mmGetAllUniqueNames.expectations) > 0 {
		mmGetAllUniqueNames.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetAllUniqueNames method")
	}

	mmGetAllUniqueNames.mock.funcGetAllUniqueNames = f
	mmGetAllUniqueNames.mock.funcGetAllUniqueNamesOrigin = minimock.CallerInfo(1)
	return mmGetAllUniqueNames.mock
}

// Times sets number of times PermissionDomainService.GetAllUniqueNames should be invoked
func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) Times(n uint64) *mPermissionDomainServiceMockGetAllUniqueNames {
	if n == 0 {
		mmGetAllUniqueNames.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetAllUniqueNames mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAllUniqueNames.expectedInvocations, n)
	mmGetAllUniqueNames.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAllUniqueNames
}

func (mmGetAllUniqueNames *mPermissionDomainServiceMockGetAllUniqueNames) invocationsDone() bool {
	if len(mmGetAllUniqueNames.expectations) == 0 && mmGetAllUniqueNames.defaultExpectation == nil && mmGetAllUniqueNames.mock.funcGetAllUniqueNames == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAllUniqueNames.mock.afterGetAllUniqueNamesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAllUniqueNames.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAllUniqueNames implements mm_service.PermissionDomainService
func (mmGetAllUniqueNames *PermissionDomainServiceMock) GetAllUniqueNames() (m1 map[string]struct {
}, err error) {
	mm_atomic.AddUint64(&mmGetAllUniqueNames.beforeGetAllUniqueNamesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAllUniqueNames.afterGetAllUniqueNamesCounter, 1)

	mmGetAllUniqueNames.t.Helper()

	if mmGetAllUniqueNames.inspectFuncGetAllUniqueNames != nil {
		mmGetAllUniqueNames.inspectFuncGetAllUniqueNames()
	}

	if mmGetAllUniqueNames.GetAllUniqueNamesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAllUniqueNames.GetAllUniqueNamesMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAllUniqueNames.GetAllUniqueNamesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAllUniqueNames.t.Fatal("No results are set for the PermissionDomainServiceMock.GetAllUniqueNames")
		}
		return (*mm_results).m1, (*mm_results).err
	}
	if mmGetAllUniqueNames.funcGetAllUniqueNames != nil {
		return mmGetAllUniqueNames.funcGetAllUniqueNames()
	}
	mmGetAllUniqueNames.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetAllUniqueNames.")
	return
}

// GetAllUniqueNamesAfterCounter returns a count of finished PermissionDomainServiceMock.GetAllUniqueNames invocations
func (mmGetAllUniqueNames *PermissionDomainServiceMock) GetAllUniqueNamesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllUniqueNames.afterGetAllUniqueNamesCounter)
}

// GetAllUniqueNamesBeforeCounter returns a count of PermissionDomainServiceMock.GetAllUniqueNames invocations
func (mmGetAllUniqueNames *PermissionDomainServiceMock) GetAllUniqueNamesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAllUniqueNames.beforeGetAllUniqueNamesCounter)
}

// MinimockGetAllUniqueNamesDone returns true if the count of the GetAllUniqueNames invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetAllUniqueNamesDone() bool {
	if m.GetAllUniqueNamesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllUniqueNamesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllUniqueNamesMock.invocationsDone()
}

// MinimockGetAllUniqueNamesInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetAllUniqueNamesInspect() {
	for _, e := range m.GetAllUniqueNamesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to PermissionDomainServiceMock.GetAllUniqueNames")
		}
	}

	afterGetAllUniqueNamesCounter := mm_atomic.LoadUint64(&m.afterGetAllUniqueNamesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllUniqueNamesMock.defaultExpectation != nil && afterGetAllUniqueNamesCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetAllUniqueNames at\n%s", m.GetAllUniqueNamesMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAllUniqueNames != nil && afterGetAllUniqueNamesCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetAllUniqueNames at\n%s", m.funcGetAllUniqueNamesOrigin)
	}

	if !m.GetAllUniqueNamesMock.invocationsDone() && afterGetAllUniqueNamesCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetAllUniqueNames at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllUniqueNamesMock.expectedInvocations), m.GetAllUniqueNamesMock.expectedInvocationsOrigin, afterGetAllUniqueNamesCounter)
	}
}

type mPermissionDomainServiceMockGetByCategoryID struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetByCategoryIDExpectation
	expectations       []*PermissionDomainServiceMockGetByCategoryIDExpectation

	callArgs []*PermissionDomainServiceMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetByCategoryIDExpectation specifies expectation struct of the PermissionDomainService.GetByCategoryID
type PermissionDomainServiceMockGetByCategoryIDExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetByCategoryIDParams
	paramPtrs          *PermissionDomainServiceMockGetByCategoryIDParamPtrs
	expectationOrigins PermissionDomainServiceMockGetByCategoryIDExpectationOrigins
	results            *PermissionDomainServiceMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetByCategoryIDParams contains parameters of the PermissionDomainService.GetByCategoryID
type PermissionDomainServiceMockGetByCategoryIDParams struct {
	categoryID int64
}

// PermissionDomainServiceMockGetByCategoryIDParamPtrs contains pointers to parameters of the PermissionDomainService.GetByCategoryID
type PermissionDomainServiceMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// PermissionDomainServiceMockGetByCategoryIDResults contains results of the PermissionDomainService.GetByCategoryID
type PermissionDomainServiceMockGetByCategoryIDResults struct {
	pa1 []permissionentity.Permission
	err error
}

// PermissionDomainServiceMockGetByCategoryIDOrigins contains origins of expectations of the PermissionDomainService.GetByCategoryID
type PermissionDomainServiceMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Optional() *mPermissionDomainServiceMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for PermissionDomainService.GetByCategoryID
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Expect(categoryID int64) *mPermissionDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("PermissionDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &PermissionDomainServiceMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("PermissionDomainServiceMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &PermissionDomainServiceMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for PermissionDomainService.GetByCategoryID
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mPermissionDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("PermissionDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &PermissionDomainServiceMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("PermissionDomainServiceMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetByCategoryID
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Inspect(f func(categoryID int64)) *mPermissionDomainServiceMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by PermissionDomainService.GetByCategoryID
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Return(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("PermissionDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &PermissionDomainServiceMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &PermissionDomainServiceMockGetByCategoryIDResults{pa1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the PermissionDomainService.GetByCategoryID method
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Set(f func(categoryID int64) (pa1 []permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the PermissionDomainService.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) When(categoryID int64) *PermissionDomainServiceMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("PermissionDomainServiceMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &PermissionDomainServiceMockGetByCategoryIDParams{categoryID},
		expectationOrigins: PermissionDomainServiceMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetByCategoryIDExpectation) Then(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetByCategoryIDResults{pa1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetByCategoryID should be invoked
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Times(n uint64) *mPermissionDomainServiceMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_service.PermissionDomainService
func (mmGetByCategoryID *PermissionDomainServiceMock) GetByCategoryID(categoryID int64) (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := PermissionDomainServiceMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("PermissionDomainServiceMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("PermissionDomainServiceMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the PermissionDomainServiceMock.GetByCategoryID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished PermissionDomainServiceMock.GetByCategoryID invocations
func (mmGetByCategoryID *PermissionDomainServiceMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of PermissionDomainServiceMock.GetByCategoryID invocations
func (mmGetByCategoryID *PermissionDomainServiceMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mPermissionDomainServiceMockGetByCategoryID) Calls() []*PermissionDomainServiceMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mPermissionDomainServiceMockGetByID struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetByIDExpectation
	expectations       []*PermissionDomainServiceMockGetByIDExpectation

	callArgs []*PermissionDomainServiceMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetByIDExpectation specifies expectation struct of the PermissionDomainService.GetByID
type PermissionDomainServiceMockGetByIDExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetByIDParams
	paramPtrs          *PermissionDomainServiceMockGetByIDParamPtrs
	expectationOrigins PermissionDomainServiceMockGetByIDExpectationOrigins
	results            *PermissionDomainServiceMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetByIDParams contains parameters of the PermissionDomainService.GetByID
type PermissionDomainServiceMockGetByIDParams struct {
	id int64
}

// PermissionDomainServiceMockGetByIDParamPtrs contains pointers to parameters of the PermissionDomainService.GetByID
type PermissionDomainServiceMockGetByIDParamPtrs struct {
	id *int64
}

// PermissionDomainServiceMockGetByIDResults contains results of the PermissionDomainService.GetByID
type PermissionDomainServiceMockGetByIDResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionDomainServiceMockGetByIDOrigins contains origins of expectations of the PermissionDomainService.GetByID
type PermissionDomainServiceMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mPermissionDomainServiceMockGetByID) Optional() *mPermissionDomainServiceMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for PermissionDomainService.GetByID
func (mmGetByID *mPermissionDomainServiceMockGetByID) Expect(id int64) *mPermissionDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &PermissionDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("PermissionDomainServiceMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &PermissionDomainServiceMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for PermissionDomainService.GetByID
func (mmGetByID *mPermissionDomainServiceMockGetByID) ExpectIdParam1(id int64) *mPermissionDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &PermissionDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("PermissionDomainServiceMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetByID
func (mmGetByID *mPermissionDomainServiceMockGetByID) Inspect(f func(id int64)) *mPermissionDomainServiceMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by PermissionDomainService.GetByID
func (mmGetByID *mPermissionDomainServiceMockGetByID) Return(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &PermissionDomainServiceMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &PermissionDomainServiceMockGetByIDResults{p1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the PermissionDomainService.GetByID method
func (mmGetByID *mPermissionDomainServiceMockGetByID) Set(f func(id int64) (p1 permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the PermissionDomainService.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mPermissionDomainServiceMockGetByID) When(id int64) *PermissionDomainServiceMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("PermissionDomainServiceMock.GetByID mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &PermissionDomainServiceMockGetByIDParams{id},
		expectationOrigins: PermissionDomainServiceMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetByID return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetByIDExpectation) Then(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetByIDResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetByID should be invoked
func (mmGetByID *mPermissionDomainServiceMockGetByID) Times(n uint64) *mPermissionDomainServiceMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mPermissionDomainServiceMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_service.PermissionDomainService
func (mmGetByID *PermissionDomainServiceMock) GetByID(id int64) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := PermissionDomainServiceMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("PermissionDomainServiceMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("PermissionDomainServiceMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the PermissionDomainServiceMock.GetByID")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished PermissionDomainServiceMock.GetByID invocations
func (mmGetByID *PermissionDomainServiceMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of PermissionDomainServiceMock.GetByID invocations
func (mmGetByID *PermissionDomainServiceMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mPermissionDomainServiceMockGetByID) Calls() []*PermissionDomainServiceMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mPermissionDomainServiceMockGetByIDs struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetByIDsExpectation
	expectations       []*PermissionDomainServiceMockGetByIDsExpectation

	callArgs []*PermissionDomainServiceMockGetByIDsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetByIDsExpectation specifies expectation struct of the PermissionDomainService.GetByIDs
type PermissionDomainServiceMockGetByIDsExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetByIDsParams
	paramPtrs          *PermissionDomainServiceMockGetByIDsParamPtrs
	expectationOrigins PermissionDomainServiceMockGetByIDsExpectationOrigins
	results            *PermissionDomainServiceMockGetByIDsResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetByIDsParams contains parameters of the PermissionDomainService.GetByIDs
type PermissionDomainServiceMockGetByIDsParams struct {
	permissionIDs []int64
}

// PermissionDomainServiceMockGetByIDsParamPtrs contains pointers to parameters of the PermissionDomainService.GetByIDs
type PermissionDomainServiceMockGetByIDsParamPtrs struct {
	permissionIDs *[]int64
}

// PermissionDomainServiceMockGetByIDsResults contains results of the PermissionDomainService.GetByIDs
type PermissionDomainServiceMockGetByIDsResults struct {
	pa1 []permissionentity.Permission
	err error
}

// PermissionDomainServiceMockGetByIDsOrigins contains origins of expectations of the PermissionDomainService.GetByIDs
type PermissionDomainServiceMockGetByIDsExpectationOrigins struct {
	origin              string
	originPermissionIDs string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Optional() *mPermissionDomainServiceMockGetByIDs {
	mmGetByIDs.optional = true
	return mmGetByIDs
}

// Expect sets up expected params for PermissionDomainService.GetByIDs
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Expect(permissionIDs []int64) *mPermissionDomainServiceMockGetByIDs {
	if mmGetByIDs.mock.funcGetByIDs != nil {
		mmGetByIDs.mock.t.Fatalf("PermissionDomainServiceMock.GetByIDs mock is already set by Set")
	}

	if mmGetByIDs.defaultExpectation == nil {
		mmGetByIDs.defaultExpectation = &PermissionDomainServiceMockGetByIDsExpectation{}
	}

	if mmGetByIDs.defaultExpectation.paramPtrs != nil {
		mmGetByIDs.mock.t.Fatalf("PermissionDomainServiceMock.GetByIDs mock is already set by ExpectParams functions")
	}

	mmGetByIDs.defaultExpectation.params = &PermissionDomainServiceMockGetByIDsParams{permissionIDs}
	mmGetByIDs.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByIDs.expectations {
		if minimock.Equal(e.params, mmGetByIDs.defaultExpectation.params) {
			mmGetByIDs.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByIDs.defaultExpectation.params)
		}
	}

	return mmGetByIDs
}

// ExpectPermissionIDsParam1 sets up expected param permissionIDs for PermissionDomainService.GetByIDs
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) ExpectPermissionIDsParam1(permissionIDs []int64) *mPermissionDomainServiceMockGetByIDs {
	if mmGetByIDs.mock.funcGetByIDs != nil {
		mmGetByIDs.mock.t.Fatalf("PermissionDomainServiceMock.GetByIDs mock is already set by Set")
	}

	if mmGetByIDs.defaultExpectation == nil {
		mmGetByIDs.defaultExpectation = &PermissionDomainServiceMockGetByIDsExpectation{}
	}

	if mmGetByIDs.defaultExpectation.params != nil {
		mmGetByIDs.mock.t.Fatalf("PermissionDomainServiceMock.GetByIDs mock is already set by Expect")
	}

	if mmGetByIDs.defaultExpectation.paramPtrs == nil {
		mmGetByIDs.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByIDsParamPtrs{}
	}
	mmGetByIDs.defaultExpectation.paramPtrs.permissionIDs = &permissionIDs
	mmGetByIDs.defaultExpectation.expectationOrigins.originPermissionIDs = minimock.CallerInfo(1)

	return mmGetByIDs
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetByIDs
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Inspect(f func(permissionIDs []int64)) *mPermissionDomainServiceMockGetByIDs {
	if mmGetByIDs.mock.inspectFuncGetByIDs != nil {
		mmGetByIDs.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetByIDs")
	}

	mmGetByIDs.mock.inspectFuncGetByIDs = f

	return mmGetByIDs
}

// Return sets up results that will be returned by PermissionDomainService.GetByIDs
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Return(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetByIDs.mock.funcGetByIDs != nil {
		mmGetByIDs.mock.t.Fatalf("PermissionDomainServiceMock.GetByIDs mock is already set by Set")
	}

	if mmGetByIDs.defaultExpectation == nil {
		mmGetByIDs.defaultExpectation = &PermissionDomainServiceMockGetByIDsExpectation{mock: mmGetByIDs.mock}
	}
	mmGetByIDs.defaultExpectation.results = &PermissionDomainServiceMockGetByIDsResults{pa1, err}
	mmGetByIDs.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByIDs.mock
}

// Set uses given function f to mock the PermissionDomainService.GetByIDs method
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Set(f func(permissionIDs []int64) (pa1 []permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetByIDs.defaultExpectation != nil {
		mmGetByIDs.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetByIDs method")
	}

	if len(mmGetByIDs.expectations) > 0 {
		mmGetByIDs.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetByIDs method")
	}

	mmGetByIDs.mock.funcGetByIDs = f
	mmGetByIDs.mock.funcGetByIDsOrigin = minimock.CallerInfo(1)
	return mmGetByIDs.mock
}

// When sets expectation for the PermissionDomainService.GetByIDs which will trigger the result defined by the following
// Then helper
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) When(permissionIDs []int64) *PermissionDomainServiceMockGetByIDsExpectation {
	if mmGetByIDs.mock.funcGetByIDs != nil {
		mmGetByIDs.mock.t.Fatalf("PermissionDomainServiceMock.GetByIDs mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetByIDsExpectation{
		mock:               mmGetByIDs.mock,
		params:             &PermissionDomainServiceMockGetByIDsParams{permissionIDs},
		expectationOrigins: PermissionDomainServiceMockGetByIDsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByIDs.expectations = append(mmGetByIDs.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetByIDs return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetByIDsExpectation) Then(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetByIDsResults{pa1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetByIDs should be invoked
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Times(n uint64) *mPermissionDomainServiceMockGetByIDs {
	if n == 0 {
		mmGetByIDs.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetByIDs mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByIDs.expectedInvocations, n)
	mmGetByIDs.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByIDs
}

func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) invocationsDone() bool {
	if len(mmGetByIDs.expectations) == 0 && mmGetByIDs.defaultExpectation == nil && mmGetByIDs.mock.funcGetByIDs == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByIDs.mock.afterGetByIDsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByIDs.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByIDs implements mm_service.PermissionDomainService
func (mmGetByIDs *PermissionDomainServiceMock) GetByIDs(permissionIDs []int64) (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByIDs.beforeGetByIDsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByIDs.afterGetByIDsCounter, 1)

	mmGetByIDs.t.Helper()

	if mmGetByIDs.inspectFuncGetByIDs != nil {
		mmGetByIDs.inspectFuncGetByIDs(permissionIDs)
	}

	mm_params := PermissionDomainServiceMockGetByIDsParams{permissionIDs}

	// Record call args
	mmGetByIDs.GetByIDsMock.mutex.Lock()
	mmGetByIDs.GetByIDsMock.callArgs = append(mmGetByIDs.GetByIDsMock.callArgs, &mm_params)
	mmGetByIDs.GetByIDsMock.mutex.Unlock()

	for _, e := range mmGetByIDs.GetByIDsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByIDs.GetByIDsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByIDs.GetByIDsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByIDs.GetByIDsMock.defaultExpectation.params
		mm_want_ptrs := mmGetByIDs.GetByIDsMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetByIDsParams{permissionIDs}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permissionIDs != nil && !minimock.Equal(*mm_want_ptrs.permissionIDs, mm_got.permissionIDs) {
				mmGetByIDs.t.Errorf("PermissionDomainServiceMock.GetByIDs got unexpected parameter permissionIDs, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByIDs.GetByIDsMock.defaultExpectation.expectationOrigins.originPermissionIDs, *mm_want_ptrs.permissionIDs, mm_got.permissionIDs, minimock.Diff(*mm_want_ptrs.permissionIDs, mm_got.permissionIDs))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByIDs.t.Errorf("PermissionDomainServiceMock.GetByIDs got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByIDs.GetByIDsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByIDs.GetByIDsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByIDs.t.Fatal("No results are set for the PermissionDomainServiceMock.GetByIDs")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByIDs.funcGetByIDs != nil {
		return mmGetByIDs.funcGetByIDs(permissionIDs)
	}
	mmGetByIDs.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetByIDs. %v", permissionIDs)
	return
}

// GetByIDsAfterCounter returns a count of finished PermissionDomainServiceMock.GetByIDs invocations
func (mmGetByIDs *PermissionDomainServiceMock) GetByIDsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByIDs.afterGetByIDsCounter)
}

// GetByIDsBeforeCounter returns a count of PermissionDomainServiceMock.GetByIDs invocations
func (mmGetByIDs *PermissionDomainServiceMock) GetByIDsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByIDs.beforeGetByIDsCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetByIDs.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByIDs *mPermissionDomainServiceMockGetByIDs) Calls() []*PermissionDomainServiceMockGetByIDsParams {
	mmGetByIDs.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetByIDsParams, len(mmGetByIDs.callArgs))
	copy(argCopy, mmGetByIDs.callArgs)

	mmGetByIDs.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDsDone returns true if the count of the GetByIDs invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetByIDsDone() bool {
	if m.GetByIDsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDsMock.invocationsDone()
}

// MinimockGetByIDsInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetByIDsInspect() {
	for _, e := range m.GetByIDsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByIDs at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDsCounter := mm_atomic.LoadUint64(&m.afterGetByIDsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDsMock.defaultExpectation != nil && afterGetByIDsCounter < 1 {
		if m.GetByIDsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByIDs at\n%s", m.GetByIDsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByIDs at\n%s with params: %#v", m.GetByIDsMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByIDs != nil && afterGetByIDsCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByIDs at\n%s", m.funcGetByIDsOrigin)
	}

	if !m.GetByIDsMock.invocationsDone() && afterGetByIDsCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetByIDs at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDsMock.expectedInvocations), m.GetByIDsMock.expectedInvocationsOrigin, afterGetByIDsCounter)
	}
}

type mPermissionDomainServiceMockGetByNameAndMethod struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetByNameAndMethodExpectation
	expectations       []*PermissionDomainServiceMockGetByNameAndMethodExpectation

	callArgs []*PermissionDomainServiceMockGetByNameAndMethodParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetByNameAndMethodExpectation specifies expectation struct of the PermissionDomainService.GetByNameAndMethod
type PermissionDomainServiceMockGetByNameAndMethodExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetByNameAndMethodParams
	paramPtrs          *PermissionDomainServiceMockGetByNameAndMethodParamPtrs
	expectationOrigins PermissionDomainServiceMockGetByNameAndMethodExpectationOrigins
	results            *PermissionDomainServiceMockGetByNameAndMethodResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetByNameAndMethodParams contains parameters of the PermissionDomainService.GetByNameAndMethod
type PermissionDomainServiceMockGetByNameAndMethodParams struct {
	name   string
	method string
}

// PermissionDomainServiceMockGetByNameAndMethodParamPtrs contains pointers to parameters of the PermissionDomainService.GetByNameAndMethod
type PermissionDomainServiceMockGetByNameAndMethodParamPtrs struct {
	name   *string
	method *string
}

// PermissionDomainServiceMockGetByNameAndMethodResults contains results of the PermissionDomainService.GetByNameAndMethod
type PermissionDomainServiceMockGetByNameAndMethodResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionDomainServiceMockGetByNameAndMethodOrigins contains origins of expectations of the PermissionDomainService.GetByNameAndMethod
type PermissionDomainServiceMockGetByNameAndMethodExpectationOrigins struct {
	origin       string
	originName   string
	originMethod string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Optional() *mPermissionDomainServiceMockGetByNameAndMethod {
	mmGetByNameAndMethod.optional = true
	return mmGetByNameAndMethod
}

// Expect sets up expected params for PermissionDomainService.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Expect(name string, method string) *mPermissionDomainServiceMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionDomainServiceMockGetByNameAndMethodExpectation{}
	}

	if mmGetByNameAndMethod.defaultExpectation.paramPtrs != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by ExpectParams functions")
	}

	mmGetByNameAndMethod.defaultExpectation.params = &PermissionDomainServiceMockGetByNameAndMethodParams{name, method}
	mmGetByNameAndMethod.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByNameAndMethod.expectations {
		if minimock.Equal(e.params, mmGetByNameAndMethod.defaultExpectation.params) {
			mmGetByNameAndMethod.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByNameAndMethod.defaultExpectation.params)
		}
	}

	return mmGetByNameAndMethod
}

// ExpectNameParam1 sets up expected param name for PermissionDomainService.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) ExpectNameParam1(name string) *mPermissionDomainServiceMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionDomainServiceMockGetByNameAndMethodExpectation{}
	}

	if mmGetByNameAndMethod.defaultExpectation.params != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Expect")
	}

	if mmGetByNameAndMethod.defaultExpectation.paramPtrs == nil {
		mmGetByNameAndMethod.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByNameAndMethodParamPtrs{}
	}
	mmGetByNameAndMethod.defaultExpectation.paramPtrs.name = &name
	mmGetByNameAndMethod.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmGetByNameAndMethod
}

// ExpectMethodParam2 sets up expected param method for PermissionDomainService.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) ExpectMethodParam2(method string) *mPermissionDomainServiceMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionDomainServiceMockGetByNameAndMethodExpectation{}
	}

	if mmGetByNameAndMethod.defaultExpectation.params != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Expect")
	}

	if mmGetByNameAndMethod.defaultExpectation.paramPtrs == nil {
		mmGetByNameAndMethod.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByNameAndMethodParamPtrs{}
	}
	mmGetByNameAndMethod.defaultExpectation.paramPtrs.method = &method
	mmGetByNameAndMethod.defaultExpectation.expectationOrigins.originMethod = minimock.CallerInfo(1)

	return mmGetByNameAndMethod
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Inspect(f func(name string, method string)) *mPermissionDomainServiceMockGetByNameAndMethod {
	if mmGetByNameAndMethod.mock.inspectFuncGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetByNameAndMethod")
	}

	mmGetByNameAndMethod.mock.inspectFuncGetByNameAndMethod = f

	return mmGetByNameAndMethod
}

// Return sets up results that will be returned by PermissionDomainService.GetByNameAndMethod
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Return(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Set")
	}

	if mmGetByNameAndMethod.defaultExpectation == nil {
		mmGetByNameAndMethod.defaultExpectation = &PermissionDomainServiceMockGetByNameAndMethodExpectation{mock: mmGetByNameAndMethod.mock}
	}
	mmGetByNameAndMethod.defaultExpectation.results = &PermissionDomainServiceMockGetByNameAndMethodResults{p1, err}
	mmGetByNameAndMethod.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByNameAndMethod.mock
}

// Set uses given function f to mock the PermissionDomainService.GetByNameAndMethod method
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Set(f func(name string, method string) (p1 permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetByNameAndMethod.defaultExpectation != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetByNameAndMethod method")
	}

	if len(mmGetByNameAndMethod.expectations) > 0 {
		mmGetByNameAndMethod.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetByNameAndMethod method")
	}

	mmGetByNameAndMethod.mock.funcGetByNameAndMethod = f
	mmGetByNameAndMethod.mock.funcGetByNameAndMethodOrigin = minimock.CallerInfo(1)
	return mmGetByNameAndMethod.mock
}

// When sets expectation for the PermissionDomainService.GetByNameAndMethod which will trigger the result defined by the following
// Then helper
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) When(name string, method string) *PermissionDomainServiceMockGetByNameAndMethodExpectation {
	if mmGetByNameAndMethod.mock.funcGetByNameAndMethod != nil {
		mmGetByNameAndMethod.mock.t.Fatalf("PermissionDomainServiceMock.GetByNameAndMethod mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetByNameAndMethodExpectation{
		mock:               mmGetByNameAndMethod.mock,
		params:             &PermissionDomainServiceMockGetByNameAndMethodParams{name, method},
		expectationOrigins: PermissionDomainServiceMockGetByNameAndMethodExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByNameAndMethod.expectations = append(mmGetByNameAndMethod.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetByNameAndMethod return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetByNameAndMethodExpectation) Then(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetByNameAndMethodResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetByNameAndMethod should be invoked
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Times(n uint64) *mPermissionDomainServiceMockGetByNameAndMethod {
	if n == 0 {
		mmGetByNameAndMethod.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetByNameAndMethod mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByNameAndMethod.expectedInvocations, n)
	mmGetByNameAndMethod.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByNameAndMethod
}

func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) invocationsDone() bool {
	if len(mmGetByNameAndMethod.expectations) == 0 && mmGetByNameAndMethod.defaultExpectation == nil && mmGetByNameAndMethod.mock.funcGetByNameAndMethod == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByNameAndMethod.mock.afterGetByNameAndMethodCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByNameAndMethod.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByNameAndMethod implements mm_service.PermissionDomainService
func (mmGetByNameAndMethod *PermissionDomainServiceMock) GetByNameAndMethod(name string, method string) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByNameAndMethod.beforeGetByNameAndMethodCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByNameAndMethod.afterGetByNameAndMethodCounter, 1)

	mmGetByNameAndMethod.t.Helper()

	if mmGetByNameAndMethod.inspectFuncGetByNameAndMethod != nil {
		mmGetByNameAndMethod.inspectFuncGetByNameAndMethod(name, method)
	}

	mm_params := PermissionDomainServiceMockGetByNameAndMethodParams{name, method}

	// Record call args
	mmGetByNameAndMethod.GetByNameAndMethodMock.mutex.Lock()
	mmGetByNameAndMethod.GetByNameAndMethodMock.callArgs = append(mmGetByNameAndMethod.GetByNameAndMethodMock.callArgs, &mm_params)
	mmGetByNameAndMethod.GetByNameAndMethodMock.mutex.Unlock()

	for _, e := range mmGetByNameAndMethod.GetByNameAndMethodMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.params
		mm_want_ptrs := mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetByNameAndMethodParams{name, method}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmGetByNameAndMethod.t.Errorf("PermissionDomainServiceMock.GetByNameAndMethod got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

			if mm_want_ptrs.method != nil && !minimock.Equal(*mm_want_ptrs.method, mm_got.method) {
				mmGetByNameAndMethod.t.Errorf("PermissionDomainServiceMock.GetByNameAndMethod got unexpected parameter method, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.originMethod, *mm_want_ptrs.method, mm_got.method, minimock.Diff(*mm_want_ptrs.method, mm_got.method))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByNameAndMethod.t.Errorf("PermissionDomainServiceMock.GetByNameAndMethod got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByNameAndMethod.GetByNameAndMethodMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByNameAndMethod.t.Fatal("No results are set for the PermissionDomainServiceMock.GetByNameAndMethod")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetByNameAndMethod.funcGetByNameAndMethod != nil {
		return mmGetByNameAndMethod.funcGetByNameAndMethod(name, method)
	}
	mmGetByNameAndMethod.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetByNameAndMethod. %v %v", name, method)
	return
}

// GetByNameAndMethodAfterCounter returns a count of finished PermissionDomainServiceMock.GetByNameAndMethod invocations
func (mmGetByNameAndMethod *PermissionDomainServiceMock) GetByNameAndMethodAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByNameAndMethod.afterGetByNameAndMethodCounter)
}

// GetByNameAndMethodBeforeCounter returns a count of PermissionDomainServiceMock.GetByNameAndMethod invocations
func (mmGetByNameAndMethod *PermissionDomainServiceMock) GetByNameAndMethodBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByNameAndMethod.beforeGetByNameAndMethodCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetByNameAndMethod.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByNameAndMethod *mPermissionDomainServiceMockGetByNameAndMethod) Calls() []*PermissionDomainServiceMockGetByNameAndMethodParams {
	mmGetByNameAndMethod.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetByNameAndMethodParams, len(mmGetByNameAndMethod.callArgs))
	copy(argCopy, mmGetByNameAndMethod.callArgs)

	mmGetByNameAndMethod.mutex.RUnlock()

	return argCopy
}

// MinimockGetByNameAndMethodDone returns true if the count of the GetByNameAndMethod invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetByNameAndMethodDone() bool {
	if m.GetByNameAndMethodMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByNameAndMethodMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByNameAndMethodMock.invocationsDone()
}

// MinimockGetByNameAndMethodInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetByNameAndMethodInspect() {
	for _, e := range m.GetByNameAndMethodMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByNameAndMethod at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByNameAndMethodCounter := mm_atomic.LoadUint64(&m.afterGetByNameAndMethodCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByNameAndMethodMock.defaultExpectation != nil && afterGetByNameAndMethodCounter < 1 {
		if m.GetByNameAndMethodMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByNameAndMethod at\n%s", m.GetByNameAndMethodMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByNameAndMethod at\n%s with params: %#v", m.GetByNameAndMethodMock.defaultExpectation.expectationOrigins.origin, *m.GetByNameAndMethodMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByNameAndMethod != nil && afterGetByNameAndMethodCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByNameAndMethod at\n%s", m.funcGetByNameAndMethodOrigin)
	}

	if !m.GetByNameAndMethodMock.invocationsDone() && afterGetByNameAndMethodCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetByNameAndMethod at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByNameAndMethodMock.expectedInvocations), m.GetByNameAndMethodMock.expectedInvocationsOrigin, afterGetByNameAndMethodCounter)
	}
}

type mPermissionDomainServiceMockGetByRoleID struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetByRoleIDExpectation
	expectations       []*PermissionDomainServiceMockGetByRoleIDExpectation

	callArgs []*PermissionDomainServiceMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetByRoleIDExpectation specifies expectation struct of the PermissionDomainService.GetByRoleID
type PermissionDomainServiceMockGetByRoleIDExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetByRoleIDParams
	paramPtrs          *PermissionDomainServiceMockGetByRoleIDParamPtrs
	expectationOrigins PermissionDomainServiceMockGetByRoleIDExpectationOrigins
	results            *PermissionDomainServiceMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetByRoleIDParams contains parameters of the PermissionDomainService.GetByRoleID
type PermissionDomainServiceMockGetByRoleIDParams struct {
	roleID int64
}

// PermissionDomainServiceMockGetByRoleIDParamPtrs contains pointers to parameters of the PermissionDomainService.GetByRoleID
type PermissionDomainServiceMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// PermissionDomainServiceMockGetByRoleIDResults contains results of the PermissionDomainService.GetByRoleID
type PermissionDomainServiceMockGetByRoleIDResults struct {
	pa1 []permissionentity.Permission
	err error
}

// PermissionDomainServiceMockGetByRoleIDOrigins contains origins of expectations of the PermissionDomainService.GetByRoleID
type PermissionDomainServiceMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Optional() *mPermissionDomainServiceMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for PermissionDomainService.GetByRoleID
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Expect(roleID int64) *mPermissionDomainServiceMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("PermissionDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &PermissionDomainServiceMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("PermissionDomainServiceMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &PermissionDomainServiceMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for PermissionDomainService.GetByRoleID
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mPermissionDomainServiceMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("PermissionDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &PermissionDomainServiceMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("PermissionDomainServiceMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetByRoleID
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Inspect(f func(roleID int64)) *mPermissionDomainServiceMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by PermissionDomainService.GetByRoleID
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Return(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("PermissionDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &PermissionDomainServiceMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &PermissionDomainServiceMockGetByRoleIDResults{pa1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the PermissionDomainService.GetByRoleID method
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Set(f func(roleID int64) (pa1 []permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the PermissionDomainService.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) When(roleID int64) *PermissionDomainServiceMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("PermissionDomainServiceMock.GetByRoleID mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &PermissionDomainServiceMockGetByRoleIDParams{roleID},
		expectationOrigins: PermissionDomainServiceMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetByRoleIDExpectation) Then(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetByRoleIDResults{pa1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetByRoleID should be invoked
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Times(n uint64) *mPermissionDomainServiceMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_service.PermissionDomainService
func (mmGetByRoleID *PermissionDomainServiceMock) GetByRoleID(roleID int64) (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := PermissionDomainServiceMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("PermissionDomainServiceMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("PermissionDomainServiceMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the PermissionDomainServiceMock.GetByRoleID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished PermissionDomainServiceMock.GetByRoleID invocations
func (mmGetByRoleID *PermissionDomainServiceMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of PermissionDomainServiceMock.GetByRoleID invocations
func (mmGetByRoleID *PermissionDomainServiceMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mPermissionDomainServiceMockGetByRoleID) Calls() []*PermissionDomainServiceMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

type mPermissionDomainServiceMockGetByUserID struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetByUserIDExpectation
	expectations       []*PermissionDomainServiceMockGetByUserIDExpectation

	callArgs []*PermissionDomainServiceMockGetByUserIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetByUserIDExpectation specifies expectation struct of the PermissionDomainService.GetByUserID
type PermissionDomainServiceMockGetByUserIDExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetByUserIDParams
	paramPtrs          *PermissionDomainServiceMockGetByUserIDParamPtrs
	expectationOrigins PermissionDomainServiceMockGetByUserIDExpectationOrigins
	results            *PermissionDomainServiceMockGetByUserIDResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetByUserIDParams contains parameters of the PermissionDomainService.GetByUserID
type PermissionDomainServiceMockGetByUserIDParams struct {
	userID int64
}

// PermissionDomainServiceMockGetByUserIDParamPtrs contains pointers to parameters of the PermissionDomainService.GetByUserID
type PermissionDomainServiceMockGetByUserIDParamPtrs struct {
	userID *int64
}

// PermissionDomainServiceMockGetByUserIDResults contains results of the PermissionDomainService.GetByUserID
type PermissionDomainServiceMockGetByUserIDResults struct {
	pa1 []permissionentity.Permission
	err error
}

// PermissionDomainServiceMockGetByUserIDOrigins contains origins of expectations of the PermissionDomainService.GetByUserID
type PermissionDomainServiceMockGetByUserIDExpectationOrigins struct {
	origin       string
	originUserID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Optional() *mPermissionDomainServiceMockGetByUserID {
	mmGetByUserID.optional = true
	return mmGetByUserID
}

// Expect sets up expected params for PermissionDomainService.GetByUserID
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Expect(userID int64) *mPermissionDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &PermissionDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.paramPtrs != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionDomainServiceMock.GetByUserID mock is already set by ExpectParams functions")
	}

	mmGetByUserID.defaultExpectation.params = &PermissionDomainServiceMockGetByUserIDParams{userID}
	mmGetByUserID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByUserID.expectations {
		if minimock.Equal(e.params, mmGetByUserID.defaultExpectation.params) {
			mmGetByUserID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByUserID.defaultExpectation.params)
		}
	}

	return mmGetByUserID
}

// ExpectUserIDParam1 sets up expected param userID for PermissionDomainService.GetByUserID
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) ExpectUserIDParam1(userID int64) *mPermissionDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &PermissionDomainServiceMockGetByUserIDExpectation{}
	}

	if mmGetByUserID.defaultExpectation.params != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionDomainServiceMock.GetByUserID mock is already set by Expect")
	}

	if mmGetByUserID.defaultExpectation.paramPtrs == nil {
		mmGetByUserID.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetByUserIDParamPtrs{}
	}
	mmGetByUserID.defaultExpectation.paramPtrs.userID = &userID
	mmGetByUserID.defaultExpectation.expectationOrigins.originUserID = minimock.CallerInfo(1)

	return mmGetByUserID
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetByUserID
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Inspect(f func(userID int64)) *mPermissionDomainServiceMockGetByUserID {
	if mmGetByUserID.mock.inspectFuncGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetByUserID")
	}

	mmGetByUserID.mock.inspectFuncGetByUserID = f

	return mmGetByUserID
}

// Return sets up results that will be returned by PermissionDomainService.GetByUserID
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Return(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionDomainServiceMock.GetByUserID mock is already set by Set")
	}

	if mmGetByUserID.defaultExpectation == nil {
		mmGetByUserID.defaultExpectation = &PermissionDomainServiceMockGetByUserIDExpectation{mock: mmGetByUserID.mock}
	}
	mmGetByUserID.defaultExpectation.results = &PermissionDomainServiceMockGetByUserIDResults{pa1, err}
	mmGetByUserID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// Set uses given function f to mock the PermissionDomainService.GetByUserID method
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Set(f func(userID int64) (pa1 []permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmGetByUserID.defaultExpectation != nil {
		mmGetByUserID.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetByUserID method")
	}

	if len(mmGetByUserID.expectations) > 0 {
		mmGetByUserID.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetByUserID method")
	}

	mmGetByUserID.mock.funcGetByUserID = f
	mmGetByUserID.mock.funcGetByUserIDOrigin = minimock.CallerInfo(1)
	return mmGetByUserID.mock
}

// When sets expectation for the PermissionDomainService.GetByUserID which will trigger the result defined by the following
// Then helper
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) When(userID int64) *PermissionDomainServiceMockGetByUserIDExpectation {
	if mmGetByUserID.mock.funcGetByUserID != nil {
		mmGetByUserID.mock.t.Fatalf("PermissionDomainServiceMock.GetByUserID mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetByUserIDExpectation{
		mock:               mmGetByUserID.mock,
		params:             &PermissionDomainServiceMockGetByUserIDParams{userID},
		expectationOrigins: PermissionDomainServiceMockGetByUserIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByUserID.expectations = append(mmGetByUserID.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetByUserID return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetByUserIDExpectation) Then(pa1 []permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetByUserIDResults{pa1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetByUserID should be invoked
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Times(n uint64) *mPermissionDomainServiceMockGetByUserID {
	if n == 0 {
		mmGetByUserID.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetByUserID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByUserID.expectedInvocations, n)
	mmGetByUserID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByUserID
}

func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) invocationsDone() bool {
	if len(mmGetByUserID.expectations) == 0 && mmGetByUserID.defaultExpectation == nil && mmGetByUserID.mock.funcGetByUserID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByUserID.mock.afterGetByUserIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByUserID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByUserID implements mm_service.PermissionDomainService
func (mmGetByUserID *PermissionDomainServiceMock) GetByUserID(userID int64) (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetByUserID.beforeGetByUserIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByUserID.afterGetByUserIDCounter, 1)

	mmGetByUserID.t.Helper()

	if mmGetByUserID.inspectFuncGetByUserID != nil {
		mmGetByUserID.inspectFuncGetByUserID(userID)
	}

	mm_params := PermissionDomainServiceMockGetByUserIDParams{userID}

	// Record call args
	mmGetByUserID.GetByUserIDMock.mutex.Lock()
	mmGetByUserID.GetByUserIDMock.callArgs = append(mmGetByUserID.GetByUserIDMock.callArgs, &mm_params)
	mmGetByUserID.GetByUserIDMock.mutex.Unlock()

	for _, e := range mmGetByUserID.GetByUserIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetByUserID.GetByUserIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByUserID.GetByUserIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByUserID.GetByUserIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByUserID.GetByUserIDMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetByUserIDParams{userID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.userID != nil && !minimock.Equal(*mm_want_ptrs.userID, mm_got.userID) {
				mmGetByUserID.t.Errorf("PermissionDomainServiceMock.GetByUserID got unexpected parameter userID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.originUserID, *mm_want_ptrs.userID, mm_got.userID, minimock.Diff(*mm_want_ptrs.userID, mm_got.userID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByUserID.t.Errorf("PermissionDomainServiceMock.GetByUserID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByUserID.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByUserID.GetByUserIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByUserID.t.Fatal("No results are set for the PermissionDomainServiceMock.GetByUserID")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetByUserID.funcGetByUserID != nil {
		return mmGetByUserID.funcGetByUserID(userID)
	}
	mmGetByUserID.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetByUserID. %v", userID)
	return
}

// GetByUserIDAfterCounter returns a count of finished PermissionDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *PermissionDomainServiceMock) GetByUserIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.afterGetByUserIDCounter)
}

// GetByUserIDBeforeCounter returns a count of PermissionDomainServiceMock.GetByUserID invocations
func (mmGetByUserID *PermissionDomainServiceMock) GetByUserIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByUserID.beforeGetByUserIDCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetByUserID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByUserID *mPermissionDomainServiceMockGetByUserID) Calls() []*PermissionDomainServiceMockGetByUserIDParams {
	mmGetByUserID.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetByUserIDParams, len(mmGetByUserID.callArgs))
	copy(argCopy, mmGetByUserID.callArgs)

	mmGetByUserID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByUserIDDone returns true if the count of the GetByUserID invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetByUserIDDone() bool {
	if m.GetByUserIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByUserIDMock.invocationsDone()
}

// MinimockGetByUserIDInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetByUserIDInspect() {
	for _, e := range m.GetByUserIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByUserID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByUserIDCounter := mm_atomic.LoadUint64(&m.afterGetByUserIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByUserIDMock.defaultExpectation != nil && afterGetByUserIDCounter < 1 {
		if m.GetByUserIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByUserID at\n%s", m.GetByUserIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByUserID at\n%s with params: %#v", m.GetByUserIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByUserIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByUserID != nil && afterGetByUserIDCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetByUserID at\n%s", m.funcGetByUserIDOrigin)
	}

	if !m.GetByUserIDMock.invocationsDone() && afterGetByUserIDCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetByUserID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByUserIDMock.expectedInvocations), m.GetByUserIDMock.expectedInvocationsOrigin, afterGetByUserIDCounter)
	}
}

type mPermissionDomainServiceMockGetIDsByName struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockGetIDsByNameExpectation
	expectations       []*PermissionDomainServiceMockGetIDsByNameExpectation

	callArgs []*PermissionDomainServiceMockGetIDsByNameParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockGetIDsByNameExpectation specifies expectation struct of the PermissionDomainService.GetIDsByName
type PermissionDomainServiceMockGetIDsByNameExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockGetIDsByNameParams
	paramPtrs          *PermissionDomainServiceMockGetIDsByNameParamPtrs
	expectationOrigins PermissionDomainServiceMockGetIDsByNameExpectationOrigins
	results            *PermissionDomainServiceMockGetIDsByNameResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockGetIDsByNameParams contains parameters of the PermissionDomainService.GetIDsByName
type PermissionDomainServiceMockGetIDsByNameParams struct {
	name string
}

// PermissionDomainServiceMockGetIDsByNameParamPtrs contains pointers to parameters of the PermissionDomainService.GetIDsByName
type PermissionDomainServiceMockGetIDsByNameParamPtrs struct {
	name *string
}

// PermissionDomainServiceMockGetIDsByNameResults contains results of the PermissionDomainService.GetIDsByName
type PermissionDomainServiceMockGetIDsByNameResults struct {
	ia1 []int64
	err error
}

// PermissionDomainServiceMockGetIDsByNameOrigins contains origins of expectations of the PermissionDomainService.GetIDsByName
type PermissionDomainServiceMockGetIDsByNameExpectationOrigins struct {
	origin     string
	originName string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Optional() *mPermissionDomainServiceMockGetIDsByName {
	mmGetIDsByName.optional = true
	return mmGetIDsByName
}

// Expect sets up expected params for PermissionDomainService.GetIDsByName
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Expect(name string) *mPermissionDomainServiceMockGetIDsByName {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionDomainServiceMock.GetIDsByName mock is already set by Set")
	}

	if mmGetIDsByName.defaultExpectation == nil {
		mmGetIDsByName.defaultExpectation = &PermissionDomainServiceMockGetIDsByNameExpectation{}
	}

	if mmGetIDsByName.defaultExpectation.paramPtrs != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionDomainServiceMock.GetIDsByName mock is already set by ExpectParams functions")
	}

	mmGetIDsByName.defaultExpectation.params = &PermissionDomainServiceMockGetIDsByNameParams{name}
	mmGetIDsByName.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetIDsByName.expectations {
		if minimock.Equal(e.params, mmGetIDsByName.defaultExpectation.params) {
			mmGetIDsByName.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetIDsByName.defaultExpectation.params)
		}
	}

	return mmGetIDsByName
}

// ExpectNameParam1 sets up expected param name for PermissionDomainService.GetIDsByName
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) ExpectNameParam1(name string) *mPermissionDomainServiceMockGetIDsByName {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionDomainServiceMock.GetIDsByName mock is already set by Set")
	}

	if mmGetIDsByName.defaultExpectation == nil {
		mmGetIDsByName.defaultExpectation = &PermissionDomainServiceMockGetIDsByNameExpectation{}
	}

	if mmGetIDsByName.defaultExpectation.params != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionDomainServiceMock.GetIDsByName mock is already set by Expect")
	}

	if mmGetIDsByName.defaultExpectation.paramPtrs == nil {
		mmGetIDsByName.defaultExpectation.paramPtrs = &PermissionDomainServiceMockGetIDsByNameParamPtrs{}
	}
	mmGetIDsByName.defaultExpectation.paramPtrs.name = &name
	mmGetIDsByName.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmGetIDsByName
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.GetIDsByName
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Inspect(f func(name string)) *mPermissionDomainServiceMockGetIDsByName {
	if mmGetIDsByName.mock.inspectFuncGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.GetIDsByName")
	}

	mmGetIDsByName.mock.inspectFuncGetIDsByName = f

	return mmGetIDsByName
}

// Return sets up results that will be returned by PermissionDomainService.GetIDsByName
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Return(ia1 []int64, err error) *PermissionDomainServiceMock {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionDomainServiceMock.GetIDsByName mock is already set by Set")
	}

	if mmGetIDsByName.defaultExpectation == nil {
		mmGetIDsByName.defaultExpectation = &PermissionDomainServiceMockGetIDsByNameExpectation{mock: mmGetIDsByName.mock}
	}
	mmGetIDsByName.defaultExpectation.results = &PermissionDomainServiceMockGetIDsByNameResults{ia1, err}
	mmGetIDsByName.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetIDsByName.mock
}

// Set uses given function f to mock the PermissionDomainService.GetIDsByName method
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Set(f func(name string) (ia1 []int64, err error)) *PermissionDomainServiceMock {
	if mmGetIDsByName.defaultExpectation != nil {
		mmGetIDsByName.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.GetIDsByName method")
	}

	if len(mmGetIDsByName.expectations) > 0 {
		mmGetIDsByName.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.GetIDsByName method")
	}

	mmGetIDsByName.mock.funcGetIDsByName = f
	mmGetIDsByName.mock.funcGetIDsByNameOrigin = minimock.CallerInfo(1)
	return mmGetIDsByName.mock
}

// When sets expectation for the PermissionDomainService.GetIDsByName which will trigger the result defined by the following
// Then helper
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) When(name string) *PermissionDomainServiceMockGetIDsByNameExpectation {
	if mmGetIDsByName.mock.funcGetIDsByName != nil {
		mmGetIDsByName.mock.t.Fatalf("PermissionDomainServiceMock.GetIDsByName mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockGetIDsByNameExpectation{
		mock:               mmGetIDsByName.mock,
		params:             &PermissionDomainServiceMockGetIDsByNameParams{name},
		expectationOrigins: PermissionDomainServiceMockGetIDsByNameExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetIDsByName.expectations = append(mmGetIDsByName.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.GetIDsByName return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockGetIDsByNameExpectation) Then(ia1 []int64, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockGetIDsByNameResults{ia1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.GetIDsByName should be invoked
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Times(n uint64) *mPermissionDomainServiceMockGetIDsByName {
	if n == 0 {
		mmGetIDsByName.mock.t.Fatalf("Times of PermissionDomainServiceMock.GetIDsByName mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetIDsByName.expectedInvocations, n)
	mmGetIDsByName.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetIDsByName
}

func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) invocationsDone() bool {
	if len(mmGetIDsByName.expectations) == 0 && mmGetIDsByName.defaultExpectation == nil && mmGetIDsByName.mock.funcGetIDsByName == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetIDsByName.mock.afterGetIDsByNameCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetIDsByName.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetIDsByName implements mm_service.PermissionDomainService
func (mmGetIDsByName *PermissionDomainServiceMock) GetIDsByName(name string) (ia1 []int64, err error) {
	mm_atomic.AddUint64(&mmGetIDsByName.beforeGetIDsByNameCounter, 1)
	defer mm_atomic.AddUint64(&mmGetIDsByName.afterGetIDsByNameCounter, 1)

	mmGetIDsByName.t.Helper()

	if mmGetIDsByName.inspectFuncGetIDsByName != nil {
		mmGetIDsByName.inspectFuncGetIDsByName(name)
	}

	mm_params := PermissionDomainServiceMockGetIDsByNameParams{name}

	// Record call args
	mmGetIDsByName.GetIDsByNameMock.mutex.Lock()
	mmGetIDsByName.GetIDsByNameMock.callArgs = append(mmGetIDsByName.GetIDsByNameMock.callArgs, &mm_params)
	mmGetIDsByName.GetIDsByNameMock.mutex.Unlock()

	for _, e := range mmGetIDsByName.GetIDsByNameMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ia1, e.results.err
		}
	}

	if mmGetIDsByName.GetIDsByNameMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetIDsByName.GetIDsByNameMock.defaultExpectation.Counter, 1)
		mm_want := mmGetIDsByName.GetIDsByNameMock.defaultExpectation.params
		mm_want_ptrs := mmGetIDsByName.GetIDsByNameMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockGetIDsByNameParams{name}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmGetIDsByName.t.Errorf("PermissionDomainServiceMock.GetIDsByName got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetIDsByName.GetIDsByNameMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetIDsByName.t.Errorf("PermissionDomainServiceMock.GetIDsByName got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetIDsByName.GetIDsByNameMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetIDsByName.GetIDsByNameMock.defaultExpectation.results
		if mm_results == nil {
			mmGetIDsByName.t.Fatal("No results are set for the PermissionDomainServiceMock.GetIDsByName")
		}
		return (*mm_results).ia1, (*mm_results).err
	}
	if mmGetIDsByName.funcGetIDsByName != nil {
		return mmGetIDsByName.funcGetIDsByName(name)
	}
	mmGetIDsByName.t.Fatalf("Unexpected call to PermissionDomainServiceMock.GetIDsByName. %v", name)
	return
}

// GetIDsByNameAfterCounter returns a count of finished PermissionDomainServiceMock.GetIDsByName invocations
func (mmGetIDsByName *PermissionDomainServiceMock) GetIDsByNameAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetIDsByName.afterGetIDsByNameCounter)
}

// GetIDsByNameBeforeCounter returns a count of PermissionDomainServiceMock.GetIDsByName invocations
func (mmGetIDsByName *PermissionDomainServiceMock) GetIDsByNameBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetIDsByName.beforeGetIDsByNameCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.GetIDsByName.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetIDsByName *mPermissionDomainServiceMockGetIDsByName) Calls() []*PermissionDomainServiceMockGetIDsByNameParams {
	mmGetIDsByName.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockGetIDsByNameParams, len(mmGetIDsByName.callArgs))
	copy(argCopy, mmGetIDsByName.callArgs)

	mmGetIDsByName.mutex.RUnlock()

	return argCopy
}

// MinimockGetIDsByNameDone returns true if the count of the GetIDsByName invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockGetIDsByNameDone() bool {
	if m.GetIDsByNameMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetIDsByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetIDsByNameMock.invocationsDone()
}

// MinimockGetIDsByNameInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockGetIDsByNameInspect() {
	for _, e := range m.GetIDsByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetIDsByName at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetIDsByNameCounter := mm_atomic.LoadUint64(&m.afterGetIDsByNameCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetIDsByNameMock.defaultExpectation != nil && afterGetIDsByNameCounter < 1 {
		if m.GetIDsByNameMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetIDsByName at\n%s", m.GetIDsByNameMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.GetIDsByName at\n%s with params: %#v", m.GetIDsByNameMock.defaultExpectation.expectationOrigins.origin, *m.GetIDsByNameMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetIDsByName != nil && afterGetIDsByNameCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.GetIDsByName at\n%s", m.funcGetIDsByNameOrigin)
	}

	if !m.GetIDsByNameMock.invocationsDone() && afterGetIDsByNameCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.GetIDsByName at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetIDsByNameMock.expectedInvocations), m.GetIDsByNameMock.expectedInvocationsOrigin, afterGetIDsByNameCounter)
	}
}

type mPermissionDomainServiceMockHasCategoryPermission struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockHasCategoryPermissionExpectation
	expectations       []*PermissionDomainServiceMockHasCategoryPermissionExpectation

	callArgs []*PermissionDomainServiceMockHasCategoryPermissionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockHasCategoryPermissionExpectation specifies expectation struct of the PermissionDomainService.HasCategoryPermission
type PermissionDomainServiceMockHasCategoryPermissionExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockHasCategoryPermissionParams
	paramPtrs          *PermissionDomainServiceMockHasCategoryPermissionParamPtrs
	expectationOrigins PermissionDomainServiceMockHasCategoryPermissionExpectationOrigins
	results            *PermissionDomainServiceMockHasCategoryPermissionResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockHasCategoryPermissionParams contains parameters of the PermissionDomainService.HasCategoryPermission
type PermissionDomainServiceMockHasCategoryPermissionParams struct {
	categoryID   int64
	permissionID int64
}

// PermissionDomainServiceMockHasCategoryPermissionParamPtrs contains pointers to parameters of the PermissionDomainService.HasCategoryPermission
type PermissionDomainServiceMockHasCategoryPermissionParamPtrs struct {
	categoryID   *int64
	permissionID *int64
}

// PermissionDomainServiceMockHasCategoryPermissionResults contains results of the PermissionDomainService.HasCategoryPermission
type PermissionDomainServiceMockHasCategoryPermissionResults struct {
	b1  bool
	err error
}

// PermissionDomainServiceMockHasCategoryPermissionOrigins contains origins of expectations of the PermissionDomainService.HasCategoryPermission
type PermissionDomainServiceMockHasCategoryPermissionExpectationOrigins struct {
	origin             string
	originCategoryID   string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Optional() *mPermissionDomainServiceMockHasCategoryPermission {
	mmHasCategoryPermission.optional = true
	return mmHasCategoryPermission
}

// Expect sets up expected params for PermissionDomainService.HasCategoryPermission
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Expect(categoryID int64, permissionID int64) *mPermissionDomainServiceMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &PermissionDomainServiceMockHasCategoryPermissionExpectation{}
	}

	if mmHasCategoryPermission.defaultExpectation.paramPtrs != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by ExpectParams functions")
	}

	mmHasCategoryPermission.defaultExpectation.params = &PermissionDomainServiceMockHasCategoryPermissionParams{categoryID, permissionID}
	mmHasCategoryPermission.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmHasCategoryPermission.expectations {
		if minimock.Equal(e.params, mmHasCategoryPermission.defaultExpectation.params) {
			mmHasCategoryPermission.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmHasCategoryPermission.defaultExpectation.params)
		}
	}

	return mmHasCategoryPermission
}

// ExpectCategoryIDParam1 sets up expected param categoryID for PermissionDomainService.HasCategoryPermission
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) ExpectCategoryIDParam1(categoryID int64) *mPermissionDomainServiceMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &PermissionDomainServiceMockHasCategoryPermissionExpectation{}
	}

	if mmHasCategoryPermission.defaultExpectation.params != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Expect")
	}

	if mmHasCategoryPermission.defaultExpectation.paramPtrs == nil {
		mmHasCategoryPermission.defaultExpectation.paramPtrs = &PermissionDomainServiceMockHasCategoryPermissionParamPtrs{}
	}
	mmHasCategoryPermission.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmHasCategoryPermission.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmHasCategoryPermission
}

// ExpectPermissionIDParam2 sets up expected param permissionID for PermissionDomainService.HasCategoryPermission
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) ExpectPermissionIDParam2(permissionID int64) *mPermissionDomainServiceMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &PermissionDomainServiceMockHasCategoryPermissionExpectation{}
	}

	if mmHasCategoryPermission.defaultExpectation.params != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Expect")
	}

	if mmHasCategoryPermission.defaultExpectation.paramPtrs == nil {
		mmHasCategoryPermission.defaultExpectation.paramPtrs = &PermissionDomainServiceMockHasCategoryPermissionParamPtrs{}
	}
	mmHasCategoryPermission.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmHasCategoryPermission.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmHasCategoryPermission
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.HasCategoryPermission
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Inspect(f func(categoryID int64, permissionID int64)) *mPermissionDomainServiceMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.inspectFuncHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.HasCategoryPermission")
	}

	mmHasCategoryPermission.mock.inspectFuncHasCategoryPermission = f

	return mmHasCategoryPermission
}

// Return sets up results that will be returned by PermissionDomainService.HasCategoryPermission
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Return(b1 bool, err error) *PermissionDomainServiceMock {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &PermissionDomainServiceMockHasCategoryPermissionExpectation{mock: mmHasCategoryPermission.mock}
	}
	mmHasCategoryPermission.defaultExpectation.results = &PermissionDomainServiceMockHasCategoryPermissionResults{b1, err}
	mmHasCategoryPermission.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmHasCategoryPermission.mock
}

// Set uses given function f to mock the PermissionDomainService.HasCategoryPermission method
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Set(f func(categoryID int64, permissionID int64) (b1 bool, err error)) *PermissionDomainServiceMock {
	if mmHasCategoryPermission.defaultExpectation != nil {
		mmHasCategoryPermission.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.HasCategoryPermission method")
	}

	if len(mmHasCategoryPermission.expectations) > 0 {
		mmHasCategoryPermission.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.HasCategoryPermission method")
	}

	mmHasCategoryPermission.mock.funcHasCategoryPermission = f
	mmHasCategoryPermission.mock.funcHasCategoryPermissionOrigin = minimock.CallerInfo(1)
	return mmHasCategoryPermission.mock
}

// When sets expectation for the PermissionDomainService.HasCategoryPermission which will trigger the result defined by the following
// Then helper
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) When(categoryID int64, permissionID int64) *PermissionDomainServiceMockHasCategoryPermissionExpectation {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("PermissionDomainServiceMock.HasCategoryPermission mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockHasCategoryPermissionExpectation{
		mock:               mmHasCategoryPermission.mock,
		params:             &PermissionDomainServiceMockHasCategoryPermissionParams{categoryID, permissionID},
		expectationOrigins: PermissionDomainServiceMockHasCategoryPermissionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmHasCategoryPermission.expectations = append(mmHasCategoryPermission.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.HasCategoryPermission return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockHasCategoryPermissionExpectation) Then(b1 bool, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockHasCategoryPermissionResults{b1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.HasCategoryPermission should be invoked
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Times(n uint64) *mPermissionDomainServiceMockHasCategoryPermission {
	if n == 0 {
		mmHasCategoryPermission.mock.t.Fatalf("Times of PermissionDomainServiceMock.HasCategoryPermission mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmHasCategoryPermission.expectedInvocations, n)
	mmHasCategoryPermission.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmHasCategoryPermission
}

func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) invocationsDone() bool {
	if len(mmHasCategoryPermission.expectations) == 0 && mmHasCategoryPermission.defaultExpectation == nil && mmHasCategoryPermission.mock.funcHasCategoryPermission == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmHasCategoryPermission.mock.afterHasCategoryPermissionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmHasCategoryPermission.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// HasCategoryPermission implements mm_service.PermissionDomainService
func (mmHasCategoryPermission *PermissionDomainServiceMock) HasCategoryPermission(categoryID int64, permissionID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmHasCategoryPermission.beforeHasCategoryPermissionCounter, 1)
	defer mm_atomic.AddUint64(&mmHasCategoryPermission.afterHasCategoryPermissionCounter, 1)

	mmHasCategoryPermission.t.Helper()

	if mmHasCategoryPermission.inspectFuncHasCategoryPermission != nil {
		mmHasCategoryPermission.inspectFuncHasCategoryPermission(categoryID, permissionID)
	}

	mm_params := PermissionDomainServiceMockHasCategoryPermissionParams{categoryID, permissionID}

	// Record call args
	mmHasCategoryPermission.HasCategoryPermissionMock.mutex.Lock()
	mmHasCategoryPermission.HasCategoryPermissionMock.callArgs = append(mmHasCategoryPermission.HasCategoryPermissionMock.callArgs, &mm_params)
	mmHasCategoryPermission.HasCategoryPermissionMock.mutex.Unlock()

	for _, e := range mmHasCategoryPermission.HasCategoryPermissionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.Counter, 1)
		mm_want := mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.params
		mm_want_ptrs := mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockHasCategoryPermissionParams{categoryID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmHasCategoryPermission.t.Errorf("PermissionDomainServiceMock.HasCategoryPermission got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmHasCategoryPermission.t.Errorf("PermissionDomainServiceMock.HasCategoryPermission got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmHasCategoryPermission.t.Errorf("PermissionDomainServiceMock.HasCategoryPermission got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.results
		if mm_results == nil {
			mmHasCategoryPermission.t.Fatal("No results are set for the PermissionDomainServiceMock.HasCategoryPermission")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmHasCategoryPermission.funcHasCategoryPermission != nil {
		return mmHasCategoryPermission.funcHasCategoryPermission(categoryID, permissionID)
	}
	mmHasCategoryPermission.t.Fatalf("Unexpected call to PermissionDomainServiceMock.HasCategoryPermission. %v %v", categoryID, permissionID)
	return
}

// HasCategoryPermissionAfterCounter returns a count of finished PermissionDomainServiceMock.HasCategoryPermission invocations
func (mmHasCategoryPermission *PermissionDomainServiceMock) HasCategoryPermissionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryPermission.afterHasCategoryPermissionCounter)
}

// HasCategoryPermissionBeforeCounter returns a count of PermissionDomainServiceMock.HasCategoryPermission invocations
func (mmHasCategoryPermission *PermissionDomainServiceMock) HasCategoryPermissionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryPermission.beforeHasCategoryPermissionCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.HasCategoryPermission.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmHasCategoryPermission *mPermissionDomainServiceMockHasCategoryPermission) Calls() []*PermissionDomainServiceMockHasCategoryPermissionParams {
	mmHasCategoryPermission.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockHasCategoryPermissionParams, len(mmHasCategoryPermission.callArgs))
	copy(argCopy, mmHasCategoryPermission.callArgs)

	mmHasCategoryPermission.mutex.RUnlock()

	return argCopy
}

// MinimockHasCategoryPermissionDone returns true if the count of the HasCategoryPermission invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockHasCategoryPermissionDone() bool {
	if m.HasCategoryPermissionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.HasCategoryPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.HasCategoryPermissionMock.invocationsDone()
}

// MinimockHasCategoryPermissionInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockHasCategoryPermissionInspect() {
	for _, e := range m.HasCategoryPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.HasCategoryPermission at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterHasCategoryPermissionCounter := mm_atomic.LoadUint64(&m.afterHasCategoryPermissionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.HasCategoryPermissionMock.defaultExpectation != nil && afterHasCategoryPermissionCounter < 1 {
		if m.HasCategoryPermissionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.HasCategoryPermission at\n%s", m.HasCategoryPermissionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.HasCategoryPermission at\n%s with params: %#v", m.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.origin, *m.HasCategoryPermissionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcHasCategoryPermission != nil && afterHasCategoryPermissionCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.HasCategoryPermission at\n%s", m.funcHasCategoryPermissionOrigin)
	}

	if !m.HasCategoryPermissionMock.invocationsDone() && afterHasCategoryPermissionCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.HasCategoryPermission at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.HasCategoryPermissionMock.expectedInvocations), m.HasCategoryPermissionMock.expectedInvocationsOrigin, afterHasCategoryPermissionCounter)
	}
}

type mPermissionDomainServiceMockUnassignPermissionFromCategory struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation
	expectations       []*PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation

	callArgs []*PermissionDomainServiceMockUnassignPermissionFromCategoryParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation specifies expectation struct of the PermissionDomainService.UnassignPermissionFromCategory
type PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockUnassignPermissionFromCategoryParams
	paramPtrs          *PermissionDomainServiceMockUnassignPermissionFromCategoryParamPtrs
	expectationOrigins PermissionDomainServiceMockUnassignPermissionFromCategoryExpectationOrigins
	results            *PermissionDomainServiceMockUnassignPermissionFromCategoryResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockUnassignPermissionFromCategoryParams contains parameters of the PermissionDomainService.UnassignPermissionFromCategory
type PermissionDomainServiceMockUnassignPermissionFromCategoryParams struct {
	categoryID   int64
	permissionID int64
}

// PermissionDomainServiceMockUnassignPermissionFromCategoryParamPtrs contains pointers to parameters of the PermissionDomainService.UnassignPermissionFromCategory
type PermissionDomainServiceMockUnassignPermissionFromCategoryParamPtrs struct {
	categoryID   *int64
	permissionID *int64
}

// PermissionDomainServiceMockUnassignPermissionFromCategoryResults contains results of the PermissionDomainService.UnassignPermissionFromCategory
type PermissionDomainServiceMockUnassignPermissionFromCategoryResults struct {
	err error
}

// PermissionDomainServiceMockUnassignPermissionFromCategoryOrigins contains origins of expectations of the PermissionDomainService.UnassignPermissionFromCategory
type PermissionDomainServiceMockUnassignPermissionFromCategoryExpectationOrigins struct {
	origin             string
	originCategoryID   string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Optional() *mPermissionDomainServiceMockUnassignPermissionFromCategory {
	mmUnassignPermissionFromCategory.optional = true
	return mmUnassignPermissionFromCategory
}

// Expect sets up expected params for PermissionDomainService.UnassignPermissionFromCategory
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Expect(categoryID int64, permissionID int64) *mPermissionDomainServiceMockUnassignPermissionFromCategory {
	if mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Set")
	}

	if mmUnassignPermissionFromCategory.defaultExpectation == nil {
		mmUnassignPermissionFromCategory.defaultExpectation = &PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation{}
	}

	if mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by ExpectParams functions")
	}

	mmUnassignPermissionFromCategory.defaultExpectation.params = &PermissionDomainServiceMockUnassignPermissionFromCategoryParams{categoryID, permissionID}
	mmUnassignPermissionFromCategory.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUnassignPermissionFromCategory.expectations {
		if minimock.Equal(e.params, mmUnassignPermissionFromCategory.defaultExpectation.params) {
			mmUnassignPermissionFromCategory.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUnassignPermissionFromCategory.defaultExpectation.params)
		}
	}

	return mmUnassignPermissionFromCategory
}

// ExpectCategoryIDParam1 sets up expected param categoryID for PermissionDomainService.UnassignPermissionFromCategory
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) ExpectCategoryIDParam1(categoryID int64) *mPermissionDomainServiceMockUnassignPermissionFromCategory {
	if mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Set")
	}

	if mmUnassignPermissionFromCategory.defaultExpectation == nil {
		mmUnassignPermissionFromCategory.defaultExpectation = &PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation{}
	}

	if mmUnassignPermissionFromCategory.defaultExpectation.params != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Expect")
	}

	if mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs == nil {
		mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs = &PermissionDomainServiceMockUnassignPermissionFromCategoryParamPtrs{}
	}
	mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmUnassignPermissionFromCategory.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmUnassignPermissionFromCategory
}

// ExpectPermissionIDParam2 sets up expected param permissionID for PermissionDomainService.UnassignPermissionFromCategory
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) ExpectPermissionIDParam2(permissionID int64) *mPermissionDomainServiceMockUnassignPermissionFromCategory {
	if mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Set")
	}

	if mmUnassignPermissionFromCategory.defaultExpectation == nil {
		mmUnassignPermissionFromCategory.defaultExpectation = &PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation{}
	}

	if mmUnassignPermissionFromCategory.defaultExpectation.params != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Expect")
	}

	if mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs == nil {
		mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs = &PermissionDomainServiceMockUnassignPermissionFromCategoryParamPtrs{}
	}
	mmUnassignPermissionFromCategory.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmUnassignPermissionFromCategory.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmUnassignPermissionFromCategory
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.UnassignPermissionFromCategory
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Inspect(f func(categoryID int64, permissionID int64)) *mPermissionDomainServiceMockUnassignPermissionFromCategory {
	if mmUnassignPermissionFromCategory.mock.inspectFuncUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.UnassignPermissionFromCategory")
	}

	mmUnassignPermissionFromCategory.mock.inspectFuncUnassignPermissionFromCategory = f

	return mmUnassignPermissionFromCategory
}

// Return sets up results that will be returned by PermissionDomainService.UnassignPermissionFromCategory
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Return(err error) *PermissionDomainServiceMock {
	if mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Set")
	}

	if mmUnassignPermissionFromCategory.defaultExpectation == nil {
		mmUnassignPermissionFromCategory.defaultExpectation = &PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation{mock: mmUnassignPermissionFromCategory.mock}
	}
	mmUnassignPermissionFromCategory.defaultExpectation.results = &PermissionDomainServiceMockUnassignPermissionFromCategoryResults{err}
	mmUnassignPermissionFromCategory.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUnassignPermissionFromCategory.mock
}

// Set uses given function f to mock the PermissionDomainService.UnassignPermissionFromCategory method
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Set(f func(categoryID int64, permissionID int64) (err error)) *PermissionDomainServiceMock {
	if mmUnassignPermissionFromCategory.defaultExpectation != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.UnassignPermissionFromCategory method")
	}

	if len(mmUnassignPermissionFromCategory.expectations) > 0 {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.UnassignPermissionFromCategory method")
	}

	mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory = f
	mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategoryOrigin = minimock.CallerInfo(1)
	return mmUnassignPermissionFromCategory.mock
}

// When sets expectation for the PermissionDomainService.UnassignPermissionFromCategory which will trigger the result defined by the following
// Then helper
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) When(categoryID int64, permissionID int64) *PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation {
	if mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("PermissionDomainServiceMock.UnassignPermissionFromCategory mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation{
		mock:               mmUnassignPermissionFromCategory.mock,
		params:             &PermissionDomainServiceMockUnassignPermissionFromCategoryParams{categoryID, permissionID},
		expectationOrigins: PermissionDomainServiceMockUnassignPermissionFromCategoryExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUnassignPermissionFromCategory.expectations = append(mmUnassignPermissionFromCategory.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.UnassignPermissionFromCategory return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockUnassignPermissionFromCategoryExpectation) Then(err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockUnassignPermissionFromCategoryResults{err}
	return e.mock
}

// Times sets number of times PermissionDomainService.UnassignPermissionFromCategory should be invoked
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Times(n uint64) *mPermissionDomainServiceMockUnassignPermissionFromCategory {
	if n == 0 {
		mmUnassignPermissionFromCategory.mock.t.Fatalf("Times of PermissionDomainServiceMock.UnassignPermissionFromCategory mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUnassignPermissionFromCategory.expectedInvocations, n)
	mmUnassignPermissionFromCategory.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUnassignPermissionFromCategory
}

func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) invocationsDone() bool {
	if len(mmUnassignPermissionFromCategory.expectations) == 0 && mmUnassignPermissionFromCategory.defaultExpectation == nil && mmUnassignPermissionFromCategory.mock.funcUnassignPermissionFromCategory == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUnassignPermissionFromCategory.mock.afterUnassignPermissionFromCategoryCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUnassignPermissionFromCategory.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// UnassignPermissionFromCategory implements mm_service.PermissionDomainService
func (mmUnassignPermissionFromCategory *PermissionDomainServiceMock) UnassignPermissionFromCategory(categoryID int64, permissionID int64) (err error) {
	mm_atomic.AddUint64(&mmUnassignPermissionFromCategory.beforeUnassignPermissionFromCategoryCounter, 1)
	defer mm_atomic.AddUint64(&mmUnassignPermissionFromCategory.afterUnassignPermissionFromCategoryCounter, 1)

	mmUnassignPermissionFromCategory.t.Helper()

	if mmUnassignPermissionFromCategory.inspectFuncUnassignPermissionFromCategory != nil {
		mmUnassignPermissionFromCategory.inspectFuncUnassignPermissionFromCategory(categoryID, permissionID)
	}

	mm_params := PermissionDomainServiceMockUnassignPermissionFromCategoryParams{categoryID, permissionID}

	// Record call args
	mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.mutex.Lock()
	mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.callArgs = append(mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.callArgs, &mm_params)
	mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.mutex.Unlock()

	for _, e := range mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.Counter, 1)
		mm_want := mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.params
		mm_want_ptrs := mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockUnassignPermissionFromCategoryParams{categoryID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmUnassignPermissionFromCategory.t.Errorf("PermissionDomainServiceMock.UnassignPermissionFromCategory got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmUnassignPermissionFromCategory.t.Errorf("PermissionDomainServiceMock.UnassignPermissionFromCategory got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUnassignPermissionFromCategory.t.Errorf("PermissionDomainServiceMock.UnassignPermissionFromCategory got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUnassignPermissionFromCategory.UnassignPermissionFromCategoryMock.defaultExpectation.results
		if mm_results == nil {
			mmUnassignPermissionFromCategory.t.Fatal("No results are set for the PermissionDomainServiceMock.UnassignPermissionFromCategory")
		}
		return (*mm_results).err
	}
	if mmUnassignPermissionFromCategory.funcUnassignPermissionFromCategory != nil {
		return mmUnassignPermissionFromCategory.funcUnassignPermissionFromCategory(categoryID, permissionID)
	}
	mmUnassignPermissionFromCategory.t.Fatalf("Unexpected call to PermissionDomainServiceMock.UnassignPermissionFromCategory. %v %v", categoryID, permissionID)
	return
}

// UnassignPermissionFromCategoryAfterCounter returns a count of finished PermissionDomainServiceMock.UnassignPermissionFromCategory invocations
func (mmUnassignPermissionFromCategory *PermissionDomainServiceMock) UnassignPermissionFromCategoryAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUnassignPermissionFromCategory.afterUnassignPermissionFromCategoryCounter)
}

// UnassignPermissionFromCategoryBeforeCounter returns a count of PermissionDomainServiceMock.UnassignPermissionFromCategory invocations
func (mmUnassignPermissionFromCategory *PermissionDomainServiceMock) UnassignPermissionFromCategoryBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUnassignPermissionFromCategory.beforeUnassignPermissionFromCategoryCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.UnassignPermissionFromCategory.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUnassignPermissionFromCategory *mPermissionDomainServiceMockUnassignPermissionFromCategory) Calls() []*PermissionDomainServiceMockUnassignPermissionFromCategoryParams {
	mmUnassignPermissionFromCategory.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockUnassignPermissionFromCategoryParams, len(mmUnassignPermissionFromCategory.callArgs))
	copy(argCopy, mmUnassignPermissionFromCategory.callArgs)

	mmUnassignPermissionFromCategory.mutex.RUnlock()

	return argCopy
}

// MinimockUnassignPermissionFromCategoryDone returns true if the count of the UnassignPermissionFromCategory invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockUnassignPermissionFromCategoryDone() bool {
	if m.UnassignPermissionFromCategoryMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UnassignPermissionFromCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UnassignPermissionFromCategoryMock.invocationsDone()
}

// MinimockUnassignPermissionFromCategoryInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockUnassignPermissionFromCategoryInspect() {
	for _, e := range m.UnassignPermissionFromCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.UnassignPermissionFromCategory at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUnassignPermissionFromCategoryCounter := mm_atomic.LoadUint64(&m.afterUnassignPermissionFromCategoryCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UnassignPermissionFromCategoryMock.defaultExpectation != nil && afterUnassignPermissionFromCategoryCounter < 1 {
		if m.UnassignPermissionFromCategoryMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.UnassignPermissionFromCategory at\n%s", m.UnassignPermissionFromCategoryMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.UnassignPermissionFromCategory at\n%s with params: %#v", m.UnassignPermissionFromCategoryMock.defaultExpectation.expectationOrigins.origin, *m.UnassignPermissionFromCategoryMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUnassignPermissionFromCategory != nil && afterUnassignPermissionFromCategoryCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.UnassignPermissionFromCategory at\n%s", m.funcUnassignPermissionFromCategoryOrigin)
	}

	if !m.UnassignPermissionFromCategoryMock.invocationsDone() && afterUnassignPermissionFromCategoryCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.UnassignPermissionFromCategory at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UnassignPermissionFromCategoryMock.expectedInvocations), m.UnassignPermissionFromCategoryMock.expectedInvocationsOrigin, afterUnassignPermissionFromCategoryCounter)
	}
}

type mPermissionDomainServiceMockUpdate struct {
	optional           bool
	mock               *PermissionDomainServiceMock
	defaultExpectation *PermissionDomainServiceMockUpdateExpectation
	expectations       []*PermissionDomainServiceMockUpdateExpectation

	callArgs []*PermissionDomainServiceMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionDomainServiceMockUpdateExpectation specifies expectation struct of the PermissionDomainService.Update
type PermissionDomainServiceMockUpdateExpectation struct {
	mock               *PermissionDomainServiceMock
	params             *PermissionDomainServiceMockUpdateParams
	paramPtrs          *PermissionDomainServiceMockUpdateParamPtrs
	expectationOrigins PermissionDomainServiceMockUpdateExpectationOrigins
	results            *PermissionDomainServiceMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// PermissionDomainServiceMockUpdateParams contains parameters of the PermissionDomainService.Update
type PermissionDomainServiceMockUpdateParams struct {
	permission permissionentity.Permission
}

// PermissionDomainServiceMockUpdateParamPtrs contains pointers to parameters of the PermissionDomainService.Update
type PermissionDomainServiceMockUpdateParamPtrs struct {
	permission *permissionentity.Permission
}

// PermissionDomainServiceMockUpdateResults contains results of the PermissionDomainService.Update
type PermissionDomainServiceMockUpdateResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionDomainServiceMockUpdateOrigins contains origins of expectations of the PermissionDomainService.Update
type PermissionDomainServiceMockUpdateExpectationOrigins struct {
	origin           string
	originPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mPermissionDomainServiceMockUpdate) Optional() *mPermissionDomainServiceMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for PermissionDomainService.Update
func (mmUpdate *mPermissionDomainServiceMockUpdate) Expect(permission permissionentity.Permission) *mPermissionDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &PermissionDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("PermissionDomainServiceMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &PermissionDomainServiceMockUpdateParams{permission}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectPermissionParam1 sets up expected param permission for PermissionDomainService.Update
func (mmUpdate *mPermissionDomainServiceMockUpdate) ExpectPermissionParam1(permission permissionentity.Permission) *mPermissionDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &PermissionDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("PermissionDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &PermissionDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.permission = &permission
	mmUpdate.defaultExpectation.expectationOrigins.originPermission = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the PermissionDomainService.Update
func (mmUpdate *mPermissionDomainServiceMockUpdate) Inspect(f func(permission permissionentity.Permission)) *mPermissionDomainServiceMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for PermissionDomainServiceMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by PermissionDomainService.Update
func (mmUpdate *mPermissionDomainServiceMockUpdate) Return(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &PermissionDomainServiceMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &PermissionDomainServiceMockUpdateResults{p1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the PermissionDomainService.Update method
func (mmUpdate *mPermissionDomainServiceMockUpdate) Set(f func(permission permissionentity.Permission) (p1 permissionentity.Permission, err error)) *PermissionDomainServiceMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the PermissionDomainService.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the PermissionDomainService.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the PermissionDomainService.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mPermissionDomainServiceMockUpdate) When(permission permissionentity.Permission) *PermissionDomainServiceMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("PermissionDomainServiceMock.Update mock is already set by Set")
	}

	expectation := &PermissionDomainServiceMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &PermissionDomainServiceMockUpdateParams{permission},
		expectationOrigins: PermissionDomainServiceMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up PermissionDomainService.Update return parameters for the expectation previously defined by the When method
func (e *PermissionDomainServiceMockUpdateExpectation) Then(p1 permissionentity.Permission, err error) *PermissionDomainServiceMock {
	e.results = &PermissionDomainServiceMockUpdateResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionDomainService.Update should be invoked
func (mmUpdate *mPermissionDomainServiceMockUpdate) Times(n uint64) *mPermissionDomainServiceMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of PermissionDomainServiceMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mPermissionDomainServiceMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_service.PermissionDomainService
func (mmUpdate *PermissionDomainServiceMock) Update(permission permissionentity.Permission) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(permission)
	}

	mm_params := PermissionDomainServiceMockUpdateParams{permission}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := PermissionDomainServiceMockUpdateParams{permission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.permission != nil && !minimock.Equal(*mm_want_ptrs.permission, mm_got.permission) {
				mmUpdate.t.Errorf("PermissionDomainServiceMock.Update got unexpected parameter permission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originPermission, *mm_want_ptrs.permission, mm_got.permission, minimock.Diff(*mm_want_ptrs.permission, mm_got.permission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("PermissionDomainServiceMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the PermissionDomainServiceMock.Update")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(permission)
	}
	mmUpdate.t.Fatalf("Unexpected call to PermissionDomainServiceMock.Update. %v", permission)
	return
}

// UpdateAfterCounter returns a count of finished PermissionDomainServiceMock.Update invocations
func (mmUpdate *PermissionDomainServiceMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of PermissionDomainServiceMock.Update invocations
func (mmUpdate *PermissionDomainServiceMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to PermissionDomainServiceMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mPermissionDomainServiceMockUpdate) Calls() []*PermissionDomainServiceMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*PermissionDomainServiceMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *PermissionDomainServiceMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *PermissionDomainServiceMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionDomainServiceMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to PermissionDomainServiceMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionDomainServiceMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *PermissionDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockAssignPermissionToCategoryInspect()

			m.MinimockCreateInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetAllCategoryPermissionsInspect()

			m.MinimockGetAllUniqueNamesInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByIDsInspect()

			m.MinimockGetByNameAndMethodInspect()

			m.MinimockGetByRoleIDInspect()

			m.MinimockGetByUserIDInspect()

			m.MinimockGetIDsByNameInspect()

			m.MinimockHasCategoryPermissionInspect()

			m.MinimockUnassignPermissionFromCategoryInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *PermissionDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *PermissionDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockAssignPermissionToCategoryDone() &&
		m.MinimockCreateDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetAllCategoryPermissionsDone() &&
		m.MinimockGetAllUniqueNamesDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByIDsDone() &&
		m.MinimockGetByNameAndMethodDone() &&
		m.MinimockGetByRoleIDDone() &&
		m.MinimockGetByUserIDDone() &&
		m.MinimockGetIDsByNameDone() &&
		m.MinimockHasCategoryPermissionDone() &&
		m.MinimockUnassignPermissionFromCategoryDone() &&
		m.MinimockUpdateDone()
}
