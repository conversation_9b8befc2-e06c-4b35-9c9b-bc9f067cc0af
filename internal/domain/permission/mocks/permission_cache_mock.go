// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/repository.PermissionCache -o permission_cache_mock.go -n PermissionCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	"github.com/gojuno/minimock/v3"
)

// PermissionCacheMock implements mm_repository.PermissionCache
type PermissionCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetPermission          func(ctx context.Context, id int64) (p1 permissionentity.Permission, err error)
	funcGetPermissionOrigin    string
	inspectFuncGetPermission   func(ctx context.Context, id int64)
	afterGetPermissionCounter  uint64
	beforeGetPermissionCounter uint64
	GetPermissionMock          mPermissionCacheMockGetPermission

	funcGetPermissions          func(ctx context.Context) (pa1 []permissionentity.Permission, err error)
	funcGetPermissionsOrigin    string
	inspectFuncGetPermissions   func(ctx context.Context)
	afterGetPermissionsCounter  uint64
	beforeGetPermissionsCounter uint64
	GetPermissionsMock          mPermissionCacheMockGetPermissions

	funcSetPermission          func(ctx context.Context, permission permissionentity.Permission) (err error)
	funcSetPermissionOrigin    string
	inspectFuncSetPermission   func(ctx context.Context, permission permissionentity.Permission)
	afterSetPermissionCounter  uint64
	beforeSetPermissionCounter uint64
	SetPermissionMock          mPermissionCacheMockSetPermission

	funcSetPermissions          func(ctx context.Context, permissions []permissionentity.Permission) (err error)
	funcSetPermissionsOrigin    string
	inspectFuncSetPermissions   func(ctx context.Context, permissions []permissionentity.Permission)
	afterSetPermissionsCounter  uint64
	beforeSetPermissionsCounter uint64
	SetPermissionsMock          mPermissionCacheMockSetPermissions
}

// NewPermissionCacheMock returns a mock for mm_repository.PermissionCache
func NewPermissionCacheMock(t minimock.Tester) *PermissionCacheMock {
	m := &PermissionCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetPermissionMock = mPermissionCacheMockGetPermission{mock: m}
	m.GetPermissionMock.callArgs = []*PermissionCacheMockGetPermissionParams{}

	m.GetPermissionsMock = mPermissionCacheMockGetPermissions{mock: m}
	m.GetPermissionsMock.callArgs = []*PermissionCacheMockGetPermissionsParams{}

	m.SetPermissionMock = mPermissionCacheMockSetPermission{mock: m}
	m.SetPermissionMock.callArgs = []*PermissionCacheMockSetPermissionParams{}

	m.SetPermissionsMock = mPermissionCacheMockSetPermissions{mock: m}
	m.SetPermissionsMock.callArgs = []*PermissionCacheMockSetPermissionsParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mPermissionCacheMockGetPermission struct {
	optional           bool
	mock               *PermissionCacheMock
	defaultExpectation *PermissionCacheMockGetPermissionExpectation
	expectations       []*PermissionCacheMockGetPermissionExpectation

	callArgs []*PermissionCacheMockGetPermissionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionCacheMockGetPermissionExpectation specifies expectation struct of the PermissionCache.GetPermission
type PermissionCacheMockGetPermissionExpectation struct {
	mock               *PermissionCacheMock
	params             *PermissionCacheMockGetPermissionParams
	paramPtrs          *PermissionCacheMockGetPermissionParamPtrs
	expectationOrigins PermissionCacheMockGetPermissionExpectationOrigins
	results            *PermissionCacheMockGetPermissionResults
	returnOrigin       string
	Counter            uint64
}

// PermissionCacheMockGetPermissionParams contains parameters of the PermissionCache.GetPermission
type PermissionCacheMockGetPermissionParams struct {
	ctx context.Context
	id  int64
}

// PermissionCacheMockGetPermissionParamPtrs contains pointers to parameters of the PermissionCache.GetPermission
type PermissionCacheMockGetPermissionParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// PermissionCacheMockGetPermissionResults contains results of the PermissionCache.GetPermission
type PermissionCacheMockGetPermissionResults struct {
	p1  permissionentity.Permission
	err error
}

// PermissionCacheMockGetPermissionOrigins contains origins of expectations of the PermissionCache.GetPermission
type PermissionCacheMockGetPermissionExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetPermission *mPermissionCacheMockGetPermission) Optional() *mPermissionCacheMockGetPermission {
	mmGetPermission.optional = true
	return mmGetPermission
}

// Expect sets up expected params for PermissionCache.GetPermission
func (mmGetPermission *mPermissionCacheMockGetPermission) Expect(ctx context.Context, id int64) *mPermissionCacheMockGetPermission {
	if mmGetPermission.mock.funcGetPermission != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Set")
	}

	if mmGetPermission.defaultExpectation == nil {
		mmGetPermission.defaultExpectation = &PermissionCacheMockGetPermissionExpectation{}
	}

	if mmGetPermission.defaultExpectation.paramPtrs != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by ExpectParams functions")
	}

	mmGetPermission.defaultExpectation.params = &PermissionCacheMockGetPermissionParams{ctx, id}
	mmGetPermission.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetPermission.expectations {
		if minimock.Equal(e.params, mmGetPermission.defaultExpectation.params) {
			mmGetPermission.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetPermission.defaultExpectation.params)
		}
	}

	return mmGetPermission
}

// ExpectCtxParam1 sets up expected param ctx for PermissionCache.GetPermission
func (mmGetPermission *mPermissionCacheMockGetPermission) ExpectCtxParam1(ctx context.Context) *mPermissionCacheMockGetPermission {
	if mmGetPermission.mock.funcGetPermission != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Set")
	}

	if mmGetPermission.defaultExpectation == nil {
		mmGetPermission.defaultExpectation = &PermissionCacheMockGetPermissionExpectation{}
	}

	if mmGetPermission.defaultExpectation.params != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Expect")
	}

	if mmGetPermission.defaultExpectation.paramPtrs == nil {
		mmGetPermission.defaultExpectation.paramPtrs = &PermissionCacheMockGetPermissionParamPtrs{}
	}
	mmGetPermission.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetPermission.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetPermission
}

// ExpectIdParam2 sets up expected param id for PermissionCache.GetPermission
func (mmGetPermission *mPermissionCacheMockGetPermission) ExpectIdParam2(id int64) *mPermissionCacheMockGetPermission {
	if mmGetPermission.mock.funcGetPermission != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Set")
	}

	if mmGetPermission.defaultExpectation == nil {
		mmGetPermission.defaultExpectation = &PermissionCacheMockGetPermissionExpectation{}
	}

	if mmGetPermission.defaultExpectation.params != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Expect")
	}

	if mmGetPermission.defaultExpectation.paramPtrs == nil {
		mmGetPermission.defaultExpectation.paramPtrs = &PermissionCacheMockGetPermissionParamPtrs{}
	}
	mmGetPermission.defaultExpectation.paramPtrs.id = &id
	mmGetPermission.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetPermission
}

// Inspect accepts an inspector function that has same arguments as the PermissionCache.GetPermission
func (mmGetPermission *mPermissionCacheMockGetPermission) Inspect(f func(ctx context.Context, id int64)) *mPermissionCacheMockGetPermission {
	if mmGetPermission.mock.inspectFuncGetPermission != nil {
		mmGetPermission.mock.t.Fatalf("Inspect function is already set for PermissionCacheMock.GetPermission")
	}

	mmGetPermission.mock.inspectFuncGetPermission = f

	return mmGetPermission
}

// Return sets up results that will be returned by PermissionCache.GetPermission
func (mmGetPermission *mPermissionCacheMockGetPermission) Return(p1 permissionentity.Permission, err error) *PermissionCacheMock {
	if mmGetPermission.mock.funcGetPermission != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Set")
	}

	if mmGetPermission.defaultExpectation == nil {
		mmGetPermission.defaultExpectation = &PermissionCacheMockGetPermissionExpectation{mock: mmGetPermission.mock}
	}
	mmGetPermission.defaultExpectation.results = &PermissionCacheMockGetPermissionResults{p1, err}
	mmGetPermission.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetPermission.mock
}

// Set uses given function f to mock the PermissionCache.GetPermission method
func (mmGetPermission *mPermissionCacheMockGetPermission) Set(f func(ctx context.Context, id int64) (p1 permissionentity.Permission, err error)) *PermissionCacheMock {
	if mmGetPermission.defaultExpectation != nil {
		mmGetPermission.mock.t.Fatalf("Default expectation is already set for the PermissionCache.GetPermission method")
	}

	if len(mmGetPermission.expectations) > 0 {
		mmGetPermission.mock.t.Fatalf("Some expectations are already set for the PermissionCache.GetPermission method")
	}

	mmGetPermission.mock.funcGetPermission = f
	mmGetPermission.mock.funcGetPermissionOrigin = minimock.CallerInfo(1)
	return mmGetPermission.mock
}

// When sets expectation for the PermissionCache.GetPermission which will trigger the result defined by the following
// Then helper
func (mmGetPermission *mPermissionCacheMockGetPermission) When(ctx context.Context, id int64) *PermissionCacheMockGetPermissionExpectation {
	if mmGetPermission.mock.funcGetPermission != nil {
		mmGetPermission.mock.t.Fatalf("PermissionCacheMock.GetPermission mock is already set by Set")
	}

	expectation := &PermissionCacheMockGetPermissionExpectation{
		mock:               mmGetPermission.mock,
		params:             &PermissionCacheMockGetPermissionParams{ctx, id},
		expectationOrigins: PermissionCacheMockGetPermissionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetPermission.expectations = append(mmGetPermission.expectations, expectation)
	return expectation
}

// Then sets up PermissionCache.GetPermission return parameters for the expectation previously defined by the When method
func (e *PermissionCacheMockGetPermissionExpectation) Then(p1 permissionentity.Permission, err error) *PermissionCacheMock {
	e.results = &PermissionCacheMockGetPermissionResults{p1, err}
	return e.mock
}

// Times sets number of times PermissionCache.GetPermission should be invoked
func (mmGetPermission *mPermissionCacheMockGetPermission) Times(n uint64) *mPermissionCacheMockGetPermission {
	if n == 0 {
		mmGetPermission.mock.t.Fatalf("Times of PermissionCacheMock.GetPermission mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetPermission.expectedInvocations, n)
	mmGetPermission.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetPermission
}

func (mmGetPermission *mPermissionCacheMockGetPermission) invocationsDone() bool {
	if len(mmGetPermission.expectations) == 0 && mmGetPermission.defaultExpectation == nil && mmGetPermission.mock.funcGetPermission == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetPermission.mock.afterGetPermissionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetPermission.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetPermission implements mm_repository.PermissionCache
func (mmGetPermission *PermissionCacheMock) GetPermission(ctx context.Context, id int64) (p1 permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetPermission.beforeGetPermissionCounter, 1)
	defer mm_atomic.AddUint64(&mmGetPermission.afterGetPermissionCounter, 1)

	mmGetPermission.t.Helper()

	if mmGetPermission.inspectFuncGetPermission != nil {
		mmGetPermission.inspectFuncGetPermission(ctx, id)
	}

	mm_params := PermissionCacheMockGetPermissionParams{ctx, id}

	// Record call args
	mmGetPermission.GetPermissionMock.mutex.Lock()
	mmGetPermission.GetPermissionMock.callArgs = append(mmGetPermission.GetPermissionMock.callArgs, &mm_params)
	mmGetPermission.GetPermissionMock.mutex.Unlock()

	for _, e := range mmGetPermission.GetPermissionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.p1, e.results.err
		}
	}

	if mmGetPermission.GetPermissionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetPermission.GetPermissionMock.defaultExpectation.Counter, 1)
		mm_want := mmGetPermission.GetPermissionMock.defaultExpectation.params
		mm_want_ptrs := mmGetPermission.GetPermissionMock.defaultExpectation.paramPtrs

		mm_got := PermissionCacheMockGetPermissionParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetPermission.t.Errorf("PermissionCacheMock.GetPermission got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetPermission.GetPermissionMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetPermission.t.Errorf("PermissionCacheMock.GetPermission got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetPermission.GetPermissionMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetPermission.t.Errorf("PermissionCacheMock.GetPermission got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetPermission.GetPermissionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetPermission.GetPermissionMock.defaultExpectation.results
		if mm_results == nil {
			mmGetPermission.t.Fatal("No results are set for the PermissionCacheMock.GetPermission")
		}
		return (*mm_results).p1, (*mm_results).err
	}
	if mmGetPermission.funcGetPermission != nil {
		return mmGetPermission.funcGetPermission(ctx, id)
	}
	mmGetPermission.t.Fatalf("Unexpected call to PermissionCacheMock.GetPermission. %v %v", ctx, id)
	return
}

// GetPermissionAfterCounter returns a count of finished PermissionCacheMock.GetPermission invocations
func (mmGetPermission *PermissionCacheMock) GetPermissionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetPermission.afterGetPermissionCounter)
}

// GetPermissionBeforeCounter returns a count of PermissionCacheMock.GetPermission invocations
func (mmGetPermission *PermissionCacheMock) GetPermissionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetPermission.beforeGetPermissionCounter)
}

// Calls returns a list of arguments used in each call to PermissionCacheMock.GetPermission.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetPermission *mPermissionCacheMockGetPermission) Calls() []*PermissionCacheMockGetPermissionParams {
	mmGetPermission.mutex.RLock()

	argCopy := make([]*PermissionCacheMockGetPermissionParams, len(mmGetPermission.callArgs))
	copy(argCopy, mmGetPermission.callArgs)

	mmGetPermission.mutex.RUnlock()

	return argCopy
}

// MinimockGetPermissionDone returns true if the count of the GetPermission invocations corresponds
// the number of defined expectations
func (m *PermissionCacheMock) MinimockGetPermissionDone() bool {
	if m.GetPermissionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetPermissionMock.invocationsDone()
}

// MinimockGetPermissionInspect logs each unmet expectation
func (m *PermissionCacheMock) MinimockGetPermissionInspect() {
	for _, e := range m.GetPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionCacheMock.GetPermission at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetPermissionCounter := mm_atomic.LoadUint64(&m.afterGetPermissionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetPermissionMock.defaultExpectation != nil && afterGetPermissionCounter < 1 {
		if m.GetPermissionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionCacheMock.GetPermission at\n%s", m.GetPermissionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionCacheMock.GetPermission at\n%s with params: %#v", m.GetPermissionMock.defaultExpectation.expectationOrigins.origin, *m.GetPermissionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetPermission != nil && afterGetPermissionCounter < 1 {
		m.t.Errorf("Expected call to PermissionCacheMock.GetPermission at\n%s", m.funcGetPermissionOrigin)
	}

	if !m.GetPermissionMock.invocationsDone() && afterGetPermissionCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionCacheMock.GetPermission at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetPermissionMock.expectedInvocations), m.GetPermissionMock.expectedInvocationsOrigin, afterGetPermissionCounter)
	}
}

type mPermissionCacheMockGetPermissions struct {
	optional           bool
	mock               *PermissionCacheMock
	defaultExpectation *PermissionCacheMockGetPermissionsExpectation
	expectations       []*PermissionCacheMockGetPermissionsExpectation

	callArgs []*PermissionCacheMockGetPermissionsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionCacheMockGetPermissionsExpectation specifies expectation struct of the PermissionCache.GetPermissions
type PermissionCacheMockGetPermissionsExpectation struct {
	mock               *PermissionCacheMock
	params             *PermissionCacheMockGetPermissionsParams
	paramPtrs          *PermissionCacheMockGetPermissionsParamPtrs
	expectationOrigins PermissionCacheMockGetPermissionsExpectationOrigins
	results            *PermissionCacheMockGetPermissionsResults
	returnOrigin       string
	Counter            uint64
}

// PermissionCacheMockGetPermissionsParams contains parameters of the PermissionCache.GetPermissions
type PermissionCacheMockGetPermissionsParams struct {
	ctx context.Context
}

// PermissionCacheMockGetPermissionsParamPtrs contains pointers to parameters of the PermissionCache.GetPermissions
type PermissionCacheMockGetPermissionsParamPtrs struct {
	ctx *context.Context
}

// PermissionCacheMockGetPermissionsResults contains results of the PermissionCache.GetPermissions
type PermissionCacheMockGetPermissionsResults struct {
	pa1 []permissionentity.Permission
	err error
}

// PermissionCacheMockGetPermissionsOrigins contains origins of expectations of the PermissionCache.GetPermissions
type PermissionCacheMockGetPermissionsExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Optional() *mPermissionCacheMockGetPermissions {
	mmGetPermissions.optional = true
	return mmGetPermissions
}

// Expect sets up expected params for PermissionCache.GetPermissions
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Expect(ctx context.Context) *mPermissionCacheMockGetPermissions {
	if mmGetPermissions.mock.funcGetPermissions != nil {
		mmGetPermissions.mock.t.Fatalf("PermissionCacheMock.GetPermissions mock is already set by Set")
	}

	if mmGetPermissions.defaultExpectation == nil {
		mmGetPermissions.defaultExpectation = &PermissionCacheMockGetPermissionsExpectation{}
	}

	if mmGetPermissions.defaultExpectation.paramPtrs != nil {
		mmGetPermissions.mock.t.Fatalf("PermissionCacheMock.GetPermissions mock is already set by ExpectParams functions")
	}

	mmGetPermissions.defaultExpectation.params = &PermissionCacheMockGetPermissionsParams{ctx}
	mmGetPermissions.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetPermissions.expectations {
		if minimock.Equal(e.params, mmGetPermissions.defaultExpectation.params) {
			mmGetPermissions.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetPermissions.defaultExpectation.params)
		}
	}

	return mmGetPermissions
}

// ExpectCtxParam1 sets up expected param ctx for PermissionCache.GetPermissions
func (mmGetPermissions *mPermissionCacheMockGetPermissions) ExpectCtxParam1(ctx context.Context) *mPermissionCacheMockGetPermissions {
	if mmGetPermissions.mock.funcGetPermissions != nil {
		mmGetPermissions.mock.t.Fatalf("PermissionCacheMock.GetPermissions mock is already set by Set")
	}

	if mmGetPermissions.defaultExpectation == nil {
		mmGetPermissions.defaultExpectation = &PermissionCacheMockGetPermissionsExpectation{}
	}

	if mmGetPermissions.defaultExpectation.params != nil {
		mmGetPermissions.mock.t.Fatalf("PermissionCacheMock.GetPermissions mock is already set by Expect")
	}

	if mmGetPermissions.defaultExpectation.paramPtrs == nil {
		mmGetPermissions.defaultExpectation.paramPtrs = &PermissionCacheMockGetPermissionsParamPtrs{}
	}
	mmGetPermissions.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetPermissions.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetPermissions
}

// Inspect accepts an inspector function that has same arguments as the PermissionCache.GetPermissions
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Inspect(f func(ctx context.Context)) *mPermissionCacheMockGetPermissions {
	if mmGetPermissions.mock.inspectFuncGetPermissions != nil {
		mmGetPermissions.mock.t.Fatalf("Inspect function is already set for PermissionCacheMock.GetPermissions")
	}

	mmGetPermissions.mock.inspectFuncGetPermissions = f

	return mmGetPermissions
}

// Return sets up results that will be returned by PermissionCache.GetPermissions
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Return(pa1 []permissionentity.Permission, err error) *PermissionCacheMock {
	if mmGetPermissions.mock.funcGetPermissions != nil {
		mmGetPermissions.mock.t.Fatalf("PermissionCacheMock.GetPermissions mock is already set by Set")
	}

	if mmGetPermissions.defaultExpectation == nil {
		mmGetPermissions.defaultExpectation = &PermissionCacheMockGetPermissionsExpectation{mock: mmGetPermissions.mock}
	}
	mmGetPermissions.defaultExpectation.results = &PermissionCacheMockGetPermissionsResults{pa1, err}
	mmGetPermissions.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetPermissions.mock
}

// Set uses given function f to mock the PermissionCache.GetPermissions method
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Set(f func(ctx context.Context) (pa1 []permissionentity.Permission, err error)) *PermissionCacheMock {
	if mmGetPermissions.defaultExpectation != nil {
		mmGetPermissions.mock.t.Fatalf("Default expectation is already set for the PermissionCache.GetPermissions method")
	}

	if len(mmGetPermissions.expectations) > 0 {
		mmGetPermissions.mock.t.Fatalf("Some expectations are already set for the PermissionCache.GetPermissions method")
	}

	mmGetPermissions.mock.funcGetPermissions = f
	mmGetPermissions.mock.funcGetPermissionsOrigin = minimock.CallerInfo(1)
	return mmGetPermissions.mock
}

// When sets expectation for the PermissionCache.GetPermissions which will trigger the result defined by the following
// Then helper
func (mmGetPermissions *mPermissionCacheMockGetPermissions) When(ctx context.Context) *PermissionCacheMockGetPermissionsExpectation {
	if mmGetPermissions.mock.funcGetPermissions != nil {
		mmGetPermissions.mock.t.Fatalf("PermissionCacheMock.GetPermissions mock is already set by Set")
	}

	expectation := &PermissionCacheMockGetPermissionsExpectation{
		mock:               mmGetPermissions.mock,
		params:             &PermissionCacheMockGetPermissionsParams{ctx},
		expectationOrigins: PermissionCacheMockGetPermissionsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetPermissions.expectations = append(mmGetPermissions.expectations, expectation)
	return expectation
}

// Then sets up PermissionCache.GetPermissions return parameters for the expectation previously defined by the When method
func (e *PermissionCacheMockGetPermissionsExpectation) Then(pa1 []permissionentity.Permission, err error) *PermissionCacheMock {
	e.results = &PermissionCacheMockGetPermissionsResults{pa1, err}
	return e.mock
}

// Times sets number of times PermissionCache.GetPermissions should be invoked
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Times(n uint64) *mPermissionCacheMockGetPermissions {
	if n == 0 {
		mmGetPermissions.mock.t.Fatalf("Times of PermissionCacheMock.GetPermissions mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetPermissions.expectedInvocations, n)
	mmGetPermissions.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetPermissions
}

func (mmGetPermissions *mPermissionCacheMockGetPermissions) invocationsDone() bool {
	if len(mmGetPermissions.expectations) == 0 && mmGetPermissions.defaultExpectation == nil && mmGetPermissions.mock.funcGetPermissions == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetPermissions.mock.afterGetPermissionsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetPermissions.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetPermissions implements mm_repository.PermissionCache
func (mmGetPermissions *PermissionCacheMock) GetPermissions(ctx context.Context) (pa1 []permissionentity.Permission, err error) {
	mm_atomic.AddUint64(&mmGetPermissions.beforeGetPermissionsCounter, 1)
	defer mm_atomic.AddUint64(&mmGetPermissions.afterGetPermissionsCounter, 1)

	mmGetPermissions.t.Helper()

	if mmGetPermissions.inspectFuncGetPermissions != nil {
		mmGetPermissions.inspectFuncGetPermissions(ctx)
	}

	mm_params := PermissionCacheMockGetPermissionsParams{ctx}

	// Record call args
	mmGetPermissions.GetPermissionsMock.mutex.Lock()
	mmGetPermissions.GetPermissionsMock.callArgs = append(mmGetPermissions.GetPermissionsMock.callArgs, &mm_params)
	mmGetPermissions.GetPermissionsMock.mutex.Unlock()

	for _, e := range mmGetPermissions.GetPermissionsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.pa1, e.results.err
		}
	}

	if mmGetPermissions.GetPermissionsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetPermissions.GetPermissionsMock.defaultExpectation.Counter, 1)
		mm_want := mmGetPermissions.GetPermissionsMock.defaultExpectation.params
		mm_want_ptrs := mmGetPermissions.GetPermissionsMock.defaultExpectation.paramPtrs

		mm_got := PermissionCacheMockGetPermissionsParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetPermissions.t.Errorf("PermissionCacheMock.GetPermissions got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetPermissions.GetPermissionsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetPermissions.t.Errorf("PermissionCacheMock.GetPermissions got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetPermissions.GetPermissionsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetPermissions.GetPermissionsMock.defaultExpectation.results
		if mm_results == nil {
			mmGetPermissions.t.Fatal("No results are set for the PermissionCacheMock.GetPermissions")
		}
		return (*mm_results).pa1, (*mm_results).err
	}
	if mmGetPermissions.funcGetPermissions != nil {
		return mmGetPermissions.funcGetPermissions(ctx)
	}
	mmGetPermissions.t.Fatalf("Unexpected call to PermissionCacheMock.GetPermissions. %v", ctx)
	return
}

// GetPermissionsAfterCounter returns a count of finished PermissionCacheMock.GetPermissions invocations
func (mmGetPermissions *PermissionCacheMock) GetPermissionsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetPermissions.afterGetPermissionsCounter)
}

// GetPermissionsBeforeCounter returns a count of PermissionCacheMock.GetPermissions invocations
func (mmGetPermissions *PermissionCacheMock) GetPermissionsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetPermissions.beforeGetPermissionsCounter)
}

// Calls returns a list of arguments used in each call to PermissionCacheMock.GetPermissions.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetPermissions *mPermissionCacheMockGetPermissions) Calls() []*PermissionCacheMockGetPermissionsParams {
	mmGetPermissions.mutex.RLock()

	argCopy := make([]*PermissionCacheMockGetPermissionsParams, len(mmGetPermissions.callArgs))
	copy(argCopy, mmGetPermissions.callArgs)

	mmGetPermissions.mutex.RUnlock()

	return argCopy
}

// MinimockGetPermissionsDone returns true if the count of the GetPermissions invocations corresponds
// the number of defined expectations
func (m *PermissionCacheMock) MinimockGetPermissionsDone() bool {
	if m.GetPermissionsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetPermissionsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetPermissionsMock.invocationsDone()
}

// MinimockGetPermissionsInspect logs each unmet expectation
func (m *PermissionCacheMock) MinimockGetPermissionsInspect() {
	for _, e := range m.GetPermissionsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionCacheMock.GetPermissions at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetPermissionsCounter := mm_atomic.LoadUint64(&m.afterGetPermissionsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetPermissionsMock.defaultExpectation != nil && afterGetPermissionsCounter < 1 {
		if m.GetPermissionsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionCacheMock.GetPermissions at\n%s", m.GetPermissionsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionCacheMock.GetPermissions at\n%s with params: %#v", m.GetPermissionsMock.defaultExpectation.expectationOrigins.origin, *m.GetPermissionsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetPermissions != nil && afterGetPermissionsCounter < 1 {
		m.t.Errorf("Expected call to PermissionCacheMock.GetPermissions at\n%s", m.funcGetPermissionsOrigin)
	}

	if !m.GetPermissionsMock.invocationsDone() && afterGetPermissionsCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionCacheMock.GetPermissions at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetPermissionsMock.expectedInvocations), m.GetPermissionsMock.expectedInvocationsOrigin, afterGetPermissionsCounter)
	}
}

type mPermissionCacheMockSetPermission struct {
	optional           bool
	mock               *PermissionCacheMock
	defaultExpectation *PermissionCacheMockSetPermissionExpectation
	expectations       []*PermissionCacheMockSetPermissionExpectation

	callArgs []*PermissionCacheMockSetPermissionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionCacheMockSetPermissionExpectation specifies expectation struct of the PermissionCache.SetPermission
type PermissionCacheMockSetPermissionExpectation struct {
	mock               *PermissionCacheMock
	params             *PermissionCacheMockSetPermissionParams
	paramPtrs          *PermissionCacheMockSetPermissionParamPtrs
	expectationOrigins PermissionCacheMockSetPermissionExpectationOrigins
	results            *PermissionCacheMockSetPermissionResults
	returnOrigin       string
	Counter            uint64
}

// PermissionCacheMockSetPermissionParams contains parameters of the PermissionCache.SetPermission
type PermissionCacheMockSetPermissionParams struct {
	ctx        context.Context
	permission permissionentity.Permission
}

// PermissionCacheMockSetPermissionParamPtrs contains pointers to parameters of the PermissionCache.SetPermission
type PermissionCacheMockSetPermissionParamPtrs struct {
	ctx        *context.Context
	permission *permissionentity.Permission
}

// PermissionCacheMockSetPermissionResults contains results of the PermissionCache.SetPermission
type PermissionCacheMockSetPermissionResults struct {
	err error
}

// PermissionCacheMockSetPermissionOrigins contains origins of expectations of the PermissionCache.SetPermission
type PermissionCacheMockSetPermissionExpectationOrigins struct {
	origin           string
	originCtx        string
	originPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetPermission *mPermissionCacheMockSetPermission) Optional() *mPermissionCacheMockSetPermission {
	mmSetPermission.optional = true
	return mmSetPermission
}

// Expect sets up expected params for PermissionCache.SetPermission
func (mmSetPermission *mPermissionCacheMockSetPermission) Expect(ctx context.Context, permission permissionentity.Permission) *mPermissionCacheMockSetPermission {
	if mmSetPermission.mock.funcSetPermission != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Set")
	}

	if mmSetPermission.defaultExpectation == nil {
		mmSetPermission.defaultExpectation = &PermissionCacheMockSetPermissionExpectation{}
	}

	if mmSetPermission.defaultExpectation.paramPtrs != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by ExpectParams functions")
	}

	mmSetPermission.defaultExpectation.params = &PermissionCacheMockSetPermissionParams{ctx, permission}
	mmSetPermission.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetPermission.expectations {
		if minimock.Equal(e.params, mmSetPermission.defaultExpectation.params) {
			mmSetPermission.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetPermission.defaultExpectation.params)
		}
	}

	return mmSetPermission
}

// ExpectCtxParam1 sets up expected param ctx for PermissionCache.SetPermission
func (mmSetPermission *mPermissionCacheMockSetPermission) ExpectCtxParam1(ctx context.Context) *mPermissionCacheMockSetPermission {
	if mmSetPermission.mock.funcSetPermission != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Set")
	}

	if mmSetPermission.defaultExpectation == nil {
		mmSetPermission.defaultExpectation = &PermissionCacheMockSetPermissionExpectation{}
	}

	if mmSetPermission.defaultExpectation.params != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Expect")
	}

	if mmSetPermission.defaultExpectation.paramPtrs == nil {
		mmSetPermission.defaultExpectation.paramPtrs = &PermissionCacheMockSetPermissionParamPtrs{}
	}
	mmSetPermission.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetPermission.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetPermission
}

// ExpectPermissionParam2 sets up expected param permission for PermissionCache.SetPermission
func (mmSetPermission *mPermissionCacheMockSetPermission) ExpectPermissionParam2(permission permissionentity.Permission) *mPermissionCacheMockSetPermission {
	if mmSetPermission.mock.funcSetPermission != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Set")
	}

	if mmSetPermission.defaultExpectation == nil {
		mmSetPermission.defaultExpectation = &PermissionCacheMockSetPermissionExpectation{}
	}

	if mmSetPermission.defaultExpectation.params != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Expect")
	}

	if mmSetPermission.defaultExpectation.paramPtrs == nil {
		mmSetPermission.defaultExpectation.paramPtrs = &PermissionCacheMockSetPermissionParamPtrs{}
	}
	mmSetPermission.defaultExpectation.paramPtrs.permission = &permission
	mmSetPermission.defaultExpectation.expectationOrigins.originPermission = minimock.CallerInfo(1)

	return mmSetPermission
}

// Inspect accepts an inspector function that has same arguments as the PermissionCache.SetPermission
func (mmSetPermission *mPermissionCacheMockSetPermission) Inspect(f func(ctx context.Context, permission permissionentity.Permission)) *mPermissionCacheMockSetPermission {
	if mmSetPermission.mock.inspectFuncSetPermission != nil {
		mmSetPermission.mock.t.Fatalf("Inspect function is already set for PermissionCacheMock.SetPermission")
	}

	mmSetPermission.mock.inspectFuncSetPermission = f

	return mmSetPermission
}

// Return sets up results that will be returned by PermissionCache.SetPermission
func (mmSetPermission *mPermissionCacheMockSetPermission) Return(err error) *PermissionCacheMock {
	if mmSetPermission.mock.funcSetPermission != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Set")
	}

	if mmSetPermission.defaultExpectation == nil {
		mmSetPermission.defaultExpectation = &PermissionCacheMockSetPermissionExpectation{mock: mmSetPermission.mock}
	}
	mmSetPermission.defaultExpectation.results = &PermissionCacheMockSetPermissionResults{err}
	mmSetPermission.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetPermission.mock
}

// Set uses given function f to mock the PermissionCache.SetPermission method
func (mmSetPermission *mPermissionCacheMockSetPermission) Set(f func(ctx context.Context, permission permissionentity.Permission) (err error)) *PermissionCacheMock {
	if mmSetPermission.defaultExpectation != nil {
		mmSetPermission.mock.t.Fatalf("Default expectation is already set for the PermissionCache.SetPermission method")
	}

	if len(mmSetPermission.expectations) > 0 {
		mmSetPermission.mock.t.Fatalf("Some expectations are already set for the PermissionCache.SetPermission method")
	}

	mmSetPermission.mock.funcSetPermission = f
	mmSetPermission.mock.funcSetPermissionOrigin = minimock.CallerInfo(1)
	return mmSetPermission.mock
}

// When sets expectation for the PermissionCache.SetPermission which will trigger the result defined by the following
// Then helper
func (mmSetPermission *mPermissionCacheMockSetPermission) When(ctx context.Context, permission permissionentity.Permission) *PermissionCacheMockSetPermissionExpectation {
	if mmSetPermission.mock.funcSetPermission != nil {
		mmSetPermission.mock.t.Fatalf("PermissionCacheMock.SetPermission mock is already set by Set")
	}

	expectation := &PermissionCacheMockSetPermissionExpectation{
		mock:               mmSetPermission.mock,
		params:             &PermissionCacheMockSetPermissionParams{ctx, permission},
		expectationOrigins: PermissionCacheMockSetPermissionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetPermission.expectations = append(mmSetPermission.expectations, expectation)
	return expectation
}

// Then sets up PermissionCache.SetPermission return parameters for the expectation previously defined by the When method
func (e *PermissionCacheMockSetPermissionExpectation) Then(err error) *PermissionCacheMock {
	e.results = &PermissionCacheMockSetPermissionResults{err}
	return e.mock
}

// Times sets number of times PermissionCache.SetPermission should be invoked
func (mmSetPermission *mPermissionCacheMockSetPermission) Times(n uint64) *mPermissionCacheMockSetPermission {
	if n == 0 {
		mmSetPermission.mock.t.Fatalf("Times of PermissionCacheMock.SetPermission mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetPermission.expectedInvocations, n)
	mmSetPermission.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetPermission
}

func (mmSetPermission *mPermissionCacheMockSetPermission) invocationsDone() bool {
	if len(mmSetPermission.expectations) == 0 && mmSetPermission.defaultExpectation == nil && mmSetPermission.mock.funcSetPermission == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetPermission.mock.afterSetPermissionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetPermission.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetPermission implements mm_repository.PermissionCache
func (mmSetPermission *PermissionCacheMock) SetPermission(ctx context.Context, permission permissionentity.Permission) (err error) {
	mm_atomic.AddUint64(&mmSetPermission.beforeSetPermissionCounter, 1)
	defer mm_atomic.AddUint64(&mmSetPermission.afterSetPermissionCounter, 1)

	mmSetPermission.t.Helper()

	if mmSetPermission.inspectFuncSetPermission != nil {
		mmSetPermission.inspectFuncSetPermission(ctx, permission)
	}

	mm_params := PermissionCacheMockSetPermissionParams{ctx, permission}

	// Record call args
	mmSetPermission.SetPermissionMock.mutex.Lock()
	mmSetPermission.SetPermissionMock.callArgs = append(mmSetPermission.SetPermissionMock.callArgs, &mm_params)
	mmSetPermission.SetPermissionMock.mutex.Unlock()

	for _, e := range mmSetPermission.SetPermissionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetPermission.SetPermissionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetPermission.SetPermissionMock.defaultExpectation.Counter, 1)
		mm_want := mmSetPermission.SetPermissionMock.defaultExpectation.params
		mm_want_ptrs := mmSetPermission.SetPermissionMock.defaultExpectation.paramPtrs

		mm_got := PermissionCacheMockSetPermissionParams{ctx, permission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetPermission.t.Errorf("PermissionCacheMock.SetPermission got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetPermission.SetPermissionMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.permission != nil && !minimock.Equal(*mm_want_ptrs.permission, mm_got.permission) {
				mmSetPermission.t.Errorf("PermissionCacheMock.SetPermission got unexpected parameter permission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetPermission.SetPermissionMock.defaultExpectation.expectationOrigins.originPermission, *mm_want_ptrs.permission, mm_got.permission, minimock.Diff(*mm_want_ptrs.permission, mm_got.permission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetPermission.t.Errorf("PermissionCacheMock.SetPermission got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetPermission.SetPermissionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetPermission.SetPermissionMock.defaultExpectation.results
		if mm_results == nil {
			mmSetPermission.t.Fatal("No results are set for the PermissionCacheMock.SetPermission")
		}
		return (*mm_results).err
	}
	if mmSetPermission.funcSetPermission != nil {
		return mmSetPermission.funcSetPermission(ctx, permission)
	}
	mmSetPermission.t.Fatalf("Unexpected call to PermissionCacheMock.SetPermission. %v %v", ctx, permission)
	return
}

// SetPermissionAfterCounter returns a count of finished PermissionCacheMock.SetPermission invocations
func (mmSetPermission *PermissionCacheMock) SetPermissionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetPermission.afterSetPermissionCounter)
}

// SetPermissionBeforeCounter returns a count of PermissionCacheMock.SetPermission invocations
func (mmSetPermission *PermissionCacheMock) SetPermissionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetPermission.beforeSetPermissionCounter)
}

// Calls returns a list of arguments used in each call to PermissionCacheMock.SetPermission.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetPermission *mPermissionCacheMockSetPermission) Calls() []*PermissionCacheMockSetPermissionParams {
	mmSetPermission.mutex.RLock()

	argCopy := make([]*PermissionCacheMockSetPermissionParams, len(mmSetPermission.callArgs))
	copy(argCopy, mmSetPermission.callArgs)

	mmSetPermission.mutex.RUnlock()

	return argCopy
}

// MinimockSetPermissionDone returns true if the count of the SetPermission invocations corresponds
// the number of defined expectations
func (m *PermissionCacheMock) MinimockSetPermissionDone() bool {
	if m.SetPermissionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetPermissionMock.invocationsDone()
}

// MinimockSetPermissionInspect logs each unmet expectation
func (m *PermissionCacheMock) MinimockSetPermissionInspect() {
	for _, e := range m.SetPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionCacheMock.SetPermission at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetPermissionCounter := mm_atomic.LoadUint64(&m.afterSetPermissionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetPermissionMock.defaultExpectation != nil && afterSetPermissionCounter < 1 {
		if m.SetPermissionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionCacheMock.SetPermission at\n%s", m.SetPermissionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionCacheMock.SetPermission at\n%s with params: %#v", m.SetPermissionMock.defaultExpectation.expectationOrigins.origin, *m.SetPermissionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetPermission != nil && afterSetPermissionCounter < 1 {
		m.t.Errorf("Expected call to PermissionCacheMock.SetPermission at\n%s", m.funcSetPermissionOrigin)
	}

	if !m.SetPermissionMock.invocationsDone() && afterSetPermissionCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionCacheMock.SetPermission at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetPermissionMock.expectedInvocations), m.SetPermissionMock.expectedInvocationsOrigin, afterSetPermissionCounter)
	}
}

type mPermissionCacheMockSetPermissions struct {
	optional           bool
	mock               *PermissionCacheMock
	defaultExpectation *PermissionCacheMockSetPermissionsExpectation
	expectations       []*PermissionCacheMockSetPermissionsExpectation

	callArgs []*PermissionCacheMockSetPermissionsParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// PermissionCacheMockSetPermissionsExpectation specifies expectation struct of the PermissionCache.SetPermissions
type PermissionCacheMockSetPermissionsExpectation struct {
	mock               *PermissionCacheMock
	params             *PermissionCacheMockSetPermissionsParams
	paramPtrs          *PermissionCacheMockSetPermissionsParamPtrs
	expectationOrigins PermissionCacheMockSetPermissionsExpectationOrigins
	results            *PermissionCacheMockSetPermissionsResults
	returnOrigin       string
	Counter            uint64
}

// PermissionCacheMockSetPermissionsParams contains parameters of the PermissionCache.SetPermissions
type PermissionCacheMockSetPermissionsParams struct {
	ctx         context.Context
	permissions []permissionentity.Permission
}

// PermissionCacheMockSetPermissionsParamPtrs contains pointers to parameters of the PermissionCache.SetPermissions
type PermissionCacheMockSetPermissionsParamPtrs struct {
	ctx         *context.Context
	permissions *[]permissionentity.Permission
}

// PermissionCacheMockSetPermissionsResults contains results of the PermissionCache.SetPermissions
type PermissionCacheMockSetPermissionsResults struct {
	err error
}

// PermissionCacheMockSetPermissionsOrigins contains origins of expectations of the PermissionCache.SetPermissions
type PermissionCacheMockSetPermissionsExpectationOrigins struct {
	origin            string
	originCtx         string
	originPermissions string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Optional() *mPermissionCacheMockSetPermissions {
	mmSetPermissions.optional = true
	return mmSetPermissions
}

// Expect sets up expected params for PermissionCache.SetPermissions
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Expect(ctx context.Context, permissions []permissionentity.Permission) *mPermissionCacheMockSetPermissions {
	if mmSetPermissions.mock.funcSetPermissions != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Set")
	}

	if mmSetPermissions.defaultExpectation == nil {
		mmSetPermissions.defaultExpectation = &PermissionCacheMockSetPermissionsExpectation{}
	}

	if mmSetPermissions.defaultExpectation.paramPtrs != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by ExpectParams functions")
	}

	mmSetPermissions.defaultExpectation.params = &PermissionCacheMockSetPermissionsParams{ctx, permissions}
	mmSetPermissions.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetPermissions.expectations {
		if minimock.Equal(e.params, mmSetPermissions.defaultExpectation.params) {
			mmSetPermissions.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetPermissions.defaultExpectation.params)
		}
	}

	return mmSetPermissions
}

// ExpectCtxParam1 sets up expected param ctx for PermissionCache.SetPermissions
func (mmSetPermissions *mPermissionCacheMockSetPermissions) ExpectCtxParam1(ctx context.Context) *mPermissionCacheMockSetPermissions {
	if mmSetPermissions.mock.funcSetPermissions != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Set")
	}

	if mmSetPermissions.defaultExpectation == nil {
		mmSetPermissions.defaultExpectation = &PermissionCacheMockSetPermissionsExpectation{}
	}

	if mmSetPermissions.defaultExpectation.params != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Expect")
	}

	if mmSetPermissions.defaultExpectation.paramPtrs == nil {
		mmSetPermissions.defaultExpectation.paramPtrs = &PermissionCacheMockSetPermissionsParamPtrs{}
	}
	mmSetPermissions.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetPermissions.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetPermissions
}

// ExpectPermissionsParam2 sets up expected param permissions for PermissionCache.SetPermissions
func (mmSetPermissions *mPermissionCacheMockSetPermissions) ExpectPermissionsParam2(permissions []permissionentity.Permission) *mPermissionCacheMockSetPermissions {
	if mmSetPermissions.mock.funcSetPermissions != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Set")
	}

	if mmSetPermissions.defaultExpectation == nil {
		mmSetPermissions.defaultExpectation = &PermissionCacheMockSetPermissionsExpectation{}
	}

	if mmSetPermissions.defaultExpectation.params != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Expect")
	}

	if mmSetPermissions.defaultExpectation.paramPtrs == nil {
		mmSetPermissions.defaultExpectation.paramPtrs = &PermissionCacheMockSetPermissionsParamPtrs{}
	}
	mmSetPermissions.defaultExpectation.paramPtrs.permissions = &permissions
	mmSetPermissions.defaultExpectation.expectationOrigins.originPermissions = minimock.CallerInfo(1)

	return mmSetPermissions
}

// Inspect accepts an inspector function that has same arguments as the PermissionCache.SetPermissions
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Inspect(f func(ctx context.Context, permissions []permissionentity.Permission)) *mPermissionCacheMockSetPermissions {
	if mmSetPermissions.mock.inspectFuncSetPermissions != nil {
		mmSetPermissions.mock.t.Fatalf("Inspect function is already set for PermissionCacheMock.SetPermissions")
	}

	mmSetPermissions.mock.inspectFuncSetPermissions = f

	return mmSetPermissions
}

// Return sets up results that will be returned by PermissionCache.SetPermissions
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Return(err error) *PermissionCacheMock {
	if mmSetPermissions.mock.funcSetPermissions != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Set")
	}

	if mmSetPermissions.defaultExpectation == nil {
		mmSetPermissions.defaultExpectation = &PermissionCacheMockSetPermissionsExpectation{mock: mmSetPermissions.mock}
	}
	mmSetPermissions.defaultExpectation.results = &PermissionCacheMockSetPermissionsResults{err}
	mmSetPermissions.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetPermissions.mock
}

// Set uses given function f to mock the PermissionCache.SetPermissions method
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Set(f func(ctx context.Context, permissions []permissionentity.Permission) (err error)) *PermissionCacheMock {
	if mmSetPermissions.defaultExpectation != nil {
		mmSetPermissions.mock.t.Fatalf("Default expectation is already set for the PermissionCache.SetPermissions method")
	}

	if len(mmSetPermissions.expectations) > 0 {
		mmSetPermissions.mock.t.Fatalf("Some expectations are already set for the PermissionCache.SetPermissions method")
	}

	mmSetPermissions.mock.funcSetPermissions = f
	mmSetPermissions.mock.funcSetPermissionsOrigin = minimock.CallerInfo(1)
	return mmSetPermissions.mock
}

// When sets expectation for the PermissionCache.SetPermissions which will trigger the result defined by the following
// Then helper
func (mmSetPermissions *mPermissionCacheMockSetPermissions) When(ctx context.Context, permissions []permissionentity.Permission) *PermissionCacheMockSetPermissionsExpectation {
	if mmSetPermissions.mock.funcSetPermissions != nil {
		mmSetPermissions.mock.t.Fatalf("PermissionCacheMock.SetPermissions mock is already set by Set")
	}

	expectation := &PermissionCacheMockSetPermissionsExpectation{
		mock:               mmSetPermissions.mock,
		params:             &PermissionCacheMockSetPermissionsParams{ctx, permissions},
		expectationOrigins: PermissionCacheMockSetPermissionsExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetPermissions.expectations = append(mmSetPermissions.expectations, expectation)
	return expectation
}

// Then sets up PermissionCache.SetPermissions return parameters for the expectation previously defined by the When method
func (e *PermissionCacheMockSetPermissionsExpectation) Then(err error) *PermissionCacheMock {
	e.results = &PermissionCacheMockSetPermissionsResults{err}
	return e.mock
}

// Times sets number of times PermissionCache.SetPermissions should be invoked
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Times(n uint64) *mPermissionCacheMockSetPermissions {
	if n == 0 {
		mmSetPermissions.mock.t.Fatalf("Times of PermissionCacheMock.SetPermissions mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetPermissions.expectedInvocations, n)
	mmSetPermissions.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetPermissions
}

func (mmSetPermissions *mPermissionCacheMockSetPermissions) invocationsDone() bool {
	if len(mmSetPermissions.expectations) == 0 && mmSetPermissions.defaultExpectation == nil && mmSetPermissions.mock.funcSetPermissions == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetPermissions.mock.afterSetPermissionsCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetPermissions.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetPermissions implements mm_repository.PermissionCache
func (mmSetPermissions *PermissionCacheMock) SetPermissions(ctx context.Context, permissions []permissionentity.Permission) (err error) {
	mm_atomic.AddUint64(&mmSetPermissions.beforeSetPermissionsCounter, 1)
	defer mm_atomic.AddUint64(&mmSetPermissions.afterSetPermissionsCounter, 1)

	mmSetPermissions.t.Helper()

	if mmSetPermissions.inspectFuncSetPermissions != nil {
		mmSetPermissions.inspectFuncSetPermissions(ctx, permissions)
	}

	mm_params := PermissionCacheMockSetPermissionsParams{ctx, permissions}

	// Record call args
	mmSetPermissions.SetPermissionsMock.mutex.Lock()
	mmSetPermissions.SetPermissionsMock.callArgs = append(mmSetPermissions.SetPermissionsMock.callArgs, &mm_params)
	mmSetPermissions.SetPermissionsMock.mutex.Unlock()

	for _, e := range mmSetPermissions.SetPermissionsMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetPermissions.SetPermissionsMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetPermissions.SetPermissionsMock.defaultExpectation.Counter, 1)
		mm_want := mmSetPermissions.SetPermissionsMock.defaultExpectation.params
		mm_want_ptrs := mmSetPermissions.SetPermissionsMock.defaultExpectation.paramPtrs

		mm_got := PermissionCacheMockSetPermissionsParams{ctx, permissions}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetPermissions.t.Errorf("PermissionCacheMock.SetPermissions got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetPermissions.SetPermissionsMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.permissions != nil && !minimock.Equal(*mm_want_ptrs.permissions, mm_got.permissions) {
				mmSetPermissions.t.Errorf("PermissionCacheMock.SetPermissions got unexpected parameter permissions, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetPermissions.SetPermissionsMock.defaultExpectation.expectationOrigins.originPermissions, *mm_want_ptrs.permissions, mm_got.permissions, minimock.Diff(*mm_want_ptrs.permissions, mm_got.permissions))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetPermissions.t.Errorf("PermissionCacheMock.SetPermissions got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetPermissions.SetPermissionsMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetPermissions.SetPermissionsMock.defaultExpectation.results
		if mm_results == nil {
			mmSetPermissions.t.Fatal("No results are set for the PermissionCacheMock.SetPermissions")
		}
		return (*mm_results).err
	}
	if mmSetPermissions.funcSetPermissions != nil {
		return mmSetPermissions.funcSetPermissions(ctx, permissions)
	}
	mmSetPermissions.t.Fatalf("Unexpected call to PermissionCacheMock.SetPermissions. %v %v", ctx, permissions)
	return
}

// SetPermissionsAfterCounter returns a count of finished PermissionCacheMock.SetPermissions invocations
func (mmSetPermissions *PermissionCacheMock) SetPermissionsAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetPermissions.afterSetPermissionsCounter)
}

// SetPermissionsBeforeCounter returns a count of PermissionCacheMock.SetPermissions invocations
func (mmSetPermissions *PermissionCacheMock) SetPermissionsBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetPermissions.beforeSetPermissionsCounter)
}

// Calls returns a list of arguments used in each call to PermissionCacheMock.SetPermissions.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetPermissions *mPermissionCacheMockSetPermissions) Calls() []*PermissionCacheMockSetPermissionsParams {
	mmSetPermissions.mutex.RLock()

	argCopy := make([]*PermissionCacheMockSetPermissionsParams, len(mmSetPermissions.callArgs))
	copy(argCopy, mmSetPermissions.callArgs)

	mmSetPermissions.mutex.RUnlock()

	return argCopy
}

// MinimockSetPermissionsDone returns true if the count of the SetPermissions invocations corresponds
// the number of defined expectations
func (m *PermissionCacheMock) MinimockSetPermissionsDone() bool {
	if m.SetPermissionsMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetPermissionsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetPermissionsMock.invocationsDone()
}

// MinimockSetPermissionsInspect logs each unmet expectation
func (m *PermissionCacheMock) MinimockSetPermissionsInspect() {
	for _, e := range m.SetPermissionsMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to PermissionCacheMock.SetPermissions at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetPermissionsCounter := mm_atomic.LoadUint64(&m.afterSetPermissionsCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetPermissionsMock.defaultExpectation != nil && afterSetPermissionsCounter < 1 {
		if m.SetPermissionsMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to PermissionCacheMock.SetPermissions at\n%s", m.SetPermissionsMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to PermissionCacheMock.SetPermissions at\n%s with params: %#v", m.SetPermissionsMock.defaultExpectation.expectationOrigins.origin, *m.SetPermissionsMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetPermissions != nil && afterSetPermissionsCounter < 1 {
		m.t.Errorf("Expected call to PermissionCacheMock.SetPermissions at\n%s", m.funcSetPermissionsOrigin)
	}

	if !m.SetPermissionsMock.invocationsDone() && afterSetPermissionsCounter > 0 {
		m.t.Errorf("Expected %d calls to PermissionCacheMock.SetPermissions at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetPermissionsMock.expectedInvocations), m.SetPermissionsMock.expectedInvocationsOrigin, afterSetPermissionsCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *PermissionCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetPermissionInspect()

			m.MinimockGetPermissionsInspect()

			m.MinimockSetPermissionInspect()

			m.MinimockSetPermissionsInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *PermissionCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *PermissionCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetPermissionDone() &&
		m.MinimockGetPermissionsDone() &&
		m.MinimockSetPermissionDone() &&
		m.MinimockSetPermissionsDone()
}
