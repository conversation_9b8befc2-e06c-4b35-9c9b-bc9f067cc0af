package repository

import (
	"context"

	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
)

//go:generate minimock -i PermissionPrimeDB -o ../mocks/permission_prime_db_mock.go -s _mock.go
type PermissionPrimeDB interface {
	Create(permission permissionentity.Permission) (permissionentity.Permission, error)
	GetAll() ([]permissionentity.Permission, error)
	GetAllUniqueNames() (map[string]struct{}, error)
	GetByID(id int64) (permissionentity.Permission, error)
	GetByNameAndMethod(name, method string) (permissionentity.Permission, error)
	GetByUserID(userID int64) ([]permissionentity.Permission, error)
	GetIDsByName(name string) ([]int64, error)
	Update(permission permissionentity.Permission) (permissionentity.Permission, error)
}

//go:generate minimock -i PermissionCache -o ../mocks/permission_cache_mock.go -s _mock.go
type PermissionCache interface {
	GetPermission(ctx context.Context, id int64) (permissionentity.Permission, error)
	GetPermissions(ctx context.Context) ([]permissionentity.Permission, error)
	SetPermission(ctx context.Context, permission permissionentity.Permission) error
	SetPermissions(ctx context.Context, permissions []permissionentity.Permission) error
}
