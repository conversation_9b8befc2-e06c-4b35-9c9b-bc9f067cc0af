package entity

import "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"

type Permission struct {
	ID     int64
	Name   string
	Method string
}

func (p *Permission) LabelRu() string {
	return constants.PermissionsRu[p.Name]
}

type PermissionUpdateData struct {
	ID     int64
	Name   *string
	Method *string
}
type PermissionWithCategories struct {
	PermissionName string
	Categories     []CategoryActivity
}

type CategoryActivity struct {
	ID       int64
	Name     string
	IsActive bool
}
