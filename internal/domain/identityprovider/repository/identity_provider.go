package repository

import (
	identityentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
)

//go:generate minimock -i IdentityProvider -o ../mocks/identity_provider_mock.go -s _mock.go
type IdentityProvider interface {
	GetClientID() string
	GetRealm() string
	GetURL() string
	GetUsersByEmail(email string) ([]identityentity.KeycloakUser, error)
	GetUsersBySearch(search string) ([]identityentity.KeycloakUser, error)
	GetVersion() (string, error)
}
