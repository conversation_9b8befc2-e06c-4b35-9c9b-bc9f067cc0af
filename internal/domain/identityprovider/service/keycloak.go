package service

import (
	identityentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	identityrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository"
)

//go:generate minimock -i KeycloakDomainService -o ../mocks/keycloak_domain_service_mock.go -s _mock.go
type KeycloakDomainService interface {
	GetClientID() string
	GetRealm() string
	GetURL() string
	GetUsersByEmail(email string) ([]identityentity.KeycloakUser, error)
	GetUsersBySearch(search string) ([]identityentity.KeycloakUser, error)
	GetVersion() (string, error)
}

type keycloakDomainService struct {
	identityProvider identityrepository.IdentityProvider
}

func NewKeycloakDomainService(
	identityProvider identityrepository.IdentityProvider,
) KeycloakDomainService {
	return &keycloakDomainService{
		identityProvider: identityProvider,
	}
}

func (s *keycloakDomainService) GetClientID() string {
	return s.identityProvider.GetClientID()
}

func (s *keycloakDomainService) GetRealm() string {
	return s.identityProvider.GetRealm()
}

func (s *keycloakDomainService) GetURL() string {
	return s.identityProvider.GetURL()
}

func (s *keycloakDomainService) GetUsersByEmail(email string) ([]identityentity.KeycloakUser, error) {
	return s.identityProvider.GetUsersByEmail(email)
}

func (s *keycloakDomainService) GetUsersBySearch(search string) ([]identityentity.KeycloakUser, error) {
	return s.identityProvider.GetUsersBySearch(search)
}

func (s *keycloakDomainService) GetVersion() (string, error) {
	return s.identityProvider.GetVersion()
}
