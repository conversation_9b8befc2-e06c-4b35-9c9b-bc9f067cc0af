// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/repository.IdentityProvider -o identity_provider_mock.go -n IdentityProviderMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	identityentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	"github.com/gojuno/minimock/v3"
)

// IdentityProviderMock implements mm_repository.IdentityProvider
type IdentityProviderMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetClientID          func() (s1 string)
	funcGetClientIDOrigin    string
	inspectFuncGetClientID   func()
	afterGetClientIDCounter  uint64
	beforeGetClientIDCounter uint64
	GetClientIDMock          mIdentityProviderMockGetClientID

	funcGetRealm          func() (s1 string)
	funcGetRealmOrigin    string
	inspectFuncGetRealm   func()
	afterGetRealmCounter  uint64
	beforeGetRealmCounter uint64
	GetRealmMock          mIdentityProviderMockGetRealm

	funcGetURL          func() (s1 string)
	funcGetURLOrigin    string
	inspectFuncGetURL   func()
	afterGetURLCounter  uint64
	beforeGetURLCounter uint64
	GetURLMock          mIdentityProviderMockGetURL

	funcGetUsersByEmail          func(email string) (ka1 []identityentity.KeycloakUser, err error)
	funcGetUsersByEmailOrigin    string
	inspectFuncGetUsersByEmail   func(email string)
	afterGetUsersByEmailCounter  uint64
	beforeGetUsersByEmailCounter uint64
	GetUsersByEmailMock          mIdentityProviderMockGetUsersByEmail

	funcGetUsersBySearch          func(search string) (ka1 []identityentity.KeycloakUser, err error)
	funcGetUsersBySearchOrigin    string
	inspectFuncGetUsersBySearch   func(search string)
	afterGetUsersBySearchCounter  uint64
	beforeGetUsersBySearchCounter uint64
	GetUsersBySearchMock          mIdentityProviderMockGetUsersBySearch

	funcGetVersion          func() (s1 string, err error)
	funcGetVersionOrigin    string
	inspectFuncGetVersion   func()
	afterGetVersionCounter  uint64
	beforeGetVersionCounter uint64
	GetVersionMock          mIdentityProviderMockGetVersion
}

// NewIdentityProviderMock returns a mock for mm_repository.IdentityProvider
func NewIdentityProviderMock(t minimock.Tester) *IdentityProviderMock {
	m := &IdentityProviderMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetClientIDMock = mIdentityProviderMockGetClientID{mock: m}

	m.GetRealmMock = mIdentityProviderMockGetRealm{mock: m}

	m.GetURLMock = mIdentityProviderMockGetURL{mock: m}

	m.GetUsersByEmailMock = mIdentityProviderMockGetUsersByEmail{mock: m}
	m.GetUsersByEmailMock.callArgs = []*IdentityProviderMockGetUsersByEmailParams{}

	m.GetUsersBySearchMock = mIdentityProviderMockGetUsersBySearch{mock: m}
	m.GetUsersBySearchMock.callArgs = []*IdentityProviderMockGetUsersBySearchParams{}

	m.GetVersionMock = mIdentityProviderMockGetVersion{mock: m}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mIdentityProviderMockGetClientID struct {
	optional           bool
	mock               *IdentityProviderMock
	defaultExpectation *IdentityProviderMockGetClientIDExpectation
	expectations       []*IdentityProviderMockGetClientIDExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// IdentityProviderMockGetClientIDExpectation specifies expectation struct of the IdentityProvider.GetClientID
type IdentityProviderMockGetClientIDExpectation struct {
	mock *IdentityProviderMock

	results      *IdentityProviderMockGetClientIDResults
	returnOrigin string
	Counter      uint64
}

// IdentityProviderMockGetClientIDResults contains results of the IdentityProvider.GetClientID
type IdentityProviderMockGetClientIDResults struct {
	s1 string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetClientID *mIdentityProviderMockGetClientID) Optional() *mIdentityProviderMockGetClientID {
	mmGetClientID.optional = true
	return mmGetClientID
}

// Expect sets up expected params for IdentityProvider.GetClientID
func (mmGetClientID *mIdentityProviderMockGetClientID) Expect() *mIdentityProviderMockGetClientID {
	if mmGetClientID.mock.funcGetClientID != nil {
		mmGetClientID.mock.t.Fatalf("IdentityProviderMock.GetClientID mock is already set by Set")
	}

	if mmGetClientID.defaultExpectation == nil {
		mmGetClientID.defaultExpectation = &IdentityProviderMockGetClientIDExpectation{}
	}

	return mmGetClientID
}

// Inspect accepts an inspector function that has same arguments as the IdentityProvider.GetClientID
func (mmGetClientID *mIdentityProviderMockGetClientID) Inspect(f func()) *mIdentityProviderMockGetClientID {
	if mmGetClientID.mock.inspectFuncGetClientID != nil {
		mmGetClientID.mock.t.Fatalf("Inspect function is already set for IdentityProviderMock.GetClientID")
	}

	mmGetClientID.mock.inspectFuncGetClientID = f

	return mmGetClientID
}

// Return sets up results that will be returned by IdentityProvider.GetClientID
func (mmGetClientID *mIdentityProviderMockGetClientID) Return(s1 string) *IdentityProviderMock {
	if mmGetClientID.mock.funcGetClientID != nil {
		mmGetClientID.mock.t.Fatalf("IdentityProviderMock.GetClientID mock is already set by Set")
	}

	if mmGetClientID.defaultExpectation == nil {
		mmGetClientID.defaultExpectation = &IdentityProviderMockGetClientIDExpectation{mock: mmGetClientID.mock}
	}
	mmGetClientID.defaultExpectation.results = &IdentityProviderMockGetClientIDResults{s1}
	mmGetClientID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetClientID.mock
}

// Set uses given function f to mock the IdentityProvider.GetClientID method
func (mmGetClientID *mIdentityProviderMockGetClientID) Set(f func() (s1 string)) *IdentityProviderMock {
	if mmGetClientID.defaultExpectation != nil {
		mmGetClientID.mock.t.Fatalf("Default expectation is already set for the IdentityProvider.GetClientID method")
	}

	if len(mmGetClientID.expectations) > 0 {
		mmGetClientID.mock.t.Fatalf("Some expectations are already set for the IdentityProvider.GetClientID method")
	}

	mmGetClientID.mock.funcGetClientID = f
	mmGetClientID.mock.funcGetClientIDOrigin = minimock.CallerInfo(1)
	return mmGetClientID.mock
}

// Times sets number of times IdentityProvider.GetClientID should be invoked
func (mmGetClientID *mIdentityProviderMockGetClientID) Times(n uint64) *mIdentityProviderMockGetClientID {
	if n == 0 {
		mmGetClientID.mock.t.Fatalf("Times of IdentityProviderMock.GetClientID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetClientID.expectedInvocations, n)
	mmGetClientID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetClientID
}

func (mmGetClientID *mIdentityProviderMockGetClientID) invocationsDone() bool {
	if len(mmGetClientID.expectations) == 0 && mmGetClientID.defaultExpectation == nil && mmGetClientID.mock.funcGetClientID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetClientID.mock.afterGetClientIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetClientID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetClientID implements mm_repository.IdentityProvider
func (mmGetClientID *IdentityProviderMock) GetClientID() (s1 string) {
	mm_atomic.AddUint64(&mmGetClientID.beforeGetClientIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetClientID.afterGetClientIDCounter, 1)

	mmGetClientID.t.Helper()

	if mmGetClientID.inspectFuncGetClientID != nil {
		mmGetClientID.inspectFuncGetClientID()
	}

	if mmGetClientID.GetClientIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetClientID.GetClientIDMock.defaultExpectation.Counter, 1)

		mm_results := mmGetClientID.GetClientIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetClientID.t.Fatal("No results are set for the IdentityProviderMock.GetClientID")
		}
		return (*mm_results).s1
	}
	if mmGetClientID.funcGetClientID != nil {
		return mmGetClientID.funcGetClientID()
	}
	mmGetClientID.t.Fatalf("Unexpected call to IdentityProviderMock.GetClientID.")
	return
}

// GetClientIDAfterCounter returns a count of finished IdentityProviderMock.GetClientID invocations
func (mmGetClientID *IdentityProviderMock) GetClientIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetClientID.afterGetClientIDCounter)
}

// GetClientIDBeforeCounter returns a count of IdentityProviderMock.GetClientID invocations
func (mmGetClientID *IdentityProviderMock) GetClientIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetClientID.beforeGetClientIDCounter)
}

// MinimockGetClientIDDone returns true if the count of the GetClientID invocations corresponds
// the number of defined expectations
func (m *IdentityProviderMock) MinimockGetClientIDDone() bool {
	if m.GetClientIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetClientIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetClientIDMock.invocationsDone()
}

// MinimockGetClientIDInspect logs each unmet expectation
func (m *IdentityProviderMock) MinimockGetClientIDInspect() {
	for _, e := range m.GetClientIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to IdentityProviderMock.GetClientID")
		}
	}

	afterGetClientIDCounter := mm_atomic.LoadUint64(&m.afterGetClientIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetClientIDMock.defaultExpectation != nil && afterGetClientIDCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetClientID at\n%s", m.GetClientIDMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetClientID != nil && afterGetClientIDCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetClientID at\n%s", m.funcGetClientIDOrigin)
	}

	if !m.GetClientIDMock.invocationsDone() && afterGetClientIDCounter > 0 {
		m.t.Errorf("Expected %d calls to IdentityProviderMock.GetClientID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetClientIDMock.expectedInvocations), m.GetClientIDMock.expectedInvocationsOrigin, afterGetClientIDCounter)
	}
}

type mIdentityProviderMockGetRealm struct {
	optional           bool
	mock               *IdentityProviderMock
	defaultExpectation *IdentityProviderMockGetRealmExpectation
	expectations       []*IdentityProviderMockGetRealmExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// IdentityProviderMockGetRealmExpectation specifies expectation struct of the IdentityProvider.GetRealm
type IdentityProviderMockGetRealmExpectation struct {
	mock *IdentityProviderMock

	results      *IdentityProviderMockGetRealmResults
	returnOrigin string
	Counter      uint64
}

// IdentityProviderMockGetRealmResults contains results of the IdentityProvider.GetRealm
type IdentityProviderMockGetRealmResults struct {
	s1 string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRealm *mIdentityProviderMockGetRealm) Optional() *mIdentityProviderMockGetRealm {
	mmGetRealm.optional = true
	return mmGetRealm
}

// Expect sets up expected params for IdentityProvider.GetRealm
func (mmGetRealm *mIdentityProviderMockGetRealm) Expect() *mIdentityProviderMockGetRealm {
	if mmGetRealm.mock.funcGetRealm != nil {
		mmGetRealm.mock.t.Fatalf("IdentityProviderMock.GetRealm mock is already set by Set")
	}

	if mmGetRealm.defaultExpectation == nil {
		mmGetRealm.defaultExpectation = &IdentityProviderMockGetRealmExpectation{}
	}

	return mmGetRealm
}

// Inspect accepts an inspector function that has same arguments as the IdentityProvider.GetRealm
func (mmGetRealm *mIdentityProviderMockGetRealm) Inspect(f func()) *mIdentityProviderMockGetRealm {
	if mmGetRealm.mock.inspectFuncGetRealm != nil {
		mmGetRealm.mock.t.Fatalf("Inspect function is already set for IdentityProviderMock.GetRealm")
	}

	mmGetRealm.mock.inspectFuncGetRealm = f

	return mmGetRealm
}

// Return sets up results that will be returned by IdentityProvider.GetRealm
func (mmGetRealm *mIdentityProviderMockGetRealm) Return(s1 string) *IdentityProviderMock {
	if mmGetRealm.mock.funcGetRealm != nil {
		mmGetRealm.mock.t.Fatalf("IdentityProviderMock.GetRealm mock is already set by Set")
	}

	if mmGetRealm.defaultExpectation == nil {
		mmGetRealm.defaultExpectation = &IdentityProviderMockGetRealmExpectation{mock: mmGetRealm.mock}
	}
	mmGetRealm.defaultExpectation.results = &IdentityProviderMockGetRealmResults{s1}
	mmGetRealm.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRealm.mock
}

// Set uses given function f to mock the IdentityProvider.GetRealm method
func (mmGetRealm *mIdentityProviderMockGetRealm) Set(f func() (s1 string)) *IdentityProviderMock {
	if mmGetRealm.defaultExpectation != nil {
		mmGetRealm.mock.t.Fatalf("Default expectation is already set for the IdentityProvider.GetRealm method")
	}

	if len(mmGetRealm.expectations) > 0 {
		mmGetRealm.mock.t.Fatalf("Some expectations are already set for the IdentityProvider.GetRealm method")
	}

	mmGetRealm.mock.funcGetRealm = f
	mmGetRealm.mock.funcGetRealmOrigin = minimock.CallerInfo(1)
	return mmGetRealm.mock
}

// Times sets number of times IdentityProvider.GetRealm should be invoked
func (mmGetRealm *mIdentityProviderMockGetRealm) Times(n uint64) *mIdentityProviderMockGetRealm {
	if n == 0 {
		mmGetRealm.mock.t.Fatalf("Times of IdentityProviderMock.GetRealm mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRealm.expectedInvocations, n)
	mmGetRealm.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRealm
}

func (mmGetRealm *mIdentityProviderMockGetRealm) invocationsDone() bool {
	if len(mmGetRealm.expectations) == 0 && mmGetRealm.defaultExpectation == nil && mmGetRealm.mock.funcGetRealm == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRealm.mock.afterGetRealmCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRealm.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRealm implements mm_repository.IdentityProvider
func (mmGetRealm *IdentityProviderMock) GetRealm() (s1 string) {
	mm_atomic.AddUint64(&mmGetRealm.beforeGetRealmCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRealm.afterGetRealmCounter, 1)

	mmGetRealm.t.Helper()

	if mmGetRealm.inspectFuncGetRealm != nil {
		mmGetRealm.inspectFuncGetRealm()
	}

	if mmGetRealm.GetRealmMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRealm.GetRealmMock.defaultExpectation.Counter, 1)

		mm_results := mmGetRealm.GetRealmMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRealm.t.Fatal("No results are set for the IdentityProviderMock.GetRealm")
		}
		return (*mm_results).s1
	}
	if mmGetRealm.funcGetRealm != nil {
		return mmGetRealm.funcGetRealm()
	}
	mmGetRealm.t.Fatalf("Unexpected call to IdentityProviderMock.GetRealm.")
	return
}

// GetRealmAfterCounter returns a count of finished IdentityProviderMock.GetRealm invocations
func (mmGetRealm *IdentityProviderMock) GetRealmAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRealm.afterGetRealmCounter)
}

// GetRealmBeforeCounter returns a count of IdentityProviderMock.GetRealm invocations
func (mmGetRealm *IdentityProviderMock) GetRealmBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRealm.beforeGetRealmCounter)
}

// MinimockGetRealmDone returns true if the count of the GetRealm invocations corresponds
// the number of defined expectations
func (m *IdentityProviderMock) MinimockGetRealmDone() bool {
	if m.GetRealmMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRealmMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRealmMock.invocationsDone()
}

// MinimockGetRealmInspect logs each unmet expectation
func (m *IdentityProviderMock) MinimockGetRealmInspect() {
	for _, e := range m.GetRealmMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to IdentityProviderMock.GetRealm")
		}
	}

	afterGetRealmCounter := mm_atomic.LoadUint64(&m.afterGetRealmCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRealmMock.defaultExpectation != nil && afterGetRealmCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetRealm at\n%s", m.GetRealmMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRealm != nil && afterGetRealmCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetRealm at\n%s", m.funcGetRealmOrigin)
	}

	if !m.GetRealmMock.invocationsDone() && afterGetRealmCounter > 0 {
		m.t.Errorf("Expected %d calls to IdentityProviderMock.GetRealm at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRealmMock.expectedInvocations), m.GetRealmMock.expectedInvocationsOrigin, afterGetRealmCounter)
	}
}

type mIdentityProviderMockGetURL struct {
	optional           bool
	mock               *IdentityProviderMock
	defaultExpectation *IdentityProviderMockGetURLExpectation
	expectations       []*IdentityProviderMockGetURLExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// IdentityProviderMockGetURLExpectation specifies expectation struct of the IdentityProvider.GetURL
type IdentityProviderMockGetURLExpectation struct {
	mock *IdentityProviderMock

	results      *IdentityProviderMockGetURLResults
	returnOrigin string
	Counter      uint64
}

// IdentityProviderMockGetURLResults contains results of the IdentityProvider.GetURL
type IdentityProviderMockGetURLResults struct {
	s1 string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetURL *mIdentityProviderMockGetURL) Optional() *mIdentityProviderMockGetURL {
	mmGetURL.optional = true
	return mmGetURL
}

// Expect sets up expected params for IdentityProvider.GetURL
func (mmGetURL *mIdentityProviderMockGetURL) Expect() *mIdentityProviderMockGetURL {
	if mmGetURL.mock.funcGetURL != nil {
		mmGetURL.mock.t.Fatalf("IdentityProviderMock.GetURL mock is already set by Set")
	}

	if mmGetURL.defaultExpectation == nil {
		mmGetURL.defaultExpectation = &IdentityProviderMockGetURLExpectation{}
	}

	return mmGetURL
}

// Inspect accepts an inspector function that has same arguments as the IdentityProvider.GetURL
func (mmGetURL *mIdentityProviderMockGetURL) Inspect(f func()) *mIdentityProviderMockGetURL {
	if mmGetURL.mock.inspectFuncGetURL != nil {
		mmGetURL.mock.t.Fatalf("Inspect function is already set for IdentityProviderMock.GetURL")
	}

	mmGetURL.mock.inspectFuncGetURL = f

	return mmGetURL
}

// Return sets up results that will be returned by IdentityProvider.GetURL
func (mmGetURL *mIdentityProviderMockGetURL) Return(s1 string) *IdentityProviderMock {
	if mmGetURL.mock.funcGetURL != nil {
		mmGetURL.mock.t.Fatalf("IdentityProviderMock.GetURL mock is already set by Set")
	}

	if mmGetURL.defaultExpectation == nil {
		mmGetURL.defaultExpectation = &IdentityProviderMockGetURLExpectation{mock: mmGetURL.mock}
	}
	mmGetURL.defaultExpectation.results = &IdentityProviderMockGetURLResults{s1}
	mmGetURL.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetURL.mock
}

// Set uses given function f to mock the IdentityProvider.GetURL method
func (mmGetURL *mIdentityProviderMockGetURL) Set(f func() (s1 string)) *IdentityProviderMock {
	if mmGetURL.defaultExpectation != nil {
		mmGetURL.mock.t.Fatalf("Default expectation is already set for the IdentityProvider.GetURL method")
	}

	if len(mmGetURL.expectations) > 0 {
		mmGetURL.mock.t.Fatalf("Some expectations are already set for the IdentityProvider.GetURL method")
	}

	mmGetURL.mock.funcGetURL = f
	mmGetURL.mock.funcGetURLOrigin = minimock.CallerInfo(1)
	return mmGetURL.mock
}

// Times sets number of times IdentityProvider.GetURL should be invoked
func (mmGetURL *mIdentityProviderMockGetURL) Times(n uint64) *mIdentityProviderMockGetURL {
	if n == 0 {
		mmGetURL.mock.t.Fatalf("Times of IdentityProviderMock.GetURL mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetURL.expectedInvocations, n)
	mmGetURL.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetURL
}

func (mmGetURL *mIdentityProviderMockGetURL) invocationsDone() bool {
	if len(mmGetURL.expectations) == 0 && mmGetURL.defaultExpectation == nil && mmGetURL.mock.funcGetURL == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetURL.mock.afterGetURLCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetURL.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetURL implements mm_repository.IdentityProvider
func (mmGetURL *IdentityProviderMock) GetURL() (s1 string) {
	mm_atomic.AddUint64(&mmGetURL.beforeGetURLCounter, 1)
	defer mm_atomic.AddUint64(&mmGetURL.afterGetURLCounter, 1)

	mmGetURL.t.Helper()

	if mmGetURL.inspectFuncGetURL != nil {
		mmGetURL.inspectFuncGetURL()
	}

	if mmGetURL.GetURLMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetURL.GetURLMock.defaultExpectation.Counter, 1)

		mm_results := mmGetURL.GetURLMock.defaultExpectation.results
		if mm_results == nil {
			mmGetURL.t.Fatal("No results are set for the IdentityProviderMock.GetURL")
		}
		return (*mm_results).s1
	}
	if mmGetURL.funcGetURL != nil {
		return mmGetURL.funcGetURL()
	}
	mmGetURL.t.Fatalf("Unexpected call to IdentityProviderMock.GetURL.")
	return
}

// GetURLAfterCounter returns a count of finished IdentityProviderMock.GetURL invocations
func (mmGetURL *IdentityProviderMock) GetURLAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetURL.afterGetURLCounter)
}

// GetURLBeforeCounter returns a count of IdentityProviderMock.GetURL invocations
func (mmGetURL *IdentityProviderMock) GetURLBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetURL.beforeGetURLCounter)
}

// MinimockGetURLDone returns true if the count of the GetURL invocations corresponds
// the number of defined expectations
func (m *IdentityProviderMock) MinimockGetURLDone() bool {
	if m.GetURLMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetURLMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetURLMock.invocationsDone()
}

// MinimockGetURLInspect logs each unmet expectation
func (m *IdentityProviderMock) MinimockGetURLInspect() {
	for _, e := range m.GetURLMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to IdentityProviderMock.GetURL")
		}
	}

	afterGetURLCounter := mm_atomic.LoadUint64(&m.afterGetURLCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetURLMock.defaultExpectation != nil && afterGetURLCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetURL at\n%s", m.GetURLMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetURL != nil && afterGetURLCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetURL at\n%s", m.funcGetURLOrigin)
	}

	if !m.GetURLMock.invocationsDone() && afterGetURLCounter > 0 {
		m.t.Errorf("Expected %d calls to IdentityProviderMock.GetURL at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetURLMock.expectedInvocations), m.GetURLMock.expectedInvocationsOrigin, afterGetURLCounter)
	}
}

type mIdentityProviderMockGetUsersByEmail struct {
	optional           bool
	mock               *IdentityProviderMock
	defaultExpectation *IdentityProviderMockGetUsersByEmailExpectation
	expectations       []*IdentityProviderMockGetUsersByEmailExpectation

	callArgs []*IdentityProviderMockGetUsersByEmailParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// IdentityProviderMockGetUsersByEmailExpectation specifies expectation struct of the IdentityProvider.GetUsersByEmail
type IdentityProviderMockGetUsersByEmailExpectation struct {
	mock               *IdentityProviderMock
	params             *IdentityProviderMockGetUsersByEmailParams
	paramPtrs          *IdentityProviderMockGetUsersByEmailParamPtrs
	expectationOrigins IdentityProviderMockGetUsersByEmailExpectationOrigins
	results            *IdentityProviderMockGetUsersByEmailResults
	returnOrigin       string
	Counter            uint64
}

// IdentityProviderMockGetUsersByEmailParams contains parameters of the IdentityProvider.GetUsersByEmail
type IdentityProviderMockGetUsersByEmailParams struct {
	email string
}

// IdentityProviderMockGetUsersByEmailParamPtrs contains pointers to parameters of the IdentityProvider.GetUsersByEmail
type IdentityProviderMockGetUsersByEmailParamPtrs struct {
	email *string
}

// IdentityProviderMockGetUsersByEmailResults contains results of the IdentityProvider.GetUsersByEmail
type IdentityProviderMockGetUsersByEmailResults struct {
	ka1 []identityentity.KeycloakUser
	err error
}

// IdentityProviderMockGetUsersByEmailOrigins contains origins of expectations of the IdentityProvider.GetUsersByEmail
type IdentityProviderMockGetUsersByEmailExpectationOrigins struct {
	origin      string
	originEmail string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Optional() *mIdentityProviderMockGetUsersByEmail {
	mmGetUsersByEmail.optional = true
	return mmGetUsersByEmail
}

// Expect sets up expected params for IdentityProvider.GetUsersByEmail
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Expect(email string) *mIdentityProviderMockGetUsersByEmail {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("IdentityProviderMock.GetUsersByEmail mock is already set by Set")
	}

	if mmGetUsersByEmail.defaultExpectation == nil {
		mmGetUsersByEmail.defaultExpectation = &IdentityProviderMockGetUsersByEmailExpectation{}
	}

	if mmGetUsersByEmail.defaultExpectation.paramPtrs != nil {
		mmGetUsersByEmail.mock.t.Fatalf("IdentityProviderMock.GetUsersByEmail mock is already set by ExpectParams functions")
	}

	mmGetUsersByEmail.defaultExpectation.params = &IdentityProviderMockGetUsersByEmailParams{email}
	mmGetUsersByEmail.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersByEmail.expectations {
		if minimock.Equal(e.params, mmGetUsersByEmail.defaultExpectation.params) {
			mmGetUsersByEmail.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersByEmail.defaultExpectation.params)
		}
	}

	return mmGetUsersByEmail
}

// ExpectEmailParam1 sets up expected param email for IdentityProvider.GetUsersByEmail
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) ExpectEmailParam1(email string) *mIdentityProviderMockGetUsersByEmail {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("IdentityProviderMock.GetUsersByEmail mock is already set by Set")
	}

	if mmGetUsersByEmail.defaultExpectation == nil {
		mmGetUsersByEmail.defaultExpectation = &IdentityProviderMockGetUsersByEmailExpectation{}
	}

	if mmGetUsersByEmail.defaultExpectation.params != nil {
		mmGetUsersByEmail.mock.t.Fatalf("IdentityProviderMock.GetUsersByEmail mock is already set by Expect")
	}

	if mmGetUsersByEmail.defaultExpectation.paramPtrs == nil {
		mmGetUsersByEmail.defaultExpectation.paramPtrs = &IdentityProviderMockGetUsersByEmailParamPtrs{}
	}
	mmGetUsersByEmail.defaultExpectation.paramPtrs.email = &email
	mmGetUsersByEmail.defaultExpectation.expectationOrigins.originEmail = minimock.CallerInfo(1)

	return mmGetUsersByEmail
}

// Inspect accepts an inspector function that has same arguments as the IdentityProvider.GetUsersByEmail
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Inspect(f func(email string)) *mIdentityProviderMockGetUsersByEmail {
	if mmGetUsersByEmail.mock.inspectFuncGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("Inspect function is already set for IdentityProviderMock.GetUsersByEmail")
	}

	mmGetUsersByEmail.mock.inspectFuncGetUsersByEmail = f

	return mmGetUsersByEmail
}

// Return sets up results that will be returned by IdentityProvider.GetUsersByEmail
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Return(ka1 []identityentity.KeycloakUser, err error) *IdentityProviderMock {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("IdentityProviderMock.GetUsersByEmail mock is already set by Set")
	}

	if mmGetUsersByEmail.defaultExpectation == nil {
		mmGetUsersByEmail.defaultExpectation = &IdentityProviderMockGetUsersByEmailExpectation{mock: mmGetUsersByEmail.mock}
	}
	mmGetUsersByEmail.defaultExpectation.results = &IdentityProviderMockGetUsersByEmailResults{ka1, err}
	mmGetUsersByEmail.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersByEmail.mock
}

// Set uses given function f to mock the IdentityProvider.GetUsersByEmail method
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Set(f func(email string) (ka1 []identityentity.KeycloakUser, err error)) *IdentityProviderMock {
	if mmGetUsersByEmail.defaultExpectation != nil {
		mmGetUsersByEmail.mock.t.Fatalf("Default expectation is already set for the IdentityProvider.GetUsersByEmail method")
	}

	if len(mmGetUsersByEmail.expectations) > 0 {
		mmGetUsersByEmail.mock.t.Fatalf("Some expectations are already set for the IdentityProvider.GetUsersByEmail method")
	}

	mmGetUsersByEmail.mock.funcGetUsersByEmail = f
	mmGetUsersByEmail.mock.funcGetUsersByEmailOrigin = minimock.CallerInfo(1)
	return mmGetUsersByEmail.mock
}

// When sets expectation for the IdentityProvider.GetUsersByEmail which will trigger the result defined by the following
// Then helper
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) When(email string) *IdentityProviderMockGetUsersByEmailExpectation {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("IdentityProviderMock.GetUsersByEmail mock is already set by Set")
	}

	expectation := &IdentityProviderMockGetUsersByEmailExpectation{
		mock:               mmGetUsersByEmail.mock,
		params:             &IdentityProviderMockGetUsersByEmailParams{email},
		expectationOrigins: IdentityProviderMockGetUsersByEmailExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersByEmail.expectations = append(mmGetUsersByEmail.expectations, expectation)
	return expectation
}

// Then sets up IdentityProvider.GetUsersByEmail return parameters for the expectation previously defined by the When method
func (e *IdentityProviderMockGetUsersByEmailExpectation) Then(ka1 []identityentity.KeycloakUser, err error) *IdentityProviderMock {
	e.results = &IdentityProviderMockGetUsersByEmailResults{ka1, err}
	return e.mock
}

// Times sets number of times IdentityProvider.GetUsersByEmail should be invoked
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Times(n uint64) *mIdentityProviderMockGetUsersByEmail {
	if n == 0 {
		mmGetUsersByEmail.mock.t.Fatalf("Times of IdentityProviderMock.GetUsersByEmail mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersByEmail.expectedInvocations, n)
	mmGetUsersByEmail.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersByEmail
}

func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) invocationsDone() bool {
	if len(mmGetUsersByEmail.expectations) == 0 && mmGetUsersByEmail.defaultExpectation == nil && mmGetUsersByEmail.mock.funcGetUsersByEmail == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersByEmail.mock.afterGetUsersByEmailCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersByEmail.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersByEmail implements mm_repository.IdentityProvider
func (mmGetUsersByEmail *IdentityProviderMock) GetUsersByEmail(email string) (ka1 []identityentity.KeycloakUser, err error) {
	mm_atomic.AddUint64(&mmGetUsersByEmail.beforeGetUsersByEmailCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersByEmail.afterGetUsersByEmailCounter, 1)

	mmGetUsersByEmail.t.Helper()

	if mmGetUsersByEmail.inspectFuncGetUsersByEmail != nil {
		mmGetUsersByEmail.inspectFuncGetUsersByEmail(email)
	}

	mm_params := IdentityProviderMockGetUsersByEmailParams{email}

	// Record call args
	mmGetUsersByEmail.GetUsersByEmailMock.mutex.Lock()
	mmGetUsersByEmail.GetUsersByEmailMock.callArgs = append(mmGetUsersByEmail.GetUsersByEmailMock.callArgs, &mm_params)
	mmGetUsersByEmail.GetUsersByEmailMock.mutex.Unlock()

	for _, e := range mmGetUsersByEmail.GetUsersByEmailMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ka1, e.results.err
		}
	}

	if mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.paramPtrs

		mm_got := IdentityProviderMockGetUsersByEmailParams{email}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.email != nil && !minimock.Equal(*mm_want_ptrs.email, mm_got.email) {
				mmGetUsersByEmail.t.Errorf("IdentityProviderMock.GetUsersByEmail got unexpected parameter email, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.expectationOrigins.originEmail, *mm_want_ptrs.email, mm_got.email, minimock.Diff(*mm_want_ptrs.email, mm_got.email))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersByEmail.t.Errorf("IdentityProviderMock.GetUsersByEmail got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersByEmail.t.Fatal("No results are set for the IdentityProviderMock.GetUsersByEmail")
		}
		return (*mm_results).ka1, (*mm_results).err
	}
	if mmGetUsersByEmail.funcGetUsersByEmail != nil {
		return mmGetUsersByEmail.funcGetUsersByEmail(email)
	}
	mmGetUsersByEmail.t.Fatalf("Unexpected call to IdentityProviderMock.GetUsersByEmail. %v", email)
	return
}

// GetUsersByEmailAfterCounter returns a count of finished IdentityProviderMock.GetUsersByEmail invocations
func (mmGetUsersByEmail *IdentityProviderMock) GetUsersByEmailAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByEmail.afterGetUsersByEmailCounter)
}

// GetUsersByEmailBeforeCounter returns a count of IdentityProviderMock.GetUsersByEmail invocations
func (mmGetUsersByEmail *IdentityProviderMock) GetUsersByEmailBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByEmail.beforeGetUsersByEmailCounter)
}

// Calls returns a list of arguments used in each call to IdentityProviderMock.GetUsersByEmail.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersByEmail *mIdentityProviderMockGetUsersByEmail) Calls() []*IdentityProviderMockGetUsersByEmailParams {
	mmGetUsersByEmail.mutex.RLock()

	argCopy := make([]*IdentityProviderMockGetUsersByEmailParams, len(mmGetUsersByEmail.callArgs))
	copy(argCopy, mmGetUsersByEmail.callArgs)

	mmGetUsersByEmail.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersByEmailDone returns true if the count of the GetUsersByEmail invocations corresponds
// the number of defined expectations
func (m *IdentityProviderMock) MinimockGetUsersByEmailDone() bool {
	if m.GetUsersByEmailMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersByEmailMock.invocationsDone()
}

// MinimockGetUsersByEmailInspect logs each unmet expectation
func (m *IdentityProviderMock) MinimockGetUsersByEmailInspect() {
	for _, e := range m.GetUsersByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to IdentityProviderMock.GetUsersByEmail at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersByEmailCounter := mm_atomic.LoadUint64(&m.afterGetUsersByEmailCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersByEmailMock.defaultExpectation != nil && afterGetUsersByEmailCounter < 1 {
		if m.GetUsersByEmailMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to IdentityProviderMock.GetUsersByEmail at\n%s", m.GetUsersByEmailMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to IdentityProviderMock.GetUsersByEmail at\n%s with params: %#v", m.GetUsersByEmailMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersByEmailMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersByEmail != nil && afterGetUsersByEmailCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetUsersByEmail at\n%s", m.funcGetUsersByEmailOrigin)
	}

	if !m.GetUsersByEmailMock.invocationsDone() && afterGetUsersByEmailCounter > 0 {
		m.t.Errorf("Expected %d calls to IdentityProviderMock.GetUsersByEmail at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersByEmailMock.expectedInvocations), m.GetUsersByEmailMock.expectedInvocationsOrigin, afterGetUsersByEmailCounter)
	}
}

type mIdentityProviderMockGetUsersBySearch struct {
	optional           bool
	mock               *IdentityProviderMock
	defaultExpectation *IdentityProviderMockGetUsersBySearchExpectation
	expectations       []*IdentityProviderMockGetUsersBySearchExpectation

	callArgs []*IdentityProviderMockGetUsersBySearchParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// IdentityProviderMockGetUsersBySearchExpectation specifies expectation struct of the IdentityProvider.GetUsersBySearch
type IdentityProviderMockGetUsersBySearchExpectation struct {
	mock               *IdentityProviderMock
	params             *IdentityProviderMockGetUsersBySearchParams
	paramPtrs          *IdentityProviderMockGetUsersBySearchParamPtrs
	expectationOrigins IdentityProviderMockGetUsersBySearchExpectationOrigins
	results            *IdentityProviderMockGetUsersBySearchResults
	returnOrigin       string
	Counter            uint64
}

// IdentityProviderMockGetUsersBySearchParams contains parameters of the IdentityProvider.GetUsersBySearch
type IdentityProviderMockGetUsersBySearchParams struct {
	search string
}

// IdentityProviderMockGetUsersBySearchParamPtrs contains pointers to parameters of the IdentityProvider.GetUsersBySearch
type IdentityProviderMockGetUsersBySearchParamPtrs struct {
	search *string
}

// IdentityProviderMockGetUsersBySearchResults contains results of the IdentityProvider.GetUsersBySearch
type IdentityProviderMockGetUsersBySearchResults struct {
	ka1 []identityentity.KeycloakUser
	err error
}

// IdentityProviderMockGetUsersBySearchOrigins contains origins of expectations of the IdentityProvider.GetUsersBySearch
type IdentityProviderMockGetUsersBySearchExpectationOrigins struct {
	origin       string
	originSearch string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Optional() *mIdentityProviderMockGetUsersBySearch {
	mmGetUsersBySearch.optional = true
	return mmGetUsersBySearch
}

// Expect sets up expected params for IdentityProvider.GetUsersBySearch
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Expect(search string) *mIdentityProviderMockGetUsersBySearch {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("IdentityProviderMock.GetUsersBySearch mock is already set by Set")
	}

	if mmGetUsersBySearch.defaultExpectation == nil {
		mmGetUsersBySearch.defaultExpectation = &IdentityProviderMockGetUsersBySearchExpectation{}
	}

	if mmGetUsersBySearch.defaultExpectation.paramPtrs != nil {
		mmGetUsersBySearch.mock.t.Fatalf("IdentityProviderMock.GetUsersBySearch mock is already set by ExpectParams functions")
	}

	mmGetUsersBySearch.defaultExpectation.params = &IdentityProviderMockGetUsersBySearchParams{search}
	mmGetUsersBySearch.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersBySearch.expectations {
		if minimock.Equal(e.params, mmGetUsersBySearch.defaultExpectation.params) {
			mmGetUsersBySearch.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersBySearch.defaultExpectation.params)
		}
	}

	return mmGetUsersBySearch
}

// ExpectSearchParam1 sets up expected param search for IdentityProvider.GetUsersBySearch
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) ExpectSearchParam1(search string) *mIdentityProviderMockGetUsersBySearch {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("IdentityProviderMock.GetUsersBySearch mock is already set by Set")
	}

	if mmGetUsersBySearch.defaultExpectation == nil {
		mmGetUsersBySearch.defaultExpectation = &IdentityProviderMockGetUsersBySearchExpectation{}
	}

	if mmGetUsersBySearch.defaultExpectation.params != nil {
		mmGetUsersBySearch.mock.t.Fatalf("IdentityProviderMock.GetUsersBySearch mock is already set by Expect")
	}

	if mmGetUsersBySearch.defaultExpectation.paramPtrs == nil {
		mmGetUsersBySearch.defaultExpectation.paramPtrs = &IdentityProviderMockGetUsersBySearchParamPtrs{}
	}
	mmGetUsersBySearch.defaultExpectation.paramPtrs.search = &search
	mmGetUsersBySearch.defaultExpectation.expectationOrigins.originSearch = minimock.CallerInfo(1)

	return mmGetUsersBySearch
}

// Inspect accepts an inspector function that has same arguments as the IdentityProvider.GetUsersBySearch
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Inspect(f func(search string)) *mIdentityProviderMockGetUsersBySearch {
	if mmGetUsersBySearch.mock.inspectFuncGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("Inspect function is already set for IdentityProviderMock.GetUsersBySearch")
	}

	mmGetUsersBySearch.mock.inspectFuncGetUsersBySearch = f

	return mmGetUsersBySearch
}

// Return sets up results that will be returned by IdentityProvider.GetUsersBySearch
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Return(ka1 []identityentity.KeycloakUser, err error) *IdentityProviderMock {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("IdentityProviderMock.GetUsersBySearch mock is already set by Set")
	}

	if mmGetUsersBySearch.defaultExpectation == nil {
		mmGetUsersBySearch.defaultExpectation = &IdentityProviderMockGetUsersBySearchExpectation{mock: mmGetUsersBySearch.mock}
	}
	mmGetUsersBySearch.defaultExpectation.results = &IdentityProviderMockGetUsersBySearchResults{ka1, err}
	mmGetUsersBySearch.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersBySearch.mock
}

// Set uses given function f to mock the IdentityProvider.GetUsersBySearch method
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Set(f func(search string) (ka1 []identityentity.KeycloakUser, err error)) *IdentityProviderMock {
	if mmGetUsersBySearch.defaultExpectation != nil {
		mmGetUsersBySearch.mock.t.Fatalf("Default expectation is already set for the IdentityProvider.GetUsersBySearch method")
	}

	if len(mmGetUsersBySearch.expectations) > 0 {
		mmGetUsersBySearch.mock.t.Fatalf("Some expectations are already set for the IdentityProvider.GetUsersBySearch method")
	}

	mmGetUsersBySearch.mock.funcGetUsersBySearch = f
	mmGetUsersBySearch.mock.funcGetUsersBySearchOrigin = minimock.CallerInfo(1)
	return mmGetUsersBySearch.mock
}

// When sets expectation for the IdentityProvider.GetUsersBySearch which will trigger the result defined by the following
// Then helper
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) When(search string) *IdentityProviderMockGetUsersBySearchExpectation {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("IdentityProviderMock.GetUsersBySearch mock is already set by Set")
	}

	expectation := &IdentityProviderMockGetUsersBySearchExpectation{
		mock:               mmGetUsersBySearch.mock,
		params:             &IdentityProviderMockGetUsersBySearchParams{search},
		expectationOrigins: IdentityProviderMockGetUsersBySearchExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersBySearch.expectations = append(mmGetUsersBySearch.expectations, expectation)
	return expectation
}

// Then sets up IdentityProvider.GetUsersBySearch return parameters for the expectation previously defined by the When method
func (e *IdentityProviderMockGetUsersBySearchExpectation) Then(ka1 []identityentity.KeycloakUser, err error) *IdentityProviderMock {
	e.results = &IdentityProviderMockGetUsersBySearchResults{ka1, err}
	return e.mock
}

// Times sets number of times IdentityProvider.GetUsersBySearch should be invoked
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Times(n uint64) *mIdentityProviderMockGetUsersBySearch {
	if n == 0 {
		mmGetUsersBySearch.mock.t.Fatalf("Times of IdentityProviderMock.GetUsersBySearch mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersBySearch.expectedInvocations, n)
	mmGetUsersBySearch.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersBySearch
}

func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) invocationsDone() bool {
	if len(mmGetUsersBySearch.expectations) == 0 && mmGetUsersBySearch.defaultExpectation == nil && mmGetUsersBySearch.mock.funcGetUsersBySearch == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersBySearch.mock.afterGetUsersBySearchCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersBySearch.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersBySearch implements mm_repository.IdentityProvider
func (mmGetUsersBySearch *IdentityProviderMock) GetUsersBySearch(search string) (ka1 []identityentity.KeycloakUser, err error) {
	mm_atomic.AddUint64(&mmGetUsersBySearch.beforeGetUsersBySearchCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersBySearch.afterGetUsersBySearchCounter, 1)

	mmGetUsersBySearch.t.Helper()

	if mmGetUsersBySearch.inspectFuncGetUsersBySearch != nil {
		mmGetUsersBySearch.inspectFuncGetUsersBySearch(search)
	}

	mm_params := IdentityProviderMockGetUsersBySearchParams{search}

	// Record call args
	mmGetUsersBySearch.GetUsersBySearchMock.mutex.Lock()
	mmGetUsersBySearch.GetUsersBySearchMock.callArgs = append(mmGetUsersBySearch.GetUsersBySearchMock.callArgs, &mm_params)
	mmGetUsersBySearch.GetUsersBySearchMock.mutex.Unlock()

	for _, e := range mmGetUsersBySearch.GetUsersBySearchMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ka1, e.results.err
		}
	}

	if mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.paramPtrs

		mm_got := IdentityProviderMockGetUsersBySearchParams{search}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.search != nil && !minimock.Equal(*mm_want_ptrs.search, mm_got.search) {
				mmGetUsersBySearch.t.Errorf("IdentityProviderMock.GetUsersBySearch got unexpected parameter search, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.expectationOrigins.originSearch, *mm_want_ptrs.search, mm_got.search, minimock.Diff(*mm_want_ptrs.search, mm_got.search))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersBySearch.t.Errorf("IdentityProviderMock.GetUsersBySearch got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersBySearch.t.Fatal("No results are set for the IdentityProviderMock.GetUsersBySearch")
		}
		return (*mm_results).ka1, (*mm_results).err
	}
	if mmGetUsersBySearch.funcGetUsersBySearch != nil {
		return mmGetUsersBySearch.funcGetUsersBySearch(search)
	}
	mmGetUsersBySearch.t.Fatalf("Unexpected call to IdentityProviderMock.GetUsersBySearch. %v", search)
	return
}

// GetUsersBySearchAfterCounter returns a count of finished IdentityProviderMock.GetUsersBySearch invocations
func (mmGetUsersBySearch *IdentityProviderMock) GetUsersBySearchAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersBySearch.afterGetUsersBySearchCounter)
}

// GetUsersBySearchBeforeCounter returns a count of IdentityProviderMock.GetUsersBySearch invocations
func (mmGetUsersBySearch *IdentityProviderMock) GetUsersBySearchBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersBySearch.beforeGetUsersBySearchCounter)
}

// Calls returns a list of arguments used in each call to IdentityProviderMock.GetUsersBySearch.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersBySearch *mIdentityProviderMockGetUsersBySearch) Calls() []*IdentityProviderMockGetUsersBySearchParams {
	mmGetUsersBySearch.mutex.RLock()

	argCopy := make([]*IdentityProviderMockGetUsersBySearchParams, len(mmGetUsersBySearch.callArgs))
	copy(argCopy, mmGetUsersBySearch.callArgs)

	mmGetUsersBySearch.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersBySearchDone returns true if the count of the GetUsersBySearch invocations corresponds
// the number of defined expectations
func (m *IdentityProviderMock) MinimockGetUsersBySearchDone() bool {
	if m.GetUsersBySearchMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersBySearchMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersBySearchMock.invocationsDone()
}

// MinimockGetUsersBySearchInspect logs each unmet expectation
func (m *IdentityProviderMock) MinimockGetUsersBySearchInspect() {
	for _, e := range m.GetUsersBySearchMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to IdentityProviderMock.GetUsersBySearch at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersBySearchCounter := mm_atomic.LoadUint64(&m.afterGetUsersBySearchCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersBySearchMock.defaultExpectation != nil && afterGetUsersBySearchCounter < 1 {
		if m.GetUsersBySearchMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to IdentityProviderMock.GetUsersBySearch at\n%s", m.GetUsersBySearchMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to IdentityProviderMock.GetUsersBySearch at\n%s with params: %#v", m.GetUsersBySearchMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersBySearchMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersBySearch != nil && afterGetUsersBySearchCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetUsersBySearch at\n%s", m.funcGetUsersBySearchOrigin)
	}

	if !m.GetUsersBySearchMock.invocationsDone() && afterGetUsersBySearchCounter > 0 {
		m.t.Errorf("Expected %d calls to IdentityProviderMock.GetUsersBySearch at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersBySearchMock.expectedInvocations), m.GetUsersBySearchMock.expectedInvocationsOrigin, afterGetUsersBySearchCounter)
	}
}

type mIdentityProviderMockGetVersion struct {
	optional           bool
	mock               *IdentityProviderMock
	defaultExpectation *IdentityProviderMockGetVersionExpectation
	expectations       []*IdentityProviderMockGetVersionExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// IdentityProviderMockGetVersionExpectation specifies expectation struct of the IdentityProvider.GetVersion
type IdentityProviderMockGetVersionExpectation struct {
	mock *IdentityProviderMock

	results      *IdentityProviderMockGetVersionResults
	returnOrigin string
	Counter      uint64
}

// IdentityProviderMockGetVersionResults contains results of the IdentityProvider.GetVersion
type IdentityProviderMockGetVersionResults struct {
	s1  string
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetVersion *mIdentityProviderMockGetVersion) Optional() *mIdentityProviderMockGetVersion {
	mmGetVersion.optional = true
	return mmGetVersion
}

// Expect sets up expected params for IdentityProvider.GetVersion
func (mmGetVersion *mIdentityProviderMockGetVersion) Expect() *mIdentityProviderMockGetVersion {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("IdentityProviderMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &IdentityProviderMockGetVersionExpectation{}
	}

	return mmGetVersion
}

// Inspect accepts an inspector function that has same arguments as the IdentityProvider.GetVersion
func (mmGetVersion *mIdentityProviderMockGetVersion) Inspect(f func()) *mIdentityProviderMockGetVersion {
	if mmGetVersion.mock.inspectFuncGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("Inspect function is already set for IdentityProviderMock.GetVersion")
	}

	mmGetVersion.mock.inspectFuncGetVersion = f

	return mmGetVersion
}

// Return sets up results that will be returned by IdentityProvider.GetVersion
func (mmGetVersion *mIdentityProviderMockGetVersion) Return(s1 string, err error) *IdentityProviderMock {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("IdentityProviderMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &IdentityProviderMockGetVersionExpectation{mock: mmGetVersion.mock}
	}
	mmGetVersion.defaultExpectation.results = &IdentityProviderMockGetVersionResults{s1, err}
	mmGetVersion.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// Set uses given function f to mock the IdentityProvider.GetVersion method
func (mmGetVersion *mIdentityProviderMockGetVersion) Set(f func() (s1 string, err error)) *IdentityProviderMock {
	if mmGetVersion.defaultExpectation != nil {
		mmGetVersion.mock.t.Fatalf("Default expectation is already set for the IdentityProvider.GetVersion method")
	}

	if len(mmGetVersion.expectations) > 0 {
		mmGetVersion.mock.t.Fatalf("Some expectations are already set for the IdentityProvider.GetVersion method")
	}

	mmGetVersion.mock.funcGetVersion = f
	mmGetVersion.mock.funcGetVersionOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// Times sets number of times IdentityProvider.GetVersion should be invoked
func (mmGetVersion *mIdentityProviderMockGetVersion) Times(n uint64) *mIdentityProviderMockGetVersion {
	if n == 0 {
		mmGetVersion.mock.t.Fatalf("Times of IdentityProviderMock.GetVersion mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetVersion.expectedInvocations, n)
	mmGetVersion.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetVersion
}

func (mmGetVersion *mIdentityProviderMockGetVersion) invocationsDone() bool {
	if len(mmGetVersion.expectations) == 0 && mmGetVersion.defaultExpectation == nil && mmGetVersion.mock.funcGetVersion == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetVersion.mock.afterGetVersionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetVersion.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetVersion implements mm_repository.IdentityProvider
func (mmGetVersion *IdentityProviderMock) GetVersion() (s1 string, err error) {
	mm_atomic.AddUint64(&mmGetVersion.beforeGetVersionCounter, 1)
	defer mm_atomic.AddUint64(&mmGetVersion.afterGetVersionCounter, 1)

	mmGetVersion.t.Helper()

	if mmGetVersion.inspectFuncGetVersion != nil {
		mmGetVersion.inspectFuncGetVersion()
	}

	if mmGetVersion.GetVersionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetVersion.GetVersionMock.defaultExpectation.Counter, 1)

		mm_results := mmGetVersion.GetVersionMock.defaultExpectation.results
		if mm_results == nil {
			mmGetVersion.t.Fatal("No results are set for the IdentityProviderMock.GetVersion")
		}
		return (*mm_results).s1, (*mm_results).err
	}
	if mmGetVersion.funcGetVersion != nil {
		return mmGetVersion.funcGetVersion()
	}
	mmGetVersion.t.Fatalf("Unexpected call to IdentityProviderMock.GetVersion.")
	return
}

// GetVersionAfterCounter returns a count of finished IdentityProviderMock.GetVersion invocations
func (mmGetVersion *IdentityProviderMock) GetVersionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.afterGetVersionCounter)
}

// GetVersionBeforeCounter returns a count of IdentityProviderMock.GetVersion invocations
func (mmGetVersion *IdentityProviderMock) GetVersionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.beforeGetVersionCounter)
}

// MinimockGetVersionDone returns true if the count of the GetVersion invocations corresponds
// the number of defined expectations
func (m *IdentityProviderMock) MinimockGetVersionDone() bool {
	if m.GetVersionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetVersionMock.invocationsDone()
}

// MinimockGetVersionInspect logs each unmet expectation
func (m *IdentityProviderMock) MinimockGetVersionInspect() {
	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to IdentityProviderMock.GetVersion")
		}
	}

	afterGetVersionCounter := mm_atomic.LoadUint64(&m.afterGetVersionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetVersionMock.defaultExpectation != nil && afterGetVersionCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetVersion at\n%s", m.GetVersionMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetVersion != nil && afterGetVersionCounter < 1 {
		m.t.Errorf("Expected call to IdentityProviderMock.GetVersion at\n%s", m.funcGetVersionOrigin)
	}

	if !m.GetVersionMock.invocationsDone() && afterGetVersionCounter > 0 {
		m.t.Errorf("Expected %d calls to IdentityProviderMock.GetVersion at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetVersionMock.expectedInvocations), m.GetVersionMock.expectedInvocationsOrigin, afterGetVersionCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *IdentityProviderMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetClientIDInspect()

			m.MinimockGetRealmInspect()

			m.MinimockGetURLInspect()

			m.MinimockGetUsersByEmailInspect()

			m.MinimockGetUsersBySearchInspect()

			m.MinimockGetVersionInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *IdentityProviderMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *IdentityProviderMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetClientIDDone() &&
		m.MinimockGetRealmDone() &&
		m.MinimockGetURLDone() &&
		m.MinimockGetUsersByEmailDone() &&
		m.MinimockGetUsersBySearchDone() &&
		m.MinimockGetVersionDone()
}
