// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/service.KeycloakDomainService -o keycloak_domain_service_mock.go -n KeycloakDomainServiceMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	identityentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	"github.com/gojuno/minimock/v3"
)

// KeycloakDomainServiceMock implements mm_service.KeycloakDomainService
type KeycloakDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetClientID          func() (s1 string)
	funcGetClientIDOrigin    string
	inspectFuncGetClientID   func()
	afterGetClientIDCounter  uint64
	beforeGetClientIDCounter uint64
	GetClientIDMock          mKeycloakDomainServiceMockGetClientID

	funcGetRealm          func() (s1 string)
	funcGetRealmOrigin    string
	inspectFuncGetRealm   func()
	afterGetRealmCounter  uint64
	beforeGetRealmCounter uint64
	GetRealmMock          mKeycloakDomainServiceMockGetRealm

	funcGetURL          func() (s1 string)
	funcGetURLOrigin    string
	inspectFuncGetURL   func()
	afterGetURLCounter  uint64
	beforeGetURLCounter uint64
	GetURLMock          mKeycloakDomainServiceMockGetURL

	funcGetUsersByEmail          func(email string) (ka1 []identityentity.KeycloakUser, err error)
	funcGetUsersByEmailOrigin    string
	inspectFuncGetUsersByEmail   func(email string)
	afterGetUsersByEmailCounter  uint64
	beforeGetUsersByEmailCounter uint64
	GetUsersByEmailMock          mKeycloakDomainServiceMockGetUsersByEmail

	funcGetUsersBySearch          func(search string) (ka1 []identityentity.KeycloakUser, err error)
	funcGetUsersBySearchOrigin    string
	inspectFuncGetUsersBySearch   func(search string)
	afterGetUsersBySearchCounter  uint64
	beforeGetUsersBySearchCounter uint64
	GetUsersBySearchMock          mKeycloakDomainServiceMockGetUsersBySearch

	funcGetVersion          func() (s1 string, err error)
	funcGetVersionOrigin    string
	inspectFuncGetVersion   func()
	afterGetVersionCounter  uint64
	beforeGetVersionCounter uint64
	GetVersionMock          mKeycloakDomainServiceMockGetVersion
}

// NewKeycloakDomainServiceMock returns a mock for mm_service.KeycloakDomainService
func NewKeycloakDomainServiceMock(t minimock.Tester) *KeycloakDomainServiceMock {
	m := &KeycloakDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetClientIDMock = mKeycloakDomainServiceMockGetClientID{mock: m}

	m.GetRealmMock = mKeycloakDomainServiceMockGetRealm{mock: m}

	m.GetURLMock = mKeycloakDomainServiceMockGetURL{mock: m}

	m.GetUsersByEmailMock = mKeycloakDomainServiceMockGetUsersByEmail{mock: m}
	m.GetUsersByEmailMock.callArgs = []*KeycloakDomainServiceMockGetUsersByEmailParams{}

	m.GetUsersBySearchMock = mKeycloakDomainServiceMockGetUsersBySearch{mock: m}
	m.GetUsersBySearchMock.callArgs = []*KeycloakDomainServiceMockGetUsersBySearchParams{}

	m.GetVersionMock = mKeycloakDomainServiceMockGetVersion{mock: m}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mKeycloakDomainServiceMockGetClientID struct {
	optional           bool
	mock               *KeycloakDomainServiceMock
	defaultExpectation *KeycloakDomainServiceMockGetClientIDExpectation
	expectations       []*KeycloakDomainServiceMockGetClientIDExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// KeycloakDomainServiceMockGetClientIDExpectation specifies expectation struct of the KeycloakDomainService.GetClientID
type KeycloakDomainServiceMockGetClientIDExpectation struct {
	mock *KeycloakDomainServiceMock

	results      *KeycloakDomainServiceMockGetClientIDResults
	returnOrigin string
	Counter      uint64
}

// KeycloakDomainServiceMockGetClientIDResults contains results of the KeycloakDomainService.GetClientID
type KeycloakDomainServiceMockGetClientIDResults struct {
	s1 string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) Optional() *mKeycloakDomainServiceMockGetClientID {
	mmGetClientID.optional = true
	return mmGetClientID
}

// Expect sets up expected params for KeycloakDomainService.GetClientID
func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) Expect() *mKeycloakDomainServiceMockGetClientID {
	if mmGetClientID.mock.funcGetClientID != nil {
		mmGetClientID.mock.t.Fatalf("KeycloakDomainServiceMock.GetClientID mock is already set by Set")
	}

	if mmGetClientID.defaultExpectation == nil {
		mmGetClientID.defaultExpectation = &KeycloakDomainServiceMockGetClientIDExpectation{}
	}

	return mmGetClientID
}

// Inspect accepts an inspector function that has same arguments as the KeycloakDomainService.GetClientID
func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) Inspect(f func()) *mKeycloakDomainServiceMockGetClientID {
	if mmGetClientID.mock.inspectFuncGetClientID != nil {
		mmGetClientID.mock.t.Fatalf("Inspect function is already set for KeycloakDomainServiceMock.GetClientID")
	}

	mmGetClientID.mock.inspectFuncGetClientID = f

	return mmGetClientID
}

// Return sets up results that will be returned by KeycloakDomainService.GetClientID
func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) Return(s1 string) *KeycloakDomainServiceMock {
	if mmGetClientID.mock.funcGetClientID != nil {
		mmGetClientID.mock.t.Fatalf("KeycloakDomainServiceMock.GetClientID mock is already set by Set")
	}

	if mmGetClientID.defaultExpectation == nil {
		mmGetClientID.defaultExpectation = &KeycloakDomainServiceMockGetClientIDExpectation{mock: mmGetClientID.mock}
	}
	mmGetClientID.defaultExpectation.results = &KeycloakDomainServiceMockGetClientIDResults{s1}
	mmGetClientID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetClientID.mock
}

// Set uses given function f to mock the KeycloakDomainService.GetClientID method
func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) Set(f func() (s1 string)) *KeycloakDomainServiceMock {
	if mmGetClientID.defaultExpectation != nil {
		mmGetClientID.mock.t.Fatalf("Default expectation is already set for the KeycloakDomainService.GetClientID method")
	}

	if len(mmGetClientID.expectations) > 0 {
		mmGetClientID.mock.t.Fatalf("Some expectations are already set for the KeycloakDomainService.GetClientID method")
	}

	mmGetClientID.mock.funcGetClientID = f
	mmGetClientID.mock.funcGetClientIDOrigin = minimock.CallerInfo(1)
	return mmGetClientID.mock
}

// Times sets number of times KeycloakDomainService.GetClientID should be invoked
func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) Times(n uint64) *mKeycloakDomainServiceMockGetClientID {
	if n == 0 {
		mmGetClientID.mock.t.Fatalf("Times of KeycloakDomainServiceMock.GetClientID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetClientID.expectedInvocations, n)
	mmGetClientID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetClientID
}

func (mmGetClientID *mKeycloakDomainServiceMockGetClientID) invocationsDone() bool {
	if len(mmGetClientID.expectations) == 0 && mmGetClientID.defaultExpectation == nil && mmGetClientID.mock.funcGetClientID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetClientID.mock.afterGetClientIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetClientID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetClientID implements mm_service.KeycloakDomainService
func (mmGetClientID *KeycloakDomainServiceMock) GetClientID() (s1 string) {
	mm_atomic.AddUint64(&mmGetClientID.beforeGetClientIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetClientID.afterGetClientIDCounter, 1)

	mmGetClientID.t.Helper()

	if mmGetClientID.inspectFuncGetClientID != nil {
		mmGetClientID.inspectFuncGetClientID()
	}

	if mmGetClientID.GetClientIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetClientID.GetClientIDMock.defaultExpectation.Counter, 1)

		mm_results := mmGetClientID.GetClientIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetClientID.t.Fatal("No results are set for the KeycloakDomainServiceMock.GetClientID")
		}
		return (*mm_results).s1
	}
	if mmGetClientID.funcGetClientID != nil {
		return mmGetClientID.funcGetClientID()
	}
	mmGetClientID.t.Fatalf("Unexpected call to KeycloakDomainServiceMock.GetClientID.")
	return
}

// GetClientIDAfterCounter returns a count of finished KeycloakDomainServiceMock.GetClientID invocations
func (mmGetClientID *KeycloakDomainServiceMock) GetClientIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetClientID.afterGetClientIDCounter)
}

// GetClientIDBeforeCounter returns a count of KeycloakDomainServiceMock.GetClientID invocations
func (mmGetClientID *KeycloakDomainServiceMock) GetClientIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetClientID.beforeGetClientIDCounter)
}

// MinimockGetClientIDDone returns true if the count of the GetClientID invocations corresponds
// the number of defined expectations
func (m *KeycloakDomainServiceMock) MinimockGetClientIDDone() bool {
	if m.GetClientIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetClientIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetClientIDMock.invocationsDone()
}

// MinimockGetClientIDInspect logs each unmet expectation
func (m *KeycloakDomainServiceMock) MinimockGetClientIDInspect() {
	for _, e := range m.GetClientIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to KeycloakDomainServiceMock.GetClientID")
		}
	}

	afterGetClientIDCounter := mm_atomic.LoadUint64(&m.afterGetClientIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetClientIDMock.defaultExpectation != nil && afterGetClientIDCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetClientID at\n%s", m.GetClientIDMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetClientID != nil && afterGetClientIDCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetClientID at\n%s", m.funcGetClientIDOrigin)
	}

	if !m.GetClientIDMock.invocationsDone() && afterGetClientIDCounter > 0 {
		m.t.Errorf("Expected %d calls to KeycloakDomainServiceMock.GetClientID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetClientIDMock.expectedInvocations), m.GetClientIDMock.expectedInvocationsOrigin, afterGetClientIDCounter)
	}
}

type mKeycloakDomainServiceMockGetRealm struct {
	optional           bool
	mock               *KeycloakDomainServiceMock
	defaultExpectation *KeycloakDomainServiceMockGetRealmExpectation
	expectations       []*KeycloakDomainServiceMockGetRealmExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// KeycloakDomainServiceMockGetRealmExpectation specifies expectation struct of the KeycloakDomainService.GetRealm
type KeycloakDomainServiceMockGetRealmExpectation struct {
	mock *KeycloakDomainServiceMock

	results      *KeycloakDomainServiceMockGetRealmResults
	returnOrigin string
	Counter      uint64
}

// KeycloakDomainServiceMockGetRealmResults contains results of the KeycloakDomainService.GetRealm
type KeycloakDomainServiceMockGetRealmResults struct {
	s1 string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) Optional() *mKeycloakDomainServiceMockGetRealm {
	mmGetRealm.optional = true
	return mmGetRealm
}

// Expect sets up expected params for KeycloakDomainService.GetRealm
func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) Expect() *mKeycloakDomainServiceMockGetRealm {
	if mmGetRealm.mock.funcGetRealm != nil {
		mmGetRealm.mock.t.Fatalf("KeycloakDomainServiceMock.GetRealm mock is already set by Set")
	}

	if mmGetRealm.defaultExpectation == nil {
		mmGetRealm.defaultExpectation = &KeycloakDomainServiceMockGetRealmExpectation{}
	}

	return mmGetRealm
}

// Inspect accepts an inspector function that has same arguments as the KeycloakDomainService.GetRealm
func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) Inspect(f func()) *mKeycloakDomainServiceMockGetRealm {
	if mmGetRealm.mock.inspectFuncGetRealm != nil {
		mmGetRealm.mock.t.Fatalf("Inspect function is already set for KeycloakDomainServiceMock.GetRealm")
	}

	mmGetRealm.mock.inspectFuncGetRealm = f

	return mmGetRealm
}

// Return sets up results that will be returned by KeycloakDomainService.GetRealm
func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) Return(s1 string) *KeycloakDomainServiceMock {
	if mmGetRealm.mock.funcGetRealm != nil {
		mmGetRealm.mock.t.Fatalf("KeycloakDomainServiceMock.GetRealm mock is already set by Set")
	}

	if mmGetRealm.defaultExpectation == nil {
		mmGetRealm.defaultExpectation = &KeycloakDomainServiceMockGetRealmExpectation{mock: mmGetRealm.mock}
	}
	mmGetRealm.defaultExpectation.results = &KeycloakDomainServiceMockGetRealmResults{s1}
	mmGetRealm.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetRealm.mock
}

// Set uses given function f to mock the KeycloakDomainService.GetRealm method
func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) Set(f func() (s1 string)) *KeycloakDomainServiceMock {
	if mmGetRealm.defaultExpectation != nil {
		mmGetRealm.mock.t.Fatalf("Default expectation is already set for the KeycloakDomainService.GetRealm method")
	}

	if len(mmGetRealm.expectations) > 0 {
		mmGetRealm.mock.t.Fatalf("Some expectations are already set for the KeycloakDomainService.GetRealm method")
	}

	mmGetRealm.mock.funcGetRealm = f
	mmGetRealm.mock.funcGetRealmOrigin = minimock.CallerInfo(1)
	return mmGetRealm.mock
}

// Times sets number of times KeycloakDomainService.GetRealm should be invoked
func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) Times(n uint64) *mKeycloakDomainServiceMockGetRealm {
	if n == 0 {
		mmGetRealm.mock.t.Fatalf("Times of KeycloakDomainServiceMock.GetRealm mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetRealm.expectedInvocations, n)
	mmGetRealm.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetRealm
}

func (mmGetRealm *mKeycloakDomainServiceMockGetRealm) invocationsDone() bool {
	if len(mmGetRealm.expectations) == 0 && mmGetRealm.defaultExpectation == nil && mmGetRealm.mock.funcGetRealm == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetRealm.mock.afterGetRealmCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetRealm.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetRealm implements mm_service.KeycloakDomainService
func (mmGetRealm *KeycloakDomainServiceMock) GetRealm() (s1 string) {
	mm_atomic.AddUint64(&mmGetRealm.beforeGetRealmCounter, 1)
	defer mm_atomic.AddUint64(&mmGetRealm.afterGetRealmCounter, 1)

	mmGetRealm.t.Helper()

	if mmGetRealm.inspectFuncGetRealm != nil {
		mmGetRealm.inspectFuncGetRealm()
	}

	if mmGetRealm.GetRealmMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetRealm.GetRealmMock.defaultExpectation.Counter, 1)

		mm_results := mmGetRealm.GetRealmMock.defaultExpectation.results
		if mm_results == nil {
			mmGetRealm.t.Fatal("No results are set for the KeycloakDomainServiceMock.GetRealm")
		}
		return (*mm_results).s1
	}
	if mmGetRealm.funcGetRealm != nil {
		return mmGetRealm.funcGetRealm()
	}
	mmGetRealm.t.Fatalf("Unexpected call to KeycloakDomainServiceMock.GetRealm.")
	return
}

// GetRealmAfterCounter returns a count of finished KeycloakDomainServiceMock.GetRealm invocations
func (mmGetRealm *KeycloakDomainServiceMock) GetRealmAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRealm.afterGetRealmCounter)
}

// GetRealmBeforeCounter returns a count of KeycloakDomainServiceMock.GetRealm invocations
func (mmGetRealm *KeycloakDomainServiceMock) GetRealmBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetRealm.beforeGetRealmCounter)
}

// MinimockGetRealmDone returns true if the count of the GetRealm invocations corresponds
// the number of defined expectations
func (m *KeycloakDomainServiceMock) MinimockGetRealmDone() bool {
	if m.GetRealmMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetRealmMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetRealmMock.invocationsDone()
}

// MinimockGetRealmInspect logs each unmet expectation
func (m *KeycloakDomainServiceMock) MinimockGetRealmInspect() {
	for _, e := range m.GetRealmMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to KeycloakDomainServiceMock.GetRealm")
		}
	}

	afterGetRealmCounter := mm_atomic.LoadUint64(&m.afterGetRealmCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetRealmMock.defaultExpectation != nil && afterGetRealmCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetRealm at\n%s", m.GetRealmMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetRealm != nil && afterGetRealmCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetRealm at\n%s", m.funcGetRealmOrigin)
	}

	if !m.GetRealmMock.invocationsDone() && afterGetRealmCounter > 0 {
		m.t.Errorf("Expected %d calls to KeycloakDomainServiceMock.GetRealm at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetRealmMock.expectedInvocations), m.GetRealmMock.expectedInvocationsOrigin, afterGetRealmCounter)
	}
}

type mKeycloakDomainServiceMockGetURL struct {
	optional           bool
	mock               *KeycloakDomainServiceMock
	defaultExpectation *KeycloakDomainServiceMockGetURLExpectation
	expectations       []*KeycloakDomainServiceMockGetURLExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// KeycloakDomainServiceMockGetURLExpectation specifies expectation struct of the KeycloakDomainService.GetURL
type KeycloakDomainServiceMockGetURLExpectation struct {
	mock *KeycloakDomainServiceMock

	results      *KeycloakDomainServiceMockGetURLResults
	returnOrigin string
	Counter      uint64
}

// KeycloakDomainServiceMockGetURLResults contains results of the KeycloakDomainService.GetURL
type KeycloakDomainServiceMockGetURLResults struct {
	s1 string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetURL *mKeycloakDomainServiceMockGetURL) Optional() *mKeycloakDomainServiceMockGetURL {
	mmGetURL.optional = true
	return mmGetURL
}

// Expect sets up expected params for KeycloakDomainService.GetURL
func (mmGetURL *mKeycloakDomainServiceMockGetURL) Expect() *mKeycloakDomainServiceMockGetURL {
	if mmGetURL.mock.funcGetURL != nil {
		mmGetURL.mock.t.Fatalf("KeycloakDomainServiceMock.GetURL mock is already set by Set")
	}

	if mmGetURL.defaultExpectation == nil {
		mmGetURL.defaultExpectation = &KeycloakDomainServiceMockGetURLExpectation{}
	}

	return mmGetURL
}

// Inspect accepts an inspector function that has same arguments as the KeycloakDomainService.GetURL
func (mmGetURL *mKeycloakDomainServiceMockGetURL) Inspect(f func()) *mKeycloakDomainServiceMockGetURL {
	if mmGetURL.mock.inspectFuncGetURL != nil {
		mmGetURL.mock.t.Fatalf("Inspect function is already set for KeycloakDomainServiceMock.GetURL")
	}

	mmGetURL.mock.inspectFuncGetURL = f

	return mmGetURL
}

// Return sets up results that will be returned by KeycloakDomainService.GetURL
func (mmGetURL *mKeycloakDomainServiceMockGetURL) Return(s1 string) *KeycloakDomainServiceMock {
	if mmGetURL.mock.funcGetURL != nil {
		mmGetURL.mock.t.Fatalf("KeycloakDomainServiceMock.GetURL mock is already set by Set")
	}

	if mmGetURL.defaultExpectation == nil {
		mmGetURL.defaultExpectation = &KeycloakDomainServiceMockGetURLExpectation{mock: mmGetURL.mock}
	}
	mmGetURL.defaultExpectation.results = &KeycloakDomainServiceMockGetURLResults{s1}
	mmGetURL.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetURL.mock
}

// Set uses given function f to mock the KeycloakDomainService.GetURL method
func (mmGetURL *mKeycloakDomainServiceMockGetURL) Set(f func() (s1 string)) *KeycloakDomainServiceMock {
	if mmGetURL.defaultExpectation != nil {
		mmGetURL.mock.t.Fatalf("Default expectation is already set for the KeycloakDomainService.GetURL method")
	}

	if len(mmGetURL.expectations) > 0 {
		mmGetURL.mock.t.Fatalf("Some expectations are already set for the KeycloakDomainService.GetURL method")
	}

	mmGetURL.mock.funcGetURL = f
	mmGetURL.mock.funcGetURLOrigin = minimock.CallerInfo(1)
	return mmGetURL.mock
}

// Times sets number of times KeycloakDomainService.GetURL should be invoked
func (mmGetURL *mKeycloakDomainServiceMockGetURL) Times(n uint64) *mKeycloakDomainServiceMockGetURL {
	if n == 0 {
		mmGetURL.mock.t.Fatalf("Times of KeycloakDomainServiceMock.GetURL mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetURL.expectedInvocations, n)
	mmGetURL.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetURL
}

func (mmGetURL *mKeycloakDomainServiceMockGetURL) invocationsDone() bool {
	if len(mmGetURL.expectations) == 0 && mmGetURL.defaultExpectation == nil && mmGetURL.mock.funcGetURL == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetURL.mock.afterGetURLCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetURL.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetURL implements mm_service.KeycloakDomainService
func (mmGetURL *KeycloakDomainServiceMock) GetURL() (s1 string) {
	mm_atomic.AddUint64(&mmGetURL.beforeGetURLCounter, 1)
	defer mm_atomic.AddUint64(&mmGetURL.afterGetURLCounter, 1)

	mmGetURL.t.Helper()

	if mmGetURL.inspectFuncGetURL != nil {
		mmGetURL.inspectFuncGetURL()
	}

	if mmGetURL.GetURLMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetURL.GetURLMock.defaultExpectation.Counter, 1)

		mm_results := mmGetURL.GetURLMock.defaultExpectation.results
		if mm_results == nil {
			mmGetURL.t.Fatal("No results are set for the KeycloakDomainServiceMock.GetURL")
		}
		return (*mm_results).s1
	}
	if mmGetURL.funcGetURL != nil {
		return mmGetURL.funcGetURL()
	}
	mmGetURL.t.Fatalf("Unexpected call to KeycloakDomainServiceMock.GetURL.")
	return
}

// GetURLAfterCounter returns a count of finished KeycloakDomainServiceMock.GetURL invocations
func (mmGetURL *KeycloakDomainServiceMock) GetURLAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetURL.afterGetURLCounter)
}

// GetURLBeforeCounter returns a count of KeycloakDomainServiceMock.GetURL invocations
func (mmGetURL *KeycloakDomainServiceMock) GetURLBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetURL.beforeGetURLCounter)
}

// MinimockGetURLDone returns true if the count of the GetURL invocations corresponds
// the number of defined expectations
func (m *KeycloakDomainServiceMock) MinimockGetURLDone() bool {
	if m.GetURLMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetURLMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetURLMock.invocationsDone()
}

// MinimockGetURLInspect logs each unmet expectation
func (m *KeycloakDomainServiceMock) MinimockGetURLInspect() {
	for _, e := range m.GetURLMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to KeycloakDomainServiceMock.GetURL")
		}
	}

	afterGetURLCounter := mm_atomic.LoadUint64(&m.afterGetURLCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetURLMock.defaultExpectation != nil && afterGetURLCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetURL at\n%s", m.GetURLMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetURL != nil && afterGetURLCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetURL at\n%s", m.funcGetURLOrigin)
	}

	if !m.GetURLMock.invocationsDone() && afterGetURLCounter > 0 {
		m.t.Errorf("Expected %d calls to KeycloakDomainServiceMock.GetURL at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetURLMock.expectedInvocations), m.GetURLMock.expectedInvocationsOrigin, afterGetURLCounter)
	}
}

type mKeycloakDomainServiceMockGetUsersByEmail struct {
	optional           bool
	mock               *KeycloakDomainServiceMock
	defaultExpectation *KeycloakDomainServiceMockGetUsersByEmailExpectation
	expectations       []*KeycloakDomainServiceMockGetUsersByEmailExpectation

	callArgs []*KeycloakDomainServiceMockGetUsersByEmailParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// KeycloakDomainServiceMockGetUsersByEmailExpectation specifies expectation struct of the KeycloakDomainService.GetUsersByEmail
type KeycloakDomainServiceMockGetUsersByEmailExpectation struct {
	mock               *KeycloakDomainServiceMock
	params             *KeycloakDomainServiceMockGetUsersByEmailParams
	paramPtrs          *KeycloakDomainServiceMockGetUsersByEmailParamPtrs
	expectationOrigins KeycloakDomainServiceMockGetUsersByEmailExpectationOrigins
	results            *KeycloakDomainServiceMockGetUsersByEmailResults
	returnOrigin       string
	Counter            uint64
}

// KeycloakDomainServiceMockGetUsersByEmailParams contains parameters of the KeycloakDomainService.GetUsersByEmail
type KeycloakDomainServiceMockGetUsersByEmailParams struct {
	email string
}

// KeycloakDomainServiceMockGetUsersByEmailParamPtrs contains pointers to parameters of the KeycloakDomainService.GetUsersByEmail
type KeycloakDomainServiceMockGetUsersByEmailParamPtrs struct {
	email *string
}

// KeycloakDomainServiceMockGetUsersByEmailResults contains results of the KeycloakDomainService.GetUsersByEmail
type KeycloakDomainServiceMockGetUsersByEmailResults struct {
	ka1 []identityentity.KeycloakUser
	err error
}

// KeycloakDomainServiceMockGetUsersByEmailOrigins contains origins of expectations of the KeycloakDomainService.GetUsersByEmail
type KeycloakDomainServiceMockGetUsersByEmailExpectationOrigins struct {
	origin      string
	originEmail string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Optional() *mKeycloakDomainServiceMockGetUsersByEmail {
	mmGetUsersByEmail.optional = true
	return mmGetUsersByEmail
}

// Expect sets up expected params for KeycloakDomainService.GetUsersByEmail
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Expect(email string) *mKeycloakDomainServiceMockGetUsersByEmail {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersByEmail mock is already set by Set")
	}

	if mmGetUsersByEmail.defaultExpectation == nil {
		mmGetUsersByEmail.defaultExpectation = &KeycloakDomainServiceMockGetUsersByEmailExpectation{}
	}

	if mmGetUsersByEmail.defaultExpectation.paramPtrs != nil {
		mmGetUsersByEmail.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersByEmail mock is already set by ExpectParams functions")
	}

	mmGetUsersByEmail.defaultExpectation.params = &KeycloakDomainServiceMockGetUsersByEmailParams{email}
	mmGetUsersByEmail.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersByEmail.expectations {
		if minimock.Equal(e.params, mmGetUsersByEmail.defaultExpectation.params) {
			mmGetUsersByEmail.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersByEmail.defaultExpectation.params)
		}
	}

	return mmGetUsersByEmail
}

// ExpectEmailParam1 sets up expected param email for KeycloakDomainService.GetUsersByEmail
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) ExpectEmailParam1(email string) *mKeycloakDomainServiceMockGetUsersByEmail {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersByEmail mock is already set by Set")
	}

	if mmGetUsersByEmail.defaultExpectation == nil {
		mmGetUsersByEmail.defaultExpectation = &KeycloakDomainServiceMockGetUsersByEmailExpectation{}
	}

	if mmGetUsersByEmail.defaultExpectation.params != nil {
		mmGetUsersByEmail.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersByEmail mock is already set by Expect")
	}

	if mmGetUsersByEmail.defaultExpectation.paramPtrs == nil {
		mmGetUsersByEmail.defaultExpectation.paramPtrs = &KeycloakDomainServiceMockGetUsersByEmailParamPtrs{}
	}
	mmGetUsersByEmail.defaultExpectation.paramPtrs.email = &email
	mmGetUsersByEmail.defaultExpectation.expectationOrigins.originEmail = minimock.CallerInfo(1)

	return mmGetUsersByEmail
}

// Inspect accepts an inspector function that has same arguments as the KeycloakDomainService.GetUsersByEmail
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Inspect(f func(email string)) *mKeycloakDomainServiceMockGetUsersByEmail {
	if mmGetUsersByEmail.mock.inspectFuncGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("Inspect function is already set for KeycloakDomainServiceMock.GetUsersByEmail")
	}

	mmGetUsersByEmail.mock.inspectFuncGetUsersByEmail = f

	return mmGetUsersByEmail
}

// Return sets up results that will be returned by KeycloakDomainService.GetUsersByEmail
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Return(ka1 []identityentity.KeycloakUser, err error) *KeycloakDomainServiceMock {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersByEmail mock is already set by Set")
	}

	if mmGetUsersByEmail.defaultExpectation == nil {
		mmGetUsersByEmail.defaultExpectation = &KeycloakDomainServiceMockGetUsersByEmailExpectation{mock: mmGetUsersByEmail.mock}
	}
	mmGetUsersByEmail.defaultExpectation.results = &KeycloakDomainServiceMockGetUsersByEmailResults{ka1, err}
	mmGetUsersByEmail.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersByEmail.mock
}

// Set uses given function f to mock the KeycloakDomainService.GetUsersByEmail method
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Set(f func(email string) (ka1 []identityentity.KeycloakUser, err error)) *KeycloakDomainServiceMock {
	if mmGetUsersByEmail.defaultExpectation != nil {
		mmGetUsersByEmail.mock.t.Fatalf("Default expectation is already set for the KeycloakDomainService.GetUsersByEmail method")
	}

	if len(mmGetUsersByEmail.expectations) > 0 {
		mmGetUsersByEmail.mock.t.Fatalf("Some expectations are already set for the KeycloakDomainService.GetUsersByEmail method")
	}

	mmGetUsersByEmail.mock.funcGetUsersByEmail = f
	mmGetUsersByEmail.mock.funcGetUsersByEmailOrigin = minimock.CallerInfo(1)
	return mmGetUsersByEmail.mock
}

// When sets expectation for the KeycloakDomainService.GetUsersByEmail which will trigger the result defined by the following
// Then helper
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) When(email string) *KeycloakDomainServiceMockGetUsersByEmailExpectation {
	if mmGetUsersByEmail.mock.funcGetUsersByEmail != nil {
		mmGetUsersByEmail.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersByEmail mock is already set by Set")
	}

	expectation := &KeycloakDomainServiceMockGetUsersByEmailExpectation{
		mock:               mmGetUsersByEmail.mock,
		params:             &KeycloakDomainServiceMockGetUsersByEmailParams{email},
		expectationOrigins: KeycloakDomainServiceMockGetUsersByEmailExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersByEmail.expectations = append(mmGetUsersByEmail.expectations, expectation)
	return expectation
}

// Then sets up KeycloakDomainService.GetUsersByEmail return parameters for the expectation previously defined by the When method
func (e *KeycloakDomainServiceMockGetUsersByEmailExpectation) Then(ka1 []identityentity.KeycloakUser, err error) *KeycloakDomainServiceMock {
	e.results = &KeycloakDomainServiceMockGetUsersByEmailResults{ka1, err}
	return e.mock
}

// Times sets number of times KeycloakDomainService.GetUsersByEmail should be invoked
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Times(n uint64) *mKeycloakDomainServiceMockGetUsersByEmail {
	if n == 0 {
		mmGetUsersByEmail.mock.t.Fatalf("Times of KeycloakDomainServiceMock.GetUsersByEmail mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersByEmail.expectedInvocations, n)
	mmGetUsersByEmail.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersByEmail
}

func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) invocationsDone() bool {
	if len(mmGetUsersByEmail.expectations) == 0 && mmGetUsersByEmail.defaultExpectation == nil && mmGetUsersByEmail.mock.funcGetUsersByEmail == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersByEmail.mock.afterGetUsersByEmailCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersByEmail.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersByEmail implements mm_service.KeycloakDomainService
func (mmGetUsersByEmail *KeycloakDomainServiceMock) GetUsersByEmail(email string) (ka1 []identityentity.KeycloakUser, err error) {
	mm_atomic.AddUint64(&mmGetUsersByEmail.beforeGetUsersByEmailCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersByEmail.afterGetUsersByEmailCounter, 1)

	mmGetUsersByEmail.t.Helper()

	if mmGetUsersByEmail.inspectFuncGetUsersByEmail != nil {
		mmGetUsersByEmail.inspectFuncGetUsersByEmail(email)
	}

	mm_params := KeycloakDomainServiceMockGetUsersByEmailParams{email}

	// Record call args
	mmGetUsersByEmail.GetUsersByEmailMock.mutex.Lock()
	mmGetUsersByEmail.GetUsersByEmailMock.callArgs = append(mmGetUsersByEmail.GetUsersByEmailMock.callArgs, &mm_params)
	mmGetUsersByEmail.GetUsersByEmailMock.mutex.Unlock()

	for _, e := range mmGetUsersByEmail.GetUsersByEmailMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ka1, e.results.err
		}
	}

	if mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.paramPtrs

		mm_got := KeycloakDomainServiceMockGetUsersByEmailParams{email}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.email != nil && !minimock.Equal(*mm_want_ptrs.email, mm_got.email) {
				mmGetUsersByEmail.t.Errorf("KeycloakDomainServiceMock.GetUsersByEmail got unexpected parameter email, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.expectationOrigins.originEmail, *mm_want_ptrs.email, mm_got.email, minimock.Diff(*mm_want_ptrs.email, mm_got.email))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersByEmail.t.Errorf("KeycloakDomainServiceMock.GetUsersByEmail got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersByEmail.GetUsersByEmailMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersByEmail.t.Fatal("No results are set for the KeycloakDomainServiceMock.GetUsersByEmail")
		}
		return (*mm_results).ka1, (*mm_results).err
	}
	if mmGetUsersByEmail.funcGetUsersByEmail != nil {
		return mmGetUsersByEmail.funcGetUsersByEmail(email)
	}
	mmGetUsersByEmail.t.Fatalf("Unexpected call to KeycloakDomainServiceMock.GetUsersByEmail. %v", email)
	return
}

// GetUsersByEmailAfterCounter returns a count of finished KeycloakDomainServiceMock.GetUsersByEmail invocations
func (mmGetUsersByEmail *KeycloakDomainServiceMock) GetUsersByEmailAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByEmail.afterGetUsersByEmailCounter)
}

// GetUsersByEmailBeforeCounter returns a count of KeycloakDomainServiceMock.GetUsersByEmail invocations
func (mmGetUsersByEmail *KeycloakDomainServiceMock) GetUsersByEmailBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersByEmail.beforeGetUsersByEmailCounter)
}

// Calls returns a list of arguments used in each call to KeycloakDomainServiceMock.GetUsersByEmail.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersByEmail *mKeycloakDomainServiceMockGetUsersByEmail) Calls() []*KeycloakDomainServiceMockGetUsersByEmailParams {
	mmGetUsersByEmail.mutex.RLock()

	argCopy := make([]*KeycloakDomainServiceMockGetUsersByEmailParams, len(mmGetUsersByEmail.callArgs))
	copy(argCopy, mmGetUsersByEmail.callArgs)

	mmGetUsersByEmail.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersByEmailDone returns true if the count of the GetUsersByEmail invocations corresponds
// the number of defined expectations
func (m *KeycloakDomainServiceMock) MinimockGetUsersByEmailDone() bool {
	if m.GetUsersByEmailMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersByEmailMock.invocationsDone()
}

// MinimockGetUsersByEmailInspect logs each unmet expectation
func (m *KeycloakDomainServiceMock) MinimockGetUsersByEmailInspect() {
	for _, e := range m.GetUsersByEmailMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersByEmail at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersByEmailCounter := mm_atomic.LoadUint64(&m.afterGetUsersByEmailCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersByEmailMock.defaultExpectation != nil && afterGetUsersByEmailCounter < 1 {
		if m.GetUsersByEmailMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersByEmail at\n%s", m.GetUsersByEmailMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersByEmail at\n%s with params: %#v", m.GetUsersByEmailMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersByEmailMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersByEmail != nil && afterGetUsersByEmailCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersByEmail at\n%s", m.funcGetUsersByEmailOrigin)
	}

	if !m.GetUsersByEmailMock.invocationsDone() && afterGetUsersByEmailCounter > 0 {
		m.t.Errorf("Expected %d calls to KeycloakDomainServiceMock.GetUsersByEmail at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersByEmailMock.expectedInvocations), m.GetUsersByEmailMock.expectedInvocationsOrigin, afterGetUsersByEmailCounter)
	}
}

type mKeycloakDomainServiceMockGetUsersBySearch struct {
	optional           bool
	mock               *KeycloakDomainServiceMock
	defaultExpectation *KeycloakDomainServiceMockGetUsersBySearchExpectation
	expectations       []*KeycloakDomainServiceMockGetUsersBySearchExpectation

	callArgs []*KeycloakDomainServiceMockGetUsersBySearchParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// KeycloakDomainServiceMockGetUsersBySearchExpectation specifies expectation struct of the KeycloakDomainService.GetUsersBySearch
type KeycloakDomainServiceMockGetUsersBySearchExpectation struct {
	mock               *KeycloakDomainServiceMock
	params             *KeycloakDomainServiceMockGetUsersBySearchParams
	paramPtrs          *KeycloakDomainServiceMockGetUsersBySearchParamPtrs
	expectationOrigins KeycloakDomainServiceMockGetUsersBySearchExpectationOrigins
	results            *KeycloakDomainServiceMockGetUsersBySearchResults
	returnOrigin       string
	Counter            uint64
}

// KeycloakDomainServiceMockGetUsersBySearchParams contains parameters of the KeycloakDomainService.GetUsersBySearch
type KeycloakDomainServiceMockGetUsersBySearchParams struct {
	search string
}

// KeycloakDomainServiceMockGetUsersBySearchParamPtrs contains pointers to parameters of the KeycloakDomainService.GetUsersBySearch
type KeycloakDomainServiceMockGetUsersBySearchParamPtrs struct {
	search *string
}

// KeycloakDomainServiceMockGetUsersBySearchResults contains results of the KeycloakDomainService.GetUsersBySearch
type KeycloakDomainServiceMockGetUsersBySearchResults struct {
	ka1 []identityentity.KeycloakUser
	err error
}

// KeycloakDomainServiceMockGetUsersBySearchOrigins contains origins of expectations of the KeycloakDomainService.GetUsersBySearch
type KeycloakDomainServiceMockGetUsersBySearchExpectationOrigins struct {
	origin       string
	originSearch string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Optional() *mKeycloakDomainServiceMockGetUsersBySearch {
	mmGetUsersBySearch.optional = true
	return mmGetUsersBySearch
}

// Expect sets up expected params for KeycloakDomainService.GetUsersBySearch
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Expect(search string) *mKeycloakDomainServiceMockGetUsersBySearch {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersBySearch mock is already set by Set")
	}

	if mmGetUsersBySearch.defaultExpectation == nil {
		mmGetUsersBySearch.defaultExpectation = &KeycloakDomainServiceMockGetUsersBySearchExpectation{}
	}

	if mmGetUsersBySearch.defaultExpectation.paramPtrs != nil {
		mmGetUsersBySearch.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersBySearch mock is already set by ExpectParams functions")
	}

	mmGetUsersBySearch.defaultExpectation.params = &KeycloakDomainServiceMockGetUsersBySearchParams{search}
	mmGetUsersBySearch.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetUsersBySearch.expectations {
		if minimock.Equal(e.params, mmGetUsersBySearch.defaultExpectation.params) {
			mmGetUsersBySearch.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetUsersBySearch.defaultExpectation.params)
		}
	}

	return mmGetUsersBySearch
}

// ExpectSearchParam1 sets up expected param search for KeycloakDomainService.GetUsersBySearch
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) ExpectSearchParam1(search string) *mKeycloakDomainServiceMockGetUsersBySearch {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersBySearch mock is already set by Set")
	}

	if mmGetUsersBySearch.defaultExpectation == nil {
		mmGetUsersBySearch.defaultExpectation = &KeycloakDomainServiceMockGetUsersBySearchExpectation{}
	}

	if mmGetUsersBySearch.defaultExpectation.params != nil {
		mmGetUsersBySearch.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersBySearch mock is already set by Expect")
	}

	if mmGetUsersBySearch.defaultExpectation.paramPtrs == nil {
		mmGetUsersBySearch.defaultExpectation.paramPtrs = &KeycloakDomainServiceMockGetUsersBySearchParamPtrs{}
	}
	mmGetUsersBySearch.defaultExpectation.paramPtrs.search = &search
	mmGetUsersBySearch.defaultExpectation.expectationOrigins.originSearch = minimock.CallerInfo(1)

	return mmGetUsersBySearch
}

// Inspect accepts an inspector function that has same arguments as the KeycloakDomainService.GetUsersBySearch
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Inspect(f func(search string)) *mKeycloakDomainServiceMockGetUsersBySearch {
	if mmGetUsersBySearch.mock.inspectFuncGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("Inspect function is already set for KeycloakDomainServiceMock.GetUsersBySearch")
	}

	mmGetUsersBySearch.mock.inspectFuncGetUsersBySearch = f

	return mmGetUsersBySearch
}

// Return sets up results that will be returned by KeycloakDomainService.GetUsersBySearch
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Return(ka1 []identityentity.KeycloakUser, err error) *KeycloakDomainServiceMock {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersBySearch mock is already set by Set")
	}

	if mmGetUsersBySearch.defaultExpectation == nil {
		mmGetUsersBySearch.defaultExpectation = &KeycloakDomainServiceMockGetUsersBySearchExpectation{mock: mmGetUsersBySearch.mock}
	}
	mmGetUsersBySearch.defaultExpectation.results = &KeycloakDomainServiceMockGetUsersBySearchResults{ka1, err}
	mmGetUsersBySearch.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetUsersBySearch.mock
}

// Set uses given function f to mock the KeycloakDomainService.GetUsersBySearch method
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Set(f func(search string) (ka1 []identityentity.KeycloakUser, err error)) *KeycloakDomainServiceMock {
	if mmGetUsersBySearch.defaultExpectation != nil {
		mmGetUsersBySearch.mock.t.Fatalf("Default expectation is already set for the KeycloakDomainService.GetUsersBySearch method")
	}

	if len(mmGetUsersBySearch.expectations) > 0 {
		mmGetUsersBySearch.mock.t.Fatalf("Some expectations are already set for the KeycloakDomainService.GetUsersBySearch method")
	}

	mmGetUsersBySearch.mock.funcGetUsersBySearch = f
	mmGetUsersBySearch.mock.funcGetUsersBySearchOrigin = minimock.CallerInfo(1)
	return mmGetUsersBySearch.mock
}

// When sets expectation for the KeycloakDomainService.GetUsersBySearch which will trigger the result defined by the following
// Then helper
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) When(search string) *KeycloakDomainServiceMockGetUsersBySearchExpectation {
	if mmGetUsersBySearch.mock.funcGetUsersBySearch != nil {
		mmGetUsersBySearch.mock.t.Fatalf("KeycloakDomainServiceMock.GetUsersBySearch mock is already set by Set")
	}

	expectation := &KeycloakDomainServiceMockGetUsersBySearchExpectation{
		mock:               mmGetUsersBySearch.mock,
		params:             &KeycloakDomainServiceMockGetUsersBySearchParams{search},
		expectationOrigins: KeycloakDomainServiceMockGetUsersBySearchExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetUsersBySearch.expectations = append(mmGetUsersBySearch.expectations, expectation)
	return expectation
}

// Then sets up KeycloakDomainService.GetUsersBySearch return parameters for the expectation previously defined by the When method
func (e *KeycloakDomainServiceMockGetUsersBySearchExpectation) Then(ka1 []identityentity.KeycloakUser, err error) *KeycloakDomainServiceMock {
	e.results = &KeycloakDomainServiceMockGetUsersBySearchResults{ka1, err}
	return e.mock
}

// Times sets number of times KeycloakDomainService.GetUsersBySearch should be invoked
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Times(n uint64) *mKeycloakDomainServiceMockGetUsersBySearch {
	if n == 0 {
		mmGetUsersBySearch.mock.t.Fatalf("Times of KeycloakDomainServiceMock.GetUsersBySearch mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetUsersBySearch.expectedInvocations, n)
	mmGetUsersBySearch.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetUsersBySearch
}

func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) invocationsDone() bool {
	if len(mmGetUsersBySearch.expectations) == 0 && mmGetUsersBySearch.defaultExpectation == nil && mmGetUsersBySearch.mock.funcGetUsersBySearch == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetUsersBySearch.mock.afterGetUsersBySearchCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetUsersBySearch.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetUsersBySearch implements mm_service.KeycloakDomainService
func (mmGetUsersBySearch *KeycloakDomainServiceMock) GetUsersBySearch(search string) (ka1 []identityentity.KeycloakUser, err error) {
	mm_atomic.AddUint64(&mmGetUsersBySearch.beforeGetUsersBySearchCounter, 1)
	defer mm_atomic.AddUint64(&mmGetUsersBySearch.afterGetUsersBySearchCounter, 1)

	mmGetUsersBySearch.t.Helper()

	if mmGetUsersBySearch.inspectFuncGetUsersBySearch != nil {
		mmGetUsersBySearch.inspectFuncGetUsersBySearch(search)
	}

	mm_params := KeycloakDomainServiceMockGetUsersBySearchParams{search}

	// Record call args
	mmGetUsersBySearch.GetUsersBySearchMock.mutex.Lock()
	mmGetUsersBySearch.GetUsersBySearchMock.callArgs = append(mmGetUsersBySearch.GetUsersBySearchMock.callArgs, &mm_params)
	mmGetUsersBySearch.GetUsersBySearchMock.mutex.Unlock()

	for _, e := range mmGetUsersBySearch.GetUsersBySearchMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ka1, e.results.err
		}
	}

	if mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.Counter, 1)
		mm_want := mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.params
		mm_want_ptrs := mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.paramPtrs

		mm_got := KeycloakDomainServiceMockGetUsersBySearchParams{search}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.search != nil && !minimock.Equal(*mm_want_ptrs.search, mm_got.search) {
				mmGetUsersBySearch.t.Errorf("KeycloakDomainServiceMock.GetUsersBySearch got unexpected parameter search, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.expectationOrigins.originSearch, *mm_want_ptrs.search, mm_got.search, minimock.Diff(*mm_want_ptrs.search, mm_got.search))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetUsersBySearch.t.Errorf("KeycloakDomainServiceMock.GetUsersBySearch got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetUsersBySearch.GetUsersBySearchMock.defaultExpectation.results
		if mm_results == nil {
			mmGetUsersBySearch.t.Fatal("No results are set for the KeycloakDomainServiceMock.GetUsersBySearch")
		}
		return (*mm_results).ka1, (*mm_results).err
	}
	if mmGetUsersBySearch.funcGetUsersBySearch != nil {
		return mmGetUsersBySearch.funcGetUsersBySearch(search)
	}
	mmGetUsersBySearch.t.Fatalf("Unexpected call to KeycloakDomainServiceMock.GetUsersBySearch. %v", search)
	return
}

// GetUsersBySearchAfterCounter returns a count of finished KeycloakDomainServiceMock.GetUsersBySearch invocations
func (mmGetUsersBySearch *KeycloakDomainServiceMock) GetUsersBySearchAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersBySearch.afterGetUsersBySearchCounter)
}

// GetUsersBySearchBeforeCounter returns a count of KeycloakDomainServiceMock.GetUsersBySearch invocations
func (mmGetUsersBySearch *KeycloakDomainServiceMock) GetUsersBySearchBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetUsersBySearch.beforeGetUsersBySearchCounter)
}

// Calls returns a list of arguments used in each call to KeycloakDomainServiceMock.GetUsersBySearch.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetUsersBySearch *mKeycloakDomainServiceMockGetUsersBySearch) Calls() []*KeycloakDomainServiceMockGetUsersBySearchParams {
	mmGetUsersBySearch.mutex.RLock()

	argCopy := make([]*KeycloakDomainServiceMockGetUsersBySearchParams, len(mmGetUsersBySearch.callArgs))
	copy(argCopy, mmGetUsersBySearch.callArgs)

	mmGetUsersBySearch.mutex.RUnlock()

	return argCopy
}

// MinimockGetUsersBySearchDone returns true if the count of the GetUsersBySearch invocations corresponds
// the number of defined expectations
func (m *KeycloakDomainServiceMock) MinimockGetUsersBySearchDone() bool {
	if m.GetUsersBySearchMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetUsersBySearchMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetUsersBySearchMock.invocationsDone()
}

// MinimockGetUsersBySearchInspect logs each unmet expectation
func (m *KeycloakDomainServiceMock) MinimockGetUsersBySearchInspect() {
	for _, e := range m.GetUsersBySearchMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersBySearch at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetUsersBySearchCounter := mm_atomic.LoadUint64(&m.afterGetUsersBySearchCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetUsersBySearchMock.defaultExpectation != nil && afterGetUsersBySearchCounter < 1 {
		if m.GetUsersBySearchMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersBySearch at\n%s", m.GetUsersBySearchMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersBySearch at\n%s with params: %#v", m.GetUsersBySearchMock.defaultExpectation.expectationOrigins.origin, *m.GetUsersBySearchMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetUsersBySearch != nil && afterGetUsersBySearchCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetUsersBySearch at\n%s", m.funcGetUsersBySearchOrigin)
	}

	if !m.GetUsersBySearchMock.invocationsDone() && afterGetUsersBySearchCounter > 0 {
		m.t.Errorf("Expected %d calls to KeycloakDomainServiceMock.GetUsersBySearch at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetUsersBySearchMock.expectedInvocations), m.GetUsersBySearchMock.expectedInvocationsOrigin, afterGetUsersBySearchCounter)
	}
}

type mKeycloakDomainServiceMockGetVersion struct {
	optional           bool
	mock               *KeycloakDomainServiceMock
	defaultExpectation *KeycloakDomainServiceMockGetVersionExpectation
	expectations       []*KeycloakDomainServiceMockGetVersionExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// KeycloakDomainServiceMockGetVersionExpectation specifies expectation struct of the KeycloakDomainService.GetVersion
type KeycloakDomainServiceMockGetVersionExpectation struct {
	mock *KeycloakDomainServiceMock

	results      *KeycloakDomainServiceMockGetVersionResults
	returnOrigin string
	Counter      uint64
}

// KeycloakDomainServiceMockGetVersionResults contains results of the KeycloakDomainService.GetVersion
type KeycloakDomainServiceMockGetVersionResults struct {
	s1  string
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) Optional() *mKeycloakDomainServiceMockGetVersion {
	mmGetVersion.optional = true
	return mmGetVersion
}

// Expect sets up expected params for KeycloakDomainService.GetVersion
func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) Expect() *mKeycloakDomainServiceMockGetVersion {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("KeycloakDomainServiceMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &KeycloakDomainServiceMockGetVersionExpectation{}
	}

	return mmGetVersion
}

// Inspect accepts an inspector function that has same arguments as the KeycloakDomainService.GetVersion
func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) Inspect(f func()) *mKeycloakDomainServiceMockGetVersion {
	if mmGetVersion.mock.inspectFuncGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("Inspect function is already set for KeycloakDomainServiceMock.GetVersion")
	}

	mmGetVersion.mock.inspectFuncGetVersion = f

	return mmGetVersion
}

// Return sets up results that will be returned by KeycloakDomainService.GetVersion
func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) Return(s1 string, err error) *KeycloakDomainServiceMock {
	if mmGetVersion.mock.funcGetVersion != nil {
		mmGetVersion.mock.t.Fatalf("KeycloakDomainServiceMock.GetVersion mock is already set by Set")
	}

	if mmGetVersion.defaultExpectation == nil {
		mmGetVersion.defaultExpectation = &KeycloakDomainServiceMockGetVersionExpectation{mock: mmGetVersion.mock}
	}
	mmGetVersion.defaultExpectation.results = &KeycloakDomainServiceMockGetVersionResults{s1, err}
	mmGetVersion.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// Set uses given function f to mock the KeycloakDomainService.GetVersion method
func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) Set(f func() (s1 string, err error)) *KeycloakDomainServiceMock {
	if mmGetVersion.defaultExpectation != nil {
		mmGetVersion.mock.t.Fatalf("Default expectation is already set for the KeycloakDomainService.GetVersion method")
	}

	if len(mmGetVersion.expectations) > 0 {
		mmGetVersion.mock.t.Fatalf("Some expectations are already set for the KeycloakDomainService.GetVersion method")
	}

	mmGetVersion.mock.funcGetVersion = f
	mmGetVersion.mock.funcGetVersionOrigin = minimock.CallerInfo(1)
	return mmGetVersion.mock
}

// Times sets number of times KeycloakDomainService.GetVersion should be invoked
func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) Times(n uint64) *mKeycloakDomainServiceMockGetVersion {
	if n == 0 {
		mmGetVersion.mock.t.Fatalf("Times of KeycloakDomainServiceMock.GetVersion mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetVersion.expectedInvocations, n)
	mmGetVersion.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetVersion
}

func (mmGetVersion *mKeycloakDomainServiceMockGetVersion) invocationsDone() bool {
	if len(mmGetVersion.expectations) == 0 && mmGetVersion.defaultExpectation == nil && mmGetVersion.mock.funcGetVersion == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetVersion.mock.afterGetVersionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetVersion.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetVersion implements mm_service.KeycloakDomainService
func (mmGetVersion *KeycloakDomainServiceMock) GetVersion() (s1 string, err error) {
	mm_atomic.AddUint64(&mmGetVersion.beforeGetVersionCounter, 1)
	defer mm_atomic.AddUint64(&mmGetVersion.afterGetVersionCounter, 1)

	mmGetVersion.t.Helper()

	if mmGetVersion.inspectFuncGetVersion != nil {
		mmGetVersion.inspectFuncGetVersion()
	}

	if mmGetVersion.GetVersionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetVersion.GetVersionMock.defaultExpectation.Counter, 1)

		mm_results := mmGetVersion.GetVersionMock.defaultExpectation.results
		if mm_results == nil {
			mmGetVersion.t.Fatal("No results are set for the KeycloakDomainServiceMock.GetVersion")
		}
		return (*mm_results).s1, (*mm_results).err
	}
	if mmGetVersion.funcGetVersion != nil {
		return mmGetVersion.funcGetVersion()
	}
	mmGetVersion.t.Fatalf("Unexpected call to KeycloakDomainServiceMock.GetVersion.")
	return
}

// GetVersionAfterCounter returns a count of finished KeycloakDomainServiceMock.GetVersion invocations
func (mmGetVersion *KeycloakDomainServiceMock) GetVersionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.afterGetVersionCounter)
}

// GetVersionBeforeCounter returns a count of KeycloakDomainServiceMock.GetVersion invocations
func (mmGetVersion *KeycloakDomainServiceMock) GetVersionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetVersion.beforeGetVersionCounter)
}

// MinimockGetVersionDone returns true if the count of the GetVersion invocations corresponds
// the number of defined expectations
func (m *KeycloakDomainServiceMock) MinimockGetVersionDone() bool {
	if m.GetVersionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetVersionMock.invocationsDone()
}

// MinimockGetVersionInspect logs each unmet expectation
func (m *KeycloakDomainServiceMock) MinimockGetVersionInspect() {
	for _, e := range m.GetVersionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to KeycloakDomainServiceMock.GetVersion")
		}
	}

	afterGetVersionCounter := mm_atomic.LoadUint64(&m.afterGetVersionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetVersionMock.defaultExpectation != nil && afterGetVersionCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetVersion at\n%s", m.GetVersionMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetVersion != nil && afterGetVersionCounter < 1 {
		m.t.Errorf("Expected call to KeycloakDomainServiceMock.GetVersion at\n%s", m.funcGetVersionOrigin)
	}

	if !m.GetVersionMock.invocationsDone() && afterGetVersionCounter > 0 {
		m.t.Errorf("Expected %d calls to KeycloakDomainServiceMock.GetVersion at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetVersionMock.expectedInvocations), m.GetVersionMock.expectedInvocationsOrigin, afterGetVersionCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *KeycloakDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetClientIDInspect()

			m.MinimockGetRealmInspect()

			m.MinimockGetURLInspect()

			m.MinimockGetUsersByEmailInspect()

			m.MinimockGetUsersBySearchInspect()

			m.MinimockGetVersionInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *KeycloakDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *KeycloakDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetClientIDDone() &&
		m.MinimockGetRealmDone() &&
		m.MinimockGetURLDone() &&
		m.MinimockGetUsersByEmailDone() &&
		m.MinimockGetUsersBySearchDone() &&
		m.MinimockGetVersionDone()
}
