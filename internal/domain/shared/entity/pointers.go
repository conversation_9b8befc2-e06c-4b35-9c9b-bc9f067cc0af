package entity

// StringPtrOrNil returns a pointer to the string. If the string is empty, returns nil.
func StringPtrOrNil(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// StringPtr returns a pointer to the string always, even for empty strings.
func StringPtr(s string) *string {
	return &s
}

// BoolPtr returns a pointer to bool.
func BoolPtr(b bool) *bool {
	return &b
}

// BoolPtrOrNil returns a pointer to bool or nil for false values.
func BoolPtrOrNil(b bool) *bool {
	if !b {
		return nil
	}
	return &b
}

// Int64Ptr returns a pointer to int64.
func Int64Ptr(i int64) *int64 {
	return &i
}

// Int64PtrOrNil returns a pointer to int64 or nil for zero values.
func Int64PtrOrNil(i int64) *int64 {
	if i == 0 {
		return nil
	}
	return &i
}

// StringValueOrEmpty returns the string value or empty string if the pointer is nil.
func StringValueOrEmpty(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// BoolValueOrFalse returns the bool value or false if the pointer is nil.
func BoolValueOrFalse(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

// Int64ValueOrZero returns the int64 value or 0 if the pointer is nil.
func Int64ValueOrZero(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}
