package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSlicePtr(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		expected []string
	}{
		{
			name:     "empty slice returns pointer to empty slice",
			input:    []string{},
			expected: []string{},
		},
		{
			name:     "nil slice returns pointer to nil slice",
			input:    nil,
			expected: nil,
		},
		{
			name:     "non-empty slice returns pointer",
			input:    []string{"a", "b"},
			expected: []string{"a", "b"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SlicePtr(tt.input)
			assert.NotNil(t, result)
			if tt.expected == nil {
				assert.Nil(t, *result)
			} else {
				assert.Equal(t, tt.expected, *result)
			}
		})
	}
}

func TestSlicePtrOrNil(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		expected *[]string
	}{
		{
			name:     "empty slice returns nil",
			input:    []string{},
			expected: nil,
		},
		{
			name:     "nil slice returns nil",
			input:    nil,
			expected: nil,
		},
		{
			name:     "non-empty slice returns pointer",
			input:    []string{"a", "b"},
			expected: &[]string{"a", "b"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SlicePtrOrNil(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestSliceValueOrEmpty(t *testing.T) {
	tests := []struct {
		name     string
		input    *[]string
		expected []string
	}{
		{
			name:     "nil pointer returns empty slice",
			input:    nil,
			expected: []string{},
		},
		{
			name:     "pointer to slice returns slice value",
			input:    &[]string{"a", "b"},
			expected: []string{"a", "b"},
		},
		{
			name:     "pointer to empty slice returns empty slice",
			input:    &[]string{},
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SliceValueOrEmpty(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSliceValueOrNil(t *testing.T) {
	tests := []struct {
		name     string
		input    *[]string
		expected []string
	}{
		{
			name:     "nil pointer returns nil slice",
			input:    nil,
			expected: nil,
		},
		{
			name:     "pointer to slice returns slice value",
			input:    &[]string{"a", "b"},
			expected: []string{"a", "b"},
		},
		{
			name:     "pointer to empty slice returns empty slice",
			input:    &[]string{},
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SliceValueOrNil(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSliceLen(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		expected int64
	}{
		{
			name:     "empty slice returns 0",
			input:    []string{},
			expected: 0,
		},
		{
			name:     "nil slice returns 0",
			input:    nil,
			expected: 0,
		},
		{
			name:     "slice with elements returns correct length",
			input:    []string{"a", "b", "c"},
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SliceLen(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSliceLenPtr(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		expected *int64
	}{
		{
			name:     "empty slice returns pointer to 0",
			input:    []string{},
			expected: Int64Ptr(0),
		},
		{
			name:     "nil slice returns nil",
			input:    nil,
			expected: nil,
		},
		{
			name:     "slice with elements returns pointer to correct length",
			input:    []string{"a", "b", "c"},
			expected: Int64Ptr(3),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SliceLenPtr(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestSliceLenFromPtr(t *testing.T) {
	tests := []struct {
		name     string
		input    *[]string
		expected int64
	}{
		{
			name:     "nil pointer returns 0",
			input:    nil,
			expected: 0,
		},
		{
			name:     "pointer to empty slice returns 0",
			input:    &[]string{},
			expected: 0,
		},
		{
			name:     "pointer to slice with elements returns correct length",
			input:    &[]string{"a", "b", "c"},
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SliceLenFromPtr(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDiffIDs(t *testing.T) {
	type OldItem struct {
		ID int64
	}
	type NewItem struct {
		ItemID int64
	}

	tests := []struct {
		name           string
		oldItems       []OldItem
		newItems       []NewItem
		expectedDelete []int64
		expectedAdd    []int64
	}{
		{
			name:           "empty slices",
			oldItems:       []OldItem{},
			newItems:       []NewItem{},
			expectedDelete: []int64{},
			expectedAdd:    []int64{},
		},
		{
			name:           "no changes",
			oldItems:       []OldItem{{ID: 1}, {ID: 2}},
			newItems:       []NewItem{{ItemID: 1}, {ItemID: 2}},
			expectedDelete: []int64{},
			expectedAdd:    []int64{},
		},
		{
			name:           "add items",
			oldItems:       []OldItem{{ID: 1}},
			newItems:       []NewItem{{ItemID: 1}, {ItemID: 2}, {ItemID: 3}},
			expectedDelete: []int64{},
			expectedAdd:    []int64{2, 3},
		},
		{
			name:           "delete items",
			oldItems:       []OldItem{{ID: 1}, {ID: 2}, {ID: 3}},
			newItems:       []NewItem{{ItemID: 1}},
			expectedDelete: []int64{2, 3},
			expectedAdd:    []int64{},
		},
		{
			name:           "mixed changes",
			oldItems:       []OldItem{{ID: 1}, {ID: 2}},
			newItems:       []NewItem{{ItemID: 2}, {ItemID: 3}},
			expectedDelete: []int64{1},
			expectedAdd:    []int64{3},
		},
		{
			name:           "ignore zero IDs",
			oldItems:       []OldItem{{ID: 0}, {ID: 1}},
			newItems:       []NewItem{{ItemID: 0}, {ItemID: 2}},
			expectedDelete: []int64{1},
			expectedAdd:    []int64{2},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toDelete, toAdd := DiffIDs(
				tt.oldItems,
				tt.newItems,
				func(item OldItem) int64 { return item.ID },
				func(item NewItem) int64 { return item.ItemID },
			)

			// Sort for consistent comparison since order doesn't matter
			assert.ElementsMatch(t, tt.expectedDelete, toDelete)
			assert.ElementsMatch(t, tt.expectedAdd, toAdd)
		})
	}
}

func TestDiffSimpleIDs(t *testing.T) {
	tests := []struct {
		name           string
		oldIDs         []int64
		newIDs         []int64
		expectedDelete []int64
		expectedAdd    []int64
	}{
		{
			name:           "empty slices",
			oldIDs:         []int64{},
			newIDs:         []int64{},
			expectedDelete: []int64{},
			expectedAdd:    []int64{},
		},
		{
			name:           "no changes",
			oldIDs:         []int64{1, 2, 3},
			newIDs:         []int64{1, 2, 3},
			expectedDelete: []int64{},
			expectedAdd:    []int64{},
		},
		{
			name:           "add and delete",
			oldIDs:         []int64{1, 2},
			newIDs:         []int64{2, 3, 4},
			expectedDelete: []int64{1},
			expectedAdd:    []int64{3, 4},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			toDelete, toAdd := DiffSimpleIDs(tt.oldIDs, tt.newIDs)

			assert.ElementsMatch(t, tt.expectedDelete, toDelete)
			assert.ElementsMatch(t, tt.expectedAdd, toAdd)
		})
	}
}

func TestToSet(t *testing.T) {
	tests := []struct {
		name     string
		input    []int64
		expected map[int64]struct{}
	}{
		{
			name:     "empty slice returns empty set",
			input:    []int64{},
			expected: map[int64]struct{}{},
		},
		{
			name:     "nil slice returns empty set",
			input:    nil,
			expected: map[int64]struct{}{},
		},
		{
			name:  "single element",
			input: []int64{1},
			expected: map[int64]struct{}{
				1: {},
			},
		},
		{
			name:  "multiple unique elements",
			input: []int64{1, 2, 3},
			expected: map[int64]struct{}{
				1: {},
				2: {},
				3: {},
			},
		},
		{
			name:  "duplicate elements are deduplicated",
			input: []int64{1, 2, 1, 3, 2},
			expected: map[int64]struct{}{
				1: {},
				2: {},
				3: {},
			},
		},
		{
			name:  "negative numbers",
			input: []int64{-1, -2, 0, 1, 2},
			expected: map[int64]struct{}{
				-2: {},
				-1: {},
				0:  {},
				1:  {},
				2:  {},
			},
		},
		{
			name:  "large numbers",
			input: []int64{9223372036854775807, -9223372036854775808, 0},
			expected: map[int64]struct{}{
				9223372036854775807:  {},
				-9223372036854775808: {},
				0:                    {},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToSet(tt.input)

			// Проверяем размер
			assert.Equal(t, len(tt.expected), len(result))

			// Проверяем, что все ожидаемые элементы присутствуют
			for key := range tt.expected {
				_, exists := result[key]
				assert.True(t, exists, "ключ %d должен присутствовать в множестве", key)
			}

			// Проверяем, что нет лишних элементов
			for key := range result {
				_, expected := tt.expected[key]
				assert.True(t, expected, "ключ %d не должен присутствовать в множестве", key)
			}
		})
	}
}
