package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRequestContext(t *testing.T) {
	t.Run("create request context", func(t *testing.T) {
		ctx := RequestContext{
			UserID:     123,
			Email:      "<EMAIL>",
			CategoryID: 456,
			IsAdmin:    true,
		}

		assert.Equal(t, int64(123), ctx.UserID)
		assert.Equal(t, "<EMAIL>", ctx.Email)
		assert.Equal(t, int64(456), ctx.CategoryID)
		assert.True(t, ctx.IsAdmin)
	})

	t.Run("zero values", func(t *testing.T) {
		ctx := RequestContext{}

		assert.Equal(t, int64(0), ctx.UserID)
		assert.Equal(t, "", ctx.Email)
		assert.Equal(t, int64(0), ctx.CategoryID)
		assert.False(t, ctx.IsAdmin)
	})
}

func TestPaginationParams(t *testing.T) {
	t.Run("create pagination params", func(t *testing.T) {
		params := PaginationParams{
			Limit:  10,
			Offset: 20,
		}

		assert.Equal(t, int64(10), params.Limit)
		assert.Equal(t, int64(20), params.Offset)
	})

	t.Run("zero values", func(t *testing.T) {
		params := PaginationParams{}

		assert.Equal(t, int64(0), params.Limit)
		assert.Equal(t, int64(0), params.Offset)
	})
}

func TestPaginatedResult(t *testing.T) {
	t.Run("create paginated result with strings", func(t *testing.T) {
		result := PaginatedResult[string]{
			Items:  []string{"item1", "item2", "item3"},
			Limit:  10,
			Offset: 0,
			Total:  100,
		}

		assert.Equal(t, []string{"item1", "item2", "item3"}, result.Items)
		assert.Equal(t, int64(10), result.Limit)
		assert.Equal(t, int64(0), result.Offset)
		assert.Equal(t, int64(100), result.Total)
	})

	t.Run("create paginated result with integers", func(t *testing.T) {
		result := PaginatedResult[int]{
			Items:  []int{1, 2, 3},
			Limit:  5,
			Offset: 10,
			Total:  50,
		}

		assert.Equal(t, []int{1, 2, 3}, result.Items)
		assert.Equal(t, int64(5), result.Limit)
		assert.Equal(t, int64(10), result.Offset)
		assert.Equal(t, int64(50), result.Total)
	})

	t.Run("empty paginated result", func(t *testing.T) {
		result := PaginatedResult[string]{}

		assert.Nil(t, result.Items)
		assert.Equal(t, int64(0), result.Limit)
		assert.Equal(t, int64(0), result.Offset)
		assert.Equal(t, int64(0), result.Total)
	})
}

func TestSortParams(t *testing.T) {
	t.Run("create sort params", func(t *testing.T) {
		params := SortParams{
			Field: "name",
			Order: "asc",
		}

		assert.Equal(t, "name", params.Field)
		assert.Equal(t, "asc", params.Order)
	})

	t.Run("zero values", func(t *testing.T) {
		params := SortParams{}

		assert.Equal(t, "", params.Field)
		assert.Equal(t, "", params.Order)
	})
}

func TestSortField(t *testing.T) {
	tests := []struct {
		name     string
		field    SortField
		expected string
	}{
		{
			name:     "category field",
			field:    SortFieldCategory,
			expected: "category",
		},
		{
			name:     "createdAt field",
			field:    SortFieldCreatedAt,
			expected: "createdAt",
		},
		{
			name:     "updatedAt field",
			field:    SortFieldUpdatedAt,
			expected: "updatedAt",
		},
		{
			name:     "fullName field",
			field:    SortFieldFullName,
			expected: "fullName",
		},
		{
			name:     "product field",
			field:    SortFieldProduct,
			expected: "product",
		},
		{
			name:     "status field",
			field:    SortFieldStatus,
			expected: "status",
		},
		{
			name:     "techName field",
			field:    SortFieldTechName,
			expected: "techName",
		},
		{
			name:     "owners field",
			field:    SortFieldOwners,
			expected: "owners",
		},
		{
			name:     "participantCount field",
			field:    SortFieldParticipantCount,
			expected: "participantCount",
		},
		{
			name:     "userCount field",
			field:    SortFieldUserCount,
			expected: "userCount",
		},
		{
			name:     "groupCount field",
			field:    SortFieldGroupCount,
			expected: "groupCount",
		},
		{
			name:     "roleCount field",
			field:    SortFieldRoleCount,
			expected: "roleCount",
		},
		{
			name:     "name field",
			field:    SortFieldName,
			expected: "name",
		},
		{
			name:     "type field",
			field:    SortFieldType,
			expected: "type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.field.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSortFieldConstants(t *testing.T) {
	t.Run("all sort field constants are defined", func(t *testing.T) {
		assert.Equal(t, "category", string(SortFieldCategory))
		assert.Equal(t, "createdAt", string(SortFieldCreatedAt))
		assert.Equal(t, "updatedAt", string(SortFieldUpdatedAt))
		assert.Equal(t, "fullName", string(SortFieldFullName))
		assert.Equal(t, "product", string(SortFieldProduct))
		assert.Equal(t, "status", string(SortFieldStatus))
		assert.Equal(t, "techName", string(SortFieldTechName))
		assert.Equal(t, "owners", string(SortFieldOwners))
		assert.Equal(t, "participantCount", string(SortFieldParticipantCount))
		assert.Equal(t, "userCount", string(SortFieldUserCount))
		assert.Equal(t, "groupCount", string(SortFieldGroupCount))
		assert.Equal(t, "roleCount", string(SortFieldRoleCount))
		assert.Equal(t, "name", string(SortFieldName))
		assert.Equal(t, "type", string(SortFieldType))
	})
}

func TestSortOrder(t *testing.T) {
	tests := []struct {
		name     string
		order    SortOrder
		expected string
	}{
		{
			name:     "ascend order",
			order:    SortOrderAscend,
			expected: "ascend",
		},
		{
			name:     "descend order",
			order:    SortOrderDescend,
			expected: "descend",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.order.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSortOrderConstants(t *testing.T) {
	t.Run("all sort order constants are defined", func(t *testing.T) {
		assert.Equal(t, "ascend", string(SortOrderAscend))
		assert.Equal(t, "descend", string(SortOrderDescend))
	})
}

func TestSortFieldUsageInSortParams(t *testing.T) {
	t.Run("use sort field constants in sort params", func(t *testing.T) {
		params := SortParams{
			Field: SortFieldName.String(),
			Order: SortOrderAscend.String(),
		}

		assert.Equal(t, "name", params.Field)
		assert.Equal(t, "ascend", params.Order)
	})
}

func TestPaginatedResultWithComplexTypes(t *testing.T) {
	type User struct {
		ID   int64
		Name string
	}

	t.Run("paginated result with custom struct", func(t *testing.T) {
		users := []User{
			{ID: 1, Name: "Alice"},
			{ID: 2, Name: "Bob"},
		}

		result := PaginatedResult[User]{
			Items:  users,
			Limit:  10,
			Offset: 0,
			Total:  2,
		}

		assert.Len(t, result.Items, 2)
		assert.Equal(t, "Alice", result.Items[0].Name)
		assert.Equal(t, "Bob", result.Items[1].Name)
		assert.Equal(t, int64(10), result.Limit)
		assert.Equal(t, int64(0), result.Offset)
		assert.Equal(t, int64(2), result.Total)
	})
}
