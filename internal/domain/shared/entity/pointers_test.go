package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStringPtrOrNil(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected *string
	}{
		{
			name:     "empty string returns nil",
			input:    "",
			expected: nil,
		},
		{
			name:     "non-empty string returns pointer",
			input:    "test",
			expected: string<PERSON>ointer("test"),
		},
		{
			name:     "whitespace string returns pointer",
			input:    " ",
			expected: stringPointer(" "),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringPtrOrNil(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestStringPtr(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "empty string returns pointer to empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "non-empty string returns pointer",
			input:    "test",
			expected: "test",
		},
		{
			name:     "whitespace string returns pointer",
			input:    " ",
			expected: " ",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringPtr(tt.input)
			assert.NotNil(t, result)
			assert.Equal(t, tt.expected, *result)
		})
	}
}

func TestBoolPtr(t *testing.T) {
	tests := []struct {
		name     string
		input    bool
		expected bool
	}{
		{
			name:     "true returns pointer to true",
			input:    true,
			expected: true,
		},
		{
			name:     "false returns pointer to false",
			input:    false,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BoolPtr(tt.input)
			assert.NotNil(t, result)
			assert.Equal(t, tt.expected, *result)
		})
	}
}

func TestBoolPtrOrNil(t *testing.T) {
	tests := []struct {
		name     string
		input    bool
		expected *bool
	}{
		{
			name:     "true returns pointer to true",
			input:    true,
			expected: boolPointer(true),
		},
		{
			name:     "false returns nil",
			input:    false,
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BoolPtrOrNil(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestInt64Ptr(t *testing.T) {
	tests := []struct {
		name     string
		input    int64
		expected int64
	}{
		{
			name:     "positive number",
			input:    42,
			expected: 42,
		},
		{
			name:     "zero",
			input:    0,
			expected: 0,
		},
		{
			name:     "negative number",
			input:    -10,
			expected: -10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Int64Ptr(tt.input)
			assert.NotNil(t, result)
			assert.Equal(t, tt.expected, *result)
		})
	}
}

func TestInt64PtrOrNil(t *testing.T) {
	tests := []struct {
		name     string
		input    int64
		expected *int64
	}{
		{
			name:     "positive number returns pointer",
			input:    42,
			expected: int64Pointer(42),
		},
		{
			name:     "zero returns nil",
			input:    0,
			expected: nil,
		},
		{
			name:     "negative number returns pointer",
			input:    -10,
			expected: int64Pointer(-10),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Int64PtrOrNil(tt.input)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestStringValueOrEmpty(t *testing.T) {
	tests := []struct {
		name     string
		input    *string
		expected string
	}{
		{
			name:     "nil pointer returns empty string",
			input:    nil,
			expected: "",
		},
		{
			name:     "pointer to string returns string value",
			input:    stringPointer("test"),
			expected: "test",
		},
		{
			name:     "pointer to empty string returns empty string",
			input:    stringPointer(""),
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringValueOrEmpty(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBoolValueOrFalse(t *testing.T) {
	tests := []struct {
		name     string
		input    *bool
		expected bool
	}{
		{
			name:     "nil pointer returns false",
			input:    nil,
			expected: false,
		},
		{
			name:     "pointer to true returns true",
			input:    boolPointer(true),
			expected: true,
		},
		{
			name:     "pointer to false returns false",
			input:    boolPointer(false),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BoolValueOrFalse(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestInt64ValueOrZero(t *testing.T) {
	tests := []struct {
		name     string
		input    *int64
		expected int64
	}{
		{
			name:     "nil pointer returns zero",
			input:    nil,
			expected: 0,
		},
		{
			name:     "pointer to positive number returns value",
			input:    int64Pointer(42),
			expected: 42,
		},
		{
			name:     "pointer to zero returns zero",
			input:    int64Pointer(0),
			expected: 0,
		},
		{
			name:     "pointer to negative number returns value",
			input:    int64Pointer(-10),
			expected: -10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Int64ValueOrZero(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper functions for creating pointers in tests
func stringPointer(s string) *string {
	return &s
}

func boolPointer(b bool) *bool {
	return &b
}

func int64Pointer(i int64) *int64 {
	return &i
}
