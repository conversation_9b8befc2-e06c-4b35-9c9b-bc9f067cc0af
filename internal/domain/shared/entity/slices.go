package entity

// SlicePtr returns a pointer to the slice always, even for empty slices.
func SlicePtr[T any](s []T) *[]T {
	return &s
}

// SlicePtrOrNil returns a pointer to the slice or nil for empty slices.
func SlicePtrOrNil[T any](s []T) *[]T {
	if len(s) == 0 {
		return nil
	}
	return &s
}

// SliceValueOrEmpty returns the slice value or empty slice if the pointer is nil.
func SliceValueOrEmpty[T any](s *[]T) []T {
	if s == nil {
		return []T{}
	}
	return *s
}

// SliceValueOrNil returns the slice value or nil if the pointer is nil.
func SliceValueOrNil[T any](s *[]T) []T {
	if s == nil {
		return nil
	}
	return *s
}

// SliceLen returns the length of the slice as int64.
func SliceLen[T any](s []T) int64 {
	return int64(len(s))
}

// SliceLenPtr returns the length of the slice as int64 pointer, or nil if slice is nil.
func SliceLenPtr[T any](s []T) *int64 {
	if s == nil {
		return nil
	}
	length := int64(len(s))
	return &length
}

// SliceLenFromPtr returns the length of the slice as int64, or 0 if pointer is nil.
func SliceLenFromPtr[T any](s *[]T) int64 {
	if s == nil {
		return 0
	}
	return int64(len(*s))
}

// DiffIDs compares two slices of IDs and returns items to delete and to add.
// Takes two ID extractors to get int64 IDs from different types.
func DiffIDs[TOld, TNew any](
	oldItems []TOld,
	newItems []TNew,
	oldIDExtractor func(TOld) int64,
	newIDExtractor func(TNew) int64,
) (toDelete []int64, toAdd []int64) {
	newIDSet := make(map[int64]struct{})
	for _, item := range newItems {
		if id := newIDExtractor(item); id != 0 {
			newIDSet[id] = struct{}{}
		}
	}

	oldIDSet := make(map[int64]struct{})
	for _, item := range oldItems {
		if id := oldIDExtractor(item); id != 0 {
			oldIDSet[id] = struct{}{}
		}
	}

	// Find IDs to delete (in old but not in new)
	for id := range oldIDSet {
		if _, exists := newIDSet[id]; !exists {
			toDelete = append(toDelete, id)
		}
	}

	// Find IDs to add (in new but not in old)
	for id := range newIDSet {
		if _, exists := oldIDSet[id]; !exists {
			toAdd = append(toAdd, id)
		}
	}

	return toDelete, toAdd
}

// DiffSimpleIDs compares two slices of int64 IDs and returns items to delete and to add.
func DiffSimpleIDs(oldIDs, newIDs []int64) (toDelete []int64, toAdd []int64) {
	return DiffIDs(oldIDs, newIDs,
		func(id int64) int64 { return id },
		func(id int64) int64 { return id },
	)
}

// ToSet converts a slice of int64 to a map of int64 to struct{}.
func ToSet(slice []int64) map[int64]struct{} {
	set := make(map[int64]struct{}, len(slice))
	for _, id := range slice {
		set[id] = struct{}{}
	}
	return set
}
