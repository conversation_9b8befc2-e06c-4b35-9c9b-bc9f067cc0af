package entity

type RequestContext struct {
	UserID     int64
	Email      string
	CategoryID int64
	IsAdmin    bool
}

type PaginationParams struct {
	Limit  int64
	Offset int64
}

type PaginatedResult[T any] struct {
	Items  []T
	Limit  int64
	Offset int64
	Total  int64
}

type SortParams struct {
	Field string
	Order string
}

type SortField string

const (
	SortFieldCategory         SortField = "category"
	SortFieldCreatedAt        SortField = "createdAt"
	SortFieldUpdatedAt        SortField = "updatedAt"
	SortFieldFullName         SortField = "fullName"
	SortFieldProduct          SortField = "product"
	SortFieldStatus           SortField = "status"
	SortFieldTechName         SortField = "techName"
	SortFieldOwners           SortField = "owners"
	SortFieldParticipantCount SortField = "participantCount"
	SortFieldUserCount        SortField = "userCount"
	SortFieldGroupCount       SortField = "groupCount"
	SortFieldRoleCount        SortField = "roleCount"
	SortFieldName             SortField = "name"
	SortFieldType             SortField = "type"
)

func (s SortField) String() string {
	return string(s)
}

type SortOrder string

const (
	SortOrderAscend  SortOrder = "ascend"
	SortOrderDescend SortOrder = "descend"
)

func (s SortOrder) String() string {
	return string(s)
}
