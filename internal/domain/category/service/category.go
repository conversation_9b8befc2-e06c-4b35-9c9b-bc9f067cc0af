package service

import (
	"context"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categoryrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	grouprepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/repository"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolerepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/repository"
	userrepository "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/repository"
)

//go:generate minimock -i CategoryDomainService -o ../mocks/category_domain_service_mock.go -s _mock.go
type CategoryDomainService interface {
	Create(data categoryentity.Category) (categoryentity.Category, error)
	GetAll() ([]categoryentity.Category, error)
	GetByID(id int64) (categoryentity.Category, error)
	GetByName(name string) (categoryentity.Category, error)
	GetCategoryFull(categoryID int64) (categoryentity.CategoryFull, error)
	GetFullByID(id int64) (categoryentity.CategoryFull, error)
	ExistLinkWithGroup(categoryID, groupID int64) (bool, error)
	ExistLinkWithPermission(categoryID, permissionID int64) (bool, error)
	ExistLinkWithRole(categoryID, roleID int64) (bool, error)
	Update(data categoryentity.Category) (categoryentity.Category, error)
	Delete(ctx context.Context, id int64) error
}

type categoryDomainService struct {
	categoryGroupRepo      categoryrepository.CategoryGroupPrimeDB
	categoryPermissionRepo categoryrepository.CategoryPermissionPrimeDB
	categoryRepo           categoryrepository.CategoryPrimeDB
	categoryRoleRepo       categoryrepository.CategoryRolePrimeDB
	groupRepo              grouprepository.GroupPrimeDB
	roleRepo               rolerepository.RolePrimeDB
	userRepo               userrepository.UserPrimeDB
}

func NewCategoryDomainService(
	categoryGroupRepo categoryrepository.CategoryGroupPrimeDB,
	categoryPermissionRepo categoryrepository.CategoryPermissionPrimeDB,
	categoryRepo categoryrepository.CategoryPrimeDB,
	categoryRoleRepo categoryrepository.CategoryRolePrimeDB,
	groupRepo grouprepository.GroupPrimeDB,
	roleRepo rolerepository.RolePrimeDB,
	userRepo userrepository.UserPrimeDB,
) CategoryDomainService {
	return &categoryDomainService{
		categoryGroupRepo:      categoryGroupRepo,
		categoryPermissionRepo: categoryPermissionRepo,
		categoryRepo:           categoryRepo,
		categoryRoleRepo:       categoryRoleRepo,
		groupRepo:              groupRepo,
		roleRepo:               roleRepo,
		userRepo:               userRepo,
	}
}

func (s *categoryDomainService) Create(data categoryentity.Category) (categoryentity.Category, error) {
	return s.categoryRepo.Create(data)
}

func (s *categoryDomainService) GetAll() ([]categoryentity.Category, error) {
	return s.categoryRepo.GetAll()
}

func (s *categoryDomainService) GetByID(id int64) (categoryentity.Category, error) {
	return s.categoryRepo.GetByID(id)
}

func (s *categoryDomainService) GetByName(name string) (categoryentity.Category, error) {
	return s.categoryRepo.GetByName(name)
}

func (s *categoryDomainService) GetCategoryFull(categoryID int64) (categoryentity.CategoryFull, error) {

	cat, err := s.categoryRepo.GetByID(categoryID)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	groupsDB, err := s.categoryGroupRepo.GetByCategoryID(categoryID)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	var groups []groupentity.GroupShort
	if len(groupsDB) > 0 {
		for _, g := range groupsDB {
			group, err := s.groupRepo.GetByID(g.GroupID)
			if err != nil {
				return categoryentity.CategoryFull{}, err
			}
			groups = append(groups, groupentity.GroupShort{
				ID:       group.ID,
				Name:     group.Name,
				IsSystem: group.IsSystem,
			})
		}
	}

	rolesDB, err := s.categoryRoleRepo.GetByCategoryID(categoryID)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	var roles []roleentity.RoleWithStats
	if len(rolesDB) > 0 {
		for _, r := range rolesDB {
			role, err := s.roleRepo.GetByID(r.RoleID)
			if err != nil {
				return categoryentity.CategoryFull{}, err
			}
			roles = append(roles, roleentity.RoleWithStats{
				RoleID:   role.ID,
				RoleName: role.Name,
				IsSystem: role.IsSystem,
			})
		}
	}

	users, err := s.userRepo.GetByCategoryID(categoryID)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	var usersIDs []int64
	if len(users) > 0 {
		for _, u := range users {
			usersIDs = append(usersIDs, u.ID)
		}
	}

	return categoryentity.CategoryFull{
		ID:     categoryID,
		Name:   cat.Name,
		Groups: &groups,
		Roles:  &roles,
		Users:  &usersIDs,
	}, nil
}

func (s *categoryDomainService) GetFullByID(id int64) (categoryentity.CategoryFull, error) {

	cat, err := s.categoryRepo.GetByID(id)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	groupsDB, err := s.categoryGroupRepo.GetByCategoryID(id)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	var groups []groupentity.GroupShort
	if len(groupsDB) > 0 {
		for _, g := range groupsDB {
			group, err := s.groupRepo.GetByID(g.GroupID)
			if err != nil {
				return categoryentity.CategoryFull{}, err
			}
			groups = append(groups, groupentity.GroupShort{
				ID:       group.ID,
				Name:     group.Name,
				IsSystem: group.IsSystem,
			})
		}
	}

	rolesDB, err := s.categoryRoleRepo.GetByCategoryID(id)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	var roles []roleentity.RoleWithStats
	if len(rolesDB) > 0 {
		for _, r := range rolesDB {
			role, err := s.roleRepo.GetByID(r.RoleID)
			if err != nil {
				return categoryentity.CategoryFull{}, err
			}
			roles = append(roles, roleentity.RoleWithStats{
				RoleID:   role.ID,
				RoleName: role.Name,
				IsSystem: role.IsSystem,
			})
		}
	}

	users, err := s.userRepo.GetByCategoryID(id)
	if err != nil {
		return categoryentity.CategoryFull{}, err
	}

	var usersIDs []int64
	if len(users) > 0 {
		for _, u := range users {
			usersIDs = append(usersIDs, u.ID)
		}
	}

	return categoryentity.CategoryFull{
		ID:     id,
		Name:   cat.Name,
		Groups: &groups,
		Roles:  &roles,
		Users:  &usersIDs,
	}, nil
}

func (s *categoryDomainService) ExistLinkWithGroup(categoryID, groupID int64) (bool, error) {
	return s.categoryGroupRepo.HasCategoryGroup(categoryID, groupID)
}

func (s *categoryDomainService) ExistLinkWithPermission(categoryID, permissionID int64) (bool, error) {
	return s.categoryPermissionRepo.HasCategoryPermission(categoryID, permissionID)
}

func (s *categoryDomainService) ExistLinkWithRole(categoryID, roleID int64) (bool, error) {
	return s.categoryRoleRepo.HasCategoryRole(categoryID, roleID)
}

func (s *categoryDomainService) Update(data categoryentity.Category) (categoryentity.Category, error) {
	return s.categoryRepo.Update(data)
}

func (s *categoryDomainService) Delete(ctx context.Context, id int64) error {
	return s.categoryRepo.Delete(ctx, id)
}

func ToCurrentTypeFromIsSystem(isSystem bool) string {
	if isSystem {
		return constants.SystemType
	}
	return constants.CustomType
}
