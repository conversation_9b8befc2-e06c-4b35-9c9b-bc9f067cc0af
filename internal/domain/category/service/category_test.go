package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	categorymocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/mocks"
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	groupmocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/mocks"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
	rolemocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/mocks"
	userentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/entity"
	usermocks "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/user/mocks"
)

func createMocks(t *testing.T) (
	*categorymocks.CategoryGroupPrimeDBMock,
	*categorymocks.CategoryPermissionPrimeDBMock,
	*categorymocks.CategoryPrimeDBMock,
	*categorymocks.CategoryRolePrimeDBMock,
	*groupmocks.GroupPrimeDBMock,
	*rolemocks.RolePrimeDBMock,
	*usermocks.UserPrimeDBMock,
) {
	return categorymocks.NewCategoryGroupPrimeDBMock(t),
		categorymocks.NewCategoryPermissionPrimeDBMock(t),
		categorymocks.NewCategoryPrimeDBMock(t),
		categorymocks.NewCategoryRolePrimeDBMock(t),
		groupmocks.NewGroupPrimeDBMock(t),
		rolemocks.NewRolePrimeDBMock(t),
		usermocks.NewUserPrimeDBMock(t)
}

func TestNewCategoryDomainService(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	assert.NotNil(t, service)
	assert.IsType(t, &categoryDomainService{}, service)
}

func TestCategoryDomainService_Create_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	inputCategory := categoryentity.Category{
		Name: "Test Category",
	}
	expectedCategory := categoryentity.Category{
		ID:   123,
		Name: "Test Category",
	}

	categoryRepo.CreateMock.Expect(inputCategory).Return(expectedCategory, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.Create(inputCategory)

	require.NoError(t, err)
	assert.Equal(t, expectedCategory, result)
	assert.Equal(t, uint64(1), categoryRepo.CreateAfterCounter())
}

func TestCategoryDomainService_Create_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	inputCategory := categoryentity.Category{
		Name: "Test Category",
	}
	expectedError := errors.New("create error")

	categoryRepo.CreateMock.Expect(inputCategory).Return(categoryentity.Category{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.Create(inputCategory)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.Category{}, result)
}

func TestCategoryDomainService_GetAll_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	expectedCategories := []categoryentity.Category{
		{ID: 1, Name: "Category 1"},
		{ID: 2, Name: "Category 2"},
	}

	categoryRepo.GetAllMock.Expect().Return(expectedCategories, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetAll()

	require.NoError(t, err)
	assert.Equal(t, expectedCategories, result)
	assert.Equal(t, uint64(1), categoryRepo.GetAllAfterCounter())
}

func TestCategoryDomainService_GetAll_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	expectedError := errors.New("get all error")

	categoryRepo.GetAllMock.Expect().Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetAll()

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}

func TestCategoryDomainService_GetByID_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(123)
	expectedCategory := categoryentity.Category{
		ID:   categoryID,
		Name: "Test Category",
	}

	categoryRepo.GetByIDMock.Expect(categoryID).Return(expectedCategory, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetByID(categoryID)

	require.NoError(t, err)
	assert.Equal(t, expectedCategory, result)
	assert.Equal(t, uint64(1), categoryRepo.GetByIDAfterCounter())
}

func TestCategoryDomainService_GetByID_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(123)
	expectedError := errors.New("not found")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(categoryentity.Category{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.Category{}, result)
}

func TestCategoryDomainService_GetByName_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryName := "Test Category"
	expectedCategory := categoryentity.Category{
		ID:   456,
		Name: categoryName,
	}

	categoryRepo.GetByNameMock.Expect(categoryName).Return(expectedCategory, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetByName(categoryName)

	require.NoError(t, err)
	assert.Equal(t, expectedCategory, result)
	assert.Equal(t, uint64(1), categoryRepo.GetByNameAfterCounter())
}

func TestCategoryDomainService_GetByName_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryName := "Test Category"
	expectedError := errors.New("not found")

	categoryRepo.GetByNameMock.Expect(categoryName).Return(categoryentity.Category{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetByName(categoryName)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.Category{}, result)
}

func TestCategoryDomainService_GetCategoryFull_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	groupLinks := []categoryentity.CategoryGroupLink{
		{ID: 1, CategoryID: categoryID, GroupID: 1},
		{ID: 2, CategoryID: categoryID, GroupID: 2},
	}
	groups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
		{ID: 2, Name: "Group 2", IsSystem: false},
	}
	roleLinks := []categoryentity.CategoryRoleLink{
		{ID: 1, CategoryID: categoryID, RoleID: 1},
	}
	roles := []roleentity.Role{
		{ID: 1, Name: "Role 1", IsSystem: true},
	}
	users := []userentity.User{
		{ID: 1},
		{ID: 2},
	}

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(groupLinks, nil)
	groupRepo.GetByIDMock.When(int64(1)).Then(groups[0], nil)
	groupRepo.GetByIDMock.When(int64(2)).Then(groups[1], nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return(roleLinks, nil)
	roleRepo.GetByIDMock.Expect(int64(1)).Return(roles[0], nil)
	userRepo.GetByCategoryIDMock.Expect(categoryID).Return(users, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.NoError(t, err)
	assert.Equal(t, categoryID, result.ID)
	assert.Equal(t, "Full Category", result.Name)
	assert.Len(t, *result.Groups, 2)
	assert.Equal(t, "Group 1", (*result.Groups)[0].Name)
	assert.True(t, (*result.Groups)[0].IsSystem)
	assert.Len(t, *result.Roles, 1)
	assert.Equal(t, "Role 1", (*result.Roles)[0].RoleName)
	assert.Len(t, *result.Users, 2)
	assert.Contains(t, *result.Users, int64(1))
	assert.Contains(t, *result.Users, int64(2))
}

func TestCategoryDomainService_GetCategoryFull_CategoryNotFound(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(999)
	expectedError := errors.New("category not found")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(categoryentity.Category{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetCategoryFull_GroupError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	expectedError := errors.New("group error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetCategoryFull_GroupByIDError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	groupLinks := []categoryentity.CategoryGroupLink{
		{ID: 1, CategoryID: categoryID, GroupID: 1},
	}
	expectedError := errors.New("group by id error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(groupLinks, nil)
	groupRepo.GetByIDMock.Expect(int64(1)).Return(groupentity.Group{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetCategoryFull_RoleError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	expectedError := errors.New("role error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetCategoryFull_RoleByIDError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	roleLinks := []categoryentity.CategoryRoleLink{
		{ID: 1, CategoryID: categoryID, RoleID: 1},
	}
	expectedError := errors.New("role by id error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return(roleLinks, nil)
	roleRepo.GetByIDMock.Expect(int64(1)).Return(roleentity.Role{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetCategoryFull_UserError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	expectedError := errors.New("user error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryRoleLink{}, nil)
	userRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetCategoryFull_EmptyGroups(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	users := []userentity.User{
		{ID: 1},
	}

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryRoleLink{}, nil)
	userRepo.GetByCategoryIDMock.Expect(categoryID).Return(users, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetCategoryFull(categoryID)

	require.NoError(t, err)
	assert.Equal(t, categoryID, result.ID)
	assert.Equal(t, "Full Category", result.Name)
	assert.Len(t, *result.Groups, 0)
	assert.Len(t, *result.Roles, 0)
	assert.Len(t, *result.Users, 1)
}

func TestCategoryDomainService_GetFullByID_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(200)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	groupLinks := []categoryentity.CategoryGroupLink{
		{ID: 1, CategoryID: categoryID, GroupID: 1},
	}
	groups := []groupentity.Group{
		{ID: 1, Name: "Group 1", IsSystem: true},
	}
	roleLinks := []categoryentity.CategoryRoleLink{
		{ID: 1, CategoryID: categoryID, RoleID: 1},
	}
	roles := []roleentity.Role{
		{ID: 1, Name: "Role 1", IsSystem: true},
	}
	users := []userentity.User{
		{ID: 1},
	}

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(groupLinks, nil)
	groupRepo.GetByIDMock.Expect(int64(1)).Return(groups[0], nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return(roleLinks, nil)
	roleRepo.GetByIDMock.Expect(int64(1)).Return(roles[0], nil)
	userRepo.GetByCategoryIDMock.Expect(categoryID).Return(users, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.NoError(t, err)
	assert.Equal(t, categoryID, result.ID)
	assert.Equal(t, "Full Category", result.Name)
	assert.Len(t, *result.Groups, 1)
	assert.Equal(t, "Group 1", (*result.Groups)[0].Name)
	assert.Len(t, *result.Roles, 1)
	assert.Equal(t, "Role 1", (*result.Roles)[0].RoleName)
	assert.Len(t, *result.Users, 1)
}

func TestCategoryDomainService_GetFullByID_CategoryNotFound(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(999)
	expectedError := errors.New("category not found")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(categoryentity.Category{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetFullByID_GroupError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(200)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	expectedError := errors.New("group error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetFullByID_GroupByIDError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(200)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	groupLinks := []categoryentity.CategoryGroupLink{
		{ID: 1, CategoryID: categoryID, GroupID: 1},
	}
	expectedError := errors.New("group by id error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return(groupLinks, nil)
	groupRepo.GetByIDMock.Expect(int64(1)).Return(groupentity.Group{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetFullByID_RoleError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(200)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	expectedError := errors.New("role error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetFullByID_RoleByIDError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(200)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	roleLinks := []categoryentity.CategoryRoleLink{
		{ID: 1, CategoryID: categoryID, RoleID: 1},
	}
	expectedError := errors.New("role by id error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return(roleLinks, nil)
	roleRepo.GetByIDMock.Expect(int64(1)).Return(roleentity.Role{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_GetFullByID_UserError(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(200)
	category := categoryentity.Category{
		ID:   categoryID,
		Name: "Full Category",
	}
	expectedError := errors.New("user error")

	categoryRepo.GetByIDMock.Expect(categoryID).Return(category, nil)
	categoryGroupRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryGroupLink{}, nil)
	categoryRoleRepo.GetByCategoryIDMock.Expect(categoryID).Return([]categoryentity.CategoryRoleLink{}, nil)
	userRepo.GetByCategoryIDMock.Expect(categoryID).Return(nil, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.GetFullByID(categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.CategoryFull{}, result)
}

func TestCategoryDomainService_ExistLinkWithGroup_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	groupID := int64(200)
	expectedResult := true

	categoryGroupRepo.HasCategoryGroupMock.Expect(categoryID, groupID).Return(expectedResult, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.ExistLinkWithGroup(categoryID, groupID)

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), categoryGroupRepo.HasCategoryGroupAfterCounter())
}

func TestCategoryDomainService_ExistLinkWithGroup_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	groupID := int64(200)
	expectedError := errors.New("database error")

	categoryGroupRepo.HasCategoryGroupMock.Expect(categoryID, groupID).Return(false, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.ExistLinkWithGroup(categoryID, groupID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.False(t, result)
}

func TestCategoryDomainService_ExistLinkWithPermission_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	permissionID := int64(300)
	expectedResult := true

	categoryPermissionRepo.HasCategoryPermissionMock.Expect(categoryID, permissionID).Return(expectedResult, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.ExistLinkWithPermission(categoryID, permissionID)

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), categoryPermissionRepo.HasCategoryPermissionAfterCounter())
}

func TestCategoryDomainService_ExistLinkWithPermission_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	permissionID := int64(300)
	expectedError := errors.New("permission check failed")

	categoryPermissionRepo.HasCategoryPermissionMock.Expect(categoryID, permissionID).Return(false, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.ExistLinkWithPermission(categoryID, permissionID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.False(t, result)
}

func TestCategoryDomainService_ExistLinkWithRole_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	roleID := int64(400)
	expectedResult := false

	categoryRoleRepo.HasCategoryRoleMock.Expect(categoryID, roleID).Return(expectedResult, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.ExistLinkWithRole(categoryID, roleID)

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, uint64(1), categoryRoleRepo.HasCategoryRoleAfterCounter())
}

func TestCategoryDomainService_ExistLinkWithRole_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(100)
	roleID := int64(400)
	expectedError := errors.New("role check failed")

	categoryRoleRepo.HasCategoryRoleMock.Expect(categoryID, roleID).Return(false, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.ExistLinkWithRole(categoryID, roleID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.False(t, result)
}

func TestCategoryDomainService_Update_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	updateCategory := categoryentity.Category{
		ID:   789,
		Name: "Updated Category",
	}

	categoryRepo.UpdateMock.Expect(updateCategory).Return(updateCategory, nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.Update(updateCategory)

	require.NoError(t, err)
	assert.Equal(t, updateCategory, result)
	assert.Equal(t, uint64(1), categoryRepo.UpdateAfterCounter())
}

func TestCategoryDomainService_Update_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	updateCategory := categoryentity.Category{
		ID:   789,
		Name: "Updated Category",
	}
	expectedError := errors.New("update error")

	categoryRepo.UpdateMock.Expect(updateCategory).Return(categoryentity.Category{}, expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	result, err := service.Update(updateCategory)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Equal(t, categoryentity.Category{}, result)
}

func TestCategoryDomainService_Delete_Success(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(999)
	ctx := context.Background()

	categoryRepo.DeleteMock.Expect(ctx, categoryID).Return(nil)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	err := service.Delete(ctx, categoryID)

	require.NoError(t, err)
	assert.Equal(t, uint64(1), categoryRepo.DeleteAfterCounter())
}

func TestCategoryDomainService_Delete_Error(t *testing.T) {
	categoryGroupRepo, categoryPermissionRepo, categoryRepo, categoryRoleRepo, groupRepo, roleRepo, userRepo := createMocks(t)

	categoryID := int64(999)
	ctx := context.Background()
	expectedError := errors.New("delete error")

	categoryRepo.DeleteMock.Expect(ctx, categoryID).Return(expectedError)

	service := NewCategoryDomainService(
		categoryGroupRepo,
		categoryPermissionRepo,
		categoryRepo,
		categoryRoleRepo,
		groupRepo,
		roleRepo,
		userRepo,
	)

	err := service.Delete(ctx, categoryID)

	require.Error(t, err)
	assert.Equal(t, expectedError, err)
}

func TestToCurrentTypeFromIsSystem_SystemType(t *testing.T) {
	result := ToCurrentTypeFromIsSystem(true)
	assert.Equal(t, constants.SystemType, result)
}

func TestToCurrentTypeFromIsSystem_CustomType(t *testing.T) {
	result := ToCurrentTypeFromIsSystem(false)
	assert.Equal(t, constants.CustomType, result)
}
