// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/service.CategoryDomainService -o category_domain_service_mock.go -n CategoryDomainServiceMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryDomainServiceMock implements mm_service.CategoryDomainService
type CategoryDomainServiceMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(data categoryentity.Category) (c1 categoryentity.Category, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(data categoryentity.Category)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mCategoryDomainServiceMockCreate

	funcDelete          func(ctx context.Context, id int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(ctx context.Context, id int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mCategoryDomainServiceMockDelete

	funcExistLinkWithGroup          func(categoryID int64, groupID int64) (b1 bool, err error)
	funcExistLinkWithGroupOrigin    string
	inspectFuncExistLinkWithGroup   func(categoryID int64, groupID int64)
	afterExistLinkWithGroupCounter  uint64
	beforeExistLinkWithGroupCounter uint64
	ExistLinkWithGroupMock          mCategoryDomainServiceMockExistLinkWithGroup

	funcExistLinkWithPermission          func(categoryID int64, permissionID int64) (b1 bool, err error)
	funcExistLinkWithPermissionOrigin    string
	inspectFuncExistLinkWithPermission   func(categoryID int64, permissionID int64)
	afterExistLinkWithPermissionCounter  uint64
	beforeExistLinkWithPermissionCounter uint64
	ExistLinkWithPermissionMock          mCategoryDomainServiceMockExistLinkWithPermission

	funcExistLinkWithRole          func(categoryID int64, roleID int64) (b1 bool, err error)
	funcExistLinkWithRoleOrigin    string
	inspectFuncExistLinkWithRole   func(categoryID int64, roleID int64)
	afterExistLinkWithRoleCounter  uint64
	beforeExistLinkWithRoleCounter uint64
	ExistLinkWithRoleMock          mCategoryDomainServiceMockExistLinkWithRole

	funcGetAll          func() (ca1 []categoryentity.Category, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryDomainServiceMockGetAll

	funcGetByID          func(id int64) (c1 categoryentity.Category, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryDomainServiceMockGetByID

	funcGetByName          func(name string) (c1 categoryentity.Category, err error)
	funcGetByNameOrigin    string
	inspectFuncGetByName   func(name string)
	afterGetByNameCounter  uint64
	beforeGetByNameCounter uint64
	GetByNameMock          mCategoryDomainServiceMockGetByName

	funcGetCategoryFull          func(categoryID int64) (c1 categoryentity.CategoryFull, err error)
	funcGetCategoryFullOrigin    string
	inspectFuncGetCategoryFull   func(categoryID int64)
	afterGetCategoryFullCounter  uint64
	beforeGetCategoryFullCounter uint64
	GetCategoryFullMock          mCategoryDomainServiceMockGetCategoryFull

	funcGetFullByID          func(id int64) (c1 categoryentity.CategoryFull, err error)
	funcGetFullByIDOrigin    string
	inspectFuncGetFullByID   func(id int64)
	afterGetFullByIDCounter  uint64
	beforeGetFullByIDCounter uint64
	GetFullByIDMock          mCategoryDomainServiceMockGetFullByID

	funcUpdate          func(data categoryentity.Category) (c1 categoryentity.Category, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(data categoryentity.Category)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mCategoryDomainServiceMockUpdate
}

// NewCategoryDomainServiceMock returns a mock for mm_service.CategoryDomainService
func NewCategoryDomainServiceMock(t minimock.Tester) *CategoryDomainServiceMock {
	m := &CategoryDomainServiceMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mCategoryDomainServiceMockCreate{mock: m}
	m.CreateMock.callArgs = []*CategoryDomainServiceMockCreateParams{}

	m.DeleteMock = mCategoryDomainServiceMockDelete{mock: m}
	m.DeleteMock.callArgs = []*CategoryDomainServiceMockDeleteParams{}

	m.ExistLinkWithGroupMock = mCategoryDomainServiceMockExistLinkWithGroup{mock: m}
	m.ExistLinkWithGroupMock.callArgs = []*CategoryDomainServiceMockExistLinkWithGroupParams{}

	m.ExistLinkWithPermissionMock = mCategoryDomainServiceMockExistLinkWithPermission{mock: m}
	m.ExistLinkWithPermissionMock.callArgs = []*CategoryDomainServiceMockExistLinkWithPermissionParams{}

	m.ExistLinkWithRoleMock = mCategoryDomainServiceMockExistLinkWithRole{mock: m}
	m.ExistLinkWithRoleMock.callArgs = []*CategoryDomainServiceMockExistLinkWithRoleParams{}

	m.GetAllMock = mCategoryDomainServiceMockGetAll{mock: m}

	m.GetByIDMock = mCategoryDomainServiceMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryDomainServiceMockGetByIDParams{}

	m.GetByNameMock = mCategoryDomainServiceMockGetByName{mock: m}
	m.GetByNameMock.callArgs = []*CategoryDomainServiceMockGetByNameParams{}

	m.GetCategoryFullMock = mCategoryDomainServiceMockGetCategoryFull{mock: m}
	m.GetCategoryFullMock.callArgs = []*CategoryDomainServiceMockGetCategoryFullParams{}

	m.GetFullByIDMock = mCategoryDomainServiceMockGetFullByID{mock: m}
	m.GetFullByIDMock.callArgs = []*CategoryDomainServiceMockGetFullByIDParams{}

	m.UpdateMock = mCategoryDomainServiceMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*CategoryDomainServiceMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryDomainServiceMockCreate struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockCreateExpectation
	expectations       []*CategoryDomainServiceMockCreateExpectation

	callArgs []*CategoryDomainServiceMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockCreateExpectation specifies expectation struct of the CategoryDomainService.Create
type CategoryDomainServiceMockCreateExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockCreateParams
	paramPtrs          *CategoryDomainServiceMockCreateParamPtrs
	expectationOrigins CategoryDomainServiceMockCreateExpectationOrigins
	results            *CategoryDomainServiceMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockCreateParams contains parameters of the CategoryDomainService.Create
type CategoryDomainServiceMockCreateParams struct {
	data categoryentity.Category
}

// CategoryDomainServiceMockCreateParamPtrs contains pointers to parameters of the CategoryDomainService.Create
type CategoryDomainServiceMockCreateParamPtrs struct {
	data *categoryentity.Category
}

// CategoryDomainServiceMockCreateResults contains results of the CategoryDomainService.Create
type CategoryDomainServiceMockCreateResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryDomainServiceMockCreateOrigins contains origins of expectations of the CategoryDomainService.Create
type CategoryDomainServiceMockCreateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mCategoryDomainServiceMockCreate) Optional() *mCategoryDomainServiceMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for CategoryDomainService.Create
func (mmCreate *mCategoryDomainServiceMockCreate) Expect(data categoryentity.Category) *mCategoryDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("CategoryDomainServiceMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &CategoryDomainServiceMockCreateParams{data}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectDataParam1 sets up expected param data for CategoryDomainService.Create
func (mmCreate *mCategoryDomainServiceMockCreate) ExpectDataParam1(data categoryentity.Category) *mCategoryDomainServiceMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryDomainServiceMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("CategoryDomainServiceMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &CategoryDomainServiceMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.data = &data
	mmCreate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.Create
func (mmCreate *mCategoryDomainServiceMockCreate) Inspect(f func(data categoryentity.Category)) *mCategoryDomainServiceMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by CategoryDomainService.Create
func (mmCreate *mCategoryDomainServiceMockCreate) Return(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryDomainServiceMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryDomainServiceMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &CategoryDomainServiceMockCreateResults{c1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the CategoryDomainService.Create method
func (mmCreate *mCategoryDomainServiceMockCreate) Set(f func(data categoryentity.Category) (c1 categoryentity.Category, err error)) *CategoryDomainServiceMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the CategoryDomainService.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mCategoryDomainServiceMockCreate) When(data categoryentity.Category) *CategoryDomainServiceMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryDomainServiceMock.Create mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &CategoryDomainServiceMockCreateParams{data},
		expectationOrigins: CategoryDomainServiceMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.Create return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockCreateExpectation) Then(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockCreateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.Create should be invoked
func (mmCreate *mCategoryDomainServiceMockCreate) Times(n uint64) *mCategoryDomainServiceMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of CategoryDomainServiceMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mCategoryDomainServiceMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_service.CategoryDomainService
func (mmCreate *CategoryDomainServiceMock) Create(data categoryentity.Category) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(data)
	}

	mm_params := CategoryDomainServiceMockCreateParams{data}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockCreateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmCreate.t.Errorf("CategoryDomainServiceMock.Create got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("CategoryDomainServiceMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the CategoryDomainServiceMock.Create")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(data)
	}
	mmCreate.t.Fatalf("Unexpected call to CategoryDomainServiceMock.Create. %v", data)
	return
}

// CreateAfterCounter returns a count of finished CategoryDomainServiceMock.Create invocations
func (mmCreate *CategoryDomainServiceMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of CategoryDomainServiceMock.Create invocations
func (mmCreate *CategoryDomainServiceMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mCategoryDomainServiceMockCreate) Calls() []*CategoryDomainServiceMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mCategoryDomainServiceMockDelete struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockDeleteExpectation
	expectations       []*CategoryDomainServiceMockDeleteExpectation

	callArgs []*CategoryDomainServiceMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockDeleteExpectation specifies expectation struct of the CategoryDomainService.Delete
type CategoryDomainServiceMockDeleteExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockDeleteParams
	paramPtrs          *CategoryDomainServiceMockDeleteParamPtrs
	expectationOrigins CategoryDomainServiceMockDeleteExpectationOrigins
	results            *CategoryDomainServiceMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockDeleteParams contains parameters of the CategoryDomainService.Delete
type CategoryDomainServiceMockDeleteParams struct {
	ctx context.Context
	id  int64
}

// CategoryDomainServiceMockDeleteParamPtrs contains pointers to parameters of the CategoryDomainService.Delete
type CategoryDomainServiceMockDeleteParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// CategoryDomainServiceMockDeleteResults contains results of the CategoryDomainService.Delete
type CategoryDomainServiceMockDeleteResults struct {
	err error
}

// CategoryDomainServiceMockDeleteOrigins contains origins of expectations of the CategoryDomainService.Delete
type CategoryDomainServiceMockDeleteExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mCategoryDomainServiceMockDelete) Optional() *mCategoryDomainServiceMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for CategoryDomainService.Delete
func (mmDelete *mCategoryDomainServiceMockDelete) Expect(ctx context.Context, id int64) *mCategoryDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &CategoryDomainServiceMockDeleteParams{ctx, id}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectCtxParam1 sets up expected param ctx for CategoryDomainService.Delete
func (mmDelete *mCategoryDomainServiceMockDelete) ExpectCtxParam1(ctx context.Context) *mCategoryDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.ctx = &ctx
	mmDelete.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectIdParam2 sets up expected param id for CategoryDomainService.Delete
func (mmDelete *mCategoryDomainServiceMockDelete) ExpectIdParam2(id int64) *mCategoryDomainServiceMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryDomainServiceMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryDomainServiceMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.id = &id
	mmDelete.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.Delete
func (mmDelete *mCategoryDomainServiceMockDelete) Inspect(f func(ctx context.Context, id int64)) *mCategoryDomainServiceMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by CategoryDomainService.Delete
func (mmDelete *mCategoryDomainServiceMockDelete) Return(err error) *CategoryDomainServiceMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryDomainServiceMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &CategoryDomainServiceMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the CategoryDomainService.Delete method
func (mmDelete *mCategoryDomainServiceMockDelete) Set(f func(ctx context.Context, id int64) (err error)) *CategoryDomainServiceMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the CategoryDomainService.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mCategoryDomainServiceMockDelete) When(ctx context.Context, id int64) *CategoryDomainServiceMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryDomainServiceMock.Delete mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &CategoryDomainServiceMockDeleteParams{ctx, id},
		expectationOrigins: CategoryDomainServiceMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.Delete return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockDeleteExpectation) Then(err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockDeleteResults{err}
	return e.mock
}

// Times sets number of times CategoryDomainService.Delete should be invoked
func (mmDelete *mCategoryDomainServiceMockDelete) Times(n uint64) *mCategoryDomainServiceMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of CategoryDomainServiceMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mCategoryDomainServiceMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_service.CategoryDomainService
func (mmDelete *CategoryDomainServiceMock) Delete(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(ctx, id)
	}

	mm_params := CategoryDomainServiceMockDeleteParams{ctx, id}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockDeleteParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDelete.t.Errorf("CategoryDomainServiceMock.Delete got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDelete.t.Errorf("CategoryDomainServiceMock.Delete got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("CategoryDomainServiceMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the CategoryDomainServiceMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(ctx, id)
	}
	mmDelete.t.Fatalf("Unexpected call to CategoryDomainServiceMock.Delete. %v %v", ctx, id)
	return
}

// DeleteAfterCounter returns a count of finished CategoryDomainServiceMock.Delete invocations
func (mmDelete *CategoryDomainServiceMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of CategoryDomainServiceMock.Delete invocations
func (mmDelete *CategoryDomainServiceMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mCategoryDomainServiceMockDelete) Calls() []*CategoryDomainServiceMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mCategoryDomainServiceMockExistLinkWithGroup struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockExistLinkWithGroupExpectation
	expectations       []*CategoryDomainServiceMockExistLinkWithGroupExpectation

	callArgs []*CategoryDomainServiceMockExistLinkWithGroupParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockExistLinkWithGroupExpectation specifies expectation struct of the CategoryDomainService.ExistLinkWithGroup
type CategoryDomainServiceMockExistLinkWithGroupExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockExistLinkWithGroupParams
	paramPtrs          *CategoryDomainServiceMockExistLinkWithGroupParamPtrs
	expectationOrigins CategoryDomainServiceMockExistLinkWithGroupExpectationOrigins
	results            *CategoryDomainServiceMockExistLinkWithGroupResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockExistLinkWithGroupParams contains parameters of the CategoryDomainService.ExistLinkWithGroup
type CategoryDomainServiceMockExistLinkWithGroupParams struct {
	categoryID int64
	groupID    int64
}

// CategoryDomainServiceMockExistLinkWithGroupParamPtrs contains pointers to parameters of the CategoryDomainService.ExistLinkWithGroup
type CategoryDomainServiceMockExistLinkWithGroupParamPtrs struct {
	categoryID *int64
	groupID    *int64
}

// CategoryDomainServiceMockExistLinkWithGroupResults contains results of the CategoryDomainService.ExistLinkWithGroup
type CategoryDomainServiceMockExistLinkWithGroupResults struct {
	b1  bool
	err error
}

// CategoryDomainServiceMockExistLinkWithGroupOrigins contains origins of expectations of the CategoryDomainService.ExistLinkWithGroup
type CategoryDomainServiceMockExistLinkWithGroupExpectationOrigins struct {
	origin           string
	originCategoryID string
	originGroupID    string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Optional() *mCategoryDomainServiceMockExistLinkWithGroup {
	mmExistLinkWithGroup.optional = true
	return mmExistLinkWithGroup
}

// Expect sets up expected params for CategoryDomainService.ExistLinkWithGroup
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Expect(categoryID int64, groupID int64) *mCategoryDomainServiceMockExistLinkWithGroup {
	if mmExistLinkWithGroup.mock.funcExistLinkWithGroup != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Set")
	}

	if mmExistLinkWithGroup.defaultExpectation == nil {
		mmExistLinkWithGroup.defaultExpectation = &CategoryDomainServiceMockExistLinkWithGroupExpectation{}
	}

	if mmExistLinkWithGroup.defaultExpectation.paramPtrs != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by ExpectParams functions")
	}

	mmExistLinkWithGroup.defaultExpectation.params = &CategoryDomainServiceMockExistLinkWithGroupParams{categoryID, groupID}
	mmExistLinkWithGroup.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistLinkWithGroup.expectations {
		if minimock.Equal(e.params, mmExistLinkWithGroup.defaultExpectation.params) {
			mmExistLinkWithGroup.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistLinkWithGroup.defaultExpectation.params)
		}
	}

	return mmExistLinkWithGroup
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryDomainService.ExistLinkWithGroup
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) ExpectCategoryIDParam1(categoryID int64) *mCategoryDomainServiceMockExistLinkWithGroup {
	if mmExistLinkWithGroup.mock.funcExistLinkWithGroup != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Set")
	}

	if mmExistLinkWithGroup.defaultExpectation == nil {
		mmExistLinkWithGroup.defaultExpectation = &CategoryDomainServiceMockExistLinkWithGroupExpectation{}
	}

	if mmExistLinkWithGroup.defaultExpectation.params != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Expect")
	}

	if mmExistLinkWithGroup.defaultExpectation.paramPtrs == nil {
		mmExistLinkWithGroup.defaultExpectation.paramPtrs = &CategoryDomainServiceMockExistLinkWithGroupParamPtrs{}
	}
	mmExistLinkWithGroup.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmExistLinkWithGroup.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmExistLinkWithGroup
}

// ExpectGroupIDParam2 sets up expected param groupID for CategoryDomainService.ExistLinkWithGroup
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) ExpectGroupIDParam2(groupID int64) *mCategoryDomainServiceMockExistLinkWithGroup {
	if mmExistLinkWithGroup.mock.funcExistLinkWithGroup != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Set")
	}

	if mmExistLinkWithGroup.defaultExpectation == nil {
		mmExistLinkWithGroup.defaultExpectation = &CategoryDomainServiceMockExistLinkWithGroupExpectation{}
	}

	if mmExistLinkWithGroup.defaultExpectation.params != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Expect")
	}

	if mmExistLinkWithGroup.defaultExpectation.paramPtrs == nil {
		mmExistLinkWithGroup.defaultExpectation.paramPtrs = &CategoryDomainServiceMockExistLinkWithGroupParamPtrs{}
	}
	mmExistLinkWithGroup.defaultExpectation.paramPtrs.groupID = &groupID
	mmExistLinkWithGroup.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmExistLinkWithGroup
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.ExistLinkWithGroup
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Inspect(f func(categoryID int64, groupID int64)) *mCategoryDomainServiceMockExistLinkWithGroup {
	if mmExistLinkWithGroup.mock.inspectFuncExistLinkWithGroup != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.ExistLinkWithGroup")
	}

	mmExistLinkWithGroup.mock.inspectFuncExistLinkWithGroup = f

	return mmExistLinkWithGroup
}

// Return sets up results that will be returned by CategoryDomainService.ExistLinkWithGroup
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Return(b1 bool, err error) *CategoryDomainServiceMock {
	if mmExistLinkWithGroup.mock.funcExistLinkWithGroup != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Set")
	}

	if mmExistLinkWithGroup.defaultExpectation == nil {
		mmExistLinkWithGroup.defaultExpectation = &CategoryDomainServiceMockExistLinkWithGroupExpectation{mock: mmExistLinkWithGroup.mock}
	}
	mmExistLinkWithGroup.defaultExpectation.results = &CategoryDomainServiceMockExistLinkWithGroupResults{b1, err}
	mmExistLinkWithGroup.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithGroup.mock
}

// Set uses given function f to mock the CategoryDomainService.ExistLinkWithGroup method
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Set(f func(categoryID int64, groupID int64) (b1 bool, err error)) *CategoryDomainServiceMock {
	if mmExistLinkWithGroup.defaultExpectation != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.ExistLinkWithGroup method")
	}

	if len(mmExistLinkWithGroup.expectations) > 0 {
		mmExistLinkWithGroup.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.ExistLinkWithGroup method")
	}

	mmExistLinkWithGroup.mock.funcExistLinkWithGroup = f
	mmExistLinkWithGroup.mock.funcExistLinkWithGroupOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithGroup.mock
}

// When sets expectation for the CategoryDomainService.ExistLinkWithGroup which will trigger the result defined by the following
// Then helper
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) When(categoryID int64, groupID int64) *CategoryDomainServiceMockExistLinkWithGroupExpectation {
	if mmExistLinkWithGroup.mock.funcExistLinkWithGroup != nil {
		mmExistLinkWithGroup.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithGroup mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockExistLinkWithGroupExpectation{
		mock:               mmExistLinkWithGroup.mock,
		params:             &CategoryDomainServiceMockExistLinkWithGroupParams{categoryID, groupID},
		expectationOrigins: CategoryDomainServiceMockExistLinkWithGroupExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistLinkWithGroup.expectations = append(mmExistLinkWithGroup.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.ExistLinkWithGroup return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockExistLinkWithGroupExpectation) Then(b1 bool, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockExistLinkWithGroupResults{b1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.ExistLinkWithGroup should be invoked
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Times(n uint64) *mCategoryDomainServiceMockExistLinkWithGroup {
	if n == 0 {
		mmExistLinkWithGroup.mock.t.Fatalf("Times of CategoryDomainServiceMock.ExistLinkWithGroup mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistLinkWithGroup.expectedInvocations, n)
	mmExistLinkWithGroup.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithGroup
}

func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) invocationsDone() bool {
	if len(mmExistLinkWithGroup.expectations) == 0 && mmExistLinkWithGroup.defaultExpectation == nil && mmExistLinkWithGroup.mock.funcExistLinkWithGroup == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistLinkWithGroup.mock.afterExistLinkWithGroupCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistLinkWithGroup.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistLinkWithGroup implements mm_service.CategoryDomainService
func (mmExistLinkWithGroup *CategoryDomainServiceMock) ExistLinkWithGroup(categoryID int64, groupID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistLinkWithGroup.beforeExistLinkWithGroupCounter, 1)
	defer mm_atomic.AddUint64(&mmExistLinkWithGroup.afterExistLinkWithGroupCounter, 1)

	mmExistLinkWithGroup.t.Helper()

	if mmExistLinkWithGroup.inspectFuncExistLinkWithGroup != nil {
		mmExistLinkWithGroup.inspectFuncExistLinkWithGroup(categoryID, groupID)
	}

	mm_params := CategoryDomainServiceMockExistLinkWithGroupParams{categoryID, groupID}

	// Record call args
	mmExistLinkWithGroup.ExistLinkWithGroupMock.mutex.Lock()
	mmExistLinkWithGroup.ExistLinkWithGroupMock.callArgs = append(mmExistLinkWithGroup.ExistLinkWithGroupMock.callArgs, &mm_params)
	mmExistLinkWithGroup.ExistLinkWithGroupMock.mutex.Unlock()

	for _, e := range mmExistLinkWithGroup.ExistLinkWithGroupMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.Counter, 1)
		mm_want := mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.params
		mm_want_ptrs := mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockExistLinkWithGroupParams{categoryID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmExistLinkWithGroup.t.Errorf("CategoryDomainServiceMock.ExistLinkWithGroup got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmExistLinkWithGroup.t.Errorf("CategoryDomainServiceMock.ExistLinkWithGroup got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistLinkWithGroup.t.Errorf("CategoryDomainServiceMock.ExistLinkWithGroup got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistLinkWithGroup.ExistLinkWithGroupMock.defaultExpectation.results
		if mm_results == nil {
			mmExistLinkWithGroup.t.Fatal("No results are set for the CategoryDomainServiceMock.ExistLinkWithGroup")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistLinkWithGroup.funcExistLinkWithGroup != nil {
		return mmExistLinkWithGroup.funcExistLinkWithGroup(categoryID, groupID)
	}
	mmExistLinkWithGroup.t.Fatalf("Unexpected call to CategoryDomainServiceMock.ExistLinkWithGroup. %v %v", categoryID, groupID)
	return
}

// ExistLinkWithGroupAfterCounter returns a count of finished CategoryDomainServiceMock.ExistLinkWithGroup invocations
func (mmExistLinkWithGroup *CategoryDomainServiceMock) ExistLinkWithGroupAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistLinkWithGroup.afterExistLinkWithGroupCounter)
}

// ExistLinkWithGroupBeforeCounter returns a count of CategoryDomainServiceMock.ExistLinkWithGroup invocations
func (mmExistLinkWithGroup *CategoryDomainServiceMock) ExistLinkWithGroupBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistLinkWithGroup.beforeExistLinkWithGroupCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.ExistLinkWithGroup.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistLinkWithGroup *mCategoryDomainServiceMockExistLinkWithGroup) Calls() []*CategoryDomainServiceMockExistLinkWithGroupParams {
	mmExistLinkWithGroup.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockExistLinkWithGroupParams, len(mmExistLinkWithGroup.callArgs))
	copy(argCopy, mmExistLinkWithGroup.callArgs)

	mmExistLinkWithGroup.mutex.RUnlock()

	return argCopy
}

// MinimockExistLinkWithGroupDone returns true if the count of the ExistLinkWithGroup invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockExistLinkWithGroupDone() bool {
	if m.ExistLinkWithGroupMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistLinkWithGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistLinkWithGroupMock.invocationsDone()
}

// MinimockExistLinkWithGroupInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockExistLinkWithGroupInspect() {
	for _, e := range m.ExistLinkWithGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithGroup at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistLinkWithGroupCounter := mm_atomic.LoadUint64(&m.afterExistLinkWithGroupCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistLinkWithGroupMock.defaultExpectation != nil && afterExistLinkWithGroupCounter < 1 {
		if m.ExistLinkWithGroupMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithGroup at\n%s", m.ExistLinkWithGroupMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithGroup at\n%s with params: %#v", m.ExistLinkWithGroupMock.defaultExpectation.expectationOrigins.origin, *m.ExistLinkWithGroupMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistLinkWithGroup != nil && afterExistLinkWithGroupCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithGroup at\n%s", m.funcExistLinkWithGroupOrigin)
	}

	if !m.ExistLinkWithGroupMock.invocationsDone() && afterExistLinkWithGroupCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.ExistLinkWithGroup at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistLinkWithGroupMock.expectedInvocations), m.ExistLinkWithGroupMock.expectedInvocationsOrigin, afterExistLinkWithGroupCounter)
	}
}

type mCategoryDomainServiceMockExistLinkWithPermission struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockExistLinkWithPermissionExpectation
	expectations       []*CategoryDomainServiceMockExistLinkWithPermissionExpectation

	callArgs []*CategoryDomainServiceMockExistLinkWithPermissionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockExistLinkWithPermissionExpectation specifies expectation struct of the CategoryDomainService.ExistLinkWithPermission
type CategoryDomainServiceMockExistLinkWithPermissionExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockExistLinkWithPermissionParams
	paramPtrs          *CategoryDomainServiceMockExistLinkWithPermissionParamPtrs
	expectationOrigins CategoryDomainServiceMockExistLinkWithPermissionExpectationOrigins
	results            *CategoryDomainServiceMockExistLinkWithPermissionResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockExistLinkWithPermissionParams contains parameters of the CategoryDomainService.ExistLinkWithPermission
type CategoryDomainServiceMockExistLinkWithPermissionParams struct {
	categoryID   int64
	permissionID int64
}

// CategoryDomainServiceMockExistLinkWithPermissionParamPtrs contains pointers to parameters of the CategoryDomainService.ExistLinkWithPermission
type CategoryDomainServiceMockExistLinkWithPermissionParamPtrs struct {
	categoryID   *int64
	permissionID *int64
}

// CategoryDomainServiceMockExistLinkWithPermissionResults contains results of the CategoryDomainService.ExistLinkWithPermission
type CategoryDomainServiceMockExistLinkWithPermissionResults struct {
	b1  bool
	err error
}

// CategoryDomainServiceMockExistLinkWithPermissionOrigins contains origins of expectations of the CategoryDomainService.ExistLinkWithPermission
type CategoryDomainServiceMockExistLinkWithPermissionExpectationOrigins struct {
	origin             string
	originCategoryID   string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Optional() *mCategoryDomainServiceMockExistLinkWithPermission {
	mmExistLinkWithPermission.optional = true
	return mmExistLinkWithPermission
}

// Expect sets up expected params for CategoryDomainService.ExistLinkWithPermission
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Expect(categoryID int64, permissionID int64) *mCategoryDomainServiceMockExistLinkWithPermission {
	if mmExistLinkWithPermission.mock.funcExistLinkWithPermission != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Set")
	}

	if mmExistLinkWithPermission.defaultExpectation == nil {
		mmExistLinkWithPermission.defaultExpectation = &CategoryDomainServiceMockExistLinkWithPermissionExpectation{}
	}

	if mmExistLinkWithPermission.defaultExpectation.paramPtrs != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by ExpectParams functions")
	}

	mmExistLinkWithPermission.defaultExpectation.params = &CategoryDomainServiceMockExistLinkWithPermissionParams{categoryID, permissionID}
	mmExistLinkWithPermission.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistLinkWithPermission.expectations {
		if minimock.Equal(e.params, mmExistLinkWithPermission.defaultExpectation.params) {
			mmExistLinkWithPermission.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistLinkWithPermission.defaultExpectation.params)
		}
	}

	return mmExistLinkWithPermission
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryDomainService.ExistLinkWithPermission
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) ExpectCategoryIDParam1(categoryID int64) *mCategoryDomainServiceMockExistLinkWithPermission {
	if mmExistLinkWithPermission.mock.funcExistLinkWithPermission != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Set")
	}

	if mmExistLinkWithPermission.defaultExpectation == nil {
		mmExistLinkWithPermission.defaultExpectation = &CategoryDomainServiceMockExistLinkWithPermissionExpectation{}
	}

	if mmExistLinkWithPermission.defaultExpectation.params != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Expect")
	}

	if mmExistLinkWithPermission.defaultExpectation.paramPtrs == nil {
		mmExistLinkWithPermission.defaultExpectation.paramPtrs = &CategoryDomainServiceMockExistLinkWithPermissionParamPtrs{}
	}
	mmExistLinkWithPermission.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmExistLinkWithPermission.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmExistLinkWithPermission
}

// ExpectPermissionIDParam2 sets up expected param permissionID for CategoryDomainService.ExistLinkWithPermission
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) ExpectPermissionIDParam2(permissionID int64) *mCategoryDomainServiceMockExistLinkWithPermission {
	if mmExistLinkWithPermission.mock.funcExistLinkWithPermission != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Set")
	}

	if mmExistLinkWithPermission.defaultExpectation == nil {
		mmExistLinkWithPermission.defaultExpectation = &CategoryDomainServiceMockExistLinkWithPermissionExpectation{}
	}

	if mmExistLinkWithPermission.defaultExpectation.params != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Expect")
	}

	if mmExistLinkWithPermission.defaultExpectation.paramPtrs == nil {
		mmExistLinkWithPermission.defaultExpectation.paramPtrs = &CategoryDomainServiceMockExistLinkWithPermissionParamPtrs{}
	}
	mmExistLinkWithPermission.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmExistLinkWithPermission.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmExistLinkWithPermission
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.ExistLinkWithPermission
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Inspect(f func(categoryID int64, permissionID int64)) *mCategoryDomainServiceMockExistLinkWithPermission {
	if mmExistLinkWithPermission.mock.inspectFuncExistLinkWithPermission != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.ExistLinkWithPermission")
	}

	mmExistLinkWithPermission.mock.inspectFuncExistLinkWithPermission = f

	return mmExistLinkWithPermission
}

// Return sets up results that will be returned by CategoryDomainService.ExistLinkWithPermission
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Return(b1 bool, err error) *CategoryDomainServiceMock {
	if mmExistLinkWithPermission.mock.funcExistLinkWithPermission != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Set")
	}

	if mmExistLinkWithPermission.defaultExpectation == nil {
		mmExistLinkWithPermission.defaultExpectation = &CategoryDomainServiceMockExistLinkWithPermissionExpectation{mock: mmExistLinkWithPermission.mock}
	}
	mmExistLinkWithPermission.defaultExpectation.results = &CategoryDomainServiceMockExistLinkWithPermissionResults{b1, err}
	mmExistLinkWithPermission.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithPermission.mock
}

// Set uses given function f to mock the CategoryDomainService.ExistLinkWithPermission method
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Set(f func(categoryID int64, permissionID int64) (b1 bool, err error)) *CategoryDomainServiceMock {
	if mmExistLinkWithPermission.defaultExpectation != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.ExistLinkWithPermission method")
	}

	if len(mmExistLinkWithPermission.expectations) > 0 {
		mmExistLinkWithPermission.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.ExistLinkWithPermission method")
	}

	mmExistLinkWithPermission.mock.funcExistLinkWithPermission = f
	mmExistLinkWithPermission.mock.funcExistLinkWithPermissionOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithPermission.mock
}

// When sets expectation for the CategoryDomainService.ExistLinkWithPermission which will trigger the result defined by the following
// Then helper
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) When(categoryID int64, permissionID int64) *CategoryDomainServiceMockExistLinkWithPermissionExpectation {
	if mmExistLinkWithPermission.mock.funcExistLinkWithPermission != nil {
		mmExistLinkWithPermission.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithPermission mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockExistLinkWithPermissionExpectation{
		mock:               mmExistLinkWithPermission.mock,
		params:             &CategoryDomainServiceMockExistLinkWithPermissionParams{categoryID, permissionID},
		expectationOrigins: CategoryDomainServiceMockExistLinkWithPermissionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistLinkWithPermission.expectations = append(mmExistLinkWithPermission.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.ExistLinkWithPermission return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockExistLinkWithPermissionExpectation) Then(b1 bool, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockExistLinkWithPermissionResults{b1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.ExistLinkWithPermission should be invoked
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Times(n uint64) *mCategoryDomainServiceMockExistLinkWithPermission {
	if n == 0 {
		mmExistLinkWithPermission.mock.t.Fatalf("Times of CategoryDomainServiceMock.ExistLinkWithPermission mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistLinkWithPermission.expectedInvocations, n)
	mmExistLinkWithPermission.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithPermission
}

func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) invocationsDone() bool {
	if len(mmExistLinkWithPermission.expectations) == 0 && mmExistLinkWithPermission.defaultExpectation == nil && mmExistLinkWithPermission.mock.funcExistLinkWithPermission == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistLinkWithPermission.mock.afterExistLinkWithPermissionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistLinkWithPermission.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistLinkWithPermission implements mm_service.CategoryDomainService
func (mmExistLinkWithPermission *CategoryDomainServiceMock) ExistLinkWithPermission(categoryID int64, permissionID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistLinkWithPermission.beforeExistLinkWithPermissionCounter, 1)
	defer mm_atomic.AddUint64(&mmExistLinkWithPermission.afterExistLinkWithPermissionCounter, 1)

	mmExistLinkWithPermission.t.Helper()

	if mmExistLinkWithPermission.inspectFuncExistLinkWithPermission != nil {
		mmExistLinkWithPermission.inspectFuncExistLinkWithPermission(categoryID, permissionID)
	}

	mm_params := CategoryDomainServiceMockExistLinkWithPermissionParams{categoryID, permissionID}

	// Record call args
	mmExistLinkWithPermission.ExistLinkWithPermissionMock.mutex.Lock()
	mmExistLinkWithPermission.ExistLinkWithPermissionMock.callArgs = append(mmExistLinkWithPermission.ExistLinkWithPermissionMock.callArgs, &mm_params)
	mmExistLinkWithPermission.ExistLinkWithPermissionMock.mutex.Unlock()

	for _, e := range mmExistLinkWithPermission.ExistLinkWithPermissionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.Counter, 1)
		mm_want := mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.params
		mm_want_ptrs := mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockExistLinkWithPermissionParams{categoryID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmExistLinkWithPermission.t.Errorf("CategoryDomainServiceMock.ExistLinkWithPermission got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmExistLinkWithPermission.t.Errorf("CategoryDomainServiceMock.ExistLinkWithPermission got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistLinkWithPermission.t.Errorf("CategoryDomainServiceMock.ExistLinkWithPermission got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistLinkWithPermission.ExistLinkWithPermissionMock.defaultExpectation.results
		if mm_results == nil {
			mmExistLinkWithPermission.t.Fatal("No results are set for the CategoryDomainServiceMock.ExistLinkWithPermission")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistLinkWithPermission.funcExistLinkWithPermission != nil {
		return mmExistLinkWithPermission.funcExistLinkWithPermission(categoryID, permissionID)
	}
	mmExistLinkWithPermission.t.Fatalf("Unexpected call to CategoryDomainServiceMock.ExistLinkWithPermission. %v %v", categoryID, permissionID)
	return
}

// ExistLinkWithPermissionAfterCounter returns a count of finished CategoryDomainServiceMock.ExistLinkWithPermission invocations
func (mmExistLinkWithPermission *CategoryDomainServiceMock) ExistLinkWithPermissionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistLinkWithPermission.afterExistLinkWithPermissionCounter)
}

// ExistLinkWithPermissionBeforeCounter returns a count of CategoryDomainServiceMock.ExistLinkWithPermission invocations
func (mmExistLinkWithPermission *CategoryDomainServiceMock) ExistLinkWithPermissionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistLinkWithPermission.beforeExistLinkWithPermissionCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.ExistLinkWithPermission.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistLinkWithPermission *mCategoryDomainServiceMockExistLinkWithPermission) Calls() []*CategoryDomainServiceMockExistLinkWithPermissionParams {
	mmExistLinkWithPermission.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockExistLinkWithPermissionParams, len(mmExistLinkWithPermission.callArgs))
	copy(argCopy, mmExistLinkWithPermission.callArgs)

	mmExistLinkWithPermission.mutex.RUnlock()

	return argCopy
}

// MinimockExistLinkWithPermissionDone returns true if the count of the ExistLinkWithPermission invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockExistLinkWithPermissionDone() bool {
	if m.ExistLinkWithPermissionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistLinkWithPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistLinkWithPermissionMock.invocationsDone()
}

// MinimockExistLinkWithPermissionInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockExistLinkWithPermissionInspect() {
	for _, e := range m.ExistLinkWithPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithPermission at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistLinkWithPermissionCounter := mm_atomic.LoadUint64(&m.afterExistLinkWithPermissionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistLinkWithPermissionMock.defaultExpectation != nil && afterExistLinkWithPermissionCounter < 1 {
		if m.ExistLinkWithPermissionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithPermission at\n%s", m.ExistLinkWithPermissionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithPermission at\n%s with params: %#v", m.ExistLinkWithPermissionMock.defaultExpectation.expectationOrigins.origin, *m.ExistLinkWithPermissionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistLinkWithPermission != nil && afterExistLinkWithPermissionCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithPermission at\n%s", m.funcExistLinkWithPermissionOrigin)
	}

	if !m.ExistLinkWithPermissionMock.invocationsDone() && afterExistLinkWithPermissionCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.ExistLinkWithPermission at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistLinkWithPermissionMock.expectedInvocations), m.ExistLinkWithPermissionMock.expectedInvocationsOrigin, afterExistLinkWithPermissionCounter)
	}
}

type mCategoryDomainServiceMockExistLinkWithRole struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockExistLinkWithRoleExpectation
	expectations       []*CategoryDomainServiceMockExistLinkWithRoleExpectation

	callArgs []*CategoryDomainServiceMockExistLinkWithRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockExistLinkWithRoleExpectation specifies expectation struct of the CategoryDomainService.ExistLinkWithRole
type CategoryDomainServiceMockExistLinkWithRoleExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockExistLinkWithRoleParams
	paramPtrs          *CategoryDomainServiceMockExistLinkWithRoleParamPtrs
	expectationOrigins CategoryDomainServiceMockExistLinkWithRoleExpectationOrigins
	results            *CategoryDomainServiceMockExistLinkWithRoleResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockExistLinkWithRoleParams contains parameters of the CategoryDomainService.ExistLinkWithRole
type CategoryDomainServiceMockExistLinkWithRoleParams struct {
	categoryID int64
	roleID     int64
}

// CategoryDomainServiceMockExistLinkWithRoleParamPtrs contains pointers to parameters of the CategoryDomainService.ExistLinkWithRole
type CategoryDomainServiceMockExistLinkWithRoleParamPtrs struct {
	categoryID *int64
	roleID     *int64
}

// CategoryDomainServiceMockExistLinkWithRoleResults contains results of the CategoryDomainService.ExistLinkWithRole
type CategoryDomainServiceMockExistLinkWithRoleResults struct {
	b1  bool
	err error
}

// CategoryDomainServiceMockExistLinkWithRoleOrigins contains origins of expectations of the CategoryDomainService.ExistLinkWithRole
type CategoryDomainServiceMockExistLinkWithRoleExpectationOrigins struct {
	origin           string
	originCategoryID string
	originRoleID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Optional() *mCategoryDomainServiceMockExistLinkWithRole {
	mmExistLinkWithRole.optional = true
	return mmExistLinkWithRole
}

// Expect sets up expected params for CategoryDomainService.ExistLinkWithRole
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Expect(categoryID int64, roleID int64) *mCategoryDomainServiceMockExistLinkWithRole {
	if mmExistLinkWithRole.mock.funcExistLinkWithRole != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Set")
	}

	if mmExistLinkWithRole.defaultExpectation == nil {
		mmExistLinkWithRole.defaultExpectation = &CategoryDomainServiceMockExistLinkWithRoleExpectation{}
	}

	if mmExistLinkWithRole.defaultExpectation.paramPtrs != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by ExpectParams functions")
	}

	mmExistLinkWithRole.defaultExpectation.params = &CategoryDomainServiceMockExistLinkWithRoleParams{categoryID, roleID}
	mmExistLinkWithRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmExistLinkWithRole.expectations {
		if minimock.Equal(e.params, mmExistLinkWithRole.defaultExpectation.params) {
			mmExistLinkWithRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmExistLinkWithRole.defaultExpectation.params)
		}
	}

	return mmExistLinkWithRole
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryDomainService.ExistLinkWithRole
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) ExpectCategoryIDParam1(categoryID int64) *mCategoryDomainServiceMockExistLinkWithRole {
	if mmExistLinkWithRole.mock.funcExistLinkWithRole != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Set")
	}

	if mmExistLinkWithRole.defaultExpectation == nil {
		mmExistLinkWithRole.defaultExpectation = &CategoryDomainServiceMockExistLinkWithRoleExpectation{}
	}

	if mmExistLinkWithRole.defaultExpectation.params != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Expect")
	}

	if mmExistLinkWithRole.defaultExpectation.paramPtrs == nil {
		mmExistLinkWithRole.defaultExpectation.paramPtrs = &CategoryDomainServiceMockExistLinkWithRoleParamPtrs{}
	}
	mmExistLinkWithRole.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmExistLinkWithRole.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmExistLinkWithRole
}

// ExpectRoleIDParam2 sets up expected param roleID for CategoryDomainService.ExistLinkWithRole
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) ExpectRoleIDParam2(roleID int64) *mCategoryDomainServiceMockExistLinkWithRole {
	if mmExistLinkWithRole.mock.funcExistLinkWithRole != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Set")
	}

	if mmExistLinkWithRole.defaultExpectation == nil {
		mmExistLinkWithRole.defaultExpectation = &CategoryDomainServiceMockExistLinkWithRoleExpectation{}
	}

	if mmExistLinkWithRole.defaultExpectation.params != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Expect")
	}

	if mmExistLinkWithRole.defaultExpectation.paramPtrs == nil {
		mmExistLinkWithRole.defaultExpectation.paramPtrs = &CategoryDomainServiceMockExistLinkWithRoleParamPtrs{}
	}
	mmExistLinkWithRole.defaultExpectation.paramPtrs.roleID = &roleID
	mmExistLinkWithRole.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmExistLinkWithRole
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.ExistLinkWithRole
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Inspect(f func(categoryID int64, roleID int64)) *mCategoryDomainServiceMockExistLinkWithRole {
	if mmExistLinkWithRole.mock.inspectFuncExistLinkWithRole != nil {
		mmExistLinkWithRole.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.ExistLinkWithRole")
	}

	mmExistLinkWithRole.mock.inspectFuncExistLinkWithRole = f

	return mmExistLinkWithRole
}

// Return sets up results that will be returned by CategoryDomainService.ExistLinkWithRole
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Return(b1 bool, err error) *CategoryDomainServiceMock {
	if mmExistLinkWithRole.mock.funcExistLinkWithRole != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Set")
	}

	if mmExistLinkWithRole.defaultExpectation == nil {
		mmExistLinkWithRole.defaultExpectation = &CategoryDomainServiceMockExistLinkWithRoleExpectation{mock: mmExistLinkWithRole.mock}
	}
	mmExistLinkWithRole.defaultExpectation.results = &CategoryDomainServiceMockExistLinkWithRoleResults{b1, err}
	mmExistLinkWithRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithRole.mock
}

// Set uses given function f to mock the CategoryDomainService.ExistLinkWithRole method
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Set(f func(categoryID int64, roleID int64) (b1 bool, err error)) *CategoryDomainServiceMock {
	if mmExistLinkWithRole.defaultExpectation != nil {
		mmExistLinkWithRole.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.ExistLinkWithRole method")
	}

	if len(mmExistLinkWithRole.expectations) > 0 {
		mmExistLinkWithRole.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.ExistLinkWithRole method")
	}

	mmExistLinkWithRole.mock.funcExistLinkWithRole = f
	mmExistLinkWithRole.mock.funcExistLinkWithRoleOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithRole.mock
}

// When sets expectation for the CategoryDomainService.ExistLinkWithRole which will trigger the result defined by the following
// Then helper
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) When(categoryID int64, roleID int64) *CategoryDomainServiceMockExistLinkWithRoleExpectation {
	if mmExistLinkWithRole.mock.funcExistLinkWithRole != nil {
		mmExistLinkWithRole.mock.t.Fatalf("CategoryDomainServiceMock.ExistLinkWithRole mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockExistLinkWithRoleExpectation{
		mock:               mmExistLinkWithRole.mock,
		params:             &CategoryDomainServiceMockExistLinkWithRoleParams{categoryID, roleID},
		expectationOrigins: CategoryDomainServiceMockExistLinkWithRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmExistLinkWithRole.expectations = append(mmExistLinkWithRole.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.ExistLinkWithRole return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockExistLinkWithRoleExpectation) Then(b1 bool, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockExistLinkWithRoleResults{b1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.ExistLinkWithRole should be invoked
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Times(n uint64) *mCategoryDomainServiceMockExistLinkWithRole {
	if n == 0 {
		mmExistLinkWithRole.mock.t.Fatalf("Times of CategoryDomainServiceMock.ExistLinkWithRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmExistLinkWithRole.expectedInvocations, n)
	mmExistLinkWithRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmExistLinkWithRole
}

func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) invocationsDone() bool {
	if len(mmExistLinkWithRole.expectations) == 0 && mmExistLinkWithRole.defaultExpectation == nil && mmExistLinkWithRole.mock.funcExistLinkWithRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmExistLinkWithRole.mock.afterExistLinkWithRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmExistLinkWithRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// ExistLinkWithRole implements mm_service.CategoryDomainService
func (mmExistLinkWithRole *CategoryDomainServiceMock) ExistLinkWithRole(categoryID int64, roleID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmExistLinkWithRole.beforeExistLinkWithRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmExistLinkWithRole.afterExistLinkWithRoleCounter, 1)

	mmExistLinkWithRole.t.Helper()

	if mmExistLinkWithRole.inspectFuncExistLinkWithRole != nil {
		mmExistLinkWithRole.inspectFuncExistLinkWithRole(categoryID, roleID)
	}

	mm_params := CategoryDomainServiceMockExistLinkWithRoleParams{categoryID, roleID}

	// Record call args
	mmExistLinkWithRole.ExistLinkWithRoleMock.mutex.Lock()
	mmExistLinkWithRole.ExistLinkWithRoleMock.callArgs = append(mmExistLinkWithRole.ExistLinkWithRoleMock.callArgs, &mm_params)
	mmExistLinkWithRole.ExistLinkWithRoleMock.mutex.Unlock()

	for _, e := range mmExistLinkWithRole.ExistLinkWithRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.params
		mm_want_ptrs := mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockExistLinkWithRoleParams{categoryID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmExistLinkWithRole.t.Errorf("CategoryDomainServiceMock.ExistLinkWithRole got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmExistLinkWithRole.t.Errorf("CategoryDomainServiceMock.ExistLinkWithRole got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmExistLinkWithRole.t.Errorf("CategoryDomainServiceMock.ExistLinkWithRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmExistLinkWithRole.ExistLinkWithRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmExistLinkWithRole.t.Fatal("No results are set for the CategoryDomainServiceMock.ExistLinkWithRole")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmExistLinkWithRole.funcExistLinkWithRole != nil {
		return mmExistLinkWithRole.funcExistLinkWithRole(categoryID, roleID)
	}
	mmExistLinkWithRole.t.Fatalf("Unexpected call to CategoryDomainServiceMock.ExistLinkWithRole. %v %v", categoryID, roleID)
	return
}

// ExistLinkWithRoleAfterCounter returns a count of finished CategoryDomainServiceMock.ExistLinkWithRole invocations
func (mmExistLinkWithRole *CategoryDomainServiceMock) ExistLinkWithRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistLinkWithRole.afterExistLinkWithRoleCounter)
}

// ExistLinkWithRoleBeforeCounter returns a count of CategoryDomainServiceMock.ExistLinkWithRole invocations
func (mmExistLinkWithRole *CategoryDomainServiceMock) ExistLinkWithRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmExistLinkWithRole.beforeExistLinkWithRoleCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.ExistLinkWithRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmExistLinkWithRole *mCategoryDomainServiceMockExistLinkWithRole) Calls() []*CategoryDomainServiceMockExistLinkWithRoleParams {
	mmExistLinkWithRole.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockExistLinkWithRoleParams, len(mmExistLinkWithRole.callArgs))
	copy(argCopy, mmExistLinkWithRole.callArgs)

	mmExistLinkWithRole.mutex.RUnlock()

	return argCopy
}

// MinimockExistLinkWithRoleDone returns true if the count of the ExistLinkWithRole invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockExistLinkWithRoleDone() bool {
	if m.ExistLinkWithRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.ExistLinkWithRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.ExistLinkWithRoleMock.invocationsDone()
}

// MinimockExistLinkWithRoleInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockExistLinkWithRoleInspect() {
	for _, e := range m.ExistLinkWithRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterExistLinkWithRoleCounter := mm_atomic.LoadUint64(&m.afterExistLinkWithRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.ExistLinkWithRoleMock.defaultExpectation != nil && afterExistLinkWithRoleCounter < 1 {
		if m.ExistLinkWithRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithRole at\n%s", m.ExistLinkWithRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithRole at\n%s with params: %#v", m.ExistLinkWithRoleMock.defaultExpectation.expectationOrigins.origin, *m.ExistLinkWithRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcExistLinkWithRole != nil && afterExistLinkWithRoleCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.ExistLinkWithRole at\n%s", m.funcExistLinkWithRoleOrigin)
	}

	if !m.ExistLinkWithRoleMock.invocationsDone() && afterExistLinkWithRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.ExistLinkWithRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.ExistLinkWithRoleMock.expectedInvocations), m.ExistLinkWithRoleMock.expectedInvocationsOrigin, afterExistLinkWithRoleCounter)
	}
}

type mCategoryDomainServiceMockGetAll struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockGetAllExpectation
	expectations       []*CategoryDomainServiceMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockGetAllExpectation specifies expectation struct of the CategoryDomainService.GetAll
type CategoryDomainServiceMockGetAllExpectation struct {
	mock *CategoryDomainServiceMock

	results      *CategoryDomainServiceMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryDomainServiceMockGetAllResults contains results of the CategoryDomainService.GetAll
type CategoryDomainServiceMockGetAllResults struct {
	ca1 []categoryentity.Category
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryDomainServiceMockGetAll) Optional() *mCategoryDomainServiceMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryDomainService.GetAll
func (mmGetAll *mCategoryDomainServiceMockGetAll) Expect() *mCategoryDomainServiceMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryDomainServiceMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.GetAll
func (mmGetAll *mCategoryDomainServiceMockGetAll) Inspect(f func()) *mCategoryDomainServiceMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryDomainService.GetAll
func (mmGetAll *mCategoryDomainServiceMockGetAll) Return(ca1 []categoryentity.Category, err error) *CategoryDomainServiceMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryDomainServiceMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryDomainServiceMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryDomainServiceMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryDomainService.GetAll method
func (mmGetAll *mCategoryDomainServiceMockGetAll) Set(f func() (ca1 []categoryentity.Category, err error)) *CategoryDomainServiceMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryDomainService.GetAll should be invoked
func (mmGetAll *mCategoryDomainServiceMockGetAll) Times(n uint64) *mCategoryDomainServiceMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryDomainServiceMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryDomainServiceMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_service.CategoryDomainService
func (mmGetAll *CategoryDomainServiceMock) GetAll() (ca1 []categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryDomainServiceMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryDomainServiceMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryDomainServiceMock.GetAll invocations
func (mmGetAll *CategoryDomainServiceMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryDomainServiceMock.GetAll invocations
func (mmGetAll *CategoryDomainServiceMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryDomainServiceMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryDomainServiceMockGetByID struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockGetByIDExpectation
	expectations       []*CategoryDomainServiceMockGetByIDExpectation

	callArgs []*CategoryDomainServiceMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockGetByIDExpectation specifies expectation struct of the CategoryDomainService.GetByID
type CategoryDomainServiceMockGetByIDExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockGetByIDParams
	paramPtrs          *CategoryDomainServiceMockGetByIDParamPtrs
	expectationOrigins CategoryDomainServiceMockGetByIDExpectationOrigins
	results            *CategoryDomainServiceMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockGetByIDParams contains parameters of the CategoryDomainService.GetByID
type CategoryDomainServiceMockGetByIDParams struct {
	id int64
}

// CategoryDomainServiceMockGetByIDParamPtrs contains pointers to parameters of the CategoryDomainService.GetByID
type CategoryDomainServiceMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryDomainServiceMockGetByIDResults contains results of the CategoryDomainService.GetByID
type CategoryDomainServiceMockGetByIDResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryDomainServiceMockGetByIDOrigins contains origins of expectations of the CategoryDomainService.GetByID
type CategoryDomainServiceMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryDomainServiceMockGetByID) Optional() *mCategoryDomainServiceMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryDomainService.GetByID
func (mmGetByID *mCategoryDomainServiceMockGetByID) Expect(id int64) *mCategoryDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryDomainServiceMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryDomainServiceMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryDomainService.GetByID
func (mmGetByID *mCategoryDomainServiceMockGetByID) ExpectIdParam1(id int64) *mCategoryDomainServiceMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryDomainServiceMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryDomainServiceMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryDomainServiceMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.GetByID
func (mmGetByID *mCategoryDomainServiceMockGetByID) Inspect(f func(id int64)) *mCategoryDomainServiceMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryDomainService.GetByID
func (mmGetByID *mCategoryDomainServiceMockGetByID) Return(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryDomainServiceMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryDomainServiceMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryDomainServiceMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryDomainService.GetByID method
func (mmGetByID *mCategoryDomainServiceMockGetByID) Set(f func(id int64) (c1 categoryentity.Category, err error)) *CategoryDomainServiceMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryDomainService.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryDomainServiceMockGetByID) When(id int64) *CategoryDomainServiceMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryDomainServiceMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryDomainServiceMockGetByIDParams{id},
		expectationOrigins: CategoryDomainServiceMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockGetByIDExpectation) Then(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.GetByID should be invoked
func (mmGetByID *mCategoryDomainServiceMockGetByID) Times(n uint64) *mCategoryDomainServiceMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryDomainServiceMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryDomainServiceMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_service.CategoryDomainService
func (mmGetByID *CategoryDomainServiceMock) GetByID(id int64) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryDomainServiceMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryDomainServiceMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryDomainServiceMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryDomainServiceMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryDomainServiceMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryDomainServiceMock.GetByID invocations
func (mmGetByID *CategoryDomainServiceMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryDomainServiceMock.GetByID invocations
func (mmGetByID *CategoryDomainServiceMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryDomainServiceMockGetByID) Calls() []*CategoryDomainServiceMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryDomainServiceMockGetByName struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockGetByNameExpectation
	expectations       []*CategoryDomainServiceMockGetByNameExpectation

	callArgs []*CategoryDomainServiceMockGetByNameParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockGetByNameExpectation specifies expectation struct of the CategoryDomainService.GetByName
type CategoryDomainServiceMockGetByNameExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockGetByNameParams
	paramPtrs          *CategoryDomainServiceMockGetByNameParamPtrs
	expectationOrigins CategoryDomainServiceMockGetByNameExpectationOrigins
	results            *CategoryDomainServiceMockGetByNameResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockGetByNameParams contains parameters of the CategoryDomainService.GetByName
type CategoryDomainServiceMockGetByNameParams struct {
	name string
}

// CategoryDomainServiceMockGetByNameParamPtrs contains pointers to parameters of the CategoryDomainService.GetByName
type CategoryDomainServiceMockGetByNameParamPtrs struct {
	name *string
}

// CategoryDomainServiceMockGetByNameResults contains results of the CategoryDomainService.GetByName
type CategoryDomainServiceMockGetByNameResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryDomainServiceMockGetByNameOrigins contains origins of expectations of the CategoryDomainService.GetByName
type CategoryDomainServiceMockGetByNameExpectationOrigins struct {
	origin     string
	originName string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByName *mCategoryDomainServiceMockGetByName) Optional() *mCategoryDomainServiceMockGetByName {
	mmGetByName.optional = true
	return mmGetByName
}

// Expect sets up expected params for CategoryDomainService.GetByName
func (mmGetByName *mCategoryDomainServiceMockGetByName) Expect(name string) *mCategoryDomainServiceMockGetByName {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryDomainServiceMock.GetByName mock is already set by Set")
	}

	if mmGetByName.defaultExpectation == nil {
		mmGetByName.defaultExpectation = &CategoryDomainServiceMockGetByNameExpectation{}
	}

	if mmGetByName.defaultExpectation.paramPtrs != nil {
		mmGetByName.mock.t.Fatalf("CategoryDomainServiceMock.GetByName mock is already set by ExpectParams functions")
	}

	mmGetByName.defaultExpectation.params = &CategoryDomainServiceMockGetByNameParams{name}
	mmGetByName.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByName.expectations {
		if minimock.Equal(e.params, mmGetByName.defaultExpectation.params) {
			mmGetByName.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByName.defaultExpectation.params)
		}
	}

	return mmGetByName
}

// ExpectNameParam1 sets up expected param name for CategoryDomainService.GetByName
func (mmGetByName *mCategoryDomainServiceMockGetByName) ExpectNameParam1(name string) *mCategoryDomainServiceMockGetByName {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryDomainServiceMock.GetByName mock is already set by Set")
	}

	if mmGetByName.defaultExpectation == nil {
		mmGetByName.defaultExpectation = &CategoryDomainServiceMockGetByNameExpectation{}
	}

	if mmGetByName.defaultExpectation.params != nil {
		mmGetByName.mock.t.Fatalf("CategoryDomainServiceMock.GetByName mock is already set by Expect")
	}

	if mmGetByName.defaultExpectation.paramPtrs == nil {
		mmGetByName.defaultExpectation.paramPtrs = &CategoryDomainServiceMockGetByNameParamPtrs{}
	}
	mmGetByName.defaultExpectation.paramPtrs.name = &name
	mmGetByName.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmGetByName
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.GetByName
func (mmGetByName *mCategoryDomainServiceMockGetByName) Inspect(f func(name string)) *mCategoryDomainServiceMockGetByName {
	if mmGetByName.mock.inspectFuncGetByName != nil {
		mmGetByName.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.GetByName")
	}

	mmGetByName.mock.inspectFuncGetByName = f

	return mmGetByName
}

// Return sets up results that will be returned by CategoryDomainService.GetByName
func (mmGetByName *mCategoryDomainServiceMockGetByName) Return(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryDomainServiceMock.GetByName mock is already set by Set")
	}

	if mmGetByName.defaultExpectation == nil {
		mmGetByName.defaultExpectation = &CategoryDomainServiceMockGetByNameExpectation{mock: mmGetByName.mock}
	}
	mmGetByName.defaultExpectation.results = &CategoryDomainServiceMockGetByNameResults{c1, err}
	mmGetByName.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByName.mock
}

// Set uses given function f to mock the CategoryDomainService.GetByName method
func (mmGetByName *mCategoryDomainServiceMockGetByName) Set(f func(name string) (c1 categoryentity.Category, err error)) *CategoryDomainServiceMock {
	if mmGetByName.defaultExpectation != nil {
		mmGetByName.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.GetByName method")
	}

	if len(mmGetByName.expectations) > 0 {
		mmGetByName.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.GetByName method")
	}

	mmGetByName.mock.funcGetByName = f
	mmGetByName.mock.funcGetByNameOrigin = minimock.CallerInfo(1)
	return mmGetByName.mock
}

// When sets expectation for the CategoryDomainService.GetByName which will trigger the result defined by the following
// Then helper
func (mmGetByName *mCategoryDomainServiceMockGetByName) When(name string) *CategoryDomainServiceMockGetByNameExpectation {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryDomainServiceMock.GetByName mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockGetByNameExpectation{
		mock:               mmGetByName.mock,
		params:             &CategoryDomainServiceMockGetByNameParams{name},
		expectationOrigins: CategoryDomainServiceMockGetByNameExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByName.expectations = append(mmGetByName.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.GetByName return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockGetByNameExpectation) Then(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockGetByNameResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.GetByName should be invoked
func (mmGetByName *mCategoryDomainServiceMockGetByName) Times(n uint64) *mCategoryDomainServiceMockGetByName {
	if n == 0 {
		mmGetByName.mock.t.Fatalf("Times of CategoryDomainServiceMock.GetByName mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByName.expectedInvocations, n)
	mmGetByName.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByName
}

func (mmGetByName *mCategoryDomainServiceMockGetByName) invocationsDone() bool {
	if len(mmGetByName.expectations) == 0 && mmGetByName.defaultExpectation == nil && mmGetByName.mock.funcGetByName == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByName.mock.afterGetByNameCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByName.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByName implements mm_service.CategoryDomainService
func (mmGetByName *CategoryDomainServiceMock) GetByName(name string) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetByName.beforeGetByNameCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByName.afterGetByNameCounter, 1)

	mmGetByName.t.Helper()

	if mmGetByName.inspectFuncGetByName != nil {
		mmGetByName.inspectFuncGetByName(name)
	}

	mm_params := CategoryDomainServiceMockGetByNameParams{name}

	// Record call args
	mmGetByName.GetByNameMock.mutex.Lock()
	mmGetByName.GetByNameMock.callArgs = append(mmGetByName.GetByNameMock.callArgs, &mm_params)
	mmGetByName.GetByNameMock.mutex.Unlock()

	for _, e := range mmGetByName.GetByNameMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByName.GetByNameMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByName.GetByNameMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByName.GetByNameMock.defaultExpectation.params
		mm_want_ptrs := mmGetByName.GetByNameMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockGetByNameParams{name}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmGetByName.t.Errorf("CategoryDomainServiceMock.GetByName got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByName.GetByNameMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByName.t.Errorf("CategoryDomainServiceMock.GetByName got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByName.GetByNameMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByName.GetByNameMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByName.t.Fatal("No results are set for the CategoryDomainServiceMock.GetByName")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByName.funcGetByName != nil {
		return mmGetByName.funcGetByName(name)
	}
	mmGetByName.t.Fatalf("Unexpected call to CategoryDomainServiceMock.GetByName. %v", name)
	return
}

// GetByNameAfterCounter returns a count of finished CategoryDomainServiceMock.GetByName invocations
func (mmGetByName *CategoryDomainServiceMock) GetByNameAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByName.afterGetByNameCounter)
}

// GetByNameBeforeCounter returns a count of CategoryDomainServiceMock.GetByName invocations
func (mmGetByName *CategoryDomainServiceMock) GetByNameBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByName.beforeGetByNameCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.GetByName.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByName *mCategoryDomainServiceMockGetByName) Calls() []*CategoryDomainServiceMockGetByNameParams {
	mmGetByName.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockGetByNameParams, len(mmGetByName.callArgs))
	copy(argCopy, mmGetByName.callArgs)

	mmGetByName.mutex.RUnlock()

	return argCopy
}

// MinimockGetByNameDone returns true if the count of the GetByName invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockGetByNameDone() bool {
	if m.GetByNameMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByNameMock.invocationsDone()
}

// MinimockGetByNameInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockGetByNameInspect() {
	for _, e := range m.GetByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByName at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByNameCounter := mm_atomic.LoadUint64(&m.afterGetByNameCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByNameMock.defaultExpectation != nil && afterGetByNameCounter < 1 {
		if m.GetByNameMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByName at\n%s", m.GetByNameMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByName at\n%s with params: %#v", m.GetByNameMock.defaultExpectation.expectationOrigins.origin, *m.GetByNameMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByName != nil && afterGetByNameCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.GetByName at\n%s", m.funcGetByNameOrigin)
	}

	if !m.GetByNameMock.invocationsDone() && afterGetByNameCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.GetByName at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByNameMock.expectedInvocations), m.GetByNameMock.expectedInvocationsOrigin, afterGetByNameCounter)
	}
}

type mCategoryDomainServiceMockGetCategoryFull struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockGetCategoryFullExpectation
	expectations       []*CategoryDomainServiceMockGetCategoryFullExpectation

	callArgs []*CategoryDomainServiceMockGetCategoryFullParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockGetCategoryFullExpectation specifies expectation struct of the CategoryDomainService.GetCategoryFull
type CategoryDomainServiceMockGetCategoryFullExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockGetCategoryFullParams
	paramPtrs          *CategoryDomainServiceMockGetCategoryFullParamPtrs
	expectationOrigins CategoryDomainServiceMockGetCategoryFullExpectationOrigins
	results            *CategoryDomainServiceMockGetCategoryFullResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockGetCategoryFullParams contains parameters of the CategoryDomainService.GetCategoryFull
type CategoryDomainServiceMockGetCategoryFullParams struct {
	categoryID int64
}

// CategoryDomainServiceMockGetCategoryFullParamPtrs contains pointers to parameters of the CategoryDomainService.GetCategoryFull
type CategoryDomainServiceMockGetCategoryFullParamPtrs struct {
	categoryID *int64
}

// CategoryDomainServiceMockGetCategoryFullResults contains results of the CategoryDomainService.GetCategoryFull
type CategoryDomainServiceMockGetCategoryFullResults struct {
	c1  categoryentity.CategoryFull
	err error
}

// CategoryDomainServiceMockGetCategoryFullOrigins contains origins of expectations of the CategoryDomainService.GetCategoryFull
type CategoryDomainServiceMockGetCategoryFullExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Optional() *mCategoryDomainServiceMockGetCategoryFull {
	mmGetCategoryFull.optional = true
	return mmGetCategoryFull
}

// Expect sets up expected params for CategoryDomainService.GetCategoryFull
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Expect(categoryID int64) *mCategoryDomainServiceMockGetCategoryFull {
	if mmGetCategoryFull.mock.funcGetCategoryFull != nil {
		mmGetCategoryFull.mock.t.Fatalf("CategoryDomainServiceMock.GetCategoryFull mock is already set by Set")
	}

	if mmGetCategoryFull.defaultExpectation == nil {
		mmGetCategoryFull.defaultExpectation = &CategoryDomainServiceMockGetCategoryFullExpectation{}
	}

	if mmGetCategoryFull.defaultExpectation.paramPtrs != nil {
		mmGetCategoryFull.mock.t.Fatalf("CategoryDomainServiceMock.GetCategoryFull mock is already set by ExpectParams functions")
	}

	mmGetCategoryFull.defaultExpectation.params = &CategoryDomainServiceMockGetCategoryFullParams{categoryID}
	mmGetCategoryFull.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetCategoryFull.expectations {
		if minimock.Equal(e.params, mmGetCategoryFull.defaultExpectation.params) {
			mmGetCategoryFull.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetCategoryFull.defaultExpectation.params)
		}
	}

	return mmGetCategoryFull
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryDomainService.GetCategoryFull
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) ExpectCategoryIDParam1(categoryID int64) *mCategoryDomainServiceMockGetCategoryFull {
	if mmGetCategoryFull.mock.funcGetCategoryFull != nil {
		mmGetCategoryFull.mock.t.Fatalf("CategoryDomainServiceMock.GetCategoryFull mock is already set by Set")
	}

	if mmGetCategoryFull.defaultExpectation == nil {
		mmGetCategoryFull.defaultExpectation = &CategoryDomainServiceMockGetCategoryFullExpectation{}
	}

	if mmGetCategoryFull.defaultExpectation.params != nil {
		mmGetCategoryFull.mock.t.Fatalf("CategoryDomainServiceMock.GetCategoryFull mock is already set by Expect")
	}

	if mmGetCategoryFull.defaultExpectation.paramPtrs == nil {
		mmGetCategoryFull.defaultExpectation.paramPtrs = &CategoryDomainServiceMockGetCategoryFullParamPtrs{}
	}
	mmGetCategoryFull.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetCategoryFull.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetCategoryFull
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.GetCategoryFull
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Inspect(f func(categoryID int64)) *mCategoryDomainServiceMockGetCategoryFull {
	if mmGetCategoryFull.mock.inspectFuncGetCategoryFull != nil {
		mmGetCategoryFull.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.GetCategoryFull")
	}

	mmGetCategoryFull.mock.inspectFuncGetCategoryFull = f

	return mmGetCategoryFull
}

// Return sets up results that will be returned by CategoryDomainService.GetCategoryFull
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Return(c1 categoryentity.CategoryFull, err error) *CategoryDomainServiceMock {
	if mmGetCategoryFull.mock.funcGetCategoryFull != nil {
		mmGetCategoryFull.mock.t.Fatalf("CategoryDomainServiceMock.GetCategoryFull mock is already set by Set")
	}

	if mmGetCategoryFull.defaultExpectation == nil {
		mmGetCategoryFull.defaultExpectation = &CategoryDomainServiceMockGetCategoryFullExpectation{mock: mmGetCategoryFull.mock}
	}
	mmGetCategoryFull.defaultExpectation.results = &CategoryDomainServiceMockGetCategoryFullResults{c1, err}
	mmGetCategoryFull.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetCategoryFull.mock
}

// Set uses given function f to mock the CategoryDomainService.GetCategoryFull method
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Set(f func(categoryID int64) (c1 categoryentity.CategoryFull, err error)) *CategoryDomainServiceMock {
	if mmGetCategoryFull.defaultExpectation != nil {
		mmGetCategoryFull.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.GetCategoryFull method")
	}

	if len(mmGetCategoryFull.expectations) > 0 {
		mmGetCategoryFull.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.GetCategoryFull method")
	}

	mmGetCategoryFull.mock.funcGetCategoryFull = f
	mmGetCategoryFull.mock.funcGetCategoryFullOrigin = minimock.CallerInfo(1)
	return mmGetCategoryFull.mock
}

// When sets expectation for the CategoryDomainService.GetCategoryFull which will trigger the result defined by the following
// Then helper
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) When(categoryID int64) *CategoryDomainServiceMockGetCategoryFullExpectation {
	if mmGetCategoryFull.mock.funcGetCategoryFull != nil {
		mmGetCategoryFull.mock.t.Fatalf("CategoryDomainServiceMock.GetCategoryFull mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockGetCategoryFullExpectation{
		mock:               mmGetCategoryFull.mock,
		params:             &CategoryDomainServiceMockGetCategoryFullParams{categoryID},
		expectationOrigins: CategoryDomainServiceMockGetCategoryFullExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetCategoryFull.expectations = append(mmGetCategoryFull.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.GetCategoryFull return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockGetCategoryFullExpectation) Then(c1 categoryentity.CategoryFull, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockGetCategoryFullResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.GetCategoryFull should be invoked
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Times(n uint64) *mCategoryDomainServiceMockGetCategoryFull {
	if n == 0 {
		mmGetCategoryFull.mock.t.Fatalf("Times of CategoryDomainServiceMock.GetCategoryFull mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetCategoryFull.expectedInvocations, n)
	mmGetCategoryFull.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetCategoryFull
}

func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) invocationsDone() bool {
	if len(mmGetCategoryFull.expectations) == 0 && mmGetCategoryFull.defaultExpectation == nil && mmGetCategoryFull.mock.funcGetCategoryFull == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetCategoryFull.mock.afterGetCategoryFullCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetCategoryFull.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetCategoryFull implements mm_service.CategoryDomainService
func (mmGetCategoryFull *CategoryDomainServiceMock) GetCategoryFull(categoryID int64) (c1 categoryentity.CategoryFull, err error) {
	mm_atomic.AddUint64(&mmGetCategoryFull.beforeGetCategoryFullCounter, 1)
	defer mm_atomic.AddUint64(&mmGetCategoryFull.afterGetCategoryFullCounter, 1)

	mmGetCategoryFull.t.Helper()

	if mmGetCategoryFull.inspectFuncGetCategoryFull != nil {
		mmGetCategoryFull.inspectFuncGetCategoryFull(categoryID)
	}

	mm_params := CategoryDomainServiceMockGetCategoryFullParams{categoryID}

	// Record call args
	mmGetCategoryFull.GetCategoryFullMock.mutex.Lock()
	mmGetCategoryFull.GetCategoryFullMock.callArgs = append(mmGetCategoryFull.GetCategoryFullMock.callArgs, &mm_params)
	mmGetCategoryFull.GetCategoryFullMock.mutex.Unlock()

	for _, e := range mmGetCategoryFull.GetCategoryFullMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetCategoryFull.GetCategoryFullMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetCategoryFull.GetCategoryFullMock.defaultExpectation.Counter, 1)
		mm_want := mmGetCategoryFull.GetCategoryFullMock.defaultExpectation.params
		mm_want_ptrs := mmGetCategoryFull.GetCategoryFullMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockGetCategoryFullParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetCategoryFull.t.Errorf("CategoryDomainServiceMock.GetCategoryFull got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetCategoryFull.GetCategoryFullMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetCategoryFull.t.Errorf("CategoryDomainServiceMock.GetCategoryFull got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetCategoryFull.GetCategoryFullMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetCategoryFull.GetCategoryFullMock.defaultExpectation.results
		if mm_results == nil {
			mmGetCategoryFull.t.Fatal("No results are set for the CategoryDomainServiceMock.GetCategoryFull")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetCategoryFull.funcGetCategoryFull != nil {
		return mmGetCategoryFull.funcGetCategoryFull(categoryID)
	}
	mmGetCategoryFull.t.Fatalf("Unexpected call to CategoryDomainServiceMock.GetCategoryFull. %v", categoryID)
	return
}

// GetCategoryFullAfterCounter returns a count of finished CategoryDomainServiceMock.GetCategoryFull invocations
func (mmGetCategoryFull *CategoryDomainServiceMock) GetCategoryFullAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetCategoryFull.afterGetCategoryFullCounter)
}

// GetCategoryFullBeforeCounter returns a count of CategoryDomainServiceMock.GetCategoryFull invocations
func (mmGetCategoryFull *CategoryDomainServiceMock) GetCategoryFullBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetCategoryFull.beforeGetCategoryFullCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.GetCategoryFull.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetCategoryFull *mCategoryDomainServiceMockGetCategoryFull) Calls() []*CategoryDomainServiceMockGetCategoryFullParams {
	mmGetCategoryFull.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockGetCategoryFullParams, len(mmGetCategoryFull.callArgs))
	copy(argCopy, mmGetCategoryFull.callArgs)

	mmGetCategoryFull.mutex.RUnlock()

	return argCopy
}

// MinimockGetCategoryFullDone returns true if the count of the GetCategoryFull invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockGetCategoryFullDone() bool {
	if m.GetCategoryFullMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetCategoryFullMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetCategoryFullMock.invocationsDone()
}

// MinimockGetCategoryFullInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockGetCategoryFullInspect() {
	for _, e := range m.GetCategoryFullMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetCategoryFull at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetCategoryFullCounter := mm_atomic.LoadUint64(&m.afterGetCategoryFullCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetCategoryFullMock.defaultExpectation != nil && afterGetCategoryFullCounter < 1 {
		if m.GetCategoryFullMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetCategoryFull at\n%s", m.GetCategoryFullMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetCategoryFull at\n%s with params: %#v", m.GetCategoryFullMock.defaultExpectation.expectationOrigins.origin, *m.GetCategoryFullMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetCategoryFull != nil && afterGetCategoryFullCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.GetCategoryFull at\n%s", m.funcGetCategoryFullOrigin)
	}

	if !m.GetCategoryFullMock.invocationsDone() && afterGetCategoryFullCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.GetCategoryFull at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetCategoryFullMock.expectedInvocations), m.GetCategoryFullMock.expectedInvocationsOrigin, afterGetCategoryFullCounter)
	}
}

type mCategoryDomainServiceMockGetFullByID struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockGetFullByIDExpectation
	expectations       []*CategoryDomainServiceMockGetFullByIDExpectation

	callArgs []*CategoryDomainServiceMockGetFullByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockGetFullByIDExpectation specifies expectation struct of the CategoryDomainService.GetFullByID
type CategoryDomainServiceMockGetFullByIDExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockGetFullByIDParams
	paramPtrs          *CategoryDomainServiceMockGetFullByIDParamPtrs
	expectationOrigins CategoryDomainServiceMockGetFullByIDExpectationOrigins
	results            *CategoryDomainServiceMockGetFullByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockGetFullByIDParams contains parameters of the CategoryDomainService.GetFullByID
type CategoryDomainServiceMockGetFullByIDParams struct {
	id int64
}

// CategoryDomainServiceMockGetFullByIDParamPtrs contains pointers to parameters of the CategoryDomainService.GetFullByID
type CategoryDomainServiceMockGetFullByIDParamPtrs struct {
	id *int64
}

// CategoryDomainServiceMockGetFullByIDResults contains results of the CategoryDomainService.GetFullByID
type CategoryDomainServiceMockGetFullByIDResults struct {
	c1  categoryentity.CategoryFull
	err error
}

// CategoryDomainServiceMockGetFullByIDOrigins contains origins of expectations of the CategoryDomainService.GetFullByID
type CategoryDomainServiceMockGetFullByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Optional() *mCategoryDomainServiceMockGetFullByID {
	mmGetFullByID.optional = true
	return mmGetFullByID
}

// Expect sets up expected params for CategoryDomainService.GetFullByID
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Expect(id int64) *mCategoryDomainServiceMockGetFullByID {
	if mmGetFullByID.mock.funcGetFullByID != nil {
		mmGetFullByID.mock.t.Fatalf("CategoryDomainServiceMock.GetFullByID mock is already set by Set")
	}

	if mmGetFullByID.defaultExpectation == nil {
		mmGetFullByID.defaultExpectation = &CategoryDomainServiceMockGetFullByIDExpectation{}
	}

	if mmGetFullByID.defaultExpectation.paramPtrs != nil {
		mmGetFullByID.mock.t.Fatalf("CategoryDomainServiceMock.GetFullByID mock is already set by ExpectParams functions")
	}

	mmGetFullByID.defaultExpectation.params = &CategoryDomainServiceMockGetFullByIDParams{id}
	mmGetFullByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetFullByID.expectations {
		if minimock.Equal(e.params, mmGetFullByID.defaultExpectation.params) {
			mmGetFullByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetFullByID.defaultExpectation.params)
		}
	}

	return mmGetFullByID
}

// ExpectIdParam1 sets up expected param id for CategoryDomainService.GetFullByID
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) ExpectIdParam1(id int64) *mCategoryDomainServiceMockGetFullByID {
	if mmGetFullByID.mock.funcGetFullByID != nil {
		mmGetFullByID.mock.t.Fatalf("CategoryDomainServiceMock.GetFullByID mock is already set by Set")
	}

	if mmGetFullByID.defaultExpectation == nil {
		mmGetFullByID.defaultExpectation = &CategoryDomainServiceMockGetFullByIDExpectation{}
	}

	if mmGetFullByID.defaultExpectation.params != nil {
		mmGetFullByID.mock.t.Fatalf("CategoryDomainServiceMock.GetFullByID mock is already set by Expect")
	}

	if mmGetFullByID.defaultExpectation.paramPtrs == nil {
		mmGetFullByID.defaultExpectation.paramPtrs = &CategoryDomainServiceMockGetFullByIDParamPtrs{}
	}
	mmGetFullByID.defaultExpectation.paramPtrs.id = &id
	mmGetFullByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetFullByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.GetFullByID
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Inspect(f func(id int64)) *mCategoryDomainServiceMockGetFullByID {
	if mmGetFullByID.mock.inspectFuncGetFullByID != nil {
		mmGetFullByID.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.GetFullByID")
	}

	mmGetFullByID.mock.inspectFuncGetFullByID = f

	return mmGetFullByID
}

// Return sets up results that will be returned by CategoryDomainService.GetFullByID
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Return(c1 categoryentity.CategoryFull, err error) *CategoryDomainServiceMock {
	if mmGetFullByID.mock.funcGetFullByID != nil {
		mmGetFullByID.mock.t.Fatalf("CategoryDomainServiceMock.GetFullByID mock is already set by Set")
	}

	if mmGetFullByID.defaultExpectation == nil {
		mmGetFullByID.defaultExpectation = &CategoryDomainServiceMockGetFullByIDExpectation{mock: mmGetFullByID.mock}
	}
	mmGetFullByID.defaultExpectation.results = &CategoryDomainServiceMockGetFullByIDResults{c1, err}
	mmGetFullByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetFullByID.mock
}

// Set uses given function f to mock the CategoryDomainService.GetFullByID method
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Set(f func(id int64) (c1 categoryentity.CategoryFull, err error)) *CategoryDomainServiceMock {
	if mmGetFullByID.defaultExpectation != nil {
		mmGetFullByID.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.GetFullByID method")
	}

	if len(mmGetFullByID.expectations) > 0 {
		mmGetFullByID.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.GetFullByID method")
	}

	mmGetFullByID.mock.funcGetFullByID = f
	mmGetFullByID.mock.funcGetFullByIDOrigin = minimock.CallerInfo(1)
	return mmGetFullByID.mock
}

// When sets expectation for the CategoryDomainService.GetFullByID which will trigger the result defined by the following
// Then helper
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) When(id int64) *CategoryDomainServiceMockGetFullByIDExpectation {
	if mmGetFullByID.mock.funcGetFullByID != nil {
		mmGetFullByID.mock.t.Fatalf("CategoryDomainServiceMock.GetFullByID mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockGetFullByIDExpectation{
		mock:               mmGetFullByID.mock,
		params:             &CategoryDomainServiceMockGetFullByIDParams{id},
		expectationOrigins: CategoryDomainServiceMockGetFullByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetFullByID.expectations = append(mmGetFullByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.GetFullByID return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockGetFullByIDExpectation) Then(c1 categoryentity.CategoryFull, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockGetFullByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.GetFullByID should be invoked
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Times(n uint64) *mCategoryDomainServiceMockGetFullByID {
	if n == 0 {
		mmGetFullByID.mock.t.Fatalf("Times of CategoryDomainServiceMock.GetFullByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetFullByID.expectedInvocations, n)
	mmGetFullByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetFullByID
}

func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) invocationsDone() bool {
	if len(mmGetFullByID.expectations) == 0 && mmGetFullByID.defaultExpectation == nil && mmGetFullByID.mock.funcGetFullByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetFullByID.mock.afterGetFullByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetFullByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetFullByID implements mm_service.CategoryDomainService
func (mmGetFullByID *CategoryDomainServiceMock) GetFullByID(id int64) (c1 categoryentity.CategoryFull, err error) {
	mm_atomic.AddUint64(&mmGetFullByID.beforeGetFullByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetFullByID.afterGetFullByIDCounter, 1)

	mmGetFullByID.t.Helper()

	if mmGetFullByID.inspectFuncGetFullByID != nil {
		mmGetFullByID.inspectFuncGetFullByID(id)
	}

	mm_params := CategoryDomainServiceMockGetFullByIDParams{id}

	// Record call args
	mmGetFullByID.GetFullByIDMock.mutex.Lock()
	mmGetFullByID.GetFullByIDMock.callArgs = append(mmGetFullByID.GetFullByIDMock.callArgs, &mm_params)
	mmGetFullByID.GetFullByIDMock.mutex.Unlock()

	for _, e := range mmGetFullByID.GetFullByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetFullByID.GetFullByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetFullByID.GetFullByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetFullByID.GetFullByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetFullByID.GetFullByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockGetFullByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetFullByID.t.Errorf("CategoryDomainServiceMock.GetFullByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetFullByID.GetFullByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetFullByID.t.Errorf("CategoryDomainServiceMock.GetFullByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetFullByID.GetFullByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetFullByID.GetFullByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetFullByID.t.Fatal("No results are set for the CategoryDomainServiceMock.GetFullByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetFullByID.funcGetFullByID != nil {
		return mmGetFullByID.funcGetFullByID(id)
	}
	mmGetFullByID.t.Fatalf("Unexpected call to CategoryDomainServiceMock.GetFullByID. %v", id)
	return
}

// GetFullByIDAfterCounter returns a count of finished CategoryDomainServiceMock.GetFullByID invocations
func (mmGetFullByID *CategoryDomainServiceMock) GetFullByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetFullByID.afterGetFullByIDCounter)
}

// GetFullByIDBeforeCounter returns a count of CategoryDomainServiceMock.GetFullByID invocations
func (mmGetFullByID *CategoryDomainServiceMock) GetFullByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetFullByID.beforeGetFullByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.GetFullByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetFullByID *mCategoryDomainServiceMockGetFullByID) Calls() []*CategoryDomainServiceMockGetFullByIDParams {
	mmGetFullByID.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockGetFullByIDParams, len(mmGetFullByID.callArgs))
	copy(argCopy, mmGetFullByID.callArgs)

	mmGetFullByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetFullByIDDone returns true if the count of the GetFullByID invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockGetFullByIDDone() bool {
	if m.GetFullByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetFullByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetFullByIDMock.invocationsDone()
}

// MinimockGetFullByIDInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockGetFullByIDInspect() {
	for _, e := range m.GetFullByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetFullByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetFullByIDCounter := mm_atomic.LoadUint64(&m.afterGetFullByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetFullByIDMock.defaultExpectation != nil && afterGetFullByIDCounter < 1 {
		if m.GetFullByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetFullByID at\n%s", m.GetFullByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.GetFullByID at\n%s with params: %#v", m.GetFullByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetFullByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetFullByID != nil && afterGetFullByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.GetFullByID at\n%s", m.funcGetFullByIDOrigin)
	}

	if !m.GetFullByIDMock.invocationsDone() && afterGetFullByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.GetFullByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetFullByIDMock.expectedInvocations), m.GetFullByIDMock.expectedInvocationsOrigin, afterGetFullByIDCounter)
	}
}

type mCategoryDomainServiceMockUpdate struct {
	optional           bool
	mock               *CategoryDomainServiceMock
	defaultExpectation *CategoryDomainServiceMockUpdateExpectation
	expectations       []*CategoryDomainServiceMockUpdateExpectation

	callArgs []*CategoryDomainServiceMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryDomainServiceMockUpdateExpectation specifies expectation struct of the CategoryDomainService.Update
type CategoryDomainServiceMockUpdateExpectation struct {
	mock               *CategoryDomainServiceMock
	params             *CategoryDomainServiceMockUpdateParams
	paramPtrs          *CategoryDomainServiceMockUpdateParamPtrs
	expectationOrigins CategoryDomainServiceMockUpdateExpectationOrigins
	results            *CategoryDomainServiceMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryDomainServiceMockUpdateParams contains parameters of the CategoryDomainService.Update
type CategoryDomainServiceMockUpdateParams struct {
	data categoryentity.Category
}

// CategoryDomainServiceMockUpdateParamPtrs contains pointers to parameters of the CategoryDomainService.Update
type CategoryDomainServiceMockUpdateParamPtrs struct {
	data *categoryentity.Category
}

// CategoryDomainServiceMockUpdateResults contains results of the CategoryDomainService.Update
type CategoryDomainServiceMockUpdateResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryDomainServiceMockUpdateOrigins contains origins of expectations of the CategoryDomainService.Update
type CategoryDomainServiceMockUpdateExpectationOrigins struct {
	origin     string
	originData string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mCategoryDomainServiceMockUpdate) Optional() *mCategoryDomainServiceMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for CategoryDomainService.Update
func (mmUpdate *mCategoryDomainServiceMockUpdate) Expect(data categoryentity.Category) *mCategoryDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &CategoryDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("CategoryDomainServiceMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &CategoryDomainServiceMockUpdateParams{data}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectDataParam1 sets up expected param data for CategoryDomainService.Update
func (mmUpdate *mCategoryDomainServiceMockUpdate) ExpectDataParam1(data categoryentity.Category) *mCategoryDomainServiceMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &CategoryDomainServiceMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("CategoryDomainServiceMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &CategoryDomainServiceMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.data = &data
	mmUpdate.defaultExpectation.expectationOrigins.originData = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the CategoryDomainService.Update
func (mmUpdate *mCategoryDomainServiceMockUpdate) Inspect(f func(data categoryentity.Category)) *mCategoryDomainServiceMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for CategoryDomainServiceMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by CategoryDomainService.Update
func (mmUpdate *mCategoryDomainServiceMockUpdate) Return(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryDomainServiceMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &CategoryDomainServiceMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &CategoryDomainServiceMockUpdateResults{c1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the CategoryDomainService.Update method
func (mmUpdate *mCategoryDomainServiceMockUpdate) Set(f func(data categoryentity.Category) (c1 categoryentity.Category, err error)) *CategoryDomainServiceMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the CategoryDomainService.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the CategoryDomainService.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the CategoryDomainService.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mCategoryDomainServiceMockUpdate) When(data categoryentity.Category) *CategoryDomainServiceMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryDomainServiceMock.Update mock is already set by Set")
	}

	expectation := &CategoryDomainServiceMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &CategoryDomainServiceMockUpdateParams{data},
		expectationOrigins: CategoryDomainServiceMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up CategoryDomainService.Update return parameters for the expectation previously defined by the When method
func (e *CategoryDomainServiceMockUpdateExpectation) Then(c1 categoryentity.Category, err error) *CategoryDomainServiceMock {
	e.results = &CategoryDomainServiceMockUpdateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryDomainService.Update should be invoked
func (mmUpdate *mCategoryDomainServiceMockUpdate) Times(n uint64) *mCategoryDomainServiceMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of CategoryDomainServiceMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mCategoryDomainServiceMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_service.CategoryDomainService
func (mmUpdate *CategoryDomainServiceMock) Update(data categoryentity.Category) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(data)
	}

	mm_params := CategoryDomainServiceMockUpdateParams{data}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := CategoryDomainServiceMockUpdateParams{data}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.data != nil && !minimock.Equal(*mm_want_ptrs.data, mm_got.data) {
				mmUpdate.t.Errorf("CategoryDomainServiceMock.Update got unexpected parameter data, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originData, *mm_want_ptrs.data, mm_got.data, minimock.Diff(*mm_want_ptrs.data, mm_got.data))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("CategoryDomainServiceMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the CategoryDomainServiceMock.Update")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(data)
	}
	mmUpdate.t.Fatalf("Unexpected call to CategoryDomainServiceMock.Update. %v", data)
	return
}

// UpdateAfterCounter returns a count of finished CategoryDomainServiceMock.Update invocations
func (mmUpdate *CategoryDomainServiceMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of CategoryDomainServiceMock.Update invocations
func (mmUpdate *CategoryDomainServiceMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to CategoryDomainServiceMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mCategoryDomainServiceMockUpdate) Calls() []*CategoryDomainServiceMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*CategoryDomainServiceMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *CategoryDomainServiceMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *CategoryDomainServiceMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryDomainServiceMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to CategoryDomainServiceMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryDomainServiceMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryDomainServiceMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeleteInspect()

			m.MinimockExistLinkWithGroupInspect()

			m.MinimockExistLinkWithPermissionInspect()

			m.MinimockExistLinkWithRoleInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByNameInspect()

			m.MinimockGetCategoryFullInspect()

			m.MinimockGetFullByIDInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryDomainServiceMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryDomainServiceMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockExistLinkWithGroupDone() &&
		m.MinimockExistLinkWithPermissionDone() &&
		m.MinimockExistLinkWithRoleDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByNameDone() &&
		m.MinimockGetCategoryFullDone() &&
		m.MinimockGetFullByIDDone() &&
		m.MinimockUpdateDone()
}
