// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryPermissionPrimeDB -o category_permission_prime_db_mock.go -n CategoryPermissionPrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryPermissionPrimeDBMock implements mm_repository.CategoryPermissionPrimeDB
type CategoryPermissionPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(categoryPermission categoryentity.CategoryPermission) (c1 categoryentity.CategoryPermission, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(categoryPermission categoryentity.CategoryPermission)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mCategoryPermissionPrimeDBMockCreate

	funcDeleteCategoryPermission          func(categoryID int64, permissionID int64) (err error)
	funcDeleteCategoryPermissionOrigin    string
	inspectFuncDeleteCategoryPermission   func(categoryID int64, permissionID int64)
	afterDeleteCategoryPermissionCounter  uint64
	beforeDeleteCategoryPermissionCounter uint64
	DeleteCategoryPermissionMock          mCategoryPermissionPrimeDBMockDeleteCategoryPermission

	funcGetAll          func() (ca1 []categoryentity.CategoryPermission, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryPermissionPrimeDBMockGetAll

	funcGetByCategoryID          func(categoryID int64) (ca1 []categoryentity.CategoryPermission, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mCategoryPermissionPrimeDBMockGetByCategoryID

	funcGetByID          func(id int64) (c1 categoryentity.CategoryPermission, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryPermissionPrimeDBMockGetByID

	funcHasCategoryPermission          func(categoryID int64, permissionID int64) (b1 bool, err error)
	funcHasCategoryPermissionOrigin    string
	inspectFuncHasCategoryPermission   func(categoryID int64, permissionID int64)
	afterHasCategoryPermissionCounter  uint64
	beforeHasCategoryPermissionCounter uint64
	HasCategoryPermissionMock          mCategoryPermissionPrimeDBMockHasCategoryPermission
}

// NewCategoryPermissionPrimeDBMock returns a mock for mm_repository.CategoryPermissionPrimeDB
func NewCategoryPermissionPrimeDBMock(t minimock.Tester) *CategoryPermissionPrimeDBMock {
	m := &CategoryPermissionPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mCategoryPermissionPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*CategoryPermissionPrimeDBMockCreateParams{}

	m.DeleteCategoryPermissionMock = mCategoryPermissionPrimeDBMockDeleteCategoryPermission{mock: m}
	m.DeleteCategoryPermissionMock.callArgs = []*CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams{}

	m.GetAllMock = mCategoryPermissionPrimeDBMockGetAll{mock: m}

	m.GetByCategoryIDMock = mCategoryPermissionPrimeDBMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*CategoryPermissionPrimeDBMockGetByCategoryIDParams{}

	m.GetByIDMock = mCategoryPermissionPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryPermissionPrimeDBMockGetByIDParams{}

	m.HasCategoryPermissionMock = mCategoryPermissionPrimeDBMockHasCategoryPermission{mock: m}
	m.HasCategoryPermissionMock.callArgs = []*CategoryPermissionPrimeDBMockHasCategoryPermissionParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryPermissionPrimeDBMockCreate struct {
	optional           bool
	mock               *CategoryPermissionPrimeDBMock
	defaultExpectation *CategoryPermissionPrimeDBMockCreateExpectation
	expectations       []*CategoryPermissionPrimeDBMockCreateExpectation

	callArgs []*CategoryPermissionPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionPrimeDBMockCreateExpectation specifies expectation struct of the CategoryPermissionPrimeDB.Create
type CategoryPermissionPrimeDBMockCreateExpectation struct {
	mock               *CategoryPermissionPrimeDBMock
	params             *CategoryPermissionPrimeDBMockCreateParams
	paramPtrs          *CategoryPermissionPrimeDBMockCreateParamPtrs
	expectationOrigins CategoryPermissionPrimeDBMockCreateExpectationOrigins
	results            *CategoryPermissionPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionPrimeDBMockCreateParams contains parameters of the CategoryPermissionPrimeDB.Create
type CategoryPermissionPrimeDBMockCreateParams struct {
	categoryPermission categoryentity.CategoryPermission
}

// CategoryPermissionPrimeDBMockCreateParamPtrs contains pointers to parameters of the CategoryPermissionPrimeDB.Create
type CategoryPermissionPrimeDBMockCreateParamPtrs struct {
	categoryPermission *categoryentity.CategoryPermission
}

// CategoryPermissionPrimeDBMockCreateResults contains results of the CategoryPermissionPrimeDB.Create
type CategoryPermissionPrimeDBMockCreateResults struct {
	c1  categoryentity.CategoryPermission
	err error
}

// CategoryPermissionPrimeDBMockCreateOrigins contains origins of expectations of the CategoryPermissionPrimeDB.Create
type CategoryPermissionPrimeDBMockCreateExpectationOrigins struct {
	origin                   string
	originCategoryPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Optional() *mCategoryPermissionPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for CategoryPermissionPrimeDB.Create
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Expect(categoryPermission categoryentity.CategoryPermission) *mCategoryPermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryPermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("CategoryPermissionPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &CategoryPermissionPrimeDBMockCreateParams{categoryPermission}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCategoryPermissionParam1 sets up expected param categoryPermission for CategoryPermissionPrimeDB.Create
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) ExpectCategoryPermissionParam1(categoryPermission categoryentity.CategoryPermission) *mCategoryPermissionPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryPermissionPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("CategoryPermissionPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.categoryPermission = &categoryPermission
	mmCreate.defaultExpectation.expectationOrigins.originCategoryPermission = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionPrimeDB.Create
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Inspect(f func(categoryPermission categoryentity.CategoryPermission)) *mCategoryPermissionPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for CategoryPermissionPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by CategoryPermissionPrimeDB.Create
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Return(c1 categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPermissionPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryPermissionPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &CategoryPermissionPrimeDBMockCreateResults{c1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the CategoryPermissionPrimeDB.Create method
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Set(f func(categoryPermission categoryentity.CategoryPermission) (c1 categoryentity.CategoryPermission, err error)) *CategoryPermissionPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the CategoryPermissionPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) When(categoryPermission categoryentity.CategoryPermission) *CategoryPermissionPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPermissionPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &CategoryPermissionPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &CategoryPermissionPrimeDBMockCreateParams{categoryPermission},
		expectationOrigins: CategoryPermissionPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionPrimeDBMockCreateExpectation) Then(c1 categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	e.results = &CategoryPermissionPrimeDBMockCreateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPermissionPrimeDB.Create should be invoked
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Times(n uint64) *mCategoryPermissionPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of CategoryPermissionPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mCategoryPermissionPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.CategoryPermissionPrimeDB
func (mmCreate *CategoryPermissionPrimeDBMock) Create(categoryPermission categoryentity.CategoryPermission) (c1 categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(categoryPermission)
	}

	mm_params := CategoryPermissionPrimeDBMockCreateParams{categoryPermission}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionPrimeDBMockCreateParams{categoryPermission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryPermission != nil && !minimock.Equal(*mm_want_ptrs.categoryPermission, mm_got.categoryPermission) {
				mmCreate.t.Errorf("CategoryPermissionPrimeDBMock.Create got unexpected parameter categoryPermission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCategoryPermission, *mm_want_ptrs.categoryPermission, mm_got.categoryPermission, minimock.Diff(*mm_want_ptrs.categoryPermission, mm_got.categoryPermission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("CategoryPermissionPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the CategoryPermissionPrimeDBMock.Create")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(categoryPermission)
	}
	mmCreate.t.Fatalf("Unexpected call to CategoryPermissionPrimeDBMock.Create. %v", categoryPermission)
	return
}

// CreateAfterCounter returns a count of finished CategoryPermissionPrimeDBMock.Create invocations
func (mmCreate *CategoryPermissionPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of CategoryPermissionPrimeDBMock.Create invocations
func (mmCreate *CategoryPermissionPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mCategoryPermissionPrimeDBMockCreate) Calls() []*CategoryPermissionPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*CategoryPermissionPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *CategoryPermissionPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mCategoryPermissionPrimeDBMockDeleteCategoryPermission struct {
	optional           bool
	mock               *CategoryPermissionPrimeDBMock
	defaultExpectation *CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation
	expectations       []*CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation

	callArgs []*CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation specifies expectation struct of the CategoryPermissionPrimeDB.DeleteCategoryPermission
type CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation struct {
	mock               *CategoryPermissionPrimeDBMock
	params             *CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams
	paramPtrs          *CategoryPermissionPrimeDBMockDeleteCategoryPermissionParamPtrs
	expectationOrigins CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectationOrigins
	results            *CategoryPermissionPrimeDBMockDeleteCategoryPermissionResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams contains parameters of the CategoryPermissionPrimeDB.DeleteCategoryPermission
type CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams struct {
	categoryID   int64
	permissionID int64
}

// CategoryPermissionPrimeDBMockDeleteCategoryPermissionParamPtrs contains pointers to parameters of the CategoryPermissionPrimeDB.DeleteCategoryPermission
type CategoryPermissionPrimeDBMockDeleteCategoryPermissionParamPtrs struct {
	categoryID   *int64
	permissionID *int64
}

// CategoryPermissionPrimeDBMockDeleteCategoryPermissionResults contains results of the CategoryPermissionPrimeDB.DeleteCategoryPermission
type CategoryPermissionPrimeDBMockDeleteCategoryPermissionResults struct {
	err error
}

// CategoryPermissionPrimeDBMockDeleteCategoryPermissionOrigins contains origins of expectations of the CategoryPermissionPrimeDB.DeleteCategoryPermission
type CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectationOrigins struct {
	origin             string
	originCategoryID   string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Optional() *mCategoryPermissionPrimeDBMockDeleteCategoryPermission {
	mmDeleteCategoryPermission.optional = true
	return mmDeleteCategoryPermission
}

// Expect sets up expected params for CategoryPermissionPrimeDB.DeleteCategoryPermission
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Expect(categoryID int64, permissionID int64) *mCategoryPermissionPrimeDBMockDeleteCategoryPermission {
	if mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Set")
	}

	if mmDeleteCategoryPermission.defaultExpectation == nil {
		mmDeleteCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation{}
	}

	if mmDeleteCategoryPermission.defaultExpectation.paramPtrs != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by ExpectParams functions")
	}

	mmDeleteCategoryPermission.defaultExpectation.params = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams{categoryID, permissionID}
	mmDeleteCategoryPermission.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteCategoryPermission.expectations {
		if minimock.Equal(e.params, mmDeleteCategoryPermission.defaultExpectation.params) {
			mmDeleteCategoryPermission.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteCategoryPermission.defaultExpectation.params)
		}
	}

	return mmDeleteCategoryPermission
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryPermissionPrimeDB.DeleteCategoryPermission
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) ExpectCategoryIDParam1(categoryID int64) *mCategoryPermissionPrimeDBMockDeleteCategoryPermission {
	if mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Set")
	}

	if mmDeleteCategoryPermission.defaultExpectation == nil {
		mmDeleteCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation{}
	}

	if mmDeleteCategoryPermission.defaultExpectation.params != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Expect")
	}

	if mmDeleteCategoryPermission.defaultExpectation.paramPtrs == nil {
		mmDeleteCategoryPermission.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionParamPtrs{}
	}
	mmDeleteCategoryPermission.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmDeleteCategoryPermission.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmDeleteCategoryPermission
}

// ExpectPermissionIDParam2 sets up expected param permissionID for CategoryPermissionPrimeDB.DeleteCategoryPermission
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) ExpectPermissionIDParam2(permissionID int64) *mCategoryPermissionPrimeDBMockDeleteCategoryPermission {
	if mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Set")
	}

	if mmDeleteCategoryPermission.defaultExpectation == nil {
		mmDeleteCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation{}
	}

	if mmDeleteCategoryPermission.defaultExpectation.params != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Expect")
	}

	if mmDeleteCategoryPermission.defaultExpectation.paramPtrs == nil {
		mmDeleteCategoryPermission.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionParamPtrs{}
	}
	mmDeleteCategoryPermission.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmDeleteCategoryPermission.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmDeleteCategoryPermission
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionPrimeDB.DeleteCategoryPermission
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Inspect(f func(categoryID int64, permissionID int64)) *mCategoryPermissionPrimeDBMockDeleteCategoryPermission {
	if mmDeleteCategoryPermission.mock.inspectFuncDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("Inspect function is already set for CategoryPermissionPrimeDBMock.DeleteCategoryPermission")
	}

	mmDeleteCategoryPermission.mock.inspectFuncDeleteCategoryPermission = f

	return mmDeleteCategoryPermission
}

// Return sets up results that will be returned by CategoryPermissionPrimeDB.DeleteCategoryPermission
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Return(err error) *CategoryPermissionPrimeDBMock {
	if mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Set")
	}

	if mmDeleteCategoryPermission.defaultExpectation == nil {
		mmDeleteCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation{mock: mmDeleteCategoryPermission.mock}
	}
	mmDeleteCategoryPermission.defaultExpectation.results = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionResults{err}
	mmDeleteCategoryPermission.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteCategoryPermission.mock
}

// Set uses given function f to mock the CategoryPermissionPrimeDB.DeleteCategoryPermission method
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Set(f func(categoryID int64, permissionID int64) (err error)) *CategoryPermissionPrimeDBMock {
	if mmDeleteCategoryPermission.defaultExpectation != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionPrimeDB.DeleteCategoryPermission method")
	}

	if len(mmDeleteCategoryPermission.expectations) > 0 {
		mmDeleteCategoryPermission.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionPrimeDB.DeleteCategoryPermission method")
	}

	mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission = f
	mmDeleteCategoryPermission.mock.funcDeleteCategoryPermissionOrigin = minimock.CallerInfo(1)
	return mmDeleteCategoryPermission.mock
}

// When sets expectation for the CategoryPermissionPrimeDB.DeleteCategoryPermission which will trigger the result defined by the following
// Then helper
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) When(categoryID int64, permissionID int64) *CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation {
	if mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock is already set by Set")
	}

	expectation := &CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation{
		mock:               mmDeleteCategoryPermission.mock,
		params:             &CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams{categoryID, permissionID},
		expectationOrigins: CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteCategoryPermission.expectations = append(mmDeleteCategoryPermission.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionPrimeDB.DeleteCategoryPermission return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionPrimeDBMockDeleteCategoryPermissionExpectation) Then(err error) *CategoryPermissionPrimeDBMock {
	e.results = &CategoryPermissionPrimeDBMockDeleteCategoryPermissionResults{err}
	return e.mock
}

// Times sets number of times CategoryPermissionPrimeDB.DeleteCategoryPermission should be invoked
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Times(n uint64) *mCategoryPermissionPrimeDBMockDeleteCategoryPermission {
	if n == 0 {
		mmDeleteCategoryPermission.mock.t.Fatalf("Times of CategoryPermissionPrimeDBMock.DeleteCategoryPermission mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteCategoryPermission.expectedInvocations, n)
	mmDeleteCategoryPermission.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteCategoryPermission
}

func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) invocationsDone() bool {
	if len(mmDeleteCategoryPermission.expectations) == 0 && mmDeleteCategoryPermission.defaultExpectation == nil && mmDeleteCategoryPermission.mock.funcDeleteCategoryPermission == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteCategoryPermission.mock.afterDeleteCategoryPermissionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteCategoryPermission.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteCategoryPermission implements mm_repository.CategoryPermissionPrimeDB
func (mmDeleteCategoryPermission *CategoryPermissionPrimeDBMock) DeleteCategoryPermission(categoryID int64, permissionID int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteCategoryPermission.beforeDeleteCategoryPermissionCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteCategoryPermission.afterDeleteCategoryPermissionCounter, 1)

	mmDeleteCategoryPermission.t.Helper()

	if mmDeleteCategoryPermission.inspectFuncDeleteCategoryPermission != nil {
		mmDeleteCategoryPermission.inspectFuncDeleteCategoryPermission(categoryID, permissionID)
	}

	mm_params := CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams{categoryID, permissionID}

	// Record call args
	mmDeleteCategoryPermission.DeleteCategoryPermissionMock.mutex.Lock()
	mmDeleteCategoryPermission.DeleteCategoryPermissionMock.callArgs = append(mmDeleteCategoryPermission.DeleteCategoryPermissionMock.callArgs, &mm_params)
	mmDeleteCategoryPermission.DeleteCategoryPermissionMock.mutex.Unlock()

	for _, e := range mmDeleteCategoryPermission.DeleteCategoryPermissionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams{categoryID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmDeleteCategoryPermission.t.Errorf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmDeleteCategoryPermission.t.Errorf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteCategoryPermission.t.Errorf("CategoryPermissionPrimeDBMock.DeleteCategoryPermission got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteCategoryPermission.DeleteCategoryPermissionMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteCategoryPermission.t.Fatal("No results are set for the CategoryPermissionPrimeDBMock.DeleteCategoryPermission")
		}
		return (*mm_results).err
	}
	if mmDeleteCategoryPermission.funcDeleteCategoryPermission != nil {
		return mmDeleteCategoryPermission.funcDeleteCategoryPermission(categoryID, permissionID)
	}
	mmDeleteCategoryPermission.t.Fatalf("Unexpected call to CategoryPermissionPrimeDBMock.DeleteCategoryPermission. %v %v", categoryID, permissionID)
	return
}

// DeleteCategoryPermissionAfterCounter returns a count of finished CategoryPermissionPrimeDBMock.DeleteCategoryPermission invocations
func (mmDeleteCategoryPermission *CategoryPermissionPrimeDBMock) DeleteCategoryPermissionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteCategoryPermission.afterDeleteCategoryPermissionCounter)
}

// DeleteCategoryPermissionBeforeCounter returns a count of CategoryPermissionPrimeDBMock.DeleteCategoryPermission invocations
func (mmDeleteCategoryPermission *CategoryPermissionPrimeDBMock) DeleteCategoryPermissionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteCategoryPermission.beforeDeleteCategoryPermissionCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionPrimeDBMock.DeleteCategoryPermission.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteCategoryPermission *mCategoryPermissionPrimeDBMockDeleteCategoryPermission) Calls() []*CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams {
	mmDeleteCategoryPermission.mutex.RLock()

	argCopy := make([]*CategoryPermissionPrimeDBMockDeleteCategoryPermissionParams, len(mmDeleteCategoryPermission.callArgs))
	copy(argCopy, mmDeleteCategoryPermission.callArgs)

	mmDeleteCategoryPermission.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteCategoryPermissionDone returns true if the count of the DeleteCategoryPermission invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionPrimeDBMock) MinimockDeleteCategoryPermissionDone() bool {
	if m.DeleteCategoryPermissionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteCategoryPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteCategoryPermissionMock.invocationsDone()
}

// MinimockDeleteCategoryPermissionInspect logs each unmet expectation
func (m *CategoryPermissionPrimeDBMock) MinimockDeleteCategoryPermissionInspect() {
	for _, e := range m.DeleteCategoryPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.DeleteCategoryPermission at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCategoryPermissionCounter := mm_atomic.LoadUint64(&m.afterDeleteCategoryPermissionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteCategoryPermissionMock.defaultExpectation != nil && afterDeleteCategoryPermissionCounter < 1 {
		if m.DeleteCategoryPermissionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.DeleteCategoryPermission at\n%s", m.DeleteCategoryPermissionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.DeleteCategoryPermission at\n%s with params: %#v", m.DeleteCategoryPermissionMock.defaultExpectation.expectationOrigins.origin, *m.DeleteCategoryPermissionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteCategoryPermission != nil && afterDeleteCategoryPermissionCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.DeleteCategoryPermission at\n%s", m.funcDeleteCategoryPermissionOrigin)
	}

	if !m.DeleteCategoryPermissionMock.invocationsDone() && afterDeleteCategoryPermissionCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionPrimeDBMock.DeleteCategoryPermission at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteCategoryPermissionMock.expectedInvocations), m.DeleteCategoryPermissionMock.expectedInvocationsOrigin, afterDeleteCategoryPermissionCounter)
	}
}

type mCategoryPermissionPrimeDBMockGetAll struct {
	optional           bool
	mock               *CategoryPermissionPrimeDBMock
	defaultExpectation *CategoryPermissionPrimeDBMockGetAllExpectation
	expectations       []*CategoryPermissionPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionPrimeDBMockGetAllExpectation specifies expectation struct of the CategoryPermissionPrimeDB.GetAll
type CategoryPermissionPrimeDBMockGetAllExpectation struct {
	mock *CategoryPermissionPrimeDBMock

	results      *CategoryPermissionPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryPermissionPrimeDBMockGetAllResults contains results of the CategoryPermissionPrimeDB.GetAll
type CategoryPermissionPrimeDBMockGetAllResults struct {
	ca1 []categoryentity.CategoryPermission
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) Optional() *mCategoryPermissionPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryPermissionPrimeDB.GetAll
func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) Expect() *mCategoryPermissionPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryPermissionPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionPrimeDB.GetAll
func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) Inspect(f func()) *mCategoryPermissionPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryPermissionPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryPermissionPrimeDB.GetAll
func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) Return(ca1 []categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryPermissionPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryPermissionPrimeDBMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryPermissionPrimeDB.GetAll method
func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) Set(f func() (ca1 []categoryentity.CategoryPermission, err error)) *CategoryPermissionPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryPermissionPrimeDB.GetAll should be invoked
func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) Times(n uint64) *mCategoryPermissionPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryPermissionPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryPermissionPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryPermissionPrimeDB
func (mmGetAll *CategoryPermissionPrimeDBMock) GetAll() (ca1 []categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryPermissionPrimeDBMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryPermissionPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryPermissionPrimeDBMock.GetAll invocations
func (mmGetAll *CategoryPermissionPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryPermissionPrimeDBMock.GetAll invocations
func (mmGetAll *CategoryPermissionPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryPermissionPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryPermissionPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryPermissionPrimeDBMockGetByCategoryID struct {
	optional           bool
	mock               *CategoryPermissionPrimeDBMock
	defaultExpectation *CategoryPermissionPrimeDBMockGetByCategoryIDExpectation
	expectations       []*CategoryPermissionPrimeDBMockGetByCategoryIDExpectation

	callArgs []*CategoryPermissionPrimeDBMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionPrimeDBMockGetByCategoryIDExpectation specifies expectation struct of the CategoryPermissionPrimeDB.GetByCategoryID
type CategoryPermissionPrimeDBMockGetByCategoryIDExpectation struct {
	mock               *CategoryPermissionPrimeDBMock
	params             *CategoryPermissionPrimeDBMockGetByCategoryIDParams
	paramPtrs          *CategoryPermissionPrimeDBMockGetByCategoryIDParamPtrs
	expectationOrigins CategoryPermissionPrimeDBMockGetByCategoryIDExpectationOrigins
	results            *CategoryPermissionPrimeDBMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionPrimeDBMockGetByCategoryIDParams contains parameters of the CategoryPermissionPrimeDB.GetByCategoryID
type CategoryPermissionPrimeDBMockGetByCategoryIDParams struct {
	categoryID int64
}

// CategoryPermissionPrimeDBMockGetByCategoryIDParamPtrs contains pointers to parameters of the CategoryPermissionPrimeDB.GetByCategoryID
type CategoryPermissionPrimeDBMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// CategoryPermissionPrimeDBMockGetByCategoryIDResults contains results of the CategoryPermissionPrimeDB.GetByCategoryID
type CategoryPermissionPrimeDBMockGetByCategoryIDResults struct {
	ca1 []categoryentity.CategoryPermission
	err error
}

// CategoryPermissionPrimeDBMockGetByCategoryIDOrigins contains origins of expectations of the CategoryPermissionPrimeDB.GetByCategoryID
type CategoryPermissionPrimeDBMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Optional() *mCategoryPermissionPrimeDBMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for CategoryPermissionPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Expect(categoryID int64) *mCategoryPermissionPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryPermissionPrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &CategoryPermissionPrimeDBMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryPermissionPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mCategoryPermissionPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryPermissionPrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Inspect(f func(categoryID int64)) *mCategoryPermissionPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for CategoryPermissionPrimeDBMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by CategoryPermissionPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Return(ca1 []categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryPermissionPrimeDBMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &CategoryPermissionPrimeDBMockGetByCategoryIDResults{ca1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the CategoryPermissionPrimeDB.GetByCategoryID method
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Set(f func(categoryID int64) (ca1 []categoryentity.CategoryPermission, err error)) *CategoryPermissionPrimeDBMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionPrimeDB.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionPrimeDB.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the CategoryPermissionPrimeDB.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) When(categoryID int64) *CategoryPermissionPrimeDBMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &CategoryPermissionPrimeDBMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &CategoryPermissionPrimeDBMockGetByCategoryIDParams{categoryID},
		expectationOrigins: CategoryPermissionPrimeDBMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionPrimeDB.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionPrimeDBMockGetByCategoryIDExpectation) Then(ca1 []categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	e.results = &CategoryPermissionPrimeDBMockGetByCategoryIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryPermissionPrimeDB.GetByCategoryID should be invoked
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Times(n uint64) *mCategoryPermissionPrimeDBMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of CategoryPermissionPrimeDBMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.CategoryPermissionPrimeDB
func (mmGetByCategoryID *CategoryPermissionPrimeDBMock) GetByCategoryID(categoryID int64) (ca1 []categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := CategoryPermissionPrimeDBMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionPrimeDBMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("CategoryPermissionPrimeDBMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("CategoryPermissionPrimeDBMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the CategoryPermissionPrimeDBMock.GetByCategoryID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to CategoryPermissionPrimeDBMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished CategoryPermissionPrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryPermissionPrimeDBMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of CategoryPermissionPrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryPermissionPrimeDBMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionPrimeDBMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mCategoryPermissionPrimeDBMockGetByCategoryID) Calls() []*CategoryPermissionPrimeDBMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*CategoryPermissionPrimeDBMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionPrimeDBMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *CategoryPermissionPrimeDBMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionPrimeDBMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mCategoryPermissionPrimeDBMockGetByID struct {
	optional           bool
	mock               *CategoryPermissionPrimeDBMock
	defaultExpectation *CategoryPermissionPrimeDBMockGetByIDExpectation
	expectations       []*CategoryPermissionPrimeDBMockGetByIDExpectation

	callArgs []*CategoryPermissionPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionPrimeDBMockGetByIDExpectation specifies expectation struct of the CategoryPermissionPrimeDB.GetByID
type CategoryPermissionPrimeDBMockGetByIDExpectation struct {
	mock               *CategoryPermissionPrimeDBMock
	params             *CategoryPermissionPrimeDBMockGetByIDParams
	paramPtrs          *CategoryPermissionPrimeDBMockGetByIDParamPtrs
	expectationOrigins CategoryPermissionPrimeDBMockGetByIDExpectationOrigins
	results            *CategoryPermissionPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionPrimeDBMockGetByIDParams contains parameters of the CategoryPermissionPrimeDB.GetByID
type CategoryPermissionPrimeDBMockGetByIDParams struct {
	id int64
}

// CategoryPermissionPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the CategoryPermissionPrimeDB.GetByID
type CategoryPermissionPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryPermissionPrimeDBMockGetByIDResults contains results of the CategoryPermissionPrimeDB.GetByID
type CategoryPermissionPrimeDBMockGetByIDResults struct {
	c1  categoryentity.CategoryPermission
	err error
}

// CategoryPermissionPrimeDBMockGetByIDOrigins contains origins of expectations of the CategoryPermissionPrimeDB.GetByID
type CategoryPermissionPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Optional() *mCategoryPermissionPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryPermissionPrimeDB.GetByID
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Expect(id int64) *mCategoryPermissionPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPermissionPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryPermissionPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryPermissionPrimeDB.GetByID
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) ExpectIdParam1(id int64) *mCategoryPermissionPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPermissionPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionPrimeDB.GetByID
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Inspect(f func(id int64)) *mCategoryPermissionPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryPermissionPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryPermissionPrimeDB.GetByID
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Return(c1 categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPermissionPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryPermissionPrimeDBMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryPermissionPrimeDB.GetByID method
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Set(f func(id int64) (c1 categoryentity.CategoryPermission, err error)) *CategoryPermissionPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryPermissionPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) When(id int64) *CategoryPermissionPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryPermissionPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryPermissionPrimeDBMockGetByIDParams{id},
		expectationOrigins: CategoryPermissionPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionPrimeDBMockGetByIDExpectation) Then(c1 categoryentity.CategoryPermission, err error) *CategoryPermissionPrimeDBMock {
	e.results = &CategoryPermissionPrimeDBMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPermissionPrimeDB.GetByID should be invoked
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Times(n uint64) *mCategoryPermissionPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryPermissionPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryPermissionPrimeDB
func (mmGetByID *CategoryPermissionPrimeDBMock) GetByID(id int64) (c1 categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryPermissionPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryPermissionPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryPermissionPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryPermissionPrimeDBMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryPermissionPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryPermissionPrimeDBMock.GetByID invocations
func (mmGetByID *CategoryPermissionPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryPermissionPrimeDBMock.GetByID invocations
func (mmGetByID *CategoryPermissionPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryPermissionPrimeDBMockGetByID) Calls() []*CategoryPermissionPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryPermissionPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryPermissionPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryPermissionPrimeDBMockHasCategoryPermission struct {
	optional           bool
	mock               *CategoryPermissionPrimeDBMock
	defaultExpectation *CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation
	expectations       []*CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation

	callArgs []*CategoryPermissionPrimeDBMockHasCategoryPermissionParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation specifies expectation struct of the CategoryPermissionPrimeDB.HasCategoryPermission
type CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation struct {
	mock               *CategoryPermissionPrimeDBMock
	params             *CategoryPermissionPrimeDBMockHasCategoryPermissionParams
	paramPtrs          *CategoryPermissionPrimeDBMockHasCategoryPermissionParamPtrs
	expectationOrigins CategoryPermissionPrimeDBMockHasCategoryPermissionExpectationOrigins
	results            *CategoryPermissionPrimeDBMockHasCategoryPermissionResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionPrimeDBMockHasCategoryPermissionParams contains parameters of the CategoryPermissionPrimeDB.HasCategoryPermission
type CategoryPermissionPrimeDBMockHasCategoryPermissionParams struct {
	categoryID   int64
	permissionID int64
}

// CategoryPermissionPrimeDBMockHasCategoryPermissionParamPtrs contains pointers to parameters of the CategoryPermissionPrimeDB.HasCategoryPermission
type CategoryPermissionPrimeDBMockHasCategoryPermissionParamPtrs struct {
	categoryID   *int64
	permissionID *int64
}

// CategoryPermissionPrimeDBMockHasCategoryPermissionResults contains results of the CategoryPermissionPrimeDB.HasCategoryPermission
type CategoryPermissionPrimeDBMockHasCategoryPermissionResults struct {
	b1  bool
	err error
}

// CategoryPermissionPrimeDBMockHasCategoryPermissionOrigins contains origins of expectations of the CategoryPermissionPrimeDB.HasCategoryPermission
type CategoryPermissionPrimeDBMockHasCategoryPermissionExpectationOrigins struct {
	origin             string
	originCategoryID   string
	originPermissionID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Optional() *mCategoryPermissionPrimeDBMockHasCategoryPermission {
	mmHasCategoryPermission.optional = true
	return mmHasCategoryPermission
}

// Expect sets up expected params for CategoryPermissionPrimeDB.HasCategoryPermission
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Expect(categoryID int64, permissionID int64) *mCategoryPermissionPrimeDBMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation{}
	}

	if mmHasCategoryPermission.defaultExpectation.paramPtrs != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by ExpectParams functions")
	}

	mmHasCategoryPermission.defaultExpectation.params = &CategoryPermissionPrimeDBMockHasCategoryPermissionParams{categoryID, permissionID}
	mmHasCategoryPermission.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmHasCategoryPermission.expectations {
		if minimock.Equal(e.params, mmHasCategoryPermission.defaultExpectation.params) {
			mmHasCategoryPermission.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmHasCategoryPermission.defaultExpectation.params)
		}
	}

	return mmHasCategoryPermission
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryPermissionPrimeDB.HasCategoryPermission
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) ExpectCategoryIDParam1(categoryID int64) *mCategoryPermissionPrimeDBMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation{}
	}

	if mmHasCategoryPermission.defaultExpectation.params != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Expect")
	}

	if mmHasCategoryPermission.defaultExpectation.paramPtrs == nil {
		mmHasCategoryPermission.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockHasCategoryPermissionParamPtrs{}
	}
	mmHasCategoryPermission.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmHasCategoryPermission.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmHasCategoryPermission
}

// ExpectPermissionIDParam2 sets up expected param permissionID for CategoryPermissionPrimeDB.HasCategoryPermission
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) ExpectPermissionIDParam2(permissionID int64) *mCategoryPermissionPrimeDBMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation{}
	}

	if mmHasCategoryPermission.defaultExpectation.params != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Expect")
	}

	if mmHasCategoryPermission.defaultExpectation.paramPtrs == nil {
		mmHasCategoryPermission.defaultExpectation.paramPtrs = &CategoryPermissionPrimeDBMockHasCategoryPermissionParamPtrs{}
	}
	mmHasCategoryPermission.defaultExpectation.paramPtrs.permissionID = &permissionID
	mmHasCategoryPermission.defaultExpectation.expectationOrigins.originPermissionID = minimock.CallerInfo(1)

	return mmHasCategoryPermission
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionPrimeDB.HasCategoryPermission
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Inspect(f func(categoryID int64, permissionID int64)) *mCategoryPermissionPrimeDBMockHasCategoryPermission {
	if mmHasCategoryPermission.mock.inspectFuncHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("Inspect function is already set for CategoryPermissionPrimeDBMock.HasCategoryPermission")
	}

	mmHasCategoryPermission.mock.inspectFuncHasCategoryPermission = f

	return mmHasCategoryPermission
}

// Return sets up results that will be returned by CategoryPermissionPrimeDB.HasCategoryPermission
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Return(b1 bool, err error) *CategoryPermissionPrimeDBMock {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Set")
	}

	if mmHasCategoryPermission.defaultExpectation == nil {
		mmHasCategoryPermission.defaultExpectation = &CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation{mock: mmHasCategoryPermission.mock}
	}
	mmHasCategoryPermission.defaultExpectation.results = &CategoryPermissionPrimeDBMockHasCategoryPermissionResults{b1, err}
	mmHasCategoryPermission.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmHasCategoryPermission.mock
}

// Set uses given function f to mock the CategoryPermissionPrimeDB.HasCategoryPermission method
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Set(f func(categoryID int64, permissionID int64) (b1 bool, err error)) *CategoryPermissionPrimeDBMock {
	if mmHasCategoryPermission.defaultExpectation != nil {
		mmHasCategoryPermission.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionPrimeDB.HasCategoryPermission method")
	}

	if len(mmHasCategoryPermission.expectations) > 0 {
		mmHasCategoryPermission.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionPrimeDB.HasCategoryPermission method")
	}

	mmHasCategoryPermission.mock.funcHasCategoryPermission = f
	mmHasCategoryPermission.mock.funcHasCategoryPermissionOrigin = minimock.CallerInfo(1)
	return mmHasCategoryPermission.mock
}

// When sets expectation for the CategoryPermissionPrimeDB.HasCategoryPermission which will trigger the result defined by the following
// Then helper
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) When(categoryID int64, permissionID int64) *CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation {
	if mmHasCategoryPermission.mock.funcHasCategoryPermission != nil {
		mmHasCategoryPermission.mock.t.Fatalf("CategoryPermissionPrimeDBMock.HasCategoryPermission mock is already set by Set")
	}

	expectation := &CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation{
		mock:               mmHasCategoryPermission.mock,
		params:             &CategoryPermissionPrimeDBMockHasCategoryPermissionParams{categoryID, permissionID},
		expectationOrigins: CategoryPermissionPrimeDBMockHasCategoryPermissionExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmHasCategoryPermission.expectations = append(mmHasCategoryPermission.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionPrimeDB.HasCategoryPermission return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionPrimeDBMockHasCategoryPermissionExpectation) Then(b1 bool, err error) *CategoryPermissionPrimeDBMock {
	e.results = &CategoryPermissionPrimeDBMockHasCategoryPermissionResults{b1, err}
	return e.mock
}

// Times sets number of times CategoryPermissionPrimeDB.HasCategoryPermission should be invoked
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Times(n uint64) *mCategoryPermissionPrimeDBMockHasCategoryPermission {
	if n == 0 {
		mmHasCategoryPermission.mock.t.Fatalf("Times of CategoryPermissionPrimeDBMock.HasCategoryPermission mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmHasCategoryPermission.expectedInvocations, n)
	mmHasCategoryPermission.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmHasCategoryPermission
}

func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) invocationsDone() bool {
	if len(mmHasCategoryPermission.expectations) == 0 && mmHasCategoryPermission.defaultExpectation == nil && mmHasCategoryPermission.mock.funcHasCategoryPermission == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmHasCategoryPermission.mock.afterHasCategoryPermissionCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmHasCategoryPermission.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// HasCategoryPermission implements mm_repository.CategoryPermissionPrimeDB
func (mmHasCategoryPermission *CategoryPermissionPrimeDBMock) HasCategoryPermission(categoryID int64, permissionID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmHasCategoryPermission.beforeHasCategoryPermissionCounter, 1)
	defer mm_atomic.AddUint64(&mmHasCategoryPermission.afterHasCategoryPermissionCounter, 1)

	mmHasCategoryPermission.t.Helper()

	if mmHasCategoryPermission.inspectFuncHasCategoryPermission != nil {
		mmHasCategoryPermission.inspectFuncHasCategoryPermission(categoryID, permissionID)
	}

	mm_params := CategoryPermissionPrimeDBMockHasCategoryPermissionParams{categoryID, permissionID}

	// Record call args
	mmHasCategoryPermission.HasCategoryPermissionMock.mutex.Lock()
	mmHasCategoryPermission.HasCategoryPermissionMock.callArgs = append(mmHasCategoryPermission.HasCategoryPermissionMock.callArgs, &mm_params)
	mmHasCategoryPermission.HasCategoryPermissionMock.mutex.Unlock()

	for _, e := range mmHasCategoryPermission.HasCategoryPermissionMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.Counter, 1)
		mm_want := mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.params
		mm_want_ptrs := mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionPrimeDBMockHasCategoryPermissionParams{categoryID, permissionID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmHasCategoryPermission.t.Errorf("CategoryPermissionPrimeDBMock.HasCategoryPermission got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.permissionID != nil && !minimock.Equal(*mm_want_ptrs.permissionID, mm_got.permissionID) {
				mmHasCategoryPermission.t.Errorf("CategoryPermissionPrimeDBMock.HasCategoryPermission got unexpected parameter permissionID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.originPermissionID, *mm_want_ptrs.permissionID, mm_got.permissionID, minimock.Diff(*mm_want_ptrs.permissionID, mm_got.permissionID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmHasCategoryPermission.t.Errorf("CategoryPermissionPrimeDBMock.HasCategoryPermission got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmHasCategoryPermission.HasCategoryPermissionMock.defaultExpectation.results
		if mm_results == nil {
			mmHasCategoryPermission.t.Fatal("No results are set for the CategoryPermissionPrimeDBMock.HasCategoryPermission")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmHasCategoryPermission.funcHasCategoryPermission != nil {
		return mmHasCategoryPermission.funcHasCategoryPermission(categoryID, permissionID)
	}
	mmHasCategoryPermission.t.Fatalf("Unexpected call to CategoryPermissionPrimeDBMock.HasCategoryPermission. %v %v", categoryID, permissionID)
	return
}

// HasCategoryPermissionAfterCounter returns a count of finished CategoryPermissionPrimeDBMock.HasCategoryPermission invocations
func (mmHasCategoryPermission *CategoryPermissionPrimeDBMock) HasCategoryPermissionAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryPermission.afterHasCategoryPermissionCounter)
}

// HasCategoryPermissionBeforeCounter returns a count of CategoryPermissionPrimeDBMock.HasCategoryPermission invocations
func (mmHasCategoryPermission *CategoryPermissionPrimeDBMock) HasCategoryPermissionBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryPermission.beforeHasCategoryPermissionCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionPrimeDBMock.HasCategoryPermission.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmHasCategoryPermission *mCategoryPermissionPrimeDBMockHasCategoryPermission) Calls() []*CategoryPermissionPrimeDBMockHasCategoryPermissionParams {
	mmHasCategoryPermission.mutex.RLock()

	argCopy := make([]*CategoryPermissionPrimeDBMockHasCategoryPermissionParams, len(mmHasCategoryPermission.callArgs))
	copy(argCopy, mmHasCategoryPermission.callArgs)

	mmHasCategoryPermission.mutex.RUnlock()

	return argCopy
}

// MinimockHasCategoryPermissionDone returns true if the count of the HasCategoryPermission invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionPrimeDBMock) MinimockHasCategoryPermissionDone() bool {
	if m.HasCategoryPermissionMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.HasCategoryPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.HasCategoryPermissionMock.invocationsDone()
}

// MinimockHasCategoryPermissionInspect logs each unmet expectation
func (m *CategoryPermissionPrimeDBMock) MinimockHasCategoryPermissionInspect() {
	for _, e := range m.HasCategoryPermissionMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.HasCategoryPermission at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterHasCategoryPermissionCounter := mm_atomic.LoadUint64(&m.afterHasCategoryPermissionCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.HasCategoryPermissionMock.defaultExpectation != nil && afterHasCategoryPermissionCounter < 1 {
		if m.HasCategoryPermissionMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.HasCategoryPermission at\n%s", m.HasCategoryPermissionMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.HasCategoryPermission at\n%s with params: %#v", m.HasCategoryPermissionMock.defaultExpectation.expectationOrigins.origin, *m.HasCategoryPermissionMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcHasCategoryPermission != nil && afterHasCategoryPermissionCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionPrimeDBMock.HasCategoryPermission at\n%s", m.funcHasCategoryPermissionOrigin)
	}

	if !m.HasCategoryPermissionMock.invocationsDone() && afterHasCategoryPermissionCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionPrimeDBMock.HasCategoryPermission at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.HasCategoryPermissionMock.expectedInvocations), m.HasCategoryPermissionMock.expectedInvocationsOrigin, afterHasCategoryPermissionCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryPermissionPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeleteCategoryPermissionInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockHasCategoryPermissionInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryPermissionPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryPermissionPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeleteCategoryPermissionDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockHasCategoryPermissionDone()
}
