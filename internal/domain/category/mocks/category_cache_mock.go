// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryCache -o category_cache_mock.go -n CategoryCacheMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryCacheMock implements mm_repository.CategoryCache
type CategoryCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcDeleteCategories          func(ctx context.Context, ids []int64) (err error)
	funcDeleteCategoriesOrigin    string
	inspectFuncDeleteCategories   func(ctx context.Context, ids []int64)
	afterDeleteCategoriesCounter  uint64
	beforeDeleteCategoriesCounter uint64
	DeleteCategoriesMock          mCategoryCacheMockDeleteCategories

	funcDeleteCategory          func(ctx context.Context, id int64) (err error)
	funcDeleteCategoryOrigin    string
	inspectFuncDeleteCategory   func(ctx context.Context, id int64)
	afterDeleteCategoryCounter  uint64
	beforeDeleteCategoryCounter uint64
	DeleteCategoryMock          mCategoryCacheMockDeleteCategory

	funcGetCategories          func(ctx context.Context) (ca1 []categoryentity.Category, err error)
	funcGetCategoriesOrigin    string
	inspectFuncGetCategories   func(ctx context.Context)
	afterGetCategoriesCounter  uint64
	beforeGetCategoriesCounter uint64
	GetCategoriesMock          mCategoryCacheMockGetCategories

	funcGetCategory          func(ctx context.Context, id int64) (c2 categoryentity.Category, err error)
	funcGetCategoryOrigin    string
	inspectFuncGetCategory   func(ctx context.Context, id int64)
	afterGetCategoryCounter  uint64
	beforeGetCategoryCounter uint64
	GetCategoryMock          mCategoryCacheMockGetCategory

	funcSetCategories          func(ctx context.Context, categories []categoryentity.Category) (err error)
	funcSetCategoriesOrigin    string
	inspectFuncSetCategories   func(ctx context.Context, categories []categoryentity.Category)
	afterSetCategoriesCounter  uint64
	beforeSetCategoriesCounter uint64
	SetCategoriesMock          mCategoryCacheMockSetCategories

	funcSetCategory          func(ctx context.Context, category categoryentity.Category) (err error)
	funcSetCategoryOrigin    string
	inspectFuncSetCategory   func(ctx context.Context, category categoryentity.Category)
	afterSetCategoryCounter  uint64
	beforeSetCategoryCounter uint64
	SetCategoryMock          mCategoryCacheMockSetCategory
}

// NewCategoryCacheMock returns a mock for mm_repository.CategoryCache
func NewCategoryCacheMock(t minimock.Tester) *CategoryCacheMock {
	m := &CategoryCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.DeleteCategoriesMock = mCategoryCacheMockDeleteCategories{mock: m}
	m.DeleteCategoriesMock.callArgs = []*CategoryCacheMockDeleteCategoriesParams{}

	m.DeleteCategoryMock = mCategoryCacheMockDeleteCategory{mock: m}
	m.DeleteCategoryMock.callArgs = []*CategoryCacheMockDeleteCategoryParams{}

	m.GetCategoriesMock = mCategoryCacheMockGetCategories{mock: m}
	m.GetCategoriesMock.callArgs = []*CategoryCacheMockGetCategoriesParams{}

	m.GetCategoryMock = mCategoryCacheMockGetCategory{mock: m}
	m.GetCategoryMock.callArgs = []*CategoryCacheMockGetCategoryParams{}

	m.SetCategoriesMock = mCategoryCacheMockSetCategories{mock: m}
	m.SetCategoriesMock.callArgs = []*CategoryCacheMockSetCategoriesParams{}

	m.SetCategoryMock = mCategoryCacheMockSetCategory{mock: m}
	m.SetCategoryMock.callArgs = []*CategoryCacheMockSetCategoryParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryCacheMockDeleteCategories struct {
	optional           bool
	mock               *CategoryCacheMock
	defaultExpectation *CategoryCacheMockDeleteCategoriesExpectation
	expectations       []*CategoryCacheMockDeleteCategoriesExpectation

	callArgs []*CategoryCacheMockDeleteCategoriesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryCacheMockDeleteCategoriesExpectation specifies expectation struct of the CategoryCache.DeleteCategories
type CategoryCacheMockDeleteCategoriesExpectation struct {
	mock               *CategoryCacheMock
	params             *CategoryCacheMockDeleteCategoriesParams
	paramPtrs          *CategoryCacheMockDeleteCategoriesParamPtrs
	expectationOrigins CategoryCacheMockDeleteCategoriesExpectationOrigins
	results            *CategoryCacheMockDeleteCategoriesResults
	returnOrigin       string
	Counter            uint64
}

// CategoryCacheMockDeleteCategoriesParams contains parameters of the CategoryCache.DeleteCategories
type CategoryCacheMockDeleteCategoriesParams struct {
	ctx context.Context
	ids []int64
}

// CategoryCacheMockDeleteCategoriesParamPtrs contains pointers to parameters of the CategoryCache.DeleteCategories
type CategoryCacheMockDeleteCategoriesParamPtrs struct {
	ctx *context.Context
	ids *[]int64
}

// CategoryCacheMockDeleteCategoriesResults contains results of the CategoryCache.DeleteCategories
type CategoryCacheMockDeleteCategoriesResults struct {
	err error
}

// CategoryCacheMockDeleteCategoriesOrigins contains origins of expectations of the CategoryCache.DeleteCategories
type CategoryCacheMockDeleteCategoriesExpectationOrigins struct {
	origin    string
	originCtx string
	originIds string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Optional() *mCategoryCacheMockDeleteCategories {
	mmDeleteCategories.optional = true
	return mmDeleteCategories
}

// Expect sets up expected params for CategoryCache.DeleteCategories
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Expect(ctx context.Context, ids []int64) *mCategoryCacheMockDeleteCategories {
	if mmDeleteCategories.mock.funcDeleteCategories != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Set")
	}

	if mmDeleteCategories.defaultExpectation == nil {
		mmDeleteCategories.defaultExpectation = &CategoryCacheMockDeleteCategoriesExpectation{}
	}

	if mmDeleteCategories.defaultExpectation.paramPtrs != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by ExpectParams functions")
	}

	mmDeleteCategories.defaultExpectation.params = &CategoryCacheMockDeleteCategoriesParams{ctx, ids}
	mmDeleteCategories.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteCategories.expectations {
		if minimock.Equal(e.params, mmDeleteCategories.defaultExpectation.params) {
			mmDeleteCategories.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteCategories.defaultExpectation.params)
		}
	}

	return mmDeleteCategories
}

// ExpectCtxParam1 sets up expected param ctx for CategoryCache.DeleteCategories
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) ExpectCtxParam1(ctx context.Context) *mCategoryCacheMockDeleteCategories {
	if mmDeleteCategories.mock.funcDeleteCategories != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Set")
	}

	if mmDeleteCategories.defaultExpectation == nil {
		mmDeleteCategories.defaultExpectation = &CategoryCacheMockDeleteCategoriesExpectation{}
	}

	if mmDeleteCategories.defaultExpectation.params != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Expect")
	}

	if mmDeleteCategories.defaultExpectation.paramPtrs == nil {
		mmDeleteCategories.defaultExpectation.paramPtrs = &CategoryCacheMockDeleteCategoriesParamPtrs{}
	}
	mmDeleteCategories.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteCategories.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteCategories
}

// ExpectIdsParam2 sets up expected param ids for CategoryCache.DeleteCategories
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) ExpectIdsParam2(ids []int64) *mCategoryCacheMockDeleteCategories {
	if mmDeleteCategories.mock.funcDeleteCategories != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Set")
	}

	if mmDeleteCategories.defaultExpectation == nil {
		mmDeleteCategories.defaultExpectation = &CategoryCacheMockDeleteCategoriesExpectation{}
	}

	if mmDeleteCategories.defaultExpectation.params != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Expect")
	}

	if mmDeleteCategories.defaultExpectation.paramPtrs == nil {
		mmDeleteCategories.defaultExpectation.paramPtrs = &CategoryCacheMockDeleteCategoriesParamPtrs{}
	}
	mmDeleteCategories.defaultExpectation.paramPtrs.ids = &ids
	mmDeleteCategories.defaultExpectation.expectationOrigins.originIds = minimock.CallerInfo(1)

	return mmDeleteCategories
}

// Inspect accepts an inspector function that has same arguments as the CategoryCache.DeleteCategories
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Inspect(f func(ctx context.Context, ids []int64)) *mCategoryCacheMockDeleteCategories {
	if mmDeleteCategories.mock.inspectFuncDeleteCategories != nil {
		mmDeleteCategories.mock.t.Fatalf("Inspect function is already set for CategoryCacheMock.DeleteCategories")
	}

	mmDeleteCategories.mock.inspectFuncDeleteCategories = f

	return mmDeleteCategories
}

// Return sets up results that will be returned by CategoryCache.DeleteCategories
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Return(err error) *CategoryCacheMock {
	if mmDeleteCategories.mock.funcDeleteCategories != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Set")
	}

	if mmDeleteCategories.defaultExpectation == nil {
		mmDeleteCategories.defaultExpectation = &CategoryCacheMockDeleteCategoriesExpectation{mock: mmDeleteCategories.mock}
	}
	mmDeleteCategories.defaultExpectation.results = &CategoryCacheMockDeleteCategoriesResults{err}
	mmDeleteCategories.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteCategories.mock
}

// Set uses given function f to mock the CategoryCache.DeleteCategories method
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Set(f func(ctx context.Context, ids []int64) (err error)) *CategoryCacheMock {
	if mmDeleteCategories.defaultExpectation != nil {
		mmDeleteCategories.mock.t.Fatalf("Default expectation is already set for the CategoryCache.DeleteCategories method")
	}

	if len(mmDeleteCategories.expectations) > 0 {
		mmDeleteCategories.mock.t.Fatalf("Some expectations are already set for the CategoryCache.DeleteCategories method")
	}

	mmDeleteCategories.mock.funcDeleteCategories = f
	mmDeleteCategories.mock.funcDeleteCategoriesOrigin = minimock.CallerInfo(1)
	return mmDeleteCategories.mock
}

// When sets expectation for the CategoryCache.DeleteCategories which will trigger the result defined by the following
// Then helper
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) When(ctx context.Context, ids []int64) *CategoryCacheMockDeleteCategoriesExpectation {
	if mmDeleteCategories.mock.funcDeleteCategories != nil {
		mmDeleteCategories.mock.t.Fatalf("CategoryCacheMock.DeleteCategories mock is already set by Set")
	}

	expectation := &CategoryCacheMockDeleteCategoriesExpectation{
		mock:               mmDeleteCategories.mock,
		params:             &CategoryCacheMockDeleteCategoriesParams{ctx, ids},
		expectationOrigins: CategoryCacheMockDeleteCategoriesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteCategories.expectations = append(mmDeleteCategories.expectations, expectation)
	return expectation
}

// Then sets up CategoryCache.DeleteCategories return parameters for the expectation previously defined by the When method
func (e *CategoryCacheMockDeleteCategoriesExpectation) Then(err error) *CategoryCacheMock {
	e.results = &CategoryCacheMockDeleteCategoriesResults{err}
	return e.mock
}

// Times sets number of times CategoryCache.DeleteCategories should be invoked
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Times(n uint64) *mCategoryCacheMockDeleteCategories {
	if n == 0 {
		mmDeleteCategories.mock.t.Fatalf("Times of CategoryCacheMock.DeleteCategories mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteCategories.expectedInvocations, n)
	mmDeleteCategories.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteCategories
}

func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) invocationsDone() bool {
	if len(mmDeleteCategories.expectations) == 0 && mmDeleteCategories.defaultExpectation == nil && mmDeleteCategories.mock.funcDeleteCategories == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteCategories.mock.afterDeleteCategoriesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteCategories.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteCategories implements mm_repository.CategoryCache
func (mmDeleteCategories *CategoryCacheMock) DeleteCategories(ctx context.Context, ids []int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteCategories.beforeDeleteCategoriesCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteCategories.afterDeleteCategoriesCounter, 1)

	mmDeleteCategories.t.Helper()

	if mmDeleteCategories.inspectFuncDeleteCategories != nil {
		mmDeleteCategories.inspectFuncDeleteCategories(ctx, ids)
	}

	mm_params := CategoryCacheMockDeleteCategoriesParams{ctx, ids}

	// Record call args
	mmDeleteCategories.DeleteCategoriesMock.mutex.Lock()
	mmDeleteCategories.DeleteCategoriesMock.callArgs = append(mmDeleteCategories.DeleteCategoriesMock.callArgs, &mm_params)
	mmDeleteCategories.DeleteCategoriesMock.mutex.Unlock()

	for _, e := range mmDeleteCategories.DeleteCategoriesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteCategories.DeleteCategoriesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.paramPtrs

		mm_got := CategoryCacheMockDeleteCategoriesParams{ctx, ids}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteCategories.t.Errorf("CategoryCacheMock.DeleteCategories got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.ids != nil && !minimock.Equal(*mm_want_ptrs.ids, mm_got.ids) {
				mmDeleteCategories.t.Errorf("CategoryCacheMock.DeleteCategories got unexpected parameter ids, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.expectationOrigins.originIds, *mm_want_ptrs.ids, mm_got.ids, minimock.Diff(*mm_want_ptrs.ids, mm_got.ids))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteCategories.t.Errorf("CategoryCacheMock.DeleteCategories got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteCategories.DeleteCategoriesMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteCategories.t.Fatal("No results are set for the CategoryCacheMock.DeleteCategories")
		}
		return (*mm_results).err
	}
	if mmDeleteCategories.funcDeleteCategories != nil {
		return mmDeleteCategories.funcDeleteCategories(ctx, ids)
	}
	mmDeleteCategories.t.Fatalf("Unexpected call to CategoryCacheMock.DeleteCategories. %v %v", ctx, ids)
	return
}

// DeleteCategoriesAfterCounter returns a count of finished CategoryCacheMock.DeleteCategories invocations
func (mmDeleteCategories *CategoryCacheMock) DeleteCategoriesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteCategories.afterDeleteCategoriesCounter)
}

// DeleteCategoriesBeforeCounter returns a count of CategoryCacheMock.DeleteCategories invocations
func (mmDeleteCategories *CategoryCacheMock) DeleteCategoriesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteCategories.beforeDeleteCategoriesCounter)
}

// Calls returns a list of arguments used in each call to CategoryCacheMock.DeleteCategories.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteCategories *mCategoryCacheMockDeleteCategories) Calls() []*CategoryCacheMockDeleteCategoriesParams {
	mmDeleteCategories.mutex.RLock()

	argCopy := make([]*CategoryCacheMockDeleteCategoriesParams, len(mmDeleteCategories.callArgs))
	copy(argCopy, mmDeleteCategories.callArgs)

	mmDeleteCategories.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteCategoriesDone returns true if the count of the DeleteCategories invocations corresponds
// the number of defined expectations
func (m *CategoryCacheMock) MinimockDeleteCategoriesDone() bool {
	if m.DeleteCategoriesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteCategoriesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteCategoriesMock.invocationsDone()
}

// MinimockDeleteCategoriesInspect logs each unmet expectation
func (m *CategoryCacheMock) MinimockDeleteCategoriesInspect() {
	for _, e := range m.DeleteCategoriesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategories at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCategoriesCounter := mm_atomic.LoadUint64(&m.afterDeleteCategoriesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteCategoriesMock.defaultExpectation != nil && afterDeleteCategoriesCounter < 1 {
		if m.DeleteCategoriesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategories at\n%s", m.DeleteCategoriesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategories at\n%s with params: %#v", m.DeleteCategoriesMock.defaultExpectation.expectationOrigins.origin, *m.DeleteCategoriesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteCategories != nil && afterDeleteCategoriesCounter < 1 {
		m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategories at\n%s", m.funcDeleteCategoriesOrigin)
	}

	if !m.DeleteCategoriesMock.invocationsDone() && afterDeleteCategoriesCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryCacheMock.DeleteCategories at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteCategoriesMock.expectedInvocations), m.DeleteCategoriesMock.expectedInvocationsOrigin, afterDeleteCategoriesCounter)
	}
}

type mCategoryCacheMockDeleteCategory struct {
	optional           bool
	mock               *CategoryCacheMock
	defaultExpectation *CategoryCacheMockDeleteCategoryExpectation
	expectations       []*CategoryCacheMockDeleteCategoryExpectation

	callArgs []*CategoryCacheMockDeleteCategoryParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryCacheMockDeleteCategoryExpectation specifies expectation struct of the CategoryCache.DeleteCategory
type CategoryCacheMockDeleteCategoryExpectation struct {
	mock               *CategoryCacheMock
	params             *CategoryCacheMockDeleteCategoryParams
	paramPtrs          *CategoryCacheMockDeleteCategoryParamPtrs
	expectationOrigins CategoryCacheMockDeleteCategoryExpectationOrigins
	results            *CategoryCacheMockDeleteCategoryResults
	returnOrigin       string
	Counter            uint64
}

// CategoryCacheMockDeleteCategoryParams contains parameters of the CategoryCache.DeleteCategory
type CategoryCacheMockDeleteCategoryParams struct {
	ctx context.Context
	id  int64
}

// CategoryCacheMockDeleteCategoryParamPtrs contains pointers to parameters of the CategoryCache.DeleteCategory
type CategoryCacheMockDeleteCategoryParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// CategoryCacheMockDeleteCategoryResults contains results of the CategoryCache.DeleteCategory
type CategoryCacheMockDeleteCategoryResults struct {
	err error
}

// CategoryCacheMockDeleteCategoryOrigins contains origins of expectations of the CategoryCache.DeleteCategory
type CategoryCacheMockDeleteCategoryExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Optional() *mCategoryCacheMockDeleteCategory {
	mmDeleteCategory.optional = true
	return mmDeleteCategory
}

// Expect sets up expected params for CategoryCache.DeleteCategory
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Expect(ctx context.Context, id int64) *mCategoryCacheMockDeleteCategory {
	if mmDeleteCategory.mock.funcDeleteCategory != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Set")
	}

	if mmDeleteCategory.defaultExpectation == nil {
		mmDeleteCategory.defaultExpectation = &CategoryCacheMockDeleteCategoryExpectation{}
	}

	if mmDeleteCategory.defaultExpectation.paramPtrs != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by ExpectParams functions")
	}

	mmDeleteCategory.defaultExpectation.params = &CategoryCacheMockDeleteCategoryParams{ctx, id}
	mmDeleteCategory.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDeleteCategory.expectations {
		if minimock.Equal(e.params, mmDeleteCategory.defaultExpectation.params) {
			mmDeleteCategory.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDeleteCategory.defaultExpectation.params)
		}
	}

	return mmDeleteCategory
}

// ExpectCtxParam1 sets up expected param ctx for CategoryCache.DeleteCategory
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) ExpectCtxParam1(ctx context.Context) *mCategoryCacheMockDeleteCategory {
	if mmDeleteCategory.mock.funcDeleteCategory != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Set")
	}

	if mmDeleteCategory.defaultExpectation == nil {
		mmDeleteCategory.defaultExpectation = &CategoryCacheMockDeleteCategoryExpectation{}
	}

	if mmDeleteCategory.defaultExpectation.params != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Expect")
	}

	if mmDeleteCategory.defaultExpectation.paramPtrs == nil {
		mmDeleteCategory.defaultExpectation.paramPtrs = &CategoryCacheMockDeleteCategoryParamPtrs{}
	}
	mmDeleteCategory.defaultExpectation.paramPtrs.ctx = &ctx
	mmDeleteCategory.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDeleteCategory
}

// ExpectIdParam2 sets up expected param id for CategoryCache.DeleteCategory
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) ExpectIdParam2(id int64) *mCategoryCacheMockDeleteCategory {
	if mmDeleteCategory.mock.funcDeleteCategory != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Set")
	}

	if mmDeleteCategory.defaultExpectation == nil {
		mmDeleteCategory.defaultExpectation = &CategoryCacheMockDeleteCategoryExpectation{}
	}

	if mmDeleteCategory.defaultExpectation.params != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Expect")
	}

	if mmDeleteCategory.defaultExpectation.paramPtrs == nil {
		mmDeleteCategory.defaultExpectation.paramPtrs = &CategoryCacheMockDeleteCategoryParamPtrs{}
	}
	mmDeleteCategory.defaultExpectation.paramPtrs.id = &id
	mmDeleteCategory.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDeleteCategory
}

// Inspect accepts an inspector function that has same arguments as the CategoryCache.DeleteCategory
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Inspect(f func(ctx context.Context, id int64)) *mCategoryCacheMockDeleteCategory {
	if mmDeleteCategory.mock.inspectFuncDeleteCategory != nil {
		mmDeleteCategory.mock.t.Fatalf("Inspect function is already set for CategoryCacheMock.DeleteCategory")
	}

	mmDeleteCategory.mock.inspectFuncDeleteCategory = f

	return mmDeleteCategory
}

// Return sets up results that will be returned by CategoryCache.DeleteCategory
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Return(err error) *CategoryCacheMock {
	if mmDeleteCategory.mock.funcDeleteCategory != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Set")
	}

	if mmDeleteCategory.defaultExpectation == nil {
		mmDeleteCategory.defaultExpectation = &CategoryCacheMockDeleteCategoryExpectation{mock: mmDeleteCategory.mock}
	}
	mmDeleteCategory.defaultExpectation.results = &CategoryCacheMockDeleteCategoryResults{err}
	mmDeleteCategory.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDeleteCategory.mock
}

// Set uses given function f to mock the CategoryCache.DeleteCategory method
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Set(f func(ctx context.Context, id int64) (err error)) *CategoryCacheMock {
	if mmDeleteCategory.defaultExpectation != nil {
		mmDeleteCategory.mock.t.Fatalf("Default expectation is already set for the CategoryCache.DeleteCategory method")
	}

	if len(mmDeleteCategory.expectations) > 0 {
		mmDeleteCategory.mock.t.Fatalf("Some expectations are already set for the CategoryCache.DeleteCategory method")
	}

	mmDeleteCategory.mock.funcDeleteCategory = f
	mmDeleteCategory.mock.funcDeleteCategoryOrigin = minimock.CallerInfo(1)
	return mmDeleteCategory.mock
}

// When sets expectation for the CategoryCache.DeleteCategory which will trigger the result defined by the following
// Then helper
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) When(ctx context.Context, id int64) *CategoryCacheMockDeleteCategoryExpectation {
	if mmDeleteCategory.mock.funcDeleteCategory != nil {
		mmDeleteCategory.mock.t.Fatalf("CategoryCacheMock.DeleteCategory mock is already set by Set")
	}

	expectation := &CategoryCacheMockDeleteCategoryExpectation{
		mock:               mmDeleteCategory.mock,
		params:             &CategoryCacheMockDeleteCategoryParams{ctx, id},
		expectationOrigins: CategoryCacheMockDeleteCategoryExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDeleteCategory.expectations = append(mmDeleteCategory.expectations, expectation)
	return expectation
}

// Then sets up CategoryCache.DeleteCategory return parameters for the expectation previously defined by the When method
func (e *CategoryCacheMockDeleteCategoryExpectation) Then(err error) *CategoryCacheMock {
	e.results = &CategoryCacheMockDeleteCategoryResults{err}
	return e.mock
}

// Times sets number of times CategoryCache.DeleteCategory should be invoked
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Times(n uint64) *mCategoryCacheMockDeleteCategory {
	if n == 0 {
		mmDeleteCategory.mock.t.Fatalf("Times of CategoryCacheMock.DeleteCategory mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDeleteCategory.expectedInvocations, n)
	mmDeleteCategory.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDeleteCategory
}

func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) invocationsDone() bool {
	if len(mmDeleteCategory.expectations) == 0 && mmDeleteCategory.defaultExpectation == nil && mmDeleteCategory.mock.funcDeleteCategory == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDeleteCategory.mock.afterDeleteCategoryCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDeleteCategory.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// DeleteCategory implements mm_repository.CategoryCache
func (mmDeleteCategory *CategoryCacheMock) DeleteCategory(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDeleteCategory.beforeDeleteCategoryCounter, 1)
	defer mm_atomic.AddUint64(&mmDeleteCategory.afterDeleteCategoryCounter, 1)

	mmDeleteCategory.t.Helper()

	if mmDeleteCategory.inspectFuncDeleteCategory != nil {
		mmDeleteCategory.inspectFuncDeleteCategory(ctx, id)
	}

	mm_params := CategoryCacheMockDeleteCategoryParams{ctx, id}

	// Record call args
	mmDeleteCategory.DeleteCategoryMock.mutex.Lock()
	mmDeleteCategory.DeleteCategoryMock.callArgs = append(mmDeleteCategory.DeleteCategoryMock.callArgs, &mm_params)
	mmDeleteCategory.DeleteCategoryMock.mutex.Unlock()

	for _, e := range mmDeleteCategory.DeleteCategoryMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDeleteCategory.DeleteCategoryMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDeleteCategory.DeleteCategoryMock.defaultExpectation.Counter, 1)
		mm_want := mmDeleteCategory.DeleteCategoryMock.defaultExpectation.params
		mm_want_ptrs := mmDeleteCategory.DeleteCategoryMock.defaultExpectation.paramPtrs

		mm_got := CategoryCacheMockDeleteCategoryParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDeleteCategory.t.Errorf("CategoryCacheMock.DeleteCategory got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteCategory.DeleteCategoryMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDeleteCategory.t.Errorf("CategoryCacheMock.DeleteCategory got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDeleteCategory.DeleteCategoryMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDeleteCategory.t.Errorf("CategoryCacheMock.DeleteCategory got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDeleteCategory.DeleteCategoryMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDeleteCategory.DeleteCategoryMock.defaultExpectation.results
		if mm_results == nil {
			mmDeleteCategory.t.Fatal("No results are set for the CategoryCacheMock.DeleteCategory")
		}
		return (*mm_results).err
	}
	if mmDeleteCategory.funcDeleteCategory != nil {
		return mmDeleteCategory.funcDeleteCategory(ctx, id)
	}
	mmDeleteCategory.t.Fatalf("Unexpected call to CategoryCacheMock.DeleteCategory. %v %v", ctx, id)
	return
}

// DeleteCategoryAfterCounter returns a count of finished CategoryCacheMock.DeleteCategory invocations
func (mmDeleteCategory *CategoryCacheMock) DeleteCategoryAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteCategory.afterDeleteCategoryCounter)
}

// DeleteCategoryBeforeCounter returns a count of CategoryCacheMock.DeleteCategory invocations
func (mmDeleteCategory *CategoryCacheMock) DeleteCategoryBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDeleteCategory.beforeDeleteCategoryCounter)
}

// Calls returns a list of arguments used in each call to CategoryCacheMock.DeleteCategory.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDeleteCategory *mCategoryCacheMockDeleteCategory) Calls() []*CategoryCacheMockDeleteCategoryParams {
	mmDeleteCategory.mutex.RLock()

	argCopy := make([]*CategoryCacheMockDeleteCategoryParams, len(mmDeleteCategory.callArgs))
	copy(argCopy, mmDeleteCategory.callArgs)

	mmDeleteCategory.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteCategoryDone returns true if the count of the DeleteCategory invocations corresponds
// the number of defined expectations
func (m *CategoryCacheMock) MinimockDeleteCategoryDone() bool {
	if m.DeleteCategoryMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteCategoryMock.invocationsDone()
}

// MinimockDeleteCategoryInspect logs each unmet expectation
func (m *CategoryCacheMock) MinimockDeleteCategoryInspect() {
	for _, e := range m.DeleteCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategory at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCategoryCounter := mm_atomic.LoadUint64(&m.afterDeleteCategoryCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteCategoryMock.defaultExpectation != nil && afterDeleteCategoryCounter < 1 {
		if m.DeleteCategoryMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategory at\n%s", m.DeleteCategoryMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategory at\n%s with params: %#v", m.DeleteCategoryMock.defaultExpectation.expectationOrigins.origin, *m.DeleteCategoryMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDeleteCategory != nil && afterDeleteCategoryCounter < 1 {
		m.t.Errorf("Expected call to CategoryCacheMock.DeleteCategory at\n%s", m.funcDeleteCategoryOrigin)
	}

	if !m.DeleteCategoryMock.invocationsDone() && afterDeleteCategoryCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryCacheMock.DeleteCategory at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteCategoryMock.expectedInvocations), m.DeleteCategoryMock.expectedInvocationsOrigin, afterDeleteCategoryCounter)
	}
}

type mCategoryCacheMockGetCategories struct {
	optional           bool
	mock               *CategoryCacheMock
	defaultExpectation *CategoryCacheMockGetCategoriesExpectation
	expectations       []*CategoryCacheMockGetCategoriesExpectation

	callArgs []*CategoryCacheMockGetCategoriesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryCacheMockGetCategoriesExpectation specifies expectation struct of the CategoryCache.GetCategories
type CategoryCacheMockGetCategoriesExpectation struct {
	mock               *CategoryCacheMock
	params             *CategoryCacheMockGetCategoriesParams
	paramPtrs          *CategoryCacheMockGetCategoriesParamPtrs
	expectationOrigins CategoryCacheMockGetCategoriesExpectationOrigins
	results            *CategoryCacheMockGetCategoriesResults
	returnOrigin       string
	Counter            uint64
}

// CategoryCacheMockGetCategoriesParams contains parameters of the CategoryCache.GetCategories
type CategoryCacheMockGetCategoriesParams struct {
	ctx context.Context
}

// CategoryCacheMockGetCategoriesParamPtrs contains pointers to parameters of the CategoryCache.GetCategories
type CategoryCacheMockGetCategoriesParamPtrs struct {
	ctx *context.Context
}

// CategoryCacheMockGetCategoriesResults contains results of the CategoryCache.GetCategories
type CategoryCacheMockGetCategoriesResults struct {
	ca1 []categoryentity.Category
	err error
}

// CategoryCacheMockGetCategoriesOrigins contains origins of expectations of the CategoryCache.GetCategories
type CategoryCacheMockGetCategoriesExpectationOrigins struct {
	origin    string
	originCtx string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetCategories *mCategoryCacheMockGetCategories) Optional() *mCategoryCacheMockGetCategories {
	mmGetCategories.optional = true
	return mmGetCategories
}

// Expect sets up expected params for CategoryCache.GetCategories
func (mmGetCategories *mCategoryCacheMockGetCategories) Expect(ctx context.Context) *mCategoryCacheMockGetCategories {
	if mmGetCategories.mock.funcGetCategories != nil {
		mmGetCategories.mock.t.Fatalf("CategoryCacheMock.GetCategories mock is already set by Set")
	}

	if mmGetCategories.defaultExpectation == nil {
		mmGetCategories.defaultExpectation = &CategoryCacheMockGetCategoriesExpectation{}
	}

	if mmGetCategories.defaultExpectation.paramPtrs != nil {
		mmGetCategories.mock.t.Fatalf("CategoryCacheMock.GetCategories mock is already set by ExpectParams functions")
	}

	mmGetCategories.defaultExpectation.params = &CategoryCacheMockGetCategoriesParams{ctx}
	mmGetCategories.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetCategories.expectations {
		if minimock.Equal(e.params, mmGetCategories.defaultExpectation.params) {
			mmGetCategories.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetCategories.defaultExpectation.params)
		}
	}

	return mmGetCategories
}

// ExpectCtxParam1 sets up expected param ctx for CategoryCache.GetCategories
func (mmGetCategories *mCategoryCacheMockGetCategories) ExpectCtxParam1(ctx context.Context) *mCategoryCacheMockGetCategories {
	if mmGetCategories.mock.funcGetCategories != nil {
		mmGetCategories.mock.t.Fatalf("CategoryCacheMock.GetCategories mock is already set by Set")
	}

	if mmGetCategories.defaultExpectation == nil {
		mmGetCategories.defaultExpectation = &CategoryCacheMockGetCategoriesExpectation{}
	}

	if mmGetCategories.defaultExpectation.params != nil {
		mmGetCategories.mock.t.Fatalf("CategoryCacheMock.GetCategories mock is already set by Expect")
	}

	if mmGetCategories.defaultExpectation.paramPtrs == nil {
		mmGetCategories.defaultExpectation.paramPtrs = &CategoryCacheMockGetCategoriesParamPtrs{}
	}
	mmGetCategories.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetCategories.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetCategories
}

// Inspect accepts an inspector function that has same arguments as the CategoryCache.GetCategories
func (mmGetCategories *mCategoryCacheMockGetCategories) Inspect(f func(ctx context.Context)) *mCategoryCacheMockGetCategories {
	if mmGetCategories.mock.inspectFuncGetCategories != nil {
		mmGetCategories.mock.t.Fatalf("Inspect function is already set for CategoryCacheMock.GetCategories")
	}

	mmGetCategories.mock.inspectFuncGetCategories = f

	return mmGetCategories
}

// Return sets up results that will be returned by CategoryCache.GetCategories
func (mmGetCategories *mCategoryCacheMockGetCategories) Return(ca1 []categoryentity.Category, err error) *CategoryCacheMock {
	if mmGetCategories.mock.funcGetCategories != nil {
		mmGetCategories.mock.t.Fatalf("CategoryCacheMock.GetCategories mock is already set by Set")
	}

	if mmGetCategories.defaultExpectation == nil {
		mmGetCategories.defaultExpectation = &CategoryCacheMockGetCategoriesExpectation{mock: mmGetCategories.mock}
	}
	mmGetCategories.defaultExpectation.results = &CategoryCacheMockGetCategoriesResults{ca1, err}
	mmGetCategories.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetCategories.mock
}

// Set uses given function f to mock the CategoryCache.GetCategories method
func (mmGetCategories *mCategoryCacheMockGetCategories) Set(f func(ctx context.Context) (ca1 []categoryentity.Category, err error)) *CategoryCacheMock {
	if mmGetCategories.defaultExpectation != nil {
		mmGetCategories.mock.t.Fatalf("Default expectation is already set for the CategoryCache.GetCategories method")
	}

	if len(mmGetCategories.expectations) > 0 {
		mmGetCategories.mock.t.Fatalf("Some expectations are already set for the CategoryCache.GetCategories method")
	}

	mmGetCategories.mock.funcGetCategories = f
	mmGetCategories.mock.funcGetCategoriesOrigin = minimock.CallerInfo(1)
	return mmGetCategories.mock
}

// When sets expectation for the CategoryCache.GetCategories which will trigger the result defined by the following
// Then helper
func (mmGetCategories *mCategoryCacheMockGetCategories) When(ctx context.Context) *CategoryCacheMockGetCategoriesExpectation {
	if mmGetCategories.mock.funcGetCategories != nil {
		mmGetCategories.mock.t.Fatalf("CategoryCacheMock.GetCategories mock is already set by Set")
	}

	expectation := &CategoryCacheMockGetCategoriesExpectation{
		mock:               mmGetCategories.mock,
		params:             &CategoryCacheMockGetCategoriesParams{ctx},
		expectationOrigins: CategoryCacheMockGetCategoriesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetCategories.expectations = append(mmGetCategories.expectations, expectation)
	return expectation
}

// Then sets up CategoryCache.GetCategories return parameters for the expectation previously defined by the When method
func (e *CategoryCacheMockGetCategoriesExpectation) Then(ca1 []categoryentity.Category, err error) *CategoryCacheMock {
	e.results = &CategoryCacheMockGetCategoriesResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryCache.GetCategories should be invoked
func (mmGetCategories *mCategoryCacheMockGetCategories) Times(n uint64) *mCategoryCacheMockGetCategories {
	if n == 0 {
		mmGetCategories.mock.t.Fatalf("Times of CategoryCacheMock.GetCategories mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetCategories.expectedInvocations, n)
	mmGetCategories.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetCategories
}

func (mmGetCategories *mCategoryCacheMockGetCategories) invocationsDone() bool {
	if len(mmGetCategories.expectations) == 0 && mmGetCategories.defaultExpectation == nil && mmGetCategories.mock.funcGetCategories == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetCategories.mock.afterGetCategoriesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetCategories.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetCategories implements mm_repository.CategoryCache
func (mmGetCategories *CategoryCacheMock) GetCategories(ctx context.Context) (ca1 []categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetCategories.beforeGetCategoriesCounter, 1)
	defer mm_atomic.AddUint64(&mmGetCategories.afterGetCategoriesCounter, 1)

	mmGetCategories.t.Helper()

	if mmGetCategories.inspectFuncGetCategories != nil {
		mmGetCategories.inspectFuncGetCategories(ctx)
	}

	mm_params := CategoryCacheMockGetCategoriesParams{ctx}

	// Record call args
	mmGetCategories.GetCategoriesMock.mutex.Lock()
	mmGetCategories.GetCategoriesMock.callArgs = append(mmGetCategories.GetCategoriesMock.callArgs, &mm_params)
	mmGetCategories.GetCategoriesMock.mutex.Unlock()

	for _, e := range mmGetCategories.GetCategoriesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetCategories.GetCategoriesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetCategories.GetCategoriesMock.defaultExpectation.Counter, 1)
		mm_want := mmGetCategories.GetCategoriesMock.defaultExpectation.params
		mm_want_ptrs := mmGetCategories.GetCategoriesMock.defaultExpectation.paramPtrs

		mm_got := CategoryCacheMockGetCategoriesParams{ctx}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetCategories.t.Errorf("CategoryCacheMock.GetCategories got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetCategories.GetCategoriesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetCategories.t.Errorf("CategoryCacheMock.GetCategories got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetCategories.GetCategoriesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetCategories.GetCategoriesMock.defaultExpectation.results
		if mm_results == nil {
			mmGetCategories.t.Fatal("No results are set for the CategoryCacheMock.GetCategories")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetCategories.funcGetCategories != nil {
		return mmGetCategories.funcGetCategories(ctx)
	}
	mmGetCategories.t.Fatalf("Unexpected call to CategoryCacheMock.GetCategories. %v", ctx)
	return
}

// GetCategoriesAfterCounter returns a count of finished CategoryCacheMock.GetCategories invocations
func (mmGetCategories *CategoryCacheMock) GetCategoriesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetCategories.afterGetCategoriesCounter)
}

// GetCategoriesBeforeCounter returns a count of CategoryCacheMock.GetCategories invocations
func (mmGetCategories *CategoryCacheMock) GetCategoriesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetCategories.beforeGetCategoriesCounter)
}

// Calls returns a list of arguments used in each call to CategoryCacheMock.GetCategories.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetCategories *mCategoryCacheMockGetCategories) Calls() []*CategoryCacheMockGetCategoriesParams {
	mmGetCategories.mutex.RLock()

	argCopy := make([]*CategoryCacheMockGetCategoriesParams, len(mmGetCategories.callArgs))
	copy(argCopy, mmGetCategories.callArgs)

	mmGetCategories.mutex.RUnlock()

	return argCopy
}

// MinimockGetCategoriesDone returns true if the count of the GetCategories invocations corresponds
// the number of defined expectations
func (m *CategoryCacheMock) MinimockGetCategoriesDone() bool {
	if m.GetCategoriesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetCategoriesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetCategoriesMock.invocationsDone()
}

// MinimockGetCategoriesInspect logs each unmet expectation
func (m *CategoryCacheMock) MinimockGetCategoriesInspect() {
	for _, e := range m.GetCategoriesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryCacheMock.GetCategories at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetCategoriesCounter := mm_atomic.LoadUint64(&m.afterGetCategoriesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetCategoriesMock.defaultExpectation != nil && afterGetCategoriesCounter < 1 {
		if m.GetCategoriesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryCacheMock.GetCategories at\n%s", m.GetCategoriesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryCacheMock.GetCategories at\n%s with params: %#v", m.GetCategoriesMock.defaultExpectation.expectationOrigins.origin, *m.GetCategoriesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetCategories != nil && afterGetCategoriesCounter < 1 {
		m.t.Errorf("Expected call to CategoryCacheMock.GetCategories at\n%s", m.funcGetCategoriesOrigin)
	}

	if !m.GetCategoriesMock.invocationsDone() && afterGetCategoriesCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryCacheMock.GetCategories at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetCategoriesMock.expectedInvocations), m.GetCategoriesMock.expectedInvocationsOrigin, afterGetCategoriesCounter)
	}
}

type mCategoryCacheMockGetCategory struct {
	optional           bool
	mock               *CategoryCacheMock
	defaultExpectation *CategoryCacheMockGetCategoryExpectation
	expectations       []*CategoryCacheMockGetCategoryExpectation

	callArgs []*CategoryCacheMockGetCategoryParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryCacheMockGetCategoryExpectation specifies expectation struct of the CategoryCache.GetCategory
type CategoryCacheMockGetCategoryExpectation struct {
	mock               *CategoryCacheMock
	params             *CategoryCacheMockGetCategoryParams
	paramPtrs          *CategoryCacheMockGetCategoryParamPtrs
	expectationOrigins CategoryCacheMockGetCategoryExpectationOrigins
	results            *CategoryCacheMockGetCategoryResults
	returnOrigin       string
	Counter            uint64
}

// CategoryCacheMockGetCategoryParams contains parameters of the CategoryCache.GetCategory
type CategoryCacheMockGetCategoryParams struct {
	ctx context.Context
	id  int64
}

// CategoryCacheMockGetCategoryParamPtrs contains pointers to parameters of the CategoryCache.GetCategory
type CategoryCacheMockGetCategoryParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// CategoryCacheMockGetCategoryResults contains results of the CategoryCache.GetCategory
type CategoryCacheMockGetCategoryResults struct {
	c2  categoryentity.Category
	err error
}

// CategoryCacheMockGetCategoryOrigins contains origins of expectations of the CategoryCache.GetCategory
type CategoryCacheMockGetCategoryExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetCategory *mCategoryCacheMockGetCategory) Optional() *mCategoryCacheMockGetCategory {
	mmGetCategory.optional = true
	return mmGetCategory
}

// Expect sets up expected params for CategoryCache.GetCategory
func (mmGetCategory *mCategoryCacheMockGetCategory) Expect(ctx context.Context, id int64) *mCategoryCacheMockGetCategory {
	if mmGetCategory.mock.funcGetCategory != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Set")
	}

	if mmGetCategory.defaultExpectation == nil {
		mmGetCategory.defaultExpectation = &CategoryCacheMockGetCategoryExpectation{}
	}

	if mmGetCategory.defaultExpectation.paramPtrs != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by ExpectParams functions")
	}

	mmGetCategory.defaultExpectation.params = &CategoryCacheMockGetCategoryParams{ctx, id}
	mmGetCategory.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetCategory.expectations {
		if minimock.Equal(e.params, mmGetCategory.defaultExpectation.params) {
			mmGetCategory.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetCategory.defaultExpectation.params)
		}
	}

	return mmGetCategory
}

// ExpectCtxParam1 sets up expected param ctx for CategoryCache.GetCategory
func (mmGetCategory *mCategoryCacheMockGetCategory) ExpectCtxParam1(ctx context.Context) *mCategoryCacheMockGetCategory {
	if mmGetCategory.mock.funcGetCategory != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Set")
	}

	if mmGetCategory.defaultExpectation == nil {
		mmGetCategory.defaultExpectation = &CategoryCacheMockGetCategoryExpectation{}
	}

	if mmGetCategory.defaultExpectation.params != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Expect")
	}

	if mmGetCategory.defaultExpectation.paramPtrs == nil {
		mmGetCategory.defaultExpectation.paramPtrs = &CategoryCacheMockGetCategoryParamPtrs{}
	}
	mmGetCategory.defaultExpectation.paramPtrs.ctx = &ctx
	mmGetCategory.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmGetCategory
}

// ExpectIdParam2 sets up expected param id for CategoryCache.GetCategory
func (mmGetCategory *mCategoryCacheMockGetCategory) ExpectIdParam2(id int64) *mCategoryCacheMockGetCategory {
	if mmGetCategory.mock.funcGetCategory != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Set")
	}

	if mmGetCategory.defaultExpectation == nil {
		mmGetCategory.defaultExpectation = &CategoryCacheMockGetCategoryExpectation{}
	}

	if mmGetCategory.defaultExpectation.params != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Expect")
	}

	if mmGetCategory.defaultExpectation.paramPtrs == nil {
		mmGetCategory.defaultExpectation.paramPtrs = &CategoryCacheMockGetCategoryParamPtrs{}
	}
	mmGetCategory.defaultExpectation.paramPtrs.id = &id
	mmGetCategory.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetCategory
}

// Inspect accepts an inspector function that has same arguments as the CategoryCache.GetCategory
func (mmGetCategory *mCategoryCacheMockGetCategory) Inspect(f func(ctx context.Context, id int64)) *mCategoryCacheMockGetCategory {
	if mmGetCategory.mock.inspectFuncGetCategory != nil {
		mmGetCategory.mock.t.Fatalf("Inspect function is already set for CategoryCacheMock.GetCategory")
	}

	mmGetCategory.mock.inspectFuncGetCategory = f

	return mmGetCategory
}

// Return sets up results that will be returned by CategoryCache.GetCategory
func (mmGetCategory *mCategoryCacheMockGetCategory) Return(c2 categoryentity.Category, err error) *CategoryCacheMock {
	if mmGetCategory.mock.funcGetCategory != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Set")
	}

	if mmGetCategory.defaultExpectation == nil {
		mmGetCategory.defaultExpectation = &CategoryCacheMockGetCategoryExpectation{mock: mmGetCategory.mock}
	}
	mmGetCategory.defaultExpectation.results = &CategoryCacheMockGetCategoryResults{c2, err}
	mmGetCategory.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetCategory.mock
}

// Set uses given function f to mock the CategoryCache.GetCategory method
func (mmGetCategory *mCategoryCacheMockGetCategory) Set(f func(ctx context.Context, id int64) (c2 categoryentity.Category, err error)) *CategoryCacheMock {
	if mmGetCategory.defaultExpectation != nil {
		mmGetCategory.mock.t.Fatalf("Default expectation is already set for the CategoryCache.GetCategory method")
	}

	if len(mmGetCategory.expectations) > 0 {
		mmGetCategory.mock.t.Fatalf("Some expectations are already set for the CategoryCache.GetCategory method")
	}

	mmGetCategory.mock.funcGetCategory = f
	mmGetCategory.mock.funcGetCategoryOrigin = minimock.CallerInfo(1)
	return mmGetCategory.mock
}

// When sets expectation for the CategoryCache.GetCategory which will trigger the result defined by the following
// Then helper
func (mmGetCategory *mCategoryCacheMockGetCategory) When(ctx context.Context, id int64) *CategoryCacheMockGetCategoryExpectation {
	if mmGetCategory.mock.funcGetCategory != nil {
		mmGetCategory.mock.t.Fatalf("CategoryCacheMock.GetCategory mock is already set by Set")
	}

	expectation := &CategoryCacheMockGetCategoryExpectation{
		mock:               mmGetCategory.mock,
		params:             &CategoryCacheMockGetCategoryParams{ctx, id},
		expectationOrigins: CategoryCacheMockGetCategoryExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetCategory.expectations = append(mmGetCategory.expectations, expectation)
	return expectation
}

// Then sets up CategoryCache.GetCategory return parameters for the expectation previously defined by the When method
func (e *CategoryCacheMockGetCategoryExpectation) Then(c2 categoryentity.Category, err error) *CategoryCacheMock {
	e.results = &CategoryCacheMockGetCategoryResults{c2, err}
	return e.mock
}

// Times sets number of times CategoryCache.GetCategory should be invoked
func (mmGetCategory *mCategoryCacheMockGetCategory) Times(n uint64) *mCategoryCacheMockGetCategory {
	if n == 0 {
		mmGetCategory.mock.t.Fatalf("Times of CategoryCacheMock.GetCategory mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetCategory.expectedInvocations, n)
	mmGetCategory.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetCategory
}

func (mmGetCategory *mCategoryCacheMockGetCategory) invocationsDone() bool {
	if len(mmGetCategory.expectations) == 0 && mmGetCategory.defaultExpectation == nil && mmGetCategory.mock.funcGetCategory == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetCategory.mock.afterGetCategoryCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetCategory.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetCategory implements mm_repository.CategoryCache
func (mmGetCategory *CategoryCacheMock) GetCategory(ctx context.Context, id int64) (c2 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetCategory.beforeGetCategoryCounter, 1)
	defer mm_atomic.AddUint64(&mmGetCategory.afterGetCategoryCounter, 1)

	mmGetCategory.t.Helper()

	if mmGetCategory.inspectFuncGetCategory != nil {
		mmGetCategory.inspectFuncGetCategory(ctx, id)
	}

	mm_params := CategoryCacheMockGetCategoryParams{ctx, id}

	// Record call args
	mmGetCategory.GetCategoryMock.mutex.Lock()
	mmGetCategory.GetCategoryMock.callArgs = append(mmGetCategory.GetCategoryMock.callArgs, &mm_params)
	mmGetCategory.GetCategoryMock.mutex.Unlock()

	for _, e := range mmGetCategory.GetCategoryMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c2, e.results.err
		}
	}

	if mmGetCategory.GetCategoryMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetCategory.GetCategoryMock.defaultExpectation.Counter, 1)
		mm_want := mmGetCategory.GetCategoryMock.defaultExpectation.params
		mm_want_ptrs := mmGetCategory.GetCategoryMock.defaultExpectation.paramPtrs

		mm_got := CategoryCacheMockGetCategoryParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmGetCategory.t.Errorf("CategoryCacheMock.GetCategory got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetCategory.GetCategoryMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetCategory.t.Errorf("CategoryCacheMock.GetCategory got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetCategory.GetCategoryMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetCategory.t.Errorf("CategoryCacheMock.GetCategory got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetCategory.GetCategoryMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetCategory.GetCategoryMock.defaultExpectation.results
		if mm_results == nil {
			mmGetCategory.t.Fatal("No results are set for the CategoryCacheMock.GetCategory")
		}
		return (*mm_results).c2, (*mm_results).err
	}
	if mmGetCategory.funcGetCategory != nil {
		return mmGetCategory.funcGetCategory(ctx, id)
	}
	mmGetCategory.t.Fatalf("Unexpected call to CategoryCacheMock.GetCategory. %v %v", ctx, id)
	return
}

// GetCategoryAfterCounter returns a count of finished CategoryCacheMock.GetCategory invocations
func (mmGetCategory *CategoryCacheMock) GetCategoryAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetCategory.afterGetCategoryCounter)
}

// GetCategoryBeforeCounter returns a count of CategoryCacheMock.GetCategory invocations
func (mmGetCategory *CategoryCacheMock) GetCategoryBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetCategory.beforeGetCategoryCounter)
}

// Calls returns a list of arguments used in each call to CategoryCacheMock.GetCategory.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetCategory *mCategoryCacheMockGetCategory) Calls() []*CategoryCacheMockGetCategoryParams {
	mmGetCategory.mutex.RLock()

	argCopy := make([]*CategoryCacheMockGetCategoryParams, len(mmGetCategory.callArgs))
	copy(argCopy, mmGetCategory.callArgs)

	mmGetCategory.mutex.RUnlock()

	return argCopy
}

// MinimockGetCategoryDone returns true if the count of the GetCategory invocations corresponds
// the number of defined expectations
func (m *CategoryCacheMock) MinimockGetCategoryDone() bool {
	if m.GetCategoryMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetCategoryMock.invocationsDone()
}

// MinimockGetCategoryInspect logs each unmet expectation
func (m *CategoryCacheMock) MinimockGetCategoryInspect() {
	for _, e := range m.GetCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryCacheMock.GetCategory at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetCategoryCounter := mm_atomic.LoadUint64(&m.afterGetCategoryCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetCategoryMock.defaultExpectation != nil && afterGetCategoryCounter < 1 {
		if m.GetCategoryMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryCacheMock.GetCategory at\n%s", m.GetCategoryMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryCacheMock.GetCategory at\n%s with params: %#v", m.GetCategoryMock.defaultExpectation.expectationOrigins.origin, *m.GetCategoryMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetCategory != nil && afterGetCategoryCounter < 1 {
		m.t.Errorf("Expected call to CategoryCacheMock.GetCategory at\n%s", m.funcGetCategoryOrigin)
	}

	if !m.GetCategoryMock.invocationsDone() && afterGetCategoryCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryCacheMock.GetCategory at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetCategoryMock.expectedInvocations), m.GetCategoryMock.expectedInvocationsOrigin, afterGetCategoryCounter)
	}
}

type mCategoryCacheMockSetCategories struct {
	optional           bool
	mock               *CategoryCacheMock
	defaultExpectation *CategoryCacheMockSetCategoriesExpectation
	expectations       []*CategoryCacheMockSetCategoriesExpectation

	callArgs []*CategoryCacheMockSetCategoriesParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryCacheMockSetCategoriesExpectation specifies expectation struct of the CategoryCache.SetCategories
type CategoryCacheMockSetCategoriesExpectation struct {
	mock               *CategoryCacheMock
	params             *CategoryCacheMockSetCategoriesParams
	paramPtrs          *CategoryCacheMockSetCategoriesParamPtrs
	expectationOrigins CategoryCacheMockSetCategoriesExpectationOrigins
	results            *CategoryCacheMockSetCategoriesResults
	returnOrigin       string
	Counter            uint64
}

// CategoryCacheMockSetCategoriesParams contains parameters of the CategoryCache.SetCategories
type CategoryCacheMockSetCategoriesParams struct {
	ctx        context.Context
	categories []categoryentity.Category
}

// CategoryCacheMockSetCategoriesParamPtrs contains pointers to parameters of the CategoryCache.SetCategories
type CategoryCacheMockSetCategoriesParamPtrs struct {
	ctx        *context.Context
	categories *[]categoryentity.Category
}

// CategoryCacheMockSetCategoriesResults contains results of the CategoryCache.SetCategories
type CategoryCacheMockSetCategoriesResults struct {
	err error
}

// CategoryCacheMockSetCategoriesOrigins contains origins of expectations of the CategoryCache.SetCategories
type CategoryCacheMockSetCategoriesExpectationOrigins struct {
	origin           string
	originCtx        string
	originCategories string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetCategories *mCategoryCacheMockSetCategories) Optional() *mCategoryCacheMockSetCategories {
	mmSetCategories.optional = true
	return mmSetCategories
}

// Expect sets up expected params for CategoryCache.SetCategories
func (mmSetCategories *mCategoryCacheMockSetCategories) Expect(ctx context.Context, categories []categoryentity.Category) *mCategoryCacheMockSetCategories {
	if mmSetCategories.mock.funcSetCategories != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Set")
	}

	if mmSetCategories.defaultExpectation == nil {
		mmSetCategories.defaultExpectation = &CategoryCacheMockSetCategoriesExpectation{}
	}

	if mmSetCategories.defaultExpectation.paramPtrs != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by ExpectParams functions")
	}

	mmSetCategories.defaultExpectation.params = &CategoryCacheMockSetCategoriesParams{ctx, categories}
	mmSetCategories.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetCategories.expectations {
		if minimock.Equal(e.params, mmSetCategories.defaultExpectation.params) {
			mmSetCategories.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetCategories.defaultExpectation.params)
		}
	}

	return mmSetCategories
}

// ExpectCtxParam1 sets up expected param ctx for CategoryCache.SetCategories
func (mmSetCategories *mCategoryCacheMockSetCategories) ExpectCtxParam1(ctx context.Context) *mCategoryCacheMockSetCategories {
	if mmSetCategories.mock.funcSetCategories != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Set")
	}

	if mmSetCategories.defaultExpectation == nil {
		mmSetCategories.defaultExpectation = &CategoryCacheMockSetCategoriesExpectation{}
	}

	if mmSetCategories.defaultExpectation.params != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Expect")
	}

	if mmSetCategories.defaultExpectation.paramPtrs == nil {
		mmSetCategories.defaultExpectation.paramPtrs = &CategoryCacheMockSetCategoriesParamPtrs{}
	}
	mmSetCategories.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetCategories.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetCategories
}

// ExpectCategoriesParam2 sets up expected param categories for CategoryCache.SetCategories
func (mmSetCategories *mCategoryCacheMockSetCategories) ExpectCategoriesParam2(categories []categoryentity.Category) *mCategoryCacheMockSetCategories {
	if mmSetCategories.mock.funcSetCategories != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Set")
	}

	if mmSetCategories.defaultExpectation == nil {
		mmSetCategories.defaultExpectation = &CategoryCacheMockSetCategoriesExpectation{}
	}

	if mmSetCategories.defaultExpectation.params != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Expect")
	}

	if mmSetCategories.defaultExpectation.paramPtrs == nil {
		mmSetCategories.defaultExpectation.paramPtrs = &CategoryCacheMockSetCategoriesParamPtrs{}
	}
	mmSetCategories.defaultExpectation.paramPtrs.categories = &categories
	mmSetCategories.defaultExpectation.expectationOrigins.originCategories = minimock.CallerInfo(1)

	return mmSetCategories
}

// Inspect accepts an inspector function that has same arguments as the CategoryCache.SetCategories
func (mmSetCategories *mCategoryCacheMockSetCategories) Inspect(f func(ctx context.Context, categories []categoryentity.Category)) *mCategoryCacheMockSetCategories {
	if mmSetCategories.mock.inspectFuncSetCategories != nil {
		mmSetCategories.mock.t.Fatalf("Inspect function is already set for CategoryCacheMock.SetCategories")
	}

	mmSetCategories.mock.inspectFuncSetCategories = f

	return mmSetCategories
}

// Return sets up results that will be returned by CategoryCache.SetCategories
func (mmSetCategories *mCategoryCacheMockSetCategories) Return(err error) *CategoryCacheMock {
	if mmSetCategories.mock.funcSetCategories != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Set")
	}

	if mmSetCategories.defaultExpectation == nil {
		mmSetCategories.defaultExpectation = &CategoryCacheMockSetCategoriesExpectation{mock: mmSetCategories.mock}
	}
	mmSetCategories.defaultExpectation.results = &CategoryCacheMockSetCategoriesResults{err}
	mmSetCategories.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetCategories.mock
}

// Set uses given function f to mock the CategoryCache.SetCategories method
func (mmSetCategories *mCategoryCacheMockSetCategories) Set(f func(ctx context.Context, categories []categoryentity.Category) (err error)) *CategoryCacheMock {
	if mmSetCategories.defaultExpectation != nil {
		mmSetCategories.mock.t.Fatalf("Default expectation is already set for the CategoryCache.SetCategories method")
	}

	if len(mmSetCategories.expectations) > 0 {
		mmSetCategories.mock.t.Fatalf("Some expectations are already set for the CategoryCache.SetCategories method")
	}

	mmSetCategories.mock.funcSetCategories = f
	mmSetCategories.mock.funcSetCategoriesOrigin = minimock.CallerInfo(1)
	return mmSetCategories.mock
}

// When sets expectation for the CategoryCache.SetCategories which will trigger the result defined by the following
// Then helper
func (mmSetCategories *mCategoryCacheMockSetCategories) When(ctx context.Context, categories []categoryentity.Category) *CategoryCacheMockSetCategoriesExpectation {
	if mmSetCategories.mock.funcSetCategories != nil {
		mmSetCategories.mock.t.Fatalf("CategoryCacheMock.SetCategories mock is already set by Set")
	}

	expectation := &CategoryCacheMockSetCategoriesExpectation{
		mock:               mmSetCategories.mock,
		params:             &CategoryCacheMockSetCategoriesParams{ctx, categories},
		expectationOrigins: CategoryCacheMockSetCategoriesExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetCategories.expectations = append(mmSetCategories.expectations, expectation)
	return expectation
}

// Then sets up CategoryCache.SetCategories return parameters for the expectation previously defined by the When method
func (e *CategoryCacheMockSetCategoriesExpectation) Then(err error) *CategoryCacheMock {
	e.results = &CategoryCacheMockSetCategoriesResults{err}
	return e.mock
}

// Times sets number of times CategoryCache.SetCategories should be invoked
func (mmSetCategories *mCategoryCacheMockSetCategories) Times(n uint64) *mCategoryCacheMockSetCategories {
	if n == 0 {
		mmSetCategories.mock.t.Fatalf("Times of CategoryCacheMock.SetCategories mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetCategories.expectedInvocations, n)
	mmSetCategories.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetCategories
}

func (mmSetCategories *mCategoryCacheMockSetCategories) invocationsDone() bool {
	if len(mmSetCategories.expectations) == 0 && mmSetCategories.defaultExpectation == nil && mmSetCategories.mock.funcSetCategories == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetCategories.mock.afterSetCategoriesCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetCategories.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetCategories implements mm_repository.CategoryCache
func (mmSetCategories *CategoryCacheMock) SetCategories(ctx context.Context, categories []categoryentity.Category) (err error) {
	mm_atomic.AddUint64(&mmSetCategories.beforeSetCategoriesCounter, 1)
	defer mm_atomic.AddUint64(&mmSetCategories.afterSetCategoriesCounter, 1)

	mmSetCategories.t.Helper()

	if mmSetCategories.inspectFuncSetCategories != nil {
		mmSetCategories.inspectFuncSetCategories(ctx, categories)
	}

	mm_params := CategoryCacheMockSetCategoriesParams{ctx, categories}

	// Record call args
	mmSetCategories.SetCategoriesMock.mutex.Lock()
	mmSetCategories.SetCategoriesMock.callArgs = append(mmSetCategories.SetCategoriesMock.callArgs, &mm_params)
	mmSetCategories.SetCategoriesMock.mutex.Unlock()

	for _, e := range mmSetCategories.SetCategoriesMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetCategories.SetCategoriesMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetCategories.SetCategoriesMock.defaultExpectation.Counter, 1)
		mm_want := mmSetCategories.SetCategoriesMock.defaultExpectation.params
		mm_want_ptrs := mmSetCategories.SetCategoriesMock.defaultExpectation.paramPtrs

		mm_got := CategoryCacheMockSetCategoriesParams{ctx, categories}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetCategories.t.Errorf("CategoryCacheMock.SetCategories got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetCategories.SetCategoriesMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.categories != nil && !minimock.Equal(*mm_want_ptrs.categories, mm_got.categories) {
				mmSetCategories.t.Errorf("CategoryCacheMock.SetCategories got unexpected parameter categories, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetCategories.SetCategoriesMock.defaultExpectation.expectationOrigins.originCategories, *mm_want_ptrs.categories, mm_got.categories, minimock.Diff(*mm_want_ptrs.categories, mm_got.categories))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetCategories.t.Errorf("CategoryCacheMock.SetCategories got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetCategories.SetCategoriesMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetCategories.SetCategoriesMock.defaultExpectation.results
		if mm_results == nil {
			mmSetCategories.t.Fatal("No results are set for the CategoryCacheMock.SetCategories")
		}
		return (*mm_results).err
	}
	if mmSetCategories.funcSetCategories != nil {
		return mmSetCategories.funcSetCategories(ctx, categories)
	}
	mmSetCategories.t.Fatalf("Unexpected call to CategoryCacheMock.SetCategories. %v %v", ctx, categories)
	return
}

// SetCategoriesAfterCounter returns a count of finished CategoryCacheMock.SetCategories invocations
func (mmSetCategories *CategoryCacheMock) SetCategoriesAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetCategories.afterSetCategoriesCounter)
}

// SetCategoriesBeforeCounter returns a count of CategoryCacheMock.SetCategories invocations
func (mmSetCategories *CategoryCacheMock) SetCategoriesBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetCategories.beforeSetCategoriesCounter)
}

// Calls returns a list of arguments used in each call to CategoryCacheMock.SetCategories.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetCategories *mCategoryCacheMockSetCategories) Calls() []*CategoryCacheMockSetCategoriesParams {
	mmSetCategories.mutex.RLock()

	argCopy := make([]*CategoryCacheMockSetCategoriesParams, len(mmSetCategories.callArgs))
	copy(argCopy, mmSetCategories.callArgs)

	mmSetCategories.mutex.RUnlock()

	return argCopy
}

// MinimockSetCategoriesDone returns true if the count of the SetCategories invocations corresponds
// the number of defined expectations
func (m *CategoryCacheMock) MinimockSetCategoriesDone() bool {
	if m.SetCategoriesMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetCategoriesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetCategoriesMock.invocationsDone()
}

// MinimockSetCategoriesInspect logs each unmet expectation
func (m *CategoryCacheMock) MinimockSetCategoriesInspect() {
	for _, e := range m.SetCategoriesMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryCacheMock.SetCategories at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCategoriesCounter := mm_atomic.LoadUint64(&m.afterSetCategoriesCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetCategoriesMock.defaultExpectation != nil && afterSetCategoriesCounter < 1 {
		if m.SetCategoriesMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryCacheMock.SetCategories at\n%s", m.SetCategoriesMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryCacheMock.SetCategories at\n%s with params: %#v", m.SetCategoriesMock.defaultExpectation.expectationOrigins.origin, *m.SetCategoriesMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetCategories != nil && afterSetCategoriesCounter < 1 {
		m.t.Errorf("Expected call to CategoryCacheMock.SetCategories at\n%s", m.funcSetCategoriesOrigin)
	}

	if !m.SetCategoriesMock.invocationsDone() && afterSetCategoriesCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryCacheMock.SetCategories at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetCategoriesMock.expectedInvocations), m.SetCategoriesMock.expectedInvocationsOrigin, afterSetCategoriesCounter)
	}
}

type mCategoryCacheMockSetCategory struct {
	optional           bool
	mock               *CategoryCacheMock
	defaultExpectation *CategoryCacheMockSetCategoryExpectation
	expectations       []*CategoryCacheMockSetCategoryExpectation

	callArgs []*CategoryCacheMockSetCategoryParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryCacheMockSetCategoryExpectation specifies expectation struct of the CategoryCache.SetCategory
type CategoryCacheMockSetCategoryExpectation struct {
	mock               *CategoryCacheMock
	params             *CategoryCacheMockSetCategoryParams
	paramPtrs          *CategoryCacheMockSetCategoryParamPtrs
	expectationOrigins CategoryCacheMockSetCategoryExpectationOrigins
	results            *CategoryCacheMockSetCategoryResults
	returnOrigin       string
	Counter            uint64
}

// CategoryCacheMockSetCategoryParams contains parameters of the CategoryCache.SetCategory
type CategoryCacheMockSetCategoryParams struct {
	ctx      context.Context
	category categoryentity.Category
}

// CategoryCacheMockSetCategoryParamPtrs contains pointers to parameters of the CategoryCache.SetCategory
type CategoryCacheMockSetCategoryParamPtrs struct {
	ctx      *context.Context
	category *categoryentity.Category
}

// CategoryCacheMockSetCategoryResults contains results of the CategoryCache.SetCategory
type CategoryCacheMockSetCategoryResults struct {
	err error
}

// CategoryCacheMockSetCategoryOrigins contains origins of expectations of the CategoryCache.SetCategory
type CategoryCacheMockSetCategoryExpectationOrigins struct {
	origin         string
	originCtx      string
	originCategory string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSetCategory *mCategoryCacheMockSetCategory) Optional() *mCategoryCacheMockSetCategory {
	mmSetCategory.optional = true
	return mmSetCategory
}

// Expect sets up expected params for CategoryCache.SetCategory
func (mmSetCategory *mCategoryCacheMockSetCategory) Expect(ctx context.Context, category categoryentity.Category) *mCategoryCacheMockSetCategory {
	if mmSetCategory.mock.funcSetCategory != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Set")
	}

	if mmSetCategory.defaultExpectation == nil {
		mmSetCategory.defaultExpectation = &CategoryCacheMockSetCategoryExpectation{}
	}

	if mmSetCategory.defaultExpectation.paramPtrs != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by ExpectParams functions")
	}

	mmSetCategory.defaultExpectation.params = &CategoryCacheMockSetCategoryParams{ctx, category}
	mmSetCategory.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSetCategory.expectations {
		if minimock.Equal(e.params, mmSetCategory.defaultExpectation.params) {
			mmSetCategory.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSetCategory.defaultExpectation.params)
		}
	}

	return mmSetCategory
}

// ExpectCtxParam1 sets up expected param ctx for CategoryCache.SetCategory
func (mmSetCategory *mCategoryCacheMockSetCategory) ExpectCtxParam1(ctx context.Context) *mCategoryCacheMockSetCategory {
	if mmSetCategory.mock.funcSetCategory != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Set")
	}

	if mmSetCategory.defaultExpectation == nil {
		mmSetCategory.defaultExpectation = &CategoryCacheMockSetCategoryExpectation{}
	}

	if mmSetCategory.defaultExpectation.params != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Expect")
	}

	if mmSetCategory.defaultExpectation.paramPtrs == nil {
		mmSetCategory.defaultExpectation.paramPtrs = &CategoryCacheMockSetCategoryParamPtrs{}
	}
	mmSetCategory.defaultExpectation.paramPtrs.ctx = &ctx
	mmSetCategory.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmSetCategory
}

// ExpectCategoryParam2 sets up expected param category for CategoryCache.SetCategory
func (mmSetCategory *mCategoryCacheMockSetCategory) ExpectCategoryParam2(category categoryentity.Category) *mCategoryCacheMockSetCategory {
	if mmSetCategory.mock.funcSetCategory != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Set")
	}

	if mmSetCategory.defaultExpectation == nil {
		mmSetCategory.defaultExpectation = &CategoryCacheMockSetCategoryExpectation{}
	}

	if mmSetCategory.defaultExpectation.params != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Expect")
	}

	if mmSetCategory.defaultExpectation.paramPtrs == nil {
		mmSetCategory.defaultExpectation.paramPtrs = &CategoryCacheMockSetCategoryParamPtrs{}
	}
	mmSetCategory.defaultExpectation.paramPtrs.category = &category
	mmSetCategory.defaultExpectation.expectationOrigins.originCategory = minimock.CallerInfo(1)

	return mmSetCategory
}

// Inspect accepts an inspector function that has same arguments as the CategoryCache.SetCategory
func (mmSetCategory *mCategoryCacheMockSetCategory) Inspect(f func(ctx context.Context, category categoryentity.Category)) *mCategoryCacheMockSetCategory {
	if mmSetCategory.mock.inspectFuncSetCategory != nil {
		mmSetCategory.mock.t.Fatalf("Inspect function is already set for CategoryCacheMock.SetCategory")
	}

	mmSetCategory.mock.inspectFuncSetCategory = f

	return mmSetCategory
}

// Return sets up results that will be returned by CategoryCache.SetCategory
func (mmSetCategory *mCategoryCacheMockSetCategory) Return(err error) *CategoryCacheMock {
	if mmSetCategory.mock.funcSetCategory != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Set")
	}

	if mmSetCategory.defaultExpectation == nil {
		mmSetCategory.defaultExpectation = &CategoryCacheMockSetCategoryExpectation{mock: mmSetCategory.mock}
	}
	mmSetCategory.defaultExpectation.results = &CategoryCacheMockSetCategoryResults{err}
	mmSetCategory.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSetCategory.mock
}

// Set uses given function f to mock the CategoryCache.SetCategory method
func (mmSetCategory *mCategoryCacheMockSetCategory) Set(f func(ctx context.Context, category categoryentity.Category) (err error)) *CategoryCacheMock {
	if mmSetCategory.defaultExpectation != nil {
		mmSetCategory.mock.t.Fatalf("Default expectation is already set for the CategoryCache.SetCategory method")
	}

	if len(mmSetCategory.expectations) > 0 {
		mmSetCategory.mock.t.Fatalf("Some expectations are already set for the CategoryCache.SetCategory method")
	}

	mmSetCategory.mock.funcSetCategory = f
	mmSetCategory.mock.funcSetCategoryOrigin = minimock.CallerInfo(1)
	return mmSetCategory.mock
}

// When sets expectation for the CategoryCache.SetCategory which will trigger the result defined by the following
// Then helper
func (mmSetCategory *mCategoryCacheMockSetCategory) When(ctx context.Context, category categoryentity.Category) *CategoryCacheMockSetCategoryExpectation {
	if mmSetCategory.mock.funcSetCategory != nil {
		mmSetCategory.mock.t.Fatalf("CategoryCacheMock.SetCategory mock is already set by Set")
	}

	expectation := &CategoryCacheMockSetCategoryExpectation{
		mock:               mmSetCategory.mock,
		params:             &CategoryCacheMockSetCategoryParams{ctx, category},
		expectationOrigins: CategoryCacheMockSetCategoryExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSetCategory.expectations = append(mmSetCategory.expectations, expectation)
	return expectation
}

// Then sets up CategoryCache.SetCategory return parameters for the expectation previously defined by the When method
func (e *CategoryCacheMockSetCategoryExpectation) Then(err error) *CategoryCacheMock {
	e.results = &CategoryCacheMockSetCategoryResults{err}
	return e.mock
}

// Times sets number of times CategoryCache.SetCategory should be invoked
func (mmSetCategory *mCategoryCacheMockSetCategory) Times(n uint64) *mCategoryCacheMockSetCategory {
	if n == 0 {
		mmSetCategory.mock.t.Fatalf("Times of CategoryCacheMock.SetCategory mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSetCategory.expectedInvocations, n)
	mmSetCategory.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSetCategory
}

func (mmSetCategory *mCategoryCacheMockSetCategory) invocationsDone() bool {
	if len(mmSetCategory.expectations) == 0 && mmSetCategory.defaultExpectation == nil && mmSetCategory.mock.funcSetCategory == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSetCategory.mock.afterSetCategoryCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSetCategory.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// SetCategory implements mm_repository.CategoryCache
func (mmSetCategory *CategoryCacheMock) SetCategory(ctx context.Context, category categoryentity.Category) (err error) {
	mm_atomic.AddUint64(&mmSetCategory.beforeSetCategoryCounter, 1)
	defer mm_atomic.AddUint64(&mmSetCategory.afterSetCategoryCounter, 1)

	mmSetCategory.t.Helper()

	if mmSetCategory.inspectFuncSetCategory != nil {
		mmSetCategory.inspectFuncSetCategory(ctx, category)
	}

	mm_params := CategoryCacheMockSetCategoryParams{ctx, category}

	// Record call args
	mmSetCategory.SetCategoryMock.mutex.Lock()
	mmSetCategory.SetCategoryMock.callArgs = append(mmSetCategory.SetCategoryMock.callArgs, &mm_params)
	mmSetCategory.SetCategoryMock.mutex.Unlock()

	for _, e := range mmSetCategory.SetCategoryMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSetCategory.SetCategoryMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSetCategory.SetCategoryMock.defaultExpectation.Counter, 1)
		mm_want := mmSetCategory.SetCategoryMock.defaultExpectation.params
		mm_want_ptrs := mmSetCategory.SetCategoryMock.defaultExpectation.paramPtrs

		mm_got := CategoryCacheMockSetCategoryParams{ctx, category}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmSetCategory.t.Errorf("CategoryCacheMock.SetCategory got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetCategory.SetCategoryMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.category != nil && !minimock.Equal(*mm_want_ptrs.category, mm_got.category) {
				mmSetCategory.t.Errorf("CategoryCacheMock.SetCategory got unexpected parameter category, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSetCategory.SetCategoryMock.defaultExpectation.expectationOrigins.originCategory, *mm_want_ptrs.category, mm_got.category, minimock.Diff(*mm_want_ptrs.category, mm_got.category))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSetCategory.t.Errorf("CategoryCacheMock.SetCategory got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSetCategory.SetCategoryMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSetCategory.SetCategoryMock.defaultExpectation.results
		if mm_results == nil {
			mmSetCategory.t.Fatal("No results are set for the CategoryCacheMock.SetCategory")
		}
		return (*mm_results).err
	}
	if mmSetCategory.funcSetCategory != nil {
		return mmSetCategory.funcSetCategory(ctx, category)
	}
	mmSetCategory.t.Fatalf("Unexpected call to CategoryCacheMock.SetCategory. %v %v", ctx, category)
	return
}

// SetCategoryAfterCounter returns a count of finished CategoryCacheMock.SetCategory invocations
func (mmSetCategory *CategoryCacheMock) SetCategoryAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetCategory.afterSetCategoryCounter)
}

// SetCategoryBeforeCounter returns a count of CategoryCacheMock.SetCategory invocations
func (mmSetCategory *CategoryCacheMock) SetCategoryBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSetCategory.beforeSetCategoryCounter)
}

// Calls returns a list of arguments used in each call to CategoryCacheMock.SetCategory.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSetCategory *mCategoryCacheMockSetCategory) Calls() []*CategoryCacheMockSetCategoryParams {
	mmSetCategory.mutex.RLock()

	argCopy := make([]*CategoryCacheMockSetCategoryParams, len(mmSetCategory.callArgs))
	copy(argCopy, mmSetCategory.callArgs)

	mmSetCategory.mutex.RUnlock()

	return argCopy
}

// MinimockSetCategoryDone returns true if the count of the SetCategory invocations corresponds
// the number of defined expectations
func (m *CategoryCacheMock) MinimockSetCategoryDone() bool {
	if m.SetCategoryMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetCategoryMock.invocationsDone()
}

// MinimockSetCategoryInspect logs each unmet expectation
func (m *CategoryCacheMock) MinimockSetCategoryInspect() {
	for _, e := range m.SetCategoryMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryCacheMock.SetCategory at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCategoryCounter := mm_atomic.LoadUint64(&m.afterSetCategoryCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetCategoryMock.defaultExpectation != nil && afterSetCategoryCounter < 1 {
		if m.SetCategoryMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryCacheMock.SetCategory at\n%s", m.SetCategoryMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryCacheMock.SetCategory at\n%s with params: %#v", m.SetCategoryMock.defaultExpectation.expectationOrigins.origin, *m.SetCategoryMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSetCategory != nil && afterSetCategoryCounter < 1 {
		m.t.Errorf("Expected call to CategoryCacheMock.SetCategory at\n%s", m.funcSetCategoryOrigin)
	}

	if !m.SetCategoryMock.invocationsDone() && afterSetCategoryCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryCacheMock.SetCategory at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetCategoryMock.expectedInvocations), m.SetCategoryMock.expectedInvocationsOrigin, afterSetCategoryCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockDeleteCategoriesInspect()

			m.MinimockDeleteCategoryInspect()

			m.MinimockGetCategoriesInspect()

			m.MinimockGetCategoryInspect()

			m.MinimockSetCategoriesInspect()

			m.MinimockSetCategoryInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockDeleteCategoriesDone() &&
		m.MinimockDeleteCategoryDone() &&
		m.MinimockGetCategoriesDone() &&
		m.MinimockGetCategoryDone() &&
		m.MinimockSetCategoriesDone() &&
		m.MinimockSetCategoryDone()
}
