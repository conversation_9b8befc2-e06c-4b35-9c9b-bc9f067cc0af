// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryGroupCache -o category_group_cache_mock.go -n CategoryGroupCacheMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryGroupCacheMock implements mm_repository.CategoryGroupCache
type CategoryGroupCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetAll          func() (ca1 []categoryentity.CategoryGroupLink, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryGroupCacheMockGetAll

	funcGetByCategoryID          func(categoryID int64) (ca1 []categoryentity.CategoryGroupLink, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mCategoryGroupCacheMockGetByCategoryID

	funcGetByID          func(id int64) (c1 categoryentity.CategoryGroupLink, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryGroupCacheMockGetByID

	funcSet          func(categoryGroup categoryentity.CategoryGroupLink) (err error)
	funcSetOrigin    string
	inspectFuncSet   func(categoryGroup categoryentity.CategoryGroupLink)
	afterSetCounter  uint64
	beforeSetCounter uint64
	SetMock          mCategoryGroupCacheMockSet
}

// NewCategoryGroupCacheMock returns a mock for mm_repository.CategoryGroupCache
func NewCategoryGroupCacheMock(t minimock.Tester) *CategoryGroupCacheMock {
	m := &CategoryGroupCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetAllMock = mCategoryGroupCacheMockGetAll{mock: m}

	m.GetByCategoryIDMock = mCategoryGroupCacheMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*CategoryGroupCacheMockGetByCategoryIDParams{}

	m.GetByIDMock = mCategoryGroupCacheMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryGroupCacheMockGetByIDParams{}

	m.SetMock = mCategoryGroupCacheMockSet{mock: m}
	m.SetMock.callArgs = []*CategoryGroupCacheMockSetParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryGroupCacheMockGetAll struct {
	optional           bool
	mock               *CategoryGroupCacheMock
	defaultExpectation *CategoryGroupCacheMockGetAllExpectation
	expectations       []*CategoryGroupCacheMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupCacheMockGetAllExpectation specifies expectation struct of the CategoryGroupCache.GetAll
type CategoryGroupCacheMockGetAllExpectation struct {
	mock *CategoryGroupCacheMock

	results      *CategoryGroupCacheMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryGroupCacheMockGetAllResults contains results of the CategoryGroupCache.GetAll
type CategoryGroupCacheMockGetAllResults struct {
	ca1 []categoryentity.CategoryGroupLink
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryGroupCacheMockGetAll) Optional() *mCategoryGroupCacheMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryGroupCache.GetAll
func (mmGetAll *mCategoryGroupCacheMockGetAll) Expect() *mCategoryGroupCacheMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryGroupCacheMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryGroupCacheMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupCache.GetAll
func (mmGetAll *mCategoryGroupCacheMockGetAll) Inspect(f func()) *mCategoryGroupCacheMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryGroupCacheMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryGroupCache.GetAll
func (mmGetAll *mCategoryGroupCacheMockGetAll) Return(ca1 []categoryentity.CategoryGroupLink, err error) *CategoryGroupCacheMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryGroupCacheMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryGroupCacheMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryGroupCacheMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryGroupCache.GetAll method
func (mmGetAll *mCategoryGroupCacheMockGetAll) Set(f func() (ca1 []categoryentity.CategoryGroupLink, err error)) *CategoryGroupCacheMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryGroupCache.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryGroupCache.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryGroupCache.GetAll should be invoked
func (mmGetAll *mCategoryGroupCacheMockGetAll) Times(n uint64) *mCategoryGroupCacheMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryGroupCacheMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryGroupCacheMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryGroupCache
func (mmGetAll *CategoryGroupCacheMock) GetAll() (ca1 []categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryGroupCacheMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryGroupCacheMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryGroupCacheMock.GetAll invocations
func (mmGetAll *CategoryGroupCacheMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryGroupCacheMock.GetAll invocations
func (mmGetAll *CategoryGroupCacheMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryGroupCacheMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryGroupCacheMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryGroupCacheMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupCacheMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupCacheMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupCacheMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryGroupCacheMockGetByCategoryID struct {
	optional           bool
	mock               *CategoryGroupCacheMock
	defaultExpectation *CategoryGroupCacheMockGetByCategoryIDExpectation
	expectations       []*CategoryGroupCacheMockGetByCategoryIDExpectation

	callArgs []*CategoryGroupCacheMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupCacheMockGetByCategoryIDExpectation specifies expectation struct of the CategoryGroupCache.GetByCategoryID
type CategoryGroupCacheMockGetByCategoryIDExpectation struct {
	mock               *CategoryGroupCacheMock
	params             *CategoryGroupCacheMockGetByCategoryIDParams
	paramPtrs          *CategoryGroupCacheMockGetByCategoryIDParamPtrs
	expectationOrigins CategoryGroupCacheMockGetByCategoryIDExpectationOrigins
	results            *CategoryGroupCacheMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupCacheMockGetByCategoryIDParams contains parameters of the CategoryGroupCache.GetByCategoryID
type CategoryGroupCacheMockGetByCategoryIDParams struct {
	categoryID int64
}

// CategoryGroupCacheMockGetByCategoryIDParamPtrs contains pointers to parameters of the CategoryGroupCache.GetByCategoryID
type CategoryGroupCacheMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// CategoryGroupCacheMockGetByCategoryIDResults contains results of the CategoryGroupCache.GetByCategoryID
type CategoryGroupCacheMockGetByCategoryIDResults struct {
	ca1 []categoryentity.CategoryGroupLink
	err error
}

// CategoryGroupCacheMockGetByCategoryIDOrigins contains origins of expectations of the CategoryGroupCache.GetByCategoryID
type CategoryGroupCacheMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Optional() *mCategoryGroupCacheMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for CategoryGroupCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Expect(categoryID int64) *mCategoryGroupCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryGroupCacheMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupCacheMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &CategoryGroupCacheMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryGroupCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mCategoryGroupCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryGroupCacheMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupCacheMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &CategoryGroupCacheMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Inspect(f func(categoryID int64)) *mCategoryGroupCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for CategoryGroupCacheMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by CategoryGroupCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Return(ca1 []categoryentity.CategoryGroupLink, err error) *CategoryGroupCacheMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryGroupCacheMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &CategoryGroupCacheMockGetByCategoryIDResults{ca1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the CategoryGroupCache.GetByCategoryID method
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Set(f func(categoryID int64) (ca1 []categoryentity.CategoryGroupLink, err error)) *CategoryGroupCacheMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the CategoryGroupCache.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the CategoryGroupCache.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the CategoryGroupCache.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) When(categoryID int64) *CategoryGroupCacheMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupCacheMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &CategoryGroupCacheMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &CategoryGroupCacheMockGetByCategoryIDParams{categoryID},
		expectationOrigins: CategoryGroupCacheMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupCache.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *CategoryGroupCacheMockGetByCategoryIDExpectation) Then(ca1 []categoryentity.CategoryGroupLink, err error) *CategoryGroupCacheMock {
	e.results = &CategoryGroupCacheMockGetByCategoryIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryGroupCache.GetByCategoryID should be invoked
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Times(n uint64) *mCategoryGroupCacheMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of CategoryGroupCacheMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.CategoryGroupCache
func (mmGetByCategoryID *CategoryGroupCacheMock) GetByCategoryID(categoryID int64) (ca1 []categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := CategoryGroupCacheMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupCacheMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("CategoryGroupCacheMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("CategoryGroupCacheMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the CategoryGroupCacheMock.GetByCategoryID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to CategoryGroupCacheMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished CategoryGroupCacheMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryGroupCacheMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of CategoryGroupCacheMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryGroupCacheMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupCacheMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mCategoryGroupCacheMockGetByCategoryID) Calls() []*CategoryGroupCacheMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*CategoryGroupCacheMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *CategoryGroupCacheMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *CategoryGroupCacheMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupCacheMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mCategoryGroupCacheMockGetByID struct {
	optional           bool
	mock               *CategoryGroupCacheMock
	defaultExpectation *CategoryGroupCacheMockGetByIDExpectation
	expectations       []*CategoryGroupCacheMockGetByIDExpectation

	callArgs []*CategoryGroupCacheMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupCacheMockGetByIDExpectation specifies expectation struct of the CategoryGroupCache.GetByID
type CategoryGroupCacheMockGetByIDExpectation struct {
	mock               *CategoryGroupCacheMock
	params             *CategoryGroupCacheMockGetByIDParams
	paramPtrs          *CategoryGroupCacheMockGetByIDParamPtrs
	expectationOrigins CategoryGroupCacheMockGetByIDExpectationOrigins
	results            *CategoryGroupCacheMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupCacheMockGetByIDParams contains parameters of the CategoryGroupCache.GetByID
type CategoryGroupCacheMockGetByIDParams struct {
	id int64
}

// CategoryGroupCacheMockGetByIDParamPtrs contains pointers to parameters of the CategoryGroupCache.GetByID
type CategoryGroupCacheMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryGroupCacheMockGetByIDResults contains results of the CategoryGroupCache.GetByID
type CategoryGroupCacheMockGetByIDResults struct {
	c1  categoryentity.CategoryGroupLink
	err error
}

// CategoryGroupCacheMockGetByIDOrigins contains origins of expectations of the CategoryGroupCache.GetByID
type CategoryGroupCacheMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryGroupCacheMockGetByID) Optional() *mCategoryGroupCacheMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryGroupCache.GetByID
func (mmGetByID *mCategoryGroupCacheMockGetByID) Expect(id int64) *mCategoryGroupCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryGroupCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupCacheMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryGroupCacheMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryGroupCache.GetByID
func (mmGetByID *mCategoryGroupCacheMockGetByID) ExpectIdParam1(id int64) *mCategoryGroupCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryGroupCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupCacheMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryGroupCacheMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupCache.GetByID
func (mmGetByID *mCategoryGroupCacheMockGetByID) Inspect(f func(id int64)) *mCategoryGroupCacheMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryGroupCacheMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryGroupCache.GetByID
func (mmGetByID *mCategoryGroupCacheMockGetByID) Return(c1 categoryentity.CategoryGroupLink, err error) *CategoryGroupCacheMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryGroupCacheMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryGroupCacheMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryGroupCache.GetByID method
func (mmGetByID *mCategoryGroupCacheMockGetByID) Set(f func(id int64) (c1 categoryentity.CategoryGroupLink, err error)) *CategoryGroupCacheMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryGroupCache.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryGroupCache.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryGroupCache.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryGroupCacheMockGetByID) When(id int64) *CategoryGroupCacheMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupCacheMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryGroupCacheMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryGroupCacheMockGetByIDParams{id},
		expectationOrigins: CategoryGroupCacheMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupCache.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryGroupCacheMockGetByIDExpectation) Then(c1 categoryentity.CategoryGroupLink, err error) *CategoryGroupCacheMock {
	e.results = &CategoryGroupCacheMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryGroupCache.GetByID should be invoked
func (mmGetByID *mCategoryGroupCacheMockGetByID) Times(n uint64) *mCategoryGroupCacheMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryGroupCacheMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryGroupCacheMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryGroupCache
func (mmGetByID *CategoryGroupCacheMock) GetByID(id int64) (c1 categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryGroupCacheMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupCacheMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryGroupCacheMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryGroupCacheMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryGroupCacheMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryGroupCacheMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryGroupCacheMock.GetByID invocations
func (mmGetByID *CategoryGroupCacheMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryGroupCacheMock.GetByID invocations
func (mmGetByID *CategoryGroupCacheMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupCacheMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryGroupCacheMockGetByID) Calls() []*CategoryGroupCacheMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryGroupCacheMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryGroupCacheMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryGroupCacheMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupCacheMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupCacheMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryGroupCacheMockSet struct {
	optional           bool
	mock               *CategoryGroupCacheMock
	defaultExpectation *CategoryGroupCacheMockSetExpectation
	expectations       []*CategoryGroupCacheMockSetExpectation

	callArgs []*CategoryGroupCacheMockSetParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupCacheMockSetExpectation specifies expectation struct of the CategoryGroupCache.Set
type CategoryGroupCacheMockSetExpectation struct {
	mock               *CategoryGroupCacheMock
	params             *CategoryGroupCacheMockSetParams
	paramPtrs          *CategoryGroupCacheMockSetParamPtrs
	expectationOrigins CategoryGroupCacheMockSetExpectationOrigins
	results            *CategoryGroupCacheMockSetResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupCacheMockSetParams contains parameters of the CategoryGroupCache.Set
type CategoryGroupCacheMockSetParams struct {
	categoryGroup categoryentity.CategoryGroupLink
}

// CategoryGroupCacheMockSetParamPtrs contains pointers to parameters of the CategoryGroupCache.Set
type CategoryGroupCacheMockSetParamPtrs struct {
	categoryGroup *categoryentity.CategoryGroupLink
}

// CategoryGroupCacheMockSetResults contains results of the CategoryGroupCache.Set
type CategoryGroupCacheMockSetResults struct {
	err error
}

// CategoryGroupCacheMockSetOrigins contains origins of expectations of the CategoryGroupCache.Set
type CategoryGroupCacheMockSetExpectationOrigins struct {
	origin              string
	originCategoryGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSet *mCategoryGroupCacheMockSet) Optional() *mCategoryGroupCacheMockSet {
	mmSet.optional = true
	return mmSet
}

// Expect sets up expected params for CategoryGroupCache.Set
func (mmSet *mCategoryGroupCacheMockSet) Expect(categoryGroup categoryentity.CategoryGroupLink) *mCategoryGroupCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryGroupCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryGroupCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.paramPtrs != nil {
		mmSet.mock.t.Fatalf("CategoryGroupCacheMock.Set mock is already set by ExpectParams functions")
	}

	mmSet.defaultExpectation.params = &CategoryGroupCacheMockSetParams{categoryGroup}
	mmSet.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSet.expectations {
		if minimock.Equal(e.params, mmSet.defaultExpectation.params) {
			mmSet.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSet.defaultExpectation.params)
		}
	}

	return mmSet
}

// ExpectCategoryGroupParam1 sets up expected param categoryGroup for CategoryGroupCache.Set
func (mmSet *mCategoryGroupCacheMockSet) ExpectCategoryGroupParam1(categoryGroup categoryentity.CategoryGroupLink) *mCategoryGroupCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryGroupCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryGroupCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.params != nil {
		mmSet.mock.t.Fatalf("CategoryGroupCacheMock.Set mock is already set by Expect")
	}

	if mmSet.defaultExpectation.paramPtrs == nil {
		mmSet.defaultExpectation.paramPtrs = &CategoryGroupCacheMockSetParamPtrs{}
	}
	mmSet.defaultExpectation.paramPtrs.categoryGroup = &categoryGroup
	mmSet.defaultExpectation.expectationOrigins.originCategoryGroup = minimock.CallerInfo(1)

	return mmSet
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupCache.Set
func (mmSet *mCategoryGroupCacheMockSet) Inspect(f func(categoryGroup categoryentity.CategoryGroupLink)) *mCategoryGroupCacheMockSet {
	if mmSet.mock.inspectFuncSet != nil {
		mmSet.mock.t.Fatalf("Inspect function is already set for CategoryGroupCacheMock.Set")
	}

	mmSet.mock.inspectFuncSet = f

	return mmSet
}

// Return sets up results that will be returned by CategoryGroupCache.Set
func (mmSet *mCategoryGroupCacheMockSet) Return(err error) *CategoryGroupCacheMock {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryGroupCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryGroupCacheMockSetExpectation{mock: mmSet.mock}
	}
	mmSet.defaultExpectation.results = &CategoryGroupCacheMockSetResults{err}
	mmSet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// Set uses given function f to mock the CategoryGroupCache.Set method
func (mmSet *mCategoryGroupCacheMockSet) Set(f func(categoryGroup categoryentity.CategoryGroupLink) (err error)) *CategoryGroupCacheMock {
	if mmSet.defaultExpectation != nil {
		mmSet.mock.t.Fatalf("Default expectation is already set for the CategoryGroupCache.Set method")
	}

	if len(mmSet.expectations) > 0 {
		mmSet.mock.t.Fatalf("Some expectations are already set for the CategoryGroupCache.Set method")
	}

	mmSet.mock.funcSet = f
	mmSet.mock.funcSetOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// When sets expectation for the CategoryGroupCache.Set which will trigger the result defined by the following
// Then helper
func (mmSet *mCategoryGroupCacheMockSet) When(categoryGroup categoryentity.CategoryGroupLink) *CategoryGroupCacheMockSetExpectation {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryGroupCacheMock.Set mock is already set by Set")
	}

	expectation := &CategoryGroupCacheMockSetExpectation{
		mock:               mmSet.mock,
		params:             &CategoryGroupCacheMockSetParams{categoryGroup},
		expectationOrigins: CategoryGroupCacheMockSetExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSet.expectations = append(mmSet.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupCache.Set return parameters for the expectation previously defined by the When method
func (e *CategoryGroupCacheMockSetExpectation) Then(err error) *CategoryGroupCacheMock {
	e.results = &CategoryGroupCacheMockSetResults{err}
	return e.mock
}

// Times sets number of times CategoryGroupCache.Set should be invoked
func (mmSet *mCategoryGroupCacheMockSet) Times(n uint64) *mCategoryGroupCacheMockSet {
	if n == 0 {
		mmSet.mock.t.Fatalf("Times of CategoryGroupCacheMock.Set mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSet.expectedInvocations, n)
	mmSet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSet
}

func (mmSet *mCategoryGroupCacheMockSet) invocationsDone() bool {
	if len(mmSet.expectations) == 0 && mmSet.defaultExpectation == nil && mmSet.mock.funcSet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSet.mock.afterSetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Set implements mm_repository.CategoryGroupCache
func (mmSet *CategoryGroupCacheMock) Set(categoryGroup categoryentity.CategoryGroupLink) (err error) {
	mm_atomic.AddUint64(&mmSet.beforeSetCounter, 1)
	defer mm_atomic.AddUint64(&mmSet.afterSetCounter, 1)

	mmSet.t.Helper()

	if mmSet.inspectFuncSet != nil {
		mmSet.inspectFuncSet(categoryGroup)
	}

	mm_params := CategoryGroupCacheMockSetParams{categoryGroup}

	// Record call args
	mmSet.SetMock.mutex.Lock()
	mmSet.SetMock.callArgs = append(mmSet.SetMock.callArgs, &mm_params)
	mmSet.SetMock.mutex.Unlock()

	for _, e := range mmSet.SetMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSet.SetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSet.SetMock.defaultExpectation.Counter, 1)
		mm_want := mmSet.SetMock.defaultExpectation.params
		mm_want_ptrs := mmSet.SetMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupCacheMockSetParams{categoryGroup}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryGroup != nil && !minimock.Equal(*mm_want_ptrs.categoryGroup, mm_got.categoryGroup) {
				mmSet.t.Errorf("CategoryGroupCacheMock.Set got unexpected parameter categoryGroup, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSet.SetMock.defaultExpectation.expectationOrigins.originCategoryGroup, *mm_want_ptrs.categoryGroup, mm_got.categoryGroup, minimock.Diff(*mm_want_ptrs.categoryGroup, mm_got.categoryGroup))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSet.t.Errorf("CategoryGroupCacheMock.Set got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSet.SetMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSet.SetMock.defaultExpectation.results
		if mm_results == nil {
			mmSet.t.Fatal("No results are set for the CategoryGroupCacheMock.Set")
		}
		return (*mm_results).err
	}
	if mmSet.funcSet != nil {
		return mmSet.funcSet(categoryGroup)
	}
	mmSet.t.Fatalf("Unexpected call to CategoryGroupCacheMock.Set. %v", categoryGroup)
	return
}

// SetAfterCounter returns a count of finished CategoryGroupCacheMock.Set invocations
func (mmSet *CategoryGroupCacheMock) SetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.afterSetCounter)
}

// SetBeforeCounter returns a count of CategoryGroupCacheMock.Set invocations
func (mmSet *CategoryGroupCacheMock) SetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.beforeSetCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupCacheMock.Set.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSet *mCategoryGroupCacheMockSet) Calls() []*CategoryGroupCacheMockSetParams {
	mmSet.mutex.RLock()

	argCopy := make([]*CategoryGroupCacheMockSetParams, len(mmSet.callArgs))
	copy(argCopy, mmSet.callArgs)

	mmSet.mutex.RUnlock()

	return argCopy
}

// MinimockSetDone returns true if the count of the Set invocations corresponds
// the number of defined expectations
func (m *CategoryGroupCacheMock) MinimockSetDone() bool {
	if m.SetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetMock.invocationsDone()
}

// MinimockSetInspect logs each unmet expectation
func (m *CategoryGroupCacheMock) MinimockSetInspect() {
	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.Set at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCounter := mm_atomic.LoadUint64(&m.afterSetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetMock.defaultExpectation != nil && afterSetCounter < 1 {
		if m.SetMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.Set at\n%s", m.SetMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupCacheMock.Set at\n%s with params: %#v", m.SetMock.defaultExpectation.expectationOrigins.origin, *m.SetMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSet != nil && afterSetCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupCacheMock.Set at\n%s", m.funcSetOrigin)
	}

	if !m.SetMock.invocationsDone() && afterSetCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupCacheMock.Set at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetMock.expectedInvocations), m.SetMock.expectedInvocationsOrigin, afterSetCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryGroupCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetAllInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockSetInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryGroupCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryGroupCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockSetDone()
}
