// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryPrimeDB -o category_prime_db_mock.go -n CategoryPrimeDBMock -p mocks

import (
	"context"
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryPrimeDBMock implements mm_repository.CategoryPrimeDB
type CategoryPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(category categoryentity.Category) (c1 categoryentity.Category, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(category categoryentity.Category)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mCategoryPrimeDBMockCreate

	funcDelete          func(ctx context.Context, id int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(ctx context.Context, id int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mCategoryPrimeDBMockDelete

	funcGetAll          func() (ca1 []categoryentity.Category, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryPrimeDBMockGetAll

	funcGetByID          func(id int64) (c1 categoryentity.Category, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryPrimeDBMockGetByID

	funcGetByName          func(name string) (c1 categoryentity.Category, err error)
	funcGetByNameOrigin    string
	inspectFuncGetByName   func(name string)
	afterGetByNameCounter  uint64
	beforeGetByNameCounter uint64
	GetByNameMock          mCategoryPrimeDBMockGetByName

	funcUpdate          func(category categoryentity.Category) (c1 categoryentity.Category, err error)
	funcUpdateOrigin    string
	inspectFuncUpdate   func(category categoryentity.Category)
	afterUpdateCounter  uint64
	beforeUpdateCounter uint64
	UpdateMock          mCategoryPrimeDBMockUpdate
}

// NewCategoryPrimeDBMock returns a mock for mm_repository.CategoryPrimeDB
func NewCategoryPrimeDBMock(t minimock.Tester) *CategoryPrimeDBMock {
	m := &CategoryPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mCategoryPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*CategoryPrimeDBMockCreateParams{}

	m.DeleteMock = mCategoryPrimeDBMockDelete{mock: m}
	m.DeleteMock.callArgs = []*CategoryPrimeDBMockDeleteParams{}

	m.GetAllMock = mCategoryPrimeDBMockGetAll{mock: m}

	m.GetByIDMock = mCategoryPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryPrimeDBMockGetByIDParams{}

	m.GetByNameMock = mCategoryPrimeDBMockGetByName{mock: m}
	m.GetByNameMock.callArgs = []*CategoryPrimeDBMockGetByNameParams{}

	m.UpdateMock = mCategoryPrimeDBMockUpdate{mock: m}
	m.UpdateMock.callArgs = []*CategoryPrimeDBMockUpdateParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryPrimeDBMockCreate struct {
	optional           bool
	mock               *CategoryPrimeDBMock
	defaultExpectation *CategoryPrimeDBMockCreateExpectation
	expectations       []*CategoryPrimeDBMockCreateExpectation

	callArgs []*CategoryPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPrimeDBMockCreateExpectation specifies expectation struct of the CategoryPrimeDB.Create
type CategoryPrimeDBMockCreateExpectation struct {
	mock               *CategoryPrimeDBMock
	params             *CategoryPrimeDBMockCreateParams
	paramPtrs          *CategoryPrimeDBMockCreateParamPtrs
	expectationOrigins CategoryPrimeDBMockCreateExpectationOrigins
	results            *CategoryPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPrimeDBMockCreateParams contains parameters of the CategoryPrimeDB.Create
type CategoryPrimeDBMockCreateParams struct {
	category categoryentity.Category
}

// CategoryPrimeDBMockCreateParamPtrs contains pointers to parameters of the CategoryPrimeDB.Create
type CategoryPrimeDBMockCreateParamPtrs struct {
	category *categoryentity.Category
}

// CategoryPrimeDBMockCreateResults contains results of the CategoryPrimeDB.Create
type CategoryPrimeDBMockCreateResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryPrimeDBMockCreateOrigins contains origins of expectations of the CategoryPrimeDB.Create
type CategoryPrimeDBMockCreateExpectationOrigins struct {
	origin         string
	originCategory string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mCategoryPrimeDBMockCreate) Optional() *mCategoryPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for CategoryPrimeDB.Create
func (mmCreate *mCategoryPrimeDBMockCreate) Expect(category categoryentity.Category) *mCategoryPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("CategoryPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &CategoryPrimeDBMockCreateParams{category}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCategoryParam1 sets up expected param category for CategoryPrimeDB.Create
func (mmCreate *mCategoryPrimeDBMockCreate) ExpectCategoryParam1(category categoryentity.Category) *mCategoryPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("CategoryPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &CategoryPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.category = &category
	mmCreate.defaultExpectation.expectationOrigins.originCategory = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the CategoryPrimeDB.Create
func (mmCreate *mCategoryPrimeDBMockCreate) Inspect(f func(category categoryentity.Category)) *mCategoryPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for CategoryPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by CategoryPrimeDB.Create
func (mmCreate *mCategoryPrimeDBMockCreate) Return(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &CategoryPrimeDBMockCreateResults{c1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the CategoryPrimeDB.Create method
func (mmCreate *mCategoryPrimeDBMockCreate) Set(f func(category categoryentity.Category) (c1 categoryentity.Category, err error)) *CategoryPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the CategoryPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the CategoryPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the CategoryPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mCategoryPrimeDBMockCreate) When(category categoryentity.Category) *CategoryPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &CategoryPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &CategoryPrimeDBMockCreateParams{category},
		expectationOrigins: CategoryPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up CategoryPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *CategoryPrimeDBMockCreateExpectation) Then(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	e.results = &CategoryPrimeDBMockCreateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPrimeDB.Create should be invoked
func (mmCreate *mCategoryPrimeDBMockCreate) Times(n uint64) *mCategoryPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of CategoryPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mCategoryPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.CategoryPrimeDB
func (mmCreate *CategoryPrimeDBMock) Create(category categoryentity.Category) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(category)
	}

	mm_params := CategoryPrimeDBMockCreateParams{category}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := CategoryPrimeDBMockCreateParams{category}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.category != nil && !minimock.Equal(*mm_want_ptrs.category, mm_got.category) {
				mmCreate.t.Errorf("CategoryPrimeDBMock.Create got unexpected parameter category, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCategory, *mm_want_ptrs.category, mm_got.category, minimock.Diff(*mm_want_ptrs.category, mm_got.category))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("CategoryPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the CategoryPrimeDBMock.Create")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(category)
	}
	mmCreate.t.Fatalf("Unexpected call to CategoryPrimeDBMock.Create. %v", category)
	return
}

// CreateAfterCounter returns a count of finished CategoryPrimeDBMock.Create invocations
func (mmCreate *CategoryPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of CategoryPrimeDBMock.Create invocations
func (mmCreate *CategoryPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to CategoryPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mCategoryPrimeDBMockCreate) Calls() []*CategoryPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*CategoryPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *CategoryPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *CategoryPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mCategoryPrimeDBMockDelete struct {
	optional           bool
	mock               *CategoryPrimeDBMock
	defaultExpectation *CategoryPrimeDBMockDeleteExpectation
	expectations       []*CategoryPrimeDBMockDeleteExpectation

	callArgs []*CategoryPrimeDBMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPrimeDBMockDeleteExpectation specifies expectation struct of the CategoryPrimeDB.Delete
type CategoryPrimeDBMockDeleteExpectation struct {
	mock               *CategoryPrimeDBMock
	params             *CategoryPrimeDBMockDeleteParams
	paramPtrs          *CategoryPrimeDBMockDeleteParamPtrs
	expectationOrigins CategoryPrimeDBMockDeleteExpectationOrigins
	results            *CategoryPrimeDBMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPrimeDBMockDeleteParams contains parameters of the CategoryPrimeDB.Delete
type CategoryPrimeDBMockDeleteParams struct {
	ctx context.Context
	id  int64
}

// CategoryPrimeDBMockDeleteParamPtrs contains pointers to parameters of the CategoryPrimeDB.Delete
type CategoryPrimeDBMockDeleteParamPtrs struct {
	ctx *context.Context
	id  *int64
}

// CategoryPrimeDBMockDeleteResults contains results of the CategoryPrimeDB.Delete
type CategoryPrimeDBMockDeleteResults struct {
	err error
}

// CategoryPrimeDBMockDeleteOrigins contains origins of expectations of the CategoryPrimeDB.Delete
type CategoryPrimeDBMockDeleteExpectationOrigins struct {
	origin    string
	originCtx string
	originId  string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mCategoryPrimeDBMockDelete) Optional() *mCategoryPrimeDBMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for CategoryPrimeDB.Delete
func (mmDelete *mCategoryPrimeDBMockDelete) Expect(ctx context.Context, id int64) *mCategoryPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &CategoryPrimeDBMockDeleteParams{ctx, id}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectCtxParam1 sets up expected param ctx for CategoryPrimeDB.Delete
func (mmDelete *mCategoryPrimeDBMockDelete) ExpectCtxParam1(ctx context.Context) *mCategoryPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.ctx = &ctx
	mmDelete.defaultExpectation.expectationOrigins.originCtx = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectIdParam2 sets up expected param id for CategoryPrimeDB.Delete
func (mmDelete *mCategoryPrimeDBMockDelete) ExpectIdParam2(id int64) *mCategoryPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.id = &id
	mmDelete.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the CategoryPrimeDB.Delete
func (mmDelete *mCategoryPrimeDBMockDelete) Inspect(f func(ctx context.Context, id int64)) *mCategoryPrimeDBMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for CategoryPrimeDBMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by CategoryPrimeDB.Delete
func (mmDelete *mCategoryPrimeDBMockDelete) Return(err error) *CategoryPrimeDBMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryPrimeDBMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &CategoryPrimeDBMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the CategoryPrimeDB.Delete method
func (mmDelete *mCategoryPrimeDBMockDelete) Set(f func(ctx context.Context, id int64) (err error)) *CategoryPrimeDBMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the CategoryPrimeDB.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the CategoryPrimeDB.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the CategoryPrimeDB.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mCategoryPrimeDBMockDelete) When(ctx context.Context, id int64) *CategoryPrimeDBMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryPrimeDBMock.Delete mock is already set by Set")
	}

	expectation := &CategoryPrimeDBMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &CategoryPrimeDBMockDeleteParams{ctx, id},
		expectationOrigins: CategoryPrimeDBMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up CategoryPrimeDB.Delete return parameters for the expectation previously defined by the When method
func (e *CategoryPrimeDBMockDeleteExpectation) Then(err error) *CategoryPrimeDBMock {
	e.results = &CategoryPrimeDBMockDeleteResults{err}
	return e.mock
}

// Times sets number of times CategoryPrimeDB.Delete should be invoked
func (mmDelete *mCategoryPrimeDBMockDelete) Times(n uint64) *mCategoryPrimeDBMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of CategoryPrimeDBMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mCategoryPrimeDBMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_repository.CategoryPrimeDB
func (mmDelete *CategoryPrimeDBMock) Delete(ctx context.Context, id int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(ctx, id)
	}

	mm_params := CategoryPrimeDBMockDeleteParams{ctx, id}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := CategoryPrimeDBMockDeleteParams{ctx, id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.ctx != nil && !minimock.Equal(*mm_want_ptrs.ctx, mm_got.ctx) {
				mmDelete.t.Errorf("CategoryPrimeDBMock.Delete got unexpected parameter ctx, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originCtx, *mm_want_ptrs.ctx, mm_got.ctx, minimock.Diff(*mm_want_ptrs.ctx, mm_got.ctx))
			}

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmDelete.t.Errorf("CategoryPrimeDBMock.Delete got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("CategoryPrimeDBMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the CategoryPrimeDBMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(ctx, id)
	}
	mmDelete.t.Fatalf("Unexpected call to CategoryPrimeDBMock.Delete. %v %v", ctx, id)
	return
}

// DeleteAfterCounter returns a count of finished CategoryPrimeDBMock.Delete invocations
func (mmDelete *CategoryPrimeDBMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of CategoryPrimeDBMock.Delete invocations
func (mmDelete *CategoryPrimeDBMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to CategoryPrimeDBMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mCategoryPrimeDBMockDelete) Calls() []*CategoryPrimeDBMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*CategoryPrimeDBMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *CategoryPrimeDBMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *CategoryPrimeDBMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPrimeDBMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mCategoryPrimeDBMockGetAll struct {
	optional           bool
	mock               *CategoryPrimeDBMock
	defaultExpectation *CategoryPrimeDBMockGetAllExpectation
	expectations       []*CategoryPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPrimeDBMockGetAllExpectation specifies expectation struct of the CategoryPrimeDB.GetAll
type CategoryPrimeDBMockGetAllExpectation struct {
	mock *CategoryPrimeDBMock

	results      *CategoryPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryPrimeDBMockGetAllResults contains results of the CategoryPrimeDB.GetAll
type CategoryPrimeDBMockGetAllResults struct {
	ca1 []categoryentity.Category
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryPrimeDBMockGetAll) Optional() *mCategoryPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryPrimeDB.GetAll
func (mmGetAll *mCategoryPrimeDBMockGetAll) Expect() *mCategoryPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryPrimeDB.GetAll
func (mmGetAll *mCategoryPrimeDBMockGetAll) Inspect(f func()) *mCategoryPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryPrimeDB.GetAll
func (mmGetAll *mCategoryPrimeDBMockGetAll) Return(ca1 []categoryentity.Category, err error) *CategoryPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryPrimeDBMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryPrimeDB.GetAll method
func (mmGetAll *mCategoryPrimeDBMockGetAll) Set(f func() (ca1 []categoryentity.Category, err error)) *CategoryPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryPrimeDB.GetAll should be invoked
func (mmGetAll *mCategoryPrimeDBMockGetAll) Times(n uint64) *mCategoryPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryPrimeDB
func (mmGetAll *CategoryPrimeDBMock) GetAll() (ca1 []categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryPrimeDBMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryPrimeDBMock.GetAll invocations
func (mmGetAll *CategoryPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryPrimeDBMock.GetAll invocations
func (mmGetAll *CategoryPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryPrimeDBMockGetByID struct {
	optional           bool
	mock               *CategoryPrimeDBMock
	defaultExpectation *CategoryPrimeDBMockGetByIDExpectation
	expectations       []*CategoryPrimeDBMockGetByIDExpectation

	callArgs []*CategoryPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPrimeDBMockGetByIDExpectation specifies expectation struct of the CategoryPrimeDB.GetByID
type CategoryPrimeDBMockGetByIDExpectation struct {
	mock               *CategoryPrimeDBMock
	params             *CategoryPrimeDBMockGetByIDParams
	paramPtrs          *CategoryPrimeDBMockGetByIDParamPtrs
	expectationOrigins CategoryPrimeDBMockGetByIDExpectationOrigins
	results            *CategoryPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPrimeDBMockGetByIDParams contains parameters of the CategoryPrimeDB.GetByID
type CategoryPrimeDBMockGetByIDParams struct {
	id int64
}

// CategoryPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the CategoryPrimeDB.GetByID
type CategoryPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryPrimeDBMockGetByIDResults contains results of the CategoryPrimeDB.GetByID
type CategoryPrimeDBMockGetByIDResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryPrimeDBMockGetByIDOrigins contains origins of expectations of the CategoryPrimeDB.GetByID
type CategoryPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryPrimeDBMockGetByID) Optional() *mCategoryPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryPrimeDB.GetByID
func (mmGetByID *mCategoryPrimeDBMockGetByID) Expect(id int64) *mCategoryPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryPrimeDB.GetByID
func (mmGetByID *mCategoryPrimeDBMockGetByID) ExpectIdParam1(id int64) *mCategoryPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryPrimeDB.GetByID
func (mmGetByID *mCategoryPrimeDBMockGetByID) Inspect(f func(id int64)) *mCategoryPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryPrimeDB.GetByID
func (mmGetByID *mCategoryPrimeDBMockGetByID) Return(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryPrimeDBMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryPrimeDB.GetByID method
func (mmGetByID *mCategoryPrimeDBMockGetByID) Set(f func(id int64) (c1 categoryentity.Category, err error)) *CategoryPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryPrimeDBMockGetByID) When(id int64) *CategoryPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryPrimeDBMockGetByIDParams{id},
		expectationOrigins: CategoryPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryPrimeDBMockGetByIDExpectation) Then(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	e.results = &CategoryPrimeDBMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPrimeDB.GetByID should be invoked
func (mmGetByID *mCategoryPrimeDBMockGetByID) Times(n uint64) *mCategoryPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryPrimeDB
func (mmGetByID *CategoryPrimeDBMock) GetByID(id int64) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryPrimeDBMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryPrimeDBMock.GetByID invocations
func (mmGetByID *CategoryPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryPrimeDBMock.GetByID invocations
func (mmGetByID *CategoryPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryPrimeDBMockGetByID) Calls() []*CategoryPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryPrimeDBMockGetByName struct {
	optional           bool
	mock               *CategoryPrimeDBMock
	defaultExpectation *CategoryPrimeDBMockGetByNameExpectation
	expectations       []*CategoryPrimeDBMockGetByNameExpectation

	callArgs []*CategoryPrimeDBMockGetByNameParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPrimeDBMockGetByNameExpectation specifies expectation struct of the CategoryPrimeDB.GetByName
type CategoryPrimeDBMockGetByNameExpectation struct {
	mock               *CategoryPrimeDBMock
	params             *CategoryPrimeDBMockGetByNameParams
	paramPtrs          *CategoryPrimeDBMockGetByNameParamPtrs
	expectationOrigins CategoryPrimeDBMockGetByNameExpectationOrigins
	results            *CategoryPrimeDBMockGetByNameResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPrimeDBMockGetByNameParams contains parameters of the CategoryPrimeDB.GetByName
type CategoryPrimeDBMockGetByNameParams struct {
	name string
}

// CategoryPrimeDBMockGetByNameParamPtrs contains pointers to parameters of the CategoryPrimeDB.GetByName
type CategoryPrimeDBMockGetByNameParamPtrs struct {
	name *string
}

// CategoryPrimeDBMockGetByNameResults contains results of the CategoryPrimeDB.GetByName
type CategoryPrimeDBMockGetByNameResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryPrimeDBMockGetByNameOrigins contains origins of expectations of the CategoryPrimeDB.GetByName
type CategoryPrimeDBMockGetByNameExpectationOrigins struct {
	origin     string
	originName string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByName *mCategoryPrimeDBMockGetByName) Optional() *mCategoryPrimeDBMockGetByName {
	mmGetByName.optional = true
	return mmGetByName
}

// Expect sets up expected params for CategoryPrimeDB.GetByName
func (mmGetByName *mCategoryPrimeDBMockGetByName) Expect(name string) *mCategoryPrimeDBMockGetByName {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryPrimeDBMock.GetByName mock is already set by Set")
	}

	if mmGetByName.defaultExpectation == nil {
		mmGetByName.defaultExpectation = &CategoryPrimeDBMockGetByNameExpectation{}
	}

	if mmGetByName.defaultExpectation.paramPtrs != nil {
		mmGetByName.mock.t.Fatalf("CategoryPrimeDBMock.GetByName mock is already set by ExpectParams functions")
	}

	mmGetByName.defaultExpectation.params = &CategoryPrimeDBMockGetByNameParams{name}
	mmGetByName.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByName.expectations {
		if minimock.Equal(e.params, mmGetByName.defaultExpectation.params) {
			mmGetByName.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByName.defaultExpectation.params)
		}
	}

	return mmGetByName
}

// ExpectNameParam1 sets up expected param name for CategoryPrimeDB.GetByName
func (mmGetByName *mCategoryPrimeDBMockGetByName) ExpectNameParam1(name string) *mCategoryPrimeDBMockGetByName {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryPrimeDBMock.GetByName mock is already set by Set")
	}

	if mmGetByName.defaultExpectation == nil {
		mmGetByName.defaultExpectation = &CategoryPrimeDBMockGetByNameExpectation{}
	}

	if mmGetByName.defaultExpectation.params != nil {
		mmGetByName.mock.t.Fatalf("CategoryPrimeDBMock.GetByName mock is already set by Expect")
	}

	if mmGetByName.defaultExpectation.paramPtrs == nil {
		mmGetByName.defaultExpectation.paramPtrs = &CategoryPrimeDBMockGetByNameParamPtrs{}
	}
	mmGetByName.defaultExpectation.paramPtrs.name = &name
	mmGetByName.defaultExpectation.expectationOrigins.originName = minimock.CallerInfo(1)

	return mmGetByName
}

// Inspect accepts an inspector function that has same arguments as the CategoryPrimeDB.GetByName
func (mmGetByName *mCategoryPrimeDBMockGetByName) Inspect(f func(name string)) *mCategoryPrimeDBMockGetByName {
	if mmGetByName.mock.inspectFuncGetByName != nil {
		mmGetByName.mock.t.Fatalf("Inspect function is already set for CategoryPrimeDBMock.GetByName")
	}

	mmGetByName.mock.inspectFuncGetByName = f

	return mmGetByName
}

// Return sets up results that will be returned by CategoryPrimeDB.GetByName
func (mmGetByName *mCategoryPrimeDBMockGetByName) Return(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryPrimeDBMock.GetByName mock is already set by Set")
	}

	if mmGetByName.defaultExpectation == nil {
		mmGetByName.defaultExpectation = &CategoryPrimeDBMockGetByNameExpectation{mock: mmGetByName.mock}
	}
	mmGetByName.defaultExpectation.results = &CategoryPrimeDBMockGetByNameResults{c1, err}
	mmGetByName.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByName.mock
}

// Set uses given function f to mock the CategoryPrimeDB.GetByName method
func (mmGetByName *mCategoryPrimeDBMockGetByName) Set(f func(name string) (c1 categoryentity.Category, err error)) *CategoryPrimeDBMock {
	if mmGetByName.defaultExpectation != nil {
		mmGetByName.mock.t.Fatalf("Default expectation is already set for the CategoryPrimeDB.GetByName method")
	}

	if len(mmGetByName.expectations) > 0 {
		mmGetByName.mock.t.Fatalf("Some expectations are already set for the CategoryPrimeDB.GetByName method")
	}

	mmGetByName.mock.funcGetByName = f
	mmGetByName.mock.funcGetByNameOrigin = minimock.CallerInfo(1)
	return mmGetByName.mock
}

// When sets expectation for the CategoryPrimeDB.GetByName which will trigger the result defined by the following
// Then helper
func (mmGetByName *mCategoryPrimeDBMockGetByName) When(name string) *CategoryPrimeDBMockGetByNameExpectation {
	if mmGetByName.mock.funcGetByName != nil {
		mmGetByName.mock.t.Fatalf("CategoryPrimeDBMock.GetByName mock is already set by Set")
	}

	expectation := &CategoryPrimeDBMockGetByNameExpectation{
		mock:               mmGetByName.mock,
		params:             &CategoryPrimeDBMockGetByNameParams{name},
		expectationOrigins: CategoryPrimeDBMockGetByNameExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByName.expectations = append(mmGetByName.expectations, expectation)
	return expectation
}

// Then sets up CategoryPrimeDB.GetByName return parameters for the expectation previously defined by the When method
func (e *CategoryPrimeDBMockGetByNameExpectation) Then(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	e.results = &CategoryPrimeDBMockGetByNameResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPrimeDB.GetByName should be invoked
func (mmGetByName *mCategoryPrimeDBMockGetByName) Times(n uint64) *mCategoryPrimeDBMockGetByName {
	if n == 0 {
		mmGetByName.mock.t.Fatalf("Times of CategoryPrimeDBMock.GetByName mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByName.expectedInvocations, n)
	mmGetByName.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByName
}

func (mmGetByName *mCategoryPrimeDBMockGetByName) invocationsDone() bool {
	if len(mmGetByName.expectations) == 0 && mmGetByName.defaultExpectation == nil && mmGetByName.mock.funcGetByName == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByName.mock.afterGetByNameCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByName.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByName implements mm_repository.CategoryPrimeDB
func (mmGetByName *CategoryPrimeDBMock) GetByName(name string) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmGetByName.beforeGetByNameCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByName.afterGetByNameCounter, 1)

	mmGetByName.t.Helper()

	if mmGetByName.inspectFuncGetByName != nil {
		mmGetByName.inspectFuncGetByName(name)
	}

	mm_params := CategoryPrimeDBMockGetByNameParams{name}

	// Record call args
	mmGetByName.GetByNameMock.mutex.Lock()
	mmGetByName.GetByNameMock.callArgs = append(mmGetByName.GetByNameMock.callArgs, &mm_params)
	mmGetByName.GetByNameMock.mutex.Unlock()

	for _, e := range mmGetByName.GetByNameMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByName.GetByNameMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByName.GetByNameMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByName.GetByNameMock.defaultExpectation.params
		mm_want_ptrs := mmGetByName.GetByNameMock.defaultExpectation.paramPtrs

		mm_got := CategoryPrimeDBMockGetByNameParams{name}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.name != nil && !minimock.Equal(*mm_want_ptrs.name, mm_got.name) {
				mmGetByName.t.Errorf("CategoryPrimeDBMock.GetByName got unexpected parameter name, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByName.GetByNameMock.defaultExpectation.expectationOrigins.originName, *mm_want_ptrs.name, mm_got.name, minimock.Diff(*mm_want_ptrs.name, mm_got.name))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByName.t.Errorf("CategoryPrimeDBMock.GetByName got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByName.GetByNameMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByName.GetByNameMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByName.t.Fatal("No results are set for the CategoryPrimeDBMock.GetByName")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByName.funcGetByName != nil {
		return mmGetByName.funcGetByName(name)
	}
	mmGetByName.t.Fatalf("Unexpected call to CategoryPrimeDBMock.GetByName. %v", name)
	return
}

// GetByNameAfterCounter returns a count of finished CategoryPrimeDBMock.GetByName invocations
func (mmGetByName *CategoryPrimeDBMock) GetByNameAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByName.afterGetByNameCounter)
}

// GetByNameBeforeCounter returns a count of CategoryPrimeDBMock.GetByName invocations
func (mmGetByName *CategoryPrimeDBMock) GetByNameBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByName.beforeGetByNameCounter)
}

// Calls returns a list of arguments used in each call to CategoryPrimeDBMock.GetByName.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByName *mCategoryPrimeDBMockGetByName) Calls() []*CategoryPrimeDBMockGetByNameParams {
	mmGetByName.mutex.RLock()

	argCopy := make([]*CategoryPrimeDBMockGetByNameParams, len(mmGetByName.callArgs))
	copy(argCopy, mmGetByName.callArgs)

	mmGetByName.mutex.RUnlock()

	return argCopy
}

// MinimockGetByNameDone returns true if the count of the GetByName invocations corresponds
// the number of defined expectations
func (m *CategoryPrimeDBMock) MinimockGetByNameDone() bool {
	if m.GetByNameMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByNameMock.invocationsDone()
}

// MinimockGetByNameInspect logs each unmet expectation
func (m *CategoryPrimeDBMock) MinimockGetByNameInspect() {
	for _, e := range m.GetByNameMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByName at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByNameCounter := mm_atomic.LoadUint64(&m.afterGetByNameCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByNameMock.defaultExpectation != nil && afterGetByNameCounter < 1 {
		if m.GetByNameMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByName at\n%s", m.GetByNameMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByName at\n%s with params: %#v", m.GetByNameMock.defaultExpectation.expectationOrigins.origin, *m.GetByNameMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByName != nil && afterGetByNameCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.GetByName at\n%s", m.funcGetByNameOrigin)
	}

	if !m.GetByNameMock.invocationsDone() && afterGetByNameCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPrimeDBMock.GetByName at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByNameMock.expectedInvocations), m.GetByNameMock.expectedInvocationsOrigin, afterGetByNameCounter)
	}
}

type mCategoryPrimeDBMockUpdate struct {
	optional           bool
	mock               *CategoryPrimeDBMock
	defaultExpectation *CategoryPrimeDBMockUpdateExpectation
	expectations       []*CategoryPrimeDBMockUpdateExpectation

	callArgs []*CategoryPrimeDBMockUpdateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPrimeDBMockUpdateExpectation specifies expectation struct of the CategoryPrimeDB.Update
type CategoryPrimeDBMockUpdateExpectation struct {
	mock               *CategoryPrimeDBMock
	params             *CategoryPrimeDBMockUpdateParams
	paramPtrs          *CategoryPrimeDBMockUpdateParamPtrs
	expectationOrigins CategoryPrimeDBMockUpdateExpectationOrigins
	results            *CategoryPrimeDBMockUpdateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPrimeDBMockUpdateParams contains parameters of the CategoryPrimeDB.Update
type CategoryPrimeDBMockUpdateParams struct {
	category categoryentity.Category
}

// CategoryPrimeDBMockUpdateParamPtrs contains pointers to parameters of the CategoryPrimeDB.Update
type CategoryPrimeDBMockUpdateParamPtrs struct {
	category *categoryentity.Category
}

// CategoryPrimeDBMockUpdateResults contains results of the CategoryPrimeDB.Update
type CategoryPrimeDBMockUpdateResults struct {
	c1  categoryentity.Category
	err error
}

// CategoryPrimeDBMockUpdateOrigins contains origins of expectations of the CategoryPrimeDB.Update
type CategoryPrimeDBMockUpdateExpectationOrigins struct {
	origin         string
	originCategory string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmUpdate *mCategoryPrimeDBMockUpdate) Optional() *mCategoryPrimeDBMockUpdate {
	mmUpdate.optional = true
	return mmUpdate
}

// Expect sets up expected params for CategoryPrimeDB.Update
func (mmUpdate *mCategoryPrimeDBMockUpdate) Expect(category categoryentity.Category) *mCategoryPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &CategoryPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.paramPtrs != nil {
		mmUpdate.mock.t.Fatalf("CategoryPrimeDBMock.Update mock is already set by ExpectParams functions")
	}

	mmUpdate.defaultExpectation.params = &CategoryPrimeDBMockUpdateParams{category}
	mmUpdate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmUpdate.expectations {
		if minimock.Equal(e.params, mmUpdate.defaultExpectation.params) {
			mmUpdate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmUpdate.defaultExpectation.params)
		}
	}

	return mmUpdate
}

// ExpectCategoryParam1 sets up expected param category for CategoryPrimeDB.Update
func (mmUpdate *mCategoryPrimeDBMockUpdate) ExpectCategoryParam1(category categoryentity.Category) *mCategoryPrimeDBMockUpdate {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &CategoryPrimeDBMockUpdateExpectation{}
	}

	if mmUpdate.defaultExpectation.params != nil {
		mmUpdate.mock.t.Fatalf("CategoryPrimeDBMock.Update mock is already set by Expect")
	}

	if mmUpdate.defaultExpectation.paramPtrs == nil {
		mmUpdate.defaultExpectation.paramPtrs = &CategoryPrimeDBMockUpdateParamPtrs{}
	}
	mmUpdate.defaultExpectation.paramPtrs.category = &category
	mmUpdate.defaultExpectation.expectationOrigins.originCategory = minimock.CallerInfo(1)

	return mmUpdate
}

// Inspect accepts an inspector function that has same arguments as the CategoryPrimeDB.Update
func (mmUpdate *mCategoryPrimeDBMockUpdate) Inspect(f func(category categoryentity.Category)) *mCategoryPrimeDBMockUpdate {
	if mmUpdate.mock.inspectFuncUpdate != nil {
		mmUpdate.mock.t.Fatalf("Inspect function is already set for CategoryPrimeDBMock.Update")
	}

	mmUpdate.mock.inspectFuncUpdate = f

	return mmUpdate
}

// Return sets up results that will be returned by CategoryPrimeDB.Update
func (mmUpdate *mCategoryPrimeDBMockUpdate) Return(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryPrimeDBMock.Update mock is already set by Set")
	}

	if mmUpdate.defaultExpectation == nil {
		mmUpdate.defaultExpectation = &CategoryPrimeDBMockUpdateExpectation{mock: mmUpdate.mock}
	}
	mmUpdate.defaultExpectation.results = &CategoryPrimeDBMockUpdateResults{c1, err}
	mmUpdate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// Set uses given function f to mock the CategoryPrimeDB.Update method
func (mmUpdate *mCategoryPrimeDBMockUpdate) Set(f func(category categoryentity.Category) (c1 categoryentity.Category, err error)) *CategoryPrimeDBMock {
	if mmUpdate.defaultExpectation != nil {
		mmUpdate.mock.t.Fatalf("Default expectation is already set for the CategoryPrimeDB.Update method")
	}

	if len(mmUpdate.expectations) > 0 {
		mmUpdate.mock.t.Fatalf("Some expectations are already set for the CategoryPrimeDB.Update method")
	}

	mmUpdate.mock.funcUpdate = f
	mmUpdate.mock.funcUpdateOrigin = minimock.CallerInfo(1)
	return mmUpdate.mock
}

// When sets expectation for the CategoryPrimeDB.Update which will trigger the result defined by the following
// Then helper
func (mmUpdate *mCategoryPrimeDBMockUpdate) When(category categoryentity.Category) *CategoryPrimeDBMockUpdateExpectation {
	if mmUpdate.mock.funcUpdate != nil {
		mmUpdate.mock.t.Fatalf("CategoryPrimeDBMock.Update mock is already set by Set")
	}

	expectation := &CategoryPrimeDBMockUpdateExpectation{
		mock:               mmUpdate.mock,
		params:             &CategoryPrimeDBMockUpdateParams{category},
		expectationOrigins: CategoryPrimeDBMockUpdateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmUpdate.expectations = append(mmUpdate.expectations, expectation)
	return expectation
}

// Then sets up CategoryPrimeDB.Update return parameters for the expectation previously defined by the When method
func (e *CategoryPrimeDBMockUpdateExpectation) Then(c1 categoryentity.Category, err error) *CategoryPrimeDBMock {
	e.results = &CategoryPrimeDBMockUpdateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPrimeDB.Update should be invoked
func (mmUpdate *mCategoryPrimeDBMockUpdate) Times(n uint64) *mCategoryPrimeDBMockUpdate {
	if n == 0 {
		mmUpdate.mock.t.Fatalf("Times of CategoryPrimeDBMock.Update mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmUpdate.expectedInvocations, n)
	mmUpdate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmUpdate
}

func (mmUpdate *mCategoryPrimeDBMockUpdate) invocationsDone() bool {
	if len(mmUpdate.expectations) == 0 && mmUpdate.defaultExpectation == nil && mmUpdate.mock.funcUpdate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmUpdate.mock.afterUpdateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmUpdate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Update implements mm_repository.CategoryPrimeDB
func (mmUpdate *CategoryPrimeDBMock) Update(category categoryentity.Category) (c1 categoryentity.Category, err error) {
	mm_atomic.AddUint64(&mmUpdate.beforeUpdateCounter, 1)
	defer mm_atomic.AddUint64(&mmUpdate.afterUpdateCounter, 1)

	mmUpdate.t.Helper()

	if mmUpdate.inspectFuncUpdate != nil {
		mmUpdate.inspectFuncUpdate(category)
	}

	mm_params := CategoryPrimeDBMockUpdateParams{category}

	// Record call args
	mmUpdate.UpdateMock.mutex.Lock()
	mmUpdate.UpdateMock.callArgs = append(mmUpdate.UpdateMock.callArgs, &mm_params)
	mmUpdate.UpdateMock.mutex.Unlock()

	for _, e := range mmUpdate.UpdateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmUpdate.UpdateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmUpdate.UpdateMock.defaultExpectation.Counter, 1)
		mm_want := mmUpdate.UpdateMock.defaultExpectation.params
		mm_want_ptrs := mmUpdate.UpdateMock.defaultExpectation.paramPtrs

		mm_got := CategoryPrimeDBMockUpdateParams{category}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.category != nil && !minimock.Equal(*mm_want_ptrs.category, mm_got.category) {
				mmUpdate.t.Errorf("CategoryPrimeDBMock.Update got unexpected parameter category, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.originCategory, *mm_want_ptrs.category, mm_got.category, minimock.Diff(*mm_want_ptrs.category, mm_got.category))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmUpdate.t.Errorf("CategoryPrimeDBMock.Update got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmUpdate.UpdateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmUpdate.UpdateMock.defaultExpectation.results
		if mm_results == nil {
			mmUpdate.t.Fatal("No results are set for the CategoryPrimeDBMock.Update")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmUpdate.funcUpdate != nil {
		return mmUpdate.funcUpdate(category)
	}
	mmUpdate.t.Fatalf("Unexpected call to CategoryPrimeDBMock.Update. %v", category)
	return
}

// UpdateAfterCounter returns a count of finished CategoryPrimeDBMock.Update invocations
func (mmUpdate *CategoryPrimeDBMock) UpdateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.afterUpdateCounter)
}

// UpdateBeforeCounter returns a count of CategoryPrimeDBMock.Update invocations
func (mmUpdate *CategoryPrimeDBMock) UpdateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmUpdate.beforeUpdateCounter)
}

// Calls returns a list of arguments used in each call to CategoryPrimeDBMock.Update.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmUpdate *mCategoryPrimeDBMockUpdate) Calls() []*CategoryPrimeDBMockUpdateParams {
	mmUpdate.mutex.RLock()

	argCopy := make([]*CategoryPrimeDBMockUpdateParams, len(mmUpdate.callArgs))
	copy(argCopy, mmUpdate.callArgs)

	mmUpdate.mutex.RUnlock()

	return argCopy
}

// MinimockUpdateDone returns true if the count of the Update invocations corresponds
// the number of defined expectations
func (m *CategoryPrimeDBMock) MinimockUpdateDone() bool {
	if m.UpdateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.UpdateMock.invocationsDone()
}

// MinimockUpdateInspect logs each unmet expectation
func (m *CategoryPrimeDBMock) MinimockUpdateInspect() {
	for _, e := range m.UpdateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Update at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterUpdateCounter := mm_atomic.LoadUint64(&m.afterUpdateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.UpdateMock.defaultExpectation != nil && afterUpdateCounter < 1 {
		if m.UpdateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Update at\n%s", m.UpdateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPrimeDBMock.Update at\n%s with params: %#v", m.UpdateMock.defaultExpectation.expectationOrigins.origin, *m.UpdateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcUpdate != nil && afterUpdateCounter < 1 {
		m.t.Errorf("Expected call to CategoryPrimeDBMock.Update at\n%s", m.funcUpdateOrigin)
	}

	if !m.UpdateMock.invocationsDone() && afterUpdateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPrimeDBMock.Update at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.UpdateMock.expectedInvocations), m.UpdateMock.expectedInvocationsOrigin, afterUpdateCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeleteInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByNameInspect()

			m.MinimockUpdateInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByNameDone() &&
		m.MinimockUpdateDone()
}
