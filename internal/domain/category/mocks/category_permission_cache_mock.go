// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryPermissionCache -o category_permission_cache_mock.go -n CategoryPermissionCacheMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryPermissionCacheMock implements mm_repository.CategoryPermissionCache
type CategoryPermissionCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetAll          func() (ca1 []categoryentity.CategoryPermission, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryPermissionCacheMockGetAll

	funcGetByCategoryID          func(categoryID int64) (ca1 []categoryentity.CategoryPermission, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mCategoryPermissionCacheMockGetByCategoryID

	funcGetByID          func(id int64) (c1 categoryentity.CategoryPermission, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryPermissionCacheMockGetByID

	funcSet          func(categoryPermission categoryentity.CategoryPermission) (err error)
	funcSetOrigin    string
	inspectFuncSet   func(categoryPermission categoryentity.CategoryPermission)
	afterSetCounter  uint64
	beforeSetCounter uint64
	SetMock          mCategoryPermissionCacheMockSet
}

// NewCategoryPermissionCacheMock returns a mock for mm_repository.CategoryPermissionCache
func NewCategoryPermissionCacheMock(t minimock.Tester) *CategoryPermissionCacheMock {
	m := &CategoryPermissionCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetAllMock = mCategoryPermissionCacheMockGetAll{mock: m}

	m.GetByCategoryIDMock = mCategoryPermissionCacheMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*CategoryPermissionCacheMockGetByCategoryIDParams{}

	m.GetByIDMock = mCategoryPermissionCacheMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryPermissionCacheMockGetByIDParams{}

	m.SetMock = mCategoryPermissionCacheMockSet{mock: m}
	m.SetMock.callArgs = []*CategoryPermissionCacheMockSetParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryPermissionCacheMockGetAll struct {
	optional           bool
	mock               *CategoryPermissionCacheMock
	defaultExpectation *CategoryPermissionCacheMockGetAllExpectation
	expectations       []*CategoryPermissionCacheMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionCacheMockGetAllExpectation specifies expectation struct of the CategoryPermissionCache.GetAll
type CategoryPermissionCacheMockGetAllExpectation struct {
	mock *CategoryPermissionCacheMock

	results      *CategoryPermissionCacheMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryPermissionCacheMockGetAllResults contains results of the CategoryPermissionCache.GetAll
type CategoryPermissionCacheMockGetAllResults struct {
	ca1 []categoryentity.CategoryPermission
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryPermissionCacheMockGetAll) Optional() *mCategoryPermissionCacheMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryPermissionCache.GetAll
func (mmGetAll *mCategoryPermissionCacheMockGetAll) Expect() *mCategoryPermissionCacheMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryPermissionCacheMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryPermissionCacheMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionCache.GetAll
func (mmGetAll *mCategoryPermissionCacheMockGetAll) Inspect(f func()) *mCategoryPermissionCacheMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryPermissionCacheMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryPermissionCache.GetAll
func (mmGetAll *mCategoryPermissionCacheMockGetAll) Return(ca1 []categoryentity.CategoryPermission, err error) *CategoryPermissionCacheMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryPermissionCacheMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryPermissionCacheMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryPermissionCacheMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryPermissionCache.GetAll method
func (mmGetAll *mCategoryPermissionCacheMockGetAll) Set(f func() (ca1 []categoryentity.CategoryPermission, err error)) *CategoryPermissionCacheMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionCache.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionCache.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryPermissionCache.GetAll should be invoked
func (mmGetAll *mCategoryPermissionCacheMockGetAll) Times(n uint64) *mCategoryPermissionCacheMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryPermissionCacheMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryPermissionCacheMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryPermissionCache
func (mmGetAll *CategoryPermissionCacheMock) GetAll() (ca1 []categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryPermissionCacheMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryPermissionCacheMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryPermissionCacheMock.GetAll invocations
func (mmGetAll *CategoryPermissionCacheMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryPermissionCacheMock.GetAll invocations
func (mmGetAll *CategoryPermissionCacheMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionCacheMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryPermissionCacheMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryPermissionCacheMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionCacheMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryPermissionCacheMockGetByCategoryID struct {
	optional           bool
	mock               *CategoryPermissionCacheMock
	defaultExpectation *CategoryPermissionCacheMockGetByCategoryIDExpectation
	expectations       []*CategoryPermissionCacheMockGetByCategoryIDExpectation

	callArgs []*CategoryPermissionCacheMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionCacheMockGetByCategoryIDExpectation specifies expectation struct of the CategoryPermissionCache.GetByCategoryID
type CategoryPermissionCacheMockGetByCategoryIDExpectation struct {
	mock               *CategoryPermissionCacheMock
	params             *CategoryPermissionCacheMockGetByCategoryIDParams
	paramPtrs          *CategoryPermissionCacheMockGetByCategoryIDParamPtrs
	expectationOrigins CategoryPermissionCacheMockGetByCategoryIDExpectationOrigins
	results            *CategoryPermissionCacheMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionCacheMockGetByCategoryIDParams contains parameters of the CategoryPermissionCache.GetByCategoryID
type CategoryPermissionCacheMockGetByCategoryIDParams struct {
	categoryID int64
}

// CategoryPermissionCacheMockGetByCategoryIDParamPtrs contains pointers to parameters of the CategoryPermissionCache.GetByCategoryID
type CategoryPermissionCacheMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// CategoryPermissionCacheMockGetByCategoryIDResults contains results of the CategoryPermissionCache.GetByCategoryID
type CategoryPermissionCacheMockGetByCategoryIDResults struct {
	ca1 []categoryentity.CategoryPermission
	err error
}

// CategoryPermissionCacheMockGetByCategoryIDOrigins contains origins of expectations of the CategoryPermissionCache.GetByCategoryID
type CategoryPermissionCacheMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Optional() *mCategoryPermissionCacheMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for CategoryPermissionCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Expect(categoryID int64) *mCategoryPermissionCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryPermissionCacheMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &CategoryPermissionCacheMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryPermissionCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mCategoryPermissionCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryPermissionCacheMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &CategoryPermissionCacheMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Inspect(f func(categoryID int64)) *mCategoryPermissionCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for CategoryPermissionCacheMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by CategoryPermissionCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Return(ca1 []categoryentity.CategoryPermission, err error) *CategoryPermissionCacheMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryPermissionCacheMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &CategoryPermissionCacheMockGetByCategoryIDResults{ca1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the CategoryPermissionCache.GetByCategoryID method
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Set(f func(categoryID int64) (ca1 []categoryentity.CategoryPermission, err error)) *CategoryPermissionCacheMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionCache.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionCache.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the CategoryPermissionCache.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) When(categoryID int64) *CategoryPermissionCacheMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &CategoryPermissionCacheMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &CategoryPermissionCacheMockGetByCategoryIDParams{categoryID},
		expectationOrigins: CategoryPermissionCacheMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionCache.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionCacheMockGetByCategoryIDExpectation) Then(ca1 []categoryentity.CategoryPermission, err error) *CategoryPermissionCacheMock {
	e.results = &CategoryPermissionCacheMockGetByCategoryIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryPermissionCache.GetByCategoryID should be invoked
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Times(n uint64) *mCategoryPermissionCacheMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of CategoryPermissionCacheMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.CategoryPermissionCache
func (mmGetByCategoryID *CategoryPermissionCacheMock) GetByCategoryID(categoryID int64) (ca1 []categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := CategoryPermissionCacheMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionCacheMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("CategoryPermissionCacheMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("CategoryPermissionCacheMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the CategoryPermissionCacheMock.GetByCategoryID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to CategoryPermissionCacheMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished CategoryPermissionCacheMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryPermissionCacheMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of CategoryPermissionCacheMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryPermissionCacheMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionCacheMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mCategoryPermissionCacheMockGetByCategoryID) Calls() []*CategoryPermissionCacheMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*CategoryPermissionCacheMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionCacheMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *CategoryPermissionCacheMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionCacheMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mCategoryPermissionCacheMockGetByID struct {
	optional           bool
	mock               *CategoryPermissionCacheMock
	defaultExpectation *CategoryPermissionCacheMockGetByIDExpectation
	expectations       []*CategoryPermissionCacheMockGetByIDExpectation

	callArgs []*CategoryPermissionCacheMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionCacheMockGetByIDExpectation specifies expectation struct of the CategoryPermissionCache.GetByID
type CategoryPermissionCacheMockGetByIDExpectation struct {
	mock               *CategoryPermissionCacheMock
	params             *CategoryPermissionCacheMockGetByIDParams
	paramPtrs          *CategoryPermissionCacheMockGetByIDParamPtrs
	expectationOrigins CategoryPermissionCacheMockGetByIDExpectationOrigins
	results            *CategoryPermissionCacheMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionCacheMockGetByIDParams contains parameters of the CategoryPermissionCache.GetByID
type CategoryPermissionCacheMockGetByIDParams struct {
	id int64
}

// CategoryPermissionCacheMockGetByIDParamPtrs contains pointers to parameters of the CategoryPermissionCache.GetByID
type CategoryPermissionCacheMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryPermissionCacheMockGetByIDResults contains results of the CategoryPermissionCache.GetByID
type CategoryPermissionCacheMockGetByIDResults struct {
	c1  categoryentity.CategoryPermission
	err error
}

// CategoryPermissionCacheMockGetByIDOrigins contains origins of expectations of the CategoryPermissionCache.GetByID
type CategoryPermissionCacheMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Optional() *mCategoryPermissionCacheMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryPermissionCache.GetByID
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Expect(id int64) *mCategoryPermissionCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPermissionCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryPermissionCacheMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryPermissionCache.GetByID
func (mmGetByID *mCategoryPermissionCacheMockGetByID) ExpectIdParam1(id int64) *mCategoryPermissionCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPermissionCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryPermissionCacheMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionCache.GetByID
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Inspect(f func(id int64)) *mCategoryPermissionCacheMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryPermissionCacheMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryPermissionCache.GetByID
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Return(c1 categoryentity.CategoryPermission, err error) *CategoryPermissionCacheMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryPermissionCacheMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryPermissionCacheMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryPermissionCache.GetByID method
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Set(f func(id int64) (c1 categoryentity.CategoryPermission, err error)) *CategoryPermissionCacheMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionCache.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionCache.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryPermissionCache.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryPermissionCacheMockGetByID) When(id int64) *CategoryPermissionCacheMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryPermissionCacheMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryPermissionCacheMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryPermissionCacheMockGetByIDParams{id},
		expectationOrigins: CategoryPermissionCacheMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionCache.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionCacheMockGetByIDExpectation) Then(c1 categoryentity.CategoryPermission, err error) *CategoryPermissionCacheMock {
	e.results = &CategoryPermissionCacheMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryPermissionCache.GetByID should be invoked
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Times(n uint64) *mCategoryPermissionCacheMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryPermissionCacheMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryPermissionCacheMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryPermissionCache
func (mmGetByID *CategoryPermissionCacheMock) GetByID(id int64) (c1 categoryentity.CategoryPermission, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryPermissionCacheMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionCacheMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryPermissionCacheMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryPermissionCacheMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryPermissionCacheMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryPermissionCacheMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryPermissionCacheMock.GetByID invocations
func (mmGetByID *CategoryPermissionCacheMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryPermissionCacheMock.GetByID invocations
func (mmGetByID *CategoryPermissionCacheMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionCacheMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryPermissionCacheMockGetByID) Calls() []*CategoryPermissionCacheMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryPermissionCacheMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionCacheMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryPermissionCacheMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionCacheMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionCacheMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryPermissionCacheMockSet struct {
	optional           bool
	mock               *CategoryPermissionCacheMock
	defaultExpectation *CategoryPermissionCacheMockSetExpectation
	expectations       []*CategoryPermissionCacheMockSetExpectation

	callArgs []*CategoryPermissionCacheMockSetParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryPermissionCacheMockSetExpectation specifies expectation struct of the CategoryPermissionCache.Set
type CategoryPermissionCacheMockSetExpectation struct {
	mock               *CategoryPermissionCacheMock
	params             *CategoryPermissionCacheMockSetParams
	paramPtrs          *CategoryPermissionCacheMockSetParamPtrs
	expectationOrigins CategoryPermissionCacheMockSetExpectationOrigins
	results            *CategoryPermissionCacheMockSetResults
	returnOrigin       string
	Counter            uint64
}

// CategoryPermissionCacheMockSetParams contains parameters of the CategoryPermissionCache.Set
type CategoryPermissionCacheMockSetParams struct {
	categoryPermission categoryentity.CategoryPermission
}

// CategoryPermissionCacheMockSetParamPtrs contains pointers to parameters of the CategoryPermissionCache.Set
type CategoryPermissionCacheMockSetParamPtrs struct {
	categoryPermission *categoryentity.CategoryPermission
}

// CategoryPermissionCacheMockSetResults contains results of the CategoryPermissionCache.Set
type CategoryPermissionCacheMockSetResults struct {
	err error
}

// CategoryPermissionCacheMockSetOrigins contains origins of expectations of the CategoryPermissionCache.Set
type CategoryPermissionCacheMockSetExpectationOrigins struct {
	origin                   string
	originCategoryPermission string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSet *mCategoryPermissionCacheMockSet) Optional() *mCategoryPermissionCacheMockSet {
	mmSet.optional = true
	return mmSet
}

// Expect sets up expected params for CategoryPermissionCache.Set
func (mmSet *mCategoryPermissionCacheMockSet) Expect(categoryPermission categoryentity.CategoryPermission) *mCategoryPermissionCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryPermissionCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryPermissionCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.paramPtrs != nil {
		mmSet.mock.t.Fatalf("CategoryPermissionCacheMock.Set mock is already set by ExpectParams functions")
	}

	mmSet.defaultExpectation.params = &CategoryPermissionCacheMockSetParams{categoryPermission}
	mmSet.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSet.expectations {
		if minimock.Equal(e.params, mmSet.defaultExpectation.params) {
			mmSet.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSet.defaultExpectation.params)
		}
	}

	return mmSet
}

// ExpectCategoryPermissionParam1 sets up expected param categoryPermission for CategoryPermissionCache.Set
func (mmSet *mCategoryPermissionCacheMockSet) ExpectCategoryPermissionParam1(categoryPermission categoryentity.CategoryPermission) *mCategoryPermissionCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryPermissionCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryPermissionCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.params != nil {
		mmSet.mock.t.Fatalf("CategoryPermissionCacheMock.Set mock is already set by Expect")
	}

	if mmSet.defaultExpectation.paramPtrs == nil {
		mmSet.defaultExpectation.paramPtrs = &CategoryPermissionCacheMockSetParamPtrs{}
	}
	mmSet.defaultExpectation.paramPtrs.categoryPermission = &categoryPermission
	mmSet.defaultExpectation.expectationOrigins.originCategoryPermission = minimock.CallerInfo(1)

	return mmSet
}

// Inspect accepts an inspector function that has same arguments as the CategoryPermissionCache.Set
func (mmSet *mCategoryPermissionCacheMockSet) Inspect(f func(categoryPermission categoryentity.CategoryPermission)) *mCategoryPermissionCacheMockSet {
	if mmSet.mock.inspectFuncSet != nil {
		mmSet.mock.t.Fatalf("Inspect function is already set for CategoryPermissionCacheMock.Set")
	}

	mmSet.mock.inspectFuncSet = f

	return mmSet
}

// Return sets up results that will be returned by CategoryPermissionCache.Set
func (mmSet *mCategoryPermissionCacheMockSet) Return(err error) *CategoryPermissionCacheMock {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryPermissionCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryPermissionCacheMockSetExpectation{mock: mmSet.mock}
	}
	mmSet.defaultExpectation.results = &CategoryPermissionCacheMockSetResults{err}
	mmSet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// Set uses given function f to mock the CategoryPermissionCache.Set method
func (mmSet *mCategoryPermissionCacheMockSet) Set(f func(categoryPermission categoryentity.CategoryPermission) (err error)) *CategoryPermissionCacheMock {
	if mmSet.defaultExpectation != nil {
		mmSet.mock.t.Fatalf("Default expectation is already set for the CategoryPermissionCache.Set method")
	}

	if len(mmSet.expectations) > 0 {
		mmSet.mock.t.Fatalf("Some expectations are already set for the CategoryPermissionCache.Set method")
	}

	mmSet.mock.funcSet = f
	mmSet.mock.funcSetOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// When sets expectation for the CategoryPermissionCache.Set which will trigger the result defined by the following
// Then helper
func (mmSet *mCategoryPermissionCacheMockSet) When(categoryPermission categoryentity.CategoryPermission) *CategoryPermissionCacheMockSetExpectation {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryPermissionCacheMock.Set mock is already set by Set")
	}

	expectation := &CategoryPermissionCacheMockSetExpectation{
		mock:               mmSet.mock,
		params:             &CategoryPermissionCacheMockSetParams{categoryPermission},
		expectationOrigins: CategoryPermissionCacheMockSetExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSet.expectations = append(mmSet.expectations, expectation)
	return expectation
}

// Then sets up CategoryPermissionCache.Set return parameters for the expectation previously defined by the When method
func (e *CategoryPermissionCacheMockSetExpectation) Then(err error) *CategoryPermissionCacheMock {
	e.results = &CategoryPermissionCacheMockSetResults{err}
	return e.mock
}

// Times sets number of times CategoryPermissionCache.Set should be invoked
func (mmSet *mCategoryPermissionCacheMockSet) Times(n uint64) *mCategoryPermissionCacheMockSet {
	if n == 0 {
		mmSet.mock.t.Fatalf("Times of CategoryPermissionCacheMock.Set mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSet.expectedInvocations, n)
	mmSet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSet
}

func (mmSet *mCategoryPermissionCacheMockSet) invocationsDone() bool {
	if len(mmSet.expectations) == 0 && mmSet.defaultExpectation == nil && mmSet.mock.funcSet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSet.mock.afterSetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Set implements mm_repository.CategoryPermissionCache
func (mmSet *CategoryPermissionCacheMock) Set(categoryPermission categoryentity.CategoryPermission) (err error) {
	mm_atomic.AddUint64(&mmSet.beforeSetCounter, 1)
	defer mm_atomic.AddUint64(&mmSet.afterSetCounter, 1)

	mmSet.t.Helper()

	if mmSet.inspectFuncSet != nil {
		mmSet.inspectFuncSet(categoryPermission)
	}

	mm_params := CategoryPermissionCacheMockSetParams{categoryPermission}

	// Record call args
	mmSet.SetMock.mutex.Lock()
	mmSet.SetMock.callArgs = append(mmSet.SetMock.callArgs, &mm_params)
	mmSet.SetMock.mutex.Unlock()

	for _, e := range mmSet.SetMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSet.SetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSet.SetMock.defaultExpectation.Counter, 1)
		mm_want := mmSet.SetMock.defaultExpectation.params
		mm_want_ptrs := mmSet.SetMock.defaultExpectation.paramPtrs

		mm_got := CategoryPermissionCacheMockSetParams{categoryPermission}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryPermission != nil && !minimock.Equal(*mm_want_ptrs.categoryPermission, mm_got.categoryPermission) {
				mmSet.t.Errorf("CategoryPermissionCacheMock.Set got unexpected parameter categoryPermission, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSet.SetMock.defaultExpectation.expectationOrigins.originCategoryPermission, *mm_want_ptrs.categoryPermission, mm_got.categoryPermission, minimock.Diff(*mm_want_ptrs.categoryPermission, mm_got.categoryPermission))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSet.t.Errorf("CategoryPermissionCacheMock.Set got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSet.SetMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSet.SetMock.defaultExpectation.results
		if mm_results == nil {
			mmSet.t.Fatal("No results are set for the CategoryPermissionCacheMock.Set")
		}
		return (*mm_results).err
	}
	if mmSet.funcSet != nil {
		return mmSet.funcSet(categoryPermission)
	}
	mmSet.t.Fatalf("Unexpected call to CategoryPermissionCacheMock.Set. %v", categoryPermission)
	return
}

// SetAfterCounter returns a count of finished CategoryPermissionCacheMock.Set invocations
func (mmSet *CategoryPermissionCacheMock) SetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.afterSetCounter)
}

// SetBeforeCounter returns a count of CategoryPermissionCacheMock.Set invocations
func (mmSet *CategoryPermissionCacheMock) SetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.beforeSetCounter)
}

// Calls returns a list of arguments used in each call to CategoryPermissionCacheMock.Set.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSet *mCategoryPermissionCacheMockSet) Calls() []*CategoryPermissionCacheMockSetParams {
	mmSet.mutex.RLock()

	argCopy := make([]*CategoryPermissionCacheMockSetParams, len(mmSet.callArgs))
	copy(argCopy, mmSet.callArgs)

	mmSet.mutex.RUnlock()

	return argCopy
}

// MinimockSetDone returns true if the count of the Set invocations corresponds
// the number of defined expectations
func (m *CategoryPermissionCacheMock) MinimockSetDone() bool {
	if m.SetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetMock.invocationsDone()
}

// MinimockSetInspect logs each unmet expectation
func (m *CategoryPermissionCacheMock) MinimockSetInspect() {
	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.Set at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCounter := mm_atomic.LoadUint64(&m.afterSetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetMock.defaultExpectation != nil && afterSetCounter < 1 {
		if m.SetMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.Set at\n%s", m.SetMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryPermissionCacheMock.Set at\n%s with params: %#v", m.SetMock.defaultExpectation.expectationOrigins.origin, *m.SetMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSet != nil && afterSetCounter < 1 {
		m.t.Errorf("Expected call to CategoryPermissionCacheMock.Set at\n%s", m.funcSetOrigin)
	}

	if !m.SetMock.invocationsDone() && afterSetCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryPermissionCacheMock.Set at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetMock.expectedInvocations), m.SetMock.expectedInvocationsOrigin, afterSetCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryPermissionCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetAllInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockSetInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryPermissionCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryPermissionCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockSetDone()
}
