// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryGroupPrimeDB -o category_group_prime_db_mock.go -n CategoryGroupPrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryGroupPrimeDBMock implements mm_repository.CategoryGroupPrimeDB
type CategoryGroupPrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(categoryGroup categoryentity.CategoryGroupLink) (c1 categoryentity.CategoryGroupLink, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(categoryGroup categoryentity.CategoryGroupLink)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mCategoryGroupPrimeDBMockCreate

	funcDelete          func(categoryID int64, groupID int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(categoryID int64, groupID int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mCategoryGroupPrimeDBMockDelete

	funcGetAll          func() (ca1 []categoryentity.CategoryGroupLink, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryGroupPrimeDBMockGetAll

	funcGetByCategoryID          func(categoryID int64) (ca1 []categoryentity.CategoryGroupLink, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mCategoryGroupPrimeDBMockGetByCategoryID

	funcGetByID          func(id int64) (c1 categoryentity.CategoryGroupLink, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryGroupPrimeDBMockGetByID

	funcHasCategoryGroup          func(categoryID int64, groupID int64) (b1 bool, err error)
	funcHasCategoryGroupOrigin    string
	inspectFuncHasCategoryGroup   func(categoryID int64, groupID int64)
	afterHasCategoryGroupCounter  uint64
	beforeHasCategoryGroupCounter uint64
	HasCategoryGroupMock          mCategoryGroupPrimeDBMockHasCategoryGroup
}

// NewCategoryGroupPrimeDBMock returns a mock for mm_repository.CategoryGroupPrimeDB
func NewCategoryGroupPrimeDBMock(t minimock.Tester) *CategoryGroupPrimeDBMock {
	m := &CategoryGroupPrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mCategoryGroupPrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*CategoryGroupPrimeDBMockCreateParams{}

	m.DeleteMock = mCategoryGroupPrimeDBMockDelete{mock: m}
	m.DeleteMock.callArgs = []*CategoryGroupPrimeDBMockDeleteParams{}

	m.GetAllMock = mCategoryGroupPrimeDBMockGetAll{mock: m}

	m.GetByCategoryIDMock = mCategoryGroupPrimeDBMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*CategoryGroupPrimeDBMockGetByCategoryIDParams{}

	m.GetByIDMock = mCategoryGroupPrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryGroupPrimeDBMockGetByIDParams{}

	m.HasCategoryGroupMock = mCategoryGroupPrimeDBMockHasCategoryGroup{mock: m}
	m.HasCategoryGroupMock.callArgs = []*CategoryGroupPrimeDBMockHasCategoryGroupParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryGroupPrimeDBMockCreate struct {
	optional           bool
	mock               *CategoryGroupPrimeDBMock
	defaultExpectation *CategoryGroupPrimeDBMockCreateExpectation
	expectations       []*CategoryGroupPrimeDBMockCreateExpectation

	callArgs []*CategoryGroupPrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupPrimeDBMockCreateExpectation specifies expectation struct of the CategoryGroupPrimeDB.Create
type CategoryGroupPrimeDBMockCreateExpectation struct {
	mock               *CategoryGroupPrimeDBMock
	params             *CategoryGroupPrimeDBMockCreateParams
	paramPtrs          *CategoryGroupPrimeDBMockCreateParamPtrs
	expectationOrigins CategoryGroupPrimeDBMockCreateExpectationOrigins
	results            *CategoryGroupPrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupPrimeDBMockCreateParams contains parameters of the CategoryGroupPrimeDB.Create
type CategoryGroupPrimeDBMockCreateParams struct {
	categoryGroup categoryentity.CategoryGroupLink
}

// CategoryGroupPrimeDBMockCreateParamPtrs contains pointers to parameters of the CategoryGroupPrimeDB.Create
type CategoryGroupPrimeDBMockCreateParamPtrs struct {
	categoryGroup *categoryentity.CategoryGroupLink
}

// CategoryGroupPrimeDBMockCreateResults contains results of the CategoryGroupPrimeDB.Create
type CategoryGroupPrimeDBMockCreateResults struct {
	c1  categoryentity.CategoryGroupLink
	err error
}

// CategoryGroupPrimeDBMockCreateOrigins contains origins of expectations of the CategoryGroupPrimeDB.Create
type CategoryGroupPrimeDBMockCreateExpectationOrigins struct {
	origin              string
	originCategoryGroup string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Optional() *mCategoryGroupPrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for CategoryGroupPrimeDB.Create
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Expect(categoryGroup categoryentity.CategoryGroupLink) *mCategoryGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("CategoryGroupPrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &CategoryGroupPrimeDBMockCreateParams{categoryGroup}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCategoryGroupParam1 sets up expected param categoryGroup for CategoryGroupPrimeDB.Create
func (mmCreate *mCategoryGroupPrimeDBMockCreate) ExpectCategoryGroupParam1(categoryGroup categoryentity.CategoryGroupLink) *mCategoryGroupPrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryGroupPrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("CategoryGroupPrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.categoryGroup = &categoryGroup
	mmCreate.defaultExpectation.expectationOrigins.originCategoryGroup = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupPrimeDB.Create
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Inspect(f func(categoryGroup categoryentity.CategoryGroupLink)) *mCategoryGroupPrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for CategoryGroupPrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by CategoryGroupPrimeDB.Create
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Return(c1 categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryGroupPrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryGroupPrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &CategoryGroupPrimeDBMockCreateResults{c1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the CategoryGroupPrimeDB.Create method
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Set(f func(categoryGroup categoryentity.CategoryGroupLink) (c1 categoryentity.CategoryGroupLink, err error)) *CategoryGroupPrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the CategoryGroupPrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the CategoryGroupPrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the CategoryGroupPrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mCategoryGroupPrimeDBMockCreate) When(categoryGroup categoryentity.CategoryGroupLink) *CategoryGroupPrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryGroupPrimeDBMock.Create mock is already set by Set")
	}

	expectation := &CategoryGroupPrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &CategoryGroupPrimeDBMockCreateParams{categoryGroup},
		expectationOrigins: CategoryGroupPrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupPrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *CategoryGroupPrimeDBMockCreateExpectation) Then(c1 categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	e.results = &CategoryGroupPrimeDBMockCreateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryGroupPrimeDB.Create should be invoked
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Times(n uint64) *mCategoryGroupPrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of CategoryGroupPrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mCategoryGroupPrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.CategoryGroupPrimeDB
func (mmCreate *CategoryGroupPrimeDBMock) Create(categoryGroup categoryentity.CategoryGroupLink) (c1 categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(categoryGroup)
	}

	mm_params := CategoryGroupPrimeDBMockCreateParams{categoryGroup}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupPrimeDBMockCreateParams{categoryGroup}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryGroup != nil && !minimock.Equal(*mm_want_ptrs.categoryGroup, mm_got.categoryGroup) {
				mmCreate.t.Errorf("CategoryGroupPrimeDBMock.Create got unexpected parameter categoryGroup, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCategoryGroup, *mm_want_ptrs.categoryGroup, mm_got.categoryGroup, minimock.Diff(*mm_want_ptrs.categoryGroup, mm_got.categoryGroup))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("CategoryGroupPrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the CategoryGroupPrimeDBMock.Create")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(categoryGroup)
	}
	mmCreate.t.Fatalf("Unexpected call to CategoryGroupPrimeDBMock.Create. %v", categoryGroup)
	return
}

// CreateAfterCounter returns a count of finished CategoryGroupPrimeDBMock.Create invocations
func (mmCreate *CategoryGroupPrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of CategoryGroupPrimeDBMock.Create invocations
func (mmCreate *CategoryGroupPrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupPrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mCategoryGroupPrimeDBMockCreate) Calls() []*CategoryGroupPrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*CategoryGroupPrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *CategoryGroupPrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *CategoryGroupPrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupPrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mCategoryGroupPrimeDBMockDelete struct {
	optional           bool
	mock               *CategoryGroupPrimeDBMock
	defaultExpectation *CategoryGroupPrimeDBMockDeleteExpectation
	expectations       []*CategoryGroupPrimeDBMockDeleteExpectation

	callArgs []*CategoryGroupPrimeDBMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupPrimeDBMockDeleteExpectation specifies expectation struct of the CategoryGroupPrimeDB.Delete
type CategoryGroupPrimeDBMockDeleteExpectation struct {
	mock               *CategoryGroupPrimeDBMock
	params             *CategoryGroupPrimeDBMockDeleteParams
	paramPtrs          *CategoryGroupPrimeDBMockDeleteParamPtrs
	expectationOrigins CategoryGroupPrimeDBMockDeleteExpectationOrigins
	results            *CategoryGroupPrimeDBMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupPrimeDBMockDeleteParams contains parameters of the CategoryGroupPrimeDB.Delete
type CategoryGroupPrimeDBMockDeleteParams struct {
	categoryID int64
	groupID    int64
}

// CategoryGroupPrimeDBMockDeleteParamPtrs contains pointers to parameters of the CategoryGroupPrimeDB.Delete
type CategoryGroupPrimeDBMockDeleteParamPtrs struct {
	categoryID *int64
	groupID    *int64
}

// CategoryGroupPrimeDBMockDeleteResults contains results of the CategoryGroupPrimeDB.Delete
type CategoryGroupPrimeDBMockDeleteResults struct {
	err error
}

// CategoryGroupPrimeDBMockDeleteOrigins contains origins of expectations of the CategoryGroupPrimeDB.Delete
type CategoryGroupPrimeDBMockDeleteExpectationOrigins struct {
	origin           string
	originCategoryID string
	originGroupID    string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Optional() *mCategoryGroupPrimeDBMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for CategoryGroupPrimeDB.Delete
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Expect(categoryID int64, groupID int64) *mCategoryGroupPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryGroupPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &CategoryGroupPrimeDBMockDeleteParams{categoryID, groupID}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryGroupPrimeDB.Delete
func (mmDelete *mCategoryGroupPrimeDBMockDelete) ExpectCategoryIDParam1(categoryID int64) *mCategoryGroupPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryGroupPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmDelete.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectGroupIDParam2 sets up expected param groupID for CategoryGroupPrimeDB.Delete
func (mmDelete *mCategoryGroupPrimeDBMockDelete) ExpectGroupIDParam2(groupID int64) *mCategoryGroupPrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryGroupPrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.groupID = &groupID
	mmDelete.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupPrimeDB.Delete
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Inspect(f func(categoryID int64, groupID int64)) *mCategoryGroupPrimeDBMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for CategoryGroupPrimeDBMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by CategoryGroupPrimeDB.Delete
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Return(err error) *CategoryGroupPrimeDBMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryGroupPrimeDBMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &CategoryGroupPrimeDBMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the CategoryGroupPrimeDB.Delete method
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Set(f func(categoryID int64, groupID int64) (err error)) *CategoryGroupPrimeDBMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the CategoryGroupPrimeDB.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the CategoryGroupPrimeDB.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the CategoryGroupPrimeDB.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mCategoryGroupPrimeDBMockDelete) When(categoryID int64, groupID int64) *CategoryGroupPrimeDBMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryGroupPrimeDBMock.Delete mock is already set by Set")
	}

	expectation := &CategoryGroupPrimeDBMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &CategoryGroupPrimeDBMockDeleteParams{categoryID, groupID},
		expectationOrigins: CategoryGroupPrimeDBMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupPrimeDB.Delete return parameters for the expectation previously defined by the When method
func (e *CategoryGroupPrimeDBMockDeleteExpectation) Then(err error) *CategoryGroupPrimeDBMock {
	e.results = &CategoryGroupPrimeDBMockDeleteResults{err}
	return e.mock
}

// Times sets number of times CategoryGroupPrimeDB.Delete should be invoked
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Times(n uint64) *mCategoryGroupPrimeDBMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of CategoryGroupPrimeDBMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mCategoryGroupPrimeDBMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_repository.CategoryGroupPrimeDB
func (mmDelete *CategoryGroupPrimeDBMock) Delete(categoryID int64, groupID int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(categoryID, groupID)
	}

	mm_params := CategoryGroupPrimeDBMockDeleteParams{categoryID, groupID}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupPrimeDBMockDeleteParams{categoryID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmDelete.t.Errorf("CategoryGroupPrimeDBMock.Delete got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmDelete.t.Errorf("CategoryGroupPrimeDBMock.Delete got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("CategoryGroupPrimeDBMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the CategoryGroupPrimeDBMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(categoryID, groupID)
	}
	mmDelete.t.Fatalf("Unexpected call to CategoryGroupPrimeDBMock.Delete. %v %v", categoryID, groupID)
	return
}

// DeleteAfterCounter returns a count of finished CategoryGroupPrimeDBMock.Delete invocations
func (mmDelete *CategoryGroupPrimeDBMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of CategoryGroupPrimeDBMock.Delete invocations
func (mmDelete *CategoryGroupPrimeDBMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupPrimeDBMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mCategoryGroupPrimeDBMockDelete) Calls() []*CategoryGroupPrimeDBMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*CategoryGroupPrimeDBMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *CategoryGroupPrimeDBMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *CategoryGroupPrimeDBMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupPrimeDBMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mCategoryGroupPrimeDBMockGetAll struct {
	optional           bool
	mock               *CategoryGroupPrimeDBMock
	defaultExpectation *CategoryGroupPrimeDBMockGetAllExpectation
	expectations       []*CategoryGroupPrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupPrimeDBMockGetAllExpectation specifies expectation struct of the CategoryGroupPrimeDB.GetAll
type CategoryGroupPrimeDBMockGetAllExpectation struct {
	mock *CategoryGroupPrimeDBMock

	results      *CategoryGroupPrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryGroupPrimeDBMockGetAllResults contains results of the CategoryGroupPrimeDB.GetAll
type CategoryGroupPrimeDBMockGetAllResults struct {
	ca1 []categoryentity.CategoryGroupLink
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) Optional() *mCategoryGroupPrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryGroupPrimeDB.GetAll
func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) Expect() *mCategoryGroupPrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryGroupPrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupPrimeDB.GetAll
func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) Inspect(f func()) *mCategoryGroupPrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryGroupPrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryGroupPrimeDB.GetAll
func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) Return(ca1 []categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryGroupPrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryGroupPrimeDBMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryGroupPrimeDB.GetAll method
func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) Set(f func() (ca1 []categoryentity.CategoryGroupLink, err error)) *CategoryGroupPrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryGroupPrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryGroupPrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryGroupPrimeDB.GetAll should be invoked
func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) Times(n uint64) *mCategoryGroupPrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryGroupPrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryGroupPrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryGroupPrimeDB
func (mmGetAll *CategoryGroupPrimeDBMock) GetAll() (ca1 []categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryGroupPrimeDBMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryGroupPrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryGroupPrimeDBMock.GetAll invocations
func (mmGetAll *CategoryGroupPrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryGroupPrimeDBMock.GetAll invocations
func (mmGetAll *CategoryGroupPrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryGroupPrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryGroupPrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryGroupPrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupPrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryGroupPrimeDBMockGetByCategoryID struct {
	optional           bool
	mock               *CategoryGroupPrimeDBMock
	defaultExpectation *CategoryGroupPrimeDBMockGetByCategoryIDExpectation
	expectations       []*CategoryGroupPrimeDBMockGetByCategoryIDExpectation

	callArgs []*CategoryGroupPrimeDBMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupPrimeDBMockGetByCategoryIDExpectation specifies expectation struct of the CategoryGroupPrimeDB.GetByCategoryID
type CategoryGroupPrimeDBMockGetByCategoryIDExpectation struct {
	mock               *CategoryGroupPrimeDBMock
	params             *CategoryGroupPrimeDBMockGetByCategoryIDParams
	paramPtrs          *CategoryGroupPrimeDBMockGetByCategoryIDParamPtrs
	expectationOrigins CategoryGroupPrimeDBMockGetByCategoryIDExpectationOrigins
	results            *CategoryGroupPrimeDBMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupPrimeDBMockGetByCategoryIDParams contains parameters of the CategoryGroupPrimeDB.GetByCategoryID
type CategoryGroupPrimeDBMockGetByCategoryIDParams struct {
	categoryID int64
}

// CategoryGroupPrimeDBMockGetByCategoryIDParamPtrs contains pointers to parameters of the CategoryGroupPrimeDB.GetByCategoryID
type CategoryGroupPrimeDBMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// CategoryGroupPrimeDBMockGetByCategoryIDResults contains results of the CategoryGroupPrimeDB.GetByCategoryID
type CategoryGroupPrimeDBMockGetByCategoryIDResults struct {
	ca1 []categoryentity.CategoryGroupLink
	err error
}

// CategoryGroupPrimeDBMockGetByCategoryIDOrigins contains origins of expectations of the CategoryGroupPrimeDB.GetByCategoryID
type CategoryGroupPrimeDBMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Optional() *mCategoryGroupPrimeDBMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for CategoryGroupPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Expect(categoryID int64) *mCategoryGroupPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryGroupPrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &CategoryGroupPrimeDBMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryGroupPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mCategoryGroupPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryGroupPrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Inspect(f func(categoryID int64)) *mCategoryGroupPrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for CategoryGroupPrimeDBMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by CategoryGroupPrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Return(ca1 []categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryGroupPrimeDBMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &CategoryGroupPrimeDBMockGetByCategoryIDResults{ca1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the CategoryGroupPrimeDB.GetByCategoryID method
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Set(f func(categoryID int64) (ca1 []categoryentity.CategoryGroupLink, err error)) *CategoryGroupPrimeDBMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the CategoryGroupPrimeDB.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the CategoryGroupPrimeDB.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the CategoryGroupPrimeDB.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) When(categoryID int64) *CategoryGroupPrimeDBMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &CategoryGroupPrimeDBMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &CategoryGroupPrimeDBMockGetByCategoryIDParams{categoryID},
		expectationOrigins: CategoryGroupPrimeDBMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupPrimeDB.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *CategoryGroupPrimeDBMockGetByCategoryIDExpectation) Then(ca1 []categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	e.results = &CategoryGroupPrimeDBMockGetByCategoryIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryGroupPrimeDB.GetByCategoryID should be invoked
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Times(n uint64) *mCategoryGroupPrimeDBMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of CategoryGroupPrimeDBMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.CategoryGroupPrimeDB
func (mmGetByCategoryID *CategoryGroupPrimeDBMock) GetByCategoryID(categoryID int64) (ca1 []categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := CategoryGroupPrimeDBMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupPrimeDBMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("CategoryGroupPrimeDBMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("CategoryGroupPrimeDBMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the CategoryGroupPrimeDBMock.GetByCategoryID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to CategoryGroupPrimeDBMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished CategoryGroupPrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryGroupPrimeDBMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of CategoryGroupPrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryGroupPrimeDBMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupPrimeDBMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mCategoryGroupPrimeDBMockGetByCategoryID) Calls() []*CategoryGroupPrimeDBMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*CategoryGroupPrimeDBMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *CategoryGroupPrimeDBMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *CategoryGroupPrimeDBMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupPrimeDBMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mCategoryGroupPrimeDBMockGetByID struct {
	optional           bool
	mock               *CategoryGroupPrimeDBMock
	defaultExpectation *CategoryGroupPrimeDBMockGetByIDExpectation
	expectations       []*CategoryGroupPrimeDBMockGetByIDExpectation

	callArgs []*CategoryGroupPrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupPrimeDBMockGetByIDExpectation specifies expectation struct of the CategoryGroupPrimeDB.GetByID
type CategoryGroupPrimeDBMockGetByIDExpectation struct {
	mock               *CategoryGroupPrimeDBMock
	params             *CategoryGroupPrimeDBMockGetByIDParams
	paramPtrs          *CategoryGroupPrimeDBMockGetByIDParamPtrs
	expectationOrigins CategoryGroupPrimeDBMockGetByIDExpectationOrigins
	results            *CategoryGroupPrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupPrimeDBMockGetByIDParams contains parameters of the CategoryGroupPrimeDB.GetByID
type CategoryGroupPrimeDBMockGetByIDParams struct {
	id int64
}

// CategoryGroupPrimeDBMockGetByIDParamPtrs contains pointers to parameters of the CategoryGroupPrimeDB.GetByID
type CategoryGroupPrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryGroupPrimeDBMockGetByIDResults contains results of the CategoryGroupPrimeDB.GetByID
type CategoryGroupPrimeDBMockGetByIDResults struct {
	c1  categoryentity.CategoryGroupLink
	err error
}

// CategoryGroupPrimeDBMockGetByIDOrigins contains origins of expectations of the CategoryGroupPrimeDB.GetByID
type CategoryGroupPrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Optional() *mCategoryGroupPrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryGroupPrimeDB.GetByID
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Expect(id int64) *mCategoryGroupPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryGroupPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryGroupPrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryGroupPrimeDB.GetByID
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) ExpectIdParam1(id int64) *mCategoryGroupPrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryGroupPrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupPrimeDB.GetByID
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Inspect(f func(id int64)) *mCategoryGroupPrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryGroupPrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryGroupPrimeDB.GetByID
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Return(c1 categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryGroupPrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryGroupPrimeDBMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryGroupPrimeDB.GetByID method
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Set(f func(id int64) (c1 categoryentity.CategoryGroupLink, err error)) *CategoryGroupPrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryGroupPrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryGroupPrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryGroupPrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) When(id int64) *CategoryGroupPrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryGroupPrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryGroupPrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryGroupPrimeDBMockGetByIDParams{id},
		expectationOrigins: CategoryGroupPrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupPrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryGroupPrimeDBMockGetByIDExpectation) Then(c1 categoryentity.CategoryGroupLink, err error) *CategoryGroupPrimeDBMock {
	e.results = &CategoryGroupPrimeDBMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryGroupPrimeDB.GetByID should be invoked
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Times(n uint64) *mCategoryGroupPrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryGroupPrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryGroupPrimeDB
func (mmGetByID *CategoryGroupPrimeDBMock) GetByID(id int64) (c1 categoryentity.CategoryGroupLink, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryGroupPrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupPrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryGroupPrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryGroupPrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryGroupPrimeDBMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryGroupPrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryGroupPrimeDBMock.GetByID invocations
func (mmGetByID *CategoryGroupPrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryGroupPrimeDBMock.GetByID invocations
func (mmGetByID *CategoryGroupPrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupPrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryGroupPrimeDBMockGetByID) Calls() []*CategoryGroupPrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryGroupPrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryGroupPrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryGroupPrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupPrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryGroupPrimeDBMockHasCategoryGroup struct {
	optional           bool
	mock               *CategoryGroupPrimeDBMock
	defaultExpectation *CategoryGroupPrimeDBMockHasCategoryGroupExpectation
	expectations       []*CategoryGroupPrimeDBMockHasCategoryGroupExpectation

	callArgs []*CategoryGroupPrimeDBMockHasCategoryGroupParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryGroupPrimeDBMockHasCategoryGroupExpectation specifies expectation struct of the CategoryGroupPrimeDB.HasCategoryGroup
type CategoryGroupPrimeDBMockHasCategoryGroupExpectation struct {
	mock               *CategoryGroupPrimeDBMock
	params             *CategoryGroupPrimeDBMockHasCategoryGroupParams
	paramPtrs          *CategoryGroupPrimeDBMockHasCategoryGroupParamPtrs
	expectationOrigins CategoryGroupPrimeDBMockHasCategoryGroupExpectationOrigins
	results            *CategoryGroupPrimeDBMockHasCategoryGroupResults
	returnOrigin       string
	Counter            uint64
}

// CategoryGroupPrimeDBMockHasCategoryGroupParams contains parameters of the CategoryGroupPrimeDB.HasCategoryGroup
type CategoryGroupPrimeDBMockHasCategoryGroupParams struct {
	categoryID int64
	groupID    int64
}

// CategoryGroupPrimeDBMockHasCategoryGroupParamPtrs contains pointers to parameters of the CategoryGroupPrimeDB.HasCategoryGroup
type CategoryGroupPrimeDBMockHasCategoryGroupParamPtrs struct {
	categoryID *int64
	groupID    *int64
}

// CategoryGroupPrimeDBMockHasCategoryGroupResults contains results of the CategoryGroupPrimeDB.HasCategoryGroup
type CategoryGroupPrimeDBMockHasCategoryGroupResults struct {
	b1  bool
	err error
}

// CategoryGroupPrimeDBMockHasCategoryGroupOrigins contains origins of expectations of the CategoryGroupPrimeDB.HasCategoryGroup
type CategoryGroupPrimeDBMockHasCategoryGroupExpectationOrigins struct {
	origin           string
	originCategoryID string
	originGroupID    string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Optional() *mCategoryGroupPrimeDBMockHasCategoryGroup {
	mmHasCategoryGroup.optional = true
	return mmHasCategoryGroup
}

// Expect sets up expected params for CategoryGroupPrimeDB.HasCategoryGroup
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Expect(categoryID int64, groupID int64) *mCategoryGroupPrimeDBMockHasCategoryGroup {
	if mmHasCategoryGroup.mock.funcHasCategoryGroup != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Set")
	}

	if mmHasCategoryGroup.defaultExpectation == nil {
		mmHasCategoryGroup.defaultExpectation = &CategoryGroupPrimeDBMockHasCategoryGroupExpectation{}
	}

	if mmHasCategoryGroup.defaultExpectation.paramPtrs != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by ExpectParams functions")
	}

	mmHasCategoryGroup.defaultExpectation.params = &CategoryGroupPrimeDBMockHasCategoryGroupParams{categoryID, groupID}
	mmHasCategoryGroup.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmHasCategoryGroup.expectations {
		if minimock.Equal(e.params, mmHasCategoryGroup.defaultExpectation.params) {
			mmHasCategoryGroup.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmHasCategoryGroup.defaultExpectation.params)
		}
	}

	return mmHasCategoryGroup
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryGroupPrimeDB.HasCategoryGroup
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) ExpectCategoryIDParam1(categoryID int64) *mCategoryGroupPrimeDBMockHasCategoryGroup {
	if mmHasCategoryGroup.mock.funcHasCategoryGroup != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Set")
	}

	if mmHasCategoryGroup.defaultExpectation == nil {
		mmHasCategoryGroup.defaultExpectation = &CategoryGroupPrimeDBMockHasCategoryGroupExpectation{}
	}

	if mmHasCategoryGroup.defaultExpectation.params != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Expect")
	}

	if mmHasCategoryGroup.defaultExpectation.paramPtrs == nil {
		mmHasCategoryGroup.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockHasCategoryGroupParamPtrs{}
	}
	mmHasCategoryGroup.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmHasCategoryGroup.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmHasCategoryGroup
}

// ExpectGroupIDParam2 sets up expected param groupID for CategoryGroupPrimeDB.HasCategoryGroup
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) ExpectGroupIDParam2(groupID int64) *mCategoryGroupPrimeDBMockHasCategoryGroup {
	if mmHasCategoryGroup.mock.funcHasCategoryGroup != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Set")
	}

	if mmHasCategoryGroup.defaultExpectation == nil {
		mmHasCategoryGroup.defaultExpectation = &CategoryGroupPrimeDBMockHasCategoryGroupExpectation{}
	}

	if mmHasCategoryGroup.defaultExpectation.params != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Expect")
	}

	if mmHasCategoryGroup.defaultExpectation.paramPtrs == nil {
		mmHasCategoryGroup.defaultExpectation.paramPtrs = &CategoryGroupPrimeDBMockHasCategoryGroupParamPtrs{}
	}
	mmHasCategoryGroup.defaultExpectation.paramPtrs.groupID = &groupID
	mmHasCategoryGroup.defaultExpectation.expectationOrigins.originGroupID = minimock.CallerInfo(1)

	return mmHasCategoryGroup
}

// Inspect accepts an inspector function that has same arguments as the CategoryGroupPrimeDB.HasCategoryGroup
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Inspect(f func(categoryID int64, groupID int64)) *mCategoryGroupPrimeDBMockHasCategoryGroup {
	if mmHasCategoryGroup.mock.inspectFuncHasCategoryGroup != nil {
		mmHasCategoryGroup.mock.t.Fatalf("Inspect function is already set for CategoryGroupPrimeDBMock.HasCategoryGroup")
	}

	mmHasCategoryGroup.mock.inspectFuncHasCategoryGroup = f

	return mmHasCategoryGroup
}

// Return sets up results that will be returned by CategoryGroupPrimeDB.HasCategoryGroup
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Return(b1 bool, err error) *CategoryGroupPrimeDBMock {
	if mmHasCategoryGroup.mock.funcHasCategoryGroup != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Set")
	}

	if mmHasCategoryGroup.defaultExpectation == nil {
		mmHasCategoryGroup.defaultExpectation = &CategoryGroupPrimeDBMockHasCategoryGroupExpectation{mock: mmHasCategoryGroup.mock}
	}
	mmHasCategoryGroup.defaultExpectation.results = &CategoryGroupPrimeDBMockHasCategoryGroupResults{b1, err}
	mmHasCategoryGroup.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmHasCategoryGroup.mock
}

// Set uses given function f to mock the CategoryGroupPrimeDB.HasCategoryGroup method
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Set(f func(categoryID int64, groupID int64) (b1 bool, err error)) *CategoryGroupPrimeDBMock {
	if mmHasCategoryGroup.defaultExpectation != nil {
		mmHasCategoryGroup.mock.t.Fatalf("Default expectation is already set for the CategoryGroupPrimeDB.HasCategoryGroup method")
	}

	if len(mmHasCategoryGroup.expectations) > 0 {
		mmHasCategoryGroup.mock.t.Fatalf("Some expectations are already set for the CategoryGroupPrimeDB.HasCategoryGroup method")
	}

	mmHasCategoryGroup.mock.funcHasCategoryGroup = f
	mmHasCategoryGroup.mock.funcHasCategoryGroupOrigin = minimock.CallerInfo(1)
	return mmHasCategoryGroup.mock
}

// When sets expectation for the CategoryGroupPrimeDB.HasCategoryGroup which will trigger the result defined by the following
// Then helper
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) When(categoryID int64, groupID int64) *CategoryGroupPrimeDBMockHasCategoryGroupExpectation {
	if mmHasCategoryGroup.mock.funcHasCategoryGroup != nil {
		mmHasCategoryGroup.mock.t.Fatalf("CategoryGroupPrimeDBMock.HasCategoryGroup mock is already set by Set")
	}

	expectation := &CategoryGroupPrimeDBMockHasCategoryGroupExpectation{
		mock:               mmHasCategoryGroup.mock,
		params:             &CategoryGroupPrimeDBMockHasCategoryGroupParams{categoryID, groupID},
		expectationOrigins: CategoryGroupPrimeDBMockHasCategoryGroupExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmHasCategoryGroup.expectations = append(mmHasCategoryGroup.expectations, expectation)
	return expectation
}

// Then sets up CategoryGroupPrimeDB.HasCategoryGroup return parameters for the expectation previously defined by the When method
func (e *CategoryGroupPrimeDBMockHasCategoryGroupExpectation) Then(b1 bool, err error) *CategoryGroupPrimeDBMock {
	e.results = &CategoryGroupPrimeDBMockHasCategoryGroupResults{b1, err}
	return e.mock
}

// Times sets number of times CategoryGroupPrimeDB.HasCategoryGroup should be invoked
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Times(n uint64) *mCategoryGroupPrimeDBMockHasCategoryGroup {
	if n == 0 {
		mmHasCategoryGroup.mock.t.Fatalf("Times of CategoryGroupPrimeDBMock.HasCategoryGroup mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmHasCategoryGroup.expectedInvocations, n)
	mmHasCategoryGroup.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmHasCategoryGroup
}

func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) invocationsDone() bool {
	if len(mmHasCategoryGroup.expectations) == 0 && mmHasCategoryGroup.defaultExpectation == nil && mmHasCategoryGroup.mock.funcHasCategoryGroup == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmHasCategoryGroup.mock.afterHasCategoryGroupCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmHasCategoryGroup.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// HasCategoryGroup implements mm_repository.CategoryGroupPrimeDB
func (mmHasCategoryGroup *CategoryGroupPrimeDBMock) HasCategoryGroup(categoryID int64, groupID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmHasCategoryGroup.beforeHasCategoryGroupCounter, 1)
	defer mm_atomic.AddUint64(&mmHasCategoryGroup.afterHasCategoryGroupCounter, 1)

	mmHasCategoryGroup.t.Helper()

	if mmHasCategoryGroup.inspectFuncHasCategoryGroup != nil {
		mmHasCategoryGroup.inspectFuncHasCategoryGroup(categoryID, groupID)
	}

	mm_params := CategoryGroupPrimeDBMockHasCategoryGroupParams{categoryID, groupID}

	// Record call args
	mmHasCategoryGroup.HasCategoryGroupMock.mutex.Lock()
	mmHasCategoryGroup.HasCategoryGroupMock.callArgs = append(mmHasCategoryGroup.HasCategoryGroupMock.callArgs, &mm_params)
	mmHasCategoryGroup.HasCategoryGroupMock.mutex.Unlock()

	for _, e := range mmHasCategoryGroup.HasCategoryGroupMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.Counter, 1)
		mm_want := mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.params
		mm_want_ptrs := mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.paramPtrs

		mm_got := CategoryGroupPrimeDBMockHasCategoryGroupParams{categoryID, groupID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmHasCategoryGroup.t.Errorf("CategoryGroupPrimeDBMock.HasCategoryGroup got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.groupID != nil && !minimock.Equal(*mm_want_ptrs.groupID, mm_got.groupID) {
				mmHasCategoryGroup.t.Errorf("CategoryGroupPrimeDBMock.HasCategoryGroup got unexpected parameter groupID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.expectationOrigins.originGroupID, *mm_want_ptrs.groupID, mm_got.groupID, minimock.Diff(*mm_want_ptrs.groupID, mm_got.groupID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmHasCategoryGroup.t.Errorf("CategoryGroupPrimeDBMock.HasCategoryGroup got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmHasCategoryGroup.HasCategoryGroupMock.defaultExpectation.results
		if mm_results == nil {
			mmHasCategoryGroup.t.Fatal("No results are set for the CategoryGroupPrimeDBMock.HasCategoryGroup")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmHasCategoryGroup.funcHasCategoryGroup != nil {
		return mmHasCategoryGroup.funcHasCategoryGroup(categoryID, groupID)
	}
	mmHasCategoryGroup.t.Fatalf("Unexpected call to CategoryGroupPrimeDBMock.HasCategoryGroup. %v %v", categoryID, groupID)
	return
}

// HasCategoryGroupAfterCounter returns a count of finished CategoryGroupPrimeDBMock.HasCategoryGroup invocations
func (mmHasCategoryGroup *CategoryGroupPrimeDBMock) HasCategoryGroupAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryGroup.afterHasCategoryGroupCounter)
}

// HasCategoryGroupBeforeCounter returns a count of CategoryGroupPrimeDBMock.HasCategoryGroup invocations
func (mmHasCategoryGroup *CategoryGroupPrimeDBMock) HasCategoryGroupBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryGroup.beforeHasCategoryGroupCounter)
}

// Calls returns a list of arguments used in each call to CategoryGroupPrimeDBMock.HasCategoryGroup.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmHasCategoryGroup *mCategoryGroupPrimeDBMockHasCategoryGroup) Calls() []*CategoryGroupPrimeDBMockHasCategoryGroupParams {
	mmHasCategoryGroup.mutex.RLock()

	argCopy := make([]*CategoryGroupPrimeDBMockHasCategoryGroupParams, len(mmHasCategoryGroup.callArgs))
	copy(argCopy, mmHasCategoryGroup.callArgs)

	mmHasCategoryGroup.mutex.RUnlock()

	return argCopy
}

// MinimockHasCategoryGroupDone returns true if the count of the HasCategoryGroup invocations corresponds
// the number of defined expectations
func (m *CategoryGroupPrimeDBMock) MinimockHasCategoryGroupDone() bool {
	if m.HasCategoryGroupMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.HasCategoryGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.HasCategoryGroupMock.invocationsDone()
}

// MinimockHasCategoryGroupInspect logs each unmet expectation
func (m *CategoryGroupPrimeDBMock) MinimockHasCategoryGroupInspect() {
	for _, e := range m.HasCategoryGroupMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.HasCategoryGroup at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterHasCategoryGroupCounter := mm_atomic.LoadUint64(&m.afterHasCategoryGroupCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.HasCategoryGroupMock.defaultExpectation != nil && afterHasCategoryGroupCounter < 1 {
		if m.HasCategoryGroupMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.HasCategoryGroup at\n%s", m.HasCategoryGroupMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.HasCategoryGroup at\n%s with params: %#v", m.HasCategoryGroupMock.defaultExpectation.expectationOrigins.origin, *m.HasCategoryGroupMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcHasCategoryGroup != nil && afterHasCategoryGroupCounter < 1 {
		m.t.Errorf("Expected call to CategoryGroupPrimeDBMock.HasCategoryGroup at\n%s", m.funcHasCategoryGroupOrigin)
	}

	if !m.HasCategoryGroupMock.invocationsDone() && afterHasCategoryGroupCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryGroupPrimeDBMock.HasCategoryGroup at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.HasCategoryGroupMock.expectedInvocations), m.HasCategoryGroupMock.expectedInvocationsOrigin, afterHasCategoryGroupCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryGroupPrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeleteInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockHasCategoryGroupInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryGroupPrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryGroupPrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockHasCategoryGroupDone()
}
