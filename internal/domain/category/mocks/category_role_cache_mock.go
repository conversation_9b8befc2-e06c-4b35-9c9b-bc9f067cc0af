// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryRoleCache -o category_role_cache_mock.go -n CategoryRoleCacheMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryRoleCacheMock implements mm_repository.CategoryRoleCache
type CategoryRoleCacheMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcGetAll          func() (ca1 []categoryentity.CategoryRoleLink, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryRoleCacheMockGetAll

	funcGetByCategoryID          func(categoryID int64) (ca1 []categoryentity.CategoryRoleLink, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mCategoryRoleCacheMockGetByCategoryID

	funcGetByID          func(id int64) (c1 categoryentity.CategoryRoleLink, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryRoleCacheMockGetByID

	funcSet          func(categoryRole categoryentity.CategoryRoleLink) (err error)
	funcSetOrigin    string
	inspectFuncSet   func(categoryRole categoryentity.CategoryRoleLink)
	afterSetCounter  uint64
	beforeSetCounter uint64
	SetMock          mCategoryRoleCacheMockSet
}

// NewCategoryRoleCacheMock returns a mock for mm_repository.CategoryRoleCache
func NewCategoryRoleCacheMock(t minimock.Tester) *CategoryRoleCacheMock {
	m := &CategoryRoleCacheMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.GetAllMock = mCategoryRoleCacheMockGetAll{mock: m}

	m.GetByCategoryIDMock = mCategoryRoleCacheMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*CategoryRoleCacheMockGetByCategoryIDParams{}

	m.GetByIDMock = mCategoryRoleCacheMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryRoleCacheMockGetByIDParams{}

	m.SetMock = mCategoryRoleCacheMockSet{mock: m}
	m.SetMock.callArgs = []*CategoryRoleCacheMockSetParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryRoleCacheMockGetAll struct {
	optional           bool
	mock               *CategoryRoleCacheMock
	defaultExpectation *CategoryRoleCacheMockGetAllExpectation
	expectations       []*CategoryRoleCacheMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRoleCacheMockGetAllExpectation specifies expectation struct of the CategoryRoleCache.GetAll
type CategoryRoleCacheMockGetAllExpectation struct {
	mock *CategoryRoleCacheMock

	results      *CategoryRoleCacheMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryRoleCacheMockGetAllResults contains results of the CategoryRoleCache.GetAll
type CategoryRoleCacheMockGetAllResults struct {
	ca1 []categoryentity.CategoryRoleLink
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryRoleCacheMockGetAll) Optional() *mCategoryRoleCacheMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryRoleCache.GetAll
func (mmGetAll *mCategoryRoleCacheMockGetAll) Expect() *mCategoryRoleCacheMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryRoleCacheMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryRoleCacheMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryRoleCache.GetAll
func (mmGetAll *mCategoryRoleCacheMockGetAll) Inspect(f func()) *mCategoryRoleCacheMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryRoleCacheMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryRoleCache.GetAll
func (mmGetAll *mCategoryRoleCacheMockGetAll) Return(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRoleCacheMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryRoleCacheMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryRoleCacheMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryRoleCacheMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryRoleCache.GetAll method
func (mmGetAll *mCategoryRoleCacheMockGetAll) Set(f func() (ca1 []categoryentity.CategoryRoleLink, err error)) *CategoryRoleCacheMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryRoleCache.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryRoleCache.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryRoleCache.GetAll should be invoked
func (mmGetAll *mCategoryRoleCacheMockGetAll) Times(n uint64) *mCategoryRoleCacheMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryRoleCacheMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryRoleCacheMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryRoleCache
func (mmGetAll *CategoryRoleCacheMock) GetAll() (ca1 []categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryRoleCacheMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryRoleCacheMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryRoleCacheMock.GetAll invocations
func (mmGetAll *CategoryRoleCacheMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryRoleCacheMock.GetAll invocations
func (mmGetAll *CategoryRoleCacheMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryRoleCacheMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryRoleCacheMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryRoleCacheMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryRoleCacheMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryRoleCacheMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRoleCacheMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryRoleCacheMockGetByCategoryID struct {
	optional           bool
	mock               *CategoryRoleCacheMock
	defaultExpectation *CategoryRoleCacheMockGetByCategoryIDExpectation
	expectations       []*CategoryRoleCacheMockGetByCategoryIDExpectation

	callArgs []*CategoryRoleCacheMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRoleCacheMockGetByCategoryIDExpectation specifies expectation struct of the CategoryRoleCache.GetByCategoryID
type CategoryRoleCacheMockGetByCategoryIDExpectation struct {
	mock               *CategoryRoleCacheMock
	params             *CategoryRoleCacheMockGetByCategoryIDParams
	paramPtrs          *CategoryRoleCacheMockGetByCategoryIDParamPtrs
	expectationOrigins CategoryRoleCacheMockGetByCategoryIDExpectationOrigins
	results            *CategoryRoleCacheMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRoleCacheMockGetByCategoryIDParams contains parameters of the CategoryRoleCache.GetByCategoryID
type CategoryRoleCacheMockGetByCategoryIDParams struct {
	categoryID int64
}

// CategoryRoleCacheMockGetByCategoryIDParamPtrs contains pointers to parameters of the CategoryRoleCache.GetByCategoryID
type CategoryRoleCacheMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// CategoryRoleCacheMockGetByCategoryIDResults contains results of the CategoryRoleCache.GetByCategoryID
type CategoryRoleCacheMockGetByCategoryIDResults struct {
	ca1 []categoryentity.CategoryRoleLink
	err error
}

// CategoryRoleCacheMockGetByCategoryIDOrigins contains origins of expectations of the CategoryRoleCache.GetByCategoryID
type CategoryRoleCacheMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Optional() *mCategoryRoleCacheMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for CategoryRoleCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Expect(categoryID int64) *mCategoryRoleCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRoleCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryRoleCacheMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRoleCacheMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &CategoryRoleCacheMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryRoleCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mCategoryRoleCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRoleCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryRoleCacheMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRoleCacheMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &CategoryRoleCacheMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the CategoryRoleCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Inspect(f func(categoryID int64)) *mCategoryRoleCacheMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for CategoryRoleCacheMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by CategoryRoleCache.GetByCategoryID
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Return(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRoleCacheMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRoleCacheMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryRoleCacheMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &CategoryRoleCacheMockGetByCategoryIDResults{ca1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the CategoryRoleCache.GetByCategoryID method
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Set(f func(categoryID int64) (ca1 []categoryentity.CategoryRoleLink, err error)) *CategoryRoleCacheMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the CategoryRoleCache.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the CategoryRoleCache.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the CategoryRoleCache.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) When(categoryID int64) *CategoryRoleCacheMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRoleCacheMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &CategoryRoleCacheMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &CategoryRoleCacheMockGetByCategoryIDParams{categoryID},
		expectationOrigins: CategoryRoleCacheMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up CategoryRoleCache.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *CategoryRoleCacheMockGetByCategoryIDExpectation) Then(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRoleCacheMock {
	e.results = &CategoryRoleCacheMockGetByCategoryIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryRoleCache.GetByCategoryID should be invoked
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Times(n uint64) *mCategoryRoleCacheMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of CategoryRoleCacheMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.CategoryRoleCache
func (mmGetByCategoryID *CategoryRoleCacheMock) GetByCategoryID(categoryID int64) (ca1 []categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := CategoryRoleCacheMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryRoleCacheMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("CategoryRoleCacheMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("CategoryRoleCacheMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the CategoryRoleCacheMock.GetByCategoryID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to CategoryRoleCacheMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished CategoryRoleCacheMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryRoleCacheMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of CategoryRoleCacheMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryRoleCacheMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryRoleCacheMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mCategoryRoleCacheMockGetByCategoryID) Calls() []*CategoryRoleCacheMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*CategoryRoleCacheMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *CategoryRoleCacheMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *CategoryRoleCacheMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRoleCacheMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mCategoryRoleCacheMockGetByID struct {
	optional           bool
	mock               *CategoryRoleCacheMock
	defaultExpectation *CategoryRoleCacheMockGetByIDExpectation
	expectations       []*CategoryRoleCacheMockGetByIDExpectation

	callArgs []*CategoryRoleCacheMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRoleCacheMockGetByIDExpectation specifies expectation struct of the CategoryRoleCache.GetByID
type CategoryRoleCacheMockGetByIDExpectation struct {
	mock               *CategoryRoleCacheMock
	params             *CategoryRoleCacheMockGetByIDParams
	paramPtrs          *CategoryRoleCacheMockGetByIDParamPtrs
	expectationOrigins CategoryRoleCacheMockGetByIDExpectationOrigins
	results            *CategoryRoleCacheMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRoleCacheMockGetByIDParams contains parameters of the CategoryRoleCache.GetByID
type CategoryRoleCacheMockGetByIDParams struct {
	id int64
}

// CategoryRoleCacheMockGetByIDParamPtrs contains pointers to parameters of the CategoryRoleCache.GetByID
type CategoryRoleCacheMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryRoleCacheMockGetByIDResults contains results of the CategoryRoleCache.GetByID
type CategoryRoleCacheMockGetByIDResults struct {
	c1  categoryentity.CategoryRoleLink
	err error
}

// CategoryRoleCacheMockGetByIDOrigins contains origins of expectations of the CategoryRoleCache.GetByID
type CategoryRoleCacheMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryRoleCacheMockGetByID) Optional() *mCategoryRoleCacheMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryRoleCache.GetByID
func (mmGetByID *mCategoryRoleCacheMockGetByID) Expect(id int64) *mCategoryRoleCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRoleCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryRoleCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryRoleCacheMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryRoleCacheMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryRoleCache.GetByID
func (mmGetByID *mCategoryRoleCacheMockGetByID) ExpectIdParam1(id int64) *mCategoryRoleCacheMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRoleCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryRoleCacheMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryRoleCacheMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryRoleCacheMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryRoleCache.GetByID
func (mmGetByID *mCategoryRoleCacheMockGetByID) Inspect(f func(id int64)) *mCategoryRoleCacheMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryRoleCacheMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryRoleCache.GetByID
func (mmGetByID *mCategoryRoleCacheMockGetByID) Return(c1 categoryentity.CategoryRoleLink, err error) *CategoryRoleCacheMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRoleCacheMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryRoleCacheMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryRoleCacheMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryRoleCache.GetByID method
func (mmGetByID *mCategoryRoleCacheMockGetByID) Set(f func(id int64) (c1 categoryentity.CategoryRoleLink, err error)) *CategoryRoleCacheMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryRoleCache.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryRoleCache.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryRoleCache.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryRoleCacheMockGetByID) When(id int64) *CategoryRoleCacheMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRoleCacheMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryRoleCacheMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryRoleCacheMockGetByIDParams{id},
		expectationOrigins: CategoryRoleCacheMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryRoleCache.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryRoleCacheMockGetByIDExpectation) Then(c1 categoryentity.CategoryRoleLink, err error) *CategoryRoleCacheMock {
	e.results = &CategoryRoleCacheMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryRoleCache.GetByID should be invoked
func (mmGetByID *mCategoryRoleCacheMockGetByID) Times(n uint64) *mCategoryRoleCacheMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryRoleCacheMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryRoleCacheMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryRoleCache
func (mmGetByID *CategoryRoleCacheMock) GetByID(id int64) (c1 categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryRoleCacheMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryRoleCacheMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryRoleCacheMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryRoleCacheMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryRoleCacheMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryRoleCacheMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryRoleCacheMock.GetByID invocations
func (mmGetByID *CategoryRoleCacheMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryRoleCacheMock.GetByID invocations
func (mmGetByID *CategoryRoleCacheMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryRoleCacheMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryRoleCacheMockGetByID) Calls() []*CategoryRoleCacheMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryRoleCacheMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryRoleCacheMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryRoleCacheMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryRoleCacheMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRoleCacheMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryRoleCacheMockSet struct {
	optional           bool
	mock               *CategoryRoleCacheMock
	defaultExpectation *CategoryRoleCacheMockSetExpectation
	expectations       []*CategoryRoleCacheMockSetExpectation

	callArgs []*CategoryRoleCacheMockSetParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRoleCacheMockSetExpectation specifies expectation struct of the CategoryRoleCache.Set
type CategoryRoleCacheMockSetExpectation struct {
	mock               *CategoryRoleCacheMock
	params             *CategoryRoleCacheMockSetParams
	paramPtrs          *CategoryRoleCacheMockSetParamPtrs
	expectationOrigins CategoryRoleCacheMockSetExpectationOrigins
	results            *CategoryRoleCacheMockSetResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRoleCacheMockSetParams contains parameters of the CategoryRoleCache.Set
type CategoryRoleCacheMockSetParams struct {
	categoryRole categoryentity.CategoryRoleLink
}

// CategoryRoleCacheMockSetParamPtrs contains pointers to parameters of the CategoryRoleCache.Set
type CategoryRoleCacheMockSetParamPtrs struct {
	categoryRole *categoryentity.CategoryRoleLink
}

// CategoryRoleCacheMockSetResults contains results of the CategoryRoleCache.Set
type CategoryRoleCacheMockSetResults struct {
	err error
}

// CategoryRoleCacheMockSetOrigins contains origins of expectations of the CategoryRoleCache.Set
type CategoryRoleCacheMockSetExpectationOrigins struct {
	origin             string
	originCategoryRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmSet *mCategoryRoleCacheMockSet) Optional() *mCategoryRoleCacheMockSet {
	mmSet.optional = true
	return mmSet
}

// Expect sets up expected params for CategoryRoleCache.Set
func (mmSet *mCategoryRoleCacheMockSet) Expect(categoryRole categoryentity.CategoryRoleLink) *mCategoryRoleCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryRoleCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryRoleCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.paramPtrs != nil {
		mmSet.mock.t.Fatalf("CategoryRoleCacheMock.Set mock is already set by ExpectParams functions")
	}

	mmSet.defaultExpectation.params = &CategoryRoleCacheMockSetParams{categoryRole}
	mmSet.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmSet.expectations {
		if minimock.Equal(e.params, mmSet.defaultExpectation.params) {
			mmSet.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmSet.defaultExpectation.params)
		}
	}

	return mmSet
}

// ExpectCategoryRoleParam1 sets up expected param categoryRole for CategoryRoleCache.Set
func (mmSet *mCategoryRoleCacheMockSet) ExpectCategoryRoleParam1(categoryRole categoryentity.CategoryRoleLink) *mCategoryRoleCacheMockSet {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryRoleCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryRoleCacheMockSetExpectation{}
	}

	if mmSet.defaultExpectation.params != nil {
		mmSet.mock.t.Fatalf("CategoryRoleCacheMock.Set mock is already set by Expect")
	}

	if mmSet.defaultExpectation.paramPtrs == nil {
		mmSet.defaultExpectation.paramPtrs = &CategoryRoleCacheMockSetParamPtrs{}
	}
	mmSet.defaultExpectation.paramPtrs.categoryRole = &categoryRole
	mmSet.defaultExpectation.expectationOrigins.originCategoryRole = minimock.CallerInfo(1)

	return mmSet
}

// Inspect accepts an inspector function that has same arguments as the CategoryRoleCache.Set
func (mmSet *mCategoryRoleCacheMockSet) Inspect(f func(categoryRole categoryentity.CategoryRoleLink)) *mCategoryRoleCacheMockSet {
	if mmSet.mock.inspectFuncSet != nil {
		mmSet.mock.t.Fatalf("Inspect function is already set for CategoryRoleCacheMock.Set")
	}

	mmSet.mock.inspectFuncSet = f

	return mmSet
}

// Return sets up results that will be returned by CategoryRoleCache.Set
func (mmSet *mCategoryRoleCacheMockSet) Return(err error) *CategoryRoleCacheMock {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryRoleCacheMock.Set mock is already set by Set")
	}

	if mmSet.defaultExpectation == nil {
		mmSet.defaultExpectation = &CategoryRoleCacheMockSetExpectation{mock: mmSet.mock}
	}
	mmSet.defaultExpectation.results = &CategoryRoleCacheMockSetResults{err}
	mmSet.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// Set uses given function f to mock the CategoryRoleCache.Set method
func (mmSet *mCategoryRoleCacheMockSet) Set(f func(categoryRole categoryentity.CategoryRoleLink) (err error)) *CategoryRoleCacheMock {
	if mmSet.defaultExpectation != nil {
		mmSet.mock.t.Fatalf("Default expectation is already set for the CategoryRoleCache.Set method")
	}

	if len(mmSet.expectations) > 0 {
		mmSet.mock.t.Fatalf("Some expectations are already set for the CategoryRoleCache.Set method")
	}

	mmSet.mock.funcSet = f
	mmSet.mock.funcSetOrigin = minimock.CallerInfo(1)
	return mmSet.mock
}

// When sets expectation for the CategoryRoleCache.Set which will trigger the result defined by the following
// Then helper
func (mmSet *mCategoryRoleCacheMockSet) When(categoryRole categoryentity.CategoryRoleLink) *CategoryRoleCacheMockSetExpectation {
	if mmSet.mock.funcSet != nil {
		mmSet.mock.t.Fatalf("CategoryRoleCacheMock.Set mock is already set by Set")
	}

	expectation := &CategoryRoleCacheMockSetExpectation{
		mock:               mmSet.mock,
		params:             &CategoryRoleCacheMockSetParams{categoryRole},
		expectationOrigins: CategoryRoleCacheMockSetExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmSet.expectations = append(mmSet.expectations, expectation)
	return expectation
}

// Then sets up CategoryRoleCache.Set return parameters for the expectation previously defined by the When method
func (e *CategoryRoleCacheMockSetExpectation) Then(err error) *CategoryRoleCacheMock {
	e.results = &CategoryRoleCacheMockSetResults{err}
	return e.mock
}

// Times sets number of times CategoryRoleCache.Set should be invoked
func (mmSet *mCategoryRoleCacheMockSet) Times(n uint64) *mCategoryRoleCacheMockSet {
	if n == 0 {
		mmSet.mock.t.Fatalf("Times of CategoryRoleCacheMock.Set mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmSet.expectedInvocations, n)
	mmSet.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmSet
}

func (mmSet *mCategoryRoleCacheMockSet) invocationsDone() bool {
	if len(mmSet.expectations) == 0 && mmSet.defaultExpectation == nil && mmSet.mock.funcSet == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmSet.mock.afterSetCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmSet.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Set implements mm_repository.CategoryRoleCache
func (mmSet *CategoryRoleCacheMock) Set(categoryRole categoryentity.CategoryRoleLink) (err error) {
	mm_atomic.AddUint64(&mmSet.beforeSetCounter, 1)
	defer mm_atomic.AddUint64(&mmSet.afterSetCounter, 1)

	mmSet.t.Helper()

	if mmSet.inspectFuncSet != nil {
		mmSet.inspectFuncSet(categoryRole)
	}

	mm_params := CategoryRoleCacheMockSetParams{categoryRole}

	// Record call args
	mmSet.SetMock.mutex.Lock()
	mmSet.SetMock.callArgs = append(mmSet.SetMock.callArgs, &mm_params)
	mmSet.SetMock.mutex.Unlock()

	for _, e := range mmSet.SetMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmSet.SetMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmSet.SetMock.defaultExpectation.Counter, 1)
		mm_want := mmSet.SetMock.defaultExpectation.params
		mm_want_ptrs := mmSet.SetMock.defaultExpectation.paramPtrs

		mm_got := CategoryRoleCacheMockSetParams{categoryRole}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryRole != nil && !minimock.Equal(*mm_want_ptrs.categoryRole, mm_got.categoryRole) {
				mmSet.t.Errorf("CategoryRoleCacheMock.Set got unexpected parameter categoryRole, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmSet.SetMock.defaultExpectation.expectationOrigins.originCategoryRole, *mm_want_ptrs.categoryRole, mm_got.categoryRole, minimock.Diff(*mm_want_ptrs.categoryRole, mm_got.categoryRole))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmSet.t.Errorf("CategoryRoleCacheMock.Set got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmSet.SetMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmSet.SetMock.defaultExpectation.results
		if mm_results == nil {
			mmSet.t.Fatal("No results are set for the CategoryRoleCacheMock.Set")
		}
		return (*mm_results).err
	}
	if mmSet.funcSet != nil {
		return mmSet.funcSet(categoryRole)
	}
	mmSet.t.Fatalf("Unexpected call to CategoryRoleCacheMock.Set. %v", categoryRole)
	return
}

// SetAfterCounter returns a count of finished CategoryRoleCacheMock.Set invocations
func (mmSet *CategoryRoleCacheMock) SetAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.afterSetCounter)
}

// SetBeforeCounter returns a count of CategoryRoleCacheMock.Set invocations
func (mmSet *CategoryRoleCacheMock) SetBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmSet.beforeSetCounter)
}

// Calls returns a list of arguments used in each call to CategoryRoleCacheMock.Set.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmSet *mCategoryRoleCacheMockSet) Calls() []*CategoryRoleCacheMockSetParams {
	mmSet.mutex.RLock()

	argCopy := make([]*CategoryRoleCacheMockSetParams, len(mmSet.callArgs))
	copy(argCopy, mmSet.callArgs)

	mmSet.mutex.RUnlock()

	return argCopy
}

// MinimockSetDone returns true if the count of the Set invocations corresponds
// the number of defined expectations
func (m *CategoryRoleCacheMock) MinimockSetDone() bool {
	if m.SetMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.SetMock.invocationsDone()
}

// MinimockSetInspect logs each unmet expectation
func (m *CategoryRoleCacheMock) MinimockSetInspect() {
	for _, e := range m.SetMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.Set at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterSetCounter := mm_atomic.LoadUint64(&m.afterSetCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.SetMock.defaultExpectation != nil && afterSetCounter < 1 {
		if m.SetMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.Set at\n%s", m.SetMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRoleCacheMock.Set at\n%s with params: %#v", m.SetMock.defaultExpectation.expectationOrigins.origin, *m.SetMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcSet != nil && afterSetCounter < 1 {
		m.t.Errorf("Expected call to CategoryRoleCacheMock.Set at\n%s", m.funcSetOrigin)
	}

	if !m.SetMock.invocationsDone() && afterSetCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRoleCacheMock.Set at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.SetMock.expectedInvocations), m.SetMock.expectedInvocationsOrigin, afterSetCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryRoleCacheMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockGetAllInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockSetInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryRoleCacheMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryRoleCacheMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockSetDone()
}
