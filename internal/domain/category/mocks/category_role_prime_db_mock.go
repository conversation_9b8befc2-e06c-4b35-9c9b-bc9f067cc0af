// Code generated by http://github.com/gojuno/minimock (v3.4.5). DO NOT EDIT.

package mocks

//go:generate minimock -i git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/repository.CategoryRolePrimeDB -o category_role_prime_db_mock.go -n CategoryRolePrimeDBMock -p mocks

import (
	"sync"
	mm_atomic "sync/atomic"
	mm_time "time"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
	"github.com/gojuno/minimock/v3"
)

// CategoryRolePrimeDBMock implements mm_repository.CategoryRolePrimeDB
type CategoryRolePrimeDBMock struct {
	t          minimock.Tester
	finishOnce sync.Once

	funcCreate          func(categoryRole categoryentity.CategoryRoleLink) (c1 categoryentity.CategoryRoleLink, err error)
	funcCreateOrigin    string
	inspectFuncCreate   func(categoryRole categoryentity.CategoryRoleLink)
	afterCreateCounter  uint64
	beforeCreateCounter uint64
	CreateMock          mCategoryRolePrimeDBMockCreate

	funcDelete          func(categoryID int64, roleID int64) (err error)
	funcDeleteOrigin    string
	inspectFuncDelete   func(categoryID int64, roleID int64)
	afterDeleteCounter  uint64
	beforeDeleteCounter uint64
	DeleteMock          mCategoryRolePrimeDBMockDelete

	funcGetAll          func() (ca1 []categoryentity.CategoryRoleLink, err error)
	funcGetAllOrigin    string
	inspectFuncGetAll   func()
	afterGetAllCounter  uint64
	beforeGetAllCounter uint64
	GetAllMock          mCategoryRolePrimeDBMockGetAll

	funcGetByCategoryID          func(categoryID int64) (ca1 []categoryentity.CategoryRoleLink, err error)
	funcGetByCategoryIDOrigin    string
	inspectFuncGetByCategoryID   func(categoryID int64)
	afterGetByCategoryIDCounter  uint64
	beforeGetByCategoryIDCounter uint64
	GetByCategoryIDMock          mCategoryRolePrimeDBMockGetByCategoryID

	funcGetByID          func(id int64) (c1 categoryentity.CategoryRoleLink, err error)
	funcGetByIDOrigin    string
	inspectFuncGetByID   func(id int64)
	afterGetByIDCounter  uint64
	beforeGetByIDCounter uint64
	GetByIDMock          mCategoryRolePrimeDBMockGetByID

	funcGetByRoleID          func(roleID int64) (ca1 []categoryentity.CategoryRoleLink, err error)
	funcGetByRoleIDOrigin    string
	inspectFuncGetByRoleID   func(roleID int64)
	afterGetByRoleIDCounter  uint64
	beforeGetByRoleIDCounter uint64
	GetByRoleIDMock          mCategoryRolePrimeDBMockGetByRoleID

	funcHasCategoryRole          func(categoryID int64, roleID int64) (b1 bool, err error)
	funcHasCategoryRoleOrigin    string
	inspectFuncHasCategoryRole   func(categoryID int64, roleID int64)
	afterHasCategoryRoleCounter  uint64
	beforeHasCategoryRoleCounter uint64
	HasCategoryRoleMock          mCategoryRolePrimeDBMockHasCategoryRole
}

// NewCategoryRolePrimeDBMock returns a mock for mm_repository.CategoryRolePrimeDB
func NewCategoryRolePrimeDBMock(t minimock.Tester) *CategoryRolePrimeDBMock {
	m := &CategoryRolePrimeDBMock{t: t}

	if controller, ok := t.(minimock.MockController); ok {
		controller.RegisterMocker(m)
	}

	m.CreateMock = mCategoryRolePrimeDBMockCreate{mock: m}
	m.CreateMock.callArgs = []*CategoryRolePrimeDBMockCreateParams{}

	m.DeleteMock = mCategoryRolePrimeDBMockDelete{mock: m}
	m.DeleteMock.callArgs = []*CategoryRolePrimeDBMockDeleteParams{}

	m.GetAllMock = mCategoryRolePrimeDBMockGetAll{mock: m}

	m.GetByCategoryIDMock = mCategoryRolePrimeDBMockGetByCategoryID{mock: m}
	m.GetByCategoryIDMock.callArgs = []*CategoryRolePrimeDBMockGetByCategoryIDParams{}

	m.GetByIDMock = mCategoryRolePrimeDBMockGetByID{mock: m}
	m.GetByIDMock.callArgs = []*CategoryRolePrimeDBMockGetByIDParams{}

	m.GetByRoleIDMock = mCategoryRolePrimeDBMockGetByRoleID{mock: m}
	m.GetByRoleIDMock.callArgs = []*CategoryRolePrimeDBMockGetByRoleIDParams{}

	m.HasCategoryRoleMock = mCategoryRolePrimeDBMockHasCategoryRole{mock: m}
	m.HasCategoryRoleMock.callArgs = []*CategoryRolePrimeDBMockHasCategoryRoleParams{}

	t.Cleanup(m.MinimockFinish)

	return m
}

type mCategoryRolePrimeDBMockCreate struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockCreateExpectation
	expectations       []*CategoryRolePrimeDBMockCreateExpectation

	callArgs []*CategoryRolePrimeDBMockCreateParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockCreateExpectation specifies expectation struct of the CategoryRolePrimeDB.Create
type CategoryRolePrimeDBMockCreateExpectation struct {
	mock               *CategoryRolePrimeDBMock
	params             *CategoryRolePrimeDBMockCreateParams
	paramPtrs          *CategoryRolePrimeDBMockCreateParamPtrs
	expectationOrigins CategoryRolePrimeDBMockCreateExpectationOrigins
	results            *CategoryRolePrimeDBMockCreateResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRolePrimeDBMockCreateParams contains parameters of the CategoryRolePrimeDB.Create
type CategoryRolePrimeDBMockCreateParams struct {
	categoryRole categoryentity.CategoryRoleLink
}

// CategoryRolePrimeDBMockCreateParamPtrs contains pointers to parameters of the CategoryRolePrimeDB.Create
type CategoryRolePrimeDBMockCreateParamPtrs struct {
	categoryRole *categoryentity.CategoryRoleLink
}

// CategoryRolePrimeDBMockCreateResults contains results of the CategoryRolePrimeDB.Create
type CategoryRolePrimeDBMockCreateResults struct {
	c1  categoryentity.CategoryRoleLink
	err error
}

// CategoryRolePrimeDBMockCreateOrigins contains origins of expectations of the CategoryRolePrimeDB.Create
type CategoryRolePrimeDBMockCreateExpectationOrigins struct {
	origin             string
	originCategoryRole string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmCreate *mCategoryRolePrimeDBMockCreate) Optional() *mCategoryRolePrimeDBMockCreate {
	mmCreate.optional = true
	return mmCreate
}

// Expect sets up expected params for CategoryRolePrimeDB.Create
func (mmCreate *mCategoryRolePrimeDBMockCreate) Expect(categoryRole categoryentity.CategoryRoleLink) *mCategoryRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.paramPtrs != nil {
		mmCreate.mock.t.Fatalf("CategoryRolePrimeDBMock.Create mock is already set by ExpectParams functions")
	}

	mmCreate.defaultExpectation.params = &CategoryRolePrimeDBMockCreateParams{categoryRole}
	mmCreate.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmCreate.expectations {
		if minimock.Equal(e.params, mmCreate.defaultExpectation.params) {
			mmCreate.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmCreate.defaultExpectation.params)
		}
	}

	return mmCreate
}

// ExpectCategoryRoleParam1 sets up expected param categoryRole for CategoryRolePrimeDB.Create
func (mmCreate *mCategoryRolePrimeDBMockCreate) ExpectCategoryRoleParam1(categoryRole categoryentity.CategoryRoleLink) *mCategoryRolePrimeDBMockCreate {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryRolePrimeDBMockCreateExpectation{}
	}

	if mmCreate.defaultExpectation.params != nil {
		mmCreate.mock.t.Fatalf("CategoryRolePrimeDBMock.Create mock is already set by Expect")
	}

	if mmCreate.defaultExpectation.paramPtrs == nil {
		mmCreate.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockCreateParamPtrs{}
	}
	mmCreate.defaultExpectation.paramPtrs.categoryRole = &categoryRole
	mmCreate.defaultExpectation.expectationOrigins.originCategoryRole = minimock.CallerInfo(1)

	return mmCreate
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.Create
func (mmCreate *mCategoryRolePrimeDBMockCreate) Inspect(f func(categoryRole categoryentity.CategoryRoleLink)) *mCategoryRolePrimeDBMockCreate {
	if mmCreate.mock.inspectFuncCreate != nil {
		mmCreate.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.Create")
	}

	mmCreate.mock.inspectFuncCreate = f

	return mmCreate
}

// Return sets up results that will be returned by CategoryRolePrimeDB.Create
func (mmCreate *mCategoryRolePrimeDBMockCreate) Return(c1 categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryRolePrimeDBMock.Create mock is already set by Set")
	}

	if mmCreate.defaultExpectation == nil {
		mmCreate.defaultExpectation = &CategoryRolePrimeDBMockCreateExpectation{mock: mmCreate.mock}
	}
	mmCreate.defaultExpectation.results = &CategoryRolePrimeDBMockCreateResults{c1, err}
	mmCreate.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.Create method
func (mmCreate *mCategoryRolePrimeDBMockCreate) Set(f func(categoryRole categoryentity.CategoryRoleLink) (c1 categoryentity.CategoryRoleLink, err error)) *CategoryRolePrimeDBMock {
	if mmCreate.defaultExpectation != nil {
		mmCreate.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.Create method")
	}

	if len(mmCreate.expectations) > 0 {
		mmCreate.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.Create method")
	}

	mmCreate.mock.funcCreate = f
	mmCreate.mock.funcCreateOrigin = minimock.CallerInfo(1)
	return mmCreate.mock
}

// When sets expectation for the CategoryRolePrimeDB.Create which will trigger the result defined by the following
// Then helper
func (mmCreate *mCategoryRolePrimeDBMockCreate) When(categoryRole categoryentity.CategoryRoleLink) *CategoryRolePrimeDBMockCreateExpectation {
	if mmCreate.mock.funcCreate != nil {
		mmCreate.mock.t.Fatalf("CategoryRolePrimeDBMock.Create mock is already set by Set")
	}

	expectation := &CategoryRolePrimeDBMockCreateExpectation{
		mock:               mmCreate.mock,
		params:             &CategoryRolePrimeDBMockCreateParams{categoryRole},
		expectationOrigins: CategoryRolePrimeDBMockCreateExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmCreate.expectations = append(mmCreate.expectations, expectation)
	return expectation
}

// Then sets up CategoryRolePrimeDB.Create return parameters for the expectation previously defined by the When method
func (e *CategoryRolePrimeDBMockCreateExpectation) Then(c1 categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	e.results = &CategoryRolePrimeDBMockCreateResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryRolePrimeDB.Create should be invoked
func (mmCreate *mCategoryRolePrimeDBMockCreate) Times(n uint64) *mCategoryRolePrimeDBMockCreate {
	if n == 0 {
		mmCreate.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.Create mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmCreate.expectedInvocations, n)
	mmCreate.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmCreate
}

func (mmCreate *mCategoryRolePrimeDBMockCreate) invocationsDone() bool {
	if len(mmCreate.expectations) == 0 && mmCreate.defaultExpectation == nil && mmCreate.mock.funcCreate == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmCreate.mock.afterCreateCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmCreate.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Create implements mm_repository.CategoryRolePrimeDB
func (mmCreate *CategoryRolePrimeDBMock) Create(categoryRole categoryentity.CategoryRoleLink) (c1 categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmCreate.beforeCreateCounter, 1)
	defer mm_atomic.AddUint64(&mmCreate.afterCreateCounter, 1)

	mmCreate.t.Helper()

	if mmCreate.inspectFuncCreate != nil {
		mmCreate.inspectFuncCreate(categoryRole)
	}

	mm_params := CategoryRolePrimeDBMockCreateParams{categoryRole}

	// Record call args
	mmCreate.CreateMock.mutex.Lock()
	mmCreate.CreateMock.callArgs = append(mmCreate.CreateMock.callArgs, &mm_params)
	mmCreate.CreateMock.mutex.Unlock()

	for _, e := range mmCreate.CreateMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmCreate.CreateMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmCreate.CreateMock.defaultExpectation.Counter, 1)
		mm_want := mmCreate.CreateMock.defaultExpectation.params
		mm_want_ptrs := mmCreate.CreateMock.defaultExpectation.paramPtrs

		mm_got := CategoryRolePrimeDBMockCreateParams{categoryRole}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryRole != nil && !minimock.Equal(*mm_want_ptrs.categoryRole, mm_got.categoryRole) {
				mmCreate.t.Errorf("CategoryRolePrimeDBMock.Create got unexpected parameter categoryRole, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmCreate.CreateMock.defaultExpectation.expectationOrigins.originCategoryRole, *mm_want_ptrs.categoryRole, mm_got.categoryRole, minimock.Diff(*mm_want_ptrs.categoryRole, mm_got.categoryRole))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmCreate.t.Errorf("CategoryRolePrimeDBMock.Create got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmCreate.CreateMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmCreate.CreateMock.defaultExpectation.results
		if mm_results == nil {
			mmCreate.t.Fatal("No results are set for the CategoryRolePrimeDBMock.Create")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmCreate.funcCreate != nil {
		return mmCreate.funcCreate(categoryRole)
	}
	mmCreate.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.Create. %v", categoryRole)
	return
}

// CreateAfterCounter returns a count of finished CategoryRolePrimeDBMock.Create invocations
func (mmCreate *CategoryRolePrimeDBMock) CreateAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.afterCreateCounter)
}

// CreateBeforeCounter returns a count of CategoryRolePrimeDBMock.Create invocations
func (mmCreate *CategoryRolePrimeDBMock) CreateBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmCreate.beforeCreateCounter)
}

// Calls returns a list of arguments used in each call to CategoryRolePrimeDBMock.Create.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmCreate *mCategoryRolePrimeDBMockCreate) Calls() []*CategoryRolePrimeDBMockCreateParams {
	mmCreate.mutex.RLock()

	argCopy := make([]*CategoryRolePrimeDBMockCreateParams, len(mmCreate.callArgs))
	copy(argCopy, mmCreate.callArgs)

	mmCreate.mutex.RUnlock()

	return argCopy
}

// MinimockCreateDone returns true if the count of the Create invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockCreateDone() bool {
	if m.CreateMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.CreateMock.invocationsDone()
}

// MinimockCreateInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockCreateInspect() {
	for _, e := range m.CreateMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Create at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterCreateCounter := mm_atomic.LoadUint64(&m.afterCreateCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.CreateMock.defaultExpectation != nil && afterCreateCounter < 1 {
		if m.CreateMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Create at\n%s", m.CreateMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Create at\n%s with params: %#v", m.CreateMock.defaultExpectation.expectationOrigins.origin, *m.CreateMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcCreate != nil && afterCreateCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Create at\n%s", m.funcCreateOrigin)
	}

	if !m.CreateMock.invocationsDone() && afterCreateCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.Create at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.CreateMock.expectedInvocations), m.CreateMock.expectedInvocationsOrigin, afterCreateCounter)
	}
}

type mCategoryRolePrimeDBMockDelete struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockDeleteExpectation
	expectations       []*CategoryRolePrimeDBMockDeleteExpectation

	callArgs []*CategoryRolePrimeDBMockDeleteParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockDeleteExpectation specifies expectation struct of the CategoryRolePrimeDB.Delete
type CategoryRolePrimeDBMockDeleteExpectation struct {
	mock               *CategoryRolePrimeDBMock
	params             *CategoryRolePrimeDBMockDeleteParams
	paramPtrs          *CategoryRolePrimeDBMockDeleteParamPtrs
	expectationOrigins CategoryRolePrimeDBMockDeleteExpectationOrigins
	results            *CategoryRolePrimeDBMockDeleteResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRolePrimeDBMockDeleteParams contains parameters of the CategoryRolePrimeDB.Delete
type CategoryRolePrimeDBMockDeleteParams struct {
	categoryID int64
	roleID     int64
}

// CategoryRolePrimeDBMockDeleteParamPtrs contains pointers to parameters of the CategoryRolePrimeDB.Delete
type CategoryRolePrimeDBMockDeleteParamPtrs struct {
	categoryID *int64
	roleID     *int64
}

// CategoryRolePrimeDBMockDeleteResults contains results of the CategoryRolePrimeDB.Delete
type CategoryRolePrimeDBMockDeleteResults struct {
	err error
}

// CategoryRolePrimeDBMockDeleteOrigins contains origins of expectations of the CategoryRolePrimeDB.Delete
type CategoryRolePrimeDBMockDeleteExpectationOrigins struct {
	origin           string
	originCategoryID string
	originRoleID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmDelete *mCategoryRolePrimeDBMockDelete) Optional() *mCategoryRolePrimeDBMockDelete {
	mmDelete.optional = true
	return mmDelete
}

// Expect sets up expected params for CategoryRolePrimeDB.Delete
func (mmDelete *mCategoryRolePrimeDBMockDelete) Expect(categoryID int64, roleID int64) *mCategoryRolePrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryRolePrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.paramPtrs != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by ExpectParams functions")
	}

	mmDelete.defaultExpectation.params = &CategoryRolePrimeDBMockDeleteParams{categoryID, roleID}
	mmDelete.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmDelete.expectations {
		if minimock.Equal(e.params, mmDelete.defaultExpectation.params) {
			mmDelete.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmDelete.defaultExpectation.params)
		}
	}

	return mmDelete
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryRolePrimeDB.Delete
func (mmDelete *mCategoryRolePrimeDBMockDelete) ExpectCategoryIDParam1(categoryID int64) *mCategoryRolePrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryRolePrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmDelete.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmDelete
}

// ExpectRoleIDParam2 sets up expected param roleID for CategoryRolePrimeDB.Delete
func (mmDelete *mCategoryRolePrimeDBMockDelete) ExpectRoleIDParam2(roleID int64) *mCategoryRolePrimeDBMockDelete {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryRolePrimeDBMockDeleteExpectation{}
	}

	if mmDelete.defaultExpectation.params != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Expect")
	}

	if mmDelete.defaultExpectation.paramPtrs == nil {
		mmDelete.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockDeleteParamPtrs{}
	}
	mmDelete.defaultExpectation.paramPtrs.roleID = &roleID
	mmDelete.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmDelete
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.Delete
func (mmDelete *mCategoryRolePrimeDBMockDelete) Inspect(f func(categoryID int64, roleID int64)) *mCategoryRolePrimeDBMockDelete {
	if mmDelete.mock.inspectFuncDelete != nil {
		mmDelete.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.Delete")
	}

	mmDelete.mock.inspectFuncDelete = f

	return mmDelete
}

// Return sets up results that will be returned by CategoryRolePrimeDB.Delete
func (mmDelete *mCategoryRolePrimeDBMockDelete) Return(err error) *CategoryRolePrimeDBMock {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Set")
	}

	if mmDelete.defaultExpectation == nil {
		mmDelete.defaultExpectation = &CategoryRolePrimeDBMockDeleteExpectation{mock: mmDelete.mock}
	}
	mmDelete.defaultExpectation.results = &CategoryRolePrimeDBMockDeleteResults{err}
	mmDelete.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.Delete method
func (mmDelete *mCategoryRolePrimeDBMockDelete) Set(f func(categoryID int64, roleID int64) (err error)) *CategoryRolePrimeDBMock {
	if mmDelete.defaultExpectation != nil {
		mmDelete.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.Delete method")
	}

	if len(mmDelete.expectations) > 0 {
		mmDelete.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.Delete method")
	}

	mmDelete.mock.funcDelete = f
	mmDelete.mock.funcDeleteOrigin = minimock.CallerInfo(1)
	return mmDelete.mock
}

// When sets expectation for the CategoryRolePrimeDB.Delete which will trigger the result defined by the following
// Then helper
func (mmDelete *mCategoryRolePrimeDBMockDelete) When(categoryID int64, roleID int64) *CategoryRolePrimeDBMockDeleteExpectation {
	if mmDelete.mock.funcDelete != nil {
		mmDelete.mock.t.Fatalf("CategoryRolePrimeDBMock.Delete mock is already set by Set")
	}

	expectation := &CategoryRolePrimeDBMockDeleteExpectation{
		mock:               mmDelete.mock,
		params:             &CategoryRolePrimeDBMockDeleteParams{categoryID, roleID},
		expectationOrigins: CategoryRolePrimeDBMockDeleteExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmDelete.expectations = append(mmDelete.expectations, expectation)
	return expectation
}

// Then sets up CategoryRolePrimeDB.Delete return parameters for the expectation previously defined by the When method
func (e *CategoryRolePrimeDBMockDeleteExpectation) Then(err error) *CategoryRolePrimeDBMock {
	e.results = &CategoryRolePrimeDBMockDeleteResults{err}
	return e.mock
}

// Times sets number of times CategoryRolePrimeDB.Delete should be invoked
func (mmDelete *mCategoryRolePrimeDBMockDelete) Times(n uint64) *mCategoryRolePrimeDBMockDelete {
	if n == 0 {
		mmDelete.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.Delete mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmDelete.expectedInvocations, n)
	mmDelete.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmDelete
}

func (mmDelete *mCategoryRolePrimeDBMockDelete) invocationsDone() bool {
	if len(mmDelete.expectations) == 0 && mmDelete.defaultExpectation == nil && mmDelete.mock.funcDelete == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmDelete.mock.afterDeleteCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmDelete.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// Delete implements mm_repository.CategoryRolePrimeDB
func (mmDelete *CategoryRolePrimeDBMock) Delete(categoryID int64, roleID int64) (err error) {
	mm_atomic.AddUint64(&mmDelete.beforeDeleteCounter, 1)
	defer mm_atomic.AddUint64(&mmDelete.afterDeleteCounter, 1)

	mmDelete.t.Helper()

	if mmDelete.inspectFuncDelete != nil {
		mmDelete.inspectFuncDelete(categoryID, roleID)
	}

	mm_params := CategoryRolePrimeDBMockDeleteParams{categoryID, roleID}

	// Record call args
	mmDelete.DeleteMock.mutex.Lock()
	mmDelete.DeleteMock.callArgs = append(mmDelete.DeleteMock.callArgs, &mm_params)
	mmDelete.DeleteMock.mutex.Unlock()

	for _, e := range mmDelete.DeleteMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.err
		}
	}

	if mmDelete.DeleteMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmDelete.DeleteMock.defaultExpectation.Counter, 1)
		mm_want := mmDelete.DeleteMock.defaultExpectation.params
		mm_want_ptrs := mmDelete.DeleteMock.defaultExpectation.paramPtrs

		mm_got := CategoryRolePrimeDBMockDeleteParams{categoryID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmDelete.t.Errorf("CategoryRolePrimeDBMock.Delete got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmDelete.t.Errorf("CategoryRolePrimeDBMock.Delete got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmDelete.DeleteMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmDelete.t.Errorf("CategoryRolePrimeDBMock.Delete got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmDelete.DeleteMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmDelete.DeleteMock.defaultExpectation.results
		if mm_results == nil {
			mmDelete.t.Fatal("No results are set for the CategoryRolePrimeDBMock.Delete")
		}
		return (*mm_results).err
	}
	if mmDelete.funcDelete != nil {
		return mmDelete.funcDelete(categoryID, roleID)
	}
	mmDelete.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.Delete. %v %v", categoryID, roleID)
	return
}

// DeleteAfterCounter returns a count of finished CategoryRolePrimeDBMock.Delete invocations
func (mmDelete *CategoryRolePrimeDBMock) DeleteAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.afterDeleteCounter)
}

// DeleteBeforeCounter returns a count of CategoryRolePrimeDBMock.Delete invocations
func (mmDelete *CategoryRolePrimeDBMock) DeleteBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmDelete.beforeDeleteCounter)
}

// Calls returns a list of arguments used in each call to CategoryRolePrimeDBMock.Delete.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmDelete *mCategoryRolePrimeDBMockDelete) Calls() []*CategoryRolePrimeDBMockDeleteParams {
	mmDelete.mutex.RLock()

	argCopy := make([]*CategoryRolePrimeDBMockDeleteParams, len(mmDelete.callArgs))
	copy(argCopy, mmDelete.callArgs)

	mmDelete.mutex.RUnlock()

	return argCopy
}

// MinimockDeleteDone returns true if the count of the Delete invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockDeleteDone() bool {
	if m.DeleteMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.DeleteMock.invocationsDone()
}

// MinimockDeleteInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockDeleteInspect() {
	for _, e := range m.DeleteMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Delete at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterDeleteCounter := mm_atomic.LoadUint64(&m.afterDeleteCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.DeleteMock.defaultExpectation != nil && afterDeleteCounter < 1 {
		if m.DeleteMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Delete at\n%s", m.DeleteMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Delete at\n%s with params: %#v", m.DeleteMock.defaultExpectation.expectationOrigins.origin, *m.DeleteMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcDelete != nil && afterDeleteCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.Delete at\n%s", m.funcDeleteOrigin)
	}

	if !m.DeleteMock.invocationsDone() && afterDeleteCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.Delete at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.DeleteMock.expectedInvocations), m.DeleteMock.expectedInvocationsOrigin, afterDeleteCounter)
	}
}

type mCategoryRolePrimeDBMockGetAll struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockGetAllExpectation
	expectations       []*CategoryRolePrimeDBMockGetAllExpectation

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockGetAllExpectation specifies expectation struct of the CategoryRolePrimeDB.GetAll
type CategoryRolePrimeDBMockGetAllExpectation struct {
	mock *CategoryRolePrimeDBMock

	results      *CategoryRolePrimeDBMockGetAllResults
	returnOrigin string
	Counter      uint64
}

// CategoryRolePrimeDBMockGetAllResults contains results of the CategoryRolePrimeDB.GetAll
type CategoryRolePrimeDBMockGetAllResults struct {
	ca1 []categoryentity.CategoryRoleLink
	err error
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetAll *mCategoryRolePrimeDBMockGetAll) Optional() *mCategoryRolePrimeDBMockGetAll {
	mmGetAll.optional = true
	return mmGetAll
}

// Expect sets up expected params for CategoryRolePrimeDB.GetAll
func (mmGetAll *mCategoryRolePrimeDBMockGetAll) Expect() *mCategoryRolePrimeDBMockGetAll {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryRolePrimeDBMockGetAllExpectation{}
	}

	return mmGetAll
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.GetAll
func (mmGetAll *mCategoryRolePrimeDBMockGetAll) Inspect(f func()) *mCategoryRolePrimeDBMockGetAll {
	if mmGetAll.mock.inspectFuncGetAll != nil {
		mmGetAll.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.GetAll")
	}

	mmGetAll.mock.inspectFuncGetAll = f

	return mmGetAll
}

// Return sets up results that will be returned by CategoryRolePrimeDB.GetAll
func (mmGetAll *mCategoryRolePrimeDBMockGetAll) Return(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	if mmGetAll.mock.funcGetAll != nil {
		mmGetAll.mock.t.Fatalf("CategoryRolePrimeDBMock.GetAll mock is already set by Set")
	}

	if mmGetAll.defaultExpectation == nil {
		mmGetAll.defaultExpectation = &CategoryRolePrimeDBMockGetAllExpectation{mock: mmGetAll.mock}
	}
	mmGetAll.defaultExpectation.results = &CategoryRolePrimeDBMockGetAllResults{ca1, err}
	mmGetAll.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.GetAll method
func (mmGetAll *mCategoryRolePrimeDBMockGetAll) Set(f func() (ca1 []categoryentity.CategoryRoleLink, err error)) *CategoryRolePrimeDBMock {
	if mmGetAll.defaultExpectation != nil {
		mmGetAll.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.GetAll method")
	}

	if len(mmGetAll.expectations) > 0 {
		mmGetAll.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.GetAll method")
	}

	mmGetAll.mock.funcGetAll = f
	mmGetAll.mock.funcGetAllOrigin = minimock.CallerInfo(1)
	return mmGetAll.mock
}

// Times sets number of times CategoryRolePrimeDB.GetAll should be invoked
func (mmGetAll *mCategoryRolePrimeDBMockGetAll) Times(n uint64) *mCategoryRolePrimeDBMockGetAll {
	if n == 0 {
		mmGetAll.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.GetAll mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetAll.expectedInvocations, n)
	mmGetAll.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetAll
}

func (mmGetAll *mCategoryRolePrimeDBMockGetAll) invocationsDone() bool {
	if len(mmGetAll.expectations) == 0 && mmGetAll.defaultExpectation == nil && mmGetAll.mock.funcGetAll == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetAll.mock.afterGetAllCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetAll.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetAll implements mm_repository.CategoryRolePrimeDB
func (mmGetAll *CategoryRolePrimeDBMock) GetAll() (ca1 []categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetAll.beforeGetAllCounter, 1)
	defer mm_atomic.AddUint64(&mmGetAll.afterGetAllCounter, 1)

	mmGetAll.t.Helper()

	if mmGetAll.inspectFuncGetAll != nil {
		mmGetAll.inspectFuncGetAll()
	}

	if mmGetAll.GetAllMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetAll.GetAllMock.defaultExpectation.Counter, 1)

		mm_results := mmGetAll.GetAllMock.defaultExpectation.results
		if mm_results == nil {
			mmGetAll.t.Fatal("No results are set for the CategoryRolePrimeDBMock.GetAll")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetAll.funcGetAll != nil {
		return mmGetAll.funcGetAll()
	}
	mmGetAll.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.GetAll.")
	return
}

// GetAllAfterCounter returns a count of finished CategoryRolePrimeDBMock.GetAll invocations
func (mmGetAll *CategoryRolePrimeDBMock) GetAllAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.afterGetAllCounter)
}

// GetAllBeforeCounter returns a count of CategoryRolePrimeDBMock.GetAll invocations
func (mmGetAll *CategoryRolePrimeDBMock) GetAllBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetAll.beforeGetAllCounter)
}

// MinimockGetAllDone returns true if the count of the GetAll invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockGetAllDone() bool {
	if m.GetAllMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetAllMock.invocationsDone()
}

// MinimockGetAllInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockGetAllInspect() {
	for _, e := range m.GetAllMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Error("Expected call to CategoryRolePrimeDBMock.GetAll")
		}
	}

	afterGetAllCounter := mm_atomic.LoadUint64(&m.afterGetAllCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetAllMock.defaultExpectation != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetAll at\n%s", m.GetAllMock.defaultExpectation.returnOrigin)
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetAll != nil && afterGetAllCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetAll at\n%s", m.funcGetAllOrigin)
	}

	if !m.GetAllMock.invocationsDone() && afterGetAllCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.GetAll at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetAllMock.expectedInvocations), m.GetAllMock.expectedInvocationsOrigin, afterGetAllCounter)
	}
}

type mCategoryRolePrimeDBMockGetByCategoryID struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockGetByCategoryIDExpectation
	expectations       []*CategoryRolePrimeDBMockGetByCategoryIDExpectation

	callArgs []*CategoryRolePrimeDBMockGetByCategoryIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockGetByCategoryIDExpectation specifies expectation struct of the CategoryRolePrimeDB.GetByCategoryID
type CategoryRolePrimeDBMockGetByCategoryIDExpectation struct {
	mock               *CategoryRolePrimeDBMock
	params             *CategoryRolePrimeDBMockGetByCategoryIDParams
	paramPtrs          *CategoryRolePrimeDBMockGetByCategoryIDParamPtrs
	expectationOrigins CategoryRolePrimeDBMockGetByCategoryIDExpectationOrigins
	results            *CategoryRolePrimeDBMockGetByCategoryIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRolePrimeDBMockGetByCategoryIDParams contains parameters of the CategoryRolePrimeDB.GetByCategoryID
type CategoryRolePrimeDBMockGetByCategoryIDParams struct {
	categoryID int64
}

// CategoryRolePrimeDBMockGetByCategoryIDParamPtrs contains pointers to parameters of the CategoryRolePrimeDB.GetByCategoryID
type CategoryRolePrimeDBMockGetByCategoryIDParamPtrs struct {
	categoryID *int64
}

// CategoryRolePrimeDBMockGetByCategoryIDResults contains results of the CategoryRolePrimeDB.GetByCategoryID
type CategoryRolePrimeDBMockGetByCategoryIDResults struct {
	ca1 []categoryentity.CategoryRoleLink
	err error
}

// CategoryRolePrimeDBMockGetByCategoryIDOrigins contains origins of expectations of the CategoryRolePrimeDB.GetByCategoryID
type CategoryRolePrimeDBMockGetByCategoryIDExpectationOrigins struct {
	origin           string
	originCategoryID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Optional() *mCategoryRolePrimeDBMockGetByCategoryID {
	mmGetByCategoryID.optional = true
	return mmGetByCategoryID
}

// Expect sets up expected params for CategoryRolePrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Expect(categoryID int64) *mCategoryRolePrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryRolePrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByCategoryID mock is already set by ExpectParams functions")
	}

	mmGetByCategoryID.defaultExpectation.params = &CategoryRolePrimeDBMockGetByCategoryIDParams{categoryID}
	mmGetByCategoryID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByCategoryID.expectations {
		if minimock.Equal(e.params, mmGetByCategoryID.defaultExpectation.params) {
			mmGetByCategoryID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByCategoryID.defaultExpectation.params)
		}
	}

	return mmGetByCategoryID
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryRolePrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) ExpectCategoryIDParam1(categoryID int64) *mCategoryRolePrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryRolePrimeDBMockGetByCategoryIDExpectation{}
	}

	if mmGetByCategoryID.defaultExpectation.params != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByCategoryID mock is already set by Expect")
	}

	if mmGetByCategoryID.defaultExpectation.paramPtrs == nil {
		mmGetByCategoryID.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockGetByCategoryIDParamPtrs{}
	}
	mmGetByCategoryID.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmGetByCategoryID.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmGetByCategoryID
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Inspect(f func(categoryID int64)) *mCategoryRolePrimeDBMockGetByCategoryID {
	if mmGetByCategoryID.mock.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.GetByCategoryID")
	}

	mmGetByCategoryID.mock.inspectFuncGetByCategoryID = f

	return mmGetByCategoryID
}

// Return sets up results that will be returned by CategoryRolePrimeDB.GetByCategoryID
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Return(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	if mmGetByCategoryID.defaultExpectation == nil {
		mmGetByCategoryID.defaultExpectation = &CategoryRolePrimeDBMockGetByCategoryIDExpectation{mock: mmGetByCategoryID.mock}
	}
	mmGetByCategoryID.defaultExpectation.results = &CategoryRolePrimeDBMockGetByCategoryIDResults{ca1, err}
	mmGetByCategoryID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.GetByCategoryID method
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Set(f func(categoryID int64) (ca1 []categoryentity.CategoryRoleLink, err error)) *CategoryRolePrimeDBMock {
	if mmGetByCategoryID.defaultExpectation != nil {
		mmGetByCategoryID.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.GetByCategoryID method")
	}

	if len(mmGetByCategoryID.expectations) > 0 {
		mmGetByCategoryID.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.GetByCategoryID method")
	}

	mmGetByCategoryID.mock.funcGetByCategoryID = f
	mmGetByCategoryID.mock.funcGetByCategoryIDOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID.mock
}

// When sets expectation for the CategoryRolePrimeDB.GetByCategoryID which will trigger the result defined by the following
// Then helper
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) When(categoryID int64) *CategoryRolePrimeDBMockGetByCategoryIDExpectation {
	if mmGetByCategoryID.mock.funcGetByCategoryID != nil {
		mmGetByCategoryID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByCategoryID mock is already set by Set")
	}

	expectation := &CategoryRolePrimeDBMockGetByCategoryIDExpectation{
		mock:               mmGetByCategoryID.mock,
		params:             &CategoryRolePrimeDBMockGetByCategoryIDParams{categoryID},
		expectationOrigins: CategoryRolePrimeDBMockGetByCategoryIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByCategoryID.expectations = append(mmGetByCategoryID.expectations, expectation)
	return expectation
}

// Then sets up CategoryRolePrimeDB.GetByCategoryID return parameters for the expectation previously defined by the When method
func (e *CategoryRolePrimeDBMockGetByCategoryIDExpectation) Then(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	e.results = &CategoryRolePrimeDBMockGetByCategoryIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryRolePrimeDB.GetByCategoryID should be invoked
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Times(n uint64) *mCategoryRolePrimeDBMockGetByCategoryID {
	if n == 0 {
		mmGetByCategoryID.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.GetByCategoryID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByCategoryID.expectedInvocations, n)
	mmGetByCategoryID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByCategoryID
}

func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) invocationsDone() bool {
	if len(mmGetByCategoryID.expectations) == 0 && mmGetByCategoryID.defaultExpectation == nil && mmGetByCategoryID.mock.funcGetByCategoryID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.mock.afterGetByCategoryIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByCategoryID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByCategoryID implements mm_repository.CategoryRolePrimeDB
func (mmGetByCategoryID *CategoryRolePrimeDBMock) GetByCategoryID(categoryID int64) (ca1 []categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter, 1)

	mmGetByCategoryID.t.Helper()

	if mmGetByCategoryID.inspectFuncGetByCategoryID != nil {
		mmGetByCategoryID.inspectFuncGetByCategoryID(categoryID)
	}

	mm_params := CategoryRolePrimeDBMockGetByCategoryIDParams{categoryID}

	// Record call args
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Lock()
	mmGetByCategoryID.GetByCategoryIDMock.callArgs = append(mmGetByCategoryID.GetByCategoryIDMock.callArgs, &mm_params)
	mmGetByCategoryID.GetByCategoryIDMock.mutex.Unlock()

	for _, e := range mmGetByCategoryID.GetByCategoryIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryRolePrimeDBMockGetByCategoryIDParams{categoryID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmGetByCategoryID.t.Errorf("CategoryRolePrimeDBMock.GetByCategoryID got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByCategoryID.t.Errorf("CategoryRolePrimeDBMock.GetByCategoryID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByCategoryID.GetByCategoryIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByCategoryID.t.Fatal("No results are set for the CategoryRolePrimeDBMock.GetByCategoryID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByCategoryID.funcGetByCategoryID != nil {
		return mmGetByCategoryID.funcGetByCategoryID(categoryID)
	}
	mmGetByCategoryID.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.GetByCategoryID. %v", categoryID)
	return
}

// GetByCategoryIDAfterCounter returns a count of finished CategoryRolePrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryRolePrimeDBMock) GetByCategoryIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.afterGetByCategoryIDCounter)
}

// GetByCategoryIDBeforeCounter returns a count of CategoryRolePrimeDBMock.GetByCategoryID invocations
func (mmGetByCategoryID *CategoryRolePrimeDBMock) GetByCategoryIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByCategoryID.beforeGetByCategoryIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryRolePrimeDBMock.GetByCategoryID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByCategoryID *mCategoryRolePrimeDBMockGetByCategoryID) Calls() []*CategoryRolePrimeDBMockGetByCategoryIDParams {
	mmGetByCategoryID.mutex.RLock()

	argCopy := make([]*CategoryRolePrimeDBMockGetByCategoryIDParams, len(mmGetByCategoryID.callArgs))
	copy(argCopy, mmGetByCategoryID.callArgs)

	mmGetByCategoryID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByCategoryIDDone returns true if the count of the GetByCategoryID invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockGetByCategoryIDDone() bool {
	if m.GetByCategoryIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByCategoryIDMock.invocationsDone()
}

// MinimockGetByCategoryIDInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockGetByCategoryIDInspect() {
	for _, e := range m.GetByCategoryIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByCategoryID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByCategoryIDCounter := mm_atomic.LoadUint64(&m.afterGetByCategoryIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByCategoryIDMock.defaultExpectation != nil && afterGetByCategoryIDCounter < 1 {
		if m.GetByCategoryIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByCategoryID at\n%s", m.GetByCategoryIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByCategoryID at\n%s with params: %#v", m.GetByCategoryIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByCategoryIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByCategoryID != nil && afterGetByCategoryIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByCategoryID at\n%s", m.funcGetByCategoryIDOrigin)
	}

	if !m.GetByCategoryIDMock.invocationsDone() && afterGetByCategoryIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.GetByCategoryID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByCategoryIDMock.expectedInvocations), m.GetByCategoryIDMock.expectedInvocationsOrigin, afterGetByCategoryIDCounter)
	}
}

type mCategoryRolePrimeDBMockGetByID struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockGetByIDExpectation
	expectations       []*CategoryRolePrimeDBMockGetByIDExpectation

	callArgs []*CategoryRolePrimeDBMockGetByIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockGetByIDExpectation specifies expectation struct of the CategoryRolePrimeDB.GetByID
type CategoryRolePrimeDBMockGetByIDExpectation struct {
	mock               *CategoryRolePrimeDBMock
	params             *CategoryRolePrimeDBMockGetByIDParams
	paramPtrs          *CategoryRolePrimeDBMockGetByIDParamPtrs
	expectationOrigins CategoryRolePrimeDBMockGetByIDExpectationOrigins
	results            *CategoryRolePrimeDBMockGetByIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRolePrimeDBMockGetByIDParams contains parameters of the CategoryRolePrimeDB.GetByID
type CategoryRolePrimeDBMockGetByIDParams struct {
	id int64
}

// CategoryRolePrimeDBMockGetByIDParamPtrs contains pointers to parameters of the CategoryRolePrimeDB.GetByID
type CategoryRolePrimeDBMockGetByIDParamPtrs struct {
	id *int64
}

// CategoryRolePrimeDBMockGetByIDResults contains results of the CategoryRolePrimeDB.GetByID
type CategoryRolePrimeDBMockGetByIDResults struct {
	c1  categoryentity.CategoryRoleLink
	err error
}

// CategoryRolePrimeDBMockGetByIDOrigins contains origins of expectations of the CategoryRolePrimeDB.GetByID
type CategoryRolePrimeDBMockGetByIDExpectationOrigins struct {
	origin   string
	originId string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Optional() *mCategoryRolePrimeDBMockGetByID {
	mmGetByID.optional = true
	return mmGetByID
}

// Expect sets up expected params for CategoryRolePrimeDB.GetByID
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Expect(id int64) *mCategoryRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.paramPtrs != nil {
		mmGetByID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByID mock is already set by ExpectParams functions")
	}

	mmGetByID.defaultExpectation.params = &CategoryRolePrimeDBMockGetByIDParams{id}
	mmGetByID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByID.expectations {
		if minimock.Equal(e.params, mmGetByID.defaultExpectation.params) {
			mmGetByID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByID.defaultExpectation.params)
		}
	}

	return mmGetByID
}

// ExpectIdParam1 sets up expected param id for CategoryRolePrimeDB.GetByID
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) ExpectIdParam1(id int64) *mCategoryRolePrimeDBMockGetByID {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryRolePrimeDBMockGetByIDExpectation{}
	}

	if mmGetByID.defaultExpectation.params != nil {
		mmGetByID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByID mock is already set by Expect")
	}

	if mmGetByID.defaultExpectation.paramPtrs == nil {
		mmGetByID.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockGetByIDParamPtrs{}
	}
	mmGetByID.defaultExpectation.paramPtrs.id = &id
	mmGetByID.defaultExpectation.expectationOrigins.originId = minimock.CallerInfo(1)

	return mmGetByID
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.GetByID
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Inspect(f func(id int64)) *mCategoryRolePrimeDBMockGetByID {
	if mmGetByID.mock.inspectFuncGetByID != nil {
		mmGetByID.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.GetByID")
	}

	mmGetByID.mock.inspectFuncGetByID = f

	return mmGetByID
}

// Return sets up results that will be returned by CategoryRolePrimeDB.GetByID
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Return(c1 categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByID mock is already set by Set")
	}

	if mmGetByID.defaultExpectation == nil {
		mmGetByID.defaultExpectation = &CategoryRolePrimeDBMockGetByIDExpectation{mock: mmGetByID.mock}
	}
	mmGetByID.defaultExpectation.results = &CategoryRolePrimeDBMockGetByIDResults{c1, err}
	mmGetByID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.GetByID method
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Set(f func(id int64) (c1 categoryentity.CategoryRoleLink, err error)) *CategoryRolePrimeDBMock {
	if mmGetByID.defaultExpectation != nil {
		mmGetByID.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.GetByID method")
	}

	if len(mmGetByID.expectations) > 0 {
		mmGetByID.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.GetByID method")
	}

	mmGetByID.mock.funcGetByID = f
	mmGetByID.mock.funcGetByIDOrigin = minimock.CallerInfo(1)
	return mmGetByID.mock
}

// When sets expectation for the CategoryRolePrimeDB.GetByID which will trigger the result defined by the following
// Then helper
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) When(id int64) *CategoryRolePrimeDBMockGetByIDExpectation {
	if mmGetByID.mock.funcGetByID != nil {
		mmGetByID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByID mock is already set by Set")
	}

	expectation := &CategoryRolePrimeDBMockGetByIDExpectation{
		mock:               mmGetByID.mock,
		params:             &CategoryRolePrimeDBMockGetByIDParams{id},
		expectationOrigins: CategoryRolePrimeDBMockGetByIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByID.expectations = append(mmGetByID.expectations, expectation)
	return expectation
}

// Then sets up CategoryRolePrimeDB.GetByID return parameters for the expectation previously defined by the When method
func (e *CategoryRolePrimeDBMockGetByIDExpectation) Then(c1 categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	e.results = &CategoryRolePrimeDBMockGetByIDResults{c1, err}
	return e.mock
}

// Times sets number of times CategoryRolePrimeDB.GetByID should be invoked
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Times(n uint64) *mCategoryRolePrimeDBMockGetByID {
	if n == 0 {
		mmGetByID.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.GetByID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByID.expectedInvocations, n)
	mmGetByID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByID
}

func (mmGetByID *mCategoryRolePrimeDBMockGetByID) invocationsDone() bool {
	if len(mmGetByID.expectations) == 0 && mmGetByID.defaultExpectation == nil && mmGetByID.mock.funcGetByID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByID.mock.afterGetByIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByID implements mm_repository.CategoryRolePrimeDB
func (mmGetByID *CategoryRolePrimeDBMock) GetByID(id int64) (c1 categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetByID.beforeGetByIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByID.afterGetByIDCounter, 1)

	mmGetByID.t.Helper()

	if mmGetByID.inspectFuncGetByID != nil {
		mmGetByID.inspectFuncGetByID(id)
	}

	mm_params := CategoryRolePrimeDBMockGetByIDParams{id}

	// Record call args
	mmGetByID.GetByIDMock.mutex.Lock()
	mmGetByID.GetByIDMock.callArgs = append(mmGetByID.GetByIDMock.callArgs, &mm_params)
	mmGetByID.GetByIDMock.mutex.Unlock()

	for _, e := range mmGetByID.GetByIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.c1, e.results.err
		}
	}

	if mmGetByID.GetByIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByID.GetByIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByID.GetByIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByID.GetByIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryRolePrimeDBMockGetByIDParams{id}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.id != nil && !minimock.Equal(*mm_want_ptrs.id, mm_got.id) {
				mmGetByID.t.Errorf("CategoryRolePrimeDBMock.GetByID got unexpected parameter id, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.originId, *mm_want_ptrs.id, mm_got.id, minimock.Diff(*mm_want_ptrs.id, mm_got.id))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByID.t.Errorf("CategoryRolePrimeDBMock.GetByID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByID.GetByIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByID.GetByIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByID.t.Fatal("No results are set for the CategoryRolePrimeDBMock.GetByID")
		}
		return (*mm_results).c1, (*mm_results).err
	}
	if mmGetByID.funcGetByID != nil {
		return mmGetByID.funcGetByID(id)
	}
	mmGetByID.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.GetByID. %v", id)
	return
}

// GetByIDAfterCounter returns a count of finished CategoryRolePrimeDBMock.GetByID invocations
func (mmGetByID *CategoryRolePrimeDBMock) GetByIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.afterGetByIDCounter)
}

// GetByIDBeforeCounter returns a count of CategoryRolePrimeDBMock.GetByID invocations
func (mmGetByID *CategoryRolePrimeDBMock) GetByIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByID.beforeGetByIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryRolePrimeDBMock.GetByID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByID *mCategoryRolePrimeDBMockGetByID) Calls() []*CategoryRolePrimeDBMockGetByIDParams {
	mmGetByID.mutex.RLock()

	argCopy := make([]*CategoryRolePrimeDBMockGetByIDParams, len(mmGetByID.callArgs))
	copy(argCopy, mmGetByID.callArgs)

	mmGetByID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByIDDone returns true if the count of the GetByID invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockGetByIDDone() bool {
	if m.GetByIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByIDMock.invocationsDone()
}

// MinimockGetByIDInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockGetByIDInspect() {
	for _, e := range m.GetByIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByIDCounter := mm_atomic.LoadUint64(&m.afterGetByIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByIDMock.defaultExpectation != nil && afterGetByIDCounter < 1 {
		if m.GetByIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByID at\n%s", m.GetByIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByID at\n%s with params: %#v", m.GetByIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByID != nil && afterGetByIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByID at\n%s", m.funcGetByIDOrigin)
	}

	if !m.GetByIDMock.invocationsDone() && afterGetByIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.GetByID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByIDMock.expectedInvocations), m.GetByIDMock.expectedInvocationsOrigin, afterGetByIDCounter)
	}
}

type mCategoryRolePrimeDBMockGetByRoleID struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockGetByRoleIDExpectation
	expectations       []*CategoryRolePrimeDBMockGetByRoleIDExpectation

	callArgs []*CategoryRolePrimeDBMockGetByRoleIDParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockGetByRoleIDExpectation specifies expectation struct of the CategoryRolePrimeDB.GetByRoleID
type CategoryRolePrimeDBMockGetByRoleIDExpectation struct {
	mock               *CategoryRolePrimeDBMock
	params             *CategoryRolePrimeDBMockGetByRoleIDParams
	paramPtrs          *CategoryRolePrimeDBMockGetByRoleIDParamPtrs
	expectationOrigins CategoryRolePrimeDBMockGetByRoleIDExpectationOrigins
	results            *CategoryRolePrimeDBMockGetByRoleIDResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRolePrimeDBMockGetByRoleIDParams contains parameters of the CategoryRolePrimeDB.GetByRoleID
type CategoryRolePrimeDBMockGetByRoleIDParams struct {
	roleID int64
}

// CategoryRolePrimeDBMockGetByRoleIDParamPtrs contains pointers to parameters of the CategoryRolePrimeDB.GetByRoleID
type CategoryRolePrimeDBMockGetByRoleIDParamPtrs struct {
	roleID *int64
}

// CategoryRolePrimeDBMockGetByRoleIDResults contains results of the CategoryRolePrimeDB.GetByRoleID
type CategoryRolePrimeDBMockGetByRoleIDResults struct {
	ca1 []categoryentity.CategoryRoleLink
	err error
}

// CategoryRolePrimeDBMockGetByRoleIDOrigins contains origins of expectations of the CategoryRolePrimeDB.GetByRoleID
type CategoryRolePrimeDBMockGetByRoleIDExpectationOrigins struct {
	origin       string
	originRoleID string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Optional() *mCategoryRolePrimeDBMockGetByRoleID {
	mmGetByRoleID.optional = true
	return mmGetByRoleID
}

// Expect sets up expected params for CategoryRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Expect(roleID int64) *mCategoryRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &CategoryRolePrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs != nil {
		mmGetByRoleID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByRoleID mock is already set by ExpectParams functions")
	}

	mmGetByRoleID.defaultExpectation.params = &CategoryRolePrimeDBMockGetByRoleIDParams{roleID}
	mmGetByRoleID.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmGetByRoleID.expectations {
		if minimock.Equal(e.params, mmGetByRoleID.defaultExpectation.params) {
			mmGetByRoleID.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmGetByRoleID.defaultExpectation.params)
		}
	}

	return mmGetByRoleID
}

// ExpectRoleIDParam1 sets up expected param roleID for CategoryRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) ExpectRoleIDParam1(roleID int64) *mCategoryRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &CategoryRolePrimeDBMockGetByRoleIDExpectation{}
	}

	if mmGetByRoleID.defaultExpectation.params != nil {
		mmGetByRoleID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByRoleID mock is already set by Expect")
	}

	if mmGetByRoleID.defaultExpectation.paramPtrs == nil {
		mmGetByRoleID.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockGetByRoleIDParamPtrs{}
	}
	mmGetByRoleID.defaultExpectation.paramPtrs.roleID = &roleID
	mmGetByRoleID.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmGetByRoleID
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Inspect(f func(roleID int64)) *mCategoryRolePrimeDBMockGetByRoleID {
	if mmGetByRoleID.mock.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.GetByRoleID")
	}

	mmGetByRoleID.mock.inspectFuncGetByRoleID = f

	return mmGetByRoleID
}

// Return sets up results that will be returned by CategoryRolePrimeDB.GetByRoleID
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Return(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	if mmGetByRoleID.defaultExpectation == nil {
		mmGetByRoleID.defaultExpectation = &CategoryRolePrimeDBMockGetByRoleIDExpectation{mock: mmGetByRoleID.mock}
	}
	mmGetByRoleID.defaultExpectation.results = &CategoryRolePrimeDBMockGetByRoleIDResults{ca1, err}
	mmGetByRoleID.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.GetByRoleID method
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Set(f func(roleID int64) (ca1 []categoryentity.CategoryRoleLink, err error)) *CategoryRolePrimeDBMock {
	if mmGetByRoleID.defaultExpectation != nil {
		mmGetByRoleID.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.GetByRoleID method")
	}

	if len(mmGetByRoleID.expectations) > 0 {
		mmGetByRoleID.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.GetByRoleID method")
	}

	mmGetByRoleID.mock.funcGetByRoleID = f
	mmGetByRoleID.mock.funcGetByRoleIDOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID.mock
}

// When sets expectation for the CategoryRolePrimeDB.GetByRoleID which will trigger the result defined by the following
// Then helper
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) When(roleID int64) *CategoryRolePrimeDBMockGetByRoleIDExpectation {
	if mmGetByRoleID.mock.funcGetByRoleID != nil {
		mmGetByRoleID.mock.t.Fatalf("CategoryRolePrimeDBMock.GetByRoleID mock is already set by Set")
	}

	expectation := &CategoryRolePrimeDBMockGetByRoleIDExpectation{
		mock:               mmGetByRoleID.mock,
		params:             &CategoryRolePrimeDBMockGetByRoleIDParams{roleID},
		expectationOrigins: CategoryRolePrimeDBMockGetByRoleIDExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmGetByRoleID.expectations = append(mmGetByRoleID.expectations, expectation)
	return expectation
}

// Then sets up CategoryRolePrimeDB.GetByRoleID return parameters for the expectation previously defined by the When method
func (e *CategoryRolePrimeDBMockGetByRoleIDExpectation) Then(ca1 []categoryentity.CategoryRoleLink, err error) *CategoryRolePrimeDBMock {
	e.results = &CategoryRolePrimeDBMockGetByRoleIDResults{ca1, err}
	return e.mock
}

// Times sets number of times CategoryRolePrimeDB.GetByRoleID should be invoked
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Times(n uint64) *mCategoryRolePrimeDBMockGetByRoleID {
	if n == 0 {
		mmGetByRoleID.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.GetByRoleID mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmGetByRoleID.expectedInvocations, n)
	mmGetByRoleID.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmGetByRoleID
}

func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) invocationsDone() bool {
	if len(mmGetByRoleID.expectations) == 0 && mmGetByRoleID.defaultExpectation == nil && mmGetByRoleID.mock.funcGetByRoleID == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.mock.afterGetByRoleIDCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmGetByRoleID.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// GetByRoleID implements mm_repository.CategoryRolePrimeDB
func (mmGetByRoleID *CategoryRolePrimeDBMock) GetByRoleID(roleID int64) (ca1 []categoryentity.CategoryRoleLink, err error) {
	mm_atomic.AddUint64(&mmGetByRoleID.beforeGetByRoleIDCounter, 1)
	defer mm_atomic.AddUint64(&mmGetByRoleID.afterGetByRoleIDCounter, 1)

	mmGetByRoleID.t.Helper()

	if mmGetByRoleID.inspectFuncGetByRoleID != nil {
		mmGetByRoleID.inspectFuncGetByRoleID(roleID)
	}

	mm_params := CategoryRolePrimeDBMockGetByRoleIDParams{roleID}

	// Record call args
	mmGetByRoleID.GetByRoleIDMock.mutex.Lock()
	mmGetByRoleID.GetByRoleIDMock.callArgs = append(mmGetByRoleID.GetByRoleIDMock.callArgs, &mm_params)
	mmGetByRoleID.GetByRoleIDMock.mutex.Unlock()

	for _, e := range mmGetByRoleID.GetByRoleIDMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.ca1, e.results.err
		}
	}

	if mmGetByRoleID.GetByRoleIDMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmGetByRoleID.GetByRoleIDMock.defaultExpectation.Counter, 1)
		mm_want := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.params
		mm_want_ptrs := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.paramPtrs

		mm_got := CategoryRolePrimeDBMockGetByRoleIDParams{roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmGetByRoleID.t.Errorf("CategoryRolePrimeDBMock.GetByRoleID got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmGetByRoleID.t.Errorf("CategoryRolePrimeDBMock.GetByRoleID got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmGetByRoleID.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmGetByRoleID.GetByRoleIDMock.defaultExpectation.results
		if mm_results == nil {
			mmGetByRoleID.t.Fatal("No results are set for the CategoryRolePrimeDBMock.GetByRoleID")
		}
		return (*mm_results).ca1, (*mm_results).err
	}
	if mmGetByRoleID.funcGetByRoleID != nil {
		return mmGetByRoleID.funcGetByRoleID(roleID)
	}
	mmGetByRoleID.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.GetByRoleID. %v", roleID)
	return
}

// GetByRoleIDAfterCounter returns a count of finished CategoryRolePrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *CategoryRolePrimeDBMock) GetByRoleIDAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.afterGetByRoleIDCounter)
}

// GetByRoleIDBeforeCounter returns a count of CategoryRolePrimeDBMock.GetByRoleID invocations
func (mmGetByRoleID *CategoryRolePrimeDBMock) GetByRoleIDBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmGetByRoleID.beforeGetByRoleIDCounter)
}

// Calls returns a list of arguments used in each call to CategoryRolePrimeDBMock.GetByRoleID.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmGetByRoleID *mCategoryRolePrimeDBMockGetByRoleID) Calls() []*CategoryRolePrimeDBMockGetByRoleIDParams {
	mmGetByRoleID.mutex.RLock()

	argCopy := make([]*CategoryRolePrimeDBMockGetByRoleIDParams, len(mmGetByRoleID.callArgs))
	copy(argCopy, mmGetByRoleID.callArgs)

	mmGetByRoleID.mutex.RUnlock()

	return argCopy
}

// MinimockGetByRoleIDDone returns true if the count of the GetByRoleID invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockGetByRoleIDDone() bool {
	if m.GetByRoleIDMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.GetByRoleIDMock.invocationsDone()
}

// MinimockGetByRoleIDInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockGetByRoleIDInspect() {
	for _, e := range m.GetByRoleIDMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByRoleID at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterGetByRoleIDCounter := mm_atomic.LoadUint64(&m.afterGetByRoleIDCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.GetByRoleIDMock.defaultExpectation != nil && afterGetByRoleIDCounter < 1 {
		if m.GetByRoleIDMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByRoleID at\n%s", m.GetByRoleIDMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByRoleID at\n%s with params: %#v", m.GetByRoleIDMock.defaultExpectation.expectationOrigins.origin, *m.GetByRoleIDMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcGetByRoleID != nil && afterGetByRoleIDCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.GetByRoleID at\n%s", m.funcGetByRoleIDOrigin)
	}

	if !m.GetByRoleIDMock.invocationsDone() && afterGetByRoleIDCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.GetByRoleID at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.GetByRoleIDMock.expectedInvocations), m.GetByRoleIDMock.expectedInvocationsOrigin, afterGetByRoleIDCounter)
	}
}

type mCategoryRolePrimeDBMockHasCategoryRole struct {
	optional           bool
	mock               *CategoryRolePrimeDBMock
	defaultExpectation *CategoryRolePrimeDBMockHasCategoryRoleExpectation
	expectations       []*CategoryRolePrimeDBMockHasCategoryRoleExpectation

	callArgs []*CategoryRolePrimeDBMockHasCategoryRoleParams
	mutex    sync.RWMutex

	expectedInvocations       uint64
	expectedInvocationsOrigin string
}

// CategoryRolePrimeDBMockHasCategoryRoleExpectation specifies expectation struct of the CategoryRolePrimeDB.HasCategoryRole
type CategoryRolePrimeDBMockHasCategoryRoleExpectation struct {
	mock               *CategoryRolePrimeDBMock
	params             *CategoryRolePrimeDBMockHasCategoryRoleParams
	paramPtrs          *CategoryRolePrimeDBMockHasCategoryRoleParamPtrs
	expectationOrigins CategoryRolePrimeDBMockHasCategoryRoleExpectationOrigins
	results            *CategoryRolePrimeDBMockHasCategoryRoleResults
	returnOrigin       string
	Counter            uint64
}

// CategoryRolePrimeDBMockHasCategoryRoleParams contains parameters of the CategoryRolePrimeDB.HasCategoryRole
type CategoryRolePrimeDBMockHasCategoryRoleParams struct {
	categoryID int64
	roleID     int64
}

// CategoryRolePrimeDBMockHasCategoryRoleParamPtrs contains pointers to parameters of the CategoryRolePrimeDB.HasCategoryRole
type CategoryRolePrimeDBMockHasCategoryRoleParamPtrs struct {
	categoryID *int64
	roleID     *int64
}

// CategoryRolePrimeDBMockHasCategoryRoleResults contains results of the CategoryRolePrimeDB.HasCategoryRole
type CategoryRolePrimeDBMockHasCategoryRoleResults struct {
	b1  bool
	err error
}

// CategoryRolePrimeDBMockHasCategoryRoleOrigins contains origins of expectations of the CategoryRolePrimeDB.HasCategoryRole
type CategoryRolePrimeDBMockHasCategoryRoleExpectationOrigins struct {
	origin           string
	originCategoryID string
	originRoleID     string
}

// Marks this method to be optional. The default behavior of any method with Return() is '1 or more', meaning
// the test will fail minimock's automatic final call check if the mocked method was not called at least once.
// Optional() makes method check to work in '0 or more' mode.
// It is NOT RECOMMENDED to use this option unless you really need it, as default behaviour helps to
// catch the problems when the expected method call is totally skipped during test run.
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Optional() *mCategoryRolePrimeDBMockHasCategoryRole {
	mmHasCategoryRole.optional = true
	return mmHasCategoryRole
}

// Expect sets up expected params for CategoryRolePrimeDB.HasCategoryRole
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Expect(categoryID int64, roleID int64) *mCategoryRolePrimeDBMockHasCategoryRole {
	if mmHasCategoryRole.mock.funcHasCategoryRole != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Set")
	}

	if mmHasCategoryRole.defaultExpectation == nil {
		mmHasCategoryRole.defaultExpectation = &CategoryRolePrimeDBMockHasCategoryRoleExpectation{}
	}

	if mmHasCategoryRole.defaultExpectation.paramPtrs != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by ExpectParams functions")
	}

	mmHasCategoryRole.defaultExpectation.params = &CategoryRolePrimeDBMockHasCategoryRoleParams{categoryID, roleID}
	mmHasCategoryRole.defaultExpectation.expectationOrigins.origin = minimock.CallerInfo(1)
	for _, e := range mmHasCategoryRole.expectations {
		if minimock.Equal(e.params, mmHasCategoryRole.defaultExpectation.params) {
			mmHasCategoryRole.mock.t.Fatalf("Expectation set by When has same params: %#v", *mmHasCategoryRole.defaultExpectation.params)
		}
	}

	return mmHasCategoryRole
}

// ExpectCategoryIDParam1 sets up expected param categoryID for CategoryRolePrimeDB.HasCategoryRole
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) ExpectCategoryIDParam1(categoryID int64) *mCategoryRolePrimeDBMockHasCategoryRole {
	if mmHasCategoryRole.mock.funcHasCategoryRole != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Set")
	}

	if mmHasCategoryRole.defaultExpectation == nil {
		mmHasCategoryRole.defaultExpectation = &CategoryRolePrimeDBMockHasCategoryRoleExpectation{}
	}

	if mmHasCategoryRole.defaultExpectation.params != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Expect")
	}

	if mmHasCategoryRole.defaultExpectation.paramPtrs == nil {
		mmHasCategoryRole.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockHasCategoryRoleParamPtrs{}
	}
	mmHasCategoryRole.defaultExpectation.paramPtrs.categoryID = &categoryID
	mmHasCategoryRole.defaultExpectation.expectationOrigins.originCategoryID = minimock.CallerInfo(1)

	return mmHasCategoryRole
}

// ExpectRoleIDParam2 sets up expected param roleID for CategoryRolePrimeDB.HasCategoryRole
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) ExpectRoleIDParam2(roleID int64) *mCategoryRolePrimeDBMockHasCategoryRole {
	if mmHasCategoryRole.mock.funcHasCategoryRole != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Set")
	}

	if mmHasCategoryRole.defaultExpectation == nil {
		mmHasCategoryRole.defaultExpectation = &CategoryRolePrimeDBMockHasCategoryRoleExpectation{}
	}

	if mmHasCategoryRole.defaultExpectation.params != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Expect")
	}

	if mmHasCategoryRole.defaultExpectation.paramPtrs == nil {
		mmHasCategoryRole.defaultExpectation.paramPtrs = &CategoryRolePrimeDBMockHasCategoryRoleParamPtrs{}
	}
	mmHasCategoryRole.defaultExpectation.paramPtrs.roleID = &roleID
	mmHasCategoryRole.defaultExpectation.expectationOrigins.originRoleID = minimock.CallerInfo(1)

	return mmHasCategoryRole
}

// Inspect accepts an inspector function that has same arguments as the CategoryRolePrimeDB.HasCategoryRole
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Inspect(f func(categoryID int64, roleID int64)) *mCategoryRolePrimeDBMockHasCategoryRole {
	if mmHasCategoryRole.mock.inspectFuncHasCategoryRole != nil {
		mmHasCategoryRole.mock.t.Fatalf("Inspect function is already set for CategoryRolePrimeDBMock.HasCategoryRole")
	}

	mmHasCategoryRole.mock.inspectFuncHasCategoryRole = f

	return mmHasCategoryRole
}

// Return sets up results that will be returned by CategoryRolePrimeDB.HasCategoryRole
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Return(b1 bool, err error) *CategoryRolePrimeDBMock {
	if mmHasCategoryRole.mock.funcHasCategoryRole != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Set")
	}

	if mmHasCategoryRole.defaultExpectation == nil {
		mmHasCategoryRole.defaultExpectation = &CategoryRolePrimeDBMockHasCategoryRoleExpectation{mock: mmHasCategoryRole.mock}
	}
	mmHasCategoryRole.defaultExpectation.results = &CategoryRolePrimeDBMockHasCategoryRoleResults{b1, err}
	mmHasCategoryRole.defaultExpectation.returnOrigin = minimock.CallerInfo(1)
	return mmHasCategoryRole.mock
}

// Set uses given function f to mock the CategoryRolePrimeDB.HasCategoryRole method
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Set(f func(categoryID int64, roleID int64) (b1 bool, err error)) *CategoryRolePrimeDBMock {
	if mmHasCategoryRole.defaultExpectation != nil {
		mmHasCategoryRole.mock.t.Fatalf("Default expectation is already set for the CategoryRolePrimeDB.HasCategoryRole method")
	}

	if len(mmHasCategoryRole.expectations) > 0 {
		mmHasCategoryRole.mock.t.Fatalf("Some expectations are already set for the CategoryRolePrimeDB.HasCategoryRole method")
	}

	mmHasCategoryRole.mock.funcHasCategoryRole = f
	mmHasCategoryRole.mock.funcHasCategoryRoleOrigin = minimock.CallerInfo(1)
	return mmHasCategoryRole.mock
}

// When sets expectation for the CategoryRolePrimeDB.HasCategoryRole which will trigger the result defined by the following
// Then helper
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) When(categoryID int64, roleID int64) *CategoryRolePrimeDBMockHasCategoryRoleExpectation {
	if mmHasCategoryRole.mock.funcHasCategoryRole != nil {
		mmHasCategoryRole.mock.t.Fatalf("CategoryRolePrimeDBMock.HasCategoryRole mock is already set by Set")
	}

	expectation := &CategoryRolePrimeDBMockHasCategoryRoleExpectation{
		mock:               mmHasCategoryRole.mock,
		params:             &CategoryRolePrimeDBMockHasCategoryRoleParams{categoryID, roleID},
		expectationOrigins: CategoryRolePrimeDBMockHasCategoryRoleExpectationOrigins{origin: minimock.CallerInfo(1)},
	}
	mmHasCategoryRole.expectations = append(mmHasCategoryRole.expectations, expectation)
	return expectation
}

// Then sets up CategoryRolePrimeDB.HasCategoryRole return parameters for the expectation previously defined by the When method
func (e *CategoryRolePrimeDBMockHasCategoryRoleExpectation) Then(b1 bool, err error) *CategoryRolePrimeDBMock {
	e.results = &CategoryRolePrimeDBMockHasCategoryRoleResults{b1, err}
	return e.mock
}

// Times sets number of times CategoryRolePrimeDB.HasCategoryRole should be invoked
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Times(n uint64) *mCategoryRolePrimeDBMockHasCategoryRole {
	if n == 0 {
		mmHasCategoryRole.mock.t.Fatalf("Times of CategoryRolePrimeDBMock.HasCategoryRole mock can not be zero")
	}
	mm_atomic.StoreUint64(&mmHasCategoryRole.expectedInvocations, n)
	mmHasCategoryRole.expectedInvocationsOrigin = minimock.CallerInfo(1)
	return mmHasCategoryRole
}

func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) invocationsDone() bool {
	if len(mmHasCategoryRole.expectations) == 0 && mmHasCategoryRole.defaultExpectation == nil && mmHasCategoryRole.mock.funcHasCategoryRole == nil {
		return true
	}

	totalInvocations := mm_atomic.LoadUint64(&mmHasCategoryRole.mock.afterHasCategoryRoleCounter)
	expectedInvocations := mm_atomic.LoadUint64(&mmHasCategoryRole.expectedInvocations)

	return totalInvocations > 0 && (expectedInvocations == 0 || expectedInvocations == totalInvocations)
}

// HasCategoryRole implements mm_repository.CategoryRolePrimeDB
func (mmHasCategoryRole *CategoryRolePrimeDBMock) HasCategoryRole(categoryID int64, roleID int64) (b1 bool, err error) {
	mm_atomic.AddUint64(&mmHasCategoryRole.beforeHasCategoryRoleCounter, 1)
	defer mm_atomic.AddUint64(&mmHasCategoryRole.afterHasCategoryRoleCounter, 1)

	mmHasCategoryRole.t.Helper()

	if mmHasCategoryRole.inspectFuncHasCategoryRole != nil {
		mmHasCategoryRole.inspectFuncHasCategoryRole(categoryID, roleID)
	}

	mm_params := CategoryRolePrimeDBMockHasCategoryRoleParams{categoryID, roleID}

	// Record call args
	mmHasCategoryRole.HasCategoryRoleMock.mutex.Lock()
	mmHasCategoryRole.HasCategoryRoleMock.callArgs = append(mmHasCategoryRole.HasCategoryRoleMock.callArgs, &mm_params)
	mmHasCategoryRole.HasCategoryRoleMock.mutex.Unlock()

	for _, e := range mmHasCategoryRole.HasCategoryRoleMock.expectations {
		if minimock.Equal(*e.params, mm_params) {
			mm_atomic.AddUint64(&e.Counter, 1)
			return e.results.b1, e.results.err
		}
	}

	if mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation != nil {
		mm_atomic.AddUint64(&mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.Counter, 1)
		mm_want := mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.params
		mm_want_ptrs := mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.paramPtrs

		mm_got := CategoryRolePrimeDBMockHasCategoryRoleParams{categoryID, roleID}

		if mm_want_ptrs != nil {

			if mm_want_ptrs.categoryID != nil && !minimock.Equal(*mm_want_ptrs.categoryID, mm_got.categoryID) {
				mmHasCategoryRole.t.Errorf("CategoryRolePrimeDBMock.HasCategoryRole got unexpected parameter categoryID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.expectationOrigins.originCategoryID, *mm_want_ptrs.categoryID, mm_got.categoryID, minimock.Diff(*mm_want_ptrs.categoryID, mm_got.categoryID))
			}

			if mm_want_ptrs.roleID != nil && !minimock.Equal(*mm_want_ptrs.roleID, mm_got.roleID) {
				mmHasCategoryRole.t.Errorf("CategoryRolePrimeDBMock.HasCategoryRole got unexpected parameter roleID, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
					mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.expectationOrigins.originRoleID, *mm_want_ptrs.roleID, mm_got.roleID, minimock.Diff(*mm_want_ptrs.roleID, mm_got.roleID))
			}

		} else if mm_want != nil && !minimock.Equal(*mm_want, mm_got) {
			mmHasCategoryRole.t.Errorf("CategoryRolePrimeDBMock.HasCategoryRole got unexpected parameters, expected at\n%s:\nwant: %#v\n got: %#v%s\n",
				mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.expectationOrigins.origin, *mm_want, mm_got, minimock.Diff(*mm_want, mm_got))
		}

		mm_results := mmHasCategoryRole.HasCategoryRoleMock.defaultExpectation.results
		if mm_results == nil {
			mmHasCategoryRole.t.Fatal("No results are set for the CategoryRolePrimeDBMock.HasCategoryRole")
		}
		return (*mm_results).b1, (*mm_results).err
	}
	if mmHasCategoryRole.funcHasCategoryRole != nil {
		return mmHasCategoryRole.funcHasCategoryRole(categoryID, roleID)
	}
	mmHasCategoryRole.t.Fatalf("Unexpected call to CategoryRolePrimeDBMock.HasCategoryRole. %v %v", categoryID, roleID)
	return
}

// HasCategoryRoleAfterCounter returns a count of finished CategoryRolePrimeDBMock.HasCategoryRole invocations
func (mmHasCategoryRole *CategoryRolePrimeDBMock) HasCategoryRoleAfterCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryRole.afterHasCategoryRoleCounter)
}

// HasCategoryRoleBeforeCounter returns a count of CategoryRolePrimeDBMock.HasCategoryRole invocations
func (mmHasCategoryRole *CategoryRolePrimeDBMock) HasCategoryRoleBeforeCounter() uint64 {
	return mm_atomic.LoadUint64(&mmHasCategoryRole.beforeHasCategoryRoleCounter)
}

// Calls returns a list of arguments used in each call to CategoryRolePrimeDBMock.HasCategoryRole.
// The list is in the same order as the calls were made (i.e. recent calls have a higher index)
func (mmHasCategoryRole *mCategoryRolePrimeDBMockHasCategoryRole) Calls() []*CategoryRolePrimeDBMockHasCategoryRoleParams {
	mmHasCategoryRole.mutex.RLock()

	argCopy := make([]*CategoryRolePrimeDBMockHasCategoryRoleParams, len(mmHasCategoryRole.callArgs))
	copy(argCopy, mmHasCategoryRole.callArgs)

	mmHasCategoryRole.mutex.RUnlock()

	return argCopy
}

// MinimockHasCategoryRoleDone returns true if the count of the HasCategoryRole invocations corresponds
// the number of defined expectations
func (m *CategoryRolePrimeDBMock) MinimockHasCategoryRoleDone() bool {
	if m.HasCategoryRoleMock.optional {
		// Optional methods provide '0 or more' call count restriction.
		return true
	}

	for _, e := range m.HasCategoryRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			return false
		}
	}

	return m.HasCategoryRoleMock.invocationsDone()
}

// MinimockHasCategoryRoleInspect logs each unmet expectation
func (m *CategoryRolePrimeDBMock) MinimockHasCategoryRoleInspect() {
	for _, e := range m.HasCategoryRoleMock.expectations {
		if mm_atomic.LoadUint64(&e.Counter) < 1 {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.HasCategoryRole at\n%s with params: %#v", e.expectationOrigins.origin, *e.params)
		}
	}

	afterHasCategoryRoleCounter := mm_atomic.LoadUint64(&m.afterHasCategoryRoleCounter)
	// if default expectation was set then invocations count should be greater than zero
	if m.HasCategoryRoleMock.defaultExpectation != nil && afterHasCategoryRoleCounter < 1 {
		if m.HasCategoryRoleMock.defaultExpectation.params == nil {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.HasCategoryRole at\n%s", m.HasCategoryRoleMock.defaultExpectation.returnOrigin)
		} else {
			m.t.Errorf("Expected call to CategoryRolePrimeDBMock.HasCategoryRole at\n%s with params: %#v", m.HasCategoryRoleMock.defaultExpectation.expectationOrigins.origin, *m.HasCategoryRoleMock.defaultExpectation.params)
		}
	}
	// if func was set then invocations count should be greater than zero
	if m.funcHasCategoryRole != nil && afterHasCategoryRoleCounter < 1 {
		m.t.Errorf("Expected call to CategoryRolePrimeDBMock.HasCategoryRole at\n%s", m.funcHasCategoryRoleOrigin)
	}

	if !m.HasCategoryRoleMock.invocationsDone() && afterHasCategoryRoleCounter > 0 {
		m.t.Errorf("Expected %d calls to CategoryRolePrimeDBMock.HasCategoryRole at\n%s but found %d calls",
			mm_atomic.LoadUint64(&m.HasCategoryRoleMock.expectedInvocations), m.HasCategoryRoleMock.expectedInvocationsOrigin, afterHasCategoryRoleCounter)
	}
}

// MinimockFinish checks that all mocked methods have been called the expected number of times
func (m *CategoryRolePrimeDBMock) MinimockFinish() {
	m.finishOnce.Do(func() {
		if !m.minimockDone() {
			m.MinimockCreateInspect()

			m.MinimockDeleteInspect()

			m.MinimockGetAllInspect()

			m.MinimockGetByCategoryIDInspect()

			m.MinimockGetByIDInspect()

			m.MinimockGetByRoleIDInspect()

			m.MinimockHasCategoryRoleInspect()
		}
	})
}

// MinimockWait waits for all mocked methods to be called the expected number of times
func (m *CategoryRolePrimeDBMock) MinimockWait(timeout mm_time.Duration) {
	timeoutCh := mm_time.After(timeout)
	for {
		if m.minimockDone() {
			return
		}
		select {
		case <-timeoutCh:
			m.MinimockFinish()
			return
		case <-mm_time.After(10 * mm_time.Millisecond):
		}
	}
}

func (m *CategoryRolePrimeDBMock) minimockDone() bool {
	done := true
	return done &&
		m.MinimockCreateDone() &&
		m.MinimockDeleteDone() &&
		m.MinimockGetAllDone() &&
		m.MinimockGetByCategoryIDDone() &&
		m.MinimockGetByIDDone() &&
		m.MinimockGetByRoleIDDone() &&
		m.MinimockHasCategoryRoleDone()
}
