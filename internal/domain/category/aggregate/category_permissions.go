package aggregate

type CategoryPermissionAggregate struct {
	categoryID          int64
	permissions         map[int64]struct{}
	roles               map[int64]struct{}
	permissionCache     map[string][]int64
	existingPermissions map[int64]map[int64]struct{} // categoryID -> permissionIDs
}

func NewCategoryPermissionAggregate(categoryID int64) *CategoryPermissionAggregate {
	return &CategoryPermissionAggregate{
		categoryID:          categoryID,
		permissions:         make(map[int64]struct{}),
		roles:               make(map[int64]struct{}),
		permissionCache:     make(map[string][]int64),
		existingPermissions: make(map[int64]map[int64]struct{}),
	}
}

func (a *CategoryPermissionAggregate) AddRoleID(roleID int64) {
	a.roles[roleID] = struct{}{}
}

func (a *CategoryPermissionAggregate) AddPermissionID(permissionID int64) {
	a.permissions[permissionID] = struct{}{}
}

func (a *CategoryPermissionAggregate) GetPermissionIDs() []int64 {
	ids := make([]int64, 0, len(a.permissions))
	for id := range a.permissions {
		ids = append(ids, id)
	}
	return ids
}

func (a *CategoryPermissionAggregate) GetRoleIDs() []int64 {
	ids := make([]int64, 0, len(a.roles))
	for id := range a.roles {
		ids = append(ids, id)
	}
	return ids
}

func (a *CategoryPermissionAggregate) CachePermissionIDs(name string, ids []int64) {
	a.permissionCache[name] = ids
}

func (a *CategoryPermissionAggregate) GetCachedPermissionIDs(name string) ([]int64, bool) {
	ids, exists := a.permissionCache[name]
	return ids, exists
}

func (a *CategoryPermissionAggregate) CacheExistingPermissions(relations map[int64]map[int64]struct{}) {
	a.existingPermissions = relations
}

func (a *CategoryPermissionAggregate) HasExistingPermission(categoryID, permissionID int64) bool {
	if perms, ok := a.existingPermissions[categoryID]; ok {
		_, exists := perms[permissionID]
		return exists
	}
	return false
}
