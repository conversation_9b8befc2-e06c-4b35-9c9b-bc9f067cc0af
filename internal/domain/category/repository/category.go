package repository

import (
	"context"

	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
)

//go:generate minimock -i CategoryPrimeDB -o ../mocks/category_prime_db_mock.go -s _mock.go
type CategoryPrimeDB interface {
	Create(category categoryentity.Category) (categoryentity.Category, error)
	GetAll() ([]categoryentity.Category, error)
	GetByID(id int64) (categoryentity.Category, error)
	GetByName(name string) (categoryentity.Category, error)
	Update(category categoryentity.Category) (categoryentity.Category, error)
	Delete(ctx context.Context, id int64) error
}

//go:generate minimock -i CategoryCache -o ../mocks/category_cache_mock.go -s _mock.go
type CategoryCache interface {
	GetCategories(ctx context.Context) ([]categoryentity.Category, error)
	GetCategory(ctx context.Context, id int64) (categoryentity.Category, error)
	SetCategories(ctx context.Context, categories []categoryentity.Category) error
	SetCategory(ctx context.Context, category categoryentity.Category) error
	DeleteCategories(ctx context.Context, ids []int64) error
	DeleteCategory(ctx context.Context, id int64) error
}
