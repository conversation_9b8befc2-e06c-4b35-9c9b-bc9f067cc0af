package repository

import (
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
)

//go:generate minimock -i CategoryRolePrimeDB -o ../mocks/category_role_prime_db_mock.go -s _mock.go
type CategoryRolePrimeDB interface {
	Create(categoryRole categoryentity.CategoryRoleLink) (categoryentity.CategoryRoleLink, error)
	GetAll() ([]categoryentity.CategoryRoleLink, error)
	GetByCategoryID(categoryID int64) ([]categoryentity.CategoryRoleLink, error)
	GetByID(id int64) (categoryentity.CategoryRoleLink, error)
	GetByRoleID(roleID int64) ([]categoryentity.CategoryRoleLink, error)
	HasCategoryRole(categoryID int64, roleID int64) (bool, error)
	Delete(categoryID int64, roleID int64) error
}

//go:generate minimock -i CategoryRoleCache -o ../mocks/category_role_cache_mock.go -s _mock.go
type CategoryRoleCache interface {
	GetAll() ([]categoryentity.CategoryRoleLink, error)
	GetByCategoryID(categoryID int64) ([]categoryentity.CategoryRoleLink, error)
	GetByID(id int64) (categoryentity.CategoryRoleLink, error)
	Set(categoryRole categoryentity.CategoryRoleLink) error
}
