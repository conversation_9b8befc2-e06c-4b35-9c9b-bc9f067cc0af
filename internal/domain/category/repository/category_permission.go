package repository

import (
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
)

//go:generate minimock -i CategoryPermissionPrimeDB -o ../mocks/category_permission_prime_db_mock.go -s _mock.go
type CategoryPermissionPrimeDB interface {
	Create(categoryPermission categoryentity.CategoryPermission) (categoryentity.CategoryPermission, error)
	GetAll() ([]categoryentity.CategoryPermission, error)
	GetByCategoryID(categoryID int64) ([]categoryentity.CategoryPermission, error)
	GetByID(id int64) (categoryentity.CategoryPermission, error)
	HasCategoryPermission(categoryID int64, permissionID int64) (bool, error)
	DeleteCategoryPermission(categoryID int64, permissionID int64) error
}

//go:generate minimock -i CategoryPermissionCache -o ../mocks/category_permission_cache_mock.go -s _mock.go
type CategoryPermissionCache interface {
	GetAll() ([]categoryentity.CategoryPermission, error)
	GetByCategoryID(categoryID int64) ([]categoryentity.CategoryPermission, error)
	GetByID(id int64) (categoryentity.CategoryPermission, error)
	Set(categoryPermission categoryentity.CategoryPermission) error
}
