package repository

import (
	categoryentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/category/entity"
)

//go:generate minimock -i CategoryGroupPrimeDB -o ../mocks/category_group_prime_db_mock.go -s _mock.go
type CategoryGroupPrimeDB interface {
	Create(categoryGroup categoryentity.CategoryGroupLink) (categoryentity.CategoryGroupLink, error)
	GetAll() ([]categoryentity.CategoryGroupLink, error)
	GetByCategoryID(categoryID int64) ([]categoryentity.CategoryGroupLink, error)
	GetByID(id int64) (categoryentity.CategoryGroupLink, error)
	HasCategoryGroup(categoryID int64, groupID int64) (bool, error)
	Delete(categoryID int64, groupID int64) error
}

//go:generate minimock -i CategoryGroupCache -o ../mocks/category_group_cache_mock.go -s _mock.go
type CategoryGroupCache interface {
	GetAll() ([]categoryentity.CategoryGroupLink, error)
	GetByCategoryID(categoryID int64) ([]categoryentity.CategoryGroupLink, error)
	GetByID(id int64) (categoryentity.CategoryGroupLink, error)
	Set(categoryGroup categoryentity.CategoryGroupLink) error
}
