package entity

import (
	groupentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/group/entity"
	permissionentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/permission/entity"
	roleentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/role/entity"
)

type Category struct {
	ID   int64
	Name string
}

type CategoryGroupLink struct {
	ID         int64
	CategoryID int64
	GroupID    int64
}

type CategoryRoleLink struct {
	ID         int64
	CategoryID int64
	RoleID     int64
}

type CategoryPermission struct {
	ID           int64
	CategoryID   int64
	PermissionID int64
}

type CategoryWithPermissions struct {
	CategoryID  int64
	Permissions []permissionentity.Permission
}

type CategoryFull struct {
	ID     int64
	Name   string
	Groups *[]groupentity.GroupShort
	Roles  *[]roleentity.RoleWithStats
	Users  *[]int64
}
