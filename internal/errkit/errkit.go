package errkit

import (
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"sync"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/serror"
)

// Generic Registry for faster parsing
var (
	errorRegistry *serror.Registry[error]
	errInstances  map[string]error
	registryOnce  sync.Once
)

// ErrorCode represents error codes
type ErrorCode string

// Constants for ErrorCode type
const (
	ErrorCodeBadRequest          ErrorCode = "BadRequest"
	ErrorCodeConflict            ErrorCode = "Conflict"
	ErrorCodeErrorResponseDTO    ErrorCode = "ErrorResponseDTO"
	ErrorCodeForbidden           ErrorCode = "Forbidden"
	ErrorCodeInternalError       ErrorCode = "InternalError"
	ErrorCodeInternalServerError ErrorCode = "InternalServerError"
	ErrorCodeNotFound            ErrorCode = "NotFound"
	ErrorCodeObjectAlreadyExist  ErrorCode = "ObjectAlreadyExist"
	ErrorCodeObjectInvalidData   ErrorCode = "ObjectInvalidData"
	ErrorCodeObjectNotFound      ErrorCode = "ObjectNotFound"
	ErrorCodeObjectProtected     ErrorCode = "ObjectProtected"
	ErrorCodeResponseDTO         ErrorCode = "ResponseDTO"
	ErrorCodeTooManyRequests     ErrorCode = "TooManyRequests"
	ErrorCodeUnauthorized        ErrorCode = "Unauthorized"
	ErrorCodeUnknown             ErrorCode = "Unknown"
)

// String returns string representation
func (e ErrorCode) String() string {
	return string(e)
}

// ObjectType represents object names
type ObjectType string

// Constants for ObjectType type
const (
	ObjectTypeAPI              ObjectType = "api"
	ObjectTypeAuthentication   ObjectType = "authentication"
	ObjectTypeAuthorization    ObjectType = "authorization"
	ObjectTypeCategory         ObjectType = "category"
	ObjectTypeData             ObjectType = "data"
	ObjectTypeGroup            ObjectType = "group"
	ObjectTypeIdentityProvider ObjectType = "identityprovider"
	ObjectTypeItem             ObjectType = "item"
	ObjectTypeParticipant      ObjectType = "participant"
	ObjectTypePermission       ObjectType = "permission"
	ObjectTypeProduct          ObjectType = "product"
	ObjectTypeProposal         ObjectType = "proposal"
	ObjectTypeRequest          ObjectType = "request"
	ObjectTypeResource         ObjectType = "resource"
	ObjectTypeRole             ObjectType = "role"
	ObjectTypeServer           ObjectType = "server"
	ObjectTypeService          ObjectType = "service"
	ObjectTypeUser             ObjectType = "user"
	ObjectTypeValidation       ObjectType = "validation"
)

func (o ObjectType) String() string {
	return string(o)
}

// State represents error states
type State string

// Constants for State type
const (
	StateAlreadyExists    State = "already exists"
	StateConflict         State = "conflict"
	StateConnectionFailed State = "connection failed"
	StateDatabaseError    State = "database error"
	StateEmptyField       State = "empty field"
	StateInternalError    State = "internal error"
	StateInvalidData      State = "invalid data"
	StateNotFound         State = "not found"
	StatePermissionDenied State = "permission denied"
	StateProcessingError  State = "processing error"
	StateProtected        State = "protected"
	StateTimeout          State = "timeout"
	StateUnauthorized     State = "unauthorized"
	StateValidationFailed State = "validation failed"
)

// NewState creates a new State from custom string
func NewState(message string) State {
	return State(message)
}

func (s State) String() string {
	return string(s)
}

// ArgSuccessExit represents successful argument processing exit
type ArgSuccessExit struct {
	Message string
}

// NewArgSuccessExit creates new argument success exit error
func NewArgSuccessExit(message string) *ArgSuccessExit {
	return &ArgSuccessExit{Message: message}
}

func (e *ArgSuccessExit) Error() string {
	if e.Message != "" {
		return e.Message
	}
	return "arg success exit"
}

// ErrorResponseDTO represents optimized error response structure
type ErrorResponseDTO struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

// NewErrorResponseDTO creates response error with constants
func NewErrorResponseDTO(status int, objectType ObjectType, objectID string, state State) *ErrorResponseDTO {
	var code ErrorCode
	switch status {
	case http.StatusBadRequest:
		code = ErrorCodeBadRequest
	case http.StatusUnauthorized:
		code = ErrorCodeUnauthorized
	case http.StatusForbidden:
		code = ErrorCodeForbidden
	case http.StatusNotFound:
		code = ErrorCodeNotFound
	case http.StatusConflict:
		code = ErrorCodeConflict
	case http.StatusTooManyRequests:
		code = ErrorCodeTooManyRequests
	case http.StatusInternalServerError:
		code = ErrorCodeInternalServerError
	default:
		code = ErrorCodeUnknown
	}

	return &ErrorResponseDTO{
		Base: serror.Base{
			Status:  status,
			Code:    code.String(),
			Message: fmt.Sprintf("request error for object %s (%s): %s", objectType, objectID, state),
		},
		ObjectType: objectType.String(),
		ObjectID:   objectID,
		State:      state.String(),
	}
}

// Error returns error message
func (e *ErrorResponseDTO) Error() string {
	return fmt.Sprintf("request error for object %s (%s): %s", e.ObjectType, e.ObjectID, e.State)
}

// InternalError represents optimized internal error structure
type InternalError struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

// NewInternalError creates internal error with constants
func NewInternalError(objectType ObjectType, objectID string, state State) *InternalError {
	return &InternalError{
		Base: serror.Base{
			Status:  http.StatusInternalServerError,
			Code:    ErrorCodeInternalError.String(),
			Message: fmt.Sprintf("object of type %s (%s) internal error: %s", objectType, objectID, state),
		},
		ObjectType: objectType.String(),
		ObjectID:   objectID,
		State:      state.String(),
	}
}

func (e *InternalError) Error() string {
	return fmt.Sprintf("object of type %s (%s) internal error: %s", e.ObjectType, e.ObjectID, e.State)
}

// ObjectAlreadyExist represents optimized "already exists" error structure
type ObjectAlreadyExist struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

// NewObjectAlreadyExist creates "already exists" error with constants
func NewObjectAlreadyExist(objectType ObjectType, objectID string, state State) *ObjectAlreadyExist {
	msg := fmt.Sprintf("%s already exist: %s", objectType, state)

	return &ObjectAlreadyExist{
		Base: serror.Base{
			Status:  http.StatusConflict,
			Code:    ErrorCodeObjectAlreadyExist.String(),
			Message: msg,
		},
		ObjectType: objectType.String(),
		ObjectID:   objectID,
		State:      state.String(),
	}
}

func (e *ObjectAlreadyExist) Error() string {
	return fmt.Sprintf("object of type %s (%s) already exist", e.ObjectType, e.ObjectID)
}

// ObjectInvalidData represents optimized invalid data error structure
type ObjectInvalidData struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

// NewObjectInvalidData creates invalid data error with constants
func NewObjectInvalidData(objectType ObjectType, objectValue string, state State) *ObjectInvalidData {
	msg := fmt.Sprintf("%s has invalid value=%s: %s", objectType, objectValue, state)

	return &ObjectInvalidData{
		Base: serror.Base{
			Status:  http.StatusBadRequest,
			Code:    ErrorCodeObjectInvalidData.String(),
			Message: msg,
		},
		ObjectType: objectType.String(),
		ObjectID:   objectValue,
		State:      state.String(),
	}
}

func (e *ObjectInvalidData) Error() string {
	return fmt.Sprintf("object of type %s (%s) has invalid value=%s", e.ObjectType, e.ObjectID, e.State)
}

// ObjectNotFound represents optimized "not found" error structure
type ObjectNotFound struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

// NewObjectNotFound creates "not found" error with constants
func NewObjectNotFound(objectType ObjectType, objectID string, state State) *ObjectNotFound {
	msg := fmt.Sprintf("%s not found by ID %s", objectType, objectID)

	return &ObjectNotFound{
		Base: serror.Base{
			Status:  http.StatusNotFound,
			Code:    ErrorCodeObjectNotFound.String(),
			Message: msg,
		},
		ObjectType: objectType.String(),
		ObjectID:   objectID,
		State:      state.String(),
	}
}

func (e *ObjectNotFound) Error() string {
	return fmt.Sprintf("object of type %s (%s) not found", e.ObjectType, e.ObjectID)
}

// ObjectProtected represents optimized protected object error structure
type ObjectProtected struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

// NewObjectProtected creates protected object error with constants
func NewObjectProtected(objectType ObjectType, objectID string, state State) *ObjectProtected {
	msg := fmt.Sprintf("%s %s is protected", objectType, objectID)

	return &ObjectProtected{
		Base: serror.Base{
			Status:  http.StatusBadRequest,
			Code:    ErrorCodeObjectProtected.String(),
			Message: msg,
		},
		ObjectType: objectType.String(),
		ObjectID:   objectID,
		State:      state.String(),
	}
}

func (e *ObjectProtected) Error() string {
	return fmt.Sprintf("object of type %s (%s) is protected", e.ObjectType, e.ObjectID)
}

// ResponseDTO represents optimized response structure
type ResponseDTO struct {
	serror.Base

	ObjectType string `json:"object_type"`
	ObjectID   string `json:"object_id"`
	State      string `json:"state"`
}

func (e *ResponseDTO) Error() string {
	return fmt.Sprintf("request for object %s (%s): %s", e.ObjectType, e.ObjectID, e.State)
}

type HttpStatuser interface {
	HttpStatus() int
}

// NewErrorResponseDTOString creates error with custom state string
func NewErrorResponseDTOString(status int, objectType ObjectType, objectID string, stateMessage string) *ErrorResponseDTO {
	return NewErrorResponseDTO(status, objectType, objectID, NewState(stateMessage))
}

// NewInternalErrorString creates internal error with custom state string
func NewInternalErrorString(objectType ObjectType, objectID string, stateMessage string) *InternalError {
	return NewInternalError(objectType, objectID, NewState(stateMessage))
}

// NewObjectAlreadyExistString creates "already exists" error with custom state string
func NewObjectAlreadyExistString(objectType ObjectType, objectID string, stateMessage string) *ObjectAlreadyExist {
	return NewObjectAlreadyExist(objectType, objectID, NewState(stateMessage))
}

// NewObjectInvalidDataString creates invalid data error with custom state string
func NewObjectInvalidDataString(objectType ObjectType, objectValue string, stateMessage string) *ObjectInvalidData {
	return NewObjectInvalidData(objectType, objectValue, NewState(stateMessage))
}

// NewObjectNotFoundString creates "not found" error with custom state string
func NewObjectNotFoundString(objectType ObjectType, objectID string, stateMessage string) *ObjectNotFound {
	return NewObjectNotFound(objectType, objectID, NewState(stateMessage))
}

// NewObjectProtectedString creates protected object error with custom state string
func NewObjectProtectedString(objectType ObjectType, objectID string, stateMessage string) *ObjectProtected {
	return NewObjectProtected(objectType, objectID, NewState(stateMessage))
}

// NewObjectValidation creates validation error with constants
func NewObjectValidation(objectType ObjectType, objectID string, state State) error {
	return NewErrorResponseDTO(http.StatusBadRequest, objectType, objectID, state)
}

// IsArgSuccessExitError checks if the error is an argument success exit error
func IsArgSuccessExitError(err error) bool {
	if err == nil {
		return false
	}

	// Check for typed error
	var argSuccessExit *ArgSuccessExit
	if errors.As(err, &argSuccessExit) {
		return true
	}

	// Check for error message for backward compatibility
	return err.Error() == "arg success exit"
}

// IsNotFoundError checks if the error is a "not found" error
func IsNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	// Check for typed errors
	var objectNotFound *ObjectNotFound
	if errors.As(err, &objectNotFound) {
		return true
	}

	// Check for ErrorResponseDTO with NotFound code
	var errResp *ErrorResponseDTO
	if errors.As(err, &errResp) {
		return errResp.Status == http.StatusNotFound
	}

	return false
}

// ExtractObjectType extracts object type with optimized generic interface
func ExtractObjectType(err error) (string, bool) {
	if err == nil {
		return "", false
	}

	// Direct check for our types (fast path) using errors.As
	var errResp *ErrorResponseDTO
	if errors.As(err, &errResp) {
		return errResp.ObjectType, true
	}

	var internalErr *InternalError
	if errors.As(err, &internalErr) {
		return internalErr.ObjectType, true
	}

	var alreadyExist *ObjectAlreadyExist
	if errors.As(err, &alreadyExist) {
		return alreadyExist.ObjectType, true
	}

	var invalidData *ObjectInvalidData
	if errors.As(err, &invalidData) {
		return invalidData.ObjectType, true
	}

	var notFound *ObjectNotFound
	if errors.As(err, &notFound) {
		return notFound.ObjectType, true
	}

	var protected *ObjectProtected
	if errors.As(err, &protected) {
		return protected.ObjectType, true
	}

	var responseDTO *ResponseDTO
	if errors.As(err, &responseDTO) {
		return responseDTO.ObjectType, true
	}

	// Fallback to reflection for unknown types
	val := reflect.ValueOf(err)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	objectTypeField := val.FieldByName("ObjectType")
	if !objectTypeField.IsValid() || objectTypeField.Kind() != reflect.String {
		return "", false
	}

	return objectTypeField.String(), true
}

func ExtractStatusCode(err error) (int, bool) {
	var hs HttpStatuser
	if errors.As(err, &hs) {
		return hs.HttpStatus(), true
	}
	return 0, false
}

// IdentifyAndConvertError optimized version preserving legacy logic
func IdentifyAndConvertError(err error) (any, bool) {
	if err == nil {
		return nil, false
	}

	val := reflect.ValueOf(err)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	baseField := val.FieldByName("Base")
	if !baseField.IsValid() {
		return nil, false
	}

	codeField := baseField.FieldByName("Code")
	if !codeField.IsValid() || codeField.Kind() != reflect.String {
		return nil, false
	}

	code := codeField.String()

	// Fast check for known types with known codes
	switch ErrorCode(code) {
	case ErrorCodeInternalError:
		var internalError *InternalError
		if errors.As(err, &internalError) {
			return err, true
		}
	case ErrorCodeObjectAlreadyExist:
		var objectAlreadyExist *ObjectAlreadyExist
		if errors.As(err, &objectAlreadyExist) {
			return err, true
		}
	case ErrorCodeObjectInvalidData:
		var objectInvalidData *ObjectInvalidData
		if errors.As(err, &objectInvalidData) {
			return err, true
		}
	case ErrorCodeObjectNotFound:
		var objectNotFound *ObjectNotFound
		if errors.As(err, &objectNotFound) {
			return err, true
		}
	case ErrorCodeObjectProtected:
		var objectProtected *ObjectProtected
		if errors.As(err, &objectProtected) {
			return err, true
		}
	case ErrorCodeResponseDTO:
		var responseDTO *ResponseDTO
		if errors.As(err, &responseDTO) {
			return err, true
		}
	case ErrorCodeErrorResponseDTO:
		var errorResponseDTO *ErrorResponseDTO
		if errors.As(err, &errorResponseDTO) {
			return err, true
		}
	}

	// Fallback to legacy reflection approach for unknown types and codes
	if instance, found := errInstances[code]; found {
		instanceType := reflect.TypeOf(instance).Elem()
		newInstance := reflect.New(instanceType).Interface()

		originalValue := reflect.ValueOf(err)
		if originalValue.Kind() == reflect.Ptr {
			originalValue = originalValue.Elem()
		}
		newValue := reflect.ValueOf(newInstance).Elem()

		// NOTE: Следующий цикл может привести к панике reflect.Set если типы полей исходной
		// и целевой структур не совместимы. Это может произойти только при создании
		// пользовательских структур с полем Base отличного от serror.Base типа,
		// что является невалидным использованием API.
		// В нормальном использовании все error структуры должны встраивать serror.Base.
		//
		// ВАЖНО: Тестирование этой панической ситуации невозможно без вызова панического
		// состояния в тестах, поэтому данная ветка остается непокрытой тестами.
		// Это считается приемлемым, поскольку ситуация является результатом неправильного
		// использования API разработчиком.
		for i := 0; i < originalValue.NumField(); i++ {
			newValue.Field(i).Set(originalValue.Field(i))
		}

		return newInstance, true
	}

	return nil, false
}

// Unmarshal provides backward compatible unmarshaling
func Unmarshal(body []byte) error {
	initErrorRegistry()
	return serror.Unmarshal(body, errInstances)
}

// UnmarshalFast provides optimized unmarshaling with registry
func UnmarshalFast(body []byte) (error, error) {
	initErrorRegistry()
	return errorRegistry.UnmarshalByCode(body)
}

// UnmarshalTyped provides generic direct parsing
func UnmarshalTyped[T error](body []byte) (*T, error) {
	return serror.UnmarshalTyped[T](body)
}

// initErrorRegistry provides lazy registry initialization
func initErrorRegistry() {
	registryOnce.Do(func() {
		errorRegistry = serror.NewRegistry[error]()

		// Register factory functions for fast creation
		errorRegistry.Register(ErrorCodeErrorResponseDTO.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeInternalError.String(), func() error { return &InternalError{} })
		errorRegistry.Register(ErrorCodeObjectAlreadyExist.String(), func() error { return &ObjectAlreadyExist{} })
		errorRegistry.Register(ErrorCodeObjectInvalidData.String(), func() error { return &ObjectInvalidData{} })
		errorRegistry.Register(ErrorCodeObjectNotFound.String(), func() error { return &ObjectNotFound{} })
		errorRegistry.Register(ErrorCodeObjectProtected.String(), func() error { return &ObjectProtected{} })
		errorRegistry.Register(ErrorCodeResponseDTO.String(), func() error { return &ResponseDTO{} })

		// Register codes for ErrorResponseDTO
		errorRegistry.Register(ErrorCodeBadRequest.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeUnauthorized.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeForbidden.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeNotFound.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeConflict.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeTooManyRequests.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeInternalServerError.String(), func() error { return &ErrorResponseDTO{} })
		errorRegistry.Register(ErrorCodeUnknown.String(), func() error { return &ErrorResponseDTO{} })

		// Legacy map for backward compatibility (without ErrorResponseDTO codes for testing)
		errInstances = map[string]error{
			ErrorCodeErrorResponseDTO.String():   &ErrorResponseDTO{},
			ErrorCodeInternalError.String():      &InternalError{},
			ErrorCodeObjectAlreadyExist.String(): &ObjectAlreadyExist{},
			ErrorCodeObjectInvalidData.String():  &ObjectInvalidData{},
			ErrorCodeObjectNotFound.String():     &ObjectNotFound{},
			ErrorCodeObjectProtected.String():    &ObjectProtected{},
			ErrorCodeResponseDTO.String():        &ResponseDTO{},
		}
	})
}
