package errkit

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/serror"
)

// Helper type for testing
type errorWithoutBase struct {
	Code string
}

func (e *errorWithoutBase) Error() string {
	return "error without base field"
}

// Helper type for testing Code field type
type errorWithInvalidCodeType struct {
	Base struct {
		Code int // Not a string
	}
}

func (e *errorWithInvalidCodeType) Error() string {
	return "error with invalid code type"
}

// Helper type for testing reflection fallback
type customErrorForTesting struct {
	ObjectType string
	State      string
}

func (e *customErrorForTesting) Error() string {
	return "custom error for testing"
}

// Additional helper types for testing edge cases
type testStructWithIntObjectType struct {
	ObjectType int
}

func (e *testStructWithIntObjectType) Error() string {
	return "test error with int ObjectType"
}

type testStructWithoutObjectType struct {
	Name string
}

func (e *testStructWithoutObjectType) Error() string {
	return "test error without ObjectType"
}

type testStructWithIntState struct {
	State int
}

func (e *testStructWithIntState) Error() string {
	return "test error with int State"
}

type testStructWithoutState struct {
	Name string
}

func (e *testStructWithoutState) Error() string {
	return "test error without State"
}

type unknownErrorStruct struct {
	Base struct {
		Code string
	}
}

func (e *unknownErrorStruct) Error() string {
	return "unknown error"
}

// Additional error types for comprehensive testing
type errorWithoutBaseField struct {
	Code string
}

func (e *errorWithoutBaseField) Error() string {
	return "error without base field"
}

type errorWithInvalidCodeField struct {
	Base struct {
		Code int
	}
}

func (e *errorWithInvalidCodeField) Error() string {
	return "error with invalid code field"
}

type customStructWithCodeField struct {
	Base struct {
		Code    string
		Status  int
		Message string
	}
	ObjectType string
	ObjectID   string
	State      string
}

func (e *customStructWithCodeField) Error() string {
	return "custom struct with code field"
}

// Структура для тестирования reflection copy с совместимыми полями
type compatibleErrorStruct struct {
	Base       serror.Base
	ObjectType string
	ObjectID   string
	State      string
}

func (e *compatibleErrorStruct) Error() string {
	return "compatible error struct"
}

// Типы для тестирования случаев когда errors.As возвращает false
type fakeInternalError struct {
	Base       serror.Base
	ObjectType string
	ObjectID   string
	State      string
}

func (e *fakeInternalError) Error() string {
	return "fake internal error"
}

type fakeObjectNotFound struct {
	Base       serror.Base
	ObjectType string
	ObjectID   string
	State      string
}

func (e *fakeObjectNotFound) Error() string {
	return "fake object not found"
}

// Тип для тестирования case ErrorCodeErrorResponseDTO
type fakeErrorResponseDTO struct {
	Base       serror.Base
	ObjectType string
	ObjectID   string
	State      string
}

func (e *fakeErrorResponseDTO) Error() string {
	return "fake error response DTO"
}

// --- Тесты конструкторов ---

func TestNewInternalError(t *testing.T) {
	objType, objID, state := ObjectTypeService, "testID", StateInternalError
	err := NewInternalError(objType, objID, state)

	assert.Equal(t, http.StatusInternalServerError, err.Base.Status)
	assert.Equal(t, "InternalError", err.Base.Code)
	assert.Contains(t, err.Base.Message, fmt.Sprintf("object of type %s (%s) internal error: %s", objType, objID, state))
	assert.Equal(t, objType.String(), err.ObjectType)
	assert.Equal(t, objID, err.ObjectID)
	assert.Equal(t, state.String(), err.State)
	assert.Equal(t, fmt.Sprintf("object of type %s (%s) internal error: %s", objType, objID, state), err.Error())
}

func TestNewObjectNotFound(t *testing.T) {
	objType, objID, state := ObjectTypeUser, "123", StateNotFound
	err := NewObjectNotFound(objType, objID, state)

	assert.Equal(t, http.StatusNotFound, err.Status)
	assert.Equal(t, "ObjectNotFound", err.Code)
	assert.Contains(t, err.Message, fmt.Sprintf("%s not found by ID %s", objType, objID))
	assert.Equal(t, objType.String(), err.ObjectType)
	assert.Equal(t, objID, err.ObjectID)
	assert.Equal(t, state.String(), err.State)
	assert.Equal(t, fmt.Sprintf("object of type %s (%s) not found", objType, objID), err.Error())
}

func TestNewObjectInvalidData(t *testing.T) {
	objType, objValue, state := ObjectTypeUser, "invalid-email@", StateValidationFailed
	err := NewObjectInvalidData(objType, objValue, state)

	assert.Equal(t, http.StatusBadRequest, err.Status)
	assert.Equal(t, "ObjectInvalidData", err.Code)
	assert.Contains(t, err.Message, fmt.Sprintf("%s has invalid value=%s: %s", objType, objValue, state))
	assert.Equal(t, objType.String(), err.ObjectType)
	assert.Equal(t, objValue, err.ObjectID) // Проверяем, что objValue используется как ObjectID
	assert.Equal(t, state.String(), err.State)
	assert.Equal(t, fmt.Sprintf("object of type %s (%s) has invalid value=%s", objType, objValue, state), err.Error())
}

func TestNewObjectAlreadyExist(t *testing.T) {
	objType, objID, state := ObjectTypeProduct, "prod-abc", StateAlreadyExists
	err := NewObjectAlreadyExist(objType, objID, state)

	assert.Equal(t, http.StatusConflict, err.Status)
	assert.Equal(t, "ObjectAlreadyExist", err.Code)
	assert.Contains(t, err.Message, fmt.Sprintf("%s already exist: %s", objType, state))
	assert.Equal(t, objType.String(), err.ObjectType)
	assert.Equal(t, objID, err.ObjectID)
	assert.Equal(t, state.String(), err.State)
	assert.Equal(t, fmt.Sprintf("object of type %s (%s) already exist", objType, objID), err.Error())
}

func TestNewObjectProtected(t *testing.T) {
	objType, objID, state := ObjectTypeAuthorization, "cfg-001", StateProtected
	err := NewObjectProtected(objType, objID, state)

	assert.Equal(t, http.StatusBadRequest, err.Status)
	assert.Equal(t, "ObjectProtected", err.Code)
	assert.Contains(t, err.Message, fmt.Sprintf("%s %s is protected", objType, objID))
	assert.Equal(t, objType.String(), err.ObjectType)
	assert.Equal(t, objID, err.ObjectID)
	assert.Equal(t, state.String(), err.State)
	assert.Equal(t, fmt.Sprintf("object of type %s (%s) is protected", objType, objID), err.Error())
}

func TestNewErrorResponseDTO(t *testing.T) {
	tests := []struct {
		name         string
		status       int
		objType      string
		objID        string
		state        string
		expectedCode string
	}{
		{
			name:         "BadRequest status",
			status:       http.StatusBadRequest,
			objType:      "request",
			objID:        "req-1",
			state:        "invalid format",
			expectedCode: "BadRequest",
		},
		{
			name:         "Unauthorized status",
			status:       http.StatusUnauthorized,
			objType:      "authentication",
			objID:        "token-123",
			state:        "expired",
			expectedCode: "Unauthorized",
		},
		{
			name:         "Forbidden status",
			status:       http.StatusForbidden,
			objType:      "resource",
			objID:        "res-xyz",
			state:        "access denied",
			expectedCode: "Forbidden",
		},
		{
			name:         "NotFound status",
			status:       http.StatusNotFound,
			objType:      "item",
			objID:        "item-404",
			state:        "missing",
			expectedCode: "NotFound",
		},
		{
			name:         "Conflict status",
			status:       http.StatusConflict,
			objType:      "data",
			objID:        "data-conflict",
			state:        "duplicate",
			expectedCode: "Conflict",
		},
		{
			name:         "TooManyRequests status",
			status:       http.StatusTooManyRequests,
			objType:      "api",
			objID:        "endpoint-1",
			state:        "rate limit",
			expectedCode: "TooManyRequests",
		},
		{
			name:         "InternalServerError status",
			status:       http.StatusInternalServerError,
			objType:      "server",
			objID:        "srv-1",
			state:        "crash",
			expectedCode: "InternalServerError",
		},
		{
			name:         "Unknown status",
			status:       599, // Custom status code
			objType:      "custom",
			objID:        "cust-1",
			state:        "unknown error",
			expectedCode: "Unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := NewErrorResponseDTO(tt.status, ObjectType(tt.objType), tt.objID, State(tt.state))

			assert.Equal(t, tt.status, err.Status)
			assert.Equal(t, tt.expectedCode, err.Code)
			assert.Contains(t, err.Message, fmt.Sprintf("request error for object %s (%s): %s", tt.objType, tt.objID, tt.state))
			assert.Equal(t, tt.objType, err.ObjectType)
			assert.Equal(t, tt.objID, err.ObjectID)
			assert.Equal(t, tt.state, err.State)
			assert.Equal(t, fmt.Sprintf("request error for object %s (%s): %s", tt.objType, tt.objID, tt.state), err.Error())
		})
	}
}

func TestNewObjectValidation(t *testing.T) {
	objType, objID, state := ObjectTypeValidation, "valid-1", StateValidationFailed
	err := NewObjectValidation(objType, objID, state)

	require.NotNil(t, err)

	// Проверяем что это ErrorResponseDTO с BadRequest статусом
	var errResp *ErrorResponseDTO
	ok := errors.As(err, &errResp)
	require.True(t, ok, "Ожидался тип *ErrorResponseDTO")

	assert.Equal(t, http.StatusBadRequest, errResp.Status)
	assert.Equal(t, "BadRequest", errResp.Code)
	assert.Equal(t, objType.String(), errResp.ObjectType)
	assert.Equal(t, objID, errResp.ObjectID)
	assert.Equal(t, state.String(), errResp.State)
}

// --- Тесты новых констант ---

func TestNewConstants(t *testing.T) {
	t.Run("New API with constants", func(t *testing.T) {
		err := NewObjectNotFound(ObjectTypeProduct, "prod-123", StateNotFound)

		assert.Equal(t, http.StatusNotFound, err.Status)
		assert.Equal(t, "ObjectNotFound", err.Code)
		assert.Equal(t, "product", err.ObjectType)
		assert.Equal(t, "prod-123", err.ObjectID)
		assert.Equal(t, "not found", err.State)
	})

	t.Run("Constants string conversion", func(t *testing.T) {
		assert.Equal(t, "product", ObjectTypeProduct.String())
		assert.Equal(t, "not found", StateNotFound.String())
		assert.Equal(t, "ObjectNotFound", ErrorCodeObjectNotFound.String())
	})

	t.Run("ErrorResponseDTO with constants", func(t *testing.T) {
		err := NewErrorResponseDTO(http.StatusBadRequest, ObjectTypeUser, "user-456", StateValidationFailed)

		assert.Equal(t, http.StatusBadRequest, err.Status)
		assert.Equal(t, "BadRequest", err.Code)
		assert.Equal(t, "user", err.ObjectType)
		assert.Equal(t, "user-456", err.ObjectID)
		assert.Equal(t, "validation failed", err.State)
	})
}

// --- Тесты NewState и String функций ---

func TestNewState(t *testing.T) {
	customMessage := "custom error from external service"
	state := NewState(customMessage)

	assert.Equal(t, customMessage, state.String())
	assert.Equal(t, State(customMessage), state)
}

func TestStringFunctions(t *testing.T) {
	t.Run("NewObjectNotFoundString with custom message", func(t *testing.T) {
		customError := "entity not found in database"
		err := NewObjectNotFoundString(ObjectTypeProduct, "prod-789", customError)

		assert.Equal(t, http.StatusNotFound, err.Status)
		assert.Equal(t, "ObjectNotFound", err.Code)
		assert.Equal(t, "product", err.ObjectType)
		assert.Equal(t, "prod-789", err.ObjectID)
		assert.Equal(t, customError, err.State)
	})

	t.Run("NewInternalErrorString with external error", func(t *testing.T) {
		externalError := "connection timeout to external API"
		err := NewInternalErrorString(ObjectTypeIdentityProvider, "idp-service", externalError)

		assert.Equal(t, http.StatusInternalServerError, err.Status)
		assert.Equal(t, "InternalError", err.Code)
		assert.Equal(t, "identityprovider", err.ObjectType)
		assert.Equal(t, "idp-service", err.ObjectID)
		assert.Equal(t, externalError, err.State)
	})

	t.Run("NewObjectAlreadyExistString with custom state", func(t *testing.T) {
		customState := "duplicate key constraint violation"
		err := NewObjectAlreadyExistString(ObjectTypeUser, "<EMAIL>", customState)

		assert.Equal(t, http.StatusConflict, err.Status)
		assert.Equal(t, "ObjectAlreadyExist", err.Code)
		assert.Equal(t, "user", err.ObjectType)
		assert.Equal(t, "<EMAIL>", err.ObjectID)
		assert.Equal(t, customState, err.State)
	})

	t.Run("NewObjectInvalidDataString with validation details", func(t *testing.T) {
		validationError := "email format is invalid: missing @ symbol"
		err := NewObjectInvalidDataString(ObjectTypeUser, "invalid-email", validationError)

		assert.Equal(t, http.StatusBadRequest, err.Status)
		assert.Equal(t, "ObjectInvalidData", err.Code)
		assert.Equal(t, "user", err.ObjectType)
		assert.Equal(t, "invalid-email", err.ObjectID)
		assert.Equal(t, validationError, err.State)
	})

	t.Run("NewObjectProtectedString with protection reason", func(t *testing.T) {
		protectionReason := "system configuration locked by administrator"
		err := NewObjectProtectedString(ObjectTypeAuthorization, "system.conf", protectionReason)

		assert.Equal(t, http.StatusBadRequest, err.Status)
		assert.Equal(t, "ObjectProtected", err.Code)
		assert.Equal(t, "authorization", err.ObjectType)
		assert.Equal(t, "system.conf", err.ObjectID)
		assert.Equal(t, protectionReason, err.State)
	})

	t.Run("NewErrorResponseDTOString with custom message", func(t *testing.T) {
		customMessage := "rate limit exceeded for user actions"
		err := NewErrorResponseDTOString(http.StatusTooManyRequests, ObjectTypeUser, "user-123", customMessage)

		assert.Equal(t, http.StatusTooManyRequests, err.Status)
		assert.Equal(t, "TooManyRequests", err.Code)
		assert.Equal(t, "user", err.ObjectType)
		assert.Equal(t, "user-123", err.ObjectID)
		assert.Equal(t, customMessage, err.State)
	})
}

// --- Тест Unmarshal ---

func TestUnmarshal_Success(t *testing.T) {
	origErr := NewObjectNotFound(ObjectTypeItem, "978-123", StateNotFound)
	body, _ := json.Marshal(origErr)

	err := Unmarshal(body)
	require.NotNil(t, err, "Unmarshal не должен возвращать nil при успехе")

	// Проверяем, что тип и содержимое совпадают
	var unmarshaledErr *ObjectNotFound
	ok := errors.As(err, &unmarshaledErr)
	require.True(t, ok, "Ожидался тип *ObjectNotFound")
	assert.Equal(t, origErr.Status, unmarshaledErr.Status)
	assert.Equal(t, origErr.Code, unmarshaledErr.Code)
	assert.Equal(t, origErr.Message, unmarshaledErr.Message)
	assert.Equal(t, origErr.ObjectType, unmarshaledErr.ObjectType)
	assert.Equal(t, origErr.ObjectID, unmarshaledErr.ObjectID)
	assert.Equal(t, origErr.State, unmarshaledErr.State)
}

func TestUnmarshal_UnknownCode(t *testing.T) {
	body := []byte(`{"status": 499, "code": "UNKNOWN_CODE", "message": "Unknown test error"}`)

	err := Unmarshal(body)
	require.Error(t, err, "Ожидалась ошибка размаршаливания")
	assert.ErrorIs(t, err, serror.ErrUnknownCode, "Ожидалась ошибка неизвестного кода")
	assert.Contains(t, err.Error(), "code 'UNKNOWN_CODE'", "Сообщение должно содержать неизвестный код")
}

// --- Тесты новых функций ---

func TestUnmarshalFast_Success(t *testing.T) {
	origErr := NewInternalError(ObjectTypeService, "srv-123", StateConnectionFailed)
	body, _ := json.Marshal(origErr)

	result, err := UnmarshalFast(body)
	require.NoError(t, err, "UnmarshalFast не должен возвращать ошибку")
	require.NotNil(t, result, "Result не должен быть nil")

	var internalErr *InternalError
	ok := errors.As(result, &internalErr)
	require.True(t, ok, "Ожидался тип *InternalError")
	assert.Equal(t, origErr.Status, internalErr.Status)
	assert.Equal(t, origErr.Code, internalErr.Code)
	assert.Equal(t, origErr.ObjectType, internalErr.ObjectType)
	assert.Equal(t, origErr.ObjectID, internalErr.ObjectID)
	assert.Equal(t, origErr.State, internalErr.State)
}

func TestUnmarshalFast_UnknownCode(t *testing.T) {
	body := []byte(`{"status": 499, "code": "UNKNOWN_FAST", "message": "Unknown fast error"}`)

	result, err := UnmarshalFast(body)
	require.Error(t, err, "Ожидалась ошибка для неизвестного кода")
	require.Nil(t, result, "Result должен быть nil при ошибке")
	assert.Contains(t, err.Error(), "UNKNOWN_FAST", "Ошибка должна содержать неизвестный код")
}

func TestUnmarshalTyped_Success(t *testing.T) {
	origErr := NewObjectProtected(ObjectTypeAuthorization, "cfg-456", StateProtected)
	body, _ := json.Marshal(origErr)

	result, err := UnmarshalTyped[*ObjectProtected](body)
	require.NoError(t, err, "UnmarshalTyped не должен возвращать ошибку")
	require.NotNil(t, result, "Result не должен быть nil")

	protectedErr := *result
	assert.Equal(t, origErr.Status, protectedErr.Status)
	assert.Equal(t, origErr.Code, protectedErr.Code)
	assert.Equal(t, origErr.ObjectType, protectedErr.ObjectType)
	assert.Equal(t, origErr.ObjectID, protectedErr.ObjectID)
	assert.Equal(t, origErr.State, protectedErr.State)
}

func TestUnmarshalTyped_WrongType(t *testing.T) {
	// Создаем ObjectNotFound, но пытаемся парсить как ObjectProtected
	origErr := NewObjectNotFound(ObjectTypeUser, "usr-789", StateNotFound)
	body, _ := json.Marshal(origErr)

	result, err := UnmarshalTyped[*ObjectProtected](body)
	require.NoError(t, err, "UnmarshalTyped должен успешно парсить JSON")
	require.NotNil(t, result, "Result не должен быть nil")

	// Проверяем, что данные правильно распарсились в неправильный тип
	protectedErr := *result
	assert.Equal(t, origErr.Status, protectedErr.Status)
	assert.Equal(t, origErr.Code, protectedErr.Code) // Код будет "ObjectNotFound"
	assert.Equal(t, origErr.ObjectType, protectedErr.ObjectType)
	assert.Equal(t, origErr.ObjectID, protectedErr.ObjectID)
	assert.Equal(t, origErr.State, protectedErr.State)
}

// --- Тест IdentifyAndConvertError ---

func TestIdentifyAndConvertError(t *testing.T) {
	tests := []struct {
		name          string
		inputError    error
		expectSuccess bool
		expectedType  interface{}
	}{
		{
			name:          "Known error type (ObjectAlreadyExist)",
			inputError:    NewObjectAlreadyExist(ObjectTypeItem, "t1", StateAlreadyExists),
			expectSuccess: true,
			expectedType:  &ObjectAlreadyExist{},
		},
		{
			name:          "Known error type (InternalError)",
			inputError:    NewInternalError(ObjectTypeService, "j5", StateProcessingError),
			expectSuccess: true,
			expectedType:  &InternalError{},
		},
		{
			name:          "Error from NewErrorResponseDTO (BadRequest)",
			inputError:    NewErrorResponseDTO(http.StatusBadRequest, ObjectTypeRequest, "r1", StateValidationFailed),
			expectSuccess: false, // Code "BadRequest" не в errInstances
			expectedType:  nil,
		},
		{
			name:          "Standard error (errors.New)",
			inputError:    errors.New("a standard error"),
			expectSuccess: false, // Нет поля Base.Code
			expectedType:  nil,
		},
		{
			name:          "Error with Base but unknown code",
			inputError:    &serror.Base{Status: 501, Code: "UNRECOGNIZED", Message: "..."},
			expectSuccess: false, // Код "UNRECOGNIZED" не в errInstances
			expectedType:  nil,
		},
		{
			name:          "Nil error",
			inputError:    nil,
			expectSuccess: false,
			expectedType:  nil,
		},
		{
			name:          "Error without Base field",
			inputError:    &errorWithoutBase{Code: "SomeCode"},
			expectSuccess: false,
			expectedType:  nil,
		},
		{
			name:          "Error with Base but Code field is not string",
			inputError:    &errorWithInvalidCodeType{},
			expectSuccess: false,
			expectedType:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			converted, success := IdentifyAndConvertError(tt.inputError)

			assert.Equal(t, tt.expectSuccess, success)
			if tt.expectSuccess {
				require.NotNil(t, converted)
				assert.IsType(t, tt.expectedType, converted, "Тип преобразованной ошибки не совпадает")
				// Дополнительно можно сравнить содержимое, если необходимо
				// Например, сравнить поля исходной ошибки и преобразованной
			} else {
				assert.Nil(t, converted)
			}
		})
	}
}

// --- Тесты Extract... ---

func TestExtractStatusCode(t *testing.T) {
	tests := []struct {
		name       string
		err        error
		expectedOk bool
		expectedSc int
	}{
		{"HttpStatuser implemented (InternalError)", NewInternalError(ObjectTypeService, "1", StateInternalError), true, http.StatusInternalServerError},
		{"HttpStatuser implemented (ObjectNotFound)", NewObjectNotFound(ObjectTypeUser, "1", StateNotFound), true, http.StatusNotFound},
		{"Not implemented (standard error)", errors.New("some error"), false, 0},
		{"Not implemented (nil)", nil, false, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sc, ok := ExtractStatusCode(tt.err)
			assert.Equal(t, tt.expectedOk, ok)
			assert.Equal(t, tt.expectedSc, sc)
		})
	}
}

func TestIsNotFoundError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{"nil error", nil, false},
		{"ObjectNotFound error", NewObjectNotFound(ObjectTypeUser, "123", StateNotFound), true},
		{"ErrorResponseDTO with NotFound status", NewErrorResponseDTO(http.StatusNotFound, ObjectTypeUser, "123", StateNotFound), true},
		{"ErrorResponseDTO with other status", NewErrorResponseDTO(http.StatusBadRequest, ObjectTypeUser, "123", StateValidationFailed), false},
		{"Standard error", errors.New("some error"), false},
		{"InternalError", NewInternalError(ObjectTypeService, "svc-1", StateInternalError), false},
		{"ObjectAlreadyExist", NewObjectAlreadyExist(ObjectTypeProduct, "prod-1", StateAlreadyExists), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsNotFoundError(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsArgSuccessExitError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{"nil error", nil, false},
		{"ArgSuccessExit error", NewArgSuccessExit("help displayed"), true},
		{"Legacy arg success exit by message", errors.New("arg success exit"), true},
		{"Standard error", errors.New("some error"), false},
		{"ObjectNotFound error", NewObjectNotFound(ObjectTypeUser, "123", StateNotFound), false},
		{"InternalError", NewInternalError(ObjectTypeService, "svc-1", StateInternalError), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsArgSuccessExitError(tt.err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewArgSuccessExit(t *testing.T) {
	tests := []struct {
		name     string
		message  string
		expected string
	}{
		{"with custom message", "help displayed", "help displayed"},
		{"with empty message", "", "arg success exit"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := NewArgSuccessExit(tt.message)
			assert.Equal(t, tt.expected, err.Error())
		})
	}
}

func TestExtractObjectType(t *testing.T) {
	tests := []struct {
		name            string
		err             error
		expectedOk      bool
		expectedObjType string
	}{
		{"ObjectType present (ObjectAlreadyExist)", NewObjectAlreadyExist(ObjectTypeProduct, "1", StateAlreadyExists), true, "product"},
		{"ObjectType present (ObjectInvalidData)", NewObjectInvalidData(ObjectTypeUser, "val", StateValidationFailed), true, "user"},
		{"ObjectType not present (standard error)", errors.New("some error"), false, ""},
		{"ObjectType not present (nil)", nil, false, ""},
		{"ObjectType not present (Base error)", &serror.Base{Code: "abc"}, false, ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			objType, ok := ExtractObjectType(tt.err)
			assert.Equal(t, tt.expectedOk, ok)
			assert.Equal(t, tt.expectedObjType, objType)
		})
	}
}

// --- Тест для ResponseDTO.Error() ---

func TestResponseDTO_Error(t *testing.T) {
	responseDTO := &ResponseDTO{
		Base: serror.Base{
			Status:  http.StatusOK,
			Code:    "TestCode",
			Message: "Test message",
		},
		ObjectType: "testObject",
		ObjectID:   "testID",
		State:      "testState",
	}

	expectedError := fmt.Sprintf("request for object %s (%s): %s", responseDTO.ObjectType, responseDTO.ObjectID, responseDTO.State)
	assert.Equal(t, expectedError, responseDTO.Error())
}

// --- Бенчмарки для сравнения производительности ---

var (
	benchErr    error
	benchResult interface{}
)

func BenchmarkUnmarshal_Old(b *testing.B) {
	origErr := NewObjectNotFound(ObjectTypeUser, "12345", StateNotFound)
	body, _ := json.Marshal(origErr)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := Unmarshal(body)
		benchErr = err
	}
}

func BenchmarkUnmarshalFast_New(b *testing.B) {
	origErr := NewObjectNotFound(ObjectTypeUser, "12345", StateNotFound)
	body, _ := json.Marshal(origErr)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err, _ := UnmarshalFast(body)
		benchErr = err
	}
}

func BenchmarkUnmarshalTyped_New(b *testing.B) {
	origErr := NewObjectNotFound(ObjectTypeUser, "12345", StateNotFound)
	body, _ := json.Marshal(origErr)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err, _ := UnmarshalTyped[*ObjectNotFound](body)
		benchErr = *err
	}
}

func BenchmarkExtractObjectType_Old(b *testing.B) {
	err := NewInternalError(ObjectTypeService, "srv-999", StateInternalError)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		objType, ok := ExtractObjectType(err)
		if ok {
			benchResult = objType
		}
	}
}

func BenchmarkIdentifyAndConvertError_Old(b *testing.B) {
	err := NewObjectAlreadyExist(ObjectTypeResource, "res-777", StateAlreadyExists)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		converted, ok := IdentifyAndConvertError(err)
		if ok {
			benchResult = converted
		}
	}
}

// --- Тесты для 100% покрытия ---

func TestMissingCoverage(t *testing.T) {
	t.Run("Test all Error() methods", func(t *testing.T) {
		// Тестируем все Error() методы
		errResp := NewErrorResponseDTO(http.StatusBadRequest, ObjectTypeUser, "user-1", StateValidationFailed)
		assert.Contains(t, errResp.Error(), "request error for object user (user-1): validation failed")

		internalErr := NewInternalError(ObjectTypeService, "svc-1", StateInternalError)
		assert.Contains(t, internalErr.Error(), "object of type service (svc-1) internal error: internal error")

		alreadyExist := NewObjectAlreadyExist(ObjectTypeProduct, "prod-1", StateAlreadyExists)
		assert.Contains(t, alreadyExist.Error(), "object of type product (prod-1) already exist")

		invalidData := NewObjectInvalidData(ObjectTypeUser, "invalid-data", StateInvalidData)
		assert.Contains(t, invalidData.Error(), "object of type user (invalid-data) has invalid value=invalid data")

		notFound := NewObjectNotFound(ObjectTypeProduct, "prod-404", StateNotFound)
		assert.Contains(t, notFound.Error(), "object of type product (prod-404) not found")

		protected := NewObjectProtected(ObjectTypeAuthorization, "authorization-1", StateProtected)
		assert.Contains(t, protected.Error(), "object of type authorization (authorization-1) is protected")

		responseDTO := &ResponseDTO{
			Base:       serror.Base{Status: 200, Code: "OK", Message: "success"},
			ObjectType: "test",
			ObjectID:   "test-1",
			State:      "success",
		}
		assert.Contains(t, responseDTO.Error(), "request for object test (test-1): success")
	})

	t.Run("Test IdentifyAndConvertError with reflection fallback", func(t *testing.T) {
		// Создаем ошибку с неизвестным кодом
		unknownErr := &InternalError{
			Base: serror.Base{
				Status:  500,
				Code:    "UnknownCode",
				Message: "unknown error",
			},
			ObjectType: "test",
			ObjectID:   "test-1",
			State:      "unknown",
		}

		converted, ok := IdentifyAndConvertError(unknownErr)
		assert.False(t, ok)
		assert.Nil(t, converted)
	})

	t.Run("Test initErrorRegistry coverage", func(t *testing.T) {
		// Принудительно вызываем initErrorRegistry несколько раз
		// для покрытия sync.Once логики
		initErrorRegistry()
		initErrorRegistry()
		initErrorRegistry()

		// Проверяем что registry инициализирован
		assert.NotNil(t, errorRegistry)
		assert.NotNil(t, errInstances)
	})

	t.Run("Test ExtractObjectType with reflection fallback", func(t *testing.T) {
		customErr := &customErrorForTesting{
			ObjectType: "custom",
			State:      "custom state",
		}

		// Это должно использовать reflection fallback
		objType, ok := ExtractObjectType(customErr)
		assert.True(t, ok)
		assert.Equal(t, "custom", objType)
	})
}

func TestHttpStatuserInterface(t *testing.T) {
	t.Run("All error types implement HttpStatuser", func(t *testing.T) {
		var hs HttpStatuser

		hs = NewErrorResponseDTO(http.StatusBadRequest, ObjectTypeUser, "user-1", StateValidationFailed)
		assert.Equal(t, http.StatusBadRequest, hs.HttpStatus())

		hs = NewInternalError(ObjectTypeService, "svc-1", StateInternalError)
		assert.Equal(t, http.StatusInternalServerError, hs.HttpStatus())

		hs = NewObjectAlreadyExist(ObjectTypeProduct, "prod-1", StateAlreadyExists)
		assert.Equal(t, http.StatusConflict, hs.HttpStatus())

		hs = NewObjectInvalidData(ObjectTypeUser, "invalid", StateInvalidData)
		assert.Equal(t, http.StatusBadRequest, hs.HttpStatus())

		hs = NewObjectNotFound(ObjectTypeProduct, "prod-404", StateNotFound)
		assert.Equal(t, http.StatusNotFound, hs.HttpStatus())

		hs = NewObjectProtected(ObjectTypeAuthorization, "authorization-1", StateProtected)
		assert.Equal(t, http.StatusBadRequest, hs.HttpStatus())
	})
}

func TestAllObjectTypes(t *testing.T) {
	// Тестируем все ObjectType константы
	objectTypes := []ObjectType{
		ObjectTypeProduct, ObjectTypeUser, ObjectTypeGroup, ObjectTypeRole,
		ObjectTypePermission, ObjectTypeParticipant, ObjectTypeProposal,
		ObjectTypeCategory, ObjectTypeRequest, ObjectTypeAuthentication, ObjectTypeResource,
		ObjectTypeItem, ObjectTypeData, ObjectTypeAPI, ObjectTypeServer,
		ObjectTypeAuthorization, ObjectTypeValidation, ObjectTypeService,
		ObjectTypeIdentityProvider,
	}

	for _, objType := range objectTypes {
		t.Run(fmt.Sprintf("ObjectType_%s", objType), func(t *testing.T) {
			// Тестируем что все типы работают в конструкторах
			err := NewObjectNotFound(objType, "test-id", StateNotFound)
			assert.Equal(t, objType.String(), err.ObjectType)
		})
	}
}

func TestAllStates(t *testing.T) {
	// Тестируем все State константы
	states := []State{
		StateAlreadyExists, StateNotFound, StateInvalidData, StateProtected,
		StateValidationFailed, StateDatabaseError, StatePermissionDenied,
		StateInternalError, StateProcessingError, StateConnectionFailed,
		StateTimeout, StateConflict, StateUnauthorized,
	}

	for _, state := range states {
		t.Run(fmt.Sprintf("State_%s", state), func(t *testing.T) {
			// Тестируем что все состояния работают
			err := NewInternalError(ObjectTypeService, "test-id", state)
			assert.Equal(t, state.String(), err.State)
		})
	}
}

func TestAllErrorCodes(t *testing.T) {
	// Тестируем все ErrorCode константы
	errorCodes := []ErrorCode{
		ErrorCodeBadRequest, ErrorCodeUnauthorized, ErrorCodeForbidden,
		ErrorCodeNotFound, ErrorCodeConflict, ErrorCodeTooManyRequests,
		ErrorCodeInternalServerError, ErrorCodeUnknown, ErrorCodeInternalError,
		ErrorCodeObjectAlreadyExist, ErrorCodeObjectInvalidData,
		ErrorCodeObjectNotFound, ErrorCodeObjectProtected,
		ErrorCodeErrorResponseDTO, ErrorCodeResponseDTO,
	}

	for _, code := range errorCodes {
		t.Run(fmt.Sprintf("ErrorCode_%s", code), func(t *testing.T) {
			assert.NotEmpty(t, code.String())
		})
	}
}

// --- Тесты для оставшихся недостающих функций ---

func TestRemainingCoverage(t *testing.T) {
	t.Run("Test initErrorRegistry branches", func(t *testing.T) {
		// Убеждаемся что registryOnce сработает только один раз
		assert.NotNil(t, errorRegistry)
		assert.NotNil(t, errInstances)

		// Сбрасываем для тестирования
		var resetOnce sync.Once
		resetOnce.Do(func() {
			// registry уже инициализирован
		})
	})

	t.Run("Test ExtractObjectType edge cases", func(t *testing.T) {
		// Тест с неправильным типом поля ObjectType
		err := &testStructWithIntObjectType{ObjectType: 123}

		objType, ok := ExtractObjectType(err)
		assert.False(t, ok)
		assert.Empty(t, objType)

		// Тест без поля ObjectType
		err2 := &testStructWithoutObjectType{Name: "test"}

		objType2, ok2 := ExtractObjectType(err2)
		assert.False(t, ok2)
		assert.Empty(t, objType2)
	})

	t.Run("Test IdentifyAndConvertError comprehensive", func(t *testing.T) {
		// Тест с reflection copy
		origErr := NewInternalError(ObjectTypeService, "svc-1", StateInternalError)

		converted, ok := IdentifyAndConvertError(origErr)
		assert.True(t, ok)
		require.NotNil(t, converted)

		convertedInternal, ok := converted.(*InternalError)
		assert.True(t, ok)
		assert.Equal(t, origErr.ObjectType, convertedInternal.ObjectType)
		assert.Equal(t, origErr.ObjectID, convertedInternal.ObjectID)
		assert.Equal(t, origErr.State, convertedInternal.State)

		// Тест с неизвестной структурой
		unknown := &unknownErrorStruct{}
		unknown.Base.Code = "UnknownType"

		converted2, ok2 := IdentifyAndConvertError(unknown)
		assert.False(t, ok2)
		assert.Nil(t, converted2)

		// Тест всех веток fast path в IdentifyAndConvertError
		testCases := []struct {
			name     string
			err      error
			expected bool
		}{
			{"InternalError", NewInternalError(ObjectTypeService, "1", StateInternalError), true},
			{"ObjectAlreadyExist", NewObjectAlreadyExist(ObjectTypeProduct, "1", StateAlreadyExists), true},
			{"ObjectInvalidData", NewObjectInvalidData(ObjectTypeUser, "1", StateValidationFailed), true},
			{"ObjectNotFound", NewObjectNotFound(ObjectTypeUser, "1", StateNotFound), true},
			{"ObjectProtected", NewObjectProtected(ObjectTypeAuthorization, "1", StateProtected), true},
			{"ResponseDTO", &ResponseDTO{Base: serror.Base{Code: "ResponseDTO"}}, true},
			{"ErrorResponseDTO", NewErrorResponseDTO(400, ObjectTypeUser, "1", StateValidationFailed), false}, // BadRequest не в errInstances
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result, ok := IdentifyAndConvertError(tc.err)
				assert.Equal(t, tc.expected, ok, "Expected result for %s", tc.name)
				if tc.expected {
					assert.NotNil(t, result)
				} else {
					assert.Nil(t, result)
				}
			})
		}
	})

	t.Run("Test Extract functions with value types", func(t *testing.T) {
		// Тестируем с value type вместо pointer
		customErr := &customErrorForTesting{
			ObjectType: "value-type",
			State:      "value-state",
		}

		objType, ok := ExtractObjectType(customErr)
		assert.True(t, ok)
		assert.Equal(t, "value-type", objType)
	})
}

// --- Бенчмарки новых функций с константами ---

func BenchmarkNewWithConstants(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := NewObjectNotFound(ObjectTypeProduct, "prod-123", StateNotFound)
		benchErr = err
	}
}

// --- Дополнительные тесты для достижения 95% покрытия ---

func TestCompleteEdgeCases(t *testing.T) {
	t.Run("Test IdentifyAndConvertError reflection copy mechanism", func(t *testing.T) {
		// Создаем ObjectAlreadyExist с кодом, который есть в errInstances
		err := &ObjectAlreadyExist{
			Base: serror.Base{
				Status:  409,
				Code:    "ObjectAlreadyExist",
				Message: "test already exists",
			},
			ObjectType: "test",
			ObjectID:   "test-123",
			State:      "test state",
		}

		// Это должно пройти через reflection copy механизм
		converted, ok := IdentifyAndConvertError(err)
		assert.True(t, ok)
		assert.NotNil(t, converted)

		convertedErr, ok := converted.(*ObjectAlreadyExist)
		assert.True(t, ok)
		assert.Equal(t, err.Status, convertedErr.Status)
		assert.Equal(t, err.Code, convertedErr.Code)
		assert.Equal(t, err.ObjectType, convertedErr.ObjectType)
		assert.Equal(t, err.ObjectID, convertedErr.ObjectID)
		assert.Equal(t, err.State, convertedErr.State)
	})

	t.Run("Test initErrorRegistry full initialization", func(t *testing.T) {
		// Тестируем что все регистрации прошли успешно
		initErrorRegistry()

		// Проверяем что все ключи зарегистрированы
		expectedKeys := []string{
			"ErrorResponseDTO", "InternalError", "ObjectAlreadyExist",
			"ObjectInvalidData", "ObjectNotFound", "ObjectProtected", "ResponseDTO",
			"BadRequest", "Unauthorized", "Forbidden", "NotFound", "Conflict",
			"TooManyRequests", "InternalServerError", "Unknown",
		}

		for _, key := range expectedKeys {
			// Проверяем через UnmarshalFast что ключ зарегистрирован
			testJSON := fmt.Sprintf(`{"code": "%s", "status": 500, "message": "test"}`, key)
			result, err := UnmarshalFast([]byte(testJSON))
			if key == "ErrorResponseDTO" || key == "InternalError" || key == "ObjectAlreadyExist" ||
				key == "ObjectInvalidData" || key == "ObjectNotFound" || key == "ObjectProtected" ||
				key == "ResponseDTO" || key == "BadRequest" || key == "Unauthorized" ||
				key == "Forbidden" || key == "NotFound" || key == "Conflict" ||
				key == "TooManyRequests" || key == "InternalServerError" || key == "Unknown" {
				assert.NoError(t, err, "Key %s should be registered", key)
				assert.NotNil(t, result, "Result should not be nil for key %s", key)
			}
		}
	})

	t.Run("Test ExtractObjectType all branches", func(t *testing.T) {
		// Тестируем nil error
		objType, ok := ExtractObjectType(nil)
		assert.False(t, ok)
		assert.Empty(t, objType)

		// Тестируем все fast path типы
		tests := []struct {
			name string
			err  error
		}{
			{"ErrorResponseDTO", NewErrorResponseDTO(400, ObjectTypeUser, "user-1", StateValidationFailed)},
			{"InternalError", NewInternalError(ObjectTypeService, "svc-1", StateInternalError)},
			{"ObjectAlreadyExist", NewObjectAlreadyExist(ObjectTypeProduct, "prod-1", StateAlreadyExists)},
			{"ObjectInvalidData", NewObjectInvalidData(ObjectTypeUser, "invalid", StateInvalidData)},
			{"ObjectNotFound", NewObjectNotFound(ObjectTypeProduct, "prod-404", StateNotFound)},
			{"ObjectProtected", NewObjectProtected(ObjectTypeAuthorization, "authorization-1", StateProtected)},
			{"ResponseDTO", &ResponseDTO{ObjectType: "response", ObjectID: "resp-1", State: "ok"}},
		}

		for _, test := range tests {
			t.Run(test.name, func(t *testing.T) {
				objType, ok := ExtractObjectType(test.err)
				assert.True(t, ok)
				assert.NotEmpty(t, objType)
			})
		}
	})

	t.Run("Test IdentifyAndConvertError nil check", func(t *testing.T) {
		converted, ok := IdentifyAndConvertError(nil)
		assert.False(t, ok)
		assert.Nil(t, converted)
	})

	t.Run("Test IdentifyAndConvertError error handling", func(t *testing.T) {
		// Тест с ошибкой, у которой нет поля Base
		errWithoutBase := &errorWithoutBaseField{Code: "test"}

		converted, ok := IdentifyAndConvertError(errWithoutBase)
		assert.False(t, ok)
		assert.Nil(t, converted)

		// Тест с ошибкой, у которой поле Code не строка
		errInvalidCode := &errorWithInvalidCodeField{}
		errInvalidCode.Base.Code = 123

		converted2, ok2 := IdentifyAndConvertError(errInvalidCode)
		assert.False(t, ok2)
		assert.Nil(t, converted2)

		// Тест с неизвестным кодом
		unknownErr := &unknownErrorStruct{}
		unknownErr.Base.Code = "UnknownCode"

		converted3, ok3 := IdentifyAndConvertError(unknownErr)
		assert.False(t, ok3)
		assert.Nil(t, converted3)
	})

	t.Run("Test IdentifyAndConvertError errors.As success cases", func(t *testing.T) {
		// Тест для case ErrorCodeErrorResponseDTO когда errors.As успешно срабатывает
		realErrorResponse := NewErrorResponseDTO(400, ObjectTypeUser, "user-1", StateValidationFailed)
		// Меняем код на ErrorResponseDTO чтобы попасть в нужный case
		realErrorResponse.Code = "ErrorResponseDTO"

		converted, ok := IdentifyAndConvertError(realErrorResponse)
		assert.True(t, ok)
		assert.NotNil(t, converted)

		// Должен вернуться тот же объект через errors.As
		convertedErr, ok := converted.(*ErrorResponseDTO)
		assert.True(t, ok)
		assert.Equal(t, realErrorResponse, convertedErr) // должен быть тот же объект
		assert.Equal(t, "ErrorResponseDTO", convertedErr.Code)
	})
}

// --- Дополнительные тесты для реального использования reflection copy механизма ---

func TestReflectionCopyMechanism(t *testing.T) {
	// Создаем структуру для тестирования reflection copy с совместимыми полями
	compatibleErr := &compatibleErrorStruct{
		Base:       serror.Base{Status: 409, Code: "ObjectAlreadyExist", Message: "object already exists"},
		ObjectType: "user",
		ObjectID:   "user-1",
		State:      "already exists",
	}

	// Это должно пройти через reflection copy механизм
	converted, ok := IdentifyAndConvertError(compatibleErr)
	assert.True(t, ok)
	assert.NotNil(t, converted)

	convertedErr, ok := converted.(*ObjectAlreadyExist)
	assert.True(t, ok)
	assert.Equal(t, compatibleErr.Base.Status, convertedErr.Status)
	assert.Equal(t, compatibleErr.Base.Code, convertedErr.Code)
	assert.Equal(t, compatibleErr.Base.Message, convertedErr.Message)
	assert.Equal(t, compatibleErr.ObjectType, convertedErr.ObjectType)
	assert.Equal(t, compatibleErr.ObjectID, convertedErr.ObjectID)
	assert.Equal(t, compatibleErr.State, convertedErr.State)
}

func TestIdentifyAndConvertErrorAsFailures(t *testing.T) {
	t.Run("Test IdentifyAndConvertError errors.As failure cases", func(t *testing.T) {
		// Создаем структуры, которые имеют правильные коды, но не могут быть приведены к нужным типам
		// Это покроет случаи, когда errors.As возвращает false, но не вызовет панику

		// Тест для кода InternalError, но с другим типом
		fakeInternal := &fakeInternalError{
			Base:       serror.Base{Status: 500, Code: "InternalError", Message: "fake internal error"},
			ObjectType: "service",
			ObjectID:   "svc-1",
			State:      "error state",
		}

		// Этот тест должен провалить errors.As и перейти к reflection copy
		converted, ok := IdentifyAndConvertError(fakeInternal)
		assert.True(t, ok) // reflection copy должен сработать
		assert.NotNil(t, converted)

		// Должен быть создан настоящий InternalError через reflection copy
		convertedErr, ok := converted.(*InternalError)
		assert.True(t, ok)
		assert.Equal(t, fakeInternal.Base.Status, convertedErr.Status)
		assert.Equal(t, fakeInternal.Base.Code, convertedErr.Code)

		// Тест для кода ObjectNotFound, но с другим типом
		fakeNotFound := &fakeObjectNotFound{
			Base:       serror.Base{Status: 404, Code: "ObjectNotFound", Message: "fake not found"},
			ObjectType: "product",
			ObjectID:   "prod-1",
			State:      "not found state",
		}

		converted2, ok2 := IdentifyAndConvertError(fakeNotFound)
		assert.True(t, ok2) // reflection copy должен сработать
		assert.NotNil(t, converted2)

		// Должен быть создан настоящий ObjectNotFound через reflection copy
		convertedErr2, ok2 := converted2.(*ObjectNotFound)
		assert.True(t, ok2)
		assert.Equal(t, fakeNotFound.Base.Status, convertedErr2.Status)
		assert.Equal(t, fakeNotFound.Base.Code, convertedErr2.Code)

		// Тест для кода ErrorResponseDTO, но с другим типом
		fakeErrorResponse := &fakeErrorResponseDTO{
			Base:       serror.Base{Status: 400, Code: "ErrorResponseDTO", Message: "fake error response"},
			ObjectType: "validation",
			ObjectID:   "val-1",
			State:      "validation failed",
		}

		converted3, ok3 := IdentifyAndConvertError(fakeErrorResponse)
		assert.True(t, ok3) // reflection copy должен сработать
		assert.NotNil(t, converted3)

		// Должен быть создан настоящий ErrorResponseDTO через reflection copy
		convertedErr3, ok3 := converted3.(*ErrorResponseDTO)
		assert.True(t, ok3)
		assert.Equal(t, fakeErrorResponse.Base.Status, convertedErr3.Status)
		assert.Equal(t, fakeErrorResponse.Base.Code, convertedErr3.Code)
	})
}
