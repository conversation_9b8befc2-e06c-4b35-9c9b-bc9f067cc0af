package constants

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsSystemToConstant(t *testing.T) {
	tests := []struct {
		name     string
		isSystem bool
		expected string
	}{
		{
			name:     "true returns SystemType",
			isSystem: true,
			expected: SystemType,
		},
		{
			name:     "false returns CustomType",
			isSystem: false,
			expected: CustomType,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsSystemToConstant(tt.isSystem)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestToBoolFromSystemType(t *testing.T) {
	tests := []struct {
		name       string
		systemType string
		expected   *bool
	}{
		{
			name:       "empty string returns nil",
			systemType: "",
			expected:   nil,
		},
		{
			name:       "SystemType returns true",
			systemType: SystemType,
			expected:   boolPtr(true),
		},
		{
			name:       "CustomType returns false",
			systemType: CustomType,
			expected:   boolPtr(false),
		},
		{
			name:       "unknown type returns false",
			systemType: "unknown",
			expected:   boolPtr(false),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToBoolPtrFromSystemType(tt.systemType)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestBoolFromSystemType(t *testing.T) {
	tests := []struct {
		name       string
		systemType string
		expected   bool
	}{
		{
			name:       "empty string returns false",
			systemType: "",
			expected:   false,
		},
		{
			name:       "SystemType returns true",
			systemType: SystemType,
			expected:   true,
		},
		{
			name:       "CustomType returns false",
			systemType: CustomType,
			expected:   false,
		},
		{
			name:       "unknown type returns false",
			systemType: "unknown",
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToBoolFromSystemType(tt.systemType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSystemTypePtr(t *testing.T) {
	tests := []struct {
		name     string
		isSystem bool
		expected string
	}{
		{
			name:     "true returns pointer to SystemType",
			isSystem: true,
			expected: SystemType,
		},
		{
			name:     "false returns pointer to CustomType",
			isSystem: false,
			expected: CustomType,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SystemTypePtr(tt.isSystem)
			assert.NotNil(t, result)
			assert.Equal(t, tt.expected, *result)
		})
	}
}

func TestSystemTypePtrOrNil(t *testing.T) {
	tests := []struct {
		name     string
		isSystem bool
		expected *string
	}{
		{
			name:     "true returns pointer to SystemType",
			isSystem: true,
			expected: stringPtr(SystemType),
		},
		{
			name:     "false returns nil",
			isSystem: false,
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SystemTypePtrOrNil(tt.isSystem)
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tt.expected, *result)
			}
		})
	}
}

func TestProductStatus_String(t *testing.T) {
	tests := []struct {
		name     string
		status   ProductStatus
		expected string
	}{
		{
			name:     "ProductStatusActive returns active",
			status:   ProductStatusActive,
			expected: "active",
		},
		{
			name:     "ProductStatusArchive returns archive",
			status:   ProductStatusArchive,
			expected: "archive",
		},
		{
			name:     "ProductStatusUnknown returns unknown",
			status:   ProductStatusUnknown,
			expected: "unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.status.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestToProductStatusFromString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected ProductStatus
	}{
		{
			name:     "active returns ProductStatusActive",
			input:    "active",
			expected: ProductStatusActive,
		},
		{
			name:     "archive returns ProductStatusArchive",
			input:    "archive",
			expected: ProductStatusArchive,
		},
		{
			name:     "unknown returns ProductStatusUnknown",
			input:    "unknown",
			expected: ProductStatusUnknown,
		},
		{
			name:     "empty string returns ProductStatusUnknown",
			input:    "",
			expected: ProductStatusUnknown,
		},
		{
			name:     "invalid string returns ProductStatusUnknown",
			input:    "invalid",
			expected: ProductStatusUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToProductStatusFromString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper functions for tests
func boolPtr(b bool) *bool {
	return &b
}

func stringPtr(s string) *string {
	return &s
}
