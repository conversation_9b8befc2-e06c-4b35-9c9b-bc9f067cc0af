package constants

import "testing"

func TestToExternalEventType(t *testing.T) {
	tests := []struct {
		name     string
		internal InternalEventType
		expected ExternalEventType
	}{
		{
			name:     "message to proposalMessage",
			internal: InternalEventMessage,
			expected: ExternalEventProposalMessage,
		},
		{
			name:     "status to proposalStatus",
			internal: InternalEventStatus,
			expected: ExternalEventProposalStatus,
		},
		{
			name:     "unknown type constant",
			internal: InternalEventUnknown,
			expected: ExternalEventUnknown,
		},
		{
			name:     "invalid type",
			internal: InternalEventType("invalid"),
			expected: ExternalEventUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToExternalEventType(tt.internal)
			if result != tt.expected {
				t.<PERSON>rrorf("ToExternalEventType(%v) = %v, want %v", tt.internal, result, tt.expected)
			}
		})
	}
}

func TestToInternalEventType(t *testing.T) {
	tests := []struct {
		name     string
		external ExternalEventType
		expected InternalEventType
	}{
		{
			name:     "proposalMessage to message",
			external: ExternalEventProposalMessage,
			expected: InternalEventMessage,
		},
		{
			name:     "proposalStatus to status",
			external: ExternalEventProposalStatus,
			expected: InternalEventStatus,
		},
		{
			name:     "unknown type constant",
			external: ExternalEventUnknown,
			expected: InternalEventUnknown,
		},
		{
			name:     "invalid type",
			external: ExternalEventType("invalid"),
			expected: InternalEventUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToInternalEventType(tt.external)
			if result != tt.expected {
				t.Errorf("ToInternalEventType(%v) = %v, want %v", tt.external, result, tt.expected)
			}
		})
	}
}

func TestInternalEventType_ToExternal(t *testing.T) {
	tests := []struct {
		name     string
		internal InternalEventType
		expected ExternalEventType
	}{
		{
			name:     "message method conversion",
			internal: InternalEventMessage,
			expected: ExternalEventProposalMessage,
		},
		{
			name:     "status method conversion",
			internal: InternalEventStatus,
			expected: ExternalEventProposalStatus,
		},
		{
			name:     "unknown method conversion",
			internal: InternalEventUnknown,
			expected: ExternalEventUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.internal.ToExternal()
			if result != tt.expected {
				t.Errorf("InternalEventType.ToExternal() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestExternalEventType_ToInternal(t *testing.T) {
	tests := []struct {
		name     string
		external ExternalEventType
		expected InternalEventType
	}{
		{
			name:     "proposalMessage method conversion",
			external: ExternalEventProposalMessage,
			expected: InternalEventMessage,
		},
		{
			name:     "proposalStatus method conversion",
			external: ExternalEventProposalStatus,
			expected: InternalEventStatus,
		},
		{
			name:     "unknown method conversion",
			external: ExternalEventUnknown,
			expected: InternalEventUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.external.ToInternal()
			if result != tt.expected {
				t.Errorf("ExternalEventType.ToInternal() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestEventTypeString(t *testing.T) {
	t.Run("InternalEventType String", func(t *testing.T) {
		internal := InternalEventMessage
		expected := "message"
		if internal.String() != expected {
			t.Errorf("InternalEventType.String() = %v, want %v", internal.String(), expected)
		}
	})

	t.Run("InternalEventUnknown String", func(t *testing.T) {
		internal := InternalEventUnknown
		expected := "unknown"
		if internal.String() != expected {
			t.Errorf("InternalEventUnknown.String() = %v, want %v", internal.String(), expected)
		}
	})

	t.Run("ExternalEventType String", func(t *testing.T) {
		external := ExternalEventProposalMessage
		expected := "proposalMessage"
		if external.String() != expected {
			t.Errorf("ExternalEventType.String() = %v, want %v", external.String(), expected)
		}
	})

	t.Run("ExternalEventUnknown String", func(t *testing.T) {
		external := ExternalEventUnknown
		expected := "unknown"
		if external.String() != expected {
			t.Errorf("ExternalEventUnknown.String() = %v, want %v", external.String(), expected)
		}
	})
}

func TestToInternalEventTypeFromString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected InternalEventType
	}{
		{
			name:     "message string",
			input:    "message",
			expected: InternalEventMessage,
		},
		{
			name:     "status string",
			input:    "status",
			expected: InternalEventStatus,
		},
		{
			name:     "unknown string",
			input:    "unknown",
			expected: InternalEventUnknown,
		},
		{
			name:     "empty string",
			input:    "",
			expected: InternalEventUnknown,
		},
		{
			name:     "invalid string",
			input:    "invalid",
			expected: InternalEventUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToInternalEventTypeFromString(tt.input)
			if result != tt.expected {
				t.Errorf("ToInternalEventTypeFromString(%v) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestToExternalEventTypeFromString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected ExternalEventType
	}{
		{
			name:     "proposalMessage string",
			input:    "proposalMessage",
			expected: ExternalEventProposalMessage,
		},
		{
			name:     "proposalStatus string",
			input:    "proposalStatus",
			expected: ExternalEventProposalStatus,
		},
		{
			name:     "unknown string",
			input:    "unknown",
			expected: ExternalEventUnknown,
		},
		{
			name:     "empty string",
			input:    "",
			expected: ExternalEventUnknown,
		},
		{
			name:     "invalid string",
			input:    "invalid",
			expected: ExternalEventUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToExternalEventTypeFromString(tt.input)
			if result != tt.expected {
				t.Errorf("ToExternalEventTypeFromString(%v) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}
