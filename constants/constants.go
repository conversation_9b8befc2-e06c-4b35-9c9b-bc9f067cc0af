package constants

const (
	CustomType  = "custom"
	SystemType  = "system"
	ProductType = "product"

	ProposalStatusDraft      = "draft"
	ProposalStatusArchive    = "archive"
	ProposalStatusOnApproval = "on_approval"
	ProposalStatusRejected   = "rejected"
	ProposalStatusApproved   = "approved"

	PermissionMethodView   = "view"
	PermissionMethodCreate = "create"
	PermissionMethodUpdate = "update"
	PermissionMethodDelete = "delete"

	CategoryDefaultID int64 = 1
	CategorySystemID  int64 = 4

	ProposalGeneralStandNum = 1

	ZeroProductID    int64 = 0
	DefaultProductID int64 = 1
	DefaultLimit     int64 = 10
	DefaultOffset    int64 = 0
)

type ProductStatus string

const RequestContextKey = "requestContext"

const (
	ProductStatusActive  ProductStatus = "active"
	ProductStatusArchive ProductStatus = "archive"
	ProductStatusUnknown ProductStatus = "unknown"
)

const (
	Active  bool = true
	Archive bool = false
)

const (
	GroupActive  string = "active"
	GroupArchive string = "archive"
)

const (
	RoleActive  string = "active"
	RoleArchive string = "archive"
)

func (s ProductStatus) String() string {
	return string(s)
}

type contextKey string

const (
	UserIDKey     contextKey = "userID"
	DefaultUserID int64      = 0
)

func ToProductStatusFromString(s string) ProductStatus {
	switch s {
	case "active":
		return ProductStatusActive
	case "archive":
		return ProductStatusArchive
	default:
		return ProductStatusUnknown
	}
}

// PermissionsRu Mapping technical names to Russian
var PermissionsRu = map[string]string{
	"product":                       "Продукт",
	"product_participant":           "Продукт: участники",
	"product_participant_role":      "Участник: роли",
	"product_participant_group":     "Участник: группы",
	"product_description":           "Продукт: общее",
	"product_proposal":              "Продукт: заявки",
	"product_proposal_send_confirm": "Заявка: отправка на согласование",
	"product_proposal_confirm":      "Заявка: согласование",
	"product_proposal_comment":      "Заявка: комментарий",
	"product_proposal_history":      "Заявка: история",
	"product_stand_C":               "Продукт: управление стенд С",
	"product_stand_C1":              "Продукт: управление стенд С1",
	"product_stand_C2":              "Продукт: управление стенд С2",
	"product_stand_C3":              "Продукт: управление стенд С3",
	"product_role":                  "Продукт: роли",
	"product_role_page":             "Продукт: вкладка `Роли`",
	"product_group":                 "Продукт: группы",
	"product_group_page":            "Продукт: вкладка `Группы`",
	"product_archive":               "Продукт: архивация",
	"product_notification":          "Уведомления",
	"profile":                       "Профиль",
	"documentation":                 "Документация",
	"calculator":                    "Калькулятор",
	"billing":                       "Биллинг",
	"admin_product":                 "Админ: Продукты",
	"admin_user":                    "Админ: Пользователи",
	"admin_product_attribute":       "Админ: атрибуты Продукта",
	"admin_permission":              "Админ: пермишены",
	"admin_role_show":               "Админ: видимость ролей",
	"admin_auto_category":           "Админ: автоназначение категорий",
	"admin_group_show":              "Админ: видимость групп",
	"admin_inventory":               "Админ: учет ресурсов",
	"admin_category":                "Админ: категории",
	"admin_system_role":             "Админ: системные роли",
	"admin_system_group":            "Админ: системные группы",
	"service_status":                "Сервис: статус",
}

// PermissionsRuReverse Mapping Russian names to technical
var PermissionsRuReverse = map[string]string{
	"Продукт":                          "product",
	"Продукт: участники":               "product_participant",
	"Участник: роли":                   "product_participant_role",
	"Участник: группы":                 "product_participant_group",
	"Продукт: общее":                   "product_description",
	"Продукт: заявки":                  "product_proposal",
	"Заявка: отправка на согласование": "product_proposal_send_confirm",
	"Заявка: согласование":             "product_proposal_confirm",
	"Заявка: комментарий":              "product_proposal_comment",
	"Заявка: история":                  "product_proposal_history",
	"Продукт: управление стенд С":      "product_stand_C",
	"Продукт: управление стенд С1":     "product_stand_C1",
	"Продукт: управление стенд С2":     "product_stand_C2",
	"Продукт: управление стенд С3":     "product_stand_C3",
	"Продукт: роли":                    "product_role",
	"Продукт: вкладка `Роли`":          "product_role_page",
	"Продукт: группы":                  "product_group",
	"Продукт: вкладка `Группы`":        "product_group_page",
	"Продукт: архивация":               "product_archive",
	"Уведомления":                      "product_notification",
	"Профиль":                          "profile",
	"Документация":                     "documentation",
	"Калькулятор":                      "calculator",
	"Биллинг":                          "billing",
	"Админ: Продукты":                  "admin_product",
	"Админ: Пользователи":              "admin_user",
	"Админ: атрибуты Продукта":         "admin_product_attribute",
	"Админ: пермишены":                 "admin_permission",
	"Админ: видимость ролей":           "admin_role_show",
	"Админ: автоназначение категорий":  "admin_auto_category",
	"Админ: видимость групп":           "admin_group_show",
	"Админ: учет ресурсов":             "admin_inventory",
	"Админ: категории":                 "admin_category",
	"Админ: системные роли":            "admin_system_role",
	"Админ: системные группы":          "admin_system_group",
	"Сервис: статус":                   "service_status",
}

// PermissionsEn Mapping technical names to English
var PermissionsEn = map[string]string{
	"product":                       "Product",
	"product_participant":           "Product: Participants",
	"product_participant_role":      "Participant: Roles",
	"product_participant_group":     "Participant: Groups",
	"product_description":           "Product: Description",
	"product_proposal":              "Product: Proposals",
	"product_proposal_send_confirm": "Proposal: Send for Approval",
	"product_proposal_confirm":      "Proposal: Approval",
	"product_proposal_comment":      "Proposal: Comment",
	"product_proposal_history":      "Proposal: History",
	"product_stand_C":               "Product: Manage Stand C",
	"product_stand_C1":              "Product: Manage Stand C1",
	"product_stand_C2":              "Product: Manage Stand C2",
	"product_stand_C3":              "Product: Manage Stand C3",
	"product_role":                  "Product: Roles",
	"product_role_page":             "Product: Roles: Page",
	"product_group":                 "Product: Groups",
	"product_group_page":            "Product: Groups: Page",
	"product_archive":               "Product: Archiving",
	"product_notification":          "Notifications",
	"profile":                       "Profile",
	"documentation":                 "Documentation",
	"calculator":                    "Calculator",
	"billing":                       "Billing",
	"admin_product":                 "Admin: Products",
	"admin_user":                    "Admin: Users",
	"admin_product_attribute":       "Admin: Product Attributes",
	"admin_permission":              "Admin: Permissions",
	"admin_role_show":               "Admin: Role Visibility",
	"admin_auto_category":           "Admin: Auto-assign Categories",
	"admin_group_show":              "Admin: Group Visibility",
	"admin_inventory":               "Admin: Inventory",
	"admin_category":                "Admin: Categories",
	"admin_system_role":             "Admin: System Roles",
	"admin_system_group":            "Admin: System Groups",
	"service_status":                "Service: Status",
}

// PermissionsEnReverse Mapping English names to technical
var PermissionsEnReverse = map[string]string{
	"Product":                       "product",
	"Product: Participants":         "product_participant",
	"Participant: Roles":            "product_participant_role",
	"Participant: Groups":           "product_participant_group",
	"Product: Description":          "product_description",
	"Product: Proposals":            "product_proposal",
	"Proposal: Send for Approval":   "product_proposal_send_confirm",
	"Proposal: Approval":            "product_proposal_confirm",
	"Proposal: Comment":             "product_proposal_comment",
	"Proposal: History":             "product_proposal_history",
	"Product: Manage Stand C":       "product_stand_C",
	"Product: Manage Stand C1":      "product_stand_C1",
	"Product: Manage Stand C2":      "product_stand_C2",
	"Product: Manage Stand C3":      "product_stand_C3",
	"Product: Roles":                "product_role",
	"Product: Roles: Page":          "product_role_page",
	"Product: Groups":               "product_group",
	"Product: Groups: Page":         "product_group_page",
	"Product: Archiving":            "product_archive",
	"Notifications":                 "product_notification",
	"Profile":                       "profile",
	"Documentation":                 "documentation",
	"Calculator":                    "calculator",
	"Billing":                       "billing",
	"Admin: Products":               "admin_product",
	"Admin: Users":                  "admin_user",
	"Admin: Product Attributes":     "admin_product_attribute",
	"Admin: Permissions":            "admin_permission",
	"Admin: Role Visibility":        "admin_role_show",
	"Admin: Auto-assign Categories": "admin_auto_category",
	"Admin: Group Visibility":       "admin_group_show",
	"Admin: Inventory":              "admin_inventory",
	"Admin: Categories":             "admin_category",
	"Admin: System Roles":           "admin_system_role",
	"Admin: System Groups":          "admin_system_group",
	"Service: Status":               "service_status",
}

// PermissionMethodRu Mapping technical names to Russian
var PermissionMethodRu = map[string]string{
	PermissionMethodView:   "Просмотр",
	PermissionMethodCreate: "Создание",
	PermissionMethodUpdate: "Изменение",
	PermissionMethodDelete: "Удаление",
}

// PermissionMethodRuReverse Mapping Russian names to technical
var PermissionMethodRuReverse = map[string]string{
	"Просмотр":  PermissionMethodView,
	"Создание":  PermissionMethodCreate,
	"Изменение": PermissionMethodUpdate,
	"Удаление":  PermissionMethodDelete,
}

// PermissionMethodEn Mapping technical names to English
var PermissionMethodEn = map[string]string{
	PermissionMethodView:   "View",
	PermissionMethodCreate: "Create",
	PermissionMethodUpdate: "Update",
	PermissionMethodDelete: "Delete",
}

// PermissionMethodEnReverse Mapping English names to technical
var PermissionMethodEnReverse = map[string]string{
	"View":   PermissionMethodView,
	"Create": PermissionMethodCreate,
	"Update": PermissionMethodUpdate,
	"Delete": PermissionMethodDelete,
}

// IsSystemToConstant converts bool to system type string.
// Returns SystemType for true, CustomType for false.
func IsSystemToConstant(isSystem bool) string {
	if isSystem {
		return SystemType
	}
	return CustomType
}

// ToBoolPtrFromSystemType converts system type string to bool pointer.
// Returns nil for empty string, true for SystemType, false for CustomType.
func ToBoolPtrFromSystemType(systemType string) *bool {
	if systemType == "" {
		return nil
	}

	isSystem := systemType == SystemType
	return &isSystem
}

// ToBoolFromSystemType converts system type string to bool.
// Returns true for SystemType, false for CustomType or empty string.
func ToBoolFromSystemType(systemType string) bool {
	return systemType == SystemType
}

// SystemTypePtr returns a pointer to the system type string.
func SystemTypePtr(isSystem bool) *string {
	if isSystem {
		systemType := SystemType
		return &systemType
	}
	customType := CustomType
	return &customType
}

// SystemTypePtrOrNil returns a pointer to the system type string or nil for false.
func SystemTypePtrOrNil(isSystem bool) *string {
	if !isSystem {
		return nil
	}
	systemType := SystemType
	return &systemType
}
