package constants

// Internal proposal event types
type InternalEventType string

const (
	InternalEventUnknown InternalEventType = "unknown"
	InternalEventMessage InternalEventType = "message"
	InternalEventStatus  InternalEventType = "status"
)

// External proposal event types
type ExternalEventType string

const (
	ExternalEventUnknown         ExternalEventType = "unknown"
	ExternalEventProposalMessage ExternalEventType = "proposalMessage"
	ExternalEventProposalStatus  ExternalEventType = "proposalStatus"
)

// ToExternalEventType converts internal event type to external event type
func ToExternalEventType(internal InternalEventType) ExternalEventType {
	switch internal {
	case InternalEventMessage:
		return ExternalEventProposalMessage
	case InternalEventStatus:
		return ExternalEventProposalStatus
	case InternalEventUnknown:
		return ExternalEventUnknown
	default:
		return ExternalEventUnknown
	}
}

// ToInternalEventType converts external event type to internal event type
func ToInternalEventType(external ExternalEventType) InternalEventType {
	switch external {
	case ExternalEventProposalMessage:
		return InternalEventMessage
	case ExternalEventProposalStatus:
		return InternalEventStatus
	case ExternalEventUnknown:
		return InternalEventUnknown
	default:
		return InternalEventUnknown
	}
}

// String returns string representation of internal event type
func (e InternalEventType) String() string {
	return string(e)
}

// String returns string representation of external event type
func (e ExternalEventType) String() string {
	return string(e)
}

// ToExternal converts internal event type to external event type (method)
func (e InternalEventType) ToExternal() ExternalEventType {
	return ToExternalEventType(e)
}

// ToInternal converts external event type to internal event type (method)
func (e ExternalEventType) ToInternal() InternalEventType {
	return ToInternalEventType(e)
}

// ToInternalEventTypeFromString converts string to internal event type
func ToInternalEventTypeFromString(s string) InternalEventType {
	switch s {
	case "message":
		return InternalEventMessage
	case "status":
		return InternalEventStatus
	case "unknown":
		return InternalEventUnknown
	default:
		return InternalEventUnknown
	}
}

// ToExternalEventTypeFromString converts string to external event type
func ToExternalEventTypeFromString(s string) ExternalEventType {
	switch s {
	case "proposalMessage":
		return ExternalEventProposalMessage
	case "proposalStatus":
		return ExternalEventProposalStatus
	case "unknown":
		return ExternalEventUnknown
	default:
		return ExternalEventUnknown
	}
}
