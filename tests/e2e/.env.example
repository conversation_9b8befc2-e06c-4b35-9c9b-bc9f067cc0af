# Пример переменных окружения для E2E тестов
# Скопируйте этот файл в .env и заполните значения

# PostgreSQL (обязательно)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password

# Redis (опционально, для полноценного тестирования кэша)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Keycloak (опционально, для полноценного тестирования авторизации)
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=master
KEYCLOAK_CLIENT_ID=admin-cli
KEYCLOAK_USERNAME=admin
KEYCLOAK_PASSWORD=your_keycloak_password

# Примечания:
# - POSTGRES_PASSWORD обязательна для работы тестов
# - Если Redis недоступен, тесты будут работать без кэширования
# - Если Keycloak недоступен, тесты будут работать без проверки авторизации
# - Для загрузки переменных используйте: source .env
