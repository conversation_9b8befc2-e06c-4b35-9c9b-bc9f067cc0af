package integration

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestSystemGroup_UpdateAndGet_Success тестирует обновление и получение системной группы
func TestSystemGroup_UpdateAndGet_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста
	groupID := int64(1) // Используем существующую системную группу из фикстур (System Admins)
	updateRequest := systemgroup.GroupFullV1DTO{
		Type: constants.SystemType,
	}

	// Act - выполняем действия
	// 1. Обновляем группу через PATCH /v1/groups/:groupID
	patchResp := client.PATCH(t, fmt.Sprintf("/v1/groups/%d", groupID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, patchResp, http.StatusOK)

	var patchResult systemgroup.GroupFullV1DTO
	common.ReadJSONResponse(t, patchResp, &patchResult)

	// Проверяем, что обновление прошло успешно
	assert.Equal(t, groupID, patchResult.ID)
	assert.Equal(t, constants.SystemType, patchResult.Type)

	// 2. Получаем группу через GET /v1/groups/:groupID
	getResp := client.GET(t, fmt.Sprintf("/v1/groups/%d", groupID))

	// Assert - проверяем результат получения
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getResult systemgroup.GroupFullV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Проверяем, что данные соответствуют обновленным
	assert.Equal(t, groupID, getResult.ID)
	assert.Equal(t, constants.SystemType, getResult.Type)

	// 3. Дополнительная проверка через БД
	assertGroupInDatabase(t, db, groupID, constants.SystemType)
}

// TestSystemGroup_UpdateNonExistent_NotFound тестирует обновление несуществующей группы
func TestSystemGroup_UpdateNonExistent_NotFound(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	nonExistentGroupID := int64(99999)
	updateRequest := systemgroup.GroupFullV1DTO{
		Type: constants.SystemType,
	}

	// Act
	resp := client.PATCH(t, fmt.Sprintf("/v1/groups/%d", nonExistentGroupID), updateRequest)

	// Assert
	common.AssertStatusCode(t, resp, http.StatusNotFound)
}

// TestSystemGroup_GetNonExistent_NotFound тестирует получение несуществующей группы
func TestSystemGroup_GetNonExistent_NotFound(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	nonExistentGroupID := int64(99999)

	// Act
	resp := client.GET(t, fmt.Sprintf("/v1/groups/%d", nonExistentGroupID))

	// Assert
	common.AssertStatusCode(t, resp, http.StatusNotFound)
}

// TestSystemGroup_UpdateWithInvalidData_BadRequest тестирует обновление с некорректными данными
func TestSystemGroup_UpdateWithInvalidData_BadRequest(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	groupID := int64(1) // Используем существующую системную группу из фикстур

	// Некорректные данные (например, неправильный тип)
	invalidRequest := map[string]interface{}{
		"type": "invalid_type",
	}

	// Act
	resp := client.PATCH(t, fmt.Sprintf("/v1/groups/%d", groupID), invalidRequest)

	// Assert
	common.AssertStatusCode(t, resp, http.StatusBadRequest)
}

// TestSystemGroup_CreateUpdateDelete_FullCycle тестирует полный цикл CRUD операций
func TestSystemGroup_CreateUpdateDelete_FullCycle(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// 1. Создаем группу
	createRequest := systemgroup.GroupCreateV1DTO{
		Name: "Test E2E Group",
	}

	createResp := client.POST(t, "/v1/groups", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdGroup systemgroup.GroupShortV1DTO
	common.ReadJSONResponse(t, createResp, &createdGroup)

	groupID := createdGroup.ID
	require.NotZero(t, groupID)

	// 2. Обновляем группу
	updateRequest := systemgroup.GroupFullV1DTO{
		Type: constants.SystemType,
		Name: "Updated E2E Group",
	}

	updateResp := client.PATCH(t, fmt.Sprintf("/v1/groups/%d", groupID), updateRequest)
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	// 3. Проверяем обновление
	getResp := client.GET(t, fmt.Sprintf("/v1/groups/%d", groupID))
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var updatedGroup systemgroup.GroupFullV1DTO
	common.ReadJSONResponse(t, getResp, &updatedGroup)

	assert.Equal(t, "Updated E2E Group", updatedGroup.Name)
	assert.Equal(t, constants.SystemType, updatedGroup.Type)

	// 4. Удаляем группу (изменяем active_flg на false)
	deleteResp := client.DELETE(t, fmt.Sprintf("/v1/groups/%d", groupID))
	common.AssertStatusCode(t, deleteResp, http.StatusOK)

	// 5. Проверяем, что группа помечена как неактивная
	// Группа должна существовать, но быть неактивной
	getAfterDeleteResp := client.GET(t, fmt.Sprintf("/v1/groups/%d", groupID))
	common.AssertStatusCode(t, getAfterDeleteResp, http.StatusOK)

	var deletedGroup systemgroup.GroupFullV1DTO
	common.ReadJSONResponse(t, getAfterDeleteResp, &deletedGroup)

	// Проверяем, что данные группы сохранились
	assert.Equal(t, "Updated E2E Group", deletedGroup.Name)
	assert.Equal(t, constants.SystemType, deletedGroup.Type)
}

// assertGroupInDatabase проверяет данные группы в базе данных
func assertGroupInDatabase(t *testing.T, db *setup.TestDatabase, groupID int64, expectedType string) {
	var groupType string
	var isSystem bool

	query := `SELECT 
		CASE WHEN is_system THEN 'system' ELSE 'product' END as type,
		is_system 
	FROM groups WHERE id = $1`

	err := db.Pool.QueryRow(context.Background(), query, groupID).Scan(&groupType, &isSystem)
	require.NoError(t, err, "Failed to query group from database")

	assert.Equal(t, expectedType, groupType)
	if expectedType == constants.SystemType {
		assert.True(t, isSystem)
	} else {
		assert.False(t, isSystem)
	}
}
