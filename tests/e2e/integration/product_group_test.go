package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestProductGroup_FullWorkflow(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	createProductRequest := product.ProductCreateV1DTO{
		IID:         "TGRP",
		TechName:    "testgroup",
		Name:        "ProductFullGroupWorkflow",
		OwnerEmails: []string{"<EMAIL>"},
	}

	resp := client.POST(t, "/v1/products", createProductRequest)
	common.AssertStatusCode(t, resp, http.StatusOK)

	var createdProduct product.ProductV1DTO
	common.ReadJSONResponse(t, resp, &createdProduct)
	productID := createdProduct.ID

	createGroupRequest := productgroup.GroupCreateV1DTO{
		Name: "Test Group 1",
	}

	groupResp := client.POST(t, fmt.Sprintf("/v1/products/%d/groups", productID), createGroupRequest)
	common.AssertStatusCode(t, groupResp, http.StatusOK)

	var createdGroup productgroup.GroupShortV1DTO
	common.ReadJSONResponse(t, groupResp, &createdGroup)
	groupID := createdGroup.ID

	assert.Greater(t, createdGroup.ID, int64(0))
	assert.Equal(t, "Test Group 1", createdGroup.Name)
	assert.Equal(t, constants.CustomType, createdGroup.Type)
	assert.Equal(t, int64(0), createdGroup.RoleCount)
	assert.Equal(t, int64(0), createdGroup.ParticipantCount)
	assert.Equal(t, int64(0), createdGroup.UserCount)

	// Получение списка групп
	t.Run("GetProductGroups", func(t *testing.T) {
		getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/groups", productID))
		common.AssertStatusCode(t, getResp, http.StatusOK)

		var groups []productgroup.GroupShortV1DTO
		common.ReadJSONResponse(t, getResp, &groups)

		require.NotEmpty(t, groups)
		var foundGroup *productgroup.GroupShortV1DTO
		for _, group := range groups {
			if group.ID == createdGroup.ID {
				foundGroup = &group
				break
			}
		}

		require.NotNil(t, foundGroup, "Created group should be present in list")
		assert.Equal(t, "Test Group 1", foundGroup.Name)
		assert.Equal(t, constants.CustomType, foundGroup.Type)
	})

	// Получение полной информации о группе
	t.Run("GetGroupFull", func(t *testing.T) {
		getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/groups/%d", productID, groupID))
		common.AssertStatusCode(t, getResp, http.StatusOK)

		var fullGroup productgroup.GroupFullV1DTO
		common.ReadJSONResponse(t, getResp, &fullGroup)

		assert.Equal(t, groupID, fullGroup.ID)
		assert.Equal(t, "Test Group 1", fullGroup.Name)
		assert.Equal(t, constants.CustomType, fullGroup.Type)
		assert.Empty(t, fullGroup.ParticipantIDs)
		assert.Empty(t, fullGroup.UserIDs)
		assert.Empty(t, fullGroup.RoleIDs)
	})

	t.Run("UpdateGroup", func(t *testing.T) {
		gName := "Updated Group"
		gType := constants.CustomType

		updateReq := productgroup.GroupUpdateV1DTO{
			Name: &gName,
			Type: &gType,
		}

		patchResp := client.PATCH(t, fmt.Sprintf("/v1/products/%d/groups/%d", productID, groupID), updateReq)
		common.AssertStatusCode(t, patchResp, http.StatusOK)

		var updatedGroup productgroup.GroupFullV1DTO
		common.ReadJSONResponse(t, patchResp, &updatedGroup)

		assert.Equal(t, groupID, updatedGroup.ID)
		assert.Equal(t, "Updated Group", updatedGroup.Name)
		assert.Equal(t, constants.CustomType, updatedGroup.Type)
	})

	t.Run("DeleteGroup", func(t *testing.T) {
		delResp := client.DELETE(t, fmt.Sprintf("/v1/products/%d/groups/%d", productID, groupID))
		common.AssertStatusCode(t, delResp, http.StatusOK)
	})

	t.Run("NegativeCases", func(t *testing.T) {

		t.Run("GetGroup_NonExistentGroup", func(t *testing.T) {
			getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/groups/999999", productID))
			assert.Equal(t, http.StatusNotFound, getResp.StatusCode)
		})

		t.Run("UpdateGroup_NonExistent", func(t *testing.T) {
			name := "Ghost"
			typ := constants.CustomType
			req := productgroup.GroupUpdateV1DTO{Name: &name, Type: &typ}
			resp := client.PATCH(t, fmt.Sprintf("/v1/products/%d/groups/999999", productID), req)
			assert.Equal(t, http.StatusNotFound, resp.StatusCode)
		})

		t.Run("GetGroup_Deleted", func(t *testing.T) {
			resp := client.GET(t, fmt.Sprintf("/v1/products/%d/groups/%d", productID, groupID))
			assert.Equal(t, http.StatusNotFound, resp.StatusCode)
		})
	})
}
