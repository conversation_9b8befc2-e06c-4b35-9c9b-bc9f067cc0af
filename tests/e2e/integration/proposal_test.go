package integration

import (
	"fmt"
	"net/http"
	"testing"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
	"github.com/stretchr/testify/assert"
)

// TestProposal_CreateAndGetByProductIDAndProposalID_Success тестирует успешное создание и получение предложения по ID
func TestProposal_CreateAndGetByProductIDAndProposalID_Success(t *testing.T) {
	// Arrange - инициализация тестовой базы и сервера
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение и получаем его ID
	productID := int64(1)
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	var postResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResult)

	// Act - выполняем GET-запрос для получения предложения по ID
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d", postResult.ProductID, postResult.ID))
	common.AssertStatusCode(t, getResp, http.StatusOK)

	// Парсим ответ в DTO
	var getResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Assert - проверяем соответствие всех ключевых полей
	assert.Equal(t, getResult.PropSeq, postResult.PropSeq, "PropSeq должен совпадать")
	assert.Equal(t, getResult.ProductID, postResult.ProductID, "ProductID должен совпадать")
	assert.Equal(t, getResult.Price, postResult.Price, "Price должен совпадать")
	assert.Equal(t, getResult.Type, postResult.Type, "Type должен совпадать")
	assert.Equal(t, getResult.Status, postResult.Status, "Status должен совпадать")
	assert.Equal(t, getResult.CreatorID, postResult.CreatorID, "CreatorID должен совпадать")
	assert.Equal(t, getResult.CreatedAt, postResult.CreatedAt, "CreatedAt должен совпадать")
	assert.Equal(t, getResult.UpdatedAt, postResult.UpdatedAt, "UpdatedAt должен совпадать")
	assert.Equal(t, getResult.Data, postResult.Data, "Data должен совпадать")
}

// TestProposal_GetAll_Success тестирует успешное получение всех предложений для продукта
func TestProposal_GetAll_Success(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение
	productID := int64(2)
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	var postResultOne proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResultOne)

	// Act - получение всех предложений продукта
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals", productID))
	common.AssertStatusCode(t, getResp, http.StatusOK)

	// Парсинг массива предложений
	var getResult []proposal.ProposalV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Assert - проверка корректности возвращаемого списка
	assert.NotEmpty(t, getResult, "Результат не должен быть пустым")

	if len(getResult) > 0 {
		// Find the created proposal in the results (order doesn't matter)
		var foundProposal *proposal.ProposalV1DTO
		for i := range getResult {
			if getResult[i].ID == postResultOne.ID {
				foundProposal = &getResult[i]
				break
			}
		}

		assert.NotNil(t, foundProposal, "Созданное предложение должно быть найдено в результатах")
		if foundProposal != nil {
			assert.Equal(t, postResultOne.PropSeq, foundProposal.PropSeq, "PropSeq должен совпадать")
			assert.Equal(t, postResultOne.ProductID, foundProposal.ProductID, "ProductID должен совпадать")
			assert.Equal(t, postResultOne.Price, foundProposal.Price, "Price должен совпадать")
			assert.Equal(t, postResultOne.Type, foundProposal.Type, "Type должен совпадать")
			assert.Equal(t, postResultOne.Status, foundProposal.Status, "Status должен совпадать")
			assert.Equal(t, postResultOne.CreatorID, foundProposal.CreatorID, "CreatorID должен совпадать")
		}
	}
}

// TestProposal_GetByProductIDAndProposalID_InternalServerError_1 тестирует ошибку 500 при получении несуществующего предложения
func TestProposal_GetByProductIDAndProposalID_InternalServerError_1(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение
	productID := int64(1)
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	var postResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResult)

	// Act - попытка получить несуществующее предложение
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d", postResult.ProductID, 99999))

	// Assert - проверка возврата внутренней ошибки сервера
	common.AssertStatusCode(t, getResp, http.StatusInternalServerError)
}

// TestProposal_GetByProductIDAndProposalID_InternalServerError_2 тестирует ошибку 500 при запросе с некорректными ID
func TestProposal_GetByProductIDAndProposalID_InternalServerError_2(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - попытка получить предложение с несуществующими ID
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d", 999999, 999999))

	// Assert - проверка возврата внутренней ошибки сервера
	common.AssertStatusCode(t, getResp, http.StatusInternalServerError)
}

// TestProposal_GetByProductIDAndProposalID_NotFound тестирует возврат пустого списка при запросе несуществующего продукта
func TestProposal_GetByProductIDAndProposalID_NotFound(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - попытка получить предложение для несуществующего продукта
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals", 99999999))

	// Assert - проверка возврата пустого списка
	common.AssertStatusCode(t, getResp, http.StatusOK)
}

// TestProposal_UpdateAndGet_Success тестирует успешное обновление и получение обновленного предложения
func TestProposal_UpdateAndGet_Success(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение
	productID := int64(1)
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	// Подготовка данных для обновления
	updatePropdosal := createdDataForProposalTestUpdate()
	var postResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResult)

	// Act - выполнение обновления
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/products/%d/proposals/%d", postResult.ProductID, postResult.ID), updatePropdosal)
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	// Парсинг результата обновления
	var updateResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, updateResp, &updateResult)

	// Получение обновленного предложения
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d", productID, postResult.ID))
	var getResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Assert - проверка корректности обновления
	assert.Equal(t, getResult.PropSeq, updateResult.PropSeq, "PropSeq должен совпадать")
	assert.Equal(t, getResult.ProductID, updateResult.ProductID, "ProductID должен совпадать")
	assert.Equal(t, getResult.Price, updateResult.Price, "Price должен совпадать")
	assert.Equal(t, getResult.Type, updateResult.Type, "Type должен совпадать")
	assert.Equal(t, getResult.Status, updateResult.Status, "Status должен совпадать")
	assert.Equal(t, getResult.CreatorID, updateResult.CreatorID, "CreatorID должен совпадать")
	assert.Equal(t, getResult.CreatedAt, updateResult.CreatedAt, "CreatedAt должен совпадать")
	assert.Equal(t, getResult.UpdatedAt, updateResult.UpdatedAt, "UpdatedAt должен совпадать")
	assert.Equal(t, getResult.Data, updateResult.Data, "Data должен совпадать")

	// Проверка сохранения номера предложения - система автоматически генерирует номера
	// При обновлении номер должен сохраняться неизменным (сравниваем с результатом GET)
	if postResult.ProposalNumber != nil {
		assert.NotNil(t, getResult.ProposalNumber, "ProposalNumber должен быть заполнен после GET запроса")
		if getResult.ProposalNumber != nil {
			assert.Equal(t, *postResult.ProposalNumber, *getResult.ProposalNumber, "ProposalNumber должен сохраняться при обновлении")
		}
	}
}

// TestProposal_UpdateNonExistent_Error тестирует ошибку при обновлении несуществующего предложения
func TestProposal_UpdateNonExistent_Error(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготовка данных для обновления
	updatePropdosal := createdDataForProposalTestUpdate()

	// Act - попытка обновить несуществующее предложение
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/products/%d/proposals/%d", 999999, 9999999), updatePropdosal)

	// Assert - проверка возврата внутренней ошибки сервера
	common.AssertStatusCode(t, updateResp, http.StatusInternalServerError)
}

// TestProposal__CreateUpdateDelete_FullCycle тестирует полный цикл жизни предложения (CRUD)
func TestProposal__CreateUpdateDelete_FullCycle(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение
	productID := int64(1)
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	// Подготовка данных для обновления
	updatePropdosal := createdDataForProposalTestUpdate()
	var postResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResult)

	// Act - обновление предложения
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/products/%d/proposals/%d", postResult.ProductID, postResult.ID), updatePropdosal)
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	// Парсинг результата обновления
	var updateResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, updateResp, &updateResult)

	// Получение обновленного предложения
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d", productID, postResult.ID))
	var getResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Удаление предложения
	deleteResp := client.DELETE(t, fmt.Sprintf("/v1/products/%d/proposals/%d", productID, getResult.ID))
	common.AssertStatusCode(t, deleteResp, http.StatusOK)

	// Попытка получить удаленное предложение
	getResp = client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d", productID, postResult.ID))

	// Assert - проверка, что предложение больше не доступно
	common.AssertStatusCode(t, getResp, http.StatusInternalServerError)
}

// TestProposal__DeleteNonExistent_Error тестирует ошибку при удалении несуществующего предложения
func TestProposal__DeleteNonExistent_Error(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - попытка удалить несуществующее предложение
	deleteResp := client.DELETE(t, fmt.Sprintf("/v1/products/%d/proposals/%d", 99999999, 99999999))

	// Assert - проверка возврата внутренней ошибки сервера
	common.AssertStatusCode(t, deleteResp, http.StatusInternalServerError)
}

// createdDataForProposalTestUpdate возвращает данные для тестового обновления предложения
// Содержит комплексные данные для проверки всех полей
func createdDataForProposalTestUpdate() proposal.ProposalUpdateV1DTO {
	proposalType := "approved"
	return proposal.ProposalUpdateV1DTO{
		Price:  10,
		Type:   &proposalType,
		Status: "approved",
		Data: map[string]interface{}{
			// Сложная структура данных для проверки корректности обработки
			"catalog": []interface{}{
				map[string]interface{}{
					"moneyTotal": map[string]interface{}{
						"CPU":        2655.28,
						"RAM":        1327.68,
						"S3hot":      760,
						"SSDCSI":     56.25,
						"SSDlocal":   1.25,
						"SSDnetwork": 3.8,
					},
					"resourcesTotal": map[string]interface{}{
						"CPU":        4,
						"RAM":        16,
						"S3hot":      1000,
						"SSDCSI":     0,
						"SSDlocal":   5,
						"SSDnetwork": 0,
					},
					"sections": []interface{}{
						map[string]interface{}{
							"collapse":          false,
							"editedSectionName": "",
							"fixed":             true,
							"groups": []interface{}{
								map[string]interface{}{
									"collapse":  false,
									"groupName": "",
									"rows": map[string]interface{}{
										"row2": []interface{}{
											map[string]interface{}{
												"disabled": false,
												"help":     "При выборе данной опции...",
												"isName":   false,
												"label":    "Доступ к DevSecOps конвейеру",
												"marked":   true,
												"required": false,
												"sum":      0,
												"type":     "checkbox",
												"value":    true,
											},
										},
									},
								},
							},
							"mustRename":  false,
							"notCopyable": false,
							"sectionName": "Дополнительные настройки",
							"variables": map[string]interface{}{
								"S3": 0,
							},
						},
					},
					"standGroup": []interface{}{
						map[string]interface{}{
							"name": "Итого по контуру разработки (DH1.C)",
							"standsName": []interface{}{
								"Контур разработки (DH1.C)",
							},
						},
					},
					"standIndex": 0,
					"standName":  "Внутренний предпродуктив (DH2.C2) 1",
					"staticVariables": map[string]interface{}{
						"globalCPU":        663.82,
						"globalEnv":        1,
						"globalRAM":        82.98,
						"globalS3hot":      0.76,
						"globalSSDCSI":     11.25,
						"globalSSDlocal":   0.25,
						"globalSSDnetwork": 0.76,
					},
					"sum": 4804.26,
					"variables": map[string]interface{}{
						"globalCPU":        663.82,
						"globalEnv":        1,
						"globalRAM":        82.98,
						"globalS3hot":      0.76,
						"globalSSDCSI":     11.25,
						"globalSSDlocal":   0.25,
						"globalSSDnetwork": 0.76,
					},
				},
			},
			"staticSections": []interface{}{
				map[string]interface{}{
					"collapse":          false,
					"editedSectionName": "",
					"fixed":             true,
					"groups": []interface{}{
						map[string]interface{}{
							"collapse":  false,
							"groupName": "",
							"rows": map[string]interface{}{
								"row3": []interface{}{
									map[string]interface{}{
										"disabled": false,
										"isName":   true,
										"label":    "Выберите стенд",
										"marked":   false,
										"options": []interface{}{
											"Контур разработки (DH1.C)",
											"Контур тестирования (DH2.C1)",
										},
										"required": false,
										"sum":      0,
										"type":     "select",
										"value":    "Внутренний продуктив (DH2.C2)",
										"varList": []interface{}{
											map[string]interface{}{
												"Контур разработки (DH1.C)": map[string]interface{}{
													"globalCPU":        516.94,
													"globalEnv":        0,
													"globalRAM":        80.17,
													"globalS3hot":      9.33,
													"globalSSDCSI":     3.11,
													"globalSSDlocal":   3.11,
													"globalSSDnetwork": 3.11,
												},
											},
										},
									},
								},
							},
						},
					},
					"isName":      true,
					"mustRename":  false,
					"notCopyable": false,
					"sectionName": "Общие настройки",
					"variables": map[string]interface{}{
						"globalCPU":        663.82,
						"globalEnv":        1,
						"globalRAM":        82.98,
						"globalS3hot":      0.76,
						"globalSSDCSI":     11.25,
						"globalSSDlocal":   0.25,
						"globalSSDnetwork": 0.76,
					},
				},
			},
			"variables": map[string]interface{}{
				"globalCPU":        663.82,
				"globalEnv":        1,
				"globalRAM":        82.98,
				"globalS3hot":      0.76,
				"globalSSDCSI":     11.25,
				"globalSSDlocal":   0.25,
				"globalSSDnetwork": 0.76,
			},
		},
	}
}

// TestProposal__GetHistory_Error тестирует получение истории для несуществующего предложения
func TestProposal__GetHistory_Error(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - попытка получить историю для несуществующего предложения (некорректные ID)
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d/history", 99999999, 99999999))

	// Assert - проверка, что возвращается пустой список истории (не ошибка)
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getHistory proposal.ProposalHistoryContainerV1DTO
	common.ReadJSONResponse(t, getResp, &getHistory)

	assert.Equal(t, 0, len(getHistory.Items), "Items должен быть пуст")
}

// TestProposal__GetHistoryNonExists_Success тестирует получение истории для несуществующего предложения (успешный сценарий)
func TestProposal__GetHistoryNonExists_Success(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - попытка получить историю для несуществующего предложения
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d/history", 99999999, 99999999))

	// Assert - проверка, что возвращается пустой список истории без ошибок
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getHistory proposal.ProposalHistoryContainerV1DTO
	common.ReadJSONResponse(t, getResp, &getHistory)

	assert.Equal(t, 0, len(getHistory.Items), "Items должен быть пуст")
}

// TestProposal__GetHistory_Success тестирует успешное получение истории для существующего предложения
func TestProposal__GetHistory_Success(t *testing.T) {
	// Arrange - инициализация тестовой среды
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение для получения истории
	productID := int64(2)
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	var postResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResult)

	// Act - получение истории для существующего предложения
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d/proposals/%d/history", productID, postResult.ID))

	// Assert - проверка, что история возвращается успешно с одной записью
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getHistory proposal.ProposalHistoryContainerV1DTO
	common.ReadJSONResponse(t, getResp, &getHistory)

	assert.Equal(t, 1, len(getHistory.Items), "Items должен содержать одну запись")
}
