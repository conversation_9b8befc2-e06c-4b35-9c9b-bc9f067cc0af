package integration

import (
	"net/http"
	"testing"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestProposal_NegativeScenarios_TableDriven объединяет все негативные сценарии для предложений
func TestProposal_NegativeScenarios_TableDriven(t *testing.T) {
	t.Run("ProposalNegativeTests", func(t *testing.T) {
		db := setup.InitTestDatabase(t)
		server := setup.InitTestServer(t, db)
		client := common.NewTestClient(server.BaseURL)

		// Тесты с невалидными ID продуктов и предложений
		t.Run("GetProposalWithInvalidProductID", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/abc/proposals/1")
			common.AssertStatusCode(t, resp, http.StatusBadRequest)
		})

		t.Run("GetProposalWithInvalidProposalID", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/1/proposals/abc")
			common.AssertStatusCode(t, resp, http.StatusBadRequest)
		})

		t.Run("GetProposalWithNegativeProductID", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/-1/proposals/1")
			common.AssertStatusCode(t, resp, http.StatusInternalServerError)
		})

		t.Run("GetProposalWithNegativeProposalID", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/1/proposals/-1")
			common.AssertStatusCode(t, resp, http.StatusInternalServerError)
		})

		// Тесты с несуществующими ресурсами
		t.Run("GetNonExistentProposal", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/999999/proposals/999999")
			common.AssertStatusCode(t, resp, http.StatusInternalServerError)
		})

		t.Run("UpdateNonExistentProposal", func(t *testing.T) {
			resp := client.PATCH(t, "/v1/products/999999/proposals/999999", createProposalUpdateData())
			common.AssertStatusCode(t, resp, http.StatusInternalServerError)
		})

		t.Run("DeleteNonExistentProposal", func(t *testing.T) {
			resp := client.DELETE(t, "/v1/products/999999/proposals/999999")
			common.AssertStatusCode(t, resp, http.StatusInternalServerError)
		})

		// Тесты создания предложений с невалидными данными
		t.Run("CreateProposalWithEmptyData", func(t *testing.T) {
			resp := client.POST(t, "/v1/products/1/proposals", proposal.ProposalCreateV1DTO{})
			common.AssertStatusCode(t, resp, http.StatusOK) // API разрешает создание с пустыми данными
		})

		t.Run("CreateProposalForNonExistentProduct", func(t *testing.T) {
			resp := client.POST(t, "/v1/products/999999/proposals", createValidProposalData())
			common.AssertStatusCode(t, resp, http.StatusInternalServerError)
		})

		// Тесты истории предложений
		t.Run("GetHistoryForNonExistentProposal", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/999999/proposals/999999/history")
			common.AssertStatusCode(t, resp, http.StatusOK) // API возвращает пустой массив вместо ошибки
		})

		t.Run("GetHistoryWithInvalidProductID", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/abc/proposals/1/history")
			common.AssertStatusCode(t, resp, http.StatusBadRequest)
		})

		t.Run("GetHistoryWithInvalidProposalID", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/1/proposals/abc/history")
			common.AssertStatusCode(t, resp, http.StatusBadRequest)
		})
	})
}

// createProposalUpdateData создает данные для обновления предложения
func createProposalUpdateData() proposal.ProposalUpdateV1DTO {
	proposalType := "approved"
	return proposal.ProposalUpdateV1DTO{
		Price:  10,
		Type:   &proposalType,
		Status: "approved",
		Data: map[string]interface{}{
			"test": "data",
		},
	}
}

// createValidProposalData создает валидные данные для создания предложения
func createValidProposalData() proposal.ProposalCreateV1DTO {
	proposalType := "draft"
	return proposal.ProposalCreateV1DTO{
		Price: 100,
		Type:  &proposalType,
		Data: map[string]interface{}{
			"test": "data",
		},
	}
}
