package integration

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestKeycloakUsers_Get_Success(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("GetKeycloakUsers", func(t *testing.T) {
		getResp := client.GET(t, "/v1/keycloak/users?search=test")
		common.AssertStatusCode(t, getResp, http.StatusOK)

		var users []struct {
			ID        string `json:"id"`
			Email     string `json:"email"`
			Username  string `json:"username"`
			FullName  string `json:"fullName"`
			LastName  string `json:"lastName"`
			FirstName string `json:"firstName"`
			Position  string `json:"position"`
			Enabled   bool   `json:"enabled"`
			Photo     string `json:""`
		}

		common.ReadJSONResponse(t, getResp, &users)

		assert.NotNil(t, users)
		assert.Greater(t, len(users), 0, "user list should not be empty")

		for _, u := range users {
			assert.NotEmpty(t, u.ID, "id should not be empty")
			assert.NotEmpty(t, u.Email, "email should not be empty")
			assert.NotEmpty(t, u.Username, "username should not be empty")
			assert.NotEmpty(t, u.FullName, "fullName should not be empty")
			assert.NotEmpty(t, u.LastName, "lastName should not be empty")
			assert.NotEmpty(t, u.FirstName, "firstName should not be empty")
			assert.NotEmpty(t, u.Position, "position should not be empty")
			assert.NotNil(t, u.Enabled, "enabled should be present")
			assert.NotNil(t, u.Photo, "photo should not be empty")
		}
	})
}
