package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/category"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestCategory_CreateAndGet(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("CreateCategory_Success", func(t *testing.T) {
		createReq := category.CategoryCreateV1DTO{
			Name: "Infrastructure",
		}

		resp := client.POST(t, "/v1/categories", createReq)
		common.AssertStatusCode(t, resp, http.StatusOK)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		end := bytes.IndexByte(body, byte('}'))
		require.Greater(t, end, 0, "response does not contain valid JSON")

		validJSON := body[:end+1]

		var created category.CategoryV1DTO
		err = json.Unmarshal(validJSON, &created)
		require.NoError(t, err)

		assert.Greater(t, created.ID, int64(0))
		assert.Equal(t, "Infrastructure", created.Name)
	})

	t.Run("GetCategories_Success", func(t *testing.T) {
		resp := client.GET(t, "/v1/categories")
		common.AssertStatusCode(t, resp, http.StatusOK)

		var cats []category.CategoryV1DTO
		common.ReadJSONResponse(t, resp, &cats)

		require.NotEmpty(t, cats)

		var found bool
		for _, cat := range cats {
			if cat.Name == "Infrastructure" {
				found = true
				break
			}
		}
		assert.True(t, found, "Created category should be present in the list")
	})
}

func TestCategory_FullWorkflow(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	var categoryID int64

	t.Run("CreateCategory", func(t *testing.T) {
		createReq := category.CategoryCreateV1DTO{
			Name: "DevOps",
		}

		resp := client.POST(t, "/v1/categories", createReq)
		common.AssertStatusCode(t, resp, http.StatusOK)

		body, err := io.ReadAll(resp.Body)
		require.NoError(t, err)

		end := bytes.IndexByte(body, byte('}'))
		require.Greater(t, end, 0, "response does not contain valid JSON")

		var created category.CategoryV1DTO
		err = json.Unmarshal(body[:end+1], &created)
		require.NoError(t, err)

		assert.Greater(t, created.ID, int64(0))
		assert.Equal(t, "DevOps", created.Name)

		categoryID = created.ID
	})

	t.Run("GetCategory", func(t *testing.T) {
		resp := client.GET(t, fmt.Sprintf("/v1/categories/%d", categoryID))
		common.AssertStatusCode(t, resp, http.StatusOK)

		var cat category.CategoryV1DTO
		common.ReadJSONResponse(t, resp, &cat)

		assert.Equal(t, categoryID, cat.ID)
		assert.Equal(t, "DevOps", cat.Name)
	})

	t.Run("UpdateCategory", func(t *testing.T) {
		newName := "SRE"

		updateReq := category.CategoryUpdateV1DTO{
			Name: newName,
		}

		resp := client.PATCH(t, fmt.Sprintf("/v1/categories/%d", categoryID), updateReq)
		common.AssertStatusCode(t, resp, http.StatusOK)

		var updated category.CategoryV1DTO
		common.ReadJSONResponse(t, resp, &updated)

		assert.Equal(t, categoryID, updated.ID)
		assert.Equal(t, "SRE", updated.Name)
	})

	t.Run("DeleteCategory", func(t *testing.T) {
		resp := client.DELETE(t, fmt.Sprintf("/v1/categories/%d", categoryID))
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("GetDeletedCategory", func(t *testing.T) {
		resp := client.GET(t, fmt.Sprintf("/v1/categories/%d", categoryID))
		assert.Equal(t, http.StatusInternalServerError, resp.StatusCode)
	})
}
