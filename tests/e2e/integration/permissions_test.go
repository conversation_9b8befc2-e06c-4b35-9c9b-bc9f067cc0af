package integration

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestPermissions_Get_Success(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("GetPermissions", func(t *testing.T) {

		getResp := client.GET(t, "/v1/permissions")
		common.AssertStatusCode(t, getResp, http.StatusOK)

		var perms []struct {
			Label   string `json:"label"`
			Name    string `json:"name"`
			Methods []struct {
				Name string `json:"name"`
			} `json:"methods"`
		}

		common.ReadJSONResponse(t, getResp, &perms)

		assert.NotNil(t, perms, "permissions should not be nil")
		assert.Greater(t, len(perms), 0, "permissions list should not be empty")

		for _, p := range perms {
			assert.NotEmpty(t, p.Label, "permission label should not be empty")
			assert.NotEmpty(t, p.Name, "permission name should not be empty")
			assert.NotNil(t, p.Methods, "methods should not be nil")
			for _, m := range p.Methods {
				assert.NotEmpty(t, m.Name, "method name should not be empty")
			}
		}
	})
}
