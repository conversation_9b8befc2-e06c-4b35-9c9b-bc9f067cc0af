package integration

import (
	"fmt"
	"net/http"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/permission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/user"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestUserIntegration_FullWorkflow(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("CreateUser", func(t *testing.T) {
		createUserRequest := user.UserCreateV1DTO{
			Email:    "<EMAIL>",
			FullName: "Mock User",
			Photo:    "",
		}

		postresp := client.POST(t, "/v1/users", createUserRequest)
		common.AssertStatusCode(t, postresp, http.StatusCreated)

		var createdUser user.UserV1DTO
		common.ReadJSONResponse(t, postresp, &createdUser)

		assert.Equal(t, "<EMAIL>", createdUser.Email)
		assert.Equal(t, "Mock User", createdUser.FullName)
		assert.Equal(t, "", createdUser.Photo)

		userID := createdUser.ID

		t.Run("GetUser", func(t *testing.T) {
			getResp := client.GET(t, "/v1/users")
			common.AssertStatusCode(t, getResp, http.StatusOK)

			var userCollectionV1DTO user.UserCollectionV1DTO
			common.ReadJSONResponse(t, getResp, &userCollectionV1DTO)

			var found user.UserV1DTO
			for _, u := range userCollectionV1DTO.Items {
				if u.ID == userID {
					found = u
					break
				}
			}

			assert.Equal(t, userID, found.ID)
			assert.Equal(t, "<EMAIL>", found.Email)
			assert.Equal(t, "Mock User", found.FullName)
			assert.Equal(t, "", found.Photo)
		})

		t.Run("GetUserByID", func(t *testing.T) {
			url := fmt.Sprintf("/v1/users/%d", userID)
			getResp := client.GET(t, url)
			common.AssertStatusCode(t, getResp, http.StatusOK)

			var fetchedUser user.UserV1DTO
			common.ReadJSONResponse(t, getResp, &fetchedUser)

			assert.Equal(t, userID, fetchedUser.ID)
			assert.Equal(t, "<EMAIL>", fetchedUser.Email)
			assert.Equal(t, "Mock User", fetchedUser.FullName)
			assert.Equal(t, "", fetchedUser.Photo)
		})

		t.Run("UpdateUser", func(t *testing.T) {
			url := fmt.Sprintf("/v1/users/%d", userID)

			categoryID := int64(1)
			productIDs := []int64{1, 2, 3}
			roleIDs := []int64{1, 2, 3}
			groupIDs := []int64{1, 2, 3}

			updateReq := user.UserUpdateV1DTO{
				CategoryID: &categoryID,
				ProductIDs: &productIDs,
				RoleIDs:    &roleIDs,
				GroupIDs:   &groupIDs,
			}

			postResp := client.PATCH(t, url, updateReq)
			common.AssertStatusCode(t, postResp, http.StatusOK)

			var updatedUser user.UserV1DTO
			common.ReadJSONResponse(t, postResp, &updatedUser)

			assert.Equal(t, userID, updatedUser.ID)
			assert.Equal(t, int64(1), updatedUser.Category)
		})

		t.Run("GetUserPermissions", func(t *testing.T) {
			getResp := client.GET(t, "/v1/users/mypermissions")
			common.AssertStatusCode(t, getResp, http.StatusOK)

			var permissions []permission.PermissionV1DTO
			common.ReadJSONResponse(t, getResp, &permissions)

			assert.NotNil(t, permissions)
		})

		t.Run("GetUserGroupsWithProducts", func(t *testing.T) {
			url := fmt.Sprintf("/v1/users/%d/groups", userID)
			getResp := client.GET(t, url)
			common.AssertStatusCode(t, getResp, http.StatusOK)

			var groups []user.GroupWithProductCollectionV1DTO
			common.ReadJSONResponse(t, getResp, &groups)

			assert.NotNil(t, groups)

			if len(groups) > 0 {
				// Check that expected groups are present (order doesn't matter)
				expectedGroupIDs := []int64{1, 2, 3}
				actualGroupIDs := make([]int64, len(groups))
				for i, group := range groups {
					actualGroupIDs[i] = group.ID
				}

				// Check that all expected groups are present (order doesn't matter)
				for _, expectedID := range expectedGroupIDs {
					found := false
					for _, actualID := range actualGroupIDs {
						if actualID == expectedID {
							found = true
							break
						}
					}
					assert.True(t, found, "Expected group ID %d not found in actual groups %v", expectedID, actualGroupIDs)
				}
			}
		})

		t.Run("GetUserProducts", func(t *testing.T) {
			url := fmt.Sprintf("/v1/users/%d/products", userID)
			getResp := client.GET(t, url)
			common.AssertStatusCode(t, getResp, http.StatusOK)

			var userProducts []user.ProductBasicV1DTO
			common.ReadJSONResponse(t, getResp, &userProducts)

			assert.NotNil(t, userProducts)

			if len(userProducts) > 0 {
				// Check that expected products are present (order doesn't matter)
				expectedProductIDs := []int64{1, 2} // Only expecting products that exist in test data
				actualProductIDs := make([]int64, len(userProducts))
				for i, product := range userProducts {
					actualProductIDs[i] = product.ID
				}

				// Check that at least one expected product is present
				foundAtLeastOne := false
				for _, expectedID := range expectedProductIDs {
					for _, actualID := range actualProductIDs {
						if actualID == expectedID {
							foundAtLeastOne = true
							break
						}
					}
					if foundAtLeastOne {
						break
					}
				}
				assert.True(t, foundAtLeastOne, "Expected at least one product from %v, got %v", expectedProductIDs, actualProductIDs)
			}
		})

		t.Run("GetUserRolesWithProducts", func(t *testing.T) {
			url := fmt.Sprintf("/v1/users/%d/roles", userID)
			getResp := client.GET(t, url)
			common.AssertStatusCode(t, getResp, http.StatusOK)

			var roles user.RolesWithProductsCollectionV1DTO
			common.ReadJSONResponse(t, getResp, &roles)

			assert.NotNil(t, roles.Items)

			if len(roles.Items) > 0 {
				// Check that expected roles are present (order doesn't matter)
				expectedRoleIDs := []int64{1, 2, 3}
				actualRoleIDs := make([]int64, len(roles.Items))
				for i, role := range roles.Items {
					actualRoleIDs[i] = role.ID
				}

				// Check that at least one expected role is present
				foundAtLeastOne := false
				for _, expectedID := range expectedRoleIDs {
					for _, actualID := range actualRoleIDs {
						if actualID == expectedID {
							foundAtLeastOne = true
							break
						}
					}
					if foundAtLeastOne {
						break
					}
				}
				assert.True(t, foundAtLeastOne, "Expected at least one role from %v, got %v", expectedRoleIDs, actualRoleIDs)
			}
		})

		t.Run("DeleteUserGroups", func(t *testing.T) {
			params := url.Values{}
			groupIDs := []int64{1, 2, 3}
			for _, id := range groupIDs {
				params.Add("groupIDs", fmt.Sprintf("%d", id))
			}

			url := fmt.Sprintf("/v1/users/%d/groups?%s", userID, params.Encode())
			delResp := client.DELETE(t, url)
			common.AssertStatusCode(t, delResp, http.StatusOK)
		})

		t.Run("DeleteUserProducts", func(t *testing.T) {
			params := url.Values{}
			productIDs := []int64{1, 2, 3}
			for _, id := range productIDs {
				params.Add("productIDs", fmt.Sprintf("%d", id))
			}

			url := fmt.Sprintf("/v1/users/%d/products?%s", userID, params.Encode())
			delResp := client.DELETE(t, url)
			common.AssertStatusCode(t, delResp, http.StatusOK)
		})

		t.Run("DeleteUserRoles", func(t *testing.T) {
			params := url.Values{}
			roleIDs := []int64{1, 2, 3}
			for _, id := range roleIDs {
				params.Add("roleIDs", fmt.Sprintf("%d", id))
			}

			url := fmt.Sprintf("/v1/users/%d/roles?%s", userID, params.Encode())
			delResp := client.DELETE(t, url)
			common.AssertStatusCode(t, delResp, http.StatusOK)
		})
		t.Run("NegativeScenarios", func(t *testing.T) {
			invalidID := int64(999999)

			t.Run("GetNonExistentUser", func(t *testing.T) {
				url := fmt.Sprintf("/v1/users/%d", invalidID)
				resp := client.GET(t, url)
				common.AssertStatusCode(t, resp, http.StatusNotFound)
			})

			t.Run("UpdateNonExistentUser", func(t *testing.T) {
				url := fmt.Sprintf("/v1/users/%d", invalidID)
				updateReq := user.UserUpdateV1DTO{
					CategoryID: &invalidID,
				}
				resp := client.PATCH(t, url, updateReq)
				common.AssertStatusCode(t, resp, http.StatusInternalServerError)
			})

			t.Run("DeleteNonExistentUser", func(t *testing.T) {
				url := fmt.Sprintf("/v1/users/%d", invalidID)
				resp := client.DELETE(t, url)
				common.AssertStatusCode(t, resp, http.StatusNotFound)
			})

			t.Run("CreateUserWithInvalidEmail", func(t *testing.T) {
				createReq := user.UserCreateV1DTO{
					Email:    "not-an-email",
					FullName: "Invalid Email",
					Photo:    "",
				}
				resp := client.POST(t, "/v1/users", createReq)
				common.AssertStatusCode(t, resp, http.StatusInternalServerError)
			})

			t.Run("CreateUserWithEmptyFullName", func(t *testing.T) {
				createReq := user.UserCreateV1DTO{
					Email:    "<EMAIL>",
					FullName: "",
					Photo:    "",
				}
				resp := client.POST(t, "/v1/users", createReq)
				common.AssertStatusCode(t, resp, http.StatusInternalServerError)
			})
		})
	})
}
