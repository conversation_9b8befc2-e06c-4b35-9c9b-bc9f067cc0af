package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestAdminProposals_Get_Success тест проверяет успешное получение списка предложений через GET /v1/admin/proposals
func TestAdminProposals_Get_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)
	productID := 1

	// Создаем тестовое предложение для последующего тестирования
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", productID), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	// Act - выполняем GET-запрос для получения всех предложений
	patchResp := client.GET(t, "/v1/admin/proposals")
	common.AssertStatusCode(t, patchResp, http.StatusOK)

	// Парсим ответ в DTO
	var patchResult adminproposal.AdminProposalCollectionV1DTO
	common.ReadJSONResponse(t, patchResp, &patchResult)

	// Assert - проверяем, что список не пустой и содержит ожидаемые данные
	assert.NotEmpty(t, patchResult.Items, "Список предложений не должен быть пустым")

	if len(patchResult.Items) > 0 {
		// Find the created proposal in the results (order doesn't matter)
		var foundProposal *adminproposal.AdminProposalV1DTO
		for i := range patchResult.Items {
			if patchResult.Items[i].ProductID == int64(productID) {
				foundProposal = &patchResult.Items[i]
				break
			}
		}

		assert.NotNil(t, foundProposal, "Созданное предложение должно быть найдено в результатах")
		if foundProposal != nil {
			assert.Equal(t, int64(productID), foundProposal.ProductID, "ProductID должен соответствовать созданному")
		}
	}
}

// TestAdminProposals_GetByID_Success тест проверяет успешное получение конкретного предложения по ID через GET /v1/admin/proposals/{id}
func TestAdminProposals_GetByID_Success(t *testing.T) {
	// Arrange - инициализация тестовой базы и сервера
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем тестовое предложение и получаем его ID
	resp := client.POST(t, fmt.Sprintf("/v1/products/%d/proposals", 1), СreatedDataForProposalTest())
	common.AssertStatusCode(t, resp, http.StatusOK)

	var postResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, resp, &postResult)

	// Act - выполняем GET-запрос для получения предложения по ID
	getResp := client.GET(t, fmt.Sprintf("/v1/admin/proposals/%d", postResult.ID))
	common.AssertStatusCode(t, getResp, http.StatusOK)

	// Парсим ответ в DTO
	var getResult proposal.ProposalV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Assert - проверяем соответствие полей
	assert.Equal(t, getResult.ID, postResult.ID, "ID должен совпадать")
	assert.Equal(t, getResult.Price, postResult.Price, "Price должен совпадать")
	assert.Equal(t, getResult.ProductID, postResult.ProductID, "ProductID должен совпадать")
}

// createdDataForTest возвращает тестовые данные с вложенной структурой Data
// Структура включает:
// - catalog: массив с moneyTotal, resourcesTotal, sections и т.д.
// - staticSections: статические настройки с выпадающим списком выбора стенда
// - variables: глобальные переменные для расчетов
func СreatedDataForProposalTest() proposal.ProposalCreateV1DTO {
	proposalType := "active"
	proposalStatus := "proposalStatus"
	return proposal.ProposalCreateV1DTO{
		Price:  150.0,
		Type:   &proposalType,
		Status: &proposalStatus,
		Data: map[string]interface{}{
			"catalog": []interface{}{
				map[string]interface{}{
					"moneyTotal": map[string]interface{}{
						"CPU":        2655.28,
						"RAM":        1327.68,
						"S3hot":      760,
						"SSDCSI":     56.25,
						"SSDlocal":   1.25,
						"SSDnetwork": 3.8,
					},
					"resourcesTotal": map[string]interface{}{
						"CPU":        4,
						"RAM":        16,
						"S3hot":      1000,
						"SSDCSI":     0,
						"SSDlocal":   5,
						"SSDnetwork": 0,
					},
					"sections": []interface{}{
						map[string]interface{}{
							"collapse":          false,
							"editedSectionName": "",
							"fixed":             true,
							"groups": []interface{}{
								map[string]interface{}{
									"collapse":  false,
									"groupName": "",
									"rows": map[string]interface{}{
										"row2": []interface{}{
											map[string]interface{}{
												"disabled": false,
												"help":     "При выборе данной опции...",
												"isName":   false,
												"label":    "Доступ к DevSecOps конвейеру",
												"marked":   true,
												"required": false,
												"sum":      0,
												"type":     "checkbox",
												"value":    true,
											},
											// ... другие элементы формы
										},
									},
								},
							},
							"mustRename":  false,
							"notCopyable": false,
							"sectionName": "Дополнительные настройки",
							"variables": map[string]interface{}{
								"S3": 0,
							},
						},
						// ... другие секции
					},
					"standGroup": []interface{}{
						map[string]interface{}{
							"name": "Итого по контуру разработки (DH1.C)",
							"standsName": []interface{}{
								"Контур разработки (DH1.C)",
							},
						},
					},
					"standIndex": 0,
					"standName":  "Внутренний предпродуктив (DH2.C2) 1",
					"staticVariables": map[string]interface{}{
						"globalCPU":        663.82,
						"globalEnv":        1,
						"globalRAM":        82.98,
						"globalS3hot":      0.76,
						"globalSSDCSI":     11.25,
						"globalSSDlocal":   0.25,
						"globalSSDnetwork": 0.76,
					},
					"sum": 4804.26,
					"variables": map[string]interface{}{
						"globalCPU":        663.82,
						"globalEnv":        1,
						"globalRAM":        82.98,
						"globalS3hot":      0.76,
						"globalSSDCSI":     11.25,
						"globalSSDlocal":   0.25,
						"globalSSDnetwork": 0.76,
					},
				},
				// ... другие элементы catalog
			},
			"staticSections": []interface{}{
				map[string]interface{}{
					"collapse":          false,
					"editedSectionName": "",
					"fixed":             true,
					"groups": []interface{}{
						map[string]interface{}{
							"collapse":  false,
							"groupName": "",
							"rows": map[string]interface{}{
								"row3": []interface{}{
									map[string]interface{}{
										"disabled": false,
										"isName":   true,
										"label":    "Выберите стенд",
										"marked":   false,
										"options": []interface{}{
											"Контур разработки (DH1.C)",
											"Контур тестирования (DH2.C1)",
											// ... другие опции
										},
										"required": false,
										"sum":      0,
										"type":     "select",
										"value":    "Внутренний продуктив (DH2.C2)",
										"varList": []interface{}{
											map[string]interface{}{
												"Контур разработки (DH1.C)": map[string]interface{}{
													"globalCPU":        516.94,
													"globalEnv":        0,
													"globalRAM":        80.17,
													"globalS3hot":      9.33,
													"globalSSDCSI":     3.11,
													"globalSSDlocal":   3.11,
													"globalSSDnetwork": 3.11,
												},
											},
											// ... другие элементы varList
										},
									},
								},
							},
						},
					},
					"isName":      true,
					"mustRename":  false,
					"notCopyable": false,
					"sectionName": "Общие настройки",
					"variables": map[string]interface{}{
						"globalCPU":        663.82,
						"globalEnv":        1,
						"globalRAM":        82.98,
						"globalS3hot":      0.76,
						"globalSSDCSI":     11.25,
						"globalSSDlocal":   0.25,
						"globalSSDnetwork": 0.76,
					},
				},
			},
			"variables": map[string]interface{}{
				"globalCPU":        663.82,
				"globalEnv":        1,
				"globalRAM":        82.98,
				"globalS3hot":      0.76,
				"globalSSDCSI":     11.25,
				"globalSSDlocal":   0.25,
				"globalSSDnetwork": 0.76,
			},
		},
	}
}
