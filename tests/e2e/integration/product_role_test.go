package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestProductRoleIntegration_FullWorkflow тестирует полный workflow:
// 1. Создание продукта
// 2. Создание роли для продукта
// 3. Получение продукта как админ и проверка наличия роли
func TestProductRoleIntegration_FullWorkflow(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Step 1: Создаем продукт
	t.Run("CreateProduct", func(t *testing.T) {
		createProductRequest := product.ProductCreateV1DTO{
			IID:         "T001",     // Ограничение в 4 символа
			TechName:    "testprod", // Ограничение в 9 символов
			Name:        "Test Product for Integration",
			OwnerEmails: []string{"<EMAIL>"}, // Используем пользователя из фикстур
		}

		resp := client.POST(t, "/v1/products", createProductRequest)
		common.AssertStatusCode(t, resp, http.StatusOK)

		var createdProduct product.ProductV1DTO
		common.ReadJSONResponse(t, resp, &createdProduct)

		// Проверяем, что продукт создался корректно
		assert.Equal(t, "T001", createdProduct.IID)
		assert.Equal(t, "testprod", createdProduct.TechName)
		assert.Equal(t, "Test Product for Integration", createdProduct.Name)
		assert.Greater(t, createdProduct.ID, int64(0))

		// Сохраняем ID продукта для следующих шагов
		productID := createdProduct.ID

		// Step 2: Создаем роль для продукта
		t.Run("CreateProductRole", func(t *testing.T) {
			createRoleRequest := productrole.RoleCreateV1DTO{
				Type: constants.CustomType,
				Name: "Test Product Role",
				Permissions: []productrole.PermissionV1DTO{
					{
						Name:  "product",
						Label: "Product View",
						Methods: []productrole.MethodV1DTO{
							{Name: "view"},
						},
					},
					{
						Name:  "product",
						Label: "Product Create",
						Methods: []productrole.MethodV1DTO{
							{Name: "create"},
						},
					},
				},
			}

			roleResp := client.POST(t, fmt.Sprintf("/v1/products/%d/roles", productID), createRoleRequest)
			common.AssertStatusCode(t, roleResp, http.StatusOK)

			var createdRole productrole.RoleFullV1DTO
			common.ReadJSONResponse(t, roleResp, &createdRole)

			// Проверяем, что роль создалась корректно
			assert.Equal(t, "Test Product Role", createdRole.Name)
			assert.Equal(t, constants.CustomType, createdRole.Type)
			assert.Greater(t, createdRole.ID, int64(0))

			// Проверяем разрешения
			// Система объединяет разрешения с одинаковым именем в одно разрешение с несколькими методами
			assert.Len(t, createdRole.Permissions, 1)

			productPerm := createdRole.Permissions[0]
			assert.Equal(t, "product", productPerm.Name)
			assert.Len(t, productPerm.Methods, 2)

			// Проверяем, что есть оба метода
			methodNames := make([]string, len(productPerm.Methods))
			for i, method := range productPerm.Methods {
				methodNames[i] = method.Name
			}
			assert.Contains(t, methodNames, "view")
			assert.Contains(t, methodNames, "create")

			// Step 3: Получаем продукт как админ и проверяем наличие роли
			t.Run("GetProductAsAdminWithRole", func(t *testing.T) {
				adminResp := client.GET(t, fmt.Sprintf("/v1/admin/products/%d", productID))
				common.AssertStatusCode(t, adminResp, http.StatusOK)

				var productWithDetails adminproduct.ProductWithDetailsV1DTO
				common.ReadJSONResponse(t, adminResp, &productWithDetails)

				// Проверяем основные данные продукта
				assert.Equal(t, productID, productWithDetails.ID)
				assert.Equal(t, "T001", productWithDetails.IID)
				assert.Equal(t, "testprod", productWithDetails.TechName)
				assert.Equal(t, "Test Product for Integration", productWithDetails.Name)

				// Проверяем, что роль присутствует в продукте
				require.NotEmpty(t, productWithDetails.Roles, "Product should have roles")

				// Ищем нашу созданную роль
				var foundRole *adminproduct.RoleWithCountsV1DTO
				for _, role := range productWithDetails.Roles {
					if role.Name == "Test Product Role" {
						foundRole = &role
						break
					}
				}

				require.NotNil(t, foundRole, "Created role should be present in product details")
				assert.Equal(t, "Test Product Role", foundRole.Name)
				assert.Equal(t, constants.CustomType, foundRole.Type)
				assert.Equal(t, createdRole.ID, foundRole.ID)

				// Дополнительная проверка: получаем полную информацию о роли
				t.Run("VerifyRoleDetails", func(t *testing.T) {
					roleDetailResp := client.GET(t, fmt.Sprintf("/v1/products/%d/roles/%d", productID, createdRole.ID))
					common.AssertStatusCode(t, roleDetailResp, http.StatusOK)

					var roleDetails productrole.RoleFullV1DTO
					common.ReadJSONResponse(t, roleDetailResp, &roleDetails)

					// Проверяем, что детали роли соответствуют созданной роли
					assert.Equal(t, createdRole.ID, roleDetails.ID)
					assert.Equal(t, "Test Product Role", roleDetails.Name)
					assert.Equal(t, constants.CustomType, roleDetails.Type)
					assert.Len(t, roleDetails.Permissions, 1)

					// Проверяем конкретные разрешения
					productPerm := roleDetails.Permissions[0]
					assert.Equal(t, "product", productPerm.Name)
					assert.Len(t, productPerm.Methods, 2)

					// Проверяем методы
					methodNames := make([]string, len(productPerm.Methods))
					for i, method := range productPerm.Methods {
						methodNames[i] = method.Name
					}
					assert.Contains(t, methodNames, "view")
					assert.Contains(t, methodNames, "create")
				})
			})
		})
	})
}

// TestProductRoleIntegration_ErrorCases тестирует обработку ошибок
func TestProductRoleIntegration_ErrorCases(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("CreateRoleForNonExistentProduct", func(t *testing.T) {
		nonExistentProductID := int64(99999)

		createRoleRequest := productrole.RoleCreateV1DTO{
			Type: constants.CustomType,
			Name: "Test Role for Non-existent Product",
			Permissions: []productrole.PermissionV1DTO{
				{
					Name:  "test.permission",
					Label: "Test Permission",
					Methods: []productrole.MethodV1DTO{
						{Name: "GET"},
					},
				},
			},
		}

		resp := client.POST(t, fmt.Sprintf("/v1/products/%d/roles", nonExistentProductID), createRoleRequest)
		// Ожидаем ошибку (404 или 500 в зависимости от реализации)
		assert.True(t, resp.StatusCode >= 400, "Should return error for non-existent product")
	})

	t.Run("GetNonExistentProductAsAdmin", func(t *testing.T) {
		nonExistentProductID := int64(99999)

		resp := client.GET(t, fmt.Sprintf("/v1/admin/products/%d", nonExistentProductID))
		common.AssertStatusCode(t, resp, http.StatusNotFound)
	})

	t.Run("CreateRoleWithInvalidData", func(t *testing.T) {
		// Сначала создаем продукт
		createProductRequest := product.ProductCreateV1DTO{
			IID:         "E001",    // Ограничение в 4 символа
			TechName:    "testerr", // Ограничение в 9 символов
			Name:        "Test Product for Error Cases",
			OwnerEmails: []string{"<EMAIL>"},
		}

		productResp := client.POST(t, "/v1/products", createProductRequest)
		common.AssertStatusCode(t, productResp, http.StatusOK)

		var createdProduct product.ProductV1DTO
		common.ReadJSONResponse(t, productResp, &createdProduct)

		// Пытаемся создать роль с пустым именем разрешения
		invalidRoleRequest := productrole.RoleCreateV1DTO{
			Type: constants.CustomType,
			Name: "Invalid Role",
			Permissions: []productrole.PermissionV1DTO{
				{
					Name:  "", // Пустое имя разрешения
					Label: "Invalid Permission",
					Methods: []productrole.MethodV1DTO{
						{Name: "view"},
					},
				},
			},
		}

		resp := client.POST(t, fmt.Sprintf("/v1/products/%d/roles", createdProduct.ID), invalidRoleRequest)
		common.AssertStatusCode(t, resp, http.StatusBadRequest)
	})
}
