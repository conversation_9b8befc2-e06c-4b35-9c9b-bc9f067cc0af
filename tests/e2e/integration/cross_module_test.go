package integration

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestExample_SystemGroupsAndHealthCheck демонстрирует тестирование разных эндпоинтов
func TestExample_SystemGroupsAndHealthCheck(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act & Assert - тестируем разные эндпоинты

	// 1. Проверяем health endpoint
	healthResp := client.GET(t, "/health")
	common.AssertStatusCode(t, healthResp, http.StatusOK)

	// 2. Проверяем системные группы
	groupsResp := client.GET(t, "/v1/groups")
	common.AssertStatusCode(t, groupsResp, http.StatusOK)

	// 3. Проверяем конкретную группу
	groupResp := client.GET(t, "/v1/groups/1")
	common.AssertStatusCode(t, groupResp, http.StatusOK)

	// Все эндпоинты работают с одной БД
	healthBody := common.ReadResponseBody(t, healthResp)
	groupsBody := common.ReadResponseBody(t, groupsResp)

	assert.Contains(t, healthBody, "ok")
	assert.NotEmpty(t, groupsBody)
}

// TestCrossModule_MultipleEndpoints_Integration тестирует работу с разными эндпоинтами в одной БД
func TestCrossModule_MultipleEndpoints_Integration(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act & Assert - проверяем разные операции с группами

	// 1. Получаем список всех групп
	allGroupsResp := client.GET(t, "/v1/groups")
	common.AssertStatusCode(t, allGroupsResp, http.StatusOK)

	// 2. Получаем конкретную группу
	specificGroupResp := client.GET(t, "/v1/groups/1")
	common.AssertStatusCode(t, specificGroupResp, http.StatusOK)

	// 3. Проверяем health endpoint
	healthResp := client.GET(t, "/health")
	common.AssertStatusCode(t, healthResp, http.StatusOK)

	// Проверяем, что все запросы работают с одной БД
	allGroupsBody := common.ReadResponseBody(t, allGroupsResp)
	specificGroupBody := common.ReadResponseBody(t, specificGroupResp)
	healthBody := common.ReadResponseBody(t, healthResp)

	assert.NotEmpty(t, allGroupsBody)
	assert.NotEmpty(t, specificGroupBody)
	assert.Contains(t, healthBody, "ok")
}
