package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestProduct_CreateGet_Success тестирует успешное создание продукта, получение продукта по ID и проверку участников
func TestProduct_CreateGet_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста создания продукта
	idd := "9823" // Используем существующую системную группу из фикстур (System Admins)
	techName := "techname"
	productName := "productName"
	ownerEmails := []string{"<EMAIL>", "<EMAIL>"}
	createRequest := product.ProductCreateV1DTO{
		IID:         idd,
		TechName:    techName,
		Name:        productName,
		OwnerEmails: ownerEmails,
	}

	// Act - выполняем действия: создаем продукт через POST
	patchResp := client.POST(t, "/v1/products", createRequest)

	// Assert - проверяем результат создания
	common.AssertStatusCode(t, patchResp, http.StatusOK)

	var createResult product.ProductV1DTO
	common.ReadJSONResponse(t, patchResp, &createResult)

	// Проверяем, что созданный продукт содержит ожидаемые данные
	assert.Equal(t, idd, createResult.IID)
	assert.Equal(t, techName, createResult.TechName)
	assert.Equal(t, productName, createResult.Name)

	// Act - получаем созданный продукт через GET /v1/products/:productID
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d", createResult.ID))

	// Assert - проверяем результат получения продукта
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getResult product.ProductV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Проверяем, что полученные данные соответствуют созданным
	assert.Equal(t, getResult.ID, createResult.ID)
	assert.Equal(t, getResult.IID, createResult.IID)
	assert.Equal(t, getResult.TechName, createResult.TechName)
	assert.Equal(t, getResult.Name, createResult.Name)
	assert.Equal(t, getResult.Desc, createResult.Desc)
	assert.Equal(t, getResult.CreatorID, createResult.CreatorID)
	assert.Equal(t, getResult.CreatedAt, createResult.CreatedAt)
	assert.Equal(t, getResult.UpdatedAt, createResult.UpdatedAt)

	// Act - получаем участников продукта через GET /v1/products/:productID/participants
	getPatr := client.GET(t, fmt.Sprintf("/v1/products/%d/participants", createResult.ID))
	// Assert - проверяем результат получения участников
	common.AssertStatusCode(t, getPatr, http.StatusOK)
	var getPartResult participant.ParticipantCollectionV1DTO

	common.ReadJSONResponse(t, getPatr, &getPartResult)

	// Проверяем, что все полученные участники являются ожидаемыми владельцами
	ownersOk := true
	for _, owner := range getPartResult.Items {
		if (owner.Email == ownerEmails[0] || owner.Email == ownerEmails[1]) && owner.IsOwner == true {
			continue
		} else {
			ownersOk = false
			break
		}
	}
	assert.True(t, ownersOk, "Участники не совпадают с ожидаемыми email адресами")
}

// TestProduct_Create_Error тестирует ошибку при создании продукта с некорректными данными
func TestProduct_Create_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем пустые данные для теста (некорректный запрос)
	createRequest := product.ProductCreateV1DTO{}

	// Act - выполняем действия: пытаемся создать продукт с пустыми данными
	patchResp := client.POST(t, "/v1/products", createRequest)

	// Assert - проверяем, что получили ошибку 400 Bad Request
	common.AssertStatusCode(t, patchResp, http.StatusBadRequest)
}

// TestProduct_GetNonExists_Error тестирует ошибку при попытке получить несуществующий продукт
func TestProduct_GetNonExists_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - выполняем действия: пытаемся получить продукт с несуществующим ID
	patchResp := client.GET(t, fmt.Sprintf("/v1/products/%d", 99999))

	// Assert - проверяем, что получили ошибку 404 Not Found
	common.AssertStatusCode(t, patchResp, http.StatusNotFound)
}

// TestProduct_GetsAll_Success тестирует успешное получение списка всех продуктов
func TestProduct_GetsAll_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - выполняем действия: получаем список всех продуктов
	getAllResp := client.GET(t, "/v1/products")

	// Assert - проверяем результат получения списка
	common.AssertStatusCode(t, getAllResp, http.StatusOK)

	var getResult product.ProductCollectionV1DTO
	common.ReadJSONResponse(t, getAllResp, &getResult)
	// Примечание: Здесь можно добавить дополнительные проверки содержимого списка
}

// TestProduct_Get_InvalidID_Error тестирует ошибки при получении продукта с невалидным ID
func TestProduct_Get_InvalidID_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - выполняем действия: пытаемся получить продукт с различными невалидными ID
	resp1 := client.GET(t, "/v1/products/abc") // не число
	resp2 := client.GET(t, "/v1/products/-1")  // отрицательное число
	resp3 := client.GET(t, "/v1/products/0")   // ноль

	// Assert - проверяем, что получили соответствующие ошибки
	common.AssertStatusCode(t, resp1, http.StatusBadRequest) // Невалидный формат ID
	common.AssertStatusCode(t, resp2, http.StatusNotFound)   // Отрицательный ID не найден
	common.AssertStatusCode(t, resp3, http.StatusNotFound)   // ID 0 не найден
}

// TestProduct_CreateUpdateDelete_FullCycle тестирует полный цикл CRUD операций с продуктом
func TestProduct_CreateUpdateDelete_FullCycle(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста создания продукта
	idd := "9881" // Используем существующую системную группу из фикстур (System Admins)
	techName := "techname1"
	productName := "productName1"
	ownerEmails := []string{"<EMAIL>", "<EMAIL>"}
	createRequest := product.ProductCreateV1DTO{
		IID:         idd,
		TechName:    techName,
		Name:        productName,
		OwnerEmails: ownerEmails,
	}

	// Act - выполняем действия: создаем продукт через POST
	patchResp := client.POST(t, "/v1/products", createRequest)

	// Assert - проверяем результат создания
	common.AssertStatusCode(t, patchResp, http.StatusOK)
	var createResult product.ProductV1DTO
	common.ReadJSONResponse(t, patchResp, &createResult)

	// Проверяем, что созданный продукт содержит ожидаемые данные
	assert.Equal(t, idd, createResult.IID)
	assert.Equal(t, techName, createResult.TechName)
	assert.Equal(t, productName, createResult.Name)

	// Act - получаем созданный продукт через GET /v1/products/:productID
	getResp := client.GET(t, fmt.Sprintf("/v1/products/%d", createResult.ID))

	// Assert - проверяем результат получения продукта
	common.AssertStatusCode(t, getResp, http.StatusOK)
	var getResult product.ProductV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Проверяем, что полученные данные соответствуют созданным
	assert.Equal(t, getResult.ID, createResult.ID)
	assert.Equal(t, getResult.IID, createResult.IID)
	assert.Equal(t, getResult.TechName, createResult.TechName)
	assert.Equal(t, getResult.Name, createResult.Name)
	assert.Equal(t, getResult.Desc, createResult.Desc)
	assert.Equal(t, getResult.CreatorID, createResult.CreatorID)
	assert.Equal(t, getResult.CreatedAt, createResult.CreatedAt)
	assert.Equal(t, getResult.UpdatedAt, createResult.UpdatedAt)

	// Подготавливаем данные для обновления продукта
	updateProductIID := "7378"
	updateProductName := "newproductName"
	updateProductTechName := "newTech"
	updateProductDesc := "newDesc"
	updateProduct := product.ProductUpdateV1DTO{
		IID:      &updateProductIID,
		Name:     &updateProductName,
		TechName: &updateProductTechName,
		Desc:     &updateProductDesc,
	}

	// Act - обновляем продукт через PATCH /v1/products/:productID
	getResp = client.PATCH(t, fmt.Sprintf("/v1/products/%d", createResult.ID), updateProduct)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, getResp, http.StatusOK)

	// Act - получаем обновленный продукт через GET /v1/products/:productID
	getResp = client.GET(t, fmt.Sprintf("/v1/products/%d", createResult.ID))
	common.AssertStatusCode(t, getResp, http.StatusOK)
	common.ReadJSONResponse(t, getResp, &getResult)

	// Assert - проверяем, что данные продукта были обновлены
	assert.Equal(t, getResult.IID, updateProductIID)
	assert.Equal(t, getResult.Name, updateProductName)
	assert.Equal(t, getResult.TechName, updateProductTechName)
	assert.Equal(t, getResult.Desc, updateProductDesc)

	// Act - удаляем продукт через DELETE /v1/products/:productID
	deleteResp := client.DELETE(t, fmt.Sprintf("/v1/products/%d", createResult.ID))

	// Assert - проверяем, что удаление прошло успешно
	common.AssertStatusCode(t, deleteResp, http.StatusOK)

	// Act - пытаемся получить удаленный продукт
	getResp = client.GET(t, fmt.Sprintf("/v1/products/%d", 99999))

	// Assert - проверяем, что удаленный продукт не найден
	common.AssertStatusCode(t, getResp, http.StatusNotFound)
}

// TestProduct_Update_NonExistingProduct_Error тестирует ошибку при обновлении несуществующего продукта
func TestProduct_Update_NonExistingProduct_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для обновления
	updateProductName := "newproductName"
	updateRequest := product.ProductUpdateV1DTO{
		Name: &updateProductName,
	}

	// Act - выполняем действия: пытаемся обновить несуществующий продукт
	resp := client.PATCH(t, "/v1/products/999999", updateRequest)

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	// Примечание: Возможно, здесь ожидается 404 Not Found, в зависимости от реализации API
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}

// TestProduct_UpdateIID_Error тестирует ошибку при обновлении IID несуществующего продукта
func TestProduct_UpdateIID_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для обновления IID
	updateProductIID := "newproductName"
	updateRequest := product.ProductUpdateV1DTO{
		IID: &updateProductIID,
	}

	// Act - выполняем действия: пытаемся обновить IID несуществующего продукта
	resp := client.PATCH(t, "/v1/products/999999", updateRequest)

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	// Примечание: Возможно, здесь ожидается 404 Not Found, в зависимости от реализации API
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}

// TestProduct_UpdateTechName_Error тестирует ошибку при обновлении TechName несуществующего продукта
func TestProduct_UpdateTechName_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для обновления TechName
	updateProductTechName := "newproductName"
	updateRequest := product.ProductUpdateV1DTO{
		TechName: &updateProductTechName,
	}

	// Act - выполняем действия: пытаемся обновить TechName несуществующего продукта
	resp := client.PATCH(t, "/v1/products/999999", updateRequest)

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	// Примечание: Возможно, здесь ожидается 404 Not Found, в зависимости от реализации API
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}

// TestProduct_DeleteNonExists_Error тестирует ошибку при удалении несуществующего продукта
func TestProduct_DeleteNonExists_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - выполняем действия: пытаемся удалить несуществующий продукт
	resp := client.DELETE(t, "/v1/products/999999")

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	// Примечание: Возможно, здесь ожидается 404 Not Found, в зависимости от реализации API
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}
