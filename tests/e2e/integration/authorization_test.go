package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestAuthorization_AccountAdmin(t *testing.T) {
	db := setup.InitAuthTestDatabase(t)
	err := common.UpdateTestUsersWithRealIDs(db)
	assert.NoError(t, err)

	server := setup.InitTestServerWithMiddleware(t, db, common.CreateAuthMiddleware(common.TestUsers.AccountAdmin))
	client := common.NewTestClient(server.BaseURL)

	t.Run("PositiveScenarios", func(t *testing.T) {
		t.Run("CanAccessAllEndpoints", func(t *testing.T) {
			resp := client.GET(t, "/v1/products")
			common.AssertStatusCode(t, resp, http.StatusOK)
		})

		t.Run("CanCreateProduct", func(t *testing.T) {
			createReq := product.ProductCreateV1DTO{
				IID:         "AD01",
				TechName:    "ADMINPROD",
				Name:        "Admin Product",
				OwnerEmails: []string{"<EMAIL>"},
			}
			resp := client.POST(t, "/v1/products", createReq)
			common.AssertStatusCode(t, resp, http.StatusOK)
		})
	})

	t.Run("NegativeScenarios", func(t *testing.T) {
		t.Run("CannotAccessNonExistentResource", func(t *testing.T) {
			resp := client.GET(t, "/v1/products/99999")
			common.AssertStatusCode(t, resp, http.StatusNotFound)
		})
	})
}

func TestAuthorization_AccountUser(t *testing.T) {
	db := setup.InitAuthTestDatabase(t)
	err := common.UpdateTestUsersWithRealIDs(db)
	assert.NoError(t, err)

	server := setup.InitTestServerWithMiddleware(t, db, common.CreateAuthMiddleware(common.TestUsers.AccountUser))
	client := common.NewTestClient(server.BaseURL)

	t.Run("PositiveScenarios", func(t *testing.T) {
		t.Run("CanCreateProduct", func(t *testing.T) {
			createReq := product.ProductCreateV1DTO{
				IID:         "US01",
				TechName:    "USERPROD",
				Name:        "User Product",
				OwnerEmails: []string{"<EMAIL>"},
			}
			resp := client.POST(t, "/v1/products", createReq)
			common.AssertStatusCode(t, resp, http.StatusOK)
		})

		t.Run("CanViewProfile", func(t *testing.T) {
			userID, err := db.GetUserIDByEmail("<EMAIL>")
			assert.NoError(t, err)

			url := fmt.Sprintf("/v1/users/%d", userID)
			resp := client.GET(t, url)
			common.AssertStatusCode(t, resp, http.StatusOK)
		})
	})

	t.Run("NegativeScenarios", func(t *testing.T) {
		t.Run("CannotAccessAdminEndpoints", func(t *testing.T) {
			resp := client.GET(t, "/v1/admin/users")
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})

		t.Run("CannotManageUsers", func(t *testing.T) {
			resp := client.GET(t, "/v1/users")
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})
	})
}

func TestAuthorization_ProductOwner(t *testing.T) {
	db := setup.InitAuthTestDatabase(t)
	err := common.UpdateTestUsersWithRealIDs(db)
	assert.NoError(t, err)

	server := setup.InitTestServerWithMiddleware(t, db, common.CreateAuthMiddleware(common.TestUsers.ProductOwner))
	client := common.NewTestClient(server.BaseURL)

	t.Run("PositiveScenarios", func(t *testing.T) {
		t.Run("CanManageProducts", func(t *testing.T) {
			resp := client.GET(t, "/v1/products")
			common.AssertStatusCode(t, resp, http.StatusOK)

			createReq := product.ProductCreateV1DTO{
				IID:         "OW01",
				TechName:    "OWNERPROD",
				Name:        "Owner Product",
				OwnerEmails: []string{"<EMAIL>"},
			}
			createResp := client.POST(t, "/v1/products", createReq)
			common.AssertStatusCode(t, createResp, http.StatusOK)
		})

		t.Run("CanManageProductProposals", func(t *testing.T) {
			productID, err := db.GetProductIDByTechName("AUTHPRD1")
			assert.NoError(t, err)

			createReq := proposal.ProposalCreateV1DTO{
				Price: 100.0,
				Data:  map[string]interface{}{"test": "data"},
			}
			url := fmt.Sprintf("/v1/products/%d/proposals", productID)
			resp := client.POST(t, url, createReq)
			common.AssertStatusCode(t, resp, http.StatusOK)
		})
	})

	t.Run("NegativeScenarios", func(t *testing.T) {
		t.Run("CannotAccessAdminEndpoints", func(t *testing.T) {
			resp := client.GET(t, "/v1/admin/users")
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})

		t.Run("CannotCreateSystemRoles", func(t *testing.T) {
			createReq := map[string]interface{}{
				"name": "test_role",
				"permissions": []map[string]string{
					{"name": "test", "method": "view"},
				},
			}
			resp := client.POST(t, "/v1/roles", createReq)
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})
	})
}

func TestAuthorization_ProductParticipant(t *testing.T) {
	db := setup.InitAuthTestDatabase(t)
	err := common.UpdateTestUsersWithRealIDs(db)
	assert.NoError(t, err)

	server := setup.InitTestServerWithMiddleware(t, db, common.CreateAuthMiddleware(common.TestUsers.ProductParticipant))
	client := common.NewTestClient(server.BaseURL)

	t.Run("PositiveScenarios", func(t *testing.T) {
		t.Run("CanViewProducts", func(t *testing.T) {
			resp := client.GET(t, "/v1/products")
			common.AssertStatusCode(t, resp, http.StatusOK)
		})

		t.Run("CanCreateProposals", func(t *testing.T) {
			productID, err := db.GetProductIDByTechName("AUTHPRD1")
			assert.NoError(t, err)

			createReq := proposal.ProposalCreateV1DTO{
				Price: 200.0,
				Data:  map[string]interface{}{"participant": "test"},
			}
			url := fmt.Sprintf("/v1/products/%d/proposals", productID)
			resp := client.POST(t, url, createReq)
			common.AssertStatusCode(t, resp, http.StatusOK)
		})
	})

	t.Run("NegativeScenarios", func(t *testing.T) {
		t.Run("CannotDeleteProducts", func(t *testing.T) {
			productID, err := db.GetProductIDByTechName("AUTHPRD2")
			assert.NoError(t, err)

			url := fmt.Sprintf("/v1/products/%d", productID)
			resp := client.DELETE(t, url)
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})

		t.Run("CannotAccessAdminEndpoints", func(t *testing.T) {
			resp := client.GET(t, "/v1/admin/users")
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})
	})
}

func TestAuthorization_UnauthorizedUser(t *testing.T) {
	db := setup.InitAuthTestDatabase(t)
	err := common.UpdateTestUsersWithRealIDs(db)
	assert.NoError(t, err)

	server := setup.InitTestServerWithMiddleware(t, db, common.CreateAuthMiddleware(common.TestUsers.Unauthorized))
	client := common.NewTestClient(server.BaseURL)

	t.Run("NegativeScenarios", func(t *testing.T) {
		t.Run("CannotAccessProtectedEndpoints", func(t *testing.T) {
			resp := client.GET(t, "/v1/admin/users")
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})

		t.Run("CannotAccessCategories", func(t *testing.T) {
			createReq := map[string]interface{}{
				"name": "Unauthorized Category",
			}
			resp := client.POST(t, "/v1/categories", createReq)
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})

		t.Run("CannotCreateProposals", func(t *testing.T) {
			createReq := proposal.ProposalCreateV1DTO{
				Price: 300.0,
				Data:  map[string]interface{}{"unauthorized": "test"},
			}
			resp := client.POST(t, "/v1/products/1/proposals", createReq)
			common.AssertStatusCode(t, resp, http.StatusForbidden)
		})
	})

	t.Run("CanAccessPublicEndpoints", func(t *testing.T) {
		// Эти endpoints доступны без авторизации согласно excluded_paths в authorization.yaml
		t.Run("CanAccessProducts", func(t *testing.T) {
			resp := client.GET(t, "/v1/products")
			common.AssertStatusCode(t, resp, http.StatusOK)
		})

		t.Run("CanAccessGroups", func(t *testing.T) {
			resp := client.GET(t, "/v1/groups")
			common.AssertStatusCode(t, resp, http.StatusOK)
		})

		t.Run("CanAccessRoles", func(t *testing.T) {
			resp := client.GET(t, "/v1/roles")
			common.AssertStatusCode(t, resp, http.StatusOK)
		})
	})
}
