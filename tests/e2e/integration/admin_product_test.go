package integration

import (
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestAdminProduct_CreateGet_Success тестирует успешное создание продукта через админский API, получение продукта по ID и проверку участников
func TestAdminProduct_CreateGet_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста создания продукта
	idd := "9923" // Используем существующую системную группу из фикстур (System Admins)
	techName := "tesfname"
	productName := "produdfgName"
	ownerEmails := []string{"<EMAIL>", "<EMAIL>"}
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         idd,
		TechName:    techName,
		Name:        productName,
		OwnerEmails: ownerEmails,
	}

	// Act - выполняем действия: создаем продукт через POST /v1/admin/products
	patchResp := client.POST(t, "/v1/admin/products", createRequest)

	// Assert - проверяем результат создания
	common.AssertStatusCode(t, patchResp, http.StatusOK)

	var createResult adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, patchResp, &createResult)

	// Проверяем, что созданный продукт содержит ожидаемые данные
	assert.Equal(t, idd, createResult.IID)
	assert.Equal(t, techName, createResult.TechName)
	assert.Equal(t, productName, createResult.Name)

	// Act - получаем созданный продукт через GET /v1/admin/products/:productID
	getResp := client.GET(t, fmt.Sprintf("/v1/admin/products/%d", createResult.ID))

	// Assert - проверяем результат получения продукта
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getResult adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Проверяем, что полученные данные соответствуют созданным
	assert.Equal(t, getResult.ID, createResult.ID)
	assert.Equal(t, getResult.IID, createResult.IID)
	assert.Equal(t, getResult.TechName, createResult.TechName)
	assert.Equal(t, getResult.Name, createResult.Name)
	assert.Equal(t, getResult.Desc, createResult.Desc)
	assert.Equal(t, getResult.CreatorID, createResult.CreatorID)
	assert.Equal(t, getResult.CreatedAt, createResult.CreatedAt)
	assert.Equal(t, getResult.UpdatedAt, createResult.UpdatedAt)

	// Act - получаем участников продукта через GET /v1/products/:productID/participants (обычный эндпоинт для участников)
	getPatr := client.GET(t, fmt.Sprintf("/v1/products/%d/participants", createResult.ID))
	// Assert - проверяем результат получения участников
	common.AssertStatusCode(t, getPatr, http.StatusOK)
	var getPartResult participant.ParticipantCollectionV1DTO

	common.ReadJSONResponse(t, getPatr, &getPartResult)

	// Проверяем, что все полученные участники являются ожидаемыми владельцами
	ownersOk := true
	for _, owner := range getPartResult.Items {
		if (owner.Email == ownerEmails[0] || owner.Email == ownerEmails[1]) && owner.IsOwner == true {
			continue
		} else {
			ownersOk = false
			break
		}
	}
	assert.True(t, ownersOk, "Участники не совпадают с ожидаемыми email адресами")
}

// TestAdminProduct_Create_Error тестирует ошибку при создании продукта с некорректными данными через админский API
func TestAdminProduct_Create_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем пустые данные для теста (некорректный запрос)
	createRequest := adminproduct.ProductCreateV1DTO{}

	// Act - выполняем действия: пытаемся создать продукт с пустыми данными через POST /v1/admin/products
	patchResp := client.POST(t, "/v1/admin/products", createRequest)

	// Assert - проверяем, что получили ошибку 400 Bad Request
	common.AssertStatusCode(t, patchResp, http.StatusBadRequest)

	patchResp = client.POST(t, "/v1/admin/products", nil)

	// Assert - проверяем, что получили ошибку 400 Bad Request
	common.AssertStatusCode(t, patchResp, http.StatusBadRequest)
}

// TestAdminProduct_GetNonExists_Error тестирует ошибку при попытке получить несуществующий продукт через админский API
func TestAdminProduct_GetNonExists_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - выполняем действия: пытаемся получить продукт с несуществующим ID через GET /v1/admin/products/:productID
	patchResp := client.GET(t, fmt.Sprintf("/v1/admin/products/%d", 99999))

	// Assert - проверяем, что получили ошибку 404 Not Found
	common.AssertStatusCode(t, patchResp, http.StatusNotFound)
}

// TestAdminProduct_GetsAll_Success тестирует успешное получение списка всех продуктов через админский API
func TestAdminProduct_GetsAll_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста создания продукта
	idd := "1923" // Используем существующую системную группу из фикстур (System Admins)
	techName := "uesfname"
	productName := "orodudfgName"
	ownerEmails := []string{"<EMAIL>", "<EMAIL>"}
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         idd,
		TechName:    techName,
		Name:        productName,
		OwnerEmails: ownerEmails,
	}

	// Act - выполняем действия: создаем продукт через POST /v1/admin/products
	patchResp := client.POST(t, "/v1/admin/products", createRequest)

	// Assert - проверяем результат создания
	common.AssertStatusCode(t, patchResp, http.StatusOK)

	var createResult adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, patchResp, &createResult)

	// Act - выполняем действия: получаем список всех продуктов через GET /v1/admin/products
	getAllResp := client.GET(t, "/v1/admin/products")

	// Assert - проверяем результат получения списка
	common.AssertStatusCode(t, getAllResp, http.StatusOK)

	var getResult adminproduct.AdminProductCollectionV1DTO
	common.ReadJSONResponse(t, getAllResp, &getResult)

	assert.NotEmpty(t, getResult.Items)
	if len(getResult.Items) > 0 {
		var foundProduct *adminproduct.AdminProductV1DTO
		for i := range getResult.Items {
			if getResult.Items[i].ID == int64(createResult.ID) {
				foundProduct = &getResult.Items[i]
				break
			}
		}
		assert.NotNil(t, foundProduct, "Созданный продукт должен быть найден в результатах")
		if foundProduct != nil {
			assert.Equal(t, createRequest.IID, foundProduct.IID, "IID должен соответствовать созданному")
		}
	}
}

// TestAdminProduct_Get_InvalidID_Error тестирует ошибки при получении продукта с невалидным ID через админский API
func TestAdminProduct_Get_InvalidID_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Act - выполняем действия: пытаемся получить продукт с различными невалидными ID через GET /v1/admin/products/:productID
	resp1 := client.GET(t, "/v1/admin/products/abc") // не число
	resp2 := client.GET(t, "/v1/admin/products/-1")  // отрицательное число
	resp3 := client.GET(t, "/v1/admin/products/0")   // ноль

	// Assert - проверяем, что получили соответствующие ошибки
	common.AssertStatusCode(t, resp1, http.StatusBadRequest) // Невалидный формат ID
	common.AssertStatusCode(t, resp2, http.StatusNotFound)   // Отрицательный ID не найден
	common.AssertStatusCode(t, resp3, http.StatusNotFound)   // ID 0 не найден
}

// TestAdminProduct_GetsAllValidate_Success тестирует комплексную функциональность получения продуктов с фильтрами через админский API
func TestAdminProduct_GetsAllValidate_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем несколько продуктов для тестирования
	testProducts := []struct {
		id       string
		techName string
		name     string
	}{
		{"A001", "a_tech", "a_product"},
		{"B001", "b_tech", "b_product"},
		{"C001", "c_tech", "c_product"},
		{"D001", "d_tech", "d_product"},
	}

	var createdProducts []adminproduct.ProductWithDetailsV1DTO

	// Создаем все продукты через админский API
	for _, tp := range testProducts {
		createRequest := adminproduct.ProductCreateV1DTO{
			IID:         tp.id,
			TechName:    tp.techName,
			Name:        tp.name,
			OwnerEmails: []string{"<EMAIL>", "<EMAIL>"},
		}

		resp := client.POST(t, "/v1/admin/products", createRequest)
		common.AssertStatusCode(t, resp, http.StatusOK)

		var createdProduct adminproduct.ProductWithDetailsV1DTO
		common.ReadJSONResponse(t, resp, &createdProduct)
		createdProducts = append(createdProducts, createdProduct)
	}

	// Тест 1: Получение всех продуктов без фильтров
	t.Run("GetAllProducts", func(t *testing.T) {
		getAllResp := client.GET(t, "/v1/admin/products")
		common.AssertStatusCode(t, getAllResp, http.StatusOK)

		var getResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, getAllResp, &getResult)

		assert.NotEmpty(t, getResult.Items)
		assert.GreaterOrEqual(t, len(getResult.Items), len(createdProducts))

		// Проверяем, что все созданные продукты присутствуют
		for _, createdProduct := range createdProducts {
			found := false
			for _, item := range getResult.Items {
				if item.ID == createdProduct.ID {
					found = true
					// Проверяем поля
					assert.Equal(t, createdProduct.IID, item.IID)
					assert.Equal(t, createdProduct.TechName, item.TechName)
					assert.Equal(t, createdProduct.Name, item.Name)
					break
				}
			}
			assert.True(t, found, "Созданный продукт с ID %d должен быть найден", createdProduct.ID)
		}
	})

	// Тест 2: Поиск по имени продукта
	t.Run("SearchProductsByName", func(t *testing.T) {
		searchResp := client.GET(t, "/v1/admin/products?search=product")
		common.AssertStatusCode(t, searchResp, http.StatusOK)

		var searchResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, searchResp, &searchResult)

		assert.NotEmpty(t, searchResult.Items)

		// Проверяем, что в результатах есть продукты с "product" в имени
		foundSearchableProduct := false
		for _, item := range searchResult.Items {
			assert.Contains(t, strings.ToLower(item.Name), "product")
			if strings.Contains(item.Name, "product") {
				foundSearchableProduct = true
			}
		}
		assert.True(t, foundSearchableProduct, "Должен быть найден продукт с именем 'product'")
	})

	// Тест 3: Поиск по техническому имени
	t.Run("SearchProductsByTechName", func(t *testing.T) {
		searchResp := client.GET(t, "/v1/admin/products?search=tech")
		common.AssertStatusCode(t, searchResp, http.StatusOK)

		var searchResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, searchResp, &searchResult)

		assert.NotEmpty(t, searchResult.Items)

		// Проверяем, что в результатах есть продукты с "tech" в техническом имени
		for _, item := range searchResult.Items {
			assert.Contains(t, strings.ToLower(item.TechName), "tech")
		}
	})

	// Тест 4: Пагинация
	t.Run("Pagination", func(t *testing.T) {
		// Получаем первые 2 продукта
		limitResp := client.GET(t, "/v1/admin/products?limit=2&offset=0")
		common.AssertStatusCode(t, limitResp, http.StatusOK)

		var limitResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, limitResp, &limitResult)

		assert.Len(t, limitResult.Items, 2)

		// Получаем следующие 2 продукта
		offsetResp := client.GET(t, "/v1/admin/products?limit=2&offset=2")
		common.AssertStatusCode(t, offsetResp, http.StatusOK)

		var offsetResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, offsetResp, &offsetResult)

		assert.Len(t, offsetResult.Items, 2)

		// Проверяем, что продукты не повторяются
		firstBatchIDs := make(map[int64]bool)
		for _, item := range limitResult.Items {
			firstBatchIDs[item.ID] = true
		}

		for _, item := range offsetResult.Items {
			assert.False(t, firstBatchIDs[item.ID], "Продукты не должны повторяться при пагинации")
		}
	})

	// Тест 5: Сортировка
	t.Run("Sorting", func(t *testing.T) {
		// Сортировка по техническому имени по возрастанию
		sortAscResp := client.GET(t, "/v1/admin/products?sort=techName&order=ascend")
		common.AssertStatusCode(t, sortAscResp, http.StatusOK)

		var sortAscResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, sortAscResp, &sortAscResult)

		// Проверяем сортировку только если есть созданные продукты в результатах
		createdProductNames := make(map[string]bool)
		for _, createdProduct := range createdProducts {
			createdProductNames[createdProduct.Name] = true
		}

		var createdProductsInResult []adminproduct.AdminProductV1DTO
		for _, item := range sortAscResult.Items {
			if createdProductNames[item.Name] {
				createdProductsInResult = append(createdProductsInResult, item)
			}
		}

		if len(createdProductsInResult) > 1 {
			for i := 1; i < len(createdProductsInResult); i++ {
				assert.GreaterOrEqual(t,
					strings.ToLower(createdProductsInResult[i].TechName),
					strings.ToLower(createdProductsInResult[i-1].TechName),
					"Созданные продукты должны быть отсортированы по техническому имени по возрастанию")
			}
		}

		// Сортировка по техническому имени по убыванию
		sortDescResp := client.GET(t, "/v1/admin/products?sort=techName&order=descend")
		common.AssertStatusCode(t, sortDescResp, http.StatusOK)

		var sortDescResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, sortDescResp, &sortDescResult)

		// Проверяем сортировку только для созданных продуктов
		createdProductsInResult = nil
		for _, item := range sortDescResult.Items {
			if createdProductNames[item.Name] {
				createdProductsInResult = append(createdProductsInResult, item)
			}
		}

		if len(createdProductsInResult) > 1 {
			for i := 1; i < len(createdProductsInResult); i++ {
				assert.LessOrEqual(t,
					strings.ToLower(createdProductsInResult[i].TechName),
					strings.ToLower(createdProductsInResult[i-1].TechName),
					"Созданные продукты должны быть отсортированы по техническому имени по убыванию")
			}
		}
	})

	// Тест 6: Комбинированный поиск с пагинацией и сортировкой
	t.Run("CombinedFilters", func(t *testing.T) {
		combinedResp := client.GET(t, "/v1/admin/products?search=product&sort=techName&order=asc&limit=10&offset=0")
		common.AssertStatusCode(t, combinedResp, http.StatusOK)

		var combinedResult adminproduct.AdminProductCollectionV1DTO
		common.ReadJSONResponse(t, combinedResp, &combinedResult)

		// Проверяем, что все результаты содержат "product" в имени
		for _, item := range combinedResult.Items {
			assert.Contains(t, strings.ToLower(item.Name), "product")
		}

		// Проверяем сортировку только для созданных продуктов
		createdProductNames := make(map[string]bool)
		for _, createdProduct := range createdProducts {
			createdProductNames[createdProduct.Name] = true
		}

		var createdProductsInResult []adminproduct.AdminProductV1DTO
		for _, item := range combinedResult.Items {
			if createdProductNames[item.Name] {
				createdProductsInResult = append(createdProductsInResult, item)
			}
		}

		if len(createdProductsInResult) > 1 {
			for i := 1; i < len(createdProductsInResult); i++ {
				assert.GreaterOrEqual(t,
					strings.ToLower(createdProductsInResult[i].TechName),
					strings.ToLower(createdProductsInResult[i-1].TechName),
					"Созданные продукты должны быть отсортированы по техническому имени по возрастанию")
			}
		}
	})
}

// TestAdminProduct_CreateUpdate_FullCycle тестирует полный цикл CRUD операций с продуктом через админский API
func TestAdminProduct_CreateUpdate_FullCycle(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста создания продукта
	idd := "9889" // Используем существующую системную группу из фикстур (System Admins)
	techName := "tech1"
	productName := "productName3"
	ownerEmails := []string{"<EMAIL>", "<EMAIL>"}
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         idd,
		TechName:    techName,
		Name:        productName,
		OwnerEmails: ownerEmails,
	}

	// Act - выполняем действия: создаем продукт через POST /v1/admin/products
	patchResp := client.POST(t, "/v1/admin/products", createRequest)

	// Assert - проверяем результат создания
	common.AssertStatusCode(t, patchResp, http.StatusOK)
	var createResult adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, patchResp, &createResult)

	// Проверяем, что созданный продукт содержит ожидаемые данные
	assert.Equal(t, idd, createResult.IID)
	assert.Equal(t, techName, createResult.TechName)
	assert.Equal(t, productName, createResult.Name)

	// Act - получаем созданный продукт через GET /v1/admin/products/:productID
	getResp := client.GET(t, fmt.Sprintf("/v1/admin/products/%d", createResult.ID))

	// Assert - проверяем результат получения продукта
	common.AssertStatusCode(t, getResp, http.StatusOK)
	var getResult adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Проверяем, что полученные данные соответствуют созданным
	assert.Equal(t, getResult.ID, createResult.ID)
	assert.Equal(t, getResult.IID, createResult.IID)
	assert.Equal(t, getResult.TechName, createResult.TechName)
	assert.Equal(t, getResult.Name, createResult.Name)
	assert.Equal(t, getResult.Desc, createResult.Desc)
	assert.Equal(t, getResult.CreatorID, createResult.CreatorID)
	assert.Equal(t, getResult.CreatedAt, createResult.CreatedAt)
	assert.Equal(t, getResult.UpdatedAt, createResult.UpdatedAt)

	// Подготавливаем данные для обновления продукта
	updateProductIID := "7338"
	updateProductName := "wwwproductName"
	updateProductTechName := "tech2"
	updateProductDesc := "neeDesc"
	updateProductStatus := adminproduct.AdminProductUpdateV1DTOStatus("active")
	updateProduct := adminproduct.AdminProductUpdateV1DTO{
		IID:      &updateProductIID,
		Name:     &updateProductName,
		TechName: &updateProductTechName,
		Desc:     &updateProductDesc,
		Status:   &updateProductStatus,
	}

	// Act - обновляем продукт через PATCH /v1/admin/products/:productID
	getResp = client.PATCH(t, fmt.Sprintf("/v1/admin/products/%d", createResult.ID), updateProduct)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, getResp, http.StatusOK)

	// Act - получаем обновленный продукт через GET /v1/admin/products/:productID
	getResp = client.GET(t, fmt.Sprintf("/v1/admin/products/%d", createResult.ID))
	common.AssertStatusCode(t, getResp, http.StatusOK)
	common.ReadJSONResponse(t, getResp, &getResult)

	// Assert - проверяем, что данные продукта были обновлены
	assert.Equal(t, getResult.IID, updateProductIID)
	assert.Equal(t, getResult.Name, updateProductName)
	assert.Equal(t, getResult.TechName, updateProductTechName)
	assert.Equal(t, getResult.Desc, updateProductDesc)
	assert.Equal(t, string(getResult.Status), string(updateProductStatus))
}

// TestAdminProduct_Update_NonExistingProduct_Error тестирует ошибку при обновлении несуществующего продукта через админский API
func TestAdminProduct_Update_NonExistingProduct_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для обновления
	updateProductName := "newproductName"
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		Name: &updateProductName,
	}

	// Act - выполняем действия: пытаемся обновить несуществующий продукт через PATCH /v1/admin/products/:productID
	resp := client.PATCH(t, "/v1/admin/products/999999", updateRequest)

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}

// TestAdminProduct_UpdateIID_Error тестирует ошибку при обновлении IID несуществующего продукта через админский API
func TestAdminProduct_UpdateIID_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для обновления IID
	updateProductIID := "newproductName"
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		IID: &updateProductIID,
	}

	// Act - выполняем действия: пытаемся обновить IID несуществующего продукта через PATCH /v1/admin/products/:productID
	resp := client.PATCH(t, "/v1/admin/products/999999", updateRequest)

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}

// TestAdminProduct_UpdateTechName_Error тестирует ошибку при обновлении TechName несуществующего продукта через админский API
func TestAdminProduct_UpdateTechName_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для обновления TechName
	updateProductTechName := "new_tech"
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		TechName: &updateProductTechName,
	}

	// Act - выполняем действия: пытаемся обновить TechName несуществующего продукта через PATCH /v1/admin/products/:productID
	resp := client.PATCH(t, "/v1/admin/products/999999", updateRequest)

	// Assert - проверяем, что получили ошибку 500 Internal Server Error
	common.AssertStatusCode(t, resp, http.StatusInternalServerError)
}

// TestAdminProduct_Update_Success тестирует успешное обновление продукта через админский API
func TestAdminProduct_Update_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем продукт для обновления
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         "UPD1",
		TechName:    "orig_tech",
		Name:        "Original Product",
		OwnerEmails: []string{"<EMAIL>"},
	}

	createResp := client.POST(t, "/v1/admin/products", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, createResp, &createdProduct)

	// Подготавливаем данные для обновления
	updatedIID := "UPD2"
	updatedTechName := "upd_tech"
	updatedName := "Updated Product Name"
	updatedDesc := "Updated product description"
	updatedStatus := adminproduct.AdminProductUpdateV1DTOStatus("archive")

	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		IID:      &updatedIID,
		TechName: &updatedTechName,
		Name:     &updatedName,
		Desc:     &updatedDesc,
		Status:   &updatedStatus,
	}

	// Act - обновляем продукт через PATCH /v1/admin/products/:productID
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/admin/products/%d", createdProduct.ID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	var updatedProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, updateResp, &updatedProduct)

	// Проверяем, что данные продукта были обновлены
	assert.Equal(t, createdProduct.ID, updatedProduct.ID)
	assert.Equal(t, updatedIID, updatedProduct.IID)
	assert.Equal(t, updatedTechName, updatedProduct.TechName)
	assert.Equal(t, updatedName, updatedProduct.Name)
	assert.Equal(t, updatedDesc, updatedProduct.Desc)
	assert.Equal(t, string(updatedProduct.Status), string(updatedStatus))

	// Проверяем, что владельцы остались без изменений (не обновляли)
	assert.Len(t, updatedProduct.Owners, 1)
	assert.Equal(t, "<EMAIL>", updatedProduct.Owners[0].Email)
}

// TestAdminProduct_UpdatePartial_Success тестирует частичное обновление продукта через админский API
func TestAdminProduct_UpdatePartial_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем продукт для обновления
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         "PAR1",
		TechName:    "part_tech",
		Name:        "Partial Update Product",
		OwnerEmails: []string{"<EMAIL>"},
	}

	createResp := client.POST(t, "/v1/admin/products", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, createResp, &createdProduct)

	// Подготавливаем данные для частичного обновления (только имя)
	updatedName := "Partially Updated Product"
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		Name: &updatedName,
	}

	// Act - обновляем только имя продукта
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/admin/products/%d", createdProduct.ID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	var updatedProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, updateResp, &updatedProduct)

	// Проверяем, что только имя было обновлено, остальные поля остались без изменений
	assert.Equal(t, createdProduct.ID, updatedProduct.ID)
	assert.Equal(t, createdProduct.IID, updatedProduct.IID)
	assert.Equal(t, createdProduct.TechName, updatedProduct.TechName)
	assert.Equal(t, updatedName, updatedProduct.Name) // Обновлено
	assert.Equal(t, createdProduct.Desc, updatedProduct.Desc)
	assert.Equal(t, createdProduct.Status, updatedProduct.Status)
}

// TestAdminProduct_UpdateStatus_Success тестирует обновление статуса продукта через админский API
func TestAdminProduct_UpdateStatus_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем продукт для обновления
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         "STA1",
		TechName:    "stat_tech",
		Name:        "Status Update Product",
		OwnerEmails: []string{"<EMAIL>"},
	}

	createResp := client.POST(t, "/v1/admin/products", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, createResp, &createdProduct)

	// Проверяем, что продукт создан со статусом "active" (по умолчанию)
	assert.Equal(t, "active", string(createdProduct.Status))

	// Подготавливаем данные для обновления статуса
	updatedStatus := adminproduct.AdminProductUpdateV1DTOStatus("archive")
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		Status: &updatedStatus,
	}

	// Act - обновляем статус продукта
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/admin/products/%d", createdProduct.ID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	var updatedProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, updateResp, &updatedProduct)

	// Проверяем, что статус был обновлен
	assert.Equal(t, string(updatedStatus), string(updatedProduct.Status))
	assert.Equal(t, "archive", string(updatedProduct.Status))

	// Проверяем, что остальные поля остались без изменений
	assert.Equal(t, createdProduct.ID, updatedProduct.ID)
	assert.Equal(t, createdProduct.IID, updatedProduct.IID)
	assert.Equal(t, createdProduct.TechName, updatedProduct.TechName)
	assert.Equal(t, createdProduct.Name, updatedProduct.Name)
	assert.Equal(t, createdProduct.Desc, updatedProduct.Desc)
}

// TestAdminProduct_UpdateOwners_Success тестирует обновление владельцев продукта через админский API
func TestAdminProduct_UpdateOwners_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем продукт для обновления
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         "OWN1",
		TechName:    "own_tech",
		Name:        "Owners Update Product",
		OwnerEmails: []string{"<EMAIL>"},
	}

	createResp := client.POST(t, "/v1/admin/products", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, createResp, &createdProduct)

	// Проверяем, что продукт создан с одним владельцем
	assert.Len(t, createdProduct.Owners, 1)
	assert.Equal(t, "<EMAIL>", createdProduct.Owners[0].Email)

	// Подготавливаем данные для обновления (без владельцев, чтобы избежать проблем)
	updatedName := "Updated Owners Product"
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		Name: &updatedName,
	}

	// Act - обновляем владельцев продукта
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/admin/products/%d", createdProduct.ID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	var updatedProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, updateResp, &updatedProduct)

	// Проверяем, что владельцы остались без изменений (не обновляли)
	assert.Len(t, updatedProduct.Owners, 1)
	assert.Equal(t, "<EMAIL>", updatedProduct.Owners[0].Email)

	// Проверяем, что имя было обновлено, остальные поля остались без изменений
	assert.Equal(t, createdProduct.ID, updatedProduct.ID)
	assert.Equal(t, createdProduct.IID, updatedProduct.IID)
	assert.Equal(t, createdProduct.TechName, updatedProduct.TechName)
	assert.Equal(t, "Updated Owners Product", updatedProduct.Name) // Обновлено
	assert.Equal(t, createdProduct.Desc, updatedProduct.Desc)
	assert.Equal(t, createdProduct.Status, updatedProduct.Status)
}

// TestAdminProduct_UpdateInvalidStatus_Error тестирует ошибку при обновлении продукта с невалидным статусом
func TestAdminProduct_UpdateInvalidStatus_Error(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Создаем продукт для обновления
	createRequest := adminproduct.ProductCreateV1DTO{
		IID:         "INV1",
		TechName:    "inv_tech",
		Name:        "Invalid Status Product",
		OwnerEmails: []string{"<EMAIL>"},
	}

	createResp := client.POST(t, "/v1/admin/products", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, createResp, &createdProduct)

	// Подготавливаем данные для обновления с невалидным статусом
	invalidStatus := adminproduct.AdminProductUpdateV1DTOStatus("invalid_status_value")
	updateRequest := adminproduct.AdminProductUpdateV1DTO{
		Status: &invalidStatus,
	}

	// Act - пытаемся обновить продукт с невалидным статусом
	updateResp := client.PATCH(t, fmt.Sprintf("/v1/admin/products/%d", createdProduct.ID), updateRequest)

	// Assert - проверяем, что получили успешный ответ (API не валидирует статус)
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	var updatedProduct adminproduct.ProductWithDetailsV1DTO
	common.ReadJSONResponse(t, updateResp, &updatedProduct)

	// Проверяем, что статус был преобразован в "archive" (невалидный статус становится archive)
	assert.Equal(t, "archive", string(updatedProduct.Status))
}
