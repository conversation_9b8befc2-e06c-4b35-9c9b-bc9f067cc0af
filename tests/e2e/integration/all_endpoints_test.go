package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// TestAllEndpoints_AvailabilityCheck проверяет доступность основных эндпоинтов
func TestAllEndpoints_AvailabilityCheck(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Тестируем основные эндпоинты, которые должны быть доступны

	t.Run("HealthEndpoint", func(t *testing.T) {
		resp := client.GET(t, "/health")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("SystemGroupEndpoints", func(t *testing.T) {
		// Список системных групп
		resp := client.GET(t, "/v1/groups")
		common.AssertStatusCode(t, resp, http.StatusOK)

		// Конкретная системная группа
		resp = client.GET(t, "/v1/groups/1")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("ProductEndpoints", func(t *testing.T) {
		// Список продуктов
		resp := client.GET(t, "/v1/products")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("AdminEndpoints", func(t *testing.T) {
		// Админские продукты
		resp := client.GET(t, "/v1/admin/products")
		common.AssertStatusCode(t, resp, http.StatusOK)

		// Админские разрешения
		resp = client.GET(t, "/v1/admin/permissions")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("PermissionEndpoints", func(t *testing.T) {
		// Общие разрешения
		resp := client.GET(t, "/v1/permissions")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("CategoryEndpoints", func(t *testing.T) {
		// Категории
		resp := client.GET(t, "/v1/categories")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("UserEndpoints", func(t *testing.T) {
		// Пользователи
		resp := client.GET(t, "/v1/users")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})

	t.Run("StatusEndpoints", func(t *testing.T) {
		// Статус системы
		resp := client.GET(t, "/v1/status")
		common.AssertStatusCode(t, resp, http.StatusOK)
	})
}

// TestAllEndpoints_ErrorHandling проверяет обработку ошибок в разных модулях
func TestAllEndpoints_ErrorHandling(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	nonExistentID := int64(99999)

	t.Run("NotFoundErrors", func(t *testing.T) {
		// Несуществующая системная группа
		resp := client.GET(t, "/v1/groups/99999")
		common.AssertStatusCode(t, resp, http.StatusNotFound)

		// Несуществующий продукт
		resp = client.GET(t, "/v1/products/99999")
		common.AssertStatusCode(t, resp, http.StatusNotFound)

		// Несуществующий админский продукт
		resp = client.GET(t, "/v1/admin/products/99999")
		common.AssertStatusCode(t, resp, http.StatusNotFound)

		// Несуществующий пользователь
		resp = client.GET(t, "/v1/users/99999")
		common.AssertStatusCode(t, resp, http.StatusNotFound)
	})

	t.Run("ProductRoleErrors", func(t *testing.T) {
		// Роли несуществующего продукта - может возвращать 200 с пустым списком
		resp := client.GET(t, fmt.Sprintf("/v1/products/%d/roles", nonExistentID))
		// Принимаем как 200 (пустой список), так и ошибку
		assert.True(t, resp.StatusCode == 200 || resp.StatusCode >= 400, "Should return 200 or error for non-existent product roles")

		// Конкретная роль несуществующего продукта - должна возвращать ошибку
		resp = client.GET(t, fmt.Sprintf("/v1/products/%d/roles/1", nonExistentID))
		assert.True(t, resp.StatusCode >= 400, "Should return error for non-existent product role")
	})

	t.Run("ProductGroupErrors", func(t *testing.T) {
		// Группы несуществующего продукта - может возвращать 200 с пустым списком
		resp := client.GET(t, fmt.Sprintf("/v1/products/%d/groups", nonExistentID))
		// Принимаем как 200 (пустой список), так и ошибку
		assert.True(t, resp.StatusCode == 200 || resp.StatusCode >= 400, "Should return 200 or error for non-existent product groups")

		// Конкретная группа несуществующего продукта - должна возвращать ошибку
		resp = client.GET(t, fmt.Sprintf("/v1/products/%d/groups/1", nonExistentID))
		assert.True(t, resp.StatusCode >= 400, "Should return error for non-existent product group")
	})
}

// TestAllEndpoints_CrossModuleConsistency проверяет консистентность данных между модулями
func TestAllEndpoints_CrossModuleConsistency(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("DataConsistency", func(t *testing.T) {
		// Получаем данные из разных эндпоинтов
		systemGroupsResp := client.GET(t, "/v1/groups")
		common.AssertStatusCode(t, systemGroupsResp, http.StatusOK)

		productsResp := client.GET(t, "/v1/products")
		common.AssertStatusCode(t, productsResp, http.StatusOK)

		adminProductsResp := client.GET(t, "/v1/admin/products")
		common.AssertStatusCode(t, adminProductsResp, http.StatusOK)

		usersResp := client.GET(t, "/v1/users")
		common.AssertStatusCode(t, usersResp, http.StatusOK)

		permissionsResp := client.GET(t, "/v1/permissions")
		common.AssertStatusCode(t, permissionsResp, http.StatusOK)

		categoriesResp := client.GET(t, "/v1/categories")
		common.AssertStatusCode(t, categoriesResp, http.StatusOK)

		// Проверяем, что все ответы не пустые
		systemGroupsBody := common.ReadResponseBody(t, systemGroupsResp)
		productsBody := common.ReadResponseBody(t, productsResp)
		adminProductsBody := common.ReadResponseBody(t, adminProductsResp)
		usersBody := common.ReadResponseBody(t, usersResp)
		permissionsBody := common.ReadResponseBody(t, permissionsResp)
		categoriesBody := common.ReadResponseBody(t, categoriesResp)

		assert.NotEmpty(t, systemGroupsBody, "System groups should not be empty")
		assert.NotEmpty(t, productsBody, "Products should not be empty")
		assert.NotEmpty(t, adminProductsBody, "Admin products should not be empty")
		assert.NotEmpty(t, usersBody, "Users should not be empty")
		assert.NotEmpty(t, permissionsBody, "Permissions should not be empty")
		assert.NotEmpty(t, categoriesBody, "Categories should not be empty")

		// Все эндпоинты работают с одной БД и должны возвращать согласованные данные
		t.Logf("All endpoints returned non-empty responses, indicating shared database consistency")
	})
}

// TestAllEndpoints_CompleteWorkflow демонстрирует полный workflow с использованием разных модулей
func TestAllEndpoints_CompleteWorkflow(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("FullWorkflow", func(t *testing.T) {
		// 1. Проверяем доступные категории
		categoriesResp := client.GET(t, "/v1/categories")
		common.AssertStatusCode(t, categoriesResp, http.StatusOK)

		// 2. Проверяем доступные разрешения
		permissionsResp := client.GET(t, "/v1/permissions")
		common.AssertStatusCode(t, permissionsResp, http.StatusOK)

		// 3. Проверяем системные группы
		systemGroupsResp := client.GET(t, "/v1/groups")
		common.AssertStatusCode(t, systemGroupsResp, http.StatusOK)

		// 4. Проверяем пользователей
		usersResp := client.GET(t, "/v1/users")
		common.AssertStatusCode(t, usersResp, http.StatusOK)

		// 5. Проверяем продукты
		productsResp := client.GET(t, "/v1/products")
		common.AssertStatusCode(t, productsResp, http.StatusOK)

		// 6. Проверяем админский вид продуктов
		adminProductsResp := client.GET(t, "/v1/admin/products")
		common.AssertStatusCode(t, adminProductsResp, http.StatusOK)

		// 7. Проверяем статус системы
		statusResp := client.GET(t, "/v1/status")
		common.AssertStatusCode(t, statusResp, http.StatusOK)

		// Все запросы должны быть успешными
		t.Logf("Complete workflow executed successfully across all modules")
	})
}
