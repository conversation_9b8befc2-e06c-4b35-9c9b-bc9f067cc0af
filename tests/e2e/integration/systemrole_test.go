package integration

import (
	"fmt"
	"net/http"
	"testing"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSystemRole_UpdateNonExistent_NotFound тестирует обновление несуществующей системной роли и возвращение ошибки
func TestSystemRole_UpdateNonExistent_NotFound(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// Подготавливаем данные для теста
	roleID := int64(174) // Используем несуществующую системную роль
	name := "testName"
	updateRequest := systemrole.RoleUpdateV1DTO{
		Name: &name,
	}

	// Act - выполняем действия
	// 1. Обновляем роль через PATCH /v1/roles/:roleID
	patchResp := client.PATCH(t, fmt.Sprintf("/v1/roles/%d", roleID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, patchResp, http.StatusNotFound)
}

// TestSystemRole_UpdateWithInvalidData_BadRequest тестирует обновление с некорректными данными
func TestSystemRole_UpdateWithInvalidData_BadRequest(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	createRequest := systemrole.RoleCreateV1DTO{
		Name: "testsN",
		Type: "system",
		Permissions: []systemrole.PermissionV1DTO{
			{
				Label: "somelabel",
				Name:  "product",
				Methods: []systemrole.MethodV1DTO{
					{
						Name: "view",
					},
				},
			},
		},
	}
	createResp := client.POST(t, "/v1/roles", createRequest) // Создаем системную роль для тестирования обновления
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdRole systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, createResp, &createdRole)

	roleID := createdRole.ID // Используем созданную роль
	// Некорректные данные (например, неправильный тип)
	invalidRequest := map[string]interface{}{
		"name": "strin123g",
		"permissions": []map[string]interface{}{
			{
				"name": "556565",
				"methods": []map[string]interface{}{
					{
						"name": "string",
					},
				},
			},
		},
	}

	// Act
	resp := client.PATCH(t, fmt.Sprintf("/v1/roles/%d", roleID), invalidRequest)

	// Assert
	common.AssertStatusCode(t, resp, http.StatusNotFound)
}

// TestSystemRole_UpdateAndGet_Success тестирует обновление с корректными данными и ответом
func TestSystemRole_UpdateAndGet_Success(t *testing.T) {
	// Arrange - настройка тестового окружения
	db := setup.InitTestDatabase(t)
	// Фикстуры уже загружены в InitTestDatabase через CleanData

	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	createRequest := systemrole.RoleCreateV1DTO{
		Name: "testsN",
		Type: "system",
		Permissions: []systemrole.PermissionV1DTO{
			{
				Label: "somelabel",
				Name:  "product",
				Methods: []systemrole.MethodV1DTO{
					{
						Name: "view",
					},
				},
			},
		},
	}
	createResp := client.POST(t, "/v1/roles", createRequest) // Создаем системную роль для тестирования обновления
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdRole systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, createResp, &createdRole)

	roleID := createdRole.ID // Используем созданную роль
	NewRoleType := "custom"
	newRoleName := "strin123asdfasdf"
	updateRequest := systemrole.RoleUpdateV1DTO{
		Name: &newRoleName,
		Type: &NewRoleType,
		Permissions: &[]systemrole.PermissionV1DTO{{
			Label: "somelabel",
			Name:  "product",
			Methods: []systemrole.MethodV1DTO{
				{
					Name: "view",
				},
			},
		},
		},
	}
	// Act - выполняем действия
	// 1. Обновляем группу через PATCH /v1/roles/:roleID
	patchResp := client.PATCH(t, fmt.Sprintf("/v1/roles/%d", roleID), updateRequest)

	// Assert - проверяем результат обновления
	common.AssertStatusCode(t, patchResp, http.StatusOK)

	var patchResult systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, patchResp, &patchResult)

	// Проверяем, что обновление прошло успешно
	assert.Equal(t, roleID, patchResult.ID)
	assert.Equal(t, constants.CustomType, patchResult.Type)

	// 2. Получаем группу через GET /v1/roles/:roleID
	getResp := client.GET(t, fmt.Sprintf("/v1/roles/%d", roleID))

	// Assert - проверяем результат получения
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var getResult systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, getResp, &getResult)

	// Проверяем, что данные соответствуют обновленным
	assert.Equal(t, roleID, getResult.ID)
	assert.Equal(t, constants.CustomType, getResult.Type)

	// 3. Дополнительная проверка через БД
	// assertGroupInDatabase(t, db, roleID, constants.SystemType)
}

// TestSystemRole_GetNonExistent_NotFound тестирует получение несуществующей группы
func TestSystemRole_GetNonExistent_NotFound(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	nonExistentRoleID := int64(99994)

	// Act
	resp := client.GET(t, fmt.Sprintf("/v1/roles/%d", nonExistentRoleID))

	// Assert
	common.AssertStatusCode(t, resp, http.StatusNotFound)
}

// TestSystemRole_CreateUpdateDelete_FullCycle тестирует полный цикл CRUD операций
func TestSystemRole_CreateUpdateDelete_FullCycle(t *testing.T) {
	// Arrange
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	// 1. Создаем группу
	createRequest := systemrole.RoleCreateV1DTO{
		Name: "Test E2E Role",
		Type: "custom",
	}

	createResp := client.POST(t, "/v1/roles", createRequest)
	common.AssertStatusCode(t, createResp, http.StatusOK)

	var createdGroup systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, createResp, &createdGroup)

	roleID := createdGroup.ID
	require.NotZero(t, roleID)

	// 2. Обновляем роль
	updateRequest := systemrole.RoleFullV1DTO{
		Type: constants.SystemType,
		Name: "Updated E2E Group",
	}

	updateResp := client.PATCH(t, fmt.Sprintf("/v1/roles/%d", roleID), updateRequest)
	common.AssertStatusCode(t, updateResp, http.StatusOK)

	// 3. Проверяем обновление
	getResp := client.GET(t, fmt.Sprintf("/v1/roles/%d", roleID))
	common.AssertStatusCode(t, getResp, http.StatusOK)

	var updatedRole systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, getResp, &updatedRole)

	assert.Equal(t, "Updated E2E Group", updatedRole.Name)
	assert.Equal(t, constants.SystemType, updatedRole.Type)

	// 4. Удаляем группу (изменяем active_flg на false)
	deleteResp := client.DELETE(t, fmt.Sprintf("/v1/roles/%d", roleID))
	common.AssertStatusCode(t, deleteResp, http.StatusOK)

	// 5. Проверяем, что группа помечена как неактивная
	// Группа должна существовать, но быть неактивной
	getAfterDeleteResp := client.GET(t, fmt.Sprintf("/v1/admin/roles/%d", roleID))
	common.AssertStatusCode(t, getAfterDeleteResp, http.StatusOK)

	var deletedRole systemrole.RoleFullV1DTO
	common.ReadJSONResponse(t, getAfterDeleteResp, &deletedRole)

	// Проверяем, что данные роли сохранились
	assert.Equal(t, "Updated E2E Group", deletedRole.Name)
	assert.Equal(t, constants.SystemType, deletedRole.Type)
}
