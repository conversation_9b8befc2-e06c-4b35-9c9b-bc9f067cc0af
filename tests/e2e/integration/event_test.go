package integration

import (
	"bufio"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/common"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestEvents_Create_Success(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("CreateEvent", func(t *testing.T) {

		requestBody := map[string]interface{}{
			"event": map[string]interface{}{},
		}

		postResp := client.POST(t, "/v1/events", requestBody)
		common.AssertStatusCode(t, postResp, http.StatusOK)

		var respBody map[string]interface{}
		common.ReadJSONResponse(t, postResp, &respBody)

		assert.NotNil(t, respBody)
		assert.Equal(t, "not implemented, status: 200 for testing", respBody["message"])
	})
}

func TestEvents_GetSource_Success(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("GetEventSource", func(t *testing.T) {
		req, err := http.NewRequest("GET", server.BaseURL+"/v1/events/source", nil)
		require.NoError(t, err)

		resp, err := client.HTTPClient.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "text/event-stream", resp.Header.Get("Content-Type"))

		reader := bufio.NewReader(resp.Body)
		var lines []string
		for {
			line, err := reader.ReadString('\n')
			if err != nil {
				t.Fatalf("failed to read SSE stream: %v", err)
			}
			lines = append(lines, line)
			if line == "\n" {
				break
			}
		}

		fullMessage := strings.Join(lines, "")
		assert.Contains(t, fullMessage, "event: connected")
		assert.Contains(t, fullMessage, "data: Connection established")
	})
}

func TestEvents_GetUnread_Success(t *testing.T) {
	db := setup.InitTestDatabase(t)
	server := setup.InitTestServer(t, db)
	client := common.NewTestClient(server.BaseURL)

	t.Run("GetUnreadEvents", func(t *testing.T) {
		req, err := http.NewRequest("GET", server.BaseURL+"/v1/events/unread", nil)
		require.NoError(t, err)

		resp, err := client.HTTPClient.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "application/json; charset=utf-8", resp.Header.Get("Content-Type"))

		var events []map[string]interface{}
		common.ReadJSONResponse(t, resp, &events)

		assert.NotNil(t, events)

		if len(events) > 0 {
			event := events[0]
			assert.Contains(t, event, "type")
			assert.Contains(t, event, "createdAt")
			assert.Contains(t, event, "meta")

			meta, ok := event["meta"].(map[string]interface{})
			assert.True(t, ok, "meta should be an object")

			assert.Contains(t, meta, "proposalID")

			// Опционально:
			if msg, ok := event["message"]; ok {
				assert.IsType(t, "", msg)
			}

			if status, ok := meta["status"]; ok {
				assert.Contains(t, []string{"draft", "archive", "on_approval", "rejected", "approved", "default", "success", "error", "warning"}, status)
			}
		}
	})
}
