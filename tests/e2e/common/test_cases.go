package common

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

// HTTPMethod представляет HTTP метод
type HTTPMethod string

// Константы HTTP методов
const (
	GET    HTTPMethod = "GET"
	POST   HTTPMethod = "POST"
	PATCH  HTTPMethod = "PATCH"
	DELETE HTTPMethod = "DELETE"
	PUT    HTTPMethod = "PUT"
)

// TestCase представляет отдельный тестовый случай
type TestCase struct {
	Name           string
	Method         HTTPMethod
	URL            string
	Body           any
	ExpectedStatus int
	Description    string
}

// TestEnvironment представляет тестовое окружение
type TestEnvironment struct {
	Client *TestClient
	t      *testing.T
}

// SetupTestEnvironment создает тестовое окружение
func SetupTestEnvironment(t *testing.T) *TestEnvironment {
	return &TestEnvironment{
		Client: NewTestClient("http://localhost:8080"), // Будет заменено на реальный URL
		t:      t,
	}
}

// RunTestCases выполняет список тестовых случаев
func (env *TestEnvironment) RunTestCases(testCases []TestCase) {
	for _, tc := range testCases {
		env.t.Run(tc.Name, func(t *testing.T) {
			var resp *http.Response

			switch tc.Method {
			case GET:
				resp = env.Client.GET(t, tc.URL)
			case POST:
				resp = env.Client.POST(t, tc.URL, tc.Body)
			case PATCH:
				resp = env.Client.PATCH(t, tc.URL, tc.Body)
			case DELETE:
				resp = env.Client.DELETE(t, tc.URL)
			default:
				t.Fatalf("Unsupported HTTP method: %s", tc.Method)
			}

			assert.Equal(t, tc.ExpectedStatus, resp.StatusCode, tc.Description)
		})
	}
}
