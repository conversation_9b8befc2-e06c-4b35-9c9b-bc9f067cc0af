package common

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestClient представляет HTTP клиент для тестов
type TestClient struct {
	BaseURL    string
	HTTPClient *http.Client
	AuthToken  string
}

// NewTestClient создает новый тестовый клиент
func NewTestClient(baseURL string) *TestClient {
	return &TestClient{
		BaseURL:    baseURL,
		HTTPClient: &http.Client{},
	}
}

// SetAuthToken устанавливает токен авторизации
func (c *TestClient) SetAuthToken(token string) {
	c.AuthToken = token
}

// GET выполняет GET запрос
func (c *TestClient) GET(t *testing.T, path string) *http.Response {
	req, err := http.NewRequest("GET", c.BaseURL+path, nil)
	require.NoError(t, err)

	if c.AuthToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.Auth<PERSON>oken)
	}

	resp, err := c.HTTPClient.Do(req)
	require.NoError(t, err)

	return resp
}

// POST выполняет POST запрос с JSON телом
func (c *TestClient) POST(t *testing.T, path string, body any) *http.Response {
	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		require.NoError(t, err)
		bodyReader = bytes.NewReader(jsonBody)
	}

	req, err := http.NewRequest("POST", c.BaseURL+path, bodyReader)
	require.NoError(t, err)

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	if c.AuthToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.AuthToken)
	}

	resp, err := c.HTTPClient.Do(req)
	require.NoError(t, err)

	return resp
}

// PATCH выполняет PATCH запрос с JSON телом
func (c *TestClient) PATCH(t *testing.T, path string, body any) *http.Response {
	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		require.NoError(t, err)
		bodyReader = bytes.NewReader(jsonBody)
	}

	req, err := http.NewRequest("PATCH", c.BaseURL+path, bodyReader)
	require.NoError(t, err)

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	if c.AuthToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.AuthToken)
	}

	resp, err := c.HTTPClient.Do(req)
	require.NoError(t, err)

	return resp
}

// DELETE выполняет DELETE запрос
func (c *TestClient) DELETE(t *testing.T, path string) *http.Response {
	req, err := http.NewRequest("DELETE", c.BaseURL+path, nil)
	require.NoError(t, err)

	if c.AuthToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.AuthToken)
	}

	resp, err := c.HTTPClient.Do(req)
	require.NoError(t, err)

	return resp
}

// ReadJSONResponse читает JSON ответ в структуру
func ReadJSONResponse(t *testing.T, resp *http.Response, target any) {
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err)

	if len(body) > 0 {
		err = json.Unmarshal(body, target)
		require.NoError(t, err, "Failed to unmarshal response: %s", string(body))
	}
}

// ReadResponseBody читает тело ответа как строку
func ReadResponseBody(t *testing.T, resp *http.Response) string {
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err)

	return string(body)
}

// AssertStatusCode проверяет код ответа
func AssertStatusCode(t *testing.T, resp *http.Response, expectedCode int) {
	if resp.StatusCode != expectedCode {
		body := ReadResponseBody(t, resp)
		require.Equal(t, expectedCode, resp.StatusCode,
			"Unexpected status code. Response body: %s", body)
	}
}
