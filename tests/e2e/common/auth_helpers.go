package common

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

// AuthTestUser представляет тестового пользователя для авторизации
type AuthTestUser struct {
	Email  string
	UserID int64
	Roles  []string
}

// CreateAuthMiddleware создает middleware для авторизационных тестов
func CreateAuthMiddleware(user AuthTestUser) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(constants.RequestContextKey, entity.RequestContext{
			UserID:     user.UserID,
			Email:      user.Email,
			CategoryID: 1,
			IsAdmin:    false,
		})
		c.Next()
	}
}

// CreateUnauthorizedMiddleware создает middleware, которое не устанавливает пользователя
func CreateUnauthorizedMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"error": "Unauthorized",
		})
	}
}

// TestUsers содержит предопределенных тестовых пользователей
var TestUsers = struct {
	AccountAdmin       AuthTestUser
	AccountUser        AuthTestUser
	ProductOwner       AuthTestUser
	ProductParticipant AuthTestUser
	Unauthorized       AuthTestUser
}{
	AccountAdmin: AuthTestUser{
		Email:  "<EMAIL>",
		UserID: 1, // Будет обновлен динамически
		Roles:  []string{"account_admin"},
	},
	AccountUser: AuthTestUser{
		Email:  "<EMAIL>",
		UserID: 2, // Будет обновлен динамически
		Roles:  []string{"account_user"},
	},
	ProductOwner: AuthTestUser{
		Email:  "<EMAIL>",
		UserID: 3, // Будет обновлен динамически
		Roles:  []string{"product_owner"},
	},
	ProductParticipant: AuthTestUser{
		Email:  "<EMAIL>",
		UserID: 4, // Будет обновлен динамически
		Roles:  []string{"product_participant"},
	},
	Unauthorized: AuthTestUser{
		Email:  "<EMAIL>",
		UserID: 5, // Будет обновлен динамически
		Roles:  []string{},
	},
}

// UpdateTestUsersWithRealIDs обновляет ID пользователей из БД
func UpdateTestUsersWithRealIDs(db *setup.TestDatabase) error {
	adminID, err := db.GetUserIDByEmail(TestUsers.AccountAdmin.Email)
	if err != nil {
		return err
	}
	TestUsers.AccountAdmin.UserID = adminID

	userID, err := db.GetUserIDByEmail(TestUsers.AccountUser.Email)
	if err != nil {
		return err
	}
	TestUsers.AccountUser.UserID = userID

	ownerID, err := db.GetUserIDByEmail(TestUsers.ProductOwner.Email)
	if err != nil {
		return err
	}
	TestUsers.ProductOwner.UserID = ownerID

	participantID, err := db.GetUserIDByEmail(TestUsers.ProductParticipant.Email)
	if err != nil {
		return err
	}
	TestUsers.ProductParticipant.UserID = participantID

	unauthorizedID, err := db.GetUserIDByEmail(TestUsers.Unauthorized.Email)
	if err != nil {
		return err
	}
	TestUsers.Unauthorized.UserID = unauthorizedID

	return nil
}
