package setup

import (
	"github.com/gin-gonic/gin"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/constants"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/shared/entity"
)

// MockAuthMiddleware создает middleware для тестов, который устанавливает mock request context
func MockAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Устанавливаем тестовый request context
		// Используем данные из фикстур - пользователь с ID 1 (<EMAIL>)
		c.Set(constants.RequestContextKey, entity.RequestContext{
			UserID:     1,
			Email:      "<EMAIL>",
			CategoryID: 1,
			IsAdmin:    true,
		})
		c.Next()
	}
}

// MockAuthMiddlewareWithUser создает middleware с конкретным пользователем
func MockAuthMiddlewareWithUser(userID int64, email string, categoryID int64, isAdmin bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(constants.RequestContextKey, entity.RequestContext{
			UserID:     userID,
			Email:      email,
			CategoryID: categoryID,
			IsAdmin:    isAdmin,
		})
		c.Next()
	}
}
