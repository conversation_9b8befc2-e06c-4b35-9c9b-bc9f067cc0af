package setup

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"

	"github.com/jackc/pgx/v5/pgxpool"
	_ "github.com/jackc/pgx/v5/stdlib"
	"github.com/stretchr/testify/require"
)

// TestDatabase представляет тестовую базу данных
type TestDatabase struct {
	Pool   *pgxpool.Pool
	DBName string
	Config *pgxpool.Config
}

// Глобальные переменные для управления общей БД
var (
	sharedTestDB  *TestDatabase
	dbMutex       sync.Mutex
	dbInitialized bool
)

// InitTestDatabase создает и настраивает общую тестовую базу данных
func InitTestDatabase(t *testing.T) *TestDatabase {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	// Если БД уже инициализирована, возвращаем существующую
	if dbInitialized && sharedTestDB != nil {
		// Просто возвращаем существующую БД без очистки
		// Тесты должны быть написаны так, чтобы не зависеть от порядка выполнения
		return sharedTestDB
	}

	// Создаем БД только при первом вызове
	sharedTestDB = createSharedDatabase(t)
	dbInitialized = true

	// Регистрируем cleanup только один раз для всего процесса.
	// Используем глобальный cleanup, а не t.Cleanup для каждого теста
	return sharedTestDB
}

// createSharedDatabase создает общую БД для всех тестов
func createSharedDatabase(t *testing.T) *TestDatabase {
	// Получаем параметры подключения из переменных окружения
	host := getEnvOrDefault("POSTGRES_HOST", "localhost")
	port := getEnvOrDefault("POSTGRES_PORT", "5432")
	user := getEnvOrDefault("POSTGRES_USER", "postgres")
	password := os.Getenv("POSTGRES_PASSWORD")

	require.NotEmpty(t, password, "POSTGRES_PASSWORD environment variable must be set")

	// Создаем имя для общей тестовой БД
	dbName := fmt.Sprintf("pms_e2e_test_%s", generateTestID())

	// Подключаемся к postgres для создания тестовой БД
	adminConnStr := fmt.Sprintf("postgres://%s:%s@%s:%s/postgres?sslmode=disable",
		user, password, host, port)

	adminDB, err := sql.Open("pgx", adminConnStr)
	require.NoError(t, err)
	defer adminDB.Close()

	// Создаем тестовую БД
	_, err = adminDB.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
	require.NoError(t, err)

	// Подключаемся к тестовой БД
	testConnStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		user, password, host, port, dbName)

	config, err := pgxpool.ParseConfig(testConnStr)
	require.NoError(t, err)

	pool, err := pgxpool.NewWithConfig(context.Background(), config)
	require.NoError(t, err)

	testDB := &TestDatabase{
		Pool:   pool,
		DBName: dbName,
		Config: config,
	}

	// Применяем миграции
	err = testDB.ApplyMigrations()
	require.NoError(t, err)

	// Загружаем фикстуры при первом создании БД
	err = testDB.LoadFixtures()
	require.NoError(t, err)

	return testDB
}

// ApplyMigrations применяет все миграции к тестовой БД
func (db *TestDatabase) ApplyMigrations() error {
	// Получаем список всех файлов миграций
	migrationFiles, err := db.getMigrationFiles()
	if err != nil {
		return fmt.Errorf("failed to get migration files: %w", err)
	}

	// Применяем миграции в порядке их имен (сортировка по timestamp)
	for _, filename := range migrationFiles {
		err := db.applyMigrationFile(filename)
		if err != nil {
			return fmt.Errorf("failed to apply migration %s: %w", filename, err)
		}
	}

	return nil
}

// LoadFixtures загружает тестовые данные
func (db *TestDatabase) LoadFixtures() error {
	fixtureSQL, err := os.ReadFile("../../../migrations/tests/01_create_test_db.sql")
	if err != nil {
		return fmt.Errorf("failed to read fixture file: %w", err)
	}

	_, err = db.Pool.Exec(context.Background(), string(fixtureSQL))
	if err != nil {
		return fmt.Errorf("failed to load fixtures: %w", err)
	}

	return nil
}

// LoadUserFixtures загружает только пользовательские данные (без системных)
func (db *TestDatabase) LoadUserFixtures() error {
	// Загружаем только пользовательские данные из фикстур
	userFixturesSQL := `
-- Создаем пользователей
INSERT INTO users (category_id, email, full_name, position, is_admin) VALUES
    (1, '<EMAIL>', 'Владелец 1', 'Product Owner', false),
    (1, '<EMAIL>', 'Владелец 2', 'Product Owner', false),
    (1, '<EMAIL>', 'Разработчик 1', 'Senior Developer', false),
    (1, '<EMAIL>', 'Разработчик 2', 'Junior Developer', false),
    (2, '<EMAIL>', 'Менеджер', 'Project Manager', false),
    (3, '<EMAIL>', 'Тестировщик', 'QA Engineer', false),
    (1, '<EMAIL>', 'Администратор', 'System Admin', true);

-- Создаем продукты
INSERT INTO products (iid, tech_name, name, description, creator_id) VALUES
    ('P001', 'TESTPROD1', 'Тестовый продукт 1', 'Продукт для тестирования функциональности участников', 1),
    ('P002', 'TESTPROD2', 'Тестовый продукт 2', 'Продукт для тестирования удаления', 2);

-- Добавляем участников в первый продукт
INSERT INTO participants (product_id, user_id)
SELECT 1, id FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Добавляем участников во второй продукт
INSERT INTO participants (product_id, user_id)
SELECT 2, id FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Создаем тестовые продуктовые группы
INSERT INTO groups (name, product_id, is_system) VALUES
    ('Product 1 Admins', 1, false),
    ('Product 1 Developers', 1, false),
    ('Product 2 Admins', 2, false);
`

	_, err := db.Pool.Exec(context.Background(), userFixturesSQL)
	if err != nil {
		return fmt.Errorf("failed to load user fixtures: %w", err)
	}

	return nil
}

// CleanData очищает пользовательские данные между тестами, сохраняя системные данные
func (db *TestDatabase) CleanData() error {
	// Очищаем только пользовательские данные, оставляя системные (роли, разрешения, категории)
	userDataTables := []string{
		"participants_roles",
		"participants_groups",
		"users_groups",
		"groups_roles",
		"participants",
		"groups WHERE product_id IS NOT NULL", // Только продуктовые группы
		"products",
		"users",
		"proposals",
	}

	// Очищаем пользовательские данные
	for _, table := range userDataTables {
		var query string
		if strings.Contains(table, "WHERE") {
			// Для условных удалений
			parts := strings.Split(table, " WHERE ")
			query = fmt.Sprintf("DELETE FROM %s WHERE %s", parts[0], parts[1])
		} else {
			// Для полной очистки таблиц
			query = fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", table)
		}

		_, err := db.Pool.Exec(context.Background(), query)
		if err != nil {
			// Игнорируем ошибки для таблиц, которых может не быть
			continue
		}
	}

	// Загружаем только пользовательские данные из фикстур
	return db.LoadUserFixtures()
}

// Cleanup очищает тестовую БД
func (db *TestDatabase) Cleanup(t *testing.T) {
	if db.Pool != nil {
		db.Pool.Close()
	}

	// Удаляем тестовую БД
	host := getEnvOrDefault("POSTGRES_HOST", "localhost")
	port := getEnvOrDefault("POSTGRES_PORT", "5432")
	user := getEnvOrDefault("POSTGRES_USER", "postgres")
	password := os.Getenv("POSTGRES_PASSWORD")

	adminConnStr := fmt.Sprintf("postgres://%s:%s@%s:%s/postgres?sslmode=disable",
		user, password, host, port)

	adminDB, err := sql.Open("pgx", adminConnStr)
	if err != nil {
		t.Logf("Failed to connect to admin DB for cleanup: %v", err)
		return
	}
	defer adminDB.Close()

	// Завершаем активные соединения
	_, _ = adminDB.Exec(fmt.Sprintf(
		"SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s' AND pid <> pg_backend_pid()",
		db.DBName))

	// Удаляем БД
	_, err = adminDB.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", db.DBName))
	if err != nil {
		t.Logf("Failed to drop test database %s: %v", db.DBName, err)
	}
}

// CleanupSharedDatabase удаляет общую тестовую БД.
// Должна вызываться в конце всех тестов
func CleanupSharedDatabase() {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	if sharedTestDB != nil {
		// Закрываем соединения
		if sharedTestDB.Pool != nil {
			sharedTestDB.Pool.Close()
		}

		// Удаляем БД
		host := getEnvOrDefault("POSTGRES_HOST", "localhost")
		port := getEnvOrDefault("POSTGRES_PORT", "5432")
		user := getEnvOrDefault("POSTGRES_USER", "postgres")
		password := os.Getenv("POSTGRES_PASSWORD")

		adminConnStr := fmt.Sprintf("postgres://%s:%s@%s:%s/postgres?sslmode=disable",
			user, password, host, port)

		adminDB, err := sql.Open("pgx", adminConnStr)
		if err != nil {
			return
		}
		defer adminDB.Close()

		// Завершаем активные соединения
		_, _ = adminDB.Exec(fmt.Sprintf(
			"SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s' AND pid <> pg_backend_pid()",
			sharedTestDB.DBName))

		// Удаляем БД
		_, _ = adminDB.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", sharedTestDB.DBName))

		sharedTestDB = nil
		dbInitialized = false
	}
}

func generateTestID() string {
	// Используем один ID для всех тестов в рамках одного процесса
	// Это обеспечивает использование одной БД для всех тестов
	return fmt.Sprintf("%d", os.Getpid())
}

// getMigrationFiles возвращает отсортированный список файлов миграций
func (db *TestDatabase) getMigrationFiles() ([]string, error) {
	migrationDir := "../../../migrations"
	entries, err := os.ReadDir(migrationDir)
	if err != nil {
		return nil, err
	}

	var migrationFiles []string
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".sql") && !strings.Contains(entry.Name(), "test") {
			migrationFiles = append(migrationFiles, entry.Name())
		}
	}

	// Файлы уже отсортированы по имени благодаря timestamp в начале
	return migrationFiles, nil
}

// applyMigrationFile применяет одну миграцию
func (db *TestDatabase) applyMigrationFile(filename string) error {
	// Валидация имени файла для предотвращения path traversal атак
	if strings.Contains(filename, "..") || strings.Contains(filename, "/") || strings.Contains(filename, "\\") {
		return fmt.Errorf("invalid migration filename: %s", filename)
	}

	// Дополнительная валидация: проверяем, что файл имеет правильное расширение
	if !strings.HasSuffix(filename, ".sql") {
		return fmt.Errorf("invalid migration file extension: %s", filename)
	}

	// Используем безопасный путь к директории миграций
	migrationsDir := "../../../migrations"
	migrationPath := filepath.Join(migrationsDir, filename)

	// Проверяем, что итоговый путь находится в ожидаемой директории
	absPath, err := filepath.Abs(migrationPath)
	if err != nil {
		return fmt.Errorf("failed to resolve migration path: %w", err)
	}

	absMigrationsDir, err := filepath.Abs(migrationsDir)
	if err != nil {
		return fmt.Errorf("failed to resolve migrations directory: %w", err)
	}

	if !strings.HasPrefix(absPath, absMigrationsDir) {
		return fmt.Errorf("migration file outside of migrations directory: %s", filename)
	}

	// #nosec G304 - путь к файлу миграции проверен и безопасен
	migrationSQL, err := os.ReadFile(migrationPath)
	if err != nil {
		return err
	}

	// Извлекаем только UP часть миграции
	upSQL := extractUpMigration(string(migrationSQL))

	// Выполняем миграцию
	_, err = db.Pool.Exec(context.Background(), upSQL)
	return err
}

func extractUpMigration(migrationSQL string) string {
	// Простая реализация извлечения UP части миграции
	// В реальном проекте можно использовать более сложную логику
	lines := strings.Split(migrationSQL, "\n")
	var upLines []string
	inUpSection := false

	for _, line := range lines {
		if strings.Contains(line, "-- +goose Up") {
			inUpSection = true
			continue
		}
		if strings.Contains(line, "-- +goose Down") {
			break
		}
		if inUpSection {
			upLines = append(upLines, line)
		}
	}

	return strings.Join(upLines, "\n")
}
