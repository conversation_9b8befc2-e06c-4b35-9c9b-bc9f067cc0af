package setup

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/require"
)

// InitAuthTestDatabase создает отдельную БД для тестов авторизации
func InitAuthTestDatabase(t *testing.T) *TestDatabase {
	host := getEnvOrDefault("POSTGRES_HOST", "localhost")
	port := getEnvOrDefault("POSTGRES_PORT", "5432")
	user := getEnvOrDefault("POSTGRES_USER", "postgres")
	password := os.Getenv("POSTGRES_PASSWORD")

	require.NotEmpty(t, password, "POSTGRES_PASSWORD environment variable must be set")

	dbName := fmt.Sprintf("pms_auth_test_%s_%d", generateTestID(), time.Now().UnixNano())

	adminConnStr := fmt.Sprintf("postgres://%s:%s@%s:%s/postgres?sslmode=disable",
		user, password, host, port)

	adminDB, err := sql.Open("pgx", adminConnStr)
	require.NoError(t, err)
	defer adminDB.Close()

	_, err = adminDB.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
	require.NoError(t, err)

	testConnStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		user, password, host, port, dbName)

	config, err := pgxpool.ParseConfig(testConnStr)
	require.NoError(t, err)

	pool, err := pgxpool.NewWithConfig(context.Background(), config)
	require.NoError(t, err)

	testDB := &TestDatabase{
		Pool:   pool,
		DBName: dbName,
		Config: config,
	}

	err = testDB.ApplyMigrations()
	require.NoError(t, err)

	err = testDB.LoadAuthFixtures()
	require.NoError(t, err)

	t.Cleanup(func() {
		testDB.Pool.Close()

		adminDB, err := sql.Open("pgx", adminConnStr)
		if err == nil {
			defer adminDB.Close()
			adminDB.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", dbName))
		}
	})

	return testDB
}

// LoadAuthFixtures загружает авторизационные тестовые данные
func (db *TestDatabase) LoadAuthFixtures() error {
	fixtureSQL, err := os.ReadFile("../../../migrations/tests/02_create_auth_test_db.sql")
	if err != nil {
		return fmt.Errorf("failed to read auth fixture file: %w", err)
	}

	_, err = db.Pool.Exec(context.Background(), string(fixtureSQL))
	if err != nil {
		return fmt.Errorf("failed to load auth fixtures: %w", err)
	}

	return nil
}

// GetUserIDByEmail возвращает ID пользователя по email
func (db *TestDatabase) GetUserIDByEmail(email string) (int64, error) {
	var userID int64
	err := db.Pool.QueryRow(context.Background(), "SELECT id FROM users WHERE email = $1", email).Scan(&userID)
	return userID, err
}

// GetProductIDByTechName возвращает ID продукта по техническому имени
func (db *TestDatabase) GetProductIDByTechName(techName string) (int64, error) {
	var productID int64
	err := db.Pool.QueryRow(context.Background(), "SELECT id FROM products WHERE tech_name = $1", techName).Scan(&productID)
	return productID, err
}
