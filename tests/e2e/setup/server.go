package setup

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"

	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/ctx"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/admingroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminpermission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproduct"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminproposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/adminuser"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/category"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/event"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/keycloak"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/participant"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/permission"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/product"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/productrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/proposal"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/status"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemgroup"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/systemrole"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/gen/openapi/user"
	appservice "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/application/shared/service"
	identityentity "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/domain/identityprovider/entity"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/authorization"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/identityprovider"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/cache"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/repositories/primedb"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/infrastructure/sse"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/presentation/api"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/internal/presentation/http/middleware"
	"git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/pkg/logger"
)

// TestServer представляет тестовый HTTP сервер
type TestServer struct {
	Server     *http.Server
	BaseURL    string
	Database   *TestDatabase
	Handler    *api.SystemGroupHandler
	AppService *appservice.Service
}

// InitTestServer создает и запускает тестовый сервер БЕЗ middleware авторизации
func InitTestServer(t *testing.T, db *TestDatabase) *TestServer {
	return initTestServerInternal(t, db, MockAuthMiddleware(), false)
}

// InitTestServerWithMiddleware создает и запускает тестовый сервер с кастомным middleware и авторизацией
func InitTestServerWithMiddleware(t *testing.T, db *TestDatabase, authMiddleware gin.HandlerFunc) *TestServer {
	return initTestServerInternal(t, db, authMiddleware, true)
}

// initTestServerInternal - общая функция для создания тестового сервера
func initTestServerInternal(t *testing.T, db *TestDatabase, authMiddleware gin.HandlerFunc, withAuthorization bool) *TestServer {
	// Настраиваем Gin в тестовом режиме
	gin.SetMode(gin.TestMode)

	// Создаем роутер
	router := gin.New()

	// Создаем реальные сервисы с подключением к тестовой БД
	appService := createAppService(t, db)

	var testCtx *ctx.Ctx
	if withAuthorization {
		// Создаем конфигурацию авторизации для тестов авторизации
		authConfig, err := loadAuthorizationConfig("../../../configs/authorization.yaml", appService)
		require.NoError(t, err)

		testCtx = &ctx.Ctx{
			AppService:    appService,
			Authorization: authConfig,
			Log:           logger.NewTemporary(),
		}
	} else {
		// Создаем минимальный контекст без авторизации для обычных тестов
		testCtx = &ctx.Ctx{
			AppService: appService,
			Log:        logger.NewTemporary(),
		}
	}

	// Создаем обработчики API
	systemGroupHandler := api.NewSystemGroupHandler(testCtx)

	// Добавляем health endpoint для проверки готовности сервера ДО middleware авторизации
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Добавляем middleware в правильном порядке
	router.Use(authMiddleware)
	if withAuthorization {
		router.Use(middleware.HandleAuthorizer(testCtx))
	}

	// Создаем все обработчики как в основном приложении
	participantHandler := api.NewParticipantHandler(testCtx)
	adminHandler := api.NewAdminHandler(testCtx)
	adminRoleHandler := api.NewAdminRoleHandler(testCtx)
	adminGroupHandler := api.NewAdminGroupHandler(testCtx)
	adminProductHandler := api.NewAdminProductHandler(testCtx)
	adminProposalHandler := api.NewAdminProposalHandler(testCtx)
	// Создаем SSE Manager для тестов
	sseManager := sse.NewSSEManager()

	adminUserHandler := api.NewAdminUserHandler(testCtx)
	categoryHandler := api.NewCategoryHandler(testCtx)
	keycloakHandler := api.NewKeycloakHandler(testCtx)
	permissionHandler := api.NewPermissionHandler(testCtx)
	proposalHandler := api.NewProposalHandler(testCtx, sseManager)
	statusHandler := api.NewStatusHandler(testCtx)
	roleHandler := api.NewRoleHandler(testCtx)
	productHandler := api.NewProductHandler(testCtx)
	groupHandler := api.NewGroupHandler(testCtx)
	userHandler := api.NewUserHandler(testCtx)
	systemRoleHandler := api.NewSystemRoleHandler(testCtx)
	eventHandler := api.NewEventHandler(testCtx, sseManager)

	// Регистрируем маршруты в том же порядке, что и в основном приложении
	// Сначала event handler (без middleware)
	event.RegisterHandlers(router, eventHandler)

	// Затем все остальные маршруты (с middleware)
	participant.RegisterHandlers(router, participantHandler)
	adminpermission.RegisterHandlers(router, adminHandler)
	adminrole.RegisterHandlers(router, adminRoleHandler)
	admingroup.RegisterHandlers(router, adminGroupHandler)
	adminproduct.RegisterHandlers(router, adminProductHandler)
	adminproposal.RegisterHandlers(router, adminProposalHandler)
	adminuser.RegisterHandlers(router, adminUserHandler)
	category.RegisterHandlers(router, categoryHandler)
	keycloak.RegisterHandlers(router, keycloakHandler)
	permission.RegisterHandlers(router, permissionHandler)
	proposal.RegisterHandlers(router, proposalHandler)
	status.RegisterHandlers(router, statusHandler)
	productrole.RegisterHandlers(router, roleHandler)
	product.RegisterHandlers(router, productHandler)
	productgroup.RegisterHandlers(router, groupHandler)
	systemgroup.RegisterHandlers(router, systemGroupHandler)
	user.RegisterHandlers(router, userHandler)
	systemrole.RegisterHandlers(router, systemRoleHandler)

	// Находим свободный порт (привязываемся только к localhost)
	listener, err := net.Listen("tcp", "localhost:0")
	require.NoError(t, err)

	port := listener.Addr().(*net.TCPAddr).Port
	baseURL := fmt.Sprintf("http://localhost:%d", port)

	// Создаем HTTP сервер с защитой от Slowloris атак
	server := &http.Server{
		Handler:           router,
		ReadHeaderTimeout: 10 * time.Second,
		ReadTimeout:       30 * time.Second,
		WriteTimeout:      30 * time.Second,
		IdleTimeout:       60 * time.Second,
	}

	testServer := &TestServer{
		Server:     server,
		BaseURL:    baseURL,
		Database:   db,
		Handler:    systemGroupHandler,
		AppService: appService,
	}

	// Запускаем сервер в отдельной горутине
	go func() {
		if err := server.Serve(listener); err != nil && !errors.Is(err, http.ErrServerClosed) {
			t.Errorf("Failed to start test server: %v", err)
		}
	}()

	// Ждем, пока сервер запустится
	require.Eventually(t, func() bool {
		resp, err := http.Get(baseURL + "/health")
		if err != nil {
			return false
		}
		defer func() {
			if closeErr := resp.Body.Close(); closeErr != nil {
				t.Logf("Warning: failed to close response body: %v", closeErr)
			}
		}()
		return resp.StatusCode == http.StatusOK
	}, 5*time.Second, 100*time.Millisecond, "Test server failed to start")

	// Регистрируем cleanup
	t.Cleanup(func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := server.Shutdown(ctx); err != nil {
			t.Logf("Warning: failed to shutdown server: %v", err)
		}
	})

	return testServer
}

// TestConfig содержит конфигурацию для тестов
type TestConfig struct {
	PostgresHost     string
	PostgresPort     int
	PostgresUser     string
	PostgresPassword string
	RedisHost        string
	RedisPort        int
	RedisPassword    string
	KeycloakURL      string
	KeycloakRealm    string
	KeycloakClientID string
	KeycloakUsername string
	KeycloakPassword string
}

// readTestConfig читает конфигурацию для тестов
func readTestConfig(t *testing.T) TestConfig {
	// Для E2E тестов используем переменные окружения
	// Это проще и надежнее, чем парсинг конфигурационного файла
	t.Logf("Reading test configuration from environment variables")
	return readConfigFromEnv()
}

// readConfigFromEnv читает конфигурацию из переменных окружения
func readConfigFromEnv() TestConfig {
	port, _ := strconv.Atoi(getEnvOrDefault("POSTGRES_PORT", "5432"))
	redisPort, _ := strconv.Atoi(getEnvOrDefault("REDIS_PORT", "6379"))

	return TestConfig{
		PostgresHost:     getEnvOrDefault("POSTGRES_HOST", "localhost"),
		PostgresPort:     port,
		PostgresUser:     getEnvOrDefault("POSTGRES_USER", "postgres"),
		PostgresPassword: os.Getenv("POSTGRES_PASSWORD"),
		RedisHost:        getEnvOrDefault("REDIS_HOST", "localhost"),
		RedisPort:        redisPort,
		RedisPassword:    os.Getenv("REDIS_PASSWORD"),
		KeycloakURL:      getEnvOrDefault("KEYCLOAK_URL", "http://localhost:8080"),
		KeycloakRealm:    getEnvOrDefault("KEYCLOAK_REALM", "test"),
		KeycloakClientID: getEnvOrDefault("KEYCLOAK_CLIENT_ID", "admin-cli"),
		KeycloakUsername: getEnvOrDefault("KEYCLOAK_USERNAME", "test-user"),
		KeycloakPassword: os.Getenv("KEYCLOAK_PASSWORD"),
	}
}

// createRealDBRegistry создает реальное подключение к БД
func createRealDBRegistry(t *testing.T, testDB *TestDatabase, config TestConfig) primedb.Registry {
	// Используем настройки тестовой БД
	settings := primedb.Settings{
		Host:     config.PostgresHost,
		Port:     config.PostgresPort,
		User:     config.PostgresUser,
		Password: config.PostgresPassword,
		Database: testDB.DBName, // Используем имя тестовой БД
		SSLMode:  "disable",
	}

	db, err := primedb.Connect(settings)
	require.NoError(t, err, "Failed to connect to test database")

	// Регистрируем cleanup для закрытия соединения
	t.Cleanup(func() {
		if err := db.Close(); err != nil {
			t.Logf("Warning: failed to close database connection: %v", err)
		}
	})

	return *primedb.NewRegistry(&db)
}

// createRealCacheRegistry создает реальное подключение к Redis или mock для тестов
func createRealCacheRegistry(t *testing.T, config TestConfig) cache.Registry {
	redisAddr := fmt.Sprintf("%s:%d", config.RedisHost, config.RedisPort)

	// Проверяем доступность Redis (опционально)
	if !isRedisAvailable(redisAddr, config.RedisPassword) {
		t.Logf("Warning: Redis not available at %s, using mock cache", redisAddr)
		// Создаем mock cache registry для тестов
		return createMockCacheRegistry()
	}

	return *cache.NewRegistry(redisAddr, config.RedisPassword)
}

// createMockCacheRegistry создает mock cache registry для тестов
func createMockCacheRegistry() cache.Registry {
	// Создаем mock status cache, который всегда возвращает успешный ответ
	mockStatusCache := &mockStatusCache{}

	return cache.Registry{
		Status: mockStatusCache,
		Group:  nil, // Для тестов групп можно добавить mock при необходимости
	}
}

// mockStatusCache - простая реализация StatusCache для тестов
type mockStatusCache struct{}

func (m *mockStatusCache) GetVersion(ctx context.Context) (string, error) {
	return "mock-redis-version", nil
}

// createRealIdentityRegistry создает реальное подключение к Keycloak
func createRealIdentityRegistry(t *testing.T, config TestConfig) identityprovider.Registry {
	settings := identityprovider.Settings{
		ProviderURL: config.KeycloakURL,
		Realm:       config.KeycloakRealm,
		ClientID:    config.KeycloakClientID,
		Username:    config.KeycloakUsername,
		Password:    config.KeycloakPassword,
		GrantType:   "password",
		SearchLimit: 100,
	}

	// Проверяем доступность Keycloak (опционально)
	if !isKeycloakAvailable(config.KeycloakURL) {
		t.Logf("Warning: Keycloak not available at %s, using mock identity provider", config.KeycloakURL)
		// Создаем mock identity provider registry для тестов
		return createMockIdentityRegistry()
	}

	return *identityprovider.NewRegistry(context.Background(), settings)
}

// createMockIdentityRegistry создает mock identity registry для тестов
func createMockIdentityRegistry() identityprovider.Registry {
	// Создаем mock identity provider, который всегда возвращает успешный ответ
	mockIdentityProvider := &mockIdentityProvider{}

	return identityprovider.Registry{
		Identity: mockIdentityProvider,
	}
}

// mockIdentityProvider - простая реализация IdentityProvider для тестов
type mockIdentityProvider struct{}

func (m *mockIdentityProvider) GetClientID() string {
	return "mock-client-id"
}

func (m *mockIdentityProvider) GetRealm() string {
	return "mock-realm"
}

func (m *mockIdentityProvider) GetURL() string {
	return "http://mock-keycloak:8080"
}

func (m *mockIdentityProvider) GetVersion() (string, error) {
	return "mock-keycloak-version", nil
}

func (m *mockIdentityProvider) GetUsersByEmail(email string) ([]identityentity.KeycloakUser, error) {
	return []identityentity.KeycloakUser{
		{
			ID:        "mock-user-1",
			Email:     email,
			Username:  "mock-user",
			FullName:  "Mock User",
			FirstName: "Mock",
			LastName:  "User",
			Position:  "Test Position",
			Enabled:   true,
		},
	}, nil
}

func (m *mockIdentityProvider) GetUsersBySearch(search string) ([]identityentity.KeycloakUser, error) {
	return []identityentity.KeycloakUser{
		{
			ID:        "mock-user-1",
			Email:     "<EMAIL>",
			Username:  "mock-user",
			FullName:  "Mock User",
			FirstName: "Mock",
			LastName:  "User",
			Position:  "Test Position",
			Enabled:   true,
		},
	}, nil
}

// isRedisAvailable проверяет доступность Redis
func isRedisAvailable(addr, password string) bool {
	// Создаем временный клиент для проверки
	statusCache := cache.NewStatusCache(addr, password)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// Пытаемся получить версию Redis
	_, err := statusCache.GetVersion(ctx)
	return err == nil
}

// isKeycloakAvailable проверяет доступность Keycloak
func isKeycloakAvailable(url string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", url+"/health", nil)
	if err != nil {
		return false
	}

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode < 500
}

// getEnvOrDefault возвращает значение переменной окружения или значение по умолчанию
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// createAppService создает полный набор сервисов для тестов с реальными подключениями
func createAppService(t *testing.T, db *TestDatabase) *appservice.Service {
	// Читаем конфигурацию из файла
	config := readTestConfig(t)

	// Создаем реальные подключения
	dbRegistry := createRealDBRegistry(t, db, config)
	cacheRegistry := createRealCacheRegistry(t, config)
	identityRegistry := createRealIdentityRegistry(t, config)

	// Создаем сервисы с реальными зависимостями
	return appservice.NewFromDependencies(dbRegistry, cacheRegistry, identityRegistry, "e2e-test-version")
}

// loadAuthorizationConfig loads authorization configuration from YAML file
func loadAuthorizationConfig(filePath string, appService *appservice.Service) (*authorization.Config, error) {
	data, err := os.ReadFile(filepath.Clean(filePath))
	if err != nil {
		return nil, fmt.Errorf("failed to read authorization file: %w", err)
	}

	var config authorization.Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse authorization YAML: %w", err)
	}

	// Set validator if appService is provided
	if appService != nil {
		config.SetValidator(authorization.NewServiceValidator(appService.Permission))
	}

	return &config, nil
}
