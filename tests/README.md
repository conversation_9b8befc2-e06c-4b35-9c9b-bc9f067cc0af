# Тестирование

## Запуск тестов

### E2E тесты

```bash
# Все E2E тесты
./scripts/e2e_tests.sh

# Интеграционные тесты напрямую
cd tests/e2e/integration && go test -v

# Конкретный тест
go test -v -run TestSystemGroup_UpdateAndGet_Success
```

### Переменные окружения

```bash
export POSTGRES_HOST=*************
export POSTGRES_PORT=5432
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password
export REDIS_HOST=localhost
export REDIS_PORT=6379
export KEYCLOAK_URL=http://localhost:8080
export KEYCLOAK_REALM=master
```

## Написание E2E тестов

### Структура теста

```go
func TestModuleName_Action_ExpectedResult(t *testing.T) {
    // Arrange - настройка
    db := setup.SetupTestDatabase(t)
    server := setup.SetupTestServer(t, db)
    client := common.NewTestClient(server.BaseURL)

    // Act - выполнение
    resp := client.GET(t, "/v1/endpoint")

    // Assert - проверка
    common.AssertStatusCode(t, resp, http.StatusOK)
}
```

### Добавление новых тестов

**Для интеграционных тестов (рекомендуется):**
1. Добавить тестовые файлы в `tests/e2e/integration/`
2. Использовать package `integration`
3. Все тесты будут использовать общую БД

**Для отдельного модуля:**
1. Создать папку `tests/e2e/modulename/`
2. Добавить `main_test.go`:

```go
package modulename

import (
    "os"
    "testing"
    "git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms/tests/e2e/setup"
)

func TestMain(m *testing.M) {
    code := m.Run()
    setup.CleanupSharedDatabase()
    os.Exit(code)
}
```

3. Создать тестовые файлы `*_test.go`

### Особенности

- Все тесты используют одну общую БД
- Данные между тестами не очищаются
- Тесты должны быть независимы от порядка выполнения
- Используйте уникальные данные или проверяйте существующие
- Интеграционные тесты в `tests/e2e/integration/` могут тестировать разные модули вместе

### HTTP клиент

```go
// GET запрос
resp := client.GET(t, "/v1/groups/123")

// POST с JSON
resp := client.POST(t, "/v1/groups", createRequest)

// PATCH с JSON  
resp := client.PATCH(t, "/v1/groups/123", updateRequest)

// DELETE
resp := client.DELETE(t, "/v1/groups/123")

// Проверка статуса
common.AssertStatusCode(t, resp, http.StatusOK)

// Чтение JSON ответа
var result GroupDTO
common.ReadJSONResponse(t, resp, &result)
```

### Типы тестов

- **Happy path**: Успешные сценарии
- **Error cases**: Обработка ошибок (404, 400, 500)
- **Validation**: Проверка валидации данных
- **Full cycle**: Полные CRUD сценарии

### Примеры

```go
// Успешное обновление
func TestGroup_Update_Success(t *testing.T) {
    db := setup.SetupTestDatabase(t)
    server := setup.SetupTestServer(t, db)
    client := common.NewTestClient(server.BaseURL)
    
    updateRequest := GroupDTO{Name: "Updated Name"}
    resp := client.PATCH(t, "/v1/groups/1", updateRequest)
    
    common.AssertStatusCode(t, resp, http.StatusOK)
}

// Ошибка 404
func TestGroup_UpdateNonExistent_NotFound(t *testing.T) {
    db := setup.SetupTestDatabase(t)
    server := setup.SetupTestServer(t, db)
    client := common.NewTestClient(server.BaseURL)
    
    updateRequest := GroupDTO{Name: "Test"}
    resp := client.PATCH(t, "/v1/groups/99999", updateRequest)
    
    common.AssertStatusCode(t, resp, http.StatusNotFound)
}
```
