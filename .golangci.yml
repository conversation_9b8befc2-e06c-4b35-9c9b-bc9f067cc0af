version: "2"  # Версия формата конфигурационного файла (v2 — для golangci-lint 2.x)

run: # Параметры запуска линтера
  timeout: 5m  # Максимальное время выполнения анализа
  relative-path-mode: gomod  # Пути к файлам интерпретируются относительно корня go-модуля
  issues-exit-code: 1  # Код выхода = 1, если найдены ошибки (полезно для CI)
  tests: true  # Анализировать также _test.go файлы
  modules-download-mode: readonly  # Не разрешать загрузку зависимостей во время анализа (безопасно и быстрее на CI)

output: # Настройки вывода результатов линтинга
  formats:
    text: # Формат вывода в терминал
      print-linter-name: true  # Показывать имя линтера рядом с каждым сообщением
      print-issued-lines: true  # Показывать строку кода, где найдена проблема
      colors: true  # Использовать цветной вывод (для лучшей читаемости)

issues: # Общие настройки для обработки проблем
  max-issues-per-linter: 0  # Без лимита на количество ошибок от одного линтера
  max-same-issues: 0  # Без лимита на количество одинаковых ошибок
  uniq-by-line: true  # Отображать максимум одну ошибку на строку (избегает спама от разных линтеров по одной строке)

linters: # Список активных и отключённых линтеров
  default: standard  # Использовать стандартный набор линтеров golangci-lint
  enable: # Явно включенные дополнительные линтеры
    - errcheck        # Проверяет, что ошибки не игнорируются
    - staticcheck     # Глубокий статический анализ (includes gosimple, stylecheck)
    - govet           # Встроенный в Go инструмент анализа (ловит потенциальные баги)
    - gocritic        # Набор продвинутых правил для улучшения качества кода
    - revive          # Проверяет стиль кода, оформление, названия
    - unused          # Находит неиспользуемые переменные, типы, функции
    - gosec           # Проверка на уязвимости и небезопасные практики
    - depguard        # Запрещает импорт указанных пакетов (контроль зависимостей)
    - bodyclose       # Проверяет, что закрывается `resp.Body` у HTTP-запросов
    - asciicheck      # Предупреждает о не-ASCII символах в коде
    - cyclop          # Контролирует цикломатическую сложность функций
    - dupl            # Находит дублированные фрагменты кода
    - ineffassign     # Обнаруживает неиспользуемые присваивания
    - unparam         # Находит неиспользуемые параметры функций
    - errorlint       # Рекомендует использовать `errors.Is` / `errors.As` вместо прямых сравнений
    - errname         # Проверяет имена переменных/типов ошибок (должны содержать "Err")
    - forbidigo       # Запрещает определённые вызовы или конструкции по regex-паттернам
    - contextcheck    # Проверяет, что `context.Context` передаётся в методы/функции
  disable: # Линтеры, которые намеренно отключены
    - gocyclo  # Старый линтер сложности (заменён на cyclop)
    - lll      # Проверка длины строк (часто шумная и мешает)
    - containedctx    # Предупреждает, если `context.Context` сохраняется в структуре (плохо)

  exclusions: # Исключения из анализа
    generated: strict  # Игнорировать сгенерированные файлы (по "// Code generated ... DO NOT EDIT.")
    rules: # Локальные исключения по пути/файлу/линерам
      - path: _test\.go  # Для всех тестов:
        linters:
          - cyclop     # Не проверять сложность тестов
          - dupl       # Не проверять дублирование в тестах
          - gosec      # Не искать уязвимости в тестах
          - staticcheck # Не проверять статический анализ в тестах
          - forbidigo  # Разрешить time.Sleep и fmt.Print* в тестах
          - errcheck   # Разрешить игнорирование ошибок в тестах
          - errname    # Не проверять имена ошибок в тестах
          - unused     # Не проверять неиспользуемые элементы в тестах
      - path: mocks/  # Для всех моков:
        linters:
          - revive     # Не проверять стиль в моках
          - staticcheck # Не проверять статический анализ в моках
          - unused     # Не проверять неиспользуемые элементы в моках
          - errcheck   # Не проверять обработку ошибок в моках
          - gosec      # Не проверять безопасность в моках
          - gocritic   # Не проверять критические замечания в моках
      - path: gen/  # Для всех генерированных файлов:
        linters:
          - revive     # Не проверять стиль в генерированных файлах
          - staticcheck # Не проверять статический анализ
          - unused     # Не проверять неиспользуемые элементы
          - errcheck   # Не проверять обработку ошибок
          - gosec      # Не проверять безопасность
          - gocritic   # Не проверять критические замечания
          - cyclop     # Не проверять сложность
          - dupl       # Не проверять дублирование
      - text: "mock"  # Исключить все файлы содержащие "mock" в названии
        linters:
          - revive
          - staticcheck
          - unused
          - errcheck

  settings: # Индивидуальные настройки для конкретных линтеров

    gosec:
      # Включаем все правила, включая экспериментальные
      config:
        global:
          audit: true        # Активировать все правила безопасности
          show-ignored: true # Показывать проигнорированные проблемы в комментариях
        severity: "medium"   # Минимальная важность проблем, high/medium/low
        confidence: "medium" # Минимальный уровень уверенности, high/medium/low
    
    cyclop:
      max-complexity: 20  # Допустимая сложность функции (больше 10, но не слишком жёстко)

    depguard:
      rules:
        main:
          deny:
            - pkg: io/ioutil  # Запрет использовать устаревший пакет
              desc: "Использование устаревшего пакета io/ioutil запрещено (замените вызовы на аналоги из пакетов os/io)"

    revive:
      severity: warning  # Нарушения revive будут как предупреждения, а не ошибки

    forbidigo:
      exclude-godoc-examples: true  # Не применять запреты к Godoc-примерам
      analyze-types: true  # Искать запреты и в типах/константах, а не только в функциях
      forbid: # Список запрещённых паттернов
        - pattern: '^fmt\.Print.*$'  # Запрет использования fmt.Print*, fmt.Println и т.д.
          msg: "Запрещено напрямую использовать fmt.Print* для логирования (вместо этого используйте структурированный логгер)"
        - pattern: '^time\.Sleep$'  # Запрет использования time.Sleep
          msg: "Запрещено использовать time.Sleep в продакшен-коде (используй таймеры/контекст)"
        - pattern: '^http\.DefaultClient$'  # Запрет использования небезопасного http.DefaultClient
          msg: "Не используй http.DefaultClient (нет таймаутов); создай *http.Client с таймаутами"

formatters: # Форматтеры кода (проверка стиля)
  enable:
    - gofumpt  # Строгая версия gofmt с дополнительными правилами
    - gci      # Форматтер импортов (гибкая альтернатива goimports)
  settings:
    gofumpt:
      extra-rules: false  # Включить дополнительные строгие правила (например, удаление лишних пустых строк)
    gci:
      sections: # Порядок группировки импортов
        - Standard  # Стандартная библиотека
        - Default   # Сторонние зависимости
        - Prefix(git.ds.ecpk.sibintek.ru/ecpk/command-center/backend/pms)  # Локальные импорты проекта
      no-inline-comments: false  # Разрешить комментарии после импортов (true — запретит)