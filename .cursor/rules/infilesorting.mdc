---
description: Apply when Go service files show CRUD method disorder, misplaced constructors, mixed public/private methods, or scattered helper functions. Trigger on structure violations of: interface → struct → constructors → CRUD methods → private methods → helpers.
globs: 
alwaysApply: false
---
### 🔄 Rule: Entity Sorting within Service Files

**Applicability**: When organizing Go service file contents

**Order of element placement in file:**

1. **Package and imports**
   - Package declaration
   - Standard library imports
   - Third-party imports  
   - Project local imports
   - Empty line after each section

2. **Interface** (if exists)
   - Main service interface (e.g., `ProductService`)
   - With mock generation: `//go:generate`

3. **Struct** implementing the interface
   - Main service structure (e.g., `productService`)

4. **Main Constructor**
   - Primary constructor (e.g., `NewProductService`)

5. **Other Constructors** 
   - Other constructors (functions with `New` prefix)
   - **Sorting**: alphabetical

6. **Interface Methods**
   - Methods defined in the interface
   - **CRUD-based sorting:**
     - **CREATE**: `create`, `add`, `assign` (by prefix priority, then alphabetical)
     - **READ**: `get`, `find`, `fetch`, `list`, `is`, `exist`, `has` (by prefix priority, then alphabetical)
     - **UPDATE**: `update`, `edit`, `modify`, `set` (by prefix priority, then alphabetical) 
     - **DELETE**: `delete`, `remove`, `unassign`, `deactivate` (by prefix priority, then alphabetical)

7. **Public Methods** of structure (not from interface)
   - Public methods of service structure (start with capital letter)
   - **Sorting**: similar to interface methods - by CRUD principle

8. **Private Methods** of structure
   - Private methods of service structure (start with lowercase letter)
   - **Sorting**: alphabetical

9. **Helper Functions**
   - Helper functions that are not structure methods
   - **Sorting**: alphabetical

**CRUD Sorting Details:**

```go
// CREATE prefixes (by priority):
create*, add*, assign*

// READ prefixes (by priority):
get*, find*, fetch*, list*, is*, exist*, has*

// UPDATE prefixes (by priority):
update*, edit*, modify*, set*

// DELETE prefixes (by priority):
delete*, remove*, unassign*, deactivate*
```

**Sorting rules within CRUD groups:**
1. First by prefix priority
2. Then alphabetically (lowercase)
3. Methods without explicit CRUD prefix belong to READ operations

**Separators:**
- Empty line between each section
- Empty line between each method/function

**File Structure Example:**
```go
package service

import (...)

//go:generate mockgen
type ProductService interface {...}

type productService struct {...}

func NewProductService(...) ProductService {...}

func NewProductHelper(...) Helper {...}

// CREATE methods
func (s *productService) CreateProduct(...) {...}
func (s *productService) AddParticipant(...) {...}

// READ methods  
func (s *productService) GetProduct(...) {...}
func (s *productService) ListProducts(...) {...}

// UPDATE methods
func (s *productService) UpdateProduct(...) {...}

// DELETE methods
func (s *productService) DeleteProduct(...) {...}

// Private methods (alphabetical)
func (s *productService) validateProduct(...) {...}

// Helper functions (alphabetical)
func formatProductName(...) {...}
```

**Automation**: Use `scripts/reorganize_service.py` for automatic file reorganization according to these rules. 