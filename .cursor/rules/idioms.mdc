---
description: 
globs: 
alwaysApply: true
---
### 🐹 Rule: Idiomatic Go Code

**Applicability**: When writing and refactoring Go code to ensure idiomaticity, structure, and maintainability

## 📝 Naming

### Variables and Functions
- **camelCase** for private: `userService`, `getCurrentUser`
- **PascalCase** for public: `UserService`, `GetCurrentUser`
- **Short names** for short scopes: `i`, `u`, `err`
- **Descriptive names** for larger scopes: `userRepository`, `productManager`

### Constants and Enumerations
```go
// Single constants
const MaxRetries = 3

// Constant groups
const (
    StatusActive   = "active"
    StatusInactive = "inactive"
    StatusPending  = "pending"
)

// Enum-like types
type UserRole string

const (
    UserRoleAdmin UserRole = "admin"
    UserRoleUser  UserRole = "user"
    UserRoleGuest UserRole = "guest"
)
```

### Types and Structures
- **PascalCase** for exported: `UserService`, `ProductData`
- **camelCase** for private: `userRepository`, `configLoader`
- **Purpose-based suffixes**: `Data`, `Config`, `Service`, `Repository`
- **Use `any`** instead of `interface{}` (Go 1.18+): `func Process(data any) error` instead of `func Process(data interface{}) error`

### Packages
- **Short, descriptive**: `auth`, `user`, `config`
- **DON'T use**: `util`, `common`, `helper` (too generic)
- **Avoid stuttering**: `user.User` → `user.Profile`

## 🏗️ Code Structure

### File Order
```go
package service

// 1. Imports (standard library → external → local)
import (
    "context"
    "time"
    
    "github.com/external/package"
    
    "project/internal/domain"
)

// 2. Types and constants
type Service interface { ... }
type service struct { ... }

// 3. Constructors
func NewService(...) Service { ... }

// 4. Public methods (by CRUD: Create → Read → Update → Delete)
func (s *service) CreateUser(...) error { ... }
func (s *service) GetUser(...) (*User, error) { ... }

// 5. Private methods (alphabetical)
func (s *service) validateInput(...) error { ... }
```

### Line Wrapping for Long Lines

#### Function Parameters
```go
// ✅ Good: wrap after opening parenthesis
func NewUserService(
    userRepo UserRepository,
    authService AuthService,
    logger Logger,
    cache CacheService,
) UserService {
    return &userService{
        userRepo:    userRepo,
        authService: authService,
        logger:      logger,
        cache:       cache,
    }
}

// ✅ Good: align parameters vertically
func (s *service) ProcessUser(userID int64, 
                              userData UserData,
                              options ProcessingOptions) error {
    // implementation
}
```

#### Struct Fields
```go
// ✅ Good: one field per line for readability
type UserService struct {
    userRepo     UserRepository
    authService  AuthService
    logger       Logger
    cache        CacheService
    metrics      MetricsCollector
    validator    InputValidator
}

// ✅ Good: align field types
type Config struct {
    DatabaseURL      string        `yaml:"database_url"`
    RedisURL         string        `yaml:"redis_url"`
    LogLevel         string        `yaml:"log_level"`
    RequestTimeout   time.Duration `yaml:"request_timeout"`
    MaxRetries       int           `yaml:"max_retries"`
    EnableProfiling  bool          `yaml:"enable_profiling"`
}
```

#### Interface Methods
```go
// ✅ Good: wrap long method signatures
type UserService interface {
    CreateUser(ctx context.Context, 
               userData UserCreateData) (*User, error)
    
    UpdateUserProfile(ctx context.Context,
                      userID int64,
                      profileData ProfileUpdateData,
                      options UpdateOptions) error
    
    GetUsersByFilters(ctx context.Context,
                      filters UserFilters,
                      pagination PaginationParams) (*PaginatedUsers, error)
}
```

#### Function Calls
```go
// ✅ Good: wrap function calls with many arguments
user, err := service.CreateUserWithDetails(
    ctx,
    userData,
    ProfileData{
        FirstName: "John",
        LastName:  "Doe",
        Email:     "<EMAIL>",
    },
    CreateOptions{
        SendWelcomeEmail: true,
        ValidateEmail:    true,
        GeneratePassword: false,
    },
)

// ✅ Good: chain method calls on separate lines
result := query.
    Where("status = ?", StatusActive).
    Where("created_at > ?", startDate).
    Order("created_at DESC").
    Limit(limit).
    Find()
```

### Function Decomposition
```go
// ❌ Bad: large function
func (s *service) ProcessUser(data UserData) error {
    // 50+ lines of code
}

// ✅ Good: decomposed
func (s *service) ProcessUser(data UserData) error {
    if err := s.validateUserData(data); err != nil {
        return err
    }
    
    user := s.buildUser(data)
    
    return s.saveUser(user)
}

func (s *service) validateUserData(data UserData) error { ... }
func (s *service) buildUser(data UserData) *User { ... }
func (s *service) saveUser(user *User) error { ... }
```

## 📚 Documentation

### Comments for Public Elements
```go
// UserService manages system users.
// Provides methods for creating, retrieving, and updating users.
type UserService interface {
    // CreateUser creates a new user with the specified data.
    // Returns an error if the data is invalid or the user already exists.
    CreateUser(data UserCreateData) (*User, error)
    
    // GetUserByID returns a user by their identifier.
    // Returns ErrUserNotFound if the user is not found.
    GetUserByID(id int64) (*User, error)
}
```

### TODO and FIXME Comments
```go
// TODO(username): add email validation
// FIXME: race condition when creating users simultaneously  
// HACK: temporary solution until API refactoring
```

## ⚡ Idiomatic Patterns

### Error Handling
```go
// ✅ Correct
func (s *service) GetUser(id int64) (*User, error) {
    if id <= 0 {
        return nil, ErrInvalidUserID
    }
    
    user, err := s.repo.FindByID(id)
    if err != nil {
        return nil, fmt.Errorf("finding user %d: %w", id, err)
    }
    
    return user, nil
}

// ❌ Wrong: swallowing errors
func (s *service) GetUser(id int64) *User {
    user, _ := s.repo.FindByID(id) // ignoring error
    return user
}
```

### Context Usage
```go
func (s *service) CreateUser(ctx context.Context, data UserCreateData) error {
    select {
    case <-ctx.Done():
        return ctx.Err()
    default:
    }
    
    return s.repo.SaveWithContext(ctx, user)
}
```

### Zero Values and Initialization
```go
// ✅ Use zero values
type Config struct {
    Timeout time.Duration // 0 by default
    Retries int          // 0 by default
    Debug   bool         // false by default
}

// Optional parameters via functional options
type Option func(*Config)

func WithTimeout(timeout time.Duration) Option {
    return func(c *Config) {
        c.Timeout = timeout
    }
}

func NewService(options ...Option) *Service {
    config := Config{} // zero value
    for _, opt := range options {
        opt(&config)
    }
    return &Service{config: config}
}
```

### Slice Declaration Best Practices
```go
// ✅ Correct: empty slice declaration
var stands []proposalentity.Stand

// ❌ Avoid: empty slice literal (triggers linter warning)
stands := []proposalentity.Stand{}

// ✅ Correct: slice with known capacity
items := make([]string, 0, 10)

// ✅ Correct: slice with initial data
items := []string{"item1", "item2"}

// ✅ Correct: nil slice in tests
func TestWithEmptyData(t *testing.T) {
    var proposals []proposalentity.Proposal
    // test logic...
}

// ❌ Avoid: empty slice literal in tests
func TestWithEmptyData(t *testing.T) {
    proposals := []proposalentity.Proposal{}
    // test logic...
}
```

### Modern Type Usage
```go
// ✅ Good: use 'any' (Go 1.18+)
func ProcessData(data any) error {
    return processGeneric(data)
}

func ConvertToJSON(data any) ([]byte, error) {
    return json.Marshal(data)
}

// ❌ Outdated: use interface{}
func ProcessData(data interface{}) error {
    return processGeneric(data)
}

// ✅ Good: prefer specific types when possible
func ProcessUser(user *User) error {
    return process(user)
}

// ❌ Less preferred: generic when specific type known
func ProcessUser(user any) error {
    return process(user.(*User)) // requires type assertion
}
```

## 🎯 Consistency

### Project-wide Uniformity
- **Consistent error patterns**: `ErrUserNotFound`, `ErrInvalidInput`
- **Common suffixes**: `Service`, `Repository`, `Handler`
- **Unified return style**: `(result, error)` or `error`
- **Consistent naming**: `ctx context.Context` always as first parameter

### Formatting
- Use `gofmt` and `goimports`
- Maximum 100 characters per line
- Empty lines to separate logical blocks
- Align struct fields with tabs

**Checking tools**: `golint`, `go vet`, `golangci-lint`, `staticcheck` 
