---
description: Apply when creating, modifying, or reviewing scripts in the project. Trigger on file operations with .py and .sh extensions, script organization tasks, naming convention violations, missing documentation, or when establishing project scripting standards.
globs: 
alwaysApply: false
---
# 📜 Rule: Script Writing

**Applicability**: When creating and modifying scripts in the project

## 📁 File Naming

### Python scripts (.py)
- **Style**: `snake_case`
- **Examples**: 
  - `reorganize_service.py`
  - `sort_interface_methods.py`
  - `data_migration_tool.py`

### Shell scripts (.sh)
- **Style**: `kebab-case`
- **Examples**:
  - `sort-openapi.sh`
  - `setup-bruno-api.sh`
  - `pre-push-check.sh`

### Rationale
- **Python**: Follows PEP 8 and accepted Python community conventions
- **Shell**: Traditional approach in Unix/Linux systems and DevOps practices

## 🌐 Languages

### Comments and text in scripts
- **Language**: **English** (unless explicitly specified otherwise)
- **Applies to**:
  - Code comments
  - Documentation strings (docstrings)
  - User messages (print, echo)
  - Help information (--help)

**Examples**:
```python
#!/usr/bin/env python3
"""
Script for sorting methods in Go interfaces by CRUD principle.
Designed for files containing only interfaces (e.g., repository files).
"""

def categorize_methods_by_crud(method_names):
    """Categorizes methods by CRUD operations with prefix priority"""
    # Sort each group by prefix priority, then alphabetically
    create_sorted = sorted(create_methods, key=lambda x: get_prefix_priority(x, create_prefixes))
```

```bash
#!/bin/bash
# Sort OpenAPI specification file according to standard
echo "✅ OpenAPI specification successfully sorted!"
```

### README files
- **Language**: **Russian** (unless specified otherwise)
- **Applies to**:
  - `README.md` files in script folders
  - Usage documentation
  - Functionality descriptions

**Rationale**: The project is maintained by a Russian-speaking team, documentation should be accessible to all participants.

## 📋 Script Structure

### Python scripts
```python
#!/usr/bin/env python3
"""
Brief description of script purpose.
More detailed explanation if needed.
"""

import argparse
import os
import sys

def main():
    parser = argparse.ArgumentParser(
        description='Brief description of script',
        epilog='''
Usage examples:
  python3 script_name.py argument1
  python3 script_name.py --option value
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    # Implementation

if __name__ == '__main__':
    sys.exit(main())
```

### Shell scripts
```bash
#!/bin/bash
# Brief description of script purpose
# More detailed explanation if needed

set -euo pipefail  # Exit on error, undefined vars, pipe failures

main() {
    # Implementation
}

# Call main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

## 🛠️ Required Elements

### All scripts must have:
1. **Shebang** in the first line
2. **Brief description** of purpose
3. **Error handling** (set -euo pipefail for bash, try/except for Python)
4. **Help information** (--help option)
5. **Backup creation** when modifying files
6. **Informative messages** about execution progress

### Additionally recommended:
- Input parameter validation
- Logging of important operations
- Documentation of complex logic
- Usage examples in --help

## 📦 Organization

### Scripts folder structure
```
scripts/
├── README.md                      # Description in Russian
├── reorganize_service.py          # Python: snake_case
├── sort_interface_methods.py      # Python: snake_case
├── sort-openapi.sh               # Shell: kebab-case
├── setup-bruno-api.sh            # Shell: kebab-case
└── pre-push-check.sh             # Shell: kebab-case
```

### README should contain:
- Overview of all scripts
- Usage examples
- Dependencies description
- Working principles

## ✅ Checklist

Before committing, ensure:
- [ ] File name follows conventions
- [ ] All comments are in English
- [ ] README is in Russian (if present)
- [ ] Shebang and brief description are present
- [ ] Script is executable (chmod +x)
- [ ] Error handling is implemented
- [ ] --help option works 