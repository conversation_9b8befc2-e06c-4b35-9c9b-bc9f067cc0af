logfile: stdout
loglevel: debug
api:
  bind: 0.0.0.0:8080
  cors:
    allowOrigins:
      - "*"
  clientMaxBodySize: 512Kb
  rateLimit:
    enabled: true
    perIPLimit: 100      # requests per second per IP
    perIPWindow: 1       # per IP window in seconds
    globalLimit: 5000    # total server requests per second
    globalWindow: 1      # global window in seconds
    burstLimit: 20       # burst requests per IP
    burstWindow: 10      # burst window in seconds
redis:
  host: redisURL
  port: redisPort
  password: REDIS_PASS # environment variable
  db: 0
  poolSize: 10
  dialTimeout: 5
  poolTimeout: 5
  readTimeout: 2
  writeTimeout: 2
pgSQL:
  host: host
  port: port
  sslmode: mode # disable, require, etc.
  db: dbName
  user: username
  password: POSTGRES_PASSWORD # environment variable
keycloak:
  url: URL
  realm: realm
  clientID: client id
  username: pms-svc
  password: KEYCLOAK_PMS_SVC_PASSWORD # environment variable
  grantType: password
  searchLimit: 120
  insecureSkipVerify: true # for production should be false
