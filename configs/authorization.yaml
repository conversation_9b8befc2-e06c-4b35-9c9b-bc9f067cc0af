# Authorization configuration for PMS
# This file contains settings for authorization middleware

# ============================================================================
# YAML ANCHORS - definitions for reuse
# ============================================================================

# Permission methods
permission_methods:
  create: &CREATE_PERM "create"
  view: &VIEW_PERM "view"
  update: &UPDATE_PERM "update"
  delete: &DELETE_PERM "delete"

# Permission names
permissions:
  # Admin permissions
  admin_category: &ADMIN_CATEGORY "admin_category"
  admin_permission: &ADMIN_PERMISSION "admin_permission"
  admin_system_group: &ADMIN_SYSTEM_GROUP "admin_system_group"
  admin_system_role: &ADMIN_SYSTEM_ROLE "admin_system_role"
  admin_user: &ADMIN_USER "admin_user"

  # Profile permissions
  profile: &PROFILE "profile"

  # Service permissions
  service_status: &SERVICE_STATUS "service_status"

  # Product permissions
  product: &PRODUCT "product"
  product_description: &PRODUCT_DESCRIPTION "product_description"
  product_group: &PRODUCT_GROUP "product_group"
  product_participant: &PRODUCT_PARTICIPANT "product_participant"
  product_participant_group: &PRODUCT_PARTICIPANT_GROUP "product_participant_group"
  product_participant_role: &PRODUCT_PARTICIPANT_ROLE "product_participant_role"
  product_proposal: &PRODUCT_PROPOSAL "product_proposal"
  product_proposal_history: &PRODUCT_PROPOSAL_HISTORY "product_proposal_history"
  product_proposal_send_confirm: &PRODUCT_PROPOSAL_SEND_CONFIRM "product_proposal_send_confirm"
  product_role: &PRODUCT_ROLE "product_role"

# Default roles
default_roles:
  account_admin: &ACCOUNT_ADMIN "account_admin"
  account_user: &ACCOUNT_USER "account_user"
  product_owner: &PRODUCT_OWNER "product_owner"
  product_participant: &PRODUCT_PARTICIPANT "product_participant"

# ============================================================================
# MAIN CONFIGURATION
# ============================================================================

# Protected paths - paths requiring additional protected role checks
protected_paths:
  - "/v1/products/{productID}/roles/{roleID}"
  - "/v1/roles/{roleID}"

# Excluded paths - paths accessible without permission checks for specified methods
excluded_paths:
  "/v1/events":
    - "POST"
  "/v1/groups":
    - "GET"
  "/v1/keycloak/users":
    - "GET"
  "/v1/permissions":
    - "GET"
  "/v1/products":
    - "GET"
  "/v1/products/{productID}/groups":
    - "GET"
  "/v1/products/{productID}/roles":
    - "GET"
  "/v1/roles":
    - "GET"
  "/v1/users/current":
    - "GET"
  "/v1/users/{userID}/groups":
    - "GET"
  "/v1/users/{userID}/products":
    - "GET"
  "/v1/users/{userID}/roles":
    - "GET"

# Permission rules for endpoints
# Structure: path -> method -> required permission
endpoint_permissions:
  # Admin users
  "/v1/admin/users":
    "GET":
      permission: *ADMIN_USER
      method: *VIEW_PERM

  "/v1/admin/users/{userID}":
    "GET":
      permission: *ADMIN_USER
      method: *VIEW_PERM
    "PATCH":
      permission: *ADMIN_USER
      method: *UPDATE_PERM

  # Categories
  "/v1/categories":
    "POST":
      permission: *ADMIN_CATEGORY
      method: *CREATE_PERM
    "GET":
      permission: *ADMIN_CATEGORY
      method: *VIEW_PERM

  "/v1/categories/{categoryID}":
    "GET":
      permission: *ADMIN_CATEGORY
      method: *VIEW_PERM
    "PATCH":
      permission: *ADMIN_CATEGORY
      method: *UPDATE_PERM
    "DELETE":
      permission: *ADMIN_CATEGORY
      method: *DELETE_PERM

  "/v1/categories/{categoryID}/permissions":
    "GET":
      permission: *ADMIN_PERMISSION
      method: *VIEW_PERM

  # System groups
  "/v1/groups":
    "POST":
      permission: *ADMIN_SYSTEM_GROUP
      method: *CREATE_PERM
    "GET":
      permission: *ADMIN_SYSTEM_GROUP
      method: *VIEW_PERM

  "/v1/groups/{groupID}":
    "GET":
      permission: *ADMIN_SYSTEM_GROUP
      method: *VIEW_PERM
    "PATCH":
      permission: *ADMIN_SYSTEM_GROUP
      method: *UPDATE_PERM
    "DELETE":
      permission: *ADMIN_SYSTEM_GROUP
      method: *DELETE_PERM

  # Keycloak
  "/v1/keycloak/users":
    "GET":
      permission: *ADMIN_USER
      method: *VIEW_PERM

  # Permissions
  "/v1/permissions":
    "GET":
      permission: *ADMIN_PERMISSION
      method: *VIEW_PERM

  # Products
  "/v1/products":
    "POST":
      permission: *PRODUCT
      method: *CREATE_PERM
    "GET":
      permission: *PRODUCT
      method: *VIEW_PERM

  "/v1/products/{productID}":
    "GET":
      permission: *PRODUCT_DESCRIPTION
      method: *VIEW_PERM
    "PATCH":
      permission: *PRODUCT_DESCRIPTION
      method: *UPDATE_PERM
    "DELETE":
      permission: *PRODUCT_DESCRIPTION
      method: *DELETE_PERM

  "/v1/products/{productID}/groups":
    "POST":
      permission: *PRODUCT_GROUP
      method: *CREATE_PERM
    "GET":
      permission: *PRODUCT_GROUP
      method: *VIEW_PERM

  "/v1/products/{productID}/groups/{groupID}":
    "GET":
      permission: *PRODUCT_GROUP
      method: *VIEW_PERM
    "PATCH":
      permission: *PRODUCT_GROUP
      method: *UPDATE_PERM
    "DELETE":
      permission: *PRODUCT_GROUP
      method: *DELETE_PERM

  "/v1/products/{productID}/mypermissions":
    "GET":
      permission: *PRODUCT
      method: *VIEW_PERM

  "/v1/products/{productID}/participants":
    "POST":
      permission: *PRODUCT_PARTICIPANT
      method: *CREATE_PERM
    "GET":
      permission: *PRODUCT_PARTICIPANT
      method: *VIEW_PERM

  "/v1/products/{productID}/participants/{participantID}":
    "GET":
      permission: *PRODUCT_PARTICIPANT
      method: *VIEW_PERM
    "DELETE":
      permission: *PRODUCT_PARTICIPANT
      method: *DELETE_PERM

  "/v1/products/{productID}/participants/{participantID}/groups":
    "GET":
      permission: *PRODUCT_PARTICIPANT_GROUP
      method: *VIEW_PERM
    "PATCH":
      permission: *PRODUCT_PARTICIPANT_GROUP
      method: *UPDATE_PERM

  "/v1/products/{productID}/participants/{participantID}/roles":
    "GET":
      permission: *PRODUCT_PARTICIPANT_ROLE
      method: *VIEW_PERM
    "PATCH":
      permission: *PRODUCT_PARTICIPANT_ROLE
      method: *UPDATE_PERM

  "/v1/products/{productID}/proposals":
    "POST":
      permission: *PRODUCT_PROPOSAL
      method: *CREATE_PERM
    "GET":
      permission: *PRODUCT_PROPOSAL
      method: *VIEW_PERM

  "/v1/products/{productID}/proposals/{proposalID}":
    "GET":
      permission: *PRODUCT_PROPOSAL
      method: *VIEW_PERM
    "PATCH":
      permission: *PRODUCT_PROPOSAL
      method: *UPDATE_PERM
    "DELETE":
      permission: *PRODUCT_PROPOSAL
      method: *DELETE_PERM

  "/v1/products/{productID}/proposals/{proposalID}/history":
    "GET":
      permission: *PRODUCT_PROPOSAL_HISTORY
      method: *VIEW_PERM

  "/v1/products/{productID}/proposals/{proposalID}/status":
    "POST":
      permission: *PRODUCT_PROPOSAL_SEND_CONFIRM
      method: *VIEW_PERM

  "/v1/products/{productID}/proposals/{proposalID}/view":
    "PUT":
      permission: *PRODUCT_PROPOSAL
      method: *UPDATE_PERM

  "/v1/products/{productID}/roles":
    "POST":
      permission: *PRODUCT_ROLE
      method: *CREATE_PERM
    "GET":
      permission: *PRODUCT_ROLE
      method: *VIEW_PERM

  "/v1/products/{productID}/roles/{roleID}":
    "GET":
      permission: *PRODUCT_ROLE
      method: *VIEW_PERM
    "PATCH":
      permission: *PRODUCT_ROLE
      method: *UPDATE_PERM
    "DELETE":
      permission: *PRODUCT_ROLE
      method: *DELETE_PERM

  # System roles
  "/v1/roles":
    "POST":
      permission: *ADMIN_SYSTEM_ROLE
      method: *CREATE_PERM
    "GET":
      permission: *ADMIN_SYSTEM_ROLE
      method: *VIEW_PERM

  "/v1/roles/{roleID}":
    "GET":
      permission: *ADMIN_SYSTEM_ROLE
      method: *VIEW_PERM
    "PATCH":
      permission: *ADMIN_SYSTEM_ROLE
      method: *UPDATE_PERM
    "DELETE":
      permission: *ADMIN_SYSTEM_ROLE
      method: *DELETE_PERM

  # Status
  "/v1/status":
    "GET":
      permission: *SERVICE_STATUS
      method: *VIEW_PERM

  # Users
  "/v1/user/{userID}/groups":
    "GET":
      permission: *ADMIN_USER
      method: *VIEW_PERM

  "/v1/users":
    "GET":
      permission: *ADMIN_USER
      method: *VIEW_PERM

  "/v1/users/mypermissions":
    "GET":
      permission: *PROFILE
      method: *VIEW_PERM

  "/v1/users/{userID}":
    "GET":
      permission: *PROFILE
      method: *VIEW_PERM
    "PATCH":
      permission: *ADMIN_USER
      method: *UPDATE_PERM

# Default role access rules - paths accessible by default roles without permission checks
default_role_access:
  # Account admin has access to all endpoints with all methods
  *ACCOUNT_ADMIN:
    - path: "*"
      methods: ["GET", "POST", "PATCH", "DELETE", "PUT"]

  # Account user basic access
  *ACCOUNT_USER:
    # Product creation only
    - path: "/v1/products"
      methods: ["POST"]
    # Profile access
    - path: "/v1/users/{userID}"
      methods: ["GET"]
    # Documentation access (no specific endpoint)

  # Product owner access for product-related endpoints
  *PRODUCT_OWNER:
    # Product management
    - path: "/v1/products"
      methods: ["GET", "POST"]
    - path: "/v1/products/{productID}"
      methods: ["GET", "PATCH"]
    # Product description management
    - path: "/v1/products/{productID}"
      methods: ["GET", "PATCH"]
    # Product participants management
    - path: "/v1/products/{productID}/participants"
      methods: ["GET", "POST"]
    - path: "/v1/products/{productID}/participants/{participantID}"
      methods: ["GET", "DELETE"]
    # Product participant groups management
    - path: "/v1/products/{productID}/participants/{participantID}/groups"
      methods: ["GET", "PATCH"]
    # Product participant roles management
    - path: "/v1/products/{productID}/participants/{participantID}/roles"
      methods: ["GET", "PATCH"]
    # Product proposals management
    - path: "/v1/products/{productID}/proposals"
      methods: ["GET", "POST"]
    - path: "/v1/products/{productID}/proposals/{proposalID}"
      methods: ["GET", "PATCH", "DELETE"]
    # Product proposal status
    - path: "/v1/products/{productID}/proposals/{proposalID}/status"
      methods: ["POST"]
    # Profile access
    - path: "/v1/users/{userID}"
      methods: ["GET"]
    # Documentation access (no specific endpoint)

  # Product participant access
  *PRODUCT_PARTICIPANT:
    # Product viewing and creation
    - path: "/v1/products"
      methods: ["GET", "POST"]
    - path: "/v1/products/{productID}"
      methods: ["GET"]
    # Product participants viewing
    - path: "/v1/products/{productID}/participants"
      methods: ["GET"]
    - path: "/v1/products/{productID}/participants/{participantID}"
      methods: ["GET"]
    # Product proposals management
    - path: "/v1/products/{productID}/proposals"
      methods: ["GET", "POST"]
    - path: "/v1/products/{productID}/proposals/{proposalID}"
      methods: ["GET", "PATCH", "DELETE"]
    # Product proposal status
    - path: "/v1/products/{productID}/proposals/{proposalID}/status"
      methods: ["POST"]
    # Profile access
    - path: "/v1/users/{userID}"
      methods: ["GET"]
    # Documentation access (no specific endpoint)
