WORKING_TEST_PACKAGES := \
	./internal/application/category/service \
	./internal/application/group/service \
	./internal/application/keycloak/mapper \
	./internal/application/keycloak/service \
	./internal/application/pagination \
	./internal/application/participant/mapper \
	./internal/application/participant/service \
	./internal/application/permission/mapper \
	./internal/application/permission/service \
	./internal/application/product/service \
	./internal/application/proposal/mapper \
	./internal/application/proposal/service \
	./internal/application/role/service \
	./internal/application/shared/service \
	./internal/application/status/mapper \
	./internal/application/status/service \
	./internal/application/user/mapper \
	./internal/application/user/query \
	./internal/application/user/service \
	./internal/domain/group/service \
	./internal/domain/user/service \
	./internal/errkit \
	./internal/presentation/api \
	./internal/presentation/http/middleware \
	./pkg/logger \
	./pkg/ratelimiter \
	./pkg/serror

generate-api-all: \
	generate-api-admingroup \
	generate-api-adminpermission \
	generate-api-adminproduct \
	generate-api-adminproposal \
	generate-api-adminrole \
	generate-api-adminuser \
	generate-category \
	generate-event \
	generate-keycloak \
	generate-participant \
	generate-permission \
	generate-product \
	generate-productgroup \
	generate-productrole \
	generate-proposal \
	generate-status \
	generate-systemgroup \
	generate-systemrole \
	generate-user

# Backward compatibility alias
generate-all: generate-api-all

# Admin API generators
generate-api-admingroup:
	cd api && oapi-codegen --config=./admingroup.cfg.yaml ./openapi.yaml

generate-api-adminpermission:
	cd api && oapi-codegen --config=./adminpermission.cfg.yaml ./openapi.yaml

generate-api-adminproduct:
	cd api && oapi-codegen --config=./adminproduct.cfg.yaml ./openapi.yaml

generate-api-adminproposal:
	cd api && oapi-codegen --config=./adminproposal.cfg.yaml ./openapi.yaml

generate-api-adminrole:
	cd api && oapi-codegen --config=./adminrole.cfg.yaml ./openapi.yaml

generate-api-adminuser:
	cd api && oapi-codegen --config=./adminuser.cfg.yaml ./openapi.yaml

generate-category:
	cd api && oapi-codegen --config=./category.cfg.yaml ./openapi.yaml

generate-event:
	cd api && oapi-codegen --config=./event.cfg.yaml ./openapi.yaml

generate-keycloak:
	cd api && oapi-codegen --config=./keycloak.cfg.yaml ./openapi.yaml

generate-participant:
	cd api && oapi-codegen --config=./participant.cfg.yaml ./openapi.yaml

generate-permission:
	cd api && oapi-codegen --config=./permission.cfg.yaml ./openapi.yaml

generate-product:
	cd api && oapi-codegen --config=./product.cfg.yaml ./openapi.yaml

generate-productgroup:
	cd api && oapi-codegen --config=./productgroup.cfg.yaml ./openapi.yaml

generate-productrole:
	cd api && oapi-codegen --config=./productrole.cfg.yaml ./openapi.yaml

generate-proposal:
	cd api && oapi-codegen --config=./proposal.cfg.yaml ./openapi.yaml

generate-status:
	cd api && oapi-codegen --config=./status.cfg.yaml ./openapi.yaml

generate-systemgroup:
	cd api && oapi-codegen --config=./systemgroup.cfg.yaml ./openapi.yaml

generate-systemrole:
	cd api && oapi-codegen --config=./systemrole.cfg.yaml ./openapi.yaml

generate-user:
	cd api && oapi-codegen --config=./user.cfg.yaml ./openapi.yaml

# SQLC generation
generate-sqlc:
	sqlc generate
	@echo "Normalizing line endings..."
	@find gen/sqlc -name "*.go" -type f -print0 | xargs -0 sed -i '' 's/\r$$//'
	#to run in wsl
	#@find gen/sqlc -name "*.go" -type f -print0 | xargs -0 sed -i 's/\r$$//'

# SQLC validation
sqlc-vet:
	sqlc vet

# Test commands
test:
	go test ./...

test-working:
	go test $(WORKING_TEST_PACKAGES)

test-coverage:
	go test -coverprofile=coverage.out -covermode=atomic $(WORKING_TEST_PACKAGES)

test-coverage-html: test-coverage
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-coverage-summary: test-coverage
	@echo "=== Test Coverage Summary ==="
	@go tool cover -func=coverage.out | tail -1

test-verbose:
	go test -v $(WORKING_TEST_PACKAGES)

test-e2e:
	@echo "Running Go E2E tests..."
	@./scripts/e2e_tests.sh

pre-push-check:
	@echo "Running code checks..."
	@./scripts/pre-push-check.sh

check-endpoint-methods-consistency:
	@echo "Running endpoints check..."
	@scripts/check-endpoint-methods-consistency.sh

# Mock generation
generate-mocks:
	@echo "Generating mocks..."
	go generate ./internal/domain/...
	@echo "Resetting unchanged files..."
	@git diff --name-only | xargs -I {} sh -c 'if git diff --no-index /dev/null {} > /dev/null 2>&1 || [ "$$(git diff {} | wc -l)" = "0" ]; then git checkout -- {} 2>/dev/null || true; fi' || true

# Domain-specific mocks
generate-mocks-category:
	go generate ./internal/domain/category/...
	@git checkout -- $$(git diff --name-only internal/domain/category/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-group:
	go generate ./internal/domain/group/...
	@git checkout -- $$(git diff --name-only internal/domain/group/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-identityprovider:
	go generate ./internal/domain/identityprovider/...
	@git checkout -- $$(git diff --name-only internal/domain/identityprovider/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-participant:
	go generate ./internal/domain/participant/...
	@git checkout -- $$(git diff --name-only internal/domain/participant/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-permission:
	go generate ./internal/domain/permission/...
	@git checkout -- $$(git diff --name-only internal/domain/permission/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-product:
	go generate ./internal/domain/product/...
	@git checkout -- $$(git diff --name-only internal/domain/product/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-proposal:
	go generate ./internal/domain/proposal/...
	@git checkout -- $$(git diff --name-only internal/domain/proposal/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-role:
	go generate ./internal/domain/role/...
	@git checkout -- $$(git diff --name-only internal/domain/role/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-shared:
	go generate ./internal/domain/shared/...
	@git checkout -- $$(git diff --name-only internal/domain/shared/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-status:
	go generate ./internal/domain/status/...
	@git checkout -- $$(git diff --name-only internal/domain/status/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

generate-mocks-user:
	go generate ./internal/domain/user/...
	@git checkout -- $$(git diff --name-only internal/domain/user/ 2>/dev/null | xargs -I {} sh -c 'if [ "$$(git diff {} | grep -v "^@@\|^+++\|^---" | wc -l)" = "0" ]; then echo {}; fi' 2>/dev/null) 2>/dev/null || true

.PHONY: \
	diagnose-changes \
	generate-api-admingroup \
	generate-api-adminpermission \
	generate-api-adminproduct \
	generate-api-adminproposal \
	generate-api-adminrole \
	generate-api-adminuser \
	generate-api-all \
	generate-all \
	generate-category \
	generate-event \
	generate-keycloak \
	generate-mocks \
	generate-mocks-category \
	generate-mocks-group \
	generate-mocks-identityprovider \
	generate-mocks-participant \
	generate-mocks-permission \
	generate-mocks-product \
	generate-mocks-proposal \
	generate-mocks-role \
	generate-mocks-shared \
	generate-mocks-status \
	generate-mocks-user \
	generate-participant \
	generate-permission \
	generate-product \
	generate-productgroup \
	generate-productrole \
	generate-proposal \
	generate-sqlc \
	generate-status \
	generate-systemgroup \
	generate-systemrole \
	generate-user \
	pre-push-check \
	sqlc-vet \
	test \
	test-ci \
	test-coverage \
	test-coverage-cobertura \
	test-coverage-html \
	test-coverage-summary \
	test-e2e-all \
	test-e2e-bash \
	test-e2e-go \
	test-e2e-python \
	test-junit \
	test-verbose \
	test-working
