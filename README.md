# pms

## Описание
Сервис `pms` предназначен для управления проектами.

## Запуск
```dockerfile
docker build -t pms .
docker run -v путь_к_файлу/pms.conf:/pms.conf -p порт:порт pms
```
Порт может быть произвольный, но он должен совпадать с тем портом, который указан в конфиг файле.

## Endpoints

Полное описание endpoints находится в файле api/openapi.yaml
- <a href="api/openapi.yaml" target="_blank">Открыть спецификацию API</a>

## Конфигурационный файл

Конфигурационный файл `pms.conf` должен находиться в каталоге `configs` корне проекта. Создайте файл на основе `example.conf`:

### Общие параметры

- **logfile**: Место, куда будет выводиться лог. По умолчанию: `stdout`.
- **loglevel**: Уровень логирования. По умолчанию: `debug`.

### Параметры API

- **bind**: Адрес и порт, на котором будет работать ваше приложение. По умолчанию: `0.0.0.0:8080`.
- **cors**: допустимые адреса для Allow Origins.
- **clientMaxBodySize**: Максимальный размер тела запроса, который может быть отправлен клиентом. По умолчанию: `512Kb`.

### Параметры нереляционной СУБД Redis

- **host**: IP-адрес сервера.
- **port**: Порт, на котором слушает.
- **password**: Пароль для подключения.
- **db**: Номер логической базы данных 
- **poolSize**: Максимальное количество соединений в пуле.
- **dialTimeout**: Максимальное время подключения (в секундах).
- **poolTimeout**: Максимальное время ожидания свободного соединения из пула (в секундах).
- **readTimeout**: Максимальное время ожидания ответа от Redis при чтении данных (в секундах).
- **writeTimeout**: Максимальное время отправки данных в Redis (в секундах).


### Параметры базы данных PostgreSQL

- **host**: Адрес хоста БД.
- **port**: Порт для подключения к БД.
- **sslmode**: Режим подключения к БД.
- **db**: Название БД.
- **user**: Имя пользователя для подключения к БД.
- **password**: Пароль для подключения к БД.

### Параметры аутентификации

#### Keycloak
- **url**: url Keycloak, например, `http://localhost:9099`
- **clientID**: ID клиента в Keycloak
- **realm**: название realm в Keycloak
- **username**: имя сервисной учётки
- **password**: пароль сервисной учётки
- **grantType**: тип авторизации
- **searchLimit**: ограничение количества результатов
Пароли для базы данных и Keycloak берутся из переменных окружения.

### (Для разработчиков) Локальная разработка проекта

#### Требования:
- **Golang**  
- **Redis**  
- **PostgreSQL**  
- **oapi-codegen**  
- **sqlc**

#### 🧱 Генерация кода
Для генерации api из спецификации openapi.yaml используется команды в cmd/pms.go
Например: 
```bash
go generate -run "oapi-codegen --config=../api/permission.cfg.yaml ../api/openapi.yaml" ./cmd
```
Для генерации go кода на основе sql, необходимо добавить в <a href="internal/infrastructure/repositories/primedb/queries" target="_blank">internal/../queries</a> необходимый запрос и сгенерировать командой   
```bash
sqlc generate
```



